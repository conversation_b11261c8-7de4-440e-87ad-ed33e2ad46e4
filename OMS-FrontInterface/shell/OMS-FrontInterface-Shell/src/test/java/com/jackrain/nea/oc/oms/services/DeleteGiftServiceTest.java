package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@RunWith(SpringRunner.class)
@EnableAutoConfiguration
@Transactional
@SpringBootTest
public class DeleteGiftServiceTest {
    @Autowired
    OcBOrderItemMapper itemMapper;
    @Autowired
    OcBOrderMapper ocBOrderMapper;
    @Autowired
    IpBStandplatRefundItemMapper refundItemMapper;
    @Autowired
    IpBStandplatRefundMapper refundMapper;

   /* @Autowired
    DeleteGiftService deleteGiftService;*/

    @Autowired
    ActualLackProcessingOrdersService actualLackProcessingOrdersService;
/*
    @Test
    public void entryOrderCreate() {
        System.out.println(deleteGiftService);
    }*/

    @Test
    public void entryOrderCreateaa() {
        String obj = "{'currentPage':0,'pageSize':10,'ID':11}";
//        ValueHolderV14 valueHolderV14 =  actualLackProcessingOrdersService.queryMx(obj,null);
//        System.out.println(JSON.toJSONString(valueHolderV14));
    }

}
