package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.resource.SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class QueryOrderListServiceTest {

    @Autowired
    private QueryOrderListService queryOrderListService;

    @Test
    public void queryOrderList() {

        queryOrderListService.queryOrderList(SystemUserResource.getRootUser(),
                "{\"page\":{\"pageSize\":20,\"pageNum\":1}," +
                        "\"label\":[{\"val\":\"1\",\"text\":\"缺\",\"sort\":13,\"key\":\"IS_LACKSTOCK\"}],\"queryInfo\":[],\"status\":{\"label\":\"全部\",\"value\":\"0\",\"isShow\":true},\"highSearch\":[],\"sort\":[]}");
    }

}