package com.jackrain.nea.util;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import junit.framework.TestCase;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;

import java.text.ParseException;
import java.util.Date;

/**
 * @author: ssf
 * @Date: 2022/08/24 19:33
 * @Description :
 */
public class MD5UtilTest{

    @Test
    public void aa(){
        OcBOrder order=new OcBOrder();
        order.setCpCShopId(84L);
        order.setPlatform(2);
        order.setCpCPhyWarehouseId(1374752L);
        order.setOrderType(1);
        order.setReceiverName("冯坏坏");
        order.setReceiverMobile("***********");
        order.setReceiverPhone("021-88888888");
        order.setCpCRegionProvinceId(420000L);
        order.setCpCRegionCityId(420100L);
        order.setCpCRegionAreaId(420106L);
        order.setReceiverAddress("黎安路999号大虹桥国际");
//        order.setBusinessTypeId(15L);
        MD5Util.encryptOrderInfo4Merge(order);
        System.out.printf(JSON.toJSONString(order));
    }


    @Test
    public void aa11() throws ParseException {
        Date startDate=  DateUtils.parseDate("2022-06-01","yyyy-MM-dd");
        System.out.printf(startDate.toString());
    }
}