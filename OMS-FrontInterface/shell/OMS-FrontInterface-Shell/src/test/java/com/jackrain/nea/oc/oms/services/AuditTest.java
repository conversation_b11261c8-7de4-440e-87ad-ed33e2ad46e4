package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.services.audit.AuditStrategyHandlerFactory;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderAutoAuditService;
import com.jackrain.nea.st.model.StCAutoCheck;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

/**
 * @Auther: 黄志优
 * @Date: 2020/12/4 16:47
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class AuditTest {


    public static void main(String[] args) {
        Date stb = new Date(Long.parseLong("1607366771112"));
//        Date stb = new Date(1606752000000L);
        Date ste = new Date(1607443200000L);
        System.out.println(stb);

//        st b 1606752000000
//        st e 1607443200000
//        1607311594000
    }

    @Autowired
    AuditStrategyHandlerFactory auditStrategyHandlerFactory;

    @Autowired
    private OmsOrderAutoAuditService omsOrderAutoAuditService;

    @Autowired
    private OmsOrderService omsOrderService;


    @Test
    public void audit(){
        omsOrderService.updateAuditSuccess(Long.parseLong("859098"),"root");
    }

    @Test
    public void auditTest(){
        OcBOrderRelation relation = omsOrderService.selectOmsOrderInfo(5382562L);
//        OcBOrderRelation relation = omsOrderService.selectOmsOrderInfo(859090L);


        StCAutoCheck stCAutoCheckDO = JSON.parseObject("  {\n" +
                "    \"AD_CLIENT_ID\": 37,\n" +
                "    \"AD_ORG_ID\": 27,\n" +
                "    \"ANTI_AUDIT_WAIT_TIME\": 0,\n" +
                "    \"BEGIN_TIME\": 1606752000000,\n" +
                "    \"CP_C_LOGISTICS_ID\": \"50\",\n" +
                "    \"CP_C_SHOP_ID\": 260,\n" +
                "    \"CP_C_SHOP_TITLE\": \"爱五月天的橙子\",\n" +
                "    \"CREATIONDATE\": 1605767834000,\n" +
                "    \"EFFECTIVE_CONDITION\": \"1,2\",\n" +
                "    \"END_TIME\": 1607443200000,\n" +
                "    \"HOLD_WAIT_TIME\": 10,\n" +
                "    \"ID\": 219,\n" +
                "    \"ISACTIVE\": \"Y\",\n" +
                "    \"IS_AUTOCHECK_EXCHANGE\": \"N\",\n" +
                "    \"IS_AUTOCHECK_ORDER\": \"Y\",\n" +
                "    \"IS_AUTOCHECK_PAY\": \"N\",\n" +
                "    \"IS_FULL_GIFT_ORDER\": \"N\",\n" +
                "    \"IS_MANUAL_ORDER\": \"N\",\n" +
                "    \"IS_MERGE_ORDER\": \"Y\",\n" +
                "    \"IS_REMARK_AUTOCHECK\": \"N\",\n" +
                "    \"LIMIT_PRICE_DOWN\": 0.0100,\n" +
                "    \"LIMIT_PRICE_UP\": 0.5000,\n" +
                "    \"MODIFIEDDATE\": 1607307548000,\n" +
                "    \"MODIFIERENAME\": \"系统管理员\",\n" +
                "    \"MODIFIERID\": 893,\n" +
                "    \"MODIFIERNAME\": \"root\",\n" +
                "    \"OWNERENAME\": \"系统管理员\",\n" +
                "    \"OWNERID\": 893,\n" +
                "    \"OWNERNAME\": \"root\",\n" +
                "    \"RECEIVER_ADDRESS\": \"123\",\n" +
                "    \"WAIT_TIME\": 0\n" +
                "  }", StCAutoCheck.class);

        relation.setStCAutoCheck(stCAutoCheckDO);
//        omsOrderAutoAuditService.checkOrderAmountScope(relation);
        omsOrderAutoAuditService.checkDateType(relation);



    }
}
