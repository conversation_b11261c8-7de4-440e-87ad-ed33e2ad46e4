//package com.jackrain.nea.oc.oms.services;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.OMSFrontInterfaceMainApplication;
//import com.jackrain.nea.ac.sc.utils.config.SgAcMqConfig;
//import com.jackrain.nea.resource.SystemUserResource;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
//@Slf4j
//public class OcbCancelOrderMergeServiceTest {
//
//    @Resource
//    private OcbCancelOrderMergeService ocbCancelOrderMergeService;
//
//    @Resource
//    private OrderMergeService orderMergeService;
//    @MockBean
//    private SgAcMqConfig sgAcMqConfig;
//
//    //@MockBean
//    //private ActualLackProcessingOrdersAuditingCmd actualLackProcessingOrdersAuditingCmd;
//
//    @Test
//    public void cancelMergeOrder() {
//        List<Long> ids = new ArrayList<>();
//        ids.add(5323520L);
//        ocbCancelOrderMergeService.cancelMergeOrder(SystemUserResource.getRootUser(), ids);
//    }
//
//    @Test
//    public void merge() {
//        orderMergeService.merge(Arrays.asList(5331054L,5330998L),"[TEST]",SystemUserResource.getRootUser());
//    }
//}