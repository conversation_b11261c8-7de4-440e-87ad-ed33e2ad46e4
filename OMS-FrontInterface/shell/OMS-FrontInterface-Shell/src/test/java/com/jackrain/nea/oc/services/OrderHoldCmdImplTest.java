package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.resource.SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class OrderHoldCmdImplTest {

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Test
    public void manualHoldOrder() {
        String json = "{\"ids\":[5951314]}";
        ocBOrderHoldService.manualUnHoldOrder(JSON.parseObject(json), SystemUserResource.getRootUser());

    }
}