package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.resource.SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: 黄志优
 * @Date: 2020/11/15 20:59
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class MergeOrderTest {

    @Autowired
    private OrderMergeService orderMergeService;

    @Test
    public void mergeOrder() {

        List<Long> list = new ArrayList<>();

        list.add(Long.parseLong("5548990"));
        list.add(Long.parseLong("5548989"));

        orderMergeService.mergeOrderMenuOne(list, SystemUserResource.getRootUser());
    }
}
