package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.OMSFrontInterfaceMainApplication;
import com.jackrain.nea.oc.oms.api.OcBOrderCheckCmd;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.resource.SystemUserResource;
import junit.framework.TestCase;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Auther: 黄志优
 * @Date: 2020/12/11 17:59
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OMSFrontInterfaceMainApplication.class)
@Slf4j
public class OcBOrderCheckCmdImplTest extends TestCase {

    @Reference(version = "1.0", group = "oms-fi")
    private OcBOrderCheckCmd ocBOrderCheckCmd;

    @Test
    public void testOrderCheck() {
        JSONObject obj = JSON.parseObject("{\"ids\":[5754320],\"type\":\"1\"}");

        OrderICheckRequest orderCheckRequest = new OrderICheckRequest();
        JSONArray jsonArray = obj.getJSONArray("ids");
        Long[] ids = new Long[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            ids[i] = jsonArray.getLong(i);
            if (log.isDebugEnabled()) {
                log.debug("_手动审核OrderId={}", ids[i]);
            }
        }
        orderCheckRequest.setIds(ids);
        orderCheckRequest.setType(obj.getLong("type"));
        orderCheckRequest.setIsCheck(obj.getLong("isCheck"));

        ocBOrderCheckCmd.orderCheck(orderCheckRequest, SystemUserResource.getRootUser());
    }
}