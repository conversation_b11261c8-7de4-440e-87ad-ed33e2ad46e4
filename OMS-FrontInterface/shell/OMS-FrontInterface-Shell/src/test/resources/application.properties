# åºç¨ç¨åºå
spring.application.name=R3-OC-OMS-FI
# apollo éç½®å¯å¨ï¼Trueè¡¨ç¤ºApplicationå¯å¨æ¶é¦åå è½½Apolloéç½®
#apollo.bootstrap.enabled=true
# apollo.bootstrap.namespace è¡¨ç¤ºå è½½Apolloçnamespaceçåå®¹ãéç¨è±æ,è¿æ¥è¡¨ç¤ºå¤ä¸ª
#apollo.bootstrap.namespaces=application,drds,dubbo,redis,elasticsearch,slaverrds,reloadschema,common,oss
# å¤è¯­è¨é»è®¤è®¾ç½®ãå¨Apolloä¸­éç½®çæ æ³çæï¼åªè½å¨application.propertieséç½®
spring.locale.default=zh_CN
# é»è®¤ç³»ç»ç«¯å£å·
server.port=9091
# å½åç³»ç»è¿è¡éç½®ãä¸»è¦ç¨äºæ¥å¿çè¾åºï¼çäº§çº§å«å°ä¸åè¾åºdebugç¸å³æ¥å¿
spring.profiles.active=dev
# åºç¨ç¨åºå-Dubboåºç¨ç¨åº
dubbo.application.name=R3-OC-OMS-FI
# R3æ åæå¡GroupåVersionå®ä¹
model.version=oms-fi:9