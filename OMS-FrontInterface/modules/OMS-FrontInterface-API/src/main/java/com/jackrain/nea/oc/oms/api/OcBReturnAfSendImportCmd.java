package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.vo.OcBReturnAfSendImpVO;
import com.jackrain.nea.oc.oms.vo.OcBReturnAfSendItemVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2021/12/15 18:51
 */
public interface OcBReturnAfSendImportCmd {
    ValueHolderV14 ocBReturnAfSendImport(Map<String, List<OcBReturnAfSendItemVO>> mapItem,List<OcBReturnAfSendImpVO> oc<PERSON><PERSON>rList, User user) throws NDSException;
}
