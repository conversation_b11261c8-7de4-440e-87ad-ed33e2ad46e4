package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.vo.OcBOrderPreImpVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @ClassName OcBPreOrderImportCmd
 * @Description 订单预导入
 * <AUTHOR>
 * @Date 2022/11/16 13:50
 * @Version 1.0
 */
public interface OcBPreOrderImportCmd {

    ValueHolderV14 importPreOrder(List<OcBOrderPreImpVO> ocBOrderPreImpVOS, User user, String modelCode);

    ValueHolderV14 getAllModel();
}
