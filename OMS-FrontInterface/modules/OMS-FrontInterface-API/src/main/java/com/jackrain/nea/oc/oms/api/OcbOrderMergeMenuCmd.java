package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;

import java.util.List;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-07-04 09:41
 */
public interface OcbOrderMergeMenuCmd extends Command {

    @Override
    ValueHolder execute(QuerySession session) throws NDSException;

    /**
     * 取消订单合并
     *
     * @param user
     * @param orderIds
     * @return 取消结果
     */
    ValueHolderV14 cancelMergeOrder(User user, List<Long> orderIds);

    /**
     * 订单页面手动合并
     *
     * @param session
     */
    ValueHolder mergeOrderOne(QuerySession session) throws NDSException;

}
