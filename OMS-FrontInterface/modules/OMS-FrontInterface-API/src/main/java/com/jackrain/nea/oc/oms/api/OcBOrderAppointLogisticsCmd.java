package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/28 18:15
 * @Description 手工指定快递接口
 */
public interface OcBOrderAppointLogisticsCmd {

    ValueHolderV14<JSONObject> queryLogistics(List<Long> ids, Integer page, Integer size);

    ValueHolderV14<Void> appointLogistics(List<Long> ids, Long logisticsId, User user);

    ValueHolderV14<Void> cancelAppointLogistics(List<Long> ids, User user, boolean isAuto);
}
