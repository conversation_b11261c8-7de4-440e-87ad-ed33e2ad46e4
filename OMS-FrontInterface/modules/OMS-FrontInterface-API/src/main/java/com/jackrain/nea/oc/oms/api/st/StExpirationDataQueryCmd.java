package com.jackrain.nea.oc.oms.api.st;

import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 商品效期策略查询命令接口
 **/
public interface StExpirationDataQueryCmd {

    /**
     * 查询公共的商品效期策略
     *
     * @param monthStart 月份起始数
     * @return 效期策略列表
     */
    ValueHolderV14<List<String>> queryCommonExpirationData(Integer monthStart);
}
