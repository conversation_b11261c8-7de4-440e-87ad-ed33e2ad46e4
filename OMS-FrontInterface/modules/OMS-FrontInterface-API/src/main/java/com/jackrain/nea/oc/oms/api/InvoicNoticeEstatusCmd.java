package com.jackrain.nea.oc.oms.api;


import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;

/**
 * @author: huang.z<PERSON><PERSON>
 * @description: 开票通知单 审核
 * @since: 2019-07-23
 * create at : 2019-07-23 15:00
 */
public interface InvoicNoticeEstatusCmd {
    ValueHolder changeEstatusByUnAudit(QuerySession session) throws NDSException;

    ValueHolder changeEstatusByStop(QuerySession session) throws NDSException;

    ValueHolder changeEstatusByStart(QuerySession session) throws NDSException;
}
