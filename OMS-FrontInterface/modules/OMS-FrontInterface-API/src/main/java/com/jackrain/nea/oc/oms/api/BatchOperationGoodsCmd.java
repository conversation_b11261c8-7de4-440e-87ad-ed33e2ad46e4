package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 商品的批量操作
 *
 * @author: 夏继超
 * @since: 2020/2/14
 * create at : 2020/2/14 11:53
 */
public interface BatchOperationGoodsCmd {

    /**
     * 批量删除商品
     *
     * @param param     入参
     * @param loginUser 当前用户
     * @param usrPem    用户权限
     * @return
     * @throws NDSException
     */
    ValueHolderV14 batchDeleteGoods(JSONObject param, User loginUser, UserPermission usrPem) throws NDSException;

    /**
     * 批量新增商品
     *
     * @param obj
     * @param user
     * @param usrPem
     * @return
     * @throws NDSException
     */
    ValueHolderV14 batchAddGoods(JSONObject obj, User user, UserPermission usrPem) throws NDSException;

    /**
     * 批量替换商品
     *
     * @param obj
     * @param user
     * @param usrPem
     * @return
     */
    @Deprecated
    ValueHolderV14 batchChangeGoods(JSONObject obj, User user, UserPermission usrPem);

    /**
     * 批量替换商品（异步）
     *
     * @param obj
     * @param user
     * @param usrPem
     * @return
     */
    ValueHolderV14 batchChangeGoodsAsync(JSONObject obj, User user, UserPermission usrPem);

    /**
     * 批量添加订单标记
     *
     * @param obj
     * @param user
     * @return
     */
    ValueHolderV14 batchAddLabel(JSONObject obj, User user);

    /**
     * 手工打标：会员加急发货
     *
     * @param obj  传入的参数，JSON对象：{"ids":[1,2,3],"isDeliveryUrgent":1}
     * @param user 当前操作用户
     * @return 响应结果
     */
    ValueHolderV14 orderDeliveryUrgent(JSONObject obj, User user);

    /**
     * <AUTHOR>
     * @Date 22:53 2021/8/31
     * @Description 
     */
    ValueHolderV14 orderUnDeliveryUrgent(JSONObject obj, User user);
}
