package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: 夏继超
 * @since: 2019/3/21
 * create at : 2019/3/21 13:34
 */
public interface OcCancelChangingOrRefundCmd {
    /**
     * 退换货取消
     *
     * @param obj  传入的参数
     * @param user 当前用户
     * @return
     */
    ValueHolderV14 ocCancelChangingOrRefund(JSONObject obj, User user) throws NDSException;

    ValueHolderV14 checkCancelReqParams(JSONObject obj, User user);

    ValueHolderV14 qmRefundService(JSONObject obj, User user);
}
