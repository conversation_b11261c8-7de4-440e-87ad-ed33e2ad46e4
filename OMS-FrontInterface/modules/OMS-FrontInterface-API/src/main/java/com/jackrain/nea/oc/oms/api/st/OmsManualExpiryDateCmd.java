package com.jackrain.nea.oc.oms.api.st;

import com.jackrain.nea.oc.oms.dto.ExpiryDateItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/6/23 下午4:13
 * @Version 1.0
 */
public interface OmsManualExpiryDateCmd {

    public ValueHolderV14 selectOmsOrderItem(List<Long> ids);


    public ValueHolderV14 executeOmsOrderItem(List<ExpiryDateItem> expiryDateItems, User user);

    /**
     * 自动重新指定效期
     * @param ids
     * @return
     */
    ValueHolderV14 autoExpiryDate(List<Long> ids, User user);
}
