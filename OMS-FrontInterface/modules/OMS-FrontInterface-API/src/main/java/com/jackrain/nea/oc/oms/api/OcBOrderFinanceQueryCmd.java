package com.jackrain.nea.oc.oms.api;


import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * 财务提供给财务的接口
 *
 * @date 2019/4/22
 * @author: ming.fz
 */
public interface OcBOrderFinanceQueryCmd {
    /**
     * 通过财务传入条件查询财务需要字段
     *
     * @param shopId      下单店铺id
     * @param orderSource 订单来源
     * @param isWriteoff  是否插入核销流水
     * @param orderType   订单类型
     * @param orderStatus 订单状态
     * @param pageSize    每页显示条数
     * @param pageNum     当前页
     * @return 订单主子表对象
     * @throws NDSException
     */
    ValueHolderV14 query(Long shopId, String orderSource, Long isWriteoff, List<Long> orderType, List<Long> orderStatus,
                         Integer pageSize, Integer pageNum) throws NDSException;
}
