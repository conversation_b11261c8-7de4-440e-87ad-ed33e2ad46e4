package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.Map;

/**
 * 修改退换货卖家备注
 *
 * @author: xiWen.z
 * create at: 2019/9/17 0017
 */
public interface OcBReturnOrderRemarkCmd {

    /**
     * 修改
     *
     * @param jsnObj
     * @param usr
     * @return
     */
    ValueHolderV14 modifySellerRemark(JSONObject jsnObj, User usr);

    /**
     * 导入模板
     *
     * @param usr
     * @return
     */
    ValueHolderV14 downloadReturnRemarkTemp(User usr);


    /**
     * 导入数据
     *
     * @param sourceMap
     * @param osc
     * @param cv
     * @param usr
     * @return
     */
    ValueHolderV14 importReturnRemark(Map<String, OcBReturnOrder> sourceMap, JSONArray osc, boolean cv, User usr);
}
