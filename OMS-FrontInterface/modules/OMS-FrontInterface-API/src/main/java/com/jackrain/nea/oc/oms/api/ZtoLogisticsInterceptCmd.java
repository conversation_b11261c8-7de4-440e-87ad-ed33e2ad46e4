package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/2 17:23
 * @Description
 */
public interface ZtoLogisticsInterceptCmd {

    ValueHolderV14<List<OcBOrder>> interceptCheck(List<Long> orderIds);

    ValueHolderV14<Void> interceptCreate(List<Long> orderIds, String interceptReason, User user, String refundNo);

    ValueHolderV14<Void> interceptCancel(List<Long> ids, User user);
}
