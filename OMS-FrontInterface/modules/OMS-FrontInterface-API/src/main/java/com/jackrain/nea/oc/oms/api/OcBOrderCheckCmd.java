package com.jackrain.nea.oc.oms.api;


import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 订单审核
 *
 * @date 2019/3/8
 * @author: ming.fz
 */
public interface OcBOrderCheckCmd {
    ValueHolderV14 orderCheck(OrderICheckRequest param, User user) throws NDSException;

    ValueHolderV14 checkIssuingOrder(Long orderId,Long shopId) throws NDSException;

    ValueHolderV14 mandatoryAuditOrder(OrderICheckRequest orderCheckRequest, User user) throws NDSException;

}
