package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryOrderConditionResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 订单列表-初始化查询条件
 *
 * @author: xiwen.z
 * create at: 2019/3/8 0008
 */
public interface OcBOrderInitQueryCmd {

    /**
     * @param loginUser User
     * @param per       string
     * @return vh vh
     */
    ValueHolderV14<QueryOrderConditionResult> queryConditionInit(String param, User loginUser, UserPermission per);
}
