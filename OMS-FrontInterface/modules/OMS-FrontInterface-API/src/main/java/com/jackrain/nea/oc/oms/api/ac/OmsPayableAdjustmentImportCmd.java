package com.jackrain.nea.oc.oms.api.ac;

import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.springframework.web.multipart.MultipartFile;

/**
 * @author:洪艺安
 * @since: 2019/7/11
 * @create at : 2019/7/11 9:09
 */
public interface OmsPayableAdjustmentImportCmd extends Command {
    /**
     * 下载应付款调整单模板
     * @return
     */
    public ValueHolderV14 downloadTemp();

    /**
     * 导入应付款调整单
     * @return
     */
    public ValueHolderV14 importPayableAdjustment(MultipartFile file, User user);

}
