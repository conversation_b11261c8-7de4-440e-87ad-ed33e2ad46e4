package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: heliu
 * @since: 2019/5/7
 * create at : 2019/5/7 19:15
 */
public interface OcBOrderSplitCmd {

    ValueHolderV14 querySkuListAndStorageInfo(JSONObject obj, User user) throws NDSException;

    ValueHolderV14 saveSplitOrderInfo(String param, User loginUser) throws NDSException;


    ValueHolderV14 confirmSplitOrder(JSONObject obj, User user) throws NDSException;


    ValueHolderV14 confirmSplitOrderByRow(List<Long> orderIds, User user, String splitReason) throws NDSException;


    ValueHolderV14 confirmSplitOrderByOne(List<Long> orderIds, User user, String splitReason) throws NDSException;

    /**
     * 根据订单明细id跟总的数量 来分别计算不同明细需要拆出来的数量
     *
     * @param obj
     * @param user
     * @return
     * @throws NDSException
     */
    ValueHolderV14 getNumByItemIdAndQty(JSONObject obj, User user) throws NDSException;


    ValueHolderV14 splitOrderByBoxStrategy(JSONObject obj, User user) throws NDSException;


}