package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.request.o2o.SplitOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * o2o 拆单
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/13
 */
public interface SplitSaleOrderCmd {

    /**
     * o2o 拆单
     *
     * @param splitOrderRequest 请求参
     * @param user              操作用户
     * @return 拆单结果
     */
    ValueHolderV14<JSON> splitSaleOrder(SplitOrderRequest splitOrderRequest, User user);
}
