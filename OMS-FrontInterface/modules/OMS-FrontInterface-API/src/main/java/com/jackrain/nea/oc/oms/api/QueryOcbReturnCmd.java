package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * 供外部的查询接口
 *
 * @author: 夏继超
 * @since: 2019/4/18
 * create at : 2019/4/18 18:37
 */
public interface QueryOcbReturnCmd {
    /**
     * 根据条件查询退换货单的数据的集合
     *
     * @param returnStatus 退单状态的集合
     * @param isWriteroff  是否插入核销流水
     * @param count        返回的记录数
     * @return 返回值
     */
    ValueHolderV14<OcBReturnOrder> queryOcbReturn(Long cpShopId, List<Long> returnStatus, Long isWriteroff, Long count);
}
