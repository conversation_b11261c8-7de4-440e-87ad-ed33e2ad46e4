package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: 孙俊磊
 * @since: 2019-03-24
 * create at:  2019-03-24 18:21
 * 扫描入库_保存
 * 扫描入库列表界面查询
 * 综合服务
 */
public interface ScanIncomingCmd {
    /**
     * 扫描入库_保存服务
     *
     * @param param     退货入库单主子表需要的数据
     * @param loginUser 当前登录用户
     * @return holder
     */
    ValueHolderV14 saveScanIncoming(String param, User loginUser) throws NDSException;

    /**
     * 扫描入库-获取退换货主子表信息
     *
     * @param param     查询条件
     * @param loginUser 当前用户
     * @return 主子表信息
     */
    ValueHolderV14 getScanIncomingInfo(String param, User loginUser) throws NDSException;

    /**
     * 获取一条退换货明细
     *
     * @param param     skuEcode
     * @param loginUser 当前用户
     * @return 一条明细
     */
    ValueHolderV14 getOneRefundItem(String param, User loginUser) throws NDSException;

    /**
     * 根据当前登录用户获取创建的批次
     *
     * @param loginUser 用户
     * @return holder
     */
    ValueHolderV14 getCurrentBatch(User loginUser) throws NDSException;


    ValueHolderV14 getLogicalWarehouseInfo(Long batchId);
}
