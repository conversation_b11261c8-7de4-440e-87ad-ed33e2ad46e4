package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @ClassName OcBReturnOrderUpdateCmd
 * @Description 退换货单数据修改
 * <AUTHOR>
 * @Date 2025/4/22 13:40
 * @Version 1.0
 */
public interface OcBReturnOrderUpdateCmd {

    /**
     * 逻辑入库单审核完成后 调用退换货单修改数据
     *
     * @param billNo
     * @return
     */
    ValueHolderV14 updateByStoInResultSubmit(String billNo);
}
