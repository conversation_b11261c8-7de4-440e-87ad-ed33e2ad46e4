package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @ClassName OcBOrderReRunTobeconfirmedCmd
 * @Description 重新执行待分配任务接口
 * <AUTHOR>
 * @Date 2025/5/23 14:25
 * @Version 1.0
 */
public interface OcBOrderReRunTobeconfirmedCmd {

    /**
     * 重新执行待分配任务
     * 根据订单ID查询订单，只处理状态为待分配的订单
     * 查询oc_b_tobeconfirmed_task表，如果存在记录则将状态设置为0
     * 如果不存在记录则创建新记录
     *
     * @param ids 订单ID列表
     * @param user 用户信息
     * @return 处理结果
     * @throws NDSException 异常信息
     */
    ValueHolderV14 reRunTobeconfirmed(List<Long> ids, User user) throws NDSException;
}
