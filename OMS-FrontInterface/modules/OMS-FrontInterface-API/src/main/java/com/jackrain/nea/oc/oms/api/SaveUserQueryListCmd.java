package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.UserConfig;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

import java.util.List;

// 用户绑定查询条件
public interface SaveUserQueryListCmd {

    ValueHolder saveTableQuery(String tableName, List<UserConfig> userConfigList, User userWeb) throws NDSException;
}
