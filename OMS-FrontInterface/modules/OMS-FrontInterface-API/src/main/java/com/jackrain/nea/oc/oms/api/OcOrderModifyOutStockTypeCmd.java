package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * <AUTHOR>
 * @date ：Created in 13:22 2020/3/6
 * description ：修改订单出库类型
 * @ Modified By：
 */
public interface OcOrderModifyOutStockTypeCmd {
    /**
     * 修改订单出库类型
     *
     * @param idArray 订单ID集合
     * @param type    出库类型 0 电商出库  1 大货出库
     * @param user    操作用户
     * @return 结果
     */
    ValueHolderV14<JSONArray> modifyOutStockType(JSONArray idArray, int type, User user);
}
