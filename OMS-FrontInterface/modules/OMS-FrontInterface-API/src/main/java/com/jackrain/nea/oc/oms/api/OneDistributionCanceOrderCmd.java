package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.request.OmsOcBOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR>
 * @create 2021/11/4 18:16
 */
public interface OneDistributionCanceOrderCmd {

    /**
     * 一件代发取消订单
     * @param request
     * @return
     */
    ValueHolderV14 oneDistributionCanceOrder(JSONObject request);

}
