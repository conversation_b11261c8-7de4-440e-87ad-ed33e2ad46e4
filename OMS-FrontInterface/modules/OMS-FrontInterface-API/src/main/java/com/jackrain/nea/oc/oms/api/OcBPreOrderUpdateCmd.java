package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @ClassName OcBPreOrderUpdateShopCmd
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/12 15:53
 * @Version 1.0
 */
public interface OcBPreOrderUpdateCmd {

    /**
     * 修改店铺
     *
     * @param tids      预导入的平台单号
     * @param shopTitle 需要修改的店铺名称
     * @param user      用户
     * @return
     */
    ValueHolderV14<Void> updateShop(List<String> tids, String shopTitle, User user);

    /**
     * 预导入订单转入
     *
     * @param serialNumber 流水号
     * @return
     */
    ValueHolderV14 transferBySerial(String serialNumber, User user);
}
