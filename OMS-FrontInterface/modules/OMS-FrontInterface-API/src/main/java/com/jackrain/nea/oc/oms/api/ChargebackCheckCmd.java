package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: 周琳胜
 * @since: 2019/3/22
 * create at : 2019/3/22 10:34
 */

/**
 * 退单审核
 */
public interface ChargebackCheckCmd {
    ValueHolderV14 execute(JSONObject obj, User user) throws NDSException;

    ValueHolderV14 syncPlatformRefundStatus(List<Long> idList, User user) throws NDSException;

    /**
     * SAP库存异动同步更新 退换货单同步通用平台状态
     * @param returnIdSuccessList
     * @param returnIdFailList
     * @param user
     * @return
     * @throws NDSException
     */
    ValueHolderV14 updatePlatformRefundStatus(List<String> returnIdSuccessList,List<String> returnIdFailList, User user) throws NDSException;

}
