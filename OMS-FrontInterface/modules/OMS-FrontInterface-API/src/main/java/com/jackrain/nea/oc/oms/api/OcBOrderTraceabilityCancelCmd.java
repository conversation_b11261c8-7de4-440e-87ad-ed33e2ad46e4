package com.jackrain.nea.oc.oms.api;


import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @program: r3-oc-oms
 * @description: 订单取消溯源标记
 * @author: lijin
 * @create: 2024-12-19
 **/
public interface OcBOrderTraceabilityCancelCmd {

    /**
     * 取消溯源标记
     *
     * @param orderIds 订单ID列表
     * @param user     用户
     * @return 执行结果
     */
    ValueHolderV14<Void> cancelTraceability(List<Long> orderIds, User user);
}
