package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.request.OcBOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR>
 * @create 2020-07-09
 * @desc 订单查询
 **/
public interface OcBOrderQueryFiCmd {

    /**
     * 订单列表查询
     *
     * @param ocBOrderRequest 请求参数
     * @return
     */
    ValueHolderV14 queryOrderList(OcBOrderRequest ocBOrderRequest);

    /**
     * 获取对应零售订单的下单店铺、对应平台店铺档案的平台
     *
     * @param ocBOrderRequest 请求参数
     * @return
     */
    ValueHolderV14 queryPlatform(OcBOrderRequest ocBOrderRequest);
}
