package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * 取消订单
 *
 * @date 2019/9/16
 * @author: ming.fz
 */
public interface ReturnNewOrderQueryOrigOrderCmd {

    ValueHolderV14<String> queryOcBOrder(JSONObject param, User user) throws NDSException;

    ValueHolderV14<List> updateReturnBOrder(JSONObject param, User user) throws NDSException;

    /**
     * 退单次品调拨
     *
     * @param id
     * @param user
     * @return
     * @throws NDSException
     */
    ValueHolderV14<List> skuDb(Long id, User user) throws NDSException;
}
