package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * 退货入库单作废按钮
 *
 * @author: 夏继超
 * @since: 2019/3/26
 * create at : 2019/3/26 15:55
 */
public interface InvalidButton {
    /**
     * 退货入库作废按钮
     *
     * @param param 传入的参数
     * @param user  当前用户
     * @return 返回的数据
     */
    ValueHolder returnCancel(JSONObject param, User user) throws NDSException;
}
