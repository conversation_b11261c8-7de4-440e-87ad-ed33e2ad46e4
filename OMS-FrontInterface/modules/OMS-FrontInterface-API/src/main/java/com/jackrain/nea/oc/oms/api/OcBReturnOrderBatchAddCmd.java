package com.jackrain.nea.oc.oms.api;


import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: 郑小龙
 * @create: 2020-03-12 09:16
 **/
public interface OcBReturnOrderBatchAddCmd {

    ValueHolderV14 ReturnOrderBatchAdd(List<Long> ids, boolean isback, User user) throws NDSException;
}
