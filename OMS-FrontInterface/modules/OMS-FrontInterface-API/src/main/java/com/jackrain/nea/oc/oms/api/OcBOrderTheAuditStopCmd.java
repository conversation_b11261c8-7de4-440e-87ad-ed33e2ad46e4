package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.request.OrderICheckStopRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 订单反审核并卡单
 *
 * <AUTHOR>
 */
public interface OcBOrderTheAuditStopCmd {

    ValueHolderV14 orderTheAuditStop(OrderICheckStopRequest param, User user) throws NDSException;
}
