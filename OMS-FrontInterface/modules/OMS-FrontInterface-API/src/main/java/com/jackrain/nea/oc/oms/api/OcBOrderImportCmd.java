package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpVO;
import com.jackrain.nea.oc.oms.vo.OcBOrderPreImpVO;
import com.jackrain.nea.oc.oms.vo.OcBOrderRemarkImpVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: 江家雷
 * @create: 2020-11-20 17:13
 **/
public interface OcBOrderImportCmd {

    /**
     * 导入订单
     */
    ValueHolderV14 importOrderList(List<OcBOrderExtend> ocBOrderList, List<OcBOrderItemExtend> ocBOrderItemList, User user,String origFileName);

    ValueHolderV14 importGiftOrderList(List<OcBOrderExtend> ocBOrderList, List<OcBOrderItemExtend> ocBOrderItemList, User user);

    ValueHolderV14<List<OcBOrderRemarkImpVO>> updateOrderAddress(List<OcBOrderRemarkImpVO> orderRemarkList, User user);

    ValueHolderV14 exportError(ValueHolderV14<List<OcBOrderRemarkImpVO>> v14, User user);

    /**
     * 异常错误信息导出
     *
     * @param ocBOrderList 订单列表信息
     * @param user         用户信息
     * @return
     */
    String exportImpErrorResult(List<OcBOrderImpVO> ocBOrderList, User user, String origFileName);

    /**
     * @param ocBOrderPreImpVOList
     * @param user
     * @return
     */
    String exportPreImpErrorResult(List<OcBOrderPreImpVO> ocBOrderPreImpVOList, User user, String modelCode);

}
