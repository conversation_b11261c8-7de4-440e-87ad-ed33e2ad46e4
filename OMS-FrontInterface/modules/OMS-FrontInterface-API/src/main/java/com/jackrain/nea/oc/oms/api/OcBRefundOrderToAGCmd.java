package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-04-02
 * create at:  2019-04-02 10:02
 * 退单传AG服务
 */
public interface OcBRefundOrderToAGCmd {

    /**
     * @param param     退换货单信息
     * @param loginUser 当前操作用户
     */
    ValueHolderV14 refundOrderToAg(String param, User loginUser);
}
