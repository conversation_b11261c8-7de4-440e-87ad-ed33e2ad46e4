package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * 发货后退款单 打款
 *
 * @author: 江家雷
 * @since: 2020/08/14
 * create at :2020/08/14 10:26
 */
public interface RefundPaymentCmd extends Command {

    /**
     * 批量更新打款状态
     *
     * @param list
     * @param user
     * @return
     */
    ValueHolderV14 batchUpdateOcBReturnAfSend(List<OcBReturnAfSend> list, User user);

    /**
     * 模板下载
     *
     * @return
     */
    ValueHolderV14 downloadTemp();

}
