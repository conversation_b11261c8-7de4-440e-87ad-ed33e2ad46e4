package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.table.AcFPriceSetting;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;

/**
 * 外部对指定效期的操作相关接口
 *
 * <AUTHOR>
 */
public interface ExpiryDateThirdCmd {

    ValueHolderV14 save(JSONObject jsonObject);

    ValueHolderV14 preCheck(JSONObject jsonObject);

}
