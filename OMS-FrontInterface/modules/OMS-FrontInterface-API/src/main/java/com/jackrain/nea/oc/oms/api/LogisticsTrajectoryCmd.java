package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.request.kdzs.KdzsCallBackRequest;
import com.jackrain.nea.oc.oms.model.result.kdzs.KdzsCallBackResponse;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import javax.servlet.http.HttpServletRequest;

/**
 * description:新增入库物流轨迹
 * @Author:  liuwenjin
 * @Date 2021/12/7 4:41 下午
 */
public interface LogisticsTrajectoryCmd {

    ValueHolderV14 insterLogisticsTrajectory(JSONObject jsonObject);

}
