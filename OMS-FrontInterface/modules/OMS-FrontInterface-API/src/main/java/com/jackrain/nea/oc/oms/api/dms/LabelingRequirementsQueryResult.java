package com.jackrain.nea.oc.oms.api.dms;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName LabelingRequirementsQueryResult
 * @Description 增值服务档案查询结果
 * <AUTHOR>
 * @Date 2024/6/7 11:45
 * @Version 1.0
 */
@Data
public class LabelingRequirementsQueryResult implements Serializable {
    private static final long serialVersionUID = -8235898559840750223L;

    /**
     * 增值服务编码
     */
    private String addedTypeCode;

    /**
     * 增值服务名称
     */
    private String addedTypeName;
}
