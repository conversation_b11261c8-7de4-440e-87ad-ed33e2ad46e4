package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrder;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderRefund;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 15:03
 **/
public interface OcBReturnOrderImportCmd {
    public ValueHolderV14 importList(List<ExtOcBReturnOrder> extOcBReturnOrderList,
                                     List<ExtOcBReturnOrderRefund> extOcBReturnOrderRefundList,
                                     List<ExtOcBReturnOrderExchange> extOcBReturnOrderExchangeList,
                                     User user);
}
