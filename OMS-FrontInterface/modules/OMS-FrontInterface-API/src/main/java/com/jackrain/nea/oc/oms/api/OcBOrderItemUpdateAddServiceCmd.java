package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @ClassName OcBOrderItemUpdateAddServiceCmd
 * @Description 修改增值服务
 * <AUTHOR>
 * @Date 2023/10/23 11:13
 * @Version 1.0
 */
public interface OcBOrderItemUpdateAddServiceCmd {
    /**
     * 修改增值服务
     *
     * @param object
     * @param user
     * @return
     */
    ValueHolderV14 updateItemAddService(JSONObject object, User user);
}
