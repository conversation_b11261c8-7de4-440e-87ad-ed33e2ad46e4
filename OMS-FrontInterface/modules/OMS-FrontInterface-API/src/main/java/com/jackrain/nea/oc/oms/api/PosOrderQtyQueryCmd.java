package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.result.PosOrderQtyQueryItemResult;
import com.jackrain.nea.oc.oms.model.result.PosOrderQtyQueryResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @ClassName : PosOrderQtyQueryCmd  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-09-08 11:50  
 */
public interface PosOrderQtyQueryCmd {

    /**
     * 查询订单发货数量
     *
     * @param tid 查询参数
     * @return QimenPosOrderStatusResult 订单状态
     */
    ValueHolderV14<PosOrderQtyQueryResult> queryOrderQtyOut(String tid);

}
