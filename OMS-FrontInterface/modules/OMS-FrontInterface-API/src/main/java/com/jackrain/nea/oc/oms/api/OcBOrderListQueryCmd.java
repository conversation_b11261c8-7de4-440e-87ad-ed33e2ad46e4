package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryEsListResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderByBillNoResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderListResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.request.OcBOederQueryByBillNoRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * 订单列表查询
 *
 * @author: xiwen.z
 * create at: 2019/3/8 0008
 */
public interface OcBOrderListQueryCmd {


    /**
     * 查询订单列表
     *
     * @param jsonStr   jsonStr
     * @param loginUser User
     * @param pem       UserPermission
     * @return vh vh
     */
    ValueHolderV14<QueryOrderListResult> queryOrderList(String jsonStr, User loginUser, UserPermission pem);


    /**
     * 订单管理-列表查询
     *
     * @param usr   当前操作用户
     * @param param 查询参数
     * @return 数据结果集
     */
    ValueHolderV14 queryOrderList(User usr, String param);

    /**
     * 平台单号查询订单
     *
     * @param usr
     * @param param
     * @return
     */
    List<OcBOrder> queryOrder(User usr, String param);

    /**
     * 单据编号查询订单状态，库存中心用
     *
     * @param request
     * @return
     */
    ValueHolderV14<QueryOrderByBillNoResult> queryOrderByBillNo(OcBOederQueryByBillNoRequest request);

    /**
     * 单据编号查询订单状态，库存中心用
     *
     * @param request
     * @return
     */
    OcBOrder queryOrderEsByBillNo(String request);


    /**
     * 通过ES平台单号查询订单id(不包含其他column)
     *
     * @param usr   当前操作用户
     * @param param 查询参数
     * @return 数据结果集
     */
    QueryEsListResult queryOrderEsList(User usr, String param);
}
