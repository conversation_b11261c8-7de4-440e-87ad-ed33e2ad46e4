package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.model.SgBPhyInResultExt;
import com.jackrain.nea.oc.model.SgBPhyInResultItemExt;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * 零售退货单更新入库结果服务
 *
 * @author: xiWen.z
 * create at: 2019/5/9 0009
 */
public interface OcBRetailReturnUpdateRefundInCmd {

    /**
     * @param sgPir     结果单
     * @param inRstList 明细
     * @param usr       用户
     * @return VH
     */
    ValueHolderV14 retailOrderUpdateRefundIn(SgBPhyInResultExt sgPir, List<SgBPhyInResultItemExt> inRstList,
                                             User usr);
}
