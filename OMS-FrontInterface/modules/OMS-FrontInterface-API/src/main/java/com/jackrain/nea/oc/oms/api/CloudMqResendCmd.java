package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * Description： 手工消息发送
 * Author: RESET
 * Date: Created in 2020/8/16 18:21
 * Modified By:
 */
public interface CloudMqResendCmd {

    /**
     * 销退单发送消息
     *
     * @param returnOrderId
     * @param user
     * @return
     * @throws NDSException
     */
    ValueHolderV14 returnOrder2PosMqRetry(Long returnOrderId, User user) throws NDSException;

}
