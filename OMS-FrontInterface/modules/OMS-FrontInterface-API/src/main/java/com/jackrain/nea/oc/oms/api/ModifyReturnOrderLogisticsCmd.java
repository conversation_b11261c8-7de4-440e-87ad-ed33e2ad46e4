package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.request.UpdateReturnOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 批量修改物流公司
 *
 * @author: xiWen.z
 * create at: 2019/8/16 0016
 */
public interface ModifyReturnOrderLogisticsCmd {

    /**
     * 修改物流公司
     *
     * @param req UpdateReturnOrderRequest
     * @param usr user
     * @return vh
     */
    ValueHolderV14 modifyReturnOrderLogistics(UpdateReturnOrderRequest req, User usr);

    /**
     * 修改退回快递
     *
     * @param model     传入的参数
     * @param loginUser 当前用户
     * @return
     */
    ValueHolderV14 modifyReturnExpress(UpdateReturnOrderRequest model, User loginUser);

    /**
     * 预退货单传WMS服务
     *
     * @param param
     * @param loginUser
     * @return
     */
    ValueHolderV14 returnToWms(JSONObject param, User loginUser);
}
