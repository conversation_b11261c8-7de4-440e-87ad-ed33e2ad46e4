package com.jackrain.nea.oc.oms.api;


import com.jackrain.nea.oc.oms.model.request.OrderMarkingRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 订单打标
 *
 * @date 2022/6/14
 * @author: ming.fz
 */
public interface OcBOrderMarkingCmd {

    ValueHolderV14 orderMarking(OrderMarkingRequest param, User user);

    ValueHolderV14 clearMarking(OrderMarkingRequest param, User user);
}
