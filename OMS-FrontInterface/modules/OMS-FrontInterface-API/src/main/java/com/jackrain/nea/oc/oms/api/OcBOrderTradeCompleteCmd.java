package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.request.OrderICheckStopRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * 订单交易完成
 *
 * <AUTHOR>
 */
public interface OcBOrderTradeCompleteCmd {

    ValueHolderV14 tradeComplete(List<Long> ids, User user) throws NDSException;
}
