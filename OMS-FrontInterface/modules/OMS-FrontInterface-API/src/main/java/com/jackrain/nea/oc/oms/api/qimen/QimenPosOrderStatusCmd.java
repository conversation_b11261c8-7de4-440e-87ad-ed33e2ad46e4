package com.jackrain.nea.oc.oms.api.qimen;


import com.jackrain.nea.oc.oms.model.request.QimenPosOrderStatusRequest;
import com.jackrain.nea.oc.oms.model.result.QimenPosOrderStatusResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 奇门POS-订单状态查询接口
 *
 * @Auther: 黄志优
 * @Date: 2020/8/30 12:57
 * @Description:
 */
public interface QimenPosOrderStatusCmd {

    /**
     * 查询订单状态
     *
     * @param qimenPosOrderStatusRequest 查询参数
     * @return QimenPosOrderStatusResult 订单状态
     */
    ValueHolderV14<QimenPosOrderStatusResult> queryOrderStatus(QimenPosOrderStatusRequest qimenPosOrderStatusRequest);


    /**
     * 查询订单状态
     *
     * @param request 查询参数
     * @return user 用户
     */
    ValueHolderV14 queryOrderStatus(QimenPosOrderStatusRequest request, User user);
}
