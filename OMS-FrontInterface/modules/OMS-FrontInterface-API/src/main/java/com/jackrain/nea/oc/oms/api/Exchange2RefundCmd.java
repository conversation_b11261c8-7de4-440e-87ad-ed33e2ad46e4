package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * @Author: lyj
 * @Date: 2021/10/13
 * @Version 1.0
 */
public interface Exchange2RefundCmd {

    /**
     * 退货转换货
     *
     * @param obj
     * @param user
     * @return
     */
    ValueHolderV14 exchange2RefundCmd(JSONObject obj, User user) throws NDSException;

}
