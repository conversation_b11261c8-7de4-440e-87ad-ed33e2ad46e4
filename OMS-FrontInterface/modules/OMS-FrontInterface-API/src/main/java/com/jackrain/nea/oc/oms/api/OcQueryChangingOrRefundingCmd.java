package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryChangingOrRefundingResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 退换货列表查询服务
 *
 * @author: 郑立轩
 * @since: 2019/3/11
 * create at : 2019/3/11 13:21
 */
public interface OcQueryChangingOrRefundingCmd {

    ValueHolderV14<QueryChangingOrRefundingResult> queryChangingOrRefunding(UserPermission usrPem, JSONObject object, User user);

}
