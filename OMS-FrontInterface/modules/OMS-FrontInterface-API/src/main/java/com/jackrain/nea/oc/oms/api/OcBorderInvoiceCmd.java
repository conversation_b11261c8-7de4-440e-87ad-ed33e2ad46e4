package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.request.SaveInvoiceRequest;
import com.jackrain.nea.oc.oms.model.request.SaveRecordInvoiceRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 订单开票
 *
 * @author: xiWen.z
 * create at: 2019/7/23 0023
 */
public interface OcBorderInvoiceCmd {

    /**
     * 订单开票.添加.校验
     *
     * @param saveInvoiceRequest SaveInvoiceRequest
     * @param loginUser          User
     * @return vh14
     */
    ValueHolderV14 checkAddOrderInvoicing(SaveInvoiceRequest saveInvoiceRequest, User loginUser);

    /**
     * 记录开票.校验
     *
     * @param paramObj  Oc_b_order.id
     * @param loginUser User
     * @return vh14
     */
    ValueHolderV14 checkRecordInvoice(JSONObject paramObj, User loginUser);

    /**
     * 记录开票.新增
     *
     * @param ocBOrderInvoiceInform SaveRecordInvoiceRequest
     * @param loginUser             User
     * @return vh14
     */
    ValueHolderV14 recordOrderInvoicing(SaveRecordInvoiceRequest ocBOrderInvoiceInform, User loginUser);
}
