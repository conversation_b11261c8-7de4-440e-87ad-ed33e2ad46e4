package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * @Author: qinjunlong
 * @Date: 2020/12/02 12:40
 * @Version 1.0
 */
public interface Refund2ExchangeCmd {

    /**
     * 退货转换货
     *
     * @param obj
     * @param user
     * @return
     */
    ValueHolder refund2ExchangeCmd(JSONObject obj, User user) throws NDSException;


    /**
     * 退转换前置校验
     *
     * @param obj
     * @return
     */
    ValueHolder validate(JSONObject obj) throws NDSException;

}
