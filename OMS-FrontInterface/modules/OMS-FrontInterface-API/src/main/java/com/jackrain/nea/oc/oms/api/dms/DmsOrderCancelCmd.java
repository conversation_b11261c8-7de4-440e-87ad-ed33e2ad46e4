package com.jackrain.nea.oc.oms.api.dms;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * dms单据取消
 *
 * <AUTHOR>
 */
public interface DmsOrderCancelCmd {

    /**
     * 取消订单
     *
     * @param params
     * @return
     */
    ValueHolderV14 cancelOrder(JSONObject params);

    /**
     * 取消退单
     *
     * @param params
     * @return
     */
    ValueHolderV14 cancelReturn(JSONObject params);

}
