package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: huang.z<PERSON><PERSON>
 * @create: 2019-07-23 21:30
 */
public interface InvoiceTableQueryCmd {

    ValueHolderV14 queryByShop(JSONObject obj);

    ValueHolderV14 queryInvoiceItem(JSONObject obj);

    ValueHolderV14 queryInvoiceNoticeInfo(JSONObject obj);

    ValueHolderV14 selectInvoiceNoticeList(JSONObject obj);
}
