package com.jackrain.nea.oc.oms.api.patrol;

/**
 * 线上巡查接口
 *
 * @author: 夏继超
 * @since: 2019/6/4
 * create at : 2019/6/4 11:22
 */
public interface OnLineForCheckCmd {
    /**
     * 没有明细《占单》       （待分配 ，没有明细）
     *
     * @return
     */
    String onCheck1();

    /**
     * 中间表重复《转单》
     *
     * @return
     */
    String onCheck2();

    /**
     * 订单不在等待卖家同意退款状态，非人工拦截处于拦截状态的（明细状态和头表的拦截状态   标记退款完整、退款转换、订单拦截）？？？
     *
     * @return
     */
    String onCheck3();
}
