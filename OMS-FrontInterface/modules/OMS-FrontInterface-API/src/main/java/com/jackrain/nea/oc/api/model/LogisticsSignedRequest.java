package com.jackrain.nea.oc.api.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2024-05-13
 * @desc 物流签收信息请求
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "LogisticsSignedRequest", description = "物流签收信息请求")
public class LogisticsSignedRequest extends BasePageRequest {
    private static final long serialVersionUID = -461644854987286547L;

    /**
     * 快递单号
     */
    @ApiModelProperty(value = "快递单号")
    @JSONField(name = "LOGISTICS_CODE")
    private String logisticsCode;

    /**
     * 物流公司编码
     */
    @ApiModelProperty(value = "物流公司编码")
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    /**
     * 物流公司名称
     */
    @ApiModelProperty(value = "物流公司名称")
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间", required = true)
    @JSONField(name = "SIGNING_TIME")
    private Date signingTime;

    /**
     * 物流状态：3-签收，4-退签
     */
    @ApiModelProperty(value = "物流状态：3-签收，4-退签", required = true)
    @JSONField(name = "STATE")
    private Integer state;
}
