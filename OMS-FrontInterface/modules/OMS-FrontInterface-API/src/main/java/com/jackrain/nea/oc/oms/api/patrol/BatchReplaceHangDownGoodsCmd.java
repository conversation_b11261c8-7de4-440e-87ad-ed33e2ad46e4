package com.jackrain.nea.oc.oms.api.patrol;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @Author: 黄世新
 * @Date: 2020/3/24 1:31 下午
 * @Version 1.0
 */
public interface BatchReplaceHangDownGoodsCmd {

    ValueHolderV14 batchReplaceHangDownGoods(JSONObject param, User user, UserPermission usrPem);

}
