package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 11:16
 **/
public interface OcBReturnOrderExportCmd {

    /**
     * 返回导出结果
     */
    public ValueHolderV14 exportList(JSONObject object, User user);

    /**
     * 下载模板
     */
    public ValueHolderV14 downloadTemp();

    /**
     * 退换货订单导出
     *
     * @param object jsonObject
     * @param user   user
     * @return vh
     */
    ValueHolderV14 exportReturnOrder(JSONObject object, User user, UserPermission usrPem);
}
