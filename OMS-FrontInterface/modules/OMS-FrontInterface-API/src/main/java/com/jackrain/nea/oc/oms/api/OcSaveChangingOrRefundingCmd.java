package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * @author: 李杰
 * @since: 2019/3/12
 * create at : 2019/3/12 10:14
 */
public interface OcSaveChangingOrRefundingCmd extends Command {
    /**
     * 退换货订单新增
     *
     * @param obj
     * @param user
     * @return
     */
    ValueHolder saveReturnOrder(JSONObject obj, User user) throws NDSException;

    /**
     * 换货新增判断库存检查
     *
     * @param obj
     * @param user
     * @return
     */
    ValueHolderV14 checkAllStroreStock(JSONObject obj, User user) throws NDSException;

    /**
     * @param obj
     * @param user
     * @return
     */
    ValueHolderV14 addReturnOrderItem(JSONObject obj, User user);

    /**
     * @param obj
     * @param user
     * @return
     */
    ValueHolderV14 deleteReturnOrderItem(JSONObject obj, User user);
}
