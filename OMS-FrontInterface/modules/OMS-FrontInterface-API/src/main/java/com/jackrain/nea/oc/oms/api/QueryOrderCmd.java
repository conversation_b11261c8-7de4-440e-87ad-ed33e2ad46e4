package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.request.OcBOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * 订单查询
 *
 * @Author: qinjunlong
 * @Date: 2020-08-05 13:32
 * @Version 1.0
 */
public interface QueryOrderCmd {

    /**
     * @param ocBOrderRequest
     * @return
     */
    ValueHolderV14<List<OcBOrder>> queryOrderByRequest(OcBOrderRequest ocBOrderRequest);

}
