package com.jackrain.nea.oc.oms.api.st;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 对等换货策略明细删除命令接口
 **/
public interface StCEquityBarterStrategyItemDeleteCmd {

    /**
     * 删除对等换货策略明细
     *
     * @param shopType
     * @param shopIds
     * @param exchangeSkuId
     * @param exchangeQty
     * @param equitySkuId
     * @param equityQty
     * @param user
     * @return
     */
    ValueHolderV14<Void> deleteStrategyItems(Integer shopType, List<Long> shopIds, Long exchangeSkuId,
                                             BigDecimal exchangeQty, Long equitySkuId, BigDecimal equityQty, User user);
}
