package com.jackrain.nea.oc.oms.api.sap;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 *
 * @Desc :
 * <AUTHOR>
 * @Date : 2022/6/29
 */
public interface SapOcOrderApiCmd {

    /**
     * pod签收
     *
     * @param params params
     * @return v14
     */
    ValueHolderV14 ocOrderSignFor(JSONObject params);

    ValueHolderV14 sapOcOrder(JSONObject params);
}
