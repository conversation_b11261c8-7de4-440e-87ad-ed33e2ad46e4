package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @ClassName MiniptOrderCmd
 * @Description
 * @Date 2022/8/25 下午4:40
 * @Created by wuhang
 */
public interface MiniptOrderCmd {

    /**
     * 小平台订单取消 (单创)
     * @param sourceCode 平台单号
     * @param user
     * @param msg
     * @return
     */
    ValueHolderV14 cancelOrderForward(String sourceCode, User user, String msg);

    /**
     * 小平台订单取消 (单创) 小平台订单逆向取消走OcCancelChangingOrRefundCmd.qmRefundService
     * @param orderId
     * @param user
     * @param msg
     * @return
     */
    ValueHolderV14 cancelOrderReverse(String tid, User user);
}
