package com.jackrain.nea.oc.oms.api.ac;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author:洪艺安
 * @since: 2019/7/12
 * @create at : 2019/7/12 10:22
 */
public interface OmsPayableAdjustmentExportCmd extends Command {
    /**
     * 导入应付款调整单
     * @return
     */
    public ValueHolderV14 exportPayableAdjustment(JSONObject obj, User user);
}
