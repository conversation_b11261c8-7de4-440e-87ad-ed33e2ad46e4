package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.vo.OcBOrderImpBeefOrderVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: 江家雷
 * @create: 2020-11-20 17:13
 **/
public interface OcBOrderImportBeefOrderCmd {

    /**
     * 导入订单
     */
    ValueHolderV14<String> importOrderList(List<OcBOrderImpBeefOrderVO> ocBOrderList, User user, String origFileName);

    /**
     * 异常错误信息导出
     *
     * @param ocBOrderList 订单列表信息
     * @param user         用户信息
     * @return
     */
    String exportImpErrorResult(List<OcBOrderImpBeefOrderVO> ocBOrderList, User user, String origFileName);


}
