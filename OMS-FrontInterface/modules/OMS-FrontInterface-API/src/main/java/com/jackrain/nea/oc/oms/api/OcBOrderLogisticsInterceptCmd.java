package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.util.ValueHolder;

/**
 * <p>OcBOrderLogisticsIntercept dubbo</p>
 *
 * <AUTHOR>
 * @since 2023/3/23
 */
public interface OcBOrderLogisticsInterceptCmd {
    /**
     * 根据物流单号和主键ID更新物流拦截状态
     *
     * @param id            主键ID
     * @param logisticsCode 物流单号
     * @param interceptStatus 拦截状态
     * @return 更新结果
     */
    ValueHolder updateInterceptStatusByIdAndLogisticsCode(Long id, String logisticsCode, Integer interceptStatus);
}
