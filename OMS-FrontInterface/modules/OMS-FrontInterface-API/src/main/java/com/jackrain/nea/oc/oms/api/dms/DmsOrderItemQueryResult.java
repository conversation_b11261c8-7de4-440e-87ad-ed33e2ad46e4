package com.jackrain.nea.oc.oms.api.dms;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName DmsOrderItemQueryResult
 * @Description
 * <AUTHOR>
 * @Date 2025/3/18 10:52
 * @Version 1.0
 */
@Data
public class DmsOrderItemQueryResult implements Serializable {

    private static final long serialVersionUID = 6026474108771148689L;
    private String skuECode;

    private Integer num;

    private List<DmsOrderItemBatchResult> batch;
}
