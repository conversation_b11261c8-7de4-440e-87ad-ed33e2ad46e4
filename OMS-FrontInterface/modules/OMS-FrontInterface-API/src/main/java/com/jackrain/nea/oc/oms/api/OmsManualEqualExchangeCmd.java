package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.dto.EqualExchangeStInfo;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/7/11 下午2:54
 * @Version 1.0
 */
public interface OmsManualEqualExchangeCmd {

    /**
     * 查询勾选的信息
     * @param orderIds
     * @return
     */
    ValueHolderV14 selectEqualExchangeOrder(List<Long> orderIds);

    /**
     * 查询当前skuID对等换货策略信息
     *
     * @param skuId
     * @return
     */
    ValueHolderV14<List<EqualExchangeStInfo>> selectEqualExchange(String skuId);


    /**
     * 确认手动执行
     * @param exchangeStInfos
     * @param user
     * @return
     */
    ValueHolderV14 confirmEqualExchange(EqualExchangeStInfo exchangeStInfos, User user);


}
