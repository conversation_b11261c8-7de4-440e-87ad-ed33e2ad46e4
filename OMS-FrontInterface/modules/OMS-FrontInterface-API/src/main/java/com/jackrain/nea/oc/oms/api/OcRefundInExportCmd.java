package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 退货入库导出接口
 *
 * @author: 夏继超
 * @since: 2019/6/6
 * create at : 2019/6/6 14:49
 */
public interface OcRefundInExportCmd {
    /**
     * @param jsonStr   传入的参数
     * @param loginUser 当前登录用户
     * @return
     */
    ValueHolderV14 exportList(String jsonStr, User loginUser);

    /**
     * 退货入库单导出
     *
     * @param jsonStr   string
     * @param loginUser user
     * @return vh
     */
    ValueHolderV14 exportRefundIn(String jsonStr, User loginUser);
}
