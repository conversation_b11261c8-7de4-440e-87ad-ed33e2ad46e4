package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: 孙继东
 * @since: 2019-03-11
 * create at : 2019-03-11 10:48
 */
public interface UpdateOrderInfoCmd {
    ValueHolderV14 checkOrder(List<Long> ids, int flag);

    ValueHolderV14 updateLogistics(List<Long> ids, Long cLogisticsId, User loginUser) throws NDSException;

    ValueHolderV14 updateWarehouse(List<Long> ids, Long shareStoresId, String shareStoresEcode, Long warehouseId, String warehouseEcode, String updateRemark, boolean isBatch, User loginUser) throws NDSException;
    
    JSONObject queryList(User user, JSONObject jsn);

    ValueHolderV14 reallocateLogistics(String jsonStr, User user, UserPermission pem);

    ValueHolderV14 reallocateWarehouse(String jsonStr, User user, UserPermission pem);

    ValueHolderV14 queryOmsShopStorage(JSONObject jsonObject, User loginUser);

    ValueHolderV14 queryOmsWarehouse(JSONObject jsonObject, User loginUser);

    /**
     * 重新寻物流
     * @param ids ids
     * @param loginUser loginUser
     * @return
     * @throws NDSException
     */
    ValueHolderV14 reDistributionLogistics(List<Long> ids, User loginUser) throws NDSException;
}
