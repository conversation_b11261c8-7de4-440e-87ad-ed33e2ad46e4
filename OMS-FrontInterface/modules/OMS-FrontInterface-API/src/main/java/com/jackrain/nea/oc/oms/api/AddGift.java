package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

/**
 * @author: 夏继超
 * @since: 2019/3/12
 * create at : 2019/3/12 20:48
 */
public interface AddGift {
    /**
     * 新增赠品
     *
     * @param param     传入的参数
     * @param loginUser 当前登录的用户
     * @return 返回的数据
     */
    ValueHolder addGift(JSONObject param, User loginUser) throws NDSException;
}
