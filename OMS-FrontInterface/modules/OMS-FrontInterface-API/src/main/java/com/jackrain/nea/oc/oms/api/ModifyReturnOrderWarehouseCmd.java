package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.UpdateReturnOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * @author: xiWen.z
 * create at: 2019/7/16 0016
 */
public interface ModifyReturnOrderWarehouseCmd {

    /**
     * @param req
     * @param usr
     * @return
     */
    ValueHolderV14 modifyReturnOrderWarehouse(UpdateReturnOrderRequest req, User usr);
}
