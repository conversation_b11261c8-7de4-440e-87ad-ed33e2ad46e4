package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/18
 */
public interface OmsTaskOrderCmd {


    /**
     * modify task order status
     *
     * @param shardKey
     * @param tableName
     * @param status
     * @return
     */
    ValueHolderV14 modifyTaskOrderStatus(List<Long> shardKey, String tableName, Integer status);


}
