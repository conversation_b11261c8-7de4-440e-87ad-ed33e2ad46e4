package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.api.model.LogisticsSignedRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR>
 * @create 2024-05-13
 * @desc 物流签收信息处理
 **/
public interface LogisticsSignedFiCmd {

    /**
     * 处理已签收的物流单号
     *
     * @param logisticsSignedRequest 物流签收信息请求
     * @return 处理结果
     */
    ValueHolderV14 processSignedLogistics(LogisticsSignedRequest logisticsSignedRequest);

    /**
     * 处理退回状态的物流单号（仅维护withdrawalTime字段）
     *
     * @param logisticsSignedRequest 物流签收信息请求
     * @return 处理结果
     */
    ValueHolderV14 processReturnBackLogistics(LogisticsSignedRequest logisticsSignedRequest);
}
