package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.Command;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @author: 李杰
 * @since: 2019/3/11
 * create at : 2019/3/11 10:57
 */
public interface RemarkUpdateCmd extends Command {

    ValueHolder updateRemark(JSONObject obj, User user) throws NDSException;

    ValueHolder reUpdateRemark(JSONObject obj, User user) throws NDSException;

    /**
     * 退单修改卖家备注
     *
     * @param obj  入参
     * @param user
     * @return
     * @throws NDSException
     */
    ValueHolder reUpdateSellerRemark(JSONObject obj, User user) throws NDSException;

    /**
     * 批量导入修改备注
     *
     * @param param      订单主表对象
     * @param sourceCode 平台单号
     * @param cover
     * @param user       当前登录用户
     * @return
     */
    ValueHolderV14 batchImport(List<OcBOrder> param, JSONArray sourceCode, Boolean cover, User user);

    /**
     * <AUTHOR>
     * @Date 15:41 2021/8/13
     * @Description  批量修改内部备注
     */
    ValueHolder bacthUpdateInsideRemark(JSONObject obj, User user);
}
