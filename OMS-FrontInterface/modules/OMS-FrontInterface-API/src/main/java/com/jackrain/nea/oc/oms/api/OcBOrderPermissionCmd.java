package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.web.face.User;

/**
 * @author: xiWen.z
 * create at: 2019/8/26 0026
 */

@Deprecated
public interface OcBOrderPermissionCmd {

    /**
     * 当前用户权限.获取
     *
     * @param pem UserPermission
     * @param tn  table name
     * @param usr user
     * @return UserPermission
     */
    UserPermission getCurrentUserPermission(UserPermission pem, String tn, User usr);

}
