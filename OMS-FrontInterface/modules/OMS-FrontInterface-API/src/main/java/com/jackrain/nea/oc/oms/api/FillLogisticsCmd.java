package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 申请补充物流信息
 * @date 2021/12/8 11:09
 */
public interface FillLogisticsCmd {


    /**
     * 申请补充物流信息
     *
     * @param obj
     * @param user
     * @return
     */
    ValueHolderV14 fillLogistics(JSONObject obj, User user) throws NDSException;
}
