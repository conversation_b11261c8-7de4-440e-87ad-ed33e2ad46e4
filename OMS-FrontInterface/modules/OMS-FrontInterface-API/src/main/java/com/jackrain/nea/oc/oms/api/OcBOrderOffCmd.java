package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 取消订单
 *
 * @date 2019/3/7
 * @author: ming.fz
 */
public interface OcBOrderOffCmd {

    /**
     * 中台调用取消
     *
     * @param param
     * @param user
     * @return
     * @throws NDSException
     */
    ValueHolderV14 updateStatus(OrderICheckRequest param, User user) throws NDSException;

    /**
     * 奇门调用取消
     *
     * @param obj
     * @param user
     * @return
     */
    ValueHolderV14 qmOrderService(JSONObject obj, User user);

}
