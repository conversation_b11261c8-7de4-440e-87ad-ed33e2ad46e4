package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcbOrderMergeMenuCmd;
import com.jackrain.nea.oc.oms.services.OcbCancelOrderMergeService;
import com.jackrain.nea.oc.oms.services.OcbOrderMergeMenuService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-07-04 09:44
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcbOrderMergeMenuImpl extends CommandAdapter implements OcbOrderMergeMenuCmd {

    @Autowired
    private OcbOrderMergeMenuService ocbOrderMergeMenuService;
    @Autowired
    private OcbCancelOrderMergeService ocbCancelOrderMergeService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ocbOrderMergeMenuService.mergeOrder(session);
    }

    @Override
    public ValueHolderV14 cancelMergeOrder(User user, List<Long> orderIds) {
        return ocbCancelOrderMergeService.cancelMergeOrder(user, orderIds);
    }

    @Override
    public ValueHolder mergeOrderOne(QuerySession session) throws NDSException {
        return ocbOrderMergeMenuService.mergeOrderOne(session);
    }
}
