package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.DeleteGift;
import com.jackrain.nea.oc.oms.services.DeleteGiftService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 删除赠品
 *
 * @author: 夏继超
 * @since: 2019/3/11
 * create at : 2019/3/11 14:51
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class DeleteGiftImpl implements DeleteGift {
    @Autowired
    DeleteGiftService deleteGiftService;
  /*  @Override
    public ValueHolderV14<DeleteGiftResult> deleteGift(Long orderId, List<Long> ids, User loginUser) {
        return deleteGiftService.deleteGift(orderId, ids, loginUser);
    }*/

    @Override
    public ValueHolderV14 deleteGift(JSONObject obj, User loginUser) throws NDSException {
        return deleteGiftService.deleteGift1(obj, loginUser);
    }
}
