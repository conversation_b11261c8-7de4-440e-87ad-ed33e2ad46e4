package com.jackrain.nea.oc.services.naika;

import com.alibaba.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.naika.OrderNaiKaQueryCmd;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaOrderQueryService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName OrderNaiKaQueryCmdImpl
 * @Description 奶卡管理页面查询
 * <AUTHOR>
 * @Date 2022/7/21 18:10
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderNaiKaQueryCmdImpl extends CommandAdapter implements OrderNaiKaQueryCmd {

    @Autowired
    private OmsNaiKaOrderQueryService queryService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return queryService.naiKaOrderQuery(session);
    }
}
