package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderManualDeliveryCmd;
import com.jackrain.nea.oc.oms.services.OmsOrderManualDeliveryOrderService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 手工修改为平台发货
 * @Date 2020/1/6
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderManualDeliveryCmdImpl implements OcBOrderManualDeliveryCmd {

    @Autowired
    private OmsOrderManualDeliveryOrderService omsOrderManualDeliveryOrderService;

    @Override
    public ValueHolderV14 orderManualDelivery(JSONObject param, User user) throws NDSException {
        return omsOrderManualDeliveryOrderService.doManualDeliveryOrder(param, user);
    }

}