package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBStorePriceControlSaveCmd;
import com.jackrain.nea.oc.oms.api.OcBStoreRefundInDelCmd;
import com.jackrain.nea.oc.oms.services.OcBStorePriceControlService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.xml.ws.Action;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/1/17 18:17
 */

@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBStorePriceControlSaveCmdImpl extends CommandAdapter implements OcBStorePriceControlSaveCmd {

    @Autowired
    private OcBStorePriceControlService ocBStorePriceControlService;


    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ocBStorePriceControlService.save(session);
    }
}
