package com.jackrain.nea.oc.services.st;

import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCHoldOrderReasonDeleteCmd;
import com.jackrain.nea.st.service.StCHoldOrderReasonDeleteService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 秦雄飞
 * @time: 2023/2/15 16:46
 * @description: hold单原因删除
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCHoldOrderReasonDeleteCmdImpl extends CommandAdapter implements StCHoldOrderReasonDeleteCmd {
    @Autowired
    private StCHoldOrderReasonDeleteService stCHoldOrderReasonDeleteService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        return stCHoldOrderReasonDeleteService.deleteHoldOrderReason(request);
    }
}

