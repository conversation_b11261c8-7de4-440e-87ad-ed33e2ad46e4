package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.RemarkUpdateCmd;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.RemarkUpdateService;
import com.jackrain.nea.oc.oms.services.ReturnUpdateRemarkService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 李杰
 * @since: 2019/3/11
 * create at : 2019/3/11 11:05
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RemarkUpdateCmdImpl extends CommandAdapter implements RemarkUpdateCmd {

    @Autowired
    private RemarkUpdateService remarkUpdateservice;
    @Autowired
    private ReturnUpdateRemarkService returnUpdateRemarkService;

    @Override
    public ValueHolder updateRemark(JSONObject obj, User user) throws NDSException {
        return remarkUpdateservice.remarkUpdate(obj, user);
    }

    @Override
    public ValueHolder reUpdateRemark(JSONObject obj, User user) throws NDSException {
        return returnUpdateRemarkService.remarkUpdate(obj, user);
    }

    @Override
    public ValueHolder reUpdateSellerRemark(JSONObject obj, User user) throws NDSException {
        return returnUpdateRemarkService.reUpdateSellerRemark(obj, user);
    }

    @Override
    public ValueHolderV14 batchImport(List<OcBOrder> param, JSONArray sourceCode, Boolean cover, User user) {
        return returnUpdateRemarkService.batchImport1(param, sourceCode, cover, user);
    }

    @Override
    public ValueHolder bacthUpdateInsideRemark(JSONObject obj, User user) {
        return remarkUpdateservice.bacthUpdateInsideRemark(obj, user);
    }


}
