package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.MarkRefundCmd;
import com.jackrain.nea.oc.oms.services.MarkRefundCompleteService;
import com.jackrain.nea.oc.oms.services.OmsMarkCancelService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 周琳胜
 * @since: 2019/3/11
 * create at : 2019/3/11 11:18
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class MarkRefundCmdImpl implements MarkRefundCmd {

    @Autowired
    private MarkRefundCompleteService refundCompleteService;
    @Autowired
    private OmsMarkCancelService omsMarkCancelService;

    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        return omsMarkCancelService.markRefund(obj, user);
    }

    @Override
    public ValueHolder markRefundCancel(Long orderId, List<Long> itemIds, User user) {
        if (CollectionUtils.isEmpty(itemIds)) {
            throw new NDSException("参数不正确");
        }
        return refundCompleteService.markRefundCancel(orderId, itemIds, user);
    }

    @Override
    public ValueHolderV14 naiKaOrderCancel(String tid) {
        return omsMarkCancelService.cancelNaiKaOrder(tid);
    }

    @Override
    public ValueHolderV14 naiKaPartOrderCancel(String tid, List<String> oid) {
        return omsMarkCancelService.cancelNaiKaPartOrder(tid, oid);
    }
}
