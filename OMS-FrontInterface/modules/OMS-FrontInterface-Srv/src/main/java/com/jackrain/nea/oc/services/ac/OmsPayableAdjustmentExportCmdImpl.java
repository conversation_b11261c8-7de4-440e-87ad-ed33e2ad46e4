package com.jackrain.nea.oc.services.ac;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ac.service.PayableAdjustmentExportService;
import com.jackrain.nea.oc.oms.api.ac.OmsPayableAdjustmentExportCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author:洪艺安
 * @since: 2019/7/12
 * @create at : 2019/7/12 10:25
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsPayableAdjustmentExportCmdImpl extends CommandAdapter implements OmsPayableAdjustmentExportCmd {
    @Autowired
    private PayableAdjustmentExportService payableAdjustmentExportService;

    @Override
    public ValueHolderV14 exportPayableAdjustment(JSONObject obj, User user) {
        return payableAdjustmentExportService.exportPayableAdjustment(obj,user);
    }
}
