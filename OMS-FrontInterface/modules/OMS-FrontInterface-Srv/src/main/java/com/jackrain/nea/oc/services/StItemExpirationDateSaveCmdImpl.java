package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StItemExpirationDateSaveCmd;
import com.jackrain.nea.st.service.StItemExpirationDataSaveService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName StItemExpirationDateSaveCmdImpl
 * @Description 商品指定效期策略保存
 * <AUTHOR>
 * @Date 2022/6/9 14:57
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oms-fi", version = "1.0")
public class StItemExpirationDateSaveCmdImpl extends CommandAdapter implements StItemExpirationDateSaveCmd {

    @Autowired
    private StItemExpirationDataSaveService stItemExpirationDataSaveService;

    @Override
     public ValueHolder execute(QuerySession querySession) throws NDSException {

         return stItemExpirationDataSaveService.execute(querySession);
     }
}
