package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.BillCmd;
import com.jackrain.nea.oc.oms.services.SaveBillService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: wangqiang
 * @Date: 2019-03-08 13:34
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class SaveBillCmdImpl extends CommandAdapter implements BillCmd {

    @Autowired
    SaveBillService saveBillService;

    @Override
    public ValueHolder saveBill(J<PERSON>NObject obj, User user) {
        return saveBillService.saveBill(obj, user, Boolean.FALSE);
    }

    @Override
    public ValueHolder updateIsLackstock(JSONObject object, User user) {
        return saveBillService.updateIsLackstock(object, user);
    }
}
