package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.LogisticsTrajectoryCmd;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.request.kdzs.KdzsCallBackRequest;
import com.jackrain.nea.oc.oms.model.result.kdzs.KdzsCallBackResponse;
import com.jackrain.nea.oc.oms.services.OcBOrderLogisticsTrajectoryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.Base64;

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class LogisticsTrajectoryCmdImpl implements LogisticsTrajectoryCmd {

    @Autowired
    OcBOrderLogisticsTrajectoryService logisticsTrajectory;

    @Override
    public ValueHolderV14 insterLogisticsTrajectory(JSONObject jsonObject) {
        return logisticsTrajectory.insterLogisticsTrajectory(jsonObject);
    }

}


