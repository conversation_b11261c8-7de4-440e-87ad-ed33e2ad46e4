package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.ScanIncomingCmd;
import com.jackrain.nea.oc.oms.services.ScanIncomingService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 孙俊磊
 * @since: 2019-03-24
 * create at:  2019-03-24 18:30
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ScanIncomingCmdImpl implements ScanIncomingCmd {

    @Autowired
    private ScanIncomingService scanIncomingService;

    @Override
    public ValueHolderV14 saveScanIncoming(String param, User loginUser) {
        return scanIncomingService.saveRefundOrder(param, loginUser);
    }

    @Override
    public ValueHolderV14 getScanIncomingInfo(String param, User loginUser) {
        return scanIncomingService.getScanIncomingInfo(param, loginUser);
    }

    @Override
    public ValueHolderV14 getOneRefundItem(String param, User loginUser) {
        return scanIncomingService.getOneRefundItem(param, loginUser);
    }

    @Override
    public ValueHolderV14 getCurrentBatch(User loginUser) {
        return scanIncomingService.getCurrentBatch(loginUser);
    }


    @Override
    public ValueHolderV14 getLogicalWarehouseInfo(Long batchId) {
        return scanIncomingService.getLogicalWarehouseInfo(batchId);
    }
}
