package com.jackrain.nea.oc.services.sap;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.sap.OcBSapSalesDataGatherVoidR3Cmd;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataGatherVoidService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> WANGJUN
 * @Date : 2022/8/24
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBSapSalesDataGatherVoidCmdImpl extends CommandAdapter implements OcBSapSalesDataGatherVoidR3Cmd {
    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        OcBSapSalesDataGatherVoidService bean = ApplicationContextHandle.getBean(OcBSapSalesDataGatherVoidService.class);
        return bean.voidSalesDataGather(session);
    }

}
