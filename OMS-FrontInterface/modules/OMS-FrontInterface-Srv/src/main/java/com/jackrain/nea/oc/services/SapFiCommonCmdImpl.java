package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.SapFiCommonCmd;
import com.jackrain.nea.oc.oms.model.currency.SapRefundModel;
import com.jackrain.nea.oc.oms.services.refund.SapRefundService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2019/3/13
 * create at : 2019/3/13 16:01
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class SapFiCommonCmdImpl implements SapFiCommonCmd {

    @Autowired
    private SapRefundService sapRefundService;

    @Override
    public ValueHolderV14 validateCanRefund(SapRefundModel sapRefundModel) {
        return sapRefundService.validateCanRefund(sapRefundModel);
    }
}
