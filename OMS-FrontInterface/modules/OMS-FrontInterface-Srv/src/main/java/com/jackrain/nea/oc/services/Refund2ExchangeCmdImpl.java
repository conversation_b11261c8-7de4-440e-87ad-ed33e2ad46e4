package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.Refund2ExchangeCmd;
import com.jackrain.nea.oc.oms.services.Refund2ExchangeService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: qinjunlong
 * @since: 2020/12/02
 * create at : 2020/12/02 12:40
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class Refund2ExchangeCmdImpl extends CommandAdapter implements Refund2ExchangeCmd {
    @Autowired
    private Refund2ExchangeService service;

    @Override
    public ValueHolder refund2ExchangeCmd(JSONObject obj, User user) throws NDSException {
        return service.refund2Exchange(obj, user);
    }

    @Override
    public ValueHolder validate(JSONObject obj) throws NDSException {
        return service.validate(obj);
    }
}
