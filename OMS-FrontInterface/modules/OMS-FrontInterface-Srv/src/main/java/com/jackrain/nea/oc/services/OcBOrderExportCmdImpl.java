package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBOrderExportCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.OcBOrderExportResult;
import com.jackrain.nea.oc.oms.services.BatchExportOcBOrderService;
import com.jackrain.nea.oc.oms.services.OcBOrderExportService;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 李龙飞
 * @create: 2019-05-14 13:30
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderExportCmdImpl implements OcBOrderExportCmd {
    @Autowired
    private OcBOrderExportService ocBOrderExportService;
    @Autowired
    private ExportUtil exportUtil;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    @Override
    public ValueHolderV14 exportList(String jsonStr, User loginUser) {
        ValueHolderV14 holderV14 = ocBOrderExportService.exportList(jsonStr, loginUser);
        if (holderV14.getCode() == ResultCode.SUCCESS && holderV14.getData() != null) {
            OcBOrderExportResult data = (OcBOrderExportResult) holderV14.getData();
            List<OcBOrderExtend> ocBOrderList = data.getOcBOrderList();
            List<OcBOrderItemExtend> ocBOrderItemList = data.getOcBOrderItemList();

            exportUtil.setEndpoint(this.endpoint);
            exportUtil.setAccessKeyId(this.accessKeyId);
            exportUtil.setAccessKeySecret(this.accessKeySecret);
            exportUtil.setBucketName(this.bucketName);
            if (StringUtils.isEmpty(timeout)) {
                //如果获取不到apllo配置参数，设置默认过期时间为30分钟
                timeout = "1800000";
            }
            exportUtil.setTimeout(this.timeout);
            /**
             *  拼接Excel主表sheet表头字段和列表
             * */
            /*String orderNames[] = {"订单标识","订单编号", "单据编号", "平台编号", "下单店铺", "单据状态", "平台", "发货仓库", "买家昵称", "退换货单", "下单日期", "付款方式", "物流公司", "物流单号",
                    "商品重量(KG)", "商品金额", "配送费用", "调整金额", "服务费", "订单优惠金额", "商品优惠金额", "总金额", "已支付金额", "代收(COD)货款", "代销结算金额", "操作费",
                    "代销运费", "收货人", "收货人手机", "电话", "邮编", "省", "市", "区", "邮费(未知)", "收货人地址", "买家备注", "卖家备注", "系统备注"};*/

            String[] orderNames = {"订单标识", "订单状态", "付款时间", "订单编号", "平台", "下单店铺", "商品总数", "缺货数量", "付款方式", "平台单号", "省", "市", "区", "收货人地址", "订单优惠金额", "卖家备注", "买家留言", "买家昵称", "物流公司", "物流单号", "创建时间", "创建人", "审核时间", "配货时间", "出库时间", "发货仓库", "订单类型", "wms撤回状态", "退货状态", "商品优惠信息", "商品总额", "调整金额", "配送费用", "服务费", "订单总额", "已收金额", "代销结算金额", "代销运费", "应收金额", "应收平台金额（京东）", "京东结算金额", "到付代收金额", "系统备注", "开票抬头", "开票内容", "订单补充信息"};
            String[] orderKeys = {"orderTag", "orderStatusName", "payTime", "id", "platformName", "cpCShopTitle", "qtyAll", "totQtyLost", "payTypeName", "sourceCode", "cpCRegionProvinceEname", "cpCRegionCityEname",
                    "cpCRegionAreaEname", "cptAddress", "orderDiscountAmt", "sellerMemo", "buyerMessage", "userNick", "cpCLogisticsEname", "expresscode", "creationdate", "ownerename", "auditTime", "distributionTime", "scanTime", "cpCPhyWarehouseEname", "orderTypeName", "wmsCancelStatusName", "returnStatusName", "productDiscountAmt", "productAmt", "adjustAmt", "shipAmt", "serviceAmt", "orderAmt", "receivedAmt", "consignAmt", "consignShipAmt", "amtReceive", "jdReceiveAmt", "jdSettleAmt", "codAmt", "sysremark", "invoiceHeader", "invoiceContent", "suffixInfo"};

            /**
             *  拼接Excel明细表sheet表头字段和列表
             * */
            String[] itemNames = {"订单编号", "商品名称",
                    "颜色", "尺码", "国标码",
                    "商品编码", "条码", "规格", "标准价", "优惠金额", "调整金额", "数量", "缺货数量", "成交价格", "成交金额", "平摊金额", "退款状态", "是否赠品", "实物报缺", "分销金额"};
            String[] itemKeys = {"ocBOrderId", "psCProEname", "psCClrEname", "psCSizeEname", "barcode", "psCProEcode", "psCSkuEcode", "skuSpec", "priceList", "amtDiscount", "adjustAmt", "qty", "qtyLost", "price", "realAmt",
                    "orderSplitAmt", "refundStatusName", "isGiftName", "isLackstockName", "distributionPrice"};
//            String itemNames[] = {"订单编号", "商品名称", "货号", "颜色", "尺码", "国标码"};
//            String itemKeys[] = {"ocBOrderId", "psCProEname", "psCProEcode", "psCClrEname", "psCSizeEname", "barcode"};
            List orderN = Lists.newArrayList(orderNames);
            List orderK = Lists.newArrayList(orderKeys);
            List itemN = Lists.newArrayList(itemNames);
            List itemK = Lists.newArrayList(itemKeys);
            long begin = System.currentTimeMillis();
            //生成Excel
            XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
            exportUtil.executeSheet(hssfWorkbook, "订单管理主表", "", orderN, orderK, ocBOrderList, false);
            exportUtil.executeSheet(hssfWorkbook, "订单管理明细", "", itemN, itemK, ocBOrderItemList, false);
            long wri = System.currentTimeMillis();
            //拼接文件名称
            String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "订单管理导出", loginUser, "OSS-Bucket/EXPORT/OC_B_ORDER/");
            long upl = System.currentTimeMillis();
            long end = System.currentTimeMillis();
            holderV14.setData(sdd);
        }
        return holderV14;
    }

    @Override
    public ValueHolderV14 downloadTemp() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "订单管理导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);
        /**
         *  拼接Excel主表sheet表头字段 : 一头牛需求变更新增：商品名称、业务员
         * */
        String orderNames[] = {"下单店铺", "配送费用", "买家昵称", "平台单号", "付款方式",
                "收货人", "收货人手机", "收货人电话", "收货人邮编", "收货人省份", "收货人市", "收货人区",
                "收货人地址", "商品名称", "商品SKU编码", "数量", "成交单价", "下单时间", "支付时间", "平台售价", "买家备注", "卖家备注", "OAID", "是否赠品", "业务员"
                , "是否明文"};
        User user = SystemUserResource.getRootUser();
        List orderN = Lists.newArrayList(orderNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        String sdd = "";
        hssfWorkbook = exportUtil.executeOrderSheet("订单管理主主子表", orderN);
        sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "订单管理导入模板", user, "OSS-Bucket/EXPORT/OC_B_ORDER/");
        holderV14.setData(sdd);
        return holderV14;
    }

    @Override
    public ValueHolderV14 downloadAddressTemp() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "批量修改地址导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String orderNames[] = {"平台单号", "省份", "城市", "区县", "详细地址",
                "收货人", "收货人手机", "收货人电话", "收货人邮编", "是否明文"};
        User user = SystemUserResource.getRootUser();
        List orderN = Lists.newArrayList(orderNames);
        if (log.isDebugEnabled()) {
            log.debug("导出模板格式如为:{}", JSON.toJSONString(orderNames));
        }
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        String sdd = "";
        hssfWorkbook = exportUtil.executeOrderRemarkSheet("批量修改地址", orderN);
        sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "批量修改地址导入模板", user, "OSS-Bucket/EXPORT/OC_B_ORDER/");
        holderV14.setData(sdd);
        return holderV14;
    }

    @Autowired
    BatchExportOcBOrderService batchExportOcBOrderService;

    @Override
    public ValueHolderV14 exportListext(String jsonStr, User loginUser, UserPermission usrPem, Boolean withTag) {
        return batchExportOcBOrderService.mainProgram(jsonStr, loginUser, usrPem, withTag);
    }

    @Override
    public ValueHolderV14 downloadUpdateRemarkTemp() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "订单管理导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String[] orderNames = {"平台单号", "备注", "旗帜", "旗帜颜色:0, 无1, 红2, 橙 3, 绿4,蓝5, 紫"};

        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List orderN = Lists.newArrayList(orderNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "修改备注数据", "", orderN, Lists.newArrayList(), Lists.newArrayList(), false);
        String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "修改备注数据导入模板", user, "OSS-Bucket/EXPORT/OC_B_ORDER/");
        holderV14.setData(sdd);
        return holderV14;
    }

    /**
     * ljp add 退货入库单模板下载
     *
     * @return
     */
    @Override
    public ValueHolderV14 downloadRefundInRemarkTemp() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "订单管理导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String[] orderNames = {"物流单号", "处理人", "处理人备注"};

        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List orderN = Lists.newArrayList(orderNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "退货入库备注导入", "", orderN, Lists.newArrayList(), Lists.newArrayList(), false);
        String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "退货入库单备注导入", user, "OSS-Bucket/IMPORT/OC_B_REFUND_IN/");
        holderV14.setData(sdd);
        return holderV14;
    }

    @Override
    public ValueHolderV14 downloadTempPre() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "订单管理导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(timeout);
        /**
         *  拼接Excel主表sheet表头字段 : 一头牛需求变更新增：商品名称、业务员
         * */
        String orderNames[] = {"配送费用", "买家昵称", "平台单号", "付款方式",
                "收货人", "收货人手机", "收货人电话", "收货人邮编", "收货人省份", "收货人市", "收货人区",
                "收货人地址", "商品名称", "数量", "下单时间", "支付时间", "平台售价", "买家备注", "卖家备注", "OAID", "是否赠品", "业务员"
                , "是否明文", "导入内容"};
        User user = SystemUserResource.getRootUser();
        List orderN = Lists.newArrayList(orderNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        String sdd = "";
        hssfWorkbook = exportUtil.executeOrderSheet("订单管理主主子表", orderN);
        sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "订单管理导入模板", user, "OSS-Bucket/EXPORT/OC_B_ORDER/");
        holderV14.setData(sdd);
        return holderV14;
    }
}
