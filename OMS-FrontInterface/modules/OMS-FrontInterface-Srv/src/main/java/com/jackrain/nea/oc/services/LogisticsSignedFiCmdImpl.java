package com.jackrain.nea.oc.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.api.model.LogisticsSignedRequest;
import com.jackrain.nea.oc.oms.api.LogisticsSignedFiCmd;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-05-13
 * @desc 物流签收信息处理实现
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class LogisticsSignedFiCmdImpl implements LogisticsSignedFiCmd {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    /**
     * 处理已签收的物流单号
     *
     * @param logisticsSignedRequest 物流签收信息请求
     * @return 处理结果
     */
    @Override
    public ValueHolderV14 processSignedLogistics(LogisticsSignedRequest logisticsSignedRequest) {
        // 参数校验
        if (logisticsSignedRequest == null || StringUtils.isBlank(logisticsSignedRequest.getLogisticsCode())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "物流单号不能为空");
        }

        if (logisticsSignedRequest.getSigningTime() == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "签收时间不能为空");
        }

        if (logisticsSignedRequest.getState() == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "物流状态不能为空");
        }
        Integer state = logisticsSignedRequest.getState();


        try {
            // 根据物流单号查询退换货单
            String logisticsCode = logisticsSignedRequest.getLogisticsCode();
            List<OcBReturnOrder> returnOrders = ocBReturnOrderMapper.selectByLogisticsCode(logisticsCode);

            // 如果没有找到退换货单，返回空结果
            if (CollectionUtils.isEmpty(returnOrders)) {
                log.info("未找到物流单号为{}的退换货单", logisticsCode);
                return new ValueHolderV14<>(ResultCode.SUCCESS, "未找到相关退换货单");
            }
            // 创建结果列表
            List<Map<String, Object>> resultList = new ArrayList<>();

            for (OcBReturnOrder returnOrder : returnOrders) {

                // 根据物流状态和退换货单的“是否原退”字段设置签收状态
                Integer isBack = returnOrder.getIsBack();
                boolean isReturnedStatus = isReturnedStatus(state.toString());
                if (isReturnedStatus) {
                    returnOrder.setSigningStatus("2"); // 2-已退签
                } else {
                    returnOrder.setSigningStatus("1"); // 1-已签收
                }
                returnOrder.setSigningTime(logisticsSignedRequest.getSigningTime());

                // 只有在签收状态为已签收时才计算要求入库时间

                // requiredStorageTime为空才设置requiredStorageTime的值
                if (returnOrder.getRequiredStorageTime() == null) {
                    // 计算要求入库时间（签收时间后3天）
                    long requiredStorageTimeMillis = logisticsSignedRequest.getSigningTime().getTime() + 3 * 24 * 60 * 60 * 1000L;
                    returnOrder.setRequiredStorageTime(new java.util.Date(requiredStorageTimeMillis));
                }

                // 更新退换货单
                ocBReturnOrderMapper.updateById(returnOrder);
            }

            // 记录处理日志
            log.info("成功处理物流单号{}的签收信息，更新了{}个退换货单", logisticsCode, returnOrders.size());

            // 返回处理结果
            return new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        } catch (Exception e) {
            log.error("处理物流签收信息异常", e);
            return new ValueHolderV14<>(ResultCode.FAIL, "处理物流签收信息异常: " + e.getMessage());
        }
    }

    private boolean isReturnedStatus(String state) {
        return "4".equals(state) || "401".equals(state) || "14".equals(state);
    }
}
