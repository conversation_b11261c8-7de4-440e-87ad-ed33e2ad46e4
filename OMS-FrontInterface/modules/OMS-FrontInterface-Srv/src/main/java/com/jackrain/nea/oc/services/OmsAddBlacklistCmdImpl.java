package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OmsAddBlacklistCmd;
import com.jackrain.nea.oc.oms.services.OmsAddBlacklistService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2019-07-01 11:04
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsAddBlacklistCmdImpl implements OmsAddBlacklistCmd {

    @Autowired
    private OmsAddBlacklistService omsAddBlacklistService;

    @Override
    public ValueHolder addBlacklist(JSONObject jsonObject, User user) {
        return omsAddBlacklistService.addBlacklist(jsonObject, user);
    }
}
