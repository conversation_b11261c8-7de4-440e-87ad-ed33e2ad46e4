package com.jackrain.nea.oc.services;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OaOrderAddCmd;
import com.jackrain.nea.oc.oms.services.OaOrderAddService;
import com.jackrain.nea.oc.oms.vo.OaOrderAddVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName OaOrderAddCmdImpl
 * @Description OA订单创建
 * <AUTHOR>
 * @Date 2024/1/22 11:45
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OaOrderAddCmdImpl implements OaOrderAddCmd {

    @Autowired
    private OaOrderAddService oaOrderAddService;

    @Override
    public ValueHolderV14 add(JSONObject jsonObject) {
        // 批量的
        JSONArray jsonArray = JSONArray.parseArray(jsonObject.getString("data"));
        List<OaOrderAddVO> oaOrderAddVOS = new ArrayList<>();
        for (Object o : jsonArray) {
            OaOrderAddVO oaOrderAddVO = new OaOrderAddVO();
            JSONObject object = JSONObject.parseObject(JSONUtil.toJsonStr(o));
            oaOrderAddVO.setCpCShopTitle(object.getString("cpCShopTitle"));
            oaOrderAddVO.setTid(object.getString("tid"));
            oaOrderAddVO.setReceiverName(object.getString("receiverName"));
            oaOrderAddVO.setReceiverMobile(object.getString("receiverMobile"));
            oaOrderAddVO.setCpCRegionProvinceEname(object.getString("cpCRegionProvinceEname"));
            oaOrderAddVO.setCpCRegionCityEname(object.getString("cpCRegionCityEname"));
            oaOrderAddVO.setCpCRegionAreaEname(object.getString("cpCRegionAreaEname"));
            oaOrderAddVO.setReceiverAddress(object.getString("receiverAddress"));
            oaOrderAddVO.setPsCSkuEcode(object.getString("psCSkuEcode"));
            oaOrderAddVO.setQty(object.getInteger("qty"));
            oaOrderAddVO.setPriceActual(object.getBigDecimal("priceActual"));
            oaOrderAddVO.setIsGift(object.getInteger("isGift"));
            oaOrderAddVO.setOwnername(object.getString("ownername"));
            oaOrderAddVO.setIsPlainAddr(object.getInteger("isPlainAddr"));
            oaOrderAddVOS.add(oaOrderAddVO);
        }
        return oaOrderAddService.oaOrderAdd(oaOrderAddVOS);
    }
}
