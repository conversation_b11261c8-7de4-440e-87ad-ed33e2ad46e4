package com.jackrain.nea.oc.services.sap;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.sap.OcBSapSalesDataRecordAddTaskSaveCmd;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordAddTaskService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/22
 * description :
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBSapSalesDataRecordAddTaskSaveCmdImpl implements OcBSapSalesDataRecordAddTaskSaveCmd {
    @Autowired
    private OcBSapSalesDataRecordAddTaskService service;

    @Override
    public ValueHolderV14 save(JSONObject obj, User user) {
        return service.save(obj, user);
    }
}
