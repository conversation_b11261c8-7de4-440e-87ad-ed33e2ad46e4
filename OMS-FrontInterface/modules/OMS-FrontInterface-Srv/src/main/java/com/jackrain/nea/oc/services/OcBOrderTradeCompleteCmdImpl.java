package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderTradeCompleteCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单交易完成
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderTradeCompleteCmdImpl implements OcBOrderTradeCompleteCmd {

    /**
     * 单次处理数量限制
     */
    private static final Integer NUM_LIMIT = 1000;

    @Resource
    private OcBOrderMapper orderMapper;

    @Override
    public ValueHolderV14 tradeComplete(List<Long> ids, User user) throws NDSException {
        ValueHolderV14<String> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "修改成功");

        if (Objects.isNull(user)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("获取用户信息失败，请重试!");
            return v14;
        }

        log.info(LogUtil.format("ocBOrder tradeComplete ids:{}, user:{}", "tradeComplete"), ids, user.getId());

        ValueHolderV14<String> v141 = check(ids, v14);
        if (v141 != null) {
            return v141;
        }

        //更新交易完成状态
        orderMapper.updateTradeFinishList(ids, TaoBaoOrderStatus.TRADE_FINISHED, user.getName());

        return v14;
    }

    private ValueHolderV14<String> check(List<Long> ids, ValueHolderV14<String> v14) {
        if (CollectionUtils.isEmpty(ids)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择需要修改的订单");
            return v14;
        }

        if (ids.size() > NUM_LIMIT) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("单次处理不能超过" + NUM_LIMIT + "条");
            return v14;
        }

        List<OcBOrder> orders = orderMapper.selectByIdsList(ids);
        if (CollectionUtils.isEmpty(orders)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("未找到有效订单");
            return v14;
        }

        List<OcBOrder> noPlatformDeliveries = orders.stream().filter(p -> !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noPlatformDeliveries)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("仅能选择平台发货的订单!");
            return v14;
        }

        return null;
    }
}