package com.jackrain.nea.oc.services.dms;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.dms.DmsOrderQueryCmd;
import com.jackrain.nea.oc.oms.api.dms.DmsOrderQueryResult;
import com.jackrain.nea.oc.oms.dms.DmsOrderQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName DmsOrderQueryCmdImpl
 * @Description DMS订单查询
 * <AUTHOR>
 * @Date 2025/3/17 17:13
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class DmsOrderQueryCmdImpl implements DmsOrderQueryCmd {

    @Autowired
    private DmsOrderQueryService dmsOrderQueryService;

    @Override
    public ValueHolderV14<List<DmsOrderQueryResult>> queryOrder(JSONObject params) {
        return dmsOrderQueryService.queryOrder(params);
    }
}
