package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OmsMiniPlatformDeliveryCmd;
import com.jackrain.nea.oc.oms.services.delivery.impl.OmsMiniPlatformDelivery;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/9/28
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsMiniPlatformDeliveryCmdImpl implements OmsMiniPlatformDeliveryCmd {

    @Autowired
    private OmsMiniPlatformDelivery omsMiniPlatformDelivery;

    @Override
    public ValueHolderV14 appointDelivery(Long id) {
        return omsMiniPlatformDelivery.appointDelivery(id);
    }
}
