package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.ReturnGift;
import com.jackrain.nea.oc.oms.services.ReturnGiftService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2019/3/13
 * create at : 2019/3/13 16:01
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ReturnGiftImpl implements ReturnGift {
    @Autowired
    ReturnGiftService returnGiftService;

    @Override
    public ValueHolderV14 returnGift(String param) {
        return returnGiftService.returnGift(param);
    }
}
