package com.jackrain.nea.oc.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBPreOrderQueryCmd;
import com.jackrain.nea.oc.oms.services.OcBPreOrderQueryService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName OcBPreOrderQueryCmdImpl
 * @Description 预导入订单列表页
 * <AUTHOR>
 * @Date 2022/10/13 17:15
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBPreOrderQueryCmdImpl extends CommandAdapter implements OcBPreOrderQueryCmd {

    @Autowired
    private OcBPreOrderQueryService ocBPreOrderQueryService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ocBPreOrderQueryService.preOrderQuery(session);
    }
}
