package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcBorderInvoiceCmd;
import com.jackrain.nea.oc.oms.model.request.SaveInvoiceRequest;
import com.jackrain.nea.oc.oms.model.request.SaveRecordInvoiceRequest;
import com.jackrain.nea.oc.oms.services.OcBOrderCheckRecordInvoice;
import com.jackrain.nea.oc.oms.services.OcBOrderRecordInvoicingService;
import com.jackrain.nea.oc.oms.services.OcBorderInvoiceService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: xiWen.z
 * create at: 2019/7/23 0023
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBorderInvoiceCmdImpl implements OcBorderInvoiceCmd {

    @Autowired
    private OcBorderInvoiceService ocBorderInvoiceService;

    @Autowired
    private OcBOrderRecordInvoicingService ocBOrderRecordInvoicingService;

    @Autowired
    private OcBOrderCheckRecordInvoice ocBOrderCheckRecordInvoice;

    /**
     * 新增开票.订单.校验
     *
     * @param saveInvoiceRequest SaveInvoiceRequest
     * @param loginUser          User
     * @return vh14
     */
    @Override
    public ValueHolderV14 checkAddOrderInvoicing(SaveInvoiceRequest saveInvoiceRequest, User loginUser) {
        return ocBorderInvoiceService.checkAddOrderInvoicing(saveInvoiceRequest, loginUser);
    }

    /**
     * 记录开票.校验
     *
     * @param paramObj  Oc_b_order.id
     * @param loginUser User
     * @return vh14
     */
    @Override
    public ValueHolderV14 checkRecordInvoice(JSONObject paramObj, User loginUser) {
        return ocBOrderCheckRecordInvoice.judgedRecordInvoicing(paramObj, loginUser);
    }

    /**
     * 记录开票.订单
     *
     * @param ocBOrderInvoiceInform SaveRecordInvoiceRequest
     * @param loginUser             User
     * @return vh14
     */
    @Override
    public ValueHolderV14 recordOrderInvoicing(SaveRecordInvoiceRequest ocBOrderInvoiceInform, User loginUser) {
        return ocBOrderRecordInvoicingService.saveRecordInvoicing(ocBOrderInvoiceInform, loginUser);
    }


}
