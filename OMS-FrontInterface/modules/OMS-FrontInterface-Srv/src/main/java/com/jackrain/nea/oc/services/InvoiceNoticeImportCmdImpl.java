package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.InvoiceNoticeImportCmd;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBInvoiceNotice;
import com.jackrain.nea.oc.oms.services.InvoiceNoticeImportService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author:洪艺安
 * @since: 2019/7/27
 * @create at : 2019/7/27 14:12
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InvoiceNoticeImportCmdImpl extends CommandAdapter implements InvoiceNoticeImportCmd {
    @Autowired
    private InvoiceNoticeImportService invoiceNoticeImportService;

    @Override
    public ValueHolderV14 downloadTemp() {
        return invoiceNoticeImportService.downloadTemp();
    }

    @Override
    public ValueHolderV14 importInvoiceNotice(List<ExtOcBInvoiceNotice> extInvoiceNoticeList, User user) {
        return invoiceNoticeImportService.importInvoiceNotice(extInvoiceNoticeList, user);
    }
}
