package com.jackrain.nea.oc.services.invoice;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.invoice.AcFOrderInvoiceRefreshAmtCmd;
import com.jackrain.nea.oc.oms.services.invoice.AcFOrderInvoiceRefreshAmtService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 发票重新计算金额
 *
 * <AUTHOR>
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFOrderInvoiceRefreshAmtCmdImpl extends CommandAdapter implements AcFOrderInvoiceRefreshAmtCmd {

    @Resource
    private AcFOrderInvoiceRefreshAmtService service;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.execute(session);
    }

}
