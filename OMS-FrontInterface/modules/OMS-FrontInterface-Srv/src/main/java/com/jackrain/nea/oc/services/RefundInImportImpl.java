package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.RefundInImportCmd;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.services.RefundInImportService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-08-26 10:27
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RefundInImportImpl implements RefundInImportCmd {

    @Autowired
    private RefundInImportService refundInImportService;

    @Override
    public ValueHolderV14 batchImport(List<OcBRefundIn> ocBRefundInList, Boolean cover, User user) {
        return refundInImportService.batchImport(ocBRefundInList, cover, user);
    }
}
