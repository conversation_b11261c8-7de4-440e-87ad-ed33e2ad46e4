package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcRefundInSaveCmd;
import com.jackrain.nea.oc.oms.model.request.OmsRefundInSaveRequest;
import com.jackrain.nea.oc.oms.services.returnin.OcRefundInService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/29
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcRefundInSaveCmdImpl implements OcRefundInSaveCmd {

    @Autowired
    private OcRefundInService ocRefundInService;

    @Override
    public ValueHolderV14<String> save(OmsRefundInSaveRequest request) {
        return ocRefundInService.save(request);
    }

}
