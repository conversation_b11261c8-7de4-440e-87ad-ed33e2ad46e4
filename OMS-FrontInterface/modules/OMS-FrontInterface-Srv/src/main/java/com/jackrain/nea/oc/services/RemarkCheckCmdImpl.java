package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.RemarkCheckCmd;
import com.jackrain.nea.oc.oms.services.RemarkCheckService;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 李杰
 * @since: 2019/3/15
 * create at : 2019/3/15 10:35
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RemarkCheckCmdImpl extends CommandAdapter implements RemarkCheckCmd {

    @Autowired
    private RemarkCheckService remarkCheckService;

    @Override
    public JSONObject check(String ids) throws NDSException {
        return remarkCheckService.check(ids);
    }
}
