package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OmsManualEqualExchangeCmd;
import com.jackrain.nea.oc.oms.dto.EqualExchangeStInfo;
import com.jackrain.nea.oc.oms.services.OmsManualEqualExchangeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/7/11 下午3:52
 * @Version 1.0
 */
@Component
public class OmsManualEqualExchangeCmdImpl implements OmsManualEqualExchangeCmd {

    @Autowired
    private OmsManualEqualExchangeService omsManualEqualExchangeService;

    @Override
    public ValueHolderV14 selectEqualExchangeOrder(List<Long> orderIds) {
        return omsManualEqualExchangeService.selectEqualExchangeOrder(orderIds);
    }

    @Override
    public ValueHolderV14<List<EqualExchangeStInfo>> selectEqualExchange(String skuCode) {
        return omsManualEqualExchangeService.selectEqualExchange(skuCode);
    }

    @Override
    public ValueHolderV14 confirmEqualExchange(EqualExchangeStInfo exchangeStInfos, User user) {
        return omsManualEqualExchangeService.confirmEqualExchange(exchangeStInfos, user);
    }
}
