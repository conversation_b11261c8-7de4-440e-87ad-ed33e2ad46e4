package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OmsOrderLockStockAndReOccupyStockCmd;
import com.jackrain.nea.oc.oms.services.OmsOrderLockStockAndReOccupyStockService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description：冻结并重新寻源 jitx合包订单拆单
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsOrderLockStockAndReOccupyStockCmdImpl implements OmsOrderLockStockAndReOccupyStockCmd {

    @Autowired
    private OmsOrderLockStockAndReOccupyStockService omsOrderLockStockAndReOccupyStockService;


    @Override
    public ValueHolderV14 lockStockAndReOccupy(String param, User user) {
        return omsOrderLockStockAndReOccupyStockService.lockStockAndReOccupyStock(param, user);
    }
}
