package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderSearchWareHouseCmd;
import com.jackrain.nea.oc.oms.model.request.OrderSerarchCheckRequest;
import com.jackrain.nea.oc.oms.services.OmsOrderAutoSearchWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: heliu
 * @since: 2019/5/5
 * create at : 2019/5/5 16:55
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderSearchWareHouseImpl implements OcBOrderSearchWareHouseCmd {

    @Autowired
    private OmsOrderAutoSearchWarehouseService omsOrderAutoSearchWarehouseService;

    @Override
    public ValueHolderV14 queryOrderSearchWareHouse(OrderSerarchCheckRequest param, User user) throws NDSException {
        return omsOrderAutoSearchWarehouseService.doAutoSearchWarehouse(param, user);
    }
}