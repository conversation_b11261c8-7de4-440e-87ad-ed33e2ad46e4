package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderToBReportCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderToBReportService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @ClassName OcBOrderToBReportImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/25 15:25
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderToBReportCmdImpl implements OcBOrderToBReportCmd {

    @Autowired
    private OcBOrderToBReportService ocBOrderToBReportService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ocBOrderToBReportService.orderListQuery(session);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
