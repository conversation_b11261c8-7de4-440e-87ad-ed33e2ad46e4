package com.jackrain.nea.oc.services.dms;

import com.jackrain.nea.oc.oms.api.dms.LabelingRequirementsQueryCmd;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName LabelingRequirementsQueryCmdImpl
 * @Description 增值服务查询
 * <AUTHOR>
 * @Date 2024/6/6 15:28
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class LabelingRequirementsQueryCmdImpl implements LabelingRequirementsQueryCmd {

    @Autowired
    private StRpcService stRpcService;

    @Override
    public ValueHolderV14 labelingRequirementsQuery() {
        return stRpcService.queryAllDoc();
    }
}
