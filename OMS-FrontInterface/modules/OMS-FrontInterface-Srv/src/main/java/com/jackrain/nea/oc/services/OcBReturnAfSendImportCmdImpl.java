package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnAfSendImportCmd;
import com.jackrain.nea.oc.oms.services.OcBReturnAfSendImportService;
import com.jackrain.nea.oc.oms.vo.OcBReturnAfSendImpVO;
import com.jackrain.nea.oc.oms.vo.OcBReturnAfSendItemVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @ClassName : OcBReturnAfSendImportCmdImpl  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-12-15 18:55  
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnAfSendImportCmdImpl implements OcBReturnAfSendImportCmd {
    @Autowired
    private OcBReturnAfSendImportService service;
    @Override
    public ValueHolderV14 ocBReturnAfSendImport(Map<String, List<OcBReturnAfSendItemVO>> mapItem,List<OcBReturnAfSendImpVO> ocBOrderList, User user) throws NDSException {

        return service.ocBReturnAfSendImport(mapItem,ocBOrderList,user);
    }
}
