package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcRefundInExportCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.OcRefundExportResult;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInExt;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItemExtend;
import com.jackrain.nea.oc.oms.nums.excel.OcBRefundInItemModel;
import com.jackrain.nea.oc.oms.nums.excel.OcBRefundInModel;
import com.jackrain.nea.oc.oms.services.OcBorderExportConvert;
import com.jackrain.nea.oc.oms.services.OcBrefundInExportService;
import com.jackrain.nea.oc.oms.services.ReturnStorageListService;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.excel.XlsIoHelper;
import com.jackrain.nea.util.excel.XlsIoModel;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 退货入库导出
 *
 * @author: 夏继超
 * @since: 2019/6/6
 * create at : 2019/6/6 14:55
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcRefundInExportCmdImpl implements OcRefundInExportCmd {
    @Autowired
    private ExportUtil exportUtil;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    @Autowired
    OcBrefundInExportService exportService;

    @Autowired
    OcBReturnOrderExportCmdImpl orderExportCmd;
    @Override
    public ValueHolderV14 exportList(String jsonStr, User loginUser) {
        ValueHolderV14 holderV14 = exportService.exportList(jsonStr, loginUser);
        if (holderV14.getCode() == ResultCode.SUCCESS && holderV14.getData() != null) {
            OcRefundExportResult data = (OcRefundExportResult) holderV14.getData();
            List<OcBRefundInExt> ocBOrderList = data.getOcBRefundIns();
            List<OcBRefundInProductItemExtend> ocBOrderItemList = data.getProductItems();
            //赋值订单单据号

            exportUtil.setEndpoint(this.endpoint);
            exportUtil.setAccessKeyId(this.accessKeyId);
            exportUtil.setAccessKeySecret(this.accessKeySecret);
            exportUtil.setBucketName(this.bucketName);
            if (StringUtils.isEmpty(timeout)) {
                //如果获取不到apllo配置参数，设置默认过期时间为30分钟
                timeout = "1800000";
            }
            exportUtil.setTimeout(this.timeout);
            /**
             *  拼接Excel主表sheet表头字段和列表
             * */
            String orderNames[] = {"入库编号", "原单单号", "原平台单号", "入库仓库", "入库实体仓名称", "买家昵称", "物流单号", "批次编号", "发件地址", "入库状态", "作废状态", "收件人手机",
                    "收件人", "修改时间", "创建时间", "匹配状态", "商品条码", "入库数量", "备注", "匹配人", "匹配时间"};
            String orderKeys[] = {"id", "origOrderNo", "sourceCode", "inStoreEname", "cpCPhyWarehouseEname", "userNick", "logisticNumber", "batchNo", "receiverAddress",
                    "warehousingStatus", "invalidState", "receiverMobile", "receiverName", "updateTime", "createTime", "matchstatusname", "allSku", "qtyAll", "remark", "matcher", "matchedTime"};

            /**
             *  拼接Excel明细表sheet表头字段和列表
             * */
            String itemNames[] = {"入库单编号", "明细编号", "国标码", "条码编码", "实收条码编码", "商品编号", "商品名称", "入库单编号", "商品标记", "是否无原单条码", "数量", "退换货单编号", "是否匹配",
                    "是否生成调整单", "实收国标码"};
            String itemKeys[] = {"ocBRefundInId", "id", "gbcode", "psCSkuEcode", "realSkuEcode", "psCProEcode", "psCProEname", "scBInId", "productMarkName", "isWithoutOrigEname", "qty", "ocBReturnOrderId", "isMatchName",
                    "isGenAdjustEname", "gbcodeActual"};
            List orderN = Lists.newArrayList(orderNames);
            List orderK = Lists.newArrayList(orderKeys);
            List itemN = Lists.newArrayList(itemNames);
            List itemK = Lists.newArrayList(itemKeys);
            //生成Excel
            XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
            exportUtil.executeSheet(hssfWorkbook, "退货入库主表", "", orderN, orderK, ocBOrderList, false);
            exportUtil.executeSheet(hssfWorkbook, "退货入库明细", "", itemN, itemK, ocBOrderItemList, false);
            String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "退货入库管理导出", loginUser, "OSS-Bucket/EXPORT/OcBRefundIn/");
            holderV14.setData(sdd);
        }
        return holderV14;
    }

    @Autowired
    private XlsIoHelper xlsIoHelper;

    @Autowired
    private ReturnStorageListService returnStorageListService;

    /**
     * 退货入库单导出
     *
     * @param jsonStr string
     * @param usr     User
     * @return ValueHolderV14
     */
    @Override
    public ValueHolderV14 exportRefundIn(String jsonStr, User usr) {

        JSONObject jsnObj;
        try {
            jsnObj = JSONObject.parseObject(jsonStr);
        } catch (Exception e) {
            ValueHolderV14<JSONObject> v = new ValueHolderV14();
            v.setCode(ResultCode.FAIL);
            v.setMessage(Resources.getMessage("参数解析异常", usr.getLocale()));
            return v;
        }
        List<Long> longs = new ArrayList<>();
        if (jsnObj.containsKey("idList")) {
            JSONArray idList = jsnObj.getJSONArray("idList");
            if (idList == null || idList.size() < OcBOrderConst.IS_STATUS_IY) {
//                ValueHolderV14<JSONObject> v = new ValueHolderV14();
//                v.setCode(ResultCode.FAIL);
//                v.setMessage(Resources.getMessage("未查询到数据", usr.getLocale()));
//                return v;
            }else {
                longs = JSONArray.parseArray(idList.toJSONString(), Long.class);
            }
        } else {
            jsnObj.put("esExport", "export");
            jsonStr = jsnObj.toString();
            ValueHolderV14 ves = returnStorageListService.returnStorageList( jsonStr, usr);
            if (ves != null && ves.getCode() == 3) {
                Object data = ves.getData();
                if (data != null) {
                    JSONArray jsnAry = (JSONArray) data;
                    longs = JSONArray.parseArray(jsnAry.toJSONString(), Long.class);
                }
            }
        }
//        if (longs == null || longs.size() < OcBOrderConst.IS_STATUS_IY) {
//            ValueHolderV14<JSONObject> v = new ValueHolderV14();
//            v.setCode(ResultCode.FAIL);
//            v.setMessage(Resources.getMessage("未查询到数据", usr.getLocale()));
//            return v;
//        }

        List<Class> list = new ArrayList<>();
        list.add(OcBRefundInModel.class);
        list.add(OcBRefundInItemModel.class);
        XlsIoModel xlsIoModel = new XlsIoModel();
        prepareConvertData(xlsIoModel);
        OcBorderExportConvert oec = new OcBorderExportConvert();

        //插入我的任务里
        AsyncTaskBody asyncTaskBody =new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("退货入库单导出");
        asyncTaskBody.setTaskType("导出");
        return orderExportCmd.asyncExport(asyncTaskBody,"退货入库单", list, xlsIoModel, longs, usr, oec, null);
    }

    /**
     * @param v XlsIoModel
     */
    private void prepareConvertData(XlsIoModel v) {
        Map<String, Map<String, Map<Object, Object>>> csm = v.getConvertSource();
        if (csm == null) {
            csm = new HashMap<>();
            v.setConvertSource(csm);
        }
        // field-get table.oc_b_order
        Map<String, Map<Object, Object>> m1 = csm.get("oc_b_refund_in");
        if (m1 == null) {
            m1 = new HashMap<>();
            csm.put("oc_b_refund_in", m1);
        }

        Map<Object, Object> inStatus = new HashMap<>();
        inStatus.put(1, "等待入库");
        inStatus.put(2, "已入库");
        inStatus.put(3, "入库作废");
        inStatus.put(4, "未关联退货单");

        Map<Object, Object> matchStatus = new HashMap<>();
        matchStatus.put(0, "未匹配");
        matchStatus.put(1, "部分匹配");
        matchStatus.put(2, "全部匹配");

        Map<Object, Object> isOffMatchStatus = new HashMap<>();
        isOffMatchStatus.put(0, "否");
        isOffMatchStatus.put(1, "是");

        Map<Object, Object> virtualInStatus = new HashMap<>();
        virtualInStatus.put(0, "未虚拟入库");
        virtualInStatus.put(1, "虚拟入库未入库");
        virtualInStatus.put(2, "虚拟入库已入库");

        Map<Object, Object> isYesNo = new HashMap<>();
        isYesNo.put("0", "否");
        isYesNo.put("1", "是");

        m1.put("in_status", inStatus);
        m1.put("match_status", matchStatus);
        m1.put("is_off_match", isOffMatchStatus);
        m1.put("virtual_in_status", virtualInStatus);
        m1.put("num_less", isYesNo);
        m1.put("num_more", isYesNo);
        m1.put("product_diff", isYesNo);

        Map<String, Map<Object, Object>> m2 = csm.get("oc_b_refund_in_product_item");
        if (m2 == null) {
            m2 = new HashMap<>();
            csm.put("oc_b_refund_in_product_item", m2);
        }
        Map<Object, Object> productMark = new HashMap<>();
        productMark.put("0", "次品");
        productMark.put("1", "正品");
        Map<Object, Object> isWithoutOrig = new HashMap<>();
        isWithoutOrig.put(0, "否");
        isWithoutOrig.put(1, "是");
        Map<Object, Object> isMatch = new HashMap<>();
        isMatch.put(0, "未匹配");
        isMatch.put(1, "已匹配");
        Map<Object, Object> isGenAdjust = new HashMap<>();
        isGenAdjust.put(0, "否");
        isGenAdjust.put(1, "是");

        m2.put("is_gen_adjust", isGenAdjust);
        m2.put("is_match", isMatch);
        m2.put("is_without_orig", isWithoutOrig);
        m2.put("product_mark", productMark);

    }
}
