package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.api.HelloCmd;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @time 2019/4/28 12:30
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class HelloCmdImpl implements HelloCmd {


    @Override
    public JSONObject queryEs() {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("AMT_RECEIVE", "399");
        /*
         * ElasticSearchUtil.search(index, type, whereKeys, filterKeys, orderKeys, range, startIndex , fileds)
         * index 查询主表
         * type  查询子表
         * whereKeys 查询条件 类似于 equalTo("column_name","value")
         * filterKeys 过滤条件 可以按照大于小于拼接  大于某个值 ("column_name","value~")
         *                                          小于某个值 ("column_name", "~value")
         *                                          在某个区间 ("column_name", "value1~value2")
         *
         * */
        JSONObject search = ElasticSearchUtil.search("oc_b_order", "oc_b_order", whereKeys, null, null, 10, 0, new String[]{"ID"});
        return search;
    }

    @Override
    public JSONObject queryEsByCondition(String index, String type, JSONObject whereKeys, JSONObject filterKeys) {
        return ElasticSearchUtil.search(index, type, whereKeys, filterKeys, null, 10, 0, new String[]{"ID"});
    }

    @Override
    public String sendMq(String var1, String var2, String var3, String var4, String var5) {
        return null;
    }


    @Override
    public String hello(String type) {
        log.info("helloCmd-type: {}", type);
//        switch (type) {
//            case "1":
//                int a = 1 / 0;
//                break;
//            case "2":
//                try {
//                    int b = 1 / 0;
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//                break;
//            case "3":
//                try {
//                    int b = 1 / 0;
//                } catch (Exception e) {
//                    log.error(this.getClass().getName() + " 测试异常日志 ", e);
//                }
//                break;
//            default:
//                log.debug("helloCmd-debug...");
//                break;
//        }
        return "hello world";
    }

}
