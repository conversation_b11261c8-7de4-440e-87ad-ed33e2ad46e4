package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OmsOrderVopResetShipCmd;
import com.jackrain.nea.oc.oms.services.OmsOrderVopBatchResetShipService;
import com.jackrain.nea.oc.oms.services.OmsOrderVopResetShipService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description：jitx合包订单重置发货
 *
 * <AUTHOR>
 * @date 2021/11/26
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsOrderVopResetShipCmdImpl implements OmsOrderVopResetShipCmd {

    @Autowired
    private OmsOrderVopResetShipService omsOrderVopResetShipService;
    @Autowired
    private OmsOrderVopBatchResetShipService omsOrderVopBatchResetShipService;


    @Override
    public ValueHolderV14 resetShip(String param, User user) {
        return omsOrderVopBatchResetShipService.resetShip(param, user);
    }
}
