package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcSaveChangingOrRefundingCmd;
import com.jackrain.nea.oc.oms.services.OcSaveChangingOrRefundingService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 李杰
 * @since: 2019/3/12
 * create at : 2019/3/12 10:15
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcSaveChangingOrRefundingCmdImpl extends CommandAdapter implements OcSaveChangingOrRefundingCmd {
    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;

    @Override
    public ValueHolder saveReturnOrder(JSONObject obj, User user) throws NDSException {
        return ocSaveChangingOrRefundingService.saveChangingOrRefunding(obj, user);
    }

    @Override
    public ValueHolderV14 checkAllStroreStock(JSONObject obj, User user) {
        return ocSaveChangingOrRefundingService.checkAllStroreStock(obj, user);
    }

    @Override
    public ValueHolderV14 addReturnOrderItem(JSONObject obj, User user) {
        return ocSaveChangingOrRefundingService.addReturnOrderItem(obj, user);
    }

    @Override
    public ValueHolderV14 deleteReturnOrderItem(JSONObject obj, User user) {
        return ocSaveChangingOrRefundingService.deleteReturnOrderItem(obj, user);
    }
}
