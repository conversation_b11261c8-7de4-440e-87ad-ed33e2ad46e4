package com.jackrain.nea.oc.services.patrol;

import com.jackrain.nea.oc.oms.api.patrol.WithdrawZtWarehouseCmd;
import com.jackrain.nea.oc.oms.services.patrol.WithdrawZtWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2019/11/15 2:42 下午
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class WithdrawZtWarehouseImpl implements WithdrawZtWarehouseCmd {

    @Autowired
    private WithdrawZtWarehouseService withdrawZtWarehouseService;

    @Override
    public ValueHolderV14 withdrawZtWarehouse(Integer pageSize) {
        return withdrawZtWarehouseService.withdrawZtWarehouse(pageSize);
    }
}
