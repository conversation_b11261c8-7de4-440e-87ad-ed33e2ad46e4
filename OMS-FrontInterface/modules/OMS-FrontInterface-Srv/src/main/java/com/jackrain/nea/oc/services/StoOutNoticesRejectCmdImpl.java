package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.StoOutNoticesRejectCmd;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OcBOrderTheAuditService;
import com.jackrain.nea.oc.oms.services.OmsOrderLockStockAndReOccupyStockService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;


/**
 * className: StoOutNoticesRejectCmdImpl
 * description:
 *
 * <AUTHOR>
 * create: 2021-07-08
 * @since JDK 1.8
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oms-fi", version = "1.0")
public class StoOutNoticesRejectCmdImpl implements StoOutNoticesRejectCmd {

    @Autowired
    private OcBOrderTheAuditService theAuditService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLockStockAndReOccupyStockService reOccupyStockService;

    @Override
    public ValueHolderV14 resetOrderStatus(Long id, User user,Boolean isRelyOnWing) {

        //订单反审核
        OrderICheckRequest request = new OrderICheckRequest();
        request.setIds(new Long[]{id});
        request.setType(LogTypeEnum.JITX_REJECT_DELIVERY.getType());
        ValueHolderV14 theAuditResult = theAuditService.orderTheAudit(request,user,false);
        if(!theAuditResult.isOK()){
            return theAuditResult;
        }

        //JITX门店订单拆单
        ValueHolderV14 splitResult = reOccupyStockService.splitOrder(id, user);
        if(splitResult.isOK()){
            return splitResult;
        }

        OcBOrder order = omsOrderService.selectOrderInfo(id);
        if(OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus())){

            //最新订单状态为待审核，更新订单状态为待分配，待占单
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(id);
            ocBOrder.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
            ocBOrder.setModifierename(user.getEname());
            ocBOrder.setModifiername(user.getName());
            ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_0);
            ocBOrder.setModifierid(Long.valueOf(user.getId()));
            ocBOrder.setModifieddate(new Date());
            boolean result = omsOrderService.updateOrderInfo(ocBOrder);

            if(result){
                theAuditResult.setMessage("重置零售发货单状态成功");
            }else {
                theAuditResult.setCode(ResultCode.FAIL);
                theAuditResult.setMessage("重置零售发货单状态失败");
            }
        }

        return theAuditResult;
    }
}
