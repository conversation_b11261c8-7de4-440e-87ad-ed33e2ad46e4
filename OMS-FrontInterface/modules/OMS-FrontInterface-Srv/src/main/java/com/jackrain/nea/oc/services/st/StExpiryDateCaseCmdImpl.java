package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StExpiryDateCaseCmd;
import com.jackrain.nea.st.service.StExpiryDateCaseService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2022/6/15 上午11:21
 * @Version 1.0
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StExpiryDateCaseCmdImpl extends CommandAdapter implements StExpiryDateCaseCmd {

    @Autowired
    private StExpiryDateCaseService stExpiryDateCaseService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stExpiryDateCaseService.expiryDateCaseService(session);
    }
}
