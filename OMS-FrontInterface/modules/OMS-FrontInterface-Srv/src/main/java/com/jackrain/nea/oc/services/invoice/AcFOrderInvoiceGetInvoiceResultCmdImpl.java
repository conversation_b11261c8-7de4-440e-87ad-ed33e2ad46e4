package com.jackrain.nea.oc.services.invoice;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.invoice.AcFOrderInvoiceGetInvoiceResultCmd;
import com.jackrain.nea.oc.oms.services.invoice.AcFOrderInvoiceGetInvoiceResultService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 *
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFOrderInvoiceGetInvoiceResultCmdImpl extends CommandAdapter implements AcFOrderInvoiceGetInvoiceResultCmd {

    @Autowired
    private AcFOrderInvoiceGetInvoiceResultService service;


    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.execute(session);
    }

}
