package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.QueryOrderCmd;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.QueryOrderService;
import com.jackrain.nea.oc.request.OcBOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 订单查询
 *
 * @Author: qinjunlong
 * @Date: 2020-08-05 13:32
 * @Version 1.0
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class QueryOrderCmdImpl implements QueryOrderCmd {
    @Autowired
    private QueryOrderService queryOrderList;

    @Override
    public ValueHolderV14<List<OcBOrder>> queryOrderByRequest(OcBOrderRequest ocBOrderRequest) {
        return queryOrderList.queryOrderListByRequest(ocBOrderRequest);
    }
}
