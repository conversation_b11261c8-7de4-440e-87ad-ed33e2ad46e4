package com.jackrain.nea.oc.services.patrol;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.patrol.ClearRedisCacheCmd;
import com.jackrain.nea.oc.oms.services.patrol.ClearRedisCacheService;
import com.jackrain.nea.ps.model.ProductSku;

import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2019/11/6 2:05 下午
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ClearRedisCacheImpl implements ClearRedisCacheCmd {

    @Autowired
    private ClearRedisCacheService clearRedisCacheService;
    @Autowired
    private PsRpcService psRpcService;

    @Override
    public ValueHolderV14 clearRedisCache(String redisKey, String type, String env) {
        return clearRedisCacheService.clearRedisCache(redisKey, type, env);
    }

    @Override
    public String selectSku(String sku) {
        ProductSku productSku = psRpcService.selectProductSku(sku);
        if (productSku != null) {
            return JSONObject.toJSONString(productSku);
        }
        return null;
    }
}
