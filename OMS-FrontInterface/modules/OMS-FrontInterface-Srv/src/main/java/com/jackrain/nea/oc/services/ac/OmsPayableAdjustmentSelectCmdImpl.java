package com.jackrain.nea.oc.services.ac;

import com.jackrain.nea.ac.service.PayableAdjustmentSelectService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ac.OmsPayableAdjustmentSelectCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 陈俊明
 * @since: 2019-04-08
 * @create at : 2019-04-08 14:31
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsPayableAdjustmentSelectCmdImpl extends CommandAdapter implements OmsPayableAdjustmentSelectCmd {
    @Autowired
    private PayableAdjustmentSelectService payableAdjustmentSelectService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return payableAdjustmentSelectService.execute(querySession);
    }
}
