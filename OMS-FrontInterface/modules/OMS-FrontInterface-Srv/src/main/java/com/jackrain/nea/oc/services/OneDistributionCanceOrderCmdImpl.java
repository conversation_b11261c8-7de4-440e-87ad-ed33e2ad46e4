package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OneDistributionCanceOrderCmd;
import com.jackrain.nea.oc.oms.services.OneDistributionCanceOrderService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName : OneDistributionCanceOrderCmdImpl  
 * @Description : 
 * <AUTHOR>  YCH
 * r@Date: 2021-11-04 18:26
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OneDistributionCanceOrderCmdImpl implements OneDistributionCanceOrderCmd {

    @Autowired
    private OneDistributionCanceOrderService service;

    @Override
    public ValueHolderV14 oneDistributionCanceOrder(JSONObject request) {
        return service.oneDistributionCanceOrder(request);
    }
}
