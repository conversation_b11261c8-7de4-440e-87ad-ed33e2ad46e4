package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.StCDropshipBasePriceQueryCmd;
import com.jackrain.nea.oc.oms.services.StCDropshipBasePriceQueryService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 一件代发客户基价策略查询命令实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCDropshipBasePriceQueryCmdImpl extends CommandAdapter implements StCDropshipBasePriceQueryCmd {

    @Autowired
    private StCDropshipBasePriceQueryService queryService;

    @Override
    public ValueHolder getBasePrice(JSONObject obj, User user) {
        return queryService.getBasePrice(obj, user);
    }
}
