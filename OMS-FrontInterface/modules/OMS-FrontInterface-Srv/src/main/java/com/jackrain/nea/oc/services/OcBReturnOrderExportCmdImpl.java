package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderExportCmd;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrder;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.OcBReturnOrderExportResult;
import com.jackrain.nea.oc.oms.model.result.OrderReturnResult;
import com.jackrain.nea.oc.oms.model.result.QueryChangingOrRefundingResult;
import com.jackrain.nea.oc.oms.nums.excel.OcBReturnOrderExchangeModel;
import com.jackrain.nea.oc.oms.nums.excel.OcBReturnOrderModel;
import com.jackrain.nea.oc.oms.nums.excel.OcBReturnOrderRefundModel;
import com.jackrain.nea.oc.oms.services.CpQueryChangingOrRefundingService;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderExportService;
import com.jackrain.nea.oc.oms.services.OcBorderExportConvert;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.excel.XlsIoHelper;
import com.jackrain.nea.util.excel.XlsIoModel;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 11:19
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderExportCmdImpl implements OcBReturnOrderExportCmd {

    /**
     * 最大导出数量限制
     */
    private final static String EXPORT_MAX_ROW_NUM_LIMIT = "export_max_row_num_limit";

    /**
     * 最大导出数量默认值
     */
    private final static int DEF_EXPORT_MAX_ROW_NUM_LIMIT = 1000000;

    @Autowired
    private OcBReturnOrderExportService exportService;
    @Autowired
    private CpQueryChangingOrRefundingService service;

    @Autowired
    private ExportUtil exportUtil;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    @Override
    public ValueHolderV14 exportList(JSONObject object, User user) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        List<Long> idList = Lists.newArrayList();
        //获取idList
        if (object.containsKey("idList")) {
            idList = (List<Long>) object.get("idList");
        } else {
            ValueHolderV14<QueryChangingOrRefundingResult> resultValueHolderV14 = service.queryChangingOrRefunding(null, object, user);
            if (ResultCode.FAIL == resultValueHolderV14.getCode()) {
                return resultValueHolderV14;
            }
            idList = resultValueHolderV14.getData().getQueryResult().parallelStream().map(OrderReturnResult::getId).collect(Collectors.toList());
        }
        //根据id查询
        ValueHolderV14<OcBReturnOrderExportResult> resultValueHolderV14 = exportService.exportList(idList);
        OcBReturnOrderExportResult data = resultValueHolderV14.getData();
        List<ExtOcBReturnOrder> orderList = data.getOrderList();
        List<ExtOcBReturnOrderRefund> refundList = data.getRefundList();
        List<ExtOcBReturnOrderExchange> exchangeList = data.getExchangeList();
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);
        /**
         *  拼接Excel主表sheet表头字段和列表
         * */
        String orderNames[] = {"退单编号", "原始订单编号", "单据类型", "买家昵称", "原始平台单号", "店铺名称", "平台退款单号", "退回物流公司", "退款原因",
                "退回物流单号", "换货预留库存", "是否原退", "备注", "收货人", "收货人手机", "收货人电话", "收货人邮编", "收货人省份", "收货人市",
                "收货人区", "收货人地址", "换货邮费", "商品应退金额", "应退邮费", "其他金额", "换货金额", "退货单总金额", "代销结算金额", "入库实体仓", "淘宝换货平台单号", "单据状态"};
        String orderKeys[] = {"id", "origOrderId", "billTypeName", "buyerNick", "origSourceCode", "cpCShopTitle", "returnId", "cpCLogisticsEname", "returnReasonName", "logisticsCode",
                "isReservedName", "isBackName", "remark", "receiveName", "receiveMobile", "receivePhone", "receiveZip", "receiverProvinceName", "receiverCityName", "receiverAreaName",
                "receiveAddress", "shipAmt", "returnAmtList", "returnAmtShip", "returnAmtOther", "exchangeAmt", "returnAmtActual", "consignAmtSettle", "cpCPhyWarehouseInEname", "tbDisputeId", "returnStatusName"};

        /**
         *  拼接Excel退货明细表sheet表头字段和列表
         * */
        String refundNames[] = {"退单编号", "条码", "国标码", "商品编码", "商品名称", "可退数量", "申请数量", "吊牌价", "单件退货金额", "退货金额", "入库数量", "商品标记"};
        String refundKeys[] = {"ocBReturnOrderId", "psCSkuEcode", "barcode", "psCProEcode", "psCProEname", "qtyCanRefund", "qtyRefund", "price", "amtRefundSingle", "allAmtRefund", "qtyIn", "productMark"};

        /**
         *  拼接Excel换货明细表sheet表头字段和列表
         * */
        String exchangeNames[] = {"退单编号", "条码", "国标码", "商品编码", "商品名称", "吊牌价", "换货金额", "换货数量"};
        String exchangeKeys[] = {"ocBReturnOrderId", "psCSkuEcode", "barcode", "psCProEcode", "psCProEname", "price", "amtRefund", "qtyExchange"};

        List orderN = Lists.newArrayList(orderNames);
        List orderK = Lists.newArrayList(orderKeys);
        List refundN = Lists.newArrayList(refundNames);
        List refundK = Lists.newArrayList(refundKeys);
        List exchangeN = Lists.newArrayList(exchangeNames);
        List exchangeK = Lists.newArrayList(exchangeKeys);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "退换货订单主表", "", orderN, orderK, orderList, false);
        exportUtil.executeSheet(hssfWorkbook, "退换货订单退货明细", "", refundN, refundK, refundList, false);
        exportUtil.executeSheet(hssfWorkbook, "退换货订单换货明细", "", exchangeN, exchangeK, exchangeList, false);
        String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "退换货订单导出", user, "OSS-Bucket/EXPORT/OcBReturnOrder/");
        holderV14.setData(sdd);
        return holderV14;
    }

    @Override
    public ValueHolderV14 downloadTemp() {
        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "退换货订单导入模板下载成功！");
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);
        /**
         *  拼接Excel主表sheet表头字段
         * */
        String orderNames[] = {"原始订单编号", "单据类型", "买家昵称", "原始平台单号", "店铺名称", "平台退款单号", "退回物流公司", "退款原因",
                "退回物流单号", "换货预留库存", "是否原退", "备注", "收货人", "收货人手机", "收货人电话", "收货人邮编", "收货人省份", "收货人市", "收货人区",
                "收货人地址", "换货邮费", "应退邮费", "其他金额", "代销结算金额", "头子表关联列"};

        /**
         *  拼接Excel退货明细表sheet表头字段
         * */
        String refundNames[] = {"条码", "数量", "头子表关联列"};

        /**
         *  拼接Excel换货明细表sheet表头字段
         * */
        String exchangeNames[] = {"条码", "数量", "头子表关联列"};
        User user = new UserImpl();
        ((UserImpl) user).setName("");
        List orderN = Lists.newArrayList(orderNames);
        List refundN = Lists.newArrayList(refundNames);
        List exchangeN = Lists.newArrayList(exchangeNames);
        //生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "退换货订单主表", "", orderN, Lists.newArrayList(), Lists.newArrayList(), false);
        exportUtil.executeSheet(hssfWorkbook, "退换货订单退货明细", "", refundN, Lists.newArrayList(), Lists.newArrayList(), false);
        exportUtil.executeSheet(hssfWorkbook, "退换货订单换货明细", "", exchangeN, Lists.newArrayList(), Lists.newArrayList(), false);
        String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "退换货订单导入模板", user, "OSS-Bucket/EXPORT/OcBReturnOrder/");
        holderV14.setData(sdd);
        return holderV14;
    }


    @Autowired
    private XlsIoHelper xlsIoHelper;

    /**
     * 退换货订单导出
     *
     * @param jsnObj jsonObject
     * @param user   user
     * @return vh
     */
    @Override
    public ValueHolderV14 exportReturnOrder(JSONObject jsnObj, User user, UserPermission usrPem) {

        List<Long> idList = null;
        log.info(" 退换货导出主线任务开始");

        if (jsnObj.containsKey("idList")) {
            JSONArray idList2 = jsnObj.getJSONArray("idList");
            idList = JSONArray.parseArray(idList2.toJSONString(), Long.class);
        } else {
            jsnObj.put("esExport", "export");
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            int exportSize = config.getProperty("exportExcel.exportSize", 100000);
//            int size = jsnObj.getIntValue("count");
//            if (size > 0) {
//                if (size > exportSize) {
//                    size = exportSize;
//                    jsnObj.put("count", size);
//                    // return reBuildVh("导出数据超出上限", usr, false);
//                }
//            }
            ValueHolderV14 esvh = service.queryChangingOrRefunding(usrPem, jsnObj, user);
            if (esvh.getCode() == 3) {
                Object data = esvh.getData();
                if (data != null) {
                    JSONArray jsnAry = (JSONArray) data;
                    idList = JSONArray.parseArray(jsnAry.toJSONString(), Long.class);
                }
            }
        }
        if (idList == null || idList.size() < 1) {
            ValueHolderV14<JSONObject> v = new ValueHolderV14();
            v.setCode(ResultCode.FAIL);
            v.setMessage(Resources.getMessage("未查询到数据", user.getLocale()));
            return v;
        }
        PropertiesConf pconf = ApplicationContextHandle.getBean(PropertiesConf.class);
        int exportMaxRowNumLimit = pconf.getProperty(EXPORT_MAX_ROW_NUM_LIMIT, DEF_EXPORT_MAX_ROW_NUM_LIMIT);
        if (idList.size() > exportMaxRowNumLimit) {
            ValueHolderV14<JSONObject> v = new ValueHolderV14();
            v.setCode(ResultCode.FAIL);
            v.setMessage("导出数据超出最大行数限制，最大行数：" + exportMaxRowNumLimit);
            log.error(LogUtil.format("OcBOrderListQueryService.queryOrderList Error: 导出数据超出最大行数限制"));
            return v;
        }

        List<Class> list = new ArrayList<>();
        list.add(OcBReturnOrderModel.class);
        list.add(OcBReturnOrderRefundModel.class);
        list.add(OcBReturnOrderExchangeModel.class);
        XlsIoModel xlsIoModel = new XlsIoModel();
        prepareConvertData(xlsIoModel);
        OcBorderExportConvert oec = new OcBorderExportConvert();

        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("退换货单导出");
        asyncTaskBody.setTaskType("导出");
        return asyncExport(asyncTaskBody, "退换货订单", list, xlsIoModel, idList, user, oec, usrPem);
    }

    //异步处理导出
    public ValueHolderV14 asyncExport(AsyncTaskBody asyncTaskBody, String msg, List<Class> list, XlsIoModel xlsIoModel, List<Long> idList, User user, OcBorderExportConvert oec, UserPermission usrPem) {
        ValueHolderV14 vh = new ValueHolderV14();
        //任务开始
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        List<Long> finalIdList = idList;
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            try {
                if (CollectionUtils.isEmpty(finalIdList)) {
                    throw new NDSException(Resources.getMessage("未查询到数据"));
                }
                //主线任务
                ValueHolderV14 valueHolder = xlsIoHelper.export(msg, list, xlsIoModel, finalIdList, user, oec, usrPem, null);
                retMap.put("code", valueHolder.getCode());
                retMap.put("data", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception e) {
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导出异常：" + e.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(asyncTaskBody.getId());
        vh.setMessage(Resources.getMessage("退换货单导出任务开始！"));
        return vh;
//        return xlsIoHelper.export("退换货订单", list, xlsIoModel, idList, user, oec, usrPem);
    }

    /**
     * convert.prev
     *
     * @param v XlsIoModel
     */
    private void prepareConvertData(XlsIoModel v) {

        Map<String, Map<String, Map<Object, Object>>> csm = v.getConvertSource();
        if (csm == null) {
            csm = new HashMap<>();
            v.setConvertSource(csm);
        }
        // field-get table.oc_b_order
        Map<String, Map<Object, Object>> m1 = csm.get("oc_b_return_order");
        if (m1 == null) {
            m1 = new HashMap<>();
            csm.put("oc_b_return_order", m1);
        }

        Map<Object, Object> isBack = new HashMap<>();
        isBack.put(0, "否");
        isBack.put(1, "是");
        Map<Object, Object> isReserved = new HashMap<>();
        isReserved.put(0, "否");
        isReserved.put(1, "是");
        Map<Object, Object> returnReason = new HashMap<>();
        returnReason.put("2", "商品损坏");
        returnReason.put("1", "商品原因");
        returnReason.put("3", "商品尺寸不对");
        returnReason.put("4", "客服原因");
        returnReason.put("5", "客服服务差");
        returnReason.put("6", "客服不理会");
        returnReason.put("7", "拒收");
        returnReason.put("8", "维修");
        Map<Object, Object> billType = new HashMap<>();
        billType.put(1, "退货单");
        billType.put(2, "退换货单");
        Map<Object, Object> returnStatus = new HashMap<>();
        returnStatus.put(20, "等待退货入库");
        returnStatus.put(30, "等待售后确认");
        returnStatus.put(50, "完成");
        returnStatus.put(60, "取消");

        Map<Object, Object> proReturnStatus = new HashMap<>();
        proReturnStatus.put(0, "待入库");
        proReturnStatus.put(1, "部分入库");
        proReturnStatus.put(2, "全部入库");

        Map<Object, Object> genMark = new HashMap<>();
        genMark.put("1", "无名件已入库");

        Map<Object, Object> differenceMark = new HashMap<>();
        differenceMark.put(0, "零");
        differenceMark.put(1, "少退");
        differenceMark.put(2, "多退");

        m1.put("is_back", isBack);
        m1.put("is_reserved", isReserved);
        m1.put("return_reason", returnReason);
        m1.put("bill_type", billType);
        m1.put("return_status", returnStatus);
        m1.put("pro_return_status", proReturnStatus);
        m1.put("generic_mark", genMark);
        m1.put("difference_mark", differenceMark);

        Map<String, Map<Object, Object>> m2 = csm.get("oc_b_return_order_refund");
        if (m2 == null) {
            m2 = new HashMap<>();
            csm.put("oc_b_return_order_refund", m2);
        }
        Map<Object, Object> productMark = new HashMap<>();
        productMark.put("0", "次品");
        productMark.put("1", "正品");
        m2.put("product_mark", productMark);

    }
}
