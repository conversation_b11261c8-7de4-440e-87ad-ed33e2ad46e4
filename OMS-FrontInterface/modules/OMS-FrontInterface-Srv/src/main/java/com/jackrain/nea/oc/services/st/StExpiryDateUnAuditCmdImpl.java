package com.jackrain.nea.oc.services.st;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ac.utils.ValueHolderV14Utils;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StExpiryDateUnAuditCmd;
import com.jackrain.nea.st.service.StExpiryDateAuditService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import io.searchbox.strings.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @Author: 黄世新
 * @Date: 2022/6/15 上午11:15
 * @Version 1.0
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StExpiryDateUnAuditCmdImpl extends CommandAdapter implements StExpiryDateUnAuditCmd {

    @Autowired
    private StExpiryDateAuditService stExpiryDateAuditService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stExpiryDateAuditService.expiryDateUnAuditService(session);
    }

    @OmsOperationLog(configurationFlag = false, operationType = "RESERVE_AUDIT", mainTableName = "ST_C_EXPIRY_DATE", itemsTableName = "ST_C_EXPIRY_DATE_ITEM", customizeLogAopKey = "ST_C_EXPIRY_DATE_RESERVE_AUDIT")
    @Override
    public ValueHolderV14 unAuditNotClearRedis(JSONObject param, User user){
        try {
            if(Objects.isNull(param)){
                throw new NDSException("参数不能为空");
            }
            String objidStr = param.getString("objid");
            if(StringUtils.isBlank(objidStr)){
                throw new NDSException("请选择一条数据");
            }
            if(objidStr.contains(",")){
                throw new NDSException("只能选择一条数据执行此操作");
            }
            Long objid = Long.parseLong(objidStr);
            stExpiryDateAuditService.expiryDateUnAudit(objid, user, false);
            return ValueHolderV14Utils.getSuccessValueHolder("success");
        }catch (Exception e){
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
    }
}
