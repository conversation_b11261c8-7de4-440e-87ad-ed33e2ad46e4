package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderOffCmd;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.services.OcBOrderOffService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 取消订单
 *
 * @date 2019/3/7
 * @author: ming.fz
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderOffCmdImpl implements OcBOrderOffCmd {
    @Autowired
    OcBOrderOffService ocBOrderOffService;

    @Override
    public ValueHolderV14 updateStatus(OrderICheckRequest param, User user) throws NDSException {

        return ocBOrderOffService.updateStatus(param, user);
    }

    @Override
    public ValueHolderV14 qmOrderService(JSONObject obj, User user) {
        return ocBOrderOffService.qmOrderService(obj,user);
    }

}