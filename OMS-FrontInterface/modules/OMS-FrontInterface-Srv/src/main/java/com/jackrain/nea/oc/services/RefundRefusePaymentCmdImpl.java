package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.RefundRefusePaymentCmd;
import com.jackrain.nea.oc.oms.services.RefundFormAfterDeliveryService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 发货后退款单 拒绝打款
 *
 * @author: 江家雷
 * @since: 2020/08/14
 * create at :2020/08/14 10:26
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RefundRefusePaymentCmdImpl extends CommandAdapter implements RefundRefusePaymentCmd {
    @Autowired
    private RefundFormAfterDeliveryService refundFormAfterDeliveryService;
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        User user = querySession.getUser();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONArray ids = param.getJSONArray("ids");
        String reason  = param.getString("reason");
        if (ids.isEmpty()) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "请勾选一条数据来操作");
            return vh;
        }
        if(ids.size() > 1){
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "只支持单个订单拒绝打款");
            return vh;
        }
        JSONArray array = new JSONArray();
        for (int i = 0; i < ids.size(); i++) {
            array.add(ids.getLong(i));
        }
        return refundFormAfterDeliveryService.refuseToPayOcBReturnAfSend(ids, reason, user);
    }

}
