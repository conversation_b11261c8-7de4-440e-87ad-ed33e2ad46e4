package com.jackrain.nea.oc.services.patrol;

import com.jackrain.nea.oc.oms.api.patrol.ReturnOrderProblemCmd;
import com.jackrain.nea.oc.oms.services.patrol.ReturnOrderAmtIsZeroService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/6/6 9:58 AM
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ReturnOrderProblemService implements ReturnOrderProblemCmd {

    @Autowired
    private ReturnOrderAmtIsZeroService returnOrderAmtIsZeroService;

    @Override
    public List<Long> selectOrderItem(int page, int size) {
        return returnOrderAmtIsZeroService.selectOrderItem(page, size);
    }

    @Override
    public List<String> selectOrderItemOoid(Date beginTime, Date endTime) {
        return returnOrderAmtIsZeroService.selectOrderItemOoid(beginTime, endTime);
    }

    @Override
    public List<String> selectReturnOrderNotItem(Date beginTime, Date endTime) {
        return returnOrderAmtIsZeroService.selectReturnOrderNotItem(beginTime, endTime);
    }
}
