package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ThirdPartyInterfaceLogQueryCmd;
import com.jackrain.nea.oc.oms.services.ThirdPartyInterfaceLogQueryService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @ClassName ThirdPartyInterfaceLogQueryCmdImpl
 * @Description 第三方接口日志查询
 * <AUTHOR>
 * @Date 2025/4/22 19:43
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ThirdPartyInterfaceLogQueryCmdImpl implements ThirdPartyInterfaceLogQueryCmd {

    @Autowired
    private ThirdPartyInterfaceLogQueryService thirdPartyInterfaceLogQueryService;

    @Override
    public ValueHolder execute(QuerySession session) {
        return thirdPartyInterfaceLogQueryService.queryThirdPartyInterfaceLog(session);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
