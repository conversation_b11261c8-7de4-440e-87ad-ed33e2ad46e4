package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderWarningManageCmd;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderWarningManageService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2021/12/16 10:36
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderWarningManageCmdImpl extends CommandAdapter implements OcBReturnOrderWarningManageCmd {

    @Autowired
    private OcBReturnOrderWarningManageService ocBReturnOrderWarningManageService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ocBReturnOrderWarningManageService.execute(session);
    }
}
