package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.RetransmissionWmsCmd;
import com.jackrain.nea.oc.oms.services.RetransmissionWmsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-07-22
 * create at : 2019-07-22 9:38 AM
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RetransmissionWmsImpl implements RetransmissionWmsCmd {

    @Autowired
    private RetransmissionWmsService service;

    @Override
    public ValueHolderV14 retransmissionWms(String param, User loginUser) {
        return service.retransmissionWms(param, loginUser);
    }
}
