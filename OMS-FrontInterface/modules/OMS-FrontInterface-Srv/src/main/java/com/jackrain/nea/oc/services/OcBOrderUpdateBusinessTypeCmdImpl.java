package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcBOrderUpdateBusinessTypeCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderUpdateBusinessTypeService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * @Auther: chenhao
 * @Date: 2022-08-23 13:52
 * @Description:
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderUpdateBusinessTypeCmdImpl implements OcBOrderUpdateBusinessTypeCmd {


    @Override
    public ValueHolder updateBusinessType(JSONObject obj, User user) {
        OcBOrderUpdateBusinessTypeService bean = ApplicationContextHandle.getBean(OcBOrderUpdateBusinessTypeService.class);
        return bean.updateBusinessType(obj, user);
    }
}
