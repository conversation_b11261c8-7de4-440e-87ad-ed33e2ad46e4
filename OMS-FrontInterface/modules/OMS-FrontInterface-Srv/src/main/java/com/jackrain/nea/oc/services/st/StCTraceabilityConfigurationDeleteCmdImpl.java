package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCTraceabilityConfigurationDeleteCmd;
import com.jackrain.nea.st.service.StCTraceabilityConfigurationService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-oc-oms
 * @description: 溯源配置表删除
 * @author: lijin
 * @create: 2024-12-19
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCTraceabilityConfigurationDeleteCmdImpl extends CommandAdapter implements StCTraceabilityConfigurationDeleteCmd {
    
    @Autowired
    private StCTraceabilityConfigurationService stCTraceabilityConfigurationService;
    
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return stCTraceabilityConfigurationService.delete(querySession);
    }
}
