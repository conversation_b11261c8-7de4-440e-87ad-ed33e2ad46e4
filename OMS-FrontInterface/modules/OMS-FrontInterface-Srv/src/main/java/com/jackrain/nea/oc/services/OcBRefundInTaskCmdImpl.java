package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBRefundInTaskSaveCmd;
import com.jackrain.nea.oc.oms.services.task.OcBRefundInTaskService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: qinjunlong
 * @since: 2019-03-13
 * create at:  2019-03-13 16:10
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBRefundInTaskCmdImpl extends CommandAdapter implements OcBRefundInTaskSaveCmd {


    @Autowired
    private OcBRefundInTaskService ocBRefundInTaskService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ocBRefundInTaskService.execute(session);
    }
}

