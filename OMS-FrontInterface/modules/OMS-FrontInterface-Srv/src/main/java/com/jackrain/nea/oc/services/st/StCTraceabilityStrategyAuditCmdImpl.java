package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCTraceabilityStrategyAuditCmd;
import com.jackrain.nea.st.service.StCTraceabilityStrategyAuditService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略审核
 * @author: lijin
 * @create: 2024-12-19
 **/
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCTraceabilityStrategyAuditCmdImpl extends CommandAdapter implements StCTraceabilityStrategyAuditCmd {

    @Autowired
    private StCTraceabilityStrategyAuditService stCTraceabilityStrategyAuditService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCTraceabilityStrategyAuditService.auditTraceabilityStrategy(session);
    }
}
