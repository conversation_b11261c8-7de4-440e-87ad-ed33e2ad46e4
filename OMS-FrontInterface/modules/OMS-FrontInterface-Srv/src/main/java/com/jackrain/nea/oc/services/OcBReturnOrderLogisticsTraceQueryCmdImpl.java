package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderLogisticsTraceQueryCmd;
import com.jackrain.nea.oc.oms.services.ReturnOrderLogisticSubscribeFromKdzsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 退单查询物流轨迹
 * @DateTime 2022/4/2 11:05
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderLogisticsTraceQueryCmdImpl implements OcBReturnOrderLogisticsTraceQueryCmd {
    @Autowired
    private ReturnOrderLogisticSubscribeFromKdzsService returnOrderLogisticSubscribeFromKdzsService;

    @Override
    public ValueHolderV14 execute(JSONObject param, User user) {
        return returnOrderLogisticSubscribeFromKdzsService.queryTrace(param, user);
    }
}
