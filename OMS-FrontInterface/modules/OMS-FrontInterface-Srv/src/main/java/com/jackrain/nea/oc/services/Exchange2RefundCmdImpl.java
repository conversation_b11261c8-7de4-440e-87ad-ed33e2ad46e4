package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.Exchange2RefundCmd;
import com.jackrain.nea.oc.oms.services.Exchange2RefundService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: lyj
 * @since: 2021/10/13
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class Exchange2RefundCmdImpl extends CommandAdapter implements Exchange2RefundCmd {

    @Autowired
    private Exchange2RefundService exchange2RefundService;


    @Override
    public ValueHolderV14 exchange2RefundCmd(JSONObject obj, User user) throws NDSException {
        return exchange2RefundService.exchange2Refund(obj,user);
    }
}
