package com.jackrain.nea.oc.services.dms;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.dms.DmsOrderCancelCmd;
import com.jackrain.nea.oc.oms.dms.DmsCancelService;
import com.jackrain.nea.oc.oms.model.request.DmsCancelRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * dms单据取消
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class DmsOrderCancelCmdImpl implements DmsOrderCancelCmd {

    @Autowired
    private DmsCancelService dmsCancelService;


    /**
     * 订单取消-dms
     *
     * @param params
     * @return
     */
    @Override
    public ValueHolderV14 cancelOrder(JSONObject params) {
        DmsCancelRequest dmsCancelRequest;
        try {
            dmsCancelRequest = JSONObject.parseObject(params.toString(), DmsCancelRequest.class);
        } catch (Exception e) {
            log.info(LogUtil.format("cancelOrder parseObject error params:{}", "dmsOrderCancel"), params, e);
            return ValueHolderV14Utils.getFailValueHolder("参数格式化异常!请检查入参!");
        }

        return dmsCancelService.cancelOrder(dmsCancelRequest);
    }

    /**
     * 退单取消-dms
     *
     * @param params
     * @return
     */
    @Override
    public ValueHolderV14 cancelReturn(JSONObject params) {
        DmsCancelRequest dmsCancelRequest;
        try {
            dmsCancelRequest = JSONObject.parseObject(params.toString(), DmsCancelRequest.class);
        } catch (Exception e) {
            log.info(LogUtil.format("cancelReturn parseObject error params:{}", "dmsReturnCancel"), params, e);
            return ValueHolderV14Utils.getFailValueHolder("参数格式化异常!请检查入参!");
        }

        return dmsCancelService.cancelReturn(dmsCancelRequest);
    }
}
