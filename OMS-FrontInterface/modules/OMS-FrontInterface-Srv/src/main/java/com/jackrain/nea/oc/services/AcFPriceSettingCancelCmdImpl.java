package com.jackrain.nea.oc.services;

import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.AcFPriceSettingCancelCmd;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.AcFPriceSetting;
import com.jackrain.nea.oc.oms.services.AcFPriceSettingService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.HashMap;

/**
 * className: AcFPriceSettingCancelCmdImpl
 * description:全渠道价格配置作废
 *
 * <AUTHOR>
 * create: 2021-10-27
 * @since JDK 1.8
 */
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFPriceSettingCancelCmdImpl extends CommandAdapter implements AcFPriceSettingCancelCmd {

    @Autowired
    private AcFPriceSettingService priceSettingService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {

        ValueHolder valueHolder = CommandAdapterUtil.checkDeleteSession(session, OcCommonConstant.AC_F_PRICE_SETTING);
        if (!valueHolder.isOK()) {
            return valueHolder;
        }
        Long id = (Long)((HashMap)valueHolder.getData().get("data")).get(OcCommonConstant.OBJ_ID);
        User user = session.getUser();
        AcFPriceSetting priceSetting = new AcFPriceSetting();
        priceSetting.setId(id);
        priceSetting.setIsactive(R3CommonResultConstants.VALUE_N);
        priceSetting.setCancelDate(new Date());
        if(!ObjectUtils.isEmpty(user)){
            priceSetting.setCancelId(Long.valueOf(user.getId()));
        }

        valueHolder = priceSettingService.save(priceSetting,user);
        if(valueHolder.isOK()){
            valueHolder = ValueHolderUtils.success("作废成功！");
        }
        return valueHolder;
    }

}
