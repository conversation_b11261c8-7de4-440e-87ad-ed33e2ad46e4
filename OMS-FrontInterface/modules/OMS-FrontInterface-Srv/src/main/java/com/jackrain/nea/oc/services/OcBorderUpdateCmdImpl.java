package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.ReginQueryResult;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBorderUpdateCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.order.address.AddressFrontDto;
import com.jackrain.nea.oc.oms.model.order.address.ReceiverAddressDto;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OcBorderUpdateService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;


/**
 * @author: 孙俊磊
 * @since: 2019-03-13
 * create at:  2019-03-13 16:10
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBorderUpdateCmdImpl implements OcBorderUpdateCmd {

    @Autowired
    private OcBorderUpdateService service;

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Override
    public ValueHolderV14 updateReceiveAddress(String param, User loginUser) {
        return service.updateReceiveAddress(JSON.parseObject(param), loginUser, true);
    }

    @Override
    public ValueHolderV14 updateReceiveAddressNew(String param, User loginUser) throws NDSException {
        try {
            ValueHolderV14 vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            if(StringUtils.isBlank(param)){
                vh.setMessage("缺少参数param");
                return vh;
            }
            ValueHolderV14<AddressFrontDto> vhAddressFrontDto = jsonToFrontDto(param);
            if(!vhAddressFrontDto.isOK()){
                vh.setMessage(vhAddressFrontDto.getMessage());
                return vh;
            }
            AddressFrontDto addressFrontDto = vhAddressFrontDto.getData();
            if (addressFrontDto == null) {
                vh.setMessage("不正确的参数param");
                return vh;
            }
            ReceiverAddressDto addressDto = addressFrontDto.getUpdateInfo();
            if (addressDto == null) {
                vh.setMessage("不正确的参数param");
                return vh;
            }
            OcBOrder ocBOrder = ocBOrderMapper.selectById(addressFrontDto.getId());

            if (PlatFormEnum.JINGDONG.getCode().equals(ocBOrder.getPlatform())) {
                if (StringUtils.isEmpty(addressDto.getPlatformProvince()) ||
                        StringUtils.isEmpty(addressDto.getPlatformCity()) ||
                        StringUtils.isEmpty(addressDto.getPlatformArea())) {
                    vh.setMessage("京东平台修改地址平台地址不能为空");
                    return vh;
                }
            }

            ValueHolder valueHolder = cpRpcService.getRegionNameByid(addressDto.getCpCRegionProvinceId(), addressDto.getCpCRegionCityId(), addressDto.getCpCRegionAreaId());
            if (valueHolder == null || valueHolder.get("data") == null) {
                vh.setMessage("省市区不正确");
                return vh;
            }
            ReginQueryResult reginQueryResult = (ReginQueryResult) valueHolder.get("data");
            if (reginQueryResult == null) {
                vh.setMessage("省市区不正确");
                return vh;
            }

            addressDto.setId(addressFrontDto.getId());

            addressDto.setCpCRegionProvinceEcode(reginQueryResult.getProvEcode());
            addressDto.setCpCRegionProvinceEname(reginQueryResult.getProvName());
            addressDto.setCpCRegionCityEcode(reginQueryResult.getCityEcode());
            addressDto.setCpCRegionCityEname(reginQueryResult.getCityName());
            addressDto.setCpCRegionAreaId(Objects.isNull(reginQueryResult.getRegionID()) ? 0L : reginQueryResult.getRegionID());
            addressDto.setCpCRegionAreaEcode(Objects.isNull(reginQueryResult.getRegionEcode()) ? "" : reginQueryResult.getRegionEcode());
            addressDto.setCpCRegionAreaEname(Objects.isNull(reginQueryResult.getRegionName()) ? "" : reginQueryResult.getRegionName());
            addressDto.setCpCRegionTownEname(addressDto.getCpCRegionTownEname());
            addressDto.setIsPlainAddr(addressDto.getIsPlainAddr());

//            ValueHolderV14 vhRaw = updateAddressBizService.updateReceiveAddressLock(addressDto, loginUser,true);
            ValueHolderV14 vhRaw = service.updateReceiveAddressNew(addressDto, loginUser, true,true);
            if(!vhRaw.isOK()){
                vh.setMessage(vhRaw.getMessage());
                return vh;
            }
            vh.setCode(ResultCode.SUCCESS);
            vh.setMessage(vhRaw.getMessage());
            vh.setData(vhRaw.getData());
            return vh;
        }catch (Exception ex){
            String msg = ExceptionUtils.getStackTrace(ex);
            ValueHolderV14 vh= new ValueHolderV14(ResultCode.FAIL,String.format("修改地址页面异常rpc %s", msg));
            return vh;
        }
    }

    public ValueHolderV14<AddressFrontDto> jsonToFrontDto(String jsonParam)  {
        try {
            ObjectMapper mapper = new ObjectMapper();
            AddressFrontDto dto = mapper.readValue(jsonParam, AddressFrontDto.class);
            return new ValueHolderV14<>(dto,ResultCode.SUCCESS,"");
        }catch (Exception e){
            String msg =ExceptionUtils.getStackTrace(e);
            return new ValueHolderV14<>(ResultCode.FAIL,msg);
        }
    }
}

