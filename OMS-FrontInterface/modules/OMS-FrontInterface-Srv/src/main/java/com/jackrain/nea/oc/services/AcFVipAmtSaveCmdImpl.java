package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.AcFVipAmtSaveCmd;
import com.jackrain.nea.oc.oms.services.AcFVipAmtSaveService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * className: AcFVipAmtSaveCmdImpl
 * description:唯品会商品销售额新增保存
 *
 * <AUTHOR>
 * create: 2021-06-19
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFVipAmtSaveCmdImpl extends CommandAdapter implements AcFVipAmtSaveCmd {

    @Autowired
    private AcFVipAmtSaveService vipAmtSaveService;


    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return vipAmtSaveService.execute(session);
    }

}
