package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ModifyGoodsCmd;
import com.jackrain.nea.oc.oms.services.ModifyGoodsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 周琳胜
 * @since: 2019/3/12
 * create at : 2019/3/12 11:43
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ModifyGoodsCmdImpl implements ModifyGoodsCmd {
    @Autowired
    ModifyGoodsService modifyGoodsService;

    @Override
    public ValueHolderV14 execute(JSONObject obj, User user) throws NDSException {
        return modifyGoodsService.modifyGoods(obj, user);
    }
}
