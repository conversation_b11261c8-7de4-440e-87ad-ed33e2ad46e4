package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.ShortageSplitCmd;
import com.jackrain.nea.oc.oms.services.ShortageSplitService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 缺货拆单
 * @author: 江家雷
 * @since: 2020-11-09
 * create at:  2020-11-09 18:30
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ShortageSplitCmdImpl implements ShortageSplitCmd {

    @Autowired
    private ShortageSplitService shortageSplitService;

    @Override
    public ValueHolderV14<JSONArray> splitShortgateOrder(JSONObject obj, User user) {
        return shortageSplitService.splitShortageOrder(obj, user);
    }

}
