package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.InvoicNoticeVoidCmd;
import com.jackrain.nea.oc.oms.services.InvoiceNoticeVoidService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: chenxiulou
 * @description: 开票通知 作废
 * @since: 2019-07-20
 * create at : 2019-07-20 17:51
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InvoicNoticeVoidCmdImpl extends CommandAdapter implements InvoicNoticeVoidCmd {
    @Autowired
    private InvoiceNoticeVoidService noticeVoidService;

    @Override
    public ValueHolder voidInvoiceNotice(QuerySession session) throws NDSException {
        return noticeVoidService.voidInvoiceNotice(session);
    }
}
