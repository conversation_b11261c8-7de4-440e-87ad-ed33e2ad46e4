package com.jackrain.nea.oc.services.sap;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.sap.OcBSapSalesDataRecordSaveCmd;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordSaveService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description: 销售数据记录表新增保存
 *
 * @Author: guo.kw
 * @Since: 2022/8/23
 * create at: 2022/8/23 10:47
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBSapSalesDataRecordSaveImpl extends CommandAdapter implements OcBSapSalesDataRecordSaveCmd{
    @Autowired
    private OcBSapSalesDataRecordSaveService service;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return service.save(session);
    }

}
