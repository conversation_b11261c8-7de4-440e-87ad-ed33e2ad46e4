package com.jackrain.nea.oc.services.sap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.sap.OcBSapSalesDataRecordAddTaskManualCmd;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordAddTaskService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/20
 * description :
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBSapSalesDataRecordAddTaskManualCmdImpl extends CommandAdapter implements OcBSapSalesDataRecordAddTaskManualCmd {
    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        String objId = param.getString("objid");
        User user = session.getUser();

        OcBSapSalesDataRecordAddTaskService service = ApplicationContextHandle.getBean(OcBSapSalesDataRecordAddTaskService.class);

        int failCount = 0;
        JSONArray errorInfo = new JSONArray();

        if (null == objId) {

            JSONArray ids = param.getJSONArray("ids");

            for (Object idObject : ids) {
                long id = Long.parseLong(String.valueOf(idObject));
                ValueHolderV14 holderV14 = service.doAdd(id, user);

                if (!holderV14.isOK()) {

                    JSONObject errorInfoObj = new JSONObject();
                    errorInfoObj.put("objid", id);
                    errorInfoObj.put("ecode", -1);
                    errorInfoObj.put("message", holderV14.getMessage());
                    errorInfo.add(errorInfoObj);

                    failCount++;
                }
            }

            int successCount = ids.size() - failCount;
            if (errorInfo.size() > 0) {
                vh.put("data", errorInfo);
                vh.put("code", -1);
                vh.put("message", "成功" + successCount + "条,失败" + failCount + "条");
            } else {
                vh.put("code", 0);
                vh.put("message", "成功" + successCount + "条");
            }
        } else {

            long id = Long.parseLong(objId);

            ValueHolderV14 holderV14 = service.doAdd(id, user);

            if (!holderV14.isOK()) {
                vh.put("code", -1);
                vh.put("message", holderV14.getMessage());
            } else {
                vh.put("code", 0);
                vh.put("message", "处理成功");
            }
        }
        return vh;
    }
}
