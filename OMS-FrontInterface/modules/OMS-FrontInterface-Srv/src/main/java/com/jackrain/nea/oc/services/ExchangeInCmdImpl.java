package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.ExchangeInCmd;
import com.jackrain.nea.oc.oms.services.ExchangeInService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 李杰
 * @since: 2019/4/2
 * create at : 2019/4/2 9:14
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ExchangeInCmdImpl implements ExchangeInCmd {

    @Autowired
    private ExchangeInService exchangeInService;

    @Override
    public ValueHolderV14 exchangeIn(Long id, User user) {
        return exchangeInService.exchangeIn(id, user);
    }
}
