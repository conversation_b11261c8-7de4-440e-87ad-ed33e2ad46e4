package com.jackrain.nea.oc.services.ac;

import com.jackrain.nea.ac.service.PayableAdjustmentConfirmResponsiblePartyService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ac.OmsPayableAdjustmentConfirmResponsiblePartyCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description：确认责任方
 *
 * <AUTHOR>
 * @date 2021/5/19
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsPayableAdjustmentConfirmResponsiblePartyCmdImpl extends CommandAdapter implements OmsPayableAdjustmentConfirmResponsiblePartyCmd {
    @Autowired
    private PayableAdjustmentConfirmResponsiblePartyService payableAdjustmentConfirmResponsiblePartyService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return payableAdjustmentConfirmResponsiblePartyService.execute(querySession);
    }
}