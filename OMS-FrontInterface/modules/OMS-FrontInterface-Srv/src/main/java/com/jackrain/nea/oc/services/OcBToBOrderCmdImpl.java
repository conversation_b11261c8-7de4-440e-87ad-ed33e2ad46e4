package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBToBOrderCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBToBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBToBOrder;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName OcBToBOrderCmdImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/11/28 11:49
 * @Version 1.0
 */
@Service
@Slf4j
public class OcBToBOrderCmdImpl implements OcBToBOrderCmd {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBToBOrderMapper toBOrderMapper;
    @Autowired
    private BuildSequenceUtil buildSequenceUtil;

    @Override
    public ValueHolderV14 updateRemark(JSONObject param) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        JSONArray jsonArray = param.getJSONArray("ids");
        String remark = param.getString("remark");
        if (jsonArray == null) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("参数有误");
            return holderV14;
        }
        // jsonArray 转换成List<Long>
        List<Long> ids = JSONObject.parseArray(JSONObject.toJSONString(jsonArray), Long.class);
        List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(ids);
        if (CollectionUtils.isEmpty(ocBOrderList)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("参数有误");
            return holderV14;
        }
        // 根据ocBOrderList 过滤出来tid 并且去重
        List<String> tidList = ocBOrderList.stream().map(OcBOrder::getTid).distinct().collect(Collectors.toList());
        List<OcBToBOrder> insertList = new ArrayList<>();
        List<OcBToBOrder> updateList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(tidList)) {
            for (OcBOrder ocBOrder : ocBOrderList) {
                // 根据tid来查询
                OcBToBOrder ocBToBOrder = toBOrderMapper.selectByOcBOrderId(ocBOrder.getId());
                if (ocBToBOrder != null) {
                    OcBToBOrder update = new OcBToBOrder();
                    update.setRemark(remark);
                    update.setId(ocBToBOrder.getId());
                    BaseModelUtil.makeBaseModifyField(update, null);
                    updateList.add(update);
                } else {
                    ocBToBOrder = new OcBToBOrder();
                    Long id = buildSequenceUtil.buildToBOrderSequenceId();
                    ocBToBOrder.setRemark(remark);
                    ocBToBOrder.setId(id);
                    ocBToBOrder.setTid(ocBOrder.getTid());
                    ocBToBOrder.setOcBOrderId(ocBOrder.getId());
                    BaseModelUtil.initialBaseModelSystemField(ocBToBOrder);
                    insertList.add(ocBToBOrder);
                }
            }
            ApplicationContextHandle.getBean(OcBToBOrderCmdImpl.class).insertToBOrder(insertList, updateList);
            holderV14.setCode(ResultCode.SUCCESS);
            holderV14.setMessage("更新成功");
            return holderV14;
        }
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setMessage("更新成功");
        return holderV14;
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertToBOrder(List<OcBToBOrder> insertList, List<OcBToBOrder> updateList) {
        if (CollectionUtils.isNotEmpty(insertList)) {
            toBOrderMapper.batchInsert(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            for (OcBToBOrder item : updateList) {
                toBOrderMapper.updateById(item);
            }
        }
    }
}
