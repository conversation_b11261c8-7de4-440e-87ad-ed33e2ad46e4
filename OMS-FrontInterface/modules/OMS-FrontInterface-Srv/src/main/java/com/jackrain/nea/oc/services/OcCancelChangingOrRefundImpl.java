package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcCancelChangingOrRefundCmd;
import com.jackrain.nea.oc.oms.services.OcCancelChangingOrRefundService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2019/3/12
 * create at : 2019/3/12 20:55
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcCancelChangingOrRefundImpl implements OcCancelChangingOrRefundCmd {
    @Autowired
    OcCancelChangingOrRefundService orRefundService;

    @Override
    public ValueHolderV14 ocCancelChangingOrRefund(JSONObject obj, User user) throws NDSException {
        return orRefundService.orRefundService(obj, user, Boolean.TRUE);
    }

    @Override
    public ValueHolderV14 checkCancelReqParams(JSONObject obj, User user) {
        return orRefundService.checkCancelReqParams(obj, user);
    }

    @Override
    public ValueHolderV14 qmRefundService(JSONObject obj, User user) {
        return orRefundService.qmRefundService(obj,user);
    }
}
