package com.jackrain.nea.oc.services.directreport;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.directreport.OcBDirectReportOrderVoidCmd;
import com.jackrain.nea.oc.oms.services.directreport.OcBDirectReportOrderVoidService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 直发预占-作废
 *
 * <AUTHOR>
 * @since 2024-11-28 15:47
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBDirectReportOrderVoidCmdImpl extends CommandAdapter implements OcBDirectReportOrderVoidCmd {
    @Resource
    private OcBDirectReportOrderVoidService ocBDirectReportOrderVoidService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        log.info(LogUtil.format("直发预占作废-参数：{}",
                "OcBDirectReportOrderVoidCmdImpl.execute"), JSON.toJSONString(param));

        Long objId = param.getLong("objid");
        try {
            ocBDirectReportOrderVoidService.voidOrder(objId, session.getUser());
        } catch (NDSException e) {
            log.error(LogUtil.format("直发预占作废-异常：{}",
                    "OcBDirectReportOrderVoidCmdImpl.execute"), Throwables.getStackTraceAsString(e));
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        }

        return ValueHolderUtils.getSuccessValueHolder(objId, "OC_B_DIRECT_REPORT_ORDER");
    }
}
