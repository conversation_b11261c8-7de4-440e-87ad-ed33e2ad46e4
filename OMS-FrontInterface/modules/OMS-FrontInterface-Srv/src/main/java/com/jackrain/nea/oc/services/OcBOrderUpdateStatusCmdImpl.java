package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBOrderUpdateStatusCmd;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoOrderCycleBuyMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderCycleBuy;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryProcessor;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.CollectorsUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderUpdateStatusCmdImpl implements OcBOrderUpdateStatusCmd {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OrderDeliveryProcessor orderDeliveryProcessor;

    @Autowired
    private OmsOrderLogService orderLogService;

    @Autowired
    private IpBTaobaoOrderCycleBuyMapper ipBTaobaoOrderCycleBuyMapper;

    /**
     * 将已取消的订单状态改为平台发货，并且记录日志
     *
     * @param ids
     * @return
     */
    @Override
    public ValueHolderV14 uodateById(List<Long> ids, User user) {

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(ids);
        if (CollectionUtils.isEmpty(ocBOrders)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("当前查询无记录");
            return v14;
        }
        Integer successNum = 0;
        Integer errorNum = 0;

        for (OcBOrder ocBOrder : ocBOrders) {
            if (OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus())) {
                ocBOrder.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                ocBOrderMapper.updateById(ocBOrder);
                orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_DELIVERY.getKey(), "历史取消订单手工修改为平台发货", "", "", user);
                successNum++;
            } else if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())) {
                List<Long> list = new ArrayList<>();
                list.add(ocBOrder.getId());
                ValueHolder valueHolder = orderSend(list);
                log.info(" 历史仓库发货订单触发平台发货 {}",JSON.toJSONString(valueHolder));
                if(valueHolder.isOK()){
                    successNum++;
                }else {
                    errorNum++;
                }
            } else {
                orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_DELIVERY.getKey(), "该订单非取消状态或仓库发货不允许修改为平台发货", "", "", user);
                errorNum++;
            }
        }
        v14.setMessage(String.format("执行成功记录数：%s，执行失败记录数：%s", successNum, errorNum));
        return v14;
    }

    public ValueHolder orderSend(List<Long> list) {
        HashMap map = new HashMap();
        ValueHolder result = new ValueHolder();
        if (CollectionUtils.isEmpty(list)) {
            map.put("code", ResultCode.FAIL);
            map.put("message", "参数不能为空");
            map.put("data", new HashMap<>());
            result.setData(map);
            return result;
        }
        StringBuffer errormassage = new StringBuffer();
        StringBuilder successMessage = new StringBuilder();
        try {
            for (Long objId : list) {
                OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                OcBOrder dbOcBOrder = ocBOrderMapper.selectByID(objId);
                if (null == dbOcBOrder) {
                    errormassage.append("[订单编号:" + objId + ":不存在]</br>");
                    continue;
                }
                if (!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(dbOcBOrder.getOrderStatus())) {
                    errormassage.append("[订单编号" + objId + ":状态非仓库发货，不允许平台发货！]</br>");
                    continue;
                }
                if (dbOcBOrder.getIsInreturning().equals(InreturningStatus.INRETURNING)) {
                    //如果是退款中不允许平台发货走强制发货
                    errormassage.append("[订单编号" + objId + ":退货中，请强制平台发货！]</br>");
                    continue;
                }
                if (OcBOrderConst.IS_STATUS_IY.equals(dbOcBOrder.getIsCycle())) {
                    IpBTaobaoOrderCycleBuy ipBTaobaoOrderCycleBuy = ipBTaobaoOrderCycleBuyMapper.selectOrderCycleBuyCurrPhase(dbOcBOrder.getTid(), dbOcBOrder.getCurrentCycleNumber());
                    //回传时，判断订单是否已退款，若“天猫周期购明细”中计划状态=“7”且计划退款状态=“3”则不进行回传，反之回传；
                    if (ipBTaobaoOrderCycleBuy != null && ipBTaobaoOrderCycleBuy.getPlanStatus() == 7 && ipBTaobaoOrderCycleBuy.getPlanRefundStatus() == 3) {
                        errormassage.append("[订单编号" + objId + ":状态已退款，不允许平台发货！]</br>");
                        continue;
                    }
                }
                ocBOrderRelation.setOrderInfo(dbOcBOrder);
                ocBOrderRelation.setAutomaticOperation(false);
                boolean orderResult = orderDeliveryProcessor.platformSend(ocBOrderRelation);
                if (false == orderResult) {
                    errormassage.append("[订单编号:" + objId + ":平台发货失败]</br>");
                    continue;
                }
                successMessage.append("[订单编号:" + objId + ":平台发货中 请稍后查看订单]</br>");
            }
        } catch (Exception e) {
            log.error("批量平台发货异常", e.getMessage());
            throw e;
        }
        if (StringUtils.isNotEmpty(errormassage.toString())) {
            map.put("code", ResultCode.FAIL);
            map.put("message", errormassage.toString() + successMessage.toString());
            map.put("data", new HashMap<>());
            if (log.isDebugEnabled()) {
                log.debug("批量平台发货按钮存在失败返回结果:" + result.toDebugString());
            }
            result.setData(map);
            return result;
        }
        map.put("code", ResultCode.SUCCESS);
        map.put("message", successMessage.toString());
        map.put("data", new HashMap<>());
        result.setData(map);
        if (log.isDebugEnabled()) {
            log.debug("批量平台发货按钮返回结果:" + result.toDebugString());
        }
        return result;
    }
}
