package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderTheAuditCmd;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.services.OcBOrderTheAuditService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单反审核
 *
 * @date 2019/3/12
 * @author: ming.fz
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderTheAuditCmdImpl implements OcBOrderTheAuditCmd {

    @Autowired
    OcBOrderTheAuditService ocBOrderTheAuditService;

    @Override
    public ValueHolderV14 orderTheAudit(OrderICheckRequest param, User user) throws NDSException {
        return ocBOrderTheAuditService.batchOrderTheAudit(param, user);
    }

}