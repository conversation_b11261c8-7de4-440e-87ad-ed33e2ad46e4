package com.jackrain.nea.oc.services;


import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderBatchAddCmd;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderBatchAddService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 批量新增退单
 *
 * @author: 郑小龙
 * @since: 2020/03/12
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderBatchAddCmdImpl implements OcBReturnOrderBatchAddCmd {

    @Autowired
    OcBReturnOrderBatchAddService service;

    @Override
    public ValueHolderV14 ReturnOrderBatchAdd(List<Long> ids, boolean isback, User user) throws NDSException {
        return service.ReturnOrderBatchAdd(ids, isback, user);
    }

}
