package com.jackrain.nea.oc.services.patrol;

import com.jackrain.nea.oc.oms.api.patrol.DeleteOrderInfoCmd;
import com.jackrain.nea.oc.oms.services.patrol.DeleteOrderInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2019-07-25 21:23
 * @Version 1.0
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class DeleteOrderInfoImpl implements DeleteOrderInfoCmd {

    @Autowired
    private DeleteOrderInfoService deleteOrderInfoService;


    @Override
    public String deleteOrderInfo(Long id, String type) {
        String s = deleteOrderInfoService.deleteOrderInfo(id, type);
        return s;
    }
}
