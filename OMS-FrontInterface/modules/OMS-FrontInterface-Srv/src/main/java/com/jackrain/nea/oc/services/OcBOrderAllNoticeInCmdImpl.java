package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderAllNoticeInCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderAllRefundService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * className: OcBOrderAllNoticeInCmdImpl
 * description:全渠道订单通知发货方入库
 *
 * <AUTHOR>
 * create: 2021-11-17
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderAllNoticeInCmdImpl extends CommandAdapter implements OcBOrderAllNoticeInCmd {

    @Autowired
    private OcBOrderAllRefundService allRefundService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return allRefundService.noticeIn(session);
    }

}
