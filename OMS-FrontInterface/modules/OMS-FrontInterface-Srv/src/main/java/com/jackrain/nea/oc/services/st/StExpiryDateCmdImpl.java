package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StExpiryDateCmd;
import com.jackrain.nea.st.service.StExpiryDateAddService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2022/6/14 上午10:13
 * @Version 1.0
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StExpiryDateCmdImpl extends CommandAdapter implements StExpiryDateCmd {

    @Autowired
    private StExpiryDateAddService stExpiryDateService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stExpiryDateService.expiryDateService(session);
    }
}
