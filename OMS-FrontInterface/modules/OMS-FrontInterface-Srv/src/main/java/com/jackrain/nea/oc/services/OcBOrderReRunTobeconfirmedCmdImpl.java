package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderReRunTobeconfirmedCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderReRunTobeconfirmedService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderReRunTobeconfirmedCmdImpl
 * @Description 重新执行待分配任务实现类
 * <AUTHOR>
 * @Date 2025/5/23 14:26
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderReRunTobeconfirmedCmdImpl implements OcBOrderReRunTobeconfirmedCmd {

    @Autowired
    private OcBOrderReRunTobeconfirmedService ocBOrderReRunTobeconfirmedService;

    @Override
    public ValueHolderV14 reRunTobeconfirmed(List<Long> ids, User user) throws NDSException {
        return ocBOrderReRunTobeconfirmedService.reRunTobeconfirmed(ids, user);
    }
}
