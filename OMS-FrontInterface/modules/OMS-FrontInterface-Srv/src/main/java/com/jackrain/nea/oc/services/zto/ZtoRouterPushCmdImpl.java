package com.jackrain.nea.oc.services.zto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.ip.model.result.zto.ZtoRouterPushRequest;
import com.jackrain.nea.ip.model.result.zto.ZtoRouterResponse;
import com.jackrain.nea.oc.oms.api.zto.ZtoRouterPushCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogisticsInterceptMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsIntercept;
import com.jackrain.nea.oc.oms.nums.LogisticsInterceptStatusEnum;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/8 15:34
 * @Description
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ZtoRouterPushCmdImpl implements ZtoRouterPushCmd {

    @Resource
    private OcBOrderLogisticsInterceptMapper ocBOrderLogisticsInterceptMapper;


    @Override
    public String ztoPush(String requestStr) {
        log.info(LogUtil.format("ZtoRouterPushCmdImpl.ztoPush,requestStr:{}",
                "ZtoRouterPushCmdImpl.ztoPush"), requestStr);
        ZtoRouterResponse response = new ZtoRouterResponse();
        response.setStatus(true);
        response.setMessage("系统异常");
        ZtoRouterPushRequest request = JSONObject.parseObject(requestStr, ZtoRouterPushRequest.class);
        if (StringUtils.isEmpty(request.getWayBillCode()) || StringUtils.isEmpty(request.getServiceOrderStatus())) {
            response.setStatus(false);
            response.setMessage("运单号或拦截结果状态不能为空！");
            return JSON.toJSONString(response);
        }
        List<OcBOrderLogisticsIntercept> interceptList =
                ocBOrderLogisticsInterceptMapper.selectList(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                        .eq(OcBOrderLogisticsIntercept::getBillNo, request.getServiceOrderId()));
        if (CollectionUtils.isEmpty(interceptList)) {
            response.setMessage("运单号不存在！(通过thirdBizNo查询)");
            return JSON.toJSONString(response);
        }
        for (OcBOrderLogisticsIntercept logisticsIntercept : interceptList) {
            logisticsIntercept.setModifieddate(new Date());
            logisticsIntercept.setInterceptStatus(LogisticsInterceptStatusEnum.getEnumByValue(request.getServiceOrderStatus()).getKey());
            ocBOrderLogisticsInterceptMapper.updateById(logisticsIntercept);
        }
        response.setMessage("拦截状态推送成功");
        return JSON.toJSONString(response);
    }
}
