package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderFromWMSWithdrawR3Cmd;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.services.returnorder.OcBReturnOrderDetailActionR3Service;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * description: 零售退货单-批量从wms撤回
 *
 * <AUTHOR>
 * create: 2022-01-07
 */
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
@Deprecated
public class OcBReturnOrderFromWMSWithdrawR3CmdImpl extends CommandAdapter implements OcBReturnOrderFromWMSWithdrawR3Cmd {
    @Autowired
    private OcBReturnOrderDetailActionR3Service orderDetailActionR3Service;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return orderDetailActionR3Service.batchAction(session, OcOmsReturnOrderConstant.OC_B_RETURN_ORDER_RETAIL_FROM_WMS_WITHDRAW);
    }
}
