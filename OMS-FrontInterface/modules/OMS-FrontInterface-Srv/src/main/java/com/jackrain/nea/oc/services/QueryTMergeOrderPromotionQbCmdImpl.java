package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.QueryTMergeOrderPromotionQbCmd;
import com.jackrain.nea.oc.oms.services.QueryMergeOrderPromotionQbService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: LIQB
 * @create: 2019-07-10 11:50
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc")
public class QueryTMergeOrderPromotionQbCmdImpl extends CommandAdapter implements QueryTMergeOrderPromotionQbCmd {

    @Autowired
    private QueryMergeOrderPromotionQbService queryMergeOrderPromotionQb;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return queryMergeOrderPromotionQb.queryMergeOrderPromotionQb(session);
    }
}
