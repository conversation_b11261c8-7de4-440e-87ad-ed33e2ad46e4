package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBOrderBeforeCheckCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderBeforeCheckService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/19
 * description :
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderBeforeCheckCmdImpl implements OcBOrderBeforeCheckCmd {
    @Autowired
    private OcBOrderBeforeCheckService orderBeforeCheckService;
    @Override
    public ValueHolderV14 beforeCheck(List<Long> ids, String action, User user) {
        return orderBeforeCheckService.beforeCheck(ids, action, user);
    }
}
