package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.api.SplitSaleOrderCmd;
import com.jackrain.nea.oc.oms.services.SplitSaleOrderService;
import com.jackrain.nea.oc.request.o2o.SplitOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/13
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class SplitSaleOrderCmdImpl implements SplitSaleOrderCmd {


    @Autowired
    private SplitSaleOrderService splitSaleOrderService;

    @Override
    public ValueHolderV14<JSON> splitSaleOrder(SplitOrderRequest splitOrderRequest, User user) {
        ValueHolderV14 split = splitSaleOrderService.split(splitOrderRequest, user);
        return split;
    }
}
