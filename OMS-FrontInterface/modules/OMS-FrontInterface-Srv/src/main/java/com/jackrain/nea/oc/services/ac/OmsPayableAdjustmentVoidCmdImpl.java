package com.jackrain.nea.oc.services.ac;

import com.jackrain.nea.ac.service.PayableAdjustmentVoidService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ac.OmsPayableAdjustmentVoidCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 调用BLL层
 *
 * <AUTHOR> 陈俊明
 * @since : 2019-03-26
 * create at : 2019-03-26 16:41
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsPayableAdjustmentVoidCmdImpl extends CommandAdapter implements OmsPayableAdjustmentVoidCmd {
    @Autowired
    private PayableAdjustmentVoidService payableAdjustmentVoidService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return payableAdjustmentVoidService.execute(querySession);
    }

    @Override
    public ValueHolder adjustmentVoidById(String objId, User user) {
        return payableAdjustmentVoidService.adjustmentVoidById(objId, user);
    }
}
