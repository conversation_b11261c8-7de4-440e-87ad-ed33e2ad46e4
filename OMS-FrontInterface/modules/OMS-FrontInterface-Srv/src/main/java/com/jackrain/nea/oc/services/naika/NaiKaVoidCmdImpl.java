package com.jackrain.nea.oc.services.naika;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.naika.NaiKaVoidCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaikaVoidMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
import com.jackrain.nea.oc.oms.model.enums.NaikaVoidStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaOrderVoidService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName NaiKaVoidCmdImpl
 * @Description 奶卡退单列表 作废
 * <AUTHOR>
 * @Date 2022/7/31 15:02
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class NaiKaVoidCmdImpl extends CommandAdapter implements NaiKaVoidCmd {

    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OmsNaiKaOrderVoidService omsNaiKaOrderVoidService;
    @Autowired
    private OcBOrderNaikaVoidMapper ocBOrderNaikaVoidMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", "success");

        DefaultWebEvent event = querySession.getEvent();
        User user = querySession.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONArray jsonArray = JSON.parseArray(param.get("ids").toString());
        if (CollectionUtil.isEmpty(jsonArray)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "请选择需要作废奶卡的卡号");
            return vh;
        }
        StringBuilder sb = new StringBuilder();
        for (Object object : jsonArray) {
            Long id = Long.valueOf(object.toString());
            List<OcBReturnAfSend> ocBReturnAfSendList = ocBReturnAfSendMapper.selectOcBReturnAfSendListById(Collections.singletonList(id));
            OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendList.get(0);
            // 获取商品明细表
            List<OcBReturnAfSendItem> ocBReturnAfSendItemList = ocBReturnAfSendItemMapper.selectByOcBReturnAfSendIdListBySendId(id);
            List<Long> skuIdList = ocBReturnAfSendItemList.stream().map(OcBReturnAfSendItem::getPsCSkuId).collect(Collectors.toList());
            String billNo = ocBReturnAfSend.getSourceBillNo();
            Long ocBOrderId = Long.valueOf(billNo);
            List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKa(ocBOrderId, skuIdList);
            List<Long> cardIdList = ocBOrderNaiKaList.stream().map(OcBOrderNaiKa::getId).collect(Collectors.toList());
            if (cardIdList.size() > 1) {
                sb.append(id);
                sb.append(" ");
                continue;
            }
            if (cardIdList.size() <= 0) {
                sb.append(id);
                sb.append(" ");
                continue;
            }
            ValueHolder valueHolder = omsNaiKaOrderVoidService.naiKaOrderVoid(cardIdList, id, user);
            if (valueHolder.isOK()) {
                OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
                updateOcBReturnAfSend.setId(ocBReturnAfSend.getId());
                updateOcBReturnAfSend.setModifierid(Long.valueOf(user.getId()));
                updateOcBReturnAfSend.setModifieddate(new Date());
                updateOcBReturnAfSend.setModifiername(user.getName());
                updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_SUCCESS.getCode());
                ocBReturnAfSendMapper.updateById(updateOcBReturnAfSend);

                // 如果存在中间表 则把中间表数据也更新掉
                for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItemList) {
                    List<OcBOrderNaikaVoid> ocBOrderNaikaVoidList = ocBOrderNaikaVoidMapper.getByOcBOrderIdAndItemId(ocBReturnAfSendItem.getRelationBillId(), ocBReturnAfSendItem.getRelationBillItemId());
                    if (CollectionUtil.isNotEmpty(ocBOrderNaikaVoidList)) {
                        for (OcBOrderNaikaVoid ocBOrderNaikaVoid : ocBOrderNaikaVoidList) {
                            OcBOrderNaikaVoid updateOcBOrderNaikaVoid = new OcBOrderNaikaVoid();
                            updateOcBOrderNaikaVoid.setId(ocBOrderNaikaVoid.getId());
                            updateOcBOrderNaikaVoid.setModifieddate(new Date());
                            updateOcBOrderNaikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID_SUCCESS.getStatus());
                            updateOcBOrderNaikaVoid.setOcBOrderId(ocBOrderNaikaVoid.getOcBOrderId());
                            ocBOrderNaikaVoidMapper.updateById(updateOcBOrderNaikaVoid);
                        }
                    }
                }
            } else {
                OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
                updateOcBReturnAfSend.setId(ocBReturnAfSend.getId());
                updateOcBReturnAfSend.setModifierid(Long.valueOf(user.getId()));
                updateOcBReturnAfSend.setModifieddate(new Date());
                updateOcBReturnAfSend.setModifiername(user.getName());
                updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_ERROR.getCode());
                ocBReturnAfSendMapper.updateById(updateOcBReturnAfSend);

                // 如果存在中间表 则把中间表数据也更新掉
                for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItemList) {
                    List<OcBOrderNaikaVoid> ocBOrderNaikaVoidList = ocBOrderNaikaVoidMapper.getByOcBOrderIdAndItemId(ocBReturnAfSendItem.getRelationBillId(), ocBReturnAfSendItem.getRelationBillItemId());
                    if (CollectionUtil.isNotEmpty(ocBOrderNaikaVoidList)) {
                        for (OcBOrderNaikaVoid ocBOrderNaikaVoid : ocBOrderNaikaVoidList) {
                            OcBOrderNaikaVoid updateOcBOrderNaikaVoid = new OcBOrderNaikaVoid();
                            updateOcBOrderNaikaVoid.setId(ocBOrderNaikaVoid.getId());
                            updateOcBOrderNaikaVoid.setModifieddate(new Date());
                            updateOcBOrderNaikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID_FAIL.getStatus());
                            updateOcBOrderNaikaVoid.setOcBOrderId(ocBOrderNaikaVoid.getOcBOrderId());
                            ocBOrderNaikaVoidMapper.updateById(updateOcBOrderNaikaVoid);
                        }
                    }
                }
            }
        }
        if (sb.toString().length() > 1) {
            sb.append("单据因包含1个以上奶卡，请到详情页进行作废");
            vh.put("code", ResultCode.FAIL);
            vh.put("message", sb.toString());
        }
        // 根据已发货退款单找到奶卡信息
        return vh;
    }
}
