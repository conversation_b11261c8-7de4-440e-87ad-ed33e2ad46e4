package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.AcFCustomerPackageFeeSaveCmd;
import com.jackrain.nea.oc.oms.services.AcFCustomerPackageFeeSaveService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * className: AcFCustomerPackageFeeSaveCmdImpl
 * description:经销商打包费用新增保存
 *
 * <AUTHOR>
 * create: 2021-06-19
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFCustomerPackageFeeSaveCmdImpl extends CommandAdapter implements AcFCustomerPackageFeeSaveCmd {

    @Autowired
    private AcFCustomerPackageFeeSaveService packageFeeSaveService;


    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return packageFeeSaveService.execute(session);
    }

}
