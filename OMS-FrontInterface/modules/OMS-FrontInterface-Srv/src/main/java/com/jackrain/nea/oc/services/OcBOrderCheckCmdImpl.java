package com.jackrain.nea.oc.services;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderCheckCmd;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.enums.OmsMethod;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.services.audit.AuditStrategyHandlerFactory;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderManualAuditService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.SendToBOrderMessageUtil;
import com.jackrain.nea.util.SplitListUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static com.jackrain.nea.oc.oms.model.constant.OcCommonConstant.AUDIT_SEND_DINGTALK;
import static com.jackrain.nea.util.SendToBOrderMessageUtil.AUDIT;

/**
 * 订单审核
 *
 * @date 2019/3/8
 * @author: ming.fz
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderCheckCmdImpl implements OcBOrderCheckCmd {

    @Autowired
    private OmsOrderManualAuditService omsOrderManualAuditService;

    @Autowired
    private OmsOrderAdvanceParseService orderAdvanceParseService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    AuditStrategyHandlerFactory auditStrategyHandlerFactory;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private BllRedisLockOrderUtil redisLockOrderUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private SendToBOrderMessageUtil sendToBOrderMessageUtil;

    private static final Integer FIFTY = 50;
    @Autowired
    private ThreadPoolTaskExecutor auditManualThreadPoolExecutor;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;

    @Autowired
    private IpBStandplatOrderItemMapper ipBStandplatOrderItemMapper;

    @Override
    public ValueHolderV14 orderCheck(OrderICheckRequest param, User user) throws NDSException {
        return auditOrder(param, user);
    }

    /**
     * 事务待优化，内存占用高（将事务调整到 单个明细级别） 1123
     * 界面手工审核处理逻辑（批量审核）
     *
     * @param auditOrderRequest 审核
     * @param user
     * @return
     */
    public ValueHolderV14 auditOrder(OrderICheckRequest auditOrderRequest, User user) {
        ValueHolderV14 resultValueHolderV14 = new ValueHolderV14<>();
        try {
            List<String> errors = new ArrayList<>();

            // 多线程
            Long[] orderIds = auditOrderRequest.getIds();
            List<Long> orderIdList = Arrays.asList(orderIds);
            List<List<Long>> partition = SplitListUtil.partition(orderIdList, FIFTY);
            List<Future<ValueHolderV14>> results =
                    new ArrayList<>();
            for (int i = 0; i < partition.size(); i++) {
                results.add(auditManualThreadPoolExecutor.submit(new OcBOrderCheckCmdImpl.CallableAudit(partition.get(i), user, auditOrderRequest)));
            }
            //线程执行结果获取
            for (Future<ValueHolderV14> futureResult : results) {
                try {
                    ValueHolderV14<List<String>> valueHolderV14 = futureResult.get();
                    if (CollectionUtils.isNotEmpty(valueHolderV14.getData())) {
                        errors.addAll(valueHolderV14.getData());
                    }
                } catch (InterruptedException e) {
                    log.error("OcBOrderCheckCmdImpl多线程获取InterruptedException异常", e);
                } catch (ExecutionException e) {
                    log.error("OcBOrderCheckCmdImpl多线程获取ExecutionException异常", e);
                }
            }

            if (CollectionUtils.isNotEmpty(errors)) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                // 成功数量重新计算
                int successNumber = auditOrderRequest.getIds().length - errors.size();
                sbMessage.append(String.format("审单成功%s条；审单失败%s条；失败原因：\r\n", successNumber, errors.size()));
                for (String errorMsg : errors) {
                    if (errorMsg != null) {
                        sbMessage.append(errorMsg);
                        sbMessage.append("\r\n");
                    }
                }

                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("审单全部成功。");
            }
        } catch (Exception e) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("审单异常：" + e.getMessage());
        }

        return resultValueHolderV14;
    }

    /**
     * 单个审核（带事务）
     *
     * @param auditOrderRequest
     * @param user
     * @param errors
     * @param orderRelation
     */
    @Transactional(rollbackFor = Exception.class)
    public void auditOrderSingle(OrderICheckRequest auditOrderRequest, User user, List<String> errors, OcBOrderRelation orderRelation) {
        boolean auditResult = auditStrategyHandlerFactory.doHandle(orderRelation, user);
        if (auditResult) {
            // 优化原代码多次更新的问题
            String actualPresinkStatus = null;
            boolean b = orderAdvanceParseService.checkIsDepositPreSale(orderRelation.getOrderInfo());
            if (b) {
                String reserveVarchar03 = orderRelation.getOrderInfo().getStatusPayStep();
                if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(reserveVarchar03) &&
                        TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(orderRelation.getOrderInfo().getSuggestPresinkStatus())) {
                    actualPresinkStatus = TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_NOTIFIED;
                }
            }
            int result = ocBOrderMapper.updateAuditSuccessNew(orderRelation.getOrderInfo().getId(), user, actualPresinkStatus);
            if (result > 0) {
                omsOrderManualAuditService.updateTaskAndAddLog(orderRelation.getOrderInfo(), user, auditOrderRequest.getMandatoryAudit());
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.AUDIT_SUCCESS_DATE, new Date(), orderRelation.getOrderInfo().getId(), user);
            }
        } else {
            OmsAuditFailedReason omsAuditFailedReason = orderRelation.getOmsAuditFailedReason();
            omsAuditFailedReason = omsAuditFailedReason == null ? OmsAuditFailedReason.ERROR_99 : omsAuditFailedReason;
            errors.add("OrderId=" + orderRelation.getOrderId() + "," + omsAuditFailedReason.getKey());
        }
    }

    class CallableAudit implements Callable<ValueHolderV14> {

        private final List<Long> orderIdList;
        private final User user;
        private final OrderICheckRequest auditOrderRequest;

        public CallableAudit(List<Long> orderIdList, User logUser, OrderICheckRequest auditOrderRequest) {
            this.orderIdList = orderIdList;
            this.user = logUser;
            this.auditOrderRequest = auditOrderRequest;
        }

        @Override
        public ValueHolderV14 call() throws Exception {
            ValueHolderV14 valueHolderV14 = new ValueHolderV14<>();
            List<String> errors = new ArrayList<>();
            for (Long orderId : orderIdList) {
                if (log.isDebugEnabled()) {
                    log.debug(" auditOrder start ,orderId={}", orderId);
                }
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    // 默认加锁一分钟
                    if (redisLock.tryLock(redisLockOrderUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfoOccupy(orderId);
                        if (orderRelation == null) {
                            errors.add("订单编号[" + orderId + "]查询单据占单信息为空！");
                            continue;
                        }

                        //来源平台=38&39 审核校验
                        OcBOrder orderInfo = orderRelation.getOrderInfo();
                        if ("38".equals(orderInfo.getGwSourceGroup()) || "39".equals(orderInfo.getGwSourceGroup())) {
                            Long warehouseId = orderInfo.getCpCPhyWarehouseId();
                            List<Long> storeIds = cpRpcService.queryStoreList(warehouseId);
                            if (CollectionUtils.isEmpty(storeIds)) {
                                //该实体仓关联逻辑仓不存在，请检查逻辑仓档案
                                errors.add("订单编号[" + orderId + "]实体仓关联逻辑仓不存在！");
                                continue;
                            }

                            CpStore cpStore = cpRpcService.selectCpCStoreById(storeIds.get(0));
                            if (Objects.isNull(cpStore)) {
                                //逻辑仓信息不存在
                                errors.add("订单编号[" + orderId + "]逻辑仓信息不存在！");
                                continue;
                            }

                            //通用订单
                            IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(orderInfo.getTid());
                            if (Objects.isNull(ipBStandplatOrder)) {
                                //通用订单信息不存在
                                errors.add("订单编号[" + orderId + "]通用订单信息不存在！");
                                continue;
                            }

                            List<IpBStandplatOrderItemEx> orderItemExes = ipBStandplatOrderItemMapper.selectOrderItemList(ipBStandplatOrder.getId());
                            if (CollectionUtils.isEmpty(orderItemExes)) {
                                //通用订单明细信息不存在
                                errors.add("订单编号[" + orderId + "]通用订单明细信息不存在！");
                                continue;
                            }

                            //逻辑仓档案工厂
                            String werks = cpStore.getWerks();

                            //工厂
                            String factory = orderItemExes.get(0).getFactory();
                            if (!org.apache.commons.lang3.StringUtils.equals(werks, factory)) {
                                //工厂不一致
                                errors.add("订单编号[" + orderId + "]发货工厂与指定工厂不符，请更换" + factory + "工厂的仓库发货！");
                                continue;
                            }
                        }

                        try {
                            //未退款的只要protype(0,4)
                            orderRelation.setNoRefundOrderItems(omsOrderService.getUnSuccessRefundAudit(orderRelation.getOrderItemList()));
                            orderRelation.setOmsMethod(OmsMethod.Manual);
                            /**是否强制审核*/
                            orderRelation.setMandatoryAudit(auditOrderRequest.getMandatoryAudit());
                            OcBOrderCheckCmdImpl bean = ApplicationContextHandle.getBean(OcBOrderCheckCmdImpl.class);
                            // 执行单个审核 (带事务处理的部分)  1123 性能以及锁等待问题优化 Lock wait timeout exceeded; try restarting transaction
                            bean.auditOrderSingle(auditOrderRequest, user, errors, orderRelation);
                            // 如果是tob订单
                            sendToBOrderMessageUtil.checkAndSendDingding(orderRelation.getOrderInfo(), AUDIT, null, AUDIT_SEND_DINGTALK);
                        } catch (Exception e) {
                            errors.add(String.format("%s", e.getMessage()));
                        }
                    } else {
                        throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!"));
                    }
                } catch (Exception ex) {
                    errors.add(String.format("%s", ex.getMessage()));
                } finally {
                    redisLock.unlock();
                }
            }
            valueHolderV14.setCode(ResultCode.SUCCESS);
            valueHolderV14.setMessage("success");
            valueHolderV14.setData(errors);
            return valueHolderV14;
        }
    }

    /**
     * 判断订单是否为 一件代发店铺渠道订单，是 则不允许拆单
     *
     * @param orderId
     * @param shopId
     * @return
     */
    @Override
    public ValueHolderV14 checkIssuingOrder(Long orderId, Long shopId) {
        boolean isCheck = omsOrderService.checkOrderIssuing(orderId, shopId);
        if (isCheck) {
            return ValueHolderV14Utils.getFailValueHolder("一件代发店铺渠道订单,不允许拆单！");
        } else {
            return ValueHolderV14Utils.getSuccessValueHolder("校验通过！");
        }
    }

    /**
     * 强制审核
     *
     * @param orderCheckRequest
     * @param user
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolderV14 mandatoryAuditOrder(OrderICheckRequest orderCheckRequest, User user) throws NDSException {
        orderCheckRequest.setMandatoryAudit(true);
        return auditOrder(orderCheckRequest, user);
    }
}