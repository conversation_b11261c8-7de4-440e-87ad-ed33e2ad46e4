package com.jackrain.nea.oc.services;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCRegion;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.api.OcBOrderImportBeefOrderCmd;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.services.OcBOrderImportBeefOrderService;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpBeefOrderVO;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-05-13 17:28
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderImportBeefOrderCmdImpl implements OcBOrderImportBeefOrderCmd {

    @Autowired
    private OcBOrderImportBeefOrderService ocBOrderImportBeefOrderService;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    @Autowired
    private ExportUtil exportUtil;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private PsRpcService psRpcService;
    @Resource
    private StCBusinessTypeMapper stCBusinessTypeMapper;

    @Override
    public ValueHolderV14<String> importOrderList(List<OcBOrderImpBeefOrderVO> ocBOrderList,
                                                  User user, String origFileName) {
        ValueHolderV14<String> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "订单管理导入成功！");
        // 查询基础信息 全部不过滤可用状态
        Map<String, CpShop> cpShopMap = new HashMap<>();
        Map<String, StCBusinessType> businessTypeMap = new HashMap<>();
        Map<String, CpCPhyWarehouse> warehouseMap = new HashMap<>();
        Map<String, CpLogistics> logisticsMap = new HashMap<>();
        Map<String, ProductSku> productSkuMap = new HashMap<>();
        Map<String, CpCRegion> provinceMap = new HashMap<>();
        Map<String, CpCRegion> cityMap = new HashMap<>();
        Map<String, List<CpCRegion>> areaMap = new HashMap<>();
        getBasicInfo(ocBOrderList, cpShopMap, businessTypeMap, warehouseMap,
                logisticsMap, productSkuMap, provinceMap, cityMap, areaMap);
        //检查相关信息
        boolean checkFlag = checkForImport(ocBOrderList, cpShopMap, businessTypeMap, warehouseMap,
                logisticsMap, productSkuMap, provinceMap, cityMap, areaMap);
        if (!checkFlag) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setData(exportResut(ocBOrderList, user, origFileName));
            holderV14.setMessage("订单导入失败，详情见文件内容");
            return holderV14;
        }
        try {
            ocBOrderImportBeefOrderService.batchSaveOrders(ocBOrderList, cpShopMap, productSkuMap,
                    businessTypeMap, warehouseMap, logisticsMap, provinceMap, cityMap, areaMap, user);
        } catch (Exception e) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage(e.getMessage());
        }
        return holderV14;
    }

    /**
     * 查询基础档案
     *
     * @param ocBOrderList
     * @param cpShopMap
     * @param businessTypeMap
     * @param warehouseMap
     * @param logisticsMap
     * @param productSkuMap
     * @param provinceMap
     * @param cityMap
     * @param areaMap
     */
    private void getBasicInfo(List<OcBOrderImpBeefOrderVO> ocBOrderList, Map<String, CpShop> cpShopMap,
                              Map<String, StCBusinessType> businessTypeMap, Map<String, CpCPhyWarehouse> warehouseMap,
                              Map<String, CpLogistics> logisticsMap, Map<String, ProductSku> productSkuMap,
                              Map<String, CpCRegion> provinceMap, Map<String, CpCRegion> cityMap,
                              Map<String, List<CpCRegion>> areaMap) {
        List<String> shopCodeList = new ArrayList<>();
        List<String> businessCodeList = new ArrayList<>();
        List<String> warehouseNameList = new ArrayList<>();
        List<String> logisticsCodeList = new ArrayList<>();
        List<String> skuCodeList = new ArrayList<>();
        for (OcBOrderImpBeefOrderVO orderExtend : ocBOrderList) {
            if (StringUtils.isNotBlank(orderExtend.getShopCode()) &&
                    !shopCodeList.contains(orderExtend.getShopCode())) {
                shopCodeList.add(orderExtend.getShopCode());
            }
            if (StringUtils.isNotBlank(orderExtend.getBusinessTypeCode()) &&
                    !businessCodeList.contains(orderExtend.getBusinessTypeCode())) {
                businessCodeList.add(orderExtend.getBusinessTypeCode());
            }
            if (StringUtils.isNotBlank(orderExtend.getCpCPhyWarehouseEname()) &&
                    !warehouseNameList.contains(orderExtend.getCpCPhyWarehouseEname())) {
                warehouseNameList.add(orderExtend.getCpCPhyWarehouseEname());
            }
            if (StringUtils.isNotBlank(orderExtend.getCpCLogisticsEcode()) &&
                    !logisticsCodeList.contains(orderExtend.getCpCLogisticsEcode())) {
                logisticsCodeList.add(orderExtend.getCpCLogisticsEcode());
            }
            if (StringUtils.isNotBlank(orderExtend.getPsCSkuEcode()) &&
                    !skuCodeList.contains(orderExtend.getPsCSkuEcode())) {
                skuCodeList.add(orderExtend.getPsCSkuEcode());
            }
        }
        if (CollectionUtils.isNotEmpty(shopCodeList)) {
            List<CpShop> cpShopList = cpRpcService.queryShopByCodeList(shopCodeList);
            if (CollectionUtils.isNotEmpty(cpShopList)) {
                cpShopMap.putAll(cpShopList.stream().collect(Collectors.toMap(CpShop::getEcode, Function.identity())));
            }
        }
        if (CollectionUtils.isNotEmpty(businessCodeList)) {
            List<StCBusinessType> businessTypeList =
                    stCBusinessTypeMapper.selectList(new LambdaQueryWrapper<StCBusinessType>()
                            .in(StCBusinessType::getEcode, businessCodeList).eq(StCBusinessType::getIsactive, YesNoEnum.Y.getKey()));
            if (CollectionUtils.isNotEmpty(businessCodeList)) {
                businessTypeMap.putAll(businessTypeList.stream().collect(Collectors.toMap(StCBusinessType::getEcode, Function.identity())));
            }
        }
        if (CollectionUtils.isNotEmpty(warehouseNameList)) {
            List<CpCPhyWarehouse> warehouseList = cpRpcService.queryWarehouseByNameList(warehouseNameList);
            if (CollectionUtils.isNotEmpty(warehouseList)) {
                warehouseMap.putAll(warehouseList.stream().collect(Collectors.toMap(CpCPhyWarehouse::getEname, Function.identity())));
            }
        }
        if (CollectionUtils.isNotEmpty(logisticsCodeList)) {
            List<CpLogistics> logistics = cpRpcService.queryLogisticsListByCodeIgnoreActive(logisticsCodeList);
            if (CollectionUtils.isNotEmpty(logistics)) {
                logisticsMap.putAll(logistics.stream().collect(Collectors.toMap(CpLogistics::getEcode, Function.identity())));
            }
        }
        if (CollectionUtils.isNotEmpty(skuCodeList)) {
            List<ProductSku> productSkuList = psRpcService.selectProductSkuIgnoreActive(skuCodeList);
            if (CollectionUtils.isNotEmpty(productSkuList)) {
                productSkuMap.putAll(productSkuList.stream().collect(Collectors.toMap(ProductSku::getSkuEcode, sku -> sku)));
            }
        }
        List<CpCRegion> allRegion = cpRpcService.queryAllRegion();
        if (CollectionUtils.isNotEmpty(allRegion)) {
            Map<String, List<CpCRegion>> listMap = allRegion.stream().collect(Collectors.groupingBy(CpCRegion::getRegiontype));
            provinceMap.putAll(listMap.get("PROV").stream().collect(Collectors.toMap(CpCRegion::getEname, Function.identity())));
            cityMap.putAll(listMap.get("CITY").stream().collect(Collectors.toMap(CpCRegion::getEname, Function.identity())));
            areaMap.putAll(listMap.get("DIST").stream().collect(Collectors.groupingBy(CpCRegion::getEname)));
        }

    }

    /**
     * 异常错误信息导出
     *
     * @param ocBOrderList 订单列表信息
     * @param user         用户信息
     * @return
     */
    private String exportResut(List<OcBOrderImpBeefOrderVO> ocBOrderList, User user, String origFileName) {
        List<OcBOrderImpBeefOrderVO> errorList =
                ocBOrderList.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
        // 列名
        String[] columnNames = {"主表行号", "平台单号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "sourceCode", "desc"};
        List<String> k = Lists.newArrayList(keys);
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);
        Workbook hssfWorkbook = exportUtil.execute("订单导入结果", "订单导入结果-" + origFileName, c, k, errorList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "订单管理导入错误信息", user, "OSS-Bucket/IMPORT/OC_B_ORDER/");
    }

    /**
     * 异常错误信息导出
     *
     * @param ocBOrderList 订单列表信息
     * @param user         用户信息
     * @return
     */
    @Override
    public String exportImpErrorResult(List<OcBOrderImpBeefOrderVO> ocBOrderList, User user, String origFileName) {
        List<OcBOrderImpBeefOrderVO> errorList =
                ocBOrderList.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
        // 列名
        String[] columnNames = {"主表行号", "平台单号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "sourceCode", "desc"};
        List<String> k = Lists.newArrayList(keys);
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);
        Workbook hssfWorkbook = exportUtil.execute("订单导入结果", "订单导入结果-" + origFileName, c, k, errorList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "订单管理导入错误信息", user, "OSS-Bucket/IMPORT/OC_B_ORDER/");
    }

    /**
     * 校验字段非空
     */
    public boolean checkForImport(List<OcBOrderImpBeefOrderVO> orderVOList, Map<String, CpShop> shopMap,
                                  Map<String, StCBusinessType> businessTypeMap, Map<String, CpCPhyWarehouse> warehouseMap,
                                  Map<String, CpLogistics> logisticsMap, Map<String, ProductSku> productSkuMap,
                                  Map<String, CpCRegion> provinceMap, Map<String, CpCRegion> cityMap,
                                  Map<String, List<CpCRegion>> areaMap) {
        boolean checkFlag = true;
        // 数据缺失校验
        StringBuilder checkMessage = new StringBuilder();
        for (OcBOrderImpBeefOrderVO orderVO : orderVOList) {
            CpShop cpShop = shopMap.get(orderVO.getShopCode());
            if (cpShop == null) {
                checkMessage.append("[下单店铺有误]");
            }
            if (StringUtils.isNotEmpty(orderVO.getBusinessTypeCode())) {
                StCBusinessType businessType = businessTypeMap.get(orderVO.getBusinessTypeCode());
                if (businessType == null) {
                    checkMessage.append("[业务类型有误]");
                }
            }
            if (StringUtils.isNotEmpty(orderVO.getCpCPhyWarehouseEname())) {
                CpCPhyWarehouse warehouse = warehouseMap.get(orderVO.getCpCPhyWarehouseEname());
                if (warehouse == null) {
                    checkMessage.append("[仓库信息有误]");
                }
            }
            if (StringUtils.isNotEmpty(orderVO.getCpCLogisticsEcode())) {
                CpLogistics logistics = logisticsMap.get(orderVO.getCpCLogisticsEcode());
                if (logistics == null) {
                    checkMessage.append("[物流公司有误]");
                }
            }
            // 校验省、市、区的上下级关系
            if (StringUtils.isNotEmpty(orderVO.getCpCRegionAreaEname())) {
                // 如果填写了区，必须填写市
                if (StringUtils.isEmpty(orderVO.getCpCRegionCityEname())) {
                    checkMessage.append("[填写了区，必须填写市]");
                } else {
                    // 获取区列表
                    List<CpCRegion> areaList = areaMap.get(orderVO.getCpCRegionAreaEname());
                    if (CollectionUtils.isEmpty(areaList)) {
                        checkMessage.append("[收货人区有误]");
                    } else {
                        // 校验区是否属于对应的市
                        boolean isAreaValid = false;
                        CpCRegion city = cityMap.get(orderVO.getCpCRegionCityEname());
                        if (city != null) {
                            for (CpCRegion area : areaList) {
                                if (area.getCUpId().equals(city.getId())) {
                                    isAreaValid = true;
                                    break;
                                }
                            }
                        }
                        if (!isAreaValid) {
                            checkMessage.append("[区与市的上下级关系有误]");
                        }
                    }
                }
            }

            if (StringUtils.isNotEmpty(orderVO.getCpCRegionCityEname())) {
                // 如果填写了市，必须填写省
                if (StringUtils.isEmpty(orderVO.getCpCRegionProvinceEname())) {
                    checkMessage.append("[填写了市，必须填写省]");
                } else {
                    // 获取市
                    CpCRegion city = cityMap.get(orderVO.getCpCRegionCityEname());
                    if (city == null) {
                        checkMessage.append("[收货人市有误]");
                    } else {
                        // 校验市是否属于对应的省
                        CpCRegion province = provinceMap.get(orderVO.getCpCRegionProvinceEname());
                        if (province == null || !city.getCUpId().equals(province.getId())) {
                            checkMessage.append("[市与省的上下级关系有误]");
                        }
                    }
                }
            }

            if (StringUtils.isNotEmpty(orderVO.getCpCRegionProvinceEname())) {
                // 校验省是否存在
                CpCRegion province = provinceMap.get(orderVO.getCpCRegionProvinceEname());
                if (province == null) {
                    checkMessage.append("[收货人省有误]");
                }
            }
            if (StringUtils.isNotEmpty(orderVO.getPsCSkuEcode())) {
                ProductSku productSku = productSkuMap.get(orderVO.getPsCSkuEcode());
                if (productSku == null) {
                    checkMessage.append("[SKU编码有误]");
                }
            }
            if (StringUtils.isNotEmpty(checkMessage.toString())) {
                orderVO.setDesc(checkMessage.toString());
                checkFlag = false;
                checkMessage.setLength(0);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(" end checkForImport result:{}", checkFlag);
        }

        return checkFlag;
    }

    private static boolean isFewYearAgo(int few, Date date) {
        if (date == null) {
            return false;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        LocalDateTime previousYear = LocalDateTime.now().minus(few, ChronoUnit.YEARS);
        return localDateTime.isBefore(previousYear);
    }
}
