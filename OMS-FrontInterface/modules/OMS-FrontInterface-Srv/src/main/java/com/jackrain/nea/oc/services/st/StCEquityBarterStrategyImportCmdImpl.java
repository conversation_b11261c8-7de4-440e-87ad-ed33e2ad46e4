package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.oc.oms.api.st.StCEquityBarterStrategyImportCmd;
import com.jackrain.nea.oc.oms.vo.StCEquityBarterStrategyImpVo;
import com.jackrain.nea.st.service.StCEquityBarterStrategyImportService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: lijin
 * @create: 2024-06-04
 * @description: 对等换货策略导入
 **/
@Slf4j
@Component
public class StCEquityBarterStrategyImportCmdImpl implements StCEquityBarterStrategyImportCmd {

    @Autowired
    private StCEquityBarterStrategyImportService stCEquityBarterStrategyImportService;


    @Override
    public ValueHolderV14<String> queryTemplateDownloadUrl() {
        return stCEquityBarterStrategyImportService.queryTemplateDownloadUrl();
    }

    @Override
    public ValueHolderV14<String> importCoverDataList(List<StCEquityBarterStrategyImpVo> dataImpVos, User user) {
        return stCEquityBarterStrategyImportService.importCoverDataList(dataImpVos, user);
    }

    @Override
    public ValueHolderV14<String> importAddDataList(List<StCEquityBarterStrategyImpVo> dataImpVos, User user) {
        return stCEquityBarterStrategyImportService.importAddDataList(dataImpVos, user);
    }
}
