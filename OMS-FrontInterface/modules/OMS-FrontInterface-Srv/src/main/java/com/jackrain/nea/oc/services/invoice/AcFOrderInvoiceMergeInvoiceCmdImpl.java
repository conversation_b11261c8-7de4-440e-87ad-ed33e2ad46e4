package com.jackrain.nea.oc.services.invoice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.ac.service.OrderInvoiceService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.invoice.AcFOrderInvoiceMergeInvoiceCmd;
import com.jackrain.nea.oc.oms.api.invoice.AcFOrderInvoiceRedRushCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName AcFOrderInvoiceMergeInvoiceCmdImpl
 * @Description
 * @Date 2022/9/14 下午1:44
 * @Created by wuhang
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class AcFOrderInvoiceMergeInvoiceCmdImpl extends CommandAdapter implements AcFOrderInvoiceMergeInvoiceCmd {

    @Autowired
    private OrderInvoiceService service;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        User user = querySession.getUser();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONArray ids = param.getJSONArray("ids");
        if (ids.isEmpty() && ids.size() < 2) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "请至少选择两条数据来操作");
            return vh;
        }
        ValueHolderV14 result = service.mergeInvoice(param, user);
        return service.resultConvert(result);
    }
}
