package com.jackrain.nea.oc.services;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBOrderQueryToBRemarkCmd;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderQueryToBRemarkCmdImpl
 * @Description 备注查询
 * <AUTHOR>
 * @Date 2024/12/2 15:29
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderQueryToBRemarkCmdImpl implements OcBOrderQueryToBRemarkCmd {

    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Override
    public ValueHolderV14<List<String>> queryToBRemark() {
        ValueHolderV14<List<String>> valueHolderV14 = new ValueHolderV14();
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        List<String> remarkList = businessSystemParamService.getToBOrderRemark();
        valueHolderV14.setData(remarkList);
        return valueHolderV14;
    }
}
