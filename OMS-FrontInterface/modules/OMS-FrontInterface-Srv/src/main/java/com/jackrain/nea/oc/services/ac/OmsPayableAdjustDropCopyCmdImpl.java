package com.jackrain.nea.oc.services.ac;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ac.service.PayableAdjustDropCopyService;
import com.jackrain.nea.oc.oms.api.ac.OmsPayableAdjustDropCopyCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 陈俊明
 * @since: 2019-04-30
 * @create at : 2019-04-30 9:14
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsPayableAdjustDropCopyCmdImpl implements OmsPayableAdjustDropCopyCmd {

    @Autowired
    private PayableAdjustDropCopyService payableAdjustDropCopyService;

    @Override
    public ValueHolderV14 insertPayableAdjustDropCopy(JSONObject jsonObject, User user) {
        return payableAdjustDropCopyService.insertPayableAdjustDropCopyFun(jsonObject, user);
    }
}
