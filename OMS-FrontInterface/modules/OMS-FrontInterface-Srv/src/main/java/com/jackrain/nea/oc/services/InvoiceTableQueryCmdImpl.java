package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.InvoiceTableQueryCmd;
import com.jackrain.nea.oc.oms.services.InvoiceTableQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: huang.z<PERSON><PERSON>
 * @create: 2019-07-23 21:30
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InvoiceTableQueryCmdImpl implements InvoiceTableQueryCmd {

    @Resource
    private InvoiceTableQueryService service;

    @Override
    public ValueHolderV14 queryByShop(JSONObject obj) {
        return service.queryByShop(obj);
    }

    @Override
    public ValueHolderV14 queryInvoiceItem(JSONObject obj) {
        return service.queryInvoiceItem(obj);
    }

    @Override
    public ValueHolderV14 queryInvoiceNoticeInfo(JSONObject obj) {
        return service.queryInvoiceNoticeInfo(obj);
    }

    @Override
    public ValueHolderV14 selectInvoiceNoticeList(JSONObject obj) {
        return service.selectInvoiceNoticeList(obj);
    }
}
