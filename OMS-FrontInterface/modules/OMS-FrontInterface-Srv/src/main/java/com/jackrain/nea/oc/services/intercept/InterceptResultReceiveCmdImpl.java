package com.jackrain.nea.oc.services.intercept;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.oc.oms.api.intercept.InterceptResultReceiveCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogisticsInterceptMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsIntercept;
import com.jackrain.nea.oc.oms.nums.LogisticsInterceptStatusEnum;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Author: yangrui
 * @Date: 2024-10-29 13:42
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class InterceptResultReceiveCmdImpl implements InterceptResultReceiveCmd {
    @Resource
    private OcBOrderLogisticsInterceptMapper ocBOrderLogisticsInterceptMapper;

    @Override
    public String interceptResultReceive(String param) {
        log.info(LogUtil.format("InterceptResultReceiveCmdImpl.interceptResultReceive,param:{}",
                "InterceptResultReceiveCmdImpl.interceptResultReceive"), param);
        JSONObject result = new JSONObject();
        try {
            // 参数校验
            Assert.notBlank(param, "interceptResultReceive 参数不能为空");
            JSONObject paramJson = JSONObject.parseObject(param);
            Integer logisticsServiceType = paramJson.getInteger("logisticsServiceType");
            Assert.notNull(logisticsServiceType, "logisticsServiceType 不能为空");
            String expressCode = paramJson.getString("expressCode");
            Assert.notBlank(expressCode, "expressCode 不能为空");
            Integer interceptStatus = paramJson.getInteger("interceptStatus");
            LogisticsInterceptStatusEnum logisticsInterceptStatusEnum = LogisticsInterceptStatusEnum.getEnumByKey(interceptStatus);
            Assert.isFalse(LogisticsInterceptStatusEnum.ERROR.equals(logisticsInterceptStatusEnum),
                    "interceptResult 有误");

            List<OcBOrderLogisticsIntercept> interceptList =
                    ocBOrderLogisticsInterceptMapper.selectList(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                            .eq(OcBOrderLogisticsIntercept::getExpresscode, expressCode));
            Assert.notEmpty(interceptList, "运单号不存在");

            for (OcBOrderLogisticsIntercept logisticsIntercept : interceptList) {
                logisticsIntercept.setModifieddate(new Date());
                logisticsIntercept.setInterceptStatus(logisticsInterceptStatusEnum.getKey());
                ocBOrderLogisticsInterceptMapper.updateById(logisticsIntercept);
            }
            result.put("success", true);
        }catch (Exception e) {
            log.error("InterceptResultReceiveCmdImpl.interceptResultReceive param:{} error:{}",
                    param, e.getMessage(), e);
            result.put("success", false);
            result.put("msg", e.getMessage());
        }
        return result.toJSONString();
    }
}
