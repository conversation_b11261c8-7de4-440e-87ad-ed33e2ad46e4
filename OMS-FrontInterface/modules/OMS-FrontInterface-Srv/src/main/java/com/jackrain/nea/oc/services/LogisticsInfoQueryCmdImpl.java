package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.LogisticsInfoQueryCmd;
import com.jackrain.nea.oc.oms.model.enums.LogisticsTableEnum;
import com.jackrain.nea.oc.oms.model.request.LogisticsInfoQueryRequest;
import com.jackrain.nea.oc.oms.model.result.LogisticsInfoQueryResult;
import com.jackrain.nea.oc.oms.services.logistics.LogisticsInfoQueryApi;
import com.jackrain.nea.oc.oms.services.logistics.LogisticsInfoQueryFactory;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/6/17
 * description :
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class LogisticsInfoQueryCmdImpl implements LogisticsInfoQueryCmd {

    @Override
    public ValueHolderV14<LogisticsInfoQueryResult> queryLogisticsInfo(LogisticsInfoQueryRequest request) {
        LogisticsInfoQueryApi logisticsInfoQueryApi = LogisticsInfoQueryFactory.getHandle(LogisticsTableEnum.getEnum(request.getTableName()));
        return logisticsInfoQueryApi.doHandle(request);
    }
}
