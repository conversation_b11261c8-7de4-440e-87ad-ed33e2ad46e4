package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcChangingOrRefundingDetailCmd;
import com.jackrain.nea.oc.oms.model.result.ChangingOrReFundingDetailResult;
import com.jackrain.nea.oc.oms.services.OcChangingOrRefundingDetailService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 郑立轩
 * @since: 2019/3/13
 * create at : 2019/3/13 11:14
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc")
public class OcFindChangingOrRefundingDetailsImpl implements OcChangingOrRefundingDetailCmd {
    @Autowired
    OcChangingOrRefundingDetailService service;

    @Override
    public ValueHolderV14<ChangingOrReFundingDetailResult> findDetail(JSONObject object) {
        return service.findDetail(object);
    }

    @Override
    public ValueHolderV14<ChangingOrReFundingDetailResult> returnOrderquery(JSONObject object) {
        return service.returnOrderquery(object);
    }
}
