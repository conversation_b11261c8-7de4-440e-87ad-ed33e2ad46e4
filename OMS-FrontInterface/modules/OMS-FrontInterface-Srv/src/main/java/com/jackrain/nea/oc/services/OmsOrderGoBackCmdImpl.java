package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OmsOrderGoBackCmd;
import com.jackrain.nea.oc.oms.services.OmsOrderGoBackService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/12/4 1:10 下午
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsOrderGoBackCmdImpl implements OmsOrderGoBackCmd {

    @Autowired
    private OmsOrderGoBackService omsOrderGoBackService;

    @Override
    public ValueHolderV14 orderGoBack(List<Long> ids, User user) {
        return omsOrderGoBackService.omsOrderGoBack(ids, user);
    }
}
