package com.jackrain.nea.oc.services.ac;

import com.jackrain.nea.ac.service.PayableAdjustmentSelectListService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ac.OmsPayableAdjustmentSelectListCmd;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 陈俊明
 * @since: 2019-07-09
 * @create at : 2019-07-09 21:30
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsPayableAdjustmentSelectListCmdImpl extends CommandAdapter implements OmsPayableAdjustmentSelectListCmd {
    @Autowired
    private PayableAdjustmentSelectListService payableAdjustmentSelectListService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return payableAdjustmentSelectListService.execute(querySession);
    }
}
