package com.jackrain.nea.oc.services;

import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderImportCmd;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrder;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderImportService;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.util.ImportUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-06-03 15:04
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderImportCmdImpl implements OcBReturnOrderImportCmd {
    @Autowired
    private OcBReturnOrderImportService ocBReturnOrderImportService;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;
    @Autowired
    private ExportUtil exportUtil;

    @Autowired
    private ImportUtil importUtil;

    @Override
    public ValueHolderV14 importList(List<ExtOcBReturnOrder> ocBReturnOrderList,
                                     List<ExtOcBReturnOrderRefund> extOcBReturnOrderRefundList,
                                     List<ExtOcBReturnOrderExchange> extOcBReturnOrderExchangeList,
                                     User user) {

        ValueHolderV14 holderV14 = new ValueHolderV14(ResultCode.SUCCESS, "退换货订单导入成功!");

        //匹配主表和明细表数据，校验必传字段
        if (ExtOcBReturnOrder.dealBK(ocBReturnOrderList, extOcBReturnOrderRefundList, extOcBReturnOrderExchangeList)) {
            if (ExtOcBReturnOrder.checkForImport(ocBReturnOrderList)) {
                //执行插入
                holderV14 = ocBReturnOrderImportService.importList(ocBReturnOrderList, user);
            } else {
                holderV14.setCode(ImportUtil.IMPORT_ERROR_CODE);
                holderV14.setMessage("退换货订单导入模板存在错误数据!");
                holderV14.setData(ocBReturnOrderList);
            }
        } else {
            holderV14.setCode(ImportUtil.IMPORT_ERROR_CODE);
            holderV14.setMessage("退换货订单导入模板存在错误数据!");
            holderV14.setData(ocBReturnOrderList);
        }
        try {
            //校验 holderV14
            if (holderV14.getCode() == ImportUtil.IMPORT_ERROR_CODE) {
                // 需返回结果集
                List<ExtOcBReturnOrder> detailsList = (List<ExtOcBReturnOrder>) holderV14.getData();
                List<ExtOcBReturnOrder> errorList = detailsList.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
                // 列名
                String columnNames[] = {"主表行号", "错误原因"};
                List c = Lists.newArrayList(columnNames);
                // map中的key
                String keys[] = {"rowNum", "desc"};
                List k = Lists.newArrayList(keys);
                exportUtil.setEndpoint(this.endpoint);
                exportUtil.setAccessKeyId(this.accessKeyId);
                exportUtil.setAccessKeySecret(this.accessKeySecret);
                exportUtil.setBucketName(this.bucketName);
                if (StringUtils.isEmpty(timeout)) {
                    //如果获取不到apllo配置参数，设置默认过期时间为30分钟
                    timeout = "1800000";
                }
                exportUtil.setTimeout(this.timeout);
                Workbook hssfWorkbook = exportUtil.execute("退换货订单", "退换货订单", c, k, errorList);
                String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "退换货订单导入错误信息", user, "OSS-Bucket/IMPORT/OC_B_ORDER/");
                holderV14.setData(sdd);
                return holderV14;
            } else {
                return holderV14;
            }
        } catch (Exception e) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage(e.getMessage());
            e.printStackTrace();
            return holderV14;
        }
    }
}
