package com.jackrain.nea.oc.services;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.aliyun.openservices.shade.com.google.common.collect.Lists;
import com.aliyun.openservices.shade.com.google.common.collect.Maps;
import com.jackrain.nea.dto.Pod2BOrderQueryDTO;
import com.jackrain.nea.oc.oms.api.Pod2BOrderQueryCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderByAdbMapper;
import com.jackrain.nea.oc.oms.model.result.Pod2BOrderQueryDataResult;
import com.jackrain.nea.oc.oms.model.result.Pod2BOrderQueryRequest;
import com.jackrain.nea.oc.oms.model.result.Pod2BOrderQueryResult;
import com.jackrain.nea.oc.oms.model.result.StoreColumnDTO;
import com.jackrain.nea.oc.oms.services.Pod2BOrderQueryService;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @ClassName Pod2BOrderQueryCmdImpl
 * @Description pod 2b订单查询
 * <AUTHOR>
 * @Date 2024/8/29 16:44
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class Pod2BOrderQueryCmdImpl implements Pod2BOrderQueryCmd {

    @Autowired
    private OcBOrderByAdbMapper ocBOrderByAdbMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private ExportUtil exportUtil;
    @Autowired
    private Pod2BOrderQueryService pod2BOrderQueryService;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    @Override
    public ValueHolderV14<Pod2BOrderQueryResult> queryByPage(Pod2BOrderQueryRequest request) {
        ValueHolderV14 resultHolder = new ValueHolderV14();
        resultHolder.setCode(ResultCode.SUCCESS);
        resultHolder.setMessage("success");
        Long salesDepartmentId = null;
        Pod2BOrderQueryResult result = new Pod2BOrderQueryResult();
        List<Map<String, Object>> saleDepartmentList = cpRpcService.findItemByAdStorecolumnName("C_STOREATTRIB2_ID");
        // 遍历获取id code name
        List<StoreColumnDTO> storeColumnDTOS = Lists.newArrayList();
        for (Map<String, Object> map : saleDepartmentList) {
            StoreColumnDTO storeColumnDTO = new StoreColumnDTO();
            storeColumnDTO.setId(Long.valueOf(map.get("ID").toString()));
            storeColumnDTO.setEcode(map.get("ECODE").toString());
            storeColumnDTO.setEname(map.get("ENAME").toString());
            storeColumnDTOS.add(storeColumnDTO);
        }
        // 如果销售部门不为空 则可以模糊查询 找到对应的id
        if (StringUtils.isNotEmpty(request.getSalesDepartment())) {
            for (StoreColumnDTO storeColumnDTO : storeColumnDTOS) {
                if (storeColumnDTO.getEname().contains(request.getSalesDepartment())) {
                    salesDepartmentId = storeColumnDTO.getId();
                    break;
                }
            }
            if (salesDepartmentId == null) {
                resultHolder.setData(result);
                return resultHolder;
            } else {
                request.setSalesDepartment(salesDepartmentId.toString());
            }
        }
        // 设置一年前的今天的零点
        request.setCreateDate(DateUtil.beginOfDay(DateUtil.offset(new Date(), DateField.YEAR, -1)));
        int total = pod2BOrderQueryService.getTotal(request);
        if (total == 0) {
            resultHolder.setData(result);
            return resultHolder;
        }
        List<Pod2BOrderQueryDTO> pod2BOrderQueryDTOList = pod2BOrderQueryService.queryDTOList(request);
        result.setPageNum(request.getPageNum());
        result.setPageSize(request.getPageSize());
        result.setTotal(total);
        List<Pod2BOrderQueryDataResult> dataResults = convert(pod2BOrderQueryDTOList, storeColumnDTOS);
        result.setData(dataResults);
        resultHolder.setData(result);
        return resultHolder;
    }

    @Override
    public ValueHolderV14<String> exportByPage(Pod2BOrderQueryRequest request) {
        ValueHolderV14<String> resultHolder = new ValueHolderV14<>();
        resultHolder.setCode(ResultCode.SUCCESS);
        resultHolder.setMessage("success");

        request.setCreateDate(DateUtil.beginOfDay(DateUtil.offset(new Date(), DateField.YEAR, -1)));
        int total = pod2BOrderQueryService.getTotal(request);
        if (total >= 5000) {
            // 导出的数量不能超过5000条数据
            resultHolder.setCode(ResultCode.FAIL);
            resultHolder.setMessage("导出数据不能超过5000条");
            return resultHolder;
        }
        if (total == 0) {
            resultHolder.setCode(ResultCode.FAIL);
            resultHolder.setMessage("导出数据为空");
            return resultHolder;
        }

        Long salesDepartmentId = null;
        List<Map<String, Object>> saleDepartmentList = cpRpcService.findItemByAdStorecolumnName("C_STOREATTRIB2_ID");
        // 遍历获取id code name
        List<StoreColumnDTO> storeColumnDTOS = Lists.newArrayList();
        for (Map<String, Object> map : saleDepartmentList) {
            StoreColumnDTO storeColumnDTO = new StoreColumnDTO();
            storeColumnDTO.setId(Long.valueOf(map.get("ID").toString()));
            storeColumnDTO.setEcode(map.get("ECODE").toString());
            storeColumnDTO.setEname(map.get("ENAME").toString());
            storeColumnDTOS.add(storeColumnDTO);
        }
        // 如果销售部门不为空 则可以模糊查询 找到对应的id
        if (StringUtils.isNotEmpty(request.getSalesDepartment())) {
            for (StoreColumnDTO storeColumnDTO : storeColumnDTOS) {
                if (storeColumnDTO.getEname().contains(request.getSalesDepartment())) {
                    salesDepartmentId = storeColumnDTO.getId();
                    break;
                }
            }
            if (salesDepartmentId == null) {
                resultHolder.setCode(ResultCode.FAIL);
                resultHolder.setMessage("导出数据为空");
                return resultHolder;
            } else {
                request.setSalesDepartment(salesDepartmentId.toString());
            }
        }

        request.setPageSize(total);
        request.setPageNum(1);
        // 设置一年前的今天的零点
        request.setCreateDate(DateUtil.beginOfDay(DateUtil.offset(new Date(), DateField.YEAR, -1)));
        List<Pod2BOrderQueryDTO> pod2BOrderQueryDTOList = pod2BOrderQueryService.queryDTOList(request);

        List<Pod2BOrderQueryDataResult> dataResults = convert(pod2BOrderQueryDTOList, storeColumnDTOS);
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);
        /**
         *  拼接Excel主表sheet表头字段和列表
         * */
        String[] orderNames = {"平台单号", "单据编号", "出库单号", "订单状态", "仓库编码", "仓库名称", "物流公司",
                "商品编码", "商品名称", "通知出库数量", "单品重量", "总重量", "订单总重量", "单品体积", "总体积", "订单总体积",
                "收货人", "电话", "省", "市", "区", "收货人地址", "买家备注", "卖家备注",
                "店铺名称", "销售部门", "审单人", "创建时间", "审核时间", "仓库发货时间", "拼车单号"};
        String[] orderKeys = {"tid", "billNo", "onNo", "statusName", "warehouseCode", "warehouseName", "logisticsName",
                "skuCode", "skuName", "qty", "weight", "totalWeight", "orderTotalWeight", "volume", "totalVolume", "orderTotalVolume",
                "buyerName", "buyerPhone", "buyerProvince", "buyerCity", "buyerArea", "buyerAddress", "buyerRemark", "sellerRemark",
                "shopName", "saleDepartment", "auditor", "createTime", "auditTime", "scanTime", "carpoolNo"};
        List orderN = com.google.common.collect.Lists.newArrayList(orderNames);
        List orderK = com.google.common.collect.Lists.newArrayList(orderKeys);
        // 生成Excel
        XSSFWorkbook hssfWorkbook = new XSSFWorkbook();
        exportUtil.executeSheet(hssfWorkbook, "发货单查询", "", orderN, orderK, dataResults, false);
        // 拼接文件名称
        String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "订单导出", SystemUserResource.getRootUser(), "OSS-Bucket/EXPORT/V_OC_B_ORDER/");
        resultHolder.setData(sdd);
        return resultHolder;
    }

    private List<Pod2BOrderQueryDataResult> convert(List<Pod2BOrderQueryDTO> pod2BOrderQueryDTOList, List<StoreColumnDTO> storeColumnDTOS) {
        if (CollectionUtils.isEmpty(pod2BOrderQueryDTOList)) {
            return null;
        }
        // storeColumnDTOS根据id字段分组 生成map
        Map<Long, StoreColumnDTO> storeColumnDTOMap = Maps.newHashMap();
        for (StoreColumnDTO storeColumnDTO : storeColumnDTOS) {
            storeColumnDTOMap.put(storeColumnDTO.getId(), storeColumnDTO);
        }

        Map<String, BigDecimal> orderTotalWeightMap = new HashMap<>();
        Map<String, BigDecimal> orderTotalVolumeMap = new HashMap<>();
        for (Pod2BOrderQueryDTO dto : pod2BOrderQueryDTOList) {
            BigDecimal itemTotalWeight = Optional.ofNullable(dto.getWeight()).orElse(BigDecimal.ZERO)
                    .multiply(new BigDecimal(dto.getQty()));
            BigDecimal itemTotalVolume = Optional.ofNullable(dto.getVolume()).orElse(BigDecimal.ZERO)
                    .multiply(new BigDecimal(dto.getQty()));

            orderTotalWeightMap.merge(dto.getBillNo(), itemTotalWeight, BigDecimal::add);
            orderTotalVolumeMap.merge(dto.getBillNo(), itemTotalVolume, BigDecimal::add);
        }

        List<Pod2BOrderQueryDataResult> result = Lists.newArrayList();
        for (Pod2BOrderQueryDTO pod2BOrderQueryDTO : pod2BOrderQueryDTOList) {
            Pod2BOrderQueryDataResult pod2BOrderQueryDataResult = new Pod2BOrderQueryDataResult();
            pod2BOrderQueryDataResult.setBillNo(pod2BOrderQueryDTO.getBillNo());
            pod2BOrderQueryDataResult.setBuyerAddress(pod2BOrderQueryDTO.getBuyerAddress());
            pod2BOrderQueryDataResult.setBuyerArea(pod2BOrderQueryDTO.getBuyerArea());
            pod2BOrderQueryDataResult.setBuyerCity(pod2BOrderQueryDTO.getBuyerCity());
            pod2BOrderQueryDataResult.setBuyerName(pod2BOrderQueryDTO.getBuyerName());
            pod2BOrderQueryDataResult.setBuyerPhone(pod2BOrderQueryDTO.getBuyerPhone());
            pod2BOrderQueryDataResult.setBuyerProvince(pod2BOrderQueryDTO.getBuyerProvince());
            pod2BOrderQueryDataResult.setBuyerRemark(pod2BOrderQueryDTO.getBuyerRemark());
            pod2BOrderQueryDataResult.setCreateTime(pod2BOrderQueryDTO.getCreateTime());
            pod2BOrderQueryDataResult.setLogisticsCode(pod2BOrderQueryDTO.getLogisticsCode());
            pod2BOrderQueryDataResult.setLogisticsName(pod2BOrderQueryDTO.getLogisticsName());
            pod2BOrderQueryDataResult.setOnNo(pod2BOrderQueryDTO.getOnNo());
            pod2BOrderQueryDataResult.setQty(pod2BOrderQueryDTO.getQty());
            pod2BOrderQueryDataResult.setSaleDepartment(pod2BOrderQueryDTO.getSaleDepartment());
            pod2BOrderQueryDataResult.setScanTime(pod2BOrderQueryDTO.getScanTime());
            pod2BOrderQueryDataResult.setSellerRemark(pod2BOrderQueryDTO.getSellerRemark());
            pod2BOrderQueryDataResult.setShopName(pod2BOrderQueryDTO.getShopName());
            pod2BOrderQueryDataResult.setSkuCode(pod2BOrderQueryDTO.getSkuCode());
            pod2BOrderQueryDataResult.setSkuName(pod2BOrderQueryDTO.getSkuName());
            pod2BOrderQueryDataResult.setStatus(pod2BOrderQueryDTO.getStatus());
            pod2BOrderQueryDataResult.setStatusName(convertStatus(pod2BOrderQueryDTO.getStatus()));
            pod2BOrderQueryDataResult.setTid(pod2BOrderQueryDTO.getTid());
            pod2BOrderQueryDataResult.setVolume(pod2BOrderQueryDTO.getVolume());
            pod2BOrderQueryDataResult.setWarehouseCode(pod2BOrderQueryDTO.getWarehouseCode());
            pod2BOrderQueryDataResult.setWeight(pod2BOrderQueryDTO.getWeight());
            pod2BOrderQueryDataResult.setTotalWeight(pod2BOrderQueryDTO.getWeight().multiply(new BigDecimal(pod2BOrderQueryDTO.getQty())));
            pod2BOrderQueryDataResult.setTotalVolume(pod2BOrderQueryDTO.getVolume().multiply(new BigDecimal(pod2BOrderQueryDTO.getQty())));
            pod2BOrderQueryDataResult.setAuditTime(pod2BOrderQueryDTO.getAuditTime());
            pod2BOrderQueryDataResult.setAuditor(pod2BOrderQueryDTO.getAuditor());
            if (StringUtils.isNotEmpty(pod2BOrderQueryDataResult.getSaleDepartment())) {
                StoreColumnDTO storeColumnDTO = storeColumnDTOMap.get(Long.valueOf(pod2BOrderQueryDataResult.getSaleDepartment()));
                pod2BOrderQueryDataResult.setSaleDepartmentName(storeColumnDTO.getEname());
            }

            pod2BOrderQueryDataResult.setCarpoolNo(pod2BOrderQueryDTO.getCarpoolNo());
            pod2BOrderQueryDataResult.setWarehouseName(pod2BOrderQueryDTO.getWarehouseName());
            pod2BOrderQueryDataResult.setOrderTotalWeight(orderTotalWeightMap.get(pod2BOrderQueryDTO.getBillNo()));
            pod2BOrderQueryDataResult.setOrderTotalVolume(orderTotalVolumeMap.get(pod2BOrderQueryDTO.getBillNo()));
            result.add(pod2BOrderQueryDataResult);
        }
        return result;
    }

    private String convertStatus(String status) {
        if (status == null) {
            return "";
        }

        switch (status) {
            case "1":
                return "待审核";
            case "2":
                return "待寻源";
            case "3":
                return "已审核";
            case "4":
                return "配货中";
            case "5":
                return "仓库发货";
            case "6":
                return "平台发货";
            case "7":
                return "已取消";
            case "8":
                return "系统作废";
            case "21":
                return "传WMS中";
            case "22":
                return "寻源中";
            case "50":
                return "待分配";
            default:
                return "";
        }
    }
}
