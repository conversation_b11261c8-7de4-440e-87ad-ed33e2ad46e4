package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OmsTaskOrderCmd;
import com.jackrain.nea.oc.oms.sap.OmsTaskOrderService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/18
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsTaskOrderCmdImpl implements OmsTaskOrderCmd {

    @Autowired
    private OmsTaskOrderService omsTaskOrderService;


    @Override
    public ValueHolderV14 modifyTaskOrderStatus(List<Long> shardKey, String tableName, Integer status) {

        return omsTaskOrderService.modifyTaskOrderStatus(shardKey, tableName, status);
    }
}
