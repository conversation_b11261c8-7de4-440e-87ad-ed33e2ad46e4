package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderAllRefundCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderAllRefundService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * className: OcBOrderAllRefundCmdImpl
 * description:全渠道订单退款操作
 *
 * <AUTHOR>
 * create: 2021-06-22
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderAllRefundCmdImpl extends CommandAdapter implements OcBOrderAllRefundCmd {

    @Autowired
    private OcBOrderAllRefundService refundService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return refundService.refund(session);
    }

}
