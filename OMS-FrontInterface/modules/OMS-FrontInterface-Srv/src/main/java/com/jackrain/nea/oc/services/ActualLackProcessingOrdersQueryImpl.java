package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ActualLackProcessingOrdersQueryCmd;
import com.jackrain.nea.oc.oms.services.ActualLackProcessingOrdersService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Liqb
 * @Desc 实缺处理:查询相关业务
 * @create: 2019-07-18 10:00
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ActualLackProcessingOrdersQueryImpl implements ActualLackProcessingOrdersQueryCmd {

    @Autowired
    private ActualLackProcessingOrdersService actualLackProcessingOrdersService;

    /**
     * 查询相关业务单据信息
     *
     * @param obj
     * @param user
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolderV14 queryMx(JSONObject obj, User user) throws NDSException {
        return actualLackProcessingOrdersService.queryMx(obj);
    }
}
