package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.oc.oms.api.st.StExpirationDataQueryCmd;
import com.jackrain.nea.st.service.StExpirationDataQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 商品效期策略查询命令实现
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StExpirationDataQueryCmdImpl implements StExpirationDataQueryCmd {

    @Resource
    private StExpirationDataQueryService stExpirationDataQueryService;

    @Override
    public ValueHolderV14<List<String>> queryCommonExpirationData(Integer monthStart) {
        return stExpirationDataQueryService.queryCommonExpirationData(monthStart);
    }
}
