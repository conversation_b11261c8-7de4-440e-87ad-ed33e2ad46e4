package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.FillLogisticsCmd;
import com.jackrain.nea.oc.oms.services.FillLogisticsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 申请补充物流信息
 * @date 2021/12/8 11:12
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
@RequiredArgsConstructor
public class FillLogisticsCmdImpl implements FillLogisticsCmd {


    private final FillLogisticsService fillLogisticsService;


    @Override
    public ValueHolderV14 fillLogistics(JSONObject obj, User user) throws NDSException {
        return fillLogisticsService.fillLogistics(obj,user);
    }
}
