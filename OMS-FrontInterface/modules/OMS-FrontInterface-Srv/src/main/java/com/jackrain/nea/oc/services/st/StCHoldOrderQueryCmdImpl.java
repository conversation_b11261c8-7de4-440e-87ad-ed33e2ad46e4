package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.oc.oms.api.st.StCHoldOrderQueryCmd;
import com.jackrain.nea.oc.oms.model.request.StCHoldOrderRequest;
import com.jackrain.nea.st.service.StCHoldOrderQueryService;
import com.jackrain.nea.sys.CommandAdapter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/07/02 17:07:00
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCHoldOrderQueryCmdImpl extends CommandAdapter implements StCHoldOrderQueryCmd {

    @Autowired
    private StCHoldOrderQueryService stCHoldOrderQueryService;


    @Override
    public List<StCHoldOrderRequest> queryStCHoldOrderByShopId(Long shopId) {
        return stCHoldOrderQueryService.queryStCHoldOrderByShopId(shopId);
    }
}

