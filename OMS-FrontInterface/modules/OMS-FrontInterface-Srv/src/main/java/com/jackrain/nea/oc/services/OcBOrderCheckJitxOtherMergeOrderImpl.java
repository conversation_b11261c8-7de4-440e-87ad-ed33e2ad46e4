package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBOrderCheckJitxOtherMergeOrderCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderPayableAdjustmentCreateCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderCheckJitxOtherMergeOrderService;
import com.jackrain.nea.oc.oms.services.OcBOrderPayableAdjustmentCreateService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * description：检查是否存在其他可合并JITX订单
 *
 * <AUTHOR>
 * @date 2021/5/20
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderCheckJitxOtherMergeOrderImpl implements OcBOrderCheckJitxOtherMergeOrderCmd {

    @Autowired
    private OcBOrderCheckJitxOtherMergeOrderService checkJitxOtherMergeOrderService;


    @Override
    public ValueHolderV14 checkJitxOtherMergerOrder(List<Long> ids, User user) {
        return checkJitxOtherMergeOrderService.checkOtherMergeOrder(ids,user);
    }
}
