package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcBOrderUpdateEstimateConTimeCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderUpdateEstimateConTimeService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> lin yu
 * @date : 2022/7/20 下午4:32
 * @describe :
 */

@Slf4j
@Component
public class OcBOrderUpdateEstimateConTimeImpl implements OcBOrderUpdateEstimateConTimeCmd {
    @Autowired
    private OcBOrderUpdateEstimateConTimeService service;

    @Override
    public ValueHolderV14 updateEstimateConTime(JSONObject object, User user) {
        return service.updateEstimateConTime(object,user);
    }
}
