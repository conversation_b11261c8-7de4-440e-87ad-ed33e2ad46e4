package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCTraceabilityStrategyDelItemCmd;
import com.jackrain.nea.st.service.StCTraceabilityStrategyDelItemService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略删除明细实现类
 * @author: lijin
 * @create: 2024-12-19
 **/
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCTraceabilityStrategyDelItemCmdImpl extends CommandAdapter implements StCTraceabilityStrategyDelItemCmd {

    @Autowired
    private StCTraceabilityStrategyDelItemService stCTraceabilityStrategyDelItemService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCTraceabilityStrategyDelItemService.traceabilityStrategyDelItemService(session);
    }
}
