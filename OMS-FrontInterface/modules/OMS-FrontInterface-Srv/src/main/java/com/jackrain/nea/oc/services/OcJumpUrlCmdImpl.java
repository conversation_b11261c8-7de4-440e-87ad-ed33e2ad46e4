//package com.jackrain.nea.oc.services;
//
//import org.apache.dubbo.config.annotation.Service;
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.oc.oms.services.OcJumpUrlService;
//import com.jackrain.nea.sg.basic.api.OcJumpUrlCmd;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * @author: 郑立轩
// * @since: 2019/3/12
// * create at : 2019/3/12 15:11
// */
//@Slf4j
//@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc")
//public class OcJumpUrlCmdImpl implements OcJumpUrlCmd {
//    @Autowired
//    private OcJumpUrlService service;
//
//    @Override
//    public ValueHolderV14<String> jumpUrl(JSONObject object, User user) {
//        return service.jumpUrl(object, user);
//    }
//}
