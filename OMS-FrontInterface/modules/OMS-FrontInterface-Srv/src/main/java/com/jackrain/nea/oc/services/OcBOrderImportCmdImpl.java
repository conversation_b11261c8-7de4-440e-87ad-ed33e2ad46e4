package com.jackrain.nea.oc.services;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.api.OcBOrderImportCmd;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import com.jackrain.nea.oc.oms.services.OcBOrderImportService;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.util.ImportUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpVO;
import com.jackrain.nea.oc.oms.vo.OcBOrderPreImpVO;
import com.jackrain.nea.oc.oms.vo.OcBOrderRemarkImpVO;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.AcScRpcService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCPreorderFieldStrategyDO;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-05-13 17:28
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderImportCmdImpl implements OcBOrderImportCmd {
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    private OcBOrderImportService ocBOrderImportService;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    @Autowired
    private ExportUtil exportUtil;

    @Autowired
    private ImportUtil importUtil;

    @Autowired
    private AcScRpcService acScRpcService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private PsRpcService psRpcService;

    private static final String REG = "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]";

    private static final Map<String, String> map = MapUtil.builder(new HashMap<String, String>())
            .put("下单店铺", "cpCShopTitle").put("配送费用", "shipAmt").put("买家昵称", "userNick")
            .put("平台单号", "sourceCode").put("付款方式", "payTypeName").put("收货人", "receiverName")
            .put("收货人手机", "receiverMobile").put("收货人电话", "receiverPhone").put("收货人邮编", "receiverZip")
            .put("收货人省份", "cpCRegionProvinceEname").put("收货人市", "cpCRegionCityEname")
            .put("收货人区", "cpCRegionAreaEname").put("收货人地址", "receiverAddress")
            .put("商品名称", "psCSkuEname").put("商品SKU编码", "psCSkuEcode").put("数量", "qty")
            .put("成交单价", "priceActual").put("下单时间", "orderDate").put("支付时间", "payTime")
            .put("平台售价", "platformPrice").put("买家备注", "buyerMessage").put("卖家备注", "sellerMemo")
            .put("OAID", "oaid").put("是否赠品", "isGiftStr")
            .put("业务员", "salesmanName").put("主表行号", "rowNum").put("错误原因", "errorMsg").build();

    @Override
    public ValueHolderV14<Object> importOrderList(List<OcBOrderExtend> ocBOrderList, List<OcBOrderItemExtend> ocBOrderItemList,
                                                  User user, String origFileName) {
        ValueHolderV14<Object> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "订单管理导入成功！");
        String key =
                ocBOrderList.stream().map(o -> Optional.ofNullable(o.getSourceCode()).orElse(""))
                        .distinct().sorted().collect(Collectors.joining(","));
        String redisKey = BllRedisKeyResources.buildImportKey(MD5Util.encryptByMD5(key));
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        if (objRedisTemplate.hasKey(redisKey)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请勿重复导入！");
            return holderV14;
        }
        objRedisTemplate.opsForValue().set(redisKey, redisKey, 60, TimeUnit.SECONDS);
        boolean resultFlag = true; // 导入成功
        // 判断订单 是否都有明细，若有订单没有明细则导入失败
        orderHasItems(ocBOrderList, ocBOrderItemList);
        // 此批数据中对应的店铺信息
        Map<String, CpShop> cpShopMap = getShopInfoMap(ocBOrderList);
        if (MapUtils.isEmpty(cpShopMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("订单导入失败!未查询到店铺信息!");
            return holderV14;
        }
        if (this.checkForImport(ocBOrderList, cpShopMap)) {
            // 查询此批数据中sku信息
            Map<String, ProductSku> productSkuMap = getProductMap(ocBOrderItemList);
            holderV14 = ocBOrderImportService.batchSaveOrders(ocBOrderList, cpShopMap, productSkuMap, 0, user);
            if (holderV14 == null || !holderV14.isOK()) {
                resultFlag = false;
            }
        } else {
            resultFlag = false;
        }
        if (!resultFlag) {
            holderV14 = new ValueHolderV14<>();
            holderV14.setData(exportResut(ocBOrderList, user,origFileName));
            holderV14.setCode(ResultCode.FAIL); //
            holderV14.setMessage("订单导入失败，详情见文件内容");
        }
        log.info(" 零售发货订单导入结果 {}", JSON.toJSONString(holderV14));
        return holderV14;
    }

    @Override
    public ValueHolderV14 importGiftOrderList(List<OcBOrderExtend> ocBOrderList, List<OcBOrderItemExtend> ocBOrderItemList, User user) {
        ValueHolderV14<Object> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "订单管理导入成功！");
        String key =
                ocBOrderList.stream().map(o -> Optional.ofNullable(o.getSourceCode()).orElse(""))
                        .distinct().sorted().collect(Collectors.joining(","));
        String redisKey = BllRedisKeyResources.buildImportKey(key);
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        if (objRedisTemplate.hasKey(redisKey)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("请勿重复导入！");
            return holderV14;
        }
        objRedisTemplate.opsForValue().set(redisKey, redisKey, 60, TimeUnit.SECONDS);
        if (isExist(ocBOrderList)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("订单导入失败!表格内或数据库存在重复数据!");
            return holderV14;
        }
        boolean resultFlag = true; // 导入成功
        // 判断订单 是否都有明细，若有订单没有明细则导入失败
        orderHasItems(ocBOrderList, ocBOrderItemList);
        // 此批数据中对应的店铺信息
        Map<String, CpShop> cpShopMap = getShopInfoMap(ocBOrderList);
        if (MapUtils.isEmpty(cpShopMap)) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("订单导入失败!未查询到店铺信息!");
            return holderV14;
        }
        if (this.checkForImport(ocBOrderList, cpShopMap)) {
            // 查询此批数据中sku信息
            Map<String, ProductSku> productSkuMap = getProductMap(ocBOrderItemList);
            holderV14 = ocBOrderImportService.batchSaveOrders(ocBOrderList, cpShopMap, productSkuMap, 1, user);
            if (holderV14 == null || !holderV14.isOK()) {
                resultFlag = false;
            }
        } else {
            resultFlag = false;
        }
        if (!resultFlag) {
            holderV14 = new ValueHolderV14<>();
            holderV14.setData(exportResut(ocBOrderList, user,""));
            holderV14.setCode(ResultCode.FAIL); //
            holderV14.setMessage("订单导入失败，详情见文件内容");
        }
        log.info(" 零售发货订单导入结果 {}", JSON.toJSONString(holderV14));
        return holderV14;
    }

    @Override
    public ValueHolderV14<List<OcBOrderRemarkImpVO>> updateOrderAddress(List<OcBOrderRemarkImpVO> orderRemarkList, User user) {

        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        List<String> sourceCodes = orderRemarkList
                .stream().filter(p->StringUtils.isNotBlank(p.getSourceCode())).map(OcBOrderRemarkImpVO::getSourceCode).distinct().collect(Collectors.toList());
        List<Long> idList = GSI4Order.getIdListBySourceCodes(sourceCodes);

        if (CollectionUtils.isNotEmpty(idList)) {
            return ocBOrderImportService.updateOrderAddress(orderRemarkList, sourceCodes, idList, user);
        } else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("修改地址导入平台单号都不存在，请检查!");
            return v14;
        }
    }

    /**
     * 查询订单明细对应的所有SKu对应的商品信息
     *
     * @param ocBOrderItemList
     * @return
     */
    private Map<String, ProductSku> getProductMap(List<OcBOrderItemExtend> ocBOrderItemList) {
        List<String> skuCodes = ocBOrderItemList.stream().map(OcBOrderItemExtend::getPsCSkuEcode).distinct().collect(Collectors.toList());
        Map<String, ProductSku> proSkuMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuCodes)) {
            List<ProductSku> proSkus = new ArrayList<>();
            for (String skuCode : skuCodes) {
                ProductSku productSku = psRpcService.selectProductSku(skuCode);
                if (productSku != null) {
                    proSkus.add(productSku);
                }
            }
            if (CollectionUtils.isNotEmpty(proSkus)) {
                proSkuMap = proSkus.stream().collect(Collectors.toMap(ProductSku::getSkuEcode, sku -> sku, (v1, v2) -> v1));
            }
        }
        return proSkuMap;
    }

    /**
     * 查询所有订单对应的店铺信息
     *
     * @param ocBOrderList
     * @return
     */
    private Map<String, CpShop> getShopInfoMap(List<OcBOrderExtend> ocBOrderList) {
        Map<String, CpShop> cpShopMap = new HashMap<>();
        List<String> cpCShopTitleList = ocBOrderList.stream().filter(x -> StringUtils.isNotEmpty(x.getCpCShopTitle())).map(OcBOrderExtend::getCpCShopTitle).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cpCShopTitleList)) {
            List<CpShop> cpShopList = cpRpcService.queryByShopTitle(cpCShopTitleList);
            if (CollectionUtils.isNotEmpty(cpShopList)) {
                Map<String, CpShop> temp = cpShopList.stream().collect(Collectors.toMap(CpShop::getCpCShopTitle, Function.identity(), (v1, v2) -> v1));
                if (temp != null) {
                    cpShopMap = temp;
                }
            }
        }
        return cpShopMap;
    }

    /**
     * 异常错误信息导出
     *
     * @param ocBOrderList 订单列表信息
     * @param user         用户信息
     * @return
     */
    private String exportResut(List<OcBOrderExtend> ocBOrderList, User user,String origFileName) {
        List<OcBOrderExtend> errorList = ocBOrderList.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
        // 列名
        String[] columnNames = {"主表行号", "平台单号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "sourceCode", "desc"};
        List<String> k = Lists.newArrayList(keys);
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);
        Workbook hssfWorkbook = exportUtil.execute("订单导入结果", "订单导入结果-"+origFileName, c, k, errorList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "订单管理导入错误信息", user, "OSS-Bucket/IMPORT/OC_B_ORDER/");
    }

    /**
     * 异常错误信息导出
     *
     * @param ocBOrderList 订单列表信息
     * @param user         用户信息
     * @return
     */
    @Override
    public String exportImpErrorResult(List<OcBOrderImpVO> ocBOrderList, User user,String origFileName) {
        List<OcBOrderImpVO> errorList = ocBOrderList.parallelStream().filter(x -> x.getDesc() != null).collect(Collectors.toList());
        if (log.isDebugEnabled()) {
            log.debug(" exportImpErrorResult errorList:{}", JSONObject.toJSONString(errorList));
        }
        // 列名
        String[] columnNames = {"主表行号", "平台单号", "错误原因"};
        List<String> c = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"rowNum", "sourceCode", "desc"};
        List<String> k = Lists.newArrayList(keys);
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);
        Workbook hssfWorkbook = exportUtil.execute("订单导入结果", "订单导入结果-"+origFileName, c, k, errorList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "订单管理导入错误信息", user, "OSS-Bucket/IMPORT/OC_B_ORDER/");
    }

    @Override
    public String exportPreImpErrorResult(List<OcBOrderPreImpVO> ocBOrderPreImpVOList, User user, String modelCode) {
        for (OcBOrderPreImpVO ocBOrderPreImpVO : ocBOrderPreImpVOList) {
            if (ObjectUtil.isNotNull(ocBOrderPreImpVO.getIsGift()) && ObjectUtil.equal(1, ocBOrderPreImpVO.getIsGift())) {
                ocBOrderPreImpVO.setIsGiftStr("是");
            } else {
                ocBOrderPreImpVO.setIsGiftStr("否");
            }
        }
        List<StCPreorderFieldStrategyDO> fieldStrategyDOList = stRpcService.getFieldStrategyByModelCode(modelCode);
        Map<String, StCPreorderFieldStrategyDO> fieldStrategyDOMap =
                fieldStrategyDOList.stream().collect(Collectors.toMap(StCPreorderFieldStrategyDO::getStandardField, Function.identity()));

        String[] columnNames = {"下单店铺", "配送费用", "买家昵称", "平台单号", "付款方式", "收货人", "收货人手机",
                "收货人电话", "收货人邮编", "收货人省份", "收货人市", "收货人区", "收货人地址", "商品名称", "商品SKU编码", "数量", "成交单价", "下单时间",
                "支付时间", "平台售价", "买家备注", "卖家备注", "OAID", "是否赠品", "业务员", "主表行号", "错误原因"};
        List<String> customizeNames = new ArrayList<>();
        List<String> fileds = new ArrayList<>();
        getCustomizeField(columnNames, fieldStrategyDOMap, customizeNames, fileds);
        exportUtil.setEndpoint(this.endpoint);
        exportUtil.setAccessKeyId(this.accessKeyId);
        exportUtil.setAccessKeySecret(this.accessKeySecret);
        exportUtil.setBucketName(this.bucketName);
        if (StringUtils.isEmpty(timeout)) {
            //如果获取不到apllo配置参数，设置默认过期时间为30分钟
            timeout = "1800000";
        }
        exportUtil.setTimeout(this.timeout);
        Workbook hssfWorkbook = exportUtil.execute("订单管理", null, customizeNames, fileds, ocBOrderPreImpVOList, false);
        return exportUtil.saveFileAndPutOss(hssfWorkbook, "订单管理预导入错误信息", user, "OSS-Bucket/IMPORT/OC_B_PRE_ORDER/");
    }

    private List<String> getCustomizeField(String[] columnNames, Map<String, StCPreorderFieldStrategyDO> fieldStrategyDOMap,
                                           List<String> customizeFieldList, List<String> fileds) {
        for (String column : columnNames) {
            String customizeField = "";
            StCPreorderFieldStrategyDO fieldStrategyDO = fieldStrategyDOMap.get(column);
            if (ObjectUtil.isNotNull(fieldStrategyDO) && StringUtils.isNotEmpty(fieldStrategyDO.getCustomizeField())) {
                customizeField = fieldStrategyDO.getCustomizeField();
                customizeFieldList.add(customizeField);
                fileds.add(map.get(column));
            }
            if (StringUtils.equals("主表行号", column) || StringUtils.equals("错误原因", column)) {
                customizeField = column;
                fileds.add(map.get(column));
                customizeFieldList.add(customizeField);
            }
        }
        return customizeFieldList;
    }

    private void orderHasItems(List<OcBOrderExtend> ocBOrderList, List<OcBOrderItemExtend> ocBOrderItemList) {
        if (CollectionUtils.isNotEmpty(ocBOrderItemList)) {
            Map<String, List<OcBOrderItemExtend>> tidMap =
                    ocBOrderItemList.stream().filter(item -> item.getTid() != null)
                            .collect(Collectors.groupingBy(OcBOrderItemExtend::getTid));
            for (OcBOrderExtend extend : ocBOrderList) {
                List<OcBOrderItemExtend> items = tidMap.get(extend.getTid());
                extend.setOrderItemList(items);
            }
        }
    }

    public static void main(String[] args) {
        String mobile = "00asd123123";
        boolean flag = true;
        for (char c : mobile.toCharArray()){
            if (c >= 0x4E00 && c <= 0x9FA5){
                flag = false;
                break;
            }
        }
        System.err.println(flag);
    }

    /***
     * 订单导入 判断订单是否存在,查看是否存在重复数据
     * @param ocBOrderList
     * @return
     */
    private Boolean isExist(List<OcBOrderExtend> ocBOrderList) {
        List<String> sourceCodes = ocBOrderList
                .stream().map(OcBOrderExtend::getSourceCode).distinct().collect(Collectors.toList());
        List<Long> idListBySourceCodes = GSI4Order.getIdListBySourceCodes(sourceCodes);
        return CollectionUtils.isNotEmpty(idListBySourceCodes);
    }

    private static boolean checkMobile(String mobile){
        filterSpecialStr(mobile);
        filterEmoji(mobile);
        boolean flag = false;
        for (char c : mobile.toCharArray()){
            if (c >= 0x4E00 && c <= 0x9FA5){
                flag = true;
                break;
            }
        }
        return flag;
    }

    /**
     * 过滤特殊字符
     * @param str
     * @return
     */
    private static String filterSpecialStr(String str){
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    private static  String filterEmoji(String source) {
        if(source != null)
        {
            Pattern emoji = Pattern.compile(REG, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(source);
            if ( emojiMatcher.find())
            {
                source = emojiMatcher.replaceAll("");
                return source ;
            }
            return source;
        }
        return source;
    }

    /**
     * 校验字段非空
     */
    public boolean checkForImport(List<OcBOrderExtend> ocBOrderExtendList, Map<String, CpShop> shopMap) {
        if (log.isDebugEnabled()) {
            log.debug(" start checkForImport list size:{}", ocBOrderExtendList.size());
        }
        boolean checkFlag = true;
        // 数据缺失校验
        StringBuilder checkMessage = new StringBuilder();
        Map<Long, Boolean> allowManualCreateMap = Maps.newHashMap();
        List<String> sourceCodes = ocBOrderExtendList
                .stream().map(OcBOrderExtend::getSourceCode).distinct().collect(Collectors.toList());
        List<String> existSourceCodes = GSI4Order.getSourceCodeListBySourceCodes(sourceCodes);
        for (OcBOrderExtend ocBOrderExtend : ocBOrderExtendList) {
            //获取明细数据
            List<OcBOrderItemExtend> orderItemList = ocBOrderExtend.getOrderItemList();
            if (StringUtils.isEmpty(ocBOrderExtend.getSourceCode())) {
                checkMessage.append("[主表平台单号不允许为空]");
            }
            if (existSourceCodes.contains(ocBOrderExtend.getSourceCode())) {
                checkMessage.append("[平台单号在系统中已存在]");
            }
            if (StringUtils.isEmpty(ocBOrderExtend.getCpCShopTitle())) {
                checkMessage.append("[主表下单店铺不允许为空]");
            } else {
                CpShop cpShop = shopMap.get(ocBOrderExtend.getCpCShopTitle());
                if (cpShop != null) {
                    Boolean isAllow = false;
                    if (allowManualCreateMap.containsKey(cpShop.getCpCShopId())) {
                        isAllow = allowManualCreateMap.get(cpShop.getCpCShopId());
                    } else {
                        //查询店铺策略 是否允许手工建单
                        StCShopStrategyDO shopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(cpShop.getCpCShopId());
                        if (shopStrategyDO != null) {
                            if (!YesNoEnum.Y.getKey().equals(shopStrategyDO.getIsManuallyCreate())) {
                                isAllow = false;
                                allowManualCreateMap.put(cpShop.getCpCShopId(), Boolean.FALSE);
                            } else {
                                isAllow = true;
                                allowManualCreateMap.put(cpShop.getCpCShopId(), Boolean.TRUE);
                            }
                        }
                    }
                    if (!isAllow) {
                        checkMessage.append("当前店铺未开启手工建单，不允许导入");
                    }

                } else {
                    checkMessage.append("[主表下单店铺有误]");
                }
            }
            if (ocBOrderExtend.getPayType() == null || ocBOrderExtend.getPayType() == 0) {
                checkMessage.append("[主表付款方式填写错误]");
            }
            if (StringUtils.isEmpty(ocBOrderExtend.getReceiverName())) {
                checkMessage.append("[主表收货人姓名不允许为空]");
            }
            if (StringUtils.isEmpty(ocBOrderExtend.getReceiverMobile())) {
                checkMessage.append("[主表收货人的手机号码不允许为空]");
            }

            if(checkMobile(ocBOrderExtend.getReceiverMobile())){
                checkMessage.append("[收货人手机号格式有误，请重新输入！]");
            }

            if (StringUtils.isEmpty(ocBOrderExtend.getReceiverAddress())) {
                checkMessage.append("[主表买家收货地址不允许为空]");
            }
            if (Objects.isNull(ocBOrderExtend.getOrderDate())) {
                checkMessage.append("[主表下单时间不能为空且格式需要为 yyyy-MM-dd HH:mm:ss]");
            } else if (isFewYearAgo(1, ocBOrderExtend.getOrderDate())) {
                checkMessage.append("[主表下单时间异常,时间不能小于当前时间 - 1年]");
            }
            if (Objects.isNull(ocBOrderExtend.getPayTime())) {
                checkMessage.append("[主表支付时间不能为空且格式需要为 yyyy-MM-dd HH:mm:ss]");
            } else if (!new Date().after(ocBOrderExtend.getPayTime())) {
                checkMessage.append("[主表支付时间异常,时间不能大于当前时间]");
            }
            if (Objects.nonNull(ocBOrderExtend.getPlatformPrice())) {
                try {
                    new BigDecimal(ocBOrderExtend.getPlatformPrice());
                } catch (Exception e) {
                    checkMessage.append("[主表主表下平台售价格式异常]");
                }
            }
            if (CollectionUtils.isEmpty(orderItemList)) {
                checkMessage.append("订单" + ocBOrderExtend.getSourceCode() + "对应的明细为空");
            } else {
                //校验明细表数据
                String messagePre = "[明细表第";
                for (OcBOrderItemExtend itemExtend : orderItemList) {
                    if (StringUtils.isEmpty(itemExtend.getPsCSkuEcode())) {
                        checkMessage.append(messagePre).append(itemExtend.getRowNum()).append("行,商品编码不允许为空]");
                    }
                    if (itemExtend.getQty() == null) {
                        checkMessage.append(messagePre).append(itemExtend.getRowNum()).append("行,数量不允许为空]");
                    }
                    if (itemExtend.getPriceActual() == null) {
                        checkMessage.append(messagePre).append(itemExtend.getRowNum()).append("行,成交单价不允许为空]");
                    }
                }
            }

            if (StringUtils.isNotEmpty(checkMessage.toString())) {
                ocBOrderExtend.setDesc(checkMessage.toString());
                checkFlag = false;
                checkMessage.setLength(0);
            }
        }

        if (log.isDebugEnabled()) {
            log.debug(" end checkForImport result:{}", checkFlag);
        }

        return checkFlag;
    }

    private static boolean isFewYearAgo(int few, Date date) {
        if (date == null) {
            return false;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        LocalDateTime previousYear = LocalDateTime.now().minus(few, ChronoUnit.YEARS);
        return localDateTime.isBefore(previousYear);
    }

    @Override
    public ValueHolderV14 exportError(ValueHolderV14<List<OcBOrderRemarkImpVO>> v14, User user) {

        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            List<OcBOrderRemarkImpVO> errorList = v14.getData();
            //列名
            String[] columnNames = {"主表行号", "错误原因"};
            List c = Lists.newArrayList(columnNames);
            // map中的key
            String[] keys = {"rowNum", "desc"};
            List k = Lists.newArrayList(keys);
            exportUtil.setEndpoint(this.endpoint);
            exportUtil.setAccessKeyId(this.accessKeyId);
            exportUtil.setAccessKeySecret(this.accessKeySecret);
            exportUtil.setBucketName(this.bucketName);
            if (StringUtils.isEmpty(timeout)) {
                //如果获取不到apllo配置参数，设置默认过期时间为30分钟
                timeout = "1800000";
            }
            exportUtil.setTimeout(this.timeout);
            Workbook hssfWorkbook = exportUtil.execute("批量修改地址", "批量修改地址错误", c, k, errorList);
            String sdd = exportUtil.saveFileAndPutOss(hssfWorkbook, "批量修改地址导入错误信息", user, "OSS-Bucket/IMPORT/OC_B_ORDER/");
            holderV14.setData(sdd);
            holderV14.setMessage(v14.getMessage());
            holderV14.setCode(v14.getCode());
            return holderV14;
        } catch (Exception e) {
            log.info("=========>>>>>>[llf]返回批量修改地址导入错误!错误信息为:" ,e);
            v14.setMessage(v14.getMessage() + e.getMessage());
            return v14;
        }
    }
}
