package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.BatchOperationGoodsCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.services.BatchOperationGoodsService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2020/2/14
 * create at : 2020/2/14 11:55
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class BacthOperationGoodsCmdImpl implements BatchOperationGoodsCmd {
    @Autowired
    BatchOperationGoodsService operationGoodsService;

    @Override
    public ValueHolderV14 batchDeleteGoods(JSONObject param, User loginUser, UserPermission usrPem) throws NDSException {
        return operationGoodsService.batchDeleteGoods(param, loginUser, usrPem);
    }

    @Override
    public ValueHolderV14 batchAddGoods(JSONObject obj, User user, UserPermission usrPem) throws NDSException {
        return operationGoodsService.batchAddGoods(obj, user, usrPem);
    }

    @Override
    @Deprecated
    public ValueHolderV14 batchChangeGoods(JSONObject obj, User user, UserPermission usrPem) {
        return operationGoodsService.batchChangeGoods(obj, user, usrPem);
    }

    @Override
    public ValueHolderV14 batchChangeGoodsAsync(JSONObject obj, User user, UserPermission usrPem) {
        return operationGoodsService.batchChangeGoodsAsync(obj, user, usrPem);
    }

    @Override
    public ValueHolderV14 batchAddLabel(JSONObject obj, User user) {
        return operationGoodsService.batchAddLabel(obj, user);
    }

    @Override
    public ValueHolderV14 orderDeliveryUrgent(JSONObject obj, User user) {
        return operationGoodsService.orderDeliveryUrgent(obj, user);
    }

    @Override
    public ValueHolderV14 orderUnDeliveryUrgent(JSONObject obj, User user) {
        return operationGoodsService.orderUnDeliveryUrgent(obj,user);
    }


}
