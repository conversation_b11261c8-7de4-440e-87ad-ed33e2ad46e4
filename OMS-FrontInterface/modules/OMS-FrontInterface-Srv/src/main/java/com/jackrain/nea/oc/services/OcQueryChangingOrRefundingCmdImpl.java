package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OcQueryChangingOrRefundingCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryChangingOrRefundingResult;
import com.jackrain.nea.oc.oms.services.CpQueryChangingOrRefundingService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 退换货订单列表查询
 *
 * @author: 郑立轩
 * @since: 2019/3/11
 * create at : 2019/3/11 15:05
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc")
public class OcQueryChangingOrRefundingCmdImpl implements OcQueryChangingOrRefundingCmd {
    @Autowired
    private CpQueryChangingOrRefundingService service;

    @Override
    public ValueHolderV14<QueryChangingOrRefundingResult> queryChangingOrRefunding(UserPermission usrPem, JSONObject object, User user) {

        return service.queryChangingOrRefunding(usrPem, object, user);
    }
}
