package com.jackrain.nea.oc.services;


import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBorderAgainDistributionLogisticsCmd;
import com.jackrain.nea.oc.oms.services.OmsOrderDistributeLogisticsService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBorderAgainDistributionLogisticsCmdImpl implements OcBorderAgainDistributionLogisticsCmd {

    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;

    @Override
    public ValueHolder againDistributionLogistics(JSONObject object, User operateUser) throws NDSException {
        return omsOrderDistributeLogisticsService.syncAgainDistributionLogistics(object,operateUser);
    }
}
