package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.DelOrderItemCmd;
import com.jackrain.nea.oc.oms.services.DelOrderItemService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: wangqiang
 * @Date: 2019-03-06 15:02
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class DelOrderItemCmdImpl extends CommandAdapter implements DelOrderItemCmd {
    @Autowired
    DelOrderItemService delOrderItemService;

    @Override
    public ValueHolder delOrderItem(JSONObject obj, User user) {
        return delOrderItemService.delOrderItem(obj, user);

    }
}
