package com.jackrain.nea.oc.services.ac;

import com.jackrain.nea.ac.model.AcFCompensationReason;
import com.jackrain.nea.ac.service.AcFCompensationTypeQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ac.OmsAcFCompensationTypeCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: anna
 * @CreateDate: 2020/7/10$ 21:19$
 * @Description: 赔付类型
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OmsAcFCompensationTypeCmdImpl implements OmsAcFCompensationTypeCmd {
    @Autowired
    AcFCompensationTypeQueryService acFCompensationTypeQueryService;

    @Override
    public ValueHolderV14<List<AcFCompensationReason>> queryAcFCompensationReasonById(Integer id) throws NDSException {
        return acFCompensationTypeQueryService.queryAcFCompensationReasonById(id);
    }
}
