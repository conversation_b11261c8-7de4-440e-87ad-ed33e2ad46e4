package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBReturnOrderUpdateCmd;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderUpdateService;
import com.jackrain.nea.oc.oms.services.ReturnOrderAuditService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName OcBReturnOrderUpdateCmdImpl
 * @Description 退换货单数据调整
 * <AUTHOR>
 * @Date 2025/4/22 13:41
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderUpdateCmdImpl implements OcBReturnOrderUpdateCmd {

    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;
    @Autowired
    private OcBReturnOrderUpdateService ocBReturnOrderUpdateService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Override
    public ValueHolderV14 updateByStoInResultSubmit(String billNo) {
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByBillNo(billNo);
        ocBReturnOrderUpdateService.updateReturnOrderCompleted(billNo);
        // 审核
        returnOrderAuditService.returnOrderAudit(ocBReturnOrder.getId(), false, SystemUserResource.getRootUser());
        return ValueHolderV14Utils.getSuccessValueHolder("success");
    }
}
