package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.hub.enums.OrderStatusEnum;
import com.jackrain.nea.oc.oms.api.PlatformGiftOrderQueryCmd;
import com.jackrain.nea.oc.oms.model.request.PlatformGiftOrderRequest;
import com.jackrain.nea.oc.oms.model.result.PlatformGiftOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: ryytn-oc-oms-v3.0
 * @description:
 * @author: haiyang
 * @create: 2024-01-15 18:01
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class PlatformGiftOrderQueryCmdImpl implements PlatformGiftOrderQueryCmd {

    @Autowired
    private OmsOrderService omsOrderService;
    @Override
    public ValueHolderV14<List<PlatformGiftOrderResult>> queryByPlatformOrderNos(PlatformGiftOrderRequest request) {
        ValueHolderV14<List<PlatformGiftOrderResult>> holderV14 = new ValueHolderV14<>(ResultCode.SUCCESS,"success");
        List<String> platformOrderNos = request.getPlatformOrderNos();
        log.info("queryByPlatformOrderNos: {}", request.getPlatformOrderNos());
        if (CollectionUtils.isNotEmpty(platformOrderNos) && platformOrderNos.size() > 500) {
            holderV14.setCode(ResultCode.FAIL);
            holderV14.setMessage("平台单号超长，不能大于500");
            return holderV14;
        }
        List<OcBOrder> orders = omsOrderService.getOrdersForOaRegexSourceCodes(platformOrderNos);
        if (CollectionUtils.isEmpty(orders)) {
            return holderV14;
        }
        Map<String, List<OcBOrder>> map =  orders.stream().collect(Collectors.groupingBy(e -> e.getSourceCode().split("-")[0]));
        log.info("noSortedMap: {}", JSON.toJSONString(map));
        for (Map.Entry<String, List<OcBOrder>> entry : map.entrySet()) {
            List<OcBOrder> value = entry.getValue();
            value = value.stream().sorted(Comparator.comparing(OcBOrder::getId)).collect(Collectors.toList());
            map.put(entry.getKey(), value);
        }
        log.info("sortedMap: {}", JSON.toJSONString(map));
        List<Map<String, Object>> queryMapList = Lists.newArrayListWithExpectedSize(map.keySet().size());
        List<OcBOrder> resultOrders = Lists.newArrayList();
        for (Map.Entry<String, List<OcBOrder>> entry: map.entrySet()) {
            List<OcBOrder> values = entry.getValue();
            OcBOrder ocBOrder = values.stream().filter(e -> !e.getSourceCode().contains("ozp") && !e.getSourceCode().contains("zp")).findFirst().get();
            resultOrders.add(ocBOrder);
            if (Lists.newArrayList(OrderStatusEnum.WAREHOUSE_DELIVERY.getVal(), OrderStatusEnum.PLATFORM_DELIVERY.getVal()).contains(ocBOrder.getOrderStatus())) {
                Map<String, Object> orderItemMap = new HashMap<>();
                orderItemMap.put("ocBOrderId", ocBOrder.getId());
                orderItemMap.put("tid", ocBOrder.getSourceCode());
                queryMapList.add(orderItemMap);
            }
            log.info("queryByPlatformOrderNos.containsOzpOrderNo: {}", ocBOrder.getSourceCode());
            List<OcBOrder> ozpOrders = values.stream().filter(e -> e.getSourceCode().contains("ozp") || e.getSourceCode().contains("zp")).collect(Collectors.toList());
            ozpOrders.forEach(e -> {
                Map<String, Object> giftOrderItemMap = new HashMap<>();
                giftOrderItemMap.put("ocBOrderId", e.getId());
                giftOrderItemMap.put("tid", e.getSourceCode());
                queryMapList.add(giftOrderItemMap);
            });
        }
        List<OcBOrderItem> orderItemList = omsOrderService.getGiftOrderDetailByTidAndOrderId(queryMapList);
        List<PlatformGiftOrderResult> results = assembleResults(resultOrders, orderItemList);
        holderV14.setData(results);
        return holderV14;
    }

    private List<PlatformGiftOrderResult> assembleResults(List<OcBOrder> resultOrders, List<OcBOrderItem> orderItemList) {
        // pushedGiftOrderItem
        Map<String, List<OcBOrderItem>> giftOrderItemMap = orderItemList.stream().filter(e -> e.getTid().contains("ozp")).collect(Collectors.groupingBy(e -> e.getTid().split("-")[0]));
        Map<String, List<OcBOrderItem>> orderItemMap = orderItemList.stream().filter(e -> !e.getTid().contains("ozp")).collect(Collectors.groupingBy(e -> e.getTid() + "_" + e.getOcBOrderId()));
        List<PlatformGiftOrderResult> orderResults = Lists.newArrayListWithExpectedSize(resultOrders.size());
        resultOrders.forEach(e -> {
            PlatformGiftOrderResult result = new PlatformGiftOrderResult();
            result.setPlatformOrderNo(e.getSourceCode());
            result.setProductAmt(e.getProductAmt());
            result.setCpCRegionProvinceEname(e.getCpCRegionProvinceEname());
            result.setCpCRegionCityEname(e.getCpCRegionCityEname());
            result.setCpCRegionAreaEname(e.getCpCRegionAreaEname());
//            result.setReceiverAddress(e.getReceiverAddress());
            result.setCpCShopTitle(e.getCpCShopTitle());
            List<PlatformGiftOrderResult.GiftOrderItemResult> orderItems = Lists.newArrayList();
            List<OcBOrderItem> currentOrderItems = orderItemMap.get(e.getSourceCode() + "_" + e.getId());
            List<OcBOrderItem> pushedGiftOrderItemList = giftOrderItemMap.get(e.getSourceCode());
            if (CollectionUtils.isNotEmpty(currentOrderItems)) {
                currentOrderItems.forEach(el -> {
                    PlatformGiftOrderResult.GiftOrderItemResult orderItemResult = new PlatformGiftOrderResult.GiftOrderItemResult();
                    orderItemResult.setSkuCode(el.getPsCSkuEcode());
                    orderItemResult.setQuantity(el.getQty().intValue());
                    orderItems.add(orderItemResult);
                });
            }
            if (CollectionUtils.isNotEmpty(pushedGiftOrderItemList)) {
                pushedGiftOrderItemList.forEach(el -> {
                    PlatformGiftOrderResult.GiftOrderItemResult orderItemResult = new PlatformGiftOrderResult.GiftOrderItemResult();
                    orderItemResult.setSkuCode(el.getPsCSkuEcode());
                    orderItemResult.setQuantity(el.getQty().intValue());
                    orderItems.add(orderItemResult);
                });
            }
            result.setGiftOrderItems(orderItems);
            orderResults.add(result);
        });
        return orderResults;
    }
}
