package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCHoldOrderDeleteCmd;
import com.jackrain.nea.st.service.StCHoldOrderDeleteService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * @program: r3-st
 * @description: hold删除，包含字表
 * @author: liuwj
 * @create: 2021-04-23 13:59
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCHoldOrderDeleteCmdImpl extends CommandAdapter implements StCHoldOrderDeleteCmd {

    @Autowired
    private StCHoldOrderDeleteService stCHoldOrderDeleteService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCHoldOrderDeleteService.execute(session);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
