package com.jackrain.nea.oc.services.tobereport;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.tobereport.OcBOrderToBReportQueryCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderToBReportService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 查询dms订单监控
 *
 * @Author: yangrui
 * @Date: 2025-01-06 18:28
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderToBReportQueryCmdImpl implements OcBOrderToBReportQueryCmd {

    @Autowired
    private OcBOrderToBReportService ocBOrderToBReportService;

    @Override
    public ValueHolderV14 queryByDmsOrderCode(JSONObject param) {
        return ocBOrderToBReportService.queryByDmsOrderCode(param);
    }
}

