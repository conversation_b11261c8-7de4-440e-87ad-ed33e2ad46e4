package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderTheAuditCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderTheAuditStopCmd;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.request.OrderICheckStopRequest;
import com.jackrain.nea.oc.oms.services.OcBOrderTheAuditService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单反审核并卡单
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderTheAuditStopCmdImpl implements OcBOrderTheAuditStopCmd {

    @Autowired
    OcBOrderTheAuditService ocBOrderTheAuditService;

    @Override
    public ValueHolderV14 orderTheAuditStop(OrderICheckStopRequest param, User user) throws NDSException {
        return ocBOrderTheAuditService.batchOrderTheAuditStop(param, user);
    }
}