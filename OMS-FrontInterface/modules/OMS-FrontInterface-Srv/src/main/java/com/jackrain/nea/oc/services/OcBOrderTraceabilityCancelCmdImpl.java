package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBOrderTraceabilityCancelCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderTraceabilityCancelService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @program: r3-oc-oms
 * @description: 订单取消溯源标记实现
 * @author: lijin
 * @create: 2024-12-19
 **/
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderTraceabilityCancelCmdImpl implements OcBOrderTraceabilityCancelCmd {

    @Autowired
    private OcBOrderTraceabilityCancelService ocBOrderTraceabilityCancelService;

    @Override
    public ValueHolderV14<Void> cancelTraceability(List<Long> orderIds, User user) {
        return ocBOrderTraceabilityCancelService.cancelTraceability(orderIds, user);
    }
}
