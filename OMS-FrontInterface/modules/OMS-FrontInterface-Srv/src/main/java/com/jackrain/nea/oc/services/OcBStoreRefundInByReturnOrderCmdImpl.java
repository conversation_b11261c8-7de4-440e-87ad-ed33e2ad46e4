package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBStoreRefundInByReturnOrderCmd;
import com.jackrain.nea.oc.oms.services.OcBStoreRefundInService;
import com.jackrain.nea.oc.oms.services.OneKeyStockInService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;

/**
 * className: OcBStoreRefundInByReturnOrderCmdImpl
 * description:一键入库
 *
 * <AUTHOR>
 * create: 2022-01-13
 * @since JDK 1.8
 */
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBStoreRefundInByReturnOrderCmdImpl extends CommandAdapter implements OcBStoreRefundInByReturnOrderCmd {

    @Autowired
    private OcBStoreRefundInService refundInService;

    @Autowired
    private OneKeyStockInService oneKeyStockInService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        PropertiesConf propertiesConf = ApplicationContextHandle.getBean(PropertiesConf.class);
        boolean isAutoMatch = propertiesConf.getPropertyBoolean("returnOrder.oneKeyStockIn.route_way:false");
        if (isAutoMatch) {
            return refundInService.inStoreByReturnOrder(session);
        }
        return syncStockIn(session);
    }


    private ValueHolder syncStockIn(QuerySession session) {

        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));

        if (ObjectUtils.isEmpty(param)) {
            return ValueHolderUtils.getFailValueHolder("参数不能为空");
        }
        JSONArray ids = param.getJSONArray("ids");
        if (ObjectUtils.isEmpty(ids)) {
            return ValueHolderUtils.getFailValueHolder("请选择需要操作的数据");
        }
        User user = session.getUser();
        List<Long> list = JSON.parseArray(param.getString("ids"), Long.class);
        ValueHolderV14<JSONArray> vh = oneKeyStockInService.batchStockIn(list, user);
        return ValueHolderUtils.getSuccessValueHolder(vh.getMessage());
    }
}
