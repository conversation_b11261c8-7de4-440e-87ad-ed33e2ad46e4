package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jackrain.nea.ac.utils.ValueHolderUtils;
import com.jackrain.nea.oc.oms.api.OcBOrderLogisticsInterceptCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogisticsInterceptMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsIntercept;
import com.jackrain.nea.oc.oms.nums.LogisticsInterceptStatusEnum;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.bouncycastle.cert.dane.DANEException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>OcBOrderLogisticsInterceptCmdImpl</p>
 *
 * <AUTHOR>
 * @since 2023/3/23
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderLogisticsInterceptCmdImpl implements OcBOrderLogisticsInterceptCmd {
    @Resource
    private OcBOrderLogisticsInterceptMapper ocBOrderLogisticsInterceptMapper;

    @Override
    public ValueHolder updateInterceptStatusByIdAndLogisticsCode(Long id, String logisticsCode, Integer status) {
        if (StringUtils.isEmpty(logisticsCode) || Objects.isNull(id)) {
            log.info(LogUtil.format("参数异常"));
            return ValueHolderUtils.getFailValueHolder("参数异常");
        }

        try {
            OcBOrderLogisticsIntercept logisticsIntercept = ocBOrderLogisticsInterceptMapper.selectOne(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                    .eq(OcBOrderLogisticsIntercept::getId, id));
            if (Objects.isNull(logisticsIntercept) || !Objects.equals(logisticsCode, logisticsIntercept.getExpresscode())) {
                log.info(LogUtil.format("数据异常,id:{},logisticsCode:{}"), id, logisticsCode);
                return ValueHolderUtils.getFailValueHolder("参数异常");
            }

            OcBOrderLogisticsIntercept updateModel = new OcBOrderLogisticsIntercept();
            updateModel.setModifieddate(new Date());
            updateModel.setInterceptStatus(status);
            ocBOrderLogisticsInterceptMapper.updateById(logisticsIntercept);
        } catch (Exception e) {
            log.error(LogUtil.format("updateInterceptStatusByIdAndLogisticsCode异常,id:{},logisticsCode:{}"), id, logisticsCode);
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        }
        return ValueHolderUtils.getSuccessValueHolder("success");
    }
}
