package com.jackrain.nea.oc.services.patrol;

import com.jackrain.nea.oc.oms.api.patrol.OrderTowmsCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.patrol.JDDiscountMoneyService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sg.service.SgOutStockNoticeService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019-08-05 17:02
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OrderTowmsImpl implements OrderTowmsCmd {


    @Autowired
    private SgOutStockNoticeService sgOutStockNoticeService;
    @Autowired
    private JDDiscountMoneyService jdDiscountMoneyService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Override
    public String towms(String ids, String type) {
        List<Long> list = new ArrayList<>();
        String[] split = ids.split(",");
        for (String s : split) {
            list.add(NumberUtils.toLong(s));
        }
        User rootUser = SystemUserResource.getRootUser();
        if (type == null || "1".equals(type)) {
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsListAndOrderStatus(list);
            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemsByOrderIds(list);
            sgOutStockNoticeService.addOutStockNoticeToWms(rootUser, ocBOrders, orderItems);
            return "上传成功";
        } else {
            sgOutStockNoticeService.wmsCompensate(rootUser, list);
        }
        return "看看成功没";
    }

    @Override
    public String towmsCompensate(Long id) {
        List<Long> list = new ArrayList<>();
        list.add(id);
        User rootUser = SystemUserResource.getRootUser();
        sgOutStockNoticeService.wmsCompensate(rootUser, list);
        return "成没成功我也不知道";
    }

    @Override
    public List<Long> discountMoney(Long platformNum) {
        List<Long> longs = jdDiscountMoneyService.discountMoney(platformNum);
        return longs;
    }
}
