package com.jackrain.nea.oc.services.model;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;

/**
 * @ClassName BnOrderModel
 * @Description 班牛工单模型
 * <AUTHOR>
 * @Date 2024/12/23 11:02
 * @Version 1.0
 */
@Data
public class BnOrderModel extends OcBOrder {
    private static final long serialVersionUID = 4633000601235330050L;

    /**
     * 班牛仓库物流信息
     */
    private String bnWarehouseLogistics;

    /**
     * 班牛测组件的id
     */
    private Long bnWarehouseLogisticsId;
}
