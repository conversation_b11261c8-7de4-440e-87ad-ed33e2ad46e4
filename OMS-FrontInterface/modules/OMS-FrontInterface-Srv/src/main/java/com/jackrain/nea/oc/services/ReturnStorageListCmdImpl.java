package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.ReturnStorageListCmd;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderConfirmService;
import com.jackrain.nea.oc.oms.services.ReturnOrderForceCompleteService;
import com.jackrain.nea.oc.oms.services.ReturnStorageListService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2019/4/1
 * create at : 2019/4/1 11:10
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class ReturnStorageListCmdImpl implements ReturnStorageListCmd {
    @Autowired
    ReturnStorageListService listService;

    @Autowired
    private ReturnOrderForceCompleteService returnOrderForceCompleteService;

    @Autowired
    private OmsReturnOrderConfirmService omsReturnOrderConfirmService;

    @Override
    public ValueHolderV14 returnStorageList(String param, User user) {
        return listService.returnStorageList(param, user);
    }

    /**
     * @param obj  传入的参数
     * @param user 当前用户
     * @return
     */
    @Override
    public ValueHolderV14 returnStorageSave(JSONObject obj, User user) {
        return listService.returnStorageSave(obj, user);
    }

    /**
     * 退货入库单手工匹配检查
     *
     * @param obj  传入的参数
     * @param user 当前的用户
     * @return 返回的数据
     */
    @Override
    public ValueHolderV14 manualMatchingCheck(JSONObject obj, User user) {
        return listService.manualMatchingCheck(obj, user);
    }

    @Override
    public ValueHolderV14 manualMatchingList(JSONObject obj, User user) {
        return listService.manualMatchingList(obj, user);
    }

    @Override
    public ValueHolderV14 searchButtonsInDetail(String obj, User user) {
        return listService.searchButtonsInDetail(obj, user);
    }

    @Override
    public ValueHolderV14 manualMatchingConfirmationButton(JSONObject param, User user) {
        return listService.manualMatchingConfirmationButton(param, user);
    }

    @Override
    public ValueHolderV14 markSureButton(JSONObject param, User user) {
        return listService.markSureButton(param, user);
    }

    @Override
    public ValueHolderV14 saveButton(JSONObject param, User user) {
        return listService.saveButton(param, user);
    }

    @Override
    public ValueHolderV14 forcedMatching(JSONObject param, User user) {
        return listService.forcedMatching(param, user);
    }

    @Override
    public ValueHolderV14 seachForced(JSONObject param, User user) {
        return listService.seachForced(param, user);
    }

    @Override
    public ValueHolderV14 forcedCompletion(JSONObject param, User user) {
        return returnOrderForceCompleteService.forcedCompletion(param, user);
    }

    @Override
    public ValueHolderV14 returnOrderConfirm(JSONObject param, User user) {
        return omsReturnOrderConfirmService.returnOrderConfirm(param, user);
    }

    @Override
    public ValueHolderV14 returnOrderSecondaryVerify(JSONObject param) {
        return omsReturnOrderConfirmService.returnOrderSecondaryVerify(param);
    }

    @Override
    public ValueHolderV14 cutInLine(JSONObject param, User user) {
        return listService.cutInLine(param, user);
    }
}
