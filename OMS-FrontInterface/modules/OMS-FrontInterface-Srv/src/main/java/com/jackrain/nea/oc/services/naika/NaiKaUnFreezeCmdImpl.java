package com.jackrain.nea.oc.services.naika;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.naika.NaiKaUnFreezeCmd;
import com.jackrain.nea.oc.oms.constant.NaiKaTypeConstant;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaOrderUnFreezeService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName NaiKaUnFreezeCmdImpl
 * @Description 奶卡管理列表页-解冻
 * <AUTHOR>
 * @Date 2022/7/31 10:20
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class NaiKaUnFreezeCmdImpl extends CommandAdapter implements NaiKaUnFreezeCmd {

    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OmsNaiKaOrderUnFreezeService unFreezeService;

    @Override
    public ValueHolder execute(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONArray jsonArray = JSON.parseArray(param.get("ids").toString());
        List<Long> errIdList = new ArrayList<>();
        List<Long> successIdList = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder();
        if (CollectionUtils.isEmpty(jsonArray)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "请选择需要进行奶卡解冻的单据记录");
            return vh;
        }

        for (Object object : jsonArray) {
            Long id = Long.valueOf(object.toString());
            try {
                List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaListByOrderId(id);
                // 需要过滤掉虚拟的订单 只要实体订单
                List<String> cardList = ocBOrderNaiKaList.stream().filter(ocBOrderNaiKa -> NaiKaTypeConstant.ENTITY.equals(ocBOrderNaiKa.getBusinessType()))
                        .map(OcBOrderNaiKa::getCardCode).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(cardList)) {
                    vh.put("code", ResultCode.FAIL);
                    vh.put("message", "奶卡信息为空");
                    return vh;
                }
                log.info("NaiKaUnFreezeCmdImpl.execute.ocBOrderNaiKaList:{}", JSONUtil.toJsonStr(ocBOrderNaiKaList));
                ValueHolder valueHolder = unFreezeService.naiKaOrderUnFreeze(id, null, ocBOrderNaiKaList);
                if (valueHolder.isOK()) {
                    successIdList.add(id);
                } else {
                    errIdList.add(id);
                    stringBuilder.append(valueHolder.get("message"));
                    stringBuilder.append(" ");
                }
            } catch (Exception e) {
                errIdList.add(id);
                stringBuilder.append("解冻失败，原因:");
                stringBuilder.append(e.getMessage());
            }
        }
        if (CollectionUtils.isNotEmpty(errIdList)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", stringBuilder.toString());
        } else {
            vh.put("data", successIdList);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "success");
        }
        return vh;
    }
}
