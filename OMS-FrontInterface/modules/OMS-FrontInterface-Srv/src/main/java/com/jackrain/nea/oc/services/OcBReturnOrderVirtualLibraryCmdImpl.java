package com.jackrain.nea.oc.services;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderVirtualLibraryCmd;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderVirtualLibraryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 退单虚拟入库
 *
 * @author: ming.fz
 * @since: 2019/3/26
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBReturnOrderVirtualLibraryCmdImpl implements OcBReturnOrderVirtualLibraryCmd {
    @Autowired
    OcBReturnOrderVirtualLibraryService ocBReturnOrderVirtualLibraryService;

    @Override
    public ValueHolderV14 execute(Long id, User user) throws NDSException {
        return ocBReturnOrderVirtualLibraryService.updateReturnInfo(id, user);
    }

    @Override
    public ValueHolderV14 batchExecute(JSONArray jsonArray, User user) throws NDSException {
        return ocBReturnOrderVirtualLibraryService.batchUpdateReturnInfo(jsonArray,user);
    }
}
