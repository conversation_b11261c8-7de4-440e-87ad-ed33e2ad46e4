package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.QueryOcbReturnCmd;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.QueryOcReturnOrderService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 夏继超
 * @since: 2019/4/18
 * create at : 2019/4/18 19:33
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class QueryOcbReturnCmdImpl implements QueryOcbReturnCmd {
    @Autowired
    QueryOcReturnOrderService ocReturnOrderService;

    /**
     * @param returnStatus 退单状态的集合
     * @param isWriteroff  是否插入核销流水
     * @param count        返回的记录数
     * @return
     */
    @Override
    public ValueHolderV14<OcBReturnOrder> queryOcbReturn(Long cpShopId, List<Long> returnStatus, Long isWriteroff, Long count) {
        return ocReturnOrderService.queryOcbReturn(cpShopId, returnStatus, isWriteroff, count);
    }
}
