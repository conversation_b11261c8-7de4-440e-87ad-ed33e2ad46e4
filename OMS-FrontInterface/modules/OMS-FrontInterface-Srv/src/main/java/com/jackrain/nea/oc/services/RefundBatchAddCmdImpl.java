package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.RefundBatchAddCmd;
import com.jackrain.nea.oc.oms.services.RefundBatchAddService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 孙继东
 * @since: 2019-03-26
 * create at : 2019-03-26 20:06
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class RefundBatchAddCmdImpl extends CommandAdapter implements RefundBatchAddCmd {
    @Autowired
    private RefundBatchAddService refundBatchAddService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return refundBatchAddService.execute(session);
    }
}
