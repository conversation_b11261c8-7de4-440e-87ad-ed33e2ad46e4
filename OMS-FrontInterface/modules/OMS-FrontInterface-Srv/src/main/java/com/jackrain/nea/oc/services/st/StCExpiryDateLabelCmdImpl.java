package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.oc.oms.api.st.StCExpiryDateLabelCmd;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDateLabel;
import com.jackrain.nea.st.service.StCExpiryDateLabelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName StCExpiryDateLabelCmdImpl
 * @Description 汇波标签
 * <AUTHOR>
 * @Date 2024/9/10 21:58
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCExpiryDateLabelCmdImpl implements StCExpiryDateLabelCmd {

    @Autowired
    private StCExpiryDateLabelService stCExpiryDateLabelService;

    @Override
    public List<StCExpiryDateLabel> selectAllByRule() {
        return stCExpiryDateLabelService.queryAllByRule();
    }
}
