package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.OcBOrderInitQueryCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryOrderConditionResult;
import com.jackrain.nea.oc.oms.services.OcBOrderInitService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 订单管理: 订单列表-初始化查询条件
 *
 * @author: xiwen.z
 * create at: 2019/3/5 0005
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderInitQueryCmdImpl implements OcBOrderInitQueryCmd {

    @Autowired
    private OcBOrderInitService ocBOrderInitService;


    @Override
    public ValueHolderV14<QueryOrderConditionResult> queryConditionInit(String pam, User usr, UserPermission pem) {
        return ocBOrderInitService.queryConditionInit(pam, usr, pem);
    }

}
