package com.jackrain.nea.oc.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderFinanceQueryCmd;
import com.jackrain.nea.oc.oms.services.OcBOrderFinanceQueryService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 提供给财务的服务
 *
 * @date 2019/4/22
 * @author: ming.fz
 */
@Slf4j
@Component
//@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class OcBOrderFinanceQueryCmdImpl implements OcBOrderFinanceQueryCmd {

    @Autowired
    private OcBOrderFinanceQueryService ocBOrderFinanceQueryService;


    @Override
    public ValueHolderV14 query(Long shopId, String orderSource, Long isWriteoff, List<Long> orderType, List<Long> orderStatus,
                                Integer pageSize, Integer pageNum) throws NDSException {
        return ocBOrderFinanceQueryService.query(shopId, orderSource, isWriteoff, orderType, orderStatus, pageSize, pageNum);
    }


}