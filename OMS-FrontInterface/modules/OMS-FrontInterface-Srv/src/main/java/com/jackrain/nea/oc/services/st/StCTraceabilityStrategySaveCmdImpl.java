package com.jackrain.nea.oc.services.st;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.st.StCTraceabilityStrategySaveCmd;
import com.jackrain.nea.st.service.StCTraceabilityStrategySaveService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略保存
 * @author: lijin
 * @create: 2024-12-19
 **/
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class StCTraceabilityStrategySaveCmdImpl extends CommandAdapter implements StCTraceabilityStrategySaveCmd {

    @Autowired
    private StCTraceabilityStrategySaveService stCTraceabilityStrategySaveService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return stCTraceabilityStrategySaveService.save(session);
    }
}
