package com.jackrain.nea.oc.services;

import com.jackrain.nea.oc.oms.api.VoidJinDongOrderCmd;
import com.jackrain.nea.oc.oms.services.JinDongOrderVoidSgSendService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Desc : 京东作废发货单
 * <AUTHOR> xiWen
 * @Date : 2020/7/21
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oms-fi")
public class VoidJinDongOrderCmdImpl implements VoidJinDongOrderCmd {

    @Autowired
    JinDongOrderVoidSgSendService jinDongOrderVoidSgSendService;


    @Override
    public ValueHolderV14 voidSgSendService(Long orderId, User user) {
        return jinDongOrderVoidSgSendService.voidSgSendService(orderId, user);
    }
}
