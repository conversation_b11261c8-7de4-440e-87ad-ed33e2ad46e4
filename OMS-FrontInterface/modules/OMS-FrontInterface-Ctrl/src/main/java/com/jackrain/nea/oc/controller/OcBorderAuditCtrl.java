package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderCheckCmd;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * @author: heliu
 * @since: 2019/4/18
 * create at : 2019/4/18 14:31
 */
@Api(value = "OcBOrderAuditCtrl", tags = "全渠道订单手动审核")
@Slf4j
@RestController
public class OcBorderAuditCtrl {

    @Autowired
    private OcBOrderCheckCmd ocBOrderCheckCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    /**
     * 订单手动审核
     *
     * @param request request对象
     * @param obj     封装请求参数
     * @return JSONObject
     */
    @ApiOperation(value = "订单手工审核")
    @PostMapping(value = "/api/cs/oc/oms/v1/auditOrder")
    public ValueHolderV14 orderCheck(HttpServletRequest request,
                                     @RequestBody JSONObject obj) {

        //获取当前系统登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //获取用户登录信息
        if (obj == null) {
            throw new NDSException(Resources.getMessage("订单手工审核请求参数不能为空!", user.getLocale()));
        }

        if (obj.getString("isAsync") == null || !obj.getBoolean("isAsync")) {

            ValueHolderV14 vh = new ValueHolderV14();
            OrderICheckRequest orderCheckRequest = new OrderICheckRequest();
            JSONArray jsonArray = obj.getJSONArray("ids");
            Long[] ids = new Long[jsonArray.size()];
            for (int i = 0; i < jsonArray.size(); i++) {
                ids[i] = jsonArray.getLong(i);
            }
            orderCheckRequest.setIds(ids);
            orderCheckRequest.setType(obj.getLong("type"));
            orderCheckRequest.setIsCheck(obj.getLong("isCheck"));
            try {
                vh = ocBOrderCheckCmd.orderCheck(orderCheckRequest, user);
            } catch (NDSException e) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("异常信息：" + Throwables.getStackTraceAsString(e)));
            }

            return vh;

        } else {
            //新增我的任务
            AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
            asyncTaskBody.setTaskId(UUID.randomUUID().toString());
            asyncTaskBody.setMenu("零售发货单");
            asyncTaskBody.setTaskType("手工审核");

            //任务开始
            asyncTaskManager.beforeExecute(user, asyncTaskBody);

            commonTaskExecutor.submit(() -> {

                ValueHolderV14 vh = new ValueHolderV14();
                OrderICheckRequest orderCheckRequest = new OrderICheckRequest();
                JSONArray jsonArray = obj.getJSONArray("ids");
                Long[] ids = new Long[jsonArray.size()];
                for (int i = 0; i < jsonArray.size(); i++) {
                    ids[i] = jsonArray.getLong(i);
                }
                orderCheckRequest.setIds(ids);
                orderCheckRequest.setType(obj.getLong("type"));
                orderCheckRequest.setIsCheck(obj.getLong("isCheck"));
                try {
                    vh = ocBOrderCheckCmd.orderCheck(orderCheckRequest, user);
                } catch (NDSException e) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("异常信息：" + Throwables.getStackTraceAsString(e)));
                }

                // xie oss
                //记录日志信息
                asyncTaskManager.afterExecute(user, asyncTaskBody, vh.toJSONObject());

            });

            ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("零售发货单-手工审核任务开始！详情请在我的任务查看"));
            vh.setData(asyncTaskBody.getId());
            return vh;
        }
    }


    /**
     * 订单拆分一件代发校验
     *
     * @param request request对象
     * @param obj     封装请求参数
     * @return JSONObject
     */
    @ApiOperation(value = "订单拆分一件代发校验")
    @PostMapping(value = "/api/cs/oc/oms/v1/checkIssuingOrder")
    public JSONObject checkIssuingOrder(HttpServletRequest request,
                                        @RequestBody JSONObject obj) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug("start.OcBOrderAuditCtrl.checkIssuingOrder.request={}", obj);
        }
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前系统登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (log.isDebugEnabled()) {
            log.debug("OcBOrderAuditCtrl.user.orderCheck{}", user);
        }
        //获取用户登录信息
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        Long orderId = obj.getLong("ORDER_ID");
        Long shopId = obj.getLong("SHOP_ID");

        if (orderId == null || "".equals(orderId)) {
            throw new NDSException(Resources.getMessage("订单ID为空!", user.getLocale()));
        }
        if (shopId == null || "".equals(shopId)) {
            throw new NDSException(Resources.getMessage("订单店铺ID为空!", user.getLocale()));
        }

        try {
            vh = ocBOrderCheckCmd.checkIssuingOrder(orderId, shopId);
            //记录日志信息。Finish 标记结束
            if (log.isDebugEnabled()) {
                log.debug("OcBOrderAuditCtrl.checkIssuingOrder.reponse={}", vh.toJSONObject());
            }
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            log.error("订单拆分一件代发校验失败" + e.getMessage());
            return vh.toJSONObject();
        }
    }


    /**
     * 订单强制审核
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "订单强制审核")
    @PostMapping(value = "/api/cs/oc/oms/v1/mandatoryAuditOrder")
    public ValueHolderV14 mandatoryAuditOrder(HttpServletRequest request,
                                              @RequestBody JSONObject obj) {

        //获取当前系统登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //获取用户登录信息
        if (obj == null) {
            throw new NDSException(Resources.getMessage("订单强制审核请求参数不能为空!", user.getLocale()));
        }

        if (obj.getString("isAsync") == null || !obj.getBoolean("isAsync")) {

            ValueHolderV14 vh = new ValueHolderV14();
            OrderICheckRequest orderCheckRequest = new OrderICheckRequest();
            JSONArray jsonArray = obj.getJSONArray("ids");
            Long[] ids = new Long[jsonArray.size()];
            for (int i = 0; i < jsonArray.size(); i++) {
                ids[i] = jsonArray.getLong(i);
            }
            orderCheckRequest.setIds(ids);
            orderCheckRequest.setType(obj.getLong("type"));
            orderCheckRequest.setIsCheck(obj.getLong("isCheck"));

            try {
                vh = ocBOrderCheckCmd.mandatoryAuditOrder(orderCheckRequest, user);
            } catch (NDSException e) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage(e.getMessage()));
            }

            return vh;

        } else {
            //新增我的任务
            AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
            asyncTaskBody.setTaskId(UUID.randomUUID().toString());
            asyncTaskBody.setMenu("零售发货单");
            asyncTaskBody.setTaskType("强制审核");

            //任务开始
            asyncTaskManager.beforeExecute(user, asyncTaskBody);

            commonTaskExecutor.submit(() -> {
                ValueHolderV14 vh = new ValueHolderV14();
                OrderICheckRequest orderCheckRequest = new OrderICheckRequest();
                JSONArray jsonArray = obj.getJSONArray("ids");
                Long[] ids = new Long[jsonArray.size()];
                for (int i = 0; i < jsonArray.size(); i++) {
                    ids[i] = jsonArray.getLong(i);
                }
                orderCheckRequest.setIds(ids);
                orderCheckRequest.setType(obj.getLong("type"));
                orderCheckRequest.setIsCheck(obj.getLong("isCheck"));

                try {
                    vh = ocBOrderCheckCmd.mandatoryAuditOrder(orderCheckRequest, user);
                } catch (NDSException e) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage(Resources.getMessage("异常信息：" + Throwables.getStackTraceAsString(e)));
                }

                //记录日志信息
                asyncTaskManager.afterExecute(user, asyncTaskBody, vh.toJSONObject());
            });

            ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("零售发货单-强制审核任务开始！详情请在我的任务查看"));
            vh.setData(asyncTaskBody.getId());
            return vh;
        }
    }
}

