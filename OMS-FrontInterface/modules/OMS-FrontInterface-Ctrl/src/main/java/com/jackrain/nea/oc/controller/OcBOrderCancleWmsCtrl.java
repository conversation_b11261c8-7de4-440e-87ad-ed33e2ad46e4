package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OrderCancleWmsCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-05-14 09:43
 */
@Api(value = "OcBOrderCancleWmsCtrl", tags = "wms撤回按钮")
@Slf4j
@RestController
public class OcBOrderCancleWmsCtrl {


    @Autowired
    private OrderCancleWmsCmd orderCancleWmsCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @RequestMapping(value = "/api/cs/oc/oms/v1/cancleWms", method = RequestMethod.POST)
    public ValueHolderV14 cancleWms(HttpServletRequest request, @RequestBody JSONObject obj) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        List<Long> ids = (List<Long>) obj.get("ids");
        return orderCancleWmsCmd.cancleWms(ids, user);
    }

}
