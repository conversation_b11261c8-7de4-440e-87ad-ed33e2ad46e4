package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderRemarkCmd;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 退换货.备注
 *
 * @author: xiWen.z
 * create at: 2019/9/17 0017
 */
@RestController
@Slf4j
@Api(value = "OcBReturnOrderRemarkCtrl", tags = "修改卖家备注")
public class OcBReturnOrderRemarkCtrl {


    @Autowired
    OcBReturnOrderRemarkCmd ocBReturnOrderRemarkCmd;


    final String cellStr = "cell_";
    final String rowStr = "row_";
    final int numeric = 0;
    final int string = 1;
    final int formula = 2;
    private NumberFormat nf = NumberFormat.getInstance();

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    /**
     * 修改卖家备注
     *
     * @param request HttpServletRequest
     * @param jsn     json param
     * @return vh
     * @throws Exception ex
     */
    @ApiOperation(value = "修改卖家备注")
    @RequestMapping(path = "api/cs/oc/oms/v1/modifyReturnSellerRemark", method = RequestMethod.POST)
    public ValueHolderV14 modifyReturnRemark(HttpServletRequest request, @RequestBody JSONObject jsn) throws Exception {

        ValueHolderV14 vh;
        User usr = r3PrimWebAuthService.getLoginPrimWebUser(request);

        vh = ocBReturnOrderRemarkCmd.modifySellerRemark(jsn, usr);
        return vh;
    }

    /**
     * 下载模板
     *
     * @param request HttpServletRequest
     * @return url
     * @throws Exception ex
     */
    @ApiOperation(value = "下载修改卖家备注模板")
    @RequestMapping(path = "api/cs/oc/oms/v1/downloadReturnRemarkTemp", method = RequestMethod.POST)
    public ValueHolderV14 downloadReturnRemarkTemp(HttpServletRequest request) throws Exception {

        User usr = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh = ocBReturnOrderRemarkCmd.downloadReturnRemarkTemp(usr);
        return vh;
    }

    /**
     * 导入退换货卖家备注
     *
     * @param request HttpServletRequest
     * @param file    excel file
     * @param cover   bool
     * @return json
     */
    @ApiOperation(value = "导入退换货卖家备注")
    @RequestMapping(value = "api/cs/oc/oms/v1/importReturnSellerRemark", method = RequestMethod.POST)
    public JSONObject importReturnRemark(HttpServletRequest request,
                                         @RequestParam(value = "file") MultipartFile file,
                                         @RequestParam("cover") Boolean cover) {

        ValueHolderV14 vh = new ValueHolderV14();
        User usr = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody =new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("退换货单卖家备注导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(usr, asyncTaskBody);

        return asyncImport(asyncTaskBody,usr,file,cover);
    }

    private JSONObject asyncImport(AsyncTaskBody asyncTaskBody, User usr, MultipartFile file, Boolean cover) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            if (file == null) {
                throw new NDSException(Resources.getMessage("导入文件不存在!", usr.getLocale()));
            }
            InputStream inputStream = null;
            try {
                inputStream = file.getInputStream();
                if (inputStream == null) {
                    throw new NDSException(Resources.getMessage("未获取到文件流"));
                }
            } catch (Exception e) {
                log.error(" 文件转换成流失败", e);
                throw new NDSException(Resources.getMessage("文件转换成流失败!"));
            }
            try {
                Workbook workbook = new XSSFWorkbook(inputStream);
                JSONObject jsnObj = this.getReturnOrderList(workbook, usr);
                Map<String, OcBReturnOrder> sourceMap = (Map<String, OcBReturnOrder>) jsnObj.get("sourceCodeMap");
                JSONArray codeAry = jsnObj.getJSONArray("sourceCode");
                if (sourceMap == null || sourceMap.size() < OcBOrderConst.IS_STATUS_IY) {
                    throw new NDSException(Resources.getMessage("请确认,导入数据是否存在"));
                }

                ValueHolderV14 valueHolder = ocBReturnOrderRemarkCmd.importReturnRemark(sourceMap, codeAry, cover, usr);
                retMap.put("code", valueHolder.getCode());
                retMap.put("data", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskManager.afterExecute(usr, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception e) {
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + e.getMessage());
                asyncTaskManager.afterExecute(usr, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("零售发货单导入任务开始！详情请在我的任务查看"));
        return holderV14.toJSONObject();
    }

    /**
     * @param workbook workbook
     * @param user     user
     * @return jsnObj
     */
    public JSONObject getReturnOrderList(Workbook workbook, User user) {

        JSONObject jsonObject = new JSONObject();
        Map<String, OcBReturnOrder> sourceMap = new HashMap<>();
        Set<String> sourceSet = new HashSet<>();
        List<Map<String, String>> exlList = Lists.newArrayList();
        try {
            exlList = readExcel(0, workbook);
        } catch (Exception e) {

        }
        workbook = null;
        int index = 0;
        JSONArray array = new JSONArray();
        OcBReturnOrder r;
        for (Map<String, String> columnMap : exlList) {
            if (index == 0) {
                if (!"平台单号".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 1))) {
                    jsonObject.put("code", ResultCode.FAIL);
                    jsonObject.put("message", "导入备注格式不正确，请参考模板");
                    return jsonObject;
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))
                        && StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 1))) {
                    String code = columnMap.get(rowStr + index + cellStr + 0);
                    String mark = columnMap.get(rowStr + index + cellStr + 1);
                    String flag = columnMap.get(rowStr + index + cellStr + 2);
                    if (StringUtils.isBlank(code)) {
                        continue;
                    }
                    if (sourceSet.add(code)) {
                        r = new OcBReturnOrder();
                        r.setOrigSourceCode(code);
                        r.setBackMessage(mark == null ? "" : mark);
                        r.setOrderflag(flag);
                        r.setModifierid(Long.valueOf(user.getId()));
                        r.setModifiername(user.getName());
                        r.setModifierename(user.getEname());
                        sourceMap.put(code, r);
                        array.add(code);
                    } else {
                        OcBReturnOrder o = sourceMap.get(code);
                        String s = o.getBackMessage();
                        if (StringUtils.isNotBlank(s)) {
                            s = s + ";" + mark;
                            o.setBackMessage(s);
                            sourceMap.put(code, o);
                        } else {
                            o.setBackMessage(mark);
                            sourceMap.put(code, o);
                        }
                    }
                }
            }
            index++;
        }
        jsonObject.put("sourceCode", array);
        jsonObject.put("sourceCodeMap", sourceMap);
        return jsonObject;
    }

    /**
     * @param sheetIndex  Integer
     * @param hasWorkbook Workbook
     * @return List
     * @throws Exception ex
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hasWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hasSheet = hasWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hasSheet)) {
            return Lists.newArrayList();
        }
        final int rowCount = hasSheet.getLastRowNum();
        for (int rowNum = 0; rowNum <= rowCount; rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hasRow = hasSheet.getRow(rowNum);
            if (hasRow != null) {
                Iterator<Cell> cellItr = hasRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    /**
     * @param cell cell
     * @return string
     */
    private String getCellValue(Cell cell) {
        final String pot = ",";
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case numeric:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(pot) >= 0) {
                    cellValue = cellValue.replace(pot, "");
                }
                break;
            case formula:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }


}
