package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OmsReplaceComposeCmd;
import com.jackrain.nea.oc.oms.api.patrol.BatchReplaceHangDownGoodsCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: 黄世新
 * @Date: 2020/2/25 4:28 下午
 * @Version 1.0
 */
@Api(value = "OmsReplaceComposeCtrl", tags = "替换组合商品")
@Slf4j
@RestController
public class OmsReplaceComposeCtrl {


    @Autowired
    private OmsReplaceComposeCmd omsReplaceComposeCmd;

    @Autowired
    private BatchReplaceHangDownGoodsCmd batchReplaceHangDownGoodsCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;


    @ApiOperation(value = "组合商品替换")
    @RequestMapping(value = "/api/cs/oc/oms/v1/replaceCompose", method = RequestMethod.POST)
    public JSONObject replaceCompose(HttpServletRequest request, @RequestBody JSONObject object) {
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }

            vh = omsReplaceComposeCmd.replaceComposePro(object, user);
            //记录日志信息。Finish 标记结束
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }


    @ApiOperation(value = "批量替换下挂商品")
    @RequestMapping(value = "/api/cs/oc/oms/v1/batchReplaceHangDownGoods", method = RequestMethod.POST)
    public JSONObject BatchReplaceHangDownGoods(HttpServletRequest request,
                                                @RequestBody JSONObject obj) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 批量替换下挂商品入参:{}", obj);

        }
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");

            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }

            vh = batchReplaceHangDownGoodsCmd.batchReplaceHangDownGoods(obj, user, usrPem);
            //记录日志信息。Finish 标记结束
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }
}
