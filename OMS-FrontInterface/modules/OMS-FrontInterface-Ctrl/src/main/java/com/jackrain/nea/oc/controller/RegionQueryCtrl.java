package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.RegionQueryByNameCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-03-22 19:19
 */
@Api(value = "RegionQueryCtrl", tags = "省市区查询")
@Slf4j
@RestController
public class RegionQueryCtrl {

    @Autowired
    private RegionQueryByNameCmd regionQueryByNameCmd;

    @ApiOperation(value = "根据name查省市区id")
    @RequestMapping(value = "/api/cs/oc/oms/v1/queryResionByName", method = RequestMethod.POST)
    public JSONObject queryRegionByName(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = regionQueryByNameCmd.queryByName(obj);
        return vh.toJSONObject();
    }
}
