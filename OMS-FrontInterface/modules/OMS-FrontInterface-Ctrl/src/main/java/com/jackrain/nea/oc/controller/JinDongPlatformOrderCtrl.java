package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.VoidJinDongOrderCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/7/21
 */

@Api(value = "JinDongPlatformOrderCtrl", tags = "京东作废发货单")
@Slf4j
@RestController
public class JinDongPlatformOrderCtrl {

    @Autowired
    private VoidJinDongOrderCmd voidJinDongOrderCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "京东作废发货单, 并作废订单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/voidJinDongSendAndOrder", method = RequestMethod.POST)
    public JSONObject voidJinDongSendAndOrder(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (obj == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("Param Is Null");
            return vh.toJSONObject();
        }
        Long id = obj.getLong("ID");
        if (id == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("Param ID Is Null");
            return vh.toJSONObject();
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        vh = voidJinDongOrderCmd.voidSgSendService(id, user);
        if (vh == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("Service Return Result Is Null");
            return vh.toJSONObject();
        }
        return vh.toJSONObject();
    }
}
