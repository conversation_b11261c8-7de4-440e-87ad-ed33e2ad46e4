package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ModifyGoodsCheckCmd;
import com.jackrain.nea.oc.oms.api.ModifyGoodsCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 周琳胜
 * @since: 2019/3/13
 * create at : 2019/3/13 16:40
 */
@Api(value = "ModifyGoodsCtrl", tags = "更换商品按钮")
@Slf4j
@RestController
public class ModifyGoodsCtrl {

    @Autowired
    private ModifyGoodsCheckCmd modifyGoodsCheckCmd;

    @Autowired
    private ModifyGoodsCmd modifyGoodsCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "更换商品前置判断")
    @RequestMapping(value = "/api/cs/oc/oms/v1/modifygoodscheck", method = RequestMethod.POST)
    public JSONObject modifyGoodsCheck(HttpServletRequest request, @RequestBody JSONObject obj) {

        //记录日志信息
        ValueHolderV14 vh = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = modifyGoodsCheckCmd.execute(obj, user);
            //记录日志信息。Finish 标记结束
//            log.debug("Finish modifyGoodsCheckCmd.modifyGoodsCheck. Return Result=" + vh.toJSONObject());
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        }
    }

    @ApiOperation(value = "更换商品确定按钮")
    @RequestMapping(value = "/api/cs/oc/oms/v1/modifygoods", method = RequestMethod.POST)
    public JSONObject modifyGoods(HttpServletRequest request, @RequestBody JSONObject obj) {
        //记录日志信息
//        log.debug("start modifyGoods.modifyGoods.ReceiveParams=" + obj);
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        // User user = Security4Utils.getUser("root");
        //ModifyGoodsCtrl mr = new ModifyGoodsCtrl();
        //User user = mr.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = modifyGoodsCmd.execute(obj, user);
            //记录日志信息。Finish 标记结束
//            log.debug("Finish modifyGoodsCmd.modifyGoods. Return Result=" + vh.toJSONObject());
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        }
    }

}
