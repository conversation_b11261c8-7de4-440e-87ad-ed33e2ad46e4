package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OrderInterceptionCmd;
import com.jackrain.nea.oc.oms.model.result.OrderInterceptionResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 夏继超
 * @since: 2019/3/11
 * create at : 2019/3/11 10:41
 */
@Api(value = "OrderInterceptionCtrl", tags = "订单拦截")
@Slf4j
@RestController
public class OrderInterceptionCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OrderInterceptionCmd orderInterceptionCmd;
    /**
     * 订单的拦截
     *
     * @param request 请求参数
     * @param obj     订单的id
     * @return 返回信息
     */
    @ApiOperation(value = "订单拦截")
    @RequestMapping(value = "api/cs/oc/oms/v1/orderInterception", method = RequestMethod.POST)
    public JSONObject orderInterception(HttpServletRequest request,
                                        @RequestBody JSONObject obj) {
        ValueHolderV14<OrderInterceptionResult> vh = new ValueHolderV14<OrderInterceptionResult>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
       /* User user = new UserImpl();
        ((UserImpl) user).setLocale(new Locale("zh"));
        ((UserImpl) user).setId(2);*/
        try {

            ValueHolder valueHolder = orderInterceptionCmd.orderInterception(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 取消订单的拦截
     *
     * @param request 请求参数
     * @param obj     订单的id
     * @return 返回信息
     */
    @ApiOperation(value = "取消订单拦截")
    @RequestMapping(value = "api/cs/oc/oms/v1/cancelInterception", method = RequestMethod.POST)
    public JSONObject cancelInterception(HttpServletRequest request,
                                         @RequestBody JSONObject obj) {
        //记录日志信息
//        log.debug("start OrderInterceptionCtrl.cancelInterception.ReceiveParams=" + obj);
        ValueHolderV14<OrderInterceptionResult> vh = new ValueHolderV14<OrderInterceptionResult>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        /*User user = new UserImpl();
        ((UserImpl) user).setLocale(new Locale("zh"));
        ((UserImpl) user).setId(2);*/
        try {

            ValueHolder valueHolder = orderInterceptionCmd.cancelInterception(obj, user);
            //记录日志信息。Finish 标记结束
//            log.debug("Finish OrderInterceptionCtrl.cancelInterception. Return Result=" + vh.toJSONObject());
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    @ApiOperation(value = "配货拦截按钮")
    @RequestMapping(value = "api/cs/oc/oms/v1/distributionInterception", method = RequestMethod.POST)
    public JSONObject distributionInterception(HttpServletRequest request,
                                               @RequestBody JSONObject obj) {
          ValueHolderV14<OrderInterceptionResult> vh = new ValueHolderV14<OrderInterceptionResult>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        /*User user = new UserImpl();
        ((UserImpl) user).setLocale(new Locale("zh"));
        ((UserImpl) user).setId(2);*/
        try {

            ValueHolderV14 valueHolder = orderInterceptionCmd.distributionInterception(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }
}
