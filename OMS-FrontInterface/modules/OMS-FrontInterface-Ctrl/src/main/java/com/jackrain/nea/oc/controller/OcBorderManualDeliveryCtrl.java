package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderManualDeliveryCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Description 手工修改为已转换controller
 * @Date 2020/1/6
 */
@Api(value = "OcBorderManualDeliveryCtrl", tags = "全渠道订单手动修改为平台发货")
@Slf4j
@RestController
public class OcBorderManualDeliveryCtrl {

    @Autowired
    private OcBOrderManualDeliveryCmd ocBOrderManualDeliveryCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "订单手动审核")
    @RequestMapping(value = "/api/cs/oc/oms/v1/doManualDeliveryOrder", method = RequestMethod.POST)
    public JSONObject orderManualDelivery(HttpServletRequest request,
                                          @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前系统登陆用户
        //User user = OcBorderAuditCtrl.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = ocBOrderManualDeliveryCmd.orderManualDelivery(obj, user);
            vh.setCode(vh.getCode());
            vh.setMessage(Resources.getMessage(vh.getMessage()));
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }
}