package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBorderDetailCmd;
import com.jackrain.nea.oc.oms.api.OcBorderUpdateCmd;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @author: 孙俊磊
 * @since: 2019-03-11
 * create at:  2019-03-11 17:54
 */
@Api(value = "OcBorderDetailCtrl", tags = "订单单对象-订单明细")
@Slf4j
@RestController
public class OcBorderDetailCtrl {

    @Autowired
    private OcBorderDetailCmd ocBorderDetailCmd;

    @Autowired
    private OcBorderUpdateCmd ocBorderUpdateCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "获取订单明细列表")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getOrderDetailList", method = RequestMethod.POST)
    public JSONObject getOrderDetailList(HttpServletRequest request, @RequestBody String param) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

        ValueHolderV14 holderV14 = ocBorderDetailCmd.getOrderDetailList(param, loginUser);
        JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));
        if(result!=null) {
            JSONObject data = result.getJSONObject("data");
            if(data!=null) {
                JSONArray records = data.getJSONArray("records");
                JSONArray jsonArray = new JSONArray();
                for (int i = 0; i < records.size(); i++) {
                    JSONObject jsonObject = records.getJSONObject(i);
                    Long proType = jsonObject.getLong("PRO_TYPE");
                    if (log.isDebugEnabled()) {
                        log.debug(this.getClass().getName() + " 获取返回数据为:{}", jsonObject.toJSONString());
                    }
                    if (proType != null && proType == SkuType.COMBINE_PRODUCT) {
                        JSONObject jsonObject1 = JSONObject.parseObject(JSONObject.toJSONString(jsonObject, new BigDecimalValueFilter4(),
                                SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullNumberAsZero));
                        jsonArray.add(jsonObject1);
                    } else {
                        JSONObject jsonObject1 = JSONObject.parseObject(JSONObject.toJSONString(jsonObject, new BigDecimalValueFilter(),
                                SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullNumberAsZero));
                        jsonArray.add(jsonObject1);
                    }
                }
                data.put("records", jsonArray);
                result.put("data", data);
            }
        }
        return result;
    }

    @ApiOperation(value = "保存订单明细")
    @RequestMapping(value = "/api/cs/oc/oms/v1/saveStandards", method = RequestMethod.POST)
    public JSONObject saveStandards(HttpServletRequest request, @RequestBody String param) {

        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            holderV14 = ocBorderDetailCmd.saveStandards(param, loginUser);
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage(ex.getMessage());
        }

        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));
        return jsonObject;
    }


    @ApiOperation(value = "更新收货地址")
    @RequestMapping(value = "/api/cs/oc/oms/v1/saveOrder", method = RequestMethod.POST)
    public JSONObject saveOrder(HttpServletRequest request, @RequestBody String param) {
        try {
            User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
            ValueHolderV14 holderV14 = new ValueHolderV14();
            holderV14 = ocBorderUpdateCmd.updateReceiveAddressNew(param, loginUser);
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(holderV14.toJSONObject(), SerializerFeature.WriteMapNullValue));
            return jsonObject;
        } catch (Exception ex) {
            String msg = ExceptionUtils.getStackTrace(ex);
            return new ValueHolderV14<>(ResultCode.FAIL,msg).toJSONObject();
        }

    }

    class BigDecimalValueFilter implements ValueFilter {
        @Override
        public Object process(Object o, String name, Object value) {

            if (null != value && value instanceof BigDecimal) {
                return ((BigDecimal) value).setScale(4, RoundingMode.HALF_UP).toString();
            }
            return value;
        }
    }

    class BigDecimalValueFilter4 implements ValueFilter {
        @Override
        public Object process(Object o, String name, Object value) {

            if (null != value && value instanceof BigDecimal) {
                return ((BigDecimal) value).setScale(4, RoundingMode.HALF_UP).toString();
            }
            return value;
        }
    }
}
