package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.Refund2ExchangeCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: qinjunlong
 * @since: 2020/12/02
 * create at : 2020/12/02 12:40
 */
@Api(value = "RefundChange2ExchangeCtrl", description = "退货转换货")
@Slf4j
@RestController
public class RefundChange2ExchangeCtrl {
    @Autowired
    private Refund2ExchangeCmd refund2ExchangeCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "退货转换货")
    @RequestMapping(value = "/api/cs/oc/oms/v1/refund2Exchange/before/validate", method = RequestMethod.POST)
    public JSONObject validate(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolder vh;
        try {
            vh = refund2ExchangeCmd.validate(obj);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh = new ValueHolder();
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

    @ApiOperation(value = "退货转换货")
    @RequestMapping(value = "/api/cs/oc/oms/v1/refund2Exchange", method = RequestMethod.POST)
    public JSONObject refund2Exchange(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolder vh;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = refund2ExchangeCmd.refund2ExchangeCmd(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh = new ValueHolder();
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

    /**
     * USER
     *
     * @return
     */
  /*  private UserImpl getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("admin");
        user.setEname("Pokemon-mapper");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }*/
}
