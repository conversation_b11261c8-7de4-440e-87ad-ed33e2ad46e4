package com.jackrain.nea.oc.controller;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBorderAgainDistributionLogisticsCmd;
import com.jackrain.nea.oc.oms.api.OrderHoldCmd;
import com.jackrain.nea.oc.oms.model.result.OrderHoldResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(value = "OrderHoldCtrl", tags = "订单重新寻物流")
@Slf4j
@RestController
public class OcBorderAgainDistributionLogisticsCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;


    @Autowired
    private OcBorderAgainDistributionLogisticsCmd logisticsCmd;

    @ApiOperation(value = "重新寻物流")
    @RequestMapping(value = "/api/cs/oc/oms/v1/againDistributionLogistics", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject againDistributionLogistics(HttpServletRequest request,
                                        @RequestBody JSONObject obj) {
        ValueHolderV14<OrderHoldResult> vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            ValueHolder valueHolder = logisticsCmd.againDistributionLogistics(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }
}
