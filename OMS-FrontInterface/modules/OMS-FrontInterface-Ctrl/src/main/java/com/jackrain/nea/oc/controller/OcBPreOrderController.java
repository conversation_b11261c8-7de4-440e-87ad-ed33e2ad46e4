package com.jackrain.nea.oc.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderImportCmd;
import com.jackrain.nea.oc.oms.api.OcBPreOrderImportCmd;
import com.jackrain.nea.oc.oms.api.OcBPreOrderUpdateCmd;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.request.OcBPreOrderUpdateShopRequest;
import com.jackrain.nea.oc.oms.services.OcBOrderImportService;
import com.jackrain.nea.oc.oms.vo.OcBOrderPreImpVO;
import com.jackrain.nea.oc.oms.vo.StCPreorderItemStrategyVO;
import com.jackrain.nea.oc.oms.vo.StCPreorderPriceSkuVO;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCPreorderFieldStrategyDO;
import com.jackrain.nea.st.model.table.StCPreorderItemStrategyDO;
import com.jackrain.nea.st.model.table.StCPreorderModelStrategyDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BeanCopierUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName OcBPreOrderController
 * @Description 订单预导入
 * <AUTHOR>
 * @Date 2022/10/12 15:06
 * @Version 1.0
 */
@Api(value = "OcBPreOrderController", tags = "订单预导入")
@Slf4j
@RestController
@RequestMapping("/api/cs/oc/oms/pre/v1/")
public class OcBPreOrderController {


    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    private static final NumberFormat nf = NumberFormat.getInstance();
    public static final String REG = "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]";

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private OcBPreOrderImportCmd ocBPreOrderImportCmd;
    @Autowired
    private OcBOrderImportCmd ocBOrderImportCmd;
    @Autowired
    private OcBPreOrderUpdateCmd ocBPreOrderUpdateCmd;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Autowired
    private OcBOrderImportService ocBOrderImportService;
    @Resource
    private CpRpcService cpRpcService;

    @ApiOperation(value = "订单管理预导入")
    @RequestMapping(path = "importOcBOrder", method = RequestMethod.POST)
    public ValueHolderV14 preImport(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file,
                                    @RequestParam(value = "modelCode", required = true) String modelCode, @RequestParam(value = "sheetName", required = true) String sheetName) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        //插入我的任务里 临时注释
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("订单预导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        return asyncPreImport(asyncTaskBody, user, file, modelCode, sheetName);
    }

    private static boolean checkMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return true;
        }
        filterSpecialStr(mobile);
        filterEmoji(mobile);
        boolean flag = false;
        for (char c : mobile.toCharArray()) {
            if (c >= 0x4E00 && c <= 0x9FA5) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    @RequestMapping(path = "getModelList", method = RequestMethod.GET)
    public ValueHolderV14 getModelList(HttpServletRequest request) {
        return ocBPreOrderImportCmd.getAllModel();
    }

    /**
     * 修改店铺信息
     *
     * @param request
     * @param obj
     * @return
     */
    @RequestMapping(value = "updateShop", method = RequestMethod.POST)
    public ValueHolderV14 updateShop(HttpServletRequest request,
                                     @RequestBody OcBPreOrderUpdateShopRequest obj) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        return ocBPreOrderUpdateCmd.updateShop(obj.getTids(), obj.getShopTitle(), user);
    }

    /**
     * 按照流水转入
     *
     * @param request
     * @param serial_number
     * @return
     */
    @RequestMapping(value = "serial/transfer", method = RequestMethod.POST)
    public ValueHolderV14 transferBySerial(HttpServletRequest request,
                                           @RequestBody String serial_number) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        // 根据tid 查询订单，并且调用订单新增接口
        return ocBPreOrderUpdateCmd.transferBySerial(serial_number, user);
    }

    @ApiOperation(value = "订单管理预获取sheet")
    @RequestMapping(path = "getSheetList", method = RequestMethod.POST)
    public ValueHolderV14 getSheetList(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        InputStream inputStream = null;
        List<String> sheetNameList = new ArrayList<>();
        ValueHolderV14 valueHolderV14 = new ValueHolderV14(ResultCode.SUCCESS, "success");
        try {
            inputStream = file.getInputStream();
            Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
            int sheetNumber = hssfWorkbook.getNumberOfSheets();
            for (int i = 0; i < sheetNumber; i++) {
                String sheetName = hssfWorkbook.getSheetAt(i).getSheetName();
                sheetNameList.add(sheetName);
            }
            valueHolderV14.setData(sheetNameList);
        } catch (Exception e) {
            valueHolderV14.setCode(ResultCode.FAIL);
            valueHolderV14.setMessage(e.getMessage());
        }
        return valueHolderV14;
    }

    public List<OcBOrderPreImpVO> getPreOcBOrderList(Workbook hssfWorkbook, String sheetName, String modelCode) {
        List<OcBOrderPreImpVO> ocBOrderPreImpVOS = Lists.newArrayList();
        List<Map<String, String>> execlList;
        try {
            execlList = readExcel(0, hssfWorkbook, sheetName, modelCode);
        } catch (Exception e) {
            log.error(LogUtil.format("读取excel数据失败", "订单预导入处理失败"), e);
            throw new NDSException(e);
        }

        int index = 1;
        List<StCPreorderItemStrategyDO> preorderItemStrategyDOList = stRpcService.getItemByModelCode(modelCode);
        Map<String, StCPreorderItemStrategyVO> itemStrategyDOMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(preorderItemStrategyDOList)) {
            for (StCPreorderItemStrategyDO preorderItemStrategyDO : preorderItemStrategyDOList) {
                StCPreorderItemStrategyVO preorderItemStrategyVO = new StCPreorderItemStrategyVO();
                BeanCopierUtil.copy(preorderItemStrategyDO, preorderItemStrategyVO);
                itemStrategyDOMap.put(preorderItemStrategyDO.getItemName(), preorderItemStrategyVO);
            }
        }

        StCPreorderModelStrategyDO strategyByModelCode = stRpcService.getStrategyByModelCode(modelCode);
        if (strategyByModelCode == null) {
            throw new NDSException("不存在已提交的预导入模版");
        }

        // 查询店铺的已审核策略
        Map<String, StCPreorderPriceSkuVO> skuPriceMap = ocBOrderImportService.getContentSkuPrice(strategyByModelCode.getCpCShopId());

        //查询店铺信息
        CpShop cpShop = cpRpcService.selectShopById(strategyByModelCode.getCpCShopId());

        for (Map<String, String> columnMap : execlList) {
            OcBOrderPreImpVO ocBOrderImpVo = new OcBOrderPreImpVO();
            if (StringUtils.isNotEmpty(columnMap.get("下单店铺")) || StringUtils.isNotEmpty(columnMap.get("平台单号"))) {
                // 组装待配货记录
                ocBOrderPreImpVOS.add(OcBOrderPreImpVO.importCreate(index, ocBOrderImpVo, columnMap, itemStrategyDOMap, skuPriceMap,
                        cpShop == null ? "" : cpShop.getCpCShopTitle()));
            }
            index++;
        }
        OcBorderListEnums.changePreImportListTo(ocBOrderPreImpVOS);
        return ocBOrderPreImpVOS;
    }

    /**
     * 过滤特殊字符
     *
     * @param str
     * @return
     */
    private static String filterSpecialStr(String str) {
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("").trim();
    }

    private static String filterEmoji(String source) {
        if (source != null) {
            Pattern emoji = Pattern.compile(REG, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(source);
            if (emojiMatcher.find()) {
                source = emojiMatcher.replaceAll("");
                return source;
            }
            return source;
        }
        return source;
    }

    private static boolean isFewYearAgo(int few, Date date) {
        if (date == null) {
            return false;
        }
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        LocalDateTime previousYear = LocalDateTime.now().minus(few, ChronoUnit.YEARS);
        return localDateTime.isBefore(previousYear);
    }

    public ValueHolderV14 asyncPreImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file, String modelCode, String sheetName) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_PRE_ORDER");
        commonTaskExecutor.submit(() -> {
            try {
                if (usrPem == null) {
                    throw new NDSException("未获取到用户权限!");
                }
                if (file == null) {
                    throw new NDSException("请求参数不能为空!");
                }
                InputStream inputStream = file.getInputStream();
                Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
                if (hssfWorkbook.getNumberOfSheets() < 1) {
                    throw new NDSException("订单管理预导入模板不正确");
                }
                List<OcBOrderPreImpVO> ocBOrderPreImpVOS = getPreOcBOrderList(hssfWorkbook, sheetName, modelCode);
                if (CollectionUtils.isEmpty(ocBOrderPreImpVOS)) {
                    throw new NDSException("导入数据不能为空!");
                }
                // 将导入做成参数配置
                PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
                int maxImportSize = config.getProperty("r3.oc.oms.import.order.max.qty", 5000);

                if (ocBOrderPreImpVOS.size() > maxImportSize) {
                    throw new NDSException("导入条数请勿超过" + maxImportSize + "条!");
                }
                ValueHolderV14 valueHolder;
                if (!isCheckFlagPre(ocBOrderPreImpVOS)) {
                    valueHolder = new ValueHolderV14<>();
                    valueHolder.setData(ocBOrderImportCmd.exportPreImpErrorResult(ocBOrderPreImpVOS, user, modelCode));
                    valueHolder.setCode(ResultCode.FAIL);
                    valueHolder.setMessage("订单导入失败，详情见文件内容");
                } else {
                    valueHolder = ocBPreOrderImportCmd.importPreOrder(ocBOrderPreImpVOS, user, modelCode);
                }

                retMap.put("code", ResultCode.SUCCESS);
                retMap.put("data", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());

                if (valueHolder.getCode() == ResultCode.FAIL) {
                    retMap.put("code", ResultCode.FAIL);
                    asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                    asyncTaskBody.setTaskType("导出");
                    asyncTaskBody.setExportUrl(String.valueOf(valueHolder.getData()));
                    throw new NDSException("导入失败，请检查导入数据");
                }

                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskBody.setTaskType("导出");
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception e) {
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + e.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("零售发货单预导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }

    /**
     * 检查数据合法性
     *
     * @param ocBOrderPreImpVOS
     * @return
     */
    private boolean isCheckFlagPre(List<OcBOrderPreImpVO> ocBOrderPreImpVOS) {
        boolean checkFlag = true;
        Map<String, Integer> repetitionMap = Maps.newHashMap();
        Map<String, String> checkMap = new HashMap<>();
        for (OcBOrderPreImpVO f : ocBOrderPreImpVOS) {
            StringBuilder checkMessage = new StringBuilder();

//            if (StringUtils.isBlank(f.getCpCShopTitle())) {
//                checkMessage.append("[店铺不允许为空]");
//            }

            if (StringUtils.isBlank(f.getSourceCode())) {
                checkMessage.append("[平台单号不允许为空]");
            }

            if (StringUtils.isBlank(f.getReceiverMobile())) {
                checkMessage.append("[收件人手机号不允许为空]");
            }

            if (StringUtils.isBlank(f.getReceiverName())) {
                checkMessage.append("[收件人不允许为空]");
            }

            if (ObjectUtil.isNull(f.getQty())) {
                checkMessage.append("[数量不允许为空]");
            }

            if (ObjectUtil.isNull(f.getPriceActual())) {
                checkMessage.append("[成交单价不允许为空]");
            }

            if (StringUtils.isBlank(f.getReceiverAddress())) {
                checkMessage.append("[详细地址不允许为空]");
            }

            if (StringUtils.isBlank(f.getPsCSkuEcode())) {
                checkMessage.append("[商品SKU(导入内容未匹配)不允许为空]");
            }

            if (checkMobile(f.getReceiverMobile())) {
                checkMessage.append("[收货人手机号格式有误，请重新输入！]");
            }

            if (f.getPayType() == null || f.getPayType() == 0) {
                checkMessage.append("[主表付款方式填写错误]");
            }

            if (Objects.isNull(f.getOrderDate())) {
                checkMessage.append("[主表下单时间不能为空且格式需要为 yyyy-MM-dd HH:mm:ss]");
            } else if (isFewYearAgo(1, f.getOrderDate())) {
                checkMessage.append("[主表下单时间异常,时间不能小于当前时间 - 1年]");
            }
            if (Objects.isNull(f.getPayTime())) {
                checkMessage.append("[主表支付时间不能为空且格式需要为 yyyy-MM-dd HH:mm:ss]");
            } else if (f.getPayTime().after(new Date())) {
                checkMessage.append("[主表支付时间异常,时间不能大于当前时间]");
            }

            String key = f.getSourceCode() + "_" + f.getPsCSkuEcode() + "_" + f.getIsGift();
            if (repetitionMap.containsKey(key)) {
                checkMessage.append("[该数据行与数据行[" + repetitionMap.get(key) + "]重复!导入内容:[" + f.getImportContent() + "]");
            } else {
                repetitionMap.put(key, f.getRowNum());
            }

            // 校验发货信息是否一致
            String value = f.getCpCShopTitle() +
                    Optional.ofNullable(f.getShipAmt()).orElse(BigDecimal.ZERO) +
                    Optional.ofNullable(f.getUserNick()).orElse("") +
                    Optional.ofNullable(f.getSourceCode()).orElse("") +
                    Optional.ofNullable(f.getPayType()).orElse(1) +
                    Optional.ofNullable(f.getReceiverName()).orElse("") +
                    Optional.ofNullable(f.getReceiverMobile()).orElse("") +
                    Optional.ofNullable(f.getReceiverPhone()).orElse("") +
                    Optional.ofNullable(f.getReceiverZip()).orElse("") +
                    Optional.ofNullable(f.getCpCRegionProvinceEname()).orElse("") +
                    Optional.ofNullable(f.getCpCRegionCityEname()).orElse("") +
                    Optional.ofNullable(f.getCpCRegionAreaEname()).orElse("") +
                    Optional.ofNullable(f.getReceiverAddress()).orElse("") +
                    f.getOrderDate() +
                    f.getPayTime() +
                    Optional.ofNullable(f.getBuyerMessage()).orElse("") +
                    Optional.ofNullable(f.getSellerMemo()).orElse("");
            if (checkMap.containsKey(f.getSourceCode())) {
                if (!StringUtils.equals(value, checkMap.get(f.getSourceCode()))) {
                    checkMessage.append("[同一平台单号发货信息不一致！]");
                }
            } else {
                checkMap.put(f.getSourceCode(), value);
            }

            if (StringUtils.isNotBlank(checkMessage.toString())) {
                f.setErrorMsg(checkMessage.toString());
                checkFlag = false;
                checkMessage.setLength(0);
            }
        }

        repetitionMap.clear();
        checkMap.clear();
        if (log.isDebugEnabled()) {
            log.debug(" end OcBOrderPreImportController  check import flag:{}, dataList:{}", checkFlag, JSONObject.toJSONString(ocBOrderPreImpVOS));
        }
        return checkFlag;
    }


    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook, String sheetName, String modelCode) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheet(sheetName);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }
        Map<Integer, String> columnMap = Maps.newTreeMap();
        Map<String, Integer> columnMap2 = Maps.newTreeMap();

        int row = 0;
        // 先获取到所有的列名以及顺序
        for (int rowNum = 0; rowNum <= 0; rowNum++) {
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    columnMap.put(cell.getColumnIndex(), getCellValue(cell));
                    columnMap2.put(getCellValue(cell), cell.getColumnIndex());
                    row++;
                }
            }
        }
        if (ObjectUtil.notEqual(row, columnMap2.keySet().size())) {
            throw new NDSException("存在重复列");
        }

        // 根据模板编码 查找到对应的模板字段。然后进行反向映射
        List<StCPreorderFieldStrategyDO> fieldStrategyDOList = stRpcService.getFieldStrategyByModelCode(modelCode);
        if (CollectionUtils.isEmpty(fieldStrategyDOList)) {
            throw new NDSException("订单导入模板异常");
        }
        Map<String, StCPreorderFieldStrategyDO> fieldStrategyDOMap =
                fieldStrategyDOList.stream().collect(Collectors.toMap(StCPreorderFieldStrategyDO::getCustomizeField, Function.identity()));

        for (int rowNum = 1; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    StCPreorderFieldStrategyDO fieldStrategyDO = fieldStrategyDOMap.get(columnMap.get(cell.getColumnIndex()));
                    String standardField = "";
                    if (ObjectUtil.isNotNull(fieldStrategyDO) && StringUtils.isNotEmpty(fieldStrategyDO.getStandardField())) {
                        standardField = fieldStrategyDO.getStandardField();
                    }
                    map.put(standardField, getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }


    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }

    private Map<String, String> getModelMap(String modelCode) {
        Map<String, String> map = new HashMap<>();

        return map;
    }

}
