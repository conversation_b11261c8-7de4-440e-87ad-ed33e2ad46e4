package com.jackrain.nea.oc.controller;

import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OcBOrderToBManualSourcingCmd;
import com.jackrain.nea.oc.oms.model.request.OcBOrderToBManualSourcingBatchRequest;
import com.jackrain.nea.oc.oms.model.request.OcBOrderToBManualSourcingRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> lin yu
 * @date : 2022/8/4 下午1:32
 * @describe :
 */

@Api(value = "OcBOrderToBManualSourcingCtrl", tags = "TOB寻仓寻物流")
@Slf4j
@RestController
public class OcBOrderToBManualSourcingCtrl {

    @Autowired
    private OcBOrderToBManualSourcingCmd toBManualSourcingCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "数据查询接口")
    @PostMapping(value = "/api/cs/oc/oms/queryData")
    public ValueHolderV14 queryData(HttpServletRequest request,
                                    @RequestBody OcBOrderToBManualSourcingRequest toBManualSourcingRequest) {


        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }
        toBManualSourcingRequest.setUser(user);
        return toBManualSourcingCmd.dataQuery(toBManualSourcingRequest);
    }


    @ApiOperation(value = "确认接口")
    @PostMapping(value = "/api/cs/oc/oms/confirm")
    public ValueHolderV14 confirm(HttpServletRequest request,
                                  @RequestBody OcBOrderToBManualSourcingRequest toBManualSourcingRequest) {


        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }
        toBManualSourcingRequest.setUser(user);
        return toBManualSourcingCmd.confirm(toBManualSourcingRequest);
    }

    @ApiOperation(value = "批量确认接口")
    @PostMapping(value = "/api/cs/oc/oms/batchConfirm")
    public ValueHolderV14 batchConfirm(HttpServletRequest request,
                                       @RequestBody OcBOrderToBManualSourcingBatchRequest toBManualSourcingBatchRequest) {


        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }

        toBManualSourcingBatchRequest.setUser(user);
        return toBManualSourcingCmd.batchConfirm(toBManualSourcingBatchRequest);
    }
}
