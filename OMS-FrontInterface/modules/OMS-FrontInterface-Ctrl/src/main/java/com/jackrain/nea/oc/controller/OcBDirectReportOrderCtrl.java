package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.share.model.result.out.SgBShareOutQueryResult;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutQueryRes;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.dto.OcBDirectReportOrderImportVo;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.request.OcBDirectReportTransRequest;
import com.jackrain.nea.oc.oms.services.directreport.OcBDirectReportOrderImportService;
import com.jackrain.nea.oc.oms.services.directreport.OcBDirectReportOrderTransStorageService;
import com.jackrain.nea.rpc.SgNewRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 直发预占单
 *
 * <AUTHOR>
 * @since 2024-12-02 16:50
 */
@Slf4j
@RestController
public class OcBDirectReportOrderCtrl {
    @Resource
    private SgNewRpcService sgNewRpcService;
    @Resource
    private OcBDirectReportOrderTransStorageService ocBDirectReportOrderTransStorageService;

    @Resource
    private OcBDirectReportOrderImportService ocBDirectReportOrderImportService;

    @Resource
    private AsyncTaskManager asyncTaskManager;

    @Resource
    private R3PrimWebAuthService r3PrimWebAuthService;

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();
    private static final Integer maxLine = 10000;

    @RequestMapping(value = "/api/cs/oc/oms/v1/OcBDirectReportOrder/qeuryShareOut", method = RequestMethod.GET)
    public ValueHolderV14<SgBShareOutQueryResult> qeuryShareOut(@RequestParam(value = "id") Long id) {
        if (Objects.isNull(id)) {
            return ValueHolderV14Utils.getFailValueHolder("ID不能为空");
        }

        SgBShareOutQueryResult data;
        try {
            data = sgNewRpcService.queryShareOut(SgConstantsIF.BILL_TYPE_DIRECT_ORDER, id, null);
        } catch (Exception e) {
            log.warn(LogUtil.format("查询配销占用失败，入参:{}，异常:{}", "SgNewRpcService.queryShareOut"),
                    id, Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder("查询配销占用失败：" + e.getMessage());
        }
        return new ValueHolderV14<>(data, ResultCode.SUCCESS, "查询成功");
    }

    @RequestMapping(value = "/api/cs/oc/oms/v1/OcBDirectReportOrder/queryStoOut", method = RequestMethod.GET)
    public ValueHolderV14<SgBStoOutQueryRes> queryStoOut(@RequestParam(value = "id") Long id) {
        if (Objects.isNull(id)) {
            return ValueHolderV14Utils.getFailValueHolder("ID不能为空");
        }

        SgBStoOutQueryRes data;
        try {
            data = sgNewRpcService.queryStoOut(SgConstantsIF.BILL_TYPE_DIRECT_ORDER, id, null);
        } catch (Exception e) {
            log.warn(LogUtil.format("查询逻辑占用失败，入参:{}，异常:{}", "SgNewRpcService.queryStoOut"),
                    id, Throwables.getStackTraceAsString(e));
            return ValueHolderV14Utils.getFailValueHolder("查询逻辑占用失败：" + e.getMessage());
        }
        return new ValueHolderV14<>(data, ResultCode.SUCCESS, "查询成功");
    }

    @RequestMapping(value = "/api/cs/oc/oms/v1/OcBDirectReportOrder/storageTrans", method = RequestMethod.POST)
    public ValueHolderV14<Void> storageTrans(HttpServletRequest httpServletRequest,
                                             @RequestBody OcBDirectReportTransRequest request) {
        ValueHolderV14<Void> v14 = new ValueHolderV14<>();
        if (request == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请求参数不能为空！");
            return v14;
        }
        if (request.getId() == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("直发预占单id不能为空！");
            return v14;
        }
        if (StringUtils.isEmpty(request.getOrderNo())) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("订单单据编号不能为空！");
            return v14;
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(httpServletRequest);
        return ocBDirectReportOrderTransStorageService.storageTrans(request, user);
    }


    @ApiOperation(value = "获取导入模板地址")
    @RequestMapping(path = "/api/cs/oc/oms/v1/OcBDirectReportOrder/importTemplate",
            method = {RequestMethod.GET, RequestMethod.POST})
    public ValueHolderV14 queryInvoiceImportUrl() {
        ValueHolderV14 vh = ocBDirectReportOrderImportService.genTemplateDownloadUrl();
        return vh;
    }


    @ApiOperation(value = "导入")
    @RequestMapping(value = "/api/cs/oc/oms/v1/OcBDirectReportOrder/import", method = RequestMethod.POST)
    public ValueHolderV14<Void> selectLogData(HttpServletRequest request,
                                              @RequestParam(value = "file", required = true) MultipartFile file) {
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = getRootUser();
        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("直发提报单头明细导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        return asyncImport(asyncTaskBody, user, file);
    }


    public ValueHolderV14 asyncImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        CompletableFuture.runAsync(() -> {
            try {
                if (file == null) {
                    throw new NDSException("导入文件不能为空!");
                }
                InputStream inputStream = file.getInputStream();
                Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
                if (hssfWorkbook.getNumberOfSheets() != 1) {
                    throw new NDSException("导入模板不正确");
                }
                List<OcBDirectReportOrderImportVo> importVoList = getImportDataList(hssfWorkbook);
                if (CollectionUtils.isEmpty(importVoList)) {
                    throw new NDSException("导入数据不能为空!");
                }

                ValueHolderV14 valueHolder = ocBDirectReportOrderImportService.importVoList(importVoList, user);

                retMap.put("code", ResultCode.SUCCESS);
                retMap.put("data", valueHolder.getData());
                retMap.put("path", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskBody.setTaskType("导出");
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception ex) {
                log.error("导入异常", ex);
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + ex.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("头明细导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }

    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public List<OcBDirectReportOrderImportVo> getImportDataList(Workbook hssfWorkbook) {
        List<OcBDirectReportOrderImportVo> importVoList = Lists.newArrayList();
        List<Map<String, String>> excelList = readExcel(0, hssfWorkbook);
        if (excelList.size() > maxLine + 2) {
            throw new NDSException("导入条数请勿超过10000！");
        }
        int index = 0;
        for (Map<String, String> columnMap : excelList) {
            if (index == 0) {
                //跳过说明头
                index++;
                continue;
            }
            if (index == 1) {
                // 校验excel字段
                if (columnMap.size() != 10
                        || !"平台店铺编码".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"分货部门编码".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"配销仓编码".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"逻辑仓编码".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"预计发货时间".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"库存释放时间".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"商品编码".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"数量".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"开始生产时间".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"结束生产时间".equals(columnMap.get(rowStr + index + cellStr + 9))
                ) {
                    throw new NDSException("导入模版不正确，请确认！");
                }
            } else {
                importVoList.add(OcBDirectReportOrderImportVo.importCreate(index, columnMap));
            }
            index++;
        }
        return importVoList;
    }

    /**
     * 读取文件
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) {
        List<Map<String, String>> list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.contains(",")) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }


}
