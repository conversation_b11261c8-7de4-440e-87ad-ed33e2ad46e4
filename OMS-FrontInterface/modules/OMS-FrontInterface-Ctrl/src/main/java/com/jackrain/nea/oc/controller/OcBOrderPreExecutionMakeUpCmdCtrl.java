package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.PreExecutionMakeUpCmd;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: hly
 * @since: 2020/04/19
 * create at : 2020/04/19 21:08
 */
@Api(value = "OcBOrderPreExecutionMakeUpCmdCtrl", tags = "订单预执行补偿接口")
@Slf4j
@RestController
public class OcBOrderPreExecutionMakeUpCmdCtrl {

    @Autowired
    private PreExecutionMakeUpCmd preExecutionMakeUpCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 订单预执行补偿请求/api/cs/oc/oms/v1/preExecutionMakeUp
     *
     * @param request 请求对象
     * @param obj     请求参数
     * @return json对象
     */
    @ApiOperation(value = "订单预执行补偿接口")
    @RequestMapping(value = "/api/cs/oc/oms/v1/preExecutionMakeUp", method = RequestMethod.POST)
    public JSONObject preExecutionMakeUp(HttpServletRequest request,
                                         @RequestBody JSONObject obj) {
        ValueHolderV14 vh = null;
        //获取当前登陆用户
        User user = SystemUserResource.getRootUser();
        try {
            vh = preExecutionMakeUpCmd.execute(obj, user);
            vh.setCode(vh.getCode());
            vh.setMessage(Resources.getMessage(vh.getMessage()));
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("订单预执行补偿接口请求失败" + Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

}