package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.ExamineTheFefundAfterDeliveryCmd;
import com.jackrain.nea.oc.oms.api.RefundRefusePaymentCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description: 额外退款
 * @author: 江家雷
 * @since: 2020/12/14
 * create at : 2020/12/14 10:27
 */
@Api(value = "RefundFormAfterDeliveryCtrl", tags = "额外退款相关服务接口")
@Slf4j
@RestController
public class RefundAfterDeliveryCrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @DubboReference(version = "1.0", group = "oms-fi")
    private ExamineTheFefundAfterDeliveryCmd examineTheFefundAfterDeliveryCmd;

    @DubboReference(version = "1.0", group = "oms-fi")
    private RefundRefusePaymentCmd refundRefusePaymentCmd;

    @ApiOperation(value = "额外退款审核")
    @PostMapping(path = "/api/cs/oc/oms/v1/examineTheRefundAfterDelivery")
    public ValueHolder examineTheRefundAfterDelivery(HttpServletRequest request, @RequestBody JSONObject ids){
        ValueHolder valueHolder;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("search", request);
        event.put("param", ids);
        querySession.setEvent(event);
        try{
            valueHolder = examineTheFefundAfterDeliveryCmd.execute(querySession);
        }catch (Exception e){
            log.error("额外退款审核异常", e);
            valueHolder = new ValueHolder();
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message",e.getMessage());
        }
        return valueHolder;
    }

    @ApiOperation(value = "额外退款拒绝打款")
    @PostMapping(path = "/api/cs/oc/oms/v1/refuseToPayOcBReturnAfSend")
    public ValueHolder refuseToPayOcBReturnAfSend(HttpServletRequest request, @RequestBody JSONObject params){
        ValueHolder valueHolder;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("search", request);
        event.put("param", params);
        querySession.setEvent(event);
        try{
            valueHolder = refundRefusePaymentCmd.execute(querySession);
        }catch (Exception e){
            log.error("额外退款拒绝打款异常", e);
            valueHolder = new ValueHolder();
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message",e.getMessage());
        }
        return valueHolder;
    }
}