package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.*;
import com.jackrain.nea.oc.oms.model.request.UpdateReturnOrderRequest;
import com.jackrain.nea.oc.oms.model.result.QueryOrderListResult;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderService;
import com.jackrain.nea.oc.services.OcBReturnOrderWmsCmdImpl;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTask;
import com.jackrain.nea.web.common.HttpRequestUnique;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 退换货订单
 *
 * @author: xiWen.z
 * create at: 2019/7/16 0016
 */
@RestController
@Slf4j
@Api(value = "OcBReturnOrderCtrl", tags = "退换货订单")
public class OcBReturnOrderCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private ModifyReturnOrderWarehouseCmd modifyReturnOrderWarehouseCmd;

    @Autowired
    private ModifyReturnOrderLogisticsCmd modifyReturnOrderLogisticsCmd;

    @Autowired
    private OcBReturnOrderBatchAddCmd ocBReturnOrderBatchAddCmd;

    @Autowired
    private OcBStoreRefundInByReturnOrderCmd ocBStoreRefundInByReturnOrderCmd;

    @Autowired
    private OcBReturnOrderLogisticsTraceQueryCmd ocBReturnOrderLogisticsTraceQueryCmd;

    @Autowired
    private OcBReturnOrderWmsCmdImpl ocBReturnOrderWmsCmd;

    @Autowired
    private OcBReturnOrderService ocBReturnOrderService;

    @Autowired
    private OcBRefundOrderTerminateWarehousingCmd ocBRefundOrderTerminateWarehousingCmd;

    @ApiOperation(value = "批量修改仓库")
    @RequestMapping(path = "/api/cs/oc/oms/v1/modifyReturnOrderWarehouse", method = RequestMethod.POST)
    public JSONObject modifyReturnOrderWarehouse(HttpServletRequest request,
                                                 @RequestParam(value = "param") String param) throws Exception {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UpdateReturnOrderRequest model = JSONObject.parseObject(param, UpdateReturnOrderRequest.class);
        ValueHolderV14<QueryOrderListResult> vh = modifyReturnOrderWarehouseCmd.modifyReturnOrderWarehouse(model, loginUser);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(), "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteMapNullValue);

        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    /**
     * 批量修改物流公司
     *
     * @param request HttpServletRequest
     * @param param   String
     * @return JSONObject
     * @throws Exception ex
     */
    @ApiOperation(value = "批量修改物流公司")
    @RequestMapping(path = "/api/cs/oc/oms/v1/modifyReturnOrderLogistics", method = RequestMethod.POST)
    public JSONObject modifyReturnOrderLogistics(HttpServletRequest request,
                                                 @RequestParam(value = "param") String param) throws Exception {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UpdateReturnOrderRequest model = JSONObject.parseObject(param, UpdateReturnOrderRequest.class);
        ValueHolderV14<QueryOrderListResult> vh = modifyReturnOrderLogisticsCmd.modifyReturnOrderLogistics(model, loginUser);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(), "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteMapNullValue);
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    /**
     * 批量修改物流公司
     *
     * @param request HttpServletRequest
     * @param param   String
     * @return JSONObject
     * @throws Exception ex
     */
    @ApiOperation(value = "批量修改退回快递")
    @RequestMapping(path = "/api/cs/oc/oms/v1/modifyReturnExpress", method = RequestMethod.POST)
    public JSONObject modifyReturnExpress(HttpServletRequest request,
                                          @RequestParam(value = "param") String param) throws Exception {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UpdateReturnOrderRequest model = JSONObject.parseObject(param, UpdateReturnOrderRequest.class);

        ValueHolderV14<QueryOrderListResult> vh = modifyReturnOrderLogisticsCmd.modifyReturnExpress(model, loginUser);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(), "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteMapNullValue);
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }


    /**
     * 预退货单传WMS服务
     *
     * @param request HttpServletRequest
     * @param param   String
     * @return JSONObject
     * @throws Exception ex
     */
    @ApiOperation(value = "预退货单传WMS服务")
    @RequestMapping(path = "/api/cs/oc/oms/v1/returnToWms", method = RequestMethod.POST)
    public JSONObject returnToWms(HttpServletRequest request,
                                  @RequestBody JSONObject param) throws Exception {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

        ValueHolderV14 vh = modifyReturnOrderLogisticsCmd.returnToWms(param, loginUser);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(), "yyyy-MM-dd HH:mm:ss",
                SerializerFeature.WriteMapNullValue);

        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    /**
     * 批量新增退单
     *
     * @param request
     * @return
     * @throws Exception
     */
    @HttpRequestUnique
    @ApiOperation(value = "批量新增退单")
    @RequestMapping(path = "/api/cs/oc/oms/v1/doBatchReturnOrder", method = RequestMethod.POST)
    public JSONObject doBatchReturnOrder(HttpServletRequest request,
                                         @RequestParam(value = "searchdata", required = true) String searchdata,
                                         @RequestParam(value = "filename", required = true) String fileName,
                                         @RequestParam(value = "filetype", required = true) String fileType,
                                         @RequestParam(value = "showColumnName", required = true) boolean showColumnName) throws Exception {

        JSONObject fixColumn = JSONObject.parseObject(JSONObject.parseObject(searchdata).get("fixedcolumns").toString());
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

        // @20200718 通过zuul请求，获取用户方式需要变更
        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("returnorder", request);
        querySession.setEvent(event);

        // User loginUser = querySession.getUser();
        OcBReturnOrderCtrl returnOrderCtrl = ApplicationContextHandle.getBean(OcBReturnOrderCtrl.class);
        JSONObject jsonObject = returnOrderCtrl.batchAddReturnOrder(querySession, searchdata, fileName, fileType,
                showColumnName, fixColumn, loginUser);
        return jsonObject;
    }


    @AsyncTask("批量新增退单")
    public JSONObject batchAddReturnOrder(QuerySession session, String searchData, String fileName, String fileType,
                                          boolean showColumnName, JSONObject fixColumn, User loginUser) {

        ValueHolderV14 v14 = new ValueHolderV14();
        JSONArray jsonArray = fixColumn.getJSONArray("ids");
        Boolean isback = fixColumn.getBoolean("is_back");
        if (null == jsonArray || jsonArray.size() == 0) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请至少选择一个订单生产退换货订单！");
            return v14.toJSONObject();
        }

        List<Long> idsList = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            idsList.add(jsonArray.getLong(i));
        }


        v14 = ocBReturnOrderBatchAddCmd.ReturnOrderBatchAdd(idsList, isback, loginUser);
        return v14.toJSONObject();
    }


    /**
     * 一键入库
     *
     * @param object
     * @return
     */
    @HttpRequestUnique
    @ApiOperation(value = "一键入库")
    @PostMapping(path = "/api/cs/oc/oms/v1/inStoreByReturnOrder")
    public ValueHolder inStoreByReturnOrder(HttpServletRequest request,@RequestBody JSONObject object) {

        if (log.isDebugEnabled()) {
            log.debug(" OcBReturnOrderCtrl.inStoreByReturnOrder Start param={};", object);
        }

        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);

        QuerySessionImpl querySession = new QuerySessionImpl(loginUser);
        DefaultWebEvent event = new DefaultWebEvent("inStoreByReturnOrder",request,false);
        event.put("param", object);
        querySession.setEvent(event);

        ValueHolder valueHolder = ocBStoreRefundInByReturnOrderCmd.execute(querySession);

        if (log.isDebugEnabled()) {
            log.debug("OcBReturnOrderCtrl.doBatchReturnOrder Finish result={};", valueHolder);
        }
        return valueHolder;
    }

    @HttpRequestUnique
    @ApiOperation(value = "查询物流轨迹")
    @PostMapping(path = "/api/cs/oc/oms/v1/queryLogisticsTrace")
    public ValueHolderV14 queryLogisticsTrace(HttpServletRequest request,@RequestBody JSONObject object) {

        if (log.isDebugEnabled()) {
            log.debug(" OcBReturnOrderCtrl.queryLogisticsTrace Start param={};", object);
        }

        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);


        ValueHolderV14 valueHolder = ocBReturnOrderLogisticsTraceQueryCmd.execute(object,loginUser);

        if (log.isDebugEnabled()) {
            log.debug("OcBReturnOrderCtrl.queryLogisticsTrace Finish result={};", valueHolder);
        }
        return valueHolder;
	}

    @ApiOperation(value = "退换货单取消wms")
    @RequestMapping(value = "/api/cs/oc/oms/v1/cancelWms", method = RequestMethod.POST)
    public ValueHolderV14 returnOrderCancelWms(HttpServletRequest request,
                                            @RequestBody JSONObject obj) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug(" start OcBOrderCtrl.returnOrderCancelWms.ReceiveParams=" + obj);
        }
        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        JSONArray jsonArray = obj.getJSONArray("ids");
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            ids.add(jsonArray.getLong(i));
        }

        ValueHolderV14 valueHolderV14 = ocBReturnOrderWmsCmd.cancelReturnOrder(ids,user);
        //记录日志信息。Finish 标记结束
        log.debug(" start OcBOrderCtrl.returnOrderCancelWms.valueHolderV14 {}",JSON.toJSONString(valueHolderV14));
        return valueHolderV14;

    }

    @ApiOperation(value = "判断物流")
    @RequestMapping(value = "/api/cs/oc/oms/v1/returnOrder/queryLogisticsCode", method = RequestMethod.POST)
    public ValueHolderV14 queryLogisticsCode(HttpServletRequest request,
                                             @RequestBody JSONObject obj) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        JSONArray jsonArray = obj.getJSONArray("ids");
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            ids.add(jsonArray.getLong(i));
        }
        ValueHolderV14 valueHolderV14 = ocBReturnOrderWmsCmd.queryLogisticsCode(ids);
        return valueHolderV14;
    }


    @ApiOperation(value = "退换货单确认")
    @RequestMapping(value = "/api/cs/oc/oms/v1/returnOrder/confirm", method = RequestMethod.POST)
    public ValueHolderV14 returnOrderConfirm(HttpServletRequest request,
                                               @RequestBody JSONObject obj) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" start OcBOrderCtrl.returnOrderConfirm.ReceiveParams={}",
                    "退换货单确认入参"), obj.toJSONString());
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        JSONArray jsonArray = obj.getJSONArray("ids");
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            ids.add(jsonArray.getLong(i));
        }

        ValueHolderV14 valueHolderV14 = ocBReturnOrderWmsCmd.confirmReturnOrder(ids, user);
        //记录日志信息。Finish 标记结束
        log.debug(LogUtil.format(" start OcBOrderCtrl.returnOrderCancelWms.valueHolderV14 {}",
                "退换货单确认出参"), JSON.toJSONString(valueHolderV14));
        return valueHolderV14;

    }

    @ApiOperation(value = "传奶卡系统")
    @RequestMapping(value = "/api/cs/oc/oms/v1/returnOrder/tonaika", method = RequestMethod.POST)
    public ValueHolderV14 toNaiKa(HttpServletRequest request,
                                  @RequestParam(value = "param") String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        return ocBReturnOrderService.toNaiKa(param, user);
    }

    @ApiOperation(value = "标记完成")
    @RequestMapping(value = "/api/cs/oc/oms/v1/returnOrder/markfinish", method = RequestMethod.POST)
    public ValueHolderV14 markFinish(HttpServletRequest request,
                                     @RequestParam(value = "param") String param) {
        return ocBReturnOrderService.markFinish(param);
    }

}
