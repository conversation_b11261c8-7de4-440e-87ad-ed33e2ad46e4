package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.DelOrderItemCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Api(value = "DelOrderItemCtrl", tags = "删除订单明细")
@Slf4j
@RestController
public class DelOrderItemCtrl {
    @Autowired
    private DelOrderItemCmd delOrderItemCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * @param request 请求
     * @param obj     参数
     * @param
     * @return return
     */
    @ApiOperation(value = "删除明细")
    @RequestMapping(value = "/api/cs/oc/oms/v1/delOrderItem", method = RequestMethod.POST)
    public JSONObject delOrderItem(HttpServletRequest request,
                                   @RequestBody JSONObject obj) {
        //记录日志信息
//        if (log.isDebugEnabled()) {
//            log.debug("start DelOrderItemCtrl.delOrderItem.ReceiveParams=" + obj);
//        }


        ValueHolder vh = new ValueHolder();
        //记录日志信息
//        log.debug("Start DelOrderItemCtrl.delOrderItem. ReceiveParams=" + obj.toJSONString() + ";");
        //获取用户信息
//        User user = DelOrderItemCtrl.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = delOrderItemCmd.delOrderItem(obj, user);

        } catch (Exception ex) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(ex.getMessage()));
        }

        //记录返回信息
//        if (log.isDebugEnabled()) {
//            log.debug("Finish SaveBilCtrl.saveBill. ReturnResult=" + vh);
//        }

        return vh.toJSONObject();

    }

}

