package com.jackrain.nea.oc.controller.invoice;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.jackrain.nea.ac.service.OrderInvoiceTaskService;
import com.jackrain.nea.oc.oms.dto.invoice.mcp.CheckOrderParamMcpDTO;
import com.jackrain.nea.oc.oms.model.result.InvoiceSaveCheckReceiverResult;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.services.invoice.AcFInvoiceApplyTransService;
import com.jackrain.nea.oc.oms.services.invoice.AcFOrderInvoiceAuditService;
import com.jackrain.nea.oc.oms.services.invoice.AcFOrderInvoiceSaveService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 财务智能开票MCP
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/cs/oc/oms/mcp/invoice")
public class OrderInvoiceMcpController {

    @Resource
    private AcFOrderInvoiceSaveService invoiceSaveService;
    @Resource
    private AcFOrderInvoiceAuditService acFOrderInvoiceAuditService;
    @Resource
    private AcFInvoiceApplyTransService acFInvoiceApplyTransService;
    @Resource
    private OrderInvoiceTaskService orderInvoiceTaskService;

    /**
     * 【开票MCP】开票校验
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "开票校验")
    @PostMapping("/checkOrderParam")
    public ValueHolderV14<List<InvoiceSaveCheckReceiverResult>> checkOrderParamMcp(@RequestBody CheckOrderParamMcpDTO param) {
        log.info(LogUtil.format("OrderInvoiceMcpController.checkOrderParam.start={}", "checkOrderParam"),
                JSONObject.toJSONString(param));
        return invoiceSaveService.checkOrderParamMcp(param);
    }

    /**
     * 【开票MCP】生成发票申请单
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "生成发票申请单")
    @PostMapping("/saveApplyInvoice")
    public ValueHolderV14<SgR3BaseResult> saveApplyInvoiceMcp(@RequestBody JSONObject param) {
        log.info(LogUtil.format("OrderInvoiceMcpController.saveApplyInvoice.start={}", "saveApplyInvoice"),
                JSONObject.toJSONString(param));
        //返回发票申请单id
        return invoiceSaveService.saveApplyInvoiceMcp(param, SystemUserResource.getRootUser());
    }

    /**
     * 【开票MCP】生成发票单
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "生成发票单")
    @PostMapping("/saveInvoice")
    public ValueHolder saveInvoiceMcp(@RequestBody JSONObject param) {
        log.info(LogUtil.format("OrderInvoiceMcpController.saveInvoice.start={}", "saveInvoice"),
                JSONObject.toJSONString(param));
        return acFInvoiceApplyTransService.transfer(param, SystemUserResource.getRootUser());
    }

    /**
     * 【开票MCP】发票单审核
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "发票单审核")
    @PostMapping("/audit")
    public ValueHolder saveApplyAuditMcp(@RequestBody JSONObject param) {
        log.info(LogUtil.format("OrderInvoiceMcpController.saveApplyAuditMcp.start={}", "saveApplyAuditMcp"),
                JSONObject.toJSONString(param));
        Long applyId = param.getLong("applyId");
        List<AcFOrderInvoice> invoice = acFOrderInvoiceAuditService.getInvoice(applyId);
        if (CollectionUtils.isEmpty(invoice)) {
            return ValueHolderUtils.getFailValueHolder("发票申请单不存在");
        }
        SgR3BaseRequest request = new SgR3BaseRequest();
        request.setIds(invoice.stream().map(AcFOrderInvoice::getId).collect(Collectors.toList()));
        request.setLoginUser(SystemUserResource.getRootUser());
        return acFOrderInvoiceAuditService.audit(request);
    }

    /**
     * 【开票MCP】开票
     *
     * @param param
     * @return
     */
    @ApiOperation(value = "开票")
    @PostMapping("/invoice")
    public ValueHolderV14 invoiceInvoiceMcp(@RequestBody JSONObject param) {
        log.info(LogUtil.format("OrderInvoiceMcpController.invoiceInvoiceMcp.start={}", "invoiceInvoiceMcp"),
                JSONObject.toJSONString(param));
        Long applyId = param.getLong("applyId");
        List<AcFOrderInvoice> invoices = acFOrderInvoiceAuditService.getInvoice(applyId);
        if (CollectionUtils.isEmpty(invoices)) {
            return ValueHolderV14Utils.getFailValueHolder("发票申请单不存在");
        }

        return orderInvoiceTaskService.exccuteInvoiceMcp(invoices);
    }


}
