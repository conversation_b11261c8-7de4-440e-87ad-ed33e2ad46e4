package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcOrderModifyOutStockTypeCmd;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date ：Created in 13:05 2020/3/6
 * description ：
 * @ Modified By：
 */
@Api(value = "OcBOrderCtrl", tags = "零售发货修改出库类型")
@Slf4j
@RestController
public class OcOrderModifyOutStockTypeController {

    @Autowired
    private OcOrderModifyOutStockTypeCmd ocOrderModifyOutStockTypeCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;


    @ApiOperation(value = "修改出库类型")
    @RequestMapping(value = "/api/cs/oc/oms/v1/modifyOutStockType", method = RequestMethod.POST)
    public JSONObject modifyOutStockType(HttpServletRequest request, @RequestBody JSONObject obj) {

        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空！", user.getLocale()));
        }

        JSONArray idArray = obj.getJSONArray("ids");
        if (CollectionUtils.isEmpty(idArray)) {
            throw new NDSException(Resources.getMessage("请选择需要修改的记录！", user.getLocale()));
        }

        Integer type = obj.getInteger("type");
        if (type == null) {
            throw new NDSException(Resources.getMessage("参数type为空，请检查参数后重试！", user.getLocale()));
        }

        return ocOrderModifyOutStockTypeCmd.modifyOutStockType(idArray, type, user).toJSONObject();
    }
}
