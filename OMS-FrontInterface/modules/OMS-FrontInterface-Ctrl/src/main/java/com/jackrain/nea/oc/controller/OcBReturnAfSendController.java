package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.GetDetailCmd;
import com.jackrain.nea.oc.oms.api.OcBReturnAfSendExportCmd;
import com.jackrain.nea.oc.oms.api.OcBReturnAfSendImportCmd;
import com.jackrain.nea.oc.oms.model.result.GetOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnType;
import com.jackrain.nea.oc.oms.model.table.OcBReturnTypeItem;
import com.jackrain.nea.oc.oms.vo.OcBReturnAfSendImpVO;
import com.jackrain.nea.oc.oms.vo.OcBReturnAfSendItemVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @ClassName : OcBReturnAfSendController
 * @Description :
 * <AUTHOR>  YCH
 * @Date: 2021-08-30 19:25
 */
@RestController
@Api(value = "OcBReturnAfSendController", tags = "额外退款单导入")
@Slf4j
public class OcBReturnAfSendController {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();

    @Autowired
    private OcBReturnAfSendExportCmd ocBReturnAfSendExportCmd;
    @Autowired
    private OcBReturnAfSendImportCmd sendImportCmd;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private GetDetailCmd getDetailCmd;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }

    /**
     * 下载模板
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "下载模板")
    @RequestMapping(path = "/api/cs/oc/oms/v1/downloadReturnAfSendTemp", method = RequestMethod.POST)
    public ValueHolderV14 downloadTemp()
            throws Exception {

        //处理返回数据
        ValueHolderV14 holderV14 = ocBReturnAfSendExportCmd.downloadTemp();
        return holderV14;
    }

    @ApiOperation(value = "订单管理导入")
    @RequestMapping(path = "/api/cs/oc/oms/v1/importReturnAfSend", method = RequestMethod.POST)
    public ValueHolderV14 importReturnAfSend(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {

        if (log.isDebugEnabled()) {
            log.debug("======额外退款单导入模板开始啦=======");
        }
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody =new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("额外退款单自定义导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        return asyncImport(asyncTaskBody,user,file);
    }

    private ValueHolderV14 asyncImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            try {
                if (file == null) {
                    throw new NDSException("请求参数不能为空!");
                }
                InputStream inputStream = file.getInputStream();
                Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
                if (hssfWorkbook.getNumberOfSheets() != 1) {
                    throw new NDSException("额外订单导入模板不正确");
                }
                List<OcBReturnAfSendImpVO> ocBOrderList = getOcBOrderList(hssfWorkbook);
                if (log.isDebugEnabled()) {
                    log.debug(this.getClass().getName() + ",ocBOrderList:{}", JSON.toJSONString(ocBOrderList));
                }
                if (CollectionUtils.isEmpty(ocBOrderList)) {
                    throw new NDSException("导入数据不能为空!");
                }
                PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
                int returnAfSendMaxQty = config.getProperty("r3.oc.oms.import.returnafsend.max.qty", 5000);
                if (ocBOrderList.size() > returnAfSendMaxQty) {
                    throw new NDSException("导入条数超过配置上限" + returnAfSendMaxQty);
                }

                if (CollectionUtils.isEmpty(ocBOrderList)) {
                    throw new NDSException("订单管理导入模板主表数据不能为空!");
                }

                // 校验数据是否重复
                if (isRepetition(ocBOrderList)) {
                    throw new NDSException("订单管理导入数据重复!");
                }

                Map<String, List<OcBReturnAfSendItemVO>> mapItem = new HashMap<>();
                Map<String, OcBReturnAfSendImpVO> mapMain = new HashMap<>();

                for (OcBReturnAfSendImpVO main : ocBOrderList) {
                    //判断必填字段是否为空
                    checkout(main);
                    String sourceBillNo = main.getSourceBillNo();

                    mapMain.put(sourceBillNo, main);
                    if (mapItem.containsKey(sourceBillNo)) {
                        List<OcBReturnAfSendItemVO> list = mapItem.get(sourceBillNo);
                        OcBReturnAfSendItemVO itemVO = new OcBReturnAfSendItemVO();
                        itemVO.setAmtReturn(main.getAmtReturn());
                        itemVO.setFreight(main.getFreight());
                        itemVO.setPsCSkuEcode(main.getPsCSkuEcode());
                        itemVO.setQty(main.getQty());
                        list.add(itemVO);
                        mapItem.put(sourceBillNo, list);
                    } else {
                        List<OcBReturnAfSendItemVO> list = new ArrayList<>();
                        OcBReturnAfSendItemVO itemVO = new OcBReturnAfSendItemVO();
                        itemVO.setAmtReturn(main.getAmtReturn());
                        itemVO.setFreight(main.getFreight());
                        itemVO.setPsCSkuEcode(main.getPsCSkuEcode());
                        itemVO.setQty(main.getQty());
                        list.add(itemVO);
                        mapItem.put(sourceBillNo, list);
                    }
                }
                ValueHolderV14 v14 = sendImportCmd.ocBReturnAfSendImport(mapItem, ocBOrderList, user);
                retMap.put("code", v14.getCode());
                retMap.put("data", v14.getData());
                retMap.put("message", v14.getMessage());
                retMap.put("path",String.valueOf(v14.getData()));
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(v14.getData()));
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception ex) {
                log.error("OcBOrderImportController.importByPro导入失败", ex);
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + ex.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("额外退款单自定义导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }


    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public List<OcBReturnAfSendImpVO> getOcBOrderList(Workbook hssfWorkbook) {
        List<OcBReturnAfSendImpVO> returnAfSendImpVOList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();

        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;

        for (Map<String, String> columnMap : execlList) {
            OcBReturnAfSendImpVO ocBReturnAfSendImpVO = new OcBReturnAfSendImpVO();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 14
                        || !"单据日期".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"原始订单编号".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"退款原因".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"支付方式".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"支付账号".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"退款分类".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"退款描述".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"收款人姓名".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"卖家备注".equals(columnMap.get(rowStr + index + cellStr + 9))
                        || !"SKU编码".equals(columnMap.get(rowStr + index + cellStr + 10))
                        || !"数量".equals(columnMap.get(rowStr + index + cellStr + 11))
                        || !"退款金额".equals(columnMap.get(rowStr + index + cellStr + 12))
                        || !"运费".equals(columnMap.get(rowStr + index + cellStr + 13))
                ) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))) {
                    // 组装待配货记录
                    returnAfSendImpVOList.add(OcBReturnAfSendImpVO.importCreate(index, ocBReturnAfSendImpVO, columnMap));
                }
            }
            index++;
        }

        return returnAfSendImpVOList;
    }


    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    /**
     * 查看是否重复
     *
     * @return
     */
    public boolean isRepetition(List<OcBReturnAfSendImpVO> ocBOrderList) {
        int size = ocBOrderList.size();
        ArrayList<OcBReturnAfSendImpVO> collect = ocBOrderList.stream().collect(Collectors
                .collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(changeVo -> {
                    // 根据 原始订单编号 和 商品编码 进行去重
                    return changeVo.getSourceBillNo() + "," + changeVo.getPsCSkuEcode();
                }))), ArrayList::new));
        return size != collect.size();
    }

    /**
     * 封装额外退款单新增保存入参
     */
    private JSONObject packageSaveParam(GetOrderResult orederInfo, OcBReturnAfSendImpVO ocBReturnAfSendImpVO, List<OcBReturnAfSendItemVO> itemVOList) {
        JSONObject result = new JSONObject();
        JSONObject main = new JSONObject();
        JSONObject itemObject = new JSONObject();
        JSONArray itemArray = new JSONArray();
        result.put("objId", -1);

        String sourceBillNo = ocBReturnAfSendImpVO.getSourceBillNo();
        ValueHolderV14<List<OcBOrderItem>> v14 = getDetailCmd.getOrderItem(Long.parseLong(sourceBillNo));
        if (log.isDebugEnabled()) {
            log.debug("OcBReturnAfSendController.packageSaveParam.v14:{}",
                    JSON.toJSONString(v14));
        }
        if (v14.getCode() == ResultCode.FAIL) {
            throw new NDSException(v14.getMessage());
        }
        //主表退款金额
        BigDecimal amtReturnApply = BigDecimal.ZERO;
        List<OcBOrderItem> ocBOrderItems = v14.getData();
        //校验导入明细是否存在零售发货单明细中

        for (OcBReturnAfSendItemVO itemVO : itemVOList) {
            List<OcBOrderItem> collect = ocBOrderItems.stream().filter(s -> s.getPsCSkuEcode().equals(itemVO.getPsCSkuEcode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                throw new NDSException("订单编号为:" + sourceBillNo + "的零售发货单明细，不存在条码编码：" + itemVO.getPsCSkuEcode());
            }
            amtReturnApply = amtReturnApply.add(itemVO.getAmtReturn());
            itemObject.put("ID", collect.get(0).getId());
            itemObject.put("AMT_RETURN", itemVO.getAmtReturn());
            itemObject.put("FREIGHT", itemVO.getFreight());
            itemObject.put("QTY_IN", itemVO.getQty());
            itemArray.add(itemObject);
        }
        //查询退款分类
        ValueHolderV14<Map<OcBReturnType, List<OcBReturnTypeItem>>> mapValueHolderV14 = getDetailCmd.selectTurnTypeByEname(ocBReturnAfSendImpVO.getOcBReturnTypeEname());
        if (mapValueHolderV14.getCode() == ResultCode.FAIL) {
            throw new NDSException(mapValueHolderV14.getMessage());
        }
        Map<OcBReturnType, List<OcBReturnTypeItem>> data = mapValueHolderV14.getData();
        OcBReturnType ocBReturnType = new OcBReturnType();
        List<OcBReturnTypeItem> ocBReturnTypeItemList = new ArrayList<>();
        for (OcBReturnType key : data.keySet()) {
            ocBReturnType = key;
            ocBReturnTypeItemList = data.get(key);
        }
        //判断退款描述是否维护
        List<OcBReturnTypeItem> collect = ocBReturnTypeItemList.stream().filter(s -> s.getEname().equals(ocBReturnAfSendImpVO.getOcBReturnTypeItemEname())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            throw new NDSException("退款分类：" + ocBReturnType.getEname() + "没有维护退款描述:" + ocBReturnAfSendImpVO.getOcBReturnTypeItemEname());
        }
        /**
         * 封装参数
         */
        main.put("ID", -1);
        main.put("AMT_RETURN_APPLY", amtReturnApply);
        main.put("BILL_NO", "");
        main.put("BILL_TYPE", "1");
        main.put("CP_C_SHOP_ECODE", orederInfo.getCpCShopEcode());
        main.put("CP_C_SHOP_ID", orederInfo.getCpCShopId());
        main.put("CP_C_SHOP_TITLE", orederInfo.getCpCShopTitle());
        main.put("IMAGE", "");
        main.put("OC_B_RETURN_TYPE_ENAME", ocBReturnType.getEname());
        main.put("OC_B_RETURN_TYPE_ID", ocBReturnType.getId());
        main.put("OC_B_RETURN_TYPE_ITEM_ID", collect.get(0).getId());
        main.put("PAY_ACCOUNT", ocBReturnAfSendImpVO.getPayAccount());
        main.put("PAY_MODE", changePay(ocBReturnAfSendImpVO.getPayModeV()));
        main.put("REASON", ocBReturnAfSendImpVO.getReason());
        main.put("RECEIVER_NAME", ocBReturnAfSendImpVO.getReceiverName());
        main.put("REFUND_ORDER_SOURCE_TYPE", "1");
        main.put("remark", ocBReturnAfSendImpVO.getRemark());
        main.put("RESPONSIBLE_PARTY", ocBReturnAfSendImpVO.getResponsibleParty());
        main.put("RESPONSIBLE_PARTY_REMARK", ocBReturnAfSendImpVO.getResponsiblePartyRemark());
        main.put("RETURN_APPLY_TIME", ocBReturnAfSendImpVO.getReturnApplyTime());
        main.put("SELLER_REMARK", ocBReturnAfSendImpVO.getSellerRemark());
        main.put("SOURCE_BILL_NO", Long.parseLong(ocBReturnAfSendImpVO.getSourceBillNo()));
        main.put("TID", orederInfo.getTid());
        main.put("VIP_NICK", "");
        main.put("VIP_PHONE", "");
        main.put("VIP_NICK", "");


        result.put("AfSend", main);
        result.put("AfSendItem", itemArray);
        if (log.isDebugEnabled()) {
            log.debug("OcBReturnAfSendController.packageSaveParam.result:{}",
                    JSON.toJSONString(v14));
        }

        return result;
    }

    /**
     * 判断必填字段是否存在
     */
    public void checkout(OcBReturnAfSendImpVO ocBReturnAfSendImpVO) {


        //原始订单编号
        if (StringUtils.isEmpty(ocBReturnAfSendImpVO.getSourceBillNo())) {
            throw new NDSException("原始订单编号不能为空!");
        }
        //支付账号
        if (StringUtils.isEmpty(ocBReturnAfSendImpVO.getPayAccount())) {
            throw new NDSException("支付账号不能为空!");
        }
        //单据日期
        if (ocBReturnAfSendImpVO.getReturnApplyTime() == null) {
            throw new NDSException("单据日期不能为空!");
        }
        //退款分类
        if (StringUtils.isEmpty(ocBReturnAfSendImpVO.getOcBReturnTypeEname())) {
            throw new NDSException("退款分类不能为空!");
        }
        //退款描述
        if (StringUtils.isEmpty(ocBReturnAfSendImpVO.getOcBReturnTypeItemEname())) {
            throw new NDSException("退款描述不能为空!");
        }
        //收款人姓名
        if (StringUtils.isEmpty(ocBReturnAfSendImpVO.getReceiverName())) {
            throw new NDSException("收款人姓名不能为空!");
        }
        //SKU编码
        if (StringUtils.isEmpty(ocBReturnAfSendImpVO.getPsCSkuEcode())) {
            throw new NDSException("SKU编码不能为空!");
        }
        //数量
        if (ocBReturnAfSendImpVO.getQty() == null) {
            throw new NDSException("数量不能为空!");
        }
        //退款金额
        if (ocBReturnAfSendImpVO.getAmtReturn() == null) {
            throw new NDSException("退款金额不能为空!");
        }
        //运费
        if (ocBReturnAfSendImpVO.getFreight() == null) {
            throw new NDSException("运费不能为空!");
        }
    }

    public String changePay(String string) {
        switch (string) {
            case "支付宝":
                return "1";

            case "微信":
                return "2";

            case "现金":
                return "3";

            case "备用金":
                return "4";

            case "财付通":
                return "5";

            case "银行":
                return "6";
            default:
                return null;
        }
    }
}
