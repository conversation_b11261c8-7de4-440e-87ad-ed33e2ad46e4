package com.jackrain.nea.oc.controller;

import cn.hutool.core.io.IoUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.controller.convert.OrderConverter;
import com.jackrain.nea.oc.controller.convert.OrderExtendItemConverter;
import com.jackrain.nea.oc.oms.api.OcBOrderImportCmd;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.services.oss.OssService;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: 李龙飞
 * @create: 2019-05-10 17:10
 **/
@RestController
@RequestMapping("/api/cs/oc/oms/v1/")
@Api(value = "OcBOrderImportController", tags = "订单管理导入")
@Slf4j
public class OcBOrderImportController {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    //CellType
    public static final int _NONE = -1;
    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    public static final int BLANK = 3;
    public static final int BOOLEAN = 4;
    public static final int ERROR = 5;
    //导入excel,业务校验错误提示返回 错误excel地址
    public static final int IMPORT_ERROR_CODE = 10001;
    //导入excelche直接返回数据
    public static final int IMPORT_RESULT_DATA = 10002;
    private static final NumberFormat nf = NumberFormat.getInstance();
    @Autowired
    private OcBOrderImportCmd ocBOrderImportCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Resource
    private OssService ossService;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }

    @ApiOperation(value = "订单管理导入")
    @RequestMapping(path = "importOcBOrder", method = RequestMethod.POST)
    public ValueHolderV14 importByPro(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {

        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        return asyncImport(asyncTaskBody, user, file);
    }




    @ApiOperation(value = "订单管理导入")
    @RequestMapping(path = "importGiftOcBOrder", method = RequestMethod.POST)
    public ValueHolderV14 importGiftOcBOrderByPro(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {

        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单赠品导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        return asyncImportGift(asyncTaskBody, user, file);
    }


    public ValueHolderV14 asyncImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file) {
        if (log.isDebugEnabled()) {
            log.debug("手工订单批量导入 asyncImport start");
        }
        ValueHolderV14 holderV14 = new ValueHolderV14();
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
//            JSONObject urlObj  = new JSONObject();

            try {
                if (usrPem == null) {
                    throw new NDSException("未获取到用户权限!");
                }
                if (file == null) {
                    throw new NDSException("请求参数不能为空!");
                }
                InputStream inputStream = file.getInputStream();
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(1024);
                IoUtil.copy(inputStream, byteArrayOutputStream, 4096);
                ByteArrayInputStream readInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
                readInputStream.mark(0);
//                ByteArrayInputStream uploadInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
                Workbook hssfWorkbook = WorkbookFactory.create(readInputStream);
                // 上传oss
                readInputStream.reset();
                String filePath = ossService.uploadFile(readInputStream,
                        "OSS-Bucket/IMPORT/OC_B_ORDER/" + file.getOriginalFilename(), true);
//                urlObj.put("导入文件地址", filePath);
//                retMap.put("path", filePath);
                asyncTaskBody.setImportUrl(filePath);
                if (hssfWorkbook.getNumberOfSheets() != 1) {
                    throw new NDSException("订单管理导入模板不正确");
                }

                List<OcBOrderImpVO> ocBOrderList = getOcBOrderList(hssfWorkbook);
                if (log.isDebugEnabled()) {
                    log.debug("asyncImport getOcBOrderList :{}", JSONObject.toJSONString(ocBOrderList));
                }
                if (CollectionUtils.isEmpty(ocBOrderList)) {
                    throw new NDSException("导入数据不能为空!");
                }

                // 将导入做成参数配置
                PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
                int maxImportSize = config.getProperty("r3.oc.oms.import.order.max.qty", 5000);

                if (ocBOrderList.size() > maxImportSize) {
                    throw new NDSException("导入条数请勿超过" + maxImportSize + "条！");
                }

                ValueHolderV14 valueHolder;
                // 返回异常结果
                if (!isCheckFlag(ocBOrderList)) {
                    valueHolder = new ValueHolderV14<>();
                    valueHolder.setData(ocBOrderImportCmd.exportImpErrorResult(ocBOrderList, user,file.getOriginalFilename()));
                    valueHolder.setCode(ResultCode.FAIL); //
                    valueHolder.setMessage("订单导入失败，详情见文件内容");
                } else {
                    OrderConverter oc = new OrderConverter();
                    List<OcBOrderExtend> bOrderExtends = Lists.newArrayList(oc.convertAll(ocBOrderList));
                    OrderExtendItemConverter orderExtendItemConverter = new OrderExtendItemConverter();
                    List<OcBOrderItemExtend> bOrderItemExtends = Lists.newArrayList(orderExtendItemConverter.convertAll(ocBOrderList));
                    if (CollectionUtils.isEmpty(ocBOrderList)) {
                        throw new NDSException("订单管理导入模板主表数据不能为空!");
                    }
                    valueHolder = ocBOrderImportCmd.importOrderList(bOrderExtends, bOrderItemExtends, user,file.getOriginalFilename());
                }

                retMap.put("code", ResultCode.SUCCESS);
                retMap.put("data", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                if(Objects.nonNull(valueHolder.getData())){
                    asyncTaskBody.setExportUrl(String.valueOf(valueHolder.getData()));
//                    urlObj.put("错误文件地址",String.valueOf(valueHolder.getData()));
                }
                //任务完成
//                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskBody.setTaskType("导出");
//                asyncTaskBody.setUrl(urlObj.toJSONString());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception ex) {
                log.info(LogUtil.format("订单文件导入失败,fileName:{}"), file.getOriginalFilename(), ex);
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + ex.getMessage());
//                asyncTaskBody.setUrl(urlObj.toJSONString());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("零售发货单导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }

    /**
     * 检查数据合法性
     *
     * @param ocBOrderList
     * @return
     */
    private boolean isCheckFlag(List<OcBOrderImpVO> ocBOrderList) {
        boolean checkFlag = true;
        Map<String, Integer> repetitionMap = Maps.newHashMap();
        Map<String, String> checkMap = new HashMap<>();
        for (OcBOrderImpVO f : ocBOrderList) {
            StringBuilder checkMessage = new StringBuilder();
            if (StringUtils.isBlank(f.getSourceCode())) {
                checkMessage.append("[平台单号不允许为空]");
            }
            if (StringUtils.isBlank(f.getPsCSkuEcode())) {
                checkMessage.append("[商品SKU不允许为空]");
            }
            String key = f.getSourceCode() + "_" + f.getPsCSkuEcode() + "_" + f.getIsGift();
            if (repetitionMap.containsKey(key)) {
                checkMessage.append("[该数据行与数据行[" + repetitionMap.get(key) + "]重复!]");
            } else {
                repetitionMap.put(key, f.getRowNum());
            }

            // 校验发货信息是否一致
            String value = f.getCpCShopTitle() +
                    Optional.ofNullable(f.getShipAmt()).orElse(BigDecimal.ZERO) +
                    Optional.ofNullable(f.getUserNick()).orElse("") +
                    Optional.ofNullable(f.getSourceCode()).orElse("") +
                    Optional.ofNullable(f.getPayType()).orElse(1) +
                    Optional.ofNullable(f.getReceiverName()).orElse("") +
                    Optional.ofNullable(f.getReceiverMobile()).orElse("") +
                    Optional.ofNullable(f.getReceiverPhone()).orElse("") +
                    Optional.ofNullable(f.getReceiverZip()).orElse("") +
                    Optional.ofNullable(f.getCpCRegionProvinceEname()).orElse("") +
                    Optional.ofNullable(f.getCpCRegionCityEname()).orElse("") +
                    Optional.ofNullable(f.getCpCRegionAreaEname()).orElse("") +
                    Optional.ofNullable(f.getReceiverAddress()).orElse("") +
                    f.getOrderDate() +
                    f.getPayTime() +
                    Optional.ofNullable(f.getBuyerMessage()).orElse("") +
                    Optional.ofNullable(f.getSellerMemo()).orElse("");
            if (checkMap.containsKey(f.getSourceCode())) {
                if (!StringUtils.equals(value, checkMap.get(f.getSourceCode()))) {
                    checkMessage.append("[同一平台单号发货信息不一致！]");
                }
            } else {
                checkMap.put(f.getSourceCode(), value);
            }

            if (StringUtils.isNotBlank(checkMessage.toString())) {
                f.setDesc(checkMessage.toString());
                checkFlag = false;
                checkMessage.setLength(0);
            }
        }

        repetitionMap.clear();
        checkMap.clear();
        if (log.isDebugEnabled()) {
            log.debug(" end OcBOrderImportController  check import flag:{}, dataList:{}", checkFlag, JSONObject.toJSONString(ocBOrderList));
        }
        return checkFlag;
    }

    public ValueHolderV14 asyncImportGift(AsyncTaskBody asyncTaskBody, User user, MultipartFile file) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            try {
                if (usrPem == null) {
                    throw new NDSException("未获取到用户权限!");
                }
                if (file == null) {
                    throw new NDSException("请求参数不能为空!");
                }
                InputStream inputStream = file.getInputStream();
                Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
                if (hssfWorkbook.getNumberOfSheets() != 1) {
                    throw new NDSException("订单管理导入模板不正确");
                }
                List<OcBOrderImpVO> ocBOrderList = getOcBOrderList(hssfWorkbook);
                if (CollectionUtils.isEmpty(ocBOrderList)) {
                    throw new NDSException("导入数据不能为空!");
                }
                if (ocBOrderList.size() > 2000) {
                    throw new NDSException("导入条数请勿超过2000！");
                }
                // 校验数据是否重复
                if (isRepetition(ocBOrderList)) {
                    throw new NDSException("订单管理导入数据重复!");
                }
                OrderConverter oc = new OrderConverter();
                List<OcBOrderExtend> bOrderExtends = Lists.newArrayList(oc.convertAll(ocBOrderList));
                OrderExtendItemConverter orderExtendItemConverter = new OrderExtendItemConverter();
                List<OcBOrderItemExtend> bOrderItemExtends = Lists.newArrayList(orderExtendItemConverter.convertAll(ocBOrderList));
                if (CollectionUtils.isEmpty(ocBOrderList)) {
                    throw new NDSException("订单管理导入模板主表数据不能为空!");
                }

                if (isOrderRepeatSourceCodeAndReceiver(ocBOrderList)) {
                    throw new NDSException("平台单号重复");
                }

//            List<OcBOrderImpVO> orderListTmp = ocBOrderList;
//            orderListTmp.stream().filter(order -> order.getCpCPhyWarehouseId() == null).collect(Collectors.toList());
//            if(CollectionUtils.isNotEmpty(orderListTmp)){
//                throw new NDSException("部分订单的发货仓库为空，请检查导入数据！");
//            }
                ValueHolderV14 valueHolder = ocBOrderImportCmd.importGiftOrderList(bOrderExtends, bOrderItemExtends, user);
                retMap.put("code", ResultCode.SUCCESS);
                retMap.put("data", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskBody.setTaskType("导出");
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception ex) {
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + ex.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("零售发货单导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }

    /**
     * 判断同一单号，发货信息是否一致，不一致返回true
     *
     * @param ocBOrderList
     * @return
     */
    private boolean isOrderRepeatSourceCodeAndReceiver(List<OcBOrderImpVO> ocBOrderList) {
        Map<String, String> checkMap = new HashMap<>();
        for (OcBOrderImpVO current : ocBOrderList) {
            StringBuilder sb = new StringBuilder();
            String value = sb.append(current.getCpCShopTitle())
                    .append(Optional.ofNullable(current.getShipAmt()).orElse(BigDecimal.ZERO))
                    .append(Optional.ofNullable(current.getUserNick()).orElse(""))
                    .append(Optional.ofNullable(current.getSourceCode()).orElse(""))
                    .append(Optional.ofNullable(current.getPayType()).orElse(1))
                    .append(Optional.ofNullable(current.getReceiverName()).orElse(""))
                    .append(Optional.ofNullable(current.getReceiverMobile()).orElse(""))
                    .append(Optional.ofNullable(current.getReceiverPhone()).orElse(""))
                    .append(Optional.ofNullable(current.getReceiverZip()).orElse(""))
                    .append(Optional.ofNullable(current.getCpCRegionProvinceEname()).orElse(""))
                    .append(Optional.ofNullable(current.getCpCRegionCityEname()).orElse(""))
                    .append(Optional.ofNullable(current.getCpCRegionAreaEname()).orElse(""))
                    .append(Optional.ofNullable(current.getReceiverAddress()).orElse(""))
                    .append(current.getOrderDate())
                    .append(current.getPayTime())
                    .append(Optional.ofNullable(current.getBuyerMessage()).orElse(""))
                    .append(Optional.ofNullable(current.getSellerMemo()).orElse(""))
                    .toString();
            if (checkMap.containsKey(current.getSourceCode())) {
                return !StringUtils.equals(value, checkMap.get(current.getSourceCode()));
            } else {
                checkMap.put(current.getSourceCode(), value);
            }
        }
        return false;
    }

    /**
     * 查看是否重复
     *
     * @return
     */
    public boolean isRepetition(List<OcBOrderImpVO> ocBOrderList) {
        int size = ocBOrderList.size();
        ArrayList<OcBOrderImpVO> collect = ocBOrderList.stream().collect(Collectors
                .collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(changeVo -> {
                    // 根据 平台单号 和 商品条码 进行去重
                    return changeVo.getSourceCode() + "," + changeVo.getPsCSkuEcode() + "," + changeVo.getIsGift();
                }))), ArrayList::new));
        return size != collect.size();
    }


    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public List<OcBOrderImpVO> getOcBOrderList(Workbook hssfWorkbook) {
        List<OcBOrderImpVO> OcBOrderImpVos = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();

        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;

        for (Map<String, String> columnMap : execlList) {
            OcBOrderImpVO ocBOrderImpVo = new OcBOrderImpVO();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 26
                        || !"下单店铺".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"配送费用".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"买家昵称".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"平台单号".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"付款方式".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"收货人".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"收货人手机".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"收货人电话".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"收货人邮编".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"收货人省份".equals(columnMap.get(rowStr + index + cellStr + 9))
                        || !"收货人市".equals(columnMap.get(rowStr + index + cellStr + 10))
                        || !"收货人区".equals(columnMap.get(rowStr + index + cellStr + 11))
                        || !"收货人地址".equals(columnMap.get(rowStr + index + cellStr + 12))
                        // 一头牛需求优化：增加商品名称
                        || !"商品名称".equals(columnMap.get(rowStr + index + cellStr + 13))
                        || !"商品SKU编码".equals(columnMap.get(rowStr + index + cellStr + 14))
                        || !"数量".equals(columnMap.get(rowStr + index + cellStr + 15))
                        || !"成交单价".equals(columnMap.get(rowStr + index + cellStr + 16))
                        || !"下单时间".equals(columnMap.get(rowStr + index + cellStr + 17))
                        || !"支付时间".equals(columnMap.get(rowStr + index + cellStr + 18))
                        || !"平台售价".equals(columnMap.get(rowStr + index + cellStr + 19))
                        || !"买家备注".equals(columnMap.get(rowStr + index + cellStr + 20))
                        || !"卖家备注".equals(columnMap.get(rowStr + index + cellStr + 21))
                        || !"OAID".equals(columnMap.get(rowStr + index + cellStr + 22))
                        || !"是否赠品".equals(columnMap.get(rowStr + index + cellStr + 23))
                        // 一头牛需求新增字段 0912
                        || !"业务员".equals(columnMap.get(rowStr + index + cellStr + 24))
                        || !"是否明文".equals(columnMap.get(rowStr + index + cellStr + 25))
                ) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0)) || StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 3))) {
                    // 组装待配货记录
                    OcBOrderImpVos.add(OcBOrderImpVO.importCreate(index, ocBOrderImpVo, columnMap));
                }
            }
            index++;
        }

        OcBorderListEnums.changeImportListTo(OcBOrderImpVos);
        return OcBOrderImpVos;
    }

    /**
     * 获取明细表sheet数据，转换成min表对象
     */
    public List<OcBOrderItemExtend> getOcBOrderItemList(Workbook hssfWorkbook, String sign) {
        List<OcBOrderItemExtend> ocBOrderItemExtendList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();
        try {
            execlList = readExcel(1, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            OcBOrderItemExtend ocBOrderItemExtend = new OcBOrderItemExtend();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 4
                        || !"商品编码".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"数量".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"成交单价".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"头子表关联列".equals(columnMap.get(rowStr + index + cellStr + 3))) {
                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))) {
                    // 组装待配货记录
                    OcBOrderItemExtend orderItemInfo = OcBOrderItemExtend.importCreate(index, ocBOrderItemExtend, columnMap);
                    if (orderItemInfo.getPrice().compareTo(BigDecimal.ZERO) == 0) {
                        orderItemInfo.setIsGift(1);
                    } else {
                        orderItemInfo.setIsGift(0);
                    }
                    ocBOrderItemExtendList.add(orderItemInfo);
                }
            }
            index++;
        }

        return ocBOrderItemExtendList;
    }

    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private String getCellValue(HSSFCell cell) {
        String value = null;
        if (cell != null) {
            switch (cell.getCellType()) {
                case FORMULA:
                    try {
                        value = String.valueOf(cell.getNumericCellValue());
                    } catch (IllegalStateException e) {
                        value = String.valueOf(cell.getRichStringCellValue());
                    }
                    break;
                case NUMERIC:
                    value = String.valueOf(cell.getNumericCellValue());
                    break;
                case STRING:
                    value = String.valueOf(cell.getRichStringCellValue());
                    break;
            }
        }
        return value;
    }
}
