package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.LogisticsInfoQueryCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.request.LogisticsInfoQueryRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/6/15
 * description :
 */
@RestController
@Slf4j
@Api(value = "OcBOrderLogisticsInfoCtrl", tags = "物流信息查询")
public class OcBOrderLogisticsInfoCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "物流轨迹信息查询")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getLogisticsInfo", method = RequestMethod.POST)
    public JSONObject BatchReplaceHangDownGoods(HttpServletRequest request,
                                                @RequestBody JSONObject obj) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 物流轨迹信息查询入参:{}", obj);

        }
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            StringBuilder errMsg = new StringBuilder(100);
            if (user == null) {
                errMsg.append("请先登录;");
            }
            String tableName = null;
            if (obj == null || obj.getLong("id") == null) {
                errMsg.append("参数错误;");
            } else {
                tableName = obj.getString("tableName");
                if (StringUtils.isEmpty(tableName)) {
                    errMsg.append("参数错误;");
                }
            }

            if (errMsg.length() > 0) {
                vh.setMessage(errMsg.toString());
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, tableName);

            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }
            LogisticsInfoQueryCmd logisticsInfoQueryCmd = (LogisticsInfoQueryCmd) (ReferenceUtil.refer(
                    ApplicationContextHandle.getApplicationContext(), LogisticsInfoQueryCmd.class.getName(),
                    "oms-fi", "1.0"));
            vh = logisticsInfoQueryCmd.queryLogisticsInfo(JSON.toJavaObject(obj, LogisticsInfoQueryRequest.class));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }
}
