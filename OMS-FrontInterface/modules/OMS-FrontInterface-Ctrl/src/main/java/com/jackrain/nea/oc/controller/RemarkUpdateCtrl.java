package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.RemarkCheckCmd;
import com.jackrain.nea.oc.oms.api.RemarkUpdateCmd;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @author: 李杰
 * @since: 2019/3/11
 * create at : 2019/3/11 14:59
 */
@Api(value = "RemarkUpdateCtrl", tags = "修改备注")
@Slf4j
@RestController
public class RemarkUpdateCtrl {

    @Autowired
    private RemarkUpdateCmd remarkUpdateCmd;

    @Autowired
    private RemarkCheckCmd remarkCheckCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    private static NumberFormat nf = NumberFormat.getInstance();

    @ApiOperation(value = "修改卖家备注")
    @RequestMapping(value = "/api/cs/oc/oms/v1/remarkUpdate", method = RequestMethod.POST)
    public JSONObject remarkUpdate(HttpServletRequest request, @RequestBody JSONObject obj) {
//        RemarkUpdateCtrl saveBil = new RemarkUpdateCtrl();
        ValueHolder vh = new ValueHolder();
//        User user = saveBil.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            vh = remarkUpdateCmd.updateRemark(obj, user);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolder();
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }

    }
    @ApiOperation(value = "修改内部备注")
    @RequestMapping(value = "/api/cs/oc/oms/v1/BacthUpdateInsideRemark", method = RequestMethod.POST)
    public JSONObject bacthUpdateInsideRemark(HttpServletRequest request, @RequestBody JSONObject obj) {
//        RemarkUpdateCtrl saveBil = new RemarkUpdateCtrl();
        ValueHolder vh = new ValueHolder();
//        User user = saveBil.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            vh = remarkUpdateCmd.bacthUpdateInsideRemark(obj, user);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolder();
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }

    }

    @ApiOperation(value = "修改卖家备注校验")
    @RequestMapping(value = "api/cs/oc/oms/v1/remarkUpdateCheck", method = RequestMethod.POST)
    public JSONObject remarkUpdateCheck(HttpServletRequest request, @Param("ids") String ids) {
        RemarkUpdateCtrl saveBil = new RemarkUpdateCtrl();
        JSONObject object = new JSONObject();
        //获取当前登陆用户
//        User user = saveBil.getRootUser();
        try {
            object = remarkCheckCmd.check(ids);
            return object;
        } catch (NDSException e) {
            return object;

        }

    }

    @ApiOperation(value = "修改备注")
    @RequestMapping(value = "/api/cs/oc/oms/v1/reRemarkUpdate", method = RequestMethod.POST)
    public JSONObject reRemarkUpdate(HttpServletRequest request, @RequestBody JSONObject obj) {
//        RemarkUpdateCtrl saveBil = new RemarkUpdateCtrl();
        ValueHolder vh = new ValueHolder();
        //获取当前登陆用户
//        User user = saveBil.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            vh = remarkUpdateCmd.reUpdateRemark(obj, user);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolder();
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    @ApiOperation(value = "修改卖家备注")
    @RequestMapping(value = "/api/cs/oc/oms/v1/reUpdateSellerRemark", method = RequestMethod.POST)
    public JSONObject reUpdateSellerRemark(HttpServletRequest request, @RequestBody JSONObject obj) {
//        RemarkUpdateCtrl saveBil = new RemarkUpdateCtrl();
        ValueHolder vh = new ValueHolder();//获取当前登陆用户
//        User user = saveBil.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            vh = remarkUpdateCmd.reUpdateSellerRemark(obj, user);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolder();
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    /**
     * 批量导入备注
     *
     * @param request 请求参
     * @param file    页面传参
     * @return
     */
    @ApiOperation(value = "批量导入备注修改")
    @RequestMapping(value = "api/cs/oc/oms/v1/batchImport", method = RequestMethod.POST)
    public JSONObject batchImport(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file, @RequestParam("cover") Boolean cover) {
        log.info(this.getClass().getName() + "批量修改备注入参：" + file);
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody =new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单备注导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        return asyncImport(asyncTaskBody,user,file,cover);

    }

    public JSONObject asyncImport(AsyncTaskBody asyncTaskBody,User user,MultipartFile file,Boolean cover){
        long beginTime = System.currentTimeMillis();
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            try {
                if (file == null) {
                    throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
                }
                InputStream inputStream = null;
                Map<String, InputStream> inputStreamMap = new HashMap<>();
                try {
                    inputStream = file.getInputStream();
                    inputStreamMap.put("inputStream", inputStream);
                    log.info(this.getClass().getName() + " 转化为流后的数据为：" + inputStream);
                } catch (Exception e) {
                    log.info( " 文件转换流失败 {}",e.getMessage());
                    throw new NDSException(Resources.getMessage("文件转换流失败!", user.getLocale()));
                }

                Workbook hssfWorkbook = null;
                try {
                    hssfWorkbook = new XSSFWorkbook(inputStream);
                } catch (Exception ex) {
                    log.error(" 1处理订单备注导入Excel数据失败 ", ex);
                    try {
                        hssfWorkbook = new HSSFWorkbook(inputStream);
                    } catch (Exception e) {
                        log.error(this.getClass().getName() + " 2处理订单修改备注导入Excel数据失败 ", e);
                        throw new NDSException(Resources.getMessage("处理订单修改备注导入Excel数据失败!", user.getLocale()));

                    }
                }

                //处理主表数据--->转换成对象
                log.info("=========>>>>>>[llf]处理主表数据--->转换成对象！");
                JSONObject object = getOcBOrderList(hssfWorkbook, user);
                Integer code = object.getInteger("code");
                if (code != null) {
                    throw new NDSException(Resources.getMessage(object.getString("message"), user.getLocale()));
                }
                List<OcBOrder> ocBOrderList = (List<OcBOrder>) object.get("ocBOrderList");
                log.info(this.getClass().getName() + "导入备注对象是" + JSONObject.toJSONString(ocBOrderList));
                JSONArray sourceCode = object.getJSONArray("sourceCode");
                if (CollectionUtils.isEmpty(ocBOrderList)) {
                    throw new NDSException(Resources.getMessage("订单管理导入模板主表数据不能为空!"));
                }
                long endTime = System.currentTimeMillis();
                log.info("批量修改数据转换对象耗时:" + (endTime - beginTime));
                long ExecutionLogicBeginTime = System.currentTimeMillis();
                ValueHolderV14 valueHolder = remarkUpdateCmd.batchImport(ocBOrderList, sourceCode, cover, user);
                retMap.put("code", valueHolder.getCode());
                retMap.put("data", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
                long ExecutionLogicEndTime = System.currentTimeMillis();
                log.info("执行业务逻辑需要的时间:" + (ExecutionLogicEndTime - ExecutionLogicBeginTime));

            }catch (Exception e){
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + e.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("零售发货单备注导入任务开始！详情请在我的任务查看"));
        return holderV14.toJSONObject();
    }

    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public JSONObject getOcBOrderList(Workbook hssfWorkbook, User user) {
        JSONObject jsonObject = new JSONObject(); // 用于存放返回值
        List<OcBOrder> ocBOrderList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();

        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;
        JSONArray array = new JSONArray(); // 用来存放平台单号
        for (Map<String, String> columnMap : execlList) {
            OcBOrder ocBOrder = new OcBOrder();

            if (index == 0) {
                // 校验excel字段
                if (!"平台单号".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"旗帜".equals(columnMap.get(rowStr + index + cellStr + 2))
                ) {
                    jsonObject.put("code", ResultCode.FAIL);
                    jsonObject.put("message", "导入备注格式不正确，请参考模板");
                    return jsonObject;
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))
                        && StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 1))
                ) {
                    array.add(columnMap.get(rowStr + index + cellStr + 0));
                    ocBOrder.setSourceCode(columnMap.get(rowStr + index + cellStr + 0));
                    ocBOrder.setSellerMemo(columnMap.get(rowStr + index + cellStr + 1));
                    ocBOrder.setOrderFlag(columnMap.get(rowStr + index + cellStr + 2));
                    ocBOrder.setModifieddate(new Date());
                    ocBOrder.setModifiername(user.getName());
                    ocBOrder.setModifierename(user.getEname());
                    // 组装待配货记录
                    ocBOrderList.add(ocBOrder);
                }
            }
            index++;

        }
        jsonObject.put("ocBOrderList", ocBOrderList);
        jsonObject.put("sourceCode", array);

        return jsonObject;
    }

    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }
  /*  private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("admin");
        user.setEname("Pokemon-mapper");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }*/
}
