package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OmsMiniPlatformDeliveryCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/9/28
 */

@Api(value = "OmsMiniPlatformDeliveryCtrl", description = "")
@Slf4j
@RestController
@RequestMapping("/api/cs/oc/oms/v1")
public class OmsMiniPlatformDeliveryCtrl {

    @DubboReference(version = "1.0", group = "oms-fi")
    private OmsMiniPlatformDeliveryCmd omsMiniPlatformDeliveryCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "appointMiniPlatDelivery")
    @RequestMapping(value = "/appointMiniPlatDelivery", method = RequestMethod.POST)
    public ValueHolderV14 exchange2Refund(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = omsMiniPlatformDeliveryCmd.appointDelivery(obj.getLong("ID"));
            return vh;
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh;
        }
    }

}
