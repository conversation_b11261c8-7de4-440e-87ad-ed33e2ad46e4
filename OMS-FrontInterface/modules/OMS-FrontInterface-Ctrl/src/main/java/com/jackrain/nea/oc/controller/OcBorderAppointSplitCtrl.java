package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBOrderAppointSplitCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: hulinyang
 * @since: 2020/3/11
 * create at : 2020/3/11 18:30
 */
@Api(value = "OcBorderAppointSplitCtrl", tags = "指定商品拆单接口")
@Slf4j
@RestController
public class OcBorderAppointSplitCtrl {

    @Autowired
    private OcBOrderAppointSplitCmd ocBOrderAppointSplitCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 手动拆单信息保存接口
     *
     * @param request 请求对象
     * @param param   请求参数
     * @return json对象
     */
    @ApiOperation(value = "指定拆单信息保存接口")
    @PostMapping(value = "/api/cs/oc/oms/v1/saveAppointSplitOrderInfo")
    public JSONObject saveAppointSplitOrderInfo(HttpServletRequest request, @RequestBody String param) {

        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
            if (usrPem == null) {
                holderV14 = new ValueHolderV14<>();
                holderV14.setMessage("未获取到用户权限");
                holderV14.setCode(ResultCode.FAIL);
                return holderV14.toJSONObject();
            }
            JSONObject params = JSON.parseObject(param);
            holderV14 = ocBOrderAppointSplitCmd.saveAppointSplitOrderInfo(params, user, usrPem);
            return holderV14.toJSONObject();
        } catch (Exception ex) {
            holderV14.setCode(-1);
            holderV14.setMessage("订单指定拆单信息保存异常" + ex.getMessage());
            return holderV14.toJSONObject();
        }
    }

}