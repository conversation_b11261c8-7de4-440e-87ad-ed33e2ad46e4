package com.jackrain.nea.oc.controller.ac;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.ac.*;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import com.jackrain.nea.web.query.QueryUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> 陈俊明
 * @since : 2019-03-25
 * create at : 2019-03-25 17:18
 */

@RestController
@Slf4j
@Api(value = "OmsPayableAdjustmentController", description = "丢件单")
public class OmsPayableAdjustmentController {

    @Autowired
    private OmsPayableAdjustmentImportCmd payableAdjustmentImportCmd;
    @Autowired
    private OmsPayableAdjustmentExportCmd payableAdjustmentExportCmd;
    @Autowired
    OmsAcFCompensationTypeCmd acFCompensationTypeCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OmsPayableAdjustmentConfirmResponsiblePartyCmd omsPayableAdjustmentConfirmResponsiblePartyCmd;

    @Autowired
    private OmsPayableAdjustmentSaveCmd omsPayableAdjustmentSaveCmd;

    @Autowired
    private OmsPayableAdjustmentAuditCmd omsPayableAdjustmentAuditCmd;

    @Autowired
    private OmsPayableAdjustmentFiAuditCmd omsPayableAdjustmentFiAuditCmd;

    @Autowired
    private OmsPayableAdjustmentBizAuditCmd omsPayableAdjustmentBizAuditCmd;

    @Autowired
    private OmsPayableAdjustmentCancelAuditCmd omsPayableAdjustmentCancelAuditCmd;

    @Autowired
    private OmsPayableAdjustmentDelCmd omsPayableAdjustmentDelCmd;

    @Autowired
    private OmsPayableAdjustmentVoidCmd omsPayableAdjustmentVoidCmd;

    @Autowired
    private OmsPayableGetCompensateCmd omsPayableGetCompensateCmd;

    @Autowired
    private OmsPayableAdjustmentSelectCmd omsPayableAdjustmentSelectCmd;

    @Autowired
    private OmsPayableAdjustmentSelectListCmd omsPayableAdjustmentSelectListCmd;

    @ApiOperation(value = "应付款调整单确认责任方")
    @RequestMapping(path = "/api/cs/oc/oms/confirmResponsibleParty", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder confirmResponsibleParty(HttpServletRequest request,
                                               @RequestParam(value = "param", required = true)
                                                       String param) {
//        User user = (UserImpl) request.getSession().getAttribute("user");
//        User user = getRootUser();
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = omsPayableAdjustmentConfirmResponsiblePartyCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "应付款调整单添加保存")
    @RequestMapping(path = "/api/cs/oc/oms/savePayableAdjustment", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 savePayableAdjustment(HttpServletRequest request,
                                                @RequestParam(value = "param", required = true)
                                                        String param) {
//        User user = (UserImpl) request.getSession().getAttribute("user");
//        User user = getRootUser();
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolderV14 result = omsPayableAdjustmentSaveCmd.executeSave(querySession);
        return result;
    }

    @ApiOperation(value = "应付款调整单客审")
    @RequestMapping(path = "/api/cs/oc/oms/auditPayableAdjustment", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder auditPayableAdjustment(HttpServletRequest request,
                                              @RequestParam(value = "param", required = true)
                                                      String param) {
//        User user = (UserImpl) request.getSession().getAttribute("user");
//        User user = getRootUser();
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = omsPayableAdjustmentAuditCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "应付款调整单财审")
    @RequestMapping(path = "/api/cs/oc/oms/fiAuditPayableAdjustment", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder fiAuditPayableAdjustment(HttpServletRequest request,
                                                @RequestParam(value = "param", required = true)
                                                        String param) {
//        User user = (UserImpl) request.getSession().getAttribute("user");
//        User user = getRootUser();
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = omsPayableAdjustmentFiAuditCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "应付款调整单业审")
    @RequestMapping(path = "/api/cs/oc/oms/bizAuditPayableAdjustment", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder bizAuditPayableAdjustment(HttpServletRequest request,
                                                 @RequestParam(value = "param", required = true)
                                                         String param) {
//        User user = (UserImpl) request.getSession().getAttribute("user");
//        User user = getRootUser();
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = omsPayableAdjustmentBizAuditCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "应付款调整单反审核")
    @RequestMapping(path = "/api/cs/oc/oms/cancelAuditPayableAdjustment", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder cancelAuditPayableAdjustment(HttpServletRequest request,
                                                    @RequestParam(value = "param", required = true)
                                                            String param) {
//        User user = (UserImpl) request.getSession().getAttribute("user");
//        User user = getRootUser();
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = omsPayableAdjustmentCancelAuditCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "应付款调整单行删除")
    @RequestMapping(path = "/api/cs/oc/oms/delPayableAdjustment", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder delPayableAdjustment(HttpServletRequest request,
                                            @RequestParam(value = "param", required = true)
                                                    String param) {
//        User user = (UserImpl) request.getSession().getAttribute("user");
//        User user = getRootUser();
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = omsPayableAdjustmentDelCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "应付款调整单作废")
    @RequestMapping(path = "/api/cs/oc/oms/voidPayableAdjustment", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidPayableAdjustment(HttpServletRequest request,
                                             @RequestParam(value = "param", required = true)
                                                     String param) {
//        User user = (UserImpl) request.getSession().getAttribute("user");
//        User user = getRootUser();
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = omsPayableAdjustmentVoidCmd.execute(querySession);
        return result;
    }

    @ApiOperation(value = "应付款调整单获取丢单赔付策略接口")
    @RequestMapping(path = "/api/cs/oc/oms/getCompensate", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject getCompensate(HttpServletRequest request,
                                    @RequestParam(value = "param", required = true)
                                            String param) {
//        User user = (UserImpl) request.getSession().getAttribute("user");
//        User user = getRootUser();
//        QuerySessionImpl querySession = new QuerySessionImpl(user);
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = omsPayableGetCompensateCmd.execute(querySession);
        return JSONObject.parseObject(JSONObject.toJSONString(result.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    @ApiOperation(value = "应付款调整单双击列表查看单据信息接口")
    @RequestMapping(path = "/api/cs/oc/oms/getPayableAdjustment", method = {RequestMethod.POST})
    public JSONObject getPayableAdjustment(HttpServletRequest request,
                                           @RequestParam(value = "param", required = true)
                                                   String param) {
//        QuerySessionImpl querySession = new QuerySessionImpl();
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder vh = omsPayableAdjustmentSelectCmd.execute(querySession);

        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    @ApiOperation(value = "应付款调整单查询列表接口")
    @RequestMapping(path = "/api/cs/oc/oms/getPayableAdjustmentList", method = {RequestMethod.POST})
    public JSONObject getPayableAdjustmentList(HttpServletRequest request,
                                               @RequestParam(value = "param", required = true)
                                                       String param) {
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        QuerySession querySession = QueryUtils.createQuerySession(user);
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder vh = omsPayableAdjustmentSelectListCmd.execute(querySession);

        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

    @ApiOperation(value = "应付款调整单下载导入模板接口")
    @RequestMapping(path = "/api/cs/oc/oms/downloadPayableAdjustment", method = {RequestMethod.POST})
    public ValueHolderV14 downloadPayableAdjustment() {
        return payableAdjustmentImportCmd.downloadTemp();
    }

    @ApiOperation(value = "应付款调整单导入接口")
    @RequestMapping(path = "/api/cs/oc/oms/importPayableAdjustment", method = RequestMethod.POST)
    public ValueHolderV14 importPayableAdjustment(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        return payableAdjustmentImportCmd.importPayableAdjustment(file, user);
    }

    @ApiOperation(value = "列表导出")
    @RequestMapping(path = "/api/cs/oc/oms/exportPayableAdjustment", method = RequestMethod.POST)
    public ValueHolderV14 getOrderList(HttpServletRequest request, @RequestBody JSONObject obj) {
//        UserImpl user = (UserImpl) request.getSession().getAttribute("user");
        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
        return payableAdjustmentExportCmd.exportPayableAdjustment(obj, user);

    }

    @ApiOperation(value = "赔付原因查询")
    @RequestMapping(path = "/api/cs/oc/oms/getCompensationReason", method = RequestMethod.POST)
    public ValueHolderV14 queryCompensationReason(@RequestParam(value = "id", required = true) Integer id) {

        return acFCompensationTypeCmd.queryAcFCompensationReasonById(id);

    }


    /**
     * 获取 测试用户
     */
    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("root");
        user.setEname("系统管理员");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }
}
