package com.jackrain.nea.oc.controller.o2o;

import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.CloudMqResendCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * Description： 补发消息
 * Author: RESET
 * Date: Created in 2020/8/16 17:51
 * Modified By:
 */
@Api(value = "CloudMqResendController", tags = "手工发送消息")
@Slf4j
@RestController
public class CloudMqResendController {

    @Autowired
    private CloudMqResendCmd cloudMqResendCmd;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "销退单据发送POS")
    @RequestMapping(value = "/api/cs/oc/oms/v1/returnOrder/mq2pos/retry", method = RequestMethod.GET)
    public ValueHolderV14 returnOrder2PosMqRetry(HttpServletRequest request, @RequestParam(value = "returnOrderId", required = true) Long returnOrderId) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        ValueHolderV14 r = cloudMqResendCmd.returnOrder2PosMqRetry(returnOrderId, user);
        return r;
    }

}
