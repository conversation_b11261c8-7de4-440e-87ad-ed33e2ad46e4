package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.*;
import com.jackrain.nea.oc.oms.extmodel.ExtOcBInvoiceNotice;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * @author: chenxiulou
 * @description: 开票通知
 * @since: 2019-07-20
 * create at : 2019-07-20 17:29
 */
@Api(value = "OcBInvoiceNoticeCtrl", tags = "开票通知")
@Slf4j
@RestController
public class OcBInvoiceNoticeCtrl {

    public static final String CELL_STR = "cell_";
    public static final String ROW_STR = "row_";

    @Autowired
    private InvoiceTableQueryCmd invoiceTableQueryCmd;
    @Autowired
    private InvoiceNoticeImportCmd invoiceNoticeImportCmd;
    @Autowired
    private InvoiceNoticeExportCmd invoiceNoticeExportCmd;
    @Autowired
    private InvoiceNoticeAutomaticCmd invoiceNoticeAutomaticCmd;
    @Autowired
    private InvoiceNoticeMergeCmd invoiceNoticeMergeCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private InvoicNoticeSaveCmd invoicNoticeSaveCmd;

    @Autowired
    private InvoicNoticeVoidCmd invoicNoticeVoidCmd;

    @Autowired
    private InvoicNoticeEstatusCmd invoicNoticeEstatusCmd;


    @ApiOperation(value = "开票通知新增")
    @RequestMapping(path = "/api/cs/oc/oms/v1/saveInvoiceNotice", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder saveInvoiceNotice(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        // 3.调用服务
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = invoicNoticeSaveCmd.saveInvoiceNotice(querySession);
        return result;
    }

    @ApiOperation(value = "开票通知作废")
    @RequestMapping(path = "/api/cs/oc/oms/v1/voidInvoiceNotice", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder voidInvoiceNotice(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        // 3.调用服务
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = invoicNoticeVoidCmd.voidInvoiceNotice(querySession);
        return result;
    }

    @ApiOperation(value = "开票通知审核")
    @RequestMapping(path = "/api/cs/oc/oms/v1/auditInvoiceNotice", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder auditInvoiceNotice(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        // 3.调用服务
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = invoicNoticeSaveCmd.auditInvoiceNotice(querySession);
        return result;
    }

    @ApiOperation(value = "开票通知反审核")
    @RequestMapping(path = "/api/cs/oc/oms/v1/unAuditInvoiceNotice", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder unAuditInvoiceNotice(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        // 3.调用服务
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = invoicNoticeEstatusCmd.changeEstatusByUnAudit(querySession);
        return result;
    }

    @ApiOperation(value = "开票通知暂缓开票")
    @RequestMapping(path = "/api/cs/oc/oms/v1/stopInvoiceNotice", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder stopInvoiceNotice(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        // 3.调用服务
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = invoicNoticeEstatusCmd.changeEstatusByStop(querySession);
        return result;
    }

    @ApiOperation(value = "开票通知取消暂缓开票")
    @RequestMapping(path = "/api/cs/oc/oms/v1/startInvoiceNotice", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder startInvoiceNotice(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        // 3.调用服务
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = invoicNoticeEstatusCmd.changeEstatusByStart(querySession);
        return result;
    }

    @ApiOperation(value = "开票通知确认开票")
    @RequestMapping(path = "/api/cs/oc/oms/v1/confirmInvoiceNotice", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolder confirmInvoiceNotice(HttpServletRequest request, @RequestParam(value = "param", required = true) String param) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        QuerySessionImpl querySession = new QuerySessionImpl(user);
        // 3.调用服务
        DefaultWebEvent event = new DefaultWebEvent("test", request, false);
        event.put("param", JSON.parseObject(param));
        querySession.setEvent(event);
        ValueHolder result = invoicNoticeSaveCmd.confirmInvoiceNotice(querySession);
        return result;
    }

    @ApiOperation(value = "获取开票店铺相关信息")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getInvoiceShop", method = RequestMethod.POST)
    public JSONObject queryByShop(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = invoiceTableQueryCmd.queryByShop(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "获取开票商品相关信息")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getInvoiceItemTable", method = RequestMethod.POST)
    public JSONObject queryInvoiceItem(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = invoiceTableQueryCmd.queryInvoiceItem(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "获取开票通知所有信息")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getInvoiceNoticeInfo", method = RequestMethod.POST)
    public JSONObject queryInvoiceNoticeInfo(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = invoiceTableQueryCmd.queryInvoiceNoticeInfo(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "列表查询开票通知")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getInvoiceNoticetList", method = RequestMethod.POST)
    public JSONObject selectInvoiceNoticeList(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = invoiceTableQueryCmd.selectInvoiceNoticeList(obj);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "开票通知下载导入模板")
    @RequestMapping(path = "/api/cs/oc/oms/v1/downloadInvoiceNotice", method = {RequestMethod.POST})
    public ValueHolderV14 downloadInvoiceNotice() {
        return invoiceNoticeImportCmd.downloadTemp();
    }

    @ApiOperation(value = "开票通知导入接口")
    @RequestMapping(path = "/api/cs/oc/oms/v1/importInvoiceNotice", method = RequestMethod.POST)
    public ValueHolderV14 importInvoiceNotice(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//                User user = SystemUserResource.getRootUser();
        //1.传入数据校验
        ValueHolderV14 vh = new ValueHolderV14();
        if (file == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不能为空!");
            return vh;
        }
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件转换成流失败!");
            return vh;
        }
        //2.解析Excel
        Workbook workbook = null;
        try {
            workbook = getWorkbookForImportFile(inputStream, file);
        } catch (IOException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("文件解析Excel失败!");
            return vh;
        }

        List<Map<String, String>> execlList = null;
        try {
            execlList = OcBReturnOrderImportCtrl.readExcel(0, workbook);
            if (execlList == null) {
                throw new NDSException("读取Eexel文件失败");
            }
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(e.getMessage());
            return vh;
        }
        //3.生成开票通知信息集合
        int index = 0;
        List<ExtOcBInvoiceNotice> invoiceNoticeList = Lists.newArrayList();
        for (Map<String, String> columnMap : execlList) {
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 6
                        || !"单据编号".equals(columnMap.get(ROW_STR + index + CELL_STR + 0))
                        || !"发票号".equals(columnMap.get(ROW_STR + index + CELL_STR + 1))
                        || !"开票日期".equals(columnMap.get(ROW_STR + index + CELL_STR + 2))
                        || !"开票人".equals(columnMap.get(ROW_STR + index + CELL_STR + 3))
                        || !"快递公司".equals(columnMap.get(ROW_STR + index + CELL_STR + 4))
                        || !"快递单号".equals(columnMap.get(ROW_STR + index + CELL_STR + 5))) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("导入文件与模板不符,请重新下载模板后导入");
                    return vh;
                }
            } else {
                // 组装待配货记录
                ExtOcBInvoiceNotice importInvoiceNotice = getImportInvoiceModel(index, columnMap);
                invoiceNoticeList.add(importInvoiceNotice);
            }
            index++;
        }
        return invoiceNoticeImportCmd.importInvoiceNotice(invoiceNoticeList, user);
    }

    @ApiOperation(value = "列表导出")
    @RequestMapping(path = "/api/cs/oc/oms/v1/exportInvoiceNotice", method = RequestMethod.POST)
    public ValueHolderV14 getOrderList(HttpServletRequest request, @RequestBody JSONObject obj) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        return invoiceNoticeExportCmd.exportInvoiceNotice(obj, user);
    }

    /**
     * @param inputStream
     * @param file
     * @return org.apache.poi.ss.usermodel.Workbook
     * @Description 分版本处理Excel数据
     * <AUTHOR>
     * @date 2019/7/27 15:05
     */
    private Workbook getWorkbookForImportFile(InputStream inputStream, MultipartFile file) throws IOException {
        Workbook workbook = null;
        String fileName = file.getName();
        if (fileName.toLowerCase().endsWith("xls")) {
            workbook = new HSSFWorkbook(inputStream);
        } else {
            workbook = new XSSFWorkbook(inputStream);
        }
        return workbook;
    }

    private ExtOcBInvoiceNotice getImportInvoiceModel(int index, Map<String, String> columnMap) {
        ExtOcBInvoiceNotice extOcBInvoiceNotice = new ExtOcBInvoiceNotice();
        extOcBInvoiceNotice.setBillNo(columnMap.get(ROW_STR + index + CELL_STR + 0));
//        extOcBInvoiceNotice.setTaxNo(columnMap.get(ROW_STR + index + CELL_STR + 1));
        extOcBInvoiceNotice.setInvoiceNo(columnMap.get(ROW_STR + index + CELL_STR + 1));
        extOcBInvoiceNotice.setInvoiceTimeStr(columnMap.get(ROW_STR + index + CELL_STR + 2));
        extOcBInvoiceNotice.setInvoiceEname(columnMap.get(ROW_STR + index + CELL_STR + 3));
        extOcBInvoiceNotice.setCpCLogisticsEname(columnMap.get(ROW_STR + index + CELL_STR + 4));
        extOcBInvoiceNotice.setLogisticsNo(columnMap.get(ROW_STR + index + CELL_STR + 5));
        return extOcBInvoiceNotice;
    }

    @ApiOperation(value = "自动开票")
    @RequestMapping(path = "/api/cs/oc/oms/v1/createInvoiceNotice", method = RequestMethod.POST)
    public ValueHolderV14 createInvoiceNotice(HttpServletRequest request, @RequestBody JSONObject obj) {
        User user = SystemUserResource.getRootUser();
        return invoiceNoticeAutomaticCmd.createInvoiceNotice(obj.getLong("ocBOrderId"), user);
    }

    @ApiOperation(value = "合并开票")
    @RequestMapping(path = "/api/cs/oc/oms/v1/mergeInvoiceNotice", method = RequestMethod.POST)
    public ValueHolderV14 mergeInvoiceNotice(HttpServletRequest request, @RequestBody JSONObject obj) {
        User user = SystemUserResource.getRootUser();
        return invoiceNoticeMergeCmd.mergeInvoiceNotice(user);
    }
}
