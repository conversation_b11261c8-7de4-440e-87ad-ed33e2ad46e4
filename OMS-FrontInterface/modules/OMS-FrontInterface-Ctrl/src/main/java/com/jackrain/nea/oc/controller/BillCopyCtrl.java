package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.BillCopyCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Api(value = "BillCopyCtrl", tags = "单据复制按钮")
@Slf4j
@RestController
public class BillCopyCtrl {
    @Autowired
    private BillCopyCmd billCopyCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * @param request 请求
     * @param obj     参数
     * @param
     * @return return
     */

    @ApiOperation(value = "单据复制、丢单复制")
    @RequestMapping(value = "/api/cs/oc/oms/v1/billCopy", method = RequestMethod.POST)
    public JSONObject billCopy(HttpServletRequest request,
                               @RequestBody JSONObject obj) {
        ValueHolder vh;
        //获取当前登陆用户
//        User user = SystemUserResource.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        try {
            //记录日志信息
            if (log.isDebugEnabled()) {
                log.debug("start BillCopyCtrl.billcopy.ReceiveParams=" + obj.toJSONString());
            }
            vh = billCopyCmd.billCopy(obj, user);
            JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //记录日志信息。Finish 标记结束
//            if (log.isDebugEnabled()) {
//                log.debug("Finish BillCopyCtrl.billcopy. Return Result=" + vh.toJSONObject());
//            }
            return result;
        } catch (NDSException e) {
            vh = new ValueHolder();
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }

    }


}

