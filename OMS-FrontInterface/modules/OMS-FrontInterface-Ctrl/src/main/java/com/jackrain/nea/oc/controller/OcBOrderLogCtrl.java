package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName OcBOrderLogCtrl
 * @Description 订单操作日志
 * <AUTHOR>
 * @Date 2024/5/24 18:02
 * @Version 1.0
 */
@RestController
@Slf4j
public class OcBOrderLogCtrl {

    @Resource
    private OmsOrderLogService omsOrderLogService;

    @RequestMapping(value = "/api/cs/oc/oms/v1/orderLog/get", method = RequestMethod.POST)
    public JSONObject getLogByOrderId(HttpServletRequest request,
                                      @RequestParam(value = "objid") Long objid, @RequestParam(value = "searchdata") String searchdata) {
        return omsOrderLogService.getLogByOrderId(objid, searchdata);
    }
}
