package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.RefundPaymentCmd;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendPaymentEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @author: 江家雷
 * @create: 2020-08-200 17:10
 **/
@RestController
@RequestMapping("/api/cs/oc/oms/v1/")
@Api(value = "OcBReturnAfSendImportController", tags = "订单管理")
@Slf4j
public class OcBReturnAfSendImportController {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private RefundPaymentCmd refundPaymentCmd;
    @Autowired
    private AsyncTaskManager asyncTaskManager;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    /**
     * 下载模板
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "导入打款结果模板")
    @RequestMapping(path = "downloadUpdateOcBReturnAfSendTemp", method = RequestMethod.POST)
    public ValueHolderV14 downloadTemp()
            throws Exception {

        //处理返回数据
        ValueHolderV14 holderV14 = refundPaymentCmd.downloadTemp();
        return holderV14;
    }

    @ApiOperation(value = "批量更新打款状态")
    @RequestMapping(path = "batchUpdateOcBReturnAfSend", method = RequestMethod.POST)
    public ValueHolderV14 updateOcBReturnAfSend(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody =new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("额外退款单打款结果导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        return asyncImport(asyncTaskBody,user,file);
    }

    private ValueHolderV14 asyncImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        log.info(" asyncImport开始 {}",JSON.toJSONString(asyncTaskBody));
        commonTaskExecutor.submit(() -> {
            try {
                if (file == null) {
                    throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
                }
                InputStream inputStream = file.getInputStream();
                Workbook workbook = WorkbookFactory.create(inputStream);
                List<OcBReturnAfSend> list = readExcel(workbook);
                if (CollectionUtils.isEmpty(list)) {
                    throw new NDSException(Resources.getMessage("文件中没有数据!"));
                }

                // 导入数据
                ValueHolderV14 v14 = refundPaymentCmd.batchUpdateOcBReturnAfSend(list, user);
                log.info(" asyncImport结束 {}",JSON.toJSONString(asyncTaskBody));
                retMap.put("code", v14.getCode());
                retMap.put("data", v14.getData());
                retMap.put("message", v14.getMessage());
                //任务完成
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }  catch (Exception e) {
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + e.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("额外退款单导入打款结果任务开始！详情请在我的任务查看"));
        return holderV14;
    }

    /**
     * @param workbook
     * @return
     */
    public List<OcBReturnAfSend> readExcel(Workbook workbook) {
        List<OcBReturnAfSend> list = Lists.newArrayList();
        Sheet sheet = workbook.getSheetAt(0);
        if (Objects.isNull(sheet)) {
            return null;
        }

        for (int rowNum = 1; rowNum <= sheet.getLastRowNum(); rowNum++) {
            Row row = sheet.getRow(rowNum);
            OcBReturnAfSend ocBReturnAfSend = new OcBReturnAfSend();
            ocBReturnAfSend.setBillNo(getCellValue(row.getCell(0)));
            String paymentStatus = getCellValue(row.getCell(1));
            if (paymentStatus.contains("处理成功")) {
                // 打款状态:0 未打款 1 打款中 2 打款成功 3 打款失败
                ocBReturnAfSend.setPaymentStatus(ReturnAfSendPaymentEnum.PAYMENT_SUCCESS.toInteger());
                ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
                ocBReturnAfSend.setPaymentFailReason("");
            }
            if (paymentStatus.contains("校验失败")) {
                // 打款状态:0 未打款 1 打款中 2 打款成功 3 打款失败
                ocBReturnAfSend.setPaymentStatus(ReturnAfSendPaymentEnum.PAYMENT_FAIL.toInteger());
                ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.NOREFUND.getVal());
                ocBReturnAfSend.setPaymentFailReason(getCellValue(row.getCell(2)));
            }
            list.add(ocBReturnAfSend);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        if (Objects.isNull(cell)) {
            return "";
        }
        NumberFormat nf = NumberFormat.getInstance();
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case 0:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case 2:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }

}
