package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.BusinessSystem;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.AgainOccupyStockCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderAppointLogisticsCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderBeforeCheckCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderCheckJitxOtherMergeOrderCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderExportCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderImportCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderMarkingCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderOffCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderPayableAdjustmentCreateCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderReRunTobeconfirmedCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderTheAuditCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderTheAuditStopCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderTradeCompleteCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderUpdateBusinessTypeCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderUpdatePreDeliveryTimeCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderUpdateStatusCmd;
import com.jackrain.nea.oc.oms.api.OmsOrderLockStockAndReOccupyStockCmd;
import com.jackrain.nea.oc.oms.api.OmsOrderVopResetShipCmd;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.request.OrderICheckStopRequest;
import com.jackrain.nea.oc.oms.model.request.OrderMarkingRequest;
import com.jackrain.nea.oc.oms.model.result.OrderHoldResult;
import com.jackrain.nea.oc.oms.vo.OcBOrderRemarkImpVO;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Api(value = "OcBOrderCtrl", tags = "反审核、取消订单、生成丢件单、校验是否存在其他可合并JITX订单")
@Slf4j
@RestController
public class OcBOrderCtrl {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    @Autowired
    private OcBOrderOffCmd ocBOrderOffCmd;

    @Autowired
    private OcBOrderTheAuditCmd ocBOrderTheAuditCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OcBOrderExportCmd ocBOrderExportCmd;

    @Autowired
    private OcBOrderImportCmd ocBOrderImportCmd;

    @Autowired
    private OcBOrderUpdateStatusCmd ocBOrderUpdateStatusCmd;
    @Autowired
    private OcBOrderMarkingCmd ocBOrderMarkingCmd;

    @Autowired
    private OcBOrderCheckJitxOtherMergeOrderCmd ocBOrderCheckJitxOtherMergeOrderCmd;
    @Autowired
    private OcBOrderPayableAdjustmentCreateCmd ocBOrderPayableAdjustmentCreateCmd;

    @Autowired
    private OmsOrderLockStockAndReOccupyStockCmd omsOrderLockStockAndReOccupyStockCmd;

    @Autowired
    private OmsOrderVopResetShipCmd omsOrderVopResetShipCmd;

    @Autowired
    private AgainOccupyStockCmd againOccupyStockCmd;

    @Autowired
    private OcBOrderUpdateBusinessTypeCmd updateBusinessTypeCmd;

    @Autowired
    private OcBOrderUpdatePreDeliveryTimeCmd orderUpdatePreDeliveryTimeCmd;

    @Autowired
    private OcBOrderBeforeCheckCmd orderBeforeCheckCmd;

    @Autowired
    private AsyncTaskManager asyncTaskManager;

    @Autowired
    private OcBOrderTheAuditStopCmd ocBOrderTheAuditStopCmd;

    @Autowired
    private OcBOrderTradeCompleteCmd ocBOrderTradeCompleteCmd;

    @Resource
    private OcBOrderAppointLogisticsCmd ocBOrderAppointLogisticsCmd;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Autowired
    private BusinessSystem businessSystem;
    @Autowired
    private OcBOrderReRunTobeconfirmedCmd ocBOrderReRunTobeconfirmedCmd;

    private static final String SPLIT = "=";

    //CellType
    public static final int _NONE = -1;
    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    public static final int BLANK = 3;
    public static final int BOOLEAN = 4;
    public static final int ERROR = 5;

    //导入excel,业务校验错误提示返回 错误excel地址
    public static final int IMPORT_ERROR_CODE = 10001;
    //导入excelche直接返回数据
    public static final int IMPORT_RESULT_DATA = 10002;
    private static NumberFormat nf = NumberFormat.getInstance();


    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }


    /**
     * 订单反审核
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "重跑待分配")
    @RequestMapping(value = "/api/cs/oc/oms/v1/reRunTobeconfirmed", method = RequestMethod.POST)
    public ValueHolderV14 reRunTobeconfirmed(HttpServletRequest request,
                                        @RequestBody JSONObject obj) {

        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        JSONArray jsonArray = obj.getJSONArray("ids");
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            ids.add(jsonArray.getLong(i));
        }
        ValueHolderV14 valueHolderV14 = ocBOrderReRunTobeconfirmedCmd.reRunTobeconfirmed(ids, user);
        return valueHolderV14;
    }

    /**
     * 订单反审核
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "订单反审核")
    @RequestMapping(value = "/api/cs/oc/oms/v1/auditOrderReserve", method = RequestMethod.POST)
    public ValueHolderV14 orderTheAudit(HttpServletRequest request,
                                        @RequestBody JSONObject obj) {


        // 获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        if (obj.getString("isAsync") == null || !obj.getBoolean("isAsync")) {

            ValueHolderV14 vh;
            OrderICheckRequest orderICheckRequest = new OrderICheckRequest();
            JSONArray jsonArray = obj.getJSONArray("ids");
            Long[] ids = new Long[jsonArray.size()];
            for (int i = 0; i < jsonArray.size(); i++) {
                ids[i] = jsonArray.getLong(i);
            }
            orderICheckRequest.setIds(ids);
            orderICheckRequest.setType(obj.getLong("type"));
            orderICheckRequest.setIsCheck(obj.getLong("isCheck"));

            try {
                vh = ocBOrderTheAuditCmd.orderTheAudit(orderICheckRequest, user);
            } catch (NDSException e) {
                vh = new ValueHolderV14();
                vh.setCode(ResultCode.FAIL);
                vh.setMessage(Resources.getMessage("异常信息：" + Throwables.getStackTraceAsString(e)));
            }
            return vh;

        } else {
            JSONArray jsonArray = obj.getJSONArray("ids");
            Long[] ids = new Long[jsonArray.size()];
            for (int i = 0; i < jsonArray.size(); i++) {
                ids[i] = jsonArray.getLong(i);
            }
            OrderICheckRequest orderICheckRequest = new OrderICheckRequest();
            orderICheckRequest.setIds(ids);
            orderICheckRequest.setType(obj.getLong("type"));
            orderICheckRequest.setIsCheck(obj.getLong("isCheck"));
            ValueHolderV14 holderV14 = ocBOrderTheAuditCmd.orderTheAudit(orderICheckRequest, user);
            return holderV14;
        }
    }

    @ApiOperation(value = "")
    @RequestMapping(value = "/api/cs/oc/oms/v1/updateStatusByIds", method = RequestMethod.POST)
    public ValueHolderV14 updateStatusByIds(HttpServletRequest request,
                                            @RequestBody JSONObject obj) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug(" start OcBOrderCtrl.updateStatusByIds.ReceiveParams=" + obj);
        }
        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        JSONArray jsonArray = obj.getJSONArray("ids");
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            ids.add(jsonArray.getLong(i));
        }
        ValueHolderV14 valueHolderV14 = ocBOrderUpdateStatusCmd.uodateById(ids, user);
        //记录日志信息。Finish 标记结束
        log.debug(" start OcBOrderCtrl.updateStatus.valueHolderV14 {}", JSON.toJSONString(valueHolderV14));
        return valueHolderV14;
    }

    /**
     * 取消订单
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "取消订单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/cancelOrder", method = RequestMethod.POST)
    public JSONObject updateStatus(HttpServletRequest request,
                                   @RequestBody JSONObject obj) {

        ValueHolderV14 vh;
        //获取当前登陆用户
        //User user = Security4Utils.getUser("root");
        //User user = OcBOrderCtrl.getRootUser();
        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        OrderICheckRequest orderICheckRequest = new OrderICheckRequest();
        JSONArray jsonArray = obj.getJSONArray("ids");
        Long[] ids = new Long[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            ids[i] = jsonArray.getLong(i);
        }
        orderICheckRequest.setIds(ids);
        orderICheckRequest.setType(obj.getLong("type"));
        orderICheckRequest.setIsCheck(obj.getLong("isCheck"));

        try {
            vh = ocBOrderOffCmd.updateStatus(orderICheckRequest, user);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    @ApiOperation(value = "生成丢件单")
    @RequestMapping(value = "/api/cs/oc/oms/payableAdjustmentCreate", method = RequestMethod.POST)
    public ValueHolderV14 payableAdjustmentCreate(HttpServletRequest request,
                                                  @RequestBody JSONObject obj) {
        JSONArray jsonArray = obj.getJSONArray("ids");
        Long[] ids = new Long[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            ids[i] = jsonArray.getLong(i);
        }
        User user = (UserImpl) request.getSession().getAttribute("user");
        if (user == null) {
            user = SystemUserResource.getRootUser();
        }
//        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        return ocBOrderPayableAdjustmentCreateCmd.payableAdjustmentCreate(ids, user);
    }

    @ApiOperation(value = "校验是否存在其他可合并的JITX订单")
    @RequestMapping(value = "/api/cs/oc/oms/jitx/checkOtherMergeOrder", method = RequestMethod.POST)
    public ValueHolderV14 checkOtherMergeOrder(HttpServletRequest request,
                                               @RequestParam(value = "param") String param) {
        JSONObject obj = JSONObject.parseObject(param);
        JSONArray jsonArray = obj.getJSONArray("ids");
        if (jsonArray == null || jsonArray.isEmpty()) {
            ValueHolderV14 v14 = new ValueHolderV14();
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择数据");
            return v14;
        }
        List<Long> ids = new ArrayList<>(jsonArray.size());
        for (int i = 0; i < jsonArray.size(); i++) {
            ids.add(jsonArray.getLong(i));
        }
        User user = (UserImpl) request.getSession().getAttribute("user");
        if (user == null) {
            user = SystemUserResource.getRootUser();
        }
//        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        return ocBOrderCheckJitxOtherMergeOrderCmd.checkJitxOtherMergerOrder(ids, user);
    }

    @ApiOperation(value = "冻结并重新寻源")
    @RequestMapping(value = "/api/cs/oc/oms/stock/lockStockAndReOccupy", method = RequestMethod.POST)
    public ValueHolderV14 lockStockAndReOccupy(HttpServletRequest request,
                                               @RequestParam(value = "param") String param) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug("start OcBOrderCtrl.lockStockAndReOccupy.ReceiveParams:{}", param);
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        return omsOrderLockStockAndReOccupyStockCmd.lockStockAndReOccupy(param, user);
    }

    @ApiOperation(value = "零售发货单-补发订单")
    @RequestMapping(value = "/api/cs/oc/oms/vop/resetShip", method = RequestMethod.POST)
    public ValueHolderV14 resetShip(HttpServletRequest request,
                                    @RequestBody JSONObject param) {
        //记录日志信息
        if (log.isDebugEnabled()) {
            log.debug("start OcBOrderCtrl.resetShip.ReceiveParams:{}", param);
        }
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        return omsOrderVopResetShipCmd.resetShip(JSONObject.toJSONString(param), user);
    }

    /**
     * 下载模板
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "下载模板")
    @RequestMapping(path = "/api/cs/oc/oms/v1/downloadOrderRemarkTemp", method = RequestMethod.POST)
    public ValueHolderV14 downloadAddressTemp()
            throws Exception {

        //处理返回数据
        ValueHolderV14 holderV14 = ocBOrderExportCmd.downloadAddressTemp();
        return holderV14;
    }

    @ApiOperation(value = "批量修改地址导入")
    @RequestMapping(path = "/api/cs/oc/oms/v1/importOrderRemark", method = RequestMethod.POST)
    public ValueHolderV14 importOrderAddresskByPro(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        //插入我的任务里
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("批量修改地址导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        return asyncImport(asyncTaskBody, user, file);


    }

    private ValueHolderV14 asyncImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {
            try {
                if (file == null) {
                    throw new NDSException("请求参数不能为空!");
                }
                InputStream inputStream = file.getInputStream();
                Workbook hssfWorkbook = WorkbookFactory.create(inputStream);
                if (hssfWorkbook.getNumberOfSheets() != 1) {
                    throw new NDSException("订单修改地址导入模板不正确");
                }
                List<OcBOrderRemarkImpVO> orderRemarkList = getOrderRemarkList(hssfWorkbook);
                if (CollectionUtils.isEmpty(orderRemarkList)) {
                    throw new NDSException("导入数据不能为空!");
                }
                if (orderRemarkList.size() > 2000) {
                    throw new NDSException("导入条数请勿超过2000！");
                }
                //校验必填字段不能为空
                checkRequired(orderRemarkList);
//                if (!v14.isOK()) {
//                    return ocBOrderImportCmd.exportError(v14, user);
//                }
                if (CollectionUtils.isEmpty(orderRemarkList)) {
                    throw new NDSException("订单修改地址导入模板主表数据不能为空!");
                }
                // 校验数据是否重复
                if (isRepetition(orderRemarkList)) {
                    throw new NDSException("订单修改地址导入数据重复,平台单号不能重复,请检查!");
                }
                ValueHolderV14<List<OcBOrderRemarkImpVO>> holderV141 = ocBOrderImportCmd.updateOrderAddress(orderRemarkList, user);
                if (holderV141.getCode() == ResultCode.FAIL) {
                    ValueHolderV14 valueHolderV14 = ocBOrderImportCmd.exportError(holderV141, user);
                    retMap.put("data", valueHolderV14.getData());
                    retMap.put("path", valueHolderV14.getData());
                    asyncTaskBody.setUrl(String.valueOf(valueHolderV14.getData()));
                }
                //return holderV141;

                retMap.put("code", ResultCode.SUCCESS);
                retMap.put("message", holderV141.getMessage());
                //任务完成
                asyncTaskBody.setTaskType("导出");
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));

            } catch (Exception ex) {
                log.error(LogUtil.format("导入失败", "批量修改地址"), ex);
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + ex.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("批量修改导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }


    private void checkRequired(List<OcBOrderRemarkImpVO> orderRemarkList) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
//        List<String> errorList = new ArrayList<>();
//        List<OcBOrderRemarkImpVO> resultList = new ArrayList<>();
        for (OcBOrderRemarkImpVO orderRemarkImpVO : orderRemarkList) {

            //平台单号
            if (StringUtils.isEmpty(orderRemarkImpVO.getSourceCode())) {
                orderRemarkImpVO.setDesc("平台单号不能为空，请检查数据！");
            }
            //省份
            if (StringUtils.isEmpty(orderRemarkImpVO.getCpCRegionProvinceEname())) {
                String error = "平台单号：" + orderRemarkImpVO.getSourceCode() + ", 省份不能为空！";
                orderRemarkImpVO.setDesc(error);
            }
            //市
            if (StringUtils.isEmpty(orderRemarkImpVO.getCpCRegionCityEname())) {
                String error = "平台单号：" + orderRemarkImpVO.getSourceCode() + ", 城市不能为空！";
                orderRemarkImpVO.setDesc(error);
            }
            //详细地址
            if (StringUtils.isEmpty(orderRemarkImpVO.getReceiverAddress())) {
                String error = "平台单号：" + orderRemarkImpVO.getSourceCode() + ", 详细地址不能为空！";
                orderRemarkImpVO.setDesc(error);
            }
            //收货人
            if (StringUtils.isEmpty(orderRemarkImpVO.getReceiverName())) {
                String error = "平台单号：" + orderRemarkImpVO.getSourceCode() + ", 收货人不能为空！";
                orderRemarkImpVO.setDesc(error);
            }
            //收货人手机
            if (StringUtils.isEmpty(orderRemarkImpVO.getReceiverMobile())) {
                String error = "平台单号：" + orderRemarkImpVO.getSourceCode() + ", 收货人手机不能为空！";
                orderRemarkImpVO.setDesc(error);
            }
        }
    }

    /**
     * @param orderRemarkList
     * @return
     */
    private boolean isRepetition(List<OcBOrderRemarkImpVO> orderRemarkList) {
        int size = orderRemarkList.size();
        List<String> collect = orderRemarkList.stream().map(OcBOrderRemarkImpVO::getSourceCode).distinct().collect(Collectors.toList());
        return size != collect.size();
    }

    @ApiOperation(value = "重新寻源")
    @RequestMapping(value = "/api/cs/oc/oms/v1/againOccupyStock", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 againOccupyStock(HttpServletRequest request,
                                           @RequestBody JSONObject obj) {

        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        //新增我的任务
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("零售发货单");
        asyncTaskBody.setTaskType("批量重新寻源");

        //任务开始
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        commonTaskExecutor.submit(() -> {

            //记录日志信息
            ValueHolderV14<OrderHoldResult> valueHolder = new ValueHolderV14<>();

            try {

                ValueHolder occupyValueHolder = againOccupyStockCmd.againOccupyStock(obj, user);

                //记录日志信息
                asyncTaskManager.afterExecute(user, asyncTaskBody, occupyValueHolder.toJSONObject());

            } catch (NDSException e) {

                valueHolder.setCode(ResultCode.FAIL);
                valueHolder.setMessage(Resources.getMessage("异常信息：" + Throwables.getStackTraceAsString(e)));

                //记录日志信息
                asyncTaskManager.afterExecute(user, asyncTaskBody, valueHolder.toJSONObject());
            }

        });

        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS,
                Resources.getMessage("零售发货单-批量重新寻源任务开始！详情请在我的任务查看"));
        vh.setData(asyncTaskBody.getId());
        return vh;
    }

    /**
     * 获取主it 表sheet数据，转换成主表对象
     */
    public List<OcBOrderRemarkImpVO> getOrderRemarkList(Workbook hssfWorkbook) {
        List<OcBOrderRemarkImpVO> ocBOrderRemarkImpVOS = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();

        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {

        }
        int index = 0;

        for (Map<String, String> columnMap : execlList) {
            OcBOrderRemarkImpVO ocBOrderRemarkImpVO = new OcBOrderRemarkImpVO();
            if (index == 0) {
                // 校验excel字段
                if (columnMap.size() != 10
                        || !"平台单号".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"省份".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"城市".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"区县".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"详细地址".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"收货人".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"收货人手机".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"收货人电话".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"收货人邮编".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"是否明文".equals(columnMap.get(rowStr + index + cellStr + 9))
                ) {

                    return Lists.newArrayList();
                }
            } else {
                if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))) {
                    // 组装待配货记录
                    ocBOrderRemarkImpVOS.add(OcBOrderRemarkImpVO.importCreate(index, ocBOrderRemarkImpVO, columnMap));
                }
            }
            index++;
        }
        return ocBOrderRemarkImpVOS;
    }


    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    @ApiOperation(value = "手工取消打标")
    @RequestMapping(value = "/api/cs/oc/oms/clearOrderMarking", method = RequestMethod.POST)
    public ValueHolderV14 clearOrderMarking(HttpServletRequest request,
                                            @RequestBody OrderMarkingRequest param) {
        ValueHolderV14 failVh = new ValueHolderV14();
        failVh.setCode(ResultCode.FAIL);
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            failVh.setMessage("用户不能为空!");
            return failVh;
        }
        if (param == null) {
            failVh.setMessage("请求参数不能为空!");
            return failVh;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderCtrl.clearOrderMarking params= {}",
                    JSONObject.toJSONString(param)), JSONObject.toJSONString(param));
        }
        return ocBOrderMarkingCmd.clearMarking(param, user);
    }

    @ApiOperation(value = "手工打标")
    @RequestMapping(value = "/api/cs/oc/oms/orderMarking", method = RequestMethod.POST)
    public ValueHolderV14 orderMarking(HttpServletRequest request,
                                       @RequestBody OrderMarkingRequest param) {
        ValueHolderV14 failVh = new ValueHolderV14();
        failVh.setCode(ResultCode.FAIL);
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            failVh.setMessage("用户不能为空!");
            return failVh;
        }
        if (param == null) {
            failVh.setMessage("请求参数不能为空!");
            return failVh;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcBOrderCtrl.clearOrderMarking params= {}",
                    JSONObject.toJSONString(param)), JSONObject.toJSONString(param));
        }
        return ocBOrderMarkingCmd.orderMarking(param, user);
    }

    @ApiOperation(value = "修改业务类型")
    @RequestMapping(value = "/api/cs/oc/oms/v1/updateBusinessType", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject updateBusinessType(HttpServletRequest request,
                                         @RequestBody JSONObject obj) {
        //记录日志信息
        ValueHolderV14<OrderHoldResult> vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolder valueHolder = updateBusinessTypeCmd.updateBusinessType(obj, user);
            //记录日志信息。Finish 标记结束
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    @ApiOperation(value = "批量修改预计发货时间")
    @RequestMapping(value = "/api/cs/oc/oms/v1/updatePreDeliveryTime", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject updatePreDeliveryTime(HttpServletRequest request,
                                            @RequestBody JSONObject obj) {
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14<String> v14 = orderUpdatePreDeliveryTimeCmd.updatePreDeliveryTime(obj, user);
            return v14.toJSONObject();
        } catch (NDSException e) {
            ValueHolderV14<OrderHoldResult> v14 = new ValueHolderV14<>();
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(Resources.getMessage(e.getMessage()));
            return v14.toJSONObject();
        }
    }

    /**
     * 订单操作前置校验
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "订单操作前置校验")
    @RequestMapping(value = "/api/cs/oc/oms/v1/orderBeforeCheck", method = RequestMethod.POST)
    public JSONObject orderBeforeCheck(HttpServletRequest request,
                                       @RequestBody JSONObject obj) {
        ValueHolderV14 vh;
        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        JSONArray jsonArray = obj.getJSONArray("ids");
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            ids.add(jsonArray.getLong(i));
        }
        try {
            vh = orderBeforeCheckCmd.beforeCheck(ids, obj.getString("action"), user);
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
        }
        return vh.toJSONObject();
    }

    /**
     * 订单反审核并卡单
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "订单反审核并卡单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/auditOrderReserveAndDetention", method = RequestMethod.POST)
    public ValueHolderV14 orderTheAuditDetention(HttpServletRequest request, @RequestBody JSONObject obj) {
        // 获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        JSONArray jsonArray = obj.getJSONArray("ids");
        Long[] ids = new Long[jsonArray.size()];
        for (int i = 0; i < jsonArray.size(); i++) {
            ids[i] = jsonArray.getLong(i);
        }
        OrderICheckStopRequest orderICheckStopRequest = new OrderICheckStopRequest();
        orderICheckStopRequest.setIds(ids);
        orderICheckStopRequest.setType(obj.getLong("type"));
        orderICheckStopRequest.setIsCheck(obj.getLong("isCheck"));
        ValueHolderV14 holderV14 = ocBOrderTheAuditStopCmd.orderTheAuditStop(orderICheckStopRequest, user);
        return holderV14;
    }

    /**
     * 零售发货单交易完成
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "零售发货单交易完成")
    @RequestMapping(value = "/api/cs/oc/oms/v1/tradeComplete", method = RequestMethod.POST)
    public ValueHolderV14 tradeComplete(HttpServletRequest request, @RequestBody JSONObject obj) {
        // 获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        List<Long> ids = JSONObject.parseArray(obj.getJSONArray("ids").toJSONString(), Long.class);
        ValueHolderV14 holderV14 = ocBOrderTradeCompleteCmd.tradeComplete(ids, user);
        return holderV14;
    }

    /**
     * 手工指定快递数据校验及快递信息返回
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "手工指定快递数据校验及快递信息返回")
    @RequestMapping(value = "/api/cs/oc/oms/v1/queryLogistics", method = RequestMethod.POST)
    public ValueHolderV14<JSONObject> queryLogistics(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14<JSONObject> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        // 获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        List<Long> ids = JSONObject.parseArray(obj.getJSONArray("ids").toJSONString(), Long.class);
        if (CollectionUtils.isEmpty(ids)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择需要指定快递的单据！");
            return v14;
        }
        Integer page = obj.getInteger("page");
        if (page == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("页码不能为空！");
            return v14;
        }
        Integer size = obj.getInteger("size");
        if (size == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("每页显示条数不能为空！");
            return v14;
        }
        return ocBOrderAppointLogisticsCmd.queryLogistics(ids, page, size);
    }

    /**
     * 指定快递
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "指定快递")
    @RequestMapping(value = "/api/cs/oc/oms/v1/appointLogistics", method = RequestMethod.POST)
    public ValueHolderV14<Void> appointLogistics(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        // 获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        List<Long> ids = JSONObject.parseArray(obj.getJSONArray("ids").toJSONString(), Long.class);
        if (CollectionUtils.isEmpty(ids)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择需要指定快递的单据！");
            return v14;
        }
        Long logisticsId = obj.getLong("logisticsId");
        if (logisticsId == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("物流公司不能为空！");
            return v14;
        }
        return ocBOrderAppointLogisticsCmd.appointLogistics(ids, logisticsId, user);
    }

    /**
     * 取消指定快递
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "取消指定快递")
    @RequestMapping(value = "/api/cs/oc/oms/v1/cancelAppointLogistics", method = RequestMethod.POST)
    public ValueHolderV14<Void> cancelAppointLogistics(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        // 获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = SystemUserResource.getRootUser();
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }

        List<Long> ids = JSONObject.parseArray(obj.getJSONArray("ids").toJSONString(), Long.class);
        if (CollectionUtils.isEmpty(ids)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择需要指定快递的单据！");
            return v14;
        }
        return ocBOrderAppointLogisticsCmd.cancelAppointLogistics(ids, user, false);
    }

    @ApiOperation(value = "TOB订单内部备注")
    @RequestMapping(path = "/api/cs/oc/oms/v1/remark/enum", method = RequestMethod.GET)
    public ValueHolderV14<String[]> orderRemarkEnumQuery() {
        ValueHolderV14<String[]> v14 = new ValueHolderV14<>();
        String remarkEnum = businessSystem.queryOrderInnerRemarkEnum();
        if (StringUtils.isEmpty(remarkEnum)) {
            return v14;
        }
        String[] split = remarkEnum.split(SPLIT);
        v14.setData(split);
        return v14;
    }
}

