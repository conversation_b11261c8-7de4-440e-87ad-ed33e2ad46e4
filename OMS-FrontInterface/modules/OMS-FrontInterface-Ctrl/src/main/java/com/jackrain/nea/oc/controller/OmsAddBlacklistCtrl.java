package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OmsAddBlacklistCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: 黄世新
 * @Date: 2019-07-01 14:21
 * @Version 1.0
 */
@Api(value = "OmsAddBlacklistCtrl", tags = "添加黑名单")
@Slf4j
@RestController
public class OmsAddBlacklistCtrl {

    @Autowired
    private OmsAddBlacklistCmd omsAddBlacklistCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "添加黑名单")
    @RequestMapping(value = "api/cs/oc/oms/v1/addBlacklist", method = RequestMethod.POST)
    public JSONObject addBlacklist(HttpServletRequest request, @RequestBody JSONObject object) {
        ValueHolder holder = new ValueHolder();
        try {
            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
            holder = omsAddBlacklistCmd.addBlacklist(object, user);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 添加黑名单异常", e);
            holder.put("code", -1);
            holder.put("message", "程序异常!");
        }
        return holder.toJSONObject();

    }


}
