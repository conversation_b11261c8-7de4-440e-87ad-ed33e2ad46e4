package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.GetDetailCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @Author: wangqiang
 * @Date: 2019-03-07 11:02
 * @Version 1.0
 */

@Api(value = "GetDetailCtrl", tags = "订单详情-查询")
@Slf4j
@RestController
public class GetDetailCtrl {

    @Autowired
    GetDetailCmd getDetailCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "订单详情-查询")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getDetail", method = RequestMethod.POST)
    public JSONObject getDetail(HttpServletRequest request,
                                @RequestBody JSONObject obj) {
        ValueHolder vh = new ValueHolder();
        JSONObject result = new JSONObject();
        //记录日志信息
//        if (log.isDebugEnabled()) {
//            log.debug("start GetDetailCtrl.getDetail.ReceiveParams=" + obj);
//        }

        try {
            // User user = GetDetailCtrl.getRootUser();
            // 获取用户
            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

            vh = getDetailCmd.getDetail(obj, user);

            result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), new BigDecimalValueFilter(),
                    SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullNumberAsZero));
        } catch (NDSException e) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
        }
        return result;
    }

 /*   private static User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("admin");
        user.setEname("Pokemon-mapper");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }*/


    class BigDecimalValueFilter implements ValueFilter {
        @Override
        public Object process(Object o, String name, Object value) {

            if (null != value && value instanceof BigDecimal) {
                return ((BigDecimal) value).setScale(4, RoundingMode.DOWN).toString();
            }
            return value;
        }
    }

}
