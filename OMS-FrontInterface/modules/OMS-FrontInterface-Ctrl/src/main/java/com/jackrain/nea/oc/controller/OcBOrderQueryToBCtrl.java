package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OcBOrderQueryToBRemarkCmd;
import com.jackrain.nea.oc.oms.api.OcBToBOrderCmd;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @ClassName OcBOrderQueryToCtrl
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/2 15:05
 * @Version 1.0
 */
@RestController
@Slf4j
@Api(value = "OcBOrderQueryToCtrl", tags = "新零售单据监控查询")
public class OcBOrderQueryToBCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OcBOrderQueryToBRemarkCmd ocBOrderQueryToBRemarkCmd;

    @Autowired
    private OcBToBOrderCmd ocBToBOrderCmd;

    @ApiOperation(value = "列表查询")
    @RequestMapping(path = "/api/cs/oc/oms/v1/tobRemark", method = RequestMethod.POST)
    public JSONObject tobRemark(HttpServletRequest request) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        return ocBOrderQueryToBRemarkCmd.queryToBRemark().toJSONObject();
    }

    @ApiOperation(value = "修改备注")
    @RequestMapping(path = "/api/cs/oc/oms/v1/updateTobRemark", method = RequestMethod.POST)
    public JSONObject updateTobRemark(HttpServletRequest request, @RequestBody String param) {
        JSONObject paramObj = JSON.parseObject(param);
        JSONObject innerParam = paramObj.getJSONObject("param");
        return ocBToBOrderCmd.updateRemark(innerParam).toJSONObject();
    }
}
