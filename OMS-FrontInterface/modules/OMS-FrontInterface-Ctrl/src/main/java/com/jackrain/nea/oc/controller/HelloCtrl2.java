package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.HelloCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderPosCancelCmd;
import com.jackrain.nea.oc.oms.api.QueryTMergeOrderCmd;
import com.jackrain.nea.oc.oms.model.request.PosOrderCancelRequest;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@Slf4j
@Api(value = "hello1", tags = "hello2")
public class HelloCtrl2 {
    @Autowired
    private HelloCmd helloCmd;

    @RequestMapping(path = "/api/cs/oc/oms/v1/storage/hello1", method = RequestMethod.GET)
    public String hello1(String type) {
        return helloCmd.hello(type);
    }

//    @Autowired
//    private OrderMergeService orderMergeService;

    @Reference(version = "1.0", group = "oc")
    private QueryTMergeOrderCmd queryTMergeOrderCmd;

    @Reference(version = "1.0", group = "oc-core")
    private OcBOrderPosCancelCmd posCancelCmd;

    @RequestMapping(path = "/api/cs/oc/oms/v1/storage/hello21", method = RequestMethod.GET)
    public String hello2(String type) {
//        int i = 1 / 0;
        return helloCmd.hello(type);
    }

    @RequestMapping(path = "/api/cs/oc/oms/v1/storage/hello3", method = RequestMethod.GET)
    public String hello3(String type) {
        return "hello 3";
    }

    @RequestMapping(path = "/api/cs/oc/oms/v1/storage/es1", method = RequestMethod.GET)
    public JSONObject es() {
        return helloCmd.queryEs();
    }

    @RequestMapping(path = "/api/cs/oc/oms/v1/storage/esCondition1", method = RequestMethod.GET)
    public JSONObject esCondition(String index, String type, String whereKeysStr, String filterKeysStr) {
        JSONObject whereKeys = JSONObject.parseObject(whereKeysStr);
        JSONObject filterKeys = JSONObject.parseObject(filterKeysStr);
        return helloCmd.queryEsByCondition(index, type, whereKeys, filterKeys);
    }

    //合单测试专用
//    @RequestMapping(path = "/api/cs/oc/oms/v1/storage/mergeOrder", method = RequestMethod.GET)
//    public JSONObject mergeOrder() {
//        orderMergeService.esHaving();
//        return null;
//    }

    //手动合单专用
    @RequestMapping(path = "/api/cs/oc/oms/v1/storage/sdMergeOrder", method = RequestMethod.POST)
    public ValueHolder sdMergeOrder(HttpServletRequest request,
                                    @RequestBody JSONObject param) {
        //User user = Security4Utils.getUser("root");
        QuerySessionImpl querySession = new QuerySessionImpl();
        DefaultWebEvent event = new DefaultWebEvent("skuGroupVoid", request, false);
        event.put("param", param);
        querySession.setEvent(event);
        ValueHolder result = queryTMergeOrderCmd.execute(querySession);
        return result;
    }

    //手动合单专用
    @RequestMapping(path = "/api/cs/oc/oms/v1/pos/posCancel", method = RequestMethod.POST)
    public ValueHolder posCancel(@RequestBody List<PosOrderCancelRequest> requests) {

        return posCancelCmd.posCancel(requests);
    }
}
