package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBOrderExportCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.async.AsyncTask;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 李龙飞
 * @create: 2019-05-14 13:26
 **/
@RestController
@Api(value = "OcBOrderExportController", tags = "订单管理导出测试")
@Slf4j
public class OcBOrderExportController {

    @Autowired
    private OcBOrderExportCmd ocBOrderExportCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 列表导出
     *
     * @param request req
     * @param param   string
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "列表导出")
    @RequestMapping(path = "/api/cs/oc/oms/v1/exportOcBOrder", method = RequestMethod.POST)
    public JSONObject getOrderList(HttpServletRequest request, @RequestParam(value = "param") String param) throws Exception {

        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh;

        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(loginUser, "OC_B_ORDER");
        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }

        OcBOrderExportController exportController = ApplicationContextHandle.getBean(OcBOrderExportController.class);
        JSONObject ret = exportController.exportExecute(param, loginUser, usrPem, Boolean.FALSE);
        return ret;
    }

    /**
     * 导出带标签
     *
     * @param request
     * @param param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "列表导出带标签")
    @RequestMapping(path = "/api/cs/oc/oms/v1/exportOcBOrder/tag", method = RequestMethod.POST)
    public JSONObject exportOrderWithTag(HttpServletRequest request, @RequestParam(value = "param") String param) throws Exception {

        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh;

        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(loginUser, "OC_B_ORDER");
        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }

        OcBOrderExportController exportController = ApplicationContextHandle.getBean(OcBOrderExportController.class);
        JSONObject ret = exportController.exportExecute(param, loginUser, usrPem, Boolean.TRUE);
        return ret;
    }


    /**
     * @param param
     * @param user
     * @return
     * @throws Exception
     */
    @AsyncTask("导出")
    public JSONObject exportExecute(String param, User user, UserPermission usrPem, Boolean withTag) throws Exception {
        ValueHolderV14 holderV14 = ocBOrderExportCmd.exportListext(param, user, usrPem, withTag);

        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", 0);
        valueHolder.put("message", holderV14.getMessage());
        valueHolder.put("data", holderV14.getData());

        return valueHolder.toJSONObject();
    }

    /**
     * 下载模板
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "下载模板")
    @RequestMapping(path = "/api/cs/oc/oms/v1/downloadOrderTemp", method = RequestMethod.POST)
    public ValueHolderV14 downloadTemp()
            throws Exception {

        //处理返回数据
        ValueHolderV14 holderV14 = ocBOrderExportCmd.downloadTemp();
        return holderV14;
    }

    /**
     * 下载模板-订单预导入
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "下载模板")
    @RequestMapping(path = "/api/cs/oc/oms/v1/downloadOrderTemp/pre", method = RequestMethod.POST)
    public ValueHolderV14 downloadTempPre() throws Exception {
        //处理返回数据
        ValueHolderV14 holderV14 = ocBOrderExportCmd.downloadTempPre();
        return holderV14;
    }

    /**
     * 下载修改备注模板
     *
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "下载修改备注模板")
    @RequestMapping(path = "/api/cs/oc/oms/v1/downloadUpdateRemarkTemp", method = RequestMethod.POST)
    public ValueHolderV14 downloadUpdateRemarkTemp()
            throws Exception {

        //处理返回数据
        ValueHolderV14 holderV14 = ocBOrderExportCmd.downloadUpdateRemarkTemp();
        return holderV14;
    }

}
