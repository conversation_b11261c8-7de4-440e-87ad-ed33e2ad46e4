package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcChangingOrRefundingDetailCmd;
import com.jackrain.nea.oc.oms.api.OcQueryChangingOrRefundingCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.services.OcFindChangingOrRefundingDetailsImpl;
import com.jackrain.nea.oc.services.OcQueryChangingOrRefundingCmdImpl;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 郑立轩
 * @since: 2019/3/11
 * create at : 2019/3/11 14:02
 */
@Api(value = "QueryChangingOrRefundingCtrl", tags = "退换货列表查询")
@Slf4j
@RestController
public class QueryChangingOrRefundingCtrl {

    @Autowired
    private OcQueryChangingOrRefundingCmdImpl cmd;

    @Autowired
    private OcFindChangingOrRefundingDetailsImpl detailCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 退换货列表查询
     *
     * @param request 请求
     * @param
     * @param
     * @return return
     */
    @ApiOperation(value = "退换货列表查询")
    @RequestMapping(value = "/api/cs/oc/oms/v1/querySalesReturn", method = RequestMethod.POST)
    public JSONObject querySalesReturn(HttpServletRequest request, @RequestBody JSONObject obj) {

        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_RETURN_ORDER");
        if (usrPem == null) {
            ValueHolderV14 vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }

        ValueHolderV14 vh = cmd.queryChangingOrRefunding(usrPem, obj, user);
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                SerializerFeature.WriteMapNullValue));
        return jsonObject;


    }

    @ApiOperation(value = "列表详情查询")
    @RequestMapping(value = "/api/cs/oc/oms/v1/findDetail", method = RequestMethod.POST)
    public JSONObject findDetail(HttpServletRequest request,
                                 @RequestBody JSONObject obj) {
        //JSONObject jsonObject = JSON.parseObject(param);
        //调用服务
        ValueHolderV14 vh = detailCmd.findDetail(obj);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));


    }

    @ApiOperation(value = "退换货订单复制")
    @RequestMapping(value = "/api/cs/oc/oms/v1/returnOrderquery", method = RequestMethod.POST)
    public JSONObject returnOrderQuery(HttpServletRequest request, @RequestBody JSONObject obj) {
        //调用服务
        ValueHolderV14 vh = detailCmd.returnOrderquery(obj);
        String result = JSON.toJSONStringWithDateFormat(vh.toJSONObject(),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue);
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

}
