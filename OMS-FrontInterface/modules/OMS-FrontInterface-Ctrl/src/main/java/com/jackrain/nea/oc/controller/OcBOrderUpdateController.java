package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OcBOrderItemUpdateAddServiceCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderItemUpdateRemarkCmd;
import com.jackrain.nea.oc.oms.api.OcBOrderUpdateEstimateConTimeCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR> lin yu
 * @date : 2022/7/20 下午4:15
 * @describe :
 */

@Api(value = "OcBOrderUpdateController", tags = "订单跟新")
@Slf4j
@RestController
public class OcBOrderUpdateController {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OcBOrderUpdateEstimateConTimeCmd ocBOrderUpdateEstimateConTimeCmd;

    @Autowired
    private OcBOrderItemUpdateRemarkCmd ocBOrderItemUpdateRemarkCmd;
    @Autowired
    private OcBOrderItemUpdateAddServiceCmd ocBOrderItemUpdateAddServiceCmd;

    @ApiOperation(value = "修改预计发货时间")
    @PostMapping(value = "/api/cs/oc/oms/v1/updateEstimateConTime")
    public ValueHolderV14 updateEstimateConTime(HttpServletRequest request,
                                                @RequestBody JSONObject obj) {
        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }

        return ocBOrderUpdateEstimateConTimeCmd.updateEstimateConTime(obj, user);
    }

    @ApiOperation(value = "修改订单明细")
    @PostMapping(value = "/api/cs/oc/oms/v1/updateItemRemark")
    public ValueHolderV14 updateItemRemark(HttpServletRequest request,
                                                @RequestBody JSONObject obj) {
        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }

        return ocBOrderItemUpdateRemarkCmd.updateItemRemark(obj, user);
    }

    @ApiOperation(value = "修改订单明细增值服务")
    @PostMapping(value = "/api/cs/oc/oms/v1/updateAddService")
    public ValueHolderV14 updateAddService(HttpServletRequest request,
                                           @RequestBody JSONObject obj) {
        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }

        return ocBOrderItemUpdateAddServiceCmd.updateItemAddService(obj, user);
    }

}

