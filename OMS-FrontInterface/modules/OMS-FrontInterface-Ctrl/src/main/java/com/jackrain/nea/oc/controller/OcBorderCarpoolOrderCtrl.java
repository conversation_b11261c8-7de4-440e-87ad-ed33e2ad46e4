package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.carpoolOrder.OmsOrderCarpoolOrderService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @author: lijin
 */
@Api(value = "OcBorderCarpoolOrderCtrl", tags = "整车拼车")
@Slf4j
@RestController
public class OcBorderCarpoolOrderCtrl {

    @Resource
    private OmsOrderCarpoolOrderService omsOrderCarpoolOrderService;
    @Resource
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "整车拼车校验并获取拼车单号")
    @PostMapping(value = "/api/cs/oc/oms/v1/carpoolOrderCheckAndGetNumber")
    public ValueHolderV14<String> carpoolOrderCheckAndGetNumber(HttpServletRequest request,
                                                                @RequestBody JSONObject obj) {
        ValueHolderV14<String> v14 = new ValueHolderV14(ResultCode.SUCCESS, "校验通过！");
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请求参数为空！");
            return v14;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.isEmpty()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择订单！");
            return v14;
        }
        List<Long> orderIds = ids.toJavaList(Long.class);
        try {
            String carpoolNo = omsOrderCarpoolOrderService.carpoolOrderCheckAndGetNumber(orderIds, user);
            v14.setData(carpoolNo);
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    @ApiOperation(value = "整车拼车确认")
    @PostMapping(value = "/api/cs/oc/oms/v1/carpoolOrderConfirm")
    public ValueHolderV14<Void> carpoolOrderConfirm(HttpServletRequest request,
                                                    @RequestBody JSONObject obj) {
        ValueHolderV14<Void> v14 = new ValueHolderV14(ResultCode.SUCCESS, "整车拼车成功！");
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请求参数为空！");
            return v14;
        }
        String carpoolNo = obj.getString("carpoolNo");
        if (StringUtils.isEmpty(carpoolNo)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("拼车单号不能为空！");
            return v14;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.isEmpty()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择订单！");
            return v14;
        }
        List<Long> orderIds = ids.toJavaList(Long.class);
        try {
            omsOrderCarpoolOrderService.carpoolOrderConfirm(orderIds, carpoolNo, user);
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

    @ApiOperation(value = "取消拼车")
    @PostMapping(value = "/api/cs/oc/oms/v1/carpoolOrderCancel")
    public ValueHolderV14<Void> carpoolOrderCancel(HttpServletRequest request,
                                                   @RequestBody JSONObject obj) {
        ValueHolderV14<Void> v14 = new ValueHolderV14(ResultCode.SUCCESS, "取消拼车成功！");
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请求参数为空！");
            return v14;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.isEmpty()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("请选择订单！");
            return v14;
        }
        List<Long> orderIds = ids.toJavaList(Long.class);
        try {
            omsOrderCarpoolOrderService.carpoolOrderCancel(orderIds, user);
        } catch (Exception e) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        }
        return v14;
    }

}