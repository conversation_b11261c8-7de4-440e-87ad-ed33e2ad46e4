package com.jackrain.nea.oc.controller.patrol;

import com.jackrain.nea.oc.oms.api.patrol.DeleteOrderInfoCmd;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @Author: 黄世新
 * @Date: 2019-07-25 21:26
 * @Version 1.0
 */

@Api(value = "DeleteOrderCtrl", tags = "删除订单数据")
@Slf4j
@RestController
public class DeleteOrderCtrl {


    @Autowired
    private DeleteOrderInfoCmd deleteOrderInfoCmd;


    @ApiOperation(value = "删除订单数据")
    @RequestMapping(value = "/api/cs/oc/oms/v1/delectOrder", method = RequestMethod.POST)
    public String delectOrder(HttpServletRequest request, Long id, String type) {
        String s = deleteOrderInfoCmd.deleteOrderInfo(id, type);
        return s;

    }


}
