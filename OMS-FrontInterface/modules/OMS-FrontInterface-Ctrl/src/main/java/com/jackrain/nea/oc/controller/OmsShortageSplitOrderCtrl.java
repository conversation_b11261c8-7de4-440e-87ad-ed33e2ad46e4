package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ShortageSplitCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 胡林洋
 * @since: 2020-3-17
 * create at : 2020-3-17 01:01
 */
@Api(value = "shortageSplitOrderCtrl", tags = "缺货拆单")
@Slf4j
@RestController
public class OmsShortageSplitOrderCtrl {

    @Autowired
    private ShortageSplitCmd shortageSplitCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @RequestMapping(value = "/api/cs/oc/oms/v1/splitOrder", method = RequestMethod.POST)
    public ValueHolderV14<JSONArray> splitOrder(@RequestBody JSONObject obj, HttpServletRequest request) {
        ValueHolderV14<JSONArray> v14 = new ValueHolderV14<>();
        try {
            if (obj == null) {
                throw new NDSException("请求参数不能为空");
            }
            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
            if (user == null) {
                throw new NDSException("用户没有权限");
            }
            return shortageSplitCmd.splitShortgateOrder(obj, user);
        } catch (Exception e) {
            v14.setCode(-1);
            v14.setMessage(e.getMessage());
            return v14;
        }
    }
}
