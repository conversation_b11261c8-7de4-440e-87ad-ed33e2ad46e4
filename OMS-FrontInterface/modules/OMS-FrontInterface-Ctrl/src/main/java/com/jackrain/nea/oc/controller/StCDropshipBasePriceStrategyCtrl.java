package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.StCDropshipBasePriceStrategyDetailMapper;
import com.jackrain.nea.oc.oms.mapper.StCDropshipBasePriceStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategy;
import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategyDetail;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName StCDropshipBasePriceStrategyCtrl
 * @Description 一件代发基价
 * <AUTHOR>
 * @Date 2025/7/2 14:58
 * @Version 1.0
 */
@Slf4j
@RestController
public class StCDropshipBasePriceStrategyCtrl {


    @Resource
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Resource
    private ThreadPoolTaskExecutor commonTaskExecutor;

    @Autowired
    private AsyncTaskManager asyncTaskManager;

    @Autowired
    private StCDropshipBasePriceStrategyMapper strategyMapper;

    @Autowired
    private StCDropshipBasePriceStrategyDetailMapper detailMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private ExportUtil exportUtil;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Value("${r3.oss.accessKey}")
    private String accessKeyId;

    @Value("${r3.oss.secretKey}")
    private String accessKeySecret;

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.timeout}")
    private String timeout;

    /**
     * 操作费导出
     *
     * @param request
     * @param jsonObject
     * @return
     */
    @PostMapping("/api/cs/oc/oms/stCDropshipBasePriceStrategy/export")
    public ValueHolderV14<String> exportStrategy(HttpServletRequest request, @RequestBody(required = false) JSONObject jsonObject) {
        log.info(LogUtil.format("导出策略，入参:{}", "AcWOptFeeController.exportConfig"), jsonObject);

        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (Objects.isNull(user)) {
            log.error("导出策略 用户信息异常");
            return new ValueHolderV14<>(ResultCode.FAIL, "用户信息异常");
        }

        /*组装查询参数*/
        // 移除无关参数，这里是一件代发基价策略导出，不是操作费导出
        AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("一件代发客户基价策略导出");
        asyncTaskBody.setTaskType("导出");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);

        commonTaskExecutor.execute(() -> {

            JSONObject taskObj = new JSONObject();
            try {
                ValueHolderV14<String> holderV14 = exportDropshipBasePriceStrategy(jsonObject, user);
                if (holderV14.isOK()) {
                    taskObj.put("code", ResultCode.SUCCESS);
                    taskObj.put("data", "点击下载");
                    taskObj.put("message", "导出成功");

                    asyncTaskBody.setUrl(holderV14.getData());
                    asyncTaskManager.afterExecute(user, asyncTaskBody, taskObj);
                } else {
                    taskObj.put("code", ResultCode.FAIL);
                    taskObj.put("message", "导出异常：" + holderV14.getMessage());
                    asyncTaskManager.afterExecute(user, asyncTaskBody, taskObj);
                }
            } catch (Exception e) {
                log.warn(LogUtil.format("导出策略异常,入参:{},异常:{}",
                        "AcWOptFeeController.exportConfig"), jsonObject, Throwables.getStackTraceAsString(e));

                taskObj.put("code", ResultCode.FAIL);
                taskObj.put("message", "导出异常：" + e.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, taskObj);
            }
        });

        return new ValueHolderV14<>(ResultCode.SUCCESS, "正在导出，请在【我的任务】下载结果");
    }

    /**
     * 导出一件代发客户基价策略
     *
     * @param jsonObject 查询参数
     * @param user 用户信息
     * @return 导出结果
     */
    private ValueHolderV14<String> exportDropshipBasePriceStrategy(JSONObject jsonObject, User user) {
        try {
            log.info("开始导出一件代发客户基价策略，入参={}，操作人={}", jsonObject, user.getName());

            // 1. 根据入参查询策略
            List<StCDropshipBasePriceStrategy> strategies;
            Long shopId = null;
            JSONArray shopIdArray = jsonObject.getJSONObject("fixedcolumns").getJSONArray("SHOP_ID");
            if (CollectionUtils.isNotEmpty(shopIdArray)) {
                shopId = shopIdArray.getLong(0);
            }

            String strategyCode = jsonObject.getJSONObject("fixedcolumns").getString("STRATEGY_CODE");

            if (shopId != null) {
                if (StringUtils.isBlank(strategyCode)){
                    // 如果有店铺ID，查询指定店铺的策略
                    log.info("根据店铺ID查询策略，shopId={}", shopId);
                    StCDropshipBasePriceStrategy strategy = strategyMapper.selectByShopId(shopId);
                    if (strategy != null) {
                        strategies = Arrays.asList(strategy);
                    } else {
                        strategies = new ArrayList<>();
                    }
                }else {
                    // 如果有店铺ID，查询指定店铺的策略
                    log.info("根据店铺ID查询策略，shopId={},strategyCode={}", shopId,strategyCode);
                    List<StCDropshipBasePriceStrategy> strategy = strategyMapper.selectByShopIdAndStrategyCode(shopId,strategyCode);
                    if (CollectionUtils.isNotEmpty(strategy)) {
                        strategies = strategy;
                    } else {
                        strategies = new ArrayList<>();
                    }
                }
            } else {
                if (StringUtils.isBlank(strategyCode)){
                    // 如果没有店铺ID，查询所有策略（不限制审核状态）
                    log.info("查询所有策略");
                    strategies = strategyMapper.selectAllStrategies();
                }else {
                    strategies = strategyMapper.selectByStrategyCode(strategyCode);
                }
            }

            if (CollectionUtils.isEmpty(strategies)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "没有策略数据可导出");
            }

            // 2. 收集所有店铺ID，批量查询店铺信息
            Set<Long> shopIds = strategies.stream()
                    .map(StCDropshipBasePriceStrategy::getShopId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

            Map<Long, String> shopIdToNameMap = new HashMap<>();
            if (!shopIds.isEmpty()) {
                // 批量查询店铺信息，避免循环查询
                for (Long cpCShopId : shopIds) {
                    try {
                        CpShop shop = cpRpcService.selectCpCShopById(cpCShopId);
                        if (shop != null) {
                            shopIdToNameMap.put(cpCShopId, shop.getCpCShopTitle());
                        }
                    } catch (Exception e) {
                        log.warn("查询店铺信息失败，shopId={}", cpCShopId, e);
                        shopIdToNameMap.put(cpCShopId, "未知店铺");
                    }
                }
            }

            // 3. 构建导出数据
            List<Map<String, Object>> exportData = new ArrayList<>();
            for (StCDropshipBasePriceStrategy strategy : strategies) {
                // 查询策略明细
                List<StCDropshipBasePriceStrategyDetail> details = detailMapper.selectByStrategyId(strategy.getId());
                if (CollectionUtils.isNotEmpty(details)) {
                    String shopName = shopIdToNameMap.getOrDefault(strategy.getShopId(),"未知店铺");

                    for (StCDropshipBasePriceStrategyDetail detail : details) {
                        Map<String, Object> row = new LinkedHashMap<>();
                        row.put("strategyCode", strategy.getStrategyCode());
                        row.put("shopName", shopName);
                        row.put("skuCode", detail.getSkuCode());
                        row.put("basePrice", detail.getBasePrice());
                        row.put("importContent", detail.getImportContent());
                        exportData.add(row);
                    }
                }
            }

            if (exportData.isEmpty()) {
                return new ValueHolderV14<>(ResultCode.FAIL, "没有明细数据可导出");
            }

            // 4. 配置OSS参数
            exportUtil.setEndpoint(this.endpoint);
            exportUtil.setAccessKeyId(this.accessKeyId);
            exportUtil.setAccessKeySecret(this.accessKeySecret);
            exportUtil.setBucketName(this.bucketName);
            if (org.apache.commons.lang3.StringUtils.isEmpty(timeout)) {
                // 如果获取不到配置参数，设置默认过期时间为30分钟
                timeout = "1800000";
            }
            exportUtil.setTimeout(this.timeout);

            // 5. 生成Excel文件并上传到OSS
            String[] headers = {"策略编码","店铺名称", "SKU编码", "基价", "导入内容"};
            String[] keys = {"strategyCode","shopName", "skuCode", "basePrice", "importContent"};

            List<String> headerList = Arrays.asList(headers);
            List<String> keyList = Arrays.asList(keys);

            XSSFWorkbook workbook = new XSSFWorkbook();
            exportUtil.executeSheet(workbook, "一件代发客户基价策略", "", headerList, keyList, exportData, false);

            String ossUrl = exportUtil.saveFileAndPutOss(workbook, "一件代发客户基价策略导出", user, "OSS-Bucket/EXPORT/StCDropshipBasePriceStrategy/");

            if (org.apache.commons.lang3.StringUtils.isEmpty(ossUrl)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "上传OSS失败");
            }

            log.info("导出一件代发客户基价策略成功，共{}条记录，OSS地址={}，操作人={}",
                    exportData.size(), ossUrl, user.getName());

            return new ValueHolderV14<>(ossUrl, ResultCode.SUCCESS, "导出成功");

        } catch (Exception e) {
            log.error("导出一件代发客户基价策略失败", e);
            return new ValueHolderV14<>(ResultCode.FAIL, "导出失败：" + e.getMessage());
        }
    }
}
