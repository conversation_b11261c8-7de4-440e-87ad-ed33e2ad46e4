package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcSaveChangingOrRefundingCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 李杰
 * @since: 2019/3/13
 * create at : 2019/3/13 11:33
 */
@Api(value = "OcSaveChangeingOrRefundingCtrl", tags = "退换货订单新增")
@Slf4j
@RestController
public class OcSaveChangeingOrRefundingCtrl {

    @Autowired
    private OcSaveChangingOrRefundingCmd ocSaveChangingOrRefundingCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "退换货订单")
    @RequestMapping(value = "api/cs/oc/oms/v1/returnOrder", method = RequestMethod.POST)
    public JSONObject saveChangingOrRefunding(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolder vh = new ValueHolder();
        //获取用户信息
        // User user = SystemUserResource.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = ocSaveChangingOrRefundingCmd.saveReturnOrder(obj, user);
        } catch (Exception ex) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(ex.getMessage()));
        }
        return vh.toJSONObject();
    }


    @ApiOperation(value = "新增预退货订单明细")
    @RequestMapping(value = "api/cs/oc/oms/v1/addReturnOrderItem", method = RequestMethod.POST)
    public JSONObject addReturnOrderItem(HttpServletRequest request, @RequestBody JSONObject obj) {
//        OcSaveChangeingOrRefundingCtrl saveBil = new OcSaveChangeingOrRefundingCtrl();
        ValueHolderV14 vh = new ValueHolderV14();
       //获取用户信息
//        User user = saveBil.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            vh = ocSaveChangingOrRefundingCmd.addReturnOrderItem(obj, user);

        } catch (Exception ex) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(ex.getMessage());
        }
        return vh.toJSONObject();
    }


    @ApiOperation(value = "新增预退货订单明细")
    @RequestMapping(value = "api/cs/oc/oms/v1/deleteReturnOrderItem", method = RequestMethod.POST)
    public JSONObject deleteReturnOrderItem(HttpServletRequest request, @RequestBody JSONObject obj) {
//        OcSaveChangeingOrRefundingCtrl saveBil = new OcSaveChangeingOrRefundingCtrl();
        ValueHolderV14 vh = new ValueHolderV14();
        //获取用户信息
//        User user = saveBil.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            vh = ocSaveChangingOrRefundingCmd.deleteReturnOrderItem(obj, user);

        } catch (Exception ex) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(ex.getMessage());
        }
        return vh.toJSONObject();
    }

    /**
     * 检查换货库存 接口
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "换货新增检查库存")
    @RequestMapping(value = "api/cs/oc/oms/v1/checkAllStroreStock", method = RequestMethod.POST)
    public JSONObject checkAllStroreStock(HttpServletRequest request, @RequestBody JSONObject obj) {

        ValueHolderV14 vh = new ValueHolderV14();
        //获取用户信息
        //User user = SystemUserResource.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            vh = ocSaveChangingOrRefundingCmd.checkAllStroreStock(obj, user);

        } catch (Exception ex) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(ex.getMessage()));
        }
        return vh.toJSONObject();
    }
//    @ApiOperation(value = "换货确认服务")
//    @RequestMapping(value = "api/cs/oc/oms/v1/exchangeIn", method = RequestMethod.POST)
//    public JSONObject exchangeIn(HttpServletRequest request, @Param("id") Long id, @Param("sellerNick")String sellerNick) {
//        OcSaveChangeingOrRefundingCtrl saveBil = new OcSaveChangeingOrRefundingCtrl();
//        ValueHolderV14 vh = new ValueHolderV14();
//        //记录日志信息
//        log.debug("Start OcSaveChangeingOrRefundingCtrl.saveChangingOrRefunding. ReceiveParams=;");
//        //获取用户信息
//        User user = saveBil.getRootUser();
////        User user =r3PrimWebAuthService.getLoginPrimWebUser(request);
//
//        try {
//            vh = exchangeInCmd.exchangeIn(id, user);
//
//        } catch (Exception ex) {
//            vh.setCode(ResultCode.FAIL);
//            vh.setMessage(Resources.getMessage(ex.getMessage()));
//        }
//
//        //记录返回信息
//        log.debug("Finish OcSaveChangeingOrRefundingCtrl.saveChangingOrRefunding. ReturnResult=" + vh.toJSONObject());
//        return vh.toJSONObject();
//    }

}
