package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.gson.JsonArray;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.ChargebackCheckCmd;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: 周琳胜
 * @since: 2019/3/25
 * create at : 2019/3/25 19:40
 */
@Api(value = "ChargebackCheckCtrl", tags = "退单审核按钮")
@Slf4j
@RestController
public class ChargebackCheckCtrl {
    @Autowired
    private ChargebackCheckCmd chargebackCheckCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;


    @ApiOperation(value = "退单审核按钮")
    @RequestMapping(value = "/api/cs/oc/oms/v1/chargebackcheck", method = RequestMethod.POST)
    public JSONObject markRefund(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            vh = chargebackCheckCmd.execute(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }


    @ApiOperation(value = "退单同步通用平台状态")
    @RequestMapping(value = "/api/cs/oc/oms/v1/syncPlatformRefundStatus", method = RequestMethod.POST)
    public JSONObject syncPlatformRefundStatus(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            AssertUtil.notNull(obj, " 参数为空");
            JSONArray jsonArray = obj.getJSONArray("ID");
            AssertUtil.assertException(jsonArray == null || jsonArray.size()<1, "请选择需要操作的数据！");
            List<Long> idList = new ArrayList<>();
            for(int i=0;i<jsonArray.size();i++){
                idList.add(jsonArray.getLongValue(i));
            }
            vh = chargebackCheckCmd.syncPlatformRefundStatus(idList, user);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

}
