package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBOrderDetailPermissionCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * oms定制页面订单权限查询
 *
 * @author: xiWen.z
 * create at: 2019/8/28 0028
 */
@RestController
@Slf4j
@Api(value = "OcBOrderPermissionQueryCtrl", tags = "oms定制页面订单权限查询")
public class OcBOrderPermissionQueryCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OcBOrderDetailPermissionCmd ocBorderDetailPmsCmd;

    /**
     * @param request HttpServletRequest
     * @param jsnObj  string. JSONObject
     * @return JSONObject
     * @throws Exception exp
     */
    @ApiOperation(value = "订单单对象权限查询")
    @RequestMapping(path = "/api/cs/oc/oms/v1/getSingleObjectPermission", method = RequestMethod.POST)
    public JSONObject getSingleOrderPerm(HttpServletRequest request, @RequestBody JSONObject jsnObj) throws Exception {

        User usr = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            String table = jsnObj.getString("table");
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(usr, "OC_B_ORDER");

            if (usrPem == null) {
                vh = new ValueHolderV14<>();
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }


            vh = ocBorderDetailPmsCmd.getOrderDetailPermission(table, usr, usrPem);

        } catch (Exception e) {
            vh.setMessage(e.getMessage());
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }
        return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
    }

}
