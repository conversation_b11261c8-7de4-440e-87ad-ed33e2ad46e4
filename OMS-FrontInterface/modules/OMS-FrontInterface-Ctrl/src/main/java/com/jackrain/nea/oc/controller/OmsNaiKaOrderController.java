package com.jackrain.nea.oc.controller;

import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.MarkRefundCmd;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaOrderVoidService;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaReturnDetailService;
import com.jackrain.nea.oc.oms.services.naika.TestService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName OmsNaiKaOrderController
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/25 19:52
 * @Version 1.0
 */
@RestController
@Slf4j
public class OmsNaiKaOrderController {


    @Autowired
    private OmsNaiKaReturnDetailService omsNaiKaReturnDetailService;
    @Autowired
    private OmsNaiKaOrderVoidService omsNaiKaOrderVoidService;
    @Autowired
    private TestService testService;
    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private MarkRefundCmd markRefundCmd;

    @GetMapping("/api/cs/oc/oms/naika/naiKaOrderCancel")
    @Deprecated
    public void naiKaOrderCancel(@RequestParam("tid") String tid) {
        markRefundCmd.naiKaOrderCancel(tid);
    }

    @GetMapping("/api/cs/oc/oms/naika/test")
    @Deprecated
    public ValueHolder test(@RequestParam("id") Long id) {
        return testService.test(id);
    }

    @GetMapping("/api/cs/oc/oms/naika/void")
    @Deprecated
    public void orderVoid(HttpServletRequest request, @RequestParam("id") Long id, @RequestParam("returnId") Long returnId) {
        User rootUser = SystemUserResource.getRootUser();
        testService.orderVoid(id, returnId, rootUser);
    }

    @GetMapping("/api/cs/oc/oms/naika/return/detail")
    public ValueHolder getNaiKaReturn(@RequestParam("id") Long id) {
        return omsNaiKaReturnDetailService.getNaiKaReturn(id);
    }

    /**
     * 奶卡管理-退 详情页作废
     *
     * @param request
     * @param cardId
     * @param id
     * @return
     */
    @GetMapping("/api/cs/oc/oms/naika/order/void")
    public ValueHolder naiKaVoid(HttpServletRequest request, @RequestParam("cardId") String cardId, @RequestParam("ID") Long id) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        String[] cardArr = cardId.split(",");
        List<Long> cardList = new ArrayList<>();
        for (String card : cardArr) {
            cardList.add(Long.valueOf(card));
        }
        return omsNaiKaOrderVoidService.naiKaOrderVoid(cardList, id, user);
    }
}
