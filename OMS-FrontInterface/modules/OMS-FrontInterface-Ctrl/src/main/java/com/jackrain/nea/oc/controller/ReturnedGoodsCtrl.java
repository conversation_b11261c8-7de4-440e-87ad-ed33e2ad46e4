package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.DynamicListCmd;
import com.jackrain.nea.oc.oms.api.InvalidButton;
import com.jackrain.nea.oc.oms.api.OcCancelChangingOrRefundCmd;
import com.jackrain.nea.oc.oms.api.OcRefundInExportCmd;
import com.jackrain.nea.oc.oms.api.ReturnCancelCmd;
import com.jackrain.nea.oc.oms.api.ReturnStorageListCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 夏继超
 * @since: 2019/3/21
 * create at : 2019/3/21 13:10
 */
@RestController
@Slf4j
@Api(value = "ReturnedGoodsCtrl", tags = "退换货相关操作")
public class ReturnedGoodsCtrl {

    @Autowired
    OcCancelChangingOrRefundCmd orRefundCmd;

    @Autowired
    ReturnCancelCmd cancelCmd;

    @Autowired
    private InvalidButton invalidButton;

    @Autowired
    ReturnStorageListCmd listCmd;

    @Autowired
    OcRefundInExportCmd ocRefundInExportCmd;

    @Autowired
    DynamicListCmd dynamicListCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    /**
     * 列表导出
     *
     * @param request req
     * @param param   string
     * @return string str
     * @throws Exception ex
     */
    @ApiOperation(value = "列表导出")
    @RequestMapping(path = "/api/cs/oc/oms/v1/exportOcBRefundIn", method = RequestMethod.POST)
    public ValueHolderV14 getOrderList(HttpServletRequest request, @RequestBody JSONObject param) {

        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        String s = param.toJSONString();
        ValueHolderV14 vh;
    /*    UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_REFUND_IN");
        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh;
        }*/

        ValueHolderV14 holderV14 = ocRefundInExportCmd.exportRefundIn(s, user);
        return holderV14;
    }

    /**
     * 退换货取消按钮
     *
     * @param request 请求参数
     * @param obj     传入的参数
     * @return 返回的类型
     * @throws Exception
     */
    @ApiOperation(value = "退换货取消服务")
    @RequestMapping(path = "/api/cs/oc/oms/v1/OcCancelChangingOrRefund", method = RequestMethod.POST)
    public JSONObject ocCancelChangingOrRefund(HttpServletRequest request, @RequestBody JSONObject obj) throws Exception {

        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = orRefundCmd.ocCancelChangingOrRefund(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    @ApiOperation(value = "取消退换货和零售发货参数状态校验服务")
    @RequestMapping(path = "/api/cs/oc/oms/v1/checkCancelParams", method = RequestMethod.POST)
    public JSONObject cancelReturnOrder(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14 vh = new ValueHolderV14<>();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            vh = orRefundCmd.checkCancelReqParams(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        } catch (Exception ex) {
            String message = Resources.getMessage(ex.getMessage());
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(message);
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 退换货作废按钮
     *
     * @param request 请求参数
     * @param param   传入的参数
     * @return
     */
    @ApiOperation(value = "退换货作废按钮")
    @RequestMapping(path = "/api/cs/oc/oms/v1/returnCancel", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject distributorFilesDelete(HttpServletRequest request, @RequestBody JSONObject param) {

        ValueHolder vh = new ValueHolder();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolder valueHolder = invalidButton.returnCancel(param, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 动态列表页面
     *
     * @param request
     * @param param
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "动态列表页面")
    @RequestMapping(path = "/api/cs/oc/oms/v1/DynamicList", method = RequestMethod.POST)
    public JSONObject returnDynamicList(HttpServletRequest request, @RequestBody String param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            Long bengin = System.currentTimeMillis();
            JSONObject jsonObject = JSONObject.parseObject(param);
            String tn = jsonObject.getString("table");
            UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, tn);
            if (usrPem == null) {
                vh.setMessage("未获取到用户权限");
                vh.setCode(ResultCode.FAIL);
                return vh.toJSONObject();
            }

            ValueHolderV14 valueHolder = dynamicListCmd.dynamicList(param, user, usrPem);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));

        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 退货入库取列表
     *
     * @param request 请求参数
     * @param param   传入的参数
     * @return 返回的类型
     * @throws Exception
     */
    @ApiOperation(value = "退货入库单列表界面")
    @RequestMapping(path = "/api/cs/oc/oms/v1/ReturnStorageList", method = RequestMethod.POST)
    public JSONObject returnStorageList(HttpServletRequest request, @RequestBody String param) throws Exception {

        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            ValueHolderV14 valueHolder = listCmd.returnStorageList(param, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 退货入库单新增编辑服务
     *
     * @param request 请求参数
     * @param obj     传入的参数
     * @return 返回的类型
     * @throws Exception
     */
    @ApiOperation(value = "退货入库单新增编辑服务")
    @RequestMapping(path = "/api/cs/oc/oms/v1/ReturnStorageSave", method = RequestMethod.POST)
    public JSONObject returnStorageSave(HttpServletRequest request, @RequestBody JSONObject obj) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.returnStorageSave(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 手工匹配服务前校验
     *
     * @param request 请求参数
     * @param obj     传入的参数
     * @return 返回的类型
     * @throws Exception
     */
    @ApiOperation(value = "手工匹配服务前校验")
    @RequestMapping(path = "/api/cs/oc/oms/v1/manualMatchingCheck", method = RequestMethod.POST)
    public JSONObject manualMatchingCheck(HttpServletRequest request, @RequestBody JSONObject obj) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.manualMatchingCheck(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 手工匹配列表服务
     *
     * @param request 请求参数
     * @param obj     传入的参数
     * @return 返回的类型
     * @throws Exception
     */
    @ApiOperation(value = "手工匹配列表服务")
    @RequestMapping(path = "/api/cs/oc/oms/v1/manualMatchingList", method = RequestMethod.POST)
    public JSONObject manualMatchingList(HttpServletRequest request, @RequestBody JSONObject obj) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.manualMatchingList(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 手工匹配明细中的搜索按钮
     *
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "手工匹配明细表按钮")
    @RequestMapping(path = "/api/cs/oc/oms/v1/searchButtonsInDetail", method = RequestMethod.POST)
    public JSONObject searchButtonsInDetail(HttpServletRequest request, @RequestBody String param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.searchButtonsInDetail(param, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            //return vh.toJSONObject();
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 手工匹配中弹出确定按钮服务
     *
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "手工匹配中弹出确定按钮服务")
    @RequestMapping(path = "/api/cs/oc/oms/v1/manualMatchingConfirmationButton", method = RequestMethod.POST)
    public JSONObject manualMatchingConfirmationButton(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.manualMatchingConfirmationButton(param, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 手工匹配中列表确定按钮服务
     *
     * @param request
     * @param param
     * @return
     */
    @ApiOperation(value = "手工匹配中列表确定按钮服务")
    @RequestMapping(path = "/api/cs/oc/oms/v1/markSureButton", method = RequestMethod.POST)
    public JSONObject markSureButton(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.markSureButton(param, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 新增高级搜索确定按钮服务
     *
     * @param request
     * @param param   传入的参数
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "新增高级搜索确定按钮服务")
    @RequestMapping(path = "/api/cs/oc/oms/v1/saveButton", method = RequestMethod.POST)
    public JSONObject saveButton(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.saveButton(param, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
            //return valueHolder.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            //return vh.toJSONObject();
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 强制匹配服务
     *
     * @param request
     * @param param   传入的参数
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "强制匹配服务")
    @RequestMapping(path = "/api/cs/oc/oms/v1/forcedMatching", method = RequestMethod.POST)
    public JSONObject forcedMatching(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.forcedMatching(param, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 强制匹配搜索列表确定按钮
     *
     * @param request
     * @param param   传入的参数
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "强制匹配搜索列表确定按钮")
    @RequestMapping(path = "/api/cs/oc/oms/v1/seachForced", method = RequestMethod.POST)
    public JSONObject searchForced(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.seachForced(param, user);
            //记录日志信息。Finish 标记结束
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            log.error("强制匹配出错", e);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 强制完成按钮
     *
     * @param request
     * @param param   传入的参数
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "强制完成按钮")
    @RequestMapping(path = "/api/cs/oc/oms/v1/forcedCompletion", method = RequestMethod.POST)
    public JSONObject forcedCompletion(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.forcedCompletion(param, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));

        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 确认按钮
     *
     * @param request
     * @param param   传入的参数
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "确认按钮二次确认")
    @RequestMapping(path = "/api/cs/oc/oms/v1/returnOrderSecondaryVerify", method = RequestMethod.POST)
    public JSONObject returnOrderSecondaryVerify(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.returnOrderSecondaryVerify(param);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));

        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 确认按钮
     *
     * @param request
     * @param param   传入的参数
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "确认按钮")
    @RequestMapping(path = "/api/cs/oc/oms/v1/returnConfirm", method = RequestMethod.POST)
    public JSONObject returnOrderConfirm(HttpServletRequest request, @RequestBody JSONObject param) throws Exception {
        ValueHolderV14 vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            ValueHolderV14 valueHolder = listCmd.returnOrderConfirm(param, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));

        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    @ApiOperation(value = "手工匹配服务前校验")
    @RequestMapping(path = "/api/cs/oc/oms/v1/nameless/cutInLine", method = RequestMethod.POST)
    public JSONObject cutInLine(HttpServletRequest request, @RequestBody JSONObject obj) throws Exception {
        return listCmd.cutInLine(obj, r3PrimWebAuthService.getLoginPrimWebUser(request)).toJSONObject();
    }

}
