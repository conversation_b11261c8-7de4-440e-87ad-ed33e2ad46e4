package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.core.schema.Table;
import com.jackrain.nea.core.schema.TableManager;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OmsSkuQueryCmd;
import com.jackrain.nea.ps.model.OmsProDelCmdRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.query.QuerySessionImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/6/30 下午2:44
 * @Version 1.0
 */
@RestController
@Api(description = "矩阵相关服务")
@Slf4j
public class OmsMatrixCorrelationCtrl {
    @Autowired
    private OmsSkuQueryCmd omsSkuQueryCmd;


    @ApiOperation(value = "获取矩阵内业务数据")
    @GetMapping(path = "api/cs/oc/oms/v1/getMatrixData")
    public JSONObject getmatrixdata(HttpServletRequest request,
                                    @RequestParam(value = "param", required = true) String param) throws Exception {
        QuerySessionImpl querySession = new QuerySessionImpl(request);
        //User user = getUser();
        //QuerySessionImpl querySession = new QuerySessionImpl(user);
        JSONObject data = JSON.parseObject(param);
        if (data == null) {
            throw new NDSException("参数异常,param为空！");
        }

        // 主表ID
        Long objId = data.getLong("objid");
        if (objId == null) {
            throw new NDSException("参数异常,objid为空!");
        }

        // 主表表名
        String tableName = data.getString("table");
        if (StringUtils.isEmpty(tableName)) {
            throw new NDSException("参数异常,table为空!");
        }

        JSONObject fixcolumn = data.getJSONObject("fixcolumn");
        String proEcode = fixcolumn.getString("PS_C_PRO_ECODE");
        //收货店仓
        Long destId = fixcolumn.getLong("CP_C_DEST_ID");
        String beginDate = fixcolumn.getString("RETAIL_BEGIN_TIME");
        String endDate = fixcolumn.getString("RETAIL_END_TIME");
        if (StringUtils.isEmpty(proEcode)) {
            throw new NDSException("参数异常,PS_C_PRO_ECODE为空!");
        }

        //获取实际表名
        TableManager manager = querySession.getTableManager();
        Table table = manager.getTable(tableName);

        if (table == null) {
            throw new NDSException("reference table is null");
        }
        if (table.isView()) {
            tableName = table.getRealTableName();
        }

        //分库键
        String depotsKey = tableName + "_ID";
        String itemTable = fixcolumn.getString("detailTableName");
        if (StringUtils.isEmpty(itemTable)) {
            itemTable = tableName + "_ITEM";
        }

        String center = table.getCategory().getSubSystem().getCenter();
        String[] gv = center.split(":");
        if (gv.length != 2) {
            throw new NDSException("center is error");
        }

        Table tableItem = manager.getTable(itemTable);
        if (tableItem == null) {
            throw new NDSException("明细表配置不存在!");
        }

        //明细表的 扩展属性
        JSONObject props = tableItem.getJSONProps();
        //颜色还是规则(查询的 字段 有变化)
        boolean isSpec1 = false;
        if (props != null) {
            isSpec1 = props.getBooleanValue("isSelectMatrixSpec");
        }

        if (log.isDebugEnabled()) {
            log.debug("isSelectMatrixSpec:" + isSpec1 + "tableName:" + tableName);
        }
        List<JSONObject> matrix = new ArrayList<>();

        ValueHolderV14 vh14;
        if (isSpec1) {
            vh14 = omsSkuQueryCmd.querySkuInfoSpec1(new OmsProDelCmdRequest(objId, itemTable.toLowerCase(), proEcode,
                    depotsKey), 0);
        } else {
            vh14 = omsSkuQueryCmd.querySkuInfo(new OmsProDelCmdRequest(objId, itemTable.toLowerCase(), proEcode, depotsKey,destId,beginDate,endDate)
                    , 0);
        }

        if (vh14 != null && vh14.getData() != null) {
            List<JSONObject> list = (List) vh14.getData();
            if (!CollectionUtils.isEmpty(list)) {
                for (JSONObject skuInfo : list) {
                    if ("SC_B_INVENTORY".equals(tableName.toUpperCase()) && skuInfo.get("STOREHOUSE") != null &&
                            fixcolumn.get("storehouse") != null && !skuInfo.getString("STOREHOUSE").equals(fixcolumn.getString("storehouse"))) {
                        continue;
                    }
                    JSONObject matrixData = new JSONObject();
                    matrixData.put("QTY", skuInfo.getLong("QTY")==null?0:skuInfo.get("QTY"));
                    matrixData.put("QTY_AVAILABLE", skuInfo.get("QTY_AVAILABLE")==null?0:skuInfo.get("QTY_AVAILABLE"));
/*                    matrixData.put("DEST_QTY_AVAILABLE", skuInfo.get("DEST_QTY_AVAILABLE")==null?0:skuInfo.get("DEST_QTY_AVAILABLE"));
                    matrixData.put("DEST_QTY_PREIN", skuInfo.get("DEST_QTY_PREIN")==null?0:skuInfo.get("DEST_QTY_PREIN"));
                   */
                    matrixData.put("DEST_QTY_RETAIL", skuInfo.get("DEST_QTY_RETAIL")==null?0:skuInfo.get("DEST_QTY_RETAIL"));
                    skuInfo.put("MATRIX_DATA", matrixData);
                    matrix.add(skuInfo);
                }
            }
        }
        JSONObject result = new JSONObject();
        result.put("code", 0);
        result.put("message", "success!");
        result.put("data", matrix);
        return result;
    }
}
