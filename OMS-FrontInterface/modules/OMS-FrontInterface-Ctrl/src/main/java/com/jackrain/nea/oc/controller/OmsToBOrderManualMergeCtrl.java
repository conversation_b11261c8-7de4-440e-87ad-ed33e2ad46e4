package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OmsToBOrderManualMergeCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/10/24 下午1:49
 * @Version 1.0
 */@Api(value = "OmsExpiryDateCtrl", tags = "TOB订单手工合并")
@Slf4j
@RestController
public class OmsToBOrderManualMergeCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private OmsToBOrderManualMergeCmd omsToBOrderManualMergeCmd;

    @RequestMapping("api/cs/oc/oms/v1/toBOrderManualMerge")
    public ValueHolderV14 toBOrderManualMerge(HttpServletRequest request,
                                                   @RequestBody JSONObject obj){
        ValueHolderV14 holder = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(-1);
            holder.setMessage("用户不能为空!");
            return holder;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null) {
            holder.setCode(-1);
            holder.setMessage("参数有误");
            return holder;
        }
        List<Long> longs = JSON.parseArray(ids.toJSONString(), Long.class);
        return omsToBOrderManualMergeCmd.orderManualMergeService(longs, user);
    }
}
