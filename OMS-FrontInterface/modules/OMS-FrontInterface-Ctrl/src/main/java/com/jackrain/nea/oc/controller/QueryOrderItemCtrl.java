package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.QueryOrderItemCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Api(value = "QueryOrderItemCtrl", tags = "查询订单、明细信息")
@Slf4j
@RestController
public class QueryOrderItemCtrl {

    @Autowired
    private QueryOrderItemCmd queryOrderItemCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;


    /**
     * @param request 请求
     * @param obj     参数
     * @param
     * @return return
     */

    @ApiOperation(value = "获取订单、明细信息")
    @RequestMapping(value = "/api/cs/oc/oms/v1/queryOrderItem", method = RequestMethod.POST)
    public JSONObject queryOrderItem(HttpServletRequest request,
                                     @RequestBody JSONObject obj) {
        ValueHolder vh;
        //获取当前登陆用户
        //User user = SystemUserResource.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
        }
        try {

            vh = queryOrderItemCmd.queryOrderItem(obj, user);
            JSONObject result = JSONObject.parseObject(JSONObject.toJSONString(
                    vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
            return result;
        } catch (NDSException e) {
            vh = new ValueHolder();
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }

    }


}

