package com.jackrain.nea.oc.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.schema.Table;
import com.jackrain.nea.core.schema.TableManager;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.QueryTabthCmd;
import com.jackrain.nea.oc.oms.api.SaveUserQueryListCmd;
import com.jackrain.nea.oc.oms.api.UserQueryListCmd;
import com.jackrain.nea.oc.oms.model.relation.UserConfig;
import com.jackrain.nea.oc.oms.model.request.SaveUserConfigRequest;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;


@Slf4j
@RestController
@Api(value = "queryListController", tags = "列表查询数据rest")
public class UserQueryListController {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private QueryTabthCmd queryTabthCmd;

    @Autowired
    private UserQueryListCmd userQueryListCmd;

    @Autowired
    private SaveUserQueryListCmd saveUserQueryListCmd;

    private User getRootUser(HttpServletRequest request) {
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        return user;
    }


    @RequestMapping(path = "/api/cs/oc/oms/v1/getTabth", method = {RequestMethod.GET, RequestMethod.POST})
    public JSONObject getTabth(HttpServletRequest request,
                               @RequestParam(value = "param", required = true) String obj) throws Exception {
        UserImpl userWeb = (UserImpl) getRootUser(request);

        JSONObject jo = JSON.parseObject(obj);
        QuerySession qsession = QueryUtils.createQuerySession(userWeb);
        TableManager tm = qsession.getTableManager();

        Table table = null;

        int tableid = jo.getIntValue("tableid");
        int refcolid = jo.getIntValue("refcolid");

        if (refcolid > 0) {
            table = tm.getColumn(refcolid).getReferenceTable();
        }

        if (tableid > 0 && table == null) {
            table = tm.getTable(tableid);
        }

        if (jo.getString("table") != null && table == null) {
            table = tm.getTable(jo.getString("table"));
        }

        if (table == null) {
            throw new NDSException("table is null");
        }

        String center = table.getCategory().getSubSystem().getCenter();
        String[] gv = center.split(":");
        if (gv.length != 2) {
            throw new NDSException("center is error");
        }
        ValueHolder result = new ValueHolder();
        try {

            result = queryTabthCmd.queryTabth(obj, userWeb);
            //记录日志信息。Finish 标记结束
        } catch (NDSException e) {
            result.put("code", ResultCode.FAIL);
            result.put("message", Resources.getMessage(e.getMessage()));
        }
        return result.toJSONObject();

    }

    @RequestMapping(path = "/api/cs/oc/oms/v1/queryListConfig", method = {RequestMethod.GET, RequestMethod.POST})
    public JSONObject queryListConfig(HttpServletRequest request,
                                      @RequestParam(value = "param", required = true) String obj) throws Exception {

        UserImpl userWeb = (UserImpl) getRootUser(request);

        JSONObject jo = JSON.parseObject(obj);
        QuerySession qsession = QueryUtils.createQuerySession(userWeb);
        TableManager tm = qsession.getTableManager();

        Table table = null;

        int tableid = jo.getIntValue("tableid");
        int refcolid = jo.getIntValue("refcolid");

        if (refcolid > 0) {
            table = tm.getColumn(refcolid).getReferenceTable();
        }

        if (tableid > 0 && table == null) {
            table = tm.getTable(tableid);
        }

        if (jo.getString("table") != null && table == null) {
            table = tm.getTable(jo.getString("table"));
        }

        if (table == null) {
            throw new NDSException("table is null");
        }

        String center = table.getCategory().getSubSystem().getCenter();
        String[] gv = center.split(":");
        if (gv.length != 2) {
            throw new NDSException("center is error");
        }
        ValueHolder result = new ValueHolder();
        try {

            result = userQueryListCmd.getTableQuery(obj, userWeb);
            //记录日志信息。Finish 标记结束
        } catch (NDSException e) {
            result.put("code", ResultCode.FAIL);
            result.put("message", Resources.getMessage(e.getMessage()));
        }
        return result.toJSONObject();

    }


    @RequestMapping(path = "/api/cs/oc/oms/v1/saveQueryListConfig", method = {RequestMethod.GET, RequestMethod.POST})
    public JSONObject saveQueryListConfig(HttpServletRequest request,
                                          @RequestBody SaveUserConfigRequest saveUserConfigRequest) throws Exception {
        if (saveUserConfigRequest == null) {
            throw new NDSException("param is null");
        }
        if (StringUtils.isEmpty(saveUserConfigRequest.getTableName())) {
            throw new NDSException("tableName is null");
        }
        if (CollectionUtils.isEmpty(saveUserConfigRequest.getUseronfigList())) {
            throw new NDSException("user config is null");
        }
        String tableName = saveUserConfigRequest.getTableName();
        List<UserConfig> userConfigList = saveUserConfigRequest.getUseronfigList();
        ValueHolder vh = new ValueHolder();
        HashMap<String, StringBuffer> data = new HashMap<>();
        for (int i = 0; i < userConfigList.size(); i++) {
            UserConfig userConfig = userConfigList.get(i);
            String orderno = userConfig.getOrderno();
            String colname = userConfig.getColname();
            Boolean isfilter = userConfig.getIsfilter();
            StringBuffer stringBuffer = new StringBuffer();

            if (StringUtils.isEmpty(orderno)) {
                stringBuffer.append("orderno is null");
            }

            if (StringUtils.isEmpty(colname)) {
                stringBuffer.append("colname is null");
            }

            if (isfilter == null) {
                stringBuffer.append("isfilter is null");
            }

            if (StringUtils.isNotEmpty(stringBuffer.toString())) {
                data.put("第　" + i + "", stringBuffer);
            }
        }
        UserImpl userWeb = (UserImpl) getRootUser(request);
        if (MapUtils.isEmpty(data)) {
            try {

                vh = saveUserQueryListCmd.saveTableQuery(tableName, userConfigList, userWeb);
                //记录日志信息。Finish 标记结束
                return vh.toJSONObject();
            } catch (NDSException e) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", Resources.getMessage(e.getMessage()));
                return vh.toJSONObject();
            }
        } else {
            vh.put("code", -1);
            vh.put("message", data);
            return vh.toJSONObject();
        }

    }
}
