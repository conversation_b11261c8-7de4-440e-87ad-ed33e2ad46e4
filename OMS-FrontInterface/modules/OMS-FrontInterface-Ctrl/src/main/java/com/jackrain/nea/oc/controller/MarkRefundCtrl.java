package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.MarkRefundCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: 周琳胜
 * @since: 2019/3/11
 * create at : 2019/3/11 19:40
 */
@Api(value = "MarkRefundCtrl", tags = "标记退款完成按钮")
@Slf4j
@RestController
public class MarkRefundCtrl {

    @Autowired
    private MarkRefundCmd markRefundCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "标记退款完成")
    @PostMapping(value = "/api/cs/oc/oms/v1/markrefund")
    public JSONObject markRefund(HttpServletRequest request, @RequestBody JSONObject obj) {
        //记录日志信息
        ValueHolderV14 vh = new ValueHolderV14();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {

            vh = markRefundCmd.execute(obj, user);
            //记录日志信息。Finish 标记结束
            return JSON.parseObject(JSON.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

    @ApiOperation(value = "标记退款取消")
    @PostMapping(value = "/api/cs/oc/oms/v1/markRefundCancel")
    public ValueHolder markRefundCancel(HttpServletRequest request, @RequestBody JSONObject obj) {
        //记录日志信息
        ValueHolder vh = new ValueHolder();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            //明细ids
            Long orderId = obj.getLong("orderId");
            //转成数组
            String ids = obj.getString("itemIds");
            if (orderId == null || StringUtils.isEmpty(ids)) {
                throw new NDSException("参数不正确！");
            }
            String[] idArray = ids.split(",");
            List<Long> itemIds = new ArrayList();
            for (String itemId : idArray) {
                itemIds.add(Long.valueOf(itemId));
            }
            if (CollectionUtils.isEmpty(itemIds)) {
                throw new NDSException("参数不正确！");
            }
            return markRefundCmd.markRefundCancel(orderId, itemIds, user);
        } catch (NDSException e) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", e.getMessage());
            return vh;
        }
    }
}
