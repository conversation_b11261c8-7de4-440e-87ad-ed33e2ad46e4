package com.jackrain.nea.oc.controller;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OcBOrderPermissionCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;

/**
 * Get User Permission
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/7/19
 */
@Slf4j
public class UserPermissionCtrlHelper {

    /**
     * get current login user permission
     *
     * @param loginUser current login user
     * @param tableName appoint table name
     * @return base permission, sensitive column
     */
    public static UserPermission currentUserPermission(User loginUser, String tableName) {

        UserPermission usrPem = null;
        try {

            OcBOrderPermissionCmd ocBOrderPermissionCmd = ApplicationContextHandle.getBean(OcBOrderPermissionCmd.class);

            usrPem = ocBOrderPermissionCmd.getCurrentUserPermission(null, tableName, loginUser);

        } catch (Exception e) {
            log.error(LogUtil.format("UserPermissionHelper.currentUserPermission.Exp: TableName-> {}"), Throwables.getStackTraceAsString(e));
        }
        return usrPem;
    }

    /**
     * get permission and return result
     *
     * @param loginUser current login user
     * @param tableName tableName belongs to R3 Config
     * @return result
     */
    public static ValueHolderV14 result4GetPermission(User loginUser, String tableName) {

        UserPermission userPermission = currentUserPermission(loginUser, tableName);
        ValueHolderV14 vh = new ValueHolderV14<>();
        if (userPermission == null) {
            vh.setMessage(Resources.getMessage("未获取到用户权限", loginUser.getLocale()));
            vh.setCode(ResultCode.FAIL);
            return vh;
        }
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(userPermission);
        return vh;
    }
}
