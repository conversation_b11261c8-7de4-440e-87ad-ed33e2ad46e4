package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.ZtoLogisticsInterceptCmd;
import com.jackrain.nea.oc.oms.model.request.ZtoLogisticsInterceptTaskRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.ZtoLogisticsInterceptService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/3/2 17:05
 * @Description
 */
@Api(value = "ZtoLogisticsInterceptCtrl", tags = "中通物流拦截")
@Slf4j
@RestController
public class ZtoLogisticsInterceptCtrl {

    @Resource
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Resource
    private ZtoLogisticsInterceptCmd ztoLogisticsInterceptCmd;

    @Resource
    private ZtoLogisticsInterceptService ztoLogisticsInterceptService;

    @GetMapping("/testTask")
    public ValueHolderV14<Void> testTask() {
        ZtoLogisticsInterceptTaskRequest request = new ZtoLogisticsInterceptTaskRequest();
        return ztoLogisticsInterceptService.interceptCreateForFail(request);
    }

    @PostMapping("api/cs/oc/oms/v1/zto/intercept/check")
    public ValueHolderV14<List<OcBOrder>> interceptCheck(@RequestBody JSONObject obj) {
        ValueHolderV14<List<OcBOrder>> holder = new ValueHolderV14<>();
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请选择需要发起包裹拦截退回的单据！");
            return holder;
        }
        List<Long> orderIds = JSON.parseArray(ids.toJSONString(), Long.class);
        return ztoLogisticsInterceptCmd.interceptCheck(orderIds);
    }

    @PostMapping("api/cs/oc/oms/v1/zto/intercept/create")
    public ValueHolderV14<Void> interceptCreate(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14<Void> holder = new ValueHolderV14<>();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (user == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("获取用户信息失败！");
            return holder;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请选择需要发起包裹拦截退回的单据！");
            return holder;
        }
        String interceptReason = obj.getString("interceptReason");
        if (StringUtils.isEmpty(interceptReason)) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("拦截退回原因不能为空！");
            return holder;
        }
        List<Long> orderIds = JSON.parseArray(ids.toJSONString(), Long.class);
        return ztoLogisticsInterceptCmd.interceptCreate(orderIds, interceptReason, user, null);
    }

    @PostMapping("api/cs/oc/oms/v1/zto/intercept/cancel")
    public ValueHolderV14<Void> interceptCancel(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14<Void> holder = new ValueHolderV14<>();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
//        User user = getRootUser();
        if (user == null) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("获取用户信息失败！");
            return holder;
        }
        JSONArray ids = obj.getJSONArray("ids");
        if (ids == null || ids.size() < 1) {
            holder.setCode(ResultCode.FAIL);
            holder.setMessage("请选择需要发起包裹取消拦截退回的单据！");
            return holder;
        }
        List<Long> orderIds = JSON.parseArray(ids.toJSONString(), Long.class);
        return ztoLogisticsInterceptCmd.interceptCancel(orderIds, user);
    }

    /**
     * 测试用户
     */
    private User getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("测试用户");
        user.setEname("test");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }

}

