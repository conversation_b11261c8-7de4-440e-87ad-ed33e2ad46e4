package com.jackrain.nea.oc.controller.patrol;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.OmsOrderAddCmd;
import com.jackrain.nea.oc.oms.model.request.OmsOcBOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: 黄世新
 * @Date: 2019/11/14 3:50 下午
 * @Version 1.0
 */

@Api(value = "OmsOrderAddCtrl", tags = "订单新增")
@Slf4j
@RestController
public class OmsOrderAddCtrl {


    @Autowired
    private OmsOrderAddCmd omsOrderAddCmd;

    @ApiOperation(value = "POS订单新增")
    @RequestMapping(value = "/api/cs/oc/oms/v1/addOrderPos", method = RequestMethod.POST)
    public JSONObject omsOrderAdd(@RequestBody JSONObject obj) {
        OmsOcBOrderRequest omsOcBOrderRequest = JSONObject.toJavaObject(obj, OmsOcBOrderRequest.class);
        ValueHolderV14 valueHolderV14 = omsOrderAddCmd.omsOrderAdd(omsOcBOrderRequest);
        return valueHolderV14.toJSONObject();

    }
}
