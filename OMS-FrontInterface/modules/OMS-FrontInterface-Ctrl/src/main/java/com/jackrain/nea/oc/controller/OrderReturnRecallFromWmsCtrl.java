package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OrderReturnRecallFromWmsServiceCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * 退单从WMS撤回
 *
 * @author: ming.fz
 * @since: 2019/8/22
 */
@Slf4j
@RestController
public class OrderReturnRecallFromWmsCtrl {

    @Autowired
    private OrderReturnRecallFromWmsServiceCmd service;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @PostMapping("api/cs/oc/oms/v1/orderReturnRecallFromWms")
    public ValueHolderV14 orderReturnRecallFromWms(HttpServletRequest request, @RequestBody JSONObject obj) {

        ValueHolderV14 vh = new ValueHolderV14();

        //获取用户登录信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        if (obj == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不能为空!");
        }

        try {
            vh = service.OrderReturnRecallFromWms(obj, user);
            return vh;
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh;

        }
    }
}
