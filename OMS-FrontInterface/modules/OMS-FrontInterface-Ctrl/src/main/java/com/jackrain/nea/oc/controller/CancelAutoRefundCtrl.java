package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.CancelAutoRefundCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: 周琳胜
 * @since: 2019/3/25
 * create at : 2019/3/25 19:40
 */
@Api(value = "CancelAutoRefundCtrl", tags = "取消自动退款")
@Slf4j
@RestController
public class CancelAutoRefundCtrl {
    @Autowired
    private CancelAutoRefundCmd cancelAutoRefundCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "退单审核按钮")
    @RequestMapping(value = "/api/cs/oc/oms/v1/cancelautorefund", method = RequestMethod.POST)
    public JSONObject markRefund(HttpServletRequest request, @RequestBody JSONObject obj) {
        //记录日志信息
//        log.debug("start CancelAutoRefundCtrl.cancelautorefund.ReceiveParams=" + obj);
        ValueHolderV14 vh = new ValueHolderV14();
        //获取当前登陆用户
//        User user = Security4Utils.getUser("root");
        //CancelAutoRefundCtrl mr = new CancelAutoRefundCtrl();
        //UserImpl user = mr.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            vh = cancelAutoRefundCmd.execute(obj, user);
            //记录日志信息。Finish 标记结束
//            log.debug("Finish chargebackCheckCmd.chargebackCheckCmd. Return Result=" + vh.toJSONObject());
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(),
                    SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh = new ValueHolderV14();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

    /**
     * USER
     *
     * @return
     */
  /*  private UserImpl getRootUser() {
        UserImpl user = new UserImpl();
        user.setId(893);
        user.setName("admin");
        user.setEname("Pokemon-mapper");
        user.setActive(true);
        user.setClientId(37);
        user.setOrgId(27);
        user.setIsAdmin(2);
        user.setIsDev(2);
        return user;
    }*/
}
