package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderExportCmd;
import com.jackrain.nea.oc.oms.api.RefundInImportCmd;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.text.NumberFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-08-26 15:26
 */
@Slf4j
@RestController
public class RefundInImportCtrl {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";

    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;

    private static NumberFormat nf = NumberFormat.getInstance();


    @Autowired
    private RefundInImportCmd refundInImportCmd;

    @Autowired
    private OcBOrderExportCmd ocBOrderExportCmd;

    @Autowired
    private AsyncTaskManager asyncTaskManager;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;
    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    /**
     * 下载模板
     *
     * @return string str
     * @throws Exception ex
     */
    @RequestMapping(path = "api/cs/oc/oms/v1/downloadRdfundInRemarkTemp", method = RequestMethod.POST)
    public ValueHolderV14 downloadRdfundInRemarkTemp()
            throws Exception {

        //处理返回数据
        ValueHolderV14 holderV14 = ocBOrderExportCmd.downloadRefundInRemarkTemp();
        return holderV14;
    }

    /**
     * 退货入库单部分处理人 处理人备注导入
     *
     * @param request
     * @param file
     * @param cover
     * @return
     */
    @RequestMapping(value = "api/cs/oc/oms/v1/refundInImport", method = RequestMethod.POST)
    public ValueHolderV14 batchImport(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file, @RequestParam("cover") Boolean cover) {

        ValueHolderV14 result = new ValueHolderV14();
//        User user = new UserImpl();
//        ((UserImpl) user).setId(1);
//        ((UserImpl) user).setName("root");
//        ((UserImpl) user).setEname("root");
//        ((UserImpl) user).setOrgId(1);
//        ((UserImpl) user).setClientId(1);
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //插入我的任务里
        AsyncTaskBody asyncTaskBody =new AsyncTaskBody();
        asyncTaskBody.setTaskId(UUID.randomUUID().toString());
        asyncTaskBody.setMenu("退货入库导入");
        asyncTaskBody.setTaskType("导入");
        asyncTaskManager.beforeExecute(user, asyncTaskBody);
        return asyncImport(asyncTaskBody,user,file,cover);
    }

    private ValueHolderV14 asyncImport(AsyncTaskBody asyncTaskBody, User user, MultipartFile file, Boolean cover) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        Map<Object, Object> retMap = new LinkedHashMap<>();
        commonTaskExecutor.submit(() -> {

            if (file == null) {
                throw new NDSException(Resources.getMessage("请求参数不能为空!", user.getLocale()));
            }
            try {
                Map<String, InputStream> inputStreamMap = new HashMap<>();
                InputStream inputStream = file.getInputStream();
                inputStreamMap.put("inputStream", inputStream);
                Workbook hssfWorkbook = new XSSFWorkbook(inputStream);
                List<OcBRefundIn> ocBRefundInList = this.getRefundInList(hssfWorkbook);
                if (CollectionUtils.isEmpty(ocBRefundInList)) {
                    throw new NDSException(Resources.getMessage("模板数据不能为空！"));
                }
                ValueHolderV14 valueHolder = refundInImportCmd.batchImport(ocBRefundInList, cover, user);
                retMap.put("code", valueHolder.getCode());
                retMap.put("data", valueHolder.getData());
                retMap.put("message", valueHolder.getMessage());
                //任务完成
                asyncTaskBody.setUrl(String.valueOf(valueHolder.getData()));
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            } catch (Exception e) {
                log.error("导入错误" + e.getMessage());
                retMap.put("code", ResultCode.FAIL);
                retMap.put("message", "导入异常：" + e.getMessage());
                asyncTaskManager.afterExecute(user, asyncTaskBody, JSON.parseObject(JSON.toJSONString(retMap)));
            }
        });
        holderV14.setCode(ResultCode.SUCCESS);
        holderV14.setData(asyncTaskBody.getId());
        holderV14.setMessage(Resources.getMessage("零售发货单导入任务开始！详情请在我的任务查看"));
        return holderV14;
    }

    public List<OcBRefundIn> getRefundInList(Workbook hssfWorkbook) {
        try {
            List<OcBRefundIn> ocBRefundInList = new ArrayList<>();
            List<Map<String, String>> execlList = this.readExcel(0, hssfWorkbook);
            int index = 0;
            for (Map<String, String> columnMap : execlList) {
                OcBRefundIn ocBRefundIn = new OcBRefundIn();
                if (index == 0) {
                    // 校验excel字段
                    if (!"物流单号".equals(columnMap.get(rowStr + index + cellStr + 0))
                            || !"处理人".equals(columnMap.get(rowStr + index + cellStr + 1))
                            || !"处理人备注".equals(columnMap.get(rowStr + index + cellStr + 2))
                    ) {
                        log.debug("导入格式不正确，请参考模板");
//                        jsonObject.put("code",ResultCode.FAIL);
//                        jsonObject.put("message","导入备注格式不正确，请参考模板");
                        return null;
                    }
                } else {
                    if (StringUtils.isNotEmpty(columnMap.get(rowStr + index + cellStr + 0))
                    ) {
                        ocBRefundIn.setLogisticNumber(columnMap.get(rowStr + index + cellStr + 0));
                        ocBRefundIn.setHandler(columnMap.get(rowStr + index + cellStr + 1));
                        ocBRefundIn.setRemarkHandle(columnMap.get(rowStr + index + cellStr + 2));
                        ocBRefundInList.add(ocBRefundIn);
                    }
                }
                index++;
            }
            return ocBRefundInList;
        } catch (Exception e) {
        }
        return null;
    }


    /**
     * 导入方法，
     *
     * @param hssfWorkbook
     * @return
     * @throws Exception
     */
    public List<Map<String, String>> readExcel(Integer sheetIndex, Workbook hssfWorkbook) throws Exception {
        List list = Lists.newArrayList();
        Sheet hssfSheet = hssfWorkbook.getSheetAt(sheetIndex);
        if (Objects.isNull(hssfSheet)) {
            return Lists.newArrayList();
        }

        for (int rowNum = 0; rowNum <= hssfSheet.getLastRowNum(); rowNum++) {
            Map<String, String> map = Maps.newTreeMap();
            Row hssfRow = hssfSheet.getRow(rowNum);
            if (hssfRow != null) {
                Iterator<Cell> cellItr = hssfRow.cellIterator();
                while (cellItr.hasNext()) {
                    Cell cell = cellItr.next();
                    map.put(rowStr + cell.getRowIndex() + cellStr + cell.getColumnIndex(), getCellValue(cell));
                }
            }
            list.add(map);
        }
        return list;
    }

    private static String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;

            default:
                cellValue = cell.getStringCellValue();
        }

        return cellValue.trim();
    }
}