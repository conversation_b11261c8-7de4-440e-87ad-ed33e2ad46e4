package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderListQueryCmd;
import com.jackrain.nea.oc.oms.api.UpdateOrderInfoCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.oc.oms.model.result.QueryEsListResult;
import com.jackrain.nea.oc.oms.util.ExportUtil;
import com.jackrain.nea.oc.oms.vo.ExecuteErrorVO;
import com.jackrain.nea.oc.oms.vo.LargeQuantitiesOrderVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.netty.util.internal.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: 孙继东
 * @since: 2019-03-12
 * create at : 2019-03-12 9:25
 */

@Api(value = "UpdateOrderInfoCtrl", tags = "物流和仓库修改按钮")
@Slf4j
@RestController
public class UpdateOrderInfoCtrl {

    @Autowired
    private UpdateOrderInfoCmd updateOrderInfoCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private AsyncTaskManager asyncTaskManager;

    @Autowired
    private OcBOrderListQueryCmd ocBOrderListQueryCmd;

    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    @Autowired
    private ExportUtil exportUtil;

    /**
     * 批量操作数量
     */
    @NacosValue(value = "${r3.batch.handle.number:20000}", autoRefreshed = true)
    private long batchHandleNumber;

    /**
     * 修改物流
     *
     * @param request      获取用户
     * @param ids          订单id集合
     * @param cLogisticsId 物流公司id
     * @return 返回信息
     */
    @ApiOperation(value = "修改物流")
    @PostMapping("/api/cs/oc/oms/v1/updateLogistics")
    public JSONObject updateLogistics(HttpServletRequest request, @RequestParam(value = "ids") List<Long> ids, @RequestParam(value = "cLogisticsId") Long cLogisticsId) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh = new ValueHolderV14();
        try {

            vh = updateOrderInfoCmd.updateLogistics(ids, cLogisticsId, loginUser);

            //记录日志信息。Finish 标记结束
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    /**
     * 修改仓库
     *
     * @param request      获取用户信息
     * @param ids          订单id集合
     * @param warehouseId  仓库id
     * @param updateRemark 改仓原因
     * @return 返回信息
     */
    @ApiOperation(value = "修改仓库")
    @PostMapping("/api/cs/oc/oms/v1/updateWarehouse")
    public JSONObject updateWarehouse(HttpServletRequest request,
                                      @RequestParam(value = "ids") List<Long> ids,
                                      @RequestParam(value = "shareStoresId") Long shareStoresId,
                                      @RequestParam(value = "shareStoresEcode") String shareStoresEcode,
                                      @RequestParam(value = "warehouseId") Long warehouseId,
                                      @RequestParam(value = "warehouseEcode") String warehouseEcode,
                                      @RequestParam(value = "updateRemark") String updateRemark) {
        //记录日志信息
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = updateOrderInfoCmd.updateWarehouse(ids, shareStoresId, shareStoresEcode, warehouseId, warehouseEcode, updateRemark, false, loginUser);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    @ApiOperation(value = "2W单修改仓库")
    @PostMapping(value = "/api/cs/oc/oms/v1/updateWarehouseBatch")
    public ValueHolderV14 updateWarehouseBatch(HttpServletRequest request, @RequestBody JSONObject paramObj) {
        log.debug("start updateWarehouseBatch.ReceiveParams:{}", paramObj);

        ValueHolderV14<LargeQuantitiesOrderVO> vh = new ValueHolderV14<>();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            JSONObject page = paramObj.getJSONObject("page");
            if (page != null) {
                page.put("pageSize", batchHandleNumber);
            }
            QueryEsListResult result = ocBOrderListQueryCmd.queryOrderEsList(user, paramObj.toJSONString());

            List<Long> ids = convertResultToIdList(result);

            AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
            asyncTaskBody.setTaskId(UUID.randomUUID().toString());
            asyncTaskBody.setMenu("2W条订单批量修改仓库");
            asyncTaskBody.setTaskType("修改仓库");
            asyncTaskManager.beforeExecute(user, asyncTaskBody);

            JSONObject updateParam = paramObj.getJSONObject("PARAM");
            checkUpdateWarehouseBatchParam(updateParam);

            commonTaskExecutor.submit(() -> {
                ValueHolder valueHolder = new ValueHolder();
                try {
                    ValueHolderV14 ret = updateOrderInfoCmd.updateWarehouse(ids, updateParam.getLong("shareStoresId"), updateParam.getString("shareStoresEcode"),
                            updateParam.getLong("warehouseId"), updateParam.getString("warehouseEcode"),
                            updateParam.getString("updateRemark"), true, user);
                    JSONObject data = (JSONObject) ret.getData();
                    if (Objects.nonNull(data)) {
                        JSONArray jsonArray = data.getJSONArray("errorVoList");
                        List<ExecuteErrorVO> errorVoList = null;
                        if (jsonArray != null) {
                            errorVoList = jsonArray.toJavaList(ExecuteErrorVO.class);
                        }

                        if (!CollectionUtils.isEmpty(errorVoList)) {
                            String url = writeFailList2Excel(errorVoList, user);
                            asyncTaskBody.setExportUrl(url);
                        }
                    }
                    valueHolder.put("message", ret.getMessage());
                    valueHolder.put("code", ResultCode.SUCCESS);
                } catch (NDSException e) {
                    log.warn(LogUtil.format("异步执行失败:{}",
                            "UpdateOrderInfoCtrl.updateWarehouseBatch"), Throwables.getStackTraceAsString(e));
                    valueHolder.put("code", ResultCode.FAIL);
                    valueHolder.put("message", Resources.getMessage("异常信息：" + Throwables.getStackTraceAsString(e)));
                }

                log.warn(LogUtil.format("异步执行结束:{}",
                        "UpdateOrderInfoCtrl.updateWarehouseBatch"), valueHolder);
                //记录日志信息 这里返回的信息中包含了异步的请求
                asyncTaskManager.afterExecute(user, asyncTaskBody, valueHolder.toJSONObject());
            });
            log.debug("Finish updateWarehouseBatch,Result：{}", vh);

            vh = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("[2W条订单批量修改仓库]任务开始！详情请在我的任务查看"));
            vh.setData(new LargeQuantitiesOrderVO(asyncTaskBody.getId()));
            return vh;
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh;
        }
    }


    /**
     * 检查批量修改仓库参数，确保 warehouseId 和 warehouseEcode 不为空
     *
     * @param paramObj 包含参数的 JSON 对象
     */
    private void checkUpdateWarehouseBatchParam(JSONObject paramObj) {
        // 检查 paramObj 是否为空
        if (paramObj == null) {
            throw new NDSException("参数对象不能为空");
        }

        // 检查 warehouseId 是否为空
        Long warehouseId = paramObj.getLong("warehouseId");
        if (warehouseId == null) {
            throw new NDSException("仓库ID不能为空");
        }

        // 检查 warehouseEcode 是否为空
        String warehouseEcode = paramObj.getString("warehouseEcode");
        if (warehouseEcode == null || warehouseEcode.trim().isEmpty()) {
            throw new NDSException("仓库编码不能为空");
        }
    }


    @ApiOperation(value = "查询库存聚合仓接口")
    @PostMapping("/api/cs/oc/oms/v1/queryOmsShopStorage")
    public JSONObject queryOmsShopStorage(HttpServletRequest request,@RequestBody JSONObject JSONObject) {
         User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        //User loginUser = SystemUserResource.getRootUser();
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = updateOrderInfoCmd.queryOmsShopStorage(JSONObject, loginUser);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    @ApiOperation(value = "查询用户的实体仓接口")
    @PostMapping("/api/cs/oc/oms/v1/queryOmsWarehouse")
    public JSONObject queryOmsWarehouse(HttpServletRequest request,@RequestBody JSONObject JSONObject) {
        User loginUser = r3PrimWebAuthService.getLoginPrimWebUser(request);
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = updateOrderInfoCmd.queryOmsWarehouse(JSONObject, loginUser);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();

        }
    }

    /**
     * 修改物流前检查订单
     *
     * @param ids 订单id集合
     * @return 返回信息
     */
    @ApiOperation(value = "修改物流前检查订单")
    @PostMapping("/api/cs/oc/oms/v1/checkOrderBeforeLogistics")
    public JSONObject checkOrderBeforeLogistics(@RequestParam("ids") List<Long> ids) {
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = updateOrderInfoCmd.checkOrder(ids, 1);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }

    /**
     * 修改仓库前检查订单
     *
     * @param ids 订单id集合
     * @return 返回信息
     */
    @ApiOperation(value = "修改仓库前检查订单")
    @PostMapping("/api/cs/oc/oms/v1/checkOrderBeforeWarehouse")
    public JSONObject checkOrderBeforeWarehouse(@RequestParam("ids") List<Long> ids) {
        ValueHolderV14 vh = new ValueHolderV14();
        try {
            vh = updateOrderInfoCmd.checkOrder(ids, 2);
            return vh.toJSONObject();
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh.toJSONObject();
        }
    }


    @ApiOperation(value = "筛选列表")
    @PostMapping("/api/cs/oc/oms/v1/getQueryList")
    public JSONObject getQueryList(HttpServletRequest request, @RequestBody JSONObject jsn) {

        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject = updateOrderInfoCmd.queryList(user, jsn);
            return jsonObject;
        } catch (NDSException e) {
            jsonObject.put("code", ResultCode.FAIL);
            jsonObject.put("message", Resources.getMessage(e.getMessage()));
            return jsonObject;
        }
    }

    @ApiOperation(value = "重新分配物流")
    @RequestMapping(value = "/api/cs/oc/oms/v1/reallocateLogistics", method = RequestMethod.POST)
    public JSONObject reallocateLogistics(HttpServletRequest request, @RequestParam(value = "param") String param) {

        ValueHolderV14 vh;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
        if (usrPem == null) {
            vh = new ValueHolderV14();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }
        vh = updateOrderInfoCmd.reallocateLogistics(param, user, usrPem);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "重新分配仓库")
    @RequestMapping(value = "/api/cs/oc/oms/v1/reallocateWarehouse", method = RequestMethod.POST)
    public JSONObject reallocateWarehouse(HttpServletRequest request, @RequestParam(value = "param") String param) {
        ValueHolderV14 vh;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }
        vh = updateOrderInfoCmd.reallocateWarehouse(param, user, null);
        return vh.toJSONObject();
    }

    @ApiOperation(value = "重新分物流")
    @RequestMapping(value = "/api/cs/oc/oms/v1/reDistributionLogistics", method = RequestMethod.POST)
    public JSONObject reDistributionLogistics(HttpServletRequest request,@RequestParam("ids") List<Long> ids) {
        ValueHolderV14 vh;
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
        if (usrPem == null) {
            vh = new ValueHolderV14<>();
            vh.setMessage("未获取到用户权限");
            vh.setCode(ResultCode.FAIL);
            return vh.toJSONObject();
        }
        vh = updateOrderInfoCmd.reDistributionLogistics(ids, user);
        return vh.toJSONObject();
    }


    private List<Long> convertResultToIdList(QueryEsListResult result) {
        String ids = result.getIds();
        if (StringUtil.isNullOrEmpty(ids)) {
            throw new NDSException("返回异常查询结果 没有符合要求的数据");
        }

        if (result.getQueryDto() != null) {
            // 可以配置
            if (result.getQueryDto().getTotalCount() > batchHandleNumber) {
                throw new NDSException("单次最大" + batchHandleNumber + "条，请检查数据");
            }
        }

        // 现在的ids 的格式是 1,2,3
        return Arrays.stream(ids.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    private String writeFailList2Excel(List<ExecuteErrorVO> retList, User user) {
        //列名
        String[] columnNames = {"订单编号", "失败原因"};
        List<String> columnList = Lists.newArrayList(columnNames);
        // map中的key
        String[] keys = {"objId", "message"};
        List<String> keyList = Lists.newArrayList(keys);

        Workbook hssfWorkbook = exportUtil.execute("结果", "2W条修改仓库", columnList, keyList, retList);
        return exportUtil.saveFileAndPutOss(hssfWorkbook,
                "2W条修改仓库执行结果",
                user,
                "OSS-Bucket/EXPORT/OC_B_ORDER/UPDATE_WAREHOUSE/");
    }
}
