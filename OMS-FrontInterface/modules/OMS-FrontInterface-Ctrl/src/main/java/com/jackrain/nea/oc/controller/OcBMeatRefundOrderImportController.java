package com.jackrain.nea.oc.controller;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.UsersDO;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.StCBusinessTypeMapper;
import com.jackrain.nea.oc.oms.model.MeatRefundOrderImportDTO;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@Api(value = "OcBMeatRefundOrderImportController", tags = "肉业售后单导入")
@Slf4j
public class OcBMeatRefundOrderImportController {

    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    public static final int _NONE = -1;
    public static final int NUMERIC = 0;
    public static final int STRING = 1;
    public static final int FORMULA = 2;
    public static final int BLANK = 3;
    public static final int BOOLEAN = 4;
    public static final int ERROR = 5;

    private static NumberFormat nf = NumberFormat.getInstance();

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
    @Autowired
    private StCBusinessTypeMapper stCBusinessTypeMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private BuildSequenceUtil sequenceGenUtil;

    @ApiOperation(value = "肉业售后单导入")
    @RequestMapping(path = "/api/cs/oc/oms/v1/importMeatRefundOrder", method = RequestMethod.POST)
    public ValueHolderV14 importMeatRefundOrder(HttpServletRequest request, @RequestParam(value = "file", required = true) MultipartFile file) {
        try {
            if (file == null) {
                throw new NDSException("请求参数不能为空!");
            }
            InputStream inputStream = file.getInputStream();
            Workbook hssfWorkbook = null;
            try {
                hssfWorkbook = new XSSFWorkbook(inputStream);
            } catch (Exception ex) {
                try {
                    hssfWorkbook = new HSSFWorkbook(inputStream);
                } catch (Exception e) {
                    throw new NDSException("Excel文件格式错误!");
                }
            }

            // 处理主表数据
            List<MeatRefundOrderImportDTO> extOcBReturnOrderList = getOcBReturnOrderList(hssfWorkbook);
            if (CollectionUtils.isEmpty(extOcBReturnOrderList)) {
                throw new NDSException("肉业售后单导入模板主表数据不能为空!");
            }
            // 取出extOcBReturnOrderList的店铺编码集合。然后去查询店铺信息并且去重
            Set<String> shopCodeSet = extOcBReturnOrderList.stream().map(MeatRefundOrderImportDTO::getShopCode).collect(Collectors.toSet());
            // 取出extOcBReturnOrderList的物流公司编码 去重
            Set<String> logisticsCodeSet = extOcBReturnOrderList.stream().map(MeatRefundOrderImportDTO::getLogisticsCode).collect(Collectors.toSet());
            // 取出extOcBReturnOrderList的商品编码 去重
            Set<String> productCodeSet = extOcBReturnOrderList.stream().map(MeatRefundOrderImportDTO::getProductCode).collect(Collectors.toSet());
            // 取出extOcBReturnOrderList的仓库 去重
            Set<String> warehouseSet = extOcBReturnOrderList.stream().map(MeatRefundOrderImportDTO::getWarehouse).collect(Collectors.toSet());
            // 取出extOcBReturnOrderList的业务类型 去重
            Set<String> businessCodeSet = extOcBReturnOrderList.stream().map(MeatRefundOrderImportDTO::getBusinessCode).collect(Collectors.toSet());

            // shopCodeSet转成list
            List<String> shopCodeList = Lists.newArrayList(shopCodeSet);
            List<String> logisticsCodeList = Lists.newArrayList(logisticsCodeSet);
            List<String> productCodeList = Lists.newArrayList(productCodeSet);
            List<String> warehouseList = Lists.newArrayList(warehouseSet);
            List<String> businessCodeList = Lists.newArrayList(businessCodeSet);
            List<CpShop> cpShopList = cpRpcService.queryShopByCodeList(shopCodeList);
            // 判断传入的店铺跟返回的店铺数量是否一致
            if (shopCodeSet.size() != cpShopList.size()) {
                throw new NDSException("店铺编码不正确!");
            }
            List<CpLogistics> cpLogisticsList = cpRpcService.queryLogisticsListByCodeIgnoreActive(logisticsCodeList);
            if (logisticsCodeSet.size() != cpLogisticsList.size()) {
                throw new NDSException("物流公司编码不正确!");
            }


            List<ProductSku> productSkuList = psRpcService.selectProductSkuIgnoreActive(productCodeList);
            if (productCodeSet.size() != productSkuList.size()) {
                throw new NDSException("商品编码不正确!");
            }
            List<CpCPhyWarehouse> cpCPhyWarehouseList = cpRpcService.queryWarehouseByNameList(warehouseList);
            if (warehouseSet.size() != cpCPhyWarehouseList.size()) {
                throw new NDSException("仓库不正确!");
            }

            List<StCBusinessType> businessTypeList =
                    stCBusinessTypeMapper.selectList(new LambdaQueryWrapper<StCBusinessType>()
                            .in(StCBusinessType::getEcode, businessCodeList).eq(StCBusinessType::getIsactive, YesNoEnum.Y.getKey()));

            if (businessCodeList.size() != businessTypeList.size()) {
                throw new NDSException("业务类型不正确!");
            }
            // cpShopList 以code为key 生成一个map
            Map<String, CpShop> cpShopMap = cpShopList.stream().collect(Collectors.toMap(CpShop::getEcode, cpShop -> cpShop));
            Map<String, CpLogistics> cpLogisticsMap = cpLogisticsList.stream().collect(Collectors.toMap(CpLogistics::getEcode, cpLogistics -> cpLogistics));
            Map<String, ProductSku> productSkuMap = productSkuList.stream().collect(Collectors.toMap(ProductSku::getEcode, productSku -> productSku));
            Map<String, CpCPhyWarehouse> cpCPhyWarehouseMap = cpCPhyWarehouseList.stream().collect(Collectors.toMap(CpCPhyWarehouse::getEname, cpCPhyWarehouse -> cpCPhyWarehouse));
            Map<String, StCBusinessType> stCBusinessTypeMap = businessTypeList.stream().collect(Collectors.toMap(StCBusinessType::getEcode, stCBusinessType -> stCBusinessType));
            // extOcBReturnOrderList 根据billNo 来进行分组
            Map<String, List<MeatRefundOrderImportDTO>> meatRefundOrderImportDTOMap = extOcBReturnOrderList.stream().collect(
                    Collectors.groupingBy(MeatRefundOrderImportDTO::getBillNo));
            // 遍历map。构建主表数据跟明细数据
            List<OcBReturnOrder> ocBReturnOrderList = Lists.newArrayList();
            List<OcBReturnOrderRefund> ocBReturnOrderRefundList = Lists.newArrayList();
            for (Map.Entry<String, List<MeatRefundOrderImportDTO>> entry : meatRefundOrderImportDTOMap.entrySet()) {
                List<MeatRefundOrderImportDTO> dtoList = entry.getValue();
                if (CollectionUtils.isEmpty(dtoList)) {
                    continue;
                }
                // 获取第一条数据用于构建主表信息
                MeatRefundOrderImportDTO firstDto = dtoList.get(0);
                // 构建主表数据
                OcBReturnOrder returnOrder = new OcBReturnOrder();
                Long id = ModelUtil.getSequence("oc_b_return_order");
                returnOrder.setId(id);
                returnOrder.setReturnId(firstDto.getRefundNo());
                returnOrder.setBillNo(entry.getKey());
                returnOrder.setTid(firstDto.getPlatformOrderNo());
                returnOrder.setLogisticsCode(firstDto.getLogisticsNo());
                CpLogistics cpLogistics = cpLogisticsMap.get(firstDto.getLogisticsCode());
                returnOrder.setCpCLogisticsEcode(firstDto.getLogisticsCode());
                if (cpLogistics != null) {
                    returnOrder.setCpCLogisticsEname(cpLogistics.getEname());
                    returnOrder.setCpCLogisticsId(cpLogistics.getId());
                }
                returnOrder.setInTime(firstDto.getInboundTime());
                returnOrder.setAuditTime(firstDto.getAuditTime());
                returnOrder.setRemark(firstDto.getRemark());
                returnOrder.setReturnPhase("售后");
                returnOrder.setReturnFlag(1);
                returnOrder.setIsCheck(1);
                returnOrder.setIsExamine(1);
                returnOrder.setIsInstorage(1);
                returnOrder.setIsTowms(2);
                returnOrder.setIsNotlogmber(StringUtils.isNotEmpty(firstDto.getLogisticsNo()) ? 1 : 0);
                returnOrder.setReturnStatus(ReturnStatusEnum.COMPLETION.getVal());
                returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());

                // 设置店铺信息
                CpShop cpShop = cpShopMap.get(firstDto.getShopCode());
                if (cpShop != null) {
                    returnOrder.setCpCShopId(cpShop.getId());
                    returnOrder.setCpCShopEcode(cpShop.getEcode());
                    returnOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
                    returnOrder.setPlatform(Math.toIntExact(cpShop.getCpCPlatformId()));
                }
                CpCPhyWarehouse cpCPhyWarehouse = cpCPhyWarehouseMap.get(firstDto.getWarehouse());
                if (cpCPhyWarehouse != null) {
                    returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getId());
                }
                StCBusinessType stCBusinessType = stCBusinessTypeMap.get(firstDto.getBusinessCode());
                if (stCBusinessType != null) {
                    returnOrder.setBusinessTypeId(stCBusinessType.getId());
                    returnOrder.setBusinessTypeCode(stCBusinessType.getEcode());
                    returnOrder.setBusinessTypeName(stCBusinessType.getEname());
                }
                returnOrder.setRefundReason(firstDto.getRefundReason());
                List<String> userNameList = new ArrayList<>();
                userNameList.add("rouyelishi");
                List<UsersDO> usersDOList = cpRpcService.queryUserByNames(userNameList);
                if (CollectionUtils.isEmpty(usersDOList)){
                    throw new NDSException("用户不存在!");
                }
                User user = new UserImpl();
                UsersDO usersDO = usersDOList.get(0);
                ((UserImpl) user).setId(usersDO.getId().intValue());
                ((UserImpl) user).setEname(usersDO.getEname());
                ((UserImpl) user).setTruename(usersDO.getTruename());
                ((UserImpl) user).setClientId(37);
                ((UserImpl) user).setOrgId(27);
                ((UserImpl) user).setName(usersDO.getName());

                BaseModelUtil.makeBaseCreateField(returnOrder, user);
                ocBReturnOrderList.add(returnOrder);

                // 构建明细数据
                for (MeatRefundOrderImportDTO dto : dtoList) {
                    OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
                    returnOrderRefund.setId(ModelUtil.getSequence("oc_b_return_order_refund"));
                    returnOrderRefund.setOcBReturnOrderId(returnOrder.getId());
                    ProductSku productSku = productSkuMap.get(dto.getProductCode());
                    returnOrderRefund.setPsCProEcode(dto.getProductCode());
                    if (productSku != null) {
                        returnOrderRefund.setPsCSkuId(productSku.getId());
                        returnOrderRefund.setPsCProEname(productSku.getName());
                        returnOrderRefund.setPsCSkuEcode(productSku.getSkuEcode());
                        returnOrderRefund.setPsCSkuEname(productSku.getName());
                    }
                    returnOrderRefund.setQtyRefund(dto.getApplyQty());
                    returnOrderRefund.setQtyIn(dto.getInboundQty());
                    returnOrderRefund.setQtyCanRefund(dto.getRefundableQty());
                    returnOrderRefund.setPrice(dto.getUnitPrice());
                    returnOrderRefund.setAmtRefund(dto.getAmount());
                    returnOrderRefund.setIsReturn(1);
                    returnOrderRefund.setAmtRefundSingle(dto.getUnitPrice());
                    returnOrderRefund.setAmtPtRefund(dto.getAmount());
                    BaseModelUtil.makeBaseCreateField(returnOrderRefund, user);
                    ocBReturnOrderRefundList.add(returnOrderRefund);
                }
            }
            applicationContext.getBean(OcBMeatRefundOrderImportController.class).batchInsertReturnOrderAndRefund(ocBReturnOrderList, ocBReturnOrderRefundList);
        } catch (Exception ex) {
            log.error("OcBMeatRefundOrderImportController.importMeatRefundOrder导入失败", ex);
            return new ValueHolderV14(ResultCode.FAIL, ex.getMessage());
        }
        return new ValueHolderV14(ResultCode.SUCCESS, "导入成功");
    }

    /**
     * 批量插入退货单和退款单
     *
     * @param ocBReturnOrderList       退货单列表
     * @param ocBReturnOrderRefundList 退款单列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertReturnOrderAndRefund(List<OcBReturnOrder> ocBReturnOrderList, List<OcBReturnOrderRefund> ocBReturnOrderRefundList) {
        ocBReturnOrderMapper.batchInsert(ocBReturnOrderList);
        ocBReturnOrderRefundMapper.batchInsert(ocBReturnOrderRefundList);
    }

    /**
     * 获取主表sheet数据，转换成主表对象
     */
    private List<MeatRefundOrderImportDTO> getOcBReturnOrderList(Workbook hssfWorkbook) {
        List<MeatRefundOrderImportDTO> importDTOList = Lists.newArrayList();
        List<Map<String, String>> execlList = Lists.newArrayList();
        try {
            execlList = readExcel(0, hssfWorkbook);
        } catch (Exception e) {
            log.error("读取主表数据失败", e);
        }

        int index = 0;
        for (Map<String, String> columnMap : execlList) {
            MeatRefundOrderImportDTO importDTO = new MeatRefundOrderImportDTO();
            if (index == 0) {
                if (columnMap.size() != 21
                        || !"单据编号".equals(columnMap.get(rowStr + index + cellStr + 0))
                        || !"店铺编码".equals(columnMap.get(rowStr + index + cellStr + 1))
                        || !"原平台单号".equals(columnMap.get(rowStr + index + cellStr + 2))
                        || !"平台退款单号".equals(columnMap.get(rowStr + index + cellStr + 3))
                        || !"入库时间".equals(columnMap.get(rowStr + index + cellStr + 4))
                        || !"审核时间".equals(columnMap.get(rowStr + index + cellStr + 5))
                        || !"备注".equals(columnMap.get(rowStr + index + cellStr + 6))
                        || !"仓库".equals(columnMap.get(rowStr + index + cellStr + 7))
                        || !"退回物流单号".equals(columnMap.get(rowStr + index + cellStr + 8))
                        || !"退回物流公司编码".equals(columnMap.get(rowStr + index + cellStr + 9))
                        || !"退款金额".equals(columnMap.get(rowStr + index + cellStr + 10))
                        || !"退款类型".equals(columnMap.get(rowStr + index + cellStr + 11))
                        || !"业务类型编码".equals(columnMap.get(rowStr + index + cellStr + 12))
                        || !"退款原因".equals(columnMap.get(rowStr + index + cellStr + 13))
                        || !"退款创建时间".equals(columnMap.get(rowStr + index + cellStr + 14))
                        || !"商品编码".equals(columnMap.get(rowStr + index + cellStr + 15))
                        || !"退货金额".equals(columnMap.get(rowStr + index + cellStr + 16))
                        || !"单件退货金额".equals(columnMap.get(rowStr + index + cellStr + 17))
                        || !"入库数量".equals(columnMap.get(rowStr + index + cellStr + 18))
                        || !"申请数量".equals(columnMap.get(rowStr + index + cellStr + 19))
                        || !"可退数量".equals(columnMap.get(rowStr + index + cellStr + 20))) {
                    return Lists.newArrayList();
                }
            } else {
                String billNo = StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 0));

                if (StringUtils.isNotEmpty(billNo)) {
                    if (StringUtils.isNotEmpty(billNo) && billNo.startsWith("TK")) {
                        billNo = "TH" + billNo.substring(2);
                    }
                    importDTO.setBillNo(billNo);
                    importDTO.setShopCode(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 1)));
                    String platformOrderNo = columnMap.get(rowStr + index + cellStr + 2);
                    importDTO.setPlatformOrderNo(platformOrderNo);
                    importDTO.setRefundNo(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 3)));

                    String inboundTimeStr = columnMap.get(rowStr + index + cellStr + 4);
                    if (StringUtils.isNotEmpty(inboundTimeStr)) {
                        importDTO.setInboundTime(DateUtil.parse(inboundTimeStr, "yyyy-MM-dd HH:mm:ss"));
                    }

                    String auditTimeStr = columnMap.get(rowStr + index + cellStr + 5);
                    if (StringUtils.isNotEmpty(auditTimeStr)) {
                        importDTO.setAuditTime(DateUtil.parse(auditTimeStr, "yyyy-MM-dd HH:mm:ss"));
                    }

                    importDTO.setRemark(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 6)));
                    importDTO.setWarehouse(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 7)));
                    importDTO.setLogisticsNo(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 8)));
                    importDTO.setLogisticsCode(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 9)));

                    String refundAmountStr = columnMap.get(rowStr + index + cellStr + 10);
                    if (StringUtils.isNotEmpty(refundAmountStr)) {
                        importDTO.setRefundAmount(new BigDecimal(refundAmountStr));
                    }

                    importDTO.setRefundType(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 11)));
                    importDTO.setBusinessCode(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 12)));
                    importDTO.setRefundReason(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 13)));

                    String refundCreateTimeStr = columnMap.get(rowStr + index + cellStr + 14);
                    if (StringUtils.isNotEmpty(refundCreateTimeStr)) {
                        importDTO.setRefundCreateTime(DateUtil.parse(refundCreateTimeStr, "yyyy-MM-dd HH:mm:ss"));
                    }

                    importDTO.setProductCode(StringUtils.trimToNull(columnMap.get(rowStr + index + cellStr + 15)));

                    String amountStr = columnMap.get(rowStr + index + cellStr + 16);
                    if (StringUtils.isNotEmpty(amountStr)) {
                        importDTO.setAmount(new BigDecimal(amountStr));
                    }

                    String unitPriceStr = columnMap.get(rowStr + index + cellStr + 17);
                    if (StringUtils.isNotEmpty(unitPriceStr)) {
                        importDTO.setUnitPrice(new BigDecimal(unitPriceStr));
                    }

                    String inboundQtyStr = columnMap.get(rowStr + index + cellStr + 18);
                    if (StringUtils.isNotEmpty(inboundQtyStr)) {
                        importDTO.setInboundQty(new BigDecimal(inboundQtyStr));
                    }

                    String applyQtyStr = columnMap.get(rowStr + index + cellStr + 19);
                    if (StringUtils.isNotEmpty(applyQtyStr)) {
                        importDTO.setApplyQty(new BigDecimal(applyQtyStr));
                    }

                    String refundableQtyStr = columnMap.get(rowStr + index + cellStr + 20);
                    if (StringUtils.isNotEmpty(refundableQtyStr)) {
                        importDTO.setRefundableQty(new BigDecimal(refundableQtyStr));
                    }

                    importDTOList.add(importDTO);
                }
            }
            index++;
        }
        return importDTOList;
    }

    /**
     * 读取Excel数据
     */
    private List<Map<String, String>> readExcel(int sheetIndex, Workbook workbook) {
        List<Map<String, String>> list = Lists.newArrayList();
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        int rowNum = sheet.getLastRowNum();
        for (int i = 0; i <= rowNum; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                Map<String, String> map = new HashMap<>();
                for (int j = 0; j < row.getLastCellNum(); j++) {
                    Cell cell = row.getCell(j);
                    if (cell != null) {
                        String value = getCellValue(cell);
                        map.put(rowStr + i + cellStr + j, value);
                    }
                }
                list.add(map);
            }
        }
        return list;
    }

    /**
     * 获取单元格的值
     */
    private String getCellValue(Cell cell) {
        int cellType = cell.getCellType();
        String cellValue;
        switch (cellType) {
            case NUMERIC:
                cellValue = nf.format(cell.getNumericCellValue());
                if (cellValue.indexOf(",") >= 0) {
                    cellValue = cellValue.replace(",", "");
                }
                break;
            case FORMULA:
                try {
                    cellValue = cell.getStringCellValue();
                } catch (IllegalStateException e) {
                    cellValue = String.valueOf(cell.getNumericCellValue());
                }
                break;
            default:
                cellValue = cell.getStringCellValue();
        }
        return cellValue.trim();
    }
}