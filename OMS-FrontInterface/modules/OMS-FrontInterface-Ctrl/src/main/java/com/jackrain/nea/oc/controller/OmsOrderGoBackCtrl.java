package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.oc.oms.api.OmsOrderGoBackCmd;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/12/4 4:35 下午
 * @Version 1.0
 */
@Api(value = "OmsOrderGoBackCtrl", tags = "退回操作")
@Slf4j
@RestController
public class OmsOrderGoBackCtrl {

    @Autowired
    private OmsOrderGoBackCmd omsOrderGoBackCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @PostMapping("api/cs/oc/oms/v1/omsOrderGoBack")
    public JSONObject omsOrderGoBack(String id) {
        ValueHolderV14 holderV14 = new ValueHolderV14();
        try {
            List<Long> ids = new ArrayList<>();
            if (StringUtils.isNotEmpty(id)) {
                String[] split = id.split(",");
                for (String s : split) {
                    ids.add(Long.valueOf(s));
                }
            }
            User rootUser = SystemUserResource.getRootUser();
            holderV14 = omsOrderGoBackCmd.orderGoBack(ids, rootUser);
        } catch (Exception e) {
            holderV14.setCode(-1);
            holderV14.setMessage(e.getMessage());
        }
        return holderV14.toJSONObject();
    }
}
