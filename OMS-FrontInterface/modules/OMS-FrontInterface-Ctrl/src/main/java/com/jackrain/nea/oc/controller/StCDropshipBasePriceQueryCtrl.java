package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCDropshipBasePriceQueryCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 一件代发客户基价策略查询控制器
 *
 * <AUTHOR>
 */
@Api(value = "StCDropshipBasePriceQueryCtrl", tags = "一件代发客户基价策略-查询")
@Slf4j
@RestController
public class StCDropshipBasePriceQueryCtrl {

    @Autowired
    private StCDropshipBasePriceQueryCmd queryCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "根据店铺ID和SKU编码查询基价")
    @RequestMapping(value = "/api/cs/oc/oms/v1/getDropshipBasePrice", method = RequestMethod.POST)
    public JSONObject getDropshipBasePrice(HttpServletRequest request,
                                          @RequestBody JSONObject obj) {
        ValueHolder vh = new ValueHolder();
        JSONObject result = new JSONObject();
        
        if (log.isDebugEnabled()) {
            log.debug("start StCDropshipBasePriceQueryCtrl.getDropshipBasePrice.ReceiveParams=" + obj);
        }

        try {
            // 获取用户
            User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

            vh = queryCmd.getBasePrice(obj, user);

            result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), new BigDecimalValueFilter(),
                    SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullNumberAsZero));
        } catch (NDSException e) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", Resources.getMessage(e.getMessage()));
            result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), new BigDecimalValueFilter(),
                    SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullNumberAsZero));
        } catch (Exception e) {
            log.error("查询一件代发基价失败", e);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "查询失败：" + e.getMessage());
            result = JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), new BigDecimalValueFilter(),
                    SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullNumberAsZero));
        }
        
        return result;
    }

    /**
     * BigDecimal 值过滤器
     */
    class BigDecimalValueFilter implements ValueFilter {
        @Override
        public Object process(Object o, String name, Object value) {
            if (null != value && value instanceof BigDecimal) {
                return ((BigDecimal) value).setScale(4, RoundingMode.DOWN).toString();
            }
            return value;
        }
    }
}
