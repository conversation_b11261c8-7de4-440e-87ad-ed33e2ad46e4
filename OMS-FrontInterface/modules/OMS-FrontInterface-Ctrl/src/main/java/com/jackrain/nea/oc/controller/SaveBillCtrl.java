package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.BillCmd;
import com.jackrain.nea.oc.oms.model.relation.UserPermission;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;


/**
 * @Author: wangqiang
 * @Date: 2019-03-08 11:50
 * @Version 1.0
 */
@Api(value = "SaveBilCtrl", tags = "订单新增")
@Slf4j
@RestController
public class SaveBillCtrl {

    @Autowired
    private BillCmd billCmd;

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @ApiOperation(value = "订单新增")
    @RequestMapping(value = "/api/cs/oc/oms/v1/saveBill", method = RequestMethod.POST)
    public JSONObject saveBill(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolder vh = new ValueHolder();
        //获取用户信息
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        UserPermission usrPem = UserPermissionCtrlHelper.currentUserPermission(user, "OC_B_ORDER");
        if (usrPem == null) {
            vh.put("message","未获取到用户权限");
            vh.put("code", ResultCode.FAIL);
            return vh.toJSONObject();
        }

        try {
            vh = billCmd.saveBill(obj, user);

        } catch (Exception ex) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", ex.getMessage());
        }

        return vh.toJSONObject();

    }

    /**
     * 修改订单明细 实物报缺接口
     *
     * @param request
     * @param obj
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "订单新增")
    @RequestMapping(value = "/api/cs/oc/oms/v1/updateIsLackstock", method = RequestMethod.POST)
    public JSONObject updateIsLackstock(HttpServletRequest request, @RequestBody JSONObject obj) throws Exception {
        ValueHolder vh = new ValueHolder();

        //获取用户信息
        //User user = SystemUserResource.getRootUser();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);

        try {
            vh = billCmd.updateIsLackstock(obj, user);

        } catch (NDSException ex) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message","新增订单失败");
        }
        return vh.toJSONObject();

    }

}
