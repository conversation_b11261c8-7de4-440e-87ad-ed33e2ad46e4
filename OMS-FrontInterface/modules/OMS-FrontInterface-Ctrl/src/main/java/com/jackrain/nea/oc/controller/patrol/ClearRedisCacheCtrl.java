package com.jackrain.nea.oc.controller.patrol;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.api.patrol.ClearRedisCacheCmd;
import com.jackrain.nea.oc.oms.api.patrol.WithdrawZtWarehouseCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: 黄世新
 * @Date: 2019/11/6 2:08 下午
 * @Version 1.0
 */
@Api(value = "ClearRedisCacheCtrl", tags = "删除订单数据")
@Slf4j
@RestController
public class ClearRedisCacheCtrl {

    @Autowired
    private ClearRedisCacheCmd clearRedisCacheCmd;

    @Autowired
    private WithdrawZtWarehouseCmd withdrawZtWarehouse;

    @ApiOperation(value = "删除redis缓存数据")
    @RequestMapping(value = "/api/cs/oc/oms/v1/deleteRedisKey", method = RequestMethod.GET)
    public JSONObject clearRedisCache(String redisKey, String type, String env) {
        ValueHolderV14 valueHolderV14 = clearRedisCacheCmd.clearRedisCache(redisKey, type, env);
        return valueHolderV14.toJSONObject();
    }


    @ApiOperation(value = "查看redis缓存sku数据")
    @RequestMapping(value = "/api/cs/oc/oms/v1/selectSkuInfo", method = RequestMethod.GET)
    public String clearRedisCache(String sku) {
        String s = clearRedisCacheCmd.selectSku(sku);
        return s;
    }


    @RequestMapping("/api/cs/oc/oms/v1/clearWarehouse")
    public String clearWarehouse(@RequestParam("pageSize") Integer pageSize) {
        ValueHolderV14 valueHolderV141 = withdrawZtWarehouse.withdrawZtWarehouse(pageSize);
        if (valueHolderV141 != null) {
            return valueHolderV141.toJSONObject().toString();
        }
        return "任务失败";
    }
}
