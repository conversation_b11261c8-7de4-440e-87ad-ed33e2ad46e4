package com.jackrain.nea.oc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Throwables;
import com.jackrain.nea.auth.R3PrimWebAuthService;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBOrderListQueryCmd;
import com.jackrain.nea.oc.oms.api.OrderHoldCmd;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.request.OrderHoldRequest;
import com.jackrain.nea.oc.oms.model.request.OrderQueryRequest;
import com.jackrain.nea.oc.oms.model.result.OrderDetentionFutureResult;
import com.jackrain.nea.oc.oms.model.result.OrderHoldResult;
import com.jackrain.nea.oc.oms.model.result.QueryEsListResult;
import com.jackrain.nea.oc.oms.vo.ExecuteErrorVO;
import com.jackrain.nea.oc.oms.vo.LargeQuantitiesOrderVO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.async.AsyncTaskBody;
import com.jackrain.nea.web.common.AsyncTaskManager;
import com.jackrain.nea.web.face.User;
import io.netty.util.internal.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @author: 江家雷
 * @since: 2020/07/04
 * create at : 2020/07/04
 */
@Api(value = "OrderHoldCtrl", tags = "订单Hold单")
@Slf4j
@RestController
public class OrderHoldCtrl {

    @Autowired
    private R3PrimWebAuthService r3PrimWebAuthService;

    @Autowired
    private OrderHoldCmd orderHoldCmd;

    @Autowired
    private OcBOrderListQueryCmd ocBOrderListQueryCmd;

    @Autowired
    private AsyncTaskManager asyncTaskManager;


    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;


    /**
     * 是否生成退单
     */
    @NacosValue(value = "${r3.batch.handle.number:20000}", autoRefreshed = true)
    private long batchHandleNumber;

    /**
     * 订单Hold单
     *
     * @param request 请求参数
     * @param obj     订单的id
     * @return 返回信息
     */
    @ApiOperation(value = "订单Hold单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/holdOrder", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject orderHold(HttpServletRequest request,
                                @RequestBody OrderHoldRequest obj) {
         ValueHolderV14<OrderHoldResult> vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            ValueHolder valueHolder = orderHoldCmd.manualHoldOrder(obj, user);
           return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 释放订单HOLD单
     *
     * @param request 请求参数
     * @param obj     订单的id
     * @return 返回信息
     */
    @ApiOperation(value = "释放订单HOLD单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/manualUnHoldOrder", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject manualUnHoldOrder(HttpServletRequest request,
                                        @RequestBody JSONObject obj) {
        ValueHolderV14<OrderHoldResult> vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            ValueHolder valueHolder = orderHoldCmd.manualUnHoldOrder(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }
    @ApiOperation(value = "手动卡单释放")
    @RequestMapping(value = "/api/cs/oc/oms/v1/orderDetentionRelease", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject orderDetentionRelease(HttpServletRequest request,
                                        @RequestBody JSONObject obj) {
         ValueHolderV14<OrderHoldResult> vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            ValueHolder valueHolder = orderHoldCmd.orderDetentionRelease(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    @ApiOperation(value = "大批量手动卡单释放")
    @RequestMapping(value = "/api/cs/oc/oms/v1/largeQuantitiesOrderDetentionRelease", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14 LargeQuantitiesOrderDetentionRelease(HttpServletRequest request,
                                            @RequestBody JSONObject obj) {

        ValueHolderV14<Object> vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            JSONObject page = obj.getJSONObject("page");
            if (page != null) {
                page.put("pageSize", batchHandleNumber);
            }

            QueryEsListResult result = ocBOrderListQueryCmd.queryOrderEsList(user, obj.toJSONString());
            if (result.getCheckPageOverDeepSizeVh() != null) {
                return result.getCheckPageOverDeepSizeVh();
            }
            long totalCount = result.getQueryDto().getTotalCount();
            //在转换的过程中会去check totalCount 是否超过了约定的值 这个方法需要放在confirm的前面
            JSONObject convertJsonObject = convertResultToJsonObject(result);

            boolean confirm = obj.getBooleanValue("confirm");
            if (!confirm){
                LargeQuantitiesOrderVO largeQuantitiesOrderVO = new LargeQuantitiesOrderVO();
                // 代表没有二次确认这个时候直接返回全部的数量
                largeQuantitiesOrderVO.setTotalCount(totalCount);
                vh.setData(largeQuantitiesOrderVO);
                return vh;
            }

            AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
            asyncTaskBody.setTaskId(UUID.randomUUID().toString());
            asyncTaskBody.setMenu("批量取消卡单【零售发货单】");
            asyncTaskBody.setTaskType("取消卡单");
            asyncTaskManager.beforeExecute(user, asyncTaskBody);

            commonTaskExecutor.submit(() -> {

                ValueHolder valueHolder = new ValueHolder();
                try {
                    valueHolder = orderHoldCmd.orderDetentionRelease(convertJsonObject, user);
                } catch (NDSException e) {
                    valueHolder.put("code", ResultCode.FAIL);

                    valueHolder.put("message", Resources.getMessage("异常信息：" + Throwables.getStackTraceAsString(e)));
                }
                // 重写valueHolder 的message部分
                // total
                // failCount
                // valueHolder.getData() -> jsonArray -> message objId;
                JSONArray message = (JSONArray) valueHolder.get("data");
                List<ExecuteErrorVO> failList = new ArrayList<>();
                if (message!=null){
                    message.forEach(item -> {
                        JSONObject jsonObject = (JSONObject) item;
                        if (jsonObject.get("objid") != null&&jsonObject.get("message")!=null) {
                            ExecuteErrorVO executeErrorVO = new ExecuteErrorVO();
                            executeErrorVO.setObjId(jsonObject.getLong("objid"));
                            executeErrorVO.setMessage(jsonObject.getString("message"));
                            failList.add(executeErrorVO);
                        }
                    });
                    // 重置状态，这样asyncTaskBody 就不会读取全部的content
                    valueHolder.put("code", ResultCode.SUCCESS);
                    // 覆盖valueHolder中的message信息
                    valueHolder.put("message", String.format("总执行 %s单，成功%s，失败%s",
                            totalCount, totalCount - failList.size(), failList.size()));
                }
                // 将failList的数据写入到oss中
                if (!CollectionUtils.isEmpty(failList)) {
                    String url = orderHoldCmd.exportImpErrorResult(failList, user, "批量取消卡单【零售发货单】错误数据");
                    asyncTaskBody.setExportUrl(url);
                }

                //记录日志信息
                asyncTaskManager.afterExecute(user, asyncTaskBody, valueHolder.toJSONObject());

            });
            vh = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("批量取消卡单【零售发货单】任务开始！详情请在我的任务查看"));
            LargeQuantitiesOrderVO largeQuantitiesOrderVO = new LargeQuantitiesOrderVO();
            largeQuantitiesOrderVO.setAsyncTaskId(asyncTaskBody.getId());
            vh.setData(largeQuantitiesOrderVO);
            return vh;
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh;
        }
    }

    private  JSONObject convertResultToJsonObject(QueryEsListResult result) {
        String ids = result.getIds();
        if (StringUtil.isNullOrEmpty(ids)) {
            throw new NDSException("返回异常查询结果 没有符合要求的数据");
        }
        // 现在需要将esResult 转成我们需要的对象

        if (result.getQueryDto()!=null ) {
            // 可以配置
            if (result.getQueryDto().getTotalCount()>batchHandleNumber) {
                throw new NDSException("单次最大"+batchHandleNumber+"条，请检查数据");
            }
        }
        // 这里可以可以考虑取消订单相关的任务
        JSONObject convertJsonObject = new JSONObject();
        // 现在的ids 的格式是 1,2,3 -> [1,2,3]
        convertJsonObject.put("ids", "["+ids+"]");
        return convertJsonObject;
    }


    /**
     * description:订单手动卡单
     * @Author:  liuwenjin
     * @Date 2022/4/12 7:44 下午
     */
    @ApiOperation(value = "大批量手动卡单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/largeQuantitiesOrderDetention", method = {RequestMethod.POST, RequestMethod.GET})
    public ValueHolderV14<LargeQuantitiesOrderVO> largeQuantitiesOrderDetention(HttpServletRequest request,
                                                                                @RequestBody JSONObject obj) {
        //记录日志信息
        log.debug("start OrderHoldCtrl.largeQuantitiesOrderDetention.ReceiveParams=" + obj);
        ValueHolderV14<LargeQuantitiesOrderVO> vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            JSONObject page = obj.getJSONObject("page");
            if (page!=null){
                page.put("pageSize",batchHandleNumber);
            }
            QueryEsListResult result = ocBOrderListQueryCmd.queryOrderEsList(user, obj.toJSONString());
            if (result.getCheckPageOverDeepSizeVh()!=null){
                return result.getCheckPageOverDeepSizeVh();
            }
            OrderQueryRequest queryDto = result.getQueryDto();
            JSONObject convertJsonObject = convertResultToJsonObject(result);

            boolean confirm = obj.getBooleanValue("confirm");
            if (!confirm){
                LargeQuantitiesOrderVO largeQuantitiesOrderVO = new LargeQuantitiesOrderVO();
                // 代表没有二次确认这个时候直接返回全部的数量
                largeQuantitiesOrderVO.setTotalCount(queryDto.getTotalCount());
                vh.setData(largeQuantitiesOrderVO);
                return vh;
            }
            AsyncTaskBody asyncTaskBody = new AsyncTaskBody();
            asyncTaskBody.setTaskId(UUID.randomUUID().toString());
            asyncTaskBody.setMenu("全部卡单【零售发货单】");
            asyncTaskBody.setTaskType("手动卡单");
            asyncTaskManager.beforeExecute(user, asyncTaskBody);

            OrderHoldCmd orderHoldCmd = (OrderHoldCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), OrderHoldCmd.class.getName(),
                    "oms-fi", "1.0");


            obj.put("ids",convertJsonObject.get("ids"));

            commonTaskExecutor.submit(() -> {

                ValueHolder valueHolder = new ValueHolder();
                try {
                    OrderDetentionFutureResult orderDetentionFutureResult = orderHoldCmd.orderDetentionFuture(obj, user);
                    if (orderDetentionFutureResult!=null && orderDetentionFutureResult.getFailList()!=null){
                        valueHolder.put("message", String.format("总执行 %s单，成功%s，失败%s",
                                orderDetentionFutureResult.getFailCount() + orderDetentionFutureResult.getSuccessCount(), orderDetentionFutureResult.getSuccessCount(), orderDetentionFutureResult.getFailCount()));
                    }
                    // 将orderDetentionFutureResult中的返回结果写入到oss中
                    List<ExecuteErrorVO> failList = orderDetentionFutureResult.getFailList();
                    // 将failList的数据写入到oss中
                    if (!CollectionUtils.isEmpty(failList)) {
                        String url = orderHoldCmd.exportImpErrorResult(failList, user, "全部卡单【零售发货单】错误数据");
                        asyncTaskBody.setExportUrl(url);
                    }
                    valueHolder.put("code", ResultCode.SUCCESS);
                } catch (NDSException e) {
                    valueHolder.put("code", ResultCode.FAIL);

                    valueHolder.put("message", Resources.getMessage("异常信息：" + Throwables.getStackTraceAsString(e)));
                }
                //记录日志信息 这里返回的信息中包含了异步的请求
                asyncTaskManager.afterExecute(user, asyncTaskBody, valueHolder.toJSONObject());

            });
            //记录日志信息。Finish 标记结束
            log.debug("Finish  OrderHoldCtrl.largeQuantitiesOrderDetention. Return Result=" + vh.toJSONObject());
            vh = new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("全部卡单【零售发货单】任务开始！详情请在我的任务查看"));
            vh.setData(new LargeQuantitiesOrderVO(asyncTaskBody.getId()));
            return vh;
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return vh;
        }
    }


    /**
     * description:订单手动卡单
     * @Author:  liuwenjin
     * @Date 2022/4/12 7:44 下午
     */
    @ApiOperation(value = "手动卡单")
    @RequestMapping(value = "/api/cs/oc/oms/v1/orderDetention", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject orderDetention(HttpServletRequest request,
                                     @RequestBody JSONObject obj) {
        //记录日志信息
        log.debug("start OrderHoldCtrl.orderDetention.ReceiveParams=" + obj);
        ValueHolderV14<OrderHoldResult> vh = new ValueHolderV14<>();
        //获取当前登陆用户
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {

            OrderHoldCmd orderHoldCmd = (OrderHoldCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(), OrderHoldCmd.class.getName(),
                    "oms-fi", "1.0");
            ValueHolder valueHolder = orderHoldCmd.orderDetention(obj, user);
            //记录日志信息。Finish 标记结束
            log.debug("Finish  OrderHoldCtrl.orderDetention. Return Result=" + vh.toJSONObject());
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }

    /**
     * 手动卡单周期购订单释放
     *
     * @param request
     * @param obj
     * @return
     */
    @ApiOperation(value = "手动卡单周期购订单释放")
    @RequestMapping(value = "/api/cs/oc/oms/v1/cycleOrderDetentionRelease", method = {RequestMethod.POST, RequestMethod.GET})
    public JSONObject cycleOrderDetentionRelease(HttpServletRequest request, @RequestBody JSONObject obj) {
        ValueHolderV14<OrderHoldResult> vh = new ValueHolderV14<>();
        User user = r3PrimWebAuthService.getLoginPrimWebUser(request);
        try {
            obj.put("cycleOrderRelease", YesNoEnum.Y.getKey());
            ValueHolder valueHolder = orderHoldCmd.orderDetentionRelease(obj, user);
            return JSONObject.parseObject(JSONObject.toJSONString(valueHolder.toJSONObject(), SerializerFeature.WriteMapNullValue));
        } catch (NDSException e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(Resources.getMessage(e.getMessage()));
            return JSONObject.parseObject(JSONObject.toJSONString(vh.toJSONObject(), SerializerFeature.WriteMapNullValue));
        }
    }


}
