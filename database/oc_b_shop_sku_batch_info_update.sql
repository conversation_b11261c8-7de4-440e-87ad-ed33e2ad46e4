-- 为 oc_b_shop_sku_batch_info 表添加店铺ID和店铺名称字段，并修改生产日期字段类型
-- 执行时间：2024-12-16

-- 1. 添加店铺ID字段
ALTER TABLE oc_b_shop_sku_batch_info
ADD COLUMN shop_id bigint(20) DEFAULT NULL COMMENT '店铺ID' AFTER id;

-- 2. 添加店铺名称字段
ALTER TABLE oc_b_shop_sku_batch_info
ADD COLUMN shop_title varchar(200) DEFAULT NULL COMMENT '店铺名称' AFTER shop_code;

-- 3. 修改生产日期字段类型从datetime改为varchar(8)，用于存储yyyyMMdd格式
-- 注意：执行前请备份数据，此操作会转换现有数据格式
ALTER TABLE oc_b_shop_sku_batch_info
MODIFY COLUMN produce_date varchar(8) DEFAULT NULL COMMENT '生产日期(yyyyMMdd格式)';

-- 4. 更新现有数据：将datetime格式转换为yyyyMMdd字符串格式
UPDATE oc_b_shop_sku_batch_info
SET produce_date = DATE_FORMAT(STR_TO_DATE(produce_date, '%Y-%m-%d %H:%i:%s'), '%Y%m%d')
WHERE produce_date IS NOT NULL AND produce_date != '';

-- 5. 添加店铺ID索引
ALTER TABLE oc_b_shop_sku_batch_info
ADD INDEX idx_shop_id (shop_id);

-- 6. 添加店铺ID+SKU的联合索引
ALTER TABLE oc_b_shop_sku_batch_info
ADD INDEX idx_shop_id_sku (shop_id, sku);

-- 5. 查看表结构（验证）
-- DESC oc_b_shop_sku_batch_info;

-- 7. 更新后的完整表结构参考
/*
CREATE TABLE `oc_b_shop_sku_batch_info` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '店铺ID',
  `shop_code` varchar(50) NOT NULL COMMENT '店铺编码',
  `shop_title` varchar(200) DEFAULT NULL COMMENT '店铺名称',
  `sku` varchar(100) DEFAULT NULL COMMENT '商品编码',
  `produce_date` varchar(8) DEFAULT NULL COMMENT '生产日期(yyyyMMdd格式)',
  `version` bigint(20) DEFAULT NULL COMMENT '版本号',
  `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
  `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人ID',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人用户名',
  `creationdate` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人ID',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人用户名',
  `modifieddate` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `isactive` char(1) DEFAULT 'Y' COMMENT '是否可用',
  PRIMARY KEY (`id`),
  KEY `idx_shop_code` (`shop_code`),
  KEY `idx_sku` (`sku`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_shop_id_sku` (`shop_id`, `sku`)
) DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '店铺最新发货效期';
*/
