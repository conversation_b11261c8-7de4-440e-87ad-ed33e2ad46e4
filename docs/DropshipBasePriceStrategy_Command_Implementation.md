# 一件代发客户基价策略命令模式实现文档

## 实现概述

参考 `StCImperfectStrategySaveCmdImpl#execute` 的实现方式，采用命令模式实现一件代发客户基价策略的新增、编辑、审核、反审核功能，不通过前端API调用，而是通过系统内部的命令执行机制。

## 架构设计

### 1. 命令模式结构
```
API接口层 (Command Interface)
    ↓
命令实现层 (Command Implementation)
    ↓
业务服务层 (Service Layer)
    ↓
数据访问层 (Mapper Layer)
```

### 2. 核心组件

#### 2.1 API接口定义
- `StCDropshipBasePriceStrategySaveCmd` - 新增/编辑命令接口
- `StCDropshipBasePriceStrategyAuditCmd` - 审核命令接口
- `StCDropshipBasePriceStrategyUnAuditCmd` - 反审核命令接口

#### 2.2 命令实现类
- `StCDropshipBasePriceStrategySaveCmdImpl` - 新增/编辑命令实现
- `StCDropshipBasePriceStrategyAuditCmdImpl` - 审核命令实现
- `StCDropshipBasePriceStrategyUnAuditCmdImpl` - 反审核命令实现

#### 2.3 业务服务类
- `StCDropshipBasePriceStrategyService` - 业务服务接口
- `StCDropshipBasePriceStrategyServiceImpl` - 业务服务实现

## 文件结构

### 1. API接口层
```
OMS-CoreService/OMS-Order-API/src/main/java/com/jackrain/nea/oc/oms/api/
├── StCDropshipBasePriceStrategySaveCmd.java
├── StCDropshipBasePriceStrategyAuditCmd.java
└── StCDropshipBasePriceStrategyUnAuditCmd.java
```

### 2. 服务接口层
```
OMS-CoreService/OMS-Order-API/src/main/java/com/jackrain/nea/oc/oms/services/
└── StCDropshipBasePriceStrategyService.java
```

### 3. 服务实现层
```
OMS-CoreService/OMS-Order-Srv/src/main/java/com/jackrain/nea/oc/oms/services/
├── StCDropshipBasePriceStrategyServiceImpl.java
├── StCDropshipBasePriceStrategySaveCmdImpl.java
├── StCDropshipBasePriceStrategyAuditCmdImpl.java
└── StCDropshipBasePriceStrategyUnAuditCmdImpl.java
```

## 核心功能实现

### 1. 新增/编辑功能

#### 1.1 数据流程
```
QuerySession → CommandAdapterUtil.checkSaveSession() → 数据解析 → 校验 → 保存
```

#### 1.2 关键实现点
- 使用 `CommandAdapterUtil.checkSaveSession()` 进行参数校验
- 通过 `fixColumn` 获取主表和明细表数据
- 使用 `CommandAdapterUtil.defaultOperator()` 设置默认操作字段
- 支持新增和编辑的统一处理逻辑

#### 1.3 数据校验
```java
private String validateStrategyData(StCDropshipBasePriceStrategy strategy, 
                                   List<StCDropshipBasePriceStrategyDetail> detailList, 
                                   Long excludeId) {
    // 1. 店铺必填
    if (strategy.getShopId() == null) {
        return "请先选择店铺！";
    }
    
    // 2. 店铺不能与已配置且已审核的数据相同
    int existCount = strategyMapper.checkShopExists(strategy.getShopId(), excludeId);
    if (existCount > 0) {
        return "店铺已存在！";
    }
    
    // 3. 明细下不能有SKU相同的多条数据
    // ... 校验逻辑
}
```

### 2. 审核功能

#### 2.1 参数获取
```java
DefaultWebEvent event = querySession.getEvent();
JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));
Long id = param.getLong(OcCommonConstant.OBJ_ID);
String auditRemark = param.getString("auditRemark");
```

#### 2.2 状态更新
- 将 `audit_status` 从 0（待审核）更新为 1（已审核）
- 记录审核人信息和审核时间
- 记录审核备注

### 3. 反审核功能

#### 3.1 状态检查
- 只有已审核状态（audit_status=1）的策略才能反审核
- 反审核后状态变为 2（已反审核）

#### 3.2 状态更新
- 更新审核状态、审核人、审核时间和备注
- 使用 `CommandAdapterUtil.defaultOperator()` 设置修改人信息

## 数据处理机制

### 1. 主子表数据解析
```java
// 主表数据
JSONObject strategyJson = fixColumn.getJSONObject("st_c_dropship_base_price_strategy");
StCDropshipBasePriceStrategy strategy = JSON.parseObject(strategyJson.toJSONString(), StCDropshipBasePriceStrategy.class);

// 明细表数据
JSONArray detailArray = fixColumn.getJSONArray("st_c_dropship_base_price_strategy_detail");
List<StCDropshipBasePriceStrategyDetail> detailList = new ArrayList<>();
for (int i = 0; i < detailArray.size(); i++) {
    JSONObject detailJson = detailArray.getJSONObject(i);
    StCDropshipBasePriceStrategyDetail detail = JSON.parseObject(detailJson.toJSONString(), StCDropshipBasePriceStrategyDetail.class);
    detailList.add(detail);
}
```

### 2. 默认字段设置
```java
// 新增时设置默认值
strategy.setAuditStatus(0); // 默认待审核状态
strategy.setStatus("Y"); // 默认启用状态
strategy.setSkuCount(CollectionUtils.isNotEmpty(detailList) ? detailList.size() : 0);
CommandAdapterUtil.defaultOperator(strategy, user);
```

### 3. 明细表处理
```java
// 编辑时先删除原有明细
detailMapper.deleteByStrategyId(strategy.getId());

// 重新插入明细
if (CollectionUtils.isNotEmpty(detailList)) {
    for (StCDropshipBasePriceStrategyDetail detail : detailList) {
        detail.setStrategyId(strategy.getId());
        CommandAdapterUtil.defaultOperator(detail, user);
    }
    detailMapper.batchInsert(detailList);
}
```

## 错误处理机制

### 1. 参数校验
- 使用 `CommandAdapterUtil.checkSaveSession()` 进行统一参数校验
- 返回 `ValueHolder` 对象，包含校验结果和错误信息

### 2. 业务校验
- 自定义业务规则校验
- 返回具体的错误提示信息

### 3. 异常处理
```java
try {
    // 业务逻辑
    return ValueHolderUtils.getSuccessValueHolder("操作成功");
} catch (Exception e) {
    log.error("操作失败", e);
    throw new NDSException("操作失败：" + e.getMessage());
}
```

## 返回值处理

### 1. 成功返回
```java
return ValueHolderUtils.getSuccessValueHolder("操作成功");
```

### 2. 失败返回
```java
return ValueHolderUtils.getFailValueHolder("错误信息");
```

### 3. ValueHolder结构
- `isSuccess()` - 操作是否成功
- `getValue()` - 返回值
- `getMessage()` - 消息信息

## 事务管理

### 1. 事务注解
```java
@Transactional(rollbackFor = Exception.class)
public ValueHolder execute(QuerySession querySession) throws NDSException {
    // 业务逻辑
}
```

### 2. 事务范围
- 主表和明细表的操作在同一事务中
- 异常时自动回滚所有操作

## 日志记录

### 1. 操作日志
```java
log.info("新增一件代发客户基价策略成功，策略ID={}，操作人={}", strategy.getId(), user.getName());
log.info("审核一件代发客户基价策略成功，策略ID={}，操作人={}", id, user.getName());
```

### 2. 错误日志
```java
log.error("一件代发客户基价策略保存失败", e);
log.error("审核一件代发客户基价策略失败", e);
```

## 使用方式

### 1. 通过命令执行器调用
```java
// 新增/编辑
StCDropshipBasePriceStrategySaveCmd saveCmd = applicationContext.getBean(StCDropshipBasePriceStrategySaveCmd.class);
ValueHolder result = saveCmd.execute(querySession);

// 审核
StCDropshipBasePriceStrategyAuditCmd auditCmd = applicationContext.getBean(StCDropshipBasePriceStrategyAuditCmd.class);
ValueHolder result = auditCmd.execute(querySession);
```

### 2. QuerySession参数结构
```json
{
  "fixColumn": {
    "st_c_dropship_base_price_strategy": {
      "shopId": 123,
      "remark": "备注信息"
    },
    "st_c_dropship_base_price_strategy_detail": [
      {
        "skuCode": "SKU001",
        "basePrice": 100.00
      }
    ]
  }
}
```

## 优势特点

### 1. 架构优势
- 遵循命令模式，职责分离清晰
- 统一的参数校验和错误处理机制
- 支持事务管理和异常回滚

### 2. 扩展性
- 易于添加新的命令类型
- 统一的接口规范，便于维护
- 支持复杂的业务逻辑处理

### 3. 可维护性
- 清晰的分层架构
- 完善的日志记录
- 统一的返回值处理

这种命令模式的实现方式与系统现有架构保持一致，提供了完整的一件代发客户基价策略管理功能，支持复杂的主子表数据处理和完善的业务规则校验。
