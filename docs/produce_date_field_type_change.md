# OcBShopSkuBatchInfo.produceDate 字段类型变更文档

## 1. 变更概述

### 1.1 变更目的
将 `OcBShopSkuBatchInfo#produceDate` 字段类型从 `Date` 改为 `String`，以适应出库系统回传的 `yyyyMMdd` 格式数据，避免不必要的类型转换。

### 1.2 变更范围
- 数据库表字段类型变更
- 实体类字段类型修改
- Service接口和实现类方法签名调整
- 业务逻辑中的日期比较方式修改
- 相关调用代码的适配

## 2. 数据库变更

### 2.1 字段类型变更
| 字段名 | 原类型 | 新类型 | 说明 |
|--------|--------|--------|------|
| `produce_date` | datetime | varchar(8) | 存储yyyyMMdd格式的生产日期 |

### 2.2 数据迁移
```sql
-- 修改字段类型
ALTER TABLE oc_b_shop_sku_batch_info 
MODIFY COLUMN produce_date varchar(8) DEFAULT NULL COMMENT '生产日期(yyyyMMdd格式)';

-- 转换现有数据
UPDATE oc_b_shop_sku_batch_info 
SET produce_date = DATE_FORMAT(STR_TO_DATE(produce_date, '%Y-%m-%d %H:%i:%s'), '%Y%m%d') 
WHERE produce_date IS NOT NULL AND produce_date != '';
```

## 3. 代码变更

### 3.1 实体类变更
**文件**: `OMS-Model/src/main/java/com/jackrain/nea/oc/oms/model/table/OcBShopSkuBatchInfo.java`

**变更内容**:
```java
// 修改前
private Date produceDate;

// 修改后
private String produceDate;
```

### 3.2 Service接口变更
**文件**: `OMS-BLL/src/main/java/com/jackrain/nea/oc/oms/services/OcBShopSkuBatchInfoService.java`

**方法签名变更**:
```java
// 修改前
boolean saveOrUpdateSkuBatchInfo(String shopCode, String sku, Date produceDate);
SaveResult saveOrUpdateSkuBatchInfoWithResult(String shopCode, String sku, Date produceDate);
boolean saveOrUpdateSkuBatchInfo(Long shopId, String shopCode, String shopTitle, String sku, Date produceDate);
SaveResult saveOrUpdateSkuBatchInfoWithResult(Long shopId, String shopCode, String shopTitle, String sku, Date produceDate);

// 修改后
boolean saveOrUpdateSkuBatchInfo(String shopCode, String sku, String produceDate);
SaveResult saveOrUpdateSkuBatchInfoWithResult(String shopCode, String sku, String produceDate);
boolean saveOrUpdateSkuBatchInfo(Long shopId, String shopCode, String shopTitle, String sku, String produceDate);
SaveResult saveOrUpdateSkuBatchInfoWithResult(Long shopId, String shopCode, String shopTitle, String sku, String produceDate);
```

**内部类变更**:
```java
// SkuBatchInfo 类
private String produceDate; // 原为 Date produceDate

// SaveResult 类
private String oldProduceDate; // 原为 Date oldProduceDate
private String newProduceDate; // 原为 Date newProduceDate
```

### 3.3 Service实现类变更
**文件**: `OMS-BLL/src/main/java/com/jackrain/nea/oc/oms/services/impl/OcBShopSkuBatchInfoServiceImpl.java`

**日期比较逻辑变更**:
```java
// 修改前
if (existingProduceDate != null && !produceDate.after(existingProduceDate)) {
    // 跳过更新
}

// 修改后
if (StringUtils.isNotBlank(existingProduceDate) && produceDate.compareTo(existingProduceDate) <= 0) {
    // 跳过更新
}
```

### 3.4 Mapper接口变更
**文件**: `OMS-BLL/src/main/java/com/jackrain/nea/oc/oms/mapper/OcBShopSkuBatchInfoMapper.java`

**方法参数类型变更**:
```java
// 修改前
int updateSkuBatchInfo(@Param("produceDate") Date produceDate, ...);

// 修改后
int updateSkuBatchInfo(@Param("produceDate") String produceDate, ...);
```

### 3.5 业务调用代码变更
**文件**: `OMS-CoreService/OMS-Order-Process/src/main/java/com/jackrain/nea/oc/oms/mq/processor/impl/sgMq/OutOrderMqProcessorImpl.java`

**调用方式变更**:
```java
// 修改前
SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
Date produceDate = sdf.parse(produceDateStr);
saveResult = ocBShopSkuBatchInfoService.saveOrUpdateSkuBatchInfoWithResult(
    cpShop.getId(), shopCode, cpShop.getCpCShopTitle(), skuCode, produceDate);

// 修改后
// 直接使用字符串，无需转换
saveResult = ocBShopSkuBatchInfoService.saveOrUpdateSkuBatchInfoWithResult(
    cpShop.getId(), shopCode, cpShop.getCpCShopTitle(), skuCode, produceDateStr);
```

**日志输出变更**:
```java
// 修改前
new SimpleDateFormat("yyyyMMdd").format(saveResult.getOldProduceDate())

// 修改后
saveResult.getOldProduceDate() // 直接使用字符串
```

### 3.6 效期倒挂检查逻辑变更
**文件**: `OMS-BLL/src/main/java/com/jackrain/nea/oc/oms/services/audit/strategy/BaseAudit.java`

**日期比较逻辑变更**:
```java
// 修改前
Date recordedEarliestDate = shopSkuBatchInfo.getProduceDate();
Date actualEarliestDate = getEarliestOccupationDate(orderId, skuCode, billNo);
if (actualEarliestDate.before(recordedEarliestDate)) {
    // 存在效期倒挂
}

// 修改后
String recordedEarliestDate = shopSkuBatchInfo.getProduceDate();
String actualEarliestDate = getEarliestOccupationDate(orderId, skuCode, billNo);
if (actualEarliestDate.compareTo(recordedEarliestDate) < 0) {
    // 存在效期倒挂
}
```

**方法返回类型变更**:
```java
// 修改前
private Date getEarliestOccupationDate(Long orderId, String skuCode, String billNo);
private Date getProduceDateFromSgItem(SgBStoOutItem item);

// 修改后
private String getEarliestOccupationDate(Long orderId, String skuCode, String billNo);
private String getProduceDateFromSgItem(SgBStoOutItem item);
```

## 4. 优势分析

### 4.1 性能优势
- **减少类型转换**: 出库系统回传的就是 `yyyyMMdd` 格式字符串，无需转换为 Date 再转回字符串
- **简化比较逻辑**: 字符串比较比 Date 对象比较更直接高效
- **减少内存占用**: String 类型比 Date 对象占用更少内存

### 4.2 维护优势
- **格式统一**: 统一使用 `yyyyMMdd` 格式，避免时区和格式转换问题
- **代码简化**: 减少了 SimpleDateFormat 的使用和异常处理
- **数据一致性**: 避免了日期格式转换可能导致的数据不一致

### 4.3 业务优势
- **精确匹配**: 字符串比较确保了日期的精确匹配，避免时间部分的干扰
- **易于调试**: 日志中直接显示 `yyyyMMdd` 格式，便于问题排查

## 5. 注意事项

### 5.1 数据格式验证
- 新增了 `isValidDateFormat` 方法验证日期格式的有效性
- 确保存储的字符串符合 `yyyyMMdd` 格式要求

### 5.2 兼容性处理
- 保持了原有的方法名称，只修改了参数类型
- 添加了完善的异常处理和日志记录

### 5.3 测试重点
- 验证日期字符串比较的正确性
- 测试边界情况（如空值、无效格式等）
- 确保效期倒挂检查逻辑的准确性

## 6. 部署建议

### 6.1 部署顺序
1. 备份现有数据
2. 执行数据库DDL脚本
3. 验证数据转换结果
4. 部署应用代码
5. 验证功能正常

### 6.2 回滚方案
```sql
-- 如需回滚，恢复字段类型
ALTER TABLE oc_b_shop_sku_batch_info 
MODIFY COLUMN produce_date datetime DEFAULT NULL COMMENT '生产日期';

-- 转换数据格式
UPDATE oc_b_shop_sku_batch_info 
SET produce_date = STR_TO_DATE(produce_date, '%Y%m%d') 
WHERE produce_date IS NOT NULL AND produce_date REGEXP '^[0-9]{8}$';
```

## 7. 总结

本次变更将 `produceDate` 字段从 `Date` 类型改为 `String` 类型，主要目的是：

1. **适应数据源格式**: 出库系统回传的就是 `yyyyMMdd` 格式字符串
2. **提升性能**: 减少不必要的类型转换
3. **简化逻辑**: 统一使用字符串比较，避免日期对象比较的复杂性
4. **提高可维护性**: 减少日期格式转换相关的代码和潜在问题

变更后的系统将更加高效和稳定，同时保持了良好的向后兼容性。
