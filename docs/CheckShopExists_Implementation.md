# checkShopExists 方法实现说明

## 功能概述

`checkShopExists` 方法用于检查指定店铺是否已经存在已审核的一件代发客户基价策略配置，确保每个店铺只能有一个已审核的策略。

## 实现方式

### 1. 方法签名
```java
@Select("<script>" +
        "SELECT COUNT(1) " +
        "FROM st_c_dropship_base_price_strategy " +
        "WHERE shop_id = #{shopId} " +
        "AND audit_status = 1 " +
        "AND status = 'Y' " +
        "AND isactive = 'Y' " +
        "<if test='excludeId != null'> AND id != #{excludeId} </if>" +
        "</script>")
int checkShopExists(@Param("shopId") Long shopId, @Param("excludeId") Long excludeId);
```

### 2. 参数说明
- **shopId**: 要检查的店铺ID
- **excludeId**: 排除的策略ID（编辑时使用，避免检查自己）

### 3. 返回值
- **int**: 返回满足条件的策略数量
  - 0: 店铺不存在已审核的策略，可以新增或编辑
  - >0: 店铺已存在已审核的策略，不能重复配置

## SQL逻辑分析

### 1. 查询条件
```sql
SELECT COUNT(1) 
FROM st_c_dropship_base_price_strategy 
WHERE shop_id = #{shopId}           -- 指定店铺
AND audit_status = 1                -- 已审核状态
AND status = 'Y'                    -- 启用状态
AND isactive = 'Y'                  -- 有效记录
AND id != #{excludeId}              -- 排除指定ID（编辑时）
```

### 2. 条件说明

#### 2.1 基础条件
- `shop_id = #{shopId}`: 查询指定店铺的策略
- `audit_status = 1`: 只检查已审核的策略
- `status = 'Y'`: 只检查启用状态的策略
- `isactive = 'Y'`: 只检查有效的记录（逻辑删除标识）

#### 2.2 排除条件
- `<if test='excludeId != null'> AND id != #{excludeId} </if>`: 
  - 编辑时排除当前编辑的策略ID
  - 新增时 excludeId 为 null，不会添加此条件

### 3. 业务逻辑

#### 3.1 新增场景
```java
// 新增时 excludeId 为 null
int existCount = strategyMapper.checkShopExists(strategy.getShopId(), null);
if (existCount > 0) {
    return "店铺已存在！";
}
```

#### 3.2 编辑场景
```java
// 编辑时传入当前策略ID，避免检查自己
int existCount = strategyMapper.checkShopExists(strategy.getShopId(), strategy.getId());
if (existCount > 0) {
    return "店铺已存在！";
}
```

## 使用场景

### 1. 数据校验流程
```java
private String validateStrategyData(StCDropshipBasePriceStrategy strategy, 
                                   List<StCDropshipBasePriceStrategyDetail> detailList, 
                                   Long excludeId) {
    // 1. 店铺必填
    if (strategy.getShopId() == null) {
        return "请先选择店铺！";
    }

    // 2. 店铺不能与已配置且已审核的数据相同
    int existCount = strategyMapper.checkShopExists(strategy.getShopId(), excludeId);
    if (existCount > 0) {
        return "店铺已存在！";
    }

    // 3. 其他校验逻辑...
    return null;
}
```

### 2. 调用时机
- **新增策略时**: 检查店铺是否已有已审核的策略
- **编辑策略时**: 检查店铺是否与其他已审核的策略冲突

## 审核状态说明

### 1. 审核状态枚举
| 状态值 | 状态名称 | 说明 |
|--------|---------|------|
| 0 | 待审核 | 新增后的默认状态，可以编辑 |
| 1 | 已审核 | 审核通过的状态，不能编辑 |
| 2 | 已反审核 | 反审核后的状态，可以重新编辑 |

### 2. 为什么只检查已审核状态
- **待审核状态（0）**: 可能会被修改或删除，不算正式生效
- **已审核状态（1）**: 正式生效的策略，需要保证唯一性
- **已反审核状态（2）**: 已失效的策略，不影响新策略创建

## 业务规则

### 1. 唯一性约束
- 每个店铺只能有一个已审核的基价策略
- 待审核和已反审核的策略不影响此约束

### 2. 编辑限制
- 已审核的策略不能编辑
- 编辑时需要排除自己，避免误判

### 3. 状态流转
```
新增 → 待审核(0) → 审核 → 已审核(1)
                      ↓
                   反审核 → 已反审核(2)
```

## 错误处理

### 1. 校验失败
```java
if (existCount > 0) {
    return "店铺已存在！";
}
```

### 2. 前端提示
- 新增时：提示用户该店铺已配置策略
- 编辑时：提示用户不能修改为已存在的店铺

## 性能考虑

### 1. 索引优化
```sql
-- 建议添加复合索引
CREATE INDEX idx_shop_audit_status ON st_c_dropship_base_price_strategy 
(shop_id, audit_status, status, isactive);
```

### 2. 查询优化
- 使用 COUNT(1) 而不是 COUNT(*)
- 只查询必要的条件字段
- 利用索引快速定位

## 扩展性

### 1. 支持多状态检查
如果将来需要检查其他状态，可以修改SQL：
```sql
AND audit_status IN (1, 2)  -- 检查已审核和已反审核
```

### 2. 支持软删除恢复
当前实现已考虑 `isactive` 字段，支持软删除机制。

## 测试用例

### 1. 新增场景测试
```java
// 测试1: 店铺不存在策略，应该返回0
int count1 = mapper.checkShopExists(123L, null);
assertEquals(0, count1);

// 测试2: 店铺存在已审核策略，应该返回1
int count2 = mapper.checkShopExists(456L, null);
assertEquals(1, count2);
```

### 2. 编辑场景测试
```java
// 测试3: 编辑自己，应该返回0
int count3 = mapper.checkShopExists(456L, 789L);
assertEquals(0, count3);

// 测试4: 编辑为其他已存在店铺，应该返回1
int count4 = mapper.checkShopExists(456L, 999L);
assertEquals(1, count4);
```

这个实现确保了店铺策略的唯一性约束，同时支持新增和编辑场景的不同需求，是一个完整且健壮的业务校验机制。
