# 简化审核状态实现说明

## 简化概述

根据需求，只保留 `auditStatus` 字段，移除所有其他审核相关字段（审核人、审核时间、审核备注等），实现最简化的审核状态管理。

## 简化内容

### 1. 实体类简化

#### 1.1 保留字段
```java
/**
 * 审核状态（0:待审核，1:已审核，2:已反审核）
 */
@JSONField(name = "AUDIT_STATUS")
private Integer auditStatus;
```

#### 1.2 删除字段
```java
// 以下字段已删除
private Long auditorId;        // 审核人ID
private String auditorName;    // 审核人姓名
private Date auditTime;        // 审核时间
private String auditRemark;    // 审核备注
```

### 2. 数据库表结构

#### 2.1 必需字段
```sql
-- 只需要添加这一个字段
ALTER TABLE st_c_dropship_base_price_strategy 
ADD COLUMN audit_status int(11) DEFAULT 0 COMMENT '审核状态（0:待审核，1:已审核，2:已反审核）';
```

#### 2.2 不需要的字段
```sql
-- 以下字段不需要创建
-- auditor_id bigint(20) DEFAULT NULL COMMENT '审核人ID';
-- auditor_name varchar(50) DEFAULT NULL COMMENT '审核人姓名';
-- audit_time datetime DEFAULT NULL COMMENT '审核时间';
-- audit_remark varchar(500) DEFAULT NULL COMMENT '审核备注';
```

### 3. Mapper简化

#### 3.1 查询字段简化
```sql
-- 查询列表
SELECT s.id, s.shop_id, s.sku_count, s.remark, s.audit_status,
       s.ownerid, s.ownername, s.ownerename, s.creationdate,
       s.modifierid, s.modifiername, s.modifierename, s.modifieddate,
       shop.shop_name, shop.shop_code
FROM st_c_dropship_base_price_strategy s
LEFT JOIN cp_c_shop shop ON s.shop_id = shop.id
WHERE s.isactive = 'Y'
```

#### 3.2 条件查询
```sql
-- 只支持按审核状态查询
<if test='strategy.auditStatus != null'> 
    AND s.audit_status = #{strategy.auditStatus} 
</if>
```

### 4. Service简化

#### 4.1 审核方法简化
```java
@Override
@Transactional(rollbackFor = Exception.class)
public ValueHolder auditExecute(QuerySession querySession) throws NDSException {
    try {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));
        Long id = param.getLong(OcCommonConstant.OBJ_ID);
        
        // 状态检查
        StCDropshipBasePriceStrategy strategy = strategyMapper.selectById(id);
        if (strategy == null) {
            return ValueHolderUtils.getFailValueHolder("策略不存在");
        }
        if (strategy.getAuditStatus() != null && strategy.getAuditStatus() == 1) {
            return ValueHolderUtils.getFailValueHolder("策略已审核，无需重复审核");
        }

        // 简化的状态更新
        StCDropshipBasePriceStrategy updateStrategy = new StCDropshipBasePriceStrategy();
        updateStrategy.setId(id);
        updateStrategy.setAuditStatus(1); // 已审核
        CommandAdapterUtil.defaultOperator(updateStrategy, user);
        
        strategyMapper.updateById(updateStrategy);
        
        return ValueHolderUtils.getSuccessValueHolder("审核成功");
    } catch (Exception e) {
        log.error("审核一件代发客户基价策略失败", e);
        throw new NDSException("审核失败：" + e.getMessage());
    }
}
```

#### 4.2 反审核方法简化
```java
@Override
@Transactional(rollbackFor = Exception.class)
public ValueHolder unAuditExecute(QuerySession querySession) throws NDSException {
    try {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));
        Long id = param.getLong(OcCommonConstant.OBJ_ID);
        
        // 状态检查
        StCDropshipBasePriceStrategy strategy = strategyMapper.selectById(id);
        if (strategy == null) {
            return ValueHolderUtils.getFailValueHolder("策略不存在");
        }
        if (strategy.getAuditStatus() == null || strategy.getAuditStatus() != 1) {
            return ValueHolderUtils.getFailValueHolder("只有已审核的策略才能反审核");
        }

        // 简化的状态更新
        StCDropshipBasePriceStrategy updateStrategy = new StCDropshipBasePriceStrategy();
        updateStrategy.setId(id);
        updateStrategy.setAuditStatus(2); // 已反审核
        CommandAdapterUtil.defaultOperator(updateStrategy, user);
        
        strategyMapper.updateById(updateStrategy);
        
        return ValueHolderUtils.getSuccessValueHolder("反审核成功");
    } catch (Exception e) {
        log.error("反审核一件代发客户基价策略失败", e);
        throw new NDSException("反审核失败：" + e.getMessage());
    }
}
```

## 状态管理

### 1. 审核状态定义
| 状态值 | 状态名称 | 说明 |
|--------|---------|------|
| 0 | 待审核 | 新增后的默认状态，可以编辑、删除 |
| 1 | 已审核 | 审核通过的状态，不能编辑、删除，参与业务逻辑 |
| 2 | 已反审核 | 反审核后的状态，可以重新编辑 |

### 2. 状态流转
```
新增 → 待审核(0) → 审核 → 已审核(1)
       ↑                    ↓
       ←←← 可重新编辑 ←← 反审核 ← 已反审核(2)
```

### 3. 业务规则
- **新增时**: 默认设置为待审核状态（0）
- **编辑限制**: 只有非已审核状态（0或2）可以编辑
- **删除限制**: 只有非已审核状态（0或2）可以删除
- **唯一性检查**: 只检查已审核状态（1）的策略

## 接口参数

### 1. 审核接口
```json
{
  "param": {
    "objId": 123  // 只需要策略ID
  }
}
```

### 2. 反审核接口
```json
{
  "param": {
    "objId": 123  // 只需要策略ID
  }
}
```

## 优势特点

### 1. 简化优势
- **减少字段**: 只保留核心的审核状态字段
- **简化逻辑**: 不需要处理审核人、审核时间等额外信息
- **降低复杂度**: 减少数据维护和查询的复杂性
- **提高性能**: 减少数据传输和存储开销

### 2. 功能完整性
- **状态控制**: 完整的审核状态流转
- **权限控制**: 基于状态的编辑和删除权限
- **业务逻辑**: 支持完整的业务流程
- **数据一致性**: 保证数据的一致性和完整性

### 3. 扩展性
- **易于扩展**: 如果将来需要审核信息，可以轻松添加
- **向后兼容**: 现有逻辑不受影响
- **灵活配置**: 可以根据需要调整审核流程

## 使用示例

### 1. 新增策略
```java
// 自动设置为待审核状态
strategy.setAuditStatus(0);
```

### 2. 查询策略
```java
// 查询所有待审核的策略
StCDropshipBasePriceStrategyDTO query = new StCDropshipBasePriceStrategyDTO();
query.setAuditStatus(0);
List<StCDropshipBasePriceStrategyDTO> list = strategyMapper.selectStrategyList(query);
```

### 3. 审核操作
```java
// 通过命令执行审核
StCDropshipBasePriceStrategyAuditCmd auditCmd = applicationContext.getBean(StCDropshipBasePriceStrategyAuditCmd.class);
ValueHolder result = auditCmd.execute(querySession);
```

### 4. 状态检查
```java
// 检查是否可以编辑
if (strategy.getAuditStatus() != null && strategy.getAuditStatus() == 1) {
    return "已审核的策略不能编辑";
}
```

## 总结

通过简化审核相关字段，实现了：

1. **最小化设计**: 只保留核心的审核状态字段
2. **功能完整**: 支持完整的审核流程和业务逻辑
3. **易于维护**: 减少了代码复杂度和维护成本
4. **性能优化**: 减少了数据传输和存储开销

这种简化的实现方式既满足了业务需求，又保持了系统的简洁性和高效性。
