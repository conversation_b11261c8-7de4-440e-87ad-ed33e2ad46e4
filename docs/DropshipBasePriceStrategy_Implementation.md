# 一件代发客户基价策略功能实现文档

## 功能概述

实现了一件代发客户基价策略的完整功能，包括新增、编辑、审核、反审核等操作，支持主子表结构的数据管理。

## 数据库表结构

### 主表：st_c_dropship_base_price_strategy
```sql
CREATE TABLE `st_c_dropship_base_price_strategy` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `shop_id` bigint(20) NOT NULL COMMENT '店铺id',
  `sku_count` int(11) DEFAULT 0 COMMENT '配置的SKU数量',
  `status` varchar(1) NOT NULL DEFAULT 'Y' COMMENT '状态（Y:启用，N：停用）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `audit_status` int(11) DEFAULT 0 COMMENT '审核状态（0:待审核，1:已审核，2:已反审核）',
  `auditor_id` bigint(20) DEFAULT NULL COMMENT '审核人ID',
  `auditor_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
  `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
  `isactive` char(1) DEFAULT 'Y' COMMENT '是否启用（Y:启用，N：未启用）',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人名称',
  `creationdate` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  `modifieddate` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_creation_date` (`creationdate`)
) DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '一件代发客户基价策略';
```

### 子表：st_c_dropship_base_price_strategy_detail
```sql
CREATE TABLE `st_c_dropship_base_price_strategy_detail` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `strategy_id` bigint(20) NOT NULL COMMENT '策略id（关联主表）',
  `sku_code` varchar(64) NOT NULL COMMENT 'SKU编码',
  `base_price` decimal(10,2) NOT NULL COMMENT '录单基价',
  `ad_client_id` bigint(20) DEFAULT '37' COMMENT '所属公司',
  `ad_org_id` bigint(20) DEFAULT '27' COMMENT '所属组织',
  `isactive` char(1) DEFAULT 'Y' COMMENT '是否启用（Y:启用，N：未启用）',
  `ownerid` bigint(20) DEFAULT NULL COMMENT '创建人',
  `ownerename` varchar(50) DEFAULT NULL COMMENT '创建人姓名',
  `ownername` varchar(50) DEFAULT NULL COMMENT '创建人名称',
  `creationdate` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modifierid` bigint(20) DEFAULT NULL COMMENT '修改人',
  `modifierename` varchar(50) DEFAULT NULL COMMENT '修改人姓名',
  `modifiername` varchar(50) DEFAULT NULL COMMENT '修改人名称',
  `modifieddate` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_strategy_sku` (`strategy_id`, `sku_code`),
  KEY `idx_strategy_id` (`strategy_id`),
  KEY `idx_sku_code` (`sku_code`)
) DEFAULT CHARSET = utf8mb4 ROW_FORMAT = DYNAMIC COMMENT = '一件代发客户基价策略明细';
```

## 实现的文件结构

### 1. 实体类
- `StCDropshipBasePriceStrategy.java` - 主表实体类
- `StCDropshipBasePriceStrategyDetail.java` - 明细表实体类
- `StCDropshipBasePriceStrategyDTO.java` - 数据传输对象

### 2. Mapper层
- `StCDropshipBasePriceStrategyMapper.java` - 主表Mapper接口
- `StCDropshipBasePriceStrategyDetailMapper.java` - 明细表Mapper接口
- `StCDropshipBasePriceStrategyMapper.xml` - 主表SQL映射文件
- `StCDropshipBasePriceStrategyDetailMapper.xml` - 明细表SQL映射文件

### 3. Service层
- `StCDropshipBasePriceStrategyService.java` - Service接口
- `StCDropshipBasePriceStrategyServiceImpl.java` - Service实现类

### 4. Controller层
- `StCDropshipBasePriceStrategyController.java` - REST API控制器

## 核心功能实现

### 1. 新增功能
- **接口**：`POST /api/dropship/basePrice/add`
- **校验规则**：
  - 店铺必填，否则提示：请先选择店铺！
  - 店铺不能与已配置且已审核的数据相同，否则提示：店铺已存在！
  - 明细下不能有SKU相同的多条数据，否则提示：存在相同商品的数据，请检测！
  - SKU编码不能为空
  - 录单基价必须大于0
- **默认状态**：保存成功后默认为待审核状态（audit_status=0）

### 2. 编辑功能
- **接口**：`POST /api/dropship/basePrice/edit`
- **限制条件**：已审核的策略不能编辑
- **校验规则**：与新增相同
- **处理逻辑**：先删除原有明细，再重新插入新明细

### 3. 审核功能
- **接口**：`POST /api/dropship/basePrice/audit/{id}`
- **限制条件**：只有待审核状态的策略才能审核
- **处理逻辑**：更新审核状态为已审核（audit_status=1），记录审核人和审核时间

### 4. 反审核功能
- **接口**：`POST /api/dropship/basePrice/unaudit/{id}`
- **限制条件**：只有已审核状态的策略才能反审核
- **处理逻辑**：更新审核状态为已反审核（audit_status=2），记录反审核人和时间

### 5. 删除功能
- **接口**：`DELETE /api/dropship/basePrice/delete/{id}`
- **限制条件**：已审核的策略不能删除
- **处理逻辑**：逻辑删除主表和明细表数据

### 6. 查询功能
- **列表查询**：`POST /api/dropship/basePrice/list`
- **详情查询**：`GET /api/dropship/basePrice/detail/{id}`
- **支持条件**：按店铺、状态、审核状态等条件查询

## 审核状态说明

| 状态值 | 状态名称 | 说明 |
|--------|---------|------|
| 0 | 待审核 | 新增后的默认状态 |
| 1 | 已审核 | 审核通过的状态 |
| 2 | 已反审核 | 反审核后的状态 |

## 业务规则

### 1. 数据唯一性
- 每个店铺只能有一个已审核的策略
- 同一策略下的SKU编码不能重复

### 2. 状态控制
- 只有待审核状态的策略可以编辑和删除
- 只有待审核状态的策略可以审核
- 只有已审核状态的策略可以反审核

### 3. 数据完整性
- 主表记录SKU数量，与明细表数量保持一致
- 删除采用逻辑删除，保证数据可追溯

## API接口说明

### 1. 查询策略列表
```
POST /api/dropship/basePrice/list
Content-Type: application/json

{
  "shopId": 123,
  "status": "Y",
  "auditStatus": 1,
  "shopName": "店铺名称"
}
```

### 2. 新增策略
```
POST /api/dropship/basePrice/add
Content-Type: application/json

{
  "shopId": 123,
  "remark": "备注信息",
  "detailList": [
    {
      "skuCode": "SKU001",
      "basePrice": 100.00
    },
    {
      "skuCode": "SKU002", 
      "basePrice": 200.00
    }
  ]
}
```

### 3. 审核策略
```
POST /api/dropship/basePrice/audit/123?auditRemark=审核通过
```

## 注意事项

### 1. 数据库字段
- 需要在主表中添加审核相关字段（audit_status、auditor_id、auditor_name、audit_time、audit_remark）
- 明细表的strategy_id和sku_code组合需要唯一约束

### 2. 事务处理
- 所有写操作都使用事务注解，确保数据一致性
- 主子表操作在同一事务中完成

### 3. 异常处理
- 完善的参数校验和异常处理
- 详细的错误日志记录

### 4. 性能考虑
- 查询时关联店铺表获取店铺名称
- 使用批量插入提高明细数据插入性能

这个实现提供了完整的一件代发客户基价策略管理功能，满足了新增、编辑、审核、反审核的业务需求，并包含了完善的数据校验和状态控制机制。
