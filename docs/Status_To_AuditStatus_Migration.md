# Status字段迁移为AuditStatus字段说明

## 修改概述

将 `StCDropshipBasePriceStrategy` 实体类中的 `status` 字段替换为 `audit_status` 字段，统一使用审核状态来管理策略的生命周期。

## 修改原因

### 1. 业务逻辑统一
- 原来的 `status` 字段（Y/N）和 `audit_status` 字段（0/1/2）功能重复
- 审核状态已经能够完整表达策略的状态，无需额外的启用/停用状态
- 简化业务逻辑，避免状态冲突

### 2. 数据模型优化
- 减少冗余字段，提高数据一致性
- 统一状态管理，便于维护和扩展
- 符合审核流程的业务特点

## 修改内容

### 1. 实体类修改

#### 1.1 StCDropshipBasePriceStrategy.java
**删除字段**：
```java
// 删除的字段
/**
 * 状态（Y:启用，N：停用）
 */
@JSONField(name = "STATUS")
private String status;
```

**保留字段**：
```java
// 保留的审核状态字段
/**
 * 审核状态（0:待审核，1:已审核，2:已反审核）
 */
@JSONField(name = "AUDIT_STATUS")
private Integer auditStatus;
```

### 2. Mapper修改

#### 2.1 查询语句优化
**修改前**：
```sql
SELECT s.id, s.shop_id, s.sku_count, s.status, s.remark, s.audit_status, ...
WHERE s.isactive = 'Y' 
AND s.status = #{strategy.status}
AND s.audit_status = #{strategy.auditStatus}
```

**修改后**：
```sql
SELECT s.id, s.shop_id, s.sku_count, s.remark, s.audit_status, ...
WHERE s.isactive = 'Y' 
AND s.audit_status = #{strategy.auditStatus}
```

#### 2.2 checkShopExists方法优化
**修改前**：
```sql
SELECT COUNT(1) 
FROM st_c_dropship_base_price_strategy 
WHERE shop_id = #{shopId} 
AND audit_status = 1 
AND status = 'Y'           -- 删除此条件
AND isactive = 'Y'
```

**修改后**：
```sql
SELECT COUNT(1) 
FROM st_c_dropship_base_price_strategy 
WHERE shop_id = #{shopId} 
AND audit_status = 1       -- 只检查审核状态
AND isactive = 'Y'
```

### 3. Service修改

#### 3.1 新增策略时的默认值设置
**修改前**：
```java
strategy.setAuditStatus(0); // 默认待审核状态
strategy.setStatus("Y");    // 默认启用状态 - 删除此行
```

**修改后**：
```java
strategy.setAuditStatus(0); // 默认待审核状态
```

## 状态管理机制

### 1. 审核状态定义
| 状态值 | 状态名称 | 说明 | 对应原status |
|--------|---------|------|-------------|
| 0 | 待审核 | 新增后的默认状态，可以编辑 | Y（启用但未生效） |
| 1 | 已审核 | 审核通过的状态，正式生效 | Y（启用且生效） |
| 2 | 已反审核 | 反审核后的状态，可以重新编辑 | N（停用） |

### 2. 状态流转
```
新增 → 待审核(0) → 审核 → 已审核(1) → 反审核 → 已反审核(2)
       ↑                                        ↓
       ←←←←←←←←← 可重新编辑 ←←←←←←←←←←←←←←←←←←
```

### 3. 业务规则
- **待审核(0)**: 可以编辑、删除，不参与业务逻辑
- **已审核(1)**: 不能编辑、删除，参与业务逻辑，店铺唯一性检查
- **已反审核(2)**: 可以重新编辑，不参与业务逻辑

## 数据库表结构调整

### 1. 建议的表结构
```sql
-- 可以考虑删除status字段（如果确认不再使用）
ALTER TABLE st_c_dropship_base_price_strategy DROP COLUMN status;

-- 或者保留但不使用（为了兼容性）
-- 保持现有表结构不变，只在应用层面不使用status字段
```

### 2. 索引优化
```sql
-- 原索引（如果存在）
DROP INDEX idx_status;

-- 新索引
CREATE INDEX idx_audit_status ON st_c_dropship_base_price_strategy (audit_status);

-- 复合索引优化
CREATE INDEX idx_shop_audit_status ON st_c_dropship_base_price_strategy 
(shop_id, audit_status, isactive);
```

## 影响分析

### 1. 正面影响
- **简化逻辑**: 减少状态字段，避免状态冲突
- **提高一致性**: 统一使用审核状态管理
- **便于维护**: 减少代码复杂度
- **业务清晰**: 状态流转更加明确

### 2. 兼容性考虑
- **数据库兼容**: 保留status字段但不使用，确保向后兼容
- **接口兼容**: DTO继承主表，自动继承字段变更
- **查询兼容**: 移除status条件，不影响现有查询逻辑

### 3. 测试要点
- **状态流转测试**: 验证0→1→2的状态流转
- **唯一性检查**: 验证只检查audit_status=1的记录
- **编辑权限**: 验证只有非已审核状态可以编辑
- **查询功能**: 验证列表查询和详情查询正常

## 迁移步骤

### 1. 代码修改（已完成）
- [x] 实体类删除status字段
- [x] Mapper删除status相关查询条件
- [x] Service删除status字段设置
- [x] 更新checkShopExists逻辑

### 2. 测试验证
- [ ] 单元测试：验证实体类字段正确
- [ ] 集成测试：验证Mapper查询正常
- [ ] 业务测试：验证完整的增删改查流程
- [ ] 状态测试：验证审核状态流转

### 3. 数据库优化（可选）
- [ ] 评估是否删除status字段
- [ ] 优化相关索引
- [ ] 清理历史数据（如果需要）

## 总结

通过将 `status` 字段替换为 `audit_status` 字段，实现了：

1. **业务逻辑简化**: 统一使用审核状态管理策略生命周期
2. **数据模型优化**: 减少冗余字段，提高数据一致性
3. **代码维护性**: 简化状态判断逻辑，减少出错可能
4. **业务流程清晰**: 审核状态更好地反映业务流程

这次修改保持了向后兼容性，同时优化了业务逻辑，为后续的功能扩展奠定了良好的基础。
