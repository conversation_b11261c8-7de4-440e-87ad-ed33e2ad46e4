# 奶卡作废逻辑优化总结

## 修改概述

根据需求调整，对奶卡作废相关逻辑进行了重新设计，主要涉及以下几个方面：

1. **保持先解冻再作废的流程**：维持原有的解冻→作废的业务流程
2. **调整状态更新时机**：将奶卡状态改为待作废的逻辑从出库后移到解冻成功后
3. **完善作废任务的容错处理**：处理没有已发货退款单的场景

## 具体修改内容

### 1. OutOrderMqProcessorImpl 修改

#### 1.1 保持原有逻辑

**文件位置**：`OMS-CoreService/OMS-Order-Process/src/main/java/com/jackrain/nea/oc/oms/mq/processor/impl/sgMq/OutOrderMqProcessorImpl.java`

**修改说明**：
- 撤销了之前添加的 `handleNaikaUnfreezeAndVoid` 方法
- 保持原有的 `checkPlatformStatusAndCreateVoidData` 方法逻辑
- 只负责创建奶卡作废中间表数据，不直接处理解冻和状态更新

**当前逻辑**：
```java
// 查询该订单的奶卡信息
List<OcBOrderNaiKa> naikaList = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(ocBOrder.getId());
if (CollectionUtils.isNotEmpty(naikaList)) {
    // 为每个奶卡生成作废数据
    createNaikaVoidData(ocBOrder, naikaList);
}
```

**职责说明**：
- 仅负责检测平台状态并创建奶卡作废记录
- 不涉及解冻状态和奶卡状态的直接修改
- 保持与现有业务流程的兼容性

### 2. UnFreezeNaiKaOrderTask 修改

#### 2.1 新增 checkAndUpdateNaikaStatusForVoid 方法

**文件位置**：`OMS-CoreService/OMS-Order-Task/src/main/java/com/jackrain/nea/oc/oms/task/naika/UnFreezeNaiKaOrderTask.java`

**功能说明**：
- 在奶卡解冻成功后，检查是否存在奶卡作废记录
- 如果存在作废记录，将奶卡状态改为待作废
- 如果不存在作废记录，按正常流程更新为解冻成功状态

**核心逻辑**：
```java
private void checkAndUpdateNaikaStatusForVoid(OcBOrder ocBOrder, List<String> unfreezeCodeList) {
    // 1. 查询该订单是否存在奶卡作废记录
    // 2. 如果存在作废记录，将解冻成功的奶卡状态改为待作废
    // 3. 如果不存在作废记录，正常更新为解冻成功状态
}
```

**处理步骤**：
1. **查询作废记录**：检查订单是否存在奶卡作废记录
2. **条件判断**：根据是否存在作废记录决定后续处理
3. **状态更新**：更新奶卡状态为待作废或解冻成功
4. **订单状态同步**：同步更新订单的奶卡状态字段
5. **异常处理**：异常情况下仍按正常流程处理

#### 2.2 修改解冻成功处理逻辑

**修改内容**：
- 在解冻成功后，不直接调用 `updateNaiKaStatus` 方法
- 改为调用 `checkAndUpdateNaikaStatusForVoid` 方法进行条件判断

**修改前**：
```java
// 执行成功
updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
// ... 其他更新逻辑
naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
applicationContext.getBean(UnFreezeNaiKaOrderTask.class)
        .updateNaiKaStatus(new ArrayList<>(unfreezeCodeList), ocBOrder.getId(),
                OmsOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus(),
                OcBOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus(), "");
```

**修改后**：
```java
// 执行成功
updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
// ... 其他更新逻辑
naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);

// 检查是否需要作废奶卡
checkAndUpdateNaikaStatusForVoid(ocBOrder, unfreezeCodeList);
```

#### 2.3 添加必要的依赖注入

**新增注入**：
```java
@Autowired
private OcBOrderNaikaVoidMapper ocBOrderNaikaVoidMapper;
```

**新增import**：
```java
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaikaVoidMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
```

### 3. DoCardVoidTask 修改

#### 2.1 修改 doSuccess 方法

**文件位置**：`OMS-CoreService/OMS-Order-Task/src/main/java/com/jackrain/nea/oc/oms/task/naika/DoCardVoidTask.java`

**修改内容**：
- 添加对 `ocBReturnAfSendId` 的空值检查
- 只有当存在关联的已发货退款单时才进行更新操作
- 增加详细的日志记录

**修改前**：
```java
// 修改已发货退款单
updateOcBReturnAfSend.setId(naikaVoid.getOcBReturnAfSendId());
updateOcBReturnAfSend.setModifierid(Long.valueOf(user.getId()));
updateOcBReturnAfSend.setModifieddate(new Date());
updateOcBReturnAfSend.setModifiername(user.getName());
updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_SUCCESS.getCode());
returnAfSendMapper.updateById(updateOcBReturnAfSend);
```

**修改后**：
```java
// 修改已发货退款单（如果存在的话）
if (naikaVoid.getOcBReturnAfSendId() != null) {
    OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
    updateOcBReturnAfSend.setId(naikaVoid.getOcBReturnAfSendId());
    updateOcBReturnAfSend.setModifierid(Long.valueOf(user.getId()));
    updateOcBReturnAfSend.setModifieddate(new Date());
    updateOcBReturnAfSend.setModifiername(user.getName());
    updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_SUCCESS.getCode());
    returnAfSendMapper.updateById(updateOcBReturnAfSend);
    
    log.info("奶卡作废成功，同时更新已发货退款单，作废记录ID={}，退款单ID={}",
            naikaVoid.getId(), naikaVoid.getOcBReturnAfSendId());
} else {
    log.info("奶卡作废成功，无关联的已发货退款单，作废记录ID={}", naikaVoid.getId());
}
```

#### 2.2 修改 doFail 方法

**修改内容**：
- 同样添加对 `ocBReturnAfSendId` 的空值检查
- 增加详细的日志记录
- 确保在没有已发货退款单的情况下也能正常处理

**修改逻辑**：
```java
// 修改已发货退款单（如果存在的话）
if (naikaVoid.getOcBReturnAfSendId() != null) {
    // 更新已发货退款单状态
    // 记录成功日志
} else {
    // 记录无关联退款单的日志
}
```

## 业务流程优化

### 优化前的流程

1. 订单出库后检测到平台状态为"已取消&关闭"
2. 创建奶卡作废记录
3. 等待定时任务处理奶卡解冻
4. 解冻完成后直接更新为解冻成功状态
5. 定时任务处理奶卡作废
6. 作废成功/失败后更新已发货退款单（可能因为没有退款单而报错）

### 优化后的流程

1. 订单出库后检测到平台状态为"已取消&关闭"
2. 创建奶卡作废记录
3. 等待定时任务处理奶卡解冻
4. 解冻完成后**检查是否存在作废记录**（新增）
5. **如果存在作废记录，将奶卡状态改为待作废**（新增）
6. **如果不存在作废记录，正常更新为解冻成功状态**（新增）
7. 定时任务处理奶卡作废
8. 作废成功/失败后**有条件地**更新已发货退款单（优化）

## 优化效果

### 1. 业务流程优化
- **保持原有解冻流程**：维持先解冻再作废的业务逻辑，确保业务完整性
- **智能状态判断**：在解冻成功后智能判断是否需要作废，提高处理精确性
- **时机优化**：将状态更新时机从出库后调整到解冻成功后，更符合业务逻辑

### 2. 稳定性提升
- **容错性增强**：处理没有已发货退款单的场景，避免空指针异常
- **日志完善**：增加详细的操作日志，便于问题排查
- **异常处理**：完善的异常处理机制，确保在异常情况下仍能正常处理

### 3. 业务逻辑优化
- **逻辑更合理**：在解冻成功的时机判断是否需要作废，避免状态冲突
- **状态管理更清晰**：明确的状态流转，便于监控和管理
- **条件判断**：基于实际业务需求进行条件判断，提高处理准确性

## 注意事项

### 1. 数据一致性
- 确保解冻状态和奶卡状态的更新是原子性的
- 添加了事务控制，保证数据一致性

### 2. 兼容性
- 保持了原有的接口和数据结构不变
- 新增的逻辑不影响现有的业务流程

### 3. 监控和日志
- 增加了详细的操作日志
- 便于后续的业务分析和问题排查

## 相关枚举和常量

### UnFreezeEnum（解冻状态枚举）
- `UN_FREEZE`：待解冻
- `UN_FREEZE_SUCCESS`：解冻成功
- `UN_FREEZE_FAIL`：解冻失败

### OmsOrderNaiKaStatusEnum（奶卡状态枚举）
- `TO_VOID`：待作废
- `VOID_SUCCESS`：作废成功
- `VOID_FAIL`：作废失败

### NaikaVoidStatusEnum（奶卡作废状态枚举）
- `VOID`：待作废
- `VOID_SUCCESS`：作废成功
- `VOID_FAIL`：作废失败

## 测试建议

### 1. 单元测试
- 测试 `handleNaikaUnfreezeAndVoid` 方法的各种场景
- 测试 `doSuccess` 和 `doFail` 方法的空值处理

### 2. 集成测试
- 测试完整的奶卡作废流程
- 验证数据一致性和状态流转

### 3. 回归测试
- 确保现有功能不受影响
- 验证异常场景的处理

这次优化主要解决了奶卡作废流程中的时机和稳定性问题，通过在合适的时机进行状态判断和更新，以及增强容错性，提升了整体的业务处理能力和逻辑合理性。

## 关键改进点总结

### 1. 时机优化
- **从出库后改为解冻成功后**：将奶卡状态更新的时机从订单出库后调整到奶卡解冻成功后
- **条件判断**：只有在解冻成功且存在作废记录的情况下才将状态改为待作废
- **保持流程完整性**：维持了先解冻再作废的完整业务流程

### 2. 逻辑优化
- **智能判断**：基于实际的作废记录存在情况进行智能判断
- **状态一致性**：确保奶卡状态与业务流程的一致性
- **异常容错**：在异常情况下仍能保证正常的业务流程

### 3. 代码结构优化
- **职责分离**：出库处理只负责创建作废记录，解冻任务负责状态更新
- **可维护性**：清晰的方法职责划分，便于后续维护和扩展
- **日志完善**：详细的日志记录，便于问题排查和业务分析
