# OcBShopSkuBatchInfo 表字段增强维护文档

## 1. 变更概述

### 1.1 变更目的
为 `oc_b_shop_sku_batch_info` 表增加店铺ID（`shop_id`）和店铺名称（`shop_title`）字段，以支持更灵活的店铺信息管理和查询。

### 1.2 变更范围
- 数据库表结构变更
- 实体类字段增加
- Mapper接口方法扩展
- Service接口和实现类增强

## 2. 数据库变更

### 2.1 新增字段
| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| `shop_id` | bigint | 20 | NULL | YES | 店铺ID |
| `shop_title` | varchar | 200 | NULL | YES | 店铺名称 |

### 2.2 新增索引
| 索引名 | 字段 | 类型 | 说明 |
|--------|------|------|------|
| `idx_shop_id` | shop_id | 单列索引 | 提高按店铺ID查询性能 |
| `idx_shop_id_sku` | shop_id, sku | 联合索引 | 提高按店铺ID+SKU查询性能 |

### 2.3 执行脚本
```sql
-- 添加店铺ID字段
ALTER TABLE oc_b_shop_sku_batch_info 
ADD COLUMN shop_id bigint(20) DEFAULT NULL COMMENT '店铺ID' AFTER id;

-- 添加店铺名称字段
ALTER TABLE oc_b_shop_sku_batch_info 
ADD COLUMN shop_title varchar(200) DEFAULT NULL COMMENT '店铺名称' AFTER shop_code;

-- 添加索引
ALTER TABLE oc_b_shop_sku_batch_info ADD INDEX idx_shop_id (shop_id);
ALTER TABLE oc_b_shop_sku_batch_info ADD INDEX idx_shop_id_sku (shop_id, sku);
```

## 3. 代码变更

### 3.1 实体类变更
**文件**: `OMS-Model/src/main/java/com/jackrain/nea/oc/oms/model/table/OcBShopSkuBatchInfo.java`

**新增字段**:
```java
@JSONField(name = "SHOP_ID")
private Long shopId;

@JSONField(name = "SHOP_TITLE")
private String shopTitle;
```

### 3.2 Mapper接口变更
**文件**: `OMS-BLL/src/main/java/com/jackrain/nea/oc/oms/mapper/OcBShopSkuBatchInfoMapper.java`

**新增方法**:
- `selectByShopIdAndSku(Long shopId, String sku)` - 根据店铺ID和SKU查询
- `selectByShopId(Long shopId)` - 根据店铺ID查询所有记录
- `selectByShopIdAndSkuList(Long shopId, List<String> skuList)` - 根据店铺ID批量查询

### 3.3 Service接口变更
**文件**: `OMS-BLL/src/main/java/com/jackrain/nea/oc/oms/services/OcBShopSkuBatchInfoService.java`

**新增方法**:
- `getByShopIdAndSku(Long shopId, String sku)` - 根据店铺ID和SKU查询
- `getByShopId(Long shopId)` - 根据店铺ID查询所有记录
- `saveOrUpdateSkuBatchInfo(Long shopId, String shopCode, String shopTitle, String sku, Date produceDate)` - 基于店铺ID保存
- `saveOrUpdateSkuBatchInfoWithResult(Long shopId, String shopCode, String shopTitle, String sku, Date produceDate)` - 基于店铺ID保存（带结果）
- `getByShopIdAndSkuList(Long shopId, List<String> skuList)` - 根据店铺ID批量查询

### 3.4 Service实现类变更
**文件**: `OMS-BLL/src/main/java/com/jackrain/nea/oc/oms/services/impl/OcBShopSkuBatchInfoServiceImpl.java`

**实现特点**:
- 支持基于店铺ID的查询和保存
- 保存时同时更新店铺编码和店铺名称
- 保持原有基于店铺编码的方法兼容性

## 4. 使用示例

### 4.1 基于店铺ID查询
```java
// 根据店铺ID和SKU查询
OcBShopSkuBatchInfo batchInfo = ocBShopSkuBatchInfoService.getByShopIdAndSku(1001L, "SKU001");

// 根据店铺ID查询所有SKU
List<OcBShopSkuBatchInfo> batchInfoList = ocBShopSkuBatchInfoService.getByShopId(1001L);
```

### 4.2 基于店铺ID保存
```java
// 保存SKU批次信息
boolean success = ocBShopSkuBatchInfoService.saveOrUpdateSkuBatchInfo(
    1001L,           // 店铺ID
    "SHOP001",       // 店铺编码
    "测试店铺",       // 店铺名称
    "SKU001",        // SKU编码
    new Date()       // 生产日期
);

// 保存并获取详细结果
SaveResult result = ocBShopSkuBatchInfoService.saveOrUpdateSkuBatchInfoWithResult(
    1001L, "SHOP001", "测试店铺", "SKU001", new Date()
);
```

### 4.3 批量查询
```java
// 根据店铺ID批量查询SKU
List<String> skuList = Arrays.asList("SKU001", "SKU002", "SKU003");
List<OcBShopSkuBatchInfo> batchInfoList = 
    ocBShopSkuBatchInfoService.getByShopIdAndSkuList(1001L, skuList);
```

## 5. 兼容性说明

### 5.1 向后兼容
- 原有基于店铺编码的方法保持不变
- 新增字段允许为空，不影响现有数据
- 现有业务逻辑无需修改

### 5.2 数据迁移
- 新增字段初始值为NULL
- 可根据需要编写数据迁移脚本，从店铺编码关联查询店铺ID和名称

### 5.3 建议的数据迁移脚本
```sql
-- 根据店铺编码更新店铺ID和名称（示例）
UPDATE oc_b_shop_sku_batch_info b 
INNER JOIN cp_shop s ON b.shop_code = s.ecode 
SET b.shop_id = s.id, b.shop_title = s.title 
WHERE b.shop_id IS NULL;
```

## 6. 测试建议

### 6.1 单元测试
- 测试新增的Mapper方法
- 测试新增的Service方法
- 验证数据保存和查询的正确性

### 6.2 集成测试
- 验证基于店铺ID的完整业务流程
- 测试新旧方法的兼容性
- 验证索引性能

### 6.3 性能测试
- 验证新增索引的查询性能
- 测试大数据量下的查询效率

## 7. 部署注意事项

### 7.1 部署顺序
1. 执行数据库DDL脚本
2. 部署应用代码
3. 执行数据迁移脚本（如需要）
4. 验证功能正常

### 7.2 回滚方案
```sql
-- 如需回滚，删除新增字段和索引
ALTER TABLE oc_b_shop_sku_batch_info DROP INDEX idx_shop_id_sku;
ALTER TABLE oc_b_shop_sku_batch_info DROP INDEX idx_shop_id;
ALTER TABLE oc_b_shop_sku_batch_info DROP COLUMN shop_title;
ALTER TABLE oc_b_shop_sku_batch_info DROP COLUMN shop_id;
```

## 8. 总结

本次变更为 `oc_b_shop_sku_batch_info` 表增加了店铺ID和店铺名称字段，提供了更灵活的店铺信息管理能力。变更保持了向后兼容性，新增的方法可以与现有方法并存使用。通过合理的索引设计，确保了查询性能的优化。
