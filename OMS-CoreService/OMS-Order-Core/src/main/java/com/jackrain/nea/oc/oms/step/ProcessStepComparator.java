package com.jackrain.nea.oc.oms.step;

import java.util.Comparator;

/**
 * 流程处理阶段排序比较器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 17:32
 */
public class ProcessStepComparator implements Comparator<ProcessStepInfo<?>> {
    @Override
    public int compare(ProcessStepInfo info1, ProcessStepInfo info2) {
        return Integer.compare(info1.getOrder(), info2.getOrder());
    }
}
