package com.jackrain.nea.oc.oms.resource;

import com.jackrain.nea.oc.oms.util.LockOrderType;

/**
 * Redis 关键字资源
 *
 * @author: 易邵峰
 * @since: 2019-01-25
 * create at : 2019-01-25 22:31
 */
public class ProcessRedisKeyResources {

    /**
     * 构造函数（隐藏）
     */
    private ProcessRedisKeyResources() {

    }

    /**
     * 创建锁定订单Redis Key值
     *
     * @param orderId       订单ID值
     * @param lockOrderType 锁定订单类型
     * @return 锁定订单Redis Key值
     */
    public static String buildLockOrderKey(long orderId, LockOrderType lockOrderType) {
        /**
         * sunyongsheng add 添加判断 占单、拆单、传wms、审核 与转单区分
         */
        if (lockOrderType == LockOrderType.UNCONFIRMED_ORDER
                || lockOrderType == LockOrderType.SPLIT_ORDER
                || lockOrderType == LockOrderType.CALL_WMS_PROCESS
                || lockOrderType == LockOrderType.PROVIDER_WMS_PROCESS
                || lockOrderType == LockOrderType.AUDIT_ORDER) {

            return "oc:oms:order:" + orderId;
        } else {
            return "oms:process:" + lockOrderType.toRedisKeyword() + ":order:" + orderId;
        }
    }

    /**
     * 创建锁定订单关联冲突业务的Redis Key值
     *
     * @param tid           交易单号
     * @param lockOrderType 锁定订单类型
     */
    public static String buildLockOrderTidKey(String tid, LockOrderType lockOrderType) {
        /**
         * 通用转单冲突的业务需要通过平台单号来加锁，否则业务会相互冲突
         */
        if (lockOrderType == LockOrderType.TRANSFER_STANDPLAT_EXCHANGE
                || lockOrderType == LockOrderType.CURRENCY_REFUND
                || lockOrderType == LockOrderType.TRANSFER_STANDPLAT_ORDER) {
            // 通用正向和退单在转换时，针对退都业务都有处理，故这里通过redis加锁避免相互影响 20221128
            return "oms:process:" + LockOrderType.TRANSFER_STANDPLAT_ORDER.toRedisKeyword() + ":order:tid:" + tid;
        } else if(lockOrderType == LockOrderType.TRANSFER_TAOBAO_EXCHANGE
                || lockOrderType == LockOrderType.TRANSFER_TAOBAO_REFUND
                || lockOrderType == LockOrderType.TRANSFER_TAOBAO_ORDER){
            return "oms:process:" + LockOrderType.TRANSFER_TAOBAO_ORDER.toRedisKeyword() + ":order:tid:" + tid;
        }else if(lockOrderType == LockOrderType.TRANSFER_JINGDONG_REFUND
                || lockOrderType == LockOrderType.TRANSFER_JINGDONG_ORDER
                || lockOrderType == LockOrderType.TRANSFER_JINGDONG_CANCEL_ORDER){
            return "oms:process:" + LockOrderType.TRANSFER_JINGDONG_ORDER.toRedisKeyword() + ":order:tid:" + tid;
        }else {
            return null;
        }
    }

    /**
     * 构建redis key
     *
     * @param topic
     * @param messageId
     * @return
     */
    public static String buildLockMessageLogKey(String topic, String messageId) {
        return new StringBuffer().append("oc:oms:mq:message:").append(topic).append(":").append(messageId).toString();
    }

}
