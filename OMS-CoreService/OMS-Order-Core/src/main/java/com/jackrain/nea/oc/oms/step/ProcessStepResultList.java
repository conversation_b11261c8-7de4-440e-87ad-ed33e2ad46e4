package com.jackrain.nea.oc.oms.step;

import java.util.ArrayList;
import java.util.List;

/**
 * 操作结果列表
 *
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 21:30
 */
public class ProcessStepResultList {

    private List<ProcessStepResult> stepResultList;

    private int itemIndex = 0;

    public ProcessStepResultList() {
        this.stepResultList = new ArrayList<>();
    }

    /**
     * 添加步骤操作结果
     *
     * @param resultInfo 步骤操作结果
     */
    public void add(ProcessStepResult resultInfo) {
        this.stepResultList.add(resultInfo);
    }

    /**
     * 移除步骤操作结果
     *
     * @param resultInfo 步骤操作结果
     */
    public void remove(ProcessStepResult resultInfo) {
        this.stepResultList.remove(resultInfo);
    }

    /**
     * 清空列表对象
     */
    public void clear() {
        this.stepResultList.clear();
    }

    /**
     * 依据Index获取阶段处理结果
     *
     * @param index Index值
     * @return 阶段处理结果值
     */
    public ProcessStepResult get(int index) {
        return this.stepResultList.get(index);
    }

    /**
     * 列表大小
     *
     * @return 列表大小
     */
    public int size() {
        return this.stepResultList.size();
    }

    /**
     * 列表对象
     *
     * @return 列表对象
     */
    public List<ProcessStepResult> getItems() {
        return this.stepResultList;
    }

    /**
     * 是否处理成功（未包含Failed状态）
     *
     * @return true=成功；false=失败
     */
    public boolean isProcessSuccess() {
        for (ProcessStepResult stepResult : this.stepResultList) {
            if (stepResult.getStatus() == StepStatus.FAILED) {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否处理成功（未包含Failed，finish状态）
     *
     * @return true=成功；false=失败
     */
    public boolean isProcessFishSuccess() {
        for (ProcessStepResult stepResult : this.stepResultList) {
            if (stepResult.getStatus() == StepStatus.FAILED) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取最后失败阶段结果
     *
     * @return 最后失败阶段结果
     */
    public ProcessStepResult getLastFailedProcessStepResult() {
        for (int i = this.stepResultList.size() - 1; i >= 0; i--) {
            ProcessStepResult stepResult = this.stepResultList.get(i);
            if (stepResult.getStatus() == StepStatus.FAILED) {
                return stepResult;
            }
        }
        return null;
    }

    /**
     * 获取最后失败或者完成阶段结果
     *
     * @return 最后失败阶段结果
     */
    public ProcessStepResult getLastFaileOrFinishProcessStepResult() {
        for (int i = this.stepResultList.size() - 1; i >= 0; i--) {
            ProcessStepResult stepResult = this.stepResultList.get(i);
            if (stepResult.getStatus() == StepStatus.FAILED || stepResult.getStatus() == StepStatus.FINISHED) {
                return stepResult;
            }
        }
        return null;
    }

}
