package com.jackrain.nea.oc.oms.process;

import com.jackrain.nea.resource.SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 多线程处理单据处理器
 *
 * @author: 易邵峰
 * @since: 2019-03-23
 * create at : 2019-03-23 12:31
 */
@Slf4j
@Component
public class MultiThreadOrderProcessor {
//    /**
//     * 线程池最大超时时间。
//     * 默认：10分钟
//     */
//    private static final int EXECUTE_THREAD_POOL_TIMEOUT = 10 * 60 * 1000;

    /**
     * 开始多线程运行处理
     *
     * @param <T>               订单单据对象
     * @param orderProcess      订单处理器
     * @param operateObjectList 订单列表
     */
    public <T> void startMultiThreadExecute(AbstractOrderProcess<T> orderProcess, List<T> operateObjectList) {
        for (T orderInfo : operateObjectList) {
            orderProcess.start(orderInfo, true, SystemUserResource.getRootUser());
        }
    }
}
