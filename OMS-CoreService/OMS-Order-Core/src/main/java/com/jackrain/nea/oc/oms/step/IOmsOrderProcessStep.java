package com.jackrain.nea.oc.oms.step;

import com.jackrain.nea.web.face.User;

/**
 * OMS 平台订单处理步骤处理器
 *
 * @param <T> 操作数据对象
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:42
 */
public interface IOmsOrderProcessStep<T> {

    /**
     * 开始处理逻辑
     *
     * @param orderInfo     订单信息
     * @param preStepResult 上一阶段处理逻辑结果
     * @param isAutoMakeup  是否为自动补偿服务
     * @param operateUser   操作用户
     * @return 处理结果
     */
    ProcessStepResult<T> startProcess(T orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup,
                                      User operateUser);
}
