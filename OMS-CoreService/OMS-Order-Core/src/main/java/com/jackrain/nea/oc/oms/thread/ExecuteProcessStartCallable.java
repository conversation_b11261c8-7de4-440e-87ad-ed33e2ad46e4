package com.jackrain.nea.oc.oms.thread;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;

import java.util.concurrent.Callable;

/**
 * @author: 易邵峰
 * @since: 2019-03-12
 * create at : 2019-03-12 22:29
 */
public class ExecuteProcessStartCallable implements Callable<ProcessStepResultList> {

    private OperateOrderMqInfo orderMqInfo;

    private IMqOrderDetailProcessor orderProcess;

    public ExecuteProcessStartCallable(IMqOrderDetailProcessor orderProcess, OperateOrderMqInfo orderMqInfo) {
        this.orderMqInfo = orderMqInfo;
        this.orderProcess = orderProcess;
    }

    @Override
    public ProcessStepResultList call() {
        return this.orderProcess.start(this.orderMqInfo);
    }
}
