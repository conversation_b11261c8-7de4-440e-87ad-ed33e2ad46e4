package com.jackrain.nea.oc.oms.annotation;

import java.lang.annotation.*;

/**
 * 处理阶段Step定义
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:36
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Step {
    /**
     * 执行顺序值
     *
     * @return 执行顺序值
     */
    int order() default 1;

    /**
     * 描述信息
     *
     * @return 描述信息
     */
    String description() default "";

    /**
     * 是否为最终必须执行阶段
     * 注意点：在整个Step过程中只能出现一个Step设置为Finally。如果设置了多个，则默认选择第一个进行Finally执行。
     *
     * @return 是否为最终必须执行阶段。true=必须执行。
     */
    boolean isFinallyMustStep() default false;

}
