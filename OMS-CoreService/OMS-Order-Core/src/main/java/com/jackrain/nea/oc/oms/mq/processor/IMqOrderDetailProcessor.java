package com.jackrain.nea.oc.oms.mq.processor;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;

/**
 * MQ 订单消息详细处理器
 *
 * @author: 易邵峰
 * @since: 2019-03-06
 * create at : 2019-03-06 14:52
 */
public interface IMqOrderDetailProcessor {

    /**
     * 开始运行订单MQ信息
     *
     * @param orderMqInfo 订单MQ信息
     * @return 运行Step列表结果
     */
    ProcessStepResultList start(OperateOrderMqInfo orderMqInfo);

    /**
     * 检查接口是否可以运行
     *
     * @param orderMqInfo 订单MQ信息
     * @return 是否可以运行。True=可以运行
     */
    boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo);
}
