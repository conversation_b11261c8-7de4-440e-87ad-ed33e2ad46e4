package com.jackrain.nea.oc.oms.process;

import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;

import java.util.concurrent.Callable;

/**
 * 多线程运行订单处理器
 *
 * @param <T> 多线程处理对象泛型
 * @author: 易邵峰
 * @since: 2019-03-23
 * create at : 2019-03-23 12:29
 */
public class ExecuteOrderProcessStartCallable<T> implements Callable<ProcessStepResultList> {

    private T orderInfo;

    private AbstractOrderProcess orderProcess;

    public ExecuteOrderProcessStartCallable(AbstractOrderProcess orderProcess, T orderInfo) {
        this.orderInfo = orderInfo;
        this.orderProcess = orderProcess;
    }

    @Override
    public ProcessStepResultList call() {
        return this.orderProcess.start(orderInfo, true, SystemUserResource.getRootUser());
    }
}
