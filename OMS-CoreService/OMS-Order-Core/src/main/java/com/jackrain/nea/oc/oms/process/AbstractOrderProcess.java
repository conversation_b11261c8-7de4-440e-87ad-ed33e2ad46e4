package com.jackrain.nea.oc.oms.process;

import com.jackrain.nea.log.LogCat;
import com.jackrain.nea.log.LogEvent;
import com.jackrain.nea.log.LogTransaction;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.exception.ProcessOrderException;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.resource.ProcessRedisKeyResources;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepComparator;
import com.jackrain.nea.oc.oms.step.ProcessStepInfo;
import com.jackrain.nea.oc.oms.step.ProcessStepInfoList;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.lang.NonNull;
import org.springframework.util.ClassUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 抽象的订单处理类
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @param <T> 单据泛型内容
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 17:46
 */
@Slf4j
public abstract class AbstractOrderProcess<T> implements ApplicationContextAware {

    /**
     * 当前操作节点信息列表
     */
    private final ProcessStepInfoList<T> processStepList;

    /**
     * 是否已经初始化对象
     */
    private boolean isInitialled = false;

    private static final String LOG_EVENT_STATUS_KEY = "Status";

    private static final String LOG_EVENT_MESSAGE_KEY = "Message";

    private static final String LOG_EVENT_DATA_KEY = "Data";

    /**
     * 构造函数
     */
    public AbstractOrderProcess() {
        this.processStepList = new ProcessStepInfoList<>();
    }

    /**
     * Redis锁默认1分钟
     */
    private static final int LOCK_ORDER_AUTO_TIMEOUT = 0;

    private ApplicationContext applicationContext;

    /**
     * 获取当前操作流程节点下的子级包名
     * 主要用于大的流程节点只是部分子级名不一致的场景
     *
     * @return 当前操作流程节点下的子级包名
     */
    protected abstract String getChildPackageName();

    /**
     * 获取当前Processor运行的包名
     *
     * @return Processor运行的包名列表
     */
    private List<String> getPackageNameList() {
        List<String> packageNameList = new ArrayList<>();
        String fullClassName = this.getClass().getName();
        fullClassName = fullClassName.replace("." + this.getClass().getSimpleName(), "");

        packageNameList.add(fullClassName);
        if (!StringUtils.isEmpty(this.getChildPackageName())) {
            packageNameList.add(fullClassName + "." + this.getChildPackageName());
        }
        return packageNameList;
    }

    /**
     * 初始化处理流程节点信息
     */
    @SuppressWarnings({"unchecked", "deprecated"})
    @PostConstruct
    protected void initialProcessStepInfo() {
        if (applicationContext == null) {
            return;
        }
        try {
            ClassPathScanningCandidateComponentProvider componentProvider = new ClassPathScanningCandidateComponentProvider(false);

            componentProvider.addIncludeFilter(new AnnotationTypeFilter(Step.class));
            List<String> packageNameList = this.getPackageNameList();
            for (String packageName : packageNameList) {
                Set<BeanDefinition> beanDefinitions = componentProvider.findCandidateComponents(packageName);
                for (BeanDefinition beanDefinition : beanDefinitions) {
                    String beanClassName = beanDefinition.getBeanClassName();
                    try {
                        if (StringUtils.isNotEmpty(beanClassName)) {
                            Class<?> clazz = ClassUtils.forName(beanClassName, this.getClass().getClassLoader());
                            Object obj = clazz.newInstance();
                            //给实例化的类注入需要的bean (@Autowired)如果不注入，被@Autowired注解的变量会报空指针
                            applicationContext.getAutowireCapableBeanFactory().autowireBean(obj);
                            if (obj instanceof IOmsOrderProcessStep<?>) {
                                Step ann = AnnotationUtils.findAnnotation(clazz, Step.class);
                                ProcessStepInfo<T> stepInfo = new ProcessStepInfo<>();
                                stepInfo.setOrder(ann.order());
                                stepInfo.setFinallyMustStep(ann.isFinallyMustStep());
                                stepInfo.setProcessStep((IOmsOrderProcessStep<T>) obj);

                                processStepList.add(stepInfo);
                            }
                        }
                    } catch (ClassNotFoundException ex) {
                        ex.printStackTrace();
                    }
                }
            }

            processStepList.getItems().sort(new ProcessStepComparator());
            for (ProcessStepInfo<?> stepInfo : processStepList.getItems()) {
                log.info(this.getClass().getSimpleName() + ";Order=" + stepInfo.getOrder() + ";Class=" + stepInfo.getProcessStep().getClass().getName());
            }

            this.isInitialled = true;
        } catch (Exception ex) {
            log.error("AbstractOrderProcessor.initialProcessStepInfo.Error", ex);
        }
    }

    /**
     * 获取当前处理单据的订单ID号
     *
     * @param orderInfo 订单单据
     * @return 订单ID
     */
    protected abstract long getProcessOrderId(T orderInfo);

    /**
     * 获取当前处理单据的订单编号
     *
     * @param orderInfo 订单单据
     * @return 订单编号
     */
    protected abstract String getProcessOrderNo(T orderInfo);

    /**
     * 获取来源平台单号
     *
     * @param orderInfo 订单单据
     * @return 获取来源平台单号
     */
    protected String getSourceTid(T orderInfo) {
        return null;
    }

    /**
     * 获取当前处理单据的订单编号-- 返回值为Long
     *
     * @param orderInfo 订单单据
     * @return 订单编号
     */
    protected long getLongProcessOrderNo(T orderInfo) {
        return 0L;
    }

    /**
     * 获取当前操作单据的锁定单据类型
     *
     * @return 锁定单据类型
     */
    protected abstract LockOrderType getCurrentLockOrderType();

    /**
     * 当前处理渠道类型
     * 主要用于：转单过程中用到，出现异常时进行补偿服务存储。其他处理流程都设置为Default。
     *
     * @return 处理渠道类型
     */
    protected abstract ChannelType getCurrentChannelType();

    /**
     * 获取当前补偿订单类型（根据补偿订单类型进行处理是否需要进行订单处理）
     *
     * @return 当前补偿订单类型
     */
    protected abstract MakeupOrderType getCurrentMakeupOrderType();

    /**
     * 处理异常信息
     *
     * @param orderId      订单ID
     * @param orderNo      订单编号
     * @param isAutoMakeup 是否自动补偿运行
     * @param transaction  日志记录
     * @param ex           处理单据异常信息
     */
    private void startProcessExceptionStep(long orderId, String orderNo, boolean isAutoMakeup, LogTransaction transaction, ProcessOrderException ex) {
        LogEvent lockEvent = LogCat.newEvent(this.getClass().getSimpleName(), "ExceptionStep");
        String errorMessage = ex.getMessage();
        errorMessage += ";OrderId=" + orderId + ";OrderNo=" + orderNo + ";IsAutoMakup=" + isAutoMakeup;
        lockEvent.addData(LOG_EVENT_STATUS_KEY, StepStatus.FAILED.toString());
        lockEvent.addData(LOG_EVENT_MESSAGE_KEY, errorMessage);
        lockEvent.complete();
        transaction.addEvent(lockEvent);
    }

    /**
     * 开始运行服务
     *
     * @param orderInfo    当前操作的订单数据
     * @param isAutoMakeup 是否自动运行
     * @param operateUser  当前操作人员信息
     * @return 运行的操作结果
     */
    public ProcessStepResultList start(T orderInfo, boolean isAutoMakeup, User operateUser) {
        if (!this.isInitialled) {
            this.initialProcessStepInfo();
        }

        String orderNo = this.getProcessOrderNo(orderInfo);
        long orderId = this.getProcessOrderId(orderInfo);
        String sourceTid = this.getSourceTid(orderInfo);
        if (log.isDebugEnabled()) {
            log.debug("OrderProcess.Start.OrderId={},OrderNo={},sourceTid={}", orderId, orderNo,sourceTid);
        }
        ProcessStepResultList stepResultList = new ProcessStepResultList();

        LogTransaction transaction = LogCat.newTransaction(this.getClass().getSimpleName(), this.getProcessOrderId(orderInfo), this.getProcessOrderNo(orderInfo), "Start");
        try {
            ProcessStepResult<T> preStepResult = null;
            try {
                // 订单ID主锁
                String lockRedisKey = ProcessRedisKeyResources.buildLockOrderKey(orderId, this.getCurrentLockOrderType());

                // tid关联业务锁
                String tidLockRedisKey = null;
                RedisReentrantLock tidRedisLock = null;
                if (StringUtils.isNotBlank(sourceTid)) {
                    tidLockRedisKey = ProcessRedisKeyResources.buildLockOrderTidKey(sourceTid, this.getCurrentLockOrderType());
                    if (StringUtils.isNotBlank(tidLockRedisKey)) {
                        tidRedisLock = new RedisReentrantLock(tidLockRedisKey, "oms-master");
                    }
                }

                LogEvent lockEvent = LogCat.newEvent(this.getClass().getSimpleName(), "LockOrder.Key=" + lockRedisKey);
                RedisReentrantLock redisLock = new RedisReentrantLock(lockRedisKey, "oms-master");
                try {
                    // 解决通用退单和正向转单相互冲突的问题，这里增加交易单号锁进行控制
                    if (redisLock.tryLock(LOCK_ORDER_AUTO_TIMEOUT, TimeUnit.MILLISECONDS)
                            && (tidRedisLock == null || tidRedisLock.tryLock(LOCK_ORDER_AUTO_TIMEOUT, TimeUnit.MILLISECONDS))) {
                        lockEvent.addData(LOG_EVENT_STATUS_KEY, StepStatus.SUCCESS.toString());
                        lockEvent.addData(LOG_EVENT_MESSAGE_KEY, "锁定单据成功完成");
                        lockEvent.complete();
                        transaction.addEvent(lockEvent);

                        LogEvent startEvent = LogCat.newEvent(this.getClass().getSimpleName(), "LockOrder.Key=" + lockRedisKey);

                        preStepResult = this.startDetailProcessStep(orderInfo, isAutoMakeup, operateUser, stepResultList, transaction);

                        startEvent.complete();
                        transaction.addEvent(startEvent);
                    } else {
                        String errorMessage = "单据ID=" + orderId + ";OrderNo=" + orderNo + "锁定失败;退出转单服务;";
                        lockEvent.addData(LOG_EVENT_STATUS_KEY, StepStatus.FAILED.toString());
                        lockEvent.addData(LOG_EVENT_MESSAGE_KEY, errorMessage);
                        lockEvent.complete();
                        transaction.addEvent(lockEvent);
                        updateTaskStatus(orderId);
                        throw new ProcessOrderException(errorMessage);
                    }
                } catch (ProcessOrderException ex) {
                    log.error("AbstractOrderProcess.Start.Error, ProcessOrderException msg:"+ex.getMessage(), ex);
                    this.startProcessExceptionStep(orderId, orderNo, isAutoMakeup, transaction, ex);
                } finally {
                    redisLock.unlock();
                    if (tidRedisLock != null) {
                        tidRedisLock.unlock();
                    }
                }
            } finally {
                processFinalStep(orderInfo, isAutoMakeup, operateUser, stepResultList, preStepResult, transaction);
            }
        } catch (Exception ex) {
            log.error("AbstractOrderProcess.Start.Error,msg:"+ex.getMessage(), ex);
            LogCat.logError(transaction, ex);
        } finally {
            transaction.complete();
//            log.info("ProcessResult={}", transaction.toJson().toJSONString());
        }
        return stepResultList;
    }

    public void updateTaskStatus(long orderId) {

    }

    /**
     * 处理详细阶段内容
     *
     * @param orderInfo      订单信息
     * @param isAutoMakeup   是否为自动补偿运行
     * @param operateUser    操作用户
     * @param stepResultList 操作阶段结果列表
     * @param transaction    记录日志信息
     * @return 最终一步的处理结果
     */
    private ProcessStepResult<T> startDetailProcessStep(T orderInfo, boolean isAutoMakeup, User operateUser, ProcessStepResultList stepResultList, LogTransaction transaction) {
        String orderNo = this.getProcessOrderNo(orderInfo);
        long orderId = this.getProcessOrderId(orderInfo);
        if (log.isDebugEnabled()) {
            log.debug("OrderProcess.StartDetailProcessStep.OrderId={},OrderNo={}", orderId, orderNo);
        }
        ProcessStepResult<T> preStepResult = null;
        ProcessStepInfo<T> stepInfo = this.processStepList.first();
        while (stepInfo != null) {
            IOmsOrderProcessStep<T> platformProcessStep = stepInfo.getProcessStep();
            LogEvent event = LogCat.newEvent(platformProcessStep.getClass().getSimpleName(), "Start");

            if (log.isDebugEnabled()) {
                log.debug("OrderProcess.Start.{};OrderId={},OrderNo={}", platformProcessStep.getClass().getSimpleName(), orderId, orderNo);
            }

            preStepResult = platformProcessStep.startProcess(orderInfo, preStepResult, isAutoMakeup, operateUser);

            if (preStepResult != null) {
                event.addData(LOG_EVENT_STATUS_KEY, preStepResult.getStatus().toString());
                Object nextOperateObj = preStepResult.getNextStepOperateObj();
                String dataMessage = nextOperateObj == null ? "" : nextOperateObj.toString();
                event.addData(LOG_EVENT_DATA_KEY, dataMessage);
                event.addData(LOG_EVENT_MESSAGE_KEY, preStepResult.getMessage());
                preStepResult.setCurrentStep(platformProcessStep);
                preStepResult.setNextStepOperateObj(nextOperateObj);
                stepResultList.add(preStepResult);

                if (preStepResult.getStatus() == StepStatus.FINISHED || preStepResult.getStatus() == StepStatus.FAILED) {
                    event.complete();
                    transaction.addEvent(event);
                    break;
                } else {
                    Class<? extends IOmsOrderProcessStep<T>> nextStep = preStepResult.getNextStepClass();
                    stepInfo = this.processStepList.next(platformProcessStep, nextStep);
                    if (log.isDebugEnabled()) {
                        log.debug("OrderProcess.Start.CurrentStep={}; nextStep={};OrderId={},OrderNo={}", platformProcessStep, nextStep, orderId, orderNo);
                    }
                }

                if (CollectionUtils.isNotEmpty(preStepResult.getLogEventList())) {
                    for (LogEvent logEvent : preStepResult.getLogEventList()) {
                        event.addChildEvent(logEvent);
                    }
                }

                event.complete();
                transaction.addEvent(event);
            } else {
                event.complete();
                transaction.addEvent(event);
                break;
            }
        }
        return preStepResult;
    }

    /**
     * 处理最终步骤内容
     *
     * @param orderInfo      订单信息
     * @param isAutoMakeup   是否为自动补偿运行
     * @param operateUser    操作用户
     * @param stepResultList 操作阶段结果列表
     * @param preStepResult  上一阶段节点处理流程结果
     * @param transaction    记录日志信息
     */
    private void processFinalStep(T orderInfo, boolean isAutoMakeup, User operateUser, ProcessStepResultList stepResultList, ProcessStepResult<T> preStepResult, LogTransaction transaction) {
        ProcessStepInfo<T> finallyStep = this.processStepList.getFinallyStep();

        if (finallyStep != null && finallyStep.getProcessStep() != null) {
            LogEvent finallyEvent = LogCat.newEvent(finallyStep.getProcessStep().getClass().getSimpleName(), "Finally");
            ProcessStepResult<T> finallyResult = finallyStep.getProcessStep().startProcess(orderInfo, preStepResult, isAutoMakeup, operateUser);
            if (finallyResult != null) {
                finallyEvent.addData(LOG_EVENT_STATUS_KEY, finallyResult.getStatus().toString());
                Object nextOperateObj = finallyResult.getNextStepOperateObj();
                String dataMessage = nextOperateObj == null ? "" : nextOperateObj.toString();
                finallyEvent.addData(LOG_EVENT_DATA_KEY, dataMessage);
                finallyEvent.addData(LOG_EVENT_MESSAGE_KEY, finallyResult.getMessage());
                finallyResult.setNextStepOperateObj(nextOperateObj);
                finallyResult.setCurrentStep(finallyStep.getProcessStep());
                stepResultList.add(finallyResult);
            }
            finallyEvent.complete();
            transaction.addEvent(finallyEvent);
        }
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}