package com.jackrain.nea.oc.oms.step;

import java.util.ArrayList;
import java.util.List;

/**
 * 处理阶段Step信息列表
 *
 * @param <T> 操作对象泛型
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 19:57
 */
public class ProcessStepInfoList<T> {

    private List<ProcessStepInfo<T>> stepInfoList;

    public ProcessStepInfoList() {
        this.stepInfoList = new ArrayList<>();
    }

    /**
     * 依据StepInfo Class对象查找对应的处理Class
     *
     * @param stepInfoClass 当前查找的Step Class对象
     * @return 在列表中的Index值。-1 未找到
     */
    public int getProcessStepInfoIndex(Class<? extends IOmsOrderProcessStep<T>> stepInfoClass) {
        int findIndex = -1;
        if (stepInfoClass == null) {
            return findIndex;
        }
        for (ProcessStepInfo<T> stepInfo : this.stepInfoList) {
            findIndex++;
            if (stepInfo.getProcessStep().getClass().getName().equals(stepInfoClass.getName())) {
                return findIndex;
            }
        }

        return findIndex;
    }

    /**
     * 依据StepInfo Class对象查找对应的处理列表中的Index值
     *
     * @param orderProcessStep 当前查找的Step对象
     * @return 在列表中的Index值。-1 未找到
     */
    public int getProcessStepInfoIndex(IOmsOrderProcessStep<T> orderProcessStep) {
        int findIndex = -1;
        if (orderProcessStep == null) {
            return findIndex;
        }
        for (ProcessStepInfo<T> stepInfo : this.stepInfoList) {
            findIndex++;
            if (stepInfo.getProcessStep().getClass().getName().equals(orderProcessStep.getClass().getName())) {
                return findIndex;
            }
        }

        return findIndex;
    }

    /**
     * 依据Class对象查找对应的处理列表中的Step处理对象
     *
     * @param stepInfoClass 当前查找的Step对象
     * @return 在列表中的Step处理对象
     */
    public ProcessStepInfo<T> getProcessStepInfo(Class stepInfoClass) {
        if (stepInfoClass == null) {
            return null;
        }
        for (ProcessStepInfo<T> stepInfo : this.stepInfoList) {
            if (stepInfo.getProcessStep().getClass().getName().equals(stepInfoClass.getName())) {
                return stepInfo;
            }
        }

        return null;
    }

    /**
     * 添加阶段处理信息对象
     *
     * @param stepInfo 阶段处理信息对象
     */
    public void add(ProcessStepInfo<T> stepInfo) {
        ProcessStepInfo findStepInfo = this.getProcessStepInfo(stepInfo.getProcessStep().getClass());
        if (findStepInfo == null) {
            this.stepInfoList.add(stepInfo);
        }
    }

    /**
     * 移除阶段处理信息对象
     *
     * @param stepInfo 阶段处理信息对象
     */
    public void remove(ProcessStepInfo<T> stepInfo) {
        ProcessStepInfo findStepInfo = this.getProcessStepInfo(stepInfo.getProcessStep().getClass());
        if (findStepInfo != null) {
            this.stepInfoList.remove(stepInfo);
        }
    }

    /**
     * 清空列表对象
     */
    public void clear() {
        this.stepInfoList.clear();
    }

    /**
     * 依据Index值获取具体的操作阶段对象
     *
     * @param index Index值
     * @return 具体的操作阶段对象
     */
    public ProcessStepInfo get(int index) {
        return this.stepInfoList.get(index);
    }

    /**
     * 列表对象大小
     *
     * @return 列表对象大小
     */
    public int size() {
        return this.stepInfoList.size();
    }

    /**
     * 列表对象
     *
     * @return 列表对象
     */
    public List<ProcessStepInfo<T>> getItems() {
        return this.stepInfoList;
    }

    /**
     * 当前列表第一个阶段处理对象
     *
     * @return 第一个阶段处理对象
     */
    public ProcessStepInfo<T> first() {
        if (this.stepInfoList.size() == 0) {
            return null;
        }
        return this.stepInfoList.get(0);
    }

    /**
     * 下一个阶段对象
     *
     * @param currentStep      当前阶段对象
     * @param currentStepClass 对象阶段对象Class
     * @return 下一个阶段对象
     */
    public ProcessStepInfo<T> next(IOmsOrderProcessStep<T> currentStep,
                                   Class<? extends IOmsOrderProcessStep<T>> currentStepClass) {
        int itemIndex;
        if (currentStepClass == null) {
            itemIndex = this.getProcessStepInfoIndex(currentStep);
            if (itemIndex == -1) {
                return null;
            }
            if (itemIndex == this.stepInfoList.size() - 1) {
                return null;
            }
            itemIndex = itemIndex + 1;
        } else {
            itemIndex = this.getProcessStepInfoIndex(currentStepClass);
            if (itemIndex == -1) {
                return null;
            }
        }

        return this.stepInfoList.get(itemIndex);
    }

    /**
     * 最终阶段需要处理对象
     *
     * @return 最终阶段需要处理对象
     */
    public ProcessStepInfo<T> getFinallyStep() {
        for (ProcessStepInfo<T> stepInfo : this.stepInfoList) {
            if (stepInfo.isFinallyMustStep()) {
                return stepInfo;
            }
        }

        return null;
    }


}
