package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.request.CancelOrderModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-12-22 14:43
 * @Description : 退换货单相关服务
 **/
public interface OcBReturnOrderCmd {

    /**
     * 取消退换货单
     * @param cancelOrderModel
     * @return
     * @throws NDSException
     */
    ValueHolderV14 cancelReturnOrder(CancelOrderModel cancelOrderModel) throws NDSException;
}
