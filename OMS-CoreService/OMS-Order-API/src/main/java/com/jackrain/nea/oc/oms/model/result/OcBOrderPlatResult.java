package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 * @create 2020-08-05
 * @desc 平台档案查询结果
 **/
@Data
public class OcBOrderPlatResult implements Serializable {

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "platform")
    private Long platform;

    /**
     * 平台档案对应的名称
     */
    private String platformName;
}
