package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.OmsReleaseStockRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2023/3/22 13:54
 * @Version 1.0
 */
public interface OmsReleaseStockOrderCmd {

    /**
     * 按订单释放库存
     * @param requestList
     * @param user
     * @return
     */
    ValueHolderV14 releaseStockOrder(List<OmsReleaseStockRequest> requestList, User user);
}
