package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.LockOrderRequest;
import com.jackrain.nea.oc.oms.model.result.LockOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @Descroption 手工锁单解锁
 * <AUTHOR>
 * @Date 2019/10/11 20:28
 */
public interface UnlockOrderCmd {
    ValueHolderV14<LockOrderResult> startUnlockOrder(LockOrderRequest lockOrderRequest);
}

