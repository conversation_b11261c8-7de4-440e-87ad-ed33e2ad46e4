package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.IpVipTimeOrderItemQueryRequest;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @author: chenxiulou
 * @description: 时效订单接口
 * @since: 2019-09-04
 * create at : 2019-09-04 16:29
 */
public interface VipTimeOrderTransferCmd {
    /**
     * 时效订单正常转换服务
     *
     * @param transferOrderRequest 参数
     * @return 返回值
     */
    ValueHolderV14<TransferOrderResult> startTransferTimeOrder(TransferOrderRequest transferOrderRequest);

    /**
     * 查询时效订单接口
     */
    ValueHolderV14<List<IpBTimeOrderVipItem>> getIpBTimeOrderVipItemByMainId(IpVipTimeOrderItemQueryRequest request);
}
