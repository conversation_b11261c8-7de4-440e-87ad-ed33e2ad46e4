package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * 天猫直发单据转换接口
 *
 * @author: 秦雄飞
 * @since: 2020-03-25
 */
public interface AlibabaAscpTransferOrderCmd {

    ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest);
}
