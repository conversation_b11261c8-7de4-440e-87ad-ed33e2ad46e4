package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR> 夏继超
 * @since : 2019-05-14
 * create at : 2019-05-14 9:18 AM
 */
public interface CurrencyTransferOrderCmd {

    ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest);

}
