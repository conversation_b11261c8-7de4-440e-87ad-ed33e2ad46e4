package com.jackrain.nea.oc.oms.api;

import java.util.List;

import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.web.face.User;

/**
 * @author: DXF
 * @since: 2020/12/7
 * create at : 2020/12/7 15:29
 */
public interface OcBOrderHoldCmd {

    void holdOrUnHoldOrder(OcBOrder ocBOrder, OrderHoldReasonEnum holdReason);


    void holdOrUnHoldOrderList(List<OcBOrder> oc<PERSON><PERSON><PERSON><PERSON>ist, OrderHoldReasonEnum holdReason);
}
