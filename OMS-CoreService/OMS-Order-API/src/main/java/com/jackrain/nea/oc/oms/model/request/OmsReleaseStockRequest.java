package com.jackrain.nea.oc.oms.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2023/3/22 13:57
 * @Version 1.0
 */
@Data
public class OmsReleaseStockRequest implements Serializable {

    /**
     * 订单id集合
     */
    private List<Long> orderIds;

    /**
     * sku信息
     */
    private Long cpCSkuId;

    /**
     * sku条码
     */
    private String cpCSkuEcode;

    /**
     * 释放数量
     */
    private BigDecimal qty;


}
