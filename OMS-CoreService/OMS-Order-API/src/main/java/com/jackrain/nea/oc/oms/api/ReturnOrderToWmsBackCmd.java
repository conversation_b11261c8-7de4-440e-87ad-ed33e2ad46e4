package com.jackrain.nea.oc.oms.api;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * description：退单传wing结果返回
 *
 * <AUTHOR>
 * @date 2021/7/16
 */
public interface ReturnOrderToWmsBackCmd {

    ValueHolderV14 noticeBack(String msg);

    //ValueHolderV14 resultBack(String msg);

    ValueHolderV14 preSaleSinkResultBack(String msg);

    ValueHolderV14 preSaleSinkOrderDeliveryCallBack(String msg);

    JSONObject customerQueryOrderOut(String param);

}
