package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;

/**
 * 一件代发客户基价策略Service
 *
 * <AUTHOR>
 */
public interface StCDropshipBasePriceStrategyService {

    /**
     * 新增/编辑策略
     *
     * @param querySession 查询会话
     * @return 操作结果
     * @throws NDSException 异常
     */
    ValueHolder execute(QuerySession querySession) throws NDSException;

    /**
     * 审核策略
     *
     * @param querySession 查询会话
     * @return 操作结果
     * @throws NDSException 异常
     */
    ValueHolder auditExecute(QuerySession querySession) throws NDSException;

    /**
     * 反审核策略
     *
     * @param querySession 查询会话
     * @return 操作结果
     * @throws NDSException 异常
     */
    ValueHolder unAuditExecute(QuerySession querySession) throws NDSException;

    /**
     * 删除策略
     *
     * @param querySession 查询会话
     * @return 操作结果
     * @throws NDSException 异常
     */
    ValueHolder deleteExecute(QuerySession querySession) throws NDSException;

}
