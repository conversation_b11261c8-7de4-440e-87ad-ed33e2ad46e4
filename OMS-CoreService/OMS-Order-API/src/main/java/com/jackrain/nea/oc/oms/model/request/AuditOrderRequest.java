package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: heliu
 * @since: 2019/4/1
 * create at : 2019/4/1 11:48
 */
@Data
public class AuditOrderRequest implements Serializable {
    private ChannelType channelType;

    private User operateUser;

    private List<Long> orderIdList;
}
