package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryRecord;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;
import java.util.Map;

/**
 * className: IpBJitxDeliveryRecordCmd
 * description:JITX寻仓结果
 *
 * <AUTHOR>
 * create: 2021-12-17
 * @since JDK 1.8
 */
public interface IpBJitxDeliveryRecordCmd {

    /**
     * 批量新增
     * @param recordList 主表
     * @param user 用户
     * @return
     */
    ValueHolderV14<Map<String,Long>> batchInsert(List<IpBJitxDeliveryRecord> recordList, User user);
}
