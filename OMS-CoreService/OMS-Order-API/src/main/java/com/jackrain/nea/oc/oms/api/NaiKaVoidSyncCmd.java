package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.NaiKaVoidSyncToOmsRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * @ClassName NaiKaVoidSyncCmd
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/10/17 11:38
 * @Version 1.0
 */
public interface NaiKaVoidSyncCmd {
    /**
     * 奶卡作废 小程序同步到oms
     *
     * @param naiKaVoidSyncToOmsRequests
     * @return
     */
    ValueHolderV14 naikaVoidSync(List<NaiKaVoidSyncToOmsRequest> naiKaVoidSyncToOmsRequests);

}
