package com.jackrain.nea.oc.oms.api;


import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

import java.util.List;

/**
 * 淘宝预售地址修改
 *
 * @date 2019/10/12
 * @author: ming.fz
 */
public interface OcBOrderUpdateAddressServiceCmd {

    /**
     * 异步调用修改地址
     *
     * @param bl
     * @param tid
     * @param user
     */
    ValueHolderV14 updateOrderAddress(boolean bl, String tid, User user);

    /**
     * 非异步调用修改地址
     *
     * @param tid
     * @param user
     */
    ValueHolderV14<String> updateOrderAddress(String tid, User user);

    /**
     * 通过sourceCode获取订单
     *
     * @param sourcesCode
     * @return
     */
    List<OcBOrder> getOrderList(String sourcesCode);


    /**
     * 通过订单id获取订单明细
     *
     */
    List<OcBOrderItem> getOrderItemList(Long orderId);
}
