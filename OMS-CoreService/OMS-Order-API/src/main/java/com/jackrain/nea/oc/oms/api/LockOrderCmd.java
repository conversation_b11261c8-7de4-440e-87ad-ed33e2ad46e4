package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.LockOrderRequest;
import com.jackrain.nea.oc.oms.model.result.LockOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @Descroption 淘宝手工锁单
 * <AUTHOR>
 * @Date 2019/5/16 20:28
 */
public interface LockOrderCmd {
    ValueHolderV14<LockOrderResult> startLockOrder(LockOrderRequest lockOrderRequest);
}

