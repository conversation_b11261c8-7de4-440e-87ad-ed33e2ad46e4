package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.CycleConfirmRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * 中台周期购对账信息处理
 *
 * <AUTHOR>
 */
public interface OmsAcCycleBuyCmd {

    /**
     * 对账变动周期购额外信息
     *
     * @param confirmRequests
     * @return
     */
    ValueHolderV14 confirmChange(List<CycleConfirmRequest> confirmRequests);

}
