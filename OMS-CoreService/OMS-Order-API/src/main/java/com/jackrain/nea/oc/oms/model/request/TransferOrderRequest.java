package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 易邵峰
 * @since: 2019-03-27
 * create at : 2019-03-27 21:57
 */
@Data
public class TransferOrderRequest implements Serializable {

    private ChannelType channelType;

    private List<String> orderNoList;

    private User operateUser;

}
