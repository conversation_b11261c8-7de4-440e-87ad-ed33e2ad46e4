package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR> 黄超
 * @since : 2019-06-27
 * create at : 2019-06-27 20:00
 */
public interface JitxTransferOrderCmd {

    ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest);

}
