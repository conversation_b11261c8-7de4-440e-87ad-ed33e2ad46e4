package com.jackrain.nea.oc.oms.model.request;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>Title: NEA-SOA</p>
 * <p>Description: com.jackrain.nea.oc.oms.model.request</p>
 *
 * <AUTHOR>
 */
@Data
public class ConfirmReturnPreDealRequest implements Serializable {

    private static final long serialVersionUID = -2720127180088811076L;

    /**
     * 对账单id
     */
    private Long confirmId;

    /**
     * 平台单号
     */
    private String tid;

}