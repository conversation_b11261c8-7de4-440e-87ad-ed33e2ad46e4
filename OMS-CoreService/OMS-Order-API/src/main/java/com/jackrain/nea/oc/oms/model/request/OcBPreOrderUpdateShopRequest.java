package com.jackrain.nea.oc.oms.model.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName OcBPreOrderUpdateShopRequest
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/13 10:33
 * @Version 1.0
 */
@Data
public class OcBPreOrderUpdateShopRequest implements Serializable {

    /**
     * 平台单号列表
     */
    private List<String> tids;

    /**
     * 店铺名称
     */
    private String shopTitle;
}
