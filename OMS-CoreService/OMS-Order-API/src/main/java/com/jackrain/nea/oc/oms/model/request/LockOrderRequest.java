package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.web.face.User;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: huang.zai<PERSON>
 * @since: 2019-10-12
 * create at : 2019-10-12 11:57
 */
@Data
public class LockOrderRequest implements Serializable {

    private ChannelType channelType;

    private List<Long> lockIdList;

    private User operateUser;

}
