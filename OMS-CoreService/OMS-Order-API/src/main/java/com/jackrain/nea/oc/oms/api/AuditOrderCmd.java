package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.AuditOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 审核
 *
 * @Auther: 黄志优
 * @Date: 2020/11/4 13:53
 * @Description:
 */
public interface AuditOrderCmd {

    /**
     * 审核订单，必须OcBOrderRelation.OmsMethod
     * @param request
     * @return
     */
    ValueHolderV14 auditOrder(AuditOrderRequest request);
}
