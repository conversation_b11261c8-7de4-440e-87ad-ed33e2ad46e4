package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.PosOrderCancelRequest;
import com.jackrain.nea.util.ValueHolder;

import java.util.List;

/**
 * className: OcBOrderPosCancelCmd
 * description:云仓退款
 *
 * <AUTHOR>
 * create: 2021-08-25
 * @since JDK 1.8
 */
public interface OcBOrderPosCancelCmd {

    /**
     * 云仓退款
     * @param requests 平台单号+skuid+数量
     * @return ValueHolder
     */
    ValueHolder posCancel(List<PosOrderCancelRequest> requests);
}
