package com.jackrain.nea.oc.oms.model.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-07-09
 * @desc 订单分页查询结果
 **/
@Data
public class OcBOrderListResult implements Serializable {
    private static final long serialVersionUID = 6607930554687284235L;
    /**
     * 订单数据
     */
    private List<OcBOrderResult> resultList;
    /**
     * 当前页
     */
    private Integer pageNum;
    /**
     * 每页条数
     */
    private Integer pageSize;
    /**
     * 总页数
     */
    private Integer totalNum;
    /**
     * 总记录数
     */
    private Integer totalSize;
}
