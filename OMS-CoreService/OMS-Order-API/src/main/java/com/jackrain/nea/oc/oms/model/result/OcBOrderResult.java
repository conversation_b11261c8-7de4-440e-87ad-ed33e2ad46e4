package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-07-09
 * @desc 订单查询结果
 **/
@Data
public class OcBOrderResult extends OcBOrder {
    /**
     * 订单明细
     */
    @JSONField(name = "ITEM_LIST")
    List<OcBOrderItem> itemList;
}
