package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.OrderUpdateInDistributionRequest;
import com.jackrain.nea.oc.oms.model.result.OrderUpdateInDistributionResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @author: 胡林洋
 * @since: 2019-04-29
 * create at : 2019-04-29 11:33
 */
public interface OmsOrderUpdateInDistributionCmd {

    ValueHolderV14<OrderUpdateInDistributionResult> startUpdateInDistribution(OrderUpdateInDistributionRequest updateInDistributionRequest);
}
