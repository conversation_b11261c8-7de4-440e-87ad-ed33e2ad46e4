package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.ReturnOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @description: 猫超直发取消订单
 * @author: 郑小龙
 * @date: 2020-06-04 11:58
 **/
public interface AlibabaAscpOrderCancelTransferCmd {
    ValueHolderV14<ReturnOrderResult> startCancelTransfer(TransferOrderRequest transferOrderRequest);
}
