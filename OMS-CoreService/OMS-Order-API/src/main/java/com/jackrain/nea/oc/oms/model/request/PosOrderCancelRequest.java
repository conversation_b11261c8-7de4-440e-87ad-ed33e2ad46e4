package com.jackrain.nea.oc.oms.model.request;

import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * className: PosOrderCancelRequest
 * description: 云仓单退款请求参数
 *
 * <AUTHOR>
 * create: 2021-08-25
 * @since JDK 1.8
 */
@Data
public class PosOrderCancelRequest implements Serializable {

    private static final long serialVersionUID = -1956860775505434358L;

    /**
     * 平台单号
     */
    private String sourceCode;

    /**
     * 明细
     */
    private List<OcBOrderItem> itemList;

}
