package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.request.OcBOrderLogPushRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR>
 * @create 2020-08-14
 * @desc 零售发货单操作日志推送
 **/
public interface OcBOrderLogPushCmd {
    /**
     * WOS系统推送操作日志
     *
     * @param ocBOrderLogPushRequest
     * @return
     */
    ValueHolderV14 wosPushOperationLog(OcBOrderLogPushRequest ocBOrderLogPushRequest);
}
