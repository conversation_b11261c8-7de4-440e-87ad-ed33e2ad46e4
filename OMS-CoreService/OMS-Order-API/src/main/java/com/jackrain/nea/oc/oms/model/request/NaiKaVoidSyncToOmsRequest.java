package com.jackrain.nea.oc.oms.model.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName NaiKaVoidSyncToOmsRequest
 * @Description 奶卡作废同步给oms
 * <AUTHOR>
 * @Date 2023/10/17 11:50
 * @Version 1.0
 */
@Data
public class NaiKaVoidSyncToOmsRequest implements Serializable {

    private static final long serialVersionUID = -6007889867900869382L;
    /**
     * 平台单号
     */
    @NotBlank(message = "平台单号不能为空")
    private String tid;
    /**
     * 卡号
     */
    @NotBlank(message = "卡号不能为空")
    private String cardCode;
    /**
     * 剩余提数
     */
    @NotNull(message = "剩余提数不能为空")
    private Integer remainingCount;
}
