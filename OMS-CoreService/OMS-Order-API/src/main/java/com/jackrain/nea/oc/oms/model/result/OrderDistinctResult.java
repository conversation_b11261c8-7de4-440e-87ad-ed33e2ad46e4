package com.jackrain.nea.oc.oms.model.result;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-06-11 14:13
 */
@Data
public class OrderDistinctResult implements Serializable {
    /**
     * 将同一买家昵称、发货仓库、平台、店铺、支付方式、收货人、收货人手机、收货人电话、
     * 收货人所在省、收货人所在市、收货人所在区、收货人详细地址等订单进行合并
     *
     * @param ocBOrderList
     * @return
     */
    @JSONField(name = "USER_NICK")
    private String userNick;//买家昵称
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;//发货仓库ID
    @JSONField(name = "PLATFORM")
    private Integer platform;//平台
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;//店铺
    @JSONField(name = "PAY_TYPE")
    private Integer payType;//支付方式
    @JSONField(name = "RECEIVER_NAME")
    private String receiverName;//收货人
    @JSONField(name = "RECEIVER_MOBILE")
    private String receiverMobile;//收货人手机
    @JSONField(name = "RECEIVER_PHONE")
    private String receiverPhone;//收货人电话
    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    private Long cpCRegionProvinceId;//省
    @JSONField(name = "CP_C_REGION_CITY_ID")
    private Long cpCRegionCityId;//市
    @JSONField(name = "CP_C_REGION_AREA_ID")
    private Long cpCRegionAreaId;//区
    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;//地址
    @JSONField(name = "ORDER_TYPE")
    private Integer orderType;
}
