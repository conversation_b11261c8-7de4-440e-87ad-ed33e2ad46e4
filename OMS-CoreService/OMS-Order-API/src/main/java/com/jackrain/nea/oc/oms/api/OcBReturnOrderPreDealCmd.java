package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.ConfirmReturnPreDealRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

import java.util.List;

/**
 * <p>Title: OcBReturnOrderPreDealCmd</p>
 * <p>Description: 预处理退货订单</p>
 *
 * <AUTHOR>
 */
public interface OcBReturnOrderPreDealCmd {

    /**
     * 预处理退货订单
     *
     * @param confirmBills
     * @return
     */
    ValueHolderV14 preDeal(List<ConfirmReturnPreDealRequest> confirmBills);

    /**
     * 重置退货订单
     *
     * @param confirmIds
     * @return
     */
    ValueHolderV14 resetReturnOrder(List<Long> confirmIds);

}
