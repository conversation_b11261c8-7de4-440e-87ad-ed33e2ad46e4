package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.ReverseO2oOrderRequest;
import com.jackrain.nea.oc.oms.model.request.ReverseO2oReturnOrderRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @ClassName ReverseO2oOrderCmd
 * @description: 反向O2O订单接口
 * @author: Lay.Jiang
 * @create: 2021-06-08 13:24
 **/
public interface ReverseO2oOrderCmd {

    /**
     * 反向O2O订单新增接口
     *
     * @param request
     * @return
     */
    ValueHolderV14 orderAdd(ReverseO2oOrderRequest request);

    /**
     * 反向O2O取消订单
     *
     * @param request
     * @return
     */
    ValueHolderV14 cancelOrder(ReverseO2oOrderRequest request);

    /***
     * 反向O2O完成订单
     * @param request
     * @return
     */
    ValueHolderV14 orderFinish(ReverseO2oOrderRequest request);

    /***
     * 创建退单
     * @param request
     * @return
     */
    ValueHolderV14 createReturnOrder(ReverseO2oReturnOrderRequest request);

    /**
     * 取消退单
     *
     * @param request
     * @return
     */
    ValueHolderV14 cancelReturnOrder(ReverseO2oReturnOrderRequest request);

    /**
     * 更新退单物流单号
     *
     * @param request
     * @return
     */
    ValueHolderV14 updateReturnOrder(ReverseO2oReturnOrderRequest request);
}