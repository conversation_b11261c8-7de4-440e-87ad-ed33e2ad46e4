package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.ReturnOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * @Descroption 手工京东退单转换接口
 * <AUTHOR>
 * @Date 2019/5/16 20:28
 */
public interface JingdongReturnOrderCmd {
    ValueHolderV14<ReturnOrderResult> startReturnOrder(TransferOrderRequest transferOrderRequest);
}

