package com.jackrain.nea.oc.oms.model.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName NaiKaAccountRequest
 * @Description 奶卡对账金额回传
 * <AUTHOR>
 * @Date 2022/9/5 09:50
 * @Version 1.0
 */
@Data
public class NaiKaAccountRequest implements Serializable {

    private static final long serialVersionUID = 7052299012841331486L;
    /**
     * 对账来源单据类型，零售发货单、已发货退款单、仅退款单
     */
    private String billType;

    /**
     * 订单中心单据主表id(零售发货单或者退换货单id)
     */
    private Long sourceOriginBillId;

    /**
     * 订单中心单据明细id(零售发货单或者已发货退款单)
     */
    private Long sourceOriginBillItemId;

    /**
     * 订单中心单据关联id(如果confirmOriginBillCode 为AF、TH 则此字段为零售发货单id)
     */
    private Long sourceOriginRltId;

    /**
     * 订单中心单据关联id(如果confirmOriginBillCode 为AF、TH 则此字段为零售发货单明细表id)
     */
    private Long sourceOriginRltItemId;

    /**
     * 对账来源单据编号，OM、TH、AF
     */
    private String confirmOriginBillCode;

    /**
     * 数量
     */
    private Integer systemNumber;

    /**
     * 调整金额
     */
    private BigDecimal adjustAmt;

    /**
     * 核销金额
     */
    private BigDecimal wipeAmt;

    /**
     * 差异金额
     */
    private BigDecimal differenceAmt;

    /**
     * 上账时间
     */
    private Date upToBillDate;
}
