package com.jackrain.nea.oc.oms.api;

import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;

/**
 * <AUTHOR>
 * @Description 淘宝经销单据转换按钮接口
 * @Date 2019-7-22
 **/
public interface TaobaoJxTransferOrderCmd {

    ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest);
}
