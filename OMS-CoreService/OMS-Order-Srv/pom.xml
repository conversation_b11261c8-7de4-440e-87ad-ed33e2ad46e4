<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.burgeon.r3</groupId>
        <artifactId>r3-oc-oms</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>r3-oc-oms-core-srv</artifactId>
    <version>3.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-core-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-core-process</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
