package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.ExchangeOrderCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.ExchangeOrderResult;
import com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.TaobaoTransferExchangeProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class ExchangeOrderCmdImpl implements ExchangeOrderCmd {

    @Autowired
    private TaobaoTransferExchangeProcessImpl taobaoTransferExchangeProcess;
    @Autowired
    private OmsTaobaoExchangeService omsTaobaoExchangeService;

    @Override
    public ValueHolderV14<ExchangeOrderResult> startReturnOrder(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<ExchangeOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (transferOrderRequest.getChannelType() == ChannelType.TAOBAO) {
                    OmsTaobaoExchangeRelation relation = omsTaobaoExchangeService.selectTaobaoExchangeInfo(orderNo);
                    if (relation != null) {
                        ProcessStepResultList processStepResult = taobaoTransferExchangeProcess.start(relation, false, transferOrderRequest.getOperateUser());
                        if (!processStepResult.isProcessSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResult.getLastFailedProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }
            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("ExchangeOrderCmdImpl,异常信息:{}", "ExchangeOrderCmdImpl"), Throwables.getStackTraceAsString(ex));

        }
        return resultValueHolderV14;
    }
}
