package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderPreDealCmd;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.request.ConfirmReturnPreDealRequest;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolderV14Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>Title: OcBReturnOrderPreDealCmdImpl</p>
 * <p>Description: 预处理退货订单</p>
 *
 * <AUTHOR>
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class OcBReturnOrderPreDealCmdImpl implements OcBReturnOrderPreDealCmd {

    @Resource
    private OcBReturnOrderMapper returnOrderMapper;

    /**
     * <p>Title: preDeal</p>
     * <p>Description: 预处理退货订单</p>
     *
     * @param confirmBills 确认退货预处理请求列表
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     */
    @Override
    public ValueHolderV14 preDeal(List<ConfirmReturnPreDealRequest> confirmBills) {
        // 如果需要处理的确认退货预处理请求为空列表，则返回成功处理的ValueHolder
        if (CollectionUtils.isEmpty(confirmBills)) {
            return ValueHolderV14Utils.getSuccessValueHolder("无数据需要处理");
        }

        // 从确认退货预处理请求中获取所有交易号（tid）并收集到一个列表
        List<String> tids = confirmBills.stream().map(ConfirmReturnPreDealRequest::getTid).collect(Collectors.toList());

        // 根据交易号列表查询待处理的退货订单
        List<OcBReturnOrder> returnOrders = returnOrderMapper.selectReturnOrdersByTids(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal(), YesNoEnum.N.getKey(), tids);

        // 如果查询不到符合条件的退货订单，则返回成功处理的ValueHolder
        if (CollectionUtils.isEmpty(returnOrders)) {
            return ValueHolderV14Utils.getSuccessValueHolder("没有符合条件的退换货单");
        }

        // 根据交易号对确认退货预处理请求进行分组
        Map<String, List<ConfirmReturnPreDealRequest>> detailMap = confirmBills.stream().collect(Collectors.groupingBy(ConfirmReturnPreDealRequest::getTid));

        // 修改财务预处理状态，并增加对账单id记录
        List<OcBReturnOrder> updates = Lists.newArrayList();
        List<Long> canUpdateIds = Lists.newArrayList();

        // 遍历查询到的退货订单
        for (OcBReturnOrder returnOrder : returnOrders) {
            OcBReturnOrder update = new OcBReturnOrder();
            update.setId(returnOrder.getId());
            update.setModifieddate(new Date());
            update.setIsConfirmPre(YesNoEnum.Y.getKey());

            // 获取与当前退货订单关联的确认退货预处理请求列表
            List<ConfirmReturnPreDealRequest> list = detailMap.get(returnOrder.getTid());

            // 如果列表为空，则输出警告日志并继续下一个退货订单的处理
            if (CollectionUtils.isEmpty(list)) {
                log.warn("OcBReturnOrderPreDealCmd.preDeal list empty tid:{}", returnOrder.getTid());
                continue;
            }

            // 获取列表中的第一个请求对象
            ConfirmReturnPreDealRequest request = list.get(0);

            // 设置确认退货单的确认号到退货订单中
            update.setConfirmBillId(request.getConfirmId());

            // 添加更新的退货订单和对应的对账单id到更新列表中
            updates.add(update);
            canUpdateIds.add(request.getConfirmId());
        }

        // 如果更新列表不为空，则批量更新退货订单
        if (CollectionUtils.isNotEmpty(updates)) {
            try {
                returnOrderMapper.batchUpdateReturnOrder(updates);
            } catch (Exception e) {
                log.warn("OcBReturnOrderPreDealCmd.preDeal update error updates:{}", JSON.toJSONString(updates), e);
                return ValueHolderV14Utils.getFailValueHolder("退货单更新失败");
            }
        }

        // 返回成功处理的ValueHolder，设置处理完成的状态码和处理的数据（可更新的确认号列表）
        ValueHolderV14 holder = ValueHolderV14Utils.getSuccessValueHolder("处理完成");
        holder.setData(canUpdateIds);

        return holder;
    }

    @Override
    public ValueHolderV14 resetReturnOrder(List<Long> confirmIds) {
        if (CollectionUtils.isEmpty(confirmIds)) {
            return ValueHolderV14Utils.getSuccessValueHolder("无数据需要处理");
        }

        //查询对账单id关联的退换货单
        QueryWrapper<OcBReturnOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("confirm_bill_id", confirmIds);
        List<OcBReturnOrder> returnOrders = returnOrderMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(returnOrders)) {
            return ValueHolderV14Utils.getSuccessValueHolder("无退换货单数据需要处理");
        }

        List<OcBReturnOrder> updates = Lists.newArrayList();
        for (OcBReturnOrder returnOrder : returnOrders) {
            OcBReturnOrder update = new OcBReturnOrder();
            update.setId(returnOrder.getId());
            update.setModifieddate(new Date());
            update.setIsConfirmPre(YesNoEnum.N.getKey());
            update.setConfirmBillId(null);
            updates.add(update);
        }

        // 如果更新列表不为空，则批量更新退货订单
        if (CollectionUtils.isNotEmpty(updates)) {
            try {
                returnOrderMapper.batchUpdateReturnOrder(updates);
            } catch (Exception e) {
                log.warn("OcBReturnOrderPreDealCmd.preDeal resetReturnOrder error updates:{}", JSON.toJSONString(updates), e);
                return ValueHolderV14Utils.getFailValueHolder("退货单还原更新失败");
            }
        }

        return ValueHolderV14Utils.getSuccessValueHolder("还原成功");
    }

}
