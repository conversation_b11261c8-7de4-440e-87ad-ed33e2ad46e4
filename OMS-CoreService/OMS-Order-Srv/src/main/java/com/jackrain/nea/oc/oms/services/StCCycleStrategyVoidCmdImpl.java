package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCCycleStrategyVoidCmd;
import com.jackrain.nea.oc.oms.mapper.StCCycleStrategyMapper;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * @ClassName StCCycleStrategyVoidCmdImpl
 * @Description 停用
 * <AUTHOR>
 * @Date 2024/8/20 16:22
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCCycleStrategyVoidCmdImpl extends CommonCommandAdapter implements StCCycleStrategyVoidCmd {

    @Autowired
    private StCCycleStrategyMapper stCCycleStrategyMapper;

    @Override
    @OmsOperationLog(operationType = "VOID", mainTableName = "ST_C_CYCLE_STRATEGY", itemsTableName = "ST_C_CYCLE_RULE_STRATEGY,ST_C_CYCLE_ITEM_STRATEGY")
    public ValueHolder execute(QuerySession session) throws NDSException {
        List<Long> objIds = getObjIds(session);
        if (CollectionUtils.isEmpty(objIds)) {
            return ValueHolderUtils.getFailValueHolder("请选择要停用的策略");
        }
        try {
            for (Long objId : objIds) {
                stCCycleStrategyMapper.voidStrategy(objId);
            }
        } catch (Exception e) {
            log.error("停用策略异常", e);
            return ValueHolderUtils.getFailValueHolder("停用策略异常");
        }
        return ValueHolderUtils.getSuccessValueHolder("停用成功");
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
