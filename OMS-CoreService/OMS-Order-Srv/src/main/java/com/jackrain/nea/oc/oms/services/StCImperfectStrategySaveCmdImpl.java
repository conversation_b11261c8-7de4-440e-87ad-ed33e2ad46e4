package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCImperfectStrategySaveCmd;
import com.jackrain.nea.st.model.StCImperfectStrategyRelation;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * 残次策略-新增
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCImperfectStrategySaveCmdImpl extends CommonCommandAdapter implements StCImperfectStrategySaveCmd {

    @Resource
    private StCImperfectStrategyService stCImperfectStrategyService;

    @Override
//    @OmsOperationLog(mainTableName = "ST_C_IMPERFECT_STRATEGY", itemsTableName = "ST_C_IMPERFECT_STRATEGY_ITEM")
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolderV14 valueHolderV14;
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        JSONObject bodyJsn = param.getJSONObject("fixcolumn");
        User user = session.getUser();
        StCImperfectStrategyRelation modelStrategyRequest = JsonUtils.jsonParseClass(bodyJsn, StCImperfectStrategyRelation.class);
        Long id = param.getLong("objid");
        String tableName = param.getString("table");
        JSONObject before = param.getJSONObject("beforevalue");
        JSONObject after = param.getJSONObject("aftervalue");
        if (id < 0) {
            valueHolderV14 = stCImperfectStrategyService.save(modelStrategyRequest, user);
        } else {
            // 修改
            valueHolderV14 = stCImperfectStrategyService.update(modelStrategyRequest, before, after, user, id);
        }
        if (!valueHolderV14.isOK()) {
            ValueHolder valueHolder = new ValueHolder();
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", valueHolderV14.getMessage());
            return valueHolder;
        }
        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", valueHolderV14.getCode());
        valueHolder.put("message", valueHolderV14.getMessage());
        valueHolder.put("data", valueHolderV14.getData());
        return ValueHolderUtils.success("新增成功！", ValueHolderUtils.createAddErrorData(tableName, (Long) valueHolderV14.getData(), null));
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
