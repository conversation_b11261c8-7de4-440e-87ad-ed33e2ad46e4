package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.JDDirectRefundTransferCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.ReturnOrderResult;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectRefund;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.JDDirectCancelProcessImpl;
import com.jackrain.nea.oc.oms.services.refund.direct.OmsJDDirectCancelService;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/4/3
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class JDDirectRefundTransferCmdImpl implements JDDirectRefundTransferCmd {

    @Autowired
    private JDDirectCancelProcessImpl jdDirectCancelProcess;

    @Autowired
    private OmsJDDirectCancelService omsJDDirectCancelService;

    @Override
    public ValueHolderV14<ReturnOrderResult> trans(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<ReturnOrderResult> vh = new ValueHolderV14<>();
        boolean hasError = false;
        int failedNumber = 0;
        int successNumber = 0;
        String currentNo = null;
        List<ProcessStepResult> errorStepResultList = new ArrayList<>();
        try {
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                currentNo = orderNo;
                if (transferOrderRequest.getChannelType() == ChannelType.JINGDONG_DIRECT) {
                    if (StringUtils.isBlank(orderNo)) {
                        log.error("JDDirectCancelTransfer.error.orderNo is blank {}", JSON.toJSONString(orderNo));
                    }
                    IpBJingdongDirectRefund ipBRefundOrder = omsJDDirectCancelService.getIpBRefundOrder(orderNo);
                    if (ipBRefundOrder == null) {
                        ProcessStepResult processStepResul = new ProcessStepResult();
                        processStepResul.setStatus(StepStatus.FAILED);
                        processStepResul.setMessage("未查询到退单");
                        errorStepResultList.add(processStepResul);
                        hasError = true;
                        failedNumber++;
                        continue;
                    }
                    OmsJDDirectCancelRelation model = OmsJDDirectCancelRelation.buildRelation(ipBRefundOrder.getId(), orderNo);
                    ProcessStepResultList result = jdDirectCancelProcess.start(model, false, SystemUserResource.getRootUser());
                    if (log.isDebugEnabled()) {
                        log.debug("JDDirectCancelTransfer.Finished.OrderNo.{}; Result={}", orderNo, JSON.toJSONString(result));
                    }
                    if (!result.isProcessSuccess()) {
                        hasError = true;
                        errorStepResultList.add(result.getLastFailedProcessStepResult());
                        failedNumber++;
                    } else {
                        successNumber++;
                    }
                }
            }
            if (hasError) {
                vh.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                vh.setMessage(sbMessage.toString());
            } else {
                vh.setCode(ResultCode.SUCCESS);
                vh.setMessage("转换完成");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("转换异常：" + ex.getMessage());
            log.error("JDDirectCancelTransfer.error.{}, JDDirectRefundTransferCmd.Exp: {}", currentNo, Throwables.getStackTraceAsString(ex));
        }
        return vh;
    }
}
