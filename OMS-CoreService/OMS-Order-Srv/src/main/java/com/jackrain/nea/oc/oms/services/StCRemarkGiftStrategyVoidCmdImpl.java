package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCRemarkGiftStrategyVoidCmd;
import com.jackrain.nea.oc.oms.mapper.StCRemarkGiftStrategyMapper;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.table.StCRemarkGiftStrategy;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * 备注赠品策略-停用
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCRemarkGiftStrategyVoidCmdImpl extends CommonCommandAdapter implements StCRemarkGiftStrategyVoidCmd {

    @Resource
    private StCRemarkGiftStrategyMapper stCRemarkGiftStrategyMapper;

    @Override
    @OmsOperationLog(operationType = "VOID", mainTableName = "ST_C_REMARK_GIFT_STRATEGY")
    public ValueHolder execute(QuerySession session) throws NDSException {
        List<Long> objIds = getObjIds(session);
        if (CollectionUtils.isEmpty(objIds)) {
            return ValueHolderUtils.getFailValueHolder("请选择要停用的策略");
        }
        try {
            List<StCRemarkGiftStrategy> strategies = stCRemarkGiftStrategyMapper.selectBatchIds(objIds);
            for (StCRemarkGiftStrategy strategy : strategies) {
                String isactive = strategy.getIsactive();
                if ("N".equals(isactive)){
                    return ValueHolderUtils.getFailValueHolder("策略已停用，无法再次停用");
                }
                stCRemarkGiftStrategyMapper.voidStrategy(strategy.getId());
                //删除缓存
                String strategyRedisKey = OmsRedisKeyResources.buildStCRemarkGiftStrategyRedisKey(strategy.getShopId());
                RedisOpsUtil.getStrRedisTemplate().delete(strategyRedisKey);
            }
        } catch (Exception e) {
            log.error("停用策略异常", e);
            return ValueHolderUtils.getFailValueHolder("停用策略异常");
        }
        return ValueHolderUtils.getSuccessValueHolder("停用成功");
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}