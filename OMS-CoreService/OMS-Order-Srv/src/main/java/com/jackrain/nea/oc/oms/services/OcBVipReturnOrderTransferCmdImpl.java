package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.util.TypeUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBVipReturnOrderTransferCmd;
import com.jackrain.nea.oc.oms.es.ES4IpVipReturnOrder;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.VipTransferReturnOrderProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * date ：Created in 09:48 2020/05/28
 * description ：退供单转换按钮
 * @ Modified By：
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class OcBVipReturnOrderTransferCmdImpl extends CommandAdapter implements OcBVipReturnOrderTransferCmd {

    @Autowired
    private VipTransferReturnOrderProcessImpl vipTransferReturnOrderProcess;

    @Autowired
    private IpVipReturnOrderService ipVipReturnOrderService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        if (null == param) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (log.isDebugEnabled()) {
            log.debug("退供单转换服务param：" + param);
        }

        boolean isIDS = param.containsKey("ids");
        boolean isObjId = param.containsKey("objid");

        if (!(isIDS || isObjId)) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (isIDS && isObjId) {
            throw new NDSException(Resources.getMessage("参数格式错误！", querySession.getLocale()));
        }

        if (isIDS && (param.getJSONArray("ids").size() <= 0)) {
            throw new NDSException(Resources.getMessage("请选择需要退供单转换的单据记录！", querySession.getLocale()));
        }

        String tableName = param.getString("table");

        if (StringUtils.isEmpty(tableName)) {
            throw new NDSException(Resources.getMessage("表名为空", querySession.getLocale()));
        }

        tableName = tableName.toLowerCase();

        if (isIDS) {
            //列表批量处理
            vh = batchMarked(tableName, param, querySession);
        } else {
            //单对象界面处理
            Long objid = param.getLongValue("objid");
            JSONObject jsonObject;
            try {
                jsonObject = separateMarked(tableName, objid, querySession);
                vh.put("code", TypeUtils.castToInt(jsonObject.get("code")));
                vh.put("message", Resources.getMessage(jsonObject.getString("message"), querySession.getLocale()));
            } catch (Exception e) {
                e.printStackTrace();
                vh.put("code", -1);
                vh.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
            }
        }
        return vh;
    }


    private JSONObject separateMarked(String tableName, Long objid, QuerySession querySession) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("objid", objid);
        try {
            String uniqueKey = "RETURN_SN";
            JSONArray searchJSONArray = ES4IpVipReturnOrder.findReturnSnById(tableName, objid);
            if (searchJSONArray == null || searchJSONArray.isEmpty()) {
                jsonObject.put("message", Resources.getMessage("单据转换失败", querySession.getLocale()));
                jsonObject.put("code", ResultCode.FAIL);
                return jsonObject;
            }
            JSONObject object = searchJSONArray.getJSONObject(0);
            String orderNo = TypeUtils.castToString(object.get(uniqueKey));

            IpVipReturnOrderRelation vipReturnOrderRelation = this.ipVipReturnOrderService.selectVipReturnOrderRelation(orderNo);
            if (vipReturnOrderRelation == null || vipReturnOrderRelation.getVipReturnOrder() == null) {
                String errorMessage = Resources.getMessage("Received OrderMqInfo Not Exist!OrderNo=" + orderNo);
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", Resources.getMessage("单据状态非未转换，不允许单据转换！", querySession.getLocale()));
            } else {
                ProcessStepResultList resultList = vipTransferReturnOrderProcess.start(vipReturnOrderRelation,
                        false, SystemUserResource.getRootUser());
                if (!resultList.isProcessSuccess()) {
                    String message;
                    if (resultList.getLastFailedProcessStepResult() != null) {
                        message = resultList.getLastFailedProcessStepResult().getMessage();
                    } else {
                        message = JSON.toJSONString(resultList);
                    }
                    jsonObject.put("code", ResultCode.FAIL);
                    jsonObject.put("message", message);
                } else {
                    jsonObject.put("message", Resources.getMessage("退供单转换成功", querySession.getLocale()));
                    jsonObject.put("code", ResultCode.SUCCESS);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("退供单转换错误:{}", "退供单转换错误", objid), Throwables.getStackTraceAsString(e));
            jsonObject.put("message", Resources.getMessage("退供单转换失败，" + e.getMessage(), querySession.getLocale()));
            jsonObject.put("code", ResultCode.FAIL);
        }
        return jsonObject;
    }

    private ValueHolder batchMarked(String tableName, JSONObject param, QuerySession querySession) {
        ValueHolder valueHolder = new ValueHolder();
        int success = 0;
        int fail = 0;
        Object[] ids = param.getJSONArray("ids").toArray();
        JSONArray listArray = new JSONArray();
        for (Object id : ids) {
            Long objid = TypeUtils.castToLong(id);
            JSONObject retJson;
            try {
                retJson = separateMarked(tableName, objid, querySession);
            } catch (Exception e) {
                e.printStackTrace();
                retJson = new JSONObject();
                retJson.put("message", Resources.getMessage(e.getMessage(), querySession.getLocale()));
                retJson.put("code", -1);
                retJson.put("objid", objid);
                listArray.add(retJson);
                fail++;
                continue;
            }
            if (retJson == null) {
                fail++;
                continue;
            }
            if (retJson.containsKey("code") && retJson.getInteger("code") == 0) {
                success++;
            } else {
                fail++;
                listArray.add(retJson);
            }
        }

        valueHolder.put("data", listArray);
        if (0 == fail) {
            valueHolder.put("code", 0);
        } else {
            valueHolder.put("code", -1);
        }
        valueHolder.put("message", Resources.getMessage("退供单转换成功的记录数：" + success + ",失败的记录数：" + fail, querySession.getLocale()));
        return valueHolder;
    }
}
