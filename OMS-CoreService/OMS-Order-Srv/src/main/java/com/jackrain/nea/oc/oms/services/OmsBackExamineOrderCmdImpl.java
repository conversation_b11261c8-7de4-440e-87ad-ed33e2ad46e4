package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.OmsBackExamineOrderCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2022/7/13 下午7:06
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class OmsBackExamineOrderCmdImpl implements OmsBackExamineOrderCmd {

    @Autowired
    private OmsBackExamineOrderService omsBackExamineOrderService;
    @Override
    public ValueHolderV14 backExamineOrder(Long orderId, User user) {
        return omsBackExamineOrderService.omsBackExamineOrder(orderId, user);
    }
}
