package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.util.ObjectUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.NaiKaVoidSyncCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordMapper;
import com.jackrain.nea.oc.oms.model.constant.OcBSapSalesDataRecordConstant;
import com.jackrain.nea.oc.oms.model.request.NaiKaVoidSyncToOmsRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecord;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName NaiKaVoidSyncCmdImpl
 * @Description 奶卡作废 同步到oms
 * <AUTHOR>
 * @Date 2023/10/17 11:39
 * @Version 1.0
 */
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
public class NaiKaVoidSyncCmdImpl implements NaiKaVoidSyncCmd {

    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBReturnAfSendMapper returnAfSendMapper;
    @Autowired
    private OcBSapSalesDataRecordMapper ocBSapSalesDataRecordMapper;
    @Autowired
    private OcBSapSalesDataRecordService ocBSapSalesDataRecordService;

    @Override
    public ValueHolderV14 naikaVoidSync(List<NaiKaVoidSyncToOmsRequest> naiKaVoidSyncToOmsRequests) {
        // 以下方案存在数据不准确的情况 但是产品要求如此。(此数据仅用于参考)
        // 根据卡号来找零售发货单 然后根据零售发货单查到已发货退款单(可能会存在查找的已发货退款单有误 但是产品) 然后根据已发货退款单找到销售数据对应的记录 再去判断此销售数据记录是否已经汇总
        for (NaiKaVoidSyncToOmsRequest voidSyncToOmsRequest : naiKaVoidSyncToOmsRequests) {
            List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaByCardCode(voidSyncToOmsRequest.getCardCode());
            if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
                return new ValueHolderV14<>(ResultCode.FAIL, "卡信息不存在");
            }
            OcBOrderNaiKa ocBOrderNaiKa = ocBOrderNaiKaList.get(0);
            Long ocborderId = ocBOrderNaiKa.getOcBOrderId();
            // 根据零售发货单id 去找已发货退款单
            List<OcBReturnAfSend> ocBReturnAfSendList = returnAfSendMapper.selectBySourceBillNo(ocborderId.toString());
            if (CollectionUtils.isEmpty(ocBReturnAfSendList)) {
                log.info("同步奶卡作废时,没有对应的已发货退款单:{}", ocborderId);
                return new ValueHolderV14<>(ResultCode.SUCCESS, "success");
            }
            for (OcBReturnAfSend ocBReturnAfSend : ocBReturnAfSendList) {
                // 根据已发货退款单 查询销售数据
                List<OcBSapSalesDataRecord> ocBSapSalesDataRecordList = ocBSapSalesDataRecordMapper.selectNaiKaRecordByAfBillNo(ocBReturnAfSend.getBillNo());
                if (CollectionUtils.isEmpty(ocBSapSalesDataRecordList)) {
                    log.info("同步奶卡作废时,销售数据还没有生成:{}", ocborderId);
                    return new ValueHolderV14<>(ResultCode.SUCCESS, "success");
                }
                // 如果有销售数据 需要看销售数据是不是已经汇总了
                OcBSapSalesDataRecord ocBSapSalesDataRecord = ocBSapSalesDataRecordList.get(0);
                if (StringUtils.isEmpty(ocBSapSalesDataRecord.getSumStatus()) || ObjectUtil.equal(ocBSapSalesDataRecord.getSumStatus(), OcBSapSalesDataRecordConstant.SUM_STATUS_ZERO)) {
                    // 如果还没汇总 则确认是否已经有值了 如果没有值 则进行赋值
                    ocBSapSalesDataRecordService.updateResidueQty(ocBReturnAfSend.getBillNo(), ocBSapSalesDataRecord.getId(), Boolean.FALSE);
                }

            }
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "success");
    }
}
