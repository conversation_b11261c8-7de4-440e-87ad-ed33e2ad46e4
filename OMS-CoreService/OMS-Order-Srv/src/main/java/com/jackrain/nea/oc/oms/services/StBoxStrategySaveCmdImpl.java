package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StBoxStrategySaveCmd;
import com.jackrain.nea.oc.oms.model.table.StCBoxStrategyEntity;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 箱型策略
 *
 * <AUTHOR>
 * @Date 2024/1/31
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StBoxStrategySaveCmdImpl extends CommonCommandAdapter implements StBoxStrategySaveCmd {

    @Autowired
    private StCBoxStrategyService boxStrategyService;

    @Override
    @OmsOperationLog(mainTableName = "ST_C_BOX_STRATEGY")
    public ValueHolder execute(QuerySession session) throws NDSException {
        StCBoxStrategyEntity stCBoxStrategy = parseObj(StCBoxStrategyEntity.class, "ST_C_BOX_STRATEGY", session);

        if (Objects.isNull(stCBoxStrategy)) {
            return ValueHolderUtils.fail("解析箱型策略的保存数据为空");
        }

        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        JSONObject bodyJsn = param.getJSONObject("fixcolumn");

        // 这种情况就是把字段值置空了
        if (bodyJsn.getJSONObject("ST_C_BOX_STRATEGY").containsKey("BOX_NAME") &&
                null == bodyJsn.getJSONObject("ST_C_BOX_STRATEGY").getString("BOX_NAME")) {
            stCBoxStrategy.setBoxName("-1");
        }

        User user = session.getUser();

        if (Objects.nonNull(stCBoxStrategy.getId())) {
            return boxStrategyService.updateBoxStrategy(user, stCBoxStrategy);
        }
        else {
            return boxStrategyService.addBoxStrategy(user, stCBoxStrategy);
        }
    }
}
