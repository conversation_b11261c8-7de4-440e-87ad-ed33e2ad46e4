package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.OcBRefundPackageTaskVoidCmd;
import com.jackrain.nea.oc.oms.services.task.OcBRefundPackageTaskService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 退货包裹状态中间表-作废
 *
 * <AUTHOR>
 * @since 2025-06-16 17:27
 */

@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class OcBRefundPackageTaskVoidCmdImpl extends CommandAdapter implements OcBRefundPackageTaskVoidCmd {
    @Resource
    private OcBRefundPackageTaskService ocBRefundPackageTaskService;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        return ocBRefundPackageTaskService.voidData(session);
    }
}
