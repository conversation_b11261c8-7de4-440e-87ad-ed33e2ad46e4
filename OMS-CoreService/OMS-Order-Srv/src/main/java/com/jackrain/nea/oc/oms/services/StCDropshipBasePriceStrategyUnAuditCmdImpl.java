package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCDropshipBasePriceStrategyUnAuditCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * 一件代发客户基价策略-反审核命令实现
 *
 * <AUTHOR>
 */
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCDropshipBasePriceStrategyUnAuditCmdImpl implements StCDropshipBasePriceStrategyUnAuditCmd {

    @Autowired
    private StCDropshipBasePriceStrategyService strategyService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return strategyService.unAuditExecute(querySession);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }

}
