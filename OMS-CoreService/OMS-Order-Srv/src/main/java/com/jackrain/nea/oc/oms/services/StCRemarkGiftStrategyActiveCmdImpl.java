package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCRemarkGiftStrategyActiveCmd;
import com.jackrain.nea.oc.oms.mapper.StCRemarkGiftStrategyMapper;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.table.StCRemarkGiftStrategy;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * 备注赠品策略-启用
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCRemarkGiftStrategyActiveCmdImpl extends CommonCommandAdapter implements StCRemarkGiftStrategyActiveCmd {

    @Resource
    private StCRemarkGiftStrategyMapper stCRemarkGiftStrategyMapper;
    @Resource
    private StCRemarkGiftStrategyService stCRemarkGiftStrategyService;

    @Override
    @OmsOperationLog(operationType = "OPEN", mainTableName = "ST_C_REMARK_GIFT_STRATEGY")
    public ValueHolder execute(QuerySession session) throws NDSException {
        List<Long> objIds = getObjIds(session);

        if (CollectionUtils.isNotEmpty(objIds)) {
            for (Long objId : objIds) {
                StCRemarkGiftStrategy strategy = stCRemarkGiftStrategyMapper.selectById(objId);
                if (strategy == null) {
                    continue;
                }

                try {
                    String isactive = strategy.getIsactive();
                    if ("Y".equals(isactive)){
                        throw new NDSException("策略已启用，无法再次启用");
                    }
                    stCRemarkGiftStrategyService.checkRule(strategy);
                } catch (Exception e) {
                    return ValueHolderUtils.fail("策略" + strategy.getStrategyCode() + "启用失败!失败原因:" + e.getMessage());
                }

                // 执行启用操作
                stCRemarkGiftStrategyMapper.activeStrategy(objId);

                // 删除缓存
                String strategyRedisKey = OmsRedisKeyResources.buildStCRemarkGiftStrategyRedisKey(strategy.getShopId());
                RedisOpsUtil.getStrRedisTemplate().delete(strategyRedisKey);
            }
        }
        return ValueHolderUtils.success("启用成功");
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}