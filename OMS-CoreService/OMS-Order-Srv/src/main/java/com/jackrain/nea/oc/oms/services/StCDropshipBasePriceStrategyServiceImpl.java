package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.mapper.StCDropshipBasePriceStrategyDetailMapper;
import com.jackrain.nea.oc.oms.mapper.StCDropshipBasePriceStrategyMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategy;
import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategyDetail;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.CommandAdapterUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 一件代发客户基价策略Service实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class StCDropshipBasePriceStrategyServiceImpl implements StCDropshipBasePriceStrategyService {

    @Autowired
    private StCDropshipBasePriceStrategyMapper strategyMapper;

    @Autowired
    private StCDropshipBasePriceStrategyDetailMapper detailMapper;

    @Resource
    private BuildSequenceUtil buildSequenceUtil;

    @Resource
    private OcBOperationLogMapper operationLogMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        try {
            // 参数校验
            ValueHolder valueHolder = CommandAdapterUtil.checkSaveSession(querySession, "st_c_dropship_base_price_strategy");
            if (!valueHolder.isOK()) {
                return valueHolder;
            }
            DefaultWebEvent event = querySession.getEvent();
            JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                    "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

            JSONObject bodyJsn = param.getJSONObject("fixcolumn");
            User user = querySession.getUser();

            Long id = param.getLong("objid");
            JSONObject fixColumn = (JSONObject) valueHolder.get(OcCommonConstant.FIX_COLUMN);
            JSONObject strategyJson = fixColumn.getJSONObject("ST_C_DROPSHIP_BASE_PRICE_STRATEGY");
            JSONArray detailArray = fixColumn.getJSONArray("ST_C_DROPSHIP_BASE_PRICE_STRATEGY_DETAIL");

            // 解析主表数据
            StCDropshipBasePriceStrategy strategy;
            if (strategyJson != null) {
                // 如果有策略数据，则解析
                strategy = JSON.parseObject(strategyJson.toJSONString(), StCDropshipBasePriceStrategy.class);
                if (id != null && id > 0) {
                    strategy.setId(id);
                }
            } else if (id != null && id > 0) {
                // 如果没有策略数据但有ID，则根据ID查询策略数据
                strategy = strategyMapper.selectById(id);
                if (strategy == null) {
                    return ValueHolderUtils.getFailValueHolder("策略不存在");
                }
            } else {
                // 既没有策略数据也没有ID，创建新的策略对象
                strategy = new StCDropshipBasePriceStrategy();
            }

            // 解析明细数据
            List<StCDropshipBasePriceStrategyDetail> detailList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(detailArray)) {
                for (int i = 0; i < detailArray.size(); i++) {
                    JSONObject detailJson = detailArray.getJSONObject(i);
                    StCDropshipBasePriceStrategyDetail detail = JSON.parseObject(detailJson.toJSONString(), StCDropshipBasePriceStrategyDetail.class);
                    detailList.add(detail);
                }
            }

            // 根据店铺ID查询是否已有策略
            StCDropshipBasePriceStrategy existingStrategy = null;
            if (strategy.getShopId() != null) {
                existingStrategy = strategyMapper.selectByShopId(strategy.getShopId());
            }

            if (existingStrategy == null) {
                // 店铺没有策略，新增策略
                // 数据校验（新增时不需要排除ID）
                String validateResult = validateStrategyData(strategy, detailList, null);
                if (StringUtils.isNotBlank(validateResult)) {
                    return ValueHolderUtils.getFailValueHolder(validateResult);
                }
                return insertStrategy(strategy, detailList, user);
            } else {
                // 店铺已有策略，检查审核状态
                if (existingStrategy.getAuditStatus() != null && existingStrategy.getAuditStatus() == 1) {
                    return ValueHolderUtils.getFailValueHolder("该店铺的策略已审核，不能维护明细！");
                }

                // 在现有策略中新增明细
                // 数据校验（检查新增的SKU是否与现有明细重复）
                String validateResult = validateNewDetails(existingStrategy.getId(), detailList);
                if (StringUtils.isNotBlank(validateResult)) {
                    return ValueHolderUtils.getFailValueHolder(validateResult);
                }
                return addDetailsToExistingStrategy(existingStrategy, detailList, user);
            }

        } catch (Exception e) {
            log.error("一件代发客户基价策略保存失败", e);
            throw new NDSException("保存失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder auditExecute(QuerySession querySession) throws NDSException {
        try {
            DefaultWebEvent event = querySession.getEvent();
            JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));

            // 获取ID列表，支持两种格式：objid 或 ids数组
            List<Long> idList = new ArrayList<>();
            if (param.containsKey(OcCommonConstant.OBJ_ID)) {
                Long id = param.getLong(OcCommonConstant.OBJ_ID);
                if (id != null) {
                    idList.add(id);
                }
            } else if (param.containsKey("ids")) {
                JSONArray idsArray = param.getJSONArray("ids");
                if (CollectionUtils.isNotEmpty(idsArray)) {
                    for (int i = 0; i < idsArray.size(); i++) {
                        String idStr = idsArray.getString(i);
                        if (StringUtils.isNotBlank(idStr)) {
                            idList.add(Long.parseLong(idStr));
                        }
                    }
                }
            }

            if (CollectionUtils.isEmpty(idList)) {
                return ValueHolderUtils.getFailValueHolder("策略ID不能为空");
            }

            User user = querySession.getUser();

            // 批量审核处理
            return batchAudit(idList, user);

        } catch (Exception e) {
            log.error("审核一件代发客户基价策略失败", e);
            throw new NDSException("审核失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder unAuditExecute(QuerySession querySession) throws NDSException {
        try {
            DefaultWebEvent event = querySession.getEvent();
            JSONObject param = JSON.parseObject(JSON.toJSONString(event.getParameterValue("param")));

            // 获取ID列表，支持两种格式：objid 或 ids数组
            List<Long> idList = new ArrayList<>();
            if (param.containsKey(OcCommonConstant.OBJ_ID)) {
                Long id = param.getLong(OcCommonConstant.OBJ_ID);
                if (id != null) {
                    idList.add(id);
                }
            } else if (param.containsKey("ids")) {
                JSONArray idsArray = param.getJSONArray("ids");
                if (CollectionUtils.isNotEmpty(idsArray)) {
                    for (int i = 0; i < idsArray.size(); i++) {
                        String idStr = idsArray.getString(i);
                        if (StringUtils.isNotBlank(idStr)) {
                            idList.add(Long.parseLong(idStr));
                        }
                    }
                }
            }

            if (CollectionUtils.isEmpty(idList)) {
                return ValueHolderUtils.getFailValueHolder("策略ID不能为空");
            }

            User user = querySession.getUser();

            // 批量反审核处理
            return batchUnAudit(idList, user);

        } catch (Exception e) {
            log.error("反审核一件代发客户基价策略失败", e);
            throw new NDSException("反审核失败：" + e.getMessage());
        }
    }

    /**
     * 新增策略
     */
    private ValueHolder insertStrategy(StCDropshipBasePriceStrategy strategy, List<StCDropshipBasePriceStrategyDetail> detailList, User user) {
        // 设置默认值
        strategy.setAuditStatus(0); // 默认待审核状态
        strategy.setSkuCount(CollectionUtils.isNotEmpty(detailList) ? detailList.size() : 0);
        strategy.setId(buildSequenceUtil.buildStCDropshipBasePriceStrategySequenceId());
        JSONObject obj = new JSONObject();
        obj.put("ST_C_DROPSHIP_BASE_PRICE_STRATEGY", strategy);
        String strategyNo = SequenceGenUtil.generateSquence("ST_C_DROPSHIP_BASE_PRICE_STRATEGY", obj, user.getLocale(), false);
        strategy.setStrategyCode(strategyNo);

        BaseModelUtil.initialBaseModelSystemField(strategy, user);
        strategyMapper.insert(strategy);

        // 插入明细
        if (CollectionUtils.isNotEmpty(detailList)) {
            List<OcBOperationLog> operationLogs = Lists.newArrayList();
            for (StCDropshipBasePriceStrategyDetail detail : detailList) {
                detail.setId(buildSequenceUtil.buildStCDropshipBasePriceStrategyDetailSequenceId());
                detail.setStrategyId(strategy.getId());
                BaseModelUtil.initialBaseModelSystemField(detail, user);

                OcBOperationLog operationLog = buildItemOperationLog(detail, user);
                operationLogs.add(operationLog);
            }
            detailMapper.batchInsert(detailList);
            //操作日志
            operationLogMapper.batchInsert(operationLogs);
        }

        updateSkuCount(strategy);

        log.info("新增一件代发客户基价策略成功，策略ID={}，操作人={}", strategy.getId(), user.getName());
        return ValueHolderUtils.success("新增成功！", ValueHolderUtils.createAddErrorData("ST_C_DROPSHIP_BASE_PRICE_STRATEGY", strategy.getId(), null));
    }

    private OcBOperationLog buildItemOperationLog(StCDropshipBasePriceStrategyDetail strategyItem, User user) {
        StringBuilder afterValue = new StringBuilder();
        afterValue.setLength(0);
        afterValue.append("内容:").append(strategyItem.getImportContent()).append(";SKU:").append(strategyItem.getSkuCode()).append(";基价:").append(strategyItem.getBasePrice());
        OcBOperationLog operationLog = getOperationLog("ST_C_DROPSHIP_BASE_PRICE_STRATEGY_DETAIL", "ADD", strategyItem.getStrategyId(),
                "策略明细", "新增策略明细", "新增", afterValue.toString(), user);
        return operationLog;
    }

    private OcBOperationLog getOperationLog(String tableName, String operationType, Long updateId,
                                            String tableDescription, String columnName, String columnBeforeValue,
                                            String columnAfterValue, User user) {
        OcBOperationLog operationLog = new OcBOperationLog();
        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_OC_B_OPERATION_LOG));
        operationLog.setTableName(tableName);
        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
        operationLog.setUpdateId(updateId);
        operationLog.setUpdateModelName(tableDescription);
        operationLog.setModContent(columnName);
        operationLog.setBeforeData(columnBeforeValue);
        operationLog.setAfterData(columnAfterValue);
        StBeanUtils.makeCreateField(operationLog, user);
        return operationLog;
    }

    /**
     * 编辑策略
     */
    private ValueHolder updateStrategy(StCDropshipBasePriceStrategy strategy, List<StCDropshipBasePriceStrategyDetail> detailList, User user) {
        // 检查策略是否存在
        StCDropshipBasePriceStrategy existStrategy = strategyMapper.selectById(strategy.getId());
        if (existStrategy == null) {
            return ValueHolderUtils.getFailValueHolder("策略不存在");
        }

        // 检查是否已审核
        if (existStrategy.getAuditStatus() != null && existStrategy.getAuditStatus() == 1) {
            return ValueHolderUtils.getFailValueHolder("已审核的策略不能编辑");
        }

        // 更新主表
        strategy.setSkuCount(CollectionUtils.isNotEmpty(detailList) ? detailList.size() : 0);
        BaseModelUtil.makeBaseModifyField(strategy, user);
        strategyMapper.updateById(strategy);

        // 删除原有明细
        detailMapper.deleteByStrategyId(strategy.getId());

        // 重新插入明细
        if (CollectionUtils.isNotEmpty(detailList)) {
            for (StCDropshipBasePriceStrategyDetail detail : detailList) {
                detail.setStrategyId(strategy.getId());
                CommandAdapterUtil.defaultOperator(detail, user);
            }
            detailMapper.batchInsert(detailList);
        }
        updateSkuCount(strategy);

        log.info("编辑一件代发客户基价策略成功，策略ID={}，操作人={}", strategy.getId(), user.getName());
        return ValueHolderUtils.success("编辑成功！", ValueHolderUtils.createAddErrorData("ST_C_DROPSHIP_BASE_PRICE_STRATEGY", strategy.getId(), null));
    }

    private void updateSkuCount(StCDropshipBasePriceStrategy strategy) {
        StCDropshipBasePriceStrategy update = new StCDropshipBasePriceStrategy();
        List<StCDropshipBasePriceStrategyDetail> detailList = detailMapper.selectByStrategyId(strategy.getId());
        update.setId(strategy.getId());
        update.setSkuCount(detailList.size());
        strategyMapper.updateById(update);
    }

    /**
     * 校验策略数据（仅用于新增策略时）
     */
    private String validateStrategyData(StCDropshipBasePriceStrategy strategy, List<StCDropshipBasePriceStrategyDetail> detailList, Long excludeId) {
        // 1. 店铺必填
        if (strategy.getShopId() == null) {
            return "请先选择店铺！";
        }

        // 2. 明细数据不能为空
        if (CollectionUtils.isEmpty(detailList)) {
            return "明细数据不能为空！";
        }

        // 3. 明细下不能有SKU相同的多条数据
        Set<String> skuSet = new HashSet<>();
        for (StCDropshipBasePriceStrategyDetail detail : detailList) {
            if (StringUtils.isBlank(detail.getSkuCode())) {
                return "SKU编码不能为空！";
            }
            if (StringUtils.isBlank(detail.getImportContent())) {
                return "导入内容不能为空！";
            }
            if (detail.getBasePrice() == null || detail.getBasePrice().compareTo(BigDecimal.ZERO) <= 0) {
                return "录单基价必须大于0！";
            }
            if (skuSet.contains(detail.getSkuCode())) {
                return "该SKU已存在，不能维护明细！";
            }
            skuSet.add(detail.getSkuCode());
        }

        return null;
    }

    /**
     * 校验新增明细数据（检查是否与现有明细重复）
     */
    private String validateNewDetails(Long strategyId, List<StCDropshipBasePriceStrategyDetail> newDetailList) {
        if (CollectionUtils.isEmpty(newDetailList)) {
            return "明细数据不能为空！";
        }

        // 获取现有明细
        List<StCDropshipBasePriceStrategyDetail> existingDetails = detailMapper.selectByStrategyId(strategyId);
        Set<String> existingSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(existingDetails)) {
            for (StCDropshipBasePriceStrategyDetail detail : existingDetails) {
                existingSet.add(detail.getSkuCode());
            }
        }

        // 检查新增明细
        Set<String> newSkuSet = new HashSet<>();
        for (StCDropshipBasePriceStrategyDetail detail : newDetailList) {
            if (StringUtils.isBlank(detail.getSkuCode())) {
                return "SKU编码不能为空！";
            }
            if (StringUtils.isBlank(detail.getImportContent())) {
                return "导入内容不能为空！";
            }
            if (detail.getBasePrice() == null || detail.getBasePrice().compareTo(BigDecimal.ZERO) <= 0) {
                return "录单基价必须大于0！";
            }

            // 检查新增明细内部是否有重复
            if (newSkuSet.contains(detail.getSkuCode())) {
                return "存在相同SKU的数据，请检测！";
            }
            newSkuSet.add(detail.getSkuCode());

            // 检查是否与现有明细重复
            if (existingSet.contains(detail.getSkuCode())) {
                return "该SKU已存在，不能维护明细！";
            }
        }

        return null;
    }

    /**
     * 在现有策略中新增明细
     */
    private ValueHolder addDetailsToExistingStrategy(StCDropshipBasePriceStrategy existingStrategy,
                                                     List<StCDropshipBasePriceStrategyDetail> detailList, User user) {
        // 插入新明细
        if (CollectionUtils.isNotEmpty(detailList)) {
            List<OcBOperationLog> operationLogs = Lists.newArrayList();
            for (StCDropshipBasePriceStrategyDetail detail : detailList) {
                detail.setId(buildSequenceUtil.buildStCDropshipBasePriceStrategyDetailSequenceId());
                detail.setStrategyId(existingStrategy.getId());
                BaseModelUtil.initialBaseModelSystemField(detail, user);

                OcBOperationLog operationLog = buildItemOperationLog(detail, user);
                operationLogs.add(operationLog);
            }
            detailMapper.batchInsert(detailList);
            operationLogMapper.batchInsert(operationLogs);
        }

        // 更新SKU数量
        updateSkuCount(existingStrategy);

        log.info("在现有策略中新增明细成功，策略ID={}，新增明细数量={}，操作人={}",
                existingStrategy.getId(), detailList.size(), user.getName());
        return ValueHolderUtils.success("新增明细成功！",
                ValueHolderUtils.createAddErrorData("st_c_dropship_base_price_strategy", existingStrategy.getId(), null));
    }

    /**
     * 批量审核策略
     */
    private ValueHolder batchAudit(List<Long> idList, User user) {
        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();

        for (Long id : idList) {
            try {
                StCDropshipBasePriceStrategy strategy = strategyMapper.selectById(id);
                if (strategy == null) {
                    failList.add("策略ID " + id + " 不存在");
                    continue;
                }

                if (strategy.getAuditStatus() != null && strategy.getAuditStatus() == 1) {
                    failList.add("策略ID " + id + " 已审核，无需重复审核");
                    continue;
                }

                // 更新审核状态
                StCDropshipBasePriceStrategy updateStrategy = new StCDropshipBasePriceStrategy();
                updateStrategy.setId(id);
                updateStrategy.setAuditStatus(1); // 已审核
                BaseModelUtil.makeBaseModifyField(updateStrategy, user);

                strategyMapper.updateById(updateStrategy);
                successList.add("策略ID " + id);
                log.info("审核一件代发客户基价策略成功，策略ID={}，操作人={}", id, user.getName());

            } catch (Exception e) {
                failList.add("策略ID " + id + " 审核失败：" + e.getMessage());
                log.error("审核策略ID={}失败", id, e);
            }
        }

        // 构造返回结果
        return buildBatchResult(successList, failList, "审核");
    }

    /**
     * 批量反审核策略
     */
    private ValueHolder batchUnAudit(List<Long> idList, User user) {
        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();

        for (Long id : idList) {
            try {
                StCDropshipBasePriceStrategy strategy = strategyMapper.selectById(id);
                if (strategy == null) {
                    failList.add("策略ID " + id + " 不存在");
                    continue;
                }

                if (strategy.getAuditStatus() == null || strategy.getAuditStatus() != 1) {
                    failList.add("策略ID " + id + " 只有已审核的策略才能反审核");
                    continue;
                }

                // 更新审核状态
                StCDropshipBasePriceStrategy updateStrategy = new StCDropshipBasePriceStrategy();
                updateStrategy.setId(id);
                updateStrategy.setAuditStatus(0); // 已反审核
                BaseModelUtil.makeBaseModifyField(updateStrategy, user);

                strategyMapper.updateById(updateStrategy);
                successList.add("策略ID " + id);
                log.info("反审核一件代发客户基价策略成功，策略ID={}，操作人={}", id, user.getName());

            } catch (Exception e) {
                failList.add("策略ID " + id + " 反审核失败：" + e.getMessage());
                log.error("反审核策略ID={}失败", id, e);
            }
        }

        // 构造返回结果
        return buildBatchResult(successList, failList, "反审核");
    }

    /**
     * 构造批量操作结果
     */
    private ValueHolder buildBatchResult(List<String> successList, List<String> failList, String operation) {
        int successCount = successList.size();
        int failCount = failList.size();
        int totalCount = successCount + failCount;

        if (totalCount == 1) {
            // 单个操作
            if (successCount == 1) {
                return ValueHolderUtils.getSuccessValueHolder(operation + "成功");
            } else {
                return ValueHolderUtils.getFailValueHolder(failList.get(0));
            }
        } else {
            // 批量操作
            if (failCount == 0) {
                return ValueHolderUtils.getSuccessValueHolder(operation + "成功，共处理 " + successCount + " 条记录");
            } else if (successCount == 0) {
                return ValueHolderUtils.getFailValueHolder(operation + "失败，共 " + failCount + " 条记录失败");
            } else {
                String message = operation + "完成，成功 " + successCount + " 条，失败 " + failCount + " 条";
                ValueHolder valueHolder = ValueHolderUtils.getSuccessValueHolder(message);

                // 添加失败详情到data中
                JSONArray failDetails = new JSONArray();
                for (String failMsg : failList) {
                    JSONObject failDetail = new JSONObject();
                    failDetail.put("message", failMsg);
                    failDetails.add(failDetail);
                }
                valueHolder.put("failDetails", failDetails);
                return valueHolder;
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder deleteExecute(QuerySession querySession) throws NDSException {
        try {
            DefaultWebEvent event = querySession.getEvent();
            JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                    "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
            JSONObject tabItem = param.getJSONObject("tabitem");
            Long objid = param.getLong("objid");
            User user = querySession.getUser();

            if (objid == null) {
                return ValueHolderUtils.getFailValueHolder("策略ID不能为空");
            }

            // 查询策略是否存在
            StCDropshipBasePriceStrategy strategy = strategyMapper.selectById(objid);
            if (strategy == null) {
                return ValueHolderUtils.getFailValueHolder("未找到当前策略");
            }

            // 检查审核状态：已审核的策略不能删除
            if (strategy.getAuditStatus() != null && strategy.getAuditStatus() == 1) {
                return ValueHolderUtils.getFailValueHolder("已审核的策略不能删除");
            }

            // 判断是不是只删除子表明细
            if (tabItem != null && tabItem.getJSONArray("ST_C_DROPSHIP_BASE_PRICE_STRATEGY_DETAIL") != null) {
                // 删除的是明细
                JSONArray detailList = tabItem.getJSONArray("ST_C_DROPSHIP_BASE_PRICE_STRATEGY_DETAIL");
                if (CollectionUtils.isNotEmpty(detailList)) {
                    for (int i = 0; i < detailList.size(); i++) {
                        Long detailId = detailList.getLong(i);
                        if (detailId != null) {
                            // 逻辑删除明细
                            StCDropshipBasePriceStrategyDetail stCDropshipBasePriceStrategyDetail = detailMapper.selectById(detailId);
                            if (stCDropshipBasePriceStrategyDetail == null){
                                continue;
                            }
                            detailMapper.deleteById(detailId);

                            OcBOperationLog operationLog = buildItemDelOperationLog(stCDropshipBasePriceStrategyDetail, user);
                            operationLogMapper.insert(operationLog);
                        }
                    }
                    // 更新SKU数量
                    updateSkuCount(strategy);
                }
                log.info("删除一件代发客户基价策略明细成功，策略ID={}，删除明细数量={}，操作人={}",
                        objid, detailList.size(), user.getName());
                return ValueHolderUtils.getSuccessValueHolder("删除明细成功");
            } else {
                // 删除整个策略（主表和所有明细）
                strategyMapper.deleteById(objid);
                detailMapper.deleteByStrategyId(objid);

                log.info("删除一件代发客户基价策略成功，策略ID={}，操作人={}", objid, user.getName());
                return ValueHolderUtils.getSuccessValueHolder("删除成功");
            }

        } catch (Exception e) {
            log.error("删除一件代发客户基价策略失败", e);
            throw new NDSException("删除失败：" + e.getMessage());
        }
    }

    private OcBOperationLog buildItemDelOperationLog(StCDropshipBasePriceStrategyDetail itemStrategy, User user) {
        StringBuilder beforeValue = new StringBuilder();
        beforeValue.setLength(0);
        beforeValue.append("内容:").append(itemStrategy.getImportContent()).append(";SKU:").append(itemStrategy.getSkuCode()).append(";基价:").append(itemStrategy.getBasePrice());
        OcBOperationLog operationLog = getOperationLog("ST_C_DROPSHIP_BASE_PRICE_STRATEGY_DETAIL", "DEL", itemStrategy.getStrategyId(),
                "策略明细", "删除策略明细", beforeValue.toString(), "删除", user);
        return operationLog;
    }

}
