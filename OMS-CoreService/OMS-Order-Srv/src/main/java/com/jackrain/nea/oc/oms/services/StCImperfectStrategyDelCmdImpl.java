package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.api.StCImperfectStrategyDelCmd;
import com.jackrain.nea.oc.oms.mapper.OcBOperationLogMapper;
import com.jackrain.nea.oc.oms.mapper.StCImperfectStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCImperfectStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.TocCcAppointRuleEnum;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategy;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategyItem;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.jackrain.nea.oc.oms.model.relation.OcBOrderConst.IS_ACTIVE_YES;

/**
 * 残次策略删除
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCImperfectStrategyDelCmdImpl implements StCImperfectStrategyDelCmd {

    @Resource
    private StCImperfectStrategyMapper strategyMapper;
    @Resource
    private StCImperfectStrategyItemMapper strategyItemMapper;
    @Resource
    private OcBOperationLogMapper operationLogMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        JSONObject tabItem = param.getJSONObject("tabitem");
        Long objid = param.getLong("objid");

        StCImperfectStrategy stCImperfectStrategy = strategyMapper.selectById(objid);
        if (Objects.isNull(stCImperfectStrategy)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("msg", "未找到当前策略");
            return valueHolder;
        }
        if (IS_ACTIVE_YES.equals(stCImperfectStrategy.getIsactive())) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("msg", "当前状态为启用状态，不允许修改");
            return valueHolder;
        }

        //判断是不是只删除子表
        if (Objects.nonNull(tabItem) && (CollectionUtils.isNotEmpty(tabItem.getJSONArray("ST_C_IMPERFECT_STRATEGY_ITEM")))) {
            // 删除的是子表
            JSONArray itemList = tabItem.getJSONArray("ST_C_IMPERFECT_STRATEGY_ITEM");
            List<OcBOperationLog> operationLogList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(itemList)) {
                for (int i = 0; i < itemList.size(); i++) {
                    Long itemId = itemList.getLong(i);
                    StCImperfectStrategyItem itemStrategy = strategyItemMapper.selectById(itemId);
                    if (Objects.nonNull(itemId)) {
                        //删除规则
                        strategyItemMapper.deleteById(itemId);
                    }
                    OcBOperationLog operationLog = buildItemOperationLog(itemStrategy, user);
                    operationLogList.add(operationLog);
                }
            }
            // 如果operationLogList不为空 则批量新增
            if (CollectionUtils.isNotEmpty(operationLogList)) {
                operationLogMapper.batchInsert(operationLogList);
            }
        } else {
            strategyMapper.deleteById(objid);
            strategyItemMapper.deleteByStrategyId(objid);
        }

        //删除缓存
        String strategyRedisKey = OmsRedisKeyResources.buildStCImperfectStrategyRedisKey(stCImperfectStrategy.getShopId());
        RedisOpsUtil.getStrRedisTemplate().delete(strategyRedisKey);

        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("msg", "删除成功");
        return valueHolder;
    }

    private OcBOperationLog buildItemOperationLog(StCImperfectStrategyItem itemStrategy, User user) {
        StringBuilder beforeValue = new StringBuilder();
        beforeValue.setLength(0);
        beforeValue.append("[").append(TocCcAppointRuleEnum.getDescriptionByVal(itemStrategy.getAppointRule())).append("],[").append(itemStrategy.getAppointContent()).append("]");
        OcBOperationLog operationLog = getOperationLog("ST_C_IMPERFECT_STRATEGY_ITEM", "DEL", itemStrategy.getImperfectStrategyId(),
                "残次策略规则", "删除残次策略规则", beforeValue.toString(), "删除", user);
        return operationLog;
    }

    /**
     * 获取操作日志对象
     *
     * @param tableName
     * @param operationType
     * @param updateId
     * @param tableDescription
     * @param columnName
     * @param columnBeforeValue
     * @param columnAfterValue
     * @param user
     * @return
     */
    private OcBOperationLog getOperationLog(String tableName, String operationType, Long updateId,
                                            String tableDescription, String columnName, String columnBeforeValue,
                                            String columnAfterValue, User user) {
        OcBOperationLog operationLog = new OcBOperationLog();
        operationLog.setId(ModelUtil.getSequence(StConstant.TAB_OC_B_OPERATION_LOG));
        operationLog.setTableName(tableName);
        operationLog.setOperationType(OperationTypeEnum.getNameByValue(operationType));
        operationLog.setUpdateId(updateId);
        operationLog.setUpdateModelName(tableDescription);
        operationLog.setModContent(columnName);
        operationLog.setBeforeData(columnBeforeValue);
        operationLog.setAfterData(columnAfterValue);
        StBeanUtils.makeCreateField(operationLog, user);
        return operationLog;
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }
}
