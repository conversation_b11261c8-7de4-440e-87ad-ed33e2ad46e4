package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCDropshipBasePriceStrategyDelCmd;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * 一件代发客户基价策略-删除命令实现
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCDropshipBasePriceStrategyDelCmdImpl implements StCDropshipBasePriceStrategyDelCmd {

    @Autowired
    private StCDropshipBasePriceStrategyService strategyService;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        return strategyService.deleteExecute(querySession);
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }

}
