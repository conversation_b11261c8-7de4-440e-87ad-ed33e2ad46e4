package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.LockOrderCmd;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.model.request.LockOrderRequest;
import com.jackrain.nea.oc.oms.model.result.LockOrderResult;
import com.jackrain.nea.oc.oms.process.transfer.impl.lock.lockorder.OrderLockProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Descroption 淘宝手工锁单
 * <AUTHOR>
 * @Date 2019/10/11 20:31
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class LockOrderCmdImpl implements LockOrderCmd {
    @Autowired
    private OrderLockProcessImpl orderLockProcessImpl;
    @Autowired
    protected IpOrderLockService ipOrderLockService;

    @Override
    public ValueHolderV14<LockOrderResult> startLockOrder(LockOrderRequest lockOrderRequest) {
        ValueHolderV14<LockOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        boolean errFlag = false;
        int failedNum = 0;
        int successNum = 0;
        List<ProcessStepResult> errorStepResultList = new ArrayList<>();
        try {
            for (Long lockId : lockOrderRequest.getLockIdList()) {
                IpOrderLockRelation ipOrderLockRelation = ipOrderLockService.getLockRelation(lockId);
                if (ipOrderLockRelation != null) {
                    ProcessStepResultList processStepResult =
                            orderLockProcessImpl.start(ipOrderLockRelation, false,
                                    lockOrderRequest.getOperateUser());
                    if (!processStepResult.isProcessSuccess()) {
                        errFlag = true;
                        failedNum++;
                        errorStepResultList.add(processStepResult.getLastFailedProcessStepResult());
                    } else {
                        successNum++;
                    }
                } else {
                    errFlag = false;
                    failedNum++;
                }
            }
            if (errFlag) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder errSbMsg = new StringBuilder();
                errSbMsg.append(String.format("锁单成功%s条；锁单失败%s条；失败原因：\r\n", successNum, failedNum));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        errSbMsg.append(stepResult.getMessage());
                        errSbMsg.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(errSbMsg.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("全部锁单成功!");
            }
        } catch (Exception ex) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("锁单异常：" + ex.getMessage());
            log.error(LogUtil.format("TaobaoLockOrderCmdImpl,异常信息:{}", "TaobaoLockOrderCmdImpl"), Throwables.getStackTraceAsString(ex));
        }
        return resultValueHolderV14;
    }
}
