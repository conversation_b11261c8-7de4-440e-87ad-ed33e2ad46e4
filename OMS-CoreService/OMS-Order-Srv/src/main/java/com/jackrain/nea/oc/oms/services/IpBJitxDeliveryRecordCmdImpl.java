package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.IpBJitxDeliveryRecordCmd;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryRecord;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * className: IpBJitxDeliveryRecordCmdImpl
 * description:JITX寻仓结果
 *
 * <AUTHOR>
 * create: 2021-12-17
 * @since JDK 1.8
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class IpBJitxDeliveryRecordCmdImpl implements IpBJitxDeliveryRecordCmd {

    @Autowired
    private IpBJitxDeliveryRecordService deliveryRecordService;

    @Override
    public ValueHolderV14<Map<String, Long>> batchInsert(List<IpBJitxDeliveryRecord> recordList, User user) {

        if(log.isDebugEnabled()){
            log.debug(" 批量新增JITX寻仓结果入参：{}", JSON.toJSONString(recordList));
        }

        ValueHolderV14<Map<String, Long>> v14 = new ValueHolderV14<>(ResultCode.FAIL,"新增失败");
        try {
            v14 = deliveryRecordService.batchInsert(recordList, user);
        }catch (Exception e){
            log.error(" 批量新增JITX寻仓结果异常：{}", Throwables.getStackTraceAsString(e));
        }
        return v14;
    }
}
