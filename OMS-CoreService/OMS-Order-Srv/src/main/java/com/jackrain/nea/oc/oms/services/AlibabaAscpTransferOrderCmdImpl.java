package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.AlibabaAscpTransferOrderCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.AlibabaAscpTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 猫超直发单据Service服务接口
 *
 * @author: 易邵峰
 * @since: 2019-03-27
 * create at : 2019-03-27 22:01
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class AlibabaAscpTransferOrderCmdImpl implements AlibabaAscpTransferOrderCmd {

    @Autowired
    private AlibabaAscpTransferOrderProcessImpl orderProcess;

    @Autowired
    private IpAlibabaAscpOrderService alibabaAscpOrderService;

    @Override
    public ValueHolderV14<TransferOrderResult> startTransferOrder(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<TransferOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("AlibabaAscpTransferOrderCmdImpl,ChannelType：{}", orderNo, "AlibabaAscpTransferOrderCmdImpl"), transferOrderRequest.getChannelType());
                }
                if (transferOrderRequest.getChannelType() == ChannelType.ALIBABAASCP) {
                    IpAlibabaAscpOrderRelation orderRelation = alibabaAscpOrderService.selectAlibabaAscpOrder(orderNo);
                    if (orderRelation != null) {
                        if (log.isDebugEnabled()) {
                            log.debug(this.getClass().getName() + " 猫超直发单据转换查询数据:{}", JSONObject.toJSONString(orderRelation));
                        }
                        ProcessStepResultList processStepResultList = orderProcess.start(orderRelation,
                                false, transferOrderRequest.getOperateUser());
                        if (!processStepResultList.isProcessSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResultList.getLastFailedProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }

            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("AlibabaAscpTransferOrderCmdImpl,异常信息:{}", "AlibabaAscpTransferOrderCmdImpl"), Throwables.getStackTraceAsString(ex));
        }

        return resultValueHolderV14;
    }

}
