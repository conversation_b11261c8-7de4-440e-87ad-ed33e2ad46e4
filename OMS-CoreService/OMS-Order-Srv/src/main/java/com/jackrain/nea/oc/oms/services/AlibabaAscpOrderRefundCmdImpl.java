package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.AlibabaAscpOrderRefundCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.ReturnOrderResult;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.AlibabaAscpOrderRefundTransferProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Descroption 手工天猫直发退单转换
 * <AUTHOR>
 * @Date 2019/5/16 20:31
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class AlibabaAscpOrderRefundCmdImpl implements AlibabaAscpOrderRefundCmd {
    @Autowired
    private AlibabaAscpOrderRefundTransferProcessImpl refundTransferProcess;
    @Autowired
    private IpBAlibabaAscpOrderRefundService orderRefundService;

    @Override
    public ValueHolderV14<ReturnOrderResult> startRefundOrder(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<ReturnOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        boolean errFlag = false;
        int failedNum = 0;
        int successNum = 0;
        List<ProcessStepResult> errorStepResultList = new ArrayList<>();
        try {
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (log.isDebugEnabled()) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("startRefundOrder,ChannelType：{}", orderNo, "startRefundOrder"), transferOrderRequest.getChannelType());
                    }
                }
                if (transferOrderRequest.getChannelType() == ChannelType.ALIBABAASCP) {
                    IpBAlibabaAscpOrderRefundRelation refundRelation = orderRefundService.getAlibabaAscpRefundRelation(orderNo);
                    if (refundRelation != null) {
                        ProcessStepResultList processStepResult =
                                refundTransferProcess.start(refundRelation, false,
                                        transferOrderRequest.getOperateUser());
                        if (!processStepResult.isProcessSuccess()) {
                            errFlag = true;
                            failedNum++;
                            errorStepResultList.add(processStepResult.getLastFailedProcessStepResult());
                        } else {
                            successNum++;
                        }
                    } else {
                        errFlag = false;
                        failedNum++;
                    }
                }
            }
            if (errFlag) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder errSbMsg = new StringBuilder();
                errSbMsg.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNum, failedNum));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        errSbMsg.append(stepResult.getMessage());
                        errSbMsg.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(errSbMsg.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("全部转换成功!");
            }
        } catch (Exception ex) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("startRefundOrder,异常信息:{}", "startRefundOrder"), Throwables.getStackTraceAsString(ex));

        }
        return resultValueHolderV14;
    }
}
