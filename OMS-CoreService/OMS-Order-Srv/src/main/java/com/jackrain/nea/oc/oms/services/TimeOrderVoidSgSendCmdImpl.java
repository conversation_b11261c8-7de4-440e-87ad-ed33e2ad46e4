package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.TimeOrderVoidSgSendCmd;
import com.jackrain.nea.oc.oms.model.request.TimeOrderVoidSgSendRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: huang.zaizai
 * @since: 2019-08-19
 * create at : 2019-08-19 11:50
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class TimeOrderVoidSgSendCmdImpl implements TimeOrderVoidSgSendCmd {
    @Autowired
    private TimeOrderVoidSgSendService service;

    @Override
    public ValueHolderV14 voidSgSendV14(TimeOrderVoidSgSendRequest request) throws NDSException {
        return service.voidSgSendV14(request);
    }
}
