package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCShortStockNoSplitStrategySaveCmd;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyDetailEntity;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyEntity;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyRelation;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utils.AssertUtils;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2024/3/4
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class StCShortStockNoSplitStrategySaveCmdImpl extends CommandAdapter implements StCShortStockNoSplitStrategySaveCmd {

    @Autowired
    private StCShortStockNoSplitStrategyService shortStockNoSplitStrategyService;

    @Override
    @OmsOperationLog(mainTableName = "ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY", itemsTableName = "ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY_DETAIL")
    public ValueHolder execute(QuerySession session) throws NDSException {

        StCShortStockNoSplitStrategyRelation stCShortStockNoSplitStrategyRelation = parseStrategy(session);

        Long id = stCShortStockNoSplitStrategyRelation.getShortStockNoSplitStrategyEntity().getId();

        if (Objects.isNull(id)) {
            return shortStockNoSplitStrategyService.addShortStockNoSplitStrategy(session.getUser(), stCShortStockNoSplitStrategyRelation);
        }
        else {
            DefaultWebEvent event = session.getEvent();
            JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                    "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
            JSONObject bodyJsn = param.getJSONObject("fixcolumn");
            JSONObject mainStrategyJson = bodyJsn.getJSONObject("ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY");
            if (Objects.nonNull(mainStrategyJson) && mainStrategyJson.containsKey("REMARK")) {
                shortStockNoSplitStrategyService.update(Wrappers.lambdaUpdate(new StCShortStockNoSplitStrategyEntity())
                        .set(StCShortStockNoSplitStrategyEntity::getRemark, mainStrategyJson.get("REMARK"))
                        .eq(StCShortStockNoSplitStrategyEntity::getId, id));
            }
            return shortStockNoSplitStrategyService.updateShortStockNoSplitStrategy(session.getUser(), stCShortStockNoSplitStrategyRelation);
        }
    }


    public StCShortStockNoSplitStrategyRelation parseStrategy(QuerySession session) {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);

        JSONObject bodyJsn = param.getJSONObject("fixcolumn");
        AssertUtils.cannot(bodyJsn == null || bodyJsn.isEmpty(), "参数体为空");

        log.info("缺货不拆策略ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY: {}", param.toJSONString());

        StCShortStockNoSplitStrategyRelation stCShortStockNoSplitStrategyRelation = new StCShortStockNoSplitStrategyRelation();
        if (null == bodyJsn || bodyJsn.isEmpty()) {
            return stCShortStockNoSplitStrategyRelation;
        }

        JSONObject mainStrategyJson = bodyJsn.getJSONObject("ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY");

        if (Objects.nonNull(mainStrategyJson)) {
            StCShortStockNoSplitStrategyEntity mainStrategy = JSON.parseObject(mainStrategyJson.toJSONString(), StCShortStockNoSplitStrategyEntity.class);
            stCShortStockNoSplitStrategyRelation.setShortStockNoSplitStrategyEntity(mainStrategy);
            stCShortStockNoSplitStrategyRelation.setIsMain(true);
        } else {
            stCShortStockNoSplitStrategyRelation.setIsMain(false);
            stCShortStockNoSplitStrategyRelation.setShortStockNoSplitStrategyEntity(new StCShortStockNoSplitStrategyEntity());
        }

        JSONArray jsonArray = bodyJsn.getJSONArray("ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY_DETAIL");
        if (Objects.nonNull(jsonArray)) {
            List<StCShortStockNoSplitStrategyDetailEntity> detailList = JSON.parseArray(jsonArray.toJSONString(), StCShortStockNoSplitStrategyDetailEntity.class);
            stCShortStockNoSplitStrategyRelation.setShortStockNoSplitStrategyDetailEntityList(detailList);
        }

        if (null != param.getString("objid") && !"-1".equals(param.getString("objid"))) {
            stCShortStockNoSplitStrategyRelation.getShortStockNoSplitStrategyEntity()
                    .setId(Long.parseLong(String.valueOf(param.getString("objid"))));

        }

        return stCShortStockNoSplitStrategyRelation;
    }
}
