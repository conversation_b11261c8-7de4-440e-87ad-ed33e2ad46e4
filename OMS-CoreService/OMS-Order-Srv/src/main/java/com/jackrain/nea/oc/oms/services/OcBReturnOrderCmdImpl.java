package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.api.OcBReturnOrderCmd;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.nums.ReturnOrderNodeEnum;
import com.jackrain.nea.oc.request.CancelOrderModel;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-12-22 14:44
 * @Description : 退换货单相关服务
 **/
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OcBReturnOrderCmdImpl implements OcBReturnOrderCmd {

    @Autowired
    private OcBReturnOrderService ocBReturnOrderService;

    @Autowired
    private OcCancelChangingOrRefundService ocCancelChangingOrRefundService;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OcBReturnOrderLogMapper logMapper;

    @Autowired
    IpRpcService ipRpcService;

    @Autowired
    private OcBReturnOrderNodeRecordService nodeRecordService;

    /**
     * 取消退换货单
     * @param cancelOrderModel
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolderV14 cancelReturnOrder(CancelOrderModel cancelOrderModel) throws NDSException {
        return ocBReturnOrderService.cancelReturnOrder(cancelOrderModel);
    }
}
