package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.VipTimeOrderCancelCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.oc.oms.nums.TimeOrderOutEnum;
import com.jackrain.nea.oc.oms.process.jitx.timeorder.cancel.VipTimeOrderCancelProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 陈秀楼
 * @since : 2019-06-27
 * create at : 2019-06-27 20:00
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class VipTimeOrderCancelCmdImpl implements VipTimeOrderCancelCmd {

    @Autowired
    private VipTimeOrderCancelProcessImpl timeOrderCancelProcess;

    @Autowired
    private IpVipTimeOrderCancelService timeOrderCancelService;

    @Override
    public ValueHolderV14<TransferOrderResult> startTransferTimeOrderCancel(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<TransferOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<ProcessStepResult> errorStepResultList = new ArrayList<>();
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("VipTimeOrderCancelCmdImpl.startTransferTimeOrderCancel.ChannelType",
                            orderNo), transferOrderRequest.getChannelType());
                }
                if (transferOrderRequest.getChannelType() == ChannelType.VIPJITX) {
                    IpBCancelTimeOrderVipRelation orderRelation = this.timeOrderCancelService.selectCancelTimeOrder(orderNo);
                    if (orderRelation != null) {
                        ProcessStepResultList processStepResultList = timeOrderCancelProcess.start(orderRelation,
                                false, transferOrderRequest.getOperateUser());
                        if (!processStepResultList.isProcessFishSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResultList.getLastFaileOrFinishProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }
            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("VipTimeOrderCancelCmdImpl.startTransferTimeOrderCancel: {}"), Throwables.getStackTraceAsString(ex));
        }
        return resultValueHolderV14;
    }

    @Override
    public ValueHolderV14 releaseTimeOrderStock(String occupiedOrderSn, User user) throws NDSException {
        return timeOrderCancelService.releaseTimeOrderStock(occupiedOrderSn, user, TimeOrderOutEnum.MAUUAL.getKey());
    }
}
