package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.OcBOrderDrpCancelCmd;
import com.jackrain.nea.util.ValueHolder;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * className: OcBOrderDrpCancelCmdImpl
 * description: DRP退款
 *
 * <AUTHOR>
 * create: 2021-08-23
 * @since JDK 1.8
 */
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OcBOrderDrpCancelCmdImpl implements OcBOrderDrpCancelCmd {

    @Autowired
    private OcBOrderAllRefundService refundService;

    @Override
    public ValueHolder drpBatchCancel(List<String> billNos) {
        return refundService.drpBatchCancel(billNos);
    }
}
