package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.OcBOrderLogCmd;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: DXF
 * @since: 2020/12/7
 * create at : 2020/12/7 10:34
 */
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class OcBOrderLogCmdImpl implements OcBOrderLogCmd {

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Override
    public void addUserOrderLog(long orderId, String billNo, String logType, String logMessage, String param, String errorMessage, User operateUser) {
        omsOrderLogService.addUserOrderLog(orderId, billNo, logType, logMessage, param, errorMessage, operateUser);
    }

    @Override
    public void addUserOrderLogList(List<OcBOrder> list, String logType, String logMessage, String param, String errorMessage, User operateUser) {
        if (CollectionUtils.isNotEmpty(list)) {
            for (OcBOrder ocBOrder : list) {
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), logType, logMessage, param, errorMessage, operateUser);
            }
        }
    }
}
