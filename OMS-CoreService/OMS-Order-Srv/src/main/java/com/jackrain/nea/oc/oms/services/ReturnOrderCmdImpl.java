package com.jackrain.nea.oc.oms.services;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.ReturnOrderCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.ReturnOrderResult;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.TaobaoTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class ReturnOrderCmdImpl implements ReturnOrderCmd {

    @Autowired
    private TaobaoTransferRefundProcessImpl refundProcess;
    @Autowired
    private OmsTaobaoRefundService omsTaobaoRefundService;


    @Override
    public ValueHolderV14<ReturnOrderResult> startReturnOrder(TransferOrderRequest transferOrderRequest) {
        ValueHolderV14<ReturnOrderResult> resultValueHolderV14 = new ValueHolderV14<>();
        boolean hasError = false;
        int failedNumber = 0;
        int successNumber = 0;
        List<ProcessStepResult> errorStepResultList = new ArrayList<>();
        try {
            for (String orderNo : transferOrderRequest.getOrderNoList()) {
                if (transferOrderRequest.getChannelType() == ChannelType.TAOBAO) {
                    OmsTaobaoRefundRelation omsTaobaoRefundRelation = omsTaobaoRefundService.selectTaoBaoRefundRelation(orderNo);
                    if (omsTaobaoRefundRelation != null) {
                        ProcessStepResultList processStepResult =
                                refundProcess.start(omsTaobaoRefundRelation, false,
                                        transferOrderRequest.getOperateUser());
                        if (!processStepResult.isProcessSuccess()) {
                            hasError = true;
                            errorStepResultList.add(processStepResult.getLastFailedProcessStepResult());
                            failedNumber++;
                        } else {
                            successNumber++;
                        }
                    } else {
                        hasError = false;
                        failedNumber++;
                    }
                }
            }
            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("转换成功%s条；转换失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (ProcessStepResult stepResult : errorStepResultList) {
                    if (stepResult != null) {
                        sbMessage.append(stepResult.getMessage());
                        sbMessage.append("\r\n");
                    }
                }
                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("转换全部成功。");
            }
        } catch (Exception ex) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("转换异常：" + ex.getMessage());
            log.error(LogUtil.format("淘宝退单转换异常,异常信息:{}", "淘宝退单转换异常"), Throwables.getStackTraceAsString(ex));
        }

        return resultValueHolderV14;
    }
}
