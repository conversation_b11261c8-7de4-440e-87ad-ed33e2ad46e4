package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.oc.oms.api.NaiKaAccountCmd;
import com.jackrain.nea.oc.oms.model.request.NaiKaAccountInitRequest;
import com.jackrain.nea.oc.oms.model.request.NaiKaAccountRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName NaiKaAccountCmdImpl
 * @Description 奶卡对账回传
 * <AUTHOR>
 * @Date 2022/9/5 11:07
 * @Version 1.0
 */
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
@Slf4j
@Component
public class NaiKaAccountCmdImpl implements NaiKaAccountCmd {

    @Autowired
    private NaiKaAccountService naiKaAccountService;

    @Override
    public ValueHolderV14 naiKaAccount(NaiKaAccountRequest request) {
        return naiKaAccountService.execute(request);
    }

    @Override
    public ValueHolderV14 naiKaAccountInit(NaiKaAccountInitRequest naiKaAccountInitRequest) {
        return naiKaAccountService.naiKaAccountInit(naiKaAccountInitRequest);
    }

}
