package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.annotation.OmsOperationLog;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.api.StCAppointExpressStrategySaveCmd;
import com.jackrain.nea.st.model.StCAppointExpressStrategy;
import com.jackrain.nea.st.model.StCAppointExpressStrategyDetail;
import com.jackrain.nea.st.model.StCAppointExpressStrategyRelation;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * @ClassName StCAppointExpressStrategyCmdImpl
 * @Description 指定快递
 * <AUTHOR>
 * @Date 2024/4/12 15:20
 * @Version 1.0
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", group = "oc-core", version = "1.0")
public class StCAppointExpressStrategySaveCmdImpl extends CommandAdapter implements StCAppointExpressStrategySaveCmd {

    @Autowired
    private StCAppointExpressStrategyService stCAppointExpressStrategyService;
    @Autowired
    private StCAppointExpressStrategyDetailService stCAppointExpressStrategyDetailService;

    @Override
    @OmsOperationLog(mainTableName = "ST_C_APPOINT_EXPRESS_STRATEGY", itemsTableName = "ST_C_APPOINT_EXPRESS_STRATEGY_DETAIL")
    public ValueHolder execute(QuerySession session) throws NDSException {
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug("Start Update model strategy Receive Params:{}", param);
        }
        User user = session.getUser();
        Long id = param.getLong("objid");
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        JSONObject before = param.getJSONObject("beforevalue");
        JSONObject after = param.getJSONObject("aftervalue");

        if (fixColumn != null && id != null) {
            if (id < 0) {
                return addModelStrategy(fixColumn, user);
            } else {
                return updateModelStrategy(fixColumn, user, id, before, after);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    private ValueHolder addModelStrategy(JSONObject fixColumn, User user) {
        ValueHolder valueHolder = new ValueHolder();
        Long id = ModelUtil.getSequence("ST_C_APPOINT_EXPRESS_STRATEGY");
        try {
            StCAppointExpressStrategyRelation modelStrategyRequest = JsonUtils.jsonParseClass(fixColumn, StCAppointExpressStrategyRelation.class);
            StCAppointExpressStrategy stCAppointExpressStrategy = modelStrategyRequest.getStCAppointExpressStrategy();
            List<StCAppointExpressStrategyDetail> stCAppointExpressStrategyDetailList = modelStrategyRequest.getStCAppointExpressStrategyDetailList();

            // 同一个店铺只能有一个策略
            if (stCAppointExpressStrategy.getStrategyType() == 2 && stCAppointExpressStrategy.getShopId() != null) {
                StCAppointExpressStrategy strategy = stCAppointExpressStrategyService.getOne(
                        new QueryWrapper<StCAppointExpressStrategy>().eq("SHOP_ID", stCAppointExpressStrategy.getShopId()).eq("ISACTIVE", "Y"));
                if (strategy != null) {
                    throw new NDSException("当前店铺已存在策略，请先删除！");
                }
            }

            if (stCAppointExpressStrategy.getStrategyType() == 1) {
                StCAppointExpressStrategy strategy = stCAppointExpressStrategyService.getCommonStrategy();
                if (strategy != null) {
                    throw new NDSException("已存在通用策略，请先删除！");
                }
            }
            // 自动生成一个编码
            String strategyNo = getStrategyNo(stCAppointExpressStrategy, user.getLocale());
            stCAppointExpressStrategy.setStrategyCode(strategyNo);
            stCAppointExpressStrategy.setId(id);
            stCAppointExpressStrategy.setAdClientId((long) user.getClientId());
            stCAppointExpressStrategy.setAdOrgId((long) user.getOrgId());
            stCAppointExpressStrategy.setCreationdate(new Date());
            stCAppointExpressStrategy.setModifieddate(new Date());
            stCAppointExpressStrategy.setOwnerid(Long.valueOf(user.getId()));
            stCAppointExpressStrategy.setOwnername(user.getName());
            stCAppointExpressStrategy.setModifierid(Long.valueOf(user.getId()));
            stCAppointExpressStrategy.setModifiername(user.getName());
            stCAppointExpressStrategy.setIsactive("N");

            // 检验明细
            if (CollectionUtils.isNotEmpty(stCAppointExpressStrategyDetailList)) {
                for (StCAppointExpressStrategyDetail detail : stCAppointExpressStrategyDetailList) {
                    if (detail.getCpCLogisticsId() == null) {
                        throw new NDSException("请选择快递公司！");
                    }
                    if (StringUtils.isBlank(detail.getMatchRule())) {
                        throw new NDSException("请输入匹配规则！");
                    }
                    if (StringUtils.isBlank(detail.getAgeing())) {
                        throw new NDSException("请输入时效！");
                    }
                    // 如果之前平台商品id存在了 不允许保存
                    StCAppointExpressStrategyDetail strategyDetail = stCAppointExpressStrategyDetailService.getByMatchRuleAndMatchContent(id, detail.getMatchRule(), detail.getMatchContent());
                    if (strategyDetail != null) {
                        throw new NDSException("当前平台商品已存在策略，请先删除！");
                    }
                }

                for (StCAppointExpressStrategyDetail detail : stCAppointExpressStrategyDetailList) {
                    detail.setId(ModelUtil.getSequence("ST_C_APPOINT_EXPRESS_STRATEGY_DETAIL"));
                    detail.setStrategyId(id);
                    detail.setAdClientId((long) user.getClientId());
                    detail.setAdOrgId((long) user.getOrgId());
                    detail.setCreationdate(new Date());
                    detail.setModifieddate(new Date());
                    detail.setOwnerid(Long.valueOf(user.getId()));
                    detail.setOwnername(user.getName());
                    detail.setModifierid(Long.valueOf(user.getId()));
                    detail.setModifiername(user.getName());
                    detail.setIsactive("Y");
                }
            }
            add(stCAppointExpressStrategy, stCAppointExpressStrategyDetailList);
            valueHolder.put("data", id);
        } catch (Exception e) {
            log.error("add appointExpressStrategy error", e);
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("message", e.getMessage());
            return valueHolder;
        }
        return ValueHolderUtils.success("新增成功！", ValueHolderUtils.createAddErrorData("ST_C_APPOINT_EXPRESS_STRATEGY", id, null));
    }

    private ValueHolder updateModelStrategy(JSONObject fixColumn, User user, Long id, JSONObject before, JSONObject after) {

        ValueHolder valueHolder = new ValueHolder();
        valueHolder.put("code", ResultCode.SUCCESS);
        valueHolder.put("message", "success");
        log.info("updateModelStrategy, param,fixColumn:{}", fixColumn.toJSONString());

        StCAppointExpressStrategyRelation modelStrategyRequest = JsonUtils.jsonParseClass(fixColumn, StCAppointExpressStrategyRelation.class);
        // 有可能是修改 也可能是新增明细
        if (modelStrategyRequest.getStCAppointExpressStrategy() != null) {
            // 可能是修改主表数据
            if (after != null) {
                StCAppointExpressStrategyRelation afterRequest = JsonUtils.jsonParseClass(after, StCAppointExpressStrategyRelation.class);
                if (afterRequest.getStCAppointExpressStrategy() != null) {
                    // 修改主表数据
                    StCAppointExpressStrategy stCAppointExpressStrategy = afterRequest.getStCAppointExpressStrategy();
                    stCAppointExpressStrategy.setId(id);
                    stCAppointExpressStrategy.setModifieddate(new Date());
                    stCAppointExpressStrategy.setModifierid(Long.valueOf(user.getId()));
                    stCAppointExpressStrategy.setModifiername(user.getName());
                    stCAppointExpressStrategy.setRemark(stCAppointExpressStrategy.getRemark());
                    stCAppointExpressStrategyService.updateById(stCAppointExpressStrategy);
                }
            }
        }
        if (modelStrategyRequest.getStCAppointExpressStrategyDetailList() != null) {
            List<StCAppointExpressStrategyDetail> stCAppointExpressStrategyDetailList = modelStrategyRequest.getStCAppointExpressStrategyDetailList();

            for (StCAppointExpressStrategyDetail detail : stCAppointExpressStrategyDetailList) {
                if (detail.getId() > 0) {
                    continue;
                }
                if (detail.getCpCLogisticsId() == null) {
                    throw new NDSException("请选择快递公司！");
                }
                if (StringUtils.isBlank(detail.getMatchRule())) {
                    throw new NDSException("请输入匹配规则！");
                }
                if (StringUtils.isBlank(detail.getAgeing())) {
                    throw new NDSException("请输入时效！");
                }
                // 如果之前平台商品id存在了 不允许保存
                StCAppointExpressStrategyDetail strategyDetail = stCAppointExpressStrategyDetailService.getByMatchRuleAndMatchContent(id, detail.getMatchRule(), detail.getMatchContent());
                if (strategyDetail != null) {
                    throw new NDSException("当前平台商品已存在策略，请先删除！");
                }
            }

            for (StCAppointExpressStrategyDetail detail : stCAppointExpressStrategyDetailList) {
                Long detailId = detail.getId();
                if (detailId < 0) {

                    Long ageing = Long.valueOf(detail.getAgeing());
                    if (ageing < 0L && ageing != -1L) {
                        throw new NDSException("时效设置有误");
                    }
                    // 新增明细数据
                    detail.setId(ModelUtil.getSequence("ST_C_APPOINT_EXPRESS_STRATEGY_DETAIL"));
                    detail.setStrategyId(id);
                    detail.setAdClientId((long) user.getClientId());
                    detail.setAdOrgId((long) user.getOrgId());
                    detail.setCreationdate(new Date());
                    detail.setModifieddate(new Date());
                    detail.setOwnerid(Long.valueOf(user.getId()));
                    detail.setOwnername(user.getName());
                    detail.setModifierid(Long.valueOf(user.getId()));
                    detail.setModifiername(user.getName());
                    detail.setIsactive("Y");
                    stCAppointExpressStrategyDetailService.save(detail);
                } else {
                    // 修改明细数据
                    detail.setModifieddate(new Date());
                    detail.setModifierid(Long.valueOf(user.getId()));
                    detail.setModifiername(user.getName());
                    detail.setModifierename(user.getEname());
                    stCAppointExpressStrategyDetailService.updateById(detail);
                }
            }
        }

        return valueHolder;

    }

    public String getStrategyNo(StCAppointExpressStrategy strategyEntity, Locale locale) {
        JSONObject obj = new JSONObject();
        obj.put("ST_C_APPOINT_EXPRESS_STRATEGY", strategyEntity);
        return SequenceGenUtil.generateSquence("ST_C_APPOINT_EXPRESS_STRATEGY", obj, locale, false);
    }

    @Transactional(rollbackFor = Exception.class)
    public void add(StCAppointExpressStrategy modelStrategyDO, List<StCAppointExpressStrategyDetail> itemStrategyDOList) {
        stCAppointExpressStrategyService.save(modelStrategyDO);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(itemStrategyDOList)) {
            stCAppointExpressStrategyDetailService.saveBatch(itemStrategyDOList);
        }
    }
}
