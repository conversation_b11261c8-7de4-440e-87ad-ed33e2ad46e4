package com.jackrain.nea.oc.oms.services;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.api.StCRemarkGiftStrategyDelCmd;
import com.jackrain.nea.oc.oms.mapper.StCRemarkGiftStrategyMapper;
import com.jackrain.nea.oc.oms.model.table.StCRemarkGiftStrategy;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Objects;

import static com.jackrain.nea.oc.oms.model.relation.OcBOrderConst.IS_ACTIVE_YES;

/**
 * 备注赠品策略删除
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@Service(protocol = "dubbo", validation = "true", version = "1.0", group = "oc-core")
public class StCRemarkGiftStrategyDelCmdImpl implements StCRemarkGiftStrategyDelCmd {

    @Resource
    private StCRemarkGiftStrategyMapper strategyMapper;

    @Override
    public ValueHolder execute(QuerySession session) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = session.getEvent();
        User user = session.getUser();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        Long objid = param.getLong("objid");

        StCRemarkGiftStrategy strategy = strategyMapper.selectById(objid);
        if (Objects.isNull(strategy)) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("msg", "未找到当前策略");
            return valueHolder;
        }

        if (IS_ACTIVE_YES.equals(strategy.getIsactive())) {
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("msg", "当前状态为启用状态，不允许删除");
            return valueHolder;
        }

        try {
            strategyMapper.deleteById(objid);

            valueHolder.put("code", ResultCode.SUCCESS);
            valueHolder.put("msg", "删除成功");
        } catch (Exception e) {
            log.error("delete remark gift strategy error", e);
            valueHolder.put("code", ResultCode.FAIL);
            valueHolder.put("msg", "删除失败");
        }

        return valueHolder;
    }

    @Override
    public ValueHolder execute(HashMap map) throws NDSException {
        return null;
    }

}