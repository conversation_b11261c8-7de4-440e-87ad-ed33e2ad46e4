//package com.jackrain.nea.oc.oms.task.sap;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.exception.NDSException;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
//import com.jackrain.nea.oc.oms.sap.Oms2SapMqConfig;
//import com.jackrain.nea.oc.oms.sap.Oms2SapStatusEnum;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.r3.mq.util.R3MqSendHelper;
//import com.jackrain.nea.rpc.IpRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.utility.ExceptionUtil;
//import com.jackrain.nea.web.face.User;
//import com.jackrain.nea.web.face.impl.UserImpl;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.ExecutionException;
//import java.util.concurrent.Future;
//import java.util.function.Function;
//import java.util.function.Predicate;
//
//
///**
// * @Desc :
// * <AUTHOR> xiWen
// * @Date : 2020/3/26
// */
//@Slf4j
//@Deprecated
//public abstract class AbstractOms2SapTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    protected R3MqSendHelper r3MqSendHelper;
//
//    @Autowired
//    protected Oms2SapMapper oms2SapMapper;
//
//    @Autowired
//    private ThreadPoolTaskExecutor commonThreadPoolExecutor;
//
//    @Autowired
//    protected Oms2SapMqConfig oms2SapMqConfig;
//    protected String nameSpaceKey = "lts";
//    protected String statusKey = "oms2SapTaskOrderStatus";
//    protected String eachSizeKey = "oms2sap.order.task.size";
//    protected int eachSize = 200;
//    /**
//     * assert value
//     */
//    protected Predicate<List> unExpect = objects -> {
//        return objects == null || objects.size() == 0;
//    };
//    /**
//     * get order id function
//     */
//    protected Function<Object, Long> orderIdFun;
//    /**
//     * thread pool config
//     */
//    int corePoolSize = 8;
//    int maxPoolSize = 20;
//    long keepAliveThreadTime = 60000;
//    @Autowired
//    private IpRpcService ipRpcService;
//
//    private static User getRootUser() {
//        UserImpl user = new UserImpl();
//        user.setId(893);
//        user.setName("admin");
//        user.setEname("Pokemon-mapper");
//        user.setActive(true);
//        user.setClientId(37);
//        user.setOrgId(27);
//        user.setIsAdmin(2);
//        user.setIsDev(2);
//        return user;
//    }
//
//    /**
//     * tog.mq
//     *
//     * @return tag
//     */
//    protected abstract String getTag();
//
//    /**
//     * topic.mq
//     *
//     * @return topic
//     */
//    protected abstract String getTopic();
//
//    /**
//     * task order table
//     *
//     * @return auto task order name
//     */
//    protected abstract String getTaskTableName();
//
//    /**
//     * origin order prev modify column
//     *
//     * @return origin order column
//     */
//    protected abstract String getOriginCol();
//
//    /**
//     * target  order table name
//     *
//     * @return origin table name
//     */
//    protected abstract String getOriginTableName();
//
//    /**
//     * current thread pool name
//     *
//     * @return thread pool name
//     */
//    protected abstract String getThreadPoolName();
//
//    /**
//     * current target order operator
//     *
//     * @param list      shard keys
//     * @param activeIds active id list
//     * @return order data
//     */
//    protected abstract List getSapItems(List<Long> list, List<Long> activeIds);
//
//    @Override
//    @XxlJob("AbstractOms2SapTask")
//    public RunTaskResult execute(JSONObject params) {
//        RunTaskResult result = new RunTaskResult();
//        long start = System.currentTimeMillis();
//        recordLog("{} {} Start execute...", this.getClass().getName(), getThreadPoolName());
//        try {
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer size = config.getProperty(eachSizeKey, 1000);
//            Integer oms2sapStatus = config.getProperty(statusKey, 0);
//
//            List<Future<Boolean>> results = new ArrayList<>();
//            Set<String> keySet = topMap.keySet();
//            if (log.isDebugEnabled()) {
//                log.debug("{} start multiple DRDS store node task,scan node list:{}", this.getClass().getName(), keySet);
//            }
//            for (String node : keySet) {
//                results.add(commonThreadPoolExecutor.submit(new OmsToSapCallable(node, topMap.get(node), size, oms2sapStatus)));
//            }
//
//            for (Future<Boolean> futureResult : results) {
//                try {
//                    recordLog(" --> Result {}", futureResult.get().toString());
//                } catch (InterruptedException e) {
//                    log.error(" -->Thread InterruptedException：" + e);
//                } catch (ExecutionException e) {
//                    log.error(" -->Thread ExecutionException：" + e);
//                }
//            }
//            long end = System.currentTimeMillis();
//            recordLog("{} execute End --> useTime {}", this.getClass().getName(), (end - start));
//            result.setSuccess(true);
//            result.setMessage("AbstractOms2SapTask execute finished, useTime: " + (end - start));
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error(ExceptionUtil.getMessage(e));
//            result.setSuccess(false);
//            result.setMessage(e.getMessage());
//        }
//        return result;
//    }
//
//    /**
//     * send mq to sap
//     * update order status
//     *
//     * @param thdName current thread name
//     * @param odrIds  origin order id
//     * @return bool
//     */
//    private boolean batchUpdateAndSendSap(String thdName, List<Long> odrIds) {
//
//        int step = 1;
//        long l = System.currentTimeMillis();
//        List<Long> activeIds = new ArrayList<>();
//        try {
//
//            List sapItems = getSapItems(odrIds, activeIds);
//            if (unExpect.test(activeIds)) {
//                activeIds = odrIds;
//            }
//            if (unExpect.test(sapItems)) {
//                step = 2;
//                oms2SapMapper.updateDynamicTaskOrder(getTaskTableName(), Oms2SapStatusEnum.PROCESSED.val(), odrIds);
//                log.error("{} Step02-1 OrigOrder SearchConvert None Or Not Exist Match Status,ids-{}", thdName, odrIds);
//                return false;
//            }
//            if (log.isDebugEnabled()) {
//                log.debug("AbstractOms2SapTask.batchUpdateAndSendSap集合:{}", sapItems);
//            }
//            step = 3;
//            AbstractOms2SapTask bean = ApplicationContextHandle.getBean(this.getClass());
//            //@20200827 零售单传SAP 状态改为'传中' 防止 IS回写覆盖状态
//            int taskResult = oms2SapMapper.updateDynamicOrigOrder(getOriginTableName(), getOriginCol(),
//                    Oms2SapStatusEnum.SUCCESS.val(), odrIds);
//            if (log.isDebugEnabled()) {
//                log.debug("AbstractOms2SapTask.batchUpdateAndSendSap,更新传中状态结果:{},集合:{}", taskResult, odrIds);
//            }
//            bean.batchSendSap(thdName, sapItems, activeIds);
//
//            step = 4;
//            boolean diff = odrIds.size() > activeIds.size();
//            if (diff) {
//                odrIds.removeAll(activeIds);
//                if (odrIds.size() > 0) {
//                    oms2SapMapper.updateDynamicTaskOrder(getTaskTableName(), Oms2SapStatusEnum.PROCESSED.val(), odrIds);
//                } else {
//                    log.error("{} Step02-4 Update Void Task Order 4, But There Is No Element,MayBe Repeat Id", thdName);
//                }
//            }
//            long l1 = System.currentTimeMillis();
//            recordLog("{} Step02 BatchDeal Finished,Spend {} ms ", thdName, (l1 - l));
//            return true;
//        } catch (Exception e) {
//            switch (step) {
//                case 1:
//                    log.error("{} Step02-1 BatchDeal Search And Convert Origin Order Exception,ids-{}", thdName, odrIds);
//                    break;
//                case 2:
//                    log.error("{} Step02-2 BatchDeal Empty OriginOrder, Update Task Exception,ids-{}", thdName, odrIds);
//                    break;
//                case 3:
//                    log.error("{} Step02-3 BatchDeal Update Task Order,Send MQ Exception,ids-{}", thdName, activeIds);
//                    break;
//                case 4:
//                    log.error("{} Step02-4 Deal Void Task Order Exception,ids-{}", thdName, odrIds);
//                    break;
//                default:
//                    log.error("{} Step02-x BatchDeal Default Exception,ids-{}", thdName, odrIds);
//                    break;
//            }
//            log.error(ExceptionUtil.getMessage(e));
//        }
//        return false;
//    }
//
//    @Transactional(rollbackFor = NDSException.class)
//    public void batchSendSap(String thdName, List sapItems, List<Long> keys) {
//
//        try {
//            List<Long> okIds = new ArrayList<>();
//            List<Long> noIds = new ArrayList<>();
//            //B2C 订单出库
//            if (getTaskTableName().equals("oc_b_task_order_sap")) {
////                ValueHolderV14 stockres = ipRpcService.b2cOrderOutStockExecute(sapItems, getRootUser());
////                if (null == stockres.getData()) {
////                    recordLog("调用IP服务-B2C销售订单出库,调用失败 返回data空，错误信息:{}", stockres.getMessage());
////                    return;
////                }
//                JSONArray resulArray =null;// JSON.parseArray(stockres.getData().toString());
//                for (int i = 0; i < resulArray.size(); i++) {
//                    String code = resulArray.getJSONObject(i).get("code").toString();
//                    Long orderId = Long.valueOf(String.valueOf(resulArray.getJSONObject(i).get("id")));
//                    if (null != orderId) {
//                        if ("0".equals(code)) {
//                            okIds.add(orderId);
//                        } else {
//                            noIds.add(orderId);
//                        }
//                    }
//                }
//                //recordLog("调用IP服务-B2C销售订单出库,返回:{}", stockres);
//            }
//            //B2C 订单退
////            if ("oc_b_task_return_sap".equals(getTaskTableName())) {
////                //ValueHolderV14 refunfres = ipRpcService.b2cOrderRefundExecute(sapItems, getRootUser());
//////                if (null == refunfres.getData()) {
//////                    recordLog("调用IP服务-B2C订单退,调用失败 返回data空，错误信息:{}", refunfres.getMessage());
//////                    return;
//////                }
//////                JSONArray resulArray = JSON.parseArray(refunfres.getData().toString());
////                for (int i = 0; i < resulArray.size(); i++) {
////                    String code = resulArray.getJSONObject(i).get("code").toString();
////                    Long orderId = Long.valueOf(String.valueOf(resulArray.getJSONObject(i).get("id")));
////                    if (null != orderId) {
////                        if ("0".equals(code)) {
////                            okIds.add(orderId);
////                        } else {
////                            noIds.add(orderId);
////                        }
////                    }
////                }
////                recordLog("调用IP服务-B2C订单退,返回:{}", refunfres);
////            }
//            if (CollectionUtils.isNotEmpty(okIds)) {
//                //IP接收成功 传MQ成功 返回OMS
//                int oktask = oms2SapMapper.updateDynamicTaskOrder(getTaskTableName(), Oms2SapStatusEnum.SUCCESS.val(), okIds);
//                recordLog("{} Step02-3 成功被IP接收传MQ Update TaskOrder,OriginOrder Result, Task-{}, Order-{} ", thdName, oktask);
//            }
//            if (CollectionUtils.isNotEmpty(noIds)) {
//                //IP接收失败 传MQ失败 返回OMS
//                int notask = oms2SapMapper.updateDynamicTaskOrder(getTaskTableName(), Oms2SapStatusEnum.FAILED.val(), noIds);
//                int noorder = oms2SapMapper.updateDynamicOrigOrder(getOriginTableName(), getOriginCol(),
//                        Oms2SapStatusEnum.FAILED.val(), noIds);
//                recordLog("{} Step02-3 IP接收失败 传MQ失败 Update TaskOrder,OriginOrder Result, Task-{}, Order-{} ", thdName, notask, noorder);
//            }
//
//            //传WOS 调用IP服务
//            IpB2COrderToWos(thdName, sapItems);
//            //String msgId = r3MqSendHelper.sendMessage(JSON.toJSONString(sapItems), getTopic(), getTag());
//            //recordLog("{} Step02-4 MQ Send Success, msgId {} ", thdName, msgId);
//
//        } catch (Exception e) {
//            log.error("{} Step02-4 Send IP CMD Exception", thdName);
//            log.error(ExceptionUtil.getMessage(e));
//            throw new NDSException("Send IP CMD To Sap Exception");
//        }
//    }
//
//    private void IpB2COrderToWos(String thdName, List sapItems) {
//        try {
//            //B2C订单出库数据 传WOS
//            if (getTaskTableName().equals("oc_b_task_order_sap")) {
//                List<Long> okwosIds = new ArrayList<>();
//                List<Long> nowosIds = new ArrayList<>();
//                ValueHolderV14 stockres =null;// ipRpcService.b2cOrderOutToWosExectue(sapItems, getRootUser());
//                if (null == stockres.getData()) {
//                    recordLog("调用IP服务-传WOSi的B2C销售订单出库,调用失败 返回data空，错误信息:{}", stockres.getMessage());
//                    return;
//                }
//                JSONArray resulArray = JSON.parseArray(stockres.getData().toString());
//                for (int i = 0; i < resulArray.size(); i++) {
//                    String code = resulArray.getJSONObject(i).get("code").toString();
//                    Long orderId = Long.valueOf(String.valueOf(resulArray.getJSONObject(i).get("id")));
//                    if (null != orderId) {
//                        if ("0".equals(code)) {
//                            okwosIds.add(orderId);
//                        } else {
//                            nowosIds.add(orderId);
//                        }
//                    }
//                }
//                recordLog("调用IP服务-传WOS的B2C销售订单出库,返回:{}", stockres);
//
//                if (CollectionUtils.isNotEmpty(okwosIds)) {
//                    //IP接收传WOS成功 传MQ成功 返回OMS
//                    int okwostask = oms2SapMapper.updateWosOrderTaskOrder(getTaskTableName(), Oms2SapStatusEnum.SUCCESS.val(), okwosIds);
//                    recordLog("{} Step02-3 成功被IP接收传MQ Update TaskOrder,OriginOrder Result,thdName-{}, Task-{}", thdName, okwostask);
//
//                }
//                if (CollectionUtils.isNotEmpty(nowosIds)) {
//                    //IP接收传WOS失败 传MQ失败 返回OMS
//                    int nowostask = oms2SapMapper.updateWosOrderTaskOrder(getTaskTableName(), Oms2SapStatusEnum.FAILED.val(), nowosIds);
//                    recordLog("{} Step02-3 IP接收失败 传MQ失败 Update TaskOrder,OriginOrder Result,thdName-{}, Task-{}", thdName, nowostask);
//                }
//            }
//        } catch (NumberFormatException e) {
//            recordLog("{} Step02-4 Send IP CMD Exception:{}", thdName, ExceptionUtil.getMessage(e));
//            throw new NDSException("Send IP CMD To Sap Exception");
//        }
//    }
//
//    /**
//     * @param list list
//     * @return string
//     */
//    protected String joinList2String(List<Long> list) {
//        StringBuilder sb = new StringBuilder();
//        for (int i = 0, l = list.size(); i < l; i++) {
//            if (i > 0) {
//                sb.append(",");
//            }
//            sb.append(list.get(i));
//        }
//        return sb.toString();
//    }
//
//    /**
//     * debug level log
//     *
//     * @param msg message
//     * @param obj exception
//     */
//    protected void recordLog(String msg, Object... obj) {
//        if (log.isDebugEnabled()) {
//            log.debug(msg, obj);
//        }
//    }
//
//    /**
//     * statistics active order id
//     *
//     * @param objects   order
//     * @param activeIds id
//     */
//    protected void statisticsCsm(List<? extends Object> objects, List<Long> activeIds) {
//        for (Object o : objects) {
//            if (o == null) {
//                continue;
//            }
//            activeIds.add(orderIdFun.apply(o));
//        }
//    }
//
//    /**
//     * inner thread class
//     */
//    @Deprecated
//    class OmsToSapCallable implements Callable<Boolean> {
//
//        private final String nodeName;
//        private final String tableName;
//        private final Integer size;
//        private final Integer status;
//
//        public OmsToSapCallable(String nodeName, String tableName, Integer size, Integer status) {
//            this.nodeName = nodeName;
//            this.tableName = tableName;
//            this.size = size;
//            this.status = status;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//
//            long t1 = System.currentTimeMillis();
//
//            String threadName = Thread.currentThread().getName();
//            recordLog("{} Step0 Task Start NodeName-{},Size-{},Status-{}", threadName, nodeName, size, status);
//            List<Long> odrIds = oms2SapMapper.selectDynamicTaskOrder(nodeName, tableName, size, status);
//            if (log.isDebugEnabled()) {
//                log.debug("{} sub drds store table {} task, query ids :{}", this.getClass().getName(), tableName, odrIds.toString());
//            }
//            if (odrIds != null) {
//                recordLog("{} Step01 TaskId Search Result-{}", threadName, odrIds.size());
//            } else {
//                recordLog("{} Step01 TaskId odrIds获取为空-{}", threadName, null);
//                return true;
//            }
//            if (unExpect.test(odrIds)) {
//                return true;
//            }
//
//            int count = odrIds.size(), length, time = 0, startIndex;
//            length = count;
//            startIndex = 0;
//            List<Long> subList;
//            while (count > 0) {
//                if (count > eachSize) {
//                    subList = odrIds.subList(startIndex, startIndex + eachSize);
//                    startIndex += eachSize;
//                } else {
//                    subList = odrIds.subList(startIndex, length);
//                }
//                boolean eachTime = batchUpdateAndSendSap(threadName, subList);
//                if (eachTime) {
//                    recordLog("{} Step02 The {} Time BatchDeal Success", threadName, ++time);
//                } else {
//                    log.error("{} Step02 The {} Time BatchDeal Failed", threadName, ++time);
//                }
//                count -= eachSize;
//            }
//
//            long t2 = System.currentTimeMillis();
//            recordLog("{} Task Finished, --useTime : {}", threadName, (t2 - t1));
//            return true;
//        }
//    }
//}
