package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderConfirmService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-08-29 15:02
 * @Description : 退换货单自动确认补偿任务 - 生成入库通知单
 **/
@Slf4j
@Component
public class AutoReturnOrderConfirmTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsReturnOrderConfirmService omsReturnOrderConfirmService;

    @Override
    @XxlJob("AutoReturnOrderConfirmTask")
    public RunTaskResult execute(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("退换货单自动确认任务开始",
                    "AutoReturnOrderConfirmTask"));
        }
        RunTaskResult result = new RunTaskResult();
        ValueHolderV14 v14 = omsReturnOrderConfirmService.autoExecuteConfirm();
        if(v14.isOK()){
            result.setSuccess(true);
            result.setMessage("执行成功！");
        }else {
            result.setSuccess(false);
            result.setMessage("执行失败！");
        }
        return result;
    }
}
