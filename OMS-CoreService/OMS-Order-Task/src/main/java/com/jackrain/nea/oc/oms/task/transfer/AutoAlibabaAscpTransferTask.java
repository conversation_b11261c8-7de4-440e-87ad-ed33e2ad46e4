package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpAlibabaAscpOrder;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.AlibabaAscpTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpAlibabaAscpOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/5 下午3:00
 * @description 猫超直发补偿任务
 **/

@Component
@Slf4j
public class AutoAlibabaAscpTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpAlibabaAscpOrderService alibabaAscpOrderService;

    @Autowired
    private AlibabaAscpTransferOrderProcessImpl alibabaAscpTransferOrderProcess;

    @Override
    @XxlJob("AutoAlibabaAscpTransferTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoAlibabaAscoTransferTask.range", DEFAULT_PAGE_SIZE);

            List<String> orderNoList = ES4IpAlibabaAscpOrder.selectUnTransferredOrderFromEs(0, pageSize);

            if (log.isDebugEnabled()) {
                log.debug("###AutoAlibabaAscpTransferTask###OrderNoList=" + JSONObject.toJSONString(orderNoList));
            }

            List<IpAlibabaAscpOrderRelation> orderRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpAlibabaAscpOrderRelation alibabaAscpOrderRelation = this.alibabaAscpOrderService.selectAlibabaAscpOrder(orderNo);
                if (alibabaAscpOrderRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoAlibabaAscpTransferTask.Order.NotExist!###OrderNo="
                            + orderNo);
                    log.error(errorMessage);
                } else {
                    orderRelationList.add(alibabaAscpOrderRelation);
                }
            }

            threadOrderProcessor.startMultiThreadExecute(this.alibabaAscpTransferOrderProcess, orderRelationList);

            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoAlibabaAscpTransferTask,异常信息:{}", "AutoAlibabaAscpTransferTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}