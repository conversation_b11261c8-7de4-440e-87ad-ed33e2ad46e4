//package com.jackrain.nea.oc.oms.task.splitorder;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Lists;
//import com.google.common.util.concurrent.ThreadFactoryBuilder;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.model.util.AdParamUtil;
//import com.jackrain.nea.oc.oms.config.AutoSplitOrderMqConfig;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSplitTaskMapper;
//import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
//import com.jackrain.nea.oc.oms.model.enums.OperateType;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSplitTask;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.services.OmsOrderService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
//import com.jackrain.nea.r3.mq.exception.SendMqException;
//import com.jackrain.nea.r3.mq.util.R3MqSendHelper;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.Tools;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.util.*;
//import java.util.concurrent.*;
//import java.util.stream.Collectors;
//
///**
// * 缺货拆单定时任务
// * 当前类已废弃，业务功能转到新的类：OrderSplitCompensationByStockTask
// *
// * @author: 江家雷
// * @since: 2020/09/15
// * create at : 2020/09/15 15:05
// */
//@Deprecated
//@Slf4j
//@Component
//public class AutoSplitOrderByStockTask extends BaseR3Task implements IR3Task {
//
//    /**
//     * 默认每次查询页数信息
//     */
//    protected static final int DEFAULT_PAGE_SIZE = 1000;
//    /**
//     * 基本线程池常量定义
//     */
//    int corePoolSize = 16;
//    int maxPoolSize = 20;
//    long keepAliveThreadTime = 60000;
//    String threadPoolName = "R3_AUTO_SPLIT_ORDER_BY_STOCK_TASK_THREAD_POOL_%d";
//    ArrayBlockingQueue blockingQueue = new ArrayBlockingQueue(16);
//    @Autowired
//    private R3MqSendHelper sendHelper;
//    @Autowired
//    private AutoSplitOrderMqConfig autoSplitOrderMqConfig;
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private OcBOrderSplitTaskMapper ocBOrderSplitTaskMapper;
//    @Autowired
//    private BuildSequenceUtil sequenceUtil;
//    @Autowired
//    private OmsOrderService omsOrderService;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        if (log.isDebugEnabled()) {
//            log.debug("Start AutoSplitOrderByStockTask.execute");
//        }
//        RunTaskResult result = new RunTaskResult();
//        ExecutorService executor = new ThreadPoolExecutor(
//                32,
//                32,
//                0,
//                TimeUnit.SECONDS,
//                blockingQueue,
//                new ThreadFactoryBuilder().setNameFormat(threadPoolName).build());
//        try {
//            String tableName = "oc_b_order";
//            if (CollectionUtils.isEmpty(nodes)) {
//                if (log.isDebugEnabled()) {
//                    log.debug("AutoSplitOrderByStockTask.nodes not get！");
//                }
//                result.setSuccess(false);
//                result.setMessage("请检查环境,node获取不到.");
//                return result;
//            }
//            List<Future<Boolean>> results = new ArrayList<>();
//            // 一个node查询一个库，每个线程查询一个库的数据做处理
//            for (String nodeName : nodes) {
//                results.add(executor.submit(new AutoSplitOrderByStockTask.OcBOrderSplitCallable(nodeName,
//            }
//            results.forEach(futureResult -> {
//                if (log.isDebugEnabled()) {
//                    try {
//                        log.debug("AutoSplitOrderByStockTask.execute Result:{}", futureResult.get().toString());
//                    } catch (InterruptedException e) {
//                        log.error("AutoSplitOrderByStockTask.InterruptedException Error:", e);
//                    } catch (ExecutionException e) {
//                        log.error("AutoSplitOrderByStockTask.ExecutionException Error:", e);
//                    }
//                }
//            });
//            if (log.isDebugEnabled()) {
//                log.debug("Finish AutoSplitOrderByStockTask.execute");
//            }
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            log.error("AutoSplitOrderByStockTask.execute error:", ex);
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        } finally {
//            executor.shutdown();
//        }
//        return result;
//    }
//
//
//    private void makeCreateField(OcBOrderSplitTask task, User user) {
//        Date date = new Date();
//        task.setAdClientId((long) user.getClientId());//所属公司
//        task.setAdOrgId((long) user.getOrgId());//所属组织
//        task.setOwnerid(Long.valueOf(user.getId()));//创建人id
//        task.setCreationdate(date);//创建时间
//        task.setOwnername(user.getName());//创建人用户名
//        task.setOwnerename(user.getEname());
//        task.setModifierid(Long.valueOf(user.getId()));//修改人id
//        task.setModifiername(user.getName());//修改人用户名
//        task.setModifierename(user.getEname());
//        task.setModifieddate(date);//修改时间
//        task.setIsactive("Y");//是否启用
//    }
//
//    class OcBOrderSplitCallable implements Callable<Boolean> {
//
//        private final String nodeName;
//        private final String taskTableName;
//
//        public OcBOrderSplitCallable(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() {
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer size = config.getProperty("lts.AutoSplitOrderByStockTask.range", 500);
//            if (log.isDebugEnabled()) {
//                log.debug("AutoSplitOrderByStockTask.Param.NodeName={}", nodeName);
//            }
//            // 支付方式  1正常 2.货到付款（不支持拆单）
//            String payTypes = "1";
//            String platforms = "50"; // 唯品会订单不允许拆单
//            List<OcBOrder> orderList = omsOrderService.selectWaitSplitOrderList(nodeName, taskTableName,
//                    payTypes, platforms, size);
//            sendSplitOrderMq(orderList);
//            return true;
//        }
//
//        /**
//         * 分割订单列表，默认200单，有配置取配置参数
//         *
//         * @param orderList
//         */
//        private void sendSplitOrderMq(List<OcBOrder> orderList) {
//            int pointsDataLimit = Tools.getInt(AdParamUtil.getParam("oms.oc.order.autoSplit.push.num"), 200);
//            if (log.isDebugEnabled()) {
//                log.debug("AutoSplitOrderByStockTask.execute  oms.oc.order.autoSplit.push.num={}", pointsDataLimit);
//            }
//            if (CollectionUtils.isEmpty(orderList)) {
//                return;
//            }
//            List<List<OcBOrder>> allList = Lists.partition(orderList, pointsDataLimit);
//
//            for (List<OcBOrder> splitList : allList) {
//                try {
//                    if (CollectionUtils.isEmpty(splitList)) {
//                        continue;
//                    }
//                    splitList.forEach(x -> x.setSplitStatus(1));
//                    ApplicationContextHandle.getBean(AutoSplitOrderByStockTask.class).batchSendSplitOrderList(splitList);
//                } catch (Exception e) {
//                    log.error("AutoSplitOrderByStockTask.sendSplitOrderMq失败,失败订单={},异常信息={}", splitList, e);
//                }
//            }
//        }
//
//
//
//    }
//    /**
//     * 批量发送ES
//     *
//     * @param orderList
//     * @throws SendMqException
//     */
//    @Transactional(rollbackFor = Exception.class)
//    public void batchSendSplitOrderList(List<OcBOrder> orderList) throws SendMqException {
//        List<Long> idsList = orderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
//        if (log.isDebugEnabled()) {
//            log.debug("AutoSplitOrderByStockTask.batchSendSplitOrderList idsList={},orderList={}",
//                    JSON.toJSONString(idsList), JSON.toJSONString(orderList));
//        }
//        String idsString =
//                orderList.stream().map(it -> String.valueOf(it.getId())).distinct().collect(Collectors.joining(","
//                ));
//        // 将订单拆单状态修改为已处理
//        ocBOrderMapper.updateSplitStatusByIds(1, idsList);
//        List<OcBOrderSplitTask> taskList = new ArrayList<>();
//        for (Long orderId : idsList) {
//            OcBOrderSplitTask task = new OcBOrderSplitTask();
//            task.setId(sequenceUtil.buildSplitOrderTaskId());
//            task.setOcBOrderId(orderId);
//            task.setStatus(1);
//            task.setSplitTimes(0);
//            task.setNextTime(Date.from(LocalDateTime.now().plusMinutes(30).atZone(ZoneId.systemDefault()).toInstant()));
//            makeCreateField(task, SystemUserResource.getRootUser());
//            taskList.add(task);
//        }
//        ocBOrderSplitTaskMapper.batchInsertOrderSplitTask(taskList);
//        this.sendMQ(idsString);
//        //发完一波mq休息1毫秒,减轻mq发送压力
//            /*try {
//                Thread.sleep(1);
//            } catch (Exception e) {
//                log.error("AutoSplitOrderByStockTast.batchSendSplitOrderList sleep异常", e);
//            }*/
//    }
//
//    /**
//     * 发送MQ消息执行审单
//     *
//     * @param idsString 订单信息
//     */
//    private void sendMQ(String idsString) throws SendMqException {
//        if (log.isDebugEnabled()) {
//            log.debug("AutoSplitOrderByStockTask.sendMQ Start orderIds={}", idsString);
//        }
//        Long cutime = System.currentTimeMillis() * 1000; // 微秒
//        Long nanoTime = System.nanoTime(); // 纳秒
//        Long micTime = cutime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
//        String msgKey = "SPLIT_ORDER_TR_BATCH" + "_" + micTime;
//        if (log.isDebugEnabled()) {
//            log.debug("msgKey" + msgKey);
//        }
//        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
//        orderMqInfo.setOperateType(OperateType.SPLIT_ORDER);
//        orderMqInfo.setOrderIds(idsString);
//        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
//        mqInfoList.add(orderMqInfo);
//        Object jsonValue = JSON.toJSONString(mqInfoList);
//        if (log.isDebugEnabled()) {
//            log.debug("AutoSplitOrderByStockTask.sendMQ开始发送MqTopic={},Tag={},message={}",
//                    autoSplitOrderMqConfig.getSendSplitMqTopic(), autoSplitOrderMqConfig.getSendSplitTag(),
//                    jsonValue);
//        }
//        sendHelper.sendMessage(jsonValue, autoSplitOrderMqConfig.getSendSplitMqTopic(),
//                autoSplitOrderMqConfig.getSendSplitTag(),
//                msgKey);
//    }
//}