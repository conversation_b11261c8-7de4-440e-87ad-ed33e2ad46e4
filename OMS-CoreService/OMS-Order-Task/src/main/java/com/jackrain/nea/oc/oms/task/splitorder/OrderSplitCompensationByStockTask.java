///*
//package com.jackrain.nea.oc.oms.task.splitorder;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.model.util.AdParamUtil;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.services.OmsOrderService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.util.Tools;
//import com.jackrain.nea.utility.LogUtil;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.Future;
//import java.util.stream.Collectors;
//
//
//*/
///***
// * 拆单补偿任务从oc_b_order表读
// * @author: wang shuai
// * @since: 2020/12/14
// *//*
//
//@Slf4j
//@Component
//public class OrderSplitCompensationByStockTask extends BaseR3Task implements IR3Task {
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private OmsOrderService omsOrderService;
//    private final static String TABLE_NAME = "oc_b_order";
//    @Autowired
//    private OrderSplitCompensationTask orderSplitCompensationTask;
//    @Autowired
//    private ThreadPoolTaskExecutor orderOutOfStockSplitThreadPoolExecutor;
//
//    @Override
//    @XxlJob("OrderSplitCompensationByStockTask")
//    public RunTaskResult execute(JSONObject params) {
//        RunTaskResult result = new RunTaskResult();
//        try {
//            if (CollectionUtils.isEmpty(nodes)) {
//                result.setSuccess(false);
//                result.setMessage("请检查环境,node获取不到.");
//                return result;
//            }
//            List<Future<Boolean>> results = new ArrayList<>();
//            // 一个node查询一个库，每个线程查询一个库的数据做处理
//            for (String nodeName : nodes) {
//            }
//            results.forEach(futureResult -> {
//                if (log.isDebugEnabled()) {
//                    try {
//                        log.debug(LogUtil.format("OrderSplitCompensationByStockTask------>线程结果:{}",  "OrderSplitCompensationByStockTask"), futureResult.get().toString());
//                    } catch (Exception e) {
//                        log.error(LogUtil.format("OrderSplitCompensationByStockTask,异常：{}", "OrderSplitCompensationByStockTask"), Throwables.getStackTraceAsString(e));
//                    }
//                }
//            });
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            log.error(LogUtil.format("OrderSplitCompensationByStockTask,异常：{}", "OrderSplitCompensationByStockTask"), Throwables.getStackTraceAsString(ex));
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        }
//        return result;
//    }
//
//    class OrderSplitCompensationByStockCallable implements Callable<Boolean> {
//
//        private final String nodeName;
//        private final String taskTableName;
//
//        public OrderSplitCompensationByStockCallable(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() {
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer size = config.getProperty("lts.AutoSplitOrderByStockTask.range", 500);
//            if (log.isDebugEnabled()) {
//                log.debug("OrderSplitCompensationByStockTask.Param.NodeName={}", nodeName);
//            }
//            // 支付方式  1正常 2.货到付款（不支持拆单）
//            String payTypes = "1";
//            String platforms = "50"; // 唯品会订单不允许拆单
//            List<OcBOrder> orderList = omsOrderService.selectWaitSplitOrderList(nodeName, taskTableName, payTypes, platforms, size);
//            sendSplitOrderMq(orderList);
//            return true;
//        }
//
//        */
///**
//         * 分割订单列表，默认200单，有配置取配置参数
//         *
//         * @param orderList
//         *//*
//
//        private void sendSplitOrderMq(List<OcBOrder> orderList) {
//            int pointsDataLimit = Tools.getInt(AdParamUtil.getParam("oms.oc.order.autoSplit.push.num"), 200);
//            if (CollectionUtils.isEmpty(orderList)) {
//                return;
//            }
//            List<List<OcBOrder>> allList = Lists.partition(orderList, pointsDataLimit);
//
//            for (List<OcBOrder> splitList : allList) {
//                try {
//                    if (CollectionUtils.isEmpty(splitList)) {
//                        continue;
//                    }
//                    splitList.forEach(x -> x.setSplitStatus(1));
//                    ApplicationContextHandle.getBean(OrderSplitCompensationByStockTask.class).batchSendSplitOrderList(splitList);
//                } catch (Exception e) {
//                    log.error(LogUtil.format("OrderSplitCompensationByStockTask.sendSplitOrderMq失败,失败订单={},异常信息={}", "OrderSplitCompensationByStockTask"),splitList, Throwables.getStackTraceAsString(e));
//                }
//            }
//        }
//    }
//
//    */
///**
//     * 批量发送ES
//     *
//     * @param orderList
//     *//*
//
//    @Transactional(rollbackFor = Exception.class)
//    public void batchSendSplitOrderList(List<OcBOrder> orderList) {
//        List<Long> idsList = orderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
//        // 将订单拆单状态修改为已处理
//        ocBOrderMapper.updateSplitStatusByIds(1, idsList);
//        // 发货缺货重新占单MQ
//        orderSplitCompensationTask.sendCompensationSplitOrderMQ(idsList, true);
//    }
//}
//
//*/
