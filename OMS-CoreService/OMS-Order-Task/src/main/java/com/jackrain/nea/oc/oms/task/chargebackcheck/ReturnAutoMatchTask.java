package com.jackrain.nea.oc.oms.task.chargebackcheck;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.es.ES4RefundIn;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.services.returnin.OcRefundInService;
import com.jackrain.nea.oc.oms.services.returnin.OcReturnInMatchService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * @author: 周琳胜
 * @since: 2019/4/2
 * create at : 2019/4/2 20:57
 */
@Slf4j
@Component
public class ReturnAutoMatchTask extends BaseR3Task implements IR3Task {


    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OcBRefundInMapper ocBRefundInMapper;

    @Autowired
    private OcRefundInService ocRefundInService;

    @Autowired
    private OcReturnInMatchService ocReturnInMatchService;
    @Autowired
    private ThreadPoolTaskExecutor refundInMatchThreadPoolExecutor;

    @Override
    @XxlJob("ReturnAutoMatchTask")
    public RunTaskResult execute(JSONObject params) {
        log.info(LogUtil.format("ReturnAutoMatchTask.execute start",
                "ReturnAutoMatchTask.execute"));
        RunTaskResult result;
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);

        // 每轮拉取数量
        int size = config.getProperty("lts.AutoMatchRefundInTask.range", 300);

        // 重置数量
        int resetSize = config.getProperty("lts.AutoMatchRefundInTask.reset.size", 0);
        int portionSize = size >> 1;
        if (resetSize > 0) {
            portionSize = resetSize;
            log.debug(LogUtil.format("EachTime.Reset.Size{}"), portionSize);
        }
        List<Long> refundIdList = ES4RefundIn.searchRefundId4MatchRecent3Days(false, size);
        log.info(LogUtil.format("ReturnAutoMatchTask.execute refundIdList:{}",
                "ReturnAutoMatchTask.execute"), JSONObject.toJSONString(refundIdList));
        if (CollectionUtils.isEmpty(refundIdList)) {
            result = new RunTaskResult();
            resetMatchTimes(portionSize);
            result.setSuccess(true);
            result.setMessage("未获取到符合匹配条件数据");
            return result;
        }
        User rootUser = SystemUserResource.getRootUser();

        // 多线程
        boolean isMulti = config.getPropertyBoolean("lts.AutoMatchRefundInTask.switch2MultiThread");
        boolean isSwitchMulti = isMulti && refundIdList.size() > 30;
        if (isSwitchMulti) {
            result = multiThreadWork(refundIdList, size, rootUser);
        } else {
            result = singleThreadWork(refundIdList, size, rootUser);
        }

        if (refundIdList.size() < portionSize) {
            resetMatchTimes(portionSize);
        }
        return result;
    }

    /**
     * 匹配
     *
     * @param refundIdList 退货入库单编号
     * @return 匹配详情
     */
    private RunTaskResult singleThreadWork(List<Long> refundIdList, int size, User rootUser) {

        StringBuilder bufferSuss = new StringBuilder();
        StringBuilder bufferFail = new StringBuilder();
        StringBuilder bufferExp = new StringBuilder();
        Long start = System.currentTimeMillis();
        Integer success = 0;
        Integer fail = 0;
        log.debug(LogUtil.format("ReturnAutoMatchTask.refundIdListSize: {}, Data: {}"), refundIdList.size(), refundIdList);
        ValueHolderV14 vh;
        List<Long> failedList = new ArrayList<>();
        for (Long refundId : refundIdList) {
            String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(refundId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    log.debug(LogUtil.format("ReturnAutoMatchTask.Start.RefundInId.{}", refundId), refundId);
                    vh = ocReturnInMatchService.match(refundId, rootUser);
                    if (vh != null && vh.isOK()) {
                        success++;
                        bufferSuss.append(refundId).append(":完成; ");
                    } else {
                        fail++;
                        String failMsg = vh == null ? "入库服务返回null " : vh.getMessage();
                        bufferFail.append(refundId).append(":失败,原因:").append(failMsg).append(";");
                        failedList.add(refundId);
                    }
                    ocBRefundInMapper.updateSingleMatchTimes(refundId);
                } else {
                    fail++;
                    bufferFail.append(refundId).append(":失败,锁单失败;");
                    log.debug(LogUtil.format("ReturnAutoMatchTask.锁单失败.RefundInId.{}", refundId), refundId);
                }

            } catch (Exception ex) {
                fail++;
                bufferExp.append(refundId).append(":异常;");
                failedList.add(refundId);
                log.error(LogUtil.format("ReturnAutoMatchTask.Execute.Error: {}", refundId), Throwables.getStackTraceAsString(ex));
                ocBRefundInMapper.updateSingleMatchTimes(refundId);
            } finally {
                redisLock.unlock();
            }
        }
        String logMsg = new StringBuilder("本次任务详情.预执行条数: ").append(size).append(", 实际查询条数: ")
                .append(refundIdList.size()).append(". 匹配结束, 成功: ").append(success).append("条, 失败: ")
                .append(fail).append("条").toString();
        RunTaskResult result = new RunTaskResult();
        result.setSuccess(true);
        result.setMessage(logMsg);
        long times = System.currentTimeMillis() - start;
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ReturnAutoMatchTask.消耗时间共计：{}; 成功: {}条, 失败: {}条,-匹配详细信息: 成功={}, 失败={}, 异常={}"),
                    times, success, fail, bufferSuss, bufferFail, bufferExp);
        }
        return result;
    }

    /***
     * 多线程处理任务
     * @param refundIdList 退货入库单ids
     * @param size 预定数量
     * @param rootUser 用户
     * @return RunTaskResult
     */
    private RunTaskResult multiThreadWork(List<Long> refundIdList, int size, User rootUser) {
        RunTaskResult result = new RunTaskResult();
        long start = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder();

        sb.append("本次任务详情.预执行条数: ").append(size).append(", 实际查询条数: ").append(refundIdList.size());
        log.debug(LogUtil.format("refundIdListSize-MT: {}, Data: {}"), refundIdList.size(), refundIdList);

        int matchNum = doMatching(refundIdList, rootUser);
        result.setSuccess(true);
        long times = System.currentTimeMillis() - start;
        sb.append(", 匹配成功条数: ").append(matchNum).append(", 共耗时: ").append(times).append(" ms");
        result.setMessage(sb.toString());
        return result;
    }


    /**
     * 分段匹配
     *
     * @param preList  退货入库单ids
     * @param rootUser 用户
     */
    private int doMatching(List<Long> preList, User rootUser) {

        int success = 0;
        int size = preList.size(), startIndex = 0;
        int length = size, eachSize = 50;
        try {

            List<Long> subList;
            List<Future<Integer>> results = new ArrayList<>();
            while (size > 0) {
                if (size > eachSize) {
                    subList = preList.subList(startIndex, startIndex + eachSize);
                    startIndex += eachSize;
                } else {
                    subList = preList.subList(startIndex, length);
                }
                results.add(refundInMatchThreadPoolExecutor.submit(new RefundInMatchTaskCallable(subList, rootUser)));
                size -= eachSize;
            }

            for (Future<Integer> result : results) {
                try {
                    success += result.get();
                } catch (InterruptedException e) {
                    log.error(LogUtil.format("Thread.InterruptedException：{}"), Throwables.getStackTraceAsString(e));
                } catch (ExecutionException e) {
                    log.error(LogUtil.format("Thread.ExecutionException：{}"), Throwables.getStackTraceAsString(e));
                }
            }

        } catch (Exception e) {
            if (e != null) {
                e.printStackTrace();
            }
            log.error(LogUtil.format("MT-ReturnAutoMatchTask.MultiThread.Matching.匹配发生异常-{}"), Throwables.getStackTraceAsString(e));
        }
        return success;
    }

    /**
     * inner thread class
     */
    class RefundInMatchTaskCallable implements Callable<Integer> {

        private final User rootUser;
        private final List<Long> subList;

        public RefundInMatchTaskCallable(List<Long> subList, User user) {
            this.rootUser = user;
            this.subList = subList;
        }

        @Override
        public Integer call() throws Exception {

            int success = 0;
            if (CollectionUtils.isEmpty(this.subList)) {
                return success;
            }

            log.debug(LogUtil.format("{}.Start."), Thread.currentThread().getName());
            long start = System.currentTimeMillis();
            ValueHolderV14 vh;
            StringBuilder sSb = new StringBuilder();
            StringBuilder fSb = new StringBuilder();
            int fail = 0;
            for (Long refundId : subList) {
                String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(refundId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        log.debug(LogUtil.format("ReturnAutoMatchTask-MT-RefundInId-{}", refundId), refundId);
                        vh = ocReturnInMatchService.match(refundId, rootUser);
                        if (vh != null && vh.isOK()) {
                            success++;
                            sSb.append(refundId).append(":完成; ");
                        } else {
                            fail++;
                            if (vh != null) {
                                fSb.append(refundId).append(":失败,原因:").append(vh.getMessage()).append("; ");
                            } else {
                                fSb.append(refundId).append(":失败,原因: 入库服务返回null; ");
                            }
                            //                    failedList.add(refundId);
                        }
                        ocBRefundInMapper.updateSingleMatchTimes(refundId);
                    } else {
                        fail++;
                        fSb.append(refundId).append(":失败,锁单失败;");
                        log.debug(LogUtil.format("ReturnAutoMatchTask 锁单失败-MT, RefundInId-{}", refundId), refundId);
                    }

                } catch (Exception ex) {
                    fail++;
                    fSb.append(refundId).append(":异常;");
                    log.error(LogUtil.format("ReturnAutoMatchTask.Execute Error-MT,{}-Exp-{}"), refundId, Throwables.getStackTraceAsString(ex));
                    ocBRefundInMapper.updateSingleMatchTimes(refundId);
                } finally {
                    redisLock.unlock();
                }
            }
            //        modifyMatchFailedTimes(failedList);
            long times = System.currentTimeMillis() - start;
            log.debug(LogUtil.format("ReturnAutoMatchTask.消耗时间共计-MT：{}ms; 成功: {}条, 失败: {}条. 匹配详细信息.成功:{}, 失败:{}"),
                    times, success, fail, sSb, fSb);
            return success;
        }
    }

    /**
     * 更新匹配失败次数
     * 当前待匹配数量 <  单次匹配量 ?  半数重置
     *
     * @param failedList   失败退货入库单ids
     * @param refundIdList 退货入库单ids
     * @param portionSize  预设置半数
     */
    private void modifyMatchFailedTimes(List<Long> failedList, List<Long> refundIdList, int portionSize) {
        modifyMatchFailedTimes(failedList);
        if (refundIdList.size() < portionSize) {
            resetMatchTimes(portionSize);
        }
    }

    /**
     * 更新匹配失败次数
     *
     * @param failedList
     */
    private void modifyMatchFailedTimes(List<Long> failedList) {
        if (CollectionUtils.isNotEmpty(failedList)) {
            ocRefundInService.reCircleUpdate4Match(failedList, false);
        }

    }

    /**
     * 重置匹配次数
     *
     * @param portionSize 设置半数
     */
    private void resetMatchTimes(int portionSize) {
        List<Long> reCircleList = ES4RefundIn.searchRefundId4MatchRecent3Days(true, portionSize);
        ocRefundInService.reCircleUpdate4Match(reCircleList, true);
    }


}
