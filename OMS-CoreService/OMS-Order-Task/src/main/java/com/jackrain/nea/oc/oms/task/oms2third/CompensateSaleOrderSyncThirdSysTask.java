/*
package com.jackrain.nea.oc.oms.task.oms2third;

import org.springframework.stereotype.Component;

*/
/**
 * @Desc : 销售补偿任务
 * <AUTHOR> xiWen
 * @Date : 2020/8/24
 *//*

@Component
public class CompensateSaleOrderSyncThirdSysTask extends AbsOmsSyncThirdSysCompensateTask {

    @Override
    protected String getTaskTableName() {
        return "oc_b_task_order_sap";
    }

    @Override
    protected String getTaskStatusCol() {
        return "NEXT_TAO_STATUS";
    }

    @Override
    protected int getTaskStatusVal() {
        return 0;
    }

    @Override
    protected String getTaskTypeCol() {
        return "IS_NEXT_TAO";
    }

    @Override
    protected int getTaskTypeVal() {
        return 1;
    }


    @Override
    protected String getOrigTableName() {
        return "OC_B_ORDER";
    }

    @Override
    protected String getOrigStatusCol() {
        return "SEND_NEXT_TAO_STATUS";
    }

    @Override
    protected String getOrigSendTimesCol() {
        return "SEND_NEXT_TAO_TIMES";
    }

    @Override
    protected String getOrigEsIndex() {
        return "oc_b_order";
    }

    @Override
    protected String getOrigEsType() {
        return "oc_b_order";
    }
}
*/
