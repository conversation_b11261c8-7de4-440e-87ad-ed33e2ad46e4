package com.jackrain.nea.oc.oms.task.st;

import com.google.common.base.Throwables;
import com.jackrain.nea.st.service.StCHoldOrderFinishService;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * HLOD单策略自动结案
 *
 * <AUTHOR>
 * @since 2025-07-03 15:27
 */
@Slf4j
@Component
public class StCHoldOrderAutoFinishTask {

    @Autowired
    private StCHoldOrderFinishService stCHoldOrderFinishService;

    @XxlJob("StCHoldOrderAutoFinishTask")
    public RunTaskResult execute() {
        log.debug(LogUtil.format("StCHoldOrderAutoFinishTask.start"));
        Long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();

        try {
            int count = stCHoldOrderFinishService.autoFinishByBusinessParams();
            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms,结案数量:" + count);
        } catch (Exception e) {
            log.error(LogUtil.format("StCHoldOrderAutoFinishTask.Error：{}"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }

        return result;
    }

}
