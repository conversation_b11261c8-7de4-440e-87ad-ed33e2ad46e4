package com.jackrain.nea.oc.oms.task.remark;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.services.OcBRemarkGiftTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 卖家添加备注处理任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OcBRemarkGiftTask extends BaseR3Task implements IR3Task {

    @Resource
    private OcBRemarkGiftTaskService remarkGiftTaskService;

    @Override
    @XxlJob("OcBRemarkGiftTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            remarkGiftTaskService.processRemarkGiftTask();
            result.setSuccess(true);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}