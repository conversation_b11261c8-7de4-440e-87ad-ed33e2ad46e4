package com.jackrain.nea.oc.oms.task.msg;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.table.OcBMsgSendRecord;
import com.jackrain.nea.oc.oms.services.task.OmsMsgTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.oc.oms.util.SendMsgUtil;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;


/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/8/29
 **/
@Slf4j
@Component
public class AutoSendMsgOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private SendMsgUtil sendMsgUtil;

    @Autowired
    private OmsMsgTaskService taskService;
    @Autowired
    private ThreadPoolTaskExecutor sendMsgTaskThreadPoolExecutor;

    @Override
    @XxlJob("AutoSendMsgOrderTask")
    public RunTaskResult execute(JSONObject params) {

        RunTaskResult result = new RunTaskResult();
        try {

            long start = System.currentTimeMillis();

            final String taskTableName = "oc_b_msg_send_record";
            Map<String, String> topMap = null;
            Set<String> nodes = topMap.keySet();
            if (CollectionUtils.isEmpty(nodes)) {
                log.debug("短信任务表.nodes not get！");
                result.setSuccess(false);
                result.setMessage("请检查环境，node获取不到！！");
                return result;
            }
            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();

            for (String nodeName : nodes) {
                results.add(sendMsgTaskThreadPoolExecutor.submit(new CallableTobeMsgTaskWithResult(nodeName, topMap.get(nodeName))));
            }

            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    log.debug("AutoSendMsgOrderTask------>线程结果:" + futureResult.get().toString());
                } catch (InterruptedException e) {
                    log.error("AutoSendMsgOrderTask多线程获取InterruptedException异常：", e);
                } catch (ExecutionException e) {
                    log.error("AutoSendMsgOrderTask多线程获取ExecutionException异常：", e);
                }
            }

            result.setMessage("AutoSendMsgOrderTask 审核服务定时任务完成 useTime :" + (System.currentTimeMillis() - start));
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(this.getClass().getName() + " 自动审核异常", ex);
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

    /**
     * 开启线程类
     */
    class CallableTobeMsgTaskWithResult implements Callable<Boolean> {
        private final String nodeName;
        private final String taskTableName;

        public CallableTobeMsgTaskWithResult(String nodeName, String name) {
            this.nodeName = nodeName;
            this.taskTableName = name;
        }

        @Override
        public Boolean call() throws Exception {

            Integer pullNum = 500;
            List<OcBMsgSendRecord> list = taskService.selectTask(nodeName, pullNum, taskTableName);
            if (CollectionUtils.isNotEmpty(list)) {
                List<Long> idList = list.stream().map(OcBMsgSendRecord::getId).collect(Collectors.toList());
                taskService.updateAuditTaskModitime(idList);
                list.forEach(ocBMsgTask -> {
                    if (sendMsgUtil.sendMessage(ocBMsgTask)) {
                        taskService.updateAuditTaskById(ocBMsgTask.getId());
                    }
                });
            }
            return true;
        }
    }

}