package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.ip.model.vips.VipCreateShipResetWorkflowRequest;
import com.jackrain.nea.ip.model.vips.VipCreateShipResetWorkflowResult;
import com.jackrain.nea.oc.oms.IpBJitxResetShipWorkflowMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJitxResetShipWorkflow;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowCreatedStateEnum;
import com.jackrain.nea.oc.oms.services.IpBJitxResetShipWorkflowService;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * description：JixX发货重置创建
 *
 * <AUTHOR>
 * @date 2021/10/18
 */
@Slf4j
@Component
public class AutoJitxCreateResetShipWorkflowTask implements IR3Task {

    @Autowired
    protected IpRpcService ipRpcService;

    @Autowired
    private IpBJitxResetShipWorkflowMapper resetShipWorkflowMapper;

    @Autowired
    private IpBJitxResetShipWorkflowService ipBJitxResetShipWorkflowService;


    @Value("${oms.oc.order.jitx.resetShip.pull.num:1000}")
    private Integer pullNum;

    /**
     * 最大失败次数
     */
    @Value("${oms.oc.order.jitx.resetShip.max.failNumber:5}")
    private Integer failNumber;

    @Override
    @XxlJob("AutoJitxCreateResetShipWorkflowTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        List<Long> idList = searchFromEs(pullNum);
        if (CollectionUtils.isEmpty(idList)) {
            result.setMessage("未从ES查询到数据");
            result.setSuccess(true);
            return result;
        }
        List<IpBJitxResetShipWorkflow> dbDataList = resetShipWorkflowMapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(dbDataList)) {
            result.setMessage("未从数据库查询到数据");
            result.setSuccess(true);
            return result;
        }
        List<IpBJitxResetShipWorkflow> filteredList = new ArrayList<>(dbDataList.size());
        for (IpBJitxResetShipWorkflow dbData : dbDataList) {
            //失败次数超过限制
            if (dbData.getFailNumber() != null && dbData.getFailNumber() > failNumber) {
                continue;
            }
            //不是未创建或者创建失败
            if (!(VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode().equals(dbData.getCreatedStatus()) || VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode().equals(dbData.getCreatedStatus()))) {
                continue;
            }
            filteredList.add(dbData);
        }
        User operateUser = SystemUserResource.getRootUser();
        if (CollectionUtils.isEmpty(filteredList)) {
            result.setMessage("ES数据查库后再次筛选无符合条件数据");
            result.setSuccess(true);
            return result;
        }
        if (log.isDebugEnabled()) {
            log.debug("JITX订单创建改仓工单任务，filtedList：{}", filteredList.size());
        }
        //分批次处理
        List<List<IpBJitxResetShipWorkflow>> listList = Lists.partition(filteredList, 20);
        for(List<IpBJitxResetShipWorkflow> workflowList:listList){
            this.create(workflowList, operateUser);
        }
        result.setMessage(String.format("执行数据条数:%d", filteredList.size()));
        result.setSuccess(true);
        return result;
    }

    public static List<Long> searchFromEs(Integer range) {
        log.info("jitx改仓工单创建查询ES数据开始 range:{}", range);

        JSONObject whereKeys = new JSONObject();
        JSONObject orderKey = new JSONObject();
        //修改时间升序
        orderKey.put("asc", false);
        orderKey.put("name", "MODIFIEDDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);

        JSONArray result = new JSONArray();
        result.add(VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode());
        result.add(VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode());
        whereKeys.put("CREATED_STATUS", result);

        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次
        filterKeys.put("FAIL_NUMBER", "~" + 5);

        String[] field = {"ID"};
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JITX_RESET_SHIP_WORKFLOW,
                OcElasticSearchIndexResources.IP_B_JITX_RESET_SHIP_WORKFLOW, whereKeys, filterKeys, order, range, 0, field);
        List<Long> orderList = new ArrayList<>();
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
        }
        return orderList;
    }

    private void create(List<IpBJitxResetShipWorkflow> shipWorkflowList, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("JITX订单创建发货重置工单任务，AutoJitxCreateResetShipWorkflowTask size：{}", shipWorkflowList.size());
        }
        if (CollectionUtils.isEmpty(shipWorkflowList)) {
            return;
        }
        VipCreateShipResetWorkflowRequest request = new VipCreateShipResetWorkflowRequest();
        List<VipCreateShipResetWorkflowRequest.VipShipResetWorkflowItem> itemCreateRequestList = new ArrayList<>(shipWorkflowList.size());
        for (IpBJitxResetShipWorkflow workflow : shipWorkflowList) {
            VipCreateShipResetWorkflowRequest.VipShipResetWorkflowItem itemCreateRequest = new VipCreateShipResetWorkflowRequest.VipShipResetWorkflowItem();
            itemCreateRequest.setOrder_sn(workflow.getOrderSn());
            itemCreateRequest.setRequest_id(workflow.getRequestId());
            itemCreateRequest.setReason_code(workflow.getReasonCode());
            itemCreateRequest.setReason_remark(workflow.getReasonRemark());
            itemCreateRequestList.add(itemCreateRequest);
        }
        request.setWorkflows(itemCreateRequestList);
        request.setVendor_id(Integer.valueOf(shipWorkflowList.get(0).getVendorId()));
        request.setSeller_nick(shipWorkflowList.get(0).getSellerNick());
        ValueHolderV14<VipCreateShipResetWorkflowResult> v14 = ipRpcService.createShipResetWorkflowVop(request);
        ipBJitxResetShipWorkflowService.transferResult(shipWorkflowList, v14);
    }
}
