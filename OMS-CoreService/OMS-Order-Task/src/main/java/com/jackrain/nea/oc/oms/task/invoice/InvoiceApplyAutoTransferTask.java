package com.jackrain.nea.oc.oms.task.invoice;

import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jackrain.nea.ac.service.InvoiceApplyService;
import com.jackrain.nea.ac.service.OrderInvoiceChangeTaskService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import com.jackrain.nea.oc.oms.model.table.StCInvoiceStrategy;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: haiyang
 * @Date: 2025-02-26 09:24
 * @Desc: 发票申请单自动转换定时任务
 */
@Component
@Slf4j
public class InvoiceApplyAutoTransferTask {

    @Autowired
    private InvoiceApplyService invoiceApplyService;
    @Autowired
    private OrderInvoiceChangeTaskService orderInvoiceChangeTaskService;
    @Autowired
    private AcFInvoiceApplyMapper acFInvoiceApplyMapper;


    @XxlJob(value = "InvoiceApplyAutoTransfer")
    public void autoTransfer() {
        log.info("发票申请单自动转换定时任务开始");
        User sysUser = SystemUserResource.getRootUser();
        int successNum = 0;
        long totalNum = 0;
        long totalPages = 1;
        int currentPage = 1;
        while(true) {
            Page<AcFInvoiceApply> page = new Page<>(currentPage, 100);
            IPage<AcFInvoiceApply> invoiceApplyIPage = invoiceApplyService.pageNotAndFailTransferRecord(page);
            if (currentPage == 1) {
                totalPages = invoiceApplyIPage.getPages();
                totalNum = invoiceApplyIPage.getTotal();
            }
            log.info("自动转换发票申请当前页：{}，总页数：{}，总记录数：{}", currentPage, totalPages, totalNum);
            if (CollectionUtils.isEmpty(invoiceApplyIPage.getRecords())) break;
            // 处理逻辑
            for (AcFInvoiceApply acFInvoiceApply : invoiceApplyIPage.getRecords()) {
                try {
                    if (InvoiceConst.TransStatus.TRANS_SUCCESS.equals(acFInvoiceApply.getTransStatus())) {
                        throw new NDSException("状态已转换不允许单据转换！");
                    }
                    ValueHolderV14<StCInvoiceStrategy> stCInvoiceStrategyValueHolderV14 =
                            orderInvoiceChangeTaskService.changeApplyInvoice(null, null, null, null, null, acFInvoiceApply, sysUser);
                    if (!stCInvoiceStrategyValueHolderV14.isOK()) {
                        throw new NDSException(stCInvoiceStrategyValueHolderV14.getMessage());
                    }
                    successNum++;
                    updateInvoiceApplyStatus(acFInvoiceApply, InvoiceConst.TransStatus.TRANS_SUCCESS, null, sysUser);
                } catch (NDSException e) {
                    // 已知业务异常，记录警告日志
                    log.warn("自动转换业务异常，单号：{}", acFInvoiceApply.getTid(), e);
                    if (!InvoiceConst.TransStatus.TRANS_SUCCESS.equals(acFInvoiceApply.getTransStatus())) {
                        updateInvoiceApplyStatus(acFInvoiceApply, InvoiceConst.TransStatus.TRANS_FAIL, e.getMessage(), sysUser);
                    }
                } catch (Exception e) {
                    // 未知异常，记录错误日志
                    log.error("自动转换系统异常，单号：{}", acFInvoiceApply.getTid(), e);
                    if (!InvoiceConst.TransStatus.TRANS_SUCCESS.equals(acFInvoiceApply.getTransStatus())) {
                        updateInvoiceApplyStatus(acFInvoiceApply, InvoiceConst.TransStatus.TRANS_FAIL, e.getMessage(), sysUser);
                    }
                }
            }
            if (currentPage >= invoiceApplyIPage.getPages()) break;
            currentPage++;
        }
        if (successNum == 0) {
            log.info("自动转换失败！失败条数：{}", totalNum);
        } else {
            if (successNum < totalNum) {
                log.info("自动转换部分失败！成功条数-失败条数：{}-{}", successNum, (totalNum - successNum));
            } else {
                log.info("自动转换成功！成功条数：{}", successNum);
            }
        }
    }

    private void updateInvoiceApplyStatus(AcFInvoiceApply apply, String status, String failReason, User sysUser) {
        AcFInvoiceApply po = new AcFInvoiceApply();
        po.setId(apply.getId());
        po.setTransStatus(status);
        if (InvoiceConst.TransStatus.TRANS_FAIL.equals(status)) {
            po.setFailCount((apply.getFailCount() == null ? 0 : apply.getFailCount()) + 1);
            po.setFailReason(StringUtils.truncate(failReason, 450)); // 使用工具类截断
        }
        BaseModelUtil.makeBaseModifyField(po, sysUser);
        acFInvoiceApplyMapper.updateById(po);
    }
}
