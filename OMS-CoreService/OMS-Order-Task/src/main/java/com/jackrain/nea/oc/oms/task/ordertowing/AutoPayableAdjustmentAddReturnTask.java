//package com.jackrain.nea.oc.oms.task.ordertowing;
//
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
//import com.jackrain.nea.ac.service.PayableAdjustmentSaveService;
//import com.jackrain.nea.ip.model.wing.WingReturnResult;
//import com.jackrain.nea.oc.oms.es.ES4PushToWingOrderQueryService;
//import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
//import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
//import com.jackrain.nea.oc.oms.model.enums.ToDRPStatusEnum;
//import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.rpc.IpRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.stream.Collectors;
//
//
///***
// * 丢件单传wing生成退换货单服务
// */
//@Slf4j
//@Component
//public class AutoPayableAdjustmentAddReturnTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private AcFPayableAdjustmentMapper payableAdjustmentMapper;
//    @Autowired
//    private AcFPayableAdjustmentItemMapper adjustmentItemMapper;
//
//    @Resource
//    private PayableAdjustmentSaveService payableAdjustmentSaveService;
//
//    @Autowired
//    private IpRpcService ipRpcService;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        log.debug(LogUtil.format("进入丢件单传wing生成退换货单服务",  "丢件单传wing生成退换货单服务"));
//        RunTaskResult result = new RunTaskResult();
//        long starTime = System.currentTimeMillis();
//        try {
//            List<Long> idList = ES4PushToWingOrderQueryService.findAdjustPaymentAddReturn(0, 200);
//            if (CollectionUtils.isEmpty(idList)) {
//                result.setSuccess(true);
//                result.setMessage("未从ES查询到符合条件的数据");
//                return result;
//            }
//            List<AcFPayableAdjustmentDO> orderList = payableAdjustmentMapper.selectBatchIds(idList);
//            if (CollectionUtils.isEmpty(orderList)) {
//                result.setSuccess(true);
//                result.setMessage("未从数据库查询到数据");
//                return result;
//            }
//            List<Long> orderIds = orderList.stream().map(AcFPayableAdjustmentDO::getId).collect(Collectors.toList());
//            ValueHolderV14<WingReturnResult> valueHolderV14 = null;
//            try {
//                valueHolderV14 = ipRpcService.pushPayAbleAdjustmentToWing(orderList);
//            } catch (Exception e) {
//                log.error(LogUtil.format("丢件丢件单调用wing接口生成退单异常：{}",  "丢件单传wing生成退换货单服务"), Throwables.getStackTraceAsString(e));
//                AcFPayableAdjustmentDO failUpdate = new AcFPayableAdjustmentDO();
//                failUpdate.setToDrpStatus(ToDRPStatusEnum.NOT.getCode());
////                failUpdate.setModifieddate(new Date());
//                payableAdjustmentMapper.update(failUpdate, new LambdaUpdateWrapper<AcFPayableAdjustmentDO>().in(AcFPayableAdjustmentDO::getId, orderIds));
//            }
//            int failNum = 0;
//            if (valueHolderV14 != null) {
//                WingReturnResult data = valueHolderV14.getData();
//                if (data.getStatus() == 1) {
//                    List<WingReturnResult.FailMsg> faileds = data.getFaileds();
//                    if (CollectionUtils.isEmpty(faileds)) {
//                        failNum = faileds.size();
//                        LambdaQueryWrapper<AcFPayableAdjustmentDO> queryWrapper = new LambdaQueryWrapper();
//                        queryWrapper.in(AcFPayableAdjustmentDO::getId, orderIds);
//                        AcFPayableAdjustmentDO payableAdjustment = new AcFPayableAdjustmentDO();
//                        payableAdjustment.setModifieddate(new Date());
//                        payableAdjustment.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
//                        payableAdjustment.setToDrpFailedReason("");
//                        payableAdjustment.setToDrpCount(0);
//                        payableAdjustmentMapper.update(payableAdjustment, queryWrapper);
//                        for (Long id : orderIds) {
//                            //插入日志
//                            payableAdjustmentSaveService.insertLogFun(SystemUserResource.getRootUser(), id,
//                                    OperatorLogTypeEnum.PUSH_TO_WING.getVal(), "丢件单传DRP生成退货单成功", new Date());
//                        }
//                    } else {
//                        List<Long> failOrderIdList = new ArrayList<>(faileds.size());
//                        for (WingReturnResult.FailMsg d : faileds) {
//                            //获取丢件单 billNo
//                            String returnOrderNo = d.getId();
//                            AcFPayableAdjustmentDO payableAdjustment = new AcFPayableAdjustmentDO();
//                            if (StringUtils.isNotEmpty(returnOrderNo)) {
//                                String message = SplitMessageUtil.splitErrMsgBySize(d.getErrMsg(), SplitMessageUtil.SIZE_200);
//                                LambdaQueryWrapper<AcFPayableAdjustmentDO> queryWrapper = new LambdaQueryWrapper();
//                                queryWrapper.eq(AcFPayableAdjustmentDO::getBillNo, returnOrderNo);
//                                List<AcFPayableAdjustmentDO> payableAdjustments = payableAdjustmentMapper.selectList(queryWrapper);
//                                if (CollectionUtils.isEmpty(payableAdjustments)) {
//                                    log.error(LogUtil.format("未查询到对应数据",  returnOrderNo));
//                                    continue;
//                                }
//                                AcFPayableAdjustmentDO payableAdjustmentOrg = payableAdjustments.get(0);
//                                failOrderIdList.add(payableAdjustmentOrg.getId());
//                                Integer toDrpCount = payableAdjustmentOrg.getToDrpCount();
//                                if (null == toDrpCount) {
//                                    toDrpCount = 1;
//                                } else {
//                                    toDrpCount = toDrpCount + 1;
//                                }
//                                try {
//                                    payableAdjustment.setId(payableAdjustmentOrg.getId());
//                                    payableAdjustment.setModifieddate(new Date());
//                                    payableAdjustment.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                                    payableAdjustment.setToDrpCount(toDrpCount);
//                                    payableAdjustment.setToDrpFailedReason(message);
//                                    payableAdjustmentMapper.updateById(payableAdjustment);
//                                    //插入失败日志
//                                    payableAdjustmentSaveService.insertLogFun(SystemUserResource.getRootUser(), payableAdjustmentOrg.getId(),
//                                            OperatorLogTypeEnum.PUSH_TO_WING.getVal(), "丢件单传DRP生成退货单失败", new Date());
//                                } catch (Exception e) {
//                                    log.error(LogUtil.format("丢件单传DRP失败,失败原因={}",  returnOrderNo), Throwables.getStackTraceAsString(e));
//                                }
//                            } else {
//                                log.error(LogUtil.format("payableAdjustmentOrderNotFoundByBillNo:{}",  returnOrderNo));
//                            }
//                        }
//                        //除去失败的 其他都为成功的
//                        if (orderIds.size() > failOrderIdList.size()) {
//                            orderIds.removeAll(failOrderIdList);
//                            LambdaQueryWrapper<AcFPayableAdjustmentDO> queryWrapper = new LambdaQueryWrapper();
//                            queryWrapper.in(AcFPayableAdjustmentDO::getId, orderIds);
//                            AcFPayableAdjustmentDO update = new AcFPayableAdjustmentDO();
//                            update.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
//                            update.setToDrpCount(0);
//                            update.setToDrpFailedReason("");
//                            payableAdjustmentMapper.update(update, queryWrapper);
//                        }
//                    }
//                }
//
//            }
//            result.setSuccess(true);
//            long endTime = System.currentTimeMillis();
//            long Time = endTime - starTime;
//            String msg = "执行成功条数：" + (orderList.size() - failNum) + ",失败条数：" + failNum+"耗时:"+Time;
//            result.setMessage(msg);
//        } catch (Exception e) {
//            log.error(" AutoPayableAdjustmentAddReturnTask.Execute Error:{}", Throwables.getStackTraceAsString(e));
//            result.setSuccess(false);
//            result.setMessage(e.getMessage());
//        }
//        return result;
//    }
//}
//
