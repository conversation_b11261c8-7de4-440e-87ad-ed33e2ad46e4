package com.jackrain.nea.oc.oms.task.hold;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.task.st.StCHoldOrderAutoFinishTask;
import com.jackrain.nea.st.service.StCHoldOrderFinishService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Description： HOLD单策略自动结案
 * Author: 江家雷
 * Date: Created in 2020/7/8 14:50
 * Modified By:
 * 20250703 生产未配置，产品有新的需求，这个应该先不用了，后面看新的任务{@link StCHoldOrderAutoFinishTask}
 */
@Slf4j
@Component
@Deprecated
public class HoldOrderStrategyAutoFinishTask implements IR3Task {

    @Autowired
    private StCHoldOrderFinishService stCHoldOrderFinishService;

    @Override
    @XxlJob("HoldOrderStrategyAutoFinishTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug(LogUtil.format("HoldOrderStrategyAutoFinishTask.start"));
        Long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();

        try {
            stCHoldOrderFinishService.holdOrderStrategyAutoFinish(1000);
            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms");
        } catch (Exception e) {
            log.error(LogUtil.format("HoldOrderStrategyAutoFinishTask.Error：{}"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }

        return result;
    }

}
