package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.es.ES4IpJingDongRefund;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.JingdongTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJingdongRefundService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Descroption 京东退货转换
 * <AUTHOR>
 * @Date 2019/4/24 19:04
 */

@Slf4j
@Component
public class AutoJdRefundOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpJingdongRefundService ipJingDongRefundService;
    @Autowired
    private JingdongTransferRefundProcessImpl jingdongTransferRefundProcess;

    @Override
    @XxlJob("AutoJdRefundOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer size = config.getProperty("lts.AutoJdRefundOrderTask.range", 1000);
            //1.遍历满足条件的订单数据
            List<String> list = ES4IpJingDongRefund.selectJingdongRefundKey(0, size);
            List<IpJingdongRefundRelation> relations = new ArrayList<>();
            for (String refundId : list) {
                IpJingdongRefundRelation ipJingdongRefundRelation = ipJingDongRefundService.getJingdongRefundRelation(refundId);
                if (ipJingdongRefundRelation != null) {
                    relations.add(ipJingdongRefundRelation);
                } else {
                    String errorMessage = Resources.getMessage("AutoJdRefundOrderTask Order " +
                            "NotExist!Id=" + refundId);
                    if (log.isDebugEnabled()) {
                        log.error(errorMessage);
                    }
                }

            }
            //2.多线程处理订单数据
            threadOrderProcessor.startMultiThreadExecute(this.jingdongTransferRefundProcess, relations);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(LogUtil.format("AutoJdRefundOrderTask.Execute.Error,异常：{}", "京东退货转换"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
