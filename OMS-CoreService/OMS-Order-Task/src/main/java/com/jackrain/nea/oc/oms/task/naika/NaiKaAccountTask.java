package com.jackrain.nea.oc.oms.task.naika;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.request.naika.NaiKaChangeAmountRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.enums.naika.AccountToNaiKaEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName NaiKaAccountTask
 * @Description 奶卡对账
 * <AUTHOR>
 * @Date 2022/9/7 20:46
 * @Version 1.0
 */
@Component
@Slf4j
public class NaiKaAccountTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private ThreadPoolTaskExecutor naikaAccountThreadPoolExecutor;

    @Override
    @XxlJob("NaiKaAccountTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            long start = System.currentTimeMillis();
            final String taskTableName = "oc_b_order_naika";
            List<Future<Boolean>> results = new ArrayList<>();
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.NaiKaAccountTask.range", 2400);
            List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectAccountToNaiKa(pageSize, taskTableName, 6);
            if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
                result.setSuccess(true);
                result.setMessage("success");
                return result;
            }
            List<List<OcBOrderNaiKa>> lists = ListSplitUtil.averageAssign(ocBOrderNaiKaList, 24);
            for (List<OcBOrderNaiKa> data : lists) {
                results.add(naikaAccountThreadPoolExecutor.submit(new NaiKaAccountTask.CallableNaiKaAccountTaskWithResult(data)));
            }
            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("NaiKaAccountTask------>线程结果:{}"), futureResult.get().toString());
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("NaiKaAccountTask多线程获取InterruptedException异常：{}", "NaiKaAccountTask"), Throwables.getStackTraceAsString(e));
                }
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("NaiKaAccountTask 奶卡对账 useTime : {}"), (System.currentTimeMillis() - start));
            }
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(LogUtil.format("奶卡对账任务执行失败,异常！{}", "NaiKaAccountTask"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Data
    public class CallableNaiKaAccountTaskWithResult implements Callable<Boolean> {

        private List<OcBOrderNaiKa> data;

        public CallableNaiKaAccountTaskWithResult(List<OcBOrderNaiKa> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            for (OcBOrderNaiKa ocBOrderNaiKa : data) {
                OcBOrderNaiKa updateOcBOrderNaiKa = new OcBOrderNaiKa();
                String errorMsg = "";
                //加锁
                String lockRedisKey = BllRedisKeyResources.buildLockCardCodeKey(ocBOrderNaiKa.getCardCode());
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        updateOcBOrderNaiKa.setOcBOrderId(ocBOrderNaiKa.getOcBOrderId());
                        updateOcBOrderNaiKa.setId(ocBOrderNaiKa.getId());
                        updateOcBOrderNaiKa.setModifieddate(new Date());

                        OcBOrder naiKaOrder = ocBOrderMapper.get4NaiKaOrder(ocBOrderNaiKa.getOcBOrderId());
                        Integer platFormId = naiKaOrder.getPlatform();
                        CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(platFormId));
                        // 取出金额 准备同步给奶卡系统
                        NaiKaChangeAmountRequest request = new NaiKaChangeAmountRequest();
                        request.setCardNumber(ocBOrderNaiKa.getCardCode());
                        request.setPlatformCode(cpCPlatform.getEcode());
                        request.setReconciliationAmount(ocBOrderNaiKa.getWipeAmt());
                        if (ocBOrderNaiKa.getWipeAmt().compareTo(BigDecimal.ZERO) < 0) {
                            // 打印日志 传奶卡系统金额为负 转换成0
                            log.info(LogUtil.format(" execute to naika account:{}, msg:{}", "同步对账到奶卡系统"), ocBOrderNaiKa.getCardCode(), "同步金额不能为负数，已转换成0");
                            request.setReconciliationAmount(BigDecimal.ZERO);
                        }
                        request.setShopCode(naiKaOrder.getCpCShopEcode());
                        request.setReconciliationTime(ocBOrderNaiKa.getUpToBillDate() == null ? new Date() : ocBOrderNaiKa.getUpToBillDate());

                        ValueHolderV14 valueHolderV14 = naiKaOrderCmd.changeAmount(request);
                        if (valueHolderV14.isOK()) {
                            // 更新奶卡状态
                            updateOcBOrderNaiKa.setAccountToNaika(AccountToNaiKaEnum.SUCCESS.getStatus());
                        } else {
                            log.error(LogUtil.format("error to execute to naika account:{}, errorMsg:{}", "同步对账到奶卡系统失败"), ocBOrderNaiKa.getCardCode(), errorMsg);
                            errorMsg = valueHolderV14.getMessage();
                            updateOcBOrderNaiKa.setAccountToNaika(AccountToNaiKaEnum.FAIL.getStatus());
                            updateOcBOrderNaiKa.setAccountToNaikaTimes(ocBOrderNaiKa.getAccountToNaikaTimes() + 1);
                        }
                    } else {
                        updateOcBOrderNaiKa.setAccountToNaika(AccountToNaiKaEnum.FAIL.getStatus());
                        updateOcBOrderNaiKa.setAccountToNaikaTimes(ocBOrderNaiKa.getAccountToNaikaTimes() + 1);
                        errorMsg = "当前有其他任务在操作，稍后会自动重试同步";
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("fail to execute to naika account:{}, errorMsg:{}", "同步对账到奶卡系统失败"), ocBOrderNaiKa.getCardCode(), e.getMessage());
                    errorMsg = e.getMessage();
                    updateOcBOrderNaiKa.setAccountToNaika(AccountToNaiKaEnum.FAIL.getStatus());
                    updateOcBOrderNaiKa.setAccountToNaikaTimes(ocBOrderNaiKa.getAccountToNaikaTimes() + 1);
                } finally {
                    if (ObjectUtil.isNotEmpty(errorMsg) && errorMsg.length() > 255) {
                        errorMsg = errorMsg.substring(0, 254);
                    }
                    updateOcBOrderNaiKa.setAccountErrorMsg(errorMsg);
                    ocBOrderNaiKaMapper.updateById(updateOcBOrderNaiKa);
                    redisLock.unlock();
                }
            }
            return true;
        }
    }
}
