//package com.jackrain.nea.oc.oms.task.splitorder;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.model.table.task.OcBOrderJingdongSplitTask;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.services.OmsOrderJdSplitService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.Future;
//
///**
// *
// */
//@Slf4j
//@Component
//public class AutoSplitJingdongOrderTask extends BaseR3Task implements IR3Task {
//
//
//    /**
//     * 默认每次查询页数信息
//     */
//    protected static final int DEFAULT_PAGE_SIZE = 1000;
//
//    @Autowired
//    private OmsOrderJdSplitService omsOrderJdSplitService;
//    @Autowired
//    private ThreadPoolTaskExecutor jdSplitTaskThreadPoolExecutor;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        if (log.isDebugEnabled()) {
//            log.debug("AutoSplitJingdongOrderTask Start");
//        }
//        RunTaskResult result = new RunTaskResult();
//
//        try {
//            String tableName = "oc_b_order_jingdong_split_task";
//            if (CollectionUtils.isEmpty(nodes)) {
//                if (log.isDebugEnabled()) {
//                    log.debug("AutoSplitJingdongOrderTask.nodes not get！");
//                }
//                result.setSuccess(false);
//                result.setMessage("请检查环境,node获取不到.");
//                return result;
//            }
//            List<Future<Boolean>> results = new ArrayList<>();
//            // 一个node查询一个库，每个线程查询一个库的数据做处理
//            for (String nodeName : nodes) {
//            }
//            results.forEach(futureResult -> {
//                if (log.isDebugEnabled()) {
//                    log.debug("AutoSplitJingdongOrderTask.execute Result:{}", JSON.toJSONString(futureResult));
//                }
//            });
//            if (log.isDebugEnabled()) {
//                log.debug("Finish AutoSplitJingdongOrderTask.execute");
//            }
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            log.error("AutoSplitJingdongOrderTask.execute error:", ex);
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        }
//        return result;
//    }
//
//    class JingdongOrderSplitCallable implements Callable<Boolean> {
//
//        private final String nodeName;
//        private final String taskTableName;
//
//        public JingdongOrderSplitCallable(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() {
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer size = config.getProperty("lts.AutoSplitJingdongOrderTask.range", 500);
//            Integer retriesTimes = config.getProperty("lts.AutoSplitJingdongOrderTask.retriesTimes", 5);
//            if (log.isDebugEnabled()) {
//                log.debug("AutoSplitJingdongOrderTask.Param.NodeName={}", nodeName);
//            }
//            List<OcBOrderJingdongSplitTask>  taskList = omsOrderJdSplitService
//                    .getJingdongSplitTaskList(nodeName, taskTableName, size, retriesTimes);
//            omsOrderJdSplitService.sendPreSplitOrderToJingdong(taskList);
//            return true;
//        }
//
//    }
//}