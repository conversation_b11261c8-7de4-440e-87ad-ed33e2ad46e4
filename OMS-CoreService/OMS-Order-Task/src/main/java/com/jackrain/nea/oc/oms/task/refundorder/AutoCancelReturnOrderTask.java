package com.jackrain.nea.oc.oms.task.refundorder;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.BusinessSystem;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.es.ES4TaoBaoRefund;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OcCancelChangingOrRefundService;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @ClassName AutoCancelReturnOrderTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/29 14:34
 * @Version 1.0
 */
@Slf4j
@Component
public class AutoCancelReturnOrderTask {

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @NacosValue(value = "${oms.return.order.cancel.num:200}", autoRefreshed = true)
    public Integer eachCancelNum;
    @Autowired
    private BusinessSystem businessSystem;
    @Autowired
    private OcCancelChangingOrRefundService cancelChangingOrRefundService;

    @XxlJob("AutoCancelReturnOrderTask")
    public void execute() {
        // 分批去获取30天内 待入库的退换货单
        List<Long> returnIdList = ocBReturnOrderMapper.selectForCancel(DateUtil.offset(new Date(), DateField.DAY_OF_YEAR, -3), eachCancelNum);
        if (CollectionUtils.isEmpty(returnIdList)) {
            return;
        }
        for (Long returnId : returnIdList) {
            try {
                // 根据退换货单id 获取退换货单信息
                OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectByid(returnId);
                if (ObjectUtil.isNull(ocBReturnOrder)) {
                    // 打印ERROR日志 然后continue
                    log.error(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 退换货单不存在！ID:{}", "退换货单自动取消"), returnId);
                    continue;
                }
                if (StringUtils.isEmpty(ocBReturnOrder.getReturnId())) {
                    OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
                    updateOcBReturnOrder.setId(returnId);
                    updateOcBReturnOrder.setModifieddate(new Date());
                    ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                    // 打印ERROR日志 然后continue
                    log.info(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 平台退款单号不存在！ID:{}", "退换货单自动取消"), returnId);
                    continue;
                }

                // 再次校验退换货单的状态 防止es有延迟
                Integer returnStatus = ocBReturnOrder.getReturnStatus();
                if (ObjectUtil.notEqual(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal(), returnStatus)) {
                    OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
                    updateOcBReturnOrder.setId(returnId);
                    updateOcBReturnOrder.setModifieddate(new Date());
                    ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                    continue;
                }
                // 待入库状态的，根据退换货单上面的订单id 获取订单上面的备注信息 看看是否存在关键字
                if (ObjectUtil.isNull(ocBReturnOrder.getOrigOrderId())) {
                    log.error(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 没关联订单！ID:{}", "退换货单自动取消"), returnId);
                    OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
                    updateOcBReturnOrder.setId(returnId);
                    updateOcBReturnOrder.setModifieddate(new Date());
                    ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                    continue;
                }
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(ocBReturnOrder.getOrigOrderId());
                if (ObjectUtil.isNull(ocBOrder)) {
                    log.error(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 订单不存在！ID:{}", "退换货单自动取消"), returnId);
                    OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
                    updateOcBReturnOrder.setId(returnId);
                    updateOcBReturnOrder.setModifieddate(new Date());
                    ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                    continue;
                }

                // 取订单卖家备注“关键字+售后单号”一致，
                String sellerMemo = ocBOrder.getSellerMemo();
                if (StringUtils.isEmpty(sellerMemo)) {
                    log.info(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 对应的订单卖家备注为空！ID:{}", "退换货单自动取消"), returnId);
                    OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
                    updateOcBReturnOrder.setId(returnId);
                    updateOcBReturnOrder.setModifieddate(new Date());
                    ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                    continue;
                }
                // 获取系统业务参数中的   系统配置-业务系统参数-退换货单取消维护关键字
                String remark = businessSystem.getReturnOrderAutoCancelRemark();
                String platformReturnNo = ocBReturnOrder.getReturnId();
                // 根据 | 把关键字给分隔开
                if (StringUtils.isEmpty(remark)) {
                    log.info(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 关键字未维护！ID:{}", "退换货单自动取消"), returnId);
                    continue;
                }
                // 获取平台退款单号
                String[] remarkArr = remark.split("\\|");
                // 组装出来所有的取消的关键词
                for (String remarkStr : remarkArr) {
                    String cancelRemark = remarkStr + platformReturnNo;
                    if (sellerMemo.contains(cancelRemark)) {
                        // 执行退换货单取消
                        ValueHolderV14 vh = new ValueHolderV14<>();
                        JSONArray jsonArray = new JSONArray();
                        jsonArray.add(returnId);
                        try {
                            ValueHolderV14 valueHolderV14 = cancelChangingOrRefundService.oneOcCancle(SystemUserResource.getRootUser(), vh, jsonArray);
                            if (valueHolderV14.isOK()) {
                                log.info(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 执行取消成功！ID:{}", "退换货单自动取消"), returnId);
                            } else {
                                // 失败的话 调整修改时间 防止每次都只拉到同一批数据
                                OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
                                updateOcBReturnOrder.setId(returnId);
                                updateOcBReturnOrder.setModifieddate(new Date());
                                ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                                log.info(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 执行取消失败！ID:{}", "退换货单自动取消"), returnId);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
                            updateOcBReturnOrder.setId(returnId);
                            updateOcBReturnOrder.setModifieddate(new Date());
                            ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                            log.info(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 执行取消发生异常！ID:{}", "退换货单自动取消"), returnId);
                        }
                        break;
                    } else {
                        log.info(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 未匹配到配置！ID:{}，cancelRemark:{} ", "退换货单自动取消"), returnId, cancelRemark);
                    }
                }
            } catch (Exception e) {
                OcBReturnOrder updateOcBReturnOrder = new OcBReturnOrder();
                updateOcBReturnOrder.setId(returnId);
                updateOcBReturnOrder.setModifieddate(new Date());
                ocBReturnOrderMapper.updateById(updateOcBReturnOrder);
                log.error(LogUtil.format("AutoCancelReturnOrderTask.execute 退换货单自动取消时 执行取消发生异常！ID:{}， errorMsg:{}", "退换货单自动取消"), returnId, e.getMessage());
            }
        }
    }
}
