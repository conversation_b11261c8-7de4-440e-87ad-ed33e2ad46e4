package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpTaoBaoJXOrder;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.TaobaoJxTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoJxOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 淘宝经销订单转换补偿定时任务
 **/
@Component
@Slf4j
public class AutoTaobaoJxTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpTaobaoJxOrderService ipTaobaoJxOrderService;

    @Autowired
    private TaobaoJxTransferOrderProcessImpl taobaoJxTransferOrderProcess;

    @Override
    @XxlJob("AutoTaobaoJxTransferTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        long start = System.currentTimeMillis();
        try {

            List<String> orderNoList = ES4IpTaoBaoJXOrder.findTidByTransStatus(0, DEFAULT_PAGE_SIZE);
            List<IpTaobaoJxOrderRelation> ipTaobaoJxOrderRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {

                IpTaobaoJxOrderRelation ipTaobaoJxOrderRelation = this.ipTaobaoJxOrderService.selectTaobaoJxOrder(orderNo);
                if (ipTaobaoJxOrderRelation == null) {
                    String errorMessage = Resources.getMessage("AutoTaobaoJxTransferTask.Order.NotExist!###OrderNo="
                            + orderNo);
                    log.error(errorMessage);
                } else {
                    ipTaobaoJxOrderRelationList.add(ipTaobaoJxOrderRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.taobaoJxTransferOrderProcess, ipTaobaoJxOrderRelationList);
            result.setSuccess(true);
            result.setMessage("任务耗时：" + (System.currentTimeMillis() - start) + "毫秒");
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoTaobaoJxTransferTask,异常信息:{}", "淘宝经销订单转换补偿定时任务"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(true);
            result.setMessage(ex.getMessage());
        }


        return null;
    }
}
