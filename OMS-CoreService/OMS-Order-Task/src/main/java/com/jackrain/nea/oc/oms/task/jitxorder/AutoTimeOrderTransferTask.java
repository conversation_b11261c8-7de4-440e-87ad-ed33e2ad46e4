package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpTimeOrderVip;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.VipTimeOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 唯品会时效订单转单补偿任务
 * 1、查询ES中SYNSTATUS=0的单据
 *
 * @author: chenxiulou
 * @since: 2019-09-03
 * create at : 2019-03-15 23:10
 */
@Component
@Slf4j
public class AutoTimeOrderTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private VipTimeOrderProcessImpl vipTimeOrderProcessImpl;

    @Override
    @XxlJob("AutoTimeOrderTransferTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            params = R3XxlJobParamHelper.xxlParam2R3Json();
            int timeOrderVipStatus = params.getIntValue("timeOrderVipStatus");
            int tranStatus = params.getIntValue("tranStatus");
            timeOrderVipStatus = timeOrderVipStatus == 0 ? TimeOrderVipStatusEnum.CREATED.getValue() : timeOrderVipStatus;
            List<String> orderNoList = ES4IpTimeOrderVip.getOccupiedOrderSnByStatusAndIsTrans(
                    timeOrderVipStatus, tranStatus, 0, DEFAULT_PAGE_SIZE);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoTimeOrderTransferTask.OrderNoList={}"), JSONObject.toJSONString(orderNoList));
            }
            List<IpVipTimeOrderRelation> ipVipTimeOrderRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpVipTimeOrderRelation timeOrderRelation = this.ipVipTimeOrderService.selectTimeOrder(orderNo);
                if (timeOrderRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoTimeOrderTransferTask.Order.NotExist!###OrderNo="
                            + orderNo);
                    log.error(LogUtil.format(errorMessage));
                } else {
                    ipVipTimeOrderRelationList.add(timeOrderRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.vipTimeOrderProcessImpl, ipVipTimeOrderRelationList);
            result.setSuccess(true);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("AutoTimeOrderTransferTask.Execute.Error: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
