package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <p>Title: TmallDddReturnBackTask</p>
 * <p>Description: 天猫退款通知平台处理任务</p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TmallDddReturnBackTask extends BaseR3Task implements IR3Task {

    @Resource
    private OcBReturnOrderService returnOrderService;

    @Override
    @XxlJob("TmallDddReturnBackTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            returnOrderService.tmallDdd();
            result.setSuccess(true);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
