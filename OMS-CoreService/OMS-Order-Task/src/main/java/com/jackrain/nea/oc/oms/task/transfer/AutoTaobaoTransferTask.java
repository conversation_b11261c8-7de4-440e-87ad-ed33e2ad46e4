package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpTaoBaoOrder;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.TaobaoTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 淘宝转单自动补偿任务  （平常开启AutoTaobaoTransferTask，大促或者单子很多的时候开启 AutoTaobao2MqTransferTask .两个只能开启一个 ）
 * 1、查询ES中ISTRANS=0的单据
 *
 * @author: 易邵峰
 * @since: 2019-03-15
 * create at : 2019-03-15 23:10
 */
@Component
@Slf4j
public class AutoTaobaoTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpTaobaoOrderService ipTaobaoOrderService;

    @Autowired
    private TaobaoTransferOrderProcessImpl taobaoTransferOrderProcess;

    @Override
    @XxlJob("AutoTaobaoTransferTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {

            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoTaobaoTransferTask.range", DEFAULT_PAGE_SIZE);
            List<String> orderNoList = ES4IpTaoBaoOrder.selectUnTransferredOrderFromEs(0, pageSize);

            List<IpTaobaoOrderRelation> orderRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpTaobaoOrderRelation taobaoOrderRelation = this.ipTaobaoOrderService.selectTaobaoOrderByTid(orderNo);
                if (taobaoOrderRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoTaobaoTransferTask.Order.NotExist!###OrderNo="
                            + orderNo);
                } else {
                    orderRelationList.add(taobaoOrderRelation);
                }
            }

            threadOrderProcessor.startMultiThreadExecute(this.taobaoTransferOrderProcess, orderRelationList);

            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoTaobaoTransferTask,异常信息:{}", "淘宝转单自动补偿任务"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}
