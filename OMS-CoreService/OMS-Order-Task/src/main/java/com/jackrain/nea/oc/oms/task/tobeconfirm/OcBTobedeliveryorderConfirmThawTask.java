//package com.jackrain.nea.oc.oms.task.tobeconfirm;
//
//
//import cn.hutool.core.util.ObjectUtil;
//import com.alibaba.dubbo.config.annotation.Reference;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.jackrain.nea.constants.ResultCode;
//import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
//import com.jackrain.nea.hub.request.naika.NaiKaThawRequest;
//import com.jackrain.nea.oc.oms.config.OmsDeliveryOrderConfig;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
//import com.jackrain.nea.oc.oms.mapper.task.OcBTobedeliveryorderConfirmTaskMapper;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
//import com.jackrain.nea.oc.oms.model.table.task.OcBTobedeliveryorderConfirmTask;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * @Auther: chenhao
// * @Date: 2022-07-08 11:37
// * @Description:
// */
//
//@Slf4j
//@Component
//public class OcBTobedeliveryorderConfirmThawTask implements IR3Task {
//
//    @Autowired
//    private OcBTobedeliveryorderConfirmTaskMapper taskMapper;
//
//    @Autowired
//    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//
//    @Reference(group = "hub", version = "1.0")
//    private NaiKaOrderCmd naiKaOrderCmd;
//
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//
//        log.info(LogUtil.format("OcBTobedeliveryorderConfirmThawTask.execute start", "OcBTobedeliveryorderConfirmThawTask"));
//
//        RunTaskResult runTaskResult = new RunTaskResult();
//
//        List<OcBTobedeliveryorderConfirmTask> taskList = taskMapper.selectList(new LambdaQueryWrapper<OcBTobedeliveryorderConfirmTask>()
//                .ne(OcBTobedeliveryorderConfirmTask::getStatus, OmsDeliveryOrderConfig.TOBEDELIVERYORDER_CONFIRM_TASK_SUCCESS)
//                .lt(OcBTobedeliveryorderConfirmTask::getFailCount, OmsDeliveryOrderConfig.FAIL_COUNT)
//                .orderByAsc(OcBTobedeliveryorderConfirmTask::getModifieddate));
//
//        if (CollectionUtils.isEmpty(taskList)) {
//            runTaskResult.setSuccess(Boolean.TRUE);
//            runTaskResult.setMessage("无数据需要处理");
//            return runTaskResult;
//        }
//
//        for (OcBTobedeliveryorderConfirmTask confirmTask : taskList) {
//            // 根据零售发货单单id 查询零售发货单数据
//            OcBOrder ocOrder = ocBOrderMapper.selectByID(confirmTask.getOrderId());
//            List<OcBOrderNaiKa> ocOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(confirmTask.getOrderId());
//            // 取出所有卡号
//            List<String> cardCodeList = ocOrderNaiKaList.stream().map(OcBOrderNaiKa::getCardCode).collect(Collectors.toList());
//            NaiKaThawRequest naiKaThawRequest = new NaiKaThawRequest();
//            naiKaThawRequest.setBillNo(ocOrder.getBillNo());
//            naiKaThawRequest.setSourceCode(ocOrder.getTid());
//            naiKaThawRequest.setCardList(cardCodeList);
//
//            OcBTobedeliveryorderConfirmTask update = new OcBTobedeliveryorderConfirmTask();
//            update.setId(confirmTask.getId());
//
//            try {
//                log.info(LogUtil.format("OcBTobedeliveryorderConfirmThawTask.orderThaw start",
//                        "OcBTobedeliveryorderConfirmThawTask.orderThaw "), JSONObject.toJSONString(naiKaThawRequest));
//                ValueHolderV14 valueHolderV14 = naiKaOrderCmd.orderThaw(naiKaThawRequest);
//                log.info(LogUtil.format("OcBTobedeliveryorderConfirmThawTask.valueHolderV14 start",
//                        "OcBTobedeliveryorderConfirmThawTask.valueHolderV14 "), JSONObject.toJSONString(valueHolderV14));
//
//                // 根据结果 对奶卡解冻表数据进行处理
//                if (ObjectUtil.equals(ResultCode.FAIL, valueHolderV14)) {
//                    // 执行失败 则对解冻次数以及状态进行修改
//                    update.setStatus(OmsDeliveryOrderConfig.TOBEDELIVERYORDER_CONFIRM_TASK_FIAL);
//                    update.setFailCount(confirmTask.getFailCount() + 1);
//                    update.setModifieddate(new Date());
//                    update.setFailReason(valueHolderV14.getMessage());
//                    taskMapper.updateById(update);
//                } else {
//                    // 执行成 则对解冻次数以及状态进行修改
//                    update.setStatus(OmsDeliveryOrderConfig.TOBEDELIVERYORDER_CONFIRM_TASK_SUCCESS);
//                    update.setModifieddate(new Date());
//                    taskMapper.updateById(update);
//                }
//            } catch (Exception e) {
//                update.setStatus(OmsDeliveryOrderConfig.TOBEDELIVERYORDER_CONFIRM_TASK_FIAL);
//                update.setFailCount(confirmTask.getFailCount() + 1);
//                update.setModifieddate(new Date());
//                update.setFailReason(e.getMessage());
//                taskMapper.updateById(update);
//            }
//        }
//
//
//        runTaskResult.setSuccess(Boolean.TRUE);
//        runTaskResult.setMessage("处理成功！");
//        return runTaskResult;
//    }
//}
