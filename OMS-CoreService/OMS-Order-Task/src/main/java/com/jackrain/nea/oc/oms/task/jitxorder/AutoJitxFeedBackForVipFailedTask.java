package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jackrain.nea.oc.oms.es.ES4IpJitXDelivery;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJitxOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDelivery;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-02-21 16:46
 * @Description : JITX寻仓单寻仓反馈，平台实际反馈失败，获取店铺默认仓库再次进行反馈
 **/
@Component
@Slf4j
public class AutoJitxFeedBackForVipFailedTask extends BaseR3Task implements IR3Task {

    @Value("${lts.jitx.feedback.state.failed.task.size:500}")
    private int pageSize;

    private static final String JITX_FEEDBACK_STATE_FAILED_DAYS = "business_system:jitx_feedback_state_failed_days";

    private static final String JITX_FEEDBACK_STATE_FAILED_VALUE = "business_system:jitx_feedback_state_failed_value";

    private static final String JITX_FEEDBACK_STATE_FAILED_MSG = "business_system:jitx_feedback_state_failed_msg";



    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private IpBJitxOrderMapper ipBJitxOrderMapper;

    @Autowired
    private IpBJitxDeliveryMapper ipBJitxDeliveryMapper;

    @Override
    @XxlJob("AutoJitxFeedBackForVipFailedTask")
    public RunTaskResult execute(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("AutoJitxFeedBackForVipFailedTask.start",
                    "AutoJitxFeedBackForVipFailedTask"));
        }
        RunTaskResult result = new RunTaskResult();
        try {
            String dayStr = (String)RedisOpsUtil.getStrRedisTemplate().opsForValue().get(JITX_FEEDBACK_STATE_FAILED_DAYS);
            Integer days = Integer.valueOf(Optional.ofNullable(dayStr).orElse("5"));
            String feedbackStateValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(JITX_FEEDBACK_STATE_FAILED_VALUE);

            List<String> feedbackStateList = Arrays.asList(feedbackStateValue.split(","));
            JSONArray syncStatusArray = new JSONArray();
            if(CollectionUtils.isNotEmpty(feedbackStateList)){
                for(String state:feedbackStateList){
                    syncStatusArray.add(state);
                }
            }
            List<String> orderNoList = ES4IpJitXDelivery.findOrderSnByFeedbackState(0, pageSize,syncStatusArray,1,days);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("寻仓单寻仓反馈补偿feedback_state=FAIL任务,orderNoList:{}",
                        "AutoJitxFeedBackForVipFailedTask"),JSON.toJSONString(orderNoList));
            }
            String feedbackStateMsg = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(JITX_FEEDBACK_STATE_FAILED_MSG);
            boolean isAllMsg = false;
            List<String> feedbackStateMsgList = new ArrayList<>();
            if(feedbackStateMsg == null || "".equals(feedbackStateMsg)){
                isAllMsg = true;
            }else{
                feedbackStateMsgList = Arrays.asList(feedbackStateMsg.split("\\|"));
            }
            JSONArray errorJsonArray = new JSONArray();
            for (String orderNo : orderNoList) {
                IpJitxDeliveryRelation jitDeliveryRelation = this.ipJitxDeliveryService.selectJitxDelivery(orderNo);
                if (jitDeliveryRelation == null) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("寻仓单寻仓反馈补偿feedback_state=FAIL任务,未查询到订单信息,不进行反馈补偿任务orderSn:{}",
                                "AutoJitxFeedBackForVipFailedTask"),orderNo);
                    }
                    continue;
                }
                if(!isAllMsg){
                    boolean isFeedback = false;
                    String feedbackmsgkey = jitDeliveryRelation.getJitxDelivery().getFeedbackmsgkey();
                    for(String msg:feedbackStateMsgList){
                        if(feedbackmsgkey.contains(msg)){
                            isFeedback = true;
                            break;
                        }
                    }
                    if(!isFeedback){
                        continue;
                    }
                }
                //查询对应JITX订单是否已存在
                int count = ipBJitxOrderMapper.countJitxOrderByOrderSn(orderNo);
                if(count > 0){
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("寻仓单寻仓反馈补偿feedback_state=FAIL任务,已存在JITX订单,不进行反馈补偿任务orderSn:{}",
                                "AutoJitxFeedBackForVipFailedTask"),orderNo);
                    }
                    //更新平台反馈状态
                    ipBJitxDeliveryMapper.update(null, Wrappers.<IpBJitxDelivery>lambdaUpdate()
                            .set(IpBJitxDelivery::getFeedbackState, null)
                            .set(IpBJitxDelivery::getFeedbackmsgkey,"JITX订单已下载不再进行平台反馈")
                            .eq(IpBJitxDelivery::getOrderSn, orderNo));
                    continue;
                }
                //开始进行反馈逻辑
                ValueHolderV14 v14 = ipJitxDeliveryService.deliveryFeedbackResultToVip(jitDeliveryRelation,SystemUserResource.getRootUser());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("寻仓单寻仓反馈补偿任务orderSn:{},response:{}",
                            "AutoJitxFeedBackForVipFailedTask"),orderNo,JSON.toJSONString(v14));
                }
                if(!v14.isOK()){
                    errorJsonArray.add("orderSn="+orderNo+v14.getMessage());
                }else{
                    //更新平台反馈状态
                    ipBJitxDeliveryMapper.update(null, Wrappers.<IpBJitxDelivery>lambdaUpdate()
                            .set(IpBJitxDelivery::getFeedbackState, null)
                            .set(IpBJitxDelivery::getFeedbackmsgkey,"寻仓反馈补偿流程完成，结束流程")
                            .eq(IpBJitxDelivery::getOrderSn, orderNo));
                }
            }
            if(errorJsonArray.size() > 0){
                result.setSuccess(false);
                result.setMessage(JSON.toJSONString(errorJsonArray));
            }else{
                result.setSuccess(true);
            }
        } catch (Exception ex) {
            log.error("AutoJitxFeedBackForVipFailedTask.Execute Error", ex);
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
