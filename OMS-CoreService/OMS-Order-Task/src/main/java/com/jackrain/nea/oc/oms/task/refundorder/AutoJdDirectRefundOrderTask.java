package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.es.ES4IpJingDongDirectRefund;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.JDDirectCancelProcessImpl;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName AutoJdDirectRefundOrderTask
 * @Description 京东厂直
 * <AUTHOR>
 * @Date 2024/4/1 16:08
 * @Version 1.0
 */
@Slf4j
@Component
public class AutoJdDirectRefundOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private JDDirectCancelProcessImpl cancelProcess;

    @Override
    @XxlJob("AutoJdDirectRefundOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        JSONArray jsonArray = ES4IpJingDongDirectRefund.selectJingdongDirectRefundKey(0, 200);
        if (CollectionUtils.isEmpty(jsonArray)) {
            return result;
        }
        for (int i = 0; i < jsonArray.size(); i++) {
            try {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                Long id = jsonObject.getLong("ID");
                String no = jsonObject.getString("REFUND_ID");
                OmsJDDirectCancelRelation model = OmsJDDirectCancelRelation.buildRelation(id, no);
                cancelProcess.start(model, false, SystemUserResource.getRootUser());
            } catch (Exception e) {
                log.error(LogUtil.format("AutoJdDirectRefundOrderTask.Execute.Error,异常：{}", "京东厂直退单"), Throwables.getStackTraceAsString(e));
            }
        }
        return result;
    }
}
