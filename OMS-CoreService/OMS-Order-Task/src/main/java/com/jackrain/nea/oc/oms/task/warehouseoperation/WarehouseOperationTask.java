//package com.jackrain.nea.oc.oms.task.warehouseoperation;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.oc.oms.services.OmsWarehouseOperationService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * @Author: 黄世新
// * @Date: 2020/3/28 7:42 下午
// * @Version 1.0
// * <p>
// * WMS回传包裹信息，定时任务读取包裹信息，根据包裹信息状态更新到发货信息表；如果是拦截成功的话，拦截失败；拒收 则进行业务操作，生成对应的退换货单
// */
//@Slf4j
//@Component
//public class WarehouseOperationTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private OmsWarehouseOperationService omsWarehouseOperationService;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//
//        RunTaskResult taskResult = new RunTaskResult();
//        try {
//            User rootUser = SystemUserResource.getRootUser();
//            omsWarehouseOperationService.selectWarehouseOperationData(rootUser);
//            taskResult.setSuccess(true);
//        } catch (Exception ex) {
//            ex.printStackTrace();
//            log.error(this.getClass().getName() + "  WarehouseOperationTask.Execute Error", ex);
//            taskResult.setSuccess(false);
//            taskResult.setMessage(ex.getMessage());
//        }
//        return taskResult;
//    }
//}
