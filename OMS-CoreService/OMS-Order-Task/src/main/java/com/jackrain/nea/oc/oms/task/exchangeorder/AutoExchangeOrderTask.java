package com.jackrain.nea.oc.oms.task.exchangeorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ESIpTaoBaoExchange;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.TaobaoTransferExchangeProcessImpl;
import com.jackrain.nea.oc.oms.services.OmsTaobaoExchangeService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * 换货单自动转换服务
 */

@Slf4j
@Component
public class AutoExchangeOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsTaobaoExchangeService omsTaobaoExchangeService;
    @Autowired
    private TaobaoTransferExchangeProcessImpl exchangeProcess;


    @Override
    @XxlJob("AutoExchangeOrderTask")
    public RunTaskResult execute(JSONObject params) {
        long starTime = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.OrderTransferTask.range", 200);
            List<Long> list = ESIpTaoBaoExchange.selectExchangeOrder(0, pageSize);
            List<OmsTaobaoExchangeRelation> relations = new ArrayList<>();
            for (Long disputeId : list) {
                OmsTaobaoExchangeRelation relation = omsTaobaoExchangeService.selectTaobaoExchangeInfo(disputeId+"");
                if (relation != null) {
                    relations.add(relation);
                } else {
                    String errorMessage = Resources.getMessage("AutoExchangOrderTask Order " +
                            "NotExist!disputeId=" + disputeId);
                    log.error(LogUtil.format(errorMessage));
                }

            }
            threadOrderProcessor.startMultiThreadExecute(this.exchangeProcess, relations);
            result.setSuccess(true);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 淘宝换货定时任务耗时:{}ms", Time);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("AutoExchangOrderTask.Execute Error {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
