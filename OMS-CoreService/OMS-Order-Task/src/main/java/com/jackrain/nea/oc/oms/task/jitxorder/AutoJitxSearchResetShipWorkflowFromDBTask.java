//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.jackrain.nea.oc.oms.model.table.IpBJitxResetShipWorkflow;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.web.face.User;
//import com.vip.vis.workflow.service.shipReset.GetShipResetWorkflowVopRequest;
//import com.vip.vis.workflow.service.shipReset.GetShipResetWorkflowVopResult;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.stream.Collectors;
//
///**
// * description：JixX发货重置查询
// *
// * <AUTHOR>
// * @date 2021/10/18
// */
//@Slf4j
//@Component
//@Deprecated
//public class AutoJitxSearchResetShipWorkflowFromDBTask extends AbstractJitxResetShipTask {
//
//
//    @Value("${oms.oc.order.jitx.searchResetShipWorkflow.pull.num:1000}")
//    private Integer pullNum;
//
//    @Override
//    protected String threadPoolName() {
//        return "R3_OMS_JITX_SEARCH_RESET_SHIP_THREAD_POOL_%d";
//    }
//
//    @Override
//    protected String executeSqlWhere() {
//        return " WHERE CREATED_STATUS = 1 AND WORKFLOW_STATUS IN ('100','200') AND ISACTIVE = 'Y' ";
//    }
//
//    @Override
//    protected String executeSqlOrder() {
//        return null;
//    }
//
//    @Override
//    protected Integer getPullNum() {
//        return pullNum;
//    }
//
//    @Override
//    protected void exceute(List<IpBJitxResetShipWorkflow> shipWorkflowList, User operateUser) {
//        if (log.isDebugEnabled()) {
//            log.debug("JITX订单获取重置发货工单任务，AutoJitxSearchResetShipWorkflowTask size：{}", shipWorkflowList.size());
//        }
//        if (CollectionUtils.isEmpty(shipWorkflowList)) {
//            return;
//        }
//        ipBJitxResetShipWorkflowService.getResetShipWorkflows(shipWorkflowList, operateUser);
//    }
//}
