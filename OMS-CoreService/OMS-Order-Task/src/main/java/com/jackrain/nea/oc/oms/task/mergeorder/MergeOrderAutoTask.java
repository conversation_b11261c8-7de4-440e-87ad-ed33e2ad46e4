package com.jackrain.nea.oc.oms.task.mergeorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.OcMergeOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;


/***
 * 合并订单自动任务
 * ljp add
 */
@Slf4j
@Component
public class MergeOrderAutoTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcMergeOrderService ocMergeOrderService;

    @Override
    public RunTaskResult execute(JSONObject params) {

        RunTaskResult result = new RunTaskResult();
        try {

            long l = System.currentTimeMillis();
            LocalDateTime ldt = LocalDateTime.now();
            String logStart = "TASK_TAG_" + ldt.getYear() + "_" + ldt.getMonthValue() + "_"
                    + ldt.getDayOfMonth() + "_" + ldt.getHour() + "_" + ldt.getMinute()+" " + ldt.getSecond();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("MergeOrderAutoTask.Start.{}"), logStart);
            }
            String msg = null;
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("MergeOrderAutoTask.End.{}.TimeCsm: {} ResultMessage: {}"), (System.currentTimeMillis() - l), msg);

            }
            result.setMessage(msg);
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(LogUtil.format("MergeOrderAutoTask.合并订单定时任务执行异常：{}",  "合并订单定时任务执行异常"), Throwables.getStackTraceAsString(e));
            String msg = "合并订单定时任务执行异常: ";
            if (e != null && e.getMessage() != null) {
                String s = e.getMessage().length() > 500 ? e.getMessage().substring(0, 500) : e.getMessage();
                msg += s;
            }
            result.setMessage(msg);
            result.setSuccess(false);
        }
        return result;
    }

}
