//package com.jackrain.nea.oc.oms.task.refundorder;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.model.table.task.OcBOrderToAgTask;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.services.task.OcBOrderToAgTaskService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.ExecutionException;
//import java.util.concurrent.Future;
//
///**
// * 淘宝退单转单传AG取消发货失败重传补偿任务
// *
// * <AUTHOR>
// * @date 2020/11/3 3:50 下午
// */
//@Deprecated
//@Component
//@Slf4j
//public class AutoRefundOrderToAgTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private OcBOrderToAgTaskService orderToAgTaskService;
//    @Autowired
//    private ThreadPoolTaskExecutor refundOrderToAgTaskThreadPoolExecutor;
//
//    @Override
//    @XxlJob("AutoRefundOrderToAgTask")
//    public RunTaskResult execute(JSONObject params) {
//        if (log.isDebugEnabled()) {
//            log.debug("Start AutoRefundOrderToAgTask.execute");
//        }
//        RunTaskResult result = new RunTaskResult();
//        try {
//            String tableName = "oc_b_order_to_ag_task";
//            if (CollectionUtils.isEmpty(nodes)) {
//                if (log.isDebugEnabled()) {
//                    log.debug("AutoRefundOrderToAgTask.nodes not get！");
//                }
//                result.setSuccess(false);
//                result.setMessage("请检查环境,node获取不到.");
//                return result;
//            }
//            List<Future<Boolean>> results = new ArrayList<>();
//            // 一个node查询一个库，每个线程查询一个库的数据做处理
//            for (String nodeName : nodes) {
//            }
//            results.forEach(futureResult -> {
//                if (log.isDebugEnabled()) {
//                    try {
//                        log.debug("AutoRefundOrderToAgTask.execute Result:{}", futureResult.get().toString());
//                    } catch (InterruptedException e) {
//                        log.error("AutoRefundOrderToAgTask.InterruptedException Error:", e);
//                    } catch (ExecutionException e) {
//                        log.error("AutoRefundOrderToAgTask.ExecutionException Error:", e);
//                    }
//                }
//            });
//            if (log.isDebugEnabled()) {
//                log.debug("Finish AutoRefundOrderToAgTask.execute");
//            }
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            log.error("AutoRefundOrderToAgTask.execute error:", ex);
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        }
//        return result;
//    }
//
//    class RefundOrderToAgCallable implements Callable<Boolean> {
//
//        private final String nodeName;
//        private final String taskTableName;
//
//        public RefundOrderToAgCallable(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() {
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer size = config.getProperty("lts.AutoRefundOrderToAgTask.range", 500);
//            Integer status = config.getProperty("lts.AutoRefundOrderToAgTask.status", 0);
//            Integer retriesTimes = config.getProperty("lts.AutoRefundOrderToAgTask.retriesTimes", 5);
//            if (log.isDebugEnabled()) {
//                log.debug("AutoRefundOrderToAgTask.Param.NodeName={}", nodeName);
//            }
//            List<OcBOrderToAgTask> orderToAgs = orderToAgTaskService.getTaskOrderToAg(nodeName, taskTableName, size,
//                    status, retriesTimes);
//            if (CollectionUtils.isNotEmpty(orderToAgs)) {
//                orderToAgs.forEach(orderToAg -> orderToAgTaskService.sendOrderToAg(orderToAg, retriesTimes));
//            }
//            return true;
//        }
//
//    }
//}
