package com.jackrain.nea.oc.oms.task.canceltransfer;


import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.ip.service.JdCancelOrderTransferService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 京东取消订单转单任务
 *
 * @author: 孙继东
 * @since: 2019-04-22
 * create at : 2019-04-22 9:34
 */
@Component
@Slf4j
@Deprecated
public class JdCancelOrderTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private JdCancelOrderTransferService jdCancelOrderTransferService;

    /**
     * 京东取消订单转单
     *
     * @return
     */
    @Override
    @XxlJob("JdCancelOrderTransferTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug("JdCancelOrderTransferTask.start");
        Long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {
            //    jdCancelOrderTransferService.cancelOrderTransfer();
            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms");
        } catch (Exception e) {
            log.error(LogUtil.format("JdCancelOrderTransferTask.Error: {}"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
