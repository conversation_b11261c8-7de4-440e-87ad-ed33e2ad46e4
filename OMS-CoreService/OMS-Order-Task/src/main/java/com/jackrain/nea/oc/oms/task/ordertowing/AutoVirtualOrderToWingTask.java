package com.jackrain.nea.oc.oms.task.ordertowing;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ip.model.wing.WingIncidentalsOrderCreateModel;
import com.jackrain.nea.ip.model.wing.WingReturnResult;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnTypeMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ToDRPStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;


/***
 * 订单传wing服务  【仓库发货】且【订单类型】为【虚拟定金】或【虚拟】的零售发货单  未传DRP或传DRP失败
 */
@Slf4j
@Component
public class AutoVirtualOrderToWingTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private OcBReturnTypeMapper returnTypeMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Value("${wing.incidental.order.default.store.code:ECTZ1}")
    private String defaultStoreCode;

    @Value("${wing.incidental.order.default.reason.type:17}")
    private String defaultReasonTypeId;

    @Autowired
    private IpRpcService ipRpcService;

    @Override
    @XxlJob("AutoVirtualOrderToWingTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug(LogUtil.format("进入订单传wing服务",  "订单传wing服务"));
        RunTaskResult result = new RunTaskResult();
        long starTime = System.currentTimeMillis();
        try {
            List<Long> virtualOrderIdList = ES4Order.findIdVirtualOrder(0, 200);
            if (CollectionUtils.isEmpty(virtualOrderIdList)) {
                result.setSuccess(true);
                result.setMessage("未从ES查询到符合条件的数据");
                return result;
            }
            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(virtualOrderIdList);
            if (CollectionUtils.isEmpty(ocBOrderList)) {
                result.setSuccess(true);
                result.setMessage("未从数据库查询到有效的数据");
                return result;
            }
//            List<OcBOrder> ocBOrderList = Lists.newArrayList(ocBOrderMapper.selectByID(1344L));
            int fail = 0;
            for (OcBOrder order : ocBOrderList) {
                ValueHolderV14 v14 = null;
                try {
                    WingIncidentalsOrderCreateModel createModel = this.transferFeild(order);
                    v14 = ipRpcService.addIncidentals(createModel);
                } catch (Exception e) {
                    fail++;
                    log.error(LogUtil.format("当前数据生成杂费单异常：{}",  "订单传wing服务", order.getId()), Throwables.getStackTraceAsString(e));
                }
                if (v14 != null) {
                    OcBOrder update = new OcBOrder();
                    update.setId(order.getId());
                    if (v14.isOK()) {
                        try {
                            WingReturnResult returnResult = (WingReturnResult) v14.getData();
                            if (returnResult.getStatus() == 1) {
//                                update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                                List<WingReturnResult.FailMsg> faileds = returnResult.getFaileds();
//                                if (CollectionUtils.isNotEmpty(faileds)) {
//                                    String message = SplitMessageUtil.splitErrMsgBySize(faileds.get(0).getErrMsg(), SplitMessageUtil.SIZE_200);
//                                    update.setToDrpFailedReason(message);
//                                    int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                                    update.setToDrpCount(count + 1);
//                                } else {
//                                    update.setToDrpStatus(ToDRPStatusEnum.SUCCESS.getCode());
//                                    update.setToDrpFailedReason("");
//                                    update.setToDrpCount(0);
//                                }
                            } else {
//                                update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                                update.setToDrpFailedReason(returnResult.getMsg());
//                                int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                                update.setToDrpCount(count + 1);
                            }

                        } catch (Exception e) {
//                            update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                            update.setToDrpFailedReason("解析转换传wing结果异常");
//                            int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                            update.setToDrpCount(count + 1);
                            log.error(LogUtil.format("解析转换传wing结果异常：{}",  "订单传wing服务"), Throwables.getStackTraceAsString(e));
                        }
                    } else {
//                        update.setToDrpStatus(ToDRPStatusEnum.FAIL.getCode());
//                        update.setToDrpFailedReason(v14.getMessage());
//                        int count = order.getToDrpCount() == null ? 0 : order.getToDrpCount();
//                        update.setToDrpCount(count + 1);
                    }
                    //ocBOrderMapper.updateById(update);
                }
            }
            result.setSuccess(true);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            String msg = "执行成功条数：" + (ocBOrderList.size() - fail) + ",失败条数：" + fail+"耗时:"+Time;
            result.setMessage(msg);
        } catch (Exception e) {
            log.error(LogUtil.format("AutoVirtualOrderToWingTask.Execute Error:{}",  "订单传wing服务"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }

    private WingIncidentalsOrderCreateModel transferFeild(OcBOrder order) {

        WingIncidentalsOrderCreateModel createModel = new WingIncidentalsOrderCreateModel();
        //("中台订单号") id;
        createModel.setId(order.getBillNo());
        //("原始中台订单号") original_id;
        createModel.setOriginal_id(order.getBillNo());
        //("原始平台单号") original_tid;
        createModel.setOriginal_tid(order.getTid());
        //("下单店铺CODE") shopCode;
        createModel.setShopCode(order.getCpCShopEcode());
        //("下单店铺名称") shopName;
        createModel.setShopName(order.getCpCShopTitle());
        if(StringUtils.isEmpty(order.getCpCShopEcode())){
            CpShop cpShop = cpRpcService.selectShopById(order.getCpCShopId());
            if(cpShop!=null){
                //("下单店铺CODE") shopCode;
                createModel.setShopCode(cpShop.getEcode());
                //("下单店铺名称") shopName;
                createModel.setShopName(cpShop.getCpCShopTitle());
            }
        }
        //("退货逻辑仓编号:默认都是做到 ECTZ1") warehouseCode;
        createModel.setWarehouseCode(defaultStoreCode);
        //("传杂费单退货原因列表ID") omsreasontypeid; 虚拟订单默认17 20210817 产品确认调整
        createModel.setOmsreasontypeid(defaultReasonTypeId);
        String orderTypeDesc = OrderTypeEnum.getTextByVal(order.getOrderType());
        orderTypeDesc = StringUtils.isEmpty(orderTypeDesc) ? "虚拟" : orderTypeDesc;
        //("单据类型") incidentalsType;
        createModel.setIncidentalsType(orderTypeDesc);
        //("退款商品描述") refunddescription;
        createModel.setRefunddescription(orderTypeDesc);
        List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItems(order.getId());
        if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
            OcBOrderItem orderItem = ocBOrderItems.get(0);
            if (orderItem != null) {
                //("退款商品描述") refunddescription;
                createModel.setRefunddescription(orderItem.getPsCSkuEcode());
            }
        }
        //("支付方式:传支付类型列表ID") paytype;
        createModel.setPaytype("2");
        //("付款时间(原始订单付款时间)") paytime;
        Date payTime = order.getPayTime() == null ? new Date() : order.getPayTime();
        createModel.setPaytime(DateUtil.format(payTime, DateUtil.dateTimeSecondsFormatter.getPattern()));
//        ("退款日期(打款时间):yyyy-mm-dd hh24:mm:ss") refunddate;
        Date endTime = order.getPlatformDeliveryTime() == null ? new Date() : order.getPlatformDeliveryTime();
        createModel.setRefunddate(DateUtil.format(endTime, DateUtil.dateTimeSecondsFormatter.getPattern()));
        //创建时间
        Date creationDate = order.getCreationdate() == null ? new Date() : order.getCreationdate();
        createModel.setCreationDate(DateUtil.format(creationDate, DateUtil.dateTimeSecondsFormatter.getPattern()));
        //("退商品金额: 正数斯凯奇应付，负数斯凯奇应收") refundgoodsamount;
        createModel.setRefundgoodsamount(order.getOrderAmt() == null ? "0" : order.getOrderAmt().negate().toString());
        //("备注") remark;
        createModel.setRemark(order.getInsideRemark());
        return createModel;
    }
}

