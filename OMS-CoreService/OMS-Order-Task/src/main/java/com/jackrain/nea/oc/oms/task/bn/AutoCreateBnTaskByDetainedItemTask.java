package com.jackrain.nea.oc.oms.task.bn;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderBnTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.BnDetainedItemColumnEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderBnTask;
import com.jackrain.nea.oc.oms.services.ReturnOrderLogService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AutoCreateBnTaskByDetainedItemTask
 * @Description 自动创建滞留件班牛工单任务
 * 滞留件判断条件：
 * 1. 是原退单：isBack = 1
 * 2. 签收状态为未签收：signingStatus = "0"
 * 3. 退签时间超过10天：withdrawalTime 不为空且距离当前时间超过10天
 * 4. 未入库状态：genericMark 字段为空 AND returnStatus 等于待入库状态值(20)
 * 5. 防重复处理：未创建过班牛工单
 * <AUTHOR>
 * @Date 2025/1/15 10:51
 * @Version 1.0
 */
@Component
@Slf4j
public class AutoCreateBnTaskByDetainedItemTask extends BaseR3Task implements IR3Task {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 滞留件判断天数阈值（硬编码10天）
     */
    private static final int DETAINED_DAYS_THRESHOLD = 10;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBReturnOrderBnTaskMapper ocBReturnOrderBnTaskMapper;

    @Autowired
    private ReturnOrderLogService logService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Value("${bn.project.id:22449}")
    private String projectId;

    @Value("${bn.app.id:36039}")
    private String appId;

    @Value("${task.limit:100}")
    private Integer limit;

    @Override
    @XxlJob("AutoCreateBnTaskByDetainedItemTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        result.setSuccess(true);

        try {
            log.info("开始执行AutoCreateBnTaskByDetainedItemTask定时任务");

            // 查询符合滞留件条件的退换货单
            List<OcBReturnOrder> detainedItems = queryDetainedItems();

            if (CollectionUtils.isEmpty(detainedItems)) {
                log.info("没有符合滞留件条件的退换货单");
                result.setMessage("没有符合滞留件条件的退换货单");
                return result;
            }

            log.info("查询到{}个符合滞留件条件的退换货单", detainedItems.size());

            // 标记为滞留件
            markAsDetainedItems(detainedItems);

            // 创建班牛工单
            createBnTasksForDetainedItems(detainedItems);

            result.setMessage("成功处理" + detainedItems.size() + "个滞留件退换货单");
        } catch (Exception e) {
            log.error("执行AutoCreateBnTaskByDetainedItemTask定时任务异常", e);
            result.setSuccess(false);
            result.setMessage("执行定时任务异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 查询符合滞留件条件的退换货单
     * 条件：
     * 1. 是原退单：isBack = 1
     * 2. 签收状态为未签收：signingStatus = "0"
     * 3. 退签时间超过10天：withdrawalTime 不为空且距离当前时间超过10天
     * 4. 未入库状态：genericMark 字段为空 AND returnStatus 等于待入库状态值(20)
     * 5. 未标记为滞留件：isDetainedItem 为空
     * 6. 未创建过班牛工单：防重复处理机制
     *
     * @return 符合条件的退换货单列表
     */
    private List<OcBReturnOrder> queryDetainedItems() {
        // 计算10天前的时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -DETAINED_DAYS_THRESHOLD);
        Date thresholdDate = calendar.getTime();

        log.info("查询滞留件条件：退签时间早于{}", DATE_FORMAT.format(thresholdDate));

        // 查询符合条件的退换货单
        List<OcBReturnOrder> returnOrders = ocBReturnOrderMapper.selectDetainedItems(
                thresholdDate,
                ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal(),
                limit
        );
        return returnOrders;
    }

    /**
     * 判断是否为滞留件
     *
     * @param returnOrder 退换货单
     * @param thresholdDate 阈值时间
     * @return true-是滞留件，false-不是滞留件
     */
    private boolean isDetainedItem(OcBReturnOrder returnOrder, Date thresholdDate) {
        // 1. 是原退单：isBack = 1
        if (returnOrder.getIsBack() == null || returnOrder.getIsBack() != 1) {
            log.debug("退换货单ID={}不是原退单，isBack={}", returnOrder.getId(), returnOrder.getIsBack());
            return false;
        }

        // 2. 签收状态为未签收：signingStatus = "0"
        if (!"0".equals(returnOrder.getSigningStatus())) {
            log.debug("退换货单ID={}签收状态不是未签收，signingStatus={}", returnOrder.getId(), returnOrder.getSigningStatus());
            return false;
        }

        // 3. 退签时间超过10天：withdrawalTime 不为空且距离当前时间超过10天
        if (returnOrder.getWithdrawalTime() == null) {
            log.debug("退换货单ID={}退签时间为空", returnOrder.getId());
            return false;
        }
        if (returnOrder.getWithdrawalTime().after(thresholdDate)) {
            log.debug("退换货单ID={}退签时间未超过{}天，withdrawalTime={}", 
                    returnOrder.getId(), DETAINED_DAYS_THRESHOLD, DATE_FORMAT.format(returnOrder.getWithdrawalTime()));
            return false;
        }

        // 4. 未入库状态：genericMark 字段为空 AND returnStatus 等于待入库状态值(20)
        if (StringUtils.isNotBlank(returnOrder.getGenericMark())) {
            log.debug("退换货单ID={}通用标记不为空，genericMark={}", returnOrder.getId(), returnOrder.getGenericMark());
            return false;
        }
        if (!ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnOrder.getReturnStatus())) {
            log.debug("退换货单ID={}状态不是待退货入库，returnStatus={}", returnOrder.getId(), returnOrder.getReturnStatus());
            return false;
        }

        log.info("退换货单ID={}符合滞留件条件", returnOrder.getId());
        return true;
    }

    /**
     * 标记为滞留件
     * 注意：处理 is_detained_item 字段可能不存在的情况
     *
     * @param detainedItems 滞留件列表
     */
    private void markAsDetainedItems(List<OcBReturnOrder> detainedItems) {
        if (CollectionUtils.isEmpty(detainedItems)) {
            return;
        }

        Date now = new Date();
        List<Long> successIds = new ArrayList<>();
        List<Long> failedIds = new ArrayList<>();

        for (OcBReturnOrder returnOrder : detainedItems) {
            try {
                // 尝试标记为滞留件，如果字段不存在则跳过但不影响后续流程
                OcBReturnOrder updateOrder = new OcBReturnOrder();
                updateOrder.setId(returnOrder.getId());
                updateOrder.setIsDetainedItem(1);
                updateOrder.setModifieddate(now);

                int updateCount = ocBReturnOrderMapper.updateById(updateOrder);
                if (updateCount > 0) {
                    successIds.add(returnOrder.getId());
                    log.info("成功标记退换货单ID={}为滞留件", returnOrder.getId());

                    // 添加日志
                    logService.addRefundOrderLog(returnOrder.getId(), "滞留件标记",
                            "系统自动标记为滞留件", SystemUserResource.getRootUser());
                } else {
                    log.warn("标记退换货单ID={}为滞留件失败，更新行数为0", returnOrder.getId());
                    failedIds.add(returnOrder.getId());
                }
            } catch (Exception e) {
                // 如果是字段不存在的异常，记录警告但不影响后续流程
                if (e.getMessage() != null && e.getMessage().toLowerCase().contains("column")
                    && e.getMessage().toLowerCase().contains("is_detained_item")) {
                    log.warn("is_detained_item字段不存在，跳过标记步骤，退换货单ID={}", returnOrder.getId());
                    failedIds.add(returnOrder.getId());
                } else {
                    log.error("标记退换货单ID={}为滞留件异常", returnOrder.getId(), e);
                    failedIds.add(returnOrder.getId());
                }
            }
        }

        log.info("滞留件标记结果：成功{}个，失败{}个", successIds.size(), failedIds.size());

        // 即使标记失败，也要继续后续的工单创建流程
        if (CollectionUtils.isNotEmpty(failedIds)) {
            log.info("部分退换货单标记失败，但将继续创建班牛工单，失败ID列表：{}", failedIds);
        }
    }

    /**
     * 为滞留件创建班牛工单
     *
     * @param detainedItems 滞留件列表
     */
    private void createBnTasksForDetainedItems(List<OcBReturnOrder> detainedItems) {
        if (CollectionUtils.isEmpty(detainedItems)) {
            return;
        }

        List<Map<String, String>> taskContents = new ArrayList<>();

        for (OcBReturnOrder returnOrder : detainedItems) {
            try {
                Map<String, String> content = buildDetainedItemTaskContent(returnOrder);
                if (content != null) {
                    taskContents.add(content);
                }
            } catch (Exception e) {
                log.error("构建滞留件工单内容异常，退换货单ID={}", returnOrder.getId(), e);
            }
        }

        if (CollectionUtils.isNotEmpty(taskContents)) {
            try {
                callBnApiForDetainedItems(taskContents);
            } catch (Exception e) {
                log.error("调用班牛API创建滞留件工单异常", e);
            }
        }
    }

    /**
     * 调用班牛API创建滞留件工单
     * TODO: 实现具体的班牛API调用逻辑
     *
     * @param taskContents 工单内容列表
     * @return 调用结果
     */
    private ValueHolderV14 callBnApiForDetainedItems(List<Map<String, String>> taskContents) {
        // TODO: 实现班牛API调用逻辑
        // 这里暂时返回成功结果，实际实现时需要调用具体的班牛API
        log.info("TODO: 调用班牛API创建滞留件工单，工单数量={}", taskContents.size());

        // 临时实现，实际使用时需要替换为真实的班牛API调用
        try {
            ValueHolderV14 result = ipRpcService.batchPushTask(taskContents, projectId, appId, null);
            return result;
        } catch (Exception e) {
            log.error("调用班牛API异常", e);
            return new ValueHolderV14(ResultCode.FAIL, "调用班牛API异常: " + e.getMessage());
        }
    }

    /**
     * 构建滞留件工单内容
     *
     * @param returnOrder 滞留件退换货单
     * @return 工单内容
     */
    private Map<String, String> buildDetainedItemTaskContent(OcBReturnOrder returnOrder) {
        if (returnOrder == null) {
            return null;
        }

        // 如果发货仓库为空则不处理
        if (returnOrder.getCpCPhyWarehouseId() == null) {
            log.info("滞留件退换货单ID={}发货仓库为空，不处理", returnOrder.getId());
            return null;
        }

        // 如果退货仓库为空则不处理
        if (returnOrder.getCpCPhyWarehouseInId() == null) {
            log.info("滞留件退换货单ID={}退货仓库为空，不处理", returnOrder.getId());
            return null;
        }

        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(returnOrder.getCpCPhyWarehouseId());
        CpCPhyWarehouse cpCPhyWarehouseIn = cpRpcService.queryByWarehouseId(returnOrder.getCpCPhyWarehouseInId());

        // 判断仓库信息是否为空
        if (cpCPhyWarehouse == null || cpCPhyWarehouseIn == null) {
            log.info("滞留件退换货单ID={}发货仓库或退货仓库信息为空，不处理", returnOrder.getId());
            return null;
        }

        Map<String, String> content = new HashMap<>();

        // 发货仓库
        content.put(BnDetainedItemColumnEnum.WAREHOUSE_OUT.getColumnId(), cpCPhyWarehouse.getEname());

        // 退货仓库
        content.put(BnDetainedItemColumnEnum.WAREHOUSE_IN.getColumnId(), cpCPhyWarehouseIn.getEname());

        // 快递公司
        if (StringUtils.isNotBlank(returnOrder.getCpCLogisticsEname())) {
            content.put(BnDetainedItemColumnEnum.LOGISTICS_COMPANY.getColumnId(), returnOrder.getCpCLogisticsEname());
        }

        // 快递单号
        if (StringUtils.isNotBlank(returnOrder.getLogisticsCode())) {
            content.put(BnDetainedItemColumnEnum.LOGISTICS_CODE.getColumnId(), returnOrder.getLogisticsCode());
        }

        // OMS创建人
        content.put(BnDetainedItemColumnEnum.OMS_CREATOR.getColumnId(), "系统管理员");

        // 创建时间
        content.put(BnDetainedItemColumnEnum.CREATE_TIME.getColumnId(), DATE_FORMAT.format(new Date()));
        log.info("构建滞留件工单内容完成，退换货单ID={}", returnOrder.getId());
        return content;
    }

    /**
     * 计算滞留天数
     *
     * @param withdrawalTime 退签时间
     * @return 滞留天数
     */
    private long calculateDetainedDays(Date withdrawalTime) {
        if (withdrawalTime == null) {
            return 0;
        }

        long diffInMillies = System.currentTimeMillis() - withdrawalTime.getTime();
        return diffInMillies / (24 * 60 * 60 * 1000);
    }
}
