package com.jackrain.nea.oc.oms.task.splitorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 自动回滚实缺订单标记任务
 *
 * @author: 胡林洋
 * @since: 2019/11/13
 * create at : 2019/11/13 15:05
 */
@Deprecated
@Slf4j
@Component
public class AutoRollBackRealShortageTagTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Override
    @XxlJob("AutoRollBackRealShortageTagTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug(this.getClass().getName() + "AutoRollBackRealShortageTagTask.execute方法开始执行===========");
        RunTaskResult result = new RunTaskResult();
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //订单自动拆单单次最大拉取记录数
            String fromDateStr = AdParamUtil.getParam("oms.oc.order.autoRollBack.realShortage.fromDate");
            Date fromDate = StringUtils.isNotEmpty(fromDateStr) ? sdf.parse(fromDateStr) : sdf.parse("2019-11-11 00:00:00");
            log.debug(this.getClass().getName() + "AutoRollBackRealShortageTagTask.execute方法内，fromDate为：" + fromDate);
            int count = ocBOrderMapper.updateRealShortageTag(fromDate);
            log.debug(this.getClass().getName() + "AutoRollBackRealShortageTagTask.updateRealShortageTag完毕，总条数count为：" + count);
            result.setSuccess(true);
            result.setMessage("AutoRollBackRealShortageTagTask,任务执行完毕!共计执行更新条数为：" + count);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("AutoRollBackRealShortageTagTask,处理过程发生异常！" + e);
            log.error("AutoRollBackRealShortageTagTask,处理过程发生异常！" + e);
        }
        return result;
    }

}