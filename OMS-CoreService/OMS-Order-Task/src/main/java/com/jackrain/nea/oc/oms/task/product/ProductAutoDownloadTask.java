package com.jackrain.nea.oc.oms.task.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.utils.DingDingUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * 定时下载商品
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProductAutoDownloadTask implements IR3Task {

    @Resource
    private IpRpcService ipRpcService;

    @Override
    @XxlJob("ProductAutoDownloadTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult result = new RunTaskResult();

        String shopIds = params.getString("shopIds");
        if (StringUtils.isBlank(shopIds)) {
            result.setSuccess(true);
            result.setMessage("参数为空");
            return result;
        }

        String[] ids = shopIds.split(",");

        boolean isSuccess = false;
        for (String id : ids) {
            Long shopId = Long.valueOf(id);
            try {
                LocalDateTime now = LocalDateTime.now();
                // 往前推1个小时
                LocalDateTime oneHourBefore = now.minusHours(1);
                ValueHolder valueHolder = ipRpcService.downloadPorduucts(shopId, oneHourBefore.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
                        now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                if (!valueHolder.isOK()) {
                    log.error(LogUtil.format("下载通用商品异常,shopId:{},valueHolder:{}", "downloadPorduucts"), shopId, JSON.toJSONString(valueHolder));
                    isSuccess = true;
                }
            } catch (Exception e) {
                log.error(LogUtil.format("下载通用商品异常,shopId:{}", "downloadPorduucts"), shopId, e);
                isSuccess = true;
            }
        }

        if (isSuccess) {
            result.setSuccess(false);
            result.setMessage("有异常店铺");
        } else {
            result.setSuccess(true);
            result.setMessage("success");
        }
        return result;
    }

}
