package com.jackrain.nea.oc.oms.task.sap;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordMapper;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordSumService;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordTaskService;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Description: 汇总销售数据记录表
 *
 * @Author: guo.kw
 * @Since: 2022/8/29
 * create at: 2022/8/29 10:32
 */
@Slf4j
@Component
public class OcBSapSalesDataRecordTask implements IR3Task {

    @Autowired
    private OcBSapSalesDataRecordMapper ocBSapSalesDataRecordMapper;
    @Autowired
    private OcBSapSalesDataRecordItemMapper ocBSapSalesDataRecordItemMapper;
    @Autowired
    private OcBSapSalesDataGatherMapper ocBSapSalesDataGatherMapper;

    @Autowired
    private OcBSapSalesDataRecordSumService ocBSapSalesDataRecordSumService;

    private static final String TABLE_NAME = "oc_b_sap_sales_data_record";

    @Autowired
    private OcBSapSalesDataRecordTaskService service;

    private final static String SAP_SALES_DATA_RECORD_SUN_XS = "OcBSapSalesDataRecordSumThreadTaskRedisKey";


    @Override
    @XxlJob("OcBSapSalesDataRecordTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult resultTask = new RunTaskResult();
        resultTask.setSuccess(Boolean.TRUE);
        resultTask.setMessage("success");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish OcBSapSalesDataRecordSumNkThreadTask execute start"));
        }
        CusRedisTemplate<Object, Object> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        try {
            if (strRedisTemplate.hasKey(SAP_SALES_DATA_RECORD_SUN_XS)) {
                log.info("OcBSapSalesDataRecordSumNkThreadTask RunTaskResult is running");
                resultTask.setMessage("RunTaskResult is running");
                return resultTask;
            } else {
                strRedisTemplate.opsForValue().set(SAP_SALES_DATA_RECORD_SUN_XS, SAP_SALES_DATA_RECORD_SUN_XS, 6L, TimeUnit.HOURS);
            }
            /*需要重试，所以这里*/
            String key = BllRedisKeyResources.buildSapSalesDataRecordTaskCompleteKey();
            strRedisTemplate.delete(key);

            ValueHolderV14 v14 = service.executeThread();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Finish OcBSapSalesDataRecordSumNkThreadTask.execute.result:{}"), JSONObject.toJSONString(v14));
            }
            if (!v14.isOK()) {
                resultTask.setSuccess(Boolean.FALSE);
                resultTask.setMessage(v14.getMessage());
            }

            /*执行完后才可以执行传SAP任务*/
            strRedisTemplate.opsForValue().set(key, key, 24L, TimeUnit.HOURS);

            strRedisTemplate.delete(SAP_SALES_DATA_RECORD_SUN_XS);
        } catch (Exception e) {
            log.error(LogUtil.format("Error OcBSapSalesDataRecordTask.execute"), Throwables.getStackTraceAsString(e));
            resultTask.setSuccess(Boolean.FALSE);
            resultTask.setMessage(e.getMessage());
            strRedisTemplate.delete(SAP_SALES_DATA_RECORD_SUN_XS);
        }
        return resultTask;
    }
}
