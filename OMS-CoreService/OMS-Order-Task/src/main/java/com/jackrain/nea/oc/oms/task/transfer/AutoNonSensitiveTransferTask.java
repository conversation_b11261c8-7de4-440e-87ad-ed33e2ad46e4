package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.es.ES4IpTaoBaoOrder;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.JingdongTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.StandPlatformTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.TaobaoTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJingdongOrderService;
import com.jackrain.nea.oc.oms.services.IpStandplatOrderService;
import com.jackrain.nea.oc.oms.services.IpTaobaoOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description:各个平台订单转换异常的补偿，主要是省市区匹配失败、商品数据匹配失败
 * 补偿间隔时间1小时以上
 * 需要按照修改时间升序排
 *
 * <AUTHOR> sunies
 * @since : 2020-11-15
 * create at : 2020-11-15 10:56
 */
@Component
@Slf4j
public class AutoNonSensitiveTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpTaobaoOrderService ipTaobaoOrderService;

    @Autowired
    private TaobaoTransferOrderProcessImpl taobaoTransferOrderProcess;

    @Autowired
    private IpJingdongOrderService ipJingdongOrderService;

    @Autowired
    private JingdongTransferOrderProcessImpl jingdongTransferOrderProcess;

    @Autowired
    private IpStandplatOrderService ipStandplatOrderService;

    @Autowired
    private StandPlatformTransferOrderProcessImpl standPlatformTransferOrderProcess;

    @Override
    @XxlJob("AutoNonSensitiveTransferTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoNonSensitiveTransferTask.range", DEFAULT_PAGE_SIZE);
            List<String> tidListOfTb = ES4IpTaoBaoOrder.selectTransferExceptionOrderFromEsOfTb(0, pageSize);
            List<String> tidListOfJd = ES4IpTaoBaoOrder.selectTransferExceptionOrderFromEsOfJd(0, pageSize);
            List<String> tidListOfStandard = ES4IpTaoBaoOrder.selectTransferExceptionOrderFromEsOfStandard(0, pageSize);

            /**
             *☆淘宝
             */
            List<IpTaobaoOrderRelation> ipTaobaoOrderRelations = Lists.newArrayList();
            for (String tid : tidListOfTb) {
                IpTaobaoOrderRelation taobaoOrderRelation = this.ipTaobaoOrderService.selectTaobaoOrderByTid(tid);
                if (taobaoOrderRelation == null) {
                    log.error(LogUtil.format("AutoTaobaoTransferTask.Order.NotExist!###OrderNo", tid));
                } else {
                    ipTaobaoOrderRelations.add(taobaoOrderRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.taobaoTransferOrderProcess, ipTaobaoOrderRelations);

            /**
             *☆京东
             */
            List<IpJingdongOrderRelation> ipJingdongOrderRelations = Lists.newArrayList();
            for (String tid : tidListOfJd) {
                IpJingdongOrderRelation jingdongOrderRelation = this.ipJingdongOrderService.selectJingdongOrder(tid);
                if (jingdongOrderRelation == null) {
                    log.error(LogUtil.format("AutoJingdongTransferTask.Order.NotExist!###OrderNo", tid));
                } else {
                    ipJingdongOrderRelations.add(jingdongOrderRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.jingdongTransferOrderProcess, ipJingdongOrderRelations);

            /**
             *☆通用
             */
            List<IpStandplatOrderRelation> ipStandPlatOrderRelations = Lists.newArrayList();
            for (String tid : tidListOfStandard) {
                IpStandplatOrderRelation standPlatOrderRelation = this.ipStandplatOrderService.selectStandplatOrder(tid);
                if (standPlatOrderRelation == null) {
                    log.error(LogUtil.format("AutoStandardTransferTask.Order.NotExist!###OrderNo", tid));
                } else {
                    ipStandPlatOrderRelations.add(standPlatOrderRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.standPlatformTransferOrderProcess, ipStandPlatOrderRelations);

            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoNonSensitiveTransferTask,异常信息:{}", "AutoNonSensitiveTransferTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}
