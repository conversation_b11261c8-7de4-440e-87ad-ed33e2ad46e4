package com.jackrain.nea.oc.oms.task.appointLogistics;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAppointLogistics;
import com.jackrain.nea.oc.oms.services.OcBOrderAppointLogisticsMapperService;
import com.jackrain.nea.oc.oms.services.OcBOrderAppointLogisticsService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/11/1 14:10
 * @Description 定时取消指定快递寻源（指定过且缺货的）
 * {"className":"com.jackrain.nea.oc.oms.task.appointLogistics.OrderCancelAppointLogisticsTask","type":"localJob"}
 */
@Slf4j
@Component
public class OrderCancelAppointLogisticsTask extends BaseR3Task implements IR3Task {

    @Resource
    private OmsOrderService omsOrderService;
    @Resource
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Resource
    private OcBOrderAppointLogisticsService ocBOrderAppointLogisticsService;
    @Autowired
    private OcBOrderAppointLogisticsMapperService ocBOrderAppointLogisticsMapperService;

    private final static String TABLE_NAME = "oc_b_order";

    @Override
    @XxlJob("OrderCancelAppointLogisticsTask")
    public RunTaskResult execute(JSONObject params) {

        RunTaskResult result = new RunTaskResult();
        try {
            List<Future<Boolean>> results = new ArrayList<>();
            List<Long> orderIdList = omsOrderService.selectOrderListByAppointLogistics(TABLE_NAME);
            if (CollectionUtils.isEmpty(orderIdList)) {
                result.setSuccess(true);
                result.setMessage("success");
                return result;
            }
            List<List<Long>> lists = ListSplitUtil.averageAssign(orderIdList, 24);
            for (List<Long> data : lists) {
                results.add(commonTaskExecutor.submit(
                        new OrderCancelAppointLogisticsTask.OrderCancelAppointLogisticsCallable(data)));
            }
            results.forEach(futureResult -> {
                try {
                    log.debug(LogUtil.format("OrderCancelAppointLogisticsTask------>线程结果:{}",
                            "OrderCancelAppointLogisticsTask"), futureResult.get().toString());
                } catch (Exception e) {
                    log.error(LogUtil.format("OrderCancelAppointLogisticsTask,异常：{}",
                            "OrderCancelAppointLogisticsTask"), Throwables.getStackTraceAsString(e));
                }
            });
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("OrderCancelAppointLogisticsTask,异常：{}",
                    "OrderCancelAppointLogisticsTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

    class OrderCancelAppointLogisticsCallable implements Callable<Boolean> {

        private final List<Long> data;

        public OrderCancelAppointLogisticsCallable(List<Long> data) {
            this.data = data;
        }

        @Override
        public Boolean call() {
            int pointsDataLimit = Tools.getInt(AdParamUtil.getParam("oms.oc.order.cancel.logistics.num"), 200);
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            List<OcBOrderAppointLogistics> ocBOrderAppointLogisticsList = ocBOrderAppointLogisticsMapperService.selectByOrderIds(data);
            if (CollectionUtils.isNotEmpty(ocBOrderAppointLogisticsList)) {
                // 过滤去除订单id
                data.removeAll(ocBOrderAppointLogisticsList.stream().map(OcBOrderAppointLogistics::getOrderId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }

            List<List<Long>> allList = Lists.partition(data, pointsDataLimit);
            for (List<Long> ids : allList) {
                ValueHolderV14<Void> v14 =
                        ocBOrderAppointLogisticsService.cancelAppointLogistics(ids, SystemUserResource.getRootUser(), true);
                log.info(LogUtil.format("OrderCancelAppointLogisticsTask.OrderCancelAppointLogisticsCallable message:{}",
                        "OrderCancelAppointLogisticsTask.OrderCancelAppointLogisticsCallable"), v14.getMessage());
            }
            return true;
        }
    }
}
