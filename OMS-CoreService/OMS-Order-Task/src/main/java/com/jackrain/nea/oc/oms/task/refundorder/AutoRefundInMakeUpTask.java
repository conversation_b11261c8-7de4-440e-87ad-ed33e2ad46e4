package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.enums.OcBRefundInTaskStatusEnum;
import com.jackrain.nea.oc.oms.services.RefundInMakeUpService;
import com.jackrain.nea.oc.oms.services.task.OcBRefundInTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @author: chenxiulou
 * @description: 退货入库单 掉SG新增入库通知单失败补偿任务
 * @since: 2019-09-25
 * create at : 2019-09-25 9:55
 */
@Slf4j
@Component
public class AutoRefundInMakeUpTask extends BaseR3Task implements IR3Task {
    @Autowired
    private RefundInMakeUpService refundInService;

    @Override
    @XxlJob("AutoRefundInMakeUpTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("AutoRefundInCreateNoticeTask begin :{}",  "AutoRefundInMakeUpTask"));
        }
        String redisKeyPrefix = OcBRefundInTaskService.REDIS_KEY_PREFIX;
        String pageSizeStr = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:" + redisKeyPrefix + "pageSize");
        String countStr = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:" + redisKeyPrefix + "count");
        String statusStr = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:" + redisKeyPrefix + "status");
        Integer pageSize = Tools.getInt(pageSizeStr, 200);
        Integer count = Tools.getInt(countStr, 5);
        statusStr = Optional.ofNullable(statusStr).orElse(OcBRefundInTaskStatusEnum.INIT.getVal()
                + "," + OcBRefundInTaskStatusEnum.HANDLE_FAIL.getVal() + ","
                + OcBRefundInTaskStatusEnum.RESOLVE_FAIL.getVal());
        try {
            refundInService.refundInCreateInNoticeMakeUp(pageSize, count, statusStr);
            result.setSuccess(true);
            result.setMessage("ok");
        } catch (Exception ex) {
            //发生异常 所有数据返回最开始的状态
            log.error(LogUtil.format("AutoRefundInCreateNoticeTask.Execute Error,异常：{}", "AutoRefundInCreateNoticeTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

}
