package com.jackrain.nea.oc.oms.task.makeup;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.ip.model.OrderFullLinkModel;
import com.jackrain.nea.ip.model.result.OrderFullLinkResult;
import com.jackrain.nea.oc.oms.es.ES4OrderLink;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLink;
import com.jackrain.nea.oc.oms.services.OmsOrderSyncFullLogService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 自动补偿失败全链路日志平台数据
 *
 * @author: heliu
 * @since: 2019/4/10
 * create at : 2019/4/10 18:37
 */
@Slf4j
@Component
@Deprecated
public class AutoOrderFullLinkLogTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OmsOrderSyncFullLogService omsOrderSyncFullLogService;

    @Override
    @XxlJob("AutoOrderFullLinkLogTask")
    public RunTaskResult execute(JSONObject params) {

        RunTaskResult result = new RunTaskResult();
        try {
            RuntimeCompute runtimeCompute = new RuntimeCompute();
            runtimeCompute.startRuntime();
            //查询等待补偿的全链路日志数据
            //全链路日志的失败的orderIDs
            List<Long> orderIds = ES4OrderLink.findOrderIdBySyncStatusOrderByCreation("0", 0, DEFAULT_PAGE_SIZE);
            //去重
            List<OcBOrderLink> ocBOrderLinks = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderIds)) {
                Set<Long> longSet = new HashSet<>(orderIds);
                ocBOrderLinks = omsOrderSyncFullLogService.selectFullLinkList(longSet);
            } else {
                return result;
            }
            //查询失败的全链路日志信息
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("自动补偿任务全链路日志query数据总条数:{}"), ocBOrderLinks.size());
            }
            //设置调用参数--批量设置
            List<OrderFullLinkModel> linkResultList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ocBOrderLinks)) {
                for (OcBOrderLink ocBOrderLink : ocBOrderLinks) {
                    //同步日志信息到平台
                    OrderFullLinkModel orderFullLinkModel = new OrderFullLinkModel();
                    //设置平台单号
                    orderFullLinkModel.setTid(ocBOrderLink.getTid());
                    //链路主表 ocBOrderLinkId
                    orderFullLinkModel.setId(ocBOrderLink.getId());
                    //分库建
                    orderFullLinkModel.setOcBOrderId(ocBOrderLink.getOcBOrderId());
                    //设置卖家昵称
                    orderFullLinkModel.setSellerNick(ocBOrderLink.getSellerNick());
                    //设置同步状态
                    orderFullLinkModel.setStatus(ocBOrderLink.getBackflowStatus());
                    //组装参数
                    linkResultList.add(orderFullLinkModel);
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("自动补偿任务开始同步全链路日志平台接口入参:{}"), linkResultList);
                }
                //传参给接口平台获取接口返回结果进行解析
                ValueHolderV14<List<OrderFullLinkResult>> valueHolderV14 = ipRpcService.orderFullLink(linkResultList, SystemUserResource.getRootUser());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("自动补偿任务开始同步全链路日志平台接口出参:{}"),  valueHolderV14.toJSONObject());
                }
                if (valueHolderV14.isOK()) {
                    List<OrderFullLinkResult> orderFullLinkResultList = valueHolderV14.getData();
                    for (OrderFullLinkResult orderFullLinkResult : orderFullLinkResultList) {
                        Long id = orderFullLinkResult.getId();
                        Long orderId = orderFullLinkResult.getOcBOrderId();
                        if ("true".equalsIgnoreCase(orderFullLinkResult.getIsSend())) {
                            log.debug(LogUtil.format("订单OrderId链路日志自动补偿更新成功:{}",orderId), orderId);
                            OcBOrderLink orderLink = new OcBOrderLink();
                            orderLink.setId(id);
                            orderLink.setOcBOrderId(orderId);
                            omsOrderSyncFullLogService.updateMakeupOrderStatusSuccess(orderLink);

                        }
                    }
                }
            }
            double usedTime = runtimeCompute.endRuntime();
            log.debug(LogUtil.format("自动补偿任务开始同步全链路日志执行成功!UsedTime=:{}"), usedTime);
            result.setSuccess(true);
            result.setMessage("自动补偿任务开始同步全链路日志执行成功!UsedTime=" + usedTime);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("AutoOrderFullLinkLogTask.Execute.异常: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
