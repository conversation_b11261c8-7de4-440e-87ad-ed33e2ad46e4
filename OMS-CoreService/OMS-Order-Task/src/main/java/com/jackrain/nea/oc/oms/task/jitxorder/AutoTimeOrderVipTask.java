package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.config.VipcomTimeOrderConfing;
import com.jackrain.nea.oc.oms.es.ES4IpTimeOrderVip;
import com.jackrain.nea.oc.oms.model.request.TimeOrderVoidSgSendRequest;
import com.jackrain.nea.oc.oms.services.TimeOrderVoidSgSendService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 唯品会时效订单24小时取消占单服务
 * 1、查询ES中creationdate>24小时的单据
 *
 * @author: huang.zaizai
 * @since: 2019-08-19
 * create at : 2019-08-19 11:10
 */
@Component
@Slf4j
public class AutoTimeOrderVipTask extends BaseR3Task implements IR3Task {

    @Autowired
    private TimeOrderVoidSgSendService timeOrderVoidSgSendService;
    @Autowired
    private VipcomTimeOrderConfing vipcomTimeOrderConfing;

    @Override
    @XxlJob("AutoTimeOrderVipTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        result.setMessage("超过24取消时效单任务成功");
        try {

            int timeOrderReleaseStockInterval = vipcomTimeOrderConfing.getTimeOrderReleaseStockInterval();
            List<String> orderNoList = ES4IpTimeOrderVip.selectOvertimeFromEs(0, 1000, timeOrderReleaseStockInterval);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoTimeOrderVipTask.OrderNoList={}"), JSONObject.toJSONString(orderNoList));
            }

            if (CollectionUtils.isEmpty(orderNoList)) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("超过24小时自动释放时效订单，当前Task查询时效订单为空"));
                    result.setSuccess(false);
                    result.setMessage("当前Task查询时效为空");
                }
            }

            List<String> collect = orderNoList.stream().distinct().collect(Collectors.toList());

            TimeOrderVoidSgSendRequest request = new TimeOrderVoidSgSendRequest();
            request.setUser(SystemUserResource.getRootUser());
            request.setRemark("超过24小时自动释放时效订单！");
            request.setIsCancel(true);
            for (String orderNo : collect) {
                request.setOccupiedOrderSn(orderNo);
                ValueHolderV14 vh = timeOrderVoidSgSendService.voidSgSendV14(request);
                if (!vh.isOK()) {
                    String errorMessage = Resources.getMessage("###AutoJitxFeedBackTask.Order.NotExist!###OrderNo="
                            + orderNo);
                    log.error(LogUtil.format(errorMessage));
                }
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("AutoTimeOrderVipTask.Execute.Error: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
