package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.es.ES4IpJitXOrder;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.services.IpJitxOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since: 2020/10/26
 * create at : 2020/10/26 2:54 下午
 */
@Component
@Slf4j
public class AutoJitxMqTransferTask extends BaseR3Task implements IR3Task {

//    @Autowired
//    private R3MqSendHelper sendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private TransferOrderMqConfig orderMqConfig;

    @Autowired
    private IpJitxOrderService ipJitxOrderService;

    @Override
    @XxlJob("AutoJitxMqTransferTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        result.setSuccess(true);
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        //默认查询总条数
        Integer totalCount = config.getProperty("lts.AutoJitxMqTransferTask.totalCount", DEFAULT_PAGE_SIZE);
        //默认发转单mq消息每批次处理数量
        Integer batchSize = config.getProperty("lts.AutoJitxMqTransferTask.batch.size", DEFAULT_PAGE_SIZE);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("AutoJitxMqTransferTask,start",  "AutoJitxMqTransferTask"));
        }
        try {
            // 从ES中查询未转换成功的单据信息
            List<String> orderNoList = ES4IpJitXOrder
                    .findOrderSnByTransStatusAndOrderStatus(0, totalCount, JitxOrderStatus.ORDER_ALREADY_AUDITED);
            if (CollectionUtils.isEmpty(orderNoList)) {
                log.warn(LogUtil.format("AutoJitxMqTransferTask",  "未查到转单数据"));
                return result;
            }
            // 将查询出来的数据根据指定大小切割成多个集合，分多批次处理
            User user = SystemUserResource.getRootUser();
            List<List<String>> lists = ListSplitUtil.splitList(orderNoList, batchSize);
            lists.forEach(orderSnList -> {
                List<IpJitxOrderRelation> orderRelationList = ipJitxOrderService.batchSelectJitxOrder(orderSnList,
                        TransferOrderStatus.NOT_TRANSFER);
                if (CollectionUtils.isEmpty(orderRelationList)) {
                    throw new NDSException("AutoJitxMqTransferTask batchSelectJitxOrder is null");
                }
                List<String> transferingOrderIdList =
                        orderRelationList.stream().map(IpJitxOrderRelation::getOrderNo).collect(Collectors.toList());
                //ipJitxOrderService.batchUpdateJitxOrderTransStatus(transferingOrderIdList, TransferOrderStatus
                // .TRANSFERRING, user, "发送mq任务变更转换中");
                //发送mq通知转单
                sendMQ(orderRelationList, transferingOrderIdList);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("jitxOrder send mq for transfer ，订单号.{}",  "AutoJitxMqTransferTask"), transferingOrderIdList);
                }
            });
        } catch (Exception e) {
            log.error(LogUtil.format("AutoJitxMqTransferTask.Execute Error,异常信息:{}", "AutoJitxMqTransferTask"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }

    private void sendMQ(List<IpJitxOrderRelation> orderRelationList, List<String> transferingOrderIdList) {
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        orderRelationList.forEach(orderRelation -> {
            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
            orderMqInfo.setChannelType(ChannelType.VIPJITX);
            orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
            orderMqInfo.setOrderId(orderRelation.getOrderId());
            orderMqInfo.setOrderNo(orderRelation.getOrderNo());
            mqInfoList.add(orderMqInfo);
        });
        String jsonValue = JSONObject.toJSONString(mqInfoList);
        try {
//            sendHelper.sendMessage(jsonValue, orderMqConfig.getSendTransferMqTopic(),
//                    orderMqConfig.getSendTransferMqTag());
            defaultProducerSend.sendTopic(MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER, MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER, jsonValue, null);
        } catch (Exception e) {
            log.error(LogUtil.format("AutoJitxMqTransferTask send mq fail ,orderNoList.{}", "AutoJitxMqTransferTask"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }

}
