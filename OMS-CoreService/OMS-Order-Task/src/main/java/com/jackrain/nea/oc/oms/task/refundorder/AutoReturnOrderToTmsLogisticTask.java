package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.oc.oms.services.ReturnOrderToTmsLogisticService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: ldh
 * @Date: 2021/12/08 11:09 PM
 * @Version 1.0
 * 退换货单推送TMS追踪物流信息任务
 */
@Slf4j
@Component
public class AutoReturnOrderToTmsLogisticTask extends BaseR3Task implements IR3Task {

    @Autowired
    private ReturnOrderToTmsLogisticService tmsLogisticService;

    @Override
    @XxlJob("AutoReturnOrderToTmsLogisticTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        if (log.isDebugEnabled()) {
            log.debug(" 退换货单推送TMS物流信息任务");
        }
        long starTime = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {

            tmsLogisticService.sendTraceInfo(params);

            result.setSuccess(true);
            if (log.isDebugEnabled()) {
                log.debug(" 退换货单推送TMS物流信息任务耗时:{}ms", System.currentTimeMillis() - starTime);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(" 退换货单推送TMS物流信息任务执行失败{}", ex.getMessage());
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;

    }
}
