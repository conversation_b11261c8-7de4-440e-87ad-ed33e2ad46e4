package com.jackrain.nea.oc.oms.task.orderwms;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sg.service.SgOutStockNoticeService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/9/11 2:03 下午
 * @Version 1.0
 * 订单传wms补偿任务
 */
@Slf4j
@Component
public class AutoOrderWmsCompensateTask extends BaseR3Task implements IR3Task {
    @Autowired
    private OmsOrderService omsOrderervice;
    @Autowired
    private SgOutStockNoticeService sgOutStockNoticeService;


    @Override
    @XxlJob("AutoOrderWmsCompensateTask")
    public RunTaskResult execute(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进入订单传wms定时任务",  "订单传wms定时任务"));
        }
        long starTime = System.currentTimeMillis();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer pageSize = config.getProperty("lts.AutoOrderWmsTask.range", 1000);
        RunTaskResult result = new RunTaskResult();
        try {

            List<Long> list = ES4Order.findIdByOrderStatusAndAuditTime(0, pageSize);

            long endTime1 = System.currentTimeMillis();
            this.spitData(list);

            result.setSuccess(true);

            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单传wms定时任务耗时:{}ms",  "订单传wms定时任务"), Time);
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("订单传wms任务执行失败,异常！{}",  "订单传wms任务执行失败"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }


    private void spitData(List<Long> list) {
        List<Long> newList = new ArrayList<>();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer pointsDataLimit = config.getProperty("lts.AutoOrderWmsTask.push.num", 400);
        User rootUser = SystemUserResource.getRootUser();
        //分批次处理
        for (int i = 0; i < list.size(); i++) {
            try {
                newList.add(list.get(i));
                if (pointsDataLimit == newList.size() || i == list.size() - 1) {
                    long starTime = System.currentTimeMillis();
                    if (CollectionUtils.isNotEmpty(newList)) {
                        sgOutStockNoticeService.wmsCompensate(rootUser, newList);
                    }
                    long endTime = System.currentTimeMillis();
                    log.debug(LogUtil.format("订单传wms完成耗时:{}ms,一共:{}条",  "订单传wms定时任务"), endTime - starTime, newList.size());
                    newList.clear();
                }
            } catch (Exception e) {
                log.error(LogUtil.format("订单传wms处理失败！{}",  "订单传wms定时任务"), Throwables.getStackTraceAsString(e));
            }
        }
    }
}
