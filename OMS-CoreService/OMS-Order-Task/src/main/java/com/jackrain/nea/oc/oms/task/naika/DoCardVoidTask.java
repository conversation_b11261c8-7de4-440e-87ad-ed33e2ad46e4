package com.jackrain.nea.oc.oms.task.naika;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.request.naika.NaiKaReissueRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaikaVoidMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
import com.jackrain.nea.oc.oms.model.enums.NaikaVoidStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @ClassName DoCardVoidTask
 * @Description 执行奶卡自动作废
 * <AUTHOR>
 * @Date 2023/3/6 17:55
 * @Version 1.0
 */
@Component
@Slf4j
public class DoCardVoidTask extends BaseR3Task implements IR3Task {

    @Autowired
    private ThreadPoolTaskExecutor doCardVoidPollExecutor;
    @Autowired
    private OcBOrderNaikaVoidMapper naikaVoidMapper;
    @Autowired
    private OcBOrderNaiKaMapper naiKaMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBReturnAfSendMapper returnAfSendMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private ApplicationContext applicationContext;

    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;

    @Override
    @XxlJob("DoCardVoidTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult resultTask = new RunTaskResult();
        resultTask.setSuccess(Boolean.TRUE);
        resultTask.setMessage("success");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("CardAutoVoidStatusTask execute start"));
        }

        long start = System.currentTimeMillis();
        final String taskTableName = "oc_b_order_naika_void";
        List<Future<Boolean>> results = new ArrayList<>();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int pageSize = config.getProperty("lts.DoCardVoidTask.range", 2400);
        List<OcBOrderNaikaVoid> ocBOrderNaikaVoidList = naikaVoidMapper.selectCardAutoVoid(pageSize, taskTableName);
        List<List<OcBOrderNaikaVoid>> lists = ListSplitUtil.averageAssign(ocBOrderNaikaVoidList, 24);

        for (List<OcBOrderNaikaVoid> data : lists) {
            results.add(doCardVoidPollExecutor.submit(new DoCardVoidTask.CardVoidTaskWithResult(data)));
        }
        //线程执行结果获取
        for (Future<Boolean> futureResult : results) {
            try {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("DoCardVoidTask------>线程结果:{}"), futureResult.get().toString());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("DoCardVoidTask多线程获取InterruptedException异常：{}", "DoCardVoidTask"), Throwables.getStackTraceAsString(e));
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("DoCardVoidTask 奶卡自动作废 useTime : {}"), (System.currentTimeMillis() - start));
        }

        return resultTask;
    }

    @Transactional(rollbackFor = Exception.class)
    public void doFail(List<Long> cardIdList, OcBOrderNaikaVoid naikaVoid) {
        User user = SystemUserResource.getRootUser();
        OcBOrderNaikaVoid updateNaikaVoid = new OcBOrderNaikaVoid();

        // 作废失败后 修改奶卡表数据
        naiKaMapper.updateNaiKaStatusByIdList(cardIdList, OmsOrderNaiKaStatusEnum.VOID_FAILED.getStatus());

        // 修改作废订单、作废状态为 作废失败
        updateNaikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID_FAIL.getStatus());
        updateNaikaVoid.setId(naikaVoid.getId());
        updateNaikaVoid.setModifieddate(new Date());
        updateNaikaVoid.setVoidTimes(naikaVoid.getVoidTimes() + 1);
        naikaVoidMapper.updateById(updateNaikaVoid);

        // 修改已发货退款单（如果存在的话）
        if (naikaVoid.getOcBReturnAfSendId() != null) {
            OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
            updateOcBReturnAfSend.setId(naikaVoid.getOcBReturnAfSendId());
            updateOcBReturnAfSend.setModifierid(Long.valueOf(user.getId()));
            updateOcBReturnAfSend.setModifieddate(new Date());
            updateOcBReturnAfSend.setModifiername(user.getName());
            updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_ERROR.getCode());
            returnAfSendMapper.updateById(updateOcBReturnAfSend);

            log.info("奶卡作废失败，同时更新已发货退款单，作废记录ID={}，退款单ID={}",
                    naikaVoid.getId(), naikaVoid.getOcBReturnAfSendId());
        } else {
            log.info("奶卡作废失败，无关联的已发货退款单，作废记录ID={}", naikaVoid.getId());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doSuccess(List<Long> cardIdList, OcBOrderNaikaVoid naikaVoid) {
        User user = SystemUserResource.getRootUser();
        OcBOrderNaikaVoid updateNaikaVoid = new OcBOrderNaikaVoid();

        // 作废成功后 修改奶卡表数据
        naiKaMapper.updateNaiKaStatusByIdList(cardIdList, OmsOrderNaiKaStatusEnum.VOID_SUCCESS.getStatus());

        // 修改作废订单、作废状态为 作废成功
        updateNaikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID_SUCCESS.getStatus());
        updateNaikaVoid.setId(naikaVoid.getId());
        updateNaikaVoid.setModifieddate(new Date());
        naikaVoidMapper.updateById(updateNaikaVoid);

        // 修改已发货退款单（如果存在的话）
        if (naikaVoid.getOcBReturnAfSendId() != null) {
            OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
            updateOcBReturnAfSend.setId(naikaVoid.getOcBReturnAfSendId());
            updateOcBReturnAfSend.setModifierid(Long.valueOf(user.getId()));
            updateOcBReturnAfSend.setModifieddate(new Date());
            updateOcBReturnAfSend.setModifiername(user.getName());
            updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.VOID_SUCCESS.getCode());
            returnAfSendMapper.updateById(updateOcBReturnAfSend);

            log.info("奶卡作废成功，同时更新已发货退款单，作废记录ID={}，退款单ID={}",
                    naikaVoid.getId(), naikaVoid.getOcBReturnAfSendId());
        } else {
            log.info("奶卡作废成功，无关联的已发货退款单，作废记录ID={}", naikaVoid.getId());
        }
    }

    class CardVoidTaskWithResult implements Callable<Boolean> {

        private final List<OcBOrderNaikaVoid> data;

        public CardVoidTaskWithResult(List<OcBOrderNaikaVoid> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            for (OcBOrderNaikaVoid naikaVoid : data) {
                // 根据奶卡作废表数据 执行奶卡作废
                List<OcBOrderNaiKa> ocBOrderNaiKaList = naiKaMapper.selectToVoidNaiKaByOcBOrderIdAndItemId(naikaVoid.getOcBOrderId(), naikaVoid.getOcBOrderItemId());
                if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
                    return true;
                }
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(naikaVoid.getOcBOrderId());
                CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(ocBOrder.getPlatform()));
                List<String> cardCodeList = ocBOrderNaiKaList.stream().map(OcBOrderNaiKa::getCardCode).collect(Collectors.toList());
                List<Long> cardIdList = ocBOrderNaiKaList.stream().map(OcBOrderNaiKa::getId).collect(Collectors.toList());

                NaiKaReissueRequest request = new NaiKaReissueRequest();
                request.setBillNo(ocBOrder.getBillNo());
                request.setSourceCode(ocBOrder.getSourceCode());
                request.setPlatformCode(cpCPlatform.getEcode());
                // 目前此字段无任何含义
                request.setType(1);
                request.setRemark("奶卡作废");
                request.setCardList(cardCodeList);
                request.setShopCode(ocBOrder.getCpCShopEcode());
                ValueHolderV14 valueHolderV14;
                Boolean success = true;
                try {
                    valueHolderV14 = naiKaOrderCmd.orderReissue(request);
                    if (!valueHolderV14.isOK()) {
                        success = false;
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("DoCardVoidTask作废失败：{}", "DoCardVoidTask"), Throwables.getStackTraceAsString(e));
                    success = false;
                }
                if (success) {
                    doSuccess(cardIdList, naikaVoid);
                } else {
                    doFail(cardIdList, naikaVoid);
                }

            }
            return true;
        }
    }
}
