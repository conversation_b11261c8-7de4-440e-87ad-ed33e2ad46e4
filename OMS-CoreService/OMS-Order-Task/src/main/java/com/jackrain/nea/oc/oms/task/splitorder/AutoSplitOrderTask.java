package com.jackrain.nea.oc.oms.task.splitorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.config.AutoSplitOrderMqConfig;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.AutoSplitStatus;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OmsOrderAutoSplitService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.retail.service.RetailNotifyService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.util.ValueHolder;
import com.xxl.job.core.handler.annotation.XxlJob;
import constant.MqConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 自动拆单定时任务
 *
 * @author: 胡林洋
 * @since: 2019/3/27
 * create at : 2019/3/27 15:05
 */
@Deprecated
@Slf4j
@Component
public class AutoSplitOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsOrderService orderService;

//    @Autowired
//    private R3MqSendHelper sendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private AutoSplitOrderMqConfig autoSplitOrderMqConfig;

    @Autowired
    private OmsOrderAutoSplitService omsOrderAutoSplitService;

    @Autowired
    private OmsOrderAutoSplitService orderAutoSplitService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private RetailNotifyService retailNotifyService;

    /**
     * 默认每次查询页数信息
     */
    protected static final int DEFAULT_PAGE_SIZE = 10000;

    @Override
    @XxlJob("AutoSplitOrderTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug(this.getClass().getName() + "AutoSplitOrderTask.execute方法开始执行===========");
        RunTaskResult result = new RunTaskResult();
        try {
            log.debug(this.getClass().getName() + "omsOrderAutoSplitService.selectWaitAutoSplitOrder方法开始执行,开始查询等待自动拆分的订单===========");
            //订单自动拆单单次最大拉取记录数
            String pullNum = AdParamUtil.getParam("oms.oc.order.autoSplit.pull.num");
            int pullNumber = StringUtils.isNotEmpty(pullNum) ? Integer.parseInt(pullNum) : DEFAULT_PAGE_SIZE;
            //查询所有的店铺策略，加载到map中
            Map<Long, String> shopStrategyMap = new HashMap<>();
            shopStrategyMap = orderAutoSplitService.loadShopStrategyToMap();
            //查询所有的店铺库存同步策略缓存到map中
            Map<Long, List<Long>> shopSyncStockStrategyMap = new HashMap<>();
            shopSyncStockStrategyMap = orderAutoSplitService.loadshopSyncStockStrategyToMap();
            //从ES中捞取数据，默认10000条数据，如果有配置的话，去配置里的最大条数,ES中按照创建时间排序
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            int splitNum = config.getProperty("oms.oc.order.autoSplit.split.num", 4);
            List<Long> orderIdTmpList = ES4Order
                    .findIdByOrderStatusAndIsInterceptAndIsSplit(0, pullNumber, splitNum);
            log.debug(this.getClass().getName() + "omsOrderAutoSplitService.selectWaitAutoSplitOrder方法,查询结果为：===========" + orderIdTmpList);
            //List<OcBOrder> orderInfoList = omsOrderAutoSplitService.getIdsByOrderInfoList(orderIdList);
            //分割捞取到的数据，默认200每批，如果有配置参数就去配置的参数，并根据店铺策略和店铺同步库存策略过滤
            splitDataList(orderIdTmpList, shopStrategyMap, shopSyncStockStrategyMap);
            result.setSuccess(true);
            result.setMessage("AutoSplitOrderTask,组装需要处理的订单SUCCESS，mq已发出");
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("AutoSplitOrderTask,组装需要处理的订单Fail,处理过程发生异常！" + e);
            log.error("AutoSplitOrderTask,组装需要处理的订单Fail,处理过程发生异常！" + e);
        }
        return result;
    }


    /**
     * 分割订单列表，默认200单，有配置取配置参数
     *
     * @param orderIdList     数据集合
     * @param shopStrategyMap
     */
    public void splitDataList(List<Long> orderIdList, Map<Long, String> shopStrategyMap, Map<Long, List<Long>> shopSyncStockStrategyMap) {
        log.debug("splitDataList方法内,orderIdList的值为：" + orderIdList + "shopStrategyMap的值为：" + shopStrategyMap + "shopSyncStockStrategyMap的值为：" + shopSyncStockStrategyMap);
        //订单自动拆单单次最大处理数 默认200单
        int pointsDataLimit = Tools.getInt(AdParamUtil.getParam("oms.oc.order.autoSplit.push.num"), 200);
        log.debug("oms.oc.order.autoSplit.push.num" + pointsDataLimit);
        List<Long> newList = new ArrayList<>();
        //分批次处理
        for (int i = 0; i < orderIdList.size(); i++) {
            List<OcBOrder> dataList = new ArrayList<>();
            newList.add(orderIdList.get(i));
            if (pointsDataLimit == newList.size() || i == orderIdList.size() - 1) {
                log.debug("splitDataList方法内,newList的值为：" + newList);
                dataList = ocBOrderMapper.queryShortageOrderInfo(newList);
                log.debug("splitDataList方法内,dataList的值为：" + dataList);
                printSplitDataList(dataList, shopStrategyMap, shopSyncStockStrategyMap);
                newList.clear();
                dataList.clear();
                log.debug("自动拆单任务-----------分批发送数据~~");
            }
        }
    }

    /**
     * 打印list然后依次发送Mq
     *
     * @param dataList        数据集合
     * @param shopStrategyMap
     */
    public void printSplitDataList(List<OcBOrder> dataList, Map<Long, String> shopStrategyMap, Map<Long, List<Long>> shopSyncStockStrategyMap) {
        //List<OcBOrder> dataList = new ArrayList<>();
        log.debug(this.getClass().getName() + "printSplitDataList方法内，dataList为：" + dataList + "shopStrategyMap为：" + shopStrategyMap + "shopSyncStockStrategyMap为：" + shopSyncStockStrategyMap);
        //数据库更新字段List
        List<Long> tranSplitOrderIds = new ArrayList<>();
        List<Long> repeatSplitOrderIds = new ArrayList<>();
        List<Long> orderIds = new ArrayList<>();
        //更新ES状态得list
        List<OcBOrder> newOrderlist = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < dataList.size(); i++) {
            OcBOrder ocBOrderDto = dataList.get(i);
            if (ocBOrderDto != null) {
                if (ocBOrderDto.getCpCShopId() != null) {
                    if (shopStrategyMap.containsKey(ocBOrderDto.getCpCShopId())) {
                        String tmp = shopStrategyMap.get(ocBOrderDto.getCpCShopId());
                        String[] tmpArray = tmp.split("-");
                        String isAutoSplitStr = tmpArray[0];
                        if ("Y".equals(isAutoSplitStr)) {
                            //获取订单店铺下逻辑仓的列表
                            List<Long> stCSyncStockStrategyIdList = shopSyncStockStrategyMap.get(ocBOrderDto.getCpCShopId());
                            if (CollectionUtils.isEmpty(stCSyncStockStrategyIdList)) {
                                log.debug(this.getClass().getName() + "订单Id为：" + ocBOrderDto.getId() + "店铺为：" + ocBOrderDto.getCpCShopId() + "订单下的店铺策略逻辑仓配置为空，自动拆单执行失败! ");
                                break;
                            } else {
                                //判断对象是否有效
                                if (sb.length() > 0) {
                                    sb.append(",");
                                }
                                sb.append(ocBOrderDto.getId());
                                log.debug(this.getClass().getName() + "printSplitDataList方法内，开始将订单：" + ocBOrderDto.getId() + " 改为拆单中");
                                //将是否拆单状态，改为“拆单中”
                                if (ocBOrderDto.getIsSplit() == AutoSplitStatus.UN_SPLIT.toInteger()) {
                                    ocBOrderDto.setIsSplit(AutoSplitStatus.TRAN_SPLIT.toInteger());
                                    tranSplitOrderIds.add(ocBOrderDto.getId());
                                } else if (ocBOrderDto.getIsSplit() == AutoSplitStatus.ALREADY_SPLIT.toInteger()) {
                                    ocBOrderDto.setIsSplit(AutoSplitStatus.REPEAT_SPLIT.toInteger());
                                    repeatSplitOrderIds.add(ocBOrderDto.getId());
                                }
                                newOrderlist.add(ocBOrderDto);
                            }
                        } else {
                            log.debug(this.getClass().getName() + "订单Id为：" + ocBOrderDto.getId() + "店铺为：" + ocBOrderDto.getCpCShopId() + "店铺策略是否开启自动拆单为否，订单缺货拆单执行结束! ");
                            //如果平台是线下pos，而且缺货，发消息通知线下pos
                            if (ocBOrderDto.getPlatform() != null && Objects.equals(ocBOrderDto.getPlatform(),
                                    PlatFormEnum.POS.getCode())) {
                                OcBOrderRelation orderRelation = orderService.selectOmsOrderInfo(ocBOrderDto.getId());
                                //List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
                                //List<OcBOrderItem> ocBOrderItems = orderItemList.stream().filter(item -> item.getQtyLost().compareTo(new BigDecimal(0)) > 0).collect(Collectors.toList());
                                //orderRelation.setOrderItemList(ocBOrderItems);
                                ValueHolder valueHolder = retailNotifyService.notifyRetailByMq(orderRelation, 0);
                                log.info("AutoSplitOrderTask,订单orderId:" + ocBOrderDto.getId() + ",缺货通知线下pos结果:" + valueHolder.toJSONObject());
                            }
                        }
                    }
                }
            }
        }
        log.debug("未拆单过的订单，开始拆单list集合--> " + tranSplitOrderIds);
        orderIds.addAll(tranSplitOrderIds);
        log.debug("已拆单过的订单，再拆单list集合--> " + repeatSplitOrderIds);
        orderIds.addAll(repeatSplitOrderIds);
        log.debug("批量发送的list集合--> " + orderIds);
        log.debug("批量发送的str集合--> " + sb);
        log.debug("批量发送的订单对象集合--> " + newOrderlist);
        //当ES没有数据的时候
        if (CollectionUtils.isNotEmpty(orderIds)) {
            //批量更新数据并更新ES
            if (CollectionUtils.isNotEmpty(tranSplitOrderIds)) {
                ocBOrderMapper.updateSplitStatusList(AutoSplitStatus.TRAN_SPLIT.toInteger(), tranSplitOrderIds);
                ocBOrderMapper.updateSplitReserveBigint10List(tranSplitOrderIds);
            }
            if (CollectionUtils.isNotEmpty(repeatSplitOrderIds)) {
                ocBOrderMapper.updateSplitStatusList(AutoSplitStatus.REPEAT_SPLIT.toInteger(), repeatSplitOrderIds);
                ocBOrderMapper.updateSplitReserveBigint10List(repeatSplitOrderIds);
            }
            log.debug("批量发送Mq的订单--> " + sb);
            this.sendMQ(sb.toString(), tranSplitOrderIds, repeatSplitOrderIds, newOrderlist);
        } else {
            log.debug("没有数据此次批量发送Mq审单不执行~~");
        }
        //发完一波mq休息1毫秒,减轻mq发送压力[和贺柳沟通后修改]
        try {
            Thread.sleep(1);
        } catch (Exception e) {
        }
    }


    /**
     * 发送MQ消息执行审单
     *
     * @param idsString 订单信息
     */
    public void sendMQ(String idsString, List<Long> tranSplitOrderIds, List<Long> repeatSplitOrderIds, List<OcBOrder> newOrderlist) {
        log.debug(this.getClass().getName() + "开始进入sendMQ方法====，idsString为：" + idsString);
        Long cutime = System.currentTimeMillis() * 1000; // 微秒
        Long nanoTime = System.nanoTime(); // 纳秒
        Long micTime = cutime + (nanoTime - nanoTime / 1000000 * 1000000) / 1000;
        String msgKey = "SPLIT_ORDER_TR_BATCH" + "_" + micTime;
        if (log.isDebugEnabled()) {
            log.debug("msgKey" + msgKey);
        }
        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
        orderMqInfo.setOperateType(OperateType.SPLIT_ORDER);
        orderMqInfo.setOrderIds(idsString);
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        mqInfoList.add(orderMqInfo);
        String jsonValue = JSONObject.toJSONString(mqInfoList);
        try {
            log.debug(this.getClass().getName() + "sendMQ方法内，开始发送MQ-------------topic为：" + autoSplitOrderMqConfig.getSendSplitMqTopic() + "tag为：" + autoSplitOrderMqConfig.getSendSplitTag());
//            sendHelper.sendMessage(jsonValue, autoSplitOrderMqConfig.getSendSplitMqTopic(),
//                    autoSplitOrderMqConfig.getSendSplitTag(),
//                    msgKey);
            defaultProducerSend.sendTopic(MqConstants.TOPIC_R3_OC_OMS_CALL_AUTOSPLIT, MqConstants.TAG_R3_OC_OMS_CALL_AUTOSPLIT, jsonValue, msgKey);
            orderAutoSplitService.batchTranSplitOrderPushEs(newOrderlist);
            log.debug(this.getClass().getName() + "sendMQ方法内，结束发送MQ-------------topic为：" + autoSplitOrderMqConfig.getSendSplitMqTopic() + "tag为：" + autoSplitOrderMqConfig.getSendSplitTag());
        } catch (Exception e) {
            log.debug("订单自动拆单发送Mq异常", e);
            ocBOrderMapper.updateSplitStatusList(AutoSplitStatus.ALREADY_SPLIT.toInteger(), repeatSplitOrderIds);
            ocBOrderMapper.updateSplitStatusList(AutoSplitStatus.UN_SPLIT.toInteger(), tranSplitOrderIds);
            e.printStackTrace();
        }
    }


}