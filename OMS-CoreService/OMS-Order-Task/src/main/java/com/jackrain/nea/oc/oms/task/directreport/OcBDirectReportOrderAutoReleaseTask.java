package com.jackrain.nea.oc.oms.task.directreport;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.directreport.OcBDirectReportOrderVoidService;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 直发预占-自动释放-监控任务
 * 定时任务扫库存释放时间小于当前时间&&（已审核||部分履约）的直发预占单进行作废
 *
 * <AUTHOR>
 * @since 2024-12-02 16:18
 */
@Slf4j
@Component
public class OcBDirectReportOrderAutoReleaseTask {
    @Resource
    private OcBDirectReportOrderVoidService ocBDirectReportOrderVoidService;

    @XxlJob("OcBDirectReportOrderAutoReleaseTask")
    public String execute() {
        try {
            ocBDirectReportOrderVoidService.autoBatchVoidOrder();
        } catch (Exception e) {
            log.error(LogUtil.format("直发预占-自动释放-监控任务失败,错误信息：{}",
                    "OcBDirectReportOrderAutoReleaseTask.execute"), Throwables.getStackTraceAsString(e));
            throw e;
        }
        return "success";
    }
}
