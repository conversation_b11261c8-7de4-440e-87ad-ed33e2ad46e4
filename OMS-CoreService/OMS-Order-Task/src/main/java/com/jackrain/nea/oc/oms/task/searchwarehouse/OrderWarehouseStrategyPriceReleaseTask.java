package com.jackrain.nea.oc.oms.task.searchwarehouse;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.services.OcBOrderWorehouseStartegyService;
import com.jackrain.nea.oc.oms.services.OrderStrategyPriceComputeService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 线上资金占用定时任务
 *
 * @author: ming.fz
 * @since: 2019-09-3
 * create at : 2019-09-3
 */
@Component
@Slf4j
public class OrderWarehouseStrategyPriceReleaseTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderWorehouseStartegyService ocBOrderWorehouseStartegyService;

    @Autowired
    private OrderStrategyPriceComputeService orderStrategyPriceComputeService;

    @Override
    @XxlJob("OrderWarehouseStrategyPriceReleaseTask")
    public RunTaskResult execute(JSONObject params) {
        long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {
            List<Long> orderIds = ocBOrderWorehouseStartegyService.selectUnTransferredOrderFromEs(0,
                    DEFAULT_PAGE_SIZE);

            List<OcBOrderParam> ocBOrderParams = ocBOrderWorehouseStartegyService.selectOrder(orderIds);

            ValueHolderV14 valueHolderV14 = orderStrategyPriceComputeService.onlineFundOccupy(ocBOrderParams,
                    null, SystemUserResource.getRootUser());
            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms");
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("线上资金占用总耗时{}, 结果:{}",  "线上资金占用"), (System.currentTimeMillis() - start), JSONObject.toJSONString(valueHolderV14));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OrderWarehouseStrategyPriceReleaseTask.Execute Error:,异常：{}", "线上资金占用"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}
