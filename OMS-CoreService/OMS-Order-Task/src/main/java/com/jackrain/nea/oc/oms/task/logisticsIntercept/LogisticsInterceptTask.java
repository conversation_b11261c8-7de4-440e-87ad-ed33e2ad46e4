package com.jackrain.nea.oc.oms.task.logisticsIntercept;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.oc.oms.model.request.ZtoLogisticsInterceptTaskRequest;
import com.jackrain.nea.oc.oms.services.LogisticsInterceptService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.lop.open.api.sdk.internal.fastjson.JSON;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/7/6
 */
@Slf4j
@Component
public class LogisticsInterceptTask implements IR3Task {

    @Autowired
    private LogisticsInterceptService logisticsInterceptService;

    @Override
    @XxlJob("LogisticsInterceptTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult runTaskResult = new RunTaskResult();
        try {
            log.info("京东、丹鸟、EMS、顺丰物流拦截失败重试定时任务 参数params ===> {}", params.toJSONString());

            ZtoLogisticsInterceptTaskRequest ztoLogisticsInterceptTaskRequest = new ZtoLogisticsInterceptTaskRequest();
            ValueHolderV14<Void> voidValueHolderV14 = logisticsInterceptService.interceptCreateForFail(ztoLogisticsInterceptTaskRequest);

            log.info("京东、丹鸟、EMS、顺丰物流拦截失败重试结果 ===> {}", JSON.toJSONString(voidValueHolderV14));

            runTaskResult.setSuccess(voidValueHolderV14.isOK());
            runTaskResult.setMessage(voidValueHolderV14.getMessage());
        }
        catch (Exception e) {
            runTaskResult.setSuccess(false);
            runTaskResult.setMessage(e.getMessage());
            log.error("京东、丹鸟、EMS、顺丰物流拦截失败重试任务执行异常");
            e.printStackTrace();
        }
        return runTaskResult;
    }
}
