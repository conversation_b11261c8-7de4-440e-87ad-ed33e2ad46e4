//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.google.common.base.Throwables;
//import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
//import com.jackrain.nea.oc.oms.services.OcBJitxModifyWarehouseLogService;
//import com.jackrain.nea.utility.LogUtil;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @create 2020-12-16
// * @desc JITX订单改仓工单任务
// **/
//@Slf4j
//public abstract class AbstractJitxChangeWarehouseTask {
//
//    /**
//     * 最大失败次数
//     */
//    @Value("${oms.oc.order.jitx.changeWarehouse.max.failNumber:5}")
//    private Integer failNumber;
//
//    @Override
//    protected String taskTableName() {
//        return "oc_b_jitx_modify_warehouse_log";
//    }
//
//    protected abstract String executeSqlWhere();
//
//    protected String whereFailNumber() {
//        return String.format(" and FAIL_NUMBER <= %d", failNumber);
//    }
//
//    protected abstract String executeSqlOrder();
//
//    protected abstract Integer getPullNum();
//
//    protected abstract void exceute(List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList, User operateUser);
//
//    @Autowired
//    private OcBJitxModifyWarehouseLogService ocBJitxModifyWarehouseLogService;
//
//    @Override
//    protected void executeRunnable(String nodeName, String tableName, User operateUser) {
//        if (log.isDebugEnabled()) {
//            log.debug(LogUtil.format("JITX订单改仓工单任务，where：{}，nodeName：{}，tableName：{}",
//                    executeSqlWhere(), nodeName, tableName));
//        }
//        try {
//            List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList = ocBJitxModifyWarehouseLogService.select(
//                    nodeName, tableName, executeSqlWhere() + whereFailNumber(),
//                    executeSqlOrder(), getPullNum());
//            if (CollectionUtils.isEmpty(modifyWarehouseLogList)) {
//                log.info(LogUtil.format("JITX订单改仓日志表查询为空nodeName：{}, taskTableName：{}, pullNum：{}"), nodeName, tableName, getPullNum());
//                return;
//            }
//            exceute(modifyWarehouseLogList, operateUser);
//        } catch (Exception e) {
//            log.info(LogUtil.format("AbstractJitxChangeWarehouseTask executeRunnable tableName：{}，error：{}"), tableName, Throwables.getStackTraceAsString(e));
//        }
//    }
//}
