package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpTaoBaoFxOrder;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx.TaobaoFxTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoFxService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 淘宝分销转单自动补偿任务
 * 查询ES中ISTRANS= 0的单据
 *
 * @author: 周琳胜
 * @since: 2019/7/9
 * create at : 2019/7/9 14:33
 */
@Component
@Slf4j
public class AutoTaobaoFxTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpTaobaoFxService ipTaobaoFxService;

    @Autowired
    private TaobaoFxTransferOrderProcessImpl taobaoFxTransferOrderProcess;

    @Override
    @XxlJob("AutoTaobaoFxTransferTask")
    public RunTaskResult execute(JSONObject params) {
        Long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {
            //分销订单转单单次最大拉取记录数 默认500单
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoTaobaoFxTransferTask.range", 500);
            List<String> orderNoList = ES4IpTaoBaoFxOrder.selectUnTransferredOrderFromEs(0, pageSize);
            List<IpTaobaoFxOrderRelation> orderRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {

                IpTaobaoFxOrderRelation taobaoFxOrderRelation = this.ipTaobaoFxService.selectTaobaoFxOrder(orderNo);
                if (taobaoFxOrderRelation == null) {
                    String errorMessage = Resources.getMessage("AutoTaobaoFxTransferTask.Order.NotExist!###OrderNo="
                            + orderNo);
                    log.error(errorMessage);
                } else {
                    orderRelationList.add(taobaoFxOrderRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.taobaoFxTransferOrderProcess, orderRelationList);
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoTaobaoFxTransferTask,异常信息:{}", "AutoTaobaoFxTransferTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(true);
            result.setMessage(ex.getMessage());
        }

        Long over = System.currentTimeMillis();
        long times = over - start;
        log.info(LogUtil.format("淘宝分销订单转单，消耗时间共计：" + times,  "淘宝分销订单转单"));
        return null;
    }
}
