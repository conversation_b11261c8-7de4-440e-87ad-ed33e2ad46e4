package com.jackrain.nea.oc.oms.task.bn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ip.model.bn.BnTaskListQueryRequest;
import com.jackrain.nea.ip.model.bn.BnTaskListQueryResponse;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.BnTaskDataMapper;
import com.jackrain.nea.oc.oms.model.table.BnTaskData;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @ClassName AutoGetBnTaskList
 * @Description 班牛工单列表数据获取类
 * <AUTHOR>
 * @Date 2025/5/12 11:10
 * @Version 1.0
 */
@Slf4j
@Component
public class AutoGetBnTaskList extends BaseR3Task implements IR3Task {

    @Autowired
    private IpRpcService rpcIpService;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private BnTaskDataMapper bnTaskDataMapper; // 注入班牛工单数据Mapper

    @Override
    @XxlJob("AutoGetBnTaskList")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult runTaskResult = new RunTaskResult();
        runTaskResult.setSuccess(true);
        String projectId = null;
        Integer pageSize = null;

        // 因为此时需要查询的状态较多。所以查询到所有的数据，然后状态是"已关闭"的 直接代码里面过滤掉
        String status = "";
        String startTime = null;
        String endTime = null;
        Boolean getAllPages = null;


//        // 尝试从参数中提取条件列对象列表
//        List<ConditionColumn> conditionColumns = createDefaultConditionColumns(bnProblemTextList, problemTextId);

        // 设置默认值
        if (projectId == null) projectId = "20940";
        if (pageSize == null) pageSize = 100;
        if (getAllPages == null) getAllPages = Boolean.TRUE;

        // 如果没有提供时间范围，使用昨天的时间范围
        if (startTime == null || endTime == null) {
            String[] timeRange = getYesterdayTimeRange();
            startTime = timeRange[0];
            endTime = timeRange[1];
        }

        // 使用Fastjson的toJSONString方法避免转义符
//        String conditionColumnsJson = com.alibaba.fastjson.JSON.toJSONString(conditionColumns);
        JSONArray data = getAllBnTaskList(projectId, pageSize, status, startTime, endTime, null);
        // 获取所有页的数据并存储到数据库
        if (data != null && !data.isEmpty()) {
            for (int i = 0; i < data.size(); i++) {
                JSONObject taskJson = data.getJSONObject(i);
                BnTaskData bnTaskData = new BnTaskData();
                bnTaskData.setId(ModelUtil.getSequence("bn_task_data"));
                bnTaskData.setProjectId(Long.valueOf(projectId));
                bnTaskData.setTaskJson(taskJson.toJSONString());
                bnTaskData.setProcessStatus(0);
                bnTaskData.setCreateTime(new Date());
                bnTaskData.setUpdateTime(new Date());
                bnTaskDataMapper.insert(bnTaskData);
            }
            log.info("成功存储 {} 条班牛工单数据到数据库", data.size());
        } else {
            runTaskResult.setMessage("班牛工单列表数据为空");
        }
        return runTaskResult;
    }

    /**
     * 获取单页班牛工单列表数据
     *
     * @param request 班牛工单列表查询请求
     * @return 查询结果包装类
     */
    public BnTaskListQueryResult getBnTaskListPage(BnTaskListQueryRequest request) {
        BnTaskListQueryResult queryResult = new BnTaskListQueryResult();
        queryResult.setSuccess(Boolean.TRUE);

        try {

            // 调用班牛工单列表接口
            log.info("查询班牛工单列表，参数：{}", com.alibaba.fastjson.JSON.toJSONString(request));

            ValueHolderV14<BnTaskListQueryResponse> valueHolderV14 = rpcIpService.queryTaskList(request);

            if (valueHolderV14.isOK()) {
                BnTaskListQueryResponse queryResponse = valueHolderV14.getData();

                Integer code = valueHolderV14.getCode();

                if (code != 0) {
                    queryResult.setSuccess(Boolean.FALSE);
                    queryResult.setMessage("查询班牛结果失败");
                    return queryResult;
                }

                JSONArray result = queryResponse.getResult();
                Integer pageNum = queryResponse.getPageNum();
                Integer total = queryResponse.getTotal();
                Integer totalPageNum = queryResponse.getTotalPageNum();

                queryResult.setData(result);
                queryResult.setPageNum(pageNum);
                queryResult.setPageSize(request.getPageSize());
                queryResult.setTotal(total);
                queryResult.setTotalPageNum(totalPageNum);
            } else {
                queryResult.setSuccess(Boolean.FALSE);
                queryResult.setMessage("查询班牛工单列表失败：" + valueHolderV14.getMessage());
            }
        } catch (Exception e) {
            log.error("获取班牛工单列表数据异常", e);
            queryResult.setSuccess(Boolean.FALSE);
            queryResult.setMessage("获取班牛工单列表数据异常：" + e.getMessage());
        }

        return queryResult;
    }

    /**
     * 获取所有班牛工单列表数据（自动分页查询所有数据并合并）
     *
     * @param projectId 项目ID
     * @param pageSize 每页大小
     * @param status 状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param conditionColumn 条件列
     * @return 任务执行结果
     */
    public JSONArray getAllBnTaskList(String projectId, Integer pageSize,
                                         String status, String startTime, String endTime, String conditionColumn) {


        // 创建一个新的JSONArray来存储所有页的数据
        JSONArray allResults = new JSONArray();
        try {
            // 创建请求对象
            BnTaskListQueryRequest request = new BnTaskListQueryRequest();
            request.setProjectId(projectId);
            request.setPage(1);
            request.setPageSize(pageSize);
            request.setStatus(status);
            request.setStarModified(startTime);
            request.setEndModified(endTime);
            request.setConditionColumn(conditionColumn);

            BnTaskListQueryResult firstPageResult = getBnTaskListPage(request);

            if (!firstPageResult.getSuccess()) {
                return null;
            }

            Integer totalPageNum = firstPageResult.getTotalPageNum();
            Integer total = firstPageResult.getTotal();

            if (totalPageNum == null || totalPageNum <= 0) {
                return null;

            }

            log.info("班牛工单列表总数据量：{}，总页数：{}", total, totalPageNum);


            // 添加第一页的数据
            JSONArray firstPageData = (JSONArray) firstPageResult.getData();
            if (firstPageData != null && !firstPageData.isEmpty()) {
                allResults.addAll(firstPageData);
            }

            // 查询剩余的页
            for (int page = 2; page <= totalPageNum; page++) {
                // 创建请求对象
                BnTaskListQueryRequest pageRequest = new BnTaskListQueryRequest();
                pageRequest.setProjectId(projectId);
                pageRequest.setPage(page);
                pageRequest.setPageSize(pageSize);
                pageRequest.setStatus(status);
                pageRequest.setStarModified(startTime);
                pageRequest.setEndModified(endTime);
                pageRequest.setConditionColumn(conditionColumn);

                BnTaskListQueryResult pageResult = getBnTaskListPage(pageRequest);

                if (!pageResult.getSuccess()) {
                    log.warn("查询第{}页数据失败：{}", page, pageResult.getMessage());
                    continue;
                }

                JSONArray pageData = (JSONArray) pageResult.getData();
                if (pageData != null && !pageData.isEmpty()) {
                    allResults.addAll(pageData);
                }

                log.info("已查询第{}页数据，当前已获取数据量：{}", page, allResults.size());
            }

            // 设置结果
            log.info("成功获取所有班牛工单列表数据，总数据量：{}", allResults.size());

        } catch (Exception e) {
            log.error("获取所有班牛工单列表数据异常", e);
        }

        return allResults;
    }

    /**
     * 获取昨天的开始时间和结束时间
     *
     * @return 包含开始时间和结束时间的数组，格式为 "yyyy-MM-dd HH:mm:ss"
     */
    private String[] getYesterdayTimeRange() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();

        // 设置为昨天
        calendar.add(Calendar.DAY_OF_MONTH, -1);

        // 设置为当天的开始时间（0点）
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        String startTime = sdf.format(calendar.getTime());

        // 设置为当天的结束时间（23点59分59秒）
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        String endTime = sdf.format(calendar.getTime());

        return new String[]{startTime, endTime};
    }

    /**
     * 创建默认的条件列对象列表
     *
     * @return 默认的条件列对象列表
     */
    public List<ConditionColumn> createDefaultConditionColumns(List<String> bnProblemTextList, Long bnProblemTextId) {
        List<ConditionColumn> conditionColumns = new ArrayList<>();
//        // 添加一个包含搜索条件（searchType="3"）
//        conditionColumns.add(ConditionColumn.builder()
//                .id(bnProblemTextId)
//                .behaviorType(1)
//                .searchType("3")
//                .value("卖方-测试2345")
//                .build());
//
//        // 添加一个等于搜索条件（searchType="1"）
//        conditionColumns.add(ConditionColumn.builder()
//                .id(340148L)
//                .behaviorType(1)
//                .searchType("1")
//                .value("等于搜索示例")
//                .build());

        // 添加一个包含任一项搜索条件（searchType="5"）
        JSONArray valueArray = new JSONArray();
        for (String text : bnProblemTextList){
            JSONObject value = new JSONObject();
            value.put("id", text);
            valueArray.add(value);
        }

        conditionColumns.add(ConditionColumn.builder()
                .id(bnProblemTextId)
                .behaviorType(1)
                .searchType("5")
                .value(valueArray)
                .build());
        return conditionColumns;
    }

    /**
     * 条件列对象，用于表示查询条件
     * 多条件查询格式:[{"id":340147,"behaviorType":1,"searchType":"3","value":"卖方-测试2345"}]
     * 其中searchType="1" 代表 等于(文本组件)；
     * searchType="3" 代表 包含(文本组件模糊搜索)；
     * searchType="5"代表 包含任一项(下拉组件)
     * 重要:如果searchType="5",value的值类型value：[{"id":123},{"id":346}]
     * searchType：
     *    1 等于
     *    2 不等于
     *    3 包含
     *    4 不包含
     *    5 包含任一项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConditionColumn {
        private Long id;           // 条件ID
        private Integer behaviorType; // 行为类型
        private String searchType;    // 搜索类型
        private Object value;         // 搜索值，当searchType="5"时，这是一个数组
    }

    /**
     * 班牛工单列表查询结果包装类
     */
    @Data
    public static class BnTaskListQueryResult {
        private Boolean success;
        private String message;
        private Object data;
        private Integer pageNum;
        private Integer pageSize;
        private Integer total;
        private Integer totalPageNum;

        public BnTaskListQueryResult() {
            this.success = Boolean.FALSE;
            this.message = "";
        }
    }

}