package com.jackrain.nea.oc.oms.task.makeup;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.process.audit.OrderAuditProcess;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 15:01 2020/5/22
 * description ：JITX订单审核补偿任务
 * @ Modified By：
 */
@Slf4j
@Component
public class AutoMakeupAuditJitxOrderTask extends BaseR3Task implements IR3Task {
    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private OrderAuditProcess orderAuditProcess;

    @Override
    @XxlJob("AutoMakeupAuditJitxOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            RuntimeCompute runtimeCompute = new RuntimeCompute();
            runtimeCompute.startRuntime();

            int pullNum = Tools.getInt(AdParamUtil.getParam("oms.oc.order.autoMakeupAudit.pull.num"), 200);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("JITX订单审核补偿任务单次最大拉取记录数={}"), pullNum);
            }

            List<Long> orderIdList = ES4Order.findJitXIdsByPagination(0, pullNum);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("JITX订单审核补偿任务需要补偿的数量={}"), orderIdList.size());
            }
            List<OcBOrderRelation> orderRelationList = new ArrayList<>();
            for (Long orderId : orderIdList) {
                log.debug(LogUtil.format("JITX订单审核补偿任务orderId!启动补偿任务审单={}"), orderId);
                OcBOrderRelation orderRelation = this.orderService.selectOmsOrderInfo(orderId);
                if (orderRelation == null) {
                    String errorMessage = Resources.getMessage("AutoMakeupAuditJitxOrderTask Order NotExist!OrderId="
                            + orderId);
                    log.error(LogUtil.format(errorMessage));
                } else {
                    orderRelationList.add(orderRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.orderAuditProcess, orderRelationList);
            double usedTime = runtimeCompute.endRuntime();
            log.debug(LogUtil.format("JITX订单审核补偿任务执行成功!UsedTime={}"), usedTime);
            result.setSuccess(true);
            result.setMessage("JITX订单审核补偿任务执行成功!UsedTime=" + usedTime);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("AutoMakeupAuditJitxOrderTask.异常: {}"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }

}
