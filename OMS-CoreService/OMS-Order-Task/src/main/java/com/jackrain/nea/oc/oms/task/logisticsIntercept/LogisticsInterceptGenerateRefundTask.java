package com.jackrain.nea.oc.oms.task.logisticsIntercept;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.OrderLogisticsInterceptTaskService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 物流拦截生成退款单定时任务
 * 每5分钟执行一次，捞取当前时间前6分钟的数据（根据创建时间）
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LogisticsInterceptGenerateRefundTask implements IR3Task {

    @Autowired
    private OrderLogisticsInterceptTaskService orderLogisticsInterceptTaskService;

    @Override
    @XxlJob("LogisticsInterceptGenerateRefundTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();

        try {

            // Calculate time range: current time to 6 minutes ago
            Date now = new Date();
            Date startTime = DateUtils.addMinutes(now, -40);
            Date endTime = DateUtils.addMinutes(now, -30);

            // 调用服务处理时间范围内的物流拦截记录
            orderLogisticsInterceptTaskService.getOrderIdsByTimeRange(startTime, endTime);

            // 设置结果
            result.setSuccess(true);
            result.setMessage("物流拦截生成退款单任务执行成功");

        } catch (Exception e) {
            log.error(LogUtil.format("LogisticsInterceptGenerateRefundTask.execute error: {}",
                    "LogisticsInterceptGenerateRefundTask.execute"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage("执行异常：" + e.getMessage());
        }

        return result;
    }
}
