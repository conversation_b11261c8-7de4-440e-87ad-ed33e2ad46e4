package com.jackrain.nea.oc.oms.task.chargebackcheck;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.OcOmsReturnOrderConstant;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OcBReturnOrderService;
import com.jackrain.nea.oc.oms.services.ReturnOrderAutoAuditService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.oc.oms.util.StandplatOrderTransferUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.service.OmsOrderStCAutocheckService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 退单自动审核任务
 *
 * @author: 周琳胜
 * @since: 2019/4/2
 * create at : 2019/4/2 20:57
 */
@Slf4j
@Component
public class ChargebackAutoCheckTask extends BaseR3Task implements IR3Task {
    @Autowired
    OcBReturnOrderService ocBReturnOrderService;

    @Autowired
    ReturnOrderAutoAuditService returnOrderAutoAuditService;

    @Autowired
    OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    OmsOrderStCAutocheckService omsOrderStCAutocheckService;

    @Autowired
    private StandplatOrderTransferUtil standplatOrderTransferUtil;

    @Override
    @XxlJob("ChargebackAutoCheckTask")
    public RunTaskResult execute(JSONObject params) {

        Long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        ValueHolderV14 vh = new ValueHolderV14();
        Integer success = 0;
        Integer fail = 0;

        //自动审核单次最大拉取记录数 默认2000单
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer pageSize = config.getProperty("lts.AutoChargeBackTask.range", 500);
        List<Long> returnOrderIdList = this.ocBReturnOrderService.selectChargebackToCheck(0, pageSize);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ChargebackAutoCheckTask.returnOrderIdList: {}"), JSON.toJSONString(returnOrderIdList));
        }
        for (Long returnOrderId : returnOrderIdList) {
            JSONObject obj = new JSONObject();
            obj.put("ID", returnOrderId);
            // 根据退单ID查出退单对象 获得平台店铺id
            OcBReturnOrder ocBReturnOrder = ocBReturnOrderMapper.selectById(returnOrderId);
            Long cpcShopId = ocBReturnOrder.getCpCShopId();
            //如果是SAP销售退单，则不校验是否开启自动审核策略
            Integer platmform = standplatOrderTransferUtil.getReturnOrderPlatmform(ocBReturnOrder);
            String businessTypeCode = ocBReturnOrder.getBusinessTypeCode();
            boolean isAutoAudit = false;
            if ((PlatFormEnum.SAP.getCode().equals(platmform) || PlatFormEnum.DMS.getCode().equals(platmform)) && OcOmsReturnOrderConstant.BUSINESS_TYPE_CODE_RYTH11.equals(businessTypeCode)) {
                isAutoAudit = true;
            } else {
                // 判断退单店铺策略是否开启自动审核策略
                isAutoAudit = omsOrderStCAutocheckService.isExitsRefundOrderStrategy(cpcShopId);
            }
            if (isAutoAudit) {
                try {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("ChargeBackAutoCheckTask.AutoAudit.returnId#{}", returnOrderId), returnOrderId);
                    }
                    vh = returnOrderAutoAuditService.returnOrderAutoAudit(obj, SystemUserResource.getRootUser());
                    if (vh.isOK()) {
                        result.setSuccess(true);
                        success++;
                    } else {
                        fail++;
                    }
                } catch (Exception ex) {
                    if (log.isDebugEnabled()) {
                        log.info(LogUtil.format("ChargebackAutoCheckTask.returnOrderAutoAudit.ExpMessage: {}",
                                returnOrderId), Throwables.getStackTraceAsString(ex));
                    }
                    result.setSuccess(true);
                    result.setMessage(ex.getMessage());
                }
            }
        }
        result.setMessage("同步成功" + success + "条，失败" + fail + "条!");
        result.setSuccess(true);
        Long over = System.currentTimeMillis();
        long times = over - start;
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ChargebackAutoCheckTask.end, 审核: {}, 条，消耗时间共计：{}, 成功: {}"),
                    returnOrderIdList.size(), times, success);
        }
        return result;
    }
}
