package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.RefundInMakeUpService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: chenxiulou
 * @description: 退货入库单入库状态卡在等待入库状态超过半个小时（按创建时间）的将入库状态改为异常5
 * @since: 2019-09-25
 * create at : 2019-09-25 9:55
 */
@Slf4j
@Component
public class AutoRefundInTimeOutTask extends BaseR3Task implements IR3Task {
    @Autowired
    private RefundInMakeUpService refundInService;
    private static final Integer timeoutConst = 30;
    private static final Integer totalConst = 2000;

    @Override
    @XxlJob("AutoRefundInTimeOutTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult result = new RunTaskResult();
        log.debug(LogUtil.format("定时任务AutoReturnOrderInTimeOutTask开始执行！:{}",  "AutoRefundInTimeOutTask"));
        int total = totalConst;
        int timeout = timeoutConst;
        try {
            total = params.getInteger("total");
            timeout = params.getInteger("timeout");
        } catch (Exception ex) {
            log.error(LogUtil.format("获取Lts参数params：total或timeout异常！{}",  "AutoRefundInTimeOutTask"), Throwables.getStackTraceAsString(ex));
        }
        try {
            ValueHolderV14 v14 = refundInService.dealTimeOutRefundInsException(total, timeout);
            result.setSuccess(true);
            result.setMessage(v14.getMessage());
        } catch (Exception ex) {
            //发生异常 所有数据返回最开始的状态
            log.error(LogUtil.format("AutoRefundInTimeOutTask.Execute Error！{}",  "AutoRefundInTimeOutTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());

        }
        return result;
    }

}
