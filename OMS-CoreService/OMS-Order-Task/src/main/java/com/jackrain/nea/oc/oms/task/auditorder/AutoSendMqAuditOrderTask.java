package com.jackrain.nea.oc.oms.task.auditorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.config.AuditOrderMqConfig;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.AutoAuditStatus;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.relation.AutoAuditCheckMergeOrderDto;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.task.OcBAuditTask;
import com.jackrain.nea.oc.oms.services.OcMergeOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.StCAutoCheckResult;
import com.jackrain.nea.st.model.table.StCAutoCheckAutoTimeDO;
import com.jackrain.nea.st.model.table.StCAutoCheckDO;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.util.SendMQAsyncUtils;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import constant.MqConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 自动审核订单Mq发送消息类
 *
 * @author: heliu
 * @since: 2019/5/28
 * create at : 2019/5/28 18:30
 */
@Slf4j
@Component
public class AutoSendMqAuditOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private AuditOrderMqConfig auditOrderMqConfig;

    @Autowired
    private SendMQAsyncUtils sendMQAsyncUtils;

    @Autowired
    private OmsAuditTaskService auditTaskService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcMergeOrderService ocMergeOrderService;
    @Autowired
    private ThreadPoolTaskExecutor auditTaskThreadPoolExecutor;

    @Override
    @XxlJob("AutoSendMqAuditOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            long executeTime = System.currentTimeMillis();
            List<StCAutoCheckResult> stCAutoCheckResultList = stRpcService.queryAutoCheckAllList();
            RuntimeCompute runtimeCompute = new RuntimeCompute();
            runtimeCompute.startRuntime();
            //查询所有的店铺策略，加载到map中
            Map<Long, StCAutoCheckDO> shopStrategyMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(stCAutoCheckResultList)) {
                for (StCAutoCheckResult autoCheckResult : stCAutoCheckResultList) {
                    StCAutoCheckDO stCAutocheckDO = autoCheckResult.getStCAutoCheckDO();
                    List<StCAutoCheckAutoTimeDO> autoTimes = autoCheckResult.getAutoTimes();
                    if ("Y".equalsIgnoreCase(stCAutocheckDO.getIsAutocheckOrder())) {
                        if (CollectionUtils.isNotEmpty(autoTimes)) {
                            DateTimeFormatter df = DateTimeFormatter.ofPattern("HH:mm");
                            for (StCAutoCheckAutoTimeDO autoTime : autoTimes) {
                                try {
                                    LocalTime start = LocalTime.parse(autoTime.getStartTime(), df);
                                    LocalTime end = LocalTime.parse(autoTime.getEndTime(), df);
                                    LocalTime now = LocalTime.now();
                                    if (now.isBefore(start) || now.isAfter(end)) {
                                        continue;
                                    }
                                    shopStrategyMap.put(stCAutocheckDO.getCpCShopId(), stCAutocheckDO);
                                } catch (Exception e) {
                                    log.error(LogUtil.format("AutoSendMqAuditOrderTask 解析时间异常: {}", stCAutocheckDO.getCpCShopId()),
                                            Throwables.getStackTraceAsString(e));
                                }
                            }
                        } else {
                            shopStrategyMap.put(stCAutocheckDO.getCpCShopId(), stCAutocheckDO);
                        }
                    }
                }
            }
            if (MapUtils.isEmpty(shopStrategyMap)) {
                result.setSuccess(true);
                result.setMessage("未开启自动审核,任务结束!");
                return result;
            }
            Set<Long> shopSetIds = shopStrategyMap.keySet();
            StringBuilder shopIdsBuilder = new StringBuilder();
            shopIdsBuilder.append("(")
                    .append(StringUtils.join(shopSetIds, ","))
                    .append(")");
            String shopIds = shopIdsBuilder.toString();
            final String taskTableName = "oc_b_audit_task";
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pullNum = config.getProperty("oms.oc.order.autoAudit.pull.num", 3000);
            List<OcBAuditTask> auditTaskList = auditTaskService.selectAuditTask(pullNum, taskTableName, shopIds, executeTime);
            if (CollectionUtils.isEmpty(auditTaskList)) {
                result.setSuccess(true);
                result.setMessage("未查询到需要审核的订单,任务结束!");
                return result;
            }
            List<Future<Boolean>> results = new ArrayList<>();
            // 对auditTaskList进行拆分 分成24个list
            List<List<OcBAuditTask>> lists = ListSplitUtil.averageAssign(auditTaskList, 24);
            for (List<OcBAuditTask> l : lists) {
                results.add(auditTaskThreadPoolExecutor.submit(new CallableTobeAuditTaskWithResult(l, shopStrategyMap)));
            }

            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    Boolean aBoolean = futureResult.get();
                } catch (Exception e) {
                    log.error(LogUtil.format("AutoSendMqAuditOrderTask多线程获取ExecutionException异常: {}"), Throwables.getStackTraceAsString(e));
                }
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("自动审核异常: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

    private void callBackExec(List<Long> orderIds) {
        orderService.updateAuditOrderList(orderIds, AutoAuditStatus.Audit_INIT.toInteger());
        //mq发送异常，audit_task表status改为0
        auditTaskService.updateAuditTaskByOrderId(orderIds);
    }

    /**
     * 根据策略上是否勾选合并审核判断改订单是否执行审核
     *
     * @param stCAutocheckDO 对象
     * @return boolean
     */
    public boolean isMergeOrder(StCAutoCheckDO stCAutocheckDO) {
        //勾选了是否合并订单条件的，勾选：执行作为判断是否合并的审核；
        return IsActiveEnum.Y.getKey().equalsIgnoreCase(stCAutocheckDO.getIsMergeOrder());
    }

    /**
     * 分割list
     *
     * @param dataList        数据集合
     * @param shopStrategyMap
     */
    public void splitDataList(List<OcBAuditTask> dataList, Map<Long, StCAutoCheckDO> shopStrategyMap) {

        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        // 取出需要处理的所有订单id
        List<Long> orderIds = dataList.stream().map(OcBAuditTask::getOrderId).collect(Collectors.toList());
        // 更新当前订单自动审核表里的更新时间
        auditTaskService.updateAuditTaskModitime(orderIds);
        // 1.先找出需要校验 是否可合单的店铺
        List<Long> isMergeOrderShopIds = new ArrayList<>(shopStrategyMap.size());
        for (Long key : shopStrategyMap.keySet()) {
            if (isMergeOrder(shopStrategyMap.get(key))) {
                isMergeOrderShopIds.add(key);
            }
        }
        // 2.查询未HOLD单的订单
        List<OcBOrder> orderList = omsOrderService.selectNotHoldOrderList(orderIds);
        // 自动审核已开启 是否校验合单
        Map<String, List<Long>> auditAndCheckMergeMap = new HashMap<>();
        // 自动审核未开启 是否校验合单
        List<Long> autoAuditList = new ArrayList<>();
        // 状态非待审核，需要将TASK表的状态修改为已处理
        List<Long> noAuditOrderList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderList)) {
            for (OcBOrder order : orderList) {
                String encryptionCode = order.getOrderEncryptionCode();
                if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus())
                        || StringUtils.isNotEmpty(encryptionCode)) {
                    if (isMergeOrderShopIds.contains(order.getCpCShopId())) {
                        if (CollectionUtils.isEmpty(auditAndCheckMergeMap.get(encryptionCode))) {
                            List<Long> ids = new ArrayList<>();
                            ids.add(order.getId());
                            auditAndCheckMergeMap.put(encryptionCode, ids);
                        } else {
                            auditAndCheckMergeMap.get(encryptionCode).add(order.getId());
                        }
                    } else {
                        autoAuditList.add(order.getId());
                    }
                } else {
                    noAuditOrderList.add(order.getId());
                }
            }
        }
        // 3.将状态不是未审核的订单对应的任务表状态改为已处理
        if (CollectionUtils.isNotEmpty(noAuditOrderList)) {
            auditTaskService.updateAuditTask(noAuditOrderList);
        }
        // 4.判断订单是否可合并，剔除可以合并的订单ID不发MQ
        // 不可合并的
        if (MapUtils.isNotEmpty(auditAndCheckMergeMap)) {
            List<List<String>> params = Lists.partition(Lists.newArrayList(auditAndCheckMergeMap.keySet()), 50);
            for (List<String> param : params) {
                List<String> paramTemp = new ArrayList<>();
                for (String key : param) {
                    paramTemp.add(key);
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("AutoSendMqAuditOrderTask.调用是否可合并接口入参={}"), JSON.toJSONString(paramTemp));
                }
                ValueHolderV14<AutoAuditCheckMergeOrderDto> vh = ocMergeOrderService.checkOrderIsMergeEnable(paramTemp);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("AutoSendMqAuditOrderTask.调用是否可合并接口出参={}.auditAndCheckMergeMap={}"),
                            JSON.toJSONString(vh), JSON.toJSONString(auditAndCheckMergeMap));
                }
                if (vh.isOK()) {
                    AutoAuditCheckMergeOrderDto autoAuditCheckMergeOrder = vh.getData();
                    if (autoAuditCheckMergeOrder != null
                            && CollectionUtils.isNotEmpty(autoAuditCheckMergeOrder.getUnableMergeCodes())) {
                        for (String key : autoAuditCheckMergeOrder.getUnableMergeCodes()) {
                            autoAuditList.addAll(auditAndCheckMergeMap.get(key));
                        }
                    }
                } else {
                    log.error(LogUtil.format("AutoSendMqAuditOrderTask.自动审核调用是否可合并接口返回结果={}"), JSON.toJSONString(vh));
                }
            }
        }
        if (CollectionUtils.isEmpty(autoAuditList)) {
            return;
        }
        //订单自动审核单次最大处理数 默认200单
        int pointsDataLimit = Tools.getInt(AdParamUtil.getParam("oms.oc.order.autoAudit.push.num"), 200);
        //分批次处理
        List<List<Long>> listList = Lists.partition(autoAuditList, pointsDataLimit);
        for (List<Long> orderIdList : listList) {
            if (CollectionUtils.isEmpty(orderIdList)) {
                continue;
            }
            //再发Mq
            this.sendMQ(orderIdList);
            auditTaskService.updateAuditTask(orderIdList);

            //发完一波mq休息1毫秒,减轻mq发送压力
            try {
                Thread.sleep(1);
            } catch (Exception e) {
            }
        }
    }

    /**
     * 发送MQ消息执行审单
     *
     * @param orderIds 订单ID集合
     */
    private void sendMQ(List<Long> orderIds) {

        String msgKey = "AUDIT_TR_BATCH" + "_" + BllCommonUtil.getmicTime();
        StringBuffer ids = new StringBuffer();
        for (int i = 0; i <= orderIds.size() - 1; i++) {
            if (i != 0) {
                ids.append(",").append(orderIds.get(i));
            } else {
                ids.append(orderIds.get(i));
            }
        }
        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
        orderMqInfo.setOperateType(OperateType.AUDIT_ORDER);
        orderMqInfo.setOrderIds(ids.toString());
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        mqInfoList.add(orderMqInfo);
        String jsonValue = JSON.toJSONString(mqInfoList);
        sendMQAsyncUtils.sendDelayMessage("default", "自动审核", jsonValue, MqConstants.TOPIC_R3_OC_OMS_CALL_AUTOAUDIT, MqConstants.TAG_R3_OC_OMS_CALL_AUTOAUDIT, msgKey, 2L, this::callBackExec, orderIds);
    }

    /**
     * 开启线程类
     */
    class CallableTobeAuditTaskWithResult implements Callable<Boolean> {
        private final List<OcBAuditTask> ocBAuditTaskList;
        private final Map<Long, StCAutoCheckDO> shopStrategyMap;

        public CallableTobeAuditTaskWithResult(List<OcBAuditTask> ocBAuditTaskList, Map<Long, StCAutoCheckDO> shopStrategyMap) {
            this.shopStrategyMap = shopStrategyMap;
            this.ocBAuditTaskList = ocBAuditTaskList;
        }

        @Override
        public Boolean call() throws Exception {
            splitDataList(ocBAuditTaskList, shopStrategyMap);
            return true;
        }
    }
}
