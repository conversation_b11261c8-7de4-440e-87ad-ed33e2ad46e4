package com.jackrain.nea.oc.oms.task.makeup;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.oc.oms.services.ReturnWrongAdjustmentServer;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 退换货自动补偿任务生成错发调整单
 * @author: 郑小龙
 * @date: 2019-09-25 14:16
 */
@Slf4j
@Component
public class AutoGenerateWrongAdjustmentTask extends BaseR3Task implements IR3Task {

    @Autowired
    ReturnWrongAdjustmentServer server;

    @Override
    @XxlJob("AutoGenerateWrongAdjustmentTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("退换货自动补偿任务生成错发调整单LTS入参={}"), params);
        }
        int count = params.getInteger("count");
        ValueHolderV14 v14 = server.execLogic(count);

        RunTaskResult result = new RunTaskResult();
        result.setSuccess(true);
        result.setMessage("处理成功！");

        if (!v14.isOK()) {
            result.setSuccess(false);
            result.setMessage(v14.getMessage());
        }
        return result;
    }
}
