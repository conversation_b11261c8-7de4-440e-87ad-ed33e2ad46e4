package com.jackrain.nea.oc.oms.task.tobeconfirm;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBLowTemperatureOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOccupyTask;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.OmsOccupyTaskService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * @ClassName OmsLowTemputreOccupyServiceTask
 * @Description 低温寻源定时任务
 * <AUTHOR>
 * @Date 2024/5/11 11:45
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsLowTemperatureOccupyServiceTask implements IR3Task {

    private static final String TABLE_NAME = "oc_b_low_temperature_occupy_task";
    @Autowired
    private OcBLowTemperatureOccupyTaskMapper lowTemperatureOccupyTaskMapper;
    @Autowired
    private ThreadPoolTaskExecutor lowTemperatureOccupyPollExecutor;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;

    @Override
    @XxlJob("OmsLowTemputreOccupyServiceTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        long startTime = System.currentTimeMillis();

        String lowTemperatureOccupySwitch = businessSystemParamService.get2CLowTemperatureOccupySwitch();
        if (StringUtils.isEmpty(lowTemperatureOccupySwitch) || "否".equals(lowTemperatureOccupySwitch)) {
            result.setSuccess(true);
            return result;
        }
        // 获取配置的 2行及以上明细优先执行的时间
        String lowTemperatureOccupyTime = businessSystemParamService.getLowTemperatureOccupyTime();
        // 得到的格式是时:分 转换成 Date
        LocalTime time = LocalTime.parse(lowTemperatureOccupyTime);
        String formattedTime = LocalDateTime.of(LocalDate.now(), time).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        boolean moreItemPriority = DateUtil.stringToDate(formattedTime).compareTo(new Date()) > 0;
        List<Long> orderIdList = new ArrayList<>();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int pageSize = config.getProperty("lts.OmsLowTemputreOccupyServiceTask.range", 200);
        if (moreItemPriority) {
            // 只捞取2行及以上明细
            orderIdList = lowTemperatureOccupyTaskMapper.selectLowTemperatureOccupyTaskWithPriority(pageSize, TABLE_NAME);
        } else {
            orderIdList = lowTemperatureOccupyTaskMapper.selectLowTemperatureOccupyTaskWithOutPriority(pageSize, TABLE_NAME);
        }
        if (CollectionUtils.isEmpty(orderIdList)) {
            result.setSuccess(true);
            result.setMessage("没有需要寻源的订单");
            return result;
        }
        List<List<Long>> lists = ListSplitUtil.averageAssign(orderIdList, 200);
        // formattedTime与当前时间进行对比
        List<Future<Integer>> results = new ArrayList<>();
        for (List<Long> data : lists) {
            results.add(lowTemperatureOccupyPollExecutor.submit(new CallableLowTemperatureOccupyServiceTaskResult(data)));
        }
        Integer count = 0;
        for (Future<Integer> integerFuture : results) {
            try {
                Integer integer = integerFuture.get();
                count = count + integer;
            } catch (Exception e) {
                log.error(LogUtil.format("低温待寻源定时任务异常:{}", "低温待寻源定时任务异常"), Throwables.getStackTraceAsString(e));
            }
        }
        long endTime = System.currentTimeMillis();
        result.setSuccess(true);
        result.setMessage("低温待寻源定时任务:条数" + count + "耗时:" + (endTime - startTime));
        return result;
    }

    class CallableLowTemperatureOccupyServiceTaskResult implements Callable<Integer> {
        public List<Long> data;

        public CallableLowTemperatureOccupyServiceTaskResult(List<Long> data) {
            this.data = data;
        }

        @Override
        public Integer call() throws Exception {
            if (CollectionUtils.isEmpty(data)) {
                return 0;
            }
            // 根据订单id 获取订单状态 过滤掉取消跟系统作废的订单
            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(data);
            List<OcBOrder> newOrderList = new ArrayList<>();
            for (OcBOrder ocBOrder : ocBOrderList) {
                if (!(ocBOrder.getOrderStatus().equals(OmsOrderStatus.CANCELLED.toInteger()) || ocBOrder.getOrderStatus().equals(OmsOrderStatus.SYS_VOID.toInteger()))) {
                    newOrderList.add(ocBOrder);
                }
            }
            if (CollectionUtils.isEmpty(newOrderList)) {
                lowTemperatureOccupyTaskMapper.updateTaskStatus(data);
                return 0;
            }
            List<OcBOccupyTask> taskList = new ArrayList<>();
            // 如果还有 则往寻源表丢数据
            for (OcBOrder ocBOrder : newOrderList) {
                Date date = new Date();
                // 需要判断是否有卡单时间
                if (ocBOrder.getIsDetention() != null && ocBOrder.getIsDetention() == 1) {
                    if (ocBOrder.getDetentionReleaseDate() == null) {
                        continue;
                    } else {
                        date = ocBOrder.getDetentionReleaseDate();
                    }
                }
                OcBOccupyTask task = new OcBOccupyTask();
                task.setId(ModelUtil.getSequence("oc_b_occupy_task"));
                task.setNextTime(date);
                task.setStatus(0);
                task.setShopId(ocBOrder.getCpCShopId());
                task.setRetryNumber(0);
                task.setOrderId(ocBOrder.getId());
                task.setCreationdate(new Date());
                taskList.add(task);
            }
            if (CollectionUtils.isNotEmpty(taskList)) {
                ocBOccupyTaskMapper.batchInsert(taskList);
            }
            // 更新数据状态
            lowTemperatureOccupyTaskMapper.updateTaskStatus(data);
            return newOrderList.size();
        }
    }

}
