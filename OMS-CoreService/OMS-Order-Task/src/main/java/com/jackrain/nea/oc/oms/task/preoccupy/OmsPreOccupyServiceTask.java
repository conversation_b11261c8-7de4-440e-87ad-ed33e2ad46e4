package com.jackrain.nea.oc.oms.task.preoccupy;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOccupyTask;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.sg.service.OmsPreOccupyService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @ClassName OmsPreOccupyServiceTask
 * @Description 订单预寻源
 * <AUTHOR>
 * @Date 2025/3/3 15:48
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsPreOccupyServiceTask implements IR3Task {

    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private ThreadPoolTaskExecutor preOccupyPollExecutor;
    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;
    @Autowired
    private OmsPreOccupyService omsPreOccupyService;

    @Override
    @XxlJob("OmsPreOccupyServiceTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        // 当前时间需要在停止寻源的时间范围内
        boolean autoOccupyStopTime = businessSystemParamService.checkAutoOccupyStopTime();
        if (!autoOccupyStopTime) {
            result.setSuccess(false);
            result.setMessage("当前时间不在停止寻源的时间范围内");
            return result;
        }
        // 获取预寻源时间节点
        String preOccupyRange = businessSystemParamService.getPreOccupyRange();
        // preOccupyRange格式为 "22:00~5:00"，解析出来
        String[] preOccupyRangeArr = preOccupyRange.split("~");
        if (preOccupyRangeArr.length != 2) {
            result.setSuccess(false);
            result.setMessage("预寻源时间节点格式错误");
            return result;
        }
        Date now = DateUtil.date();

        Date yesterday = DateUtil.yesterday();
        String startTimeStr = preOccupyRangeArr[0] + ":00";
        String endTimeStr = preOccupyRangeArr[1] + ":00";
        Date startTime = null;
        Date endTime = DateUtil.parseDateTime(DateUtil.format(now, "yyyy-MM-dd") + " " + endTimeStr);
        // 如果startTimeStr 小于endTimeStr 说明在同一天。 否则代表跨天了
        if (startTimeStr.compareTo(endTimeStr) < 0) {
            // 同一天
            startTime = DateUtil.parseDateTime(DateUtil.format(now, "yyyy-MM-dd") + " " + startTimeStr);
        } else {
            startTime = DateUtil.parseDateTime(DateUtil.format(yesterday, "yyyy-MM-dd") + " " + startTimeStr);
        }
        // 获取startTime与endTime这个时间范围内创建的订单
        List<Long> ocBOrderIdList = ocBOrderMapper.selectOcBOrderIdByCreationDate(DateUtil.format(startTime, DatePattern.NORM_DATETIME_FORMAT), DateUtil.format(endTime, DatePattern.NORM_DATETIME_FORMAT));
        // 分批执行。 一次只处理500单。分多个线程来
        if (CollectionUtils.isEmpty(ocBOrderIdList)) {
            result.setSuccess(false);
            result.setMessage("没有需要预寻源的订单");
            return result;
        }
        List<List<Long>> ocBOrderIdListList = ListSplitUtil.splitList(ocBOrderIdList, 500);
        List<Future<Integer>> results = new ArrayList<>();
        for (List<Long> idList : ocBOrderIdListList) {
            results.add(preOccupyPollExecutor.submit(new CallableOmsPreOccupyServiceTaskResult(idList)));
        }
        Integer count = 0;
        for (Future<Integer> integerFuture : results) {
            try {
                Integer integer = integerFuture.get();
                count = count + integer;
            } catch (Exception e) {
                log.error(LogUtil.format("待寻源定时任务异常:{}", "待寻源定时任务异常"), Throwables.getStackTraceAsString(e));
            }
        }
        result.setSuccess(true);
        result.setMessage("预寻源定时任务:条数" + count);
        return result;
    }

    class CallableOmsPreOccupyServiceTaskResult implements Callable<Integer> {
        private final List<Long> idList;

        public CallableOmsPreOccupyServiceTaskResult(List<Long> idList) {
            this.idList = idList;
        }

        @Override
        public Integer call() throws Exception {
            if (CollectionUtils.isEmpty(idList)) {
                return 0;
            }
            // 找到后 查一下寻源中间表是否有数据 并且状态为0
            List<OcBOccupyTask> ocBOccupyTaskList = ocBOccupyTaskMapper.selectOcBOccupyTaskByOrderIds(idList);
            if (CollectionUtils.isEmpty(ocBOccupyTaskList)) {
                return 0;
            }
            // 获取order_id的集合
            Set<Long> orderIdList = ocBOccupyTaskList.stream().map(OcBOccupyTask::getOrderId).collect(Collectors.toSet());
            List<Long> preOccupyOrderIdList = new ArrayList<>();
            // 筛选完成后 进行匹配
            for (Long orderId : orderIdList) {
                boolean preOccupy = omsPreOccupyService.preOccupy(orderId);
                if (preOccupy) {
                    preOccupyOrderIdList.add(orderId);
                }
            }
            if (CollectionUtils.isEmpty(preOccupyOrderIdList)) {
                return 0;
            }
            // 需要在寻源中间表 将创建时间设置在一天前。 nexttime 设置为当前时间。 需要支持一批订单
            Date yesterday = DateUtil.yesterday();
            ocBOccupyTaskMapper.updateByIdList(preOccupyOrderIdList, yesterday);
            return preOccupyOrderIdList.size();
        }
    }
}
