package com.jackrain.nea.oc.oms.task.distributionlogistics;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpCLogistics;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderAutoDistributionLogisticsService;
import com.jackrain.nea.oc.oms.services.OmsOrderDistributeLogisticsService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 自动分配物流（临时处理）定时补偿任务
 *
 * @author: 胡林洋
 * @since: 2019/9/9
 * create at : 2019/9/9 2:25
 */
@Slf4j
@Component
public class AutoDistributionLogisticsHandleTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsOrderAutoDistributionLogisticsService omsOrderAutoDistributionLogisticsService;

    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    /**
     * 默认每次查询页数信息
     */
    protected static final int DEFAULT_PAGE_SIZE = 200;

    @Override
    @XxlJob("AutoDistributionLogisticsHandleTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug(LogUtil.format("AutoDistributionLogisticsHandleTask.execute方法开始执行"));
        RunTaskResult result = new RunTaskResult();
        try {
            String pullNum = AdParamUtil.getParam("oms.oc.order.autodistributelogistic.pull.num");
            int pullNumber = StringUtils.isNotEmpty(pullNum) ? Integer.parseInt(pullNum) : DEFAULT_PAGE_SIZE;
            log.debug(LogUtil.format("omsOrderAutoSplitService.selectWaitAutoDistributionLogisticsOrder方法开始执行,开始查询等待自动分配物流的订单"));
            List<Long> orderIdTmpList = omsOrderAutoDistributionLogisticsService.selectWaitAutoDistributionLogisticsOrder(0, pullNumber);
            log.debug(LogUtil.format("omsOrderAutoSplitService.selectWaitAutoDistributionLogisticsOrder方法,查询结果为：{}"), orderIdTmpList);
            //分割捞取到的数据，默认200每批
            if (orderIdTmpList != null && orderIdTmpList.size() > 0) {
                splitDataList(orderIdTmpList);
            }
            result.setSuccess(true);
            result.setMessage("AutoDistributionLogisticsHandleTask,组装需要自动分物流的订单SUCCESS");
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage("AutoDistributionLogisticsHandleTask,组装需要自动分配物流的订单Fail,处理过程发生异常！" + e);
            log.error(LogUtil.format("AutoDistributionLogisticsHandleTask,组装需要处理的订单Fail,处理过程发生异常: {}"), Throwables.getStackTraceAsString(e));
        }
        return result;
    }


    /**
     * 分割订单列表，默认200单，有配置取配置参数
     *
     * @param orderIdList 数据集合
     */
    public void splitDataList(List<Long> orderIdList) {
        //订单自动拆单单次最大处理数 默认200单
        int pointsDataLimit = 200;
        //log.debug("handleDataList方法内，" + pointsDataLimit);
        List<Long> newList = new ArrayList<>();
        //分批次处理
        for (int i = 0; i < orderIdList.size(); i++) {
            List<OcBOrder> dataList = new ArrayList<>();
            newList.add(orderIdList.get(i));
            if (pointsDataLimit == newList.size() || i == orderIdList.size() - 1) {
                log.debug(LogUtil.format("splitDataList方法内,newList的值为: {}"), newList);
                handleDataList(newList);
                log.debug(LogUtil.format("splitDataList方法内,dataList的值为: {}"), dataList);
                newList.clear();
                dataList.clear();
                log.debug(LogUtil.format("自动分配物流任务分批处理数据"));
            }
        }
    }

    /**
     * 分割订单列表，默认200单，有配置取配置参数
     *
     * @param orderIdList 数据集合
     */
    public void handleDataList(List<Long> orderIdList) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.debug(LogUtil.format("handleDataList方法内,orderIdList的值为:{}"),orderIdList);
        List<OcBOrderRelation> ocBOrderRelationList = omsOrderAutoDistributionLogisticsService.packageOcBOrderRelationList(orderIdList);
        for (OcBOrderRelation ocBOrderRelation : ocBOrderRelationList) {
            Long id = ocBOrderRelation.getOrderInfo().getId();
            // 分物流
            CpCLogistics cpCLogistics =  omsOrderDistributeLogisticsService.distributeLogistics(ocBOrderRelation);

            OcBOrder newOrder = new OcBOrder();
            newOrder.setId(id);
            if (cpCLogistics != null) {
                String message = "OrderId" + ocBOrderRelation.getOrderInfo().getId() + "订单自动分配物流补偿任务,分配到物流公司.返回物流公司Id[" + cpCLogistics.getId() + "][" + cpCLogistics.getEname() + "],操作时间" + df.format(new Date());
                log.debug(LogUtil.format(message, id));
                newOrder.setCpCLogisticsId(cpCLogistics.getId());
                newOrder.setCpCLogisticsEname(cpCLogistics.getEname());
                newOrder.setCpCLogisticsEcode(cpCLogistics.getEcode());
                //插入分物流日志
                omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderInfo().getId(), ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), message, null, null, SystemUserResource.getRootUser());
            } else {
                String errorMessage = "OrderId" + ocBOrderRelation.getOrderInfo().getId() + "订单自动分配物流补偿任务,分配物流未匹配到有效物流公司,操作时间" + df.format(new Date());
                log.debug(LogUtil.format(errorMessage, id));
                //为了清除原有的物流公司
                newOrder.setCpCLogisticsId(0L);
                newOrder.setCpCLogisticsEname("");
                newOrder.setCpCLogisticsEcode("");
                //插入日志
                omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderInfo().getId(), ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), errorMessage, null, null, SystemUserResource.getRootUser());
            }
            //校验更新,幂等
            if (orderIdempotent(id)) {
                //插入日志
                omsOrderLogService.addUserOrderLog(ocBOrderRelation.getOrderInfo().getId(), ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SUBLOGISTICS_SERVICE.getKey(), "幂等校验,订单已审核！", null, null, SystemUserResource.getRootUser());
                continue;
            }
            omsOrderService.updateOrderInfo(newOrder);
        }
    }

    //bug处理：保证幂等性；更新前再查询下该订单是否已审核
    private boolean orderIdempotent(Long id) {
        OcBOrder ocBOrder = omsOrderService.selectOrderInfo(id);
        return OmsOrderStatus.CHECKED.toInteger().equals(ocBOrder.getOrderStatus());
    }


}
