//package com.jackrain.nea.oc.oms.task.auditorder;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.RuntimeCompute;
//import com.jackrain.nea.utility.LogUtil;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * @Author: 黄世新
// * @Date: 2020/4/7 3:13 下午
// * @Version 1.0
// * 审核补偿任务
// */
//@Slf4j
//@Component
//public class AutoAuditOrderCompensateTask extends BaseR3Task implements IR3Task {
//
//
//    @Override
//    @XxlJob("AutoAuditOrderCompensateTask")
//    public RunTaskResult execute(JSONObject params) {
//        RunTaskResult result = new RunTaskResult();
//        try {
//            RuntimeCompute runtimeCompute = new RuntimeCompute();
//            runtimeCompute.startRuntime();
////            omsAuditCompensateService.omsAuditCompensate();
//            double usedTime = runtimeCompute.endRuntime();
//            result.setSuccess(true);
//            result.setMessage("AutoAuditOrderCompensateTask.审核补偿任务执行成功!UsedTime=" + usedTime);
//        } catch (Exception e) {
//            log.error(LogUtil.format("AutoAuditOrderCompensateTask.Execute.Error: {}"), Throwables.getStackTraceAsString(e));
//            result.setSuccess(false);
//            result.setMessage(e.getMessage());
//
//        }
//        return result;
//    }
//}
