package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.FindReturnOrderService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 退单传WMS定时任务
 *
 * @author: 郑立轩
 * @since: 2019/4/29
 * create at : 2019/4/29 16:41
 */
@Deprecated
@Slf4j
@Component
public class TimedReturnOrderTask extends BaseR3Task implements IR3Task {
    @Autowired
    private IpRpcService service;
    @Autowired
    private FindReturnOrderService returnOrderService;
    @Autowired
    private OmsReturnOrderService omsReturnOrderService;


    @Override
    @XxlJob("TimedReturnOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        Integer range = config.getProperty("lts.TimedReturnOrderTask.range", 200);
        try {
            List<OcBReturnOrder> returnOrders = returnOrderService.findReturnOrderService(range);
            // @20200722 非空才继续调用，否则不处理
            if (CollectionUtils.isNotEmpty(returnOrders)) {
                returnOrderService.findReturnOrderByWareHouse(returnOrders);
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            //发生异常 所有数据返回最开始的状态
            log.error(LogUtil.format("TimedReturnOrderTask.Execute Error:{}",  "TimedReturnOrderTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}


