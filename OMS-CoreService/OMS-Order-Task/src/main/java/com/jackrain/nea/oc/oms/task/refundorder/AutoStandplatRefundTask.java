package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.es.ES4IpStandPlatRefund;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * 通用退单 发货后 转单服务
 *
 * @author: 夏继超
 * @since: 2019/7/13
 * create at : 2019/7/13 15:17
 */

@Slf4j
@Component
public class AutoStandplatRefundTask extends BaseR3Task implements IR3Task {

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Override
    @XxlJob("AutoStandplatRefundTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoStandplatRefundTask.range", 1000);

            List<String> list = ES4IpStandPlatRefund.findRefundNoByTransStatus(0, pageSize);
            Long start = System.currentTimeMillis();
            if (log.isDebugEnabled()) {
                log.debug(" start AutoStandplatRefundTask list:{}", JSONObject.toJSONString(list));
            }
            for (String refundId : list) {
                JSONArray sendArr = new JSONArray();
                OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
                orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
                orderMqInfo.setChannelType(ChannelType.STANDPLAT);
                orderMqInfo.setOrderType(OrderType.REFUND);
                orderMqInfo.setOrderNo(refundId);
                JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(orderMqInfo), Feature.OrderedField);
                sendArr.add(jsonObject);
                try {
                    String refundTopic = MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER;
                    String refundTag = MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER;
                    MqSendResult sendResult = defaultProducerSend.sendTopic(refundTopic, refundTag, sendArr.toJSONString(), null);
                    log.info(LogUtil.format("退单补偿发送MQ结果: {} {}"), refundId, sendResult.getMessageId());
                } catch (Exception e) {
                    log.error(LogUtil.format("退单补偿发送MQ异常: {} {}"), refundId, Throwables.getStackTraceAsString(e));
                }
            }
            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms");
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoStandplatRefundTask,异常:{}", "AutoStandplatRefundTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }

}
