package com.jackrain.nea.oc.oms.task.st;

import com.google.common.base.Throwables;
import com.jackrain.nea.st.service.StExpiryDateCaseService;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 商品效期策略自动结案
 *
 * <AUTHOR>
 * @since 2025-07-03 15:17
 */
@Slf4j
@Component
public class StCExpiryDateAutoFinishTask {

    @Autowired
    private StExpiryDateCaseService stExpiryDateCaseService;

    @XxlJob("StExpiryDateAutoFinishTask")
    public RunTaskResult execute() {
        log.debug(LogUtil.format("StExpiryDateAutoFinishTask.start"));
        Long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();

        try {
            int count = stExpiryDateCaseService.autoFinishByBusinessParams();
            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms,结案数量:" + count);
        } catch (Exception e) {
            log.error(LogUtil.format("StExpiryDateAutoFinishTask.Error：{}"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }

        return result;
    }
}
