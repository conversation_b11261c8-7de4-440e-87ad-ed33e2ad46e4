package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpJitXDelivery;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.process.jitx.feedback.JitxFeedBackDeliveryProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 唯品会寻仓反馈补偿任务
 * 1、查询ES中SYNSTATUS=0的单据
 *
 * @author: chenxiulou
 * @since: 2019-06-28
 * create at : 2019-03-15 23:10
 */
@Component
@Slf4j
public class AutoJitxFeedBackTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private JitxFeedBackDeliveryProcessImpl jitxFeedBackDeliveryProcess;

    @Value("${lts.jitx.feedback.unsync.task.size:500}")
    private int pageSize;

    @Override
    @XxlJob("AutoJitxFeedBackTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            List<String> orderNoList = ES4IpJitXDelivery.findOrderSnBySynStatus(0, pageSize, false);

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoJitxFeedBackTask.OrderNoList={}"), JSONObject.toJSONString(orderNoList));
            }

            List<IpJitxDeliveryRelation> deliveryRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpJitxDeliveryRelation jitDeliveryRelation = this.ipJitxDeliveryService.selectJitxDelivery(orderNo);
                if (jitDeliveryRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoJitxFeedBackTask.Order.NotExist!###OrderNo="
                            + orderNo);
                    log.error(LogUtil.format(errorMessage));
                } else {
                    deliveryRelationList.add(jitDeliveryRelation);
                }
            }

            threadOrderProcessor.startMultiThreadExecute(this.jitxFeedBackDeliveryProcess, deliveryRelationList);

            result.setSuccess(true);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("AutoJitxFeedBackTask.Execute.Error: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}
