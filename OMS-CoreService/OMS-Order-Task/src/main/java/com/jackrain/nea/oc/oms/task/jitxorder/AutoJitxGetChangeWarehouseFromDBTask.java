//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
//import com.jackrain.nea.oc.oms.services.JitxService;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @create 2020-12-15
// * @desc JITX订单获取改仓工单任务
// **/
//@Slf4j
//@Component
//@Deprecated
//public class AutoJitxGetChangeWarehouseFromDBTask extends AbstractJitxChangeWarehouseTask {
//
//    @Autowired
//    private JitxService jitxService;
//
//    @Value("${oms.oc.order.jitx.getChangeWarehouse.pull.num:1000}")
//    private Integer pullNum;
//
//    @Override
//    protected String threadPoolName() {
//        return "R3_OMS_JITX_GET_CHANGE_WAREHOUSE_THREAD_POOL_%d";
//    }
//
//    @Override
//    protected String executeSqlWhere() {
//        return " WHERE CREATED_STATUS = 1 AND WORKFLOW_STATE IN ('100','200') AND ISACTIVE = 'Y' ";
//    }
//
//    @Override
//    protected String executeSqlOrder() {
//        return null;
//    }
//
//    @Override
//    protected Integer getPullNum() {
//        return pullNum;
//    }
//
//    @Override
//    protected void exceute(List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList, User operateUser) {
//        if (log.isDebugEnabled()) {
//            log.debug("JITX订单获取改仓工单任务，modifyWarehouseLogListSize：{}", modifyWarehouseLogList.size());
//        }
//        jitxService.getChangeWarehouseWorkflows(modifyWarehouseLogList, operateUser);
//    }
//}
