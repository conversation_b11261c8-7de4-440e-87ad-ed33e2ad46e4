package com.jackrain.nea.oc.oms.task.voidomsorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBOrderHoldItemMapper;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * 自动释放订单
 *
 * @author: 江家雷
 * create at: 2020/07/08 11:23
 */
@Slf4j
@Component
public class AutoUnHoldOrderTask extends BaseR3Task implements IR3Task {


    private final String tableName = "OC_B_ORDER_HOLD_ITEM";

    protected String nameSpaceKey = "lts";

    protected String statusKey = "oms.task.order.hold.item.status";

    protected String eachSizeKey = "oms.order.hold.item.task.size";

    @Autowired
    protected OcBOrderHoldItemMapper ocBOrderHoldItemMapper;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private ThreadPoolTaskExecutor orderHoldItemTaskThreadPoolExecutor;

    @Override
    @XxlJob("AutoUnHoldOrderTask")
    public RunTaskResult execute(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("开始执行HOLD单自动释放任务", "开始执行HOLD单自动释放任务"));
        }
        long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {

            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer size = config.getProperty(eachSizeKey, 2400);
            String orderHoldItemStatus = config.getProperty(statusKey, "Y");
            List<Long> odrIds = ocBOrderHoldItemMapper.selectDynamicTaskOrderHoldItem(tableName, size, orderHoldItemStatus);
            if (CollectionUtils.isEmpty(odrIds)) {
                result.setSuccess(true);
                result.setMessage("success");
                return result;
            }
            List<List<Long>> lists = ListSplitUtil.averageAssign(odrIds, 24);

            List<Future<Boolean>> results = new ArrayList<>();
            for (List<Long> data : lists) {
                results.add(orderHoldItemTaskThreadPoolExecutor.submit(new AutoUnHoldOrderTask.OmsUnHoldOrderCallable(data)));
            }
            for (Future<Boolean> futureResult : results) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("AutoUnHoldOrderTask每个线程执行结果：{}", "AutoUnHoldOrderTask每个线程执行结果"), JSON.toJSONString(futureResult.get()));
                }
            }
            result.setSuccess(true);
            result.setMessage("AutoUnHoldOrderTask execute finished, useTime: " + (System.currentTimeMillis() - start));
        } catch (Exception e) {
            log.error(LogUtil.format("AutoUnHoldOrderTask 执行异常,异常信息:{}", "AutoUnHoldOrderTask执行异常"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("JAutoUnHoldOrderTask执行结束，耗时={}", "AutoUnHoldOrderTask执行结束"), System.currentTimeMillis() - start);
        }
        return result;
    }

    /**
     * inner thread class
     */
    class OmsUnHoldOrderCallable implements Callable<Boolean> {

        private final List<Long> data;

        public OmsUnHoldOrderCallable(List<Long> data) {
            this.data = data;
        }

        @Override
        public Boolean call() {
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            try {
                ocBOrderHoldService.autoUnHoldOrder(data);
            } catch (Exception e) {
                log.error(LogUtil.format("AutoUnHoldOrderTask 自动释放Hold单异常,异常信息:{}", "自动释放Hold单异常"), Throwables.getStackTraceAsString(e));
            }
            return true;
        }
    }
}
