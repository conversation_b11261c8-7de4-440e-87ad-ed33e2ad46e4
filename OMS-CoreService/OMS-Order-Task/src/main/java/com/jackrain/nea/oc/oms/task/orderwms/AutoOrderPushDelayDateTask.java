package com.jackrain.nea.oc.oms.task.orderwms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToWmsTaskMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.task.OcBToWmsTask;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.st.service.OrderPushDelayStrategyService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;


/***
 * 订单传wms中间表填充推单延时时间task
 */
@Slf4j
@Component
public class AutoOrderPushDelayDateTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsWmsTaskService wmsTaskService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBToWmsTaskMapper ocBToWmsTaskMapper;
    @Autowired
    private OrderPushDelayStrategyService orderPushDelayStrategyService;
    @Autowired
    private ThreadPoolTaskExecutor toWmsPushDelayTaskThreadPoolExecutor;


    @Override
    @XxlJob("AutoOrderPushDelayDateTask")
    public RunTaskResult execute(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("订单传wms中间表填充推单延时时间定时任务", "订单传wms中间表填充推单延时时间"));
        }
        RunTaskResult result = new RunTaskResult();
        try {

            long start = System.currentTimeMillis();
            final String taskTableName = "oc_b_to_wms_task";

            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);

            Integer pageSize = config.getProperty("lts.AutoOrderWmsTask.range", 1000) * 24;
            Integer orderStatus = config.getProperty("lts.AutoOrderWmsTask.status", 0);
            List<Long> list = wmsTaskService.selectByNodeSqlWithPushDelayNull(pageSize, taskTableName, orderStatus);
            if (CollectionUtils.isEmpty(list)) {
                result.setSuccess(true);
                result.setMessage("无订单");
                return result;
            }
            List<List<Long>> lists = ListSplitUtil.averageAssign(list, 24);

            for (List<Long> data : lists) {
                results.add(toWmsPushDelayTaskThreadPoolExecutor.submit(new CallableWmsTaskWithResult(data)));
            }

            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                log.debug(LogUtil.format("订单传wms中间表填充推单延时时间定时任务:线程结果{}",
                        "订单传wms中间表填充推单延时时间"), JSON.toJSONString(futureResult.get()));
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoOrderPushDelayDateTask 填充推单延时时间服务定时任务完成 useTime : {}", "填充推单延时时间服务定时任务完成"), System.currentTimeMillis() - start);
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("填充推单延时时间服务定时任务执行失败！{}",  "填充推单延时时间服务定时任务执行失败"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        } finally {
            //  executor.shutdown();
        }
        return result;
    }

    /**
     * 开启线程类
     */
    class CallableWmsTaskWithResult implements Callable<Boolean> {

        private final List<Long> data;

        public CallableWmsTaskWithResult(List<Long> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            //推单
            if (CollectionUtils.isNotEmpty(data)) {
                setPushDelayDate(data);
            }
            return true;
        }

    }

    /**
     * @param list
     * @return void
     * <AUTHOR>
     * @Description 修改中间表推单延时时间
     * @Date 13:56 2020/9/17
     **/
    private void setPushDelayDate(List<Long> list) {
        List<List<Long>> lists = BllCommonUtil.getBasePageList(list, 1000);
        List<OcBOrder> orderList = new ArrayList<>();
        for (List<Long> ids : lists) {
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(ids);
            if (CollectionUtils.isNotEmpty(ocBOrders)) {
                orderList.addAll(ocBOrders);
            }
        }
        if (CollectionUtils.isEmpty(orderList)) {
            log.error("填充推单延时时间 查询不到订单信息");
            return;
        }
        for (OcBOrder order : orderList) {
            Date orderPushDelayDate = orderPushDelayStrategyService.getOrderPushDelayDate(order);
            if (orderPushDelayDate != null) {
                OcBToWmsTask task = new OcBToWmsTask();
                task.setPushDelayDate(orderPushDelayDate);
                ocBToWmsTaskMapper.update(task, new QueryWrapper<OcBToWmsTask>().lambda()
                        .eq(OcBToWmsTask::getOrderId, order.getId()));
            }
        }

    }

}

