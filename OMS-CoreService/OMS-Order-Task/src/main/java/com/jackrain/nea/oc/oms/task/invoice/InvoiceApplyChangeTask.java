package com.jackrain.nea.oc.oms.task.invoice;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Stopwatch;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.service.OrderInvoiceChangeTaskService;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName InvoiceApplyChangeTask
 * @Description
 * @Date 2022/9/7 下午4:41
 * @Created by wuhang
 */
@Slf4j
@Component
public class InvoiceApplyChangeTask implements IR3Task {

    @Autowired
    private OrderInvoiceChangeTaskService service;

    @Override
    @XxlJob("InvoiceApplyChangeTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        Stopwatch started = Stopwatch.createStarted();
        try{
            service.change();
            long time = started.elapsed(TimeUnit.MILLISECONDS);
            result.setSuccess(true);
            result.setMessage("耗时：" + time + "ms");
        }catch (Exception e){
            e.printStackTrace();
            log.error(LogUtil.format("InvoiceApplyChangeTask.Error：{}"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
