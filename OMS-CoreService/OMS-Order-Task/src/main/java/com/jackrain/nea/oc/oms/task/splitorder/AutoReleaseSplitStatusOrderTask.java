package com.jackrain.nea.oc.oms.task.splitorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AutoSplitStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OmsOrderAutoSplitService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.util.Tools;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 自动拆单订单补偿定时任务
 *
 * @author: hulinyang
 * @since: 2019/9/16
 * create at : 2019/9/16 15:06
 */
@Deprecated
@Slf4j
@Component
public class AutoReleaseSplitStatusOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsOrderAutoSplitService omsOrderAutoSplitService;

    @Override
    @XxlJob("AutoReleaseSplitStatusOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            RuntimeCompute runtimeCompute = new RuntimeCompute();
            runtimeCompute.startRuntime();
            //默认60分钟
            Long autoReleaseSplitTime = Tools.getLong(AdParamUtil.getParam("oms.oc.order.autoReleaseSplitOrder.time"), 60);
            if (log.isDebugEnabled()) {
                log.debug("订单拆单补偿任务控制拉取时间##单位:分钟##autoReleaseSplitTime:{}", autoReleaseSplitTime);
            }

            int pullNum = Tools.getInt(AdParamUtil.getParam("oms.oc.order.autoReleaseSplitOrder.pull.num"), 200);
            if(log.isDebugEnabled()) {
                log.debug("订单自动拆单补偿单次最大拉取记录数{}", pullNum);
            }
            List<Long> orderIdList = ES4Order.findIdByOrderStatusAndIsIntercept(0, pullNum, autoReleaseSplitTime);
            if (log.isDebugEnabled()) {
                log.debug("订单自动拆单需要补偿的订单数量为{}", orderIdList.size());
            }
            List<OcBOrder> ocBOrderList = new ArrayList<>();
            List<Long> newOrderIdList = new ArrayList<>();
            for (Long orderId : orderIdList) {
                log.debug("订单orderId" + " -- " + orderId + "启动拆单补偿任务");
                OcBOrder orderInfo = ocBOrderMapper.selectById(orderId);
                if (orderInfo == null) {
                    String errorMessage = Resources.getMessage("AutoReleaseSplitStatusOrderTask Order NotExist!OrderId="
                            + orderId);
                    log.error(errorMessage);
                } else {
                    newOrderIdList.add(orderId);
                    orderInfo.setIsSplit(AutoSplitStatus.UN_SPLIT.toInteger());
                    orderInfo.setModifieddate(new Date());
                    ocBOrderList.add(orderInfo);
                }
            }
            //批量更新数据并更新ES
            if (CollectionUtils.isNotEmpty(orderIdList)) {
                ocBOrderMapper.updateSplitStatusList(AutoSplitStatus.UN_SPLIT.toInteger(), newOrderIdList);
                omsOrderAutoSplitService.batchTranSplitOrderPushEs(ocBOrderList);
            }
            result.setSuccess(true);
            double usedTime = runtimeCompute.endRuntime();
            log.debug("自动拆单订单补偿定时任务执行成功!UsedTime=" + usedTime);
            result.setSuccess(true);
            result.setMessage("自动拆单订单补偿定时任务执行成功!UsedTime=" + usedTime);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("AutoReleaseSplitStatusOrderTask.Execute Error", ex);
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}