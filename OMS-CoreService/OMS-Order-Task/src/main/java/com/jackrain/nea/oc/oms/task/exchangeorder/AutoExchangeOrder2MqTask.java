//package com.jackrain.nea.oc.oms.task.exchangeorder;
//
//import com.alibaba.fastjson.JSONObject;
//import com.burgeon.mq.core.DefaultProducerSend;
//import com.burgeon.mq.model.MqSendResult;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
//import com.jackrain.nea.oc.oms.constant.MqConstants;
//import com.jackrain.nea.oc.oms.mapper.IpBTaobaoExchangeMapper;
//import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
//import com.jackrain.nea.oc.oms.model.enums.ChannelType;
//import com.jackrain.nea.oc.oms.model.enums.OperateType;
//import com.jackrain.nea.oc.oms.model.enums.OrderType;
//import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.services.IpTaobaoExchangeService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.utility.LogUtil;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.ExecutionException;
//import java.util.concurrent.Future;
//
///**
// * 淘宝退单补偿发送MQ任务
// *
// * <AUTHOR>
// * @since 2020-07-24
// * Created at 2020-07-24 17:53
// */
//@Component
//@Slf4j
//public class AutoExchangeOrder2MqTask extends BaseR3Task implements IR3Task {
//
////    @Autowired
////    private R3MqSendHelper r3MqSendHelper;
//
//    @Autowired
//    private DefaultProducerSend defaultProducerSend;
//
//    @Autowired
//    private IpBTaobaoExchangeMapper ipBTaobaoExchangeMapper;
//
//    @Autowired
//    private TransferOrderMqConfig transferOrderMqConfig;
//    @Autowired
//    private IpTaobaoExchangeService ipTaobaoExchangeService;
//    @Autowired
//    private ThreadPoolTaskExecutor tbExchangeOrderToMqThreadPoolExecutor;
//
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        String tableName = "ip_b_taobao_exchange";
//        String taskName = "AutoExchangeOrder2MqTask";
//        log.debug(LogUtil.format("{} execute...", taskName), taskName);
//        RunTaskResult result = new RunTaskResult();
//        long start = System.currentTimeMillis();
//        int timeStamp = (int) (start / 1000);
//        try {
//            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//            Integer size = config.getProperty("lts.AutoExchangeOrder2MqTask.range", DEFAULT_PAGE_SIZE);
//            Integer transCount = config.getProperty("lts.AutoExchangeOrder2MqTask.order.transCount", -1);
//            Integer minutes = config.getProperty("lts.AutoExchangeOrder2MqTask.order.minutes", 7200);
//
//            Map<String, String> topMap = null;
//            Set<String> nodes = topMap.keySet();
//            if (CollectionUtils.isEmpty(nodes)) {
//                result.setSuccess(false);
//                result.setMessage("请检查DRDS环境，node信息获取失败！");
//                return result;
//            }
//            List<Future<Integer>> results = new ArrayList<>();
//            for (String node : nodes) {
//                results.add(tbExchangeOrderToMqThreadPoolExecutor.submit(new UnTransferExchangeOrderToMqCallable(node, topMap.get(node), size,
//                        TransferOrderStatus.NOT_TRANSFER.toInteger(), transCount, timeStamp, minutes)));
//            }
//            int executeCount = 0;
//            for (Future<Integer> futureResult : results) {
//                try {
//                    executeCount += futureResult.get();
//                } catch (InterruptedException e) {
//                    log.error(LogUtil.format("Thread InterruptedException.异常: {}"), Throwables.getStackTraceAsString(e));
//                } catch (ExecutionException e) {
//                    log.error(LogUtil.format("Thread ExecutionException.异常: {}"), Throwables.getStackTraceAsString(e));
//                }
//            }
//            long end = System.currentTimeMillis();
//            log.debug(LogUtil.format("{} execute end, count: {}, useTime: {} ms"), taskName, executeCount, (end - start));
//            result.setSuccess(true);
//            result.setMessage(taskName + " 执行完毕, 数量：" + executeCount + ", 用时: " + (end - start) + " ms");
//        } catch (Exception ex) {
//            log.error(LogUtil.format("{} execute error: {}"), taskName, Throwables.getStackTraceAsString(ex));
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        }
//        return result;
//    }
//
//    /**
//     * 内部类，淘宝退单补偿发送到MQ
//     */
//    class UnTransferExchangeOrderToMqCallable implements Callable<Integer> {
//        private final String nodeName;
//        private final String tableName;
//        private final Integer eachSize;
//        private final Integer isTrans;
//        private final Integer lessThanTransCnt;
//        private final Integer timestamp;
//        private final Integer minutes;
//
//        public UnTransferExchangeOrderToMqCallable(String nodeName, String tableName, Integer eachSize, Integer isTrans
//                , Integer lessThanTransCnt, Integer timestamp, Integer minutes) {
//            this.nodeName = nodeName;
//            this.tableName = tableName;
//            this.eachSize = eachSize;
//            this.isTrans = isTrans;
//            this.lessThanTransCnt = lessThanTransCnt;
//            this.timestamp = timestamp;
//            this.minutes = minutes;
//        }
//
//        @Override
//        public Integer call() {
//            String threadName = Thread.currentThread().getName();
//            boolean hasNext = true;
//            int executeCnt = 0;
//            while (hasNext) {
//                List<String> disputeIds = ipBTaobaoExchangeMapper.selectDynamicExchange(nodeName, tableName, eachSize
//                        , isTrans, lessThanTransCnt, timestamp, minutes);
//                if (disputeIds == null || disputeIds.size() == 0) {
//                    log.warn(LogUtil.format("{} 未查到淘宝退单补偿数据"), threadName);
//                    return executeCnt;
//                }
//
//                // 如果size小于每次查询数则不存在需要查询的数据
//                if (disputeIds.size() < eachSize) {
//                    hasNext = false;
//                }
//
//                List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
//                for (String orderNo : disputeIds) {
//                    OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
//                    orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
//                    orderMqInfo.setChannelType(ChannelType.TAOBAO);
//                    orderMqInfo.setOrderType(OrderType.REFUND);
//                    orderMqInfo.setOrderNo(orderNo);
//                    mqInfoList.add(orderMqInfo);
//                }
//                String jsonValue = JSONObject.toJSONString(mqInfoList);
//                int step = 1;
//                try {
//                    PropertiesConf propertiesConf = ApplicationContextHandle.getBean(PropertiesConf.class);
//                    String refundTopic = propertiesConf.getProperty("r3.oc.oms.refundTransfer.mq.topic");
//                    String refundTag = propertiesConf.getProperty("r3.oc.oms.refundTransfer.mq.tag");
//                    if (StringUtils.isEmpty(refundTopic)) {
////                        refundTopic = transferOrderMqConfig.getSendTransferMqTopic();
//                        refundTopic = MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER;
////                        refundTag = transferOrderMqConfig.getSendTransferMqTag();
//                        refundTag = MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER;
//                    }
////                    String message = r3MqSendHelper.sendMessage(jsonValue, refundTopic, refundTag);
//                    MqSendResult result = defaultProducerSend.sendTopic(refundTopic, refundTag, jsonValue, null);
//                    log.debug(LogUtil.format("{} 淘宝退单补偿发送MQ结果：{}"), threadName, result.getMessageId());
//                    step = 2;
//                    ipTaobaoExchangeService.updateDynamicExchangeTimeStamp(nodeName, tableName, disputeIds, timestamp);
//                } catch (Exception e) {
//                    switch (step) {
//                        case 1:
//                            log.error(LogUtil.format("{} 淘宝退单补偿发送MQ异常：{}"),threadName, Throwables.getStackTraceAsString(e));
//                            break;
//                        case 2:
//                            log.error(LogUtil.format("{} 淘宝退单更新时间戳异常：{}"),threadName, Throwables.getStackTraceAsString(e));
//                            break;
//                        default:
//                            log.error(LogUtil.format("{} 淘宝退单补偿未知异常：{}"),threadName, Throwables.getStackTraceAsString(e));
//                            break;
//                    }
//                    return executeCnt;
//                }
//                log.debug(LogUtil.format("Thread: {}, Execute count: {}"),  threadName, disputeIds.size());
//                executeCnt += disputeIds.size();
//            }
//            return executeCnt;
//        }
//    }
//}
