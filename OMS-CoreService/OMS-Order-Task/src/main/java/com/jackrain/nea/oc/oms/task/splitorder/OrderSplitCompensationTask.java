package com.jackrain.nea.oc.oms.task.splitorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderSplitTaskMapper;
import com.jackrain.nea.oc.oms.model.SplitOrderMqInfo;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSplitTask;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.SendMQAsyncUtils;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;


/***
 * 拆单补偿任务从oc_b_order_split_task表读
 * @author: wang shuai
 * @since: 2020/12/14
 */
@Slf4j
@Component
public class OrderSplitCompensationTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderSplitTaskMapper ocBOrderSplitTaskMapper;
    @Autowired
    private SendMQAsyncUtils sendMQAsyncUtils;
    @Autowired
    private ThreadPoolTaskExecutor orderSplitThreadPoolExecutor;

    private static final String taskTableName = "oc_b_order_split_task";

    @Override
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        /**
         * 读取apollo的配置信息
         */
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        String separator = config.getProperty("lts.orderSplitTask.compensate.statusList.separator", ",");
        Integer pageSize = config.getProperty("lts.orderSplitTask.compensate.range", 200);
        String statusStr = config.getProperty("lts.orderSplitTask.compensate.statusList", "");
        Integer limitSplitTimes = config.getProperty("lts.orderSplitTask.compensate.limit.splitTimes", 3);

        String[] statusArray = statusStr.split(separator);
        List<String> statusList = null;
        if (statusArray != null && statusArray.length > 0) {
            statusList = new ArrayList<>();
            Collections.addAll(statusList, statusArray);
        }

        try {
            Map<String, String> topMap = null;
            Set<String> nodes = topMap.keySet();
            if (CollectionUtils.isEmpty(nodes)) {
                result.setSuccess(false);
                result.setMessage("请检查环境，node获取不到！！");
                return result;
            }

            List<Future<Boolean>> results = new ArrayList<>();
            for (String nodeName : nodes) {
                try {
                    Future<Boolean> exeResult = orderSplitThreadPoolExecutor.submit(new OrderSplitCompensationTask.CallableOrderSplitResult(nodeName, topMap.get(nodeName), statusList, limitSplitTimes, pageSize));
                    results.add(exeResult);
                } catch (Exception e) {
                    log.error(LogUtil.format("OrderSplitCompensationTask,异常：{}", "OrderSplitCompensationTask"), Throwables.getStackTraceAsString(e));
                }
            }
            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    log.debug(LogUtil.format("OrderSplitCompensationTask------>线程结果:{}",  "OrderSplitCompensationTask"), futureResult.get().toString());
                } catch (InterruptedException e) {
                    log.error(LogUtil.format("OrderSplitCompensationTask,异常：{}", "OrderSplitCompensationTask"), Throwables.getStackTraceAsString(e));
                }
            }
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("OrderSplitCompensationTask,异常：{}", "OrderSplitCompensationTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }


    /**
     * 开启线程类
     */
    class CallableOrderSplitResult implements Callable<Boolean> {

        private final String nodeName;
        private final String taskTableName;
        private final List<String> statusList;
        private final int limitSplitTimes;
        private final int pageSize;

        public CallableOrderSplitResult(String nodeName, String taskTableName, List<String> statusList, int limitSplitTimes, int pageSize) {
            this.nodeName = nodeName;
            this.taskTableName = taskTableName;
            this.statusList = statusList;
            this.limitSplitTimes = limitSplitTimes;
            this.pageSize = pageSize;
        }

        @Override
        public Boolean call() throws Exception {
            List<OcBOrderSplitTask> taskList = ocBOrderSplitTaskMapper.getCompensationNodeSql(nodeName, taskTableName, statusList, limitSplitTimes, pageSize);
            if (CollectionUtils.isNotEmpty(taskList)) {
                List<Long> orderIdList = taskList.stream().map(o -> o.getOcBOrderId()).collect(Collectors.toList());

                this.processTransactional(orderIdList);
            }
            return true;
        }

        @Transactional(rollbackFor = Exception.class)
        public void processTransactional(List<Long> orderIdList) {
            try {
                // 更新订单拆单任务为拆分中
                ocBOrderSplitTaskMapper.batchUpdateThree(1, orderIdList);

                // 发货缺货重新占单MQ
                sendCompensationSplitOrderMQ(orderIdList, true);
            } catch (Exception e) {
                log.error(LogUtil.format("OrderSplitCompensationTask,异常：{}", "OrderSplitCompensationTask"), Throwables.getStackTraceAsString(e));
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }
    }

    public void sendCompensationSplitOrderMQ(List<Long> orderIdList, boolean againExecOccupy) {
        List<SplitOrderMqInfo> splitOrderMqInfoList = new ArrayList<>();
        for (Long orderId : orderIdList) {
            SplitOrderMqInfo relation = new SplitOrderMqInfo();
            relation.setOrderId(orderId);
            relation.setAgainExecOccupy(againExecOccupy);
            splitOrderMqInfoList.add(relation);
        }
        String str = JSON.toJSONString(splitOrderMqInfoList);
        PropertiesConf propertiesConf = ApplicationContextHandle.getBean(PropertiesConf.class);
//        String topic = propertiesConf.getProperty("r3.oc.oms.split.compensate.mq.topic");
        String topic = MqConstants.TOPIC_R3_OC_OMS_CALL_COMPENSATE_AUTOSPLIT;
//        String tag = propertiesConf.getProperty("r3.oc.oms.split.compensate.mq.tag");
        String tag = MqConstants.TAG_R3_OC_OMS_CALL_COMPENSATE_AUTOSPLIT;
        String msgKey = UUID.randomUUID().toString();
        sendMQAsyncUtils.sendDelayMessage("default", "补偿缺货拆单", str, topic, tag, msgKey, 2L, null, null);
        log.info(this.getClass().getName() + " 补偿缺货拆单发送拆单MQ成功,msgKey:{}", msgKey);
        log.debug(LogUtil.format("补偿缺货拆单发送拆单MQ成功,msgKey:{}",  "OrderSplitCompensationTask"), msgKey);

    }
}

