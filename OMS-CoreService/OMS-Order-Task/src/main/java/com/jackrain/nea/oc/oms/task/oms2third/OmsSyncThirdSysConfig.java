package com.jackrain.nea.oc.oms.task.oms2third;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/23
 */
@Data
@Configuration
public class OmsSyncThirdSysConfig {


    /**
     * 中台--》 奇门POS  Topic :
     */
    @Value("${r3.oc.oms.toqimen.mq.topic:}")
    private String oms2QiMenTopic;

    /**
     * 中台--》 奇门POS  tag :
     */
    @Value("${r3.oc.oms.toqimen.mq.tag:}")
    private String oms2QiMenTag;


}
