package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.VipTransferReturnOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpVipReturnOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 16:16 2020/06/28
 * description ：唯品会退供单转换补偿任务
 * @ Modified By：
 */
@Component
@Slf4j
public class AutoVipReturnOrderTransferTask extends BaseR3Task implements IR3Task {
    @Autowired
    private IpVipReturnOrderService ipVipReturnOrderService;

    @Autowired
    private VipTransferReturnOrderProcessImpl transferReturnOrderProcess;

    @Override
    @XxlJob("AutoVipReturnOrderTransferTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {

            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoVipReturnOrderTransferTask.range", DEFAULT_PAGE_SIZE);

            List<String> orderNoList = ipVipReturnOrderService.selectUnTransferredReturnOrderFromEs(0,
                    pageSize);

            List<IpVipReturnOrderRelation> vipReturnOrderRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpVipReturnOrderRelation vipReturnOrderRelation = this.ipVipReturnOrderService.selectVipReturnOrderRelation(orderNo);
                if (vipReturnOrderRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoVipReturnOrderTransferTask.Order.NotExist!###OrderNo=" + orderNo);
                    log.error(errorMessage);
                } else {
                    if (StringUtils.isNotBlank(vipReturnOrderRelation.getOrderNo())
                            && CollectionUtils.isNotEmpty(vipReturnOrderRelation.getVipReturnOrderItems())) {
                        vipReturnOrderRelationList.add(vipReturnOrderRelation);
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(vipReturnOrderRelationList)) {
                threadOrderProcessor.startMultiThreadExecute(this.transferReturnOrderProcess, vipReturnOrderRelationList);
            }

            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoVipReturnOrderTransferTask,异常信息:{}", "唯品会退供单转换补偿任务"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;
    }
}
