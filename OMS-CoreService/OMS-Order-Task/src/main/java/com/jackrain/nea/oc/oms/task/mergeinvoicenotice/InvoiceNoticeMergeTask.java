package com.jackrain.nea.oc.oms.task.mergeinvoicenotice;


import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.services.InvoiceNoticeMergeService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 自动合并开票通知
 *
 * @author: huang.zaizai
 * @since: 2019-09-03
 * create at : 2019-09-03 9:34
 */
@Component
@Slf4j
public class InvoiceNoticeMergeTask extends BaseR3Task implements IR3Task {

    @Autowired
    private InvoiceNoticeMergeService mergeService;

    /**
     * 自动合并开票通知
     *
     * @return
     */
    @Override
    @XxlJob("InvoiceNoticeMergeTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug(LogUtil.format("InvoiceNoticeMergeTask.start"));
        Long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {
            ValueHolderV14 vh = mergeService.mergeInvoiceNotice(SystemUserResource.getRootUser());
            if (!vh.isOK()) {
                throw new NDSException(vh.getMessage());
            }
            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms");
        } catch (Exception e) {
            log.error(LogUtil.format("InvoiceNoticeMergeTask.Error：{}",  "自动合并开票通知"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
