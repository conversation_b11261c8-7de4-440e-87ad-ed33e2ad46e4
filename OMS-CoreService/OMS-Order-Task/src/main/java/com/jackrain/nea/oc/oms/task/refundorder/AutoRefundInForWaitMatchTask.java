package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.oc.oms.services.RefundInMakeUpService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description 退货入库单补偿服务 - 退货入库单主表入库状态为已入库（2），退货入库单明细存在退单编号，是否匹配为否
 * @author:洪艺安
 * @since: 2019/9/25
 * @create at : 2019/9/25 13:51
 * @DeprecatedCause 此补偿方法, 不能与自动匹配任务同时开启, 如要开启,需要对退货入库单进行加锁操作
 */
@Slf4j
@Component
@Deprecated
public class AutoRefundInForWaitMatchTask extends BaseR3Task implements IR3Task {
    @Autowired
    private RefundInMakeUpService service;
    private static final Integer TOTAL_CONST = 200;

    @Override
    @XxlJob("AutoRefundInForWaitMatchTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult result = new RunTaskResult();
        int range = TOTAL_CONST;
        try {
            range = params.getInteger("range");
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getSimpleName() + "退货入库单补偿服务场景2开始执行,设置的数据量为" + range);
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 获取Lts参数params：range异常！");
        }
        try {
            service.dealWaitMatchRefundInException(range);
            result.setSuccess(true);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
