package com.jackrain.nea.oc.oms.task.naika;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.request.naika.NaiKaUnfreezeItemRequest;
import com.jackrain.nea.hub.request.naika.NaiKaUnfreezeRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaUnfreezeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaikaVoidMapper;
import com.jackrain.nea.oc.oms.mapperservice.OcBOrderNaiKaUnfreezeMapperService;
import com.jackrain.nea.oc.oms.model.enums.UnFreezeEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OcBOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaUnfreeze;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @ClassName UnFreezeNaiKaOrderTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/6/29 17:23
 * @Version 1.0
 */
@Component
@Slf4j
public class UnFreezeNaiKaOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderNaiKaUnfreezeMapper naiKaUnfreezeMapper;
    @Autowired
    private OcBOrderNaiKaUnfreezeMapperService naiKaUnfreezeMapperService;
    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ThreadPoolTaskExecutor naikaUnfreezeThreadPoolExecutor;
    @Autowired
    private OcBOrderNaikaVoidMapper ocBOrderNaikaVoidMapper;

    @Override
    @XxlJob("UnFreezeNaiKaOrderTask")
    public RunTaskResult execute(JSONObject params) {

        RunTaskResult result = new RunTaskResult();
        try {
            long start = System.currentTimeMillis();
            final String taskTableName = "oc_b_order_naika_unfreeze";
            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            int pageSize = config.getProperty("lts.UnFreezeNaiKaOrderTask.range", 2400);
            List<OcBOrderNaikaUnfreeze> naikaUnfreezeList = naiKaUnfreezeMapper.selectUnFreezeNaiKaOrder(pageSize, taskTableName, 6);
            if (CollectionUtils.isEmpty(naikaUnfreezeList)) {
                result.setSuccess(true);
                result.setMessage("无订单");
                return result;
            }
            List<List<OcBOrderNaikaUnfreeze>> lists = ListSplitUtil.averageAssign(naikaUnfreezeList, 24);
            for (List<OcBOrderNaikaUnfreeze> data : lists) {
                results.add(naikaUnfreezeThreadPoolExecutor.submit(new UnFreezeNaiKaOrderTask.CallableNaiKaUnFreezeTaskWithResult(data)));
            }
            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("UnFreezeNaiKaOrderTask------>线程结果:{}"), futureResult.get().toString());
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("UnFreezeNaiKaOrderTask多线程获取InterruptedException异常：{}", "UnFreezeNaiKaOrderTask"), Throwables.getStackTraceAsString(e));
                }
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("UnFreezeNaiKaOrderTask 解冻奶卡任务完成 useTime : {}"), (System.currentTimeMillis() - start));
            }
            result.setSuccess(true);
        } catch (Exception e) {
            log.error(LogUtil.format("解冻奶卡任务执行失败,异常！{}", "UnFreezeNaiKaOrderTask"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }

    /**
     * 检查并更新奶卡状态为待作废
     * 在解冻成功后，检查是否存在奶卡作废记录，如果存在则将奶卡状态改为待作废
     *
     * @param ocBOrder 订单信息
     * @param unfreezeCodeList 解冻成功的奶卡编码列表
     */
    private void checkAndUpdateNaikaStatusForVoid(OcBOrder ocBOrder, List<String> unfreezeCodeList) {
        try {
            // 查询该订单是否存在奶卡作废记录
            List<OcBOrderNaikaVoid> voidList = ocBOrderNaikaVoidMapper.getByOcBOrderId(ocBOrder.getId());

            if (CollectionUtils.isNotEmpty(voidList)) {
                // 存在作废记录，将解冻成功的奶卡状态改为待作废
                for (String cardCode : unfreezeCodeList) {
                    List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaList(ocBOrder.getId(), cardCode);
                    if (CollectionUtils.isNotEmpty(ocBOrderNaiKaList)) {
                        for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKaList) {
                            OcBOrderNaiKa updateOcBOrderNaiKa = new OcBOrderNaiKa();
                            updateOcBOrderNaiKa.setId(ocBOrderNaiKa.getId());
                            updateOcBOrderNaiKa.setNaikaStatus(OmsOrderNaiKaStatusEnum.TO_VOID.getStatus());
                            updateOcBOrderNaiKa.setModifieddate(new Date());
                            ocBOrderNaiKaMapper.updateById(updateOcBOrderNaiKa);
                            log.info("解冻成功后发现存在作废记录，将奶卡状态改为待作废，订单ID={}，奶卡编码={}",
                                    ocBOrder.getId(), cardCode);
                        }
                    }
                }

                log.info("解冻成功后发现存在作废记录，更新订单奶卡状态为待作废，订单ID={}", ocBOrder.getId());
            }
        } catch (Exception e) {
            log.error("检查并更新奶卡状态为待作废异常，订单ID={}", ocBOrder.getId(), e);
            // 异常情况下，仍然按正常流程更新为解冻成功状态
            applicationContext.getBean(UnFreezeNaiKaOrderTask.class)
                    .updateNaiKaStatus(new ArrayList<>(unfreezeCodeList), ocBOrder.getId(),
                            OmsOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus(),
                            OcBOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus(), "");
        }
    }


    // 更新奶卡表数据
    @Transactional(rollbackFor = Exception.class)
    public void updateNaiKaStatus(List<String> cardCodeList, Long ocBOrderId, Integer status, Integer naiKaStatus, String errorMsg) {
        for (String cardCode : cardCodeList) {
            List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaList(ocBOrderId, cardCode);
            if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
                continue;
            }
            if (ObjectUtil.isNotEmpty(errorMsg) && errorMsg.length() > 255) {
                errorMsg = errorMsg.substring(0, 254);
            }
            for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKaList) {
                OcBOrderNaiKa updateOcBOrderNaiKa = new OcBOrderNaiKa();
                updateOcBOrderNaiKa.setNaikaStatus(status);
                updateOcBOrderNaiKa.setId(ocBOrderNaiKa.getId());
                updateOcBOrderNaiKa.setOcBOrderId(ocBOrderNaiKa.getOcBOrderId());
                updateOcBOrderNaiKa.setModifieddate(new Date());
                updateOcBOrderNaiKa.setUnfreezeErrorMsg(errorMsg);
                ocBOrderNaiKaMapper.updateById(updateOcBOrderNaiKa);
            }
        }
        // 更新零售发货单
        OcBOrder updateOcBOrder = new OcBOrder();
        updateOcBOrder.setId(ocBOrderId);
        updateOcBOrder.setModifieddate(new Date());
        updateOcBOrder.setToNaikaStatus(naiKaStatus);
        ocBOrderMapper.updateById(updateOcBOrder);

    }

    class CallableNaiKaUnFreezeTaskWithResult implements Callable<Boolean> {

        private final List<OcBOrderNaikaUnfreeze> data;

        public CallableNaiKaUnFreezeTaskWithResult(List<OcBOrderNaikaUnfreeze> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            for (OcBOrderNaikaUnfreeze naikaUnfreeze : data) {
                if (naikaUnfreeze.getUnfreezeTimes() > 5) {
                    continue;
                }

                OcBOrderNaikaUnfreeze updateNaikaUnfreeze = new OcBOrderNaikaUnfreeze();
                updateNaikaUnfreeze.setOcBOrderId(naikaUnfreeze.getOcBOrderId());
                List<OcBOrderNaikaUnfreeze> unfreezeList = naiKaUnfreezeMapper.selectUnFreezeNaiKaOrderByOrderId(naikaUnfreeze.getOcBOrderId());
                List<OcBOrderNaikaUnfreeze> unfreezeSuccessList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(unfreezeList)) {
                    // 过滤 如果之前有已经解冻完成的 则将此单也设置为完成
                    unfreezeSuccessList = unfreezeList.stream().filter(s -> s.getUnfreezeStatus().equals(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(unfreezeSuccessList)) {
                        updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
                        updateNaikaUnfreeze.setModifieddate(new Date());
                        updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
                        naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
                        continue;
                    }
                }
                // 根据零售发货单单id 查询零售发货单数据
                OcBOrder ocBOrder = ocBOrderMapper.get4NaiKaOrder(naikaUnfreeze.getOcBOrderId());
                List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(naikaUnfreeze.getOcBOrderId());
                if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
                    updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
                    updateNaikaUnfreeze.setModifieddate(new Date());
                    updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
                    naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
                    continue;
                }

                NaiKaUnfreezeRequest unfreezeRequest = new NaiKaUnfreezeRequest();
                unfreezeRequest.setBillNo(ocBOrder.getBillNo());
                unfreezeRequest.setTid(ocBOrder.getSourceCode());
                CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(ocBOrder.getPlatform()));
                unfreezeRequest.setPlatformCode(cpCPlatform.getEcode());
                unfreezeRequest.setShopCode(ocBOrder.getCpCShopEcode());
                if (ocBOrder.getIsResetShip() == null || ObjectUtil.equals(0, ocBOrder.getIsResetShip())) {
                    unfreezeRequest.setType(1);
                } else {
                    unfreezeRequest.setType(2);
                }
                List<NaiKaUnfreezeItemRequest> unfreezeItemRequests = new ArrayList<>();
                List<String> unfreezeCodeList = new ArrayList<>();
                // 构造详情数据
                List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectAllOrderItem(ocBOrder.getId());
                for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                    List<OcBOrderNaiKa> ocBOrderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderIdAndItemId(ocBOrderItem.getOcBOrderId(), ocBOrderItem.getId());
                    if (CollectionUtils.isNotEmpty(ocBOrderNaiKas)) {
                        List<String> cardCodeList = new ArrayList<>();
                        for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKas) {
                            if (ObjectUtil.equals(ocBOrderNaiKa.getNaikaStatus(), OmsOrderNaiKaStatusEnum.FREEZE.getStatus())
                                    || ObjectUtil.equals(ocBOrderNaiKa.getNaikaStatus(), OmsOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus())) {
                                cardCodeList.add(ocBOrderNaiKa.getCardCode());
                            }
                        }
                        NaiKaUnfreezeItemRequest unfreezeItemRequest = new NaiKaUnfreezeItemRequest();
                        unfreezeItemRequest.setOid(ocBOrderItem.getOoid());
                        unfreezeItemRequest.setSubOrderNO(ocBOrderItem.getOoid());
                        unfreezeItemRequest.setCardList(cardCodeList);
                        unfreezeCodeList.addAll(cardCodeList);
                        unfreezeItemRequests.add(unfreezeItemRequest);
                    }
                }
                unfreezeRequest.setUnfreezeItemRequests(unfreezeItemRequests);


                try {

                    if (CollectionUtils.isEmpty(unfreezeCodeList)) {
                        updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
                        updateNaikaUnfreeze.setUnfreezeTimes(naikaUnfreeze.getUnfreezeTimes() + 1);
                        updateNaikaUnfreeze.setModifieddate(new Date());
                        updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
                        naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
                        continue;
                    }

                    ValueHolderV14 valueHolderV14 = naiKaOrderCmd.orderUnfreeze(unfreezeRequest);
                    // 根据结果 对奶卡解冻表数据进行处理
                    if (ObjectUtil.equals(ResultCode.FAIL, valueHolderV14.getCode())) {
                        // 执行失败 则对解冻次数以及状态进行修改
                        updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_FAIL.getStatus());
                        updateNaikaUnfreeze.setUnfreezeTimes(naikaUnfreeze.getUnfreezeTimes() + 1);
                        updateNaikaUnfreeze.setModifieddate(new Date());
                        updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
                        naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
                        applicationContext.getBean(UnFreezeNaiKaOrderTask.class)
                                .updateNaiKaStatus(new ArrayList<>(unfreezeCodeList), ocBOrder.getId(), OmsOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus(), OcBOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus(), valueHolderV14.getMessage());
                    } else {
                        // 执行成功
                        updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_SUCCESS.getStatus());
                        updateNaikaUnfreeze.setUnfreezeTimes(0);
                        updateNaikaUnfreeze.setModifieddate(new Date());
                        updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
                        updateNaikaUnfreeze.setUnfreezeTimes(0);
                        naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
                        applicationContext.getBean(UnFreezeNaiKaOrderTask.class)
                                .updateNaiKaStatus(new ArrayList<>(unfreezeCodeList), ocBOrder.getId(),
                                        OmsOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus(),
                                        OcBOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus(), "");
                        // 检查是否需要作废奶卡
                        checkAndUpdateNaikaStatusForVoid(ocBOrder, unfreezeCodeList);
                    }
                } catch (Exception e) {
                    updateNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE_FAIL.getStatus());
                    updateNaikaUnfreeze.setUnfreezeTimes(naikaUnfreeze.getUnfreezeTimes() + 1);
                    updateNaikaUnfreeze.setModifieddate(new Date());
                    updateNaikaUnfreeze.setId(naikaUnfreeze.getId());
                    naiKaUnfreezeMapperService.updateById(updateNaikaUnfreeze);
                    applicationContext.getBean(UnFreezeNaiKaOrderTask.class)
                            .updateNaiKaStatus(new ArrayList<>(unfreezeCodeList), ocBOrder.getId(), OmsOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus(), OcBOrderNaiKaStatusEnum.FREEZE_FAILED.getStatus(), e.getMessage());
                }
            }
            return true;
        }
    }
}
