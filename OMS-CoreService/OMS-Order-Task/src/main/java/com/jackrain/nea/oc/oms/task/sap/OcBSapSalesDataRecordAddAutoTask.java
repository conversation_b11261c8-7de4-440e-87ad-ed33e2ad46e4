package com.jackrain.nea.oc.oms.task.sap;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordAddTaskService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/6/9
 * description :出入库插入sap销售记录任务表
 */
@Component
@Slf4j
public class OcBSapSalesDataRecordAddAutoTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBSapSalesDataRecordAddTaskService service;
    @Autowired
    private ThreadPoolTaskExecutor sapSalesDataRecordAddThreadPoolExecutor;

    @Override
    @XxlJob("OcBSapSalesDataRecordAddAutoTask")
    public RunTaskResult execute(JSONObject params) {
        if (log.isDebugEnabled()) {
            log.debug("Start OcBSapSalesDataRecordAddTask.execute");
        }
        RunTaskResult result = new RunTaskResult();
        try {
            final String taskTableName = "oc_b_sap_sales_data_record_add_task";
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            int pageSize = config.getProperty("lts.sapSalesDataRecordAdd.range", 2000);
            List<Long> ids = service.selectOcBSapSalesDataRecordAddTaskSqlList(pageSize, taskTableName);
            if (CollectionUtils.isEmpty(ids)) {
                result.setSuccess(true);
                result.setMessage("success");
                return result;
            }
            List<List<Long>> lists = ListSplitUtil.averageAssign(ids, 24);
            List<Future<Boolean>> results = new ArrayList<>();
            for (List<Long> data : lists) {
                results.add(sapSalesDataRecordAddThreadPoolExecutor.submit(new CallableSelectSapSalesDataRecordAddList(data)));
            }
            result.setSuccess(true);
            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                if (!futureResult.get()) {
                    result.setSuccess(false);
                }
            }
        } catch (Exception e) {
            log.error("OcBSapSalesDataRecordAddTask.execute.error", e);
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }

    class CallableSelectSapSalesDataRecordAddList implements Callable<Boolean> {

        private final List<Long> data;

        public CallableSelectSapSalesDataRecordAddList(List<Long> data) {
            this.data = data;
        }

        @Override
        public Boolean call() {
            try {
                if (CollectionUtils.isNotEmpty(data)) {
                    service.addByTask(data);
                }
                return Boolean.TRUE;
            } catch (Exception e) {
                log.error("OcBSapSalesDataRecordAddTask.execute.call.error", e);
                return Boolean.FALSE;
            }
        }
    }
}
