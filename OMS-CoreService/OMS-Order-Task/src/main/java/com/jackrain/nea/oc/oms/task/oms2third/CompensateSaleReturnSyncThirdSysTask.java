//package com.jackrain.nea.oc.oms.task.oms2third;
//
//import org.springframework.stereotype.Component;
//
///**
// * 传互道
// *
// * @Desc : 销售退补偿任务
// * <AUTHOR> xiWen
// * @Date : 2020/8/21
// */
//@Component
//public class CompensateSaleReturnSyncThirdSysTask extends AbsOmsSyncThirdSysCompensateTask {
//
//
//    /**
//     * 退货互道task
//     */
//    @Override
//    protected String getTaskTableName() {
//        return "OC_B_TASK_RETURN_SYNC";
//    }
//
//    @Override
//    protected String getTaskStatusCol() {
//        return "NEXT_TAO_STATUS";
//    }
//
//    @Override
//    protected int getTaskStatusVal() {
//        return 0;
//    }
//
//    @Override
//    protected String getTaskTypeCol() {
//        return "IS_NEXT_TAO";
//    }
//
//    @Override
//    protected int getTaskTypeVal() {
//        return 1;
//    }
//
//    /**
//     * 退货单
//     */
//    @Override
//    protected String getOrigTableName() {
//        return "OC_B_RETURN_ORDER";
//    }
//
//    @Override
//    protected String getOrigStatusCol() {
//        return "RESERVE_BIGINT09";
//    }
//
//    @Override
//    protected String getOrigSendTimesCol() {
//        return "RESERVE_BIGINT10";
//    }
//
//    /**
//     * 退货单索引
//     */
//    @Override
//    protected String getOrigEsIndex() {
//        return "oc_b_return_order";
//    }
//
//    @Override
//    protected String getOrigEsType() {
//        return "oc_b_return_order";
//    }
//
//
//}
