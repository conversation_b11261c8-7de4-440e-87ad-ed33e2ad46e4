package com.jackrain.nea.oc.oms.task;

import com.jackrain.nea.oc.oms.process.MultiThreadOrderProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 基础R3任务类
 *
 * @author: 易邵峰
 * @since: 2019-03-23
 * create at : 2019-03-23 12:22
 */
@Slf4j
public class BaseR3Task {

    /**
     * 默认每次查询页数信息
     */
    protected static final int DEFAULT_PAGE_SIZE = 200;

    @Autowired
    protected MultiThreadOrderProcessor threadOrderProcessor;


}
