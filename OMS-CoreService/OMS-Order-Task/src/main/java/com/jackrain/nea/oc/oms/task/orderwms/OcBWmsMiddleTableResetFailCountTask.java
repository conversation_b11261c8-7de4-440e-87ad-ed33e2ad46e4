package com.jackrain.nea.oc.oms.task.orderwms;


import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.nums.OcBWmsMiddleTableEnum;
import com.jackrain.nea.oc.oms.services.task.OcBWmsMiddleTableResetFailCountService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 退货包裹状态中间表-重置失败次数
 *
 * <AUTHOR>
 * @since 2025-06-16 15:27
 */
@Slf4j
@Component
public class OcBWmsMiddleTableResetFailCountTask extends BaseR3Task implements IR3Task {

    @Resource
    private ThreadPoolTaskExecutor commonThreadPoolExecutor;
    @Autowired
    private OcBWmsMiddleTableResetFailCountService service;

    @Override
    @XxlJob("OcBWmsMiddleTableResetFailCountTask")
    public RunTaskResult execute(JSONObject params) {
        log.info("OcBWmsMiddleTableResetFailCountTask.execute.begin");
        RunTaskResult runTaskResult = new RunTaskResult();
        runTaskResult.setSuccess(true);
        runTaskResult.setMessage("重置失败次数成功");
        for (OcBWmsMiddleTableEnum tableEnum : OcBWmsMiddleTableEnum.values()) {
            commonThreadPoolExecutor.submit(() -> service.resetFailCount(tableEnum));
        }
        return runTaskResult;
    }
}
