package com.jackrain.nea.oc.oms.task.logisticsIntercept;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.ip.model.TradeMemoUpdateModel;
import com.jackrain.nea.ip.model.others.OrderRemarkSyncModel;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogisticsInterceptMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsIntercept;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName LogisticsInterceptSyncSellerMemoTask
 * @Description 同步卖家备注到平台
 * <AUTHOR>
 * @Date 2023/11/15 18:26
 * @Version 1.0
 */
@Slf4j
@Component
public class LogisticsInterceptSyncSellerMemoTask implements IR3Task {

    @Autowired
    private OcBOrderLogisticsInterceptMapper logisticsInterceptMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private IpRpcService ipRpcService;

    @Override
    @XxlJob("LogisticsInterceptSyncSellerMemoTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult runTaskResult = new RunTaskResult();
        List<OcBOrderLogisticsIntercept> logisticsInterceptList = logisticsInterceptMapper.select4SyncSellerMemo();
        if (CollectionUtil.isEmpty(logisticsInterceptList)) {
            runTaskResult.setSuccess(true);
            runTaskResult.setMessage("success");
            return runTaskResult;
        }
        List<Long> logisticsInterceptIdList = logisticsInterceptList.stream().map(OcBOrderLogisticsIntercept::getId).collect(Collectors.toList());
        // 先更新所有的同步卖家状态为 已同步
        logisticsInterceptMapper.batchUpdateSyncSellerMemo(logisticsInterceptIdList);
        // 根据平台单号进行分组
        Map<String, List<OcBOrderLogisticsIntercept>> logisticsInterceptMap = logisticsInterceptList.stream().collect(Collectors.groupingBy(OcBOrderLogisticsIntercept::getPlatformCode));
        for (String tid : logisticsInterceptMap.keySet()) {
            List<OcBOrderLogisticsIntercept> logisticsIntercepts = logisticsInterceptMap.get(tid);
            StringBuilder sb = new StringBuilder();
            OcBOrder order = new OcBOrder();
            Integer platform = null;
            for (OcBOrderLogisticsIntercept logisticsIntercept : logisticsIntercepts) {
                // 根据订单id 查询出来订单信息
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(logisticsIntercept.getOrderId());
                order = ocBOrder;
                platform = ocBOrder.getPlatform();
                // 目前只同步淘宝的卖家备注
                if (!(PlatFormEnum.TAOBAO.getCode().equals(platform) ||
                        PlatFormEnum.DOU_YIN.getCode().equals(platform) ||
                        PlatFormEnum.PINDUODUO.getCode().equals(platform) ||
                        PlatFormEnum.KUAISHOU.getCode().equals(platform))) {
                    break;
                }
                // 获取物流单号
                String expresscode = ocBOrder.getExpresscode();
                if (StringUtils.isNotEmpty(expresscode)) {
                    // 拼接回传的备注信息
                    sb.append("已拦截:");
                    sb.append(expresscode);
                    sb.append("  ");
                }
            }
            if (StringUtils.isNotEmpty(sb.toString())) {
                try {
                    List<Long> orderIdList = ocBOrderMapper.selectOcBOrderIdByTid(tid);
                    // 如果是淘宝平台
                    if (PlatFormEnum.TAOBAO.getCode().equals(platform)) {
                        TradeMemoUpdateModel request = new TradeMemoUpdateModel();
                        String shopSecretKey = cpRpcService.selectShopById(order.getCpCShopId()).getShopSecretKey();
                        String[] split = shopSecretKey.split("\\|");
                        String[] split1 = split[2].split(":");
                        String sessionKey = split1[1];
                        request.setOperateUser(SystemUserResource.getRootUser());
                        request.setTid(Long.valueOf(order.getTid()));
                        request.setFlag(Long.valueOf(order.getOrderFlag()));
                        request.setMemo(order.getSellerMemo() == null ? sb.toString() : order.getSellerMemo() + " " + sb);
                        request.setSessionKey(sessionKey);
                        request.setPlatform(order.getPlatform());
                        ipRpcService.tradeMemoUpdate(request);
                        for (Long orderId : orderIdList) {
                            OcBOrder updateOrder = new OcBOrder();
                            updateOrder.setId(orderId);
                            updateOrder.setSellerMemo(request.getMemo());
                            ocBOrderMapper.updateById(updateOrder);
                        }
                    } else if (PlatFormEnum.DOU_YIN.getCode().equals(platform) ||
                            PlatFormEnum.PINDUODUO.getCode().equals(platform) ||
                            PlatFormEnum.KUAISHOU.getCode().equals(platform)) {
                        // 如果是抖音、快手、拼多多 则走通用修改地址
                        OrderRemarkSyncModel model = new OrderRemarkSyncModel();
                        model.setPlatform(String.valueOf(platform));
                        model.setSellerNick(order.getCpCShopSellerNick());
                        model.setRemark(order.getSellerMemo() == null ? sb.toString() : order.getSellerMemo() + " " + sb);
                        model.setTid(order.getTid());
                        model.setIsAddStar("true");
                        model.setStar(order.getOrderFlag());
                        if (PlatFormEnum.DOU_YIN.getCode().equals(platform)) {
                            model.setStar("5");
                        }
                        ipRpcService.orderRemarkSync(model);
                        for (Long orderId : orderIdList) {
                            OcBOrder updateOrder = new OcBOrder();
                            updateOrder.setId(orderId);
                            updateOrder.setSellerMemo(model.getRemark());
                            ocBOrderMapper.updateById(updateOrder);
                        }
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("调用淘宝修改备注接口异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
                    OcBOrder updateOrder = new OcBOrder();
                    updateOrder.setId(order.getId());
                    updateOrder.setSysremark("修改备注同步平台异常:");
                    ocBOrderMapper.updateById(updateOrder);
                }
            }
        }
        runTaskResult.setSuccess(true);
        runTaskResult.setMessage("success");
        return runTaskResult;
    }
}
