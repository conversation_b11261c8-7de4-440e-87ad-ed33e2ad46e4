//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.ip.model.wing.WingReturnResult;
//import com.jackrain.nea.ip.model.wing.yy.JitxSxOrderAddRequest;
//import com.jackrain.nea.ip.model.wing.yy.JitxSxOrderCancelRequest;
//import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBJitxModifyWarehouseLogMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.task.OcBJitxDealerOrderTaskMapper;
//import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskOrderTypeEnum;
//import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskStatusEnum;
//import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskTypeEnum;
//import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
//import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.task.OcBJitxDealerOrderTask;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
//import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
//import com.jackrain.nea.rpc.IpRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.assertj.core.util.Lists;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//import org.springframework.util.ObjectUtils;
//
//import java.util.*;
//import java.util.concurrent.ArrayBlockingQueue;
//import java.util.concurrent.Callable;
//import java.util.concurrent.Future;
//import java.util.stream.Collectors;
//
///**
// * description：经销商占用 取消占用补偿
// *
// * <AUTHOR>
// * @date 2021/12/24
// */
//@Slf4j
//@Component
//public class AutoJitxDealerOrderMakeUpTask implements IR3Task {
//
//    @Autowired
//    protected IpRpcService ipRpcService;
//
//    @Autowired
//    private OcBJitxDealerOrderTaskMapper jitxDealerOrderTaskMapper;
//
//    @Autowired
//    private OcBJitxDealerOrderTaskService ocBJitxDealerOrderTaskService;
//
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//
//    @Autowired
//    private IpBTimeOrderVipMapper timeOrderVipMapper;
//
//    @Autowired
//    private OcBJitxDealerOrderTaskService dealerOrderTaskService;
//
//    @Autowired
//    private OcBJitxModifyWarehouseLogMapper modifyWarehouseLogMapper;
//    @Autowired
//    private ThreadPoolTaskExecutor dealerTaskMakeUpThreadPoolExecutor;
//
//    @Value("${oms.oc.order.jitx.dealerTask.pull.num:1000}")
//    private Integer pullNum;
//
//    @Value("${oms.oc.order.jitx.dealerTask.fail.num:10}")
//    private Integer failNumber;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        RunTaskResult result = new RunTaskResult();
//        ArrayBlockingQueue<Runnable> blockingQueue = new ArrayBlockingQueue<>(16);
//        long executeTime = System.currentTimeMillis();
//        final String taskTableName = "oc_b_jitx_dealer_order_task";
//        String type = JitxDealerTaskTypeEnum.YY_OCCUPY.getCode() +
//                "," + JitxDealerTaskTypeEnum.YY_CANCEL_OCCUPY.getCode() +
//                "," + JitxDealerTaskTypeEnum.YY_CHANGE_WAREHOUSE.getCode();
//        int state = JitxDealerTaskStatusEnum.FAIL.getCode();
//        Map<String, String> topMap = null;
//        Set<String> nodes = topMap.keySet();
//        if (CollectionUtils.isEmpty(nodes)) {
//            if (log.isDebugEnabled()) {
//                log.debug("{}.nodes.not.get!", this.getClass().getName());
//            }
//            result.setSuccess(false);
//            result.setMessage("请检查环境，node获取不到！！");
//            return result;
//        }
//        int dataNum = 0;
//        List<Future<Integer>> results = new ArrayList<>(nodes.size());
//        try {
//            for (String nodeName : nodes) {
//                results.add(dealerTaskMakeUpThreadPoolExecutor.submit(new CallableTobeDeakerTaskWithResult(nodeName, topMap.get(nodeName), state, type, failNumber)));
//            }
//
//            //线程执行结果获取
//            for (Future<Integer> futureResult : results) {
//                try {
//                    if (futureResult.get() != null) {
//                        dataNum += futureResult.get();
//                    }
//                    if (log.isDebugEnabled()) {
//                        log.debug("{}.线程结果={}", this.getClass().getName(), futureResult.get());
//                    }
//                } catch (Exception e) {
//                    log.error("AutoJitxDealerOrderMakeUpTask多线程获取ExecutionException异常:{}", Throwables.getStackTraceAsString(e));
//                }
//            }
//
//            if (log.isDebugEnabled()) {
//                log.debug("AutoJitxDealerOrderMakeUpTask.jitx经销商定时任务完成.useTime={}ms", (System.currentTimeMillis() - executeTime));
//            }
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            log.error("{}.经销商占用补偿异常{}", this.getClass().getName(), Throwables.getStackTraceAsString(ex));
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        }
//        result.setSuccess(true);
//        result.setMessage(String.format("处理数据条数:%d", dataNum));
//        return result;
//    }
//
//    private void handelData(List<OcBJitxDealerOrderTask> dbDataList) {
//        if (log.isDebugEnabled()) {
//            log.debug("{},当前处理数据：{}", this.getClass().getSimpleName(), JSON.toJSONString(dbDataList));
//        }
//        //占用类型的数据
//        List<OcBJitxDealerOrderTask> filteredAddList = new ArrayList<>(dbDataList.size());
//        //取消占用的数据
//        List<OcBJitxDealerOrderTask> filteredCancelList = new ArrayList<>(dbDataList.size());
//        //换仓反馈的数据
//        List<OcBJitxDealerOrderTask> exchangeStoreList = new ArrayList<>(dbDataList.size());
//
//        for (OcBJitxDealerOrderTask dbData : dbDataList) {
//            //不是未创建或者创建失败
//            if (!JitxDealerTaskStatusEnum.FAIL.getCode().equals(dbData.getState())) {
//                continue;
//            }
//            if (JitxDealerTaskTypeEnum.YY_OCCUPY.getCode().equals(dbData.getType())) {
//                filteredAddList.add(dbData);
//            }
//            if (JitxDealerTaskTypeEnum.YY_CANCEL_OCCUPY.getCode().equals(dbData.getType())) {
//                filteredCancelList.add(dbData);
//            }
//            if (JitxDealerTaskTypeEnum.YY_CHANGE_WAREHOUSE.getCode().equals(dbData.getType())) {
//                exchangeStoreList.add(dbData);
//            }
//        }
//
//        Map<String, Long> billNoAndIdAddMap = new HashMap<>(filteredAddList.size());
//        List<Long> addIdList = new ArrayList<>(filteredAddList.size());
//        //占用数据处理
//        if (CollectionUtils.isNotEmpty(filteredAddList)) {
//            if (log.isDebugEnabled()) {
//                log.debug(" 进入占用数据处理：{}", JSON.toJSONString(filteredAddList));
//            }
//            List<String> ocBOrderTidList = filteredAddList.stream().filter(x -> JitxDealerTaskOrderTypeEnum.OC_B_ORDER.getCode().equals(x.getOrderType())).map(OcBJitxDealerOrderTask::getTid).distinct().collect(Collectors.toList());
//            List<String> timeOrderTidList = filteredAddList.stream().filter(x -> JitxDealerTaskOrderTypeEnum.VIP_TIME_ORDER.getCode().equals(x.getOrderType())).map(OcBJitxDealerOrderTask::getTid).distinct().collect(Collectors.toList());
//            List<JitxSxOrderAddRequest> requestList = new ArrayList<>(filteredAddList.size());
//            try {
//                if (CollectionUtils.isNotEmpty(ocBOrderTidList)) {
//                    log.debug(" 发货单占用数据：{}", JSON.toJSONString(ocBOrderTidList));
//                    List<Long> orderIdList = ocBOrderMapper.listIdFromGsiBySourceCodes(ocBOrderTidList);
//                    if (CollectionUtils.isNotEmpty(orderIdList)) {
//                        List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsList(orderIdList);
//                        Map<String, List<OcBOrder>> groupByTidMap = ocBOrders.stream().collect(Collectors.groupingBy(OcBOrder::getTid));
//                        for (OcBJitxDealerOrderTask task : filteredAddList) {
//                            List<OcBOrder> orders = groupByTidMap.get(task.getTid());
//                            if (CollectionUtils.isNotEmpty(orders)) {
//                                billNoAndIdAddMap.put(task.getTid(), task.getId());
//                                JitxSxOrderAddRequest jitxSxOrderAddRequest = ocBJitxDealerOrderTaskService.getJitxSxOrderAddRequest(orders.get(0));
//                                if (jitxSxOrderAddRequest != null) {
//                                    addIdList.add(task.getId());
//                                    requestList.add(jitxSxOrderAddRequest);
//                                } else {
//                                    if (log.isDebugEnabled()) {
//                                        log.error("YY发货单占用组装参数异常：order.bill_no:{}", orders.get(0).getBillNo());
//                                    }
//                                }
//                            }
//                        }
//                    }
//                }
//            } catch (Exception e) {
//                log.error("{},发货单占用数据处理异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
//            }
//            try {
//                if (CollectionUtils.isNotEmpty(timeOrderTidList)) {
//                    log.debug(" 时效单占用数据：{}", JSON.toJSONString(timeOrderTidList));
//                    List<IpBTimeOrderVip> timeOrderVips = timeOrderVipMapper.selectList(new LambdaQueryWrapper<IpBTimeOrderVip>().in(IpBTimeOrderVip::getOrderSn, timeOrderTidList));
//                    Map<String, List<IpBTimeOrderVip>> groupByTidMap = timeOrderVips.stream().collect(Collectors.groupingBy(IpBTimeOrderVip::getOrderSn));
//                    for (OcBJitxDealerOrderTask task : filteredAddList) {
//                        List<IpBTimeOrderVip> orders = groupByTidMap.get(task.getTid());
//                        if (CollectionUtils.isNotEmpty(orders)) {
//                            billNoAndIdAddMap.put(task.getTid(), task.getId());
//                            JitxSxOrderAddRequest jitxSxOrderAddRequest = ocBJitxDealerOrderTaskService.getJitxSxOrderAddRequest(orders.get(0));
//                            if (jitxSxOrderAddRequest != null) {
//                                addIdList.add(task.getId());
//                                requestList.add(jitxSxOrderAddRequest);
//                            }
//                        }
//
//                    }
//                }
//            } catch (Exception e) {
//                log.error("{},发货单占用数据处理异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
//            }
//            if (CollectionUtils.isNotEmpty(requestList)) {
//                ValueHolderV14<WingReturnResult> v14 = ipRpcService.addSxJitxOrder(requestList);
//                //解析失败信息 更新失败信息
//                this.handelResult(billNoAndIdAddMap, addIdList, v14);
//            }
//        }
//        try {
//            //取消占用处理
//            Map<String, Long> billNoAndIdCancelMap = new HashMap<>(filteredCancelList.size());
//            if (CollectionUtils.isNotEmpty(filteredCancelList)) {
//                log.debug(" 取消占用数据：{}", JSON.toJSONString(filteredCancelList));
//                for (OcBJitxDealerOrderTask task : filteredCancelList) {
//                    JitxSxOrderCancelRequest request = new JitxSxOrderCancelRequest();
//                    request.setOrder_Sn(task.getTid());
//                    request.setCustomerCode(OcBJitxDealerOrderTaskService.CUSTOMER_CODE);
//
//                    ValueHolderV14<WingReturnResult> v14 = ipRpcService.cancelSxJitxOrder(request);
//                    //解析结果
//                    this.handelResult(billNoAndIdCancelMap, Lists.newArrayList(task.getId()), v14);
//                }
//            }
//        } catch (Exception e) {
//            log.error("{},取消占用数据处理异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
//        }
//
//        //换仓反馈处理
//        if (CollectionUtils.isNotEmpty(exchangeStoreList)) {
//            log.debug(" 换仓反馈数据：{}", JSON.toJSONString(exchangeStoreList));
//            List<Long> logIds = exchangeStoreList.stream().filter(r -> !ObjectUtils.isEmpty(r.getModifyWarehouseLogId()))
//                    .map(OcBJitxDealerOrderTask::getModifyWarehouseLogId).collect(Collectors.toList());
//            List<OcBJitxModifyWarehouseLog> modifyWarehouseLogs = modifyWarehouseLogMapper
//                    .selectList(new LambdaQueryWrapper<OcBJitxModifyWarehouseLog>()
//                            .in(OcBJitxModifyWarehouseLog::getId, logIds));
//            if (CollectionUtils.isNotEmpty(modifyWarehouseLogs)) {
//                Map<Long, Long> taskMap = exchangeStoreList.stream()
//                        .collect(Collectors.toMap(OcBJitxDealerOrderTask::getModifyWarehouseLogId, OcBJitxDealerOrderTask::getId));
//                List<Long> failIds = new ArrayList<>();
//                List<Long> successIds = new ArrayList<>();
//                for (OcBJitxModifyWarehouseLog modifyWarehouseLog : modifyWarehouseLogs) {
//                    Long taskId = taskMap.get(modifyWarehouseLog.getId());
//                    if (taskId == null) {
//                        continue;
//                    }
//                    try {
//                        ValueHolderV14<OcBOrder> v14 = dealerOrderTaskService.retryNotify(modifyWarehouseLog, null);
//                        if (v14.isOK()) {
//                            successIds.add(taskId);
//                        } else {
//                            jitxDealerOrderTaskMapper.updateTaskStatus(Lists.newArrayList(taskId), JitxDealerTaskStatusEnum.FAIL.getCode(), SplitMessageUtil.splitMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_999));
//                        }
//                    } catch (Exception e) {
//                        log.error("{},换仓反馈异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
//                        failIds.add(taskId);
//                    }
//                }
//
//                if (CollectionUtils.isNotEmpty(failIds)) {
//                    jitxDealerOrderTaskMapper.updateTaskStatus(failIds, JitxDealerTaskStatusEnum.FAIL.getCode(), "执行调用接口出现异常");
//                }
//                if (CollectionUtils.isNotEmpty(successIds)) {
//                    jitxDealerOrderTaskMapper.updateTaskStatus(successIds, JitxDealerTaskStatusEnum.SUCCESS.getCode(), "换仓反馈成功");
//                }
//            }
//        }
//
//    }
//
//    private void handelResult(Map<String, Long> billNoAndIdAddMap, List<Long> addIdList, ValueHolderV14<WingReturnResult> v14) {
//        try {
//            if (!v14.isOK()) {
//                jitxDealerOrderTaskMapper.updateTaskStatus(addIdList, JitxDealerTaskStatusEnum.FAIL.getCode(), SplitMessageUtil.splitMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_999));
//            } else {
//                WingReturnResult data = v14.getData();
//                if (!data.isOk()) {
//                    jitxDealerOrderTaskMapper.updateTaskStatus(addIdList, JitxDealerTaskStatusEnum.FAIL.getCode(), SplitMessageUtil.splitMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_999));
//                } else {
//                    List<WingReturnResult.FailMsg> faileds = data.getFaileds();
//                    if (CollectionUtils.isNotEmpty(faileds)) {
//                        for (WingReturnResult.FailMsg failMsg : faileds) {
//                            Long id = billNoAndIdAddMap.get(failMsg.getId());
//                            jitxDealerOrderTaskMapper.updateTaskStatus(Lists.newArrayList(id), JitxDealerTaskStatusEnum.FAIL.getCode(), SplitMessageUtil.splitMsgBySize(failMsg.getErrMsg(), SplitMessageUtil.SIZE_999));
//                        }
//                    } else {
//                        jitxDealerOrderTaskMapper.updateTaskStatus(addIdList, JitxDealerTaskStatusEnum.SUCCESS.getCode(), SplitMessageUtil.splitMsgBySize(data.getMsg(), SplitMessageUtil.SIZE_999));
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("{},解析请求wing接口数据异常：{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
//        }
//    }
//
//    class CallableTobeDeakerTaskWithResult implements Callable<Integer> {
//        private final String nodeName;
//        private final String taskTableName;
//        private final Integer state;
//        private final String type;
//        private final Integer failNumber;
//
//        public CallableTobeDeakerTaskWithResult(String nodeName, String name, Integer state, String type, int failNumber) {
//            this.nodeName = nodeName;
//            this.taskTableName = name;
//            this.state = state;
//            this.type = type;
//            this.failNumber = failNumber;
//        }
//
//        @Override
//        public Integer call() throws Exception {
//
//            List<OcBJitxDealerOrderTask> ocBJitxDealerOrderTasks = jitxDealerOrderTaskMapper.selectTaskIdList(nodeName, pullNum, taskTableName, state, type,failNumber);
//            if (log.isDebugEnabled()) {
//                log.debug("AutoJitxDealerOrderMakeUpTask.selectTaskIdList.打印数据总量{}", ocBJitxDealerOrderTasks.size());
//            }
//            if (CollectionUtils.isNotEmpty(ocBJitxDealerOrderTasks)) {
//                handelData(ocBJitxDealerOrderTasks);
//                return ocBJitxDealerOrderTasks.size();
//            }
//            if (log.isDebugEnabled()) {
//                log.debug("AutoJitxDealerOrderMakeUpTask.call.finished");
//            }
//            return 0;
//        }
//    }
//
//}
