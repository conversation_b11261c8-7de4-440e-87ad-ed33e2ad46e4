package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.services.IpStandplatRefundService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 团买买售后物流单号查询
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TmmReturnLogisticNoTask extends BaseR3Task implements IR3Task {

    @Resource
    private IpStandplatRefundService refundService;

    @Override
    @XxlJob("TmmReturnLogisticNoTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            refundService.searchReturnLogisticNo(PlatFormEnum.TUAN_MAI_MAI.getCode());
            result.setSuccess(true);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
