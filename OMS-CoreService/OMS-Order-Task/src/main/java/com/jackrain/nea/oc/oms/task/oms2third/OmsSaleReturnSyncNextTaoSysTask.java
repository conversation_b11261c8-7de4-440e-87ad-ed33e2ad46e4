//package com.jackrain.nea.oc.oms.task.oms2third;
//
//import com.alibaba.fastjson.JSON;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
//import com.jackrain.nea.oc.oms.model.relation.TaskParam;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
//import com.jackrain.nea.oc.oms.services.SaleReturnSyncNextTaoService;
//import com.jackrain.nea.oc.request.o2o.SaleReturnRequest;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * 同步互道
// *
// * @Desc : 同步门店发货销退单给互道
// * <AUTHOR> xiWen
// * @Date : 2020/8/14
// */
//@Component
//public class OmsSaleReturnSyncNextTaoSysTask extends AbsOmsSyncThirdSysTask<SaleReturnRequest> {
//
//    @Autowired
//    private OcBReturnOrderMapper ocBReturnOrderMapper;
//
//    @Autowired
//    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;
//
//    @Autowired
//    private SaleReturnSyncNextTaoService saleReturnSyncNextTaoService;
//
//    private String statusKey = "omsSyncThirdSysTask.nextTao.return.query.status";
//    private String eachSizeKey = "omsSyncThirdSysTask.nextTao.return.query.eachSize";
//
//    @Override
//    protected String getTag() {
//        return omsSyncThirdSysConfig.getOms2QiMenTag();
//    }
//
//    @Override
//    protected String getTopic() {
//        return omsSyncThirdSysConfig.getOms2QiMenTopic();
//    }
//
//    @Override
//    protected String getTaskTableName() {
//        return "OC_B_TASK_RETURN_SYNC";
//    }
//
//    @Override
//    protected String getTaskStatus() {
//        return "NEXT_TAO_STATUS";
//    }
//
//    @Override
//    protected String getTaskType() {
//        return "IS_NEXT_TAO";
//    }
//
//    @Override
//    protected int getTaskTypeVal() {
//        return 1;
//    }
//
//    @Override
//    protected String getOriginCol() {
//        return "RESERVE_BIGINT09";
//    }
//
//    @Override
//    protected String getOriginTableName() {
//        return "OC_B_RETURN_ORDER";
//    }
//
//    @Override
//    protected String getThreadPoolName() {
//        return "R3_OMS_NEXT_TAO_RETURN_TASK_THREAD_POOL_%d";
//    }
//
//    @Override
//    protected int getTaskStatusVal() {
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        Integer orderStatus = config.getProperty(statusKey, 0);
//        return orderStatus;
//    }
//
//    @Override
//    protected int getTaskEachSize() {
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        Integer taskSize = config.getProperty(eachSizeKey, 1000);
//        return taskSize;
//    }
//
//    @Override
//    protected int getBatchEachSize() {
//        return 200;
//    }
//
//    @Override
//    protected List<SaleReturnRequest> getSyncData(TaskParam taskParam) {
//
//        String ids = joinList2String(taskParam.getKeys());
//        taskParam.setKeyStrings(ids);
//        List<OcBReturnOrder> returnList = ocBReturnOrderMapper.selectListForThirdSysByIds(taskParam);
//        if (unExpect.test(returnList)) {
//            return null;
//        }
//        List<OcBReturnOrderRefund> rfnList = ocBReturnOrderRefundMapper.queryReturnOrderRefundByoIds(ids, "Y");
//        if (unExpect.test(rfnList)) {
//            return null;
//        }
//        orderIdFun = o -> ((OcBReturnOrder) o).getId();
//        statisticsCsm(returnList, taskParam.getValidKeys());
//
//
//        return saleReturnSyncNextTaoService.saleReturnOrderTransfer(returnList, rfnList, taskParam);
//    }
//
//    @Override
//    protected String convert2String(List<SaleReturnRequest> list) {
//
//        OmsSyncNextTaoRequest omsSyncNextTaoRequest = new OmsSyncNextTaoRequest();
//        omsSyncNextTaoRequest.setMethod("sm.returnorder.sync");
//        omsSyncNextTaoRequest.setCenterName("oc_oms");
//        omsSyncNextTaoRequest.setData(list);
//
//        return JSON.toJSONString(omsSyncNextTaoRequest);
//    }
//
//
//}
