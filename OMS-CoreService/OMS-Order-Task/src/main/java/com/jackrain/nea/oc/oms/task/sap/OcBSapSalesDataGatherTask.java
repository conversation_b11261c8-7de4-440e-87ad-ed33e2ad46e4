package com.jackrain.nea.oc.oms.task.sap;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.aliyuncs.utils.StringUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.sap.SapSalesDataGatherService;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utility.RedisLocker;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * Description: 汇总销售数据记录表
 *
 * @Author: guo.kw
 * @Since: 2022/8/29
 * create at: 2022/8/29 10:32
 */
@Slf4j
@Component
public class OcBSapSalesDataGatherTask implements IR3Task {
    private static final String TASK_NAME = "OcBSapSalesDataGatherTask";

    @Autowired
    private SapSalesDataGatherService service;
    @NacosValue(value = "${r3.oc.oms.task.sap.salesDataGatherTaskLockMinutes:360}", autoRefreshed = true)
    private long waitTime;

    @Override
    @XxlJob("OcBSapSalesDataGatherTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult resultTask = new RunTaskResult();
        resultTask.setSuccess(Boolean.TRUE);
        resultTask.setMessage("success");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish OcBSapSalesDataRecordTask execute start"));
        }

        String key = BllRedisKeyResources.buildSapSalesDataRecordTaskCompleteKey();
        String completeFlag = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(key);
        if (StringUtils.isEmpty(completeFlag)) {
            resultTask.setMessage("销售数据汇总任务未完成");
            return resultTask;
        }

        String lockKey = BllRedisKeyResources.buildTaskLockKey(TASK_NAME);
        RedisLocker concurrentLocker = new RedisLocker(lockKey);
        try {
            if (concurrentLocker.lock(waitTime * 1000 * 60)) {
                service.salesOrderDataGatherBack(new ArrayList<>());
            } else {
                log.warn(LogUtil.format("任务正在执行中,详情:{}", "OcBSapSalesDataGatherTask.execute"), lockKey);
                resultTask.setMessage("任务正在执行中");
                return resultTask;
            }
        } catch (Exception e) {
            log.error(LogUtil.format("Error OcBSapSalesDataGatherTask.execute"), Throwables.getStackTraceAsString(e));
            resultTask.setSuccess(Boolean.FALSE);
            resultTask.setMessage(e.getMessage());
        } finally {
            concurrentLocker.unLock();
        }
        return resultTask;
    }


}
