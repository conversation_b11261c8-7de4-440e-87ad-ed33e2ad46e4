package com.jackrain.nea.oc.oms.task.chargebackcheck;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.services.ReturnOrderAuditService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-12-01 00:18
 * @Description : 通用退单同步平台状态
 **/
@Slf4j
@Component
public class StandplatRefundSyncPlatformTask extends BaseR3Task implements IR3Task {

    @NacosValue(value = "${lts.StandplatRefundSyncPlatformTask.range:500}", autoRefreshed = true)
    public Integer range;

    @Autowired
    private ReturnOrderAuditService returnOrderAuditService;

    @Override
    @XxlJob("StandplatRefundSyncPlatformTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();

        List<Long> returnOrderIdList = ES4ReturnOrder.queryEsSyncPlatIdList(range);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StandplatRefundSyncPlatformTask.returnOrderIdList: {}"), JSON.toJSONString(returnOrderIdList));
        }
        if(CollectionUtils.isEmpty(returnOrderIdList)){
            result.setMessage("没有可以执行的数据！");
            result.setSuccess(true);
            return result;
        }
        ValueHolderV14 v14 = returnOrderAuditService.batchSyncPlatform(returnOrderIdList,true, SystemUserResource.getRootUser());
        result.setSuccess(v14.isOK());
        result.setMessage(v14.getMessage());
        return result;
    }
}
