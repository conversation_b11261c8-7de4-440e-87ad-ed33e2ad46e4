package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.google.common.base.Throwables;
import com.jackrain.nea.model.util.AdParamUtil;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.es.ES4IpJingDongSaRefund;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 京东取消订单转单补偿任务
 * @author: 郑小龙
 * @date: 2020-06-04 12:13
 **/

@Slf4j
@Component
public class AutoJdCancelRefundOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Override
    @XxlJob("AutoJdCancelRefundOrderTask")
    public RunTaskResult execute(JSONObject params) {
        log.debug(LogUtil.format("AutoJdCancelRefundOrderTask.execute方法开始执行",  "AutoJdCancelRefundOrderTask"));
        RunTaskResult result = new RunTaskResult();
        long starTime = System.currentTimeMillis();
        try {
            //一次捞取京东取消订单的数量
            int makeCount = Tools.getInt(AdParamUtil.getParam("oms.oc.order.jdcancel.makeup.num"), DEFAULT_PAGE_SIZE);
            List<Long> list = ES4IpJingDongSaRefund.selectJdCancelOrder(0, makeCount);
            //每次发mq数量
            int pageSize = Tools.getInt(AdParamUtil.getParam("oms.oc.order.jdcancel.pageSize.num"), DEFAULT_PAGE_SIZE);
            //计算发mq次数
            int page = makeCount / pageSize;
            if (makeCount % pageSize != 0) {
                page++;
            }
            log.debug(LogUtil.format("AutoJdCancelRefundOrderTask.捞取数量:{},mq数量{},mq次数:{},单据量{}",  "AutoJdCancelRefundOrderTask"), makeCount, pageSize, page, list.size());
            for (int i = 1; i <= page; i++) {
                List<Long> newList = list.stream().skip(pageSize * (i - 1)).limit(pageSize).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(newList)) {
                    //发送mq
                    sendMQ(newList);
                }
            }
            result.setSuccess(true);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 京东取消订单定时任务耗时:{}ms", Time);
            }
        } catch (Exception e) {
            log.info(LogUtil.format("AutoJdCancelRefundOrderTask.Execute Error,异常：{}", "AutoJdCancelRefundOrderTask"), Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
            result.setMessage(e.getMessage());
        }
        return result;
    }

    private void sendMQ(List<Long> newList) {
        StringBuilder idsString = new StringBuilder();
        for (Long id : newList) {
            if (idsString.length() > 0) {
                idsString.append(",");
            }
            idsString.append(id.toString());
        }
        String msgKey = "JINGDONG_CANCEL_TRANSFER_" + BllCommonUtil.getmicTime();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("AutoJdCancelRefundOrderTask.sendMQ.msgKey={}",  "AutoJdCancelRefundOrderTask"), msgKey);
        }
        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
        orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
        orderMqInfo.setChannelType(ChannelType.JINGDONG);
        orderMqInfo.setOrderType(OrderType.CANCELORDER);
        orderMqInfo.setOrderIds(idsString.toString());
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        mqInfoList.add(orderMqInfo);
        String jsonValue = JSONObject.toJSONString(mqInfoList);
        try {
//            sendHelper.sendMessage(jsonValue, transferMqConfig.getSendTransferMqTopic(), transferMqConfig.getSendTransferMqTag(), msgKey);
            defaultProducerSend.sendTopic(MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER, MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER, jsonValue, msgKey);
        } catch (Exception e) {
            log.info(LogUtil.format("AutoJdCancelRefundOrderTask.sendMQ,异常：{}", "AutoJdCancelRefundOrderTask"), Throwables.getStackTraceAsString(e));
        }
    }
}
