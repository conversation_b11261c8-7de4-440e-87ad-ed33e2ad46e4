package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.jackrain.nea.oc.oms.services.AutoNagativeAdjustmentByRefundInService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 田钦华
 * @since : 2019-09-25
 * create at : 2019-09-25 9:54 PM
 * 退货入库单 生成负向库存调整单 补偿自动任务
 */
@Slf4j
@Component
public class AutoNagativeAdjustmentByRefundInTask extends BaseR3Task implements IR3Task {

    @Autowired
    AutoNagativeAdjustmentByRefundInService service;

    @Override
    @XxlJob("AutoNagativeAdjustmentByRefundInTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult result = new RunTaskResult();
        Integer range = params.getInteger("range");
        try {
            service.doNagativeAdjustmentByRefundIn(range);
            result.setMessage("success");
            result.setSuccess(true);
        } catch (Exception ex) {
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}

