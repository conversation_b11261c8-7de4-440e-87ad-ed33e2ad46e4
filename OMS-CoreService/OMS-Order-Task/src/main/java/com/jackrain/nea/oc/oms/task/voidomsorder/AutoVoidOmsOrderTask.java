package com.jackrain.nea.oc.oms.task.voidomsorder;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.services.OmsPreSaleOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 作废订单-淘宝预售-未付尾款
 *
 * @author: xiWen.z
 * create at: 2019/10/23 0023
 */
@Slf4j
@Component
public class AutoVoidOmsOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsPreSaleOrderService omsPreSaleOrderService;

    @Override
    @XxlJob("AutoVoidOmsOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult taskResult = new RunTaskResult();
        try {

            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoVoidOmsOrderTask.range", DEFAULT_PAGE_SIZE);
            boolean isOpenVoidOrder = config.getPropertyBoolean("lts.AutoVoidOmsOrderTask.isOpen");
            if (isOpenVoidOrder) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("淘宝预售.作废未付尾款订单开始执行...",  "作废未付尾款订单开始执行"));
                }
                long l0 = System.currentTimeMillis();
                List<Long> hashKeyList = ES4Order.findIdByOrderStatusAndStatusPayStep(0, pageSize);
                omsPreSaleOrderService.executeVoidOmsOrder(hashKeyList, SystemUserResource.getRootUser());
                taskResult.setSuccess(true);
                long l1 = System.currentTimeMillis();
                if (log.isDebugEnabled()) {
                    log.debug(this.getClass().getName() + "执行时间-> " + (l1 - l0));
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("淘宝预售自动作废订单未开启",  "淘宝预售自动作废订单未开启"));
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoVoidOmsOrderTask,异常信息:{}", "AutoVoidOmsOrderTask"), Throwables.getStackTraceAsString(ex));
            taskResult.setSuccess(false);
            taskResult.setMessage(ex.getMessage());
        }
        return taskResult;
    }
}
