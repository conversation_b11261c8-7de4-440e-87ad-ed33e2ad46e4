package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.es.ES4TaoBaoRefund;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.TaobaoTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.OmsTaobaoRefundService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * 退货补偿服务
 */

@Slf4j
@Component
public class AutoRefundOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OmsTaobaoRefundService omsTaobaoRefundService;
    @Autowired
    private TaobaoTransferRefundProcessImpl taobaoTransferRefundProcess;


    @Override
    @XxlJob("AutoRefundOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        long starTime = System.currentTimeMillis();
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.OrderTransferTask.range", 200);
            List<String> list = ES4TaoBaoRefund.selectRefundOrderByRefundId(0, pageSize);
            List<OmsTaobaoRefundRelation> relations = new ArrayList<>();
            for (String refundId : list) {
                OmsTaobaoRefundRelation omsTaobaoRefundRelation = omsTaobaoRefundService.selectTaoBaoRefundRelation(refundId);
                if (omsTaobaoRefundRelation != null) {
                    relations.add(omsTaobaoRefundRelation);
                } else {
                    String errorMessage = Resources.getMessage("AutoRefundOrderTask Order " +
                            "NotExist!RefundId=" + refundId);
                    log.error(errorMessage);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.taobaoTransferRefundProcess, relations);
            result.setSuccess(true);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoRefundOrderTask.execute 淘宝退单定时任务耗时:{}ms",  "淘宝退单定时任务"), Time);

            }
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoRefundOrderTask.execute 淘宝退单定时任务异常！{}",  "淘宝退单定时任务"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
