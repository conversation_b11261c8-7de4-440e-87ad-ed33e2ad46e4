//package com.jackrain.nea.oc.oms.task.refundorder;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.services.ReturnOrderPushWmsService;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * description： 退单传WMS任务
// *
// * <AUTHOR>
// * @date 2021/11/9
// */
//@Slf4j
//@Component
//public class AutoReturnOrderToWmsTask extends BaseR3Task implements IR3Task {
//    @Autowired
//    private ReturnOrderPushWmsService pushWmsService;
//
//    @Override
//    public RunTaskResult execute(JSONObject params) {
//        if (log.isDebugEnabled()) {
//            log.debug("AutoReturnOrderToWmsTask.start");
//        }
//        RunTaskResult result = new RunTaskResult();
//        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
//        Integer range = config.getProperty("lts.TimedReturnOrderTask.range", 1000);
//
//        try {
//            pushWmsService.pushData(range);
//            result.setSuccess(true);
//        } catch (Exception ex) {
//            //发生异常 所有数据返回最开始的状态
//            log.error(LogUtil.format("AutoReturnOrderToWmsTask.Execute Error:{}",  "AutoReturnOrderToWmsTask"), Throwables.getStackTraceAsString(ex));
//            result.setSuccess(false);
//            result.setMessage(ex.getMessage());
//        }
//        return result;
//    }
//}
//
//
