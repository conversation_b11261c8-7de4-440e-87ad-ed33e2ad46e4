package com.jackrain.nea.oc.oms.task.makeup;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultSendMsgResult;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;


/**
 * LTS任务调用库存中心接口（生成入库通知单/结果单，逻辑收货单）:补偿任务可补充旧数据
 *
 * @author: tianqinhua
 * @since: 2019/9/3
 * create at : 2019/9/3 14:30
 */

@Slf4j
@Component
public class OmsToSgCreatePhyNoticsMakeUpTask extends BaseR3Task implements IR3Task {

    private static final int idNum = 100;//调用sg查询接口的id个数

    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private CpRpcService cpExtRpcService;
    @Autowired
    private ThreadPoolTaskExecutor createPhynoticsTaskThreadPoolExecutor;

    @Autowired
    private PropertiesConf pconf;
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Value("${r3.oms.task.find.es.number:500}")
    private Integer number;

    @Override
//    @XxlJob("OmsToSgCreatePhyNoticsMakeUpTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        log.debug(LogUtil.format("OmsToSgCreatePhyNoticsMakeUpTask.execute.start: {}"), params);
        RunTaskResult result = new RunTaskResult();
        result.setSuccess(true);
        result.setMessage("success");
        try {
            //log.debug(this.getClass().getName() + " task 任务开始执行");
            //log.debug(this.getClass().getName() + " task 任务获取配置开始时间={}", this.pconf.getProperty("r3.oms.create.startTime"));
            //log.debug(this.getClass().getName() + " task 任务获取配置结束时间={}", this.pconf.getProperty("r3.oms.create.endTime"));
            //Date startTime = this.pconf.getProperty("r3.oms.create.startTime")==null?sdf.parse("2019-07-15 00:00:00"):sdf.parse(this.pconf.getProperty("r3.oms.create.startTime"));
            //Date endTime = this.pconf.getProperty("r3.oms.create.endTime")==null? new Date():sdf.parse(this.pconf.getProperty("r3.oms.create.endTime"));
            // 入参开始/结束时间
            Object startTimeObj = params.get("startTime");
            Object endTimeObj = params.get("endTime");
            // 定时时间类型
            Date startTime;
            Date endTime;
            if (Objects.isNull(startTimeObj) || Objects.isNull(endTimeObj)) {
                result.setSuccess(false);
                result.setMessage("入参开始时间或者结束时间为空");
                return result;
            }
            try {
                // 时间转换
                startTime = sdf.parse(startTimeObj.toString());
                endTime = sdf.parse(endTimeObj.toString());
            } catch (Exception e) {
                result.setSuccess(false);
                result.setMessage("时间转换出错,开始时间" + startTimeObj + ",结束时间" + endTimeObj);
                return result;
            }
            if (null == startTime || null == endTime) {
                result.setSuccess(false);
                result.setMessage("时间转换后为空,开始时间" + startTime + ",结束时间" + endTime);
                return result;
            }
            log.debug(LogUtil.format("OmsToSgCreatePhyNoticsMakeUpTask.execute.data: {}-{}"), startTime, endTime);
            List<Integer> orderStatusList = new ArrayList<>();
            orderStatusList.add(5);
            orderStatusList.add(6);

            String creationDate = startTime.getTime() + "~" + endTime.getTime();

            Integer range = number;
            Integer index = 0;
            long platform = 50;
            boolean flag = false;
            JSONArray returnDatas = new JSONArray();
            do {
                JSONObject search = ES4Order.getIdsByOrderStatusAndPlaFormFilterByCDate(creationDate,
                        orderStatusList, platform, number, index);

                if (!search.isEmpty()) {
                    JSONArray returnData = search.getJSONArray("data");
                    returnDatas.addAll(returnData);
                    if (returnData.size() >= range) {
                        index += range;
                        flag = true;
                    } else {
                        flag = false;
                    }
                } else {
                    flag = false;
                }
            } while (flag);
            if (returnDatas.isEmpty()) {
                result.setSuccess(false);
                result.setMessage("查询ES为空");
                return result;
            }
            List<Long> ids = new ArrayList<>();
            for (int i = 0; i < returnDatas.size(); i++) {
                ids.add(returnDatas.getJSONObject(i).getLong("ID"));
            }
            //List<SgPhyInNoticesBillSaveRequest> sgPhyInNoticesBillSaveRequestList = new ArrayList<>();
            // 多线程调用sg接口获取 生成3张单子的入参
            ValueHolderV14<List<SgOutResultSendMsgResult>> callableGetDatasByidsFromSg =
                    callableGetDatasByidsFromSg(ids);
            //入参参数整理
            //根据订单id获取sg接口返回 结果数据
            List<SgOutResultSendMsgResult> mqOutOrderList = callableGetDatasByidsFromSg.getData();
            //主表and明细表赋值
            for (SgOutResultSendMsgResult sgOutResultSendMsgResult : mqOutOrderList) {
//                SgPhyInNoticesBillSaveRequest sgPhyInNoticesBillSaveRequest = new SgPhyInNoticesBillSaveRequest();
//                SgOutResultMqResult sgOutResultMqResult = sgOutResultSendMsgResult.getMqResult();
//                List<SgOutResultItemMqResult> sgOutResultItemMqResultList = sgOutResultSendMsgResult.getMqResultItem();
//                sgPhyInNoticesBillSaveRequest.setNotices(getNoticesRequestFunc(sgOutResultMqResult));
//                sgPhyInNoticesBillSaveRequest.setItemList(itemListGetFunc(sgOutResultItemMqResultList));
//                sgPhyInNoticesBillSaveRequest.setLoginUser(SystemUserResource.getRootUser());
//                sgPhyInNoticesBillSaveRequestList.add(sgPhyInNoticesBillSaveRequest);
            }
            //调用sg接口
            try {
//                if (log.isDebugEnabled()) {
//                    log.debug(this.getClass().getName() + " OmsToSgCreatePhyNoticsMakeUpTask调用sg生成生成入库通知单,生成逻辑收货单," +
//                            "生成入库结果单接口入参={}", sgPhyInNoticesBillSaveRequestList);
//                }
//                if (sgPhyInNoticesBillSaveRequestList == null || sgPhyInNoticesBillSaveRequestList.size() <= 0) {
//                    result.setSuccess(false);
//                    result.setMessage("无可生成入库通知单的入参数据");
//                    return result;
//                }
//                ValueHolderV14<SgR3BaseResult> resultSg =
//                        sgRpcService.saveSgBPhyInNoticesByReturn(sgPhyInNoticesBillSaveRequestList);
//                if (log.isDebugEnabled()) {
//                    log.debug(this.getClass().getName() + " OmsToSgCreatePhyNoticsMakeUpTask调用sg生成生成入库通知单,生成逻辑收货单," +
//                            "生成入库结果单接口出参={}", resultSg);
//                }
            } catch (Exception e) {
                log.error(LogUtil.format("调用sg生成生成入库通知单,生成逻辑收货单,生成入库结果单接口rpc.异常: {}"),
                        Throwables.getStackTraceAsString(e));
                result.setSuccess(false);
                result.setMessage(e.getMessage());
                return result;
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OmsToSgCreatePhyNoticsMakeUpTask.异常: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        log.debug(LogUtil.format("task 任务执行完成"));
        return result;
    }

    /**
     * 多线程获取sg接口返回的数据生成3张单子的入参
     */
    private ValueHolderV14<List<SgOutResultSendMsgResult>> callableGetDatasByidsFromSg(List<Long> ids) {
        ValueHolderV14<List<SgOutResultSendMsgResult>> result = new ValueHolderV14<>();
        List<SgOutResultSendMsgResult> msgResultAll = new ArrayList<>();

        final String tableName = "oc_b_order";
        Map<String, String> topMap = null;
        Set<String> nodes = topMap.keySet();
        if (CollectionUtils.isEmpty(nodes)) {
            if (log.isDebugEnabled()) {
                log.debug("{}.nodes.not.get!", this.getClass().getName());
            }
            result.setCode(ResultCode.FAIL);
            result.setMessage("请检查环境，node获取不到！！");
            return result;
        }
        HashMap<Integer, List<Long>> stringListHashMap = splitList(ids, nodes.size());
        List<List<Long>> listIds = new ArrayList<List<Long>>(stringListHashMap.values());
        List<Future<ValueHolderV14<List<SgOutResultSendMsgResult>>>> results =
                new ArrayList<Future<ValueHolderV14<List<SgOutResultSendMsgResult>>>>();

        if (listIds != null && listIds.size() > 0) {
            //线程提交
            for (int i = 0; i < listIds.size(); i++) {
                results.add(createPhynoticsTaskThreadPoolExecutor.submit(new CallableTaskWithResult(listIds.get(i))));
            }
            //线程执行结果获取
            for (Future<ValueHolderV14<List<SgOutResultSendMsgResult>>> futureResult : results) {
                try {
                    List<SgOutResultSendMsgResult> data = futureResult.get().getData();
                    msgResultAll.addAll(data);
                } catch (InterruptedException e) {
                    log.error(LogUtil.format("多线程获取InterruptedException异常.异常: {}"), Throwables.getStackTraceAsString(e));
                } catch (ExecutionException e) {
                    log.error(LogUtil.format("多线程获取ExecutionException异常.异常: {}"), Throwables.getStackTraceAsString(e));
                }
            }
        }
        log.debug(LogUtil.format("线程执行完毕"));
        result.setData(msgResultAll);
        return result;
    }

//    /**
//     * 获取主体数据信息
//     **/
//    private SgPhyInNoticesRequest getNoticesRequestFunc(SgOutResultMqResult sgOutResultMqResult) {
//        SgPhyInNoticesRequest notices = new SgPhyInNoticesRequest();
//        OcBOrder ocBOrder = ocBOrderMapper.selectByID(sgOutResultMqResult.getSourceBillId());//来源单据id --->查询订单表的订单编号字段
//        CpShop cpShop = getCpCshop(ocBOrder.getCpCShopId());//下单店铺id
//        if (cpShop.getCpCStoreId() != null) {
//            List<Integer> ids = new ArrayList<>();//门店主键id
//            ids.add(Integer.parseInt(cpShop.getCpCStoreId().toString()));
//            List<CpCStore> cpCStoreList = cpCStoreList(ids);
//            if (cpCStoreList != null && cpCStoreList.size() > 0) {
//                notices.setCpCPhyWarehouseId(cpCStoreList.get(0).getCpCPhyWarehouseId());//实体仓ID
//                notices.setCpCPhyWarehouseEcode(cpCStoreList.get(0).getCpCPhyWarehouseEcode());//实体仓CODE
//                notices.setCpCPhyWarehouseEname(cpCStoreList.get(0).getCpCPhyWarehouseEname());//实体仓名称
//            }
//        }
//        notices.setCpCCsEcode(ocBOrder.getCpCPhyWarehouseEcode());//发货仓CODE
//        notices.setCpCCsEname(ocBOrder.getCpCPhyWarehouseEname());//发货仓名称
//        notices.setInType(1);//入库类型：电商入库
//        notices.setSourceBillType(1);//零售发货单
//        notices.setSourceBillNo(sgOutResultMqResult.getSourceBillNo());//来源单据编号
//        notices.setSourceBillId(sgOutResultMqResult.getSourceBillId());//来源单据ID
//        notices.setBillStatus(SgInNoticeConstants.BILL_STATUS_IN_PENDING);//单据状态
//        notices.setWmsStatus(0L);//传wms状态
//        notices.setIsPassWms(0);//是否传WMS，默认 否
//        notices.setLogisticNumber(sgOutResultMqResult.getLogisticNumber());//物流单号
//        notices.setCpCStoreId(cpShop.getCpCStoreId());//逻辑仓ID
//        notices.setCpCStoreEcode(cpShop.getCpCStoreEcode());//逻辑仓Code
//        notices.setCpCStoreEname(cpShop.getCpCStoreEname());//逻辑仓名称
//        notices.setCpCLogisticsId(sgOutResultMqResult.getCpCLogisticsId());//物流公司id
//        notices.setCpCLogisticsEcode(sgOutResultMqResult.getCpCLogisticsEcode());//物流公司code
//        notices.setCpCLogisticsEname(sgOutResultMqResult.getCpCLogisticsEname());//物流公司name
//        notices.setInTime(ocBOrder.getScanTime() == null ? ocBOrder.getCreationdate() : ocBOrder.getScanTime());
//        //入库时间//:出库时间
//        notices.setCpCShopId(ocBOrder.getCpCShopId());//平台店铺
//        notices.setCpCShopTitle(ocBOrder.getCpCShopTitle());//平台店铺标题
//        notices.setSourcecode(ocBOrder.getSourceCode());//平台单号
//        return notices;
//    }
//
//    /**
//     * 获取明细信息
//     */
//    private List<SgPhyInNoticesItemRequest> itemListGetFunc(List<SgOutResultItemMqResult> sgOutResultItemMqResultList) {
//        List<SgPhyInNoticesItemRequest> itemList = new ArrayList<>();
//        if (sgOutResultItemMqResultList == null || sgOutResultItemMqResultList.size() <= 0) {
//            return new ArrayList<>();
//        }
//        sgOutResultItemMqResultList.forEach(transferItem -> {
//            SgPhyInNoticesItemRequest item = new SgPhyInNoticesItemRequest();
//            item.setId(-1L);
//            item.setQty(transferItem.getQty());
//            item.setQtyIn(transferItem.getQty());
//            item.setPsCSpec1Id(transferItem.getJitxItem().getPsCSpec1Id());
//            item.setPsCSpec1Ecode(transferItem.getJitxItem().getPsCSpec1Ecode());
//            item.setPsCSpec1Ename(transferItem.getJitxItem().getPsCSpec1Ename());
//            item.setPsCSpec2Id(transferItem.getJitxItem().getPsCSpec2Id());
//            item.setPsCSpec2Ecode(transferItem.getJitxItem().getPsCSpec2Ecode());
//            item.setPsCSpec2Ename(transferItem.getJitxItem().getPsCSpec2Ename());
//            item.setSourceBillItemId(transferItem.getJitxItem().getSourceBillItemId());
//            item.setPriceCost(transferItem.getJitxItem().getPriceCost());//商品成交价
//            item.setPriceList(transferItem.getJitxItem().getPriceList());
//            item.setGbcode(transferItem.getGbcode());//添加国标码
//            item.setPsCSkuId(transferItem.getPsCSkuId());
//            item.setPsCSkuEcode(transferItem.getPsCSkuEcode());
//            item.setPsCProId(transferItem.getPsCProId());
//            item.setPsCProEcode(transferItem.getPsCProEcode());
//            item.setPsCProEname(transferItem.getJitxItem().getPsCProEname());
//            //合并相同条码的情况条件：skuid，skucode，gbcode相同的话累积
//            if (itemList.size() <= 0) {
//                itemList.add(item);
//            } else {
//                for (SgPhyInNoticesItemRequest request : itemList) {
//                    if (request.getPsCSkuId() != null && request.getPsCSkuId().equals(transferItem.getPsCSkuId())
//                            && request.getGbcode() != null && request.getGbcode().equals(transferItem.getGbcode())
//                            && request.getPsCSkuEcode() != null && request.getPsCSkuEcode().equals(transferItem.getPsCSkuEcode())) {
//                        request.setQty(request.getQty() == null ? transferItem.getQty() == null ? BigDecimal.ZERO :
//                                transferItem.getQty() : request.getQty().add(transferItem.getQty()));
//                        request.setQtyIn(request.getQtyIn() == null ? transferItem.getQty() == null ?
//                                BigDecimal.ZERO : transferItem.getQty() :
//                                request.getQtyIn().add(transferItem.getQty()));
//                    } else {
//                        itemList.add(item);
//                        break;
//                    }
//                }
//            }
//        });
//        return itemList;
//    }

    /**
     * 根据下单店铺id获取 店铺相关信息
     *
     * @param shopId 店铺id
     * @return shop
     */
    private CpShop getCpCshop(Long shopId) {
        CpShop cpShop = new CpShop();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用销售渠道档案店铺rpc接口入参: {}",shopId), shopId);
        }
        try {
            cpShop = cpRpcService.selectShopById(shopId);//调用渠道店铺接口
        } catch (Exception e) {
            log.error(LogUtil.format("调用渠道店铺接口rpc.异常: {}"), Throwables.getStackTraceAsString(e));
            return cpShop;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("调用销售渠道档案店铺rpc接口出参: {}"), cpShop);
        }
        return cpShop == null ? new CpShop() : cpShop;
    }

    /**
     * 调用门店档案的接口获取实体仓
     *
     * @param ids 门店主键id集合
     * @return List<CpCStore> 门店档案集合
     */
    private List<CpCStore> cpCStoreList(List<Integer> ids) {

        List<CpCStore> queryStoreInfoByIdsFunc = new ArrayList<>();
        try {
            queryStoreInfoByIdsFunc = cpExtRpcService.queryStoreInfoByIds(ids);
        } catch (Exception e) {
            log.error(LogUtil.format("调用门店案店铺rpc接口获取实体仓异常: {}"), Throwables.getStackTraceAsString(e));
            return queryStoreInfoByIdsFunc;
        }
        return queryStoreInfoByIdsFunc;
    }

    /**
     *  * List集合按数量分组
     *  @param list
     *  @param quantity
     *  @return
     */

    private List<List<Long>> groupListByQuantity(List<Long> list, int quantity) {
        List<List<Long>> wrapList = new ArrayList<List<Long>>();
        if (list == null || list.size() == 0) {
            return wrapList;
        }
        int count = 0;
        while (count < list.size()) {
            wrapList.add(list.subList(count, (count + quantity) > list.size() ? list.size() : (count + quantity)));
            count += quantity;
        }
        return wrapList;
    }

    /**
     * @param num  分的份数
     * @param list 需要分的集合
     */
    public HashMap<Integer, List<Long>> splitList(List<Long> list, int num) {
        int tmpNum = list.size() / 10;//10个线程,每个小list分的个数
        if (list.size() % 10 != 0) {
            tmpNum = tmpNum + 1;
        }
        HashMap<Integer, List<Long>> stringListHashMap = new HashMap<Integer, List<Long>>(); //封装返回的多个list
        List<Long> stringlist = new ArrayList<Long>();

        for (int a = 0; a < num; a++) {
            if (tmpNum * (a + 1) >= list.size()) {
                stringlist = list.subList(a * tmpNum, list.size());
                stringListHashMap.put(a, stringlist);
                break;
            } else {
                stringlist = list.subList(a * tmpNum, tmpNum * (a + 1));
            }
            stringListHashMap.put(a, stringlist);
            stringlist = new ArrayList<Long>();
        }
        log.debug(LogUtil.format("订单id分组: {}"), stringListHashMap);
        return stringListHashMap;
    }

    /**
     * 多线程执行的线程新类
     */
    class CallableTaskWithResult implements Callable<ValueHolderV14<List<SgOutResultSendMsgResult>>> {
        private final List<Long> ids;

        public CallableTaskWithResult(List<Long> ids) {
            this.ids = ids;
        }

        @Override
        public ValueHolderV14<List<SgOutResultSendMsgResult>> call() throws Exception {
            ValueHolderV14<List<SgOutResultSendMsgResult>> tmpResult = new ValueHolderV14<>();
            List<SgOutResultSendMsgResult> tmpList = new ArrayList<>();
            log.debug(LogUtil.format("线程ids: {}"), ids);
            if (ids != null && ids.size() > 0) {
                // 再分 大小执行，最大 100条
                if (ids.size() <= idNum) {
                    tmpResult = sgRpcService.querySgPhyOutResultJITXMQBody(ids, SystemUserResource.getRootUser());
                    if (tmpResult != null && tmpResult.getData() != null) {
                        log.debug(LogUtil.format("根据id查询sg接口返回数据: {}"), tmpResult);
                        tmpList.addAll(tmpResult.getData());
                    }
                } else {
                    int tmpNum = ids.size() / idNum;//100个为1组,每个小list分的个数
                    if (ids.size() % idNum != 0) {
                        tmpNum = tmpNum + 1;
                    }
                    List<List<Long>> groupListByQuantityList = groupListByQuantity(ids, tmpNum);
                    for (int i = 0; i < groupListByQuantityList.size(); i++) {
                        tmpResult = sgRpcService.querySgPhyOutResultJITXMQBody(groupListByQuantityList.get(i),
                                SystemUserResource.getRootUser());
                        log.debug(LogUtil.format("根据id查询sg接口返回数据: {}"), tmpResult);
                        if (tmpResult != null && tmpResult.getData() != null) {
                            tmpList.addAll(tmpResult.getData());
                        }
                    }
                }
            }
            log.debug(LogUtil.format("sg依据订单id查询结果汇总: {}"), tmpList);
            tmpResult.setData(tmpList);
            return tmpResult;
        }
    }

}
