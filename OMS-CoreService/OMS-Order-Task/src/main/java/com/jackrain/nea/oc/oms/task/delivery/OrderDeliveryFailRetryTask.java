package com.jackrain.nea.oc.oms.task.delivery;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryFailMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDeliveryFail;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @ClassName OrderDeliveryFailRetryTask
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/4/6 15:17
 * @Version 1.0
 */
@Slf4j
@Component
public class OrderDeliveryFailRetryTask extends BaseR3Task implements IR3Task {

    @Autowired
    private OcBOrderDeliveryFailMapper deliveryFailMapper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Resource
    private ThreadPoolTaskExecutor doDeliveryFailRetryPollExecutor;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Override
    @XxlJob("OrderDeliveryFailRetryTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult resultTask = new RunTaskResult();
        resultTask.setSuccess(Boolean.TRUE);
        resultTask.setMessage("success");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OrderDeliveryFailRetryTask execute start"));
        }
        long start = System.currentTimeMillis();
        final String taskTableName = "oc_b_order_delivery_fail";
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        int pageSize = config.getProperty("lts.OrderDeliveryFailRetryTask.range", 1200);
        List<OcBOrderDeliveryFail> ocBOrderDeliveryFailList = deliveryFailMapper.selectDeliveryFail4Retry(pageSize, taskTableName);
        if (CollectionUtils.isEmpty(ocBOrderDeliveryFailList)) {
            return resultTask;
        }
        List<List<OcBOrderDeliveryFail>> lists = ListSplitUtil.averageAssign(ocBOrderDeliveryFailList, 24);

        List<Future<Boolean>> results = new ArrayList<>();
        for (List<OcBOrderDeliveryFail> data : lists) {
            results.add(doDeliveryFailRetryPollExecutor.submit(new OrderDeliveryFailRetryTask.RetryDeliveryTaskWithResult(data)));
        }
        //线程执行结果获取
        for (Future<Boolean> futureResult : results) {
            try {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("OrderDeliveryFailRetryTask------>线程结果:{}"), futureResult.get().toString());
                }
            } catch (Exception e) {
                log.error(LogUtil.format("OrderDeliveryFailRetryTask多线程获取InterruptedException异常：{}", "OrderDeliveryFailRetryTask"), Throwables.getStackTraceAsString(e));
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OrderDeliveryFailRetryTask 发货失败重试 useTime : {}"), (System.currentTimeMillis() - start));
        }

        return resultTask;
    }

    class RetryDeliveryTaskWithResult implements Callable<Boolean> {

        private final List<OcBOrderDeliveryFail> data;

        public RetryDeliveryTaskWithResult(List<OcBOrderDeliveryFail> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isEmpty(data)) {
                return true;
            }
            List<Long> orderIds = data.stream().map(OcBOrderDeliveryFail::getOcBOrderId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderIds)) {
                return true;
            }
            // 校验订单当前状态 如果是仓库发货状态 才进行重试，否则就认为不需要再重试了
            List<Long> warehouseDeliveryIds = ocBOrderMapper.selectWarehouseDelivery(orderIds);
            List<Long> noWarehouseDeliveryIds = ocBOrderMapper.selectNoWarehouseDelivery(orderIds);
            log.info(LogUtil.format("OrderDeliveryFailRetryTask warehouseDeliveryIds:{} noWarehouseDeliveryIds:{}"), JSONUtil.toJsonStr(warehouseDeliveryIds), JSONUtil.toJsonStr(noWarehouseDeliveryIds));

            // 如果没有仓库发货状态的订单 则不再进行重试了
            if (CollectionUtils.isEmpty(warehouseDeliveryIds)) {
                deliveryFailMapper.updateStatus(orderIds);
                return true;
            }
            try {
                String topic = MqConstants.TOPIC_R3_OC_OMS_CALL_AUTOTASK;
                String tag = MqConstants.TAG_R3_OC_OMS_CALL_AUTOTASK;

                if (Objects.nonNull(topic) && Objects.nonNull(tag)) {
                    defaultProducerSend.sendTopic(topic, tag, JSON.toJSONString(warehouseDeliveryIds),null);
                } else {
                    log.error("platform.delivery.task.topicOrTagIsNull:{}/{}/{}", topic, tag, warehouseDeliveryIds);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error(LogUtil.format("platform.delivery.fail.retry.task.sendMqError", "平台发货补偿定时任务sendMqError"), Throwables.getStackTraceAsString(e));
                return false;
            }
            // 批量更新状态为成功
            deliveryFailMapper.updateStatus(orderIds);
            return true;
        }
    }
}
