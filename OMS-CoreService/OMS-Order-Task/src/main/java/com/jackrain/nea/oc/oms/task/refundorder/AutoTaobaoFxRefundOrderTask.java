package com.jackrain.nea.oc.oms.task.refundorder;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.es.ES4IpTaoBaoFxRefund;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx.TaobaoFxTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoFxRefundService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


/**
 * 淘宝分销退货补偿服务
 */

@Slf4j
@Component
public class AutoTaobaoFxRefundOrderTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpTaobaoFxRefundService ipTaobaoFxRefundService;

    @Autowired
    private TaobaoFxTransferRefundProcessImpl taobaoFxTransferRefundProcess;

    @Override
    @XxlJob("AutoTaobaoFxRefundOrderTask")
    public RunTaskResult execute(JSONObject params) {
        Long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {
            //分销退单转单单次最大拉取记录数 默认500单
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoTaobaoFxRefundOrderTask.range", 500);

            List<String> list = ES4IpTaoBaoFxRefund.findPurchaseOrderIdByIsTrans(
                    TransferOrderStatus.NOT_TRANSFER.toInteger(), 0, pageSize);

            List<IpTaobaoFxRefundRelation> relations = new ArrayList<>();
            for (String refundId : list) {
                IpTaobaoFxRefundRelation ipTaobaoFxRefundRelation = ipTaobaoFxRefundService.selectTaobaoFxRefundRelation(refundId);
                if (ipTaobaoFxRefundRelation != null) {
                    relations.add(ipTaobaoFxRefundRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.taobaoFxTransferRefundProcess, relations);
            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoRefundOrderTask异常！{}",  "AutoRefundOrderTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        Long over = System.currentTimeMillis();
        long times = over - start;
        log.debug(LogUtil.format("淘宝分销订单转单，消耗时间共计:{}",  "淘宝分销订单转单"), times);
        return result;
    }
}
