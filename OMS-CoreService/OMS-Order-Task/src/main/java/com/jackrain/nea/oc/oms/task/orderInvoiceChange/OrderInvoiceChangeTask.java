package com.jackrain.nea.oc.oms.task.orderInvoiceChange;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @ClassName OrderInvoiceChangeTask
 * @Description
 * @Date 2022/8/31 下午4:21
 * @Created by wuhang
 */
@Slf4j
@Component
public class OrderInvoiceChangeTask implements IR3Task {

    @Override
    @XxlJob("OrderInvoiceChangeTask")
    public RunTaskResult execute(JSONObject params) {
        return null;
    }
}
