package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.OcBJitxModifyWarehouseLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowCreatedStateEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowStateEnum;
import com.jackrain.nea.oc.oms.services.JitxService;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-12-15
 * @desc JITX订单创建改仓工单任务
 **/
@Slf4j
@Component
public class AutoJitxCreateChangeWarehouseTask implements IR3Task {

    @Autowired
    private JitxService jitxService;

    @Autowired
    private OcBJitxModifyWarehouseLogMapper warehouseLogMapper;

    @Value("${oms.oc.order.jitx.createChangeWarehouse.pull.num:200}")
    private Integer pullNum;

    /**
     * 最大失败次数
     */
    @Value("${oms.oc.order.jitx.changeWarehouse.max.failNumber:5}")
    private Integer failNumber;


    @Override
    @XxlJob("AutoJitxCreateChangeWarehouseTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        List<Long> idList = searchFromEs(pullNum);
        if (CollectionUtils.isEmpty(idList)) {
            result.setMessage("未从ES查询到数据");
            result.setSuccess(true);
            return result;
        }
        List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList = warehouseLogMapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(modifyWarehouseLogList)) {
            result.setMessage("未从数据库查询到数据");
            result.setSuccess(true);
            return result;
        }
        List<OcBJitxModifyWarehouseLog> filteredList = new ArrayList<>(modifyWarehouseLogList.size());
        for (OcBJitxModifyWarehouseLog warehouseLog : modifyWarehouseLogList) {
            //失败次数超过限制
            if (warehouseLog.getFailNumber() != null && warehouseLog.getFailNumber() > failNumber) {
                continue;
            }
            //不是未创建或者创建失败
            if (!VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode().equals(warehouseLog.getCreatedStatus()) &&
                    !VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode().equals(warehouseLog.getCreatedStatus())) {
                continue;
            }
            if (StringUtils.isEmpty(warehouseLog.getOldDeliveryWarehouse())) {
                continue;
            }
            if (StringUtils.isEmpty(warehouseLog.getNewDeliveryWarehouse())) {
                continue;
            }
            filteredList.add(warehouseLog);
        }
        if (CollectionUtils.isEmpty(filteredList)) {
            result.setMessage("ES数据查库后再次筛选无符合条件数据");
            result.setSuccess(true);
            return result;
        }
        if (log.isDebugEnabled()) {
            log.debug("JITX订单创建改仓工单任务，filtedList：{}", filteredList.size());
        }
        User operateUser = SystemUserResource.getRootUser();
        jitxService.createChangeWarehouseWorkflows(filteredList, operateUser);
        result.setMessage(String.format("执行数据条数", filteredList.size()));
        result.setSuccess(true);
        return result;
    }

    public static List<Long> searchFromEs(Integer range) {
        log.info("jitx改仓工单创建查询ES数据开始 range:{}", range);

        JSONObject whereKeys = new JSONObject();
        JSONObject orderKey = new JSONObject();
        //修改时间升序
        orderKey.put("asc", false);
        orderKey.put("name", "MODIFIEDDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);

        JSONArray result = new JSONArray();
        result.add(VipJitxWorkflowCreatedStateEnum.UNCREATE.getCode());
        result.add(VipJitxWorkflowCreatedStateEnum.CREATE_FAILED.getCode());
        whereKeys.put("CREATED_STATUS", result);

        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次
        filterKeys.put("FAIL_NUMBER", "~" + 5);

        String[] field = {"ID"};
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_JITX_MODIFY_WAREHOUSE_LOG,
                OcElasticSearchIndexResources.OC_B_JITX_MODIFY_WAREHOUSE_LOG, whereKeys, filterKeys, order, range, 0, field);
        List<Long> orderList = new ArrayList<>();
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
        }
        return orderList;
    }

}
