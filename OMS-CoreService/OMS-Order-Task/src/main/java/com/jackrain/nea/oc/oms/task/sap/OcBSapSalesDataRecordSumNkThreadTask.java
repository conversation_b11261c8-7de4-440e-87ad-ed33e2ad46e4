package com.jackrain.nea.oc.oms.task.sap;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordSumNkThreadService;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * Description: 奶卡冲抵单汇总
 *
 * @Author: guo.kw
 * @Since: 2022/9/14
 * create at: 2022/9/14 20:04
 */
@Slf4j
@Component
public class OcBSapSalesDataRecordSumNkThreadTask implements IR3Task {

    @Autowired
    private OcBSapSalesDataRecordSumNkThreadService service;

    private final static String SAP_SALES_DATA_RECORD_SUN_NK = "OcBSapSalesDataRecordSumNkThreadTaskRedisKey";


    @Override
    @XxlJob("OcBSapSalesDataRecordSumNkThreadTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult resultTask = new RunTaskResult();
        resultTask.setSuccess(Boolean.TRUE);
        resultTask.setMessage("success");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Finish OcBSapSalesDataRecordSumNkThreadTask execute start"));
        }
        CusRedisTemplate<Object, Object> strRedisTemplate = RedisOpsUtil.getStrRedisTemplate();
        try {
            if (strRedisTemplate.hasKey(SAP_SALES_DATA_RECORD_SUN_NK)) {
                log.info("OcBSapSalesDataRecordSumNkThreadTask RunTaskResult is running");
                resultTask.setMessage("RunTaskResult is running");
                return resultTask;
            } else {
                strRedisTemplate.opsForValue().set(SAP_SALES_DATA_RECORD_SUN_NK, SAP_SALES_DATA_RECORD_SUN_NK, 6L, TimeUnit.HOURS);
            }
            ValueHolderV14 v14 = service.executeThread();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Finish OcBSapSalesDataRecordSumNkThreadTask.execute.result:{}"), JSONObject.toJSONString(v14));
            }
            if (!v14.isOK()) {
                resultTask.setSuccess(Boolean.FALSE);
                resultTask.setMessage(v14.getMessage());
            }
            strRedisTemplate.delete(SAP_SALES_DATA_RECORD_SUN_NK);
        } catch (Exception e) {
            log.error(LogUtil.format("Error OcBSapSalesDataRecordSumNkThreadTask.execute"), Throwables.getStackTraceAsString(e));
            resultTask.setSuccess(Boolean.FALSE);
            resultTask.setMessage(e.getMessage());
            strRedisTemplate.delete(SAP_SALES_DATA_RECORD_SUN_NK);
            //strRedisTemplate.opsForValue().set(SAP_SALES_DATA_RECORD_SUN_NK, SAP_SALES_DATA_RECORD_SUN_NK, 1L, TimeUnit.NANOSECONDS);

        }
        return resultTask;
    }
}
