package com.jackrain.nea.oc.oms.task.lockorder;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.xxl.job.starter.helper.R3XxlJobParamHelper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.lock.lockorder.OrderLockProcessImpl;
import com.jackrain.nea.oc.oms.services.IpOrderLockQueryService;
import com.jackrain.nea.oc.oms.services.IpOrderLockService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单锁单自动锁单定时任务
 *
 * @author: huang.zaizai
 * @since: 2019-10-10
 * create at : 2019-10-10 10:10
 */
@Component
@Slf4j
public class AutoOrderLockTask extends BaseR3Task implements IR3Task {

    private static final Integer totalConst = 2000;
    @Autowired
    private IpOrderLockQueryService ipOrderLockQueryService;
    @Autowired
    private IpOrderLockService ipOrderLockService;
    @Autowired
    private OrderLockProcessImpl orderLockProcessImpl;

    @Override
    @XxlJob("AutoOrderLockTask")
    public RunTaskResult execute(JSONObject params) {
        params = R3XxlJobParamHelper.xxlParam2R3Json();
        RunTaskResult result = new RunTaskResult();
        int total = totalConst;
        try {
            total = params.getInteger("total");
        } catch (Exception ex) {
            log.error(LogUtil.format("获取Lts参数params：total异常！"));
        }
        try {
            List<Long> idList = ipOrderLockQueryService.selectLockKey(0, total);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AutoOrderLockTask###idList={}"), JSONObject.toJSONString(idList));
            }
            List<IpOrderLockRelation> ipOrderLockRelationList = new ArrayList<>();
            for (Long id : idList) {
                IpOrderLockRelation orderLockRelation = this.ipOrderLockService.getLockRelation(id);
                if (orderLockRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoOrderLockTask.Order.NotExist!###Id=" + id);
                    log.error(LogUtil.format(errorMessage));
                } else {
                    ipOrderLockRelationList.add(orderLockRelation);
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.orderLockProcessImpl, ipOrderLockRelationList);
            result.setSuccess(true);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("AutoOrderLockTask.Execute.异常: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        return result;
    }
}
