package com.jackrain.nea.oc.oms.task.outofstock;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.process.wms.callwms.OrderAutoOutOfStockProcess;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单出库任务
 *
 * @author: 胡林洋
 * @since: 2019-03-29
 * create at : 2019-03-29 11:01
 */
@Slf4j
@Component
public class AutoOutOfStockOrderTask extends BaseR3Task implements IR3Task {
    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private OrderAutoOutOfStockProcess orderAutoOutOfStockProcess;

    @Autowired
    private ElasticSearchUtil elasticSearchUtil;

    @Override
    @XxlJob("AutoOutOfStockOrderTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            JSONObject whereKey = new JSONObject();
            whereKey.put("ORDER_STATUS", OmsOrderStatus.IN_DISTRIBUTION.toInteger());
            whereKey.put("WMS_CANCEL_STATUS", OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_FAIL.toInteger());

            List<Long> orderIdList = ES4Order.queryEsOrderList(whereKey, 0, DEFAULT_PAGE_SIZE);

            List<OcBOrderRelation> orderRelationList = new ArrayList<>();

            for (Long orderId : orderIdList) {
                OcBOrderRelation orderRelation = this.orderService.selectOmsOrderInfo(orderId);
                if (orderRelation == null) {
                    String errorMessage = Resources.getMessage("AutoOutOfStockOrderTask Order NotExist!OrderId="
                            + orderId);
                    log.error(errorMessage);
                } else {
                    orderRelationList.add(orderRelation);
                }
            }

            threadOrderProcessor.startMultiThreadExecute(this.orderAutoOutOfStockProcess, orderRelationList);
            result.setSuccess(true);

        } catch (Exception ex) {
            log.error(LogUtil.format("AutoOutOfStockOrderTask.Execute Erro！{}",  "AutoOutOfStockOrderTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }

    public static void main(String[] args) {
        int a = 300 / 500;
        System.out.println(a);
    }
}
