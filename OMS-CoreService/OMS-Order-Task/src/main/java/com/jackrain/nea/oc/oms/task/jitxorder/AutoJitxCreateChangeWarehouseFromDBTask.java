//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
//import com.jackrain.nea.oc.oms.services.JitxService;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @create 2020-12-15
// * @desc JITX订单创建改仓工单任务
// **/
//@Slf4j
//@Component
//@Deprecated
//public class AutoJitxCreateChangeWarehouseFromDBTask extends AbstractJitxChangeWarehouseTask {
//
//    @Autowired
//    private JitxService jitxService;
//
//    @Value("${oms.oc.order.jitx.createChangeWarehouse.pull.num:1000}")
//    private Integer pullNum;
//
//    @Override
//    protected String threadPoolName() {
//        return "R3_OMS_JITX_CREATE_CHANGE_WAREHOUSE_THREAD_POOL_%d";
//    }
//
//    @Override
//    protected String executeSqlWhere() {
//        return " WHERE CREATED_STATUS IN(0,2) AND ISACTIVE = 'Y' AND NEW_DELIVERY_WAREHOUSE is not null ";
//    }
//
//    @Override
//    protected String executeSqlOrder() {
//        return " ORDER BY SELLER_NICK,VENDOR_ID ASC ";
//    }
//
//    @Override
//    protected Integer getPullNum() {
//        return pullNum;
//    }
//
//    @Override
//    protected void exceute(List<OcBJitxModifyWarehouseLog> modifyWarehouseLogList, User operateUser) {
//        if (log.isDebugEnabled()) {
//            log.debug("JITX订单创建改仓工单任务，modifyWarehouseLogListSize：{}", modifyWarehouseLogList.size());
//        }
//        jitxService.createChangeWarehouseWorkflows(modifyWarehouseLogList, operateUser);
//    }
//}
