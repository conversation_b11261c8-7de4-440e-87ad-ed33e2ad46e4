//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.jackrain.nea.ip.model.vips.VipCreateShipResetWorkflowRequest;
//import com.jackrain.nea.ip.model.vips.VipCreateShipResetWorkflowResult;
//import com.jackrain.nea.oc.oms.model.table.IpBJitxResetShipWorkflow;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.web.face.User;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * description：JixX发货重置创建
// *
// * <AUTHOR>
// * @date 2021/10/18
// */
//@Slf4j
//@Component
//@Deprecated
//public class AutoJitxCreateResetShipWorkflowFromDBTask extends AbstractJitxResetShipTask {
//
//
//    @Value("${oms.oc.order.jitx.resetShip.pull.num:1000}")
//    private Integer pullNum;
//
//    @Override
//    protected String threadPoolName() {
//        return "R3_OMS_JITX_CREATE_RESET_SHIP_THREAD_POOL_%d";
//    }
//
//    @Override
//    protected String executeSqlWhere() {
//        return " WHERE CREATED_STATUS IN(0,2) AND ISACTIVE = 'Y' ";
//    }
//
//    @Override
//    protected String executeSqlOrder() {
//        return " ORDER BY SELLER_NICK,VENDOR_ID ASC ";
//    }
//
//    @Override
//    protected Integer getPullNum() {
//        return pullNum;
//    }
//
//    @Override
//    protected void exceute(List<IpBJitxResetShipWorkflow> shipWorkflowList, User operateUser) {
//        if (log.isDebugEnabled()) {
//            log.debug("JITX订单创建发货重置工单任务，AutoJitxCreateResetShipWorkflowTask size：{}", shipWorkflowList.size());
//        }
//        if (CollectionUtils.isEmpty(shipWorkflowList)) {
//            return;
//        }
//        VipCreateShipResetWorkflowRequest request = new VipCreateShipResetWorkflowRequest();
//        List<VipCreateShipResetWorkflowRequest.VipShipResetWorkflowItem> itemCreateRequestList = new ArrayList<>(shipWorkflowList.size());
//        for (IpBJitxResetShipWorkflow workflow : shipWorkflowList) {
//            VipCreateShipResetWorkflowRequest.VipShipResetWorkflowItem itemCreateRequest = new VipCreateShipResetWorkflowRequest.VipShipResetWorkflowItem();
//            itemCreateRequest.setOrder_sn(workflow.getOrderSn());
//            itemCreateRequest.setRequest_id(workflow.getRequestId());
//            itemCreateRequest.setReason_code(workflow.getReasonCode());
//            itemCreateRequest.setReason_remark(workflow.getReasonRemark());
//            itemCreateRequestList.add(itemCreateRequest);
//        }
//        request.setWorkflows(itemCreateRequestList);
//        request.setVendor_id(Integer.valueOf(shipWorkflowList.get(0).getVendorId()));
//        request.setSeller_nick(shipWorkflowList.get(0).getSellerNick());
//        ValueHolderV14<VipCreateShipResetWorkflowResult> v14 = ipRpcService.createShipResetWorkflowVop(request);
//        ipBJitxResetShipWorkflowService.transferResult(shipWorkflowList, v14);
//    }
//}
