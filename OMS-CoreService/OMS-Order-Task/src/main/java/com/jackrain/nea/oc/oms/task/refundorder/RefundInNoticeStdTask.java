//package com.jackrain.nea.oc.oms.task.refundorder;
//
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.collect.Lists;
//import com.jackrain.nea.model.util.ModelUtil;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnStdNotice;
//import com.jackrain.nea.oc.oms.services.OcBReturnStdNoticeService;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.rpc.StdRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.util.DateUtil;
//import com.jackrain.nea.web.face.User;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.time.DateUtils;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @description: 退货单单入库审核后回传std私域
// * @date 2021/12/30 14:35
// */
//@Slf4j
//@RequiredArgsConstructor
//@Component
//public class RefundInNoticeStdTask implements IR3Task {
//
//    private final OcBReturnStdNoticeService ocBReturnStdNoticeService;
//
//    private final StdRpcService stdRpcService;
//
//    private final OcBReturnOrderLogMapper ocBReturnOrderLogMapper;
//
//    @Override
//    public RunTaskResult execute(JSONObject param) {
//        RunTaskResult result = new RunTaskResult();
//        log.info("Start RefundInNoticeStdTask.execute. ReceiveParams:params:{};", JSONObject.toJSONString(param));
//        int limit = param.getIntValue("limit");
//        int times = param.getIntValue("times");
//        List<OcBReturnStdNotice> ocBReturnStdNotices = ocBReturnStdNoticeService.listNeedToStd(limit > 0 ? limit : 100, times > 0 ? times : 5);
//        if(CollectionUtils.isEmpty(ocBReturnStdNotices)){
//            result.setSuccess(true);
//            result.setMessage("【std回传退货入库结果任务】暂无需要执行的数据");
//        }
//        Date now = new Date();
//        Map<Integer, List<OcBReturnStdNotice>> noticeMap = ocBReturnStdNotices.stream().collect(Collectors.groupingBy(OcBReturnStdNotice::getPlatform));
//        User rootUser = SystemUserResource.getRootUser();
//        List<OcBReturnOrderLog> logList = Lists.newArrayListWithCapacity(ocBReturnStdNotices.size());
//        noticeMap.keySet().stream().forEach(platform->{
//            List<OcBReturnStdNotice> noticesList = noticeMap.get(platform);
//            List<JSONObject> dataList = noticesList.stream().map(e -> {
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("platform", e.getPlatform());
//                jsonObject.put("tid", e.getTid());
//                jsonObject.put("refund_amount", e.getReturnAmtActual());
//                jsonObject.put("refund_id", e.getReturnId());
//                jsonObject.put("out_sid", e.getLogisticsCode());
//                jsonObject.put("warehousing_time", DateUtil.format(e.getInTime(),"yyyy-MM-dd HH:mm:ss"));
//                jsonObject.put("num", e.getQtyInstore());
//                e.setNoticeStatus(1);
//                e.setUpdateTime(now);
//                return jsonObject;
//            }).collect(Collectors.toList());
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("data",dataList);
//            ValueHolderV14 valueHolderV14 = stdRpcService.refundInNotice(jsonObject, platform);
//            if(!valueHolderV14.isOK()){
//                noticesList.stream().forEach(e->{
//                    int curTimes = e.getNoticeTimes()==null?0:e.getNoticeTimes();
//                    e.setNoticeTimes(curTimes+1);
//                    e.setNoticeStatus(-1);
//                    e.setErrorMsg(this.strCountLimit(valueHolderV14.getMessage()));
//                    e.setUpdateTime(now);
//                });
//            }else{
//                logList.addAll(noticesList.stream().map(e -> this.buildReturnOrderLog(e.getOcBReturnOrderId(), "退货入库回传std", "退货入库回传std成功", rootUser)).collect(Collectors.toList()));
//            }
//        });
//        if(CollectionUtils.isNotEmpty(logList)){
//            ocBReturnOrderLogMapper.batchInsert(logList);
//        }
//        ocBReturnStdNoticeService.updateBatchById(ocBReturnStdNotices);
//        result.setSuccess(true);
//        result.setMessage("【std回传退货入库结果任务】执行成功，共回传std："+ocBReturnStdNotices.size()+"条，成功："+logList.size()+"条");
//        return result;
//    }
//
//
//
//    /**
//     * 组装退换货单日志
//     *
//     * @param user 操作用户
//     * @param id   退单编号
//     */
//    private OcBReturnOrderLog buildReturnOrderLog(Long id, String type, String logMsg, User user) {
//        OcBReturnOrderLog ocBReturnOrderLog = new OcBReturnOrderLog();
//        ocBReturnOrderLog.setAdClientId(user.getClientId() + 0L);
//        ocBReturnOrderLog.setAdOrgId(user.getOrgId() + 0L);
//        ocBReturnOrderLog.setLogType(type);
//        ocBReturnOrderLog.setLogMessage(logMsg);
//        ocBReturnOrderLog.setIpAddress(user.getLastloginip());
//        ocBReturnOrderLog.setUserName(user.getName());
//        ocBReturnOrderLog.setOwnerename(user.getEname());
//        ocBReturnOrderLog.setOcBReturnOrderId(id);
//        ocBReturnOrderLog.setId(ModelUtil.getSequence("oc_b_return_order_log"));
//        return ocBReturnOrderLog;
//    }
//
//    /**
//     * 字数控制
//     *
//     * @param str
//     */
//    private String strCountLimit(String str){
//        return StringUtils.substring(str,0,100);
//    }
//}
