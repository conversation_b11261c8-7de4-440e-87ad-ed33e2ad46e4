package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.services.IpTaobaoOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @since 2020-06-22
 * Created at 2020-06-22 14:49
 */
@Component
@Slf4j
public class AutoTaobao2MqTransferTask extends BaseR3Task implements IR3Task {
    @Autowired
    private DefaultProducerSend defaultProducerSend;
    @Autowired
    private TransferOrderMqConfig transferOrderMqConfig;
    @Autowired
    private IpTaobaoOrderService ipTaobaoOrderService;
    @Autowired
    private ThreadPoolTaskExecutor tbOrderTransferThreadPoolExecutor;

    private final String tableName = "ip_b_taobao_order";
    private final String taskName = "AutoTaobao2MqTransferTask";

    @Override
//    @XxlJob("AutoTaobao2MqTransferTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        long start = System.currentTimeMillis();

        try {

            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer size = config.getProperty("lts.AutoTaobao2MQTransferTask.range", DEFAULT_PAGE_SIZE);
            Integer status = config.getProperty("lts.AutoTaobao2MQTransferTask.order.status", TransferOrderStatus.NOT_TRANSFER.toInteger());
            Integer minutes = config.getProperty("lts.AutoTaobao2MQTransferTask.order.minutes", 0);

            Map<String, String> topMap = null;
            Set<String> nodes = topMap.keySet();
            if (CollectionUtils.isEmpty(nodes)) {
                result.setSuccess(false);
                result.setMessage("请检查DRDS环境，node信息获取失败！");
                return result;
            }
            List<Future<Integer>> results = new ArrayList<>();
            for (String node : nodes) {
                results.add(tbOrderTransferThreadPoolExecutor.submit(new UnTransferOrderToMqCallable(node, topMap.get(node), size, status, minutes)));
            }
            int executeCount = 0;
            for (Future<Integer> futureResult : results) {
                try {
                    executeCount += futureResult.get();
                } catch (Exception e) {
                    log.error(LogUtil.format("{} Thread ExecutionException,异常信息:{}", "ExecutionException"), taskName, Throwables.getStackTraceAsString(e));
                }
            }
            long end = System.currentTimeMillis();
            result.setSuccess(true);
            result.setMessage(taskName + " 执行完毕, 数量：" + executeCount + ", 用时: " + (end - start) + " ms");
        } catch (Exception ex) {
            log.error(LogUtil.format("{} execute error: {}", taskName), taskName, Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        } finally {

        }

        return result;
    }

    /**
     * debug level log
     *
     * @param msg message
     * @param obj objects
     */
    protected void debugLog(String msg, Object... obj) {
        if (log.isDebugEnabled()) {
            log.debug(msg, obj);
        }
    }

    /**
     * 内部类，淘宝补偿订单发送到MQ
     */
    class UnTransferOrderToMqCallable implements Callable<Integer> {
        private final String nodeName;
        private final String tableName;
        private final Integer eachSize;
        private final Integer isTrans;
        private final Integer minutes;

        public UnTransferOrderToMqCallable(String nodeName, String tableName, Integer eachSize, Integer isTrans, Integer minutes) {
            this.nodeName = nodeName;
            this.tableName = tableName;
            this.eachSize = eachSize;
            this.isTrans = isTrans;
            this.minutes = minutes;
        }

        @Override
        public Integer call() {
            String threadName = Thread.currentThread().getName();
            String remarks = "";
            // 获取转换失败的淘宝订单不需要用系统备注作为条件，所以设置为null
            if (isTrans == TransferOrderStatus.TRANSFERFAIL.toInteger()) {
                remarks = null;
            }
            List<String> orderNoList = ipTaobaoOrderService.selectDynamicTaskOrder(nodeName, tableName, eachSize, isTrans, remarks, minutes);
            if (orderNoList == null || orderNoList.size() == 0) {
                log.warn("{} 未查到补偿转单数据", threadName);
                return 0;
            }
            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
                orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
                orderMqInfo.setChannelType(ChannelType.TAOBAO);
                orderMqInfo.setOrderType(OrderType.NORMAL);
                orderMqInfo.setOrderNo(orderNo);
                mqInfoList.add(orderMqInfo);
            }
            String jsonValue = JSONObject.toJSONString(mqInfoList);
            int step = 1;
            try {
                if (isTrans == TransferOrderStatus.TRANSFERFAIL.toInteger()) {
                    ipTaobaoOrderService.updateSysRemark(orderNoList, "", false);
                }
                step = 2;
                MqSendResult result = defaultProducerSend.sendTopic(MqConstants.TOPIC_R3_OC_OMS_CALL_TRANSFER, MqConstants.TAG_R3_OC_OMS_CALL_TRANSFER, jsonValue, null);
                log.info("{} 补偿转单发送MQ结果：{}", threadName, result.getMessageId());
                step = 3;
                ipTaobaoOrderService.updateSysRemark(orderNoList, "补偿转单", true);
            } catch (Exception e) {
                switch (step) {
                    case 1:
                        log.error("{} 更新(isTrans=4)备注信息异常：{}", threadName, e);
                        break;
                    case 2:
                        log.error("{} 补偿转单发送MQ异常：{}", threadName, e);
                        break;
                    case 3:
                        log.error("{} 补偿转单发送MQ成功！更新备注信息异常：{}", threadName, e);
                        break;
                    default:
                        log.error("{} 补偿转单未知异常：{}", threadName, e);
                        break;
                }
                if (isTrans == TransferOrderStatus.TRANSFERFAIL.toInteger()) {
                    ipTaobaoOrderService.updateSysRemark(orderNoList, "补偿转单发生异常", false);
                } else {
                    ipTaobaoOrderService.updateSysRemark(orderNoList, "", false);
                }
                return 0;
            }
            return orderNoList.size();
        }
    }
}
