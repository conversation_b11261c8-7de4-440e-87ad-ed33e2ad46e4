package com.jackrain.nea.oc.oms.task.autoupaddress;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.es.ES4TBModifyAddr;
import com.jackrain.nea.oc.oms.services.OcBOrderUpdateAddressService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 淘宝预售地址自动修改
 *
 * @date 2019/10/12
 * @author: ming.fz
 */
@Component
@Slf4j
public class AutoOrderUpdateAddressTask extends BaseR3Task implements IR3Task {

    @Autowired
    OcBOrderUpdateAddressService ocBOrderUpdateAddressService;

    @Override
    @XxlJob("AutoOrderUpdateAddressTask")
    public RunTaskResult execute(JSONObject params) {
        long start = System.currentTimeMillis();
        RunTaskResult result = new RunTaskResult();
        try {
            List<String> orderSourceCodes = ES4TBModifyAddr.findSourceCodeByIsUpdate(0, DEFAULT_PAGE_SIZE);
            for (String orderSourceCode : orderSourceCodes) {
                ValueHolderV14<String> v14 = ocBOrderUpdateAddressService
                        .updateOrderRegion(true, orderSourceCode, SystemUserResource.getRootUser());
                ocBOrderUpdateAddressService.invokePushPlatform(v14,orderSourceCode);
            }

            result.setSuccess(true);
            result.setMessage("耗时：" + (System.currentTimeMillis() - start) + "ms");
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("AutoOrderUpdateAddressTask.Execute Error: {}"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}
