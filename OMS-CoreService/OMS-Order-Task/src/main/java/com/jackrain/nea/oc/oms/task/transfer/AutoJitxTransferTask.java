package com.jackrain.nea.oc.oms.task.transfer;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.es.ES4IpJitXOrder;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.JitxTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJitxOrderService;
import com.jackrain.nea.oc.oms.task.BaseR3Task;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 唯品会JITX转单自动补偿任务
 * 1、查询ES中ISTRANS=0的单据
 *
 * @author: 黄超
 * @since: 2019-06-27
 * create at : 2019-06-27 20:00
 */
@Component
@Slf4j
public class AutoJitxTransferTask extends BaseR3Task implements IR3Task {

    @Autowired
    private IpJitxOrderService ipJitxOrderService;

    @Autowired
    private JitxTransferOrderProcessImpl jitxTransferOrderProcess;

    @Override
    @XxlJob("AutoJitxTransferTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            Integer pageSize = config.getProperty("lts.AutoJitxOrderTransferTask.range", DEFAULT_PAGE_SIZE);

            // 从ES中查询未转换成功的单据信息
            List<String> orderNoList = ES4IpJitXOrder
                    .findOrderSnByTransStatusAndOrderStatus(0, pageSize, JitxOrderStatus.ORDER_ALREADY_AUDITED);

            if (log.isDebugEnabled()) {
                log.debug("###AutoJitxTransferTask###OrderNoList=" + JSONObject.toJSONString(orderNoList));
            }

            List<IpJitxOrderRelation> orderRelationList = new ArrayList<>();
            for (String orderNo : orderNoList) {
                IpJitxOrderRelation jitxOrderRelation = this.ipJitxOrderService.selectJitxOrder(orderNo);
                if (jitxOrderRelation == null) {
                    String errorMessage = Resources.getMessage("###AutoJitxTransferTask.Order.NotExist!###OrderNo="
                            + orderNo);
                    log.error(errorMessage);
                } else {
                    orderRelationList.add(jitxOrderRelation);
                }
            }

            if (CollectionUtils.isNotEmpty(orderRelationList)) {
                threadOrderProcessor.startMultiThreadExecute(this.jitxTransferOrderProcess, orderRelationList);
            }

            result.setSuccess(true);
        } catch (Exception ex) {
            log.error(LogUtil.format("AutoJitxTransferTask,异常信息:{}", "AutoJitxTransferTask"), Throwables.getStackTraceAsString(ex));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }

        return result;

    }
}
