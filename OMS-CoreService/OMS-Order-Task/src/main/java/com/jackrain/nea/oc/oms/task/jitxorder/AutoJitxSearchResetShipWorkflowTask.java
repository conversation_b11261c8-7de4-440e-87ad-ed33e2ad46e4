package com.jackrain.nea.oc.oms.task.jitxorder;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.IpBJitxResetShipWorkflowMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJitxResetShipWorkflow;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowCreatedStateEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowStateEnum;
import com.jackrain.nea.oc.oms.services.IpBJitxResetShipWorkflowService;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.task.IR3Task;
import com.jackrain.nea.task.RunTaskResult;
import com.jackrain.nea.web.face.User;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * description：JixX发货重置查询
 *
 * <AUTHOR>
 * @date 2021/10/18
 */
@Slf4j
@Component
public class AutoJitxSearchResetShipWorkflowTask implements IR3Task {


    @Value("${oms.oc.order.jitx.searchResetShipWorkflow.pull.num:200}")
    private Integer pullNum;

    @Autowired
    private IpBJitxResetShipWorkflowMapper resetShipWorkflowMapper;

    @Autowired
    private IpBJitxResetShipWorkflowService ipBJitxResetShipWorkflowService;

    /**
     * 最大失败次数
     */
    @Value("${oms.oc.order.jitx.resetShip.max.failNumber:5}")
    private Integer failNumber;


    @Override
    @XxlJob("AutoJitxSearchResetShipWorkflowTask")
    public RunTaskResult execute(JSONObject params) {
        RunTaskResult result = new RunTaskResult();
        List<Long> idList = searchFromEs(pullNum);
        if (CollectionUtils.isEmpty(idList)) {
            result.setMessage("未从ES查询到数据");
            result.setSuccess(true);
            return result;
        }
        List<IpBJitxResetShipWorkflow> dbDataList = resetShipWorkflowMapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(dbDataList)) {
            result.setMessage("未从数据库查询到数据");
            result.setSuccess(true);
            return result;
        }
        List<IpBJitxResetShipWorkflow> filteredList = new ArrayList<>(dbDataList.size());
        for (IpBJitxResetShipWorkflow dbData : dbDataList) {
            //失败次数超过限制
            if (dbData.getFailNumber() != null && dbData.getFailNumber() > failNumber) {
                continue;
            }
            //不是创建成功
            if (!VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode().equals(dbData.getCreatedStatus())) {
                continue;
            }
            if (!(VipJitxWorkflowStateEnum.CREATE.getKey().equals(dbData.getWorkflowStatus()) || VipJitxWorkflowStateEnum.DOING.getKey().equals(dbData.getWorkflowStatus()))) {
                continue;
            }
            filteredList.add(dbData);
        }
        User operateUser = SystemUserResource.getRootUser();
        if (CollectionUtils.isEmpty(filteredList)) {
            result.setMessage("ES数据查库后再次筛选无符合条件数据");
            result.setSuccess(true);
            return result;
        }
        if (log.isDebugEnabled()) {
            log.debug("JITX订单查询改仓工单任务，filtedList：{}", filteredList.size());
        }
        this.exceuteData(filteredList, operateUser);
        result.setMessage(String.format("执行数据条数%d:", filteredList.size()));
        result.setSuccess(true);
        return result;
    }

    public static List<Long> searchFromEs(Integer range) {
        log.info("jitx改仓工单查询查询ES数据开始 range:{}", range);

        JSONObject whereKeys = new JSONObject();
        JSONObject orderKey = new JSONObject();
        //修改时间升序
        orderKey.put("asc", false);
        orderKey.put("name", "MODIFIEDDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);

        JSONArray result = new JSONArray();
        result.add(VipJitxWorkflowStateEnum.CREATE.getKey());
        result.add(VipJitxWorkflowStateEnum.DOING.getKey());
        whereKeys.put("WORKFLOW_STATUS", result);

        whereKeys.put("CREATED_STATUS", VipJitxWorkflowCreatedStateEnum.CREATE_SUCCESS.getCode());

        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次
        filterKeys.put("FAIL_NUMBER", "~" + 5);

        String[] field = {"ID"};
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JITX_RESET_SHIP_WORKFLOW,
                OcElasticSearchIndexResources.IP_B_JITX_RESET_SHIP_WORKFLOW, whereKeys, filterKeys, order, range, 0, field);
        List<Long> orderList = new ArrayList<>();
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                orderList.add(jsonObject.getLong("ID"));
            }
        }
        return orderList;
    }

    private void exceuteData(List<IpBJitxResetShipWorkflow> shipWorkflowList, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("JITX订单获取重置发货工单任务，AutoJitxSearchResetShipWorkflowTask size：{}", shipWorkflowList.size());
        }
        if (CollectionUtils.isEmpty(shipWorkflowList)) {
            return;
        }
        ipBJitxResetShipWorkflowService.getResetShipWorkflows(shipWorkflowList, operateUser);
    }
}
