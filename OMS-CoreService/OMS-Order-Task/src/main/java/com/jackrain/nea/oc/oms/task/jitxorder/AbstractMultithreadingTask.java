//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.task.BaseR3Task;
//import com.jackrain.nea.resource.SystemUserResource;
//import com.jackrain.nea.task.IR3Task;
//import com.jackrain.nea.task.RunTaskResult;
//import com.jackrain.nea.web.face.User;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
//import java.util.Map;
//import java.util.Set;
//
///**
// * <AUTHOR>
// * @create 2020-12-16
// * @desc
// **/
//@Slf4j
//@Deprecated
//public abstract class AbstractMultithreadingTask extends BaseR3Task implements IR3Task {
//
//    @Autowired
//    private ThreadPoolTaskExecutor commonThreadPoolExecutor;
//
//    protected abstract String threadPoolName();
//
//    protected abstract String taskTableName();
//
//
//}
