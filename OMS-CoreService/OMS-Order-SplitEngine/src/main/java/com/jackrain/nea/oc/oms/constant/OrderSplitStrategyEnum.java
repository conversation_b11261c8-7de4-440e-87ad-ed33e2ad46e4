package com.jackrain.nea.oc.oms.constant;

public enum OrderSplitStrategyEnum {

    PRESALE("PRESALE", "预售拆单"),

    COMMODITY("COMMODITY", "商品属性拆单");

    private String code;

    private String message;

    OrderSplitStrategyEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }


    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
