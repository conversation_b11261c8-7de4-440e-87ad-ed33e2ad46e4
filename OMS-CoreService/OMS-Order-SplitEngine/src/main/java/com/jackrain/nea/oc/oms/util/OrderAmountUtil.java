package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.SkuType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 重新计算订单金额明细金额数据
 * @author: 江家雷
 * @since: 2020/6/28
 * create at : 2020/6/28 21:18
 */
@Slf4j
@Component
public class OrderAmountUtil {

    public OcBOrder recountOrderAmount(OcBOrderParam param) {
        OcBOrderRelation relation = new OcBOrderRelation();
        relation.setOrderInfo(param.getOcBOrder());
        relation.setOrderItemList(param.getOrderItemList());
        return recountOrderAmount(relation);
    }

    /***
     * 订单金额计算(重算订单金额时 用订单明细里 pro_type =4 或 pro_type = 0)计算
     * @param orderRelation
     */
    public OcBOrder recountOrderAmount(OcBOrderRelation orderRelation) {
        OcBOrder ocBOrder = orderRelation.getOrderInfo();
        List<OcBOrderItem> items = orderRelation.getOrderItemList();
        List<Long> restoreIds = orderRelation.getRestoreIds();
        if (CollectionUtils.isEmpty(items)) {
            return ocBOrder;
        }
        // 商品金额
        BigDecimal productAmt = BigDecimal.ZERO;
        // 订单总金额
        BigDecimal orderAmt;
        // 商品优惠金额
        BigDecimal productDiscountAmt = BigDecimal.ZERO;
        // 订单优惠金额
        BigDecimal orderDiscountAmt = BigDecimal.ZERO;
        // 商品总数量
        BigDecimal qtyAll = BigDecimal.ZERO;
        // 调整金额
        BigDecimal adjustAmt = BigDecimal.ZERO;
        // 订单总重量
        BigDecimal weight = BigDecimal.ZERO;

        int skuKindQty = 0;
        //加上sku
        String allSku = "";
        boolean flag = true;
        for (OcBOrderItem item : items) {
            Long proType = Optional.ofNullable(item.getProType()).orElse(0L);
            // 打组合标
            if (proType.intValue() == SkuType.NO_SPLIT_COMBINE) {
                ocBOrder.setIsCombination(1);
            } else {
                skuKindQty++;
            }
            if (proType.intValue() == SkuType.GIFT_PRODUCT || proType.intValue() == SkuType.COMBINE_PRODUCT) {
                continue;
            }
            // 打赠品标
            if (Optional.ofNullable(item.getIsGift()).orElse(0) == 1) {
                ocBOrder.setIsHasgift(1);
            }

            productDiscountAmt =
                    productDiscountAmt.add(Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO));
            orderDiscountAmt =
                    orderDiscountAmt.add(Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO));
            adjustAmt = adjustAmt.add(Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO));
            qtyAll = qtyAll.add(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO));
            weight = weight.add(Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO));

            BigDecimal price = Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO);
            String groupGoodsMark = item.getGroupGoodsMark();
            String equalExchangeRatio = item.getEqualExchangeRatio();
            if (StringUtils.isNotEmpty(groupGoodsMark) || StringUtils.isNotEmpty(equalExchangeRatio)) {
                BigDecimal orderSplitAmt = Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO);
                BigDecimal subtract = item.getAmtDiscount().add(orderSplitAmt).add(item.getRealAmt()).subtract(item.getAdjustAmt());
                productAmt = productAmt.add(subtract);
                item.setPrice(subtract.divide(item.getQty(), 4, BigDecimal.ROUND_HALF_UP));
                if (StringUtils.isNotEmpty(equalExchangeRatio)) {
                    if (CollectionUtils.isNotEmpty(restoreIds) && restoreIds.contains(item.getId())) {
                        //缺货不还原，不重新计算售价
                    } else {
                        String[] split = equalExchangeRatio.split(":");
                        BigDecimal ratio1 = new BigDecimal(split[0]);
                        BigDecimal ratio2 = new BigDecimal(split[1]);
                        BigDecimal ratioQty = ratio1.divide(ratio2);
                        // fixme 对等换货后这里订单明细的价格不需要重新平台售价，不然和成交金额对不上
//                        item.setPrice(subtract.divide(ratioQty, 4, BigDecimal.ROUND_HALF_UP));
                    }
                }
            } else {
                //因为组合商品拆分  平台售价是原单的
                productAmt = productAmt.add(price
                        .multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)));
            }

            if (flag) {
                if (allSku.length() >= 100) {
                    allSku += "...";
                    flag = false;
                } else {
                    allSku = allSku + item.getPsCSkuEcode() + "(" + item.getQty().intValue() + "),";
                }
            }
            item.setPriceActual(item.getRealAmt().divide(item.getQty(), 4, BigDecimal.ROUND_HALF_UP));

        }
        // 商品金额
        ocBOrder.setProductAmt(productAmt);
        // 服务费
        ocBOrder.setServiceAmt(Optional.ofNullable(ocBOrder.getServiceAmt()).orElse(BigDecimal.ZERO));
        // 物流费用
        ocBOrder.setShipAmt(Optional.ofNullable(ocBOrder.getShipAmt()).orElse(BigDecimal.ZERO));
        // 商品优惠金额
        ocBOrder.setProductDiscountAmt(productDiscountAmt);
        // 订单优惠金额
        ocBOrder.setOrderDiscountAmt(orderDiscountAmt);
        // 调整金额
        ocBOrder.setAdjustAmt(adjustAmt);
        // 订单总金额
        orderAmt = productAmt.subtract(productDiscountAmt)
                .subtract(orderDiscountAmt)
                .add(adjustAmt).add(ocBOrder.getShipAmt());
        ocBOrder.setOrderAmt(orderAmt);
        // 已收金额（对应详情页已支付金额）已支付金额 = 订单总金额 (拆合单时的逻辑)
        ocBOrder.setReceivedAmt(orderAmt);
        // 商品总数量
        ocBOrder.setQtyAll(qtyAll);
        // SKU条数（排除组合商品的条数）
        ocBOrder.setSkuKindQty(new BigDecimal(skuKindQty));
        // 订单商品总重量
        ocBOrder.setWeight(weight);
        if (StringUtils.isNotEmpty(allSku)) {
            ocBOrder.setAllSku(allSku.substring(0, allSku.length() - 1));
        }
        return ocBOrder;
    }


    /**
     * 商品金额
     *
     * @param items
     * @return
     */
    private BigDecimal getProductAmt(List<OcBOrderItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return BigDecimal.ZERO;
        }
        return items.stream()
                .filter(item -> Optional.ofNullable(item.getRefundStatus()).orElse(0) != 6 && Optional.ofNullable(item.getProType()).orElse(0L) != 4)
                .map(item -> Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO).multiply(Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private Integer getIsCombination(List<OcBOrderItem> items) {
        items = items.stream().filter(item -> item.getProType() != null &&
                (item.getProType().intValue() == SkuType.GIFT_PRODUCT || item.getProType().intValue() == SkuType.COMBINE_PRODUCT))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(items)) {
            return 1;
        } else {
            return 0;
        }
    }

    /**
     * 订单优惠金额
     *
     * @param items
     * @return
     */
    private BigDecimal getOrderSplitAmt(List<OcBOrderItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return BigDecimal.ZERO;
        }
        return items.stream()
                .filter(item -> Optional.ofNullable(item.getRefundStatus()).orElse(0) != 6 && item.getProType().intValue() != 4)
                .map(item -> Optional.ofNullable(item.getOrderSplitAmt()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 商品优惠金额
     *
     * @param items
     * @return
     */
    private BigDecimal getAmtDiscount(List<OcBOrderItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return BigDecimal.ZERO;
        }
        return items.stream()
                .filter(item -> Optional.ofNullable(item.getRefundStatus()).orElse(0) != 6 && item.getProType().intValue() != 4)
                .map(item -> Optional.ofNullable(item.getAmtDiscount()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    /**
     * 调整金额
     *
     * @param items
     * @return
     */
    private BigDecimal getAdjustAmt(List<OcBOrderItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return BigDecimal.ZERO;
        }
        return items.stream()
                .filter(item -> Optional.ofNullable(item.getRefundStatus()).orElse(0) != 6 && item.getProType().intValue() != 4)
                .map(item -> Optional.ofNullable(item.getAdjustAmt()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 订单总金额
     *
     * @param ocBOrder
     * @param items
     * @return
     */
    private BigDecimal getOrderAmt(OcBOrder ocBOrder, List<OcBOrderItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return BigDecimal.ZERO;
        }
        return getProductAmt(items)
                .subtract(getAmtDiscount(items))
                .subtract(getOrderSplitAmt(items))
                .add(getAdjustAmt(items))
                .add(Optional.ofNullable(ocBOrder.getShipAmt()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(ocBOrder.getServiceAmt()).orElse(BigDecimal.ZERO));
    }

    /**
     * 订单总重量
     *
     * @param items
     * @return
     */
    private BigDecimal getWeight(List<OcBOrderItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return BigDecimal.ZERO;
        }
        return items.stream()
                .map(item -> Optional.ofNullable(item.getStandardWeight()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 订单商品数量
     *
     * @param items
     * @return
     */
    private BigDecimal getQtyAll(List<OcBOrderItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return BigDecimal.ZERO;
        }
        return items.stream()
                .filter(item -> item.getProType() != null && item.getProType().intValue() != SkuType.NO_SPLIT_COMBINE)
                .map(item -> Optional.ofNullable(item.getQty()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 缺货不还原标记金额计算
     *
     * @param param
     * @param restoreIds
     * @return
     */
    public OcBOrder recountOrderAmountResert(OcBOrderParam param, List<Long> restoreIds) {
        OcBOrderRelation relation = new OcBOrderRelation();
        relation.setOrderInfo(param.getOcBOrder());
        relation.setOrderItemList(param.getOrderItemList());
        relation.setRestoreIds(restoreIds);
        return recountOrderAmount(relation);
    }
}