package com.jackrain.nea.oc.oms.strategy;

import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.constant.OrderSplitStrategyEnum;
import com.jackrain.nea.oc.oms.factory.OrderSplitStrategyFactory;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.OrderAutoSplitByGoodsUtil;
import com.jackrain.nea.oc.oms.util.OrderSplitUtill;
import com.jackrain.nea.st.model.result.BaseResult;
import com.jackrain.nea.st.model.result.StCWarehouseQueryResult;
import com.jackrain.nea.st.model.table.StCWarehouseBrandDO;
import com.jackrain.nea.st.model.table.StCWarehouseDO;
import com.jackrain.nea.st.model.table.StCWarehouseGoodsClassDO;
import com.jackrain.nea.st.model.table.StCWarehouseGoodsDO;
import com.jackrain.nea.st.model.table.StCWarehouseSkuDO;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 仓库拆单（根据商品属性拆单）
 * @author: 江家雷
 * @since: 2020/6/28
 * create at : 2020/6/28 16:39
 */
@Component
@Slf4j
public class CommodityOrderSplitIml implements IOrderSplitStrategy {

    @Autowired
    private OrderAutoSplitByGoodsUtil orderAutoSplitByGoodsUtil;

    @Autowired
    private OrderAmountUtil orderAmountUtil;

    @Override
    public List<OcBOrderRelation> split(OcBOrderRelation ocBOrderRelation, User user, BaseResult strategy) {
        if (strategy == null) {
            return Lists.newArrayList();
        }
        StCWarehouseQueryResult warehouseQueryResult = (StCWarehouseQueryResult) strategy;
        if (warehouseQueryResult.getStCWarehouse() == null) {
            return Lists.newArrayList();
        }
        List<OcBOrderRelation> relationList = null;
        // 性别拆分
        if ("Y".equals(warehouseQueryResult.getStCWarehouse().getIsSexSplit())) {
            relationList = splitBySex(ocBOrderRelation, warehouseQueryResult);
        }
        // 按照品牌组拆分
        if ("Y".equals(warehouseQueryResult.getStCWarehouse().getIsBrandSplit())) {
            List<OcBOrderRelation> allRelationList = new ArrayList<>();
            if (CollectionUtils.isEmpty(relationList)) {
                relationList = splitByBrandGroup(ocBOrderRelation, warehouseQueryResult);
                if (relationList != null) {
                    allRelationList.addAll(relationList);
                }
            } else {
                for (OcBOrderRelation relation : relationList) {
                    List<OcBOrderRelation> temp = splitByBrandGroup(relation, warehouseQueryResult);
                    if (CollectionUtils.isEmpty(temp)) {
                        allRelationList.add(relation);
                    } else {
                        allRelationList.addAll(temp);
                    }
                }
            }
            relationList = allRelationList;
        }
        // 商品款号拆解
        if ("Y".equals(warehouseQueryResult.getStCWarehouse().getIsGoodsSplit())) {
            List<OcBOrderRelation> allRelationList = new ArrayList<>();
            if (CollectionUtils.isEmpty(relationList)) {
                relationList = splitByGoodsSpu(ocBOrderRelation, warehouseQueryResult);
                if (relationList != null) {
                    allRelationList.addAll(relationList);
                }
            } else {
                for (OcBOrderRelation relation : relationList) {
                    List<OcBOrderRelation> temp = splitByGoodsSpu(relation, warehouseQueryResult);
                    if (CollectionUtils.isEmpty(temp)) {
                        allRelationList.add(relation);
                    } else {
                        allRelationList.addAll(temp);
                    }
                }
            }
            relationList = allRelationList;
        }
        // 商品SKU拆分
        if ("Y".equals(warehouseQueryResult.getStCWarehouse().getIsSkuSplit())) {
            List<OcBOrderRelation> allRelationList = new ArrayList<>();
            if (CollectionUtils.isEmpty(relationList)) {
                relationList = splitByGoodsSku(ocBOrderRelation, warehouseQueryResult);
                if (relationList != null) {
                    allRelationList.addAll(relationList);
                }
            } else {
                for (OcBOrderRelation relation : relationList) {
                    List<OcBOrderRelation> temp = splitByGoodsSku(relation, warehouseQueryResult);
                    if (CollectionUtils.isEmpty(temp)) {
                        allRelationList.add(relation);
                    } else {
                        allRelationList.addAll(temp);
                    }
                }
            }
            relationList = allRelationList;
        }
        // 按商品品类拆分
        if("Y".equals(warehouseQueryResult.getStCWarehouse().getIsGoodsClassSplit())){
            List<OcBOrderRelation> allRelationList = new ArrayList<>();
            if (CollectionUtils.isEmpty(relationList)) {
                relationList = splitByGoodsClass(ocBOrderRelation, warehouseQueryResult);
                if (relationList != null) {
                    allRelationList.addAll(relationList);
                }
            } else {
                for (OcBOrderRelation relation : relationList) {
                    List<OcBOrderRelation> temp = splitByGoodsClass(relation, warehouseQueryResult);
                    if (CollectionUtils.isEmpty(temp)) {
                        allRelationList.add(relation);
                    } else {
                        allRelationList.addAll(temp);
                    }
                }
            }
            relationList = allRelationList;

        }
        if (relationList == null || relationList.size() <= 1) {
            return Lists.newArrayList();
        }
        // 整理表头金额
        int suffixInfo = 0;
        for(OcBOrderRelation relation : relationList){
            orderAmountUtil.recountOrderAmount(relation);
            // 补充信息
            suffixInfo++;
            relation.getOrderInfo().setSuffixInfo(ocBOrderRelation.getOrderId() + "-AUTO-GOOD-SP-" + suffixInfo);
        }
        return relationList;
    }

    //按商品品类拆单
    private List<OcBOrderRelation> splitByGoodsClass(OcBOrderRelation relation, StCWarehouseQueryResult strategy) {
        log.info(LogUtil.format("即将通过商品拆单Order{}"), relation);
        Boolean flag = false;
        List<OcBOrderRelation> result = new ArrayList<>();
        List<StCWarehouseGoodsClassDO> stCWarehouseGoodsClassList = strategy.getStCWarehouseGoodsClassList();
        // 策略中配置的单品类限制数
        Map<Long, Integer> goodClassMap =
                stCWarehouseGoodsClassList.stream().collect(Collectors.toMap(StCWarehouseGoodsClassDO::getPsCProdimId,
                        StCWarehouseGoodsClassDO::getNum, (v1, v2) -> v1));
        // 策略中配置的多品类限制
        Map<Long, Integer> otherGoodClassMap =
                stCWarehouseGoodsClassList.stream().collect(Collectors.toMap(StCWarehouseGoodsClassDO::getPsCProdimId,
                        StCWarehouseGoodsClassDO::getOtherNum, (v1, v2) -> v1));
        // 获取分组后的商品明细
        List<OrderAutoSplitByGoodsUtil.OrderItemGroup> allItem = orderAutoSplitByGoodsUtil.getGoodGroupList(relation.getOrderItemList());
        log.info(LogUtil.format("获取分组后的商品明细{}"), relation);
        //所有赠品集合
        List<OcBOrderItem> giftItemList = relation.getOrderItemList().stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() == 1).collect(Collectors.toList());
        log.info(LogUtil.format("所有赠品集合{}"), relation);
        Iterator<OrderAutoSplitByGoodsUtil.OrderItemGroup> it = allItem.iterator();
        //分组求和，来判断是否是多品类的
        Map<Long, Integer> mapItem = allItem.stream()
                .filter(v -> v.getPsCProdimId()!=null)
                .sorted(Comparator.comparing(OrderAutoSplitByGoodsUtil.OrderItemGroup::getPsCProdimId))
                .collect(Collectors.groupingBy(
                        OrderAutoSplitByGoodsUtil.OrderItemGroup::getPsCProdimId,
                        Collectors.reducing(
                                0,
                                OrderAutoSplitByGoodsUtil.OrderItemGroup::getNum,
                                Integer::sum)));
        //是否是单品类
        if (mapItem.size()>1){
            flag=true;
        }
        //定义一个map用来存每个品类差多少，还有金额信息以后计算
        Map<Long,OrderSplitUtill>  categoryMap = new HashMap<>();
        //生成订单组合
        List<Map<Long,Integer>> orderList = new ArrayList<>();
        //品类差多少
        Map<Long,Map<String,Object>> temporaryMap = new HashMap<>();
        //剩余没有策略的主表
        OcBOrderRelation otherRelationTemp = new OcBOrderRelation();
        //剩余没有策略的明细
        List<OcBOrderItem> otherItem =new ArrayList<>();
        //循环每个明细
        while (it.hasNext()) {
            OrderAutoSplitByGoodsUtil.OrderItemGroup group = it.next();
            //订单没有策略配置的品类直接跳出去
            Long prodimId =group.getPsCProdimId();
            // 将组合商品单独拆出来
            if (group.getIsCombinedOrGiftBag()) {
                OcBOrderRelation relationTemp = new OcBOrderRelation();
                relationTemp.setOrderItemList(group.getItems());
                // 构建订单头信息
                OcBOrder ocBOrder = new OcBOrder();
                BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
                ocBOrder.setIsSpiltSkuStyle(1);
                ocBOrder.setSplitReason(SplitReason.SPLIT_BY_GOODS_CLASS);
                relationTemp.setOrderInfo(ocBOrder);
                result.add(relationTemp);
                continue;
            }
            //当前明细的数量
            int itemHasNum = group.getNum().intValue();
            List<OcBOrderItem> items = group.getItems().stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() != 1).collect(Collectors.toList());
            Long itemId = items.get(0).getId();
            //没有策略的明细单独一单
            if (goodClassMap.get(prodimId) == null) {
                otherItem.add(group.getItems().get(0));
                otherRelationTemp.setOrderItemList(otherItem);
                continue;
            }
            //算出每一类商品的单价
            BigDecimal goodsQty = items.get(0).getQty();
            BigDecimal orderSplitAmt = Optional.ofNullable(items.get(0).getOrderSplitAmt()).orElse(BigDecimal.ZERO);
            BigDecimal amtDiscount = Optional.ofNullable(items.get(0).getAmtDiscount()).orElse(BigDecimal.ZERO);
            BigDecimal adjustAmt = Optional.ofNullable(items.get(0).getAdjustAmt()).orElse(BigDecimal.ZERO);
            BigDecimal realAmt = Optional.ofNullable(items.get(0).getRealAmt()).orElse(BigDecimal.ZERO);
            BigDecimal totPriceSettle = Optional.ofNullable(items.get(0).getTotPriceSettle()).orElse(BigDecimal.ZERO);
            //算出当前品类的价格的单价
            OrderSplitUtill orderSplitUtill = new OrderSplitUtill();
            orderSplitUtill.setOrderSplitAmt(orderSplitAmt.divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP));
            orderSplitUtill.setAmtDiscount(amtDiscount.divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP));
            orderSplitUtill.setAdjustAmt(adjustAmt.divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP));
            orderSplitUtill.setRealAmt(realAmt.divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP));
            orderSplitUtill.setTotPriceSettle(totPriceSettle.divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP));
            orderSplitUtill.setOcBOrderItem(items.get(0));
            //把明细id和商品信息加进去
            categoryMap.put(itemId,orderSplitUtill);
            //判断上个明细是否有明细有没有之前需要补充给的 有的话两种情况 自己补上够用，自己补上也不够（单品类）
            Map<String,Object> map=temporaryMap.get(prodimId);
            if (map!=null && map.get("count")!=null){
                int qty = Integer.valueOf(map.get("count").toString());
                //满足组个缺的数量
                if (qty!=0&&qty<=itemHasNum){
                    Map<Long,Integer> mapList= (Map<Long, Integer>) map.get("list");
                    //加入计算对象
                    mapList.put(itemId,qty);
                    Map<Long,Integer> mapAll = new HashMap<>();
                    mapAll.putAll(mapList);
                    orderList.add(mapList);
                    Map<String,Object>  prodimKeyLackMap= new HashMap<>();
                    prodimKeyLackMap.put("list",new HashMap<Long,Integer>());
                    prodimKeyLackMap.put("count",0);
                    prodimKeyLackMap.put("flag",true);
                    temporaryMap.put(prodimId,prodimKeyLackMap);
                    //补充后剩余多少继续走
                    itemHasNum = itemHasNum - qty;

                }else {  //补上去还是不满足,更新订单明细 缺货和 明细
                    Map<Long,Integer> mapList= (Map<Long, Integer>) map.get("list");
                    //加入计算对象
                    mapList.put(itemId,itemHasNum);
                    Map<Long,Integer> mapAll = new HashMap<>();
                    mapAll.putAll(mapList);
                    Map<String,Object>  prodimKeyLackMap= new HashMap<>();
                    prodimKeyLackMap.put("list",mapAll);
                    prodimKeyLackMap.put("count",qty-itemHasNum);
                    prodimKeyLackMap.put("flag",true);
                    temporaryMap.put(prodimId,prodimKeyLackMap);
                    itemHasNum=itemHasNum-qty;
                }
            }
            while (itemHasNum > 0) {
                int goodClassNum = goodClassMap.get(prodimId).intValue();
                if (itemHasNum >= goodClassNum) {
                    Map<Long,Integer> mapList =new HashMap<>();
                    mapList.put(itemId,goodClassNum);
                    orderList.add(mapList);
                    itemHasNum = itemHasNum - goodClassNum;
                } else {
                    Map<Long,Integer>  itemAndQtyMap=new HashMap<>();
                    itemAndQtyMap.put(itemId,itemHasNum);
                    Map<String,Object>  prodimKeyMap= new HashMap<>();
                    prodimKeyMap.put("list",itemAndQtyMap);
                    prodimKeyMap.put("count",goodClassNum-itemHasNum);
                    prodimKeyMap.put("flag",true);
                    temporaryMap.put(prodimId,prodimKeyMap);
                    itemHasNum = itemHasNum - goodClassNum;
                }
            }
        }
        log.info(LogUtil.format("通过品类分组数据为List{}，是否是多品类拆单{}，剩余的数据为{}") ,orderList,flag,temporaryMap);
        //剩余的没补充完的订单 单品类直接累加多品类，重新计算
        if(!flag) {
            if (temporaryMap.size()>0){
                for (Long aLong : temporaryMap.keySet()) {
                    Map<String, Object> prodimKeyMap = temporaryMap.get(aLong);
                    Map<Long, Integer> mapList = (Map<Long, Integer>) prodimKeyMap.get("list");
                    Map<Long, Integer> newMapList = new HashMap<>();
                    newMapList.putAll(mapList);
                    orderList.add(newMapList);
                }
            }
        }else {//剩余多品类计算
            if (temporaryMap.size()>0){
            for (Long akey : temporaryMap.keySet()) {
                Map<String, Object> aprodimKeyMap = temporaryMap.get(akey);
                    Boolean aFlag = (Boolean) aprodimKeyMap.get("flag");
                    if (!aFlag){
                        continue;
                    }
                    Map<Long, Integer> aMapList = (Map<Long, Integer>) aprodimKeyMap.get("list");
                    //当前品类最多容纳数
                    Integer aOtherNax = otherGoodClassMap.get(akey);
                    //循环所以是否冲突
                    Boolean mutexFlag = false;
                    //最大容纳数等于0的自己分出去
                    if (aOtherNax==0){
                        Map<Long, Integer> newMapList = new HashMap<>();
                        newMapList.putAll(aMapList);
                        orderList.add(newMapList);
                        //吧这个元素设置为false 不参与匹配
                        Map<String,Object>  prodimKeyLackMap= new HashMap<>();
                        prodimKeyLackMap.put("list",new HashMap<Long,Integer>());
                        prodimKeyLackMap.put("count",0);
                        prodimKeyLackMap.put("flag",false);
                        temporaryMap.put(akey,prodimKeyLackMap);
                        continue;
                    }
                    Integer aNum = 0;
                    //根据品类分剩余的只能是一组
                    for (Long aLong : aMapList.keySet()) {
                        aNum =aMapList.get(aLong);
                    }
                    for (Long bkey : temporaryMap.keySet()) {
                        Boolean bFlag = (Boolean) aprodimKeyMap.get("flag");
                        if (!bFlag){
                            continue;
                        }
                        //跳过自己
                        if (akey.equals(bkey)) {
                            continue;
                        }
                        Map<String, Object> bProdimKeyMap = temporaryMap.get(bkey);
                        //当前品类最多容纳数
                        Integer bOtherMax = otherGoodClassMap.get(bkey);
                        if (bOtherMax==0){
                            continue;
                        }
                        Map<Long, Integer> bMapList = (Map<Long, Integer>) bProdimKeyMap.get("list");
                        Integer bNum = 0;
                        //根据品类分剩余的只能是一组
                        for (Long bLong : bMapList.keySet()) {
                            bNum =bMapList.get(bLong);
                        }
                        //品类两两循环校验是否冲突 返回false 说明不冲突 两个组合
                        if (!checkMutex(aNum,aOtherNax,bNum,bOtherMax)){
                            Map<Long, Integer> newMapList = new HashMap<>();
                            newMapList.putAll(aMapList);
                            newMapList.putAll(bMapList);
                            orderList.add(newMapList);
                            Map<String,Object>  prodimKeyLackMap= new HashMap<>();
                            prodimKeyLackMap.put("list",new HashMap<Long,Integer>());
                            prodimKeyLackMap.put("count",0);
                            prodimKeyLackMap.put("flag",false);
                            temporaryMap.put(akey,prodimKeyLackMap);
                            temporaryMap.put(bkey,prodimKeyLackMap);
                            mutexFlag = true;
                            break;
                        }
                    }
                    //循环所以还没有找到，自己单个走出去
                    if (!mutexFlag){
                        Map<Long, Integer> newMapList = new HashMap<>();
                        newMapList.putAll(aMapList);
                        orderList.add(newMapList);
                        Map<String,Object>  prodimKeyLackMap= new HashMap<>();
                        prodimKeyLackMap.put("list",new HashMap<Long,Integer>());
                        prodimKeyLackMap.put("count",0);
                        prodimKeyLackMap.put("flag",false);
                        temporaryMap.put(akey,prodimKeyLackMap);
                        continue;
                    }
            }
            }
        }
        //获取所以挂靠赠品
        List<OcBOrderItem> isHangGiftList = this.getGiftList(giftItemList,true);
        log.info(LogUtil.format("获取所以挂靠赠品{}") ,isHangGiftList);
        //给明细赋值计算金额，挂靠关系明细字段挂靠
        for (Map<Long, Integer> map : orderList) {
            OcBOrderRelation relationTemp = new OcBOrderRelation();
            List<OcBOrderItem> itemList = new ArrayList<>();
            for (Long itemId : map.keySet()) {
                BigDecimal qty = BigDecimal.valueOf(map.get(itemId));
                OrderSplitUtill orderSplitUtill =categoryMap.get(itemId);
                OcBOrderItem item = orderSplitUtill.getOcBOrderItem();
                orderSplitUtill.setQty(qty);
                //根据数量、单价算出对应的价格
                OcBOrderItem ocBOrderItemCopy =generateItem(item,orderSplitUtill);
                itemList.add(ocBOrderItemCopy);
                //加入赠品
                if (CollectionUtils.isNotEmpty(isHangGiftList)){
                    //判断有没有挂靠关系的赠品
                    String skuCode = item.getPsCSkuEcode();
                    List<OcBOrderItem> ZPItemList= judgeIsGifts(skuCode,isHangGiftList);
                    if (CollectionUtils.isNotEmpty(ZPItemList)){
                        itemList.addAll(ZPItemList);
                        //移除已经挂靠的赠品
                        isHangGiftList.removeAll(ZPItemList);
                    }
                }
                relationTemp.setOrderItemList(itemList);
            }
            // 构建订单头信息
            OcBOrder ocBOrder = new OcBOrder();
            BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
            ocBOrder.setSplitReason(SplitReason.SPLIT_BY_GOODS_CLASS);
            ocBOrder.setIsSpiltSkuStyle(1);
            relationTemp.setOrderInfo(ocBOrder);
            result.add(relationTemp);
        }
        //不符合策略的订单单独拆一单
        log.info(LogUtil.format("处理不符合策略的订单{}") ,otherItem);
        if (CollectionUtils.isNotEmpty(otherItem)) {
            OcBOrderRelation relationTemp = new OcBOrderRelation();
            // 构建订单头信息
            OcBOrder ocBOrder = new OcBOrder();
            BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
            ocBOrder.setSplitReason(SplitReason.SPLIT_BY_GOODS_CLASS);
            relationTemp.setOrderInfo(ocBOrder);
            relationTemp.setOrderItemList(new ArrayList<>());
            relationTemp.getOrderItemList().addAll(otherItem);
            result.add(relationTemp);
        }
        if (CollectionUtils.isEmpty(result) || result.size() == 1) {
            return Lists.newArrayList();
        }
        // 处理非挂靠赠品。
        List<OcBOrderItem> isNotgiftItems = this.getGiftList(giftItemList,false);
        if (CollectionUtils.isNotEmpty(isNotgiftItems)) {
            result.get(result.size() - 1).getOrderItemList().addAll(isNotgiftItems);
        }
        return result;
    }
    //获取当前订单的挂靠赠品
    public List<OcBOrderItem> judgeIsGifts(String skuCode, List<OcBOrderItem> isHangGiftList) {
        List<OcBOrderItem> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(isHangGiftList)) {
            return null;
        }else {
            for (OcBOrderItem ocBOrderItem : isHangGiftList) {
                //判断挂靠关系
                if (skuCode.equals(ocBOrderItem.getGiftRelation())){
                    list.add(ocBOrderItem);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(list)){
            return list;
        }
        return null;
    }
    /**
     * <AUTHOR>
     * @Date 11:19 2021/5/17
     * @Description  赠品中的挂靠赠品、费挂靠赠品
     */
    public List<OcBOrderItem> getGiftList(List<OcBOrderItem> giftItemList,boolean flag) {
        List<OcBOrderItem> list =new ArrayList<>();
        if (flag){
            //挂靠关系赠品
            List<OcBOrderItem> isHangGiftList = giftItemList.stream()
                    .filter(it -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift())
                            && StringUtils.isNotEmpty(it.getGiftRelation()))
                    .collect(Collectors.toList());
            list.addAll(isHangGiftList);
        }else {
            //非挂靠关系赠品
            List<OcBOrderItem> isNotHangGiftList = giftItemList.stream()
                    .filter(it -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift())
                            && StringUtils.isEmpty(it.getGiftRelation()))
                    .collect(Collectors.toList());
            list.addAll(isNotHangGiftList);

        }
        return list;
    }
    //校验品类是否互斥
    private boolean checkMutex(Integer aNum, Integer aOtherNax, Integer bNum, Integer bOtherMax) {
        if (aNum<=bOtherMax && bNum<=aOtherNax){
            return false;
        }
        return true;
    }
    /**
     * <AUTHOR>
     * @Date 14:44 2021/5/13
     * @Description 通过数量构建订单价格信息
     */
    private OcBOrderItem generateItem(OcBOrderItem item, OrderSplitUtill orderSplitUtill) {
        OcBOrderItem itemCopy = new OcBOrderItem();
        BeanUtils.copyProperties(item,itemCopy);
        BigDecimal qty = orderSplitUtill.getQty();
        itemCopy.setQty(qty);
        itemCopy.setOrderSplitAmt(orderSplitUtill.getOrderSplitAmt().multiply(qty));
        itemCopy.setAmtDiscount(orderSplitUtill.getAmtDiscount().multiply(qty));
        itemCopy.setAdjustAmt(orderSplitUtill.getAdjustAmt().multiply(qty));
        itemCopy.setRealAmt(orderSplitUtill.getRealAmt().multiply(qty));
        itemCopy.setTotPriceSettle(orderSplitUtill.getTotPriceSettle().multiply(qty));
        return itemCopy;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        OrderSplitStrategyFactory.register(OrderSplitStrategyEnum.COMMODITY.getCode(), this);
    }

    /***
     * 按照性别拆分
     * @param relation
     * @param strategy
     * @return
     */
    private List<OcBOrderRelation> splitBySex(OcBOrderRelation relation, StCWarehouseQueryResult strategy) {
        List<OcBOrderRelation> result = new ArrayList<>();
        StCWarehouseDO stCWarehouseDO = strategy.getStCWarehouse();
        if (!"Y".equals(stCWarehouseDO.getIsSexSplit())) {
            return Lists.newArrayList();
        }

        // 获取分组后的商品明细
        List<OrderAutoSplitByGoodsUtil.OrderItemGroup> allGoods = orderAutoSplitByGoodsUtil.getGoodGroupList(relation.getOrderItemList());
        Map<Long, List<OrderAutoSplitByGoodsUtil.OrderItemGroup>> sexMap = allGoods.stream().filter(it -> it.getSex() != null).collect(Collectors.groupingBy(OrderAutoSplitByGoodsUtil.OrderItemGroup::getSex));
        List<OrderAutoSplitByGoodsUtil.OrderItemGroup> list = allGoods.stream().filter(it -> it.getSex() == null).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            sexMap.put(null, list);
        }
        // 明细为同一性别的商品不拆分
        if (sexMap.size() == 1) {
            return Lists.newArrayList();
        }
        Iterator<Long> it = sexMap.keySet().iterator();
        while (it.hasNext()) {
            Long key = it.next();
            OcBOrderRelation temp = new OcBOrderRelation();
            List<OcBOrderItem> items = new ArrayList<>();
            OcBOrder ocBOrder = new OcBOrder();
            BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
            ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SEX);
            temp.setOrderInfo(ocBOrder);
            temp.setOrderItemList(items);
            for (OrderAutoSplitByGoodsUtil.OrderItemGroup orderItemGroup : sexMap.get(key)) {
                temp.getOrderItemList().addAll(orderItemGroup.getItems());
            }

            result.add(temp);
        }
        // 处理非挂靠赠品。
        List<OcBOrderItem> giftItems = orderAutoSplitByGoodsUtil.getGiftList(relation.getOrderItemList());
        if (CollectionUtils.isNotEmpty(giftItems)) {
            result.get(result.size() - 1).getOrderItemList().addAll(giftItems);
        }
        return result;
    }

    private List<OcBOrderRelation> splitByBrandGroup(OcBOrderRelation relation, StCWarehouseQueryResult strategy) {
        List<OcBOrderRelation> resultList = new ArrayList<>();
        List<StCWarehouseBrandDO> stCWarehouseBrandList = strategy.getStCWarehouseBrandList();
        Map<String, List<Long>> brandGroupMap = new HashMap<>();
        for (StCWarehouseBrandDO brandGroup : stCWarehouseBrandList) {
            List<Long> brandList = new ArrayList<>();
            String[] brands = brandGroup.getBrandId().split(",");
            for (String s : brands) {
                brandList.add(Long.valueOf(s));
            }
            brandGroupMap.put(brandGroup.getBrandGroup(), brandList);
        }
        // 获取分组后的商品明细
        List<OrderAutoSplitByGoodsUtil.OrderItemGroup> allGoods = orderAutoSplitByGoodsUtil.getGoodGroupList(relation.getOrderItemList());

        Iterator<OrderAutoSplitByGoodsUtil.OrderItemGroup> it = allGoods.iterator();
        Map<String, List<OrderAutoSplitByGoodsUtil.OrderItemGroup>> stringListMap = new HashMap<>();
        Iterator<String> iterator = brandGroupMap.keySet().iterator();
        while (it.hasNext()) {
            OrderAutoSplitByGoodsUtil.OrderItemGroup item = it.next();
            while (iterator.hasNext()) {
                String key = iterator.next();
                if (brandGroupMap.get(key).contains(item.getPsCBrandId())) {
                    if (CollectionUtils.isEmpty(stringListMap.get(key))) {
                        List<OrderAutoSplitByGoodsUtil.OrderItemGroup> items = new ArrayList<>();
                        items.add(item);
                        stringListMap.put(key, items);
                    } else {
                        stringListMap.get(key).add(item);
                    }
                    it.remove();
                    break;
                }
            }
        }
        if (stringListMap.isEmpty()) {
            return Lists.newArrayList();
        }
        Iterator<String> stringIterator = stringListMap.keySet().iterator();
        while (stringIterator.hasNext()) {
            String key = stringIterator.next();
            OcBOrderRelation relationTemp = new OcBOrderRelation();
            OcBOrder ocBOrder = new OcBOrder();
            BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
            ocBOrder.setSplitReason(SplitReason.SPLIT_BY_BRAND_GROUP);
            relationTemp.setOrderInfo(ocBOrder);
            for (OrderAutoSplitByGoodsUtil.OrderItemGroup itemGroup : stringListMap.get(key)) {
                if (CollectionUtils.isEmpty(relationTemp.getOrderItemList())) {
                    relationTemp.setOrderItemList(new ArrayList<>());
                }
                relationTemp.getOrderItemList().addAll(itemGroup.getItems());
            }
            resultList.add(relationTemp);
        }
        if (CollectionUtils.isNotEmpty(allGoods)) {
            OcBOrderRelation relationTemp = new OcBOrderRelation();
            for (OrderAutoSplitByGoodsUtil.OrderItemGroup itemGroup : allGoods) {
                OcBOrder ocBOrder = new OcBOrder();
                BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
                ocBOrder.setSplitReason(SplitReason.SPLIT_BY_BRAND_GROUP);
                relationTemp.setOrderInfo(ocBOrder);
                if (CollectionUtils.isEmpty(relationTemp.getOrderItemList())) {
                    relationTemp.setOrderItemList(new ArrayList<>());
                }
                relationTemp.getOrderItemList().addAll(itemGroup.getItems());
            }
            resultList.add(relationTemp);
        }
        if (resultList.size() == 1) {
            return Lists.newArrayList();
        }
        // 处理非挂靠赠品。
        List<OcBOrderItem> giftItems = orderAutoSplitByGoodsUtil.getGiftList(relation.getOrderItemList());
        if (CollectionUtils.isNotEmpty(giftItems)) {
            resultList.get(resultList.size() - 1).getOrderItemList().addAll(giftItems);
        }
        return resultList;
    }

    /***
     * 根据商品款号进行拆分
     * @param relation
     * @param strategy
     * @return
     */
    private List<OcBOrderRelation> splitByGoodsSpu(OcBOrderRelation relation, StCWarehouseQueryResult strategy) {
        List<OcBOrderRelation> result = new ArrayList<>();
        List<StCWarehouseGoodsDO> stCWarehouseGoodsList = strategy.getStCWarehouseGoodsList();
        // 策略中配置的SPU
        Map<String, Integer> spuMap =
                stCWarehouseGoodsList.stream().collect(Collectors.toMap(StCWarehouseGoodsDO::getPsCProEcode,
                        StCWarehouseGoodsDO::getNum, (v1, v2) -> v1));
        // 获取分组后的商品明细
        List<OrderAutoSplitByGoodsUtil.OrderItemGroup> allGoods =
                orderAutoSplitByGoodsUtil.getGoodGroupList(relation.getOrderItemList());
        Iterator<OrderAutoSplitByGoodsUtil.OrderItemGroup> it = allGoods.iterator();
        while (it.hasNext()) {
            OrderAutoSplitByGoodsUtil.OrderItemGroup group = it.next();
            if (spuMap.get(group.getPsCProEcode()) == null) {
                continue;
            }
            // 目前组合商品不能按数量拆单
            if (group.getIsCombinedOrGiftBag()) {
                // 将组合商品单独拆出来
                OcBOrderRelation relationTemp = new OcBOrderRelation();
                relationTemp.setOrderItemList(group.getItems());
                // 构建订单头信息
                OcBOrder ocBOrder = new OcBOrder();
                BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
                ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SPU);
                relationTemp.setOrderInfo(ocBOrder);
                result.add(relationTemp);
                it.remove();
                continue;
            }
            int itemHasNum = group.getNum().intValue();
            List<OcBOrderItem> items = group.getItems().stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() != 1).collect(Collectors.toList());
            List<OcBOrderItem> giftRelationItems = group.getItems().stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() == 1).collect(Collectors.toList());
            BigDecimal goodsQty = items.get(0).getQty();
            BigDecimal orderSplitAmt = Optional.ofNullable(items.get(0).getOrderSplitAmt()).orElse(BigDecimal.ZERO);
            BigDecimal endOrderSplitAmt = Optional.ofNullable(items.get(0).getOrderSplitAmt()).orElse(BigDecimal.ZERO);
            BigDecimal amtDiscount = Optional.ofNullable(items.get(0).getAmtDiscount()).orElse(BigDecimal.ZERO);
            BigDecimal endAmtDiscount = Optional.ofNullable(items.get(0).getAmtDiscount()).orElse(BigDecimal.ZERO);
            BigDecimal adjustAmt = Optional.ofNullable(items.get(0).getAdjustAmt()).orElse(BigDecimal.ZERO);
            BigDecimal endAdjustAmt = Optional.ofNullable(items.get(0).getAdjustAmt()).orElse(BigDecimal.ZERO);
            BigDecimal realAmt = Optional.ofNullable(items.get(0).getRealAmt()).orElse(BigDecimal.ZERO);
            BigDecimal endRealAmt = Optional.ofNullable(items.get(0).getRealAmt()).orElse(BigDecimal.ZERO);
            BigDecimal totPriceSettle = Optional.ofNullable(items.get(0).getTotPriceSettle()).orElse(BigDecimal.ZERO);
            BigDecimal endTotPriceSettle = Optional.ofNullable(items.get(0).getTotPriceSettle()).orElse(BigDecimal.ZERO);
            while (itemHasNum > 0) {
                OcBOrderRelation relationTemp = new OcBOrderRelation();
                int psCProEcodeNum = spuMap.get(group.getPsCProEcode()).intValue();
                if (itemHasNum > psCProEcodeNum) {
                    BigDecimal qty = BigDecimal.valueOf(spuMap.get(group.getPsCProEcode()));
                    List<OcBOrderItem> itemList = new ArrayList<>();
                    OcBOrderItem item = new OcBOrderItem();
                    BeanUtils.copyProperties(items.get(0), item);
                    item.setQty(qty);
                    BigDecimal tempOrderSplitAmt = orderSplitAmt.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setOrderSplitAmt(tempOrderSplitAmt);
                    BigDecimal tempAmtDiscount = amtDiscount.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setAmtDiscount(tempAmtDiscount);
                    BigDecimal tempAdjustAmt = adjustAmt.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setAdjustAmt(tempAdjustAmt);
                    BigDecimal tempRealAmt = realAmt.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setRealAmt(tempRealAmt);
                    BigDecimal tempEndTotPriceSettle = totPriceSettle.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setTotPriceSettle(totPriceSettle);
                    itemList.add(item);
                    relationTemp.setOrderItemList(itemList);
                    // 构建订单头信息
                    OcBOrder ocBOrder = new OcBOrder();
                    BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
                    ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SPU);
                    ocBOrder.setIsSpiltSkuStyle(1);
                    relationTemp.setOrderInfo(ocBOrder);
                    result.add(relationTemp);
                    itemHasNum = itemHasNum - psCProEcodeNum;
                    items.get(0).setQty(BigDecimal.valueOf(itemHasNum));
                    group.setNum(itemHasNum);
                    endOrderSplitAmt = endOrderSplitAmt.subtract(tempOrderSplitAmt);
                    endAmtDiscount = endAmtDiscount.subtract(tempAmtDiscount);
                    endAdjustAmt = endAdjustAmt.subtract(tempAdjustAmt);
                    endRealAmt = endRealAmt.subtract(tempRealAmt);
                    endTotPriceSettle = endTotPriceSettle.subtract(tempEndTotPriceSettle);
                } else {
                    OcBOrderItem item = items.get(0);
                    item.setQty(BigDecimal.valueOf(itemHasNum));
                    item.setOrderSplitAmt(endOrderSplitAmt);
                    item.setAmtDiscount(endAmtDiscount);
                    item.setAdjustAmt(endAdjustAmt);
                    item.setRealAmt(endRealAmt);
                    item.setTotPriceSettle(endTotPriceSettle);
                    relationTemp.setOrderItemList(items);
                    if (CollectionUtils.isNotEmpty(giftRelationItems)) {
                        relationTemp.getOrderItemList().addAll(giftRelationItems);
                    }
                    // 构建订单头信息
                    OcBOrder ocBOrder = new OcBOrder();
                    BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
                    ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SPU);
                    ocBOrder.setIsSpiltSkuStyle(1);
                    relationTemp.setOrderInfo(ocBOrder);
                    result.add(relationTemp);
                    itemHasNum = itemHasNum - psCProEcodeNum;
                    it.remove();
                }
            }
        }
        if (!allGoods.isEmpty()) {
            OcBOrderRelation relationTemp = new OcBOrderRelation();
            // 构建订单头信息
            OcBOrder ocBOrder = new OcBOrder();
            BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
            ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SPU);
            relationTemp.setOrderInfo(ocBOrder);
            relationTemp.setOrderItemList(new ArrayList<>());
            for (OrderAutoSplitByGoodsUtil.OrderItemGroup itemGroup : allGoods) {
                relationTemp.getOrderItemList().addAll(itemGroup.getItems());
            }
            result.add(relationTemp);
        }
        if (CollectionUtils.isEmpty(result) || result.size() == 1) {
            return Lists.newArrayList();
        }
        // 处理非挂靠赠品。
        List<OcBOrderItem> giftItems = orderAutoSplitByGoodsUtil.getGiftList(relation.getOrderItemList());
        if(CollectionUtils.isNotEmpty(giftItems)){
            result.get(result.size()-1).getOrderItemList().addAll(giftItems);
        }
        return result;
    }

    /***
     * 根据商品SKU进行拆分
     * @param relation
     * @param strategy
     * @return
     */
    private List<OcBOrderRelation> splitByGoodsSku(OcBOrderRelation relation, StCWarehouseQueryResult strategy) {
        List<OcBOrderRelation> result = new ArrayList<>();
        List<StCWarehouseSkuDO> stCWarehouseSkuList = strategy.getStCWarehouseSkuList();
        // 策略中配置的SKU
        Map<String, Integer> skuMap =
                stCWarehouseSkuList.stream().collect(Collectors.toMap(StCWarehouseSkuDO::getPsCSkuEcode,
                        StCWarehouseSkuDO::getNum, (v1, v2) -> v1));
        // 获取分组后的商品明细
        List<OrderAutoSplitByGoodsUtil.OrderItemGroup> allGoods = orderAutoSplitByGoodsUtil.getGoodGroupList(relation.getOrderItemList());
        Iterator<OrderAutoSplitByGoodsUtil.OrderItemGroup> it = allGoods.iterator();
        while (it.hasNext()) {
            OrderAutoSplitByGoodsUtil.OrderItemGroup group = it.next();
            if (skuMap.get(group.getPsCSkuEcode()) == null) {
                continue;
            }
            if (group.getIsCombinedOrGiftBag()) {
                // 将组合商品单独拆出来
                OcBOrderRelation relationTemp = new OcBOrderRelation();
                relationTemp.setOrderItemList(group.getItems());
                // 构建订单头信息
                OcBOrder ocBOrder = new OcBOrder();
                BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
                ocBOrder.setIsSpiltSkuStyle(1);
                ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SKU);
                relationTemp.setOrderInfo(ocBOrder);
                result.add(relationTemp);
                it.remove();
                continue;
            }
            int itemHasNum = group.getNum().intValue();
            List<OcBOrderItem> items = group.getItems().stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() != 1).collect(Collectors.toList());
            List<OcBOrderItem> giftRelationItems = group.getItems().stream().filter(v -> Optional.ofNullable(v.getIsGift()).orElse(0).intValue() == 1).collect(Collectors.toList());
            BigDecimal goodsQty = items.get(0).getQty();
            BigDecimal orderSplitAmt = Optional.ofNullable(items.get(0).getOrderSplitAmt()).orElse(BigDecimal.ZERO);
            BigDecimal endOrderSplitAmt = Optional.ofNullable(items.get(0).getOrderSplitAmt()).orElse(BigDecimal.ZERO);
            BigDecimal amtDiscount = Optional.ofNullable(items.get(0).getAmtDiscount()).orElse(BigDecimal.ZERO);
            BigDecimal endAmtDiscount = Optional.ofNullable(items.get(0).getAmtDiscount()).orElse(BigDecimal.ZERO);
            BigDecimal adjustAmt = Optional.ofNullable(items.get(0).getAdjustAmt()).orElse(BigDecimal.ZERO);
            BigDecimal endAdjustAmt = Optional.ofNullable(items.get(0).getAdjustAmt()).orElse(BigDecimal.ZERO);
            BigDecimal realAmt = Optional.ofNullable(items.get(0).getRealAmt()).orElse(BigDecimal.ZERO);
            BigDecimal endRealAmt = Optional.ofNullable(items.get(0).getRealAmt()).orElse(BigDecimal.ZERO);
            BigDecimal totPriceSettle = Optional.ofNullable(items.get(0).getTotPriceSettle()).orElse(BigDecimal.ZERO);
            BigDecimal endTotPriceSettle = Optional.ofNullable(items.get(0).getTotPriceSettle()).orElse(BigDecimal.ZERO);
            while (itemHasNum > 0) {
                OcBOrderRelation relationTemp = new OcBOrderRelation();
                int psCSkuEcodeNum = skuMap.get(group.getPsCSkuEcode()).intValue();
                if (itemHasNum > psCSkuEcodeNum) {
                    BigDecimal qty = BigDecimal.valueOf(skuMap.get(group.getPsCSkuEcode()));
                    List<OcBOrderItem> itemList = new ArrayList<>();
                    OcBOrderItem item = new OcBOrderItem();
                    BeanUtils.copyProperties(items.get(0), item);
                    item.setQty(qty);
                    BigDecimal tempOrderSplitAmt = orderSplitAmt.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setOrderSplitAmt(tempOrderSplitAmt);
                    BigDecimal tempAmtDiscount = amtDiscount.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setAmtDiscount(tempAmtDiscount);
                    BigDecimal tempAdjustAmt = adjustAmt.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setAdjustAmt(tempAdjustAmt);
                    BigDecimal tempRealAmt = realAmt.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setRealAmt(tempRealAmt);
                    BigDecimal tempEndTotPriceSettle = totPriceSettle.multiply(qty).divide(goodsQty, 4, BigDecimal.ROUND_HALF_UP);
                    item.setTotPriceSettle(totPriceSettle);
                    itemList.add(item);
                    relationTemp.setOrderItemList(itemList);
                    // 构建订单头信息
                    OcBOrder ocBOrder = new OcBOrder();
                    BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
                    ocBOrder.setIsSpiltSkuStyle(1);
                    ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SKU);
                    relationTemp.setOrderInfo(ocBOrder);

                    result.add(relationTemp);
                    itemHasNum = itemHasNum - psCSkuEcodeNum;
                    items.get(0).setQty(BigDecimal.valueOf(itemHasNum));
                    group.setNum(itemHasNum);
                    endOrderSplitAmt = endOrderSplitAmt.subtract(tempOrderSplitAmt);
                    endAmtDiscount = endAmtDiscount.subtract(tempAmtDiscount);
                    endAdjustAmt = endAdjustAmt.subtract(tempAdjustAmt);
                    endRealAmt = endRealAmt.subtract(tempRealAmt);
                    endTotPriceSettle = endTotPriceSettle.subtract(tempEndTotPriceSettle);
                } else {
                    OcBOrderItem item = items.get(0);
                    item.setQty(BigDecimal.valueOf(itemHasNum));
                    item.setOrderSplitAmt(endOrderSplitAmt);
                    item.setAmtDiscount(endAmtDiscount);
                    item.setAdjustAmt(endAdjustAmt);
                    item.setRealAmt(endRealAmt);
                    item.setTotPriceSettle(endTotPriceSettle);
                    relationTemp.setOrderItemList(items);
                    if (CollectionUtils.isNotEmpty(giftRelationItems)) {
                        relationTemp.getOrderItemList().addAll(giftRelationItems);
                    }
                    // 构建订单头信息
                    OcBOrder ocBOrder = new OcBOrder();
                    BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
                    ocBOrder.setIsSpiltSkuStyle(1);
                    ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SKU);
                    relationTemp.setOrderInfo(ocBOrder);
                    result.add(relationTemp);
                    itemHasNum = itemHasNum - psCSkuEcodeNum;
                    it.remove();
                }
            }
        }
        if (!allGoods.isEmpty()) {
            OcBOrderRelation relationTemp = new OcBOrderRelation();
            // 构建订单头信息
            OcBOrder ocBOrder = new OcBOrder();
            BeanUtils.copyProperties(relation.getOrderInfo(), ocBOrder);
            ocBOrder.setSplitReason(SplitReason.SPLIT_BY_SKU);
            relationTemp.setOrderInfo(ocBOrder);
            // 设置明细
            relationTemp.setOrderItemList(new ArrayList<>());
            for (OrderAutoSplitByGoodsUtil.OrderItemGroup itemGroup : allGoods) {
                relationTemp.getOrderItemList().addAll(itemGroup.getItems());
            }
            result.add(relationTemp);
        }
        if (CollectionUtils.isEmpty(result) || result.size() == 1) {
            return Lists.newArrayList();
        }
        // 处理非挂靠赠品。
        List<OcBOrderItem> giftItems = orderAutoSplitByGoodsUtil.getGiftList(relation.getOrderItemList());
        if (CollectionUtils.isNotEmpty(giftItems)) {
            result.get(result.size() - 1).getOrderItemList().addAll(giftItems);
        }
        return result;
    }
}
