package com.jackrain.nea.oc.oms.strategy;

import com.jackrain.nea.oc.oms.constant.OrderSplitStrategyEnum;
import com.jackrain.nea.oc.oms.factory.OrderSplitStrategyFactory;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.st.model.result.BaseResult;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 根据预售策略拆单（只针对店铺全款预售和自定义全款预售）
 * @author: 江家雷
 * @since: 2020/6/28
 * create at : 2020/6/28 16:17
 */
@Component
public class PresaleOrderSplitIml implements IOrderSplitStrategy {

    @Override
    public List<OcBOrderRelation> split(OcBOrderRelation ocBOrderRelation, User user, BaseResult strategy) {

        /*List<OcBOrderRelation> results = new ArrayList<>();

        if (ocBOrderRelation == null || CollectionUtils.isEmpty(ocBOrderRelation.getOrderItemList())) {
            results.add(ocBOrderRelation);
            return results;
        }
        // 1.判断订单是否包含预售SKU,不包含预售SKU则直接返回原单据
        List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
        List<OcBOrderItem> items = orderItemList.stream()
                .filter(item -> StConstants.ST_PRE_SALE_WAY_02.equals(item.getPresellType()) || StConstants.ST_PRE_SALE_WAY_03.equals(item.getPresellType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            results.add(ocBOrderRelation);
            return results;
        }

        // 2.根据商品行计划发货日期进行拆单, 普通商品行取支付日期作为计划发货日期
        Map<String, List<OcBOrderItem>> itemMap = new HashMap<>();
        for (OcBOrderItem item : orderItemList) {
            String key = item.getPlanOutDate() != null ? DateFormatUtil.formatDate(item.getPlanOutDate(), DateFormatUtil.YYYYMMDD) : DateFormatUtil.formatDate(ocBOrderRelation.getOrderInfo().getPayTime(), DateFormatUtil.YYYYMMDD);
            if (itemMap.get(key) == null) {
                List<OcBOrderItem> temp = new ArrayList<>();
                temp.add(item);
                itemMap.put(key, temp);
            } else {
                itemMap.get(key).add(item);
            }
        }


        // 构建拆单后的List
        for (String key : itemMap.keySet()) {
            OcBOrderRelation temp = new OcBOrderRelation();
            BeanUtils.copyProperties(ocBOrderRelation, temp);
            temp.setOrderItemList(itemMap.get(key));
            results.add(temp);
        }
        return results;*/
        return null;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        OrderSplitStrategyFactory.register(OrderSplitStrategyEnum.PRESALE.getCode(), this);
    }

}