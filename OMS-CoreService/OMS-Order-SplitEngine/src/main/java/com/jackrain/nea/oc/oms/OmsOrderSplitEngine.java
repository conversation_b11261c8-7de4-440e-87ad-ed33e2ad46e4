package com.jackrain.nea.oc.oms;

import com.jackrain.nea.oc.oms.factory.OrderSplitStrategyFactory;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.st.model.result.BaseResult;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: 策略引擎入口
 * @author: 江家雷
 * @since: 2020/6/28
 * create at : 2020/6/28 16:41
 */
@Component
public class OmsOrderSplitEngine {

    // 根据策略进行相应的拆单操作
    public List<OcBOrderRelation> startSplit(OcBOrderRelation ocBOrderRelation, User user, BaseResult strategy, String strategyCode) {
        return OrderSplitStrategyFactory.getOrderSplitStrategy(strategyCode).split(ocBOrderRelation, user, strategy);
    }
}