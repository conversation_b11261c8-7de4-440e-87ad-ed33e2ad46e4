package com.jackrain.nea.oc.oms.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @Description: 日期格式化 工具类
 * @author: 江家雷
 * @since: 2020/6/28
 * create at : 2020/6/28 17:38
 */
public class DateFormatUtil {

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDD = "yyyyMMdd";

    private DateFormatUtil() {
    }

    public static String formatDate(Date date, String pattern) {
        //默认时区
        try {
            ZoneId zoneId = ZoneId.systemDefault();
            LocalDateTime localDateTime = LocalDateTime.ofInstant(date.toInstant(), zoneId);
            return localDateTime.format(DateTimeFormatter.ofPattern(pattern));
        } catch (Exception e) {
            return "00:00:00 00:00:00";
        }
    }


}