package com.jackrain.nea.oc.oms.services.delivery;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Description:
 *
 * <AUTHOR> sunies
 * @since : 2020-11-12
 * create at : 2020-11-12 10:09
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class OrderDeliveryProcessorTest {
    @Resource
    private OrderDeliveryProcessor orderDeliveryProcessor;
    @Resource
    private OcBOrderMapper ocBOrderMapper;
    @Resource
    private OcBOrderItemMapper ocBOrderItemMapper;
    @MockBean
    private VipcomJitxWarehouseService vipcomJitxWarehouseService;
    @MockBean
    private CpRpcService cpRpcService;


    /**
     * 唯品会JITX平台发货单元测试
     */
    @Test
    public void platformSendOfJitx() {
        Long orderId = 608640L;
        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
        ocBOrderRelation.setOrderInfo(ocBOrderMapper.selectById(orderId));
        ocBOrderRelation.setOrderItemList(ocBOrderItemMapper.selectItemListOfUnshippedAndNonGift(orderId));
        StCVipcomJitxWarehouse stCVipcomJitxWarehouse = new StCVipcomJitxWarehouse();
        stCVipcomJitxWarehouse.setVipcomWarehouseEcode("001");
        when(vipcomJitxWarehouseService.queryJitxCapacity(anyLong(), anyLong(), anyString())).thenReturn(stCVipcomJitxWarehouse);
        CpShop cpShop = new CpShop();
        cpShop.setPlatformSupplierId("5400");
        when(cpRpcService.selectShopById(anyLong())).thenReturn(cpShop);
        orderDeliveryProcessor.platformSend(ocBOrderRelation);
    }

    /**
     * 淘宝平台发货单元测试
     * 正常订单，换货订单、正常订单的合单，换货订单的合单，正常与换货的合单、拆单、拆单与正常订单的合单，拆单与换货订单的合单
     */
    @Test
    public void platformSendOfTb() {
        Long orderId = 40L;
        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
        ocBOrderRelation.setOrderInfo(ocBOrderMapper.selectById(orderId));
        ocBOrderRelation.setOrderItemList(ocBOrderItemMapper.selectItemListOfUnshippedAndNonGift(orderId));
        CpShop cpShop = new CpShop();
        cpShop.setPlatformSupplierId("5400");
        when(cpRpcService.selectShopById(anyLong())).thenReturn(cpShop);
        when(cpRpcService.getPlatformLogisticEcode(anyLong(),anyLong())).thenReturn("SF");
        orderDeliveryProcessor.platformSend(ocBOrderRelation);
    }
}