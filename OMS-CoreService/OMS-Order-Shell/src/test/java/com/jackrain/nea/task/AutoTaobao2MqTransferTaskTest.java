package com.jackrain.nea.task;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.task.transfer.AutoTaobao2MqTransferTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Description： 定时任务
 * Author: RESET
 * Date: Created in 2020/9/1 9:54
 * Modified By:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class AutoTaobao2MqTransferTaskTest {

    @Autowired
    AutoTaobao2MqTransferTask transferTask;

    @Test
    public void testAutoTask() {
        transferTask.execute(null);
    }

}
