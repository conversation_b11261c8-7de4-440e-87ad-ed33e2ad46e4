package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Auther: 黄志优
 * @Date: 2020/11/15 18:37
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class AutoAuditSendMqTaskTest {
    @Test
    public void start() {
        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
        orderMqInfo.setOperateType(OperateType.AUDIT_ORDER);
        orderMqInfo.setOrderIds("");
        //mqProcessorFactory.startProcess();
    }
}
