package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.task.transfer.AutoJitxMqTransferTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @since: 2020/10/27
 * create at : 2020/10/27 11:10 上午
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class AutoJitxMqTransferTaskTest {

    @Autowired
    private AutoJitxMqTransferTask autoJitxMqTransferTask;

    @Test
    public void jitxMqTest() {
        autoJitxMqTransferTask.execute(null);
    }
}
