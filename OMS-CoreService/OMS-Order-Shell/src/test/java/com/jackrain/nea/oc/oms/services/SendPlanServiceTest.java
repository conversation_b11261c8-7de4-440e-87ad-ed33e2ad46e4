package com.jackrain.nea.oc.oms.services;

import com.burgeon.r3.sg.basic.model.result.SgSumStorageQueryResult;
import com.google.common.collect.Lists;
import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.model.SendPlanExecution;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-06-19
 * @desc
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
public class SendPlanServiceTest {

    @Autowired
    private SendPlanService sendPlanService;

    @Test
    public void test() {
        List<SgSumStorageQueryResult> storageQueryResultList = Lists.newArrayList();
        List<Long> warehouseIdList = Lists.newArrayList(1L, 2L, 11L, 12L);

        SendPlanExecution sendPlanExecution = new SendPlanExecution(
                123456789L, 1L,
                warehouseIdList, OrderType.TIMEORDER);
        sendPlanExecution.setCpCVipcomWahouseId(4L);
        Long warehouseId = sendPlanService.execute(sendPlanExecution, storageQueryResultList);

    }
}
