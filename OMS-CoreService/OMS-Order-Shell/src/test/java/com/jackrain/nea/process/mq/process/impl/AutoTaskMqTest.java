package com.jackrain.nea.process.mq.process.impl;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.mq.processor.impl.mq.AutoTaskMq;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Description： 自动定时任务测试类
 * Author: RESET
 * Date: Created in 2020/9/13 23:46
 * Modified By:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class AutoTaskMqTest {

    /**
     * 自动任务MQ测试
     */
    @Test
    public void testAutoTaskMq() {
        AutoTaskMq autoTaskMq = ApplicationContextHandle.getBean(AutoTaskMq.class);

        try {
            Message message = new Message();
            message.setTopic("BJ_DEV_R3_OC_OMS_CALL_AUTOTASK");
            message.setTag("tag_platform_delivery_send");
            message.setKey("AUTOTASK_TEST_KEY_001");
            message.setReconsumeTimes(6);
            message.setMsgID("AUTOTASK_TEST_MESSAGE_ID_001");

            try {
                List<Long> orderIds = new ArrayList<>();
                orderIds.add(341828L);
                orderIds.add(224L);

                message.setBody(MsgConvertUtil.objectSerialize(orderIds));
            } catch (IOException e) {
                e.printStackTrace();
            }

//            Action result = autoTaskMq.consume(message, null);
//            log.debug("testConsume.result:{}", result);
        } catch (Exception e) {
            log.error("testConsume.try.error", e);
        }

    }

}
