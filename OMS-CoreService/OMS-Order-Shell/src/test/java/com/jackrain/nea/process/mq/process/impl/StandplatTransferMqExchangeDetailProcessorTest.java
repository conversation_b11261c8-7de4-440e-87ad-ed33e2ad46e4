package com.jackrain.nea.process.mq.process.impl;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund.CurrencyTransferMqRefundDetailProcessor;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

/**
 * Description： 淘宝换货消息消费测试
 * Author: RESET
 * Date: Created in 2020/8/13 16:05
 * Modified By:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class StandplatTransferMqExchangeDetailProcessorTest {

    @Test
    @Transactional
    // @Rollback(false)
    public void testProcess() {
        CurrencyTransferMqRefundDetailProcessor process = new CurrencyTransferMqRefundDetailProcessor();
        // 自动注入
        ApplicationContextHandle.getApplicationContext().getAutowireCapableBeanFactory().autowireBean(process);

        OperateOrderMqInfo info = new OperateOrderMqInfo();
        // info.setOrderId(531);
        info.setOrderNo("7689324352");

        process.start(info);
    }

}
