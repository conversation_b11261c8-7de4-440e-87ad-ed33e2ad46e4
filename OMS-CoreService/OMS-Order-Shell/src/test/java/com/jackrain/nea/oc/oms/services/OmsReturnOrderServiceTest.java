package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.model.request.TimeOrderVoidSgSendRequest;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * Description： 查询测试
 * Author: RESET
 * Date: Created in 2020/7/22 16:58
 * Modified By:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class OmsReturnOrderServiceTest {

    @Autowired
    OmsReturnOrderService returnOrderService;

    @Autowired
    TimeOrderVoidSgSendService timeOrderVoidSgSendService;

    /**
     * 测试单据编号查询
     */
    @Test
    public void testSelectOrderByBillNo() {
        String billNo = "TH20062800000010";
        OcBReturnOrder order = returnOrderService.selectReturnOrderByBillNo(billNo);
        log.info("result:{}", JSON.toJSONString(order));
    }


    @Test
    public void test1() {
        User user = getRootUser();
        TimeOrderVoidSgSendRequest request = new TimeOrderVoidSgSendRequest();
        request.setUser(user);
        request.setPickNo("PICK-2102427077-202");
        timeOrderVoidSgSendService.voidSgSendV14(request);
    }


    public static UserImpl getRootUser() {

        final int id = 893;
        final int cId = 37;
        final int oId = 27;
        final int iAd = 1;
        final int iDev = 2;

        UserImpl users = new UserImpl();
        users.setId(id);
        users.setName("admin");
        users.setEname("Pokemon-mapper");
        users.setActive(true);
        users.setClientId(cId);
        users.setOrgId(oId);
        users.setIsAdmin(iAd);
        users.setIsDev(iDev);
        return users;
    }


}
