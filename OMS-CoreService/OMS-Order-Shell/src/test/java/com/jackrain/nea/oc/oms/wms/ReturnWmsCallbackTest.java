package com.jackrain.nea.oc.oms.wms;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.services.RefundOrderToWmsBackService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date ：Created in 10:17 2020/4/1
 * description ：
 * @ Modified By：
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
public class ReturnWmsCallbackTest {

    @Autowired
    private RefundOrderToWmsBackService refundOrderToWmsBackService;

    @Test
    public void test01() {
        String message = "{\n" +
                "\t\"request\": {\n" +
                "\t\t\"orderLines\": [{\n" +
                "\t\t\t\"orderLine\": {\n" +
                "\t\t\t\t\"itemId\": \"1743704586\",\n" +
                "\t\t\t\t\"produceCode\": \"-\",\n" +
                "\t\t\t\t\"orderLineNo\": \"null\",\n" +
                "\t\t\t\t\"inventoryType\": \"CC\",\n" +
                "\t\t\t\t\"itemCode\": \"202003240004\",\n" +
                "\t\t\t\t\"batchCode\": \"520000002127856\",\n" +
                "\t\t\t\t\"actualQty\": \"1\"\n" +
                "\t\t\t}\n" +
                "\t\t},{\n" +
                "\t\t\t\"orderLine\": {\n" +
                "\t\t\t\t\"itemId\": \"1743704586\",\n" +
                "\t\t\t\t\"produceCode\": \"-\",\n" +
                "\t\t\t\t\"orderLineNo\": \"null\",\n" +
                "\t\t\t\t\"inventoryType\": \"CC\",\n" +
                "\t\t\t\t\"itemCode\": \"202003240004\",\n" +
                "\t\t\t\t\"batchCode\": \"520000002127856\",\n" +
                "\t\t\t\t\"actualQty\": \"2\"\n" +
                "\t\t\t}\n" +
                "\t\t},{\n" +
                "\t\t\t\"orderLine\": {\n" +
                "\t\t\t\t\"itemId\": \"1743704587\",\n" +
                "\t\t\t\t\"produceCode\": \"-\",\n" +
                "\t\t\t\t\"orderLineNo\": \"null\",\n" +
                "\t\t\t\t\"inventoryType\": \"ZP\",\n" +
                "\t\t\t\t\"itemCode\": \"202003240005\",\n" +
                "\t\t\t\t\"batchCode\": \"520000002127856\",\n" +
                "\t\t\t\t\"actualQty\": \"3\"\n" +
                "\t\t\t}\n" +
                "\t\t}],\n" +
                "\t\t\"returnOrder\": {\n" +
                "\t\t\t\"orderType\": \"THRK\",\n" +
                "\t\t\t\"returnOrderCode\": \"29\",\n" +
                "\t\t\t\"outBizCode\": \"TTSHT200330000007\",\n" +
                "\t\t\t\"returnOrderId\": \"LBX0520644452096328\",\n" +
                "\t\t\t\"orderConfirmTime\": \"2020-03-31 10:11:58\",\n" +
                "\t\t\t\"warehouseCode\": \"B400\"\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t\"method\": \"returnorder.confirm\"\n" +
                "}";
        // refundOrderToWmsBackService.handle(message,false);
    }
}
