package com.jackrain.nea.oc.oms.transfer;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.mq.processor.impl.mq.QimenMessageRouteListenerMq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

/**
 * 奇门POS-接单状态回传测试
 *
 * @Auther: 黄志优
 * @Date: 2020/8/27 16:22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class QiMenBackFillMessageListenerTest {

    @Autowired
    QimenMessageRouteListenerMq receiptBackMq;


    //tag	smuat_oc_oms
    //方法	3e9nl9rhrg.burgeon.taobao.pos.salesorder.update
    //customerId	SEMIR_BJ_TEST

    //topic : BJ_DEV_R3_QIMEN_POS_CALLBACK_CLOUDHUB   tag ： smdev_oc_oms    groupId : GID_SM_OC_OMS_DEV

    @Test
    public void testConsume() {
        try {
            Message message = new Message();
            message.setTopic("BJ_DEV_R3_QIMEN_POS_CALLBACK_CLOUDHUB");
            message.setTag("smdev_oc_oms");
            message.setKey("smdev_oc_oms");
            message.setReconsumeTimes(5);
            message.setMsgID("GID_SM_OC_OMS_DEV");

            try {
                message.setBody(MsgConvertUtil.objectSerialize(buildBody()));
            } catch (IOException e) {
                e.printStackTrace();
            }

//            Action result = receiptBackMq.consume(message, null);
//            log.debug("testConsume.result:{}", result);
        } catch (Exception e) {
            log.error("testConsume.try.error", e);
        }
    }

    private Object buildBody() {
        JSONObject order = new JSONObject();
        order.put("method", "3e9nl9rhrg.burgeon.taobao.pos.salesorder.update");
        order.put("customerid", "SEMIR_BJ_TEST");

        JSONObject request = new JSONObject();
        JSONObject extendProps = new JSONObject();

        request.put("cancleDate", "2017-11-11 10:00:00");
        request.put("delivery", "0-未取货");
        request.put("shippingCode", "00000");
        request.put("zf_message", "21341");
        request.put("orderBillCode", "ON20082800000122");
        request.put("canceler", "hhh");
        request.put("confirm", "0");
        request.put("confirmor", "确认人");
        request.put("confirmDate", "2017-11-11 10:00:00");
        request.put("shippingSn", "9999");
        request.put("cancle", "cancle");
        request.put("finish", "cancle");
        request.put("finishDate", "2017-11-11 10:00:00");
        request.put("zf_type", "324");
        request.put("deliveryDate", "2017-11-11 10:00:00");
        request.put("status", "1");
        order.put("request", request);

        extendProps.put("storeCode", "15SHA0008T");
        extendProps.put("storeName", "上海康城店");
        extendProps.put("cp_c_supplier_ename", "上海伯俊");
        extendProps.put("cp_c_supplier_ecode", "20012");
        extendProps.put("pos_orderno", "20200725");
        extendProps.put("zt_orderno", "431325");
        extendProps.put("orgCode", "00001");
        extendProps.put("orgName", "上海伯俊");
        request.put("extendProps", extendProps);

        return order;
    }
}
