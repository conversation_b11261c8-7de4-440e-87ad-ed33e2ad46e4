package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.model.result.kdzs.KdzsCallBackResponse;
import com.jackrain.nea.util.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2022年04月07日 17:13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class KdzsCallBackCmdImplTest {

    @Autowired
    private KdzsCallBackCmdImpl kdzsCallBackCmd;

    private MockHttpServletRequest request;
    private MockHttpServletResponse response;

    @Before
    public void setUp(){
        request = new MockHttpServletRequest();
        request.setCharacterEncoding("UTF-8");
        request.addHeader("dataDigest", Sign());
        response = new MockHttpServletResponse();
    }

    @Test
    public void test1() {
        String data = data();
        KdzsCallBackResponse kdzsCallBackResponse = kdzsCallBackCmd.kdzsCallBack(data, null);
        log.info("------->>> kdzsCallBackRequest:{}", JSON.toJSONString(data));
        log.info("------->>> kdzsCallBackResponse:{}", JSON.toJSONString(kdzsCallBackResponse));
        Assert.assertTrue("与预期一致", kdzsCallBackResponse.getCode()==200);
    }

    private String Sign(){
        String appSecret = "123";
        StringBuffer signature = new StringBuffer();
        signature.append(appSecret);
        signature.append(data());
        signature.append(appSecret);
        // MD5加密
        String ciphertext = MD5Util.encryptByMD5(signature.toString());
        // base64编码
        String baseCiphertext = null;
        try {
            baseCiphertext = Base64.getEncoder().encodeToString(ciphertext.getBytes("utf-8"));
        } catch (UnsupportedEncodingException e) {
            log.debug(this.getClass().getName()+"，UnsupportedEncodingException:",e.getMessage());
        }
        return baseCiphertext;
    }

    private String data(){
        return "{\"cpCode\":\"EMS\",\"logisticsFullTraceList\":[{\"areaCode\":\"CN330100000000\",\"areaName\":\"浙江省,杭州市\",\"desc\":\"【杭州电商仓配揽投部】已收寄,揽投员:刘岭,电话:13754324900\",\"logisticsStatus\":\"ACCEPT\",\"subLogisticsStatus\":\"ACCEPT\",\"time\":1632123146000},{\"areaCode\":\"CN330100000000\",\"areaName\":\"浙江省,杭州市\",\"desc\":\"离开【杭州电商仓配揽投部】,下一站【杭州萧山区东片集散中心】\",\"logisticsStatus\":\"TRANSPORT\",\"subLogisticsStatus\":\"TRANSPORT\",\"time\":1632140994000},{\"areaCode\":\"CN330109000000\",\"areaName\":\"浙江省,杭州市,萧山区\",\"desc\":\"到达【杭州萧山区东片集散中心】\",\"logisticsStatus\":\"TRANSPORT\",\"subLogisticsStatus\":\"TRANSPORT\",\"time\":1632143156000},{\"areaCode\":\"CN330109000000\",\"areaName\":\"浙江省,杭州市,萧山区\",\"desc\":\"离开【杭州萧山区东片集散中心】,下一站【杭州坎山处理中心】\",\"logisticsStatus\":\"TRANSPORT\",\"subLogisticsStatus\":\"TRANSPORT\",\"time\":1632151863000},{\"areaCode\":\"CN330100000000\",\"areaName\":\"浙江省,杭州市\",\"desc\":\"离开【杭州坎山处理中心】,下一站【荆州市网路运营中心包件处理班】\",\"logisticsStatus\":\"TRANSPORT\",\"subLogisticsStatus\":\"TRANSPORT\",\"time\":1632174602000},{\"areaCode\":\"CN421000000000\",\"areaName\":\"湖北省,荆州市\",\"desc\":\"到达【荆州市网路运营中心包件处理班】\",\"logisticsStatus\":\"TRANSPORT\",\"subLogisticsStatus\":\"TRANSPORT\",\"time\":1632228064000},{\"areaCode\":\"CN421000000000\",\"areaName\":\"湖北省,荆州市\",\"desc\":\"离开【荆州市网路运营中心包件处理班】,下一站【湖北省松滋市寄递事业部投递二部营业部】\",\"logisticsStatus\":\"TRANSPORT\",\"subLogisticsStatus\":\"TRANSPORT\",\"time\":1632257646000},{\"areaCode\":\"CN421087000000\",\"areaName\":\"湖北省,荆州市,松滋市\",\"desc\":\"离开【湖北省松滋市寄递事业部投递二部营业部】,下一站【松滋市八宝邮政支局】\",\"logisticsStatus\":\"TRANSPORT\",\"subLogisticsStatus\":\"TRANSPORT\",\"time\":1632275952000},{\"desc\":\"到达【松滋市八宝邮政支局】\",\"logisticsStatus\":\"TRANSPORT\",\"subLogisticsStatus\":\"TRANSPORT\",\"time\":1632277344000},{\"desc\":\"【松滋市八宝邮政支局】安排投递,投递员:向红,电话:13451220400,揽投部电话:0716-6872160\",\"logisticsStatus\":\"DELIVERING\",\"subLogisticsStatus\":\"DELIVERING\",\"time\":1632279459000},{\"desc\":\"已签收,本人签收 :隗忠华,投递员:向红,电话:13451220400\",\"logisticsStatus\":\"SIGN\",\"subLogisticsStatus\":\"SIGN\",\"time\":1632298397000}],\"logisticsStatus\":\"SIGN\",\"logisticsStatusDesc\":\"已签收\",\"mailNo\":\"SF7444437004312\"}";
    }

}
