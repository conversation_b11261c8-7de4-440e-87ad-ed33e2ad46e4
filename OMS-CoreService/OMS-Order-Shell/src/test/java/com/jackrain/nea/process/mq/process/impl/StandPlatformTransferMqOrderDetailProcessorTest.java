package com.jackrain.nea.process.mq.process.impl;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderItemMapper;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.mq.processor.impl.transfer.normal.StandPlatformTransferMqOrderDetailProcessor;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * Description： 消息消费测试
 * Author: RESET
 * Date: Created in 2020/7/21 16:59
 * Modified By:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class StandPlatformTransferMqOrderDetailProcessorTest {

    @Autowired
    private IpBStandplatOrderItemMapper orderItemMapper;

    @Test
//    @Transactional
    @Rollback(false)
    public void testProcess() {
        StandPlatformTransferMqOrderDetailProcessor process = new StandPlatformTransferMqOrderDetailProcessor();
        // 自动注入
        ApplicationContextHandle.getApplicationContext().getAutowireCapableBeanFactory().autowireBean(process);

        OperateOrderMqInfo info = new OperateOrderMqInfo();
        info.setOrderId(1028);
        info.setOrderNo("7791231234");

        process.start(info);
    }

    @Test
    public void testFindNull() {
        List<IpBStandplatOrderItemEx> list = orderItemMapper.selectOrderItemList(-1L);

        System.out.println("[" + list + "]");

        if (list == null) {
            System.out.println("is null");
        }

        if (CollectionUtils.isEmpty(list)) {
            System.out.println("is empty");
        }

        if (list != null) {
            System.out.println("in the place");
        } else {
            System.out.println("not in the place");
        }
    }

}
