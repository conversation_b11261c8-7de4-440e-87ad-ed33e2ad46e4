package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.StCLiveCastStrategyAllResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.Instant;
import java.util.Date;
import java.util.List;

/**
 * Description： redis缓存测试
 * Author: RESET
 * Date: Created in 2020/8/31 8:37
 * Modified By:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class StRpcServiceTest {

    @Autowired
    StRpcService stRpcService;

    @Test
    public void testLiveStrategyRedis() {
        Long cpCShopId = 1000L;
        Date orderDate = Date.from(Instant.now());
        Date payTime = Date.from(Instant.now());

        // 查询第一遍
        List<StCLiveCastStrategyAllResult> r1 = stRpcService.queryLiveCastStrategy(cpCShopId, orderDate, payTime);
        log.info("r1:{}", JSON.toJSONString(r1));

        // 查询第二遍
        List<StCLiveCastStrategyAllResult> r2 = stRpcService.queryLiveCastStrategy(cpCShopId, orderDate, payTime);
        log.info("r2:{}", JSON.toJSONString(r2));
    }

}
