package com.jackrain.nea.process.mq.process.impl;


import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.process.audit.OrderAuditProcess;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderAutoAuditService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Auther: 黄志优
 * @Date: 2020/11/17 17:48
 * @Description:
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class OrderAuditProcessTest {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OrderAuditProcess auditProcess;

    @Autowired
    private OmsOrderAutoAuditService omsOrderAutoAuditService;

    @Test
    public void autoAudit(){
        OcBOrderRelation  relation  = omsOrderService.selectOmsOrderInfo(Long.parseLong("5383835"));
//        auditProcess.start(relation,true, SystemUserResource.getRootUser());

        omsOrderAutoAuditService.isExitsStrategy(relation);
    }
}
