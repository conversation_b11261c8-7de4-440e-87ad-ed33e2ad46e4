package com.jackrain.nea.oc.oms.transfer;

import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.StandplatRefundRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.IpStandplatRefundService;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @author: 夏继超
 * @since: 2019/7/18
 * create at : 2019/7/18 15:45
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class CurrencyTransferTest {
    @Autowired
    IpStandplatRefundService standplatRefundService;
    @Autowired
    private StandplatRefundRefundProcessImpl standplatRefundRefundProcess;
    @Autowired
    IpBStandplatRefundItemMapper ipBStandplatRefundItemMapper;
    @Autowired
    IpBStandplatRefundMapper refundMapper;
    @Autowired
    OcBOrderMapper orderMapper;

  /*  @Test
    public void test() {
        IpStandplatRefundRelation ipStandplatRefundRelation = standplatRefundService.selectStandplatRefundRelation("1289505433");
        if (ipStandplatRefundRelation != null) {
            standplatRefundRefundProcess.start(ipStandplatRefundRelation, false, SystemUserResource.getRootUser());
        }

    }*/

  /*  @Test
    public void test1() {
      //  IpBStandplatRefundItem Item = ipBStandplatRefundItemMapper.selectById(27L);
     //   IpBStandplatRefund refund = refundMapper.selectById(23L);

    }*/

}
