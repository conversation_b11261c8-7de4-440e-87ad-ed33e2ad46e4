package com.jackrain.nea.task;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.task.auditorder.AutoSendMqAuditOrderTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * @Auther: 黄志优
 * @Date: 2020/12/10 16:47
 * @Description: 审核任务测试
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class AutoSendMqAuditOrderTaskTest {

    @Autowired
    AutoSendMqAuditOrderTask autoSendMqAuditOrderTask;

    @Test
    public void execute() {

        autoSendMqAuditOrderTask.execute(null);
    }
}