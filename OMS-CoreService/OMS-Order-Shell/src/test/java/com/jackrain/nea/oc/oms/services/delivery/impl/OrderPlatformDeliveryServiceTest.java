package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR> sunies
 * @since : 2020-11-12
 * create at : 2020-11-12 11:24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class OrderPlatformDeliveryServiceTest {

    @Resource
    private OrderPlatformDeliveryService orderPlatformDeliveryService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Test
    public void getExpressCodeFromOrderDelivery() {
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectItemListOfUnshippedAndNonGift(40L);
        Map<Long, String> skuIdAndOoid = ocBOrderItems.stream().collect(Collectors.toMap(OcBOrderItem::getPsCSkuId, OcBOrderItem::getOoid));
        System.out.println(orderPlatformDeliveryService.getExpressCodeFromOrderDelivery(null, skuIdAndOoid.keySet()));
    }
}