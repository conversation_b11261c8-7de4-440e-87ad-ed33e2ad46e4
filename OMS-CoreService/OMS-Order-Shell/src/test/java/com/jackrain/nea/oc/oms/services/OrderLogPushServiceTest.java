package com.jackrain.nea.oc.oms.services;

import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.model.enums.WosWorkOrderTypeEnum;
import com.jackrain.nea.oc.request.OcBOrderLogPushRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @create 2020-08-18
 * @desc
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
public class OrderLogPushServiceTest {
    @Autowired
    private OrderLogPushService orderLogPushService;

    @Test
    public void test() {
        OcBOrderLogPushRequest request = OcBOrderLogPushRequest.builder()
                .billNo("OM20081000019620").logMessage("sssdsdd").logType("10").wosWorkOrderType(WosWorkOrderTypeEnum.MODIFY_INFORMATION.getCode()).build();
        orderLogPushService.wosPushOperationLog(request);

    }
}
