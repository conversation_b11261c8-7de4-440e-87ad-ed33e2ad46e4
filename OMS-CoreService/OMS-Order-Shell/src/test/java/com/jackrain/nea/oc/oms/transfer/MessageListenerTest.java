package com.jackrain.nea.oc.oms.transfer;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.mq.processor.impl.mq.RefundOrderToWmsReceiptBackMq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

/**
 * Description： 消息监听单元测试
 * Author: RESET
 * Date: Created in 2020/8/25 21:42
 * Modified By:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class MessageListenerTest {

    @Autowired
    RefundOrderToWmsReceiptBackMq receiptBackMq;

    @Test
    public void testConsume() {
        try {
            Message message = new Message();
            message.setTopic("TEST_TOPIC_004");
            message.setTag("TEST_TAG_004");
            message.setKey("TEST_KEY_004");
            message.setReconsumeTimes(6);
            message.setMsgID("TEST_MESSAGE_ID_004");

            try {
                message.setBody(MsgConvertUtil.objectSerialize(buildBody()));
            } catch (IOException e) {
                e.printStackTrace();
            }

//            Action result = receiptBackMq.consume(message, null);
//            log.debug("testConsume.result:{}", result);
        } catch (Exception e) {
            log.error("testConsume.try.error", e);
        }
    }

    private Object buildBody() {
        JSONObject order = new JSONObject();
        order.put("method", "returnorder.confirm");

        JSONObject request = new JSONObject();
        JSONObject returnOrder = new JSONObject();
        returnOrder.put("returnOrderCode", "AXOR202008280936");

        request.put("returnOrder", returnOrder);
        order.put("request", request);

        return order;
    }

}
