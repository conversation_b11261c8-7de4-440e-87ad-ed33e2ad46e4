package com.jackrain.nea.oc.oms.services;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.OmsOrderProcessApplication;
import com.jackrain.nea.oc.oms.model.request.kdzs.KdzsCallBackRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @description:
 * @DateTime 2022/4/7 16:05
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OmsOrderProcessApplication.class)
@Slf4j
public class OmsReturnOrderServiceLogisticsTraceTest {

    @Autowired
    KdzsCallBackService kdzsCallBackService;


    @Test
    public void test1() {
        String data = "{\n" +
                "    \"cpCode\":\"EMS\",\n" +
                "    \"logisticsFullTraceList\":[\n" +
                "        {\n" +
                "            \"areaCode\":\"CN330100000000\",\n" +
                "            \"areaName\":\"浙江省,杭州市\",\n" +
                "            \"desc\":\"【杭州电商仓配揽投部】已收寄,揽投员:刘岭,电话:13754324900\",\n" +
                "            \"logisticsStatus\":\"ACCEPT\",\n" +
                "            \"subLogisticsStatus\":\"ACCEPT\",\n" +
                "            \"time\":1632123146000\n" +
                "        },\n" +
                "        {\n" +
                "            \"areaCode\":\"CN330100000000\",\n" +
                "            \"areaName\":\"浙江省,杭州市\",\n" +
                "            \"desc\":\"离开【杭州电商仓配揽投部】,下一站【杭州萧山区东片集散中心】\",\n" +
                "            \"logisticsStatus\":\"TRANSPORT\",\n" +
                "            \"subLogisticsStatus\":\"TRANSPORT\",\n" +
                "            \"time\":1632140994000\n" +
                "        },\n" +
                "        {\n" +
                "            \"areaCode\":\"CN330109000000\",\n" +
                "            \"areaName\":\"浙江省,杭州市,萧山区\",\n" +
                "            \"desc\":\"到达【杭州萧山区东片集散中心】\",\n" +
                "            \"logisticsStatus\":\"TRANSPORT\",\n" +
                "            \"subLogisticsStatus\":\"TRANSPORT\",\n" +
                "            \"time\":1632143156000\n" +
                "        },\n" +
                "        {\n" +
                "            \"areaCode\":\"CN330109000000\",\n" +
                "            \"areaName\":\"浙江省,杭州市,萧山区\",\n" +
                "            \"desc\":\"离开【杭州萧山区东片集散中心】,下一站【杭州坎山处理中心】\",\n" +
                "            \"logisticsStatus\":\"TRANSPORT\",\n" +
                "            \"subLogisticsStatus\":\"TRANSPORT\",\n" +
                "            \"time\":1632151863000\n" +
                "        },\n" +
                "        {\n" +
                "            \"areaCode\":\"CN330100000000\",\n" +
                "            \"areaName\":\"浙江省,杭州市\",\n" +
                "            \"desc\":\"离开【杭州坎山处理中心】,下一站【荆州市网路运营中心包件处理班】\",\n" +
                "            \"logisticsStatus\":\"TRANSPORT\",\n" +
                "            \"subLogisticsStatus\":\"TRANSPORT\",\n" +
                "            \"time\":1632174602000\n" +
                "        },\n" +
                "        {\n" +
                "            \"areaCode\":\"CN421000000000\",\n" +
                "            \"areaName\":\"湖北省,荆州市\",\n" +
                "            \"desc\":\"到达【荆州市网路运营中心包件处理班】\",\n" +
                "            \"logisticsStatus\":\"TRANSPORT\",\n" +
                "            \"subLogisticsStatus\":\"TRANSPORT\",\n" +
                "            \"time\":1632228064000\n" +
                "        },\n" +
                "        {\n" +
                "            \"areaCode\":\"CN421000000000\",\n" +
                "            \"areaName\":\"湖北省,荆州市\",\n" +
                "            \"desc\":\"离开【荆州市网路运营中心包件处理班】,下一站【湖北省松滋市寄递事业部投递二部营业部】\",\n" +
                "            \"logisticsStatus\":\"TRANSPORT\",\n" +
                "            \"subLogisticsStatus\":\"TRANSPORT\",\n" +
                "            \"time\":1632257646000\n" +
                "        },\n" +
                "        {\n" +
                "            \"areaCode\":\"CN421087000000\",\n" +
                "            \"areaName\":\"湖北省,荆州市,松滋市\",\n" +
                "            \"desc\":\"离开【湖北省松滋市寄递事业部投递二部营业部】,下一站【松滋市八宝邮政支局】\",\n" +
                "            \"logisticsStatus\":\"TRANSPORT\",\n" +
                "            \"subLogisticsStatus\":\"TRANSPORT\",\n" +
                "            \"time\":1632275952000\n" +
                "        },\n" +
                "        {\n" +
                "            \"desc\":\"到达【松滋市八宝邮政支局】\",\n" +
                "            \"logisticsStatus\":\"TRANSPORT\",\n" +
                "            \"subLogisticsStatus\":\"TRANSPORT\",\n" +
                "            \"time\":1632277344000\n" +
                "        },\n" +
                "        {\n" +
                "            \"desc\":\"【松滋市八宝邮政支局】安排投递,投递员:向红,电话:13451220400,揽投部电话:0716-6872160\",\n" +
                "            \"logisticsStatus\":\"DELIVERING\",\n" +
                "            \"subLogisticsStatus\":\"DELIVERING\",\n" +
                "            \"time\":1632279459000\n" +
                "        },\n" +
                "        {\n" +
                "            \"desc\":\"已签收,本人签收 :隗忠华,投递员:向红,电话:13451220400\",\n" +
                "            \"logisticsStatus\":\"SIGN\",\n" +
                "            \"subLogisticsStatus\":\"SIGN\",\n" +
                "            \"time\":1632298397000\n" +
                "        }\n" +
                "    ],\n" +
                "    \"logisticsStatus\":\"SIGN\",\n" +
                "    \"logisticsStatusDesc\":\"已签收\",\n" +
                "    \"mailNo\":\"9796578035309\"\n" +
                "}";
        KdzsCallBackRequest request = JSON.parseObject(data, KdzsCallBackRequest.class);

        ValueHolderV14 v14 = kdzsCallBackService.saveAndUpdateLogisticsTrace(request);

        log.info("result:{}", JSON.toJSONString(v14));
    }


}
