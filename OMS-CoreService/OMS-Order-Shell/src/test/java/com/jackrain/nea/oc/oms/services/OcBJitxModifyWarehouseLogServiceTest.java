//package com.jackrain.nea.oc.oms.services;
//
//import com.jackrain.nea.OmsOrderProcessApplication;
//import com.jackrain.nea.oc.oms.task.jitxorder.AutoJitxCreateChangeWarehouseFromDBTask;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
///**
// * <AUTHOR>
// * @create 2020-12-28
// * @desc
// **/
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = OmsOrderProcessApplication.class)
//@Slf4j
//public class OcBJitxModifyWarehouseLogServiceTest {
//
//    @Autowired
//    private OcBJitxModifyWarehouseLogService ocBJitxModifyWarehouseLogService;
//
//    @Test
//    public void selectByNode() {
//        ocBJitxModifyWarehouseLogService.selectByNode("DEV_R3_OMS_OC_IP_1592149757496COZV_EUGX_0005",
//                "oc_b_jitx_modify_warehouse_log",
//                " WHERE CREATED_STATUS IN(0,2) AND ISACTIVE = 'Y' ",
//                " ORDER BY SELLER_NICK,VENDOR_ID ASC ",
//                10);
//    }
//
//    @Test
//    public void execute() {
//        autoJitxCreateChangeWarehouseFromDBTask.execute(null);
//    }
//}
