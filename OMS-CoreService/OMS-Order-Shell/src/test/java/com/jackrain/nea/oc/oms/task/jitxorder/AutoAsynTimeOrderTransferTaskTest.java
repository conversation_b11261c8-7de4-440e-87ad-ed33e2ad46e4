//package com.jackrain.nea.oc.oms.task.jitxorder;
//
//import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
//import com.jackrain.nea.task.RunTaskResult;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import java.util.Arrays;
//import java.util.List;
//
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest()
//public class AutoAsynTimeOrderTransferTaskTest {
//
//    @Autowired
//    private AutoAsynTimeOrderTransferTask transferTask;
//
//    @Autowired
//    private IpVipTimeOrderService ipVipTimeOrderService;
//
//    @Test
//    public void getRunTaskResult() {
//
//        RunTaskResult runTaskResult = transferTask.getRunTaskResult();
//        System.out.println(runTaskResult);
//        toEx();
//        Assert.assertTrue(runTaskResult.isSuccess());
//    }
//
//    @Test
//    public void toEx() {
//        List<String> strings = Arrays.asList(
//                "20070281432706691815181012271292760517702",
//                "20070281432706691870259820942742360517702",
//                "20070281432706691815180981935087960517702",
//                "20070281432706691815180981935907160517702"
//        );
//        strings.stream().forEach(item -> {
//            ipVipTimeOrderService.updateTimeOrderES(item);
//        });
//        Assert.assertTrue(strings.size() >= 0);
//    }
//}