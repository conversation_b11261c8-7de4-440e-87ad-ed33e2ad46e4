# \u5E94\u7528\u7A0B\u5E8F\u540D
spring.application.name=R3-OC-OMS-Core
# apollo \u914D\u7F6E\u542F\u52A8\uFF0CTrue\u8868\u793AApplication\u542F\u52A8\u65F6\u9996\u5148\u52A0\u8F7DApollo\u914D\u7F6E
#apollo.bootstrap.enabled=true
# apollo.bootstrap.namespace \u8868\u793A\u52A0\u8F7DApollo\u7684namespace\u7684\u5185\u5BB9\u3002\u91C7\u7528\u82F1\u6587,\u8FDE\u63A5\u8868\u793A\u591A\u4E2A
#apollo.bootstrap.namespaces=application,drds,dubbo,redis,elasticsearch,slaverrds,mq-consumer,mq-producer,lts,reloadschema,common,oss
# \u591A\u8BED\u8A00\u9ED8\u8BA4\u8BBE\u7F6E\u3002\u5728Apollo\u4E2D\u914D\u7F6E\u7684\u65E0\u6CD5\u751F\u6548\uFF0C\u53EA\u80FD\u5728application.properties\u914D\u7F6E
spring.locale.default=zh_CN
# \u9ED8\u8BA4\u7CFB\u7EDF\u7AEF\u53E3\u53F7
server.port=7080
# \u5F53\u524D\u7CFB\u7EDF\u8FD0\u884C\u914D\u7F6E\u3002\u4E3B\u8981\u7528\u4E8E\u65E5\u5FD7\u7684\u8F93\u51FA\uFF0C\u751F\u4EA7\u7EA7\u522B\u5C06\u4E0D\u518D\u8F93\u51FAdebug\u76F8\u5173\u65E5\u5FD7
spring.profiles.active=dev
# \u5E94\u7528\u7A0B\u5E8F\u540D-Dubbo\u5E94\u7528\u7A0B\u5E8F
dubbo.application.name=R3-OC-OMS-Core
app.id=R3-OC-OMS-Core;
nacos.config.type=properties
nacos.config.bootstrap.enable=true
nacos.config.bootstrap.log-enable=false
nacos.config.username=nacos
nacos.config.password=nacos
nacos.config.group=r3-oms
nacos.config.data-ids=oms-core,drds,redis,oss,reloadschema,oms-core-lts,oms-core-mq-consumer,oms-core-mq-producer,common,elasticsearch
nacos.config.auto-refresh=true
#dubbo.consumer.check=false
#zookeeper.server = zookeeper://127.0.0.1:2181
#dubbo.registry.address = zookeeper://127.0.0.1:2181
#dubbo.metadata-report.address = zookeeper://127.0.0.1:2181
#dubbo.config-center.address = zookeeper://127.0.0.1:2181
#dubbo.consumer.retries = 0
#dubbo.registry.check=false
#lts.jobclient.cluster-name = test-r3-oms-lts
#lts.jobclient.node-group = r3_oc_oms
#lts.jobclient.registry-address = zookeeper://**********:41181
#lts.jobclient.use-retry-client = true
#lts.jobclient.configs.job.fail.store = mapdb
#lts.tasktracker.cluster-name = test-r3-oms-lts
#lts.tasktracker.dispatch-runner.enable = true
#lts.tasktracker.node-group = r3_oc_oms
#lts.tasktracker.registry-address = zookeeper://**********:41181
#lts.tasktracker.work-threads = 20
#lts.tasktracker.configs.job.fail.store = mapdb



