package com.jackrain.nea.oc.oms.process;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.web.face.User;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;

/**
 * @author: hulinyang
 * @since: 2019-04-12
 * create at : 2019-04-12 21:28
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class OrderMainFLowProcessTest {

    @Autowired
    private OmsFreightLogisticsCostService omsFreightLogisticsCostService;

    @Autowired
    private OmsOrderOperatingCostHandleService omsOrderOperatingCostHandleService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderOutService omsOrderOutService;

    @Autowired
    private OmsOrderPlatformDeliveryService omsOrderPlatformDeliveryService;

    @Autowired
    private OmsOrderUpdateInDistributionService omsOrderUpdateInDistributionService;

    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;


    @Before
    public void initial() {
//        distributeProcess = new TransferWaitDistributeProcess();

    }

    /**
     * 订单出库
     */
    @Test
    public void testOrderOutOfStock() {
        User user = null;
        Date date = new Date();
        omsOrderOutService.orderOutOfStock(7065L, 1, user, date);
    }

    /**
     * 计算运费方案
     */
    @Test
    public void testCalcFreightLogisticsCost() {
        User user = null;
        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
        OcBOrder orderInfo = new OcBOrder();
        orderInfo.setId(12L);
        ocBOrderRelation.setOrderInfo(orderInfo);
        omsFreightLogisticsCostService.freightLogisticsCost(ocBOrderRelation, user);
    }

    /**
     * 分配物流公司
     */
    @Test
    public void orderDistributeLogistics() {
        User user = null;
        OcBOrderRelation ocBOrderRelation = omsOrderService.selectOmsOrderInfo(7163L);
        omsOrderDistributeLogisticsService.orderDistributeLogistics(ocBOrderRelation, user);
    }

    /**
     * 计算操作费方案
     */
    @Test
    public void testOrderOperatingCost() {
        User user = null;
        omsOrderOperatingCostHandleService.operatingCostHandle(12L, 3, user);
    }

    @Test
    public void testOrderPlatformDelivery() {

    }
}
