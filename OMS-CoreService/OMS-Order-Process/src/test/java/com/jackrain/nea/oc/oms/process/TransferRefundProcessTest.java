package com.jackrain.nea.oc.oms.process;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.IpBWhInnerOperateMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBWhInnerOperate;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.TaobaoTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.TaobaoTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx.TaobaoFxTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.service.OmsOrderTestService;
import com.jackrain.nea.oc.oms.services.BatchOperationGoodsService;
import com.jackrain.nea.oc.oms.services.IpTaobaoFxRefundService;
import com.jackrain.nea.oc.oms.services.IpTaobaoOrderService;
import com.jackrain.nea.oc.oms.services.IpTaobaoRefundService;
import com.jackrain.nea.oc.oms.services.MarkRefundCompleteService;
import com.jackrain.nea.oc.oms.services.OmsAddBlacklistService;
import com.jackrain.nea.oc.oms.services.OmsConstituteSplitService;
import com.jackrain.nea.oc.oms.services.OmsFortuneBagAgainSmokeService;
import com.jackrain.nea.oc.oms.services.OmsOrderAutoSearchWarehouseService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsReplaceComposeService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.oc.oms.services.OmsTaobaoRefundService;
import com.jackrain.nea.oc.oms.services.OrderMergeService;
import com.jackrain.nea.oc.oms.services.OrderWmsWithdrawService;
import com.jackrain.nea.oc.oms.services.patrol.ReturnOrderAmtIsZeroService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;

import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.resource.WmsUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.BllWebUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: sunys
 * @since: 2019-03-15
 * create at : 2019-03-15 11:28
 */

@RunWith(SpringJUnit4ClassRunner.class)
@Profile(value = "dev")
@SpringBootTest(
//        classes = {
//        // 以下是通用需要加载的
//        ApplicationContextHandle.class,
//        TmCacheConf.class,
//        TableManager.class,
//        Dictionary.class,
//
//        // 以下是你需要用到什么就加载什么
//        IpTaobaoRefundService.class,
//        TestExecutionListener.class,
//        PropertiesConf.class,
//        TransferRefundProcessTest.class,
//}
)
public class TransferRefundProcessTest {

    @Autowired
    private TaobaoTransferRefundProcessImpl taobaoTransferRefundProcessImpl;

    @Autowired
    private IpTaobaoRefundService taoBaoRefundService;
    @Autowired
    private OmsOrderTestService omsOrderTestService;
    @Autowired
    private OrderMergeService orderMergeService;

    //    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private BllWebUtil bllWebUtil;
    @Autowired
    private OmsReturnOrderService omsReturnOrderService;
    @Autowired
    private ReturnOrderAmtIsZeroService returnOrderAmtIsZeroService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OrderWmsWithdrawService orderWmsWithdrawService;
    @Autowired
    private OmsAddBlacklistService omsAddBlacklistService;
    @Autowired
    private OmsFortuneBagAgainSmokeService omsFortuneBagAgainSmokeService;
    @Autowired
    private OmsOrderAutoSearchWarehouseService omsOrderAutoSearchWarehouseService;
    @Autowired
    private IpTaobaoFxRefundService ipTaobaoFxRefundService;
    @Autowired
    private TaobaoFxTransferRefundProcessImpl taobaoFxTransferRefundProcess;
    @Autowired
    private OmsTaobaoRefundService omsTaobaoRefundService;
    @Autowired
    private IpBWhInnerOperateMapper ipBWhInnerOperateMapper;
    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;
    @Autowired
    private OmsReplaceComposeService omsReplaceComposeService;
    @Autowired
    private BatchOperationGoodsService batchOperationGoodsService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private MarkRefundCompleteService markRefundCompleteService;
    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;
    @Autowired
    private TaobaoTransferOrderProcessImpl taobaoTransferOrderProcess;
    @Autowired
    private IpTaobaoOrderService taobaoOrderService;


    @Before
    public void initial() {
//        distributeProcess = new TransferWaitDistributeProcess();

    }

//    @Test
//    public void testDistributeProcess001() {
//        IpTaobaoRefundRelation orderInfo = taoBaoRefundService.selectTaobaoRefundRelation("30987618348721412");
//
//        ProcessStepResultList resultList = taobaoTransferRefundProcessImpl.start(orderInfo, false, SystemUserResource.getRootUser());
//        System.out.println(resultList.size());
//    }

    @Test
    public void selectOcBOrderItem() throws IOException {
        OcBOrder p = ocBOrderMapper.selectByID(24772L);


        List<OcBOrderItem> items = omsOrderTestService.selectItems(24772L);
        try {
            ElasticSearchUtil.indexDocument("oc_b_order", "oc_b_order", p, p.getId());
//                 SpecialElasticSearchUtil.indexDocument("oc_b_order", "oc_b_order_item", items.get(0),
//                      items.get(0),order.getId() );

            ElasticSearchUtil.indexDocuments("oc_b_order", "oc_b_order_item", items,
                    "OC_B_ORDER_ID");
            System.out.println("ok");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void omsAddBlacklistServiceTest() {
        User rootUser = WmsUserResource.getWmsUser();
        Long id = 378039L;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId", id);
        jsonObject.put("type", 1);
        ValueHolder holder = omsAddBlacklistService.addBlacklist(jsonObject, rootUser);
        System.out.println("返回结果" + holder);
    }

    @Test
    //@Transactional(rollbackFor = Exception.class)
    public void mergeOrderTest1() throws IOException {
        //13127
        OcBOrder order = ocBOrderMapper.selectByID(107159L);
        order.setOrderStatus(4);

        ocBOrderMapper.updateById(order);
       // SpecialElasticSearchUtil.indexDocument("oc_b_order", "oc_b_order", order, order.getId());
        System.out.println("结果:ok");
        OcBOrder order1 = ocBOrderMapper.selectByID(13348L);
        System.out.println("结果2:ok" + order1);

    }


    @Test
    public void mergeOrderTest() {
//       String index, String type, JSONObject whereKeys,
// JSONObject filterKeys, String script, String... groups) {

//        JSONObject whereKeyJo = new JSONObject(); // 条件
//        whereKeyJo.put("ORDER_STATUS", 1);
//        JSONObject filterKeyJo = new JSONObject(); // 区间
//        String[] groups = new String[]{"RECEIVER_NAME", "RECEIVER_PHONE"};
//
//        JSONObject objJsonList = ElasticSearchUtil.havingCount("oc_b_order", "oc_b_order", whereKeyJo, filterKeyJo, "1~", groups);
//        if (null != objJsonList) {
//            Object data = objJsonList.get("data");
//            if (null != data) {
//                List<MergeEsHavingcountResult> mergeOderGroupsList = JSONArray.parseArray(data.toString(), MergeEsHavingcountResult.class);
//
//                for (MergeEsHavingcountResult mergeEsHavingcountResult : mergeOderGroupsList) {
//                    MergeOderGroups mergeOderGroups = mergeEsHavingcountResult.getMergeOderGroups();
//                    System.out.println(mergeOderGroups.getReceiverName());
//                }
//            }
//        }
//        List<MergeOrderModel> list = orderMergeService.selectOcBOrderList();
//        for (MergeOrderModel id : list) {
//            System.out.println(id);
//
//        }

    }

    @Test
    public void selectAddrIp() {
        String webRequestIpAddress = bllWebUtil.getWebRequestIpAddress();
        System.out.println(webRequestIpAddress);

    }

//    @Test
//    public void selectOcBOrderItem1() {
//        TransferOrderRequest orderRequest = new TransferOrderRequest();
//        List<String> orderNoList = new ArrayList<>();
//        orderNoList.add("21843939559695314");
//        orderRequest.setOrderNoList(orderNoList);
//        orderRequest.setChannelType(ChannelType.TAOBAO);
//        ValueHolderV14<ReturnOrderResult> returnOrderResultValueHolderV14 = returnOrderCmd.startReturnOrder(orderRequest);
//        System.out.println(returnOrderResultValueHolderV14);
//
//    }


    @Test
    public void insertBOrder() {

        List<OcBOrderItem> items = omsOrderTestService.selectItems(3296);
        if (CollectionUtils.isNotEmpty(items)) {


            OcBOrder order = omsOrderTestService.selectOrder(8885);
            order.setId(3296L);
            int flag = omsOrderTestService.insertOrder(order);
            if (flag > 0) {
                System.out.println("ok");
            }
        }
    }

    @Test
    public void returnOrderAmtIsZeroService() {
        List<Long> longs = returnOrderAmtIsZeroService.selectOrderItem(0, 500);
        System.out.println("结果:" + longs);
    }

    @Test
    public void returnOrderAmtIsZeroService1() {
        List<String> list = returnOrderAmtIsZeroService.selectOrderItemOoid(getTodayStartTime(), getTodayEndTime());
        System.out.println("结果:" + list);
    }

    @Test
    public void returnOrderAmtIsZeroService2() {
        List<String> list = returnOrderAmtIsZeroService.selectReturnOrderNotItem(getTodayStartTime(), getTodayEndTime());
        System.out.println("结果:" + list);
    }

    @Test
    public void test() {
        List<OcBOrderParam> ocBOrderParams = omsOrderService.selectOrderWmsWithdrawList(0, 200);
        //List<OcBOrderParam> ocBOrderParams = omsOrderService.selectOrderWmsBackWithdrawList();
        User rootUser = WmsUserResource.getWmsUser();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(ocBOrderParams)) {
            orderWmsWithdrawService.OrderWmsWithdraw(ocBOrderParams, rootUser);
        }
    }


    public Date getTodayEndTime() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 23);
        todayStart.set(Calendar.MINUTE, 59);
        todayStart.set(Calendar.SECOND, 59);
        return todayStart.getTime();
    }

    public Date getTodayStartTime() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        return todayStart.getTime();
    }


    @Test
    public void test2() {
        User rootUser = SystemUserResource.getRootUser();

        //omsOrderAutoSearchWarehouseService.checkAutoSerachOrderInfo(rootUser,4867019L);

        OcBOrder ocBOrder = ocBOrderMapper.selectByID(4867019L);
        List<OcBOrderItem> items = omsOrderTestService.selectItems(4867019L);

        omsFortuneBagAgainSmokeService.againFortuneBagSmoke(ocBOrder, items, rootUser);
    }


    @Test
    public void test3() {
        IpTaobaoFxRefundRelation ipTaobaoFxRefundRelation = ipTaobaoFxRefundService.selectTaobaoFxRefundRelation("35417427689884");
        taobaoFxTransferRefundProcess.start(ipTaobaoFxRefundRelation, false, SystemUserResource.getRootUser());
    }



    @Test
    public void testDistributeProcess003() {
        //omsWarehouseOperationService.selectWarehouseOperationData();
    }


    @Test
    public void testDistributeProcess004() throws IOException {

        OcBOrderDelivery ocBOrderDelivery = ocBOrderDeliveryMapper.selectById(7L);
        IpBWhInnerOperate innerOperate = ipBWhInnerOperateMapper.selectById(5L);
        SpecialElasticSearchUtil.indexDocument("ip_b_wh_inner_operate", "ip_b_wh_inner_operate", innerOperate, innerOperate.getId());
        SpecialElasticSearchUtil.indexCreate(OcBOrderDelivery.class);
        SpecialElasticSearchUtil.indexDocument("oc_b_order_delivery", "oc_b_order_delivery", ocBOrderDelivery, ocBOrderDelivery.getId());


    }

    @Test
    public void test111() {
        JSONObject object = new JSONObject();
        object.put("id", 959);
        object.put("itemId", 2631);
        object.put("skuEcode", "YS02020401014");
        User rootUser = SystemUserResource.getRootUser();
        omsReplaceComposeService.replaceComposePro(object, rootUser);
    }

    @Test
    public void test222() {
        OcBOrder order = ocBOrderMapper.selectById(959L);
        OcBOrderItem items = ocBOrderItemMapper.queryOrderItemById(2631L, 959L);
        List<OcBOrderItem> list = new ArrayList<>();
        list.add(items);
        User rootUser = SystemUserResource.getRootUser();
        ProductSku productSku = psRpcService.selectProductSku("1111");
        //  batchOperationGoodsService.replacePro(order, list, productSku, rootUser);
    }


    @Test
    public void test333() {
        OcBOrder order = ocBOrderMapper.selectById(25137L);
        String[] arr = new String[]{"37177"};
        markRefundCompleteService.getItemIds(arr, order);
    }


    @Test
    public void test555() {
        OcBOrder order = ocBOrderMapper.selectById(31347L);
        List<OcBOrderItem> itemList1 = ocBOrderItemMapper.selectOrderItemListOccupy(31347L);
        List<OcBOrderItem> collect = itemList1.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());

        User rootUser = SystemUserResource.getRootUser();
        List<OcBOrderItem> itemList = omsConstituteSplitService.encapsulationParameter(collect, order, rootUser, 0);
        System.out.println("结果:" + JSONObject.toJSONString(itemList));
        System.out.println("结果:" + JSONObject.toJSONString(itemList));

    }

    @Test
    public void testTransfer001() {
        String orderNo = "948428866969401141";
        IpTaobaoOrderRelation orderRelation = taobaoOrderService.selectTaobaoOrderByTid(orderNo);
        if (orderRelation != null) {
            taobaoTransferOrderProcess.start(orderRelation, false, SystemUserResource.getRootUser());
        }
    }



    @Test
    public void testDistributeProcess002() {

        OmsTaobaoRefundRelation omsTaobaoRefundRelation = omsTaobaoRefundService.selectTaoBaoRefundRelation("202101121135174959");
        ProcessStepResultList resultList = taobaoTransferRefundProcessImpl.start(omsTaobaoRefundRelation, false, SystemUserResource.getRootUser());
        System.out.println(resultList.size());
    }

}
