package com.jackrain.nea.oc.oms.service;

import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@Slf4j
public class OmsOrderTestService {


    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderMapper orderMapper;


    /**
     * 通过orderId
     *
     * @param
     * @return
     */
    public List<OcBOrderItem> selectItems(long orderId) {
        List<OcBOrderItem> items = orderItemMapper.selectOrderItemList(orderId);
        return items;
    }

    public OcBOrder selectOrder(long id) {
        OcBOrder oder = orderMapper.selectByID(id);
        return oder;
    }

    public int insertOrder(OcBOrder order) {
        int falg = orderMapper.insert(order);

        return falg;
    }


}