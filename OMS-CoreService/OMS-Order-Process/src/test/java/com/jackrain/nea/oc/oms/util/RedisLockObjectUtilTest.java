package com.jackrain.nea.oc.oms.util;

import com.burgeon.r3.R3NetworkUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.net.InetAddress;
import java.net.SocketException;
import java.net.UnknownHostException;

/**
 * Redis Lock Order 单元测试
 *
 * @author: 易邵峰
 * @since: 2019-01-28
 * create at : 2019-01-28 09:55
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class RedisLockObjectUtilTest {
//
//    @Autowired
//    private RedisLockOrderUtil lockUtil;

    //    @Test
//    public void lockOrderObject() {
//        long orderId = 1;
//        LockOrderType lockOrderType = LockOrderType.TRANSFER_TAOBAO_ORDER;
//        RedisOperateResult lockResult = lockUtil.lockOrder(orderId, lockOrderType);
//        Assert.assertTrue(lockResult.isSuccess());
//
//        System.out.println("Lock Order Success; Start Check LockKey Is Exist");
//        Assert.assertTrue(lockUtil.isLockedOrder(orderId, lockOrderType));
//
//        System.out.println("Start Thread Sleep");
//        try {
//            Thread.sleep(lockUtil.getLockOrderTimeOut());
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        System.out.println("Finished Wait Timeout; Start Check LockKey Is Exist");
//        Assert.assertFalse(lockUtil.isLockedOrder(orderId, lockOrderType));
//    }
//
//    @Test
//    public void unLockOrderObject() {
//        long objectId = 1;
//        LockOrderType lockOrderType = LockOrderType.TRANSFER_TAOBAO_ORDER;
//        RedisOperateResult unlockResult = lockUtil.unlockOrder(objectId, lockOrderType);
//        Assert.assertTrue(unlockResult.isSuccess());
//    }
    @Test
    public void test() {
        try {
            String ip = R3NetworkUtil.getLocalIpAddress();
            try {
                System.out.print("ip---" + InetAddress.getLocalHost().getAddress().toString());


            } catch (UnknownHostException e) {
                e.printStackTrace();
            }
            System.out.print("------------" + ip);
        } catch (SocketException e) {
            e.printStackTrace();
        }
    }
}