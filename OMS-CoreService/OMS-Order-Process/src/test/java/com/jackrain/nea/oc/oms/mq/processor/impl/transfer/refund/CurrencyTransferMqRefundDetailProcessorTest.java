package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @ClassName CurrencyTransferMqRefundDetailProcessorTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/5 18:54
 * @Version 1.0
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest()
@Profile(value = "dev")
public class CurrencyTransferMqRefundDetailProcessorTest {

    @Autowired
    private CurrencyTransferMqRefundDetailProcessor transferMqRefundDetailProcessor;

    //
//    [{"channelType":"STANDPLAT","operateType":"TRANSFER_ORDER","orderId":1021,"orderNo":"BQ-GA-20220705133710","orderType":"REFUND"}],
//    Topic=BJ_DEV_R3_OC_OMS_CALL_TRANSFER, Tag=OperateMqOrder, MsgKey=d07ce54b-1c6f-4b02-917d-d4c0fae40547
    @Test
    public void testCurrencyTransferMqRefundDetailProcessor() {
        OperateOrderMqInfo mqInfo = new OperateOrderMqInfo();
        mqInfo.setChannelType(ChannelType.STANDPLAT);
        mqInfo.setOperateType(OperateType.TRANSFER_ORDER);
        mqInfo.setOrderId(1034L);
        mqInfo.setOrderNo("LPKSH2022719161001");
        mqInfo.setOrderType(OrderType.REFUND);
        transferMqRefundDetailProcessor.start(mqInfo);
    }
}
