package com.jackrain.nea.oc.oms.mq;

import com.aliyun.openservices.ons.api.*;

import java.util.Date;
import java.util.Properties;

/**
 * @author: 易邵峰
 * @since: 2019-03-08
 * create at : 2019-03-08 18:37
 */
//@RunWith(SpringJUnit4ClassRunner.class)
//@SpringBootTest()
//@Profile(value = "dev")
public class MQTest {

//    @Autowired
//    private OrderMessageSendHelper sendHelper;
//
//    @Test
//    public void test1() {
//        sendHelper.sendMessage("BJ_DEV_R3_OC_OMS_CALL_TRANSFER", "DDD", "XXX", "GGGGG");
//    }

//    public static void main(String[] args) {
//        Properties properties = new Properties();
//        // 您在控制台创建的 Group ID
//        properties.put(PropertyKeyConst.GROUP_ID, "GID_BJ_DEV_R3_OC_OMS_CALL_TRANSFER");
//        // AccessKey 阿里云身份验证，在阿里云服务器管理控制台创建
//        properties.put(PropertyKeyConst.AccessKey, "LTAIBv6UjgnGicl7");
//
//        // SecretKey 阿里云身份验证，在阿里云服务器管理控制台创建
//        properties.put(PropertyKeyConst.SecretKey, "flW9YK54qjYOdNU7TbqSEQjx0jsK41");
//        // 设置 TCP 接入域名，到控制台的实例基本信息中查看
//        properties.put(PropertyKeyConst.NAMESRV_ADDR,
//                "http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80");
//        // 集群订阅方式 (默认)
//        // properties.put(PropertyKeyConst.MessageModel, PropertyValueConst.CLUSTERING);
//        // 广播订阅方式
//        // properties.put(PropertyKeyConst.MessageModel, PropertyValueConst.BROADCASTING);
//        Consumer consumer = ONSFactory.createConsumer(properties);
//
//        //订阅另外一个 Topic
//        consumer.subscribe("BJ_DEV_R3_OC_OMS_CALL_TRANSFER", "*", new MessageListener() { //订阅全部 Tag
//            public Action consume(Message message, ConsumeContext context) {
//                System.out.println("Receive: " + message);
//                return Action.CommitMessage;
//            }
//        });
//        consumer.start();
//        System.out.println("Consumer Started");
//    }


    public static void main(String[] args) {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID_BJ_DEV_R3_OC_OMS_CALL_TRANSFER");
        // AccessKey 阿里云身份验证，在阿里云服务器管理控制台创建
        properties.put(PropertyKeyConst.AccessKey, "LTAIBv6UjgnGicl7");
        // SecretKey 阿里云身份验证，在阿里云服务器管理控制台创建
        properties.put(PropertyKeyConst.SecretKey, "flW9YK54qjYOdNU7TbqSEQjx0jsK41");
        //设置发送超时时间，单位毫秒
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, "3000");
        // 设置 TCP 接入域名，到控制台的实例基本信息中查看
        properties.put(PropertyKeyConst.NAMESRV_ADDR,
                "http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80");

        Producer producer = ONSFactory.createProducer(properties);
        // 在发送消息前，必须调用 start 方法来启动 Producer，只需调用一次即可
        producer.start();

        //循环发送消息
        for (int i = 0; i < 1; i++) {
            Message msg = new Message( //
                    // Message 所属的 Topic
                    "BJ_DEV_R3_OC_OMS_CALL_TRANSFER",
                    // Message Tag 可理解为 Gmail 中的标签，对消息进行再归类，方便 Consumer 指定过滤条件在 MQ 服务器过滤
                    "TagA",
                    // Message Body 可以是任何二进制形式的数据， MQ 不做任何干预，
                    // 需要 Producer 与 Consumer 协商好一致的序列化和反序列化方式
                    "Hello MQ".getBytes());
            // 设置代表消息的业务关键属性，请尽可能全局唯一。
            // 以方便您在无法正常收到消息情况下，可通过阿里云服务器管理控制台查询消息并补发
            // 注意：不设置也不会影响消息正常收发
            msg.setKey("ORDERID_" + i);

            try {
                SendResult sendResult = producer.send(msg);
                // 同步发送消息，只要不抛异常就是成功
                if (sendResult != null) {
                    System.out.println(new Date() + " Send mq message success. Topic is:" + msg.getTopic() + " msgId is: " + sendResult.getMessageId());
                }
            } catch (Exception e) {
                // 消息发送失败，需要进行重试处理，可重新发送这条消息或持久化这条数据进行补偿处理
                System.out.println(new Date() + " Send mq message failed. Topic is:" + msg.getTopic());
                e.printStackTrace();
            }
        }

        // 在应用退出前，销毁 Producer 对象
        // 注意：如果不销毁也没有问题
        producer.shutdown();
    }
}
