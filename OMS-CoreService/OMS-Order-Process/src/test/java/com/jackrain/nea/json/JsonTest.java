package com.jackrain.nea.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: 易邵峰
 * @since: 2019-03-20
 * create at : 2019-03-20 14:45
 */
public class JsonTest {

    @Test
    public void test1() {
        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
        orderMqInfo.setChannelType(ChannelType.TAOBAO);
        orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
        orderMqInfo.setOrderId(1);
        orderMqInfo.setOrderNo("XXX");
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        mqInfoList.add(orderMqInfo);
        String jsonValue = JSONObject.toJSONString(mqInfoList);

        List<OperateOrderMqInfo> mqOrderList = JSON.parseArray(jsonValue, OperateOrderMqInfo.class);
        System.out.println(mqInfoList.toString());
    }

}
