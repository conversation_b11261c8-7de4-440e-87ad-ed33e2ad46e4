package com.jackrain.nea.oc.oms.process.wms.providewms;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * 订单仓库发货服务
 * 订单仓库发货服务，主要作用场景， 当wms订单包裹发货后，
 * 通过接口调用我们的发货服务， 订单有配货中更新为仓库发货，
 * 库存由订单占用 到实际库存扣减，  订单状态更新是同时更新 物流公司，物流编号，包裹重量
 *
 * @author: 易邵峰
 * @since: 2019-01-18
 * create at : 2019-01-18 16:15
 */
@Component
public class ProvideWmsOrderProcess extends AbstractOrderProcess<OcBOrderRelation> {

    public ProvideWmsOrderProcess() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "";
    }

    @Override
    protected long getProcessOrderId(OcBOrderRelation orderInfo) {
        return orderInfo.getOrderInfo().getId();
    }

    @Override
    protected String getProcessOrderNo(OcBOrderRelation orderInfo) {
        return orderInfo.getOrderInfo().getSourceCode();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.PROVIDER_WMS_PROCESS;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.DEFAULT;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }

}