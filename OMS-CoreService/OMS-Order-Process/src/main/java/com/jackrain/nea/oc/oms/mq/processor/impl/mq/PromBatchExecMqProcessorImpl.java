package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderPromItemMapper;
import com.jackrain.nea.oc.oms.model.GiftInfo;
import com.jackrain.nea.oc.oms.model.ReleaseHoldMqInfo;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsSelectGiftService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单促销MQ批量处理
 *
 * @author: 胡林洋
 * @since: 2020-03-30
 * create at : 2020-03-30 11:08
 */
// fixme 继承自AbstractMqProcessor tag: pm_to_oms_hold
@Slf4j
@RocketMqMessageListener(name = "PromBatchExecMqProcessorImpl", type = MqTypeEnum.DEFAULT)
public class PromBatchExecMqProcessorImpl implements BaseMessageListener {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderPromItemMapper ocBOrderPromItemMapper;

    @Autowired
    private OmsSelectGiftService omsSelectGiftService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private RedisOpsUtil redisOpsUtil;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    /**
     * 有赠品
     */
    private static final String TYPE_ONE = "1";

    /**
     * 无赠品
     */
    private static final String TYPE_TWO = "2";

    /**
     *
     */
    private static final String FAIL_ROLLBACK_DATA_PREFIX = "FAIL_ROLLBACK_DATA_";

    /**
     * 过期时间
     */
    private static final int PROM_REDIS_TIMEOUT = 24;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object obj) {
        long startTime = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("促销MQ批量处理,请求报文: {}", messageKey, messageTopic, messageTag), messageBody);
        }
        String failRedisKey = FAIL_ROLLBACK_DATA_PREFIX + messageKey;
        if (redisOpsUtil.objRedisTemplate.hasKey(messageKey)) {
            if (!redisOpsUtil.objRedisTemplate.hasKey(failRedisKey)) {
                return;
            }
            if (log.isDebugEnabled()) {
                List<String> failList = redisOpsUtil.objRedisTemplate.opsForList().range(failRedisKey, 0, -1);
                log.debug(LogUtil.format("失败的数据failList: {}", messageKey), JSON.toJSONString(failList));
                redisOpsUtil.objRedisTemplate.delete(failRedisKey);
            }
        }
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        boolean promBatchExecMqProcessorTestSwitch = config.getPropertyBoolean("PromBatchExecMqProcessorTestSwitch");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("促销MQ批量处理.promBatchExecMqProcessorTestSwitch的值为: {}", messageKey), promBatchExecMqProcessorTestSwitch);
        }

        if (!promBatchExecMqProcessorTestSwitch) {
            try {
                List<ReleaseHoldMqInfo> mqHoldOrderList = JSON.parseArray(messageBody, ReleaseHoldMqInfo.class);
                if (CollectionUtils.isEmpty(mqHoldOrderList)) {
                    return;
                }

                batchReleaseHoldOrder(messageKey, mqHoldOrderList);
                // 失败的条数
                Long failRedisSize = redisOpsUtil.objRedisTemplate.opsForList().size(failRedisKey);

                redisOpsUtil.objRedisTemplate.opsForValue().set(messageKey, messageBody, PROM_REDIS_TIMEOUT, TimeUnit.HOURS);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("促销MQ批量处理结束. 处理总条数:{} 失败的条数：{} 处理时间：{}", messageKey), mqHoldOrderList.size(), failRedisSize, System.currentTimeMillis() - startTime);
                }

                if (failRedisSize != null && failRedisSize > 0) {
                    List<String> failList = redisOpsUtil.objRedisTemplate.opsForList().range(failRedisKey, 0, -1);
                    log.debug(LogUtil.format("处理失败的数据. 失败的条数：{} 数据：{}", messageKey), failRedisSize, JSON.toJSONString(failList));
                    throw new MqException("批量执行mq服务调用有异常处理数据，重新消费。failList: " + JSON.toJSONString(failList));
                }

                // 促销执行完成埋点
                log.info(LogUtil.format("Promotion.Order.Finished;OrderNum={}", messageKey), mqHoldOrderList.size());
            } catch (Exception ex) {
                log.error(LogUtil.format("PromBatchExecMqProcessorImpl.StartProcess: {}"), Throwables.getStackTraceAsString(ex));
                throw new MqException("处理内容非法" + ex.getMessage());
            }
        }
    }

    /**
     * 批量释放订单
     *
     * @param mqHoldOrderList
     */
    public void batchReleaseHoldOrder(String messageKey, List<ReleaseHoldMqInfo> mqHoldOrderList) {
        long startTime = System.currentTimeMillis();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("批量处理促销订单开始.处理条数:{}, mqHoldOrderList:{}", messageKey),
                    mqHoldOrderList.size(), JSON.toJSONString(mqHoldOrderList));
        }
        for (ReleaseHoldMqInfo releaseHoldMqInfo : mqHoldOrderList) {
            this.releaseHoldOrder(releaseHoldMqInfo, messageKey);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("批量处理促销订单结束, 处理时间:{}",
                    messageKey), System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 释放hold单
     *
     * @param releaseHoldMqInfo
     */
    public void releaseHoldOrder(ReleaseHoldMqInfo releaseHoldMqInfo, String messageKey) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        Long orderId = releaseHoldMqInfo.getOrderId();
        List<GiftInfo> giftList = releaseHoldMqInfo.getGiftList();
        if (CollectionUtils.isEmpty(giftList)){
            updateOcBOrderPromItem(orderId, releaseHoldMqInfo.getType(), releaseHoldMqInfo.getGroupNo(), releaseHoldMqInfo.getPmID(), releaseHoldMqInfo.getGiftList());
            /**
             * 是否释放hold单订单
             */
            boolean releaseHoldOrderFlag = omsSelectGiftService.isReleaseHoldOrder(orderId);
            if (releaseHoldOrderFlag) {
                OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
                toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
                toBeConfirmedTask.setOrderId(orderId);
                toBeConfirmedTask.setCreationdate(new Date());
                toBeConfirmedTask.setStatus(0);
                toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);
                return;
            }
        }
        String promCode = releaseHoldMqInfo.getPmCode();
        String promName = releaseHoldMqInfo.getPmName();
        Long pmID = releaseHoldMqInfo.getPmID();
        String groupNo = releaseHoldMqInfo.getGroupNo();
        // 1有赠品,2无赠品
        String type = releaseHoldMqInfo.getType();
        OcBOrder orderDB;
        List<OcBOrderItem> orderItemListDB;
        List<OcBOrderItem> giftOrderItemList;

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("单条处理促销开始.释放hold单.ReleaseHoldMqInfo:{}",
                    messageKey, orderId), JSON.toJSONString(releaseHoldMqInfo));
        }
        // 查询订单信息
        OcBOrderRelation ocBOrderRelationDB = omsOrderService.selectOmsOrderInfoOccupy(orderId);
        if (ocBOrderRelationDB == null) {
            log.error(LogUtil.format("订单不存在", messageKey, orderId));
            return;
        }
        orderDB = ocBOrderRelationDB.getOrderInfo();
        Integer orderStatus = orderDB.getOrderStatus();
        Integer occupyStatus = orderDB.getOccupyStatus();

        // 当前订单状态非待分配
        if (!OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("当前订单状态非待分配不做处理.orderStatus:{} occupyStatus：{}",
                        messageKey, orderId), orderStatus, occupyStatus);
            }
            omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(),
                    OrderLogTypeEnum.GIFT_ADD.getKey(),
                    "接收到促销中心添加赠品通知，但订单状态非待分配，不再继续添加赠品！",
                    null, null, SystemUserResource.getRootUser());
            return;
        }
        if (pmID != null) {
            int count = ocBOrderPromItemMapper.isExecutedPromotionByPromId(orderId, pmID);
            if (count > 0) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("赠品已经存在.promCode:{}, giftList:{}", messageKey, orderId), promCode, giftList);
                }
                omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(), OrderLogTypeEnum.GIFT_ADD.getKey(),
                        "接收到促销中心添加赠品通知，但订单赠品已经添加！promCode:"
                                + promCode + " giftList:" + JSON.toJSONString(giftList), null, null,
                        SystemUserResource.getRootUser());
                return;
            }
        }
        // groupNo不为空，代表为分组活动
        if (StringUtils.isNotEmpty(groupNo)) {
            int count = ocBOrderPromItemMapper.isExecutedPromotionByGroupNo(orderId, groupNo);
            if (count > 0) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("赠品已经存在.groupNo:{}, giftList:{}", messageKey, orderId), groupNo, giftList);
                }
                omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(), OrderLogTypeEnum.GIFT_ADD.getKey(),
                        "接收到促销中心添加赠品通知，但订单赠品已经添加！groupNo:" + groupNo + " giftList:"
                                + JSON.toJSONString(giftList), null, null, SystemUserResource.getRootUser());
                return;
            }
        }
        orderItemListDB = ocBOrderRelationDB.getOrderItemList();
        if (CollectionUtils.isEmpty(orderItemListDB)) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("未退款的订单明细不存在", messageKey, orderId));
            }
            omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(), OrderLogTypeEnum.GIFT_ADD.getKey(),
                    "接收到促销中心添加赠品通知，但未退款的订单明细不存在,不再继续添加赠品！", null, null,
                    SystemUserResource.getRootUser());
            return;
        }
        List<OcBOrderItem> notGifItemListDB = orderItemListDB.stream().filter(orderItem -> orderItem.getIsGift().equals(0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notGifItemListDB)) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("当前商品明细去除赠品后为空,不调用赠品服务", messageKey, orderId));
            }
            omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(),
                    OrderLogTypeEnum.GIFT_ADD.getKey(), "接收到促销中心添加赠品通知，判断当前商品明细去除赠品后为空,请人工确认当前订单是否异常！", null, null, SystemUserResource.getRootUser());
            return;
        }


        // 有赠品
        if (TYPE_ONE.equals(type) && !CollectionUtils.isEmpty(giftList)) {
            ocBOrderRelationDB.setGiftInfoList(giftList);
            // 获取赠品明细
            giftOrderItemList = omsSelectGiftService.getGiftOrderItemList(ocBOrderRelationDB, promName, promCode);
        } else if (TYPE_TWO.equals(type)) {
            giftOrderItemList = new ArrayList<>();
        } else {
            log.error(LogUtil.format("未知异常,type:{} giftList:{}", messageKey, orderId), type, JSON.toJSONString(giftList));
            omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(),
                    OrderLogTypeEnum.GIFT_ADD.getKey(), "接收到促销中心添加赠品通知，未知异常！", null, null, SystemUserResource.getRootUser());
            return;
        }

        /**
         * 更新数据库
         */
        try {
            long updateDataStartTime = System.currentTimeMillis();
            /**
             * 更新订单的促销明细
             */
            this.updateOcBOrderPromItem(orderId, releaseHoldMqInfo.getType(), releaseHoldMqInfo.getGroupNo(), releaseHoldMqInfo.getPmID(), releaseHoldMqInfo.getGiftList());
            PromBatchExecMqProcessorImpl promBatchExecMqProcessorImpl = ApplicationContextHandle.getBean(PromBatchExecMqProcessorImpl.class);
            promBatchExecMqProcessorImpl.updateData(releaseHoldMqInfo, orderDB, orderItemListDB, giftOrderItemList);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("更新数据库的数据, 处理时间:{}", messageKey, orderId), System.currentTimeMillis() - updateDataStartTime);
            }
        } catch (Exception e) {
            redisOpsUtil.objRedisTemplate.opsForList().leftPush(FAIL_ROLLBACK_DATA_PREFIX + messageKey, JSON.toJSONString(releaseHoldMqInfo));
            log.error(LogUtil.format("释放hold单-更新数据发生异常: {}", orderId), Throwables.getStackTraceAsString(e));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("单条处理促销结束, 处理时间:{}", messageKey, orderId), System.currentTimeMillis() - startTime);
        }
    }


    /**
     * 更新订单促销明细
     */
    private void updateOcBOrderPromItem(Long orderId, String type, String groupNo, Long promId, List<GiftInfo> giftList) {
        String prefix = "[" + this.getClass().getName() + "." + new Exception().getStackTrace()[0].getMethodName() + "].[";
        long startTime = System.currentTimeMillis();
        String isExecute = "N";
        String isActive = "Y";
        // 有赠品
        if (TYPE_ONE.equals(type)) {
            isExecute = "Y";
            // 无赠品
        } else if (TYPE_TWO.equals(type)) {
            isActive = "N";
        }
        // groupNo不为空，代表为分组活动
        if (StringUtils.isNotEmpty(groupNo)) {
            ocBOrderPromItemMapper.updateOcBOrderPromItemAndGiftInfoByGroupNo(isExecute, isActive, orderId, groupNo, JSONObject.toJSONString(giftList));
        } else {
            ocBOrderPromItemMapper.updateOcBOrderPromItemAndGiftInfo(isExecute, isActive, orderId, promId, JSONObject.toJSONString(giftList));
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("更新订单促销明细, 处理时间:{}", orderId), System.currentTimeMillis() - startTime);
        }
    }

    @Transactional(rollbackFor = {Exception.class})
    public void updateData(ReleaseHoldMqInfo releaseHoldMqInfo, OcBOrder orderDB, List<OcBOrderItem> orderItemListDB, List<OcBOrderItem> giftOrderItemList) {
        Long orderId = orderDB.getId();
        Integer occupyStatus;

        /**
         * 是否释放hold单订单
         */
        boolean releaseHoldOrderFlag = omsSelectGiftService.isReleaseHoldOrder(orderId);
        if (releaseHoldOrderFlag) {
            /**
             * 释放hold单订单时，插入占单任务表
             */
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(orderId);
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTaskService.insertToBeConfirmedTask(toBeConfirmedTask);

            /**
             * 更新订单信息
             */
            OcBOrder updateOrderInfo = new OcBOrder();
            if (giftOrderItemList.size() > 0) {
                /**
                 * 计算订单商品数量和商品种类
                 */
                List<OcBOrderItem> orderItem = new ArrayList<>(giftOrderItemList.size() + orderItemListDB.size());
                orderItem.addAll(giftOrderItemList);
                orderItem.addAll(orderItemListDB);
                omsSelectGiftService.totalOrderItemQtyAndSkuKindQty(orderItem, updateOrderInfo);

                boolean isCombination = giftOrderItemList.stream().anyMatch(o -> (o.getProType() != null && o.getProType() == SkuType.NO_SPLIT_COMBINE));
                if (isCombination) {
                    // 组合商品标
                    updateOrderInfo.setIsCombination(1);
                }
                // 更新赠品标
                updateOrderInfo.setIsHasgift(1);
            }
            updateOrderInfo.setId(orderId);
            omsOrderService.updateOrderInfo(updateOrderInfo);
        }

        if (TYPE_ONE.equals(releaseHoldMqInfo.getType()) && !CollectionUtils.isEmpty(releaseHoldMqInfo.getGiftList()) && giftOrderItemList.size() > 0) {
            /**
             * 更新订单明细的挂靠关系
             */
            omsSelectGiftService.updateGiftReleation(releaseHoldMqInfo.getGiftList(), orderItemListDB);
            /**
             * 插入赠品明细
             */
            omsSelectGiftService.batchInsertOrderItem(giftOrderItemList);

            // 插入操作日志
            omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(),
                    OrderLogTypeEnum.GIFT_ADD.getKey(), "添加赠品成功", null, null, SystemUserResource.getRootUser());
        } else {
            //add by 李芸 2020-10-21 判断赠品标
            List<OcBOrderItem> items = ocBOrderItemMapper.selectOrderItemListAndReturn(orderId);
            List<OcBOrderItem> giftItems = items.stream().filter(p -> p.getIsGift() == 1).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(giftItems)) {
                OcBOrder updateOrderInfo = new OcBOrder();
                updateOrderInfo.setId(orderId);
                updateOrderInfo.setIsHasgift(1);
                omsOrderService.updateOrderInfo(updateOrderInfo);
            }
            //add end by 李芸 2020-0=10-21
            // 插入操作日志
            omsOrderLogService.addUserOrderLog(orderId, orderDB.getBillNo(),
                    OrderLogTypeEnum.GIFT_ADD.getKey(), "无赠品添加", null, null, SystemUserResource.getRootUser());
        }
    }


}
