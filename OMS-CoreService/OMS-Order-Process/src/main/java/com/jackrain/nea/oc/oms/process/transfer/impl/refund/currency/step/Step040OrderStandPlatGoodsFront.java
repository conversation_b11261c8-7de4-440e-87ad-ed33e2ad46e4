package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.services.OmsBeforeShipmentReturnService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 8:21 下午
 * @Version 1.0
 */
@Step(order = 40, description = "发货前处理")
@Slf4j
@Component
public class Step040OrderStandPlatGoodsFront extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsBeforeShipmentReturnService omsBeforeShipmentReturnService;


    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Step040OrderStandPlatGoodsFront.startProcess.orderInfo:{};",
                    "Step040OrderStandPlatGoodsFront"), JSON.toJSONString(orderInfo));
        }
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        try {
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            OcBOrder ocBOrder = omsOrderRelation.get(0).getOcBOrder();
            int success = 0;
            int statusNo = 0;
            boolean flag = false;
            int goodsFrontCount = 0;
            List<OmsOrderRelation> orderRelations = new ArrayList<>();

            for (int i = 0; i < omsOrderRelation.size(); i++) {
                try {
                    OmsOrderRelation orderRelation = omsOrderRelation.get(i);
                    OcBOrder order = orderRelation.getOcBOrder();
                    Integer orderStatus = order.getOrderStatus();
                    String suffixInfo = ocBOrder.getSuffixInfo();
                    Integer orderMark = orderRelation.getOrderMark();
                    //判断订单是否是退款成功作废的订单
                    boolean voidOrder = (OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                            || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) && "REFUND-VOID".equals(suffixInfo);
                    if (voidOrder) {
                        success++;
                        continue;
                    }
                    if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode().equals(orderMark)) {
                        Boolean shipmentReturn = this.beforeShipmentReturn(orderRelation, ipBStandplatRefund, operateUser);
                        if (shipmentReturn == null) {
                            statusNo++;
                            continue;
                        }

                        if (shipmentReturn) {
                            goodsFrontCount++;
                        }
                        //记录原明细 - 》执行赠品后发
                        orderRelations.add(orderRelation);
                        success++;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    flag = true;
                    log.error(LogUtil.format("发货前退款异常,退款单号:{}, 异常信息:{}", "发货前退款异常"), ipBStandplatRefund.getReturnNo(),Throwables.getStackTraceAsString(e));
                    if (e != null && SysNotesConstant.SYS_REMARK46 == e.getMessage()) {
                        TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DE_AUDIT);
                    }
                    ipStandplatRefundService.updateRefundIsTransError(ipBStandplatRefund, e.getMessage());
                }
            }
            if (flag) {
                //存在多个订单时 只要有一单在发货前处理失败 就结束转换
                return new ProcessStepResult<>(StepStatus.FAILED, "发货前退款失败!");
            }

            log.info("orderSourceCode:{},goodsFrontCount:{},success:{},参数：size:{}", ocBOrder.getSourceCode(),
                    goodsFrontCount, success, omsOrderRelation.size());

            // 原有逻辑的情况下, 添加赠品订单Hold
            Integer returnStatus =ipBStandplatRefund.getReturnStatus();
            int commRefundStatus = NumUtil.toInt(TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.toLong());
            boolean isWaitAgree = Objects.nonNull(returnStatus) && commRefundStatus == returnStatus ;
            if (isWaitAgree){
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("等待卖家同意,赠品订单流程,发货前Hold单", ipBStandplatRefund.getReturnNo()));
                }
                if (CollectionUtils.isNotEmpty(orderInfo.getIsGiftOrderRelation())){
                    for (OmsOrderRelation orderRelation : orderInfo.getIsGiftOrderRelation()) {
                        omsBeforeShipmentReturnService.waitSellerAgreeGiftOrderHold(orderRelation, operateUser);
                    }
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK17, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "发货前退款,等待卖家同意,订单处理完成");
                }
            }

            if (goodsFrontCount == omsOrderRelation.size()) {
                standPlatAutoRefundService.foundRefundFrontRefundOnly(ocBOrder, ipBStandplatRefund, operateUser);
                omsReturnOrderService.giftsThenSend(orderRelations,orderInfo.getIsGiftOrderRelation(),orderInfo.getIntermediateTableRelation(),operateUser);

                //全部退款成功后 调用AG
                return new ProcessStepResult<>(StepStatus.SUCCESS, "订单处理成功,进入下一阶段", Step080ToStanplatAutoRefund.class);
            }

            if (success == omsOrderRelation.size()) {

                omsStandPlatRefundOrderService.foundRefundFrontRefundOnly(ocBOrder, ipBStandplatRefund, operateUser);
                omsReturnOrderService.giftsThenSend(orderRelations,orderInfo.getIsGiftOrderRelation(),orderInfo.getIntermediateTableRelation(),operateUser);

                TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
                // 拼多多不结束，调用退款接口
                if (PlatFormEnum.PINDUODUO.getCode().equals(ocBOrder.getPlatform())) {
                    return new ProcessStepResult<>(StepStatus.SUCCESS, "发货前退款,订单处理完成", Step080ToStanplatAutoRefund.class);
                }

                String remark = SysNotesConstant.SYS_REMARK17;
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBStandplatRefund);

                return new ProcessStepResult<>(StepStatus.FINISHED, "发货前退款,订单处理完成");
            }
            if (statusNo == omsOrderRelation.size()) {
                String remark = SysNotesConstant.SYS_REMARK27;
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "发货前退款,状态不满足,退出转换");
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "订单处理成功,进入下一阶段");

        } catch (Exception e) {
            ipStandplatRefundService.updateRefundIsTransError(ipBStandplatRefund, e.getMessage());
            log.error(LogUtil.format("发货前退款异常,退款单号:{}, 异常信息:{}", "发货前退款异常"), ipBStandplatRefund.getReturnNo(),Throwables.getStackTraceAsString(e));
            return new ProcessStepResult<>(StepStatus.FAILED, "发货前退款处理失败!" + e.getMessage());
        }
    }

    /**
     * 处理发货前的逻辑
     *
     * @param orderRelation
     */
    private Boolean beforeShipmentReturn(OmsOrderRelation orderRelation, IpBStandplatRefund
            ipBStandplatRefund, User user) {
        //将订单拦截
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
        //订单状态为待分配时暂不处理，防止拆单，子单标识不正确
        if(OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(ocBOrder.getOrderStatus())){
            this.sendDelayMq(ipBStandplatRefund);
            throw new NDSException("订单状态为待分配,等待下次转换");
        }else if(OmsOrderStatus.OCCUPY_IN.toInteger().equals(ocBOrder.getOrderStatus())){
            this.sendDelayMq(ipBStandplatRefund);
            throw new NDSException("订单状态为寻源中,等待下次转换");
        }
        // HOLD单
        OcBOrder order = new OcBOrder();
        //是否退款中
        order.setIsInreturning(1);
        order.setId(ocBOrder.getId());
        omsOrderService.updateOrderInfo(order);
        //是否已经拦截 Hold单统一调用 HOLD单方法
        order.setIsInterecept(1);
        order.setBillNo(ocBOrder.getBillNo());
        ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
        //淘宝退单中间表的退单状态
        Integer returnStatus = ipBStandplatRefund.getReturnStatus();
        String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
        //淘宝退单中间表的退单状态
        if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)) {
            //买家已经申请退款，等待卖家同意
            return omsBeforeShipmentReturnService.taoBaoRefundStatusAgree(orderRelation, user);
        } else if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)
                || TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(status)) {
            //退款成功
            omsBeforeShipmentReturnService.refundStatusIsSuccess(orderRelation, user);
            return false;
        } else if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status) || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(status)) {
            //卖家拒绝退款、退款关闭
            omsBeforeShipmentReturnService.orderStatusIsClosed(orderRelation, user);
            return false;
        }
        return null;
    }

    /**
     * 延时1分钟再次发送退单转换mq
     *
     * @param ipBStandplatRefund
     */
    private void sendDelayMq(IpBStandplatRefund ipBStandplatRefund)  {
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
        orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
        orderMqInfo.setChannelType(ChannelType.STANDPLAT);
        orderMqInfo.setOrderType(OrderType.REFUND);
        orderMqInfo.setOrderNo(ipBStandplatRefund.getReturnNo());
        mqInfoList.add(orderMqInfo);
        String jsonValue = JSONObject.toJSONString(mqInfoList);
        try {
            log.info("Step040OrderStandPlatGoodsFront.beforeShipmentReturn.sendDelayMq");
            defaultProducerSend.sendDelayTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_TRANSFER, Mq5Constants.TAG_OPERATEMQORDER, jsonValue, null, 60000L);
        } catch (MqException e) {
            e.printStackTrace();
        }
    }
}
