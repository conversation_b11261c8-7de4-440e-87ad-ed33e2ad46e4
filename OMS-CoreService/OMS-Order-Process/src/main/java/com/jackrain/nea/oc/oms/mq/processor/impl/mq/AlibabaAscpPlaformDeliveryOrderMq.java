package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderSource;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsUpdatePlatformDeliveryStatusService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.util.OcBOrderDeliveryFailUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 猫超平台发货接收云枢纽回传MQ
 * 废弃 by haiyang 20231130
 */
@Deprecated
@Slf4j
@RocketMqMessageListener(name = "AlibabaAscpPlaformDeliveryOrderMq", type = MqTypeEnum.DEFAULT)
public class AlibabaAscpPlaformDeliveryOrderMq implements BaseMessageListener {
    @Autowired
    private OmsUpdatePlatformDeliveryStatusService omsUpdatePlatformDeliveryStatusService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;
    @Autowired
    private OcBOrderDeliveryFailUtil deliveryFailUtil;
    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        User user = SystemUserResource.getRootUser();
        try {
//            String messageBody = MsgConvertUtil.objectDeserialize(message.getBody()).toString();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("猫超平台发货接收云枢纽回传MQ:{}", messageKey), messageBody);
            }
            JSONArray objectArray = JSON.parseArray(messageBody);
            JSONObject jsonObject = JSON.parseObject(objectArray.get(0).toString());
            boolean result = jsonObject.getBoolean("issuccess");
            Long orderId = jsonObject.getLong("ID");
            String msg = "";
            String remark = "";
            if (!result) {
                msg = jsonObject.getString("error_msg");
                remark = "平台发货接口返回状态为：失败";
                ocBOrderMapper.updateMasterFailTimesById(orderId);
            } else {
                remark = "平台发货接口返回状态为：成功";
            }
            OcBOrder ocBOrder = omsOrderService.selectOrderInfo(orderId);
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())) {
                log.error(LogUtil.format("猫超平台发货，订单状态已经是平台发货，不再进行发货", orderId));
                return;
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), ocBOrder.getId() + "接收到ASCP平台发货MQ" + remark + msg, null, msg, user);

            if (ocBOrder.getOrderType().equals(OrderTypeEnum.NORMAL.getVal())
                    && ocBOrder.getIsMerge() == 0 && (ocBOrder.getIsSplit() == 0 || ocBOrder.getIsSplit() == 2)
                    && !ocBOrder.getOrderSource().equals(OmsOrderSource.MANUAL_ADD.getEcode())) {
                //正常订单平台发货
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("正常订单平台发货normalRequestUpdate开始",orderId));
                }
                normalRequestUpdate(result, ocBOrder, msg);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("正常订单平台发货normalRequestUpdate结束",orderId));
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("AlibabaAscpPlaformDeliveryOrderMq.consume.ExpMsg: {}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }

    private void normalRequestUpdate(boolean result, OcBOrder ocBOrder, String msg) {
        if (result) {
            //更新子表方法
            omsUpdatePlatformDeliveryStatusService.updateSublistOrderStatus(ocBOrder);
            //更新主表方法
            omsUpdatePlatformDeliveryStatusService.updateMasterOrderStatus(ocBOrder);
            log.debug("开始标记入参：" + JSONObject.toJSONString(ocBOrder));
            OcBOrder ocBOrderflag = new OcBOrder();
            ocBOrderflag.setId(ocBOrder.getId());
            ocBOrderflag.setIsForce(1L);
            omsOrderService.updateOrderInfo(ocBOrder);
            //作废退单
            orderPlatformDeliveryService.updateReturnOrder(ocBOrder);
        } else {
            ocBOrder.setIsForce(0L);
            ocBOrder.setForceSendFailReason(msg);
            ocBOrder.setSysremark(msg);
            omsOrderService.updateOrderInfo(ocBOrder);
            //调用更新失败次数接口【批量更新明细失败次数】
            ocBOrderItemMapper.updateFailTimesByOrderid(ocBOrder.getId());
            deliveryFailUtil.addOcBOrderDeliveryFail(ocBOrder);
        }
    }
}
