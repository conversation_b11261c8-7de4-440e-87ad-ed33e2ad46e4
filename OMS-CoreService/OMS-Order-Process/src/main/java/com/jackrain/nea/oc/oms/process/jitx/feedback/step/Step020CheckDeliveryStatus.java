package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.jitx.JitxDeliveryStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 判断待寻仓订单单据反馈状态
 *
 * @author: chenxiulou
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 20, description = "判断寻仓订单据状态")
@Slf4j
@Component
public class Step020CheckDeliveryStatus extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation deliveryInfo,
                                                                  ProcessStepResult preStepResult,
                                                                  boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxDeliveryRelation> stepResult = new ProcessStepResult<>();
        String orderStatus = deliveryInfo.getJitxDelivery().getStatus();
        boolean isJitNewStatus = JitxDeliveryStatus.NEW.equalsIgnoreCase(orderStatus);
        boolean isJitConfirmStatus = JitxDeliveryStatus.CONFIRMING.equalsIgnoreCase(orderStatus);
        boolean isJitRollBackStatus = JitxDeliveryStatus.ROLLBACK.equalsIgnoreCase(orderStatus);

        String orderNo = deliveryInfo.getOrderNo();
        long orderId = deliveryInfo.getOrderId();
        if (isJitConfirmStatus) {
            String operateMessage = "订单状态=" + orderStatus + ";寻仓确认中。进入下一阶段设置同步成功状态。OrderId=" + orderId
                    + ";OrderNo=" + orderNo;
            deliveryInfo.setRemarks("订单状态=寻仓确认中,已同步，不处理;");
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.FINISHED);
        } else if (isJitRollBackStatus) {
            String operateMessage = "订单状态=" + orderStatus + ";寻仓确认为非JITX。进入下一阶段设置同步成功状态。OrderId=" + orderId
                    + ";OrderNo=" + orderNo;
            deliveryInfo.setRemarks("订单状态=寻仓确认为非JITX,已同步，不处理;");
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.FINISHED);
            ipJitxDeliveryService.updateJitxSyncStatus(orderNo, SyncStatus.SYNCFAILD, "订单状态=寻仓确认为非JITX，更新反馈状态为JIT");
        } else if (isJitNewStatus) {
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setMessage("订单状态=" + orderStatus + "" + ";新建。单据编号=" + orderNo + "，进入下一阶段");
        } else {
            String operateMessage = "订单状态=" + orderStatus + ";寻仓确认为其他状态。进入下一阶段设置同步成功状态。OrderId=" + orderId
                    + ";OrderNo=" + orderNo;
            deliveryInfo.setRemarks("寻仓确认为其他状态:" + orderStatus + ",无法反馈线上");
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.FINISHED);
        }

        return stepResult;


    }
}
