package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/7  18:10
 * @description: 中间表转换到退换货订单服务
 */
@Component
public class TaobaoTransferExchangeProcessImpl extends AbstractOrderProcess<OmsTaobaoExchangeRelation> {
    public TaobaoTransferExchangeProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "taobao";
    }

    @Override
    protected long getProcessOrderId(OmsTaobaoExchangeRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(OmsTaobaoExchangeRelation orderInfo) {
        return orderInfo.getOrderNo();
    }


    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_TAOBAO_EXCHANGE;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }
}
