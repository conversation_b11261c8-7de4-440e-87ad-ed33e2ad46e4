package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.AbnormalTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Description： 物流公司校验（当前仅针对速卖通）
 * Author: RESET
 * Date: Created in 2020/7/23 16:30
 * Modified By:
 */
@Slf4j
@Step(order = 30, description = "校验物流信息是否存在")
@Component
public class Step030StandplatCheckLogisticsCompany extends BaseStandplatOrderProcessStep
        implements IOmsOrderProcessStep<IpStandplatOrderRelation> {

    @Autowired
    CpRpcService cpRpcService;

    /**
     * 开始处理逻辑
     *
     * @param orderInfo     订单信息
     * @param preStepResult 上一阶段处理逻辑结果
     * @param isAutoMakeup  是否为自动补偿服务
     * @param operateUser   操作用户
     * @return 处理结果
     */
    @Override
    public ProcessStepResult<IpStandplatOrderRelation> startProcess(IpStandplatOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        // 取物流公司
        if (Objects.nonNull(orderInfo) && Objects.nonNull(orderInfo.getStandplatOrder())) {
            if (orderInfo.isAliExpress()) {
                // 速卖通，需要判断物流公司是否存在
                String logisticsCompany = orderInfo.getStandplatOrder().getLogisticscompany();
                boolean exist = false;

                if (Objects.nonNull(logisticsCompany)) {
                    // 查询物流公司档案
                    LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfo(logisticsCompany);

                    if (Objects.nonNull(logisticsInfo)) {
                        exist = true;
                    }
                }

                if (!exist) {
                    // 有，正常转单，无，转单失败，标识转单失败，错误原因“物流公司不存在”
                    // 更新中间表
                    String remarks = "转单失败：物流公司不存在";
                    ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(), TransferOrderStatus.TRANSFEREXCEPTION, remarks, AbnormalTypeEnum.OTHERS.getKey());
                    return new ProcessStepResult<>(StepStatus.FINISHED, Resources.getMessage(remarks));
                }
            }
        }

        String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "校验物流公司是否存在完成，进入下一阶段");
        return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
    }
}
