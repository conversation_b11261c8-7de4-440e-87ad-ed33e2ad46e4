package com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 占单
 * @Date 2019-9-2
 **/
@Step(order = 50, description = "占单")
@Slf4j
@Component
public class Step050TimeOrderOccupyStock extends BaseVipTimeOrderProcessStep
        implements IOmsOrderProcessStep<IpVipTimeOrderRelation> {
    @Override
    public ProcessStepResult<IpVipTimeOrderRelation> startProcess(IpVipTimeOrderRelation timeOrderInfo
            , ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTimeOrderVip ipBTimeOrderVip = timeOrderInfo.getIpBTimeOrderVip();
        String sysRemark = SysNotesConstant.SYS_REMARK0;
        try {
            ipVipTimeOrderService.updateTimeOrderData(ipBTimeOrderVip,TimeOrderVipStatusEnum.IN_SINGLE.getValue(),TransferOrderStatus.TRANSFERRING.toInteger(),null,"转换中！",true);
            ValueHolderV14 valueHolderV14 = ipVipTimeOrderService.timeOrderFindSourceStrategyAndOccupyStock(timeOrderInfo, operateUser);
            if (valueHolderV14.isOK()) {
                return new ProcessStepResult<>(StepStatus.SUCCESS, "转单中！");
            } else {
                //调用寻源占用失败
                ipVipTimeOrderService.updateTimeOrderData(ipBTimeOrderVip,TimeOrderVipStatusEnum.CREATED.getValue(),TransferOrderStatus.TRANSFERFAIL.toInteger(),null,sysRemark+valueHolderV14.getMessage(),false);
                return new ProcessStepResult<>(StepStatus.FAILED, "单据" + timeOrderInfo.getBillNo() + "占单消息发送失败！");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("时效订单转换异常,异常信息:{}", "时效订单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipVipTimeOrderService.updateTimeOrderData(ipBTimeOrderVip,TimeOrderVipStatusEnum.CREATED.getValue(),TransferOrderStatus.TRANSFERFAIL.toInteger(),null,sysRemark+e.getMessage(),false);
            String errorMessage = "时效订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
