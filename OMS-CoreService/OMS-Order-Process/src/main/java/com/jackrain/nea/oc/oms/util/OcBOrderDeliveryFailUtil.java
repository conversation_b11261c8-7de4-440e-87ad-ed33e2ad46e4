package com.jackrain.nea.oc.oms.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryFailMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapperservice.OcBOrderDeliveryFailMapperService;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDeliveryFail;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @ClassName OcBOrderDeliveryFailUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/4/12 16:17
 * @Version 1.0
 */
@Component
@Slf4j
public class OcBOrderDeliveryFailUtil {

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderDeliveryFailMapper deliveryFailMapper;
    @Autowired
    private OcBOrderDeliveryFailMapperService deliveryFailMapperService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;


    public OcBOrderDeliveryFail addOcBOrderDeliveryFail(OcBOrder ocBOrder) {
        try {
            List<OcBOrderDelivery> ocBOrderDeliveryList = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
            if (CollectionUtil.isEmpty(ocBOrderDeliveryList)) {
                return null;
            }
            // 校验之前是否已存在 如果已存在 则更新
            List<OcBOrderDeliveryFail> ocBOrderDeliveryFailList = deliveryFailMapper.selectOrderDeliveryFailByOrderId(ocBOrder.getId());
            if (CollectionUtil.isEmpty(ocBOrderDeliveryFailList)) {
                OcBOrderDelivery ocBOrderDelivery = ocBOrderDeliveryList.get(0);
                OcBOrderDeliveryFail deliveryFail = new OcBOrderDeliveryFail();
                deliveryFail.setId(sequenceUtil.buildDeliveryFailId());
                deliveryFail.setOcBOrderId(ocBOrder.getId());
                deliveryFail.setTid(ocBOrder.getTid());
                deliveryFail.setCpCShopId(ocBOrder.getCpCShopId());
                deliveryFail.setCpCShopEcode(ocBOrder.getCpCShopEcode());
                deliveryFail.setCpCShopTitle(ocBOrder.getCpCShopTitle());
                deliveryFail.setCpCLogisticsId(ocBOrderDelivery.getCpCLogisticsId());
                deliveryFail.setCpCLogisticsEcode(ocBOrderDelivery.getCpCLogisticsEcode());
                deliveryFail.setCpCLogisticsEname(ocBOrderDelivery.getCpCLogisticsEname());
                deliveryFail.setLogisticNumber(ocBOrderDelivery.getLogisticNumber());
                // 设置6小时之后
                deliveryFail.setNextTime(DateUtil.offsetHour(new Date(), 6));
                deliveryFail.setRetryNumber(0);
                deliveryFail.setPlatform(ocBOrder.getPlatform());
                deliveryFail.setStatus(0);
                BaseModelUtil.initialBaseModelSystemField(deliveryFail);
                deliveryFailMapper.insert(deliveryFail);
                return deliveryFail;
            }
            OcBOrderDeliveryFail ocBOrderDeliveryFail = ocBOrderDeliveryFailList.get(0);
            OcBOrderDeliveryFail updateOcBOrderDeliveryFail = new OcBOrderDeliveryFail();
            updateOcBOrderDeliveryFail.setModifieddate(new Date());
            updateOcBOrderDeliveryFail.setId(ocBOrderDeliveryFail.getId());
            updateOcBOrderDeliveryFail.setOcBOrderId(ocBOrderDeliveryFail.getOcBOrderId());
            updateOcBOrderDeliveryFail.setRetryNumber(ocBOrderDeliveryFail.getRetryNumber() + 1);
            updateOcBOrderDeliveryFail.setNextTime(DateUtil.offsetHour(new Date(), 6));
            updateOcBOrderDeliveryFail.setStatus(0);
            deliveryFailMapperService.updateById(updateOcBOrderDeliveryFail);
            return updateOcBOrderDeliveryFail;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("保存重试订单信息失败:", Throwables.getStackTraceAsString(e)));
            return null;
        }
    }
}
