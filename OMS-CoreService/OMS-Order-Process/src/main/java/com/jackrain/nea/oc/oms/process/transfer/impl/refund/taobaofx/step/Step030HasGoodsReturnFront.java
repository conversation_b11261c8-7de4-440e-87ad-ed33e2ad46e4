package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.IpTaobaoFxRefundSendBeforeService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 发货前退款判断
 *
 * @author: 周琳胜
 * @since: 2019-07-16
 * create at : 2019-07-16 00:44
 */
@Step(order = 30, description = "发货前退款判断")
@Slf4j
@Component
public class Step030HasGoodsReturnFront extends BaseTaobaoFxRefundProcessStep
        implements IOmsOrderProcessStep<IpTaobaoFxRefundRelation> {
    @Autowired
    private IpTaobaoFxRefundSendBeforeService ipTaobaoFxRefundSendBeforeService;

    @Override
    public ProcessStepResult<IpTaobaoFxRefundRelation> startProcess(IpTaobaoFxRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        //【1,待审核】【2,缺货】 【 3,已审核】【9,预售】 【50,待分配】【21,传wms中】【4,配货中】
        log.debug("TaobaoFxReturnTransferOrder.step03" + orderInfo);
        OcBOrder ocBOrder = orderInfo.getOcBOrder();
        IpBTaobaoFxRefund taobaoFxRefund = orderInfo.getTaobaoFxRefund(); //是否退货
        try {

            if (ocBOrder != null) {
                //主表订单状态
                Integer orderStatus = ocBOrder.getOrderStatus();
                if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                        || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                        || OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                        || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)
                        || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)
                        || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus)
                        || OmsOrderStatus.PRE_SALE.toInteger().equals(orderStatus)) {
                    boolean flag = ipTaobaoFxRefundService.orderStatusHasGoodReturnStatusFront(taobaoFxRefund);
                    if (flag) {
                        //调用【发货前退退款转换服务】
                        ValueHolderV14 vh = ipTaobaoFxRefundSendBeforeService.beforeShipmentTransfer(orderInfo, operateUser);
                        if (vh.isOK()) {
                            return new ProcessStepResult<>(StepStatus.FINISHED, vh.getMessage());
                        } else {
                            return new ProcessStepResult<>(StepStatus.FAILED, vh.getMessage());
                        }
                    }
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 退单转换异常!", e);
            //修改中间表状态及系统备注
            ipTaobaoFxRefundService.updateRefundIsTransError(taobaoFxRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
