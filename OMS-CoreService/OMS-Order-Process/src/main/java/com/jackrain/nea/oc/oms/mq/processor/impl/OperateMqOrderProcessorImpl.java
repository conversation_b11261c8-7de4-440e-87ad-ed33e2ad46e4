package com.jackrain.nea.oc.oms.mq.processor.impl;

import com.alibaba.fastjson.JSON;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.util.RuntimeCompute;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.ServiceLoader;

/**
 * 处理订单MQ消息处理器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @author: 易邵峰
 * @since: 2019-02-23
 * create at : 2019-02-23 18:08
 */
@Slf4j
@RocketMqMessageListener(name = "OperateMqOrderProcessorImpl", type = MqTypeEnum.DEFAULT)
public class OperateMqOrderProcessorImpl implements BaseMessageListener {

    @Autowired
    private RuntimeCompute runtimeCompute;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * OrderProcessor 列表
     */
    private final List<IMqOrderDetailProcessor> processorList = new ArrayList<>();

    private static final List<String> listenedTags = Lists.newArrayList("OperateMqOrder");


    @PostConstruct
    public void initialMqOrderProcessor() {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OperateMqOrderProcessorImpl.Start.Construct"));
        }

        ServiceLoader<IMqOrderDetailProcessor> orderProcessLoader = ServiceLoader.load(IMqOrderDetailProcessor.class);
        Iterator<IMqOrderDetailProcessor> orderProcessIterator = orderProcessLoader.iterator();

        while (orderProcessIterator.hasNext()) {
            IMqOrderDetailProcessor orderProcessInterface = orderProcessIterator.next();
            applicationContext.getAutowireCapableBeanFactory().autowireBean(orderProcessInterface);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Start.Load.IMqOrderDetailProcessor.Name={}"), orderProcessInterface.getClass().getName());
            }
            processorList.add(orderProcessInterface);
        }

        log.info("initialMqOrderProcessor.processorList.size: {}", processorList.size());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OperateMqOrderProcessorImpl.Finish.Construct"));
        }
    }

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.info("OperateMqOrderProcessorImpl.topic-tag-key-body: {}-{}-{}-{}", messageTopic, messageTag, messageKey, messageBody);
        if (!listenedTags.contains(messageTag)) {
            return;
        }
        try {
            messageBody = this.formatValidMqMessageBody(messageBody);

            List<OperateOrderMqInfo> mqOrderList = JSON.parseArray(messageBody, OperateOrderMqInfo.class);

            if (CollectionUtils.isEmpty(mqOrderList)) {
                return;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OperateMqOrderProcessorImpl.Start.Process.Consumed.Number={}"), mqOrderList.size());
            }

            runtimeCompute.startRuntime();

            this.startExecuteOrderProcessor(mqOrderList);

            double bigUsedTime = runtimeCompute.endRuntime();

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OperateMqOrderProcessorImpl.Finished.Process.UsedTime={};Number={}"), bigUsedTime, mqOrderList.size());
            };
        } catch (Exception ex) {
            log.error(LogUtil.format("OperateMqOrderProcessorImpl.StartProcess Error: {}"), Throwables.getStackTraceAsString(ex));
            throw new MqException(ex);
        }
    }

    /**
     * 开始多线程运行处理
     *
     * @param mqOrderList 订单列表
     */
    private void startExecuteOrderProcessor(List<OperateOrderMqInfo> mqOrderList) {
        for (OperateOrderMqInfo orderMqInfo : mqOrderList) {
            try {
                for (IMqOrderDetailProcessor orderProcess : this.processorList) {
                    if (orderProcess.checkMqIsCanExecute(orderMqInfo)) {
                        orderProcess.start(orderMqInfo);
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("OperateMqOrderProcessorImpl.StartProcess.Detail.Error.OrderMqInfo={},ERROR: {}")
                        , orderMqInfo, Throwables.getStackTraceAsString(ex));
            }
        }
    }

    /**
     * 格式化MQ消息体内容，防止不是Array类型的JSON格式数据
     *
     * @param messageBody 消息体内容
     * @return 格式化Array类型的JSON数据
     */
    private String formatValidMqMessageBody(String messageBody) {
        String afterFormatMsg = messageBody;
        if (!afterFormatMsg.startsWith("[")) {
            afterFormatMsg = "[" + afterFormatMsg;
        }
        if (!afterFormatMsg.endsWith("]")) {
            afterFormatMsg = afterFormatMsg + "]";
        }

        return afterFormatMsg;
    }

}
