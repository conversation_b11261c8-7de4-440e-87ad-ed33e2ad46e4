package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongCancelRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.MultiThreadOrderProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jdcancel.JingdongcancelTransferProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJingdongSaRefundService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 京东取消订单转单消息处理器
 * @author: 郑小龙
 * @date: 2020-06-04 10:24
 **/
@Slf4j
public class JingdongCancelTransferMqRefundDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpJingdongSaRefundService saRefundService;
    @Autowired
    private JingdongcancelTransferProcessImpl jingdongcancelTransferProcess;
    @Autowired
    protected MultiThreadOrderProcessor threadOrderProcessor;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        Long micTime = BllCommonUtil.getmicTime();
        String orderNo = orderMqInfo.getOrderNo();
        String orderIds = orderMqInfo.getOrderIds();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东取消订单转单消息处理器.Start.orderIds:{}", orderNo), orderIds);
        }
        long starTime = System.currentTimeMillis();
        try {
            List<String> applyids = new ArrayList<>();
            if (StringUtils.isNotEmpty(orderNo)) {
                applyids.add(orderNo);
            }
            if (StringUtils.isNotEmpty(orderIds)) {
                String[] ids = orderIds.split(",");
                applyids = java.util.Arrays.asList(ids);//字符串转为list
            }
            //去重
            applyids = applyids.stream().distinct().collect(Collectors.toList());
            List<IpJingdongCancelRelation> relations = new ArrayList<>();
            for (String id : applyids) {
                Long applyid = Long.valueOf(id);
                IpJingdongCancelRelation ipTaobaoRefundRelation = saRefundService.getJingdongCancelSaRefund(applyid);
                if (ipTaobaoRefundRelation != null) {
                    relations.add(ipTaobaoRefundRelation);
                } else {
                    String errorMessage = Resources.getMessage(" jingdongcancel Received OrderMqInfo Not Exist!ApplyId=" + applyid);
                    log.error(LogUtil.format(errorMessage, orderNo));
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.jingdongcancelTransferProcess, relations);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("京东取消订单转单消息处理器耗时.耗时:{}ms", orderNo), Time);
            }
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("猫超直发退款消息处理器.异常: {}"), Throwables.getStackTraceAsString(e));
            }
            e.printStackTrace();
        }
        return new ProcessStepResultList();
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.JINGDONG
                && orderMqInfo.getOrderType() == OrderType.CANCELORDER;
    }
}
