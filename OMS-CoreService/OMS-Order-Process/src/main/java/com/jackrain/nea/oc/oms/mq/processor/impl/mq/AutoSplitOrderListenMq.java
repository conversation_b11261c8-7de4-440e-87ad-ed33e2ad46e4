package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.services.OmsOrderAutoSplitByStockService;
import com.jackrain.nea.oc.oms.services.OmsOrderAutoSplitService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自动拆单监听MQ
 *
 * @author: 胡林洋
 * @since: 2019/7/31
 * create at : 2019/7/31 16:14
 * 废弃 by haiyang 20231129
 */
@Deprecated
@Slf4j
@Component
public class AutoSplitOrderListenMq implements MessageListener {

    @Autowired
    private OmsOrderAutoSplitService orderAutoSplitService;
    @Autowired
    private OmsOrderAutoSplitByStockService orderAutoSplitByStockService;

    @Override
    public Action consume(Message message, ConsumeContext context) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("自动拆单监听MQ", message.getKey()));
        }
        try {
            String messageBody = MsgConvertUtil.objectDeserialize(message.getBody()).toString();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("messageBody:{}"), messageBody);
            }
            List<OperateOrderMqInfo> operateOrderMqInfoList = JSON.parseArray(messageBody, OperateOrderMqInfo.class);
            if (CollectionUtils.isEmpty(operateOrderMqInfoList)) {
                log.error(LogUtil.format("解析messageBody转化为operateOrderMqInfoList时为null", message.getKey()));
                return Action.CommitMessage;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("operateOrderMqInfoList:{}"), JSON.toJSONString(operateOrderMqInfoList));
            }
            // 查询所有的店铺库存同步策略缓存到map中
            Map<Long, List<Long>> shopSyncStockStrategyMap = orderAutoSplitService.loadshopSyncStockStrategyToMap();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("shopSyncStockStrategyMap:{}"), JSON.toJSONString(shopSyncStockStrategyMap));
            }
            for (OperateOrderMqInfo operateOrderMqInfo : operateOrderMqInfoList) {
                String ids = operateOrderMqInfo.getOrderIds();
                if (StringUtils.isNotEmpty(ids)) {
                    String[] idArray = StringUtils.split(ids, ",");
                    List<Long> orderList = Arrays.stream(idArray).map(id -> Long.valueOf(id.trim())).collect(Collectors.toList());
                    Map<Long, StCShopStrategyDO> shopStrategyDOMap = orderAutoSplitByStockService.loadShopStrategyToMap();
                    orderAutoSplitByStockService.handleSplitMqOrderList(orderList, shopSyncStockStrategyMap, shopStrategyDOMap);
                }
            }
            return Action.CommitMessage;
        } catch (Exception e) {
            log.error(LogUtil.format("自动拆单监听MQ异常: {}"), Throwables.getStackTraceAsString(e));
            return Action.ReconsumeLater;
        }
    }
}
