package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-25
 * create at : 2019-04-25 11:39 AM
 */
@Step(order = 30, description = "检查中间表订单是否已经存在")
@Slf4j
@Component
public class Step30CheckOmsOrderExist extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {

    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo,
                                                                   ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {

        IpBJingdongOrder order = orderInfo.getJingdongOrder();
        String orderState = order.getOrderState();
        Long orderNo = orderInfo.getOrderNo();
        Long orderId = order.getOrderId(); //京东平台单号
        List<OcBOrder> ocBOrderList = ipJingdongOrderService.selectOmsOrderInfo(orderNo);
        orderInfo.setOcBOrderList(ocBOrderList);

        /*
         * 若订单中间表状态是否为“交易状态”为WAIT_SELLER_STOCK_OUT（待发货）
         * A若是，则更新数据，“转换状态”=1,“转换时间”：当前时间
         *      a)订单中间表的数据在【全渠道订单】中  存在 ,则判断“卖家备注”是否与【全渠道订单】中的“卖家备注”一致
         *      b)订单中间表的数据在【全渠道订单】中  不存在 ,则判断订单中的明细条码是否在【条码档案】中存在且启用
         * B不是WAIT_SELLER_STOCK_OUT（待发货）
         *      a)订单中间表的数据在【全渠道订单】中  存在 ,则再判断“卖家备注”是否与【全渠道订单】中的“卖家备注”一致
         *      b)订单中间表的数据在【全渠道订单】中  不存在 ,则更新【京东中间表】数据。“转换状态”=2，“转换次数”：原次数+1，“转换时间”：当前时间，“系统备注”：订单状态不是待发货,标记为已转换，程序结束
         */
        boolean isHistoryOrder = StringUtils.equalsIgnoreCase(JingdongOrderStatus.WAIT_GOODS_RECEIVE_CONFIRM, orderState)
                || StringUtils.equalsIgnoreCase(JingdongOrderStatus.FINISHED_L, orderState);
        if (StringUtils.equalsIgnoreCase(JingdongOrderStatus.WAIT_SELLER_STOCK_OUT, orderState) || isHistoryOrder) {
            if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                //todo 加入取消拦截的逻辑
                Boolean aBoolean = ipJingdongOrderService.handleOrder(ocBOrderList, orderId, operateUser);
                return new ProcessStepResult<>(StepStatus.SUCCESS, aBoolean,
                        "京东中间表状态为待发货，且存在对应全渠道订单，进行下一步，卖家备注”是否与【全渠道订单】中的“卖家备注”一致",
                        Step40UpdateSellerRemark.class);
            } else {
                return new ProcessStepResult<>(StepStatus.SUCCESS,
                        "京东中间表状态为待发货，且存在对应全渠道订单，进行下一步，判断订单中的明细条码是否在【条码档案】中存在且启用",
                        Step48CheckIsExchangeOrder.class);
            }
        } else {
            /*
             * 不是待发货
             * 若存在全渠道订单，则再判断订单的平台状态是否为“锁定”（LOCKED）、“取消”（TRADE_CANCELED），
             * 如果是上述2种状态，则更新订单转换状态为未转换，程序结束
             */
            //仅用来更新
            IpBJingdongOrder updateOrder = new IpBJingdongOrder();
            // updateOrder.setId(order.getId());


            if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                if (StringUtils.equalsIgnoreCase(JingdongOrderStatus.TRADE_CANCELED, orderState)
                        || StringUtils.equalsIgnoreCase(JingdongOrderStatus.LOCKED, orderState)) {
                    updateOrder.setTransdate(new Date());
                    updateOrder.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
                    updateOrder.setTransCount(order.getTransCount() + 1L);
                    updateOrder.setSysremark("平台状态为锁定或取消,标记为已转换。");
                    ipJingdongOrderService.updateIpJingdongOrderInfo(updateOrder, orderId);
                    return new ProcessStepResult<>(StepStatus.SUCCESS,
                            "平台状态为锁定或取消,标记为已转换，更新卖家备注。", Step45UpdateSellerRemarkExt.class);
                } else {
                    return new ProcessStepResult<>(StepStatus.SUCCESS,
                            "京东中间表状态为待发货，且存在对应全渠道订单，进行下一步，卖家备注”是否与【全渠道订单】中的“卖家备注”一致",
                            Step40UpdateSellerRemark.class);
                }
            } else {
                updateOrder.setTransdate(new Date());
                updateOrder.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
                updateOrder.setTransCount(order.getTransCount() + 1L);
                updateOrder.setSysremark("订单状态不是待发货,标记为已转换");
                ipJingdongOrderService.updateIpJingdongOrderInfo(updateOrder, orderId);
                return new ProcessStepResult<>(StepStatus.FINISHED, "订单状态不是待发货,标记为已转换");
            }
        }
    }
}
