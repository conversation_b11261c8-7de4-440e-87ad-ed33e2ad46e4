package com.jackrain.nea.oc.oms.mq.processor.aop.parser.impl;

import com.aliyun.openservices.ons.api.Message;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.table.OcBMessageConsumeLog;
import com.jackrain.nea.oc.oms.mq.processor.aop.parser.IMqMessageParser;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * Description： 抽象解析器，为了封装try catch
 * Author: RESET
 * Date: Created in 2020/8/26 1:55
 * Modified By:
 */
@Slf4j
public abstract class AbstractMessageParser implements IMqMessageParser {

    /**
     * 解析器
     *
     * @param message
     * @return
     */
    @Override
    public OcBMessageConsumeLog doParse(Message message) {
        try {
            return parse(message);
        } catch (Throwable t) {
            // 解析报错不影响业务逻辑，所以不做处理
            log.error(LogUtil.format("AbstractMessageParser.doParse.error: {}", "doParse"),
                    Throwables.getStackTraceAsString(t));
        }

        return null;
    }

    /**
     * 设置基本信息
     *
     * @param message
     * @return
     */
    protected OcBMessageConsumeLog buildLogByMqInfo(Message message) {
        OcBMessageConsumeLog consumeLog = new OcBMessageConsumeLog();
        consumeLog.setTopic(message.getTopic());
        consumeLog.setTag(message.getTag());
        consumeLog.setMessageKey(message.getKey());
        consumeLog.setMessageId(message.getMsgID());
        consumeLog.setRetryCount(message.getReconsumeTimes());

        return consumeLog;
    }

    /**
     * 解析器
     *
     * @param message
     * @return
     */
    public abstract OcBMessageConsumeLog parse(Message message) throws Exception;

}
