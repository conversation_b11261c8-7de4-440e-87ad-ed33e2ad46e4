package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat.step;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 10:00 下午
 * @Version 1.0
 */
@Step(order = 20, description = "对原单以及sku的不存在的处理")
@Slf4j
@Component
public class Step020StandplatCheckExchangeStatus extends BaseStandplatExchangeProcessStep implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {
    @Value("${r3_oms_exchange_day:5}")
    private int findOrignOrderDays;

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug("BaseStandplatExchangeProcessStep.Step020StandplatCheckExchangeStatus start >>> orderInfo：{}", JSON.toJSONString(orderInfo));
        }
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        try {
            //判断换货sku是否存在
            ProductSku productSku = orderInfo.getProductSku();
            if (productSku == null) {
                String message = "换货sku在系统中不存在";
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FAILED, message);

            }
            if (SkuType.GIFT_PRODUCT == productSku.getSkuType()) {
                String message = "换货sku为福袋商品,不支持换货";
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FAILED, message);
            }
            //查找原单信息
            List<OmsOrderExchangeRelation> originalSingleOrder = orderInfo.getOriginalSingleOrder();
            if (CollectionUtils.isEmpty(originalSingleOrder)) {
                //原单信息不存在超过三天
                String message = "";
                if (checkReturnOrderData(ipBStandplatRefund)) {
                    message = SysNotesConstant.SYS_REMARK2;
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                            ipBStandplatRefund);
                } else {
                    message = SysNotesConstant.SYS_REMARK1;
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                            message, ipBStandplatRefund);
                }
                return new ProcessStepResult<>(StepStatus.FAILED, message + "转换失败");
            }
            //判断原始订单是否还有未平台发货的订单(拆单)
            List<OmsOrderExchangeRelation> exchangeRelationList = originalSingleOrder.stream().filter(p ->
                    OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(p.getOcBOrder().getOrderStatus())
                            || OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(exchangeRelationList) || originalSingleOrder.size() != exchangeRelationList.size()) {
                String message = "原始订单有未平台发货或者仓库发货的订单,无法转换";
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(),
                        message, ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FAILED, message);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "判断原单状态及sku信息成功,进入下一阶段");
        } catch (Exception e) {
//            ipStandplatRefundService.updateExchangeIsTransError(ipBStandplatRefund, e.getMessage());
            log.error(this.getClass().getName() + " 退换货转换检验原单以及sku信息异常", e);
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }


    /**
     * 检验原单不存在的时间
     *
     * @return
     */
    private boolean checkReturnOrderData(IpBStandplatRefund ipBTaobaoExchange) {
        Date date = new Date();
        //判断退单创建时间是否超过n天 间隔
        Date created = ipBTaobaoExchange.getCreationdate();
        Long intervalDays = findOrignOrderDays * 24 * 60 * 60 * 1000L + created.getTime();
        return intervalDays < date.getTime();
    }

}
