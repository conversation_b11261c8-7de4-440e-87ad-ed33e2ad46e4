package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.OcMergeOrderService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 自动合单监听MQ
 *
 * @author: ruan.gz
 * @since: 2020/06/19
 * create at : 2019/7/31 16:14
 *
 * 无用废弃
 */
@Deprecated
@Slf4j
@RocketMqMessageListener(name = "AutoMergeOrderListenMq", type = MqTypeEnum.DEFAULT)
public class AutoMergeOrderListenMq implements BaseMessageListener {

    @Autowired
    private OcMergeOrderService ocMergeOrderService;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.info("AutoMergeOrderListenMq.consume.topic-tag-key: {}-{}-{}", messageTopic, messageKey, messageTag);
        try {
            ocMergeOrderService.autoMergeConsumeHandle(messageBody, messageKey);
        } catch (Exception e) {
            log.error(LogUtil.format("AutoMergeOrderListenMq.consume.ExpMsg: {}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }
}
