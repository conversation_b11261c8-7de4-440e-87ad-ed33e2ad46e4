package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItemEx;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.web.face.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 检查订单商品是否在系统中
 *
 * @author: chenxiulou
 * @since: 2019-01-20
 * create at : 2019-01-20 02:48
 */
@Step(order = 30, description = "检查订单商品是否在系统中")
@Component
public class Step030CheckProductExist extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation deliveryInfo,
                                                                  ProcessStepResult preStepResult,
                                                                  boolean isAutoMakeup, User operateUser) {
        String orderNo = deliveryInfo.getOrderNo();
        int currentSyncStatus = deliveryInfo.getJitxDelivery().getSynstatus().intValue();

        boolean hasErrorInfo = false;
        StringBuilder sbErrorInfo = new StringBuilder();

        for (IpBJitxDeliveryItemEx jitxDeliveryItem : deliveryInfo.getJitxDeliveryItemList()) {
            String barcode = jitxDeliveryItem.getBarcode();
            if (StringUtils.isEmpty(barcode)) {
                hasErrorInfo = true;
                sbErrorInfo.append("商品barcode为空;");
                sbErrorInfo.append("\r\n");
            } else {
                PsSkuResult psSkuResult = psRpcService.selectSkuInfoByforCodes(barcode);
                if (psSkuResult == null) {
                    hasErrorInfo = true;
                    String msg = Resources.getMessage("在系统中未查询到商品条码;");
                    sbErrorInfo.append(jitxDeliveryItem.getBarcode());
                    sbErrorInfo.append(msg);
                    sbErrorInfo.append("\r\n");
                } else {
                    List<ProSku> proSkuList = psSkuResult.getProSkus();
                    ProductSku skuInfo = null;
                    if(CollectionUtils.isEmpty(proSkuList)){
                        hasErrorInfo = true;
                        String msg = Resources.getMessage("在系统中未查询到商品条码;");
                        sbErrorInfo.append(barcode);
                        sbErrorInfo.append(msg);
                        sbErrorInfo.append("\r\n");
                    }else if(proSkuList.size() > 1){
                        hasErrorInfo = true;
                        String msg = Resources.getMessage("在系统中查询到多个商品条码;");
                        sbErrorInfo.append(barcode);
                        sbErrorInfo.append(msg);
                        sbErrorInfo.append("\r\n");
                    }else{
                        skuInfo = psRpcService.selectProductSku(proSkuList.get(0).getEcode());
                        if (skuInfo == null) {
                            hasErrorInfo = true;
                            String msg = Resources.getMessage("查询商品信息，不存在;");
                            sbErrorInfo.append(barcode);
                            sbErrorInfo.append(msg);
                            sbErrorInfo.append("\r\n");
                        } else {
                            jitxDeliveryItem.setProdSku(skuInfo);
                        }
                    }
                }
            }
        }

        if (hasErrorInfo) {
            String errorMessage = "商品数据不存在，不对线上反馈寻仓结果，退出寻仓反馈操作;";
            boolean updateStatusRes = ipJitxDeliveryService.updateJitxSyncStatus(orderNo,
                    SyncStatus.SYNCFAILD, sbErrorInfo.toString() + "不对线上反馈寻仓结果;");
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else {
            if(SyncStatus.EXCEPTION.toInteger() == currentSyncStatus){
                //平台反馈
                return new ProcessStepResult<>(StepStatus.SUCCESS, "商品数据检查成功，开始进行平台反馈！",Step040FeedBackDeliveryResult.class);
            }else{
                //寻源占单，平台反馈
                return new ProcessStepResult<>(StepStatus.SUCCESS,  "商品数据检查成功，进入下一阶段");
            }
        }
    }
}
