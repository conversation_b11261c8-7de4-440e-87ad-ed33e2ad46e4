package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OmsRedisKeyResources;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 检查发货仓库是否在系统中
 *
 * @author: 黄超
 * @since: 2019-06-27
 * create at : 2019-06-27 9:00
 */
@Step(order = 50, description = "检查发货仓库是否在系统中")
@Slf4j
@Component
public class Step50JitxCheckWarehouseExist extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();

        boolean hasErrorInfo = false;
        StringBuilder sbErrorInfo = new StringBuilder();

        String deliveryWarehouse = orderInfo.getJitxOrder().getDeliveryWarehouse();
        if (StringUtils.isEmpty(deliveryWarehouse)) {
            hasErrorInfo = true;
            sbErrorInfo.append("仓库编码为空;");
            sbErrorInfo.append("\r\n");
        } else {

            log.debug("JITX单据转换时，通过店铺ID和实体仓ID获取实体仓编码,店铺ID：{},唯品会仓库编码：{}",
                    orderInfo.getJitxOrder().getCpCShopId(), deliveryWarehouse);
            String type = OmsRedisKeyResources.STORE;
            if (!YesNoEnum.Y.getVal().equals(orderInfo.getJitxOrder().getIsStoreDelivery())) {
                type = OmsRedisKeyResources.WAREHOUSE;
            }
            StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryVipcomWarehouse(orderInfo.getJitxOrder().getCpCShopId(), deliveryWarehouse, type);
            if (log.isDebugEnabled()) {
                log.debug("JITX单据转换时，通过店铺ID和实体仓ID获取实体仓编码返回数据：{}", JSONObject.toJSONString(jitxWarehouse));
            }
            if (jitxWarehouse == null || jitxWarehouse.getCpCPhyWarehouseId() == null) {
                hasErrorInfo = true;
                String msg = Resources.getMessage("不存在;");
                sbErrorInfo.append(deliveryWarehouse);
                sbErrorInfo.append(msg);
                sbErrorInfo.append("\r\n");
            } else {
                Long warehouseId = jitxWarehouse.getCpCPhyWarehouseId();
                if(Boolean.TRUE.equals(orderInfo.getSameWarehouse())){
                    //如果仓库一致，取时效单占用实体仓
                    warehouseId = orderInfo.getTimOrderItemWareHouseId();
                }
                CpCPhyWarehouse phyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
                if (phyWarehouse == null || phyWarehouse.getId() == null) {
                    hasErrorInfo = true;
                    String msg = Resources.getMessage("不存在;");
                    sbErrorInfo.append(deliveryWarehouse);
                    sbErrorInfo.append(msg);
                    sbErrorInfo.append("\r\n");
                } else {
                    orderInfo.setCpCPhyWarehouse(phyWarehouse);
                }
            }

//            CpCPhyWarehouse phyWarehouse = wareHouseService.findWareHouseByJitEcode(deliveryWarehouse);
//            if (phyWarehouse == null || phyWarehouse.getId() == null) {
//                hasErrorInfo = true;
//                String msg = Resources.getMessage("不存在;");
//                sbErrorInfo.append(deliveryWarehouse);
//                sbErrorInfo.append(msg);
//                sbErrorInfo.append("\r\n");
//            } else {
//                orderInfo.setCpCPhyWarehouse(phyWarehouse);
//            }
        }

        if (hasErrorInfo) {
            String errorMessage = "发货仓库数据不存在，退出转单操作";
            boolean updateStatusRes = ipJitxOrderService.updateJitxOrderTransStatus(orderNo,
                    TransferOrderStatus.NOT_TRANSFER, sbErrorInfo.toString());
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else {
            return new ProcessStepResult<>(StepStatus.SUCCESS, null, "检查发货仓库是否存在成功，进入下一阶段",
                    Step60JitxSaveOmsOrder.class);
        }
    }
}
