package com.jackrain.nea.oc.oms.process.merge.handle;

import com.jackrain.nea.exception.NDSException;
import lombok.Getter;

import java.util.Objects;


/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/6/18
 **/
public enum MergeEnum {

    /**
     * 基础策略
     */
    BASEMERGESTRATEGY(1, "BaseMergeStrategy"),

    /**
     * 店铺 策略
     */
    STOREMERGESTRATEGY(2, "StoreMergeStrategy"),


    /**
     * 是否可以再次拆单策略
     */
    ISSPLITMERGE(1001, "IsSplitMerge");


    @Getter
    private Integer value;

    @Getter
    private String tag;

    MergeEnum(Integer value, String tag) {
        this.value = value;
        this.tag = tag;
    }

    public static Integer getValueFromValueTag(String value) {
        MergeEnum[] values = MergeEnum.values();
        for (MergeEnum dictStatusEnum : values) {
            if (Objects.equals(dictStatusEnum.tag, value)) {
                return dictStatusEnum.value;
            }
        }
        throw new NDSException("错误的合单配置:" + value);
    }

}
