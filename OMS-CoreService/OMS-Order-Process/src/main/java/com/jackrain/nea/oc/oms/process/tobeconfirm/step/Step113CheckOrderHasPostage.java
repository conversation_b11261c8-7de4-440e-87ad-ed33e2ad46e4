package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsSpiltRuleEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.SpiltOrderParam;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.process.tobeconfirm.util.TobeConfirmedUtil;
import com.jackrain.nea.oc.oms.services.OmsOrderDifferenPriceService;
import com.jackrain.nea.oc.oms.services.OmsOrderDistributeWarehouseService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderSplitService;
import com.jackrain.nea.oc.oms.services.OmsToSapTaskService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryProcessor;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 检查是否包含虚拟条码
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:12
 */
@Step(order = 113, description = "检查是否包含虚拟条码")
@Slf4j
public class Step113CheckOrderHasPostage extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsOrderDifferenPriceService omsOrderDifferenPriceService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderSplitService orderSplitService;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderDistributeWarehouseService omsWarehousRuleService;

    @Autowired
    private OrderDeliveryProcessor orderDeliveryProcessor;

    @Autowired
    private OmsToSapTaskService omsToSapTaskService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private OmsSystemConfig omsSystemConfig;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        try {
            //toc残次订单跳过
            if (OmsOrderUtil.isToCCcOrder(orderInfo.getOrderInfo())){
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }

            // 刷新订单业务类型信息
            List<OcBOrderItem> orderItemList = orderItemMapper.selectOrderItemListOccupy(orderInfo.getOrderId());
            orderInfo.setOrderItemList(orderItemList);
            if (!TobeConfirmedUtil.checkIsJcOrder(orderInfo)) {
                //判断全部是否为虚拟
                String result = omsOrderDifferenPriceService.doAllExistDifferenPrice(orderInfo);
                //当全部为虚拟结束占单流程,更改订单状态为已取消
                if ("all".equalsIgnoreCase(result)) {
                    //设置整单为虚拟订单
                    orderInfo.getOrderInfo().setOrderType(OrderTypeEnum.DIFFPRICE.getVal());
                    // 更新订单信息
                    ocBOrderMapper.updateOrderType(orderInfo.getOrderInfo().getId(), OrderTypeEnum.DIFFPRICE.getVal());
                } else if ("part".equalsIgnoreCase(result)) {
                    Map<Set<Long>, SpiltOrderParam> setSpiltOrderParamMap = orderSplitService.startSplitVirtualOrder(orderInfo, operateUser);
                    if (setSpiltOrderParamMap != null && !setSpiltOrderParamMap.isEmpty()){
                        Map<Integer, Map<Set<Long>, SpiltOrderParam>> spiltRule = orderInfo.getSpiltRule();
                        spiltRule.put(OmsSpiltRuleEnum.FICTITIOUS.getCode(), setSpiltOrderParamMap);
                    }
                } else {
                    String message = "OrderId=" + orderInfo.getOrderId() + ",订单没有包含虚拟条码,执行下个流程!";
                    return new ProcessStepResult<>(StepStatus.SUCCESS,  message);
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, null);
        } catch (Exception ex) {
            OcBOrder tempOrderInfo = new OcBOrder();
            String operateMessage = "OrderId" + orderInfo.getOrderId() + ",订单执行虚拟服务执行异常;" + ex.getMessage();
            log.error(LogUtil.format("订单执行虚拟服务执行异常,异常信息:{}", "发货后退单转换异常", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            tempOrderInfo.setId(orderInfo.getOrderId());
            tempOrderInfo.setSysremark(SplitMessageUtil.splitMesssage(operateMessage));
            tempOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_20);
            omsOrderService.updateOrderInfo(tempOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
    }
}
