package com.jackrain.nea.oc.oms.process.transfer.impl.lock.lockorder.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.IpOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.model.resources.LockOrderConstant;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Descroption 处理锁单中间表并回调千牛锁单接口
 * <AUTHOR>
 * @Date 2019/10/10 19:44
 */
@Step(order = 30, description = "处理锁单表并回调千牛锁单接口")
@Slf4j
@Component
public class Step030DealLockOrder extends BaseLockProcessStep
        implements IOmsOrderProcessStep<IpOrderLockRelation> {
    @Override
    public ProcessStepResult<IpOrderLockRelation> startProcess(IpOrderLockRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.info(this.getClass().getName() + " 处理锁单Id{}并回调千牛锁单接口", orderInfo.getOrderId());
        IpBOrderLock orderLock = orderInfo.getOrderLock();
        List<OcBOrder> orderList = orderInfo.getOcBOrders();
        try {
            //1.获取订单ID集合,查询最新全渠道订单数据
            List<Long> orderIdList = orderList.stream().map(e -> e.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderIdList)) {
                String lockStatus = ipOrderLockService.dealLockOrder(orderIdList, orderLock, operateUser);
                if (orderLock.getIsQnLock() != null && orderLock.getIsQnLock().equals(1)) {
                    //2.调用千牛接口
                    if (IpOrderLockStatusEnum.LOCK_FAIL.getKey().equals(lockStatus) || IpOrderLockStatusEnum.PART_LOCK_FAIL.getKey().equals(lockStatus)) {
                        ipOrderLockService.dealQianNiuInterceptOrder(orderLock, LockOrderConstant.ERR_CODE, operateUser);
                    } else if (IpOrderLockStatusEnum.LOCKED.getKey().equals(lockStatus)) {
                        ipOrderLockService.dealQianNiuInterceptOrder(orderLock, "", operateUser);
                    }
                }
            }
            return new ProcessStepResult<>(StepStatus.FINISHED,
                    "单据" + orderInfo.getOrderId() + "转换完成");
        } catch (Exception e) {
            log.error(LogUtil.format("锁单异常:{}", "锁单异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipOrderLockService.updateLockErrorLog(orderLock);
            return new ProcessStepResult<>(StepStatus.FAILED, " 锁单异常：" + e.getMessage());
        }
    }
}
