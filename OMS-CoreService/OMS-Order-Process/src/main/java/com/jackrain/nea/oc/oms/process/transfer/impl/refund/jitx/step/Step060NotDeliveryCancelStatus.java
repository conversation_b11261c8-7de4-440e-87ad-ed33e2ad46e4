package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.step;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 转换全渠道订单表状态待审核、缺货、已审核、配货中
 * @Date 2019-6-26
 **/
@Step(order = 60, description = "全渠道订单表状态待审核、缺货、已审核、配货中")
@Slf4j
@Component
public class Step060NotDeliveryCancelStatus extends BaseJitxRefundProcessStep implements IOmsOrderProcessStep<IpJitxOrderRelation> {
    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJitxOrder ipBJitxOrder = orderInfo.getJitxOrder();
        if (log.isDebugEnabled()) {
            log.debug("Step060NotDeliveryCancelStatus orderNo:{}", orderInfo.getOrderNo());
        }
        try {
            List<OcBOrder> ocBOrderList = ipJitxRefundService.selectOmsOrder(orderInfo.getOrderNo());
            int success = 0;
            int fail = 0;
            for (OcBOrder ocBOrder : ocBOrderList) {
                Integer ocBOrderStatus = ocBOrder.getOrderStatus();
                if (OmsOrderStatus.CHECKED.toInteger().equals(ocBOrderStatus)
                        || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrderStatus)) {
                    // hold单
                    ocBOrderHoldService.businessHold(ocBOrder.getId(), OrderHoldReasonEnum.REFUND_HOLD);
                    // 已审核的JITX订单，未发货取消转换时拦截后进行反审核
                    OrderICheckRequest orderICheckRequest = new OrderICheckRequest();
                    Long[] ids = new Long[1];
                    ids[0] = ocBOrder.getId();
                    orderICheckRequest.setIds(ids);
                    orderICheckRequest.setType(LogTypeEnum.JITX_REFUND_BEFORE_DELIVERY.getType());

                    ValueHolderV14 valueHolderV14 = ocBOrderTheAuditService.orderTheAudit(orderICheckRequest, SystemUserResource.getRootUser(), true);
                    if (valueHolderV14.isOK()) {
                        success++;
                    } else {
                        fail++;
                    }
                }
            }
            if (success == ocBOrderList.size()) {
                return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + ipBJitxOrder.getOrderSn() + "未发货取消的JITX订单，反审核成功，进入下一步！");
            }
            if (fail > 0) {
                ipJitxRefundService.updateRefundIsTrans(TransferOrderStatus.NOT_TRANSFER,
                        "未发货取消的JITX订单，反审核失败，订单退款转换失败！", ipBJitxOrder);
                return new ProcessStepResult<>(StepStatus.FAILED, "未发货取消的JITX订单，反审核失败，订单退款转换失败！");
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "全渠道订单表状态待审核、缺货、已审核、配货中判断成功，进入下一步！");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 退单转换异常！", e);
            //修改中间表状态及系统备注
            ipJitxRefundService.updateRefundIsTransError(ipBJitxOrder, e.getMessage());
            String errorMessage = "退单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
