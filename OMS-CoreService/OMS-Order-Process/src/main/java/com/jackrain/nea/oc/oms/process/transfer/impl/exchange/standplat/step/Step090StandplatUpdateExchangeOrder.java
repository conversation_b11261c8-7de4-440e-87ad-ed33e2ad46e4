package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat.step;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2020/12/2 9:01 下午
 * @Version 1.0
 */
@Step(order = 90, description = "更新退换货单以及换货订单")
@Slf4j
@Component
public class Step090StandplatUpdateExchangeOrder extends BaseStandplatExchangeProcessStep implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {
    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        if(log.isDebugEnabled()){
            log.debug("BaseStandplatExchangeProcessStep.Step090StandplatUpdateExchangeOrder start >>> orderInfo：{}", JSON.toJSONString(orderInfo));
        }
        IpBStandplatRefund ipBTaobaoExchange = orderInfo.getIpBStandplatRefund();
        try {
            boolean flag = standplatExchangeService.updateExchangeOrderAndReturnOrder(orderInfo, operateUser);
            String message;
            if (flag) {
                message = "更新退换货单以及换货订单成功";
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message, ipBTaobaoExchange);
            } else {
                message = "更新退换货单以及换货订单成功,之前存在换货订单,修改为未转换";
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), message, ipBTaobaoExchange);
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        } catch (Exception e) {
//            ipStandplatRefundService.updateExchangeIsTransError(ipBTaobaoExchange, "更新退换货单以及换货订单失败" + e.getMessage());
            log.error(this.getClass().getName() + " 更新退换货单以及换货订单失败", e);
            String errorMessage = "更新退换货单以及换货订单失败!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
