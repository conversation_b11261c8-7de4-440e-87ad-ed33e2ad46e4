package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryMapper;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDelivery;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-03-04 14:30
 * @Description : 检查寻仓单是否下载完成
 **/
@Step(order = 60, description = "检查寻仓单是否下载完成")
@Slf4j
@Component
public class Step060CheckDeliveryDownComplete extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {
    @Value("${r3.oc.oms.delivery.virtual.occupy.wait.time:10}")
    private Integer deliveryVirtualWaitTime;

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    protected IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private IpBJitxDeliveryMapper ipBJitxDeliveryMapper;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxDeliveryRelation> stepResult = new ProcessStepResult<>();
        stepResult.setStatus(StepStatus.FAILED);
        String orderSn = orderInfo.getOrderNo();
        String rootOrderSn = orderInfo.getRootOrderSn();
        RedisReentrantLock redisLock = orderInfo.getRedisLock();
        try{
            Integer deliveryOrderCount = orderInfo.getDeliveryOrderCount();
            Integer tiemOrderCount = orderInfo.getTimeOrderCount();

            //重新查询寻仓单状态，如果存在不是未处理状态的，则结束虚拟寻源，等待下次进行,防止并发操作下，寻仓单重复虚拟寻源
            List<String> unsyncOrderSnList = orderInfo.getUnsyncOrderSnList();
            List<IpBJitxDelivery> deliveryList = ipBJitxDeliveryMapper.selectJitxDeliveryByOrderNos(unsyncOrderSnList);
            List<IpBJitxDelivery> unsyncDeliveryList = deliveryList.stream()
                    .filter(e -> SyncStatus.UNSYNC.toInteger() != e.getSynstatus().intValue()).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(unsyncDeliveryList)){
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("寻仓单orderSn:{}虚拟寻源，同批寻仓单已被修改，结束当前转换，等待下次转换unsyncOrderSnList:{}",
                            "Step060CheckDeliveryDownComplete.startProcess",orderSn,rootOrderSn),orderSn,unsyncOrderSnList);
                }
                stepResult.setStatus(StepStatus.FAILED);
                stepResult.setMessage("同批寻仓单已被修改，结束当前转换！");
            }else {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("寻仓单orderSn:{}虚拟寻源，判断同批寻仓单是否下载完成rootOrderSn:{},tiemOrderCount:{},deliveryOrderCount:{}",
                            "Step060CheckDeliveryDownComplete.startProcess",orderSn,rootOrderSn),orderSn,rootOrderSn,tiemOrderCount,deliveryOrderCount);
                }
                if(tiemOrderCount > deliveryOrderCount){
                    //判断创建时间最新的寻仓单是否达到等待间隔时间，是则进行虚拟寻仓，否则继续等待退出转换
                    List<IpJitxDeliveryRelation> deliveryRelations = orderInfo.getIpJitxDeliveryRelation();
                    List<IpBJitxDelivery> deliverys =  deliveryRelations.stream().map(IpJitxDeliveryRelation::getJitxDelivery).collect(Collectors.toList());
                    //默认升序排序
                    deliverys.sort(Comparator.comparing(IpBJitxDelivery::getCreationdate));
                    Date creationdate = deliverys.get(deliverys.size() - 1).getCreationdate();

                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.MINUTE,-deliveryVirtualWaitTime);
                    /**当前时间往前推N分钟*/
                    Date beginDate = calendar.getTime();
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("寻仓单orderSn:{}虚拟寻源，同批寻仓单未下载完成rootOrderSn:{},开始对比时间creationdate:{},beginDate:{}",
                                "Step060CheckDeliveryDownComplete.startProcess",orderSn,rootOrderSn),orderSn,rootOrderSn,creationdate,beginDate);
                    }
                    if(creationdate.compareTo(beginDate) <= 0){
                        //达到等待时间，进行虚拟寻仓
                        stepResult.setMessage("寻仓单OrderNo=" + orderSn + "同批寻仓单是否下载完成校验通过，进入下一阶段！");
                        stepResult.setStatus(StepStatus.SUCCESS);
                    }else{
                        stepResult.setStatus(StepStatus.FAILED);
                        stepResult.setMessage("同批寻仓单未下载完并且等待虚拟寻仓时间还未到，结束当前转换！");
                    }
                }else{
                    stepResult.setMessage("寻仓单OrderNo=" + orderSn + "同批寻仓单是否下载完成校验通过，进入下一阶段！");
                    stepResult.setStatus(StepStatus.SUCCESS);
                }
            }
        }catch (Exception e){
            log.error(LogUtil.format("寻仓单OrderSn:{}转换失败:{}","Step060CheckDeliveryDownComplete"),
                    orderSn, Throwables.getStackTraceAsString(e));
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, "转换失败"+e.getMessage());
        }finally {
            if(!StepStatus.SUCCESS.equals(stepResult.getStatus())){
                redisLock.unlock();
            }
        }
        return stepResult;
    }
}
