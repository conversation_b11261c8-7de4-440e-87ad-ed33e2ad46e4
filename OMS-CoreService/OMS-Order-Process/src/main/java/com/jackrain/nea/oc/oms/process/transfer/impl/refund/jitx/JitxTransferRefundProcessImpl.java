package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description JITX退单转换
 * @Date 2019-6-26
 **/
@Component
public class JitxTransferRefundProcessImpl extends AbstractOrderProcess<IpJitxOrderRelation> {
    @Override
    protected String getChildPackageName() {
        return null;
    }

    @Override
    protected long getProcessOrderId(IpJitxOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpJitxOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JITX_REFUND;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.VIPJITX;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return null;
    }
}
