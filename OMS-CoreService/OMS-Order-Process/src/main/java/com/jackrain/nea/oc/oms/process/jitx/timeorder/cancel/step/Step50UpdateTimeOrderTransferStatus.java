package com.jackrain.nea.oc.oms.process.jitx.timeorder.cancel.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderCancelService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 释放当前行，更新转换状态为2
 *
 * @author: chenxiulou
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 50, description = "释放当前行，更新转换状态为2")
@Component
@Slf4j
public class Step50UpdateTimeOrderTransferStatus extends BaseVipTimeOrderCancelProcessStep
        implements IOmsOrderProcessStep<IpBCancelTimeOrderVipRelation> {
    @Autowired
    private IpVipTimeOrderCancelService vipTimeOrderCanselService;


    @Autowired
    protected IpJitxDeliveryService ipJitxDeliveryService;

    @Override
    public ProcessStepResult<IpBCancelTimeOrderVipRelation> startProcess(IpBCancelTimeOrderVipRelation timeOrderInfo,
                                                                         ProcessStepResult preStepResult,
                                                                         boolean isAutoMakeup, User operateUser) {
        String occupiedOrderSn =  timeOrderInfo.getOrderNo();
        this.vipTimeOrderCanselService.updateTimeOrderTransStatus(occupiedOrderSn,
                TransferOrderStatus.TRANSFERRED, preStepResult.getMessage());

        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
