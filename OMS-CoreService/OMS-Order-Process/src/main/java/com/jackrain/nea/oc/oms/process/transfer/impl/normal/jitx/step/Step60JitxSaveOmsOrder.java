package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.log.LogCat;
import com.jackrain.nea.log.LogEvent;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.BllSystemParameterKeyResources;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 保存订单信息
 *
 * @author: 黄超
 * @since: 2019-06-26
 * create at : 2019-06-26 9:00
 */
@Step(order = 60, description = "保存订单信息")
@Component
@Slf4j
public class Step60JitxSaveOmsOrder extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {

//    @Autowired
//    private R3MqSendHelper sendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private TransferOrderMqConfig orderMqConfig;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    /**
     * 发送MQ消息
     *
     * @param saveOrderInfo 保存的OrderInfo订单信息
     * @param logEventList  logEvent列表
     */
    private void sendMQ(OcBOrderRelation saveOrderInfo, List<LogEvent> logEventList) {
        LogEvent eventMQ = LogCat.newEvent(Step60JitxSaveOmsOrder.class.getSimpleName(), "StartSendMQ");
        if (saveOrderInfo != null) {
            long saveOrderId = saveOrderInfo.getOrderInfo().getId();
            String billNo = saveOrderInfo.getOrderInfo().getBillNo();
            String msgKey = "JITX_TR_" + saveOrderId + "_" + billNo;
            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
            orderMqInfo.setChannelType(this.getCurrentChannelType());
            orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
            orderMqInfo.setOrderId(saveOrderId);
            orderMqInfo.setOrderNo(billNo);
            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
            mqInfoList.add(orderMqInfo);
            String jsonValue = JSONObject.toJSONString(mqInfoList);

            String messageId = null;
            try {
//                messageId = sendHelper.sendMessage(jsonValue, orderMqConfig.getSendMqTopic(),
//                        orderMqConfig.getSendMqTag(),
//                        msgKey);
                log.info("Step60JitxSaveOmsOrder.sendMq");
                MqSendResult result = defaultProducerSend.sendTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_TRANSFER, null, jsonValue, msgKey);
                messageId = result.getMessageId();
            } catch (Exception e) {
                e.printStackTrace();
            }

            eventMQ.addData("MQId", messageId);
            eventMQ.addData("MQKey", msgKey);
        }
        eventMQ.complete();
        logEventList.add(eventMQ);
    }

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        boolean isHistoryOrder = false;
        String orderNo = orderInfo.getOrderNo();
        try {
            List<LogEvent> logEventList = new ArrayList<>();
            LogEvent eventConvert = LogCat.newEvent(Step60JitxSaveOmsOrder.class.getSimpleName(), "ConvertOrder");
            OcBOrderRelation saveOrderInfo = orderService.convertJitxOrderToOrder(orderInfo, isHistoryOrder);
            eventConvert.complete();
            logEventList.add(eventConvert);

            LogEvent eventStartSave = LogCat.newEvent(Step60JitxSaveOmsOrder.class.getSimpleName(), "SaveOrder");
            boolean saveResult = orderService.saveOmsOrderInfoForJitx(saveOrderInfo, orderInfo, isHistoryOrder, operateUser);
            if (!saveResult) {
                saveOrderInfo = null;
                //埋点订单创建
            }
            eventStartSave.complete();
            logEventList.add(eventStartSave);
            if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
                this.sendMQ(saveOrderInfo, logEventList);
            }
            ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.ORDER_CREAT,new Date(),saveOrderInfo.getOrderInfo().getId(),operateUser);
            ProcessStepResult<IpJitxOrderRelation> stepResult = new ProcessStepResult<>();
            stepResult.setMessage("存储数据成功，进入下一阶段");
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step90JitxUpdateOrderTransferStatus.class);
            stepResult.setLogEventList(logEventList);
            return stepResult;
        } catch (Exception ex) {
            log.error(LogUtil.format("Step60JitxSaveOmsOrder:{}", "Step60JitxSaveOmsOrder"), Throwables.getStackTraceAsString(ex));
            String errorMessage = "存储数据失败，退出转单服务;" + ex.getMessage();
            boolean updateStatusRes = ipJitxOrderService.updateJitxOrderTransStatus(orderNo,
                    TransferOrderStatus.NOT_TRANSFER, errorMessage);
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
