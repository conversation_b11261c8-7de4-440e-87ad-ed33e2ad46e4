package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.jackrain.nea.oc.oms.services.refund.RefundOrderWmsBackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 零售退单发wms回传MQ
 *
 * @author: 秦雄飞
 * @time: 2021/5/22 3:44 下午
 * @description:
 */
@Slf4j
@RocketMqMessageListener(name = "RefundOrderToWmsBackMqListener", type = MqTypeEnum.DEFAULT)
public class RefundOrderToWmsBackMqListener implements BaseMessageListener {

    @Autowired
    private RefundOrderWmsBackService refundOrderToWmsBackService;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        try {
            if (log.isDebugEnabled()) {
                log.info("RefundOrderToWmsBackMqListener.接收wms回传mq消息:messageBody{},messageKey{}," +
                                "messageId{}," +
                                "messageTopic{}",
                        messageBody, messageKey, messageKey, messageTopic);
            }
            boolean consumed = refundOrderToWmsBackService.consume(messageBody);
            if (!consumed) {
                throw new MqException("consumedFailed");
            }
        } catch (Exception e) {
            log.error("RefundOrderToWmsBackMqListener.consume error", e);
            throw new MqException(e);

        }
    }
}
