package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-25
 * create at : 2019-04-25 11:35 AM
 * 判断淘宝订单交易状态
 */
@Step(order = 20, description = "判断淘宝订单交易状态")
@Slf4j
@Component
public class Step20CheckOrderTransactionStatus extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {

    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo,
                                                                   ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {
        IpBJingdongOrder order = orderInfo.getJingdongOrder();
        String orderState = order.getOrderState();
        Long orderId = order.getOrderId();

        //仅用来更新
        IpBJingdongOrder updateOrder = new IpBJingdongOrder();
        //updateOrder.setId(order.getId());
        //转换次数为null，默认为0
        if (null == order.getTransCount()) {
            order.setTransCount(0L);
            updateOrder.setTransCount(0L);
            ipJingdongOrderService.updateIpJingdongOrderInfo(updateOrder, orderId);
        }
        boolean isHistoryOrder = StringUtils.equalsIgnoreCase(JingdongOrderStatus.WAIT_GOODS_RECEIVE_CONFIRM, orderState)
                || StringUtils.equalsIgnoreCase(JingdongOrderStatus.FINISHED_L, orderState);
        //是否为”为WAIT_SELLER_STOCK_OUT（待发货）
        if (StringUtils.equalsIgnoreCase(JingdongOrderStatus.WAIT_SELLER_STOCK_OUT, orderState) || isHistoryOrder) {
            //若是，则更新数据，“转换状态”=1,“转换时间”：当前时间
            updateOrder.setTransdate(new Date());
//            updateOrder.setIstrans(TransferOrderStatus.TRANSFERRING.toInteger());
            ipJingdongOrderService.updateIpJingdongOrderInfo(updateOrder, orderId);
        }

        return new ProcessStepResult<>(StepStatus.SUCCESS, null, "判断淘宝订单交易状态完毕，进入下一阶段",
                Step30CheckOmsOrderExist.class);
    }
}
