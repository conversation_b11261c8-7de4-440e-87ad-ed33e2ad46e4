package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.jackrain.nea.oc.oms.mq.processor.impl.mq.ReturnOrderUpdateWarehouseProcessorImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @program: ryytn-oc-oms-v3.0
 * @description:
 * @author: haiyang
 * @create: 2024-02-01 10:09
 **/
@Slf4j
@RocketMqMessageListener(name = "SgMqStrategyListener", type = MqTypeEnum.DEFAULT)
public class SgMqStrategyListener implements BaseMessageListener {

    @Autowired
    private UpdateInDistributionMqProcessorImpl updateInDistributionMqProcessor;

    @Autowired
    private OutOrderMqProcessorImpl outOrderMqProcessor;

    @Autowired
    private ReturnOrderUpdateWarehouseProcessorImpl returnOrderUpdateWarehouseProcessor;

    @Autowired
    private TransferToOmsMqProcessorImpl transferToOmsMqProcessor;

    @Autowired
    private ReturnOrderWmsCallBackMqProcessorImpl returnOrderWmsCallBackMqProcessor;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.info("SgMqStrategyListener.topic-tag-body: {}-{}-{}", messageTopic, messageTag, messageBody);
        switch (messageTag) {
            case "sg_to_oms_wms_out_creat_receipt":
                updateInDistributionMqProcessor.consume(messageBody, messageTopic, messageKey, messageTag, object);
                break;
            case "sg_to_oms_out_result_verify_postback":
                outOrderMqProcessor.consume(messageBody, messageTopic, messageKey, messageTag, object);
                break;
            case "sg_to_oms_in_result_verify_postback":
                returnOrderUpdateWarehouseProcessor.consume(messageBody, messageTopic, messageKey, messageTag, object);
                break;
            case "sg_to_oms_transfer_result_verify_postback":
                log.info("noUsedEnter");
                transferToOmsMqProcessor.consume(messageBody, messageTopic, messageKey, messageTag, object);
                break;
            case "sg_to_oms_wms_in_creat_receipt":
                returnOrderWmsCallBackMqProcessor.consume(messageBody, messageTopic, messageKey, messageTag, object);
                break;
            default:
                log.error("SgMqStrategyListener未监听mq，tag: {}", messageTag);
        }
    }
}
