package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.process.tobeconfirm.util.TobeConfirmedUtil;
import com.jackrain.nea.oc.oms.services.OmsOrderCheckAndUpdateService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 检查和更新平摊金额
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:13
 */
@Step(order = 70, description = "检查和更新平摊金额")
@Slf4j
public class Step070CheckAndUpdateBlanceMoney extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderCheckAndUpdateService omsOrderCheckAndUpdateService;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        try {
            if (!TobeConfirmedUtil.checkIsJcOrder(orderInfo)) {
                //检测更新平摊金额
                boolean result = omsOrderCheckAndUpdateService.checkSplitAmountIsNeedReCalc(orderInfo);
                if (result) {
                    boolean resultAmount = omsOrderCheckAndUpdateService.doCheckAndUpdateBlanceMoney(orderInfo);
                    if (resultAmount) {
                        return new ProcessStepResult<>(StepStatus.SUCCESS);
                    } else {
                        String errorMessage = "订单OrderId=" + orderInfo.getOrderId() + "的订单执行订单平摊金额异常,结束后续流程";
                        OcBOrder ocBOrderDto = new OcBOrder();
                        ocBOrderDto.setId(orderInfo.getOrderId());
                        ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(errorMessage));
                        omsOrderService.updateOrderInfo(ocBOrderDto);
                        return new ProcessStepResult<>(StepStatus.FINISHED, errorMessage);
                    }
                } else {
                    String message = "订单OrderId=" + orderInfo.getOrderId() + "的订单不满足平摊金额服务执行条件,继续下一流程!";
                    return new ProcessStepResult<>(StepStatus.SUCCESS, message);
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, null);
        } catch (Exception ex) {
            log.error(LogUtil.format("TobeConfirmed.Step070CheckAndUpdateBlanceMoney.订单执行平摊金额服务异常:{}", "订单执行平摊金额服务异常"), Throwables.getStackTraceAsString(ex));
            OcBOrder ocBOrderDto = new OcBOrder();
            String operateMessage = "订单OrderId" + orderInfo.getOrderId() + "的订单执行平摊金额服务异常,异常信息-->" + ex.getMessage();
            ocBOrderDto.setId(orderInfo.getOrderId());
            ocBOrderDto.setSysremark(SplitMessageUtil.splitMesssage(operateMessage));
            ocBOrderDto.setOccupyStatus(OrderOccupyStatus.STATUS_20);
            omsOrderService.updateOrderInfo(ocBOrderDto);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
    }
}
