package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.cancel;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderCancelRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * @description: 猫超直发取消订单转换
 * @author: xtt
 * @date: 2020-09-04 11:38
 **/
@Component
public class AlibabaAscpOrderCancelTransferProcessImpl extends AbstractOrderProcess<IpBAlibabaAscpOrderCancelRelation> {

    public AlibabaAscpOrderCancelTransferProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "alibaba.ascp.cancel";
    }

    @Override
    protected long getProcessOrderId(IpBAlibabaAscpOrderCancelRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpBAlibabaAscpOrderCancelRelation orderInfo) {
        return orderInfo.getSourceCode();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_ALIBABA_ASCP_CANCEL_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.ALIBABAASCP;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return null;
    }
}
