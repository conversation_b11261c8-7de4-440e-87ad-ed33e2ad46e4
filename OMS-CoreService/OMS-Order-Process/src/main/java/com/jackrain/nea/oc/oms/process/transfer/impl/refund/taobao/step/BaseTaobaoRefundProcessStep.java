package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.burgeon.mq.core.DefaultProducerSend;
import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.oc.oms.config.TransferOrderMqConfig;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderExchangeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.oc.oms.util.TaobaoRefundOrderTransferUtil;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 基础淘宝退款单转单处理阶段
 *
 * @author: 孙勇生
 * @since: 2019-03-07
 * create at : 2019-03-07 23:13
 */
public abstract class BaseTaobaoRefundProcessStep {

    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    @Autowired
    protected IpTaobaoExchangeService taoBaoExchangeServiceIp;

    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    protected IpTaobaoRefundService ipTaobaoRefundService;

    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;

    @Autowired
    protected BeforeShipmentReturnService beforeShipmentReturnService;

    @Autowired
    protected BeforeShipmentReturnNewService beforeShipmentReturnNewService;

    @Autowired
    protected OmsBeforeShipmentReturnService omsBeforeShipmentReturnService;

    @Autowired
    protected OmsOrderService omsOrderService;

    @Autowired
    protected OmsOrderLogService omsOrderLogService;

    @Autowired
    protected IpOrderCancelToAgService ipOrderCancelToAgService;

    @Autowired
    protected OmsRefundOrderService omsRefundOrderService;

    @Autowired
    protected TaobaoRefundOrderTransferUtil taobaoRefundOrderTransferUtil;

    @Autowired
    protected OmsGoodsAfterRefundOnlyService omsGoodsAfterRefundOnly;

    @Autowired
    protected OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    protected OmsOrderRecountAmountService omsOrderRecountAmountService;

//    @Autowired
//    protected R3MqSendHelper r3MqSendHelper;

    @Autowired
    protected DefaultProducerSend defaultProducerSend;

    @Autowired
    protected TransferOrderMqConfig transferOrderMqConfig;

    @Autowired
    protected OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    protected OcBReturnOrderExchangeMapper ocBReturnOrderExchangeMapper;

    @Autowired
    protected OcBOrderMapper ocBOrderMapper;

    @Autowired
    protected Exchange2RefundService exchange2RefundService;

    @Autowired
    protected OcBOrderOffService ocBOrderOffService;

    @Autowired
    protected OcBOrderTheAuditService ocBOrderTheAuditService;

}
