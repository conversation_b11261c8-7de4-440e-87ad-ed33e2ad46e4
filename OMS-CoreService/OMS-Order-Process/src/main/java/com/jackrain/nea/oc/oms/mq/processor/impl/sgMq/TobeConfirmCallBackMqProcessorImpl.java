package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.error.MqException;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyUpdateProduceRage;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.oc.oms.util.OmsOrderSplitReasonUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 占单回执mq监听mq
 * @author: liuwj
 * @create: 2021-07-07 14:46
 **/
// fixme tag:OperateTobeConfirmCallBack
@Slf4j
@Component
//@RocketMqMessageListener(name = "TobeConfirmCallBackMqProcessorImpl", type = MqTypeEnum.DEFAULT, groupIdSuffix = "TEST")
public class TobeConfirmCallBackMqProcessorImpl {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    SgRpcService sgRpcService;

    @Autowired
    TobeConfirmCallBackMqService tobeConfirmCallBackMqService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;


    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.debug(LogUtil.format("TobeConfirmCallBackMqProcessorImpl.startProcess messageTopic:{},messageKey:{},messageBody:{},messageTag:{}",
                "TobeConfirmCallBackMqProcessorImpl.startProcess"), messageTopic, messageKey, messageBody, messageTag);
        JSONObject messageJson = JSONObject.parseObject(messageBody);
        SgFindSourceStrategyOmsResult sgFindSourceStrategyOmsResult = JSONObject.toJavaObject(messageJson, SgFindSourceStrategyOmsResult.class);
        User user = SystemUserResource.getRootUser();
        //其它订单
        Long ocBOrderId = sgFindSourceStrategyOmsResult.getSourceBillId();
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        List<SgOccupyUpdateProduceRage> sgOccupyUpdateProduceRageList = sgFindSourceStrategyOmsResult.getSgOccupyUpdateProduceRageList();
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                //查询原单
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(ocBOrderId);
                if (ocBOrder == null || OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus())) {
                    String logMsg1 = "OrderId=" + ocBOrder.getId() + "该订单不存在，或者已经被作废mq结束";
                    omsOrderLogService.addUserOrderLog(ocBOrderId, ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), logMsg1, "", "", user);
                    return;
                }
                Integer orderStatus = ocBOrder.getOrderStatus();
                if (!OmsOrderStatus.OCCUPY_IN.toInteger().equals(orderStatus)) {
                    omsOrderLogService.addUserOrderLog(ocBOrderId, ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), "订单不是寻源中,此条信息忽略", "", "", user);
                    //发送钉钉告警
                    String content = "订单id[" + ocBOrderId + "]处于非寻源中状态收到寻源消息，messageBody:" + messageBody + "messageKey:" + messageKey;
                    DingTalkUtil.dingFindSourceException(content);
                    return;
                }
                //查询原单明细
                List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectOrderItemListOccupy(ocBOrderId);
                if (CollectionUtils.isNotEmpty(sgOccupyUpdateProduceRageList)) {
                    // 对ocBOrderItemList进行groupby分组。key是id。 生成的格式是Map<Long, OcBOrderItem>
                    Map<Long, OcBOrderItem> orderItemMap = ocBOrderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, ocBOrderItem -> ocBOrderItem));
                    StringBuffer stringBuffer = new StringBuffer();
                    for (SgOccupyUpdateProduceRage sgOccupyUpdateProduceRage : sgOccupyUpdateProduceRageList) {
                        OcBOrderItem ocBOrderItem = orderItemMap.get(sgOccupyUpdateProduceRage.getSourceItemId());
                        String newRange = sgOccupyUpdateProduceRage.getBeginProduceDate() + "-" + sgOccupyUpdateProduceRage.getEndProduceDate();
                        stringBuffer.append("商品SKU:");
                        stringBuffer.append(ocBOrderItem.getPsCSkuEcode());
                        stringBuffer.append(",原效期范围为");
                        stringBuffer.append(ocBOrderItem.getExpiryDateRange());
                        stringBuffer.append(",现修改为");
                        stringBuffer.append(newRange);
                        stringBuffer.append(";");
                    }
                    omsOrderLogService.addUserOrderLog(ocBOrderId, ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), stringBuffer.toString(), "", "", user);
                }

                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), "OrderId=" + ocBOrder.getId() + "库存寻源结束,订单开始处理", "", "", user);
                //开始后续处理
                tobeConfirmCallBackMqService.TobeConfirmCallBackService(sgFindSourceStrategyOmsResult, ocBOrder, ocBOrderItemList);
            } else {
                throw new MqException("当前订单其他人正在操作,请稍后再试!");
            }
        } catch (Exception e) {
            OcBOrder erroOcBOrder = new OcBOrder();
            erroOcBOrder.setId(ocBOrderId);
            //寻源失败增加标识
            erroOcBOrder.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IY);
            ocBOrderMapper.updateById(erroOcBOrder);
            log.error(LogUtil.format("orderId订单{}，占单库存异常 {}", "占单库存异常", ocBOrderId), ocBOrderId, Throwables.getStackTraceAsString(e));
            String logMsg1 = "OrderId=" + ocBOrderId + "占用库存失败";
            omsOrderLogService.addUserOrderLog(ocBOrderId, null, OrderLogTypeEnum.OCCUPY.getKey(), logMsg1 + e.getMessage(), "", e.getMessage(), user);
            throw new MqException("异常重试");
        } finally {
            redisLock.unlock();
        }
    }
}
