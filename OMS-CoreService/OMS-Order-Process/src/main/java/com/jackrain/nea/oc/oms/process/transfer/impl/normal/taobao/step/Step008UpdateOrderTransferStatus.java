package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @author: xiWen.z
 * create at: 2019/10/12 0012
 */
@Step(order = 8, description = "释放当前行，更新转换状态为2")
@Component
@Slf4j
public class Step008UpdateOrderTransferStatus extends BaseTaobaoOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        String remark = "转单成功";
        String stepTradeStatus = orderInfo.getTaobaoOrder().getStepTradeStatus();
        String orderNo = orderInfo.getOrderNo();
        if (TaoBaoOrderStatus.FRONT_NOPAID_FINAL_NOPAID.equalsIgnoreCase(stepTradeStatus)) {
            remark = "订单不满足订单转换条件,标记为已转换";
            this.ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo, TransferOrderStatus.TRANSFERRED, remark);
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId()
                    + "状态=已转换，订单不满足订单转换条件,标记为已转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        }
        this.ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo, TransferOrderStatus.TRANSFERRED, remark);
        return new ProcessStepResult<>(StepStatus.FINISHED);
    }
}
