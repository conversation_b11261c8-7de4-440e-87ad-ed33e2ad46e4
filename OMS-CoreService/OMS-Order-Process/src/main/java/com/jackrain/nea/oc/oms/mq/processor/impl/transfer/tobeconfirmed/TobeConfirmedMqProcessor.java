package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.tobeconfirmed;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.tobeconfirm.ToBeConfirmedOrderProcess;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 进行占单处理消息处理器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @author: 易邵峰
 * @since: 2019-03-06
 * create at : 2019-03-06 15:28
 */
@Slf4j
public class TobeConfirmedMqProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private OmsOrderService orderService;

    @Autowired
    private ToBeConfirmedOrderProcess toBeConfirmedOrderProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        long orderId = orderMqInfo.getOrderId();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("进行占单处理消息处理器.Start", orderId));
        }

        OcBOrderRelation taobaoOrderRelation = this.orderService.selectOmsOrderInfoOccupy(orderId);
        if (taobaoOrderRelation == null) {
            log.error(LogUtil.format("TobeConfirmedMqProcessor.Error.Not.Exist.OrderId={}", orderId, orderId));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = toBeConfirmedOrderProcess.start(taobaoOrderRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("TobeConfirmedMqProcessor.Finished.Result={}", orderId),resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TOBE_CONFIRMED;
    }
}
