package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 10:00 下午
 * @Version 1.0
 */
@Step(order = 20, description = "对原单以及sku的不存在的处理")
@Slf4j
@Component
public class Step020CheckExchangeStatus extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        try {
            //判断换货sku是否存在
            ProductSku productSku = orderInfo.getProductSku();
            if (productSku == null) {
                String message = "换货sku在系统中不存在";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                        ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FAILED, message);

            }
            if (SkuType.GIFT_PRODUCT == productSku.getSkuType()) {
                String message = "换货sku为福袋商品,不支持换货";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                        ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FAILED, message);
            }
            //查找原单信息
            List<OmsOrderExchangeRelation> originalSingleOrder = orderInfo.getOriginalSingleOrder();
            if (CollectionUtils.isEmpty(originalSingleOrder)) {
                //原单信息不存在超过三天
                String message = "";
                if (checkReturnOrderData(ipBTaobaoExchange)) {
                    message = SysNotesConstant.SYS_REMARK2;
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                            ipBTaobaoExchange);
                } else {
                    message = SysNotesConstant.SYS_REMARK1;
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                            message, ipBTaobaoExchange);
                }
                return new ProcessStepResult<>(StepStatus.FAILED, message + "转换失败");
            }
            //判断原始订单是否还有未平台发货的订单(拆单)
            List<OmsOrderExchangeRelation> exchangeRelationList = originalSingleOrder.stream().filter(p ->
                    OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(p.getOcBOrder().getOrderStatus())
                            || OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(exchangeRelationList) || originalSingleOrder.size() != exchangeRelationList.size()) {
                String message = "原始订单有未平台发货或者仓库发货的订单,无法转换";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(),
                        message, ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FAILED, message);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "判断原单状态及sku信息成功,进入下一阶段");
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(ipBTaobaoExchange, e.getMessage());
            log.error(LogUtil.format("退换货转换检验原单以及sku信息异常:{}", "退换货转换检验原单以及sku信息异常"), Throwables.getStackTraceAsString(e));
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }


    /**
     * 检验原单不存在的时间
     *
     * @return
     */
    private boolean checkReturnOrderData( IpBTaobaoExchange ipBTaobaoExchange) {
        Date date = new Date();
        //判断退单创建时间是否超过三天
        Date created = ipBTaobaoExchange.getCreationdate();
        Long threeDays = 5 * 24 * 60 * 60 * 1000L + created.getTime();
        return threeDays < date.getTime();
    }


}
