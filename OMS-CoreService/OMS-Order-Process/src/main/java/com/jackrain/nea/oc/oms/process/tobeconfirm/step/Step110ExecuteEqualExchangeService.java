package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsEqualExchangeStService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2022/7/11 下午2:31
 * @Version 1.0
 */
@Step(order = 110, description = "执行对等换货策略")
@Slf4j
@Component
public class Step110ExecuteEqualExchangeService extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsEqualExchangeStService omsEqualExchangeStService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        try {
            if (OmsOrderUtil.wdtPlatformSend(ocBOrder)) {
                // 增加操作日志
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), "旺店通平台下推的订单 不执行对等换货策略", null, null, operateUser);
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }

            //toc残次订单跳过
            if (OmsOrderUtil.isToCCcOrder(orderInfo.getOrderInfo())){
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }

            OcBOrderParam param = new OcBOrderParam();
            param.setOcBOrder(orderInfo.getOrderInfo());
            param.setOrderItemList(orderInfo.getOrderItemList());
            boolean b = omsEqualExchangeStService.equalExchangeStService(param, operateUser);
            if (b) {
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemListOccupy(ocBOrder.getId());
                orderInfo.setOrderItemList(orderItems);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("执行对等换货策略失败:{}", ocBOrder.getId(), "执行对等换货策略失败"), Throwables.getStackTraceAsString(e));
            OcBOrder errorOrderInfo = new OcBOrder();
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage("执行对等换货策略失败"));
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_50);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, "执行对等换货策略失败");
        }
    }
}
