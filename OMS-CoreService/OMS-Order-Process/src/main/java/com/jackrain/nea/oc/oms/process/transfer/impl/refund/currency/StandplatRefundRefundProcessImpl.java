package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * @author: 夏继超
 * @since: 2019/7/13
 * create at : 2019/7/13 15:17
 */
@Component
public class StandplatRefundRefundProcessImpl extends AbstractOrderProcess<OmsStandPlatRefundRelation> {

    public StandplatRefundRefundProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "currency";
    }

    @Override
    protected long getProcessOrderId(OmsStandPlatRefundRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(OmsStandPlatRefundRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.CURRENCY_REFUND;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.STANDARD;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    /**
     * 通用订单接口，平台单号
     *
     * @param orderInfo 订单单据
     * @return
     */
    @Override
    protected String getSourceTid(OmsStandPlatRefundRelation orderInfo) {
        return orderInfo.getSourceTid();
    }
}