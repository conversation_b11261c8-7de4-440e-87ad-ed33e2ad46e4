package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.normal;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx.TaobaoFxTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoFxService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 淘宝分销转单消息处理器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @author: 周琳胜
 * @since: 2019-07-22
 * create at : 2019-07-22 15:28
 */
@Slf4j
public class TaobaoFxTransferMqOrderDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpTaobaoFxService ipTaobaoFxOrderService;

    @Autowired
    private TaobaoFxTransferOrderProcessImpl taobaoFxTransferOrderProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("TaobaoFxTransferMqOrderDetailProcessor.Start", orderNo));
        }

        IpTaobaoFxOrderRelation taobaoFxOrderRelation = this.ipTaobaoFxOrderService.selectTaobaoFxOrder(orderNo);
        if (taobaoFxOrderRelation == null || taobaoFxOrderRelation.getIpBTaobaoFxOrder() == null) {
            log.error(LogUtil.format("TaobaoFxTransferMqOrderDetailProcessor.Error.Not.Exist", orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = taobaoFxTransferOrderProcess.start(taobaoFxOrderRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("TaobaoFxTransferMqOrderDetailProcessor.Finished,Result={}", orderNo), resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.TAOBAO
                && orderMqInfo.getOrderType() == OrderType.FXORDER;
    }
}
