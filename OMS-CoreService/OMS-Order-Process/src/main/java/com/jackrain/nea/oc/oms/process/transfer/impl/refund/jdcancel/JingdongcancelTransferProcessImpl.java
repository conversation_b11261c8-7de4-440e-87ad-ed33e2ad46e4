package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jdcancel;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongCancelRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * @description: 京东取消订单转换
 * @author: 郑小龙
 * @date: 2020-05-29 11:38
 **/
@Component
public class JingdongcancelTransferProcessImpl extends AbstractOrderProcess<IpJingdongCancelRelation> {

    public JingdongcancelTransferProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "jdcancel";
    }

    @Override
    protected long getProcessOrderId(IpJingdongCancelRelation orderInfo) {
        return orderInfo.getPopafsrefundapplyid();
    }

    @Override
    protected String getProcessOrderNo(IpJingdongCancelRelation orderInfo) {
        return orderInfo.getSourceCode();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JINGDONG_CANCEL_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.JINGDONG;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return null;
    }

    /**
     * 京东取消订单接口，平台单号
     *
     * @param orderInfo 订单单据
     * @return
     */
    @Override
    protected String getSourceTid(IpJingdongCancelRelation orderInfo) {
        return orderInfo.getSourceCode();
    }
}
