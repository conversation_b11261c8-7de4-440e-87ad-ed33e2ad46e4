//package com.jackrain.nea.oc.oms.util;
//
//import com.jackrain.nea.redis.util.RedisOpsUtil;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * Redis 锁定订单工具类
// *
// * @author: 易邵峰
// * @since: 2019-01-28
// * create at : 2019-01-28 09:13
// */
//@Component
//public class RedisLockOrderUtil {
//
//    /**
//     * 操作Redis Util工具类
//     */
//    @Autowired
//    private RedisOpsUtil<String, String> redisUtil;
//
//    /**
//     * 锁定订单的KeyValue值。默认为1
//     */
//    private static final String LOCKED_ORDER_VALUE = "1";
//
//    /**
//     * 默认1分钟
//     */
//    private static final int LOCK_ORDER_AUTO_TIMEOUT = 60 * 1000;
//
//    /**
//     * 默认锁定消息
//     */
//    private static final String DEFAULT_LOCK_ORDER_MESSAGE = "SUCCESS";
////
////
////    /**
////     * 锁定单据对象
////     *
////     * @param orderId       锁定对象ID
////     * @param lockOrderType 锁定订单类型
////     * @return 锁定结果
////     */
////    public RedisOperateResult lockOrder(long orderId, LockOrderType lockOrderType) {
////        String lockKeyName = ProcessRedisKeyResources.buildAuditLockOrderKey(orderId, lockOrderType);
////        if (isLockedOrder(orderId, lockOrderType)) {
////            String errorMessage = "OrderId=" + orderId + " Is Locked! KeyName=" + lockKeyName + " Is Exist!";
////            return new RedisOperateResult(false, lockKeyName, errorMessage);
////        } else {
////            redisUtil.strRedisTemplate.opsForValue().set(lockKeyName, LOCKED_ORDER_VALUE, LOCK_ORDER_AUTO_TIMEOUT,
////                    TimeUnit.MILLISECONDS);
////            return new RedisOperateResult(true, lockKeyName, DEFAULT_LOCK_ORDER_MESSAGE);
////        }
////    }
////
////    /**
////     * 解锁单据对象
////     *
////     * @param orderId       锁定对象ID
////     * @param lockOrderType 锁定订单类型
////     * @return
////     */
////    public RedisOperateResult unlockOrder(long orderId, LockOrderType lockOrderType) {
////        String lockKeyName = ProcessRedisKeyResources.buildAuditLockOrderKey(orderId, lockOrderType);
////        redisUtil.strRedisTemplate.delete(lockKeyName);
////        return new RedisOperateResult(true, lockKeyName, DEFAULT_LOCK_ORDER_MESSAGE);
////    }
//
//    /**
//     * 获取单据锁定超时时间
//     *
//     * @return 单据锁定超时时间
//     */
//    public long getLockOrderTimeOut() {
//        return LOCK_ORDER_AUTO_TIMEOUT;
//    }
//
////    /**
////     * 是否已经单据
////     *
////     * @param orderId       锁定对象ID
////     * @param lockOrderType 锁定订单类型
////     * @return true-已锁定；false-未锁定
////     */
////    public boolean isLockedOrder(long orderId, LockOrderType lockOrderType) {
////        String lockKeyName = ProcessRedisKeyResources.buildAuditLockOrderKey(orderId, lockOrderType);
////        return redisUtil.strRedisTemplate.hasKey(lockKeyName);
////    }
//
//}
