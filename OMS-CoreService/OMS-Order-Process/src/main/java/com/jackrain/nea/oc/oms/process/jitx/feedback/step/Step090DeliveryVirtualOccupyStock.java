package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-03-11 21:30
 * @Description : 寻仓单虚拟占单
 **/
@Step(order = 90, description = "寻仓单虚拟占单")
@Slf4j
@Component
public class Step090DeliveryVirtualOccupyStock extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {

    @Value("${r3.oc.oms.delivery.virtual.occupy.exclude.tag:true}")
    private boolean excludeTag;

    @Value("${r3.oc.oms.delivery.virtual.occupy.exclude.warehouse:ZJSD0001}")
    private String excludeWarehouseEcode;

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation deliveryInfo,
                                                                  ProcessStepResult preStepResult,
                                                                  boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxDeliveryRelation> stepResult = new ProcessStepResult<>();
        String orderNo = deliveryInfo.getOrderNo();
        String rootOrderSn = deliveryInfo.getRootOrderSn();
        RedisReentrantLock redisLock = deliveryInfo.getRedisLock();
        List<String> orderSnList = deliveryInfo.getUnsyncOrderSnList();
        try{
            String orderSnStr = StringUtils.join(deliveryInfo.getUnsyncOrderSnList(), ",");
            SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest = ipJitxDeliveryService.buildDeliveryOccupyStockRequest(deliveryInfo,operateUser);
            sgFindSourceStrategyC2SRequest.setSkuItems(ipJitxDeliveryService.buildDeliveryVirtualOccupyStockRequestItem(deliveryInfo));
            sgFindSourceStrategyC2SRequest.setSourceBillNo(orderSnStr);
            sgFindSourceStrategyC2SRequest.setTid(rootOrderSn);
            sgFindSourceStrategyC2SRequest.setInventedOccupy(true);
            if(excludeTag){
                sgFindSourceStrategyC2SRequest.setExcludeWarehouseEcode(excludeWarehouseEcode);
            }
            ValueHolderV14 v14 = ipJitxDeliveryService.deliveryVirtualOccupyStockSendMq(sgFindSourceStrategyC2SRequest,orderSnList);
            if(v14.isOK()){
                stepResult.setStatus(StepStatus.FINISHED);
                stepResult.setMessage("寻源虚拟占单中");
                return stepResult;
            }
        }catch (Exception e){
            log.error(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单消息发送失败:{}","Step090DeliveryVirtualOccupyStock",rootOrderSn,orderNo),
                    rootOrderSn, Throwables.getStackTraceAsString(e));
            //更新寻仓反馈状态为：未处理
            ipJitxDeliveryService.updateJitxSyncStatus(orderNo, SyncStatus.UNSYNC, "寻仓虚拟占单消息发送失败，手动置为未处理");
        }finally {
            redisLock.unlock();
        }
        stepResult.setStatus(StepStatus.FAILED);
        stepResult.setMessage("寻仓单虚拟占单消息发送失败，转换终止");
        return stepResult;
    }
}
