package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 8:21 下午
 * @Version 1.0
 */
@Step(order = 40, description = "发货前处理")
@Slf4j
@Component
public class Step040OrderGoodsFront extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {

    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo,
                                                                   ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Step040OrderGoodsFront.startProcess.orderInfo:{};",
                    "Step040OrderGoodsFront"), JSON.toJSONString(orderInfo));
        }
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
        try {
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            OcBOrder ocBOrder = omsOrderRelation.get(0).getOcBOrder();
            int goodsFrontCount = 0;
            int success = 0;
            int statusNo = 0;
            boolean flag = false;
            List<OmsOrderRelation> orderRelations = new ArrayList<>();

            // 周期购先处理子单，再处理母单
            for (int i = 0; i < omsOrderRelation.size(); i++) {
                try {
                    OmsOrderRelation orderRelation = omsOrderRelation.get(i);
                    OcBOrder order = orderRelation.getOcBOrder();
                    Integer orderStatus = order.getOrderStatus();
                    String suffixInfo = order.getSuffixInfo();
                    Integer orderMark = orderRelation.getOrderMark();
                    //判断订单是否是退款成功作废的订单
                    boolean voidOrder = (OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                            || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus)) && "REFUND-VOID".equals(suffixInfo);
                    if (voidOrder) {
                        success++;
                        goodsFrontCount++;
                        continue;
                    }

                    // 多单
                    // 1. 客服手工取消   "REFUND-VOID"-手工取消标记
                    // 2. 买家申请退款
                    if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode().equals(orderMark)) {
                        //  周期购先处理子单，再处理母单
                        if(!checkIsCyclePurchaseOrder(order)){
                            Boolean shipmentReturn = this.beforeShipmentReturn(orderRelation, ipBTaobaoRefund, operateUser);
                            if (shipmentReturn == null) {
                                statusNo++;
                                continue;
                            }
                            if (shipmentReturn) {
                                goodsFrontCount++;
                            }
                            //记录原订单信息 -》执行赠品后发
                            orderRelations.add(orderRelation);
                            success++;
                        }
                    }
                } catch (Exception e) {
                    flag = true;
                    log.error(LogUtil.format("发货前退款异常,退款单号:{},异常信息:{}", "订单处理异常"), ipBTaobaoRefund.getRefundId(), Throwables.getStackTraceAsString(e));
                    if (e != null && SysNotesConstant.SYS_REMARK46 == e.getMessage()) {
                        TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.DE_AUDIT);
                    }
                    ipTaobaoRefundService.updateRefundIsTransError(ipBTaobaoRefund, e.getMessage());
                }
            }

            if(!flag) {
                // 获取发货前退款单母单信息
                List<OmsOrderRelation> parentOrderList = omsOrderRelation.stream().filter(p -> {
                    // 周期购母单，发货前退款
                    if (checkIsCyclePurchaseOrder(p.getOcBOrder()) && TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode().equals(p.getOrderMark())) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());

                if(CollectionUtils.isNotEmpty(parentOrderList)) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Step040OrderGoodsFront.startProcess.parentOrderList:{};",
                                "Step040OrderGoodsFront"), JSON.toJSONString(parentOrderList));
                    }
                    // 这里处理母单，如果子单全部取消，母单也更新为取消
                    for (int i = 0; i < parentOrderList.size(); i++) {
                        try {
                            if (handleCycleOrder(ipBTaobaoRefund, ocBOrder, parentOrderList.get(i))) {
                                success++;
                            }
                        } catch (Exception e) {
                            flag = true;
                            log.error(LogUtil.format("发货前退款处理周期购母单异常,退款单号:{},异常信息:{}", "订单处理异常"), ipBTaobaoRefund.getRefundId(), Throwables.getStackTraceAsString(e));
                            ipTaobaoRefundService.updateRefundIsTransError(ipBTaobaoRefund, e.getMessage());
                        }
                    }
                }
            }

            if (flag) {
                //存在多个订单时 只要有一单在发货前处理失败 就结束转换
                return new ProcessStepResult<>(StepStatus.FAILED, "发货前退款失败!");
            }
            // 赠品,赠品订单等待卖家同意处理
            // 原有逻辑的情况下, 添加赠品订单Hold
            boolean isWaitAgree = TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(ipBTaobaoRefund.getStatus());
            if (isWaitAgree){
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("淘宝等待卖家同意,赠品订单流程,发货前Hold单", ipBTaobaoRefund.getRefundId()));
                }
                if (CollectionUtils.isNotEmpty(orderInfo.getIsGiftOrderRelation())){
                    for (OmsOrderRelation orderRelation : orderInfo.getIsGiftOrderRelation()) {
                        omsBeforeShipmentReturnService.waitSellerAgreeGiftOrderHold(orderRelation, operateUser);
                    }
                    ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), SysNotesConstant.SYS_REMARK17, ipBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "发货前退款,等待卖家同意,订单处理完成");
                }
            }

            if (goodsFrontCount == omsOrderRelation.size()) {
                omsRefundOrderService.foundRefundFrontRefundOnly(ocBOrder, ipBTaobaoRefund, operateUser);
                omsReturnOrderService.giftsThenSend(orderRelations, orderInfo.getIsGiftOrderRelation(), orderInfo.getIntermediateTableRelation(), operateUser);

                //全部退款成功后 调用AG
                return new ProcessStepResult<>(StepStatus.SUCCESS, "订单处理成功,进入下一阶段", Step080ToAgRefund.class);
            }
            if (success == omsOrderRelation.size()) {
                //生成发货前退款单
                omsRefundOrderService.foundRefundFrontRefundOnly(ocBOrder, ipBTaobaoRefund, operateUser);
                omsReturnOrderService.giftsThenSend(orderRelations, orderInfo.getIsGiftOrderRelation(), orderInfo.getIntermediateTableRelation(), operateUser);

                String remark = SysNotesConstant.SYS_REMARK17;
                TransRefundNodeTipUtil.taoBaoTransTipCAS(ipBTaobaoRefund, TransNodeTipEnum.DEFAULT);
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBTaobaoRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "发货前退款,订单处理完成");
            }
            if (statusNo == omsOrderRelation.size()) {
                String remark = SysNotesConstant.SYS_REMARK27;
                ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBTaobaoRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "发货前退款,状态不满足,退出转换");
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "订单处理成功,进入下一阶段");

        } catch (Exception e) {
            ipTaobaoRefundService.updateRefundIsTransError(ipBTaobaoRefund, e.getMessage());
            log.error(LogUtil.format("发货前退款异常,退款单号:{},异常信息:{}", "订单处理异常"), ipBTaobaoRefund.getRefundId(), Throwables.getStackTraceAsString(e));
            return new ProcessStepResult<>(StepStatus.FAILED, "发货前退款处理失败!" + e.getMessage());
        }
    }

    /**
     * 周期购订单处理
     */
    private Boolean handleCycleOrder(IpBTaobaoRefund ipBTaobaoRefund, OcBOrder ocBOrder, OmsOrderRelation orderRelation) {

        // 针对周期购的订单，母单为虚拟单，且状态为待分配此处需要增加处理 20230227
        if (checkIsCyclePurchaseOrder(ocBOrder) && OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(ocBOrder.getOrderStatus())) {
            // 如果子单都已取消，则更新母单为已取消
            List<Long> subCycleOrderIdList = ES4Order.searchCyclePurchaseSubOrderIds(ocBOrder.getBillNo(), null);
            if (CollectionUtils.isNotEmpty(subCycleOrderIdList)) {
                List<OcBOrder> subList = omsOrderService.selectOrderListByIdsList(subCycleOrderIdList);
                if (CollectionUtils.isEmpty(subList)) {
                    return Boolean.FALSE;
                }
                boolean isAllCancel = true;
                for (OcBOrder p : subList) {
                    if (!OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())) {
                        isAllCancel = false;
                        break;
                    }
                }
                // 当子单是全部取消单状态，则将母单也进行更新进行更新
                if (isAllCancel) {
                    cancelOrder(ocBOrder);
                    List<Long> itemIds = orderRelation.getOcBOrderItems().stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                    //将当前明细的金额置0  取消状态变成已取消
                    ocBOrderItemMapper.updateOrderItemAmt(ocBOrder.getId(), itemIds);
                    return Boolean.TRUE;
                }
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 判断订单是否周期购订单
     *
     * @param orderInfo 订单信息
     * @return true=周期购
     */
    private boolean checkIsCyclePurchaseOrder(OcBOrder orderInfo) {
        return OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(orderInfo.getBusinessTypeCode())
                || OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER.getCode().equals(orderInfo.getBusinessTypeCode());
    }


    /**
     * 将订单置为取消状态
     *
     * @param ocBOrder
     */
    private void cancelOrder(OcBOrder ocBOrder) {
        OcBOrder orderUpdate = new OcBOrder();
        orderUpdate.setId(ocBOrder.getId());
        orderUpdate.setAdjustAmt(BigDecimal.ZERO);
        // 商品总额”：明细中“标准价”*（“商品数量”-“已退数量”）的绝对值合计
        orderUpdate.setProductAmt(BigDecimal.ZERO);
        // 商品数量”：明细“商品数量”-“已退数量”合计
        orderUpdate.setQtyAll(BigDecimal.ZERO);
        // 优惠金额”：明细“优惠金额”合计
        orderUpdate.setProductDiscountAmt(BigDecimal.ZERO);

        //订单优惠金额”：明细“整单平摊金额”合计
        orderUpdate.setOrderDiscountAmt(BigDecimal.ZERO);
        // 配送费用：更新为0
        orderUpdate.setShipAmt(BigDecimal.ZERO);
        // 服务费
        orderUpdate.setServiceAmt(BigDecimal.ZERO);
        // 已付金额 :订单总额：商品总额+配送费用+调整金额+服务费-订单优惠金额-商品优惠金额
        orderUpdate.setOrderAmt(BigDecimal.ZERO);
        // 已收金额
        orderUpdate.setReceivedAmt(BigDecimal.ZERO);
        orderUpdate.setSuffixInfo("REFUND-VOID");
        orderUpdate.setOrderStatus(OmsOrderStatus.CANCELLED.toInteger());
        ocBOrderMapper.updateById(orderUpdate);
    }


    /**
     * 处理发货前的逻辑
     *
     * @param orderRelation
     */
    private Boolean beforeShipmentReturn(OmsOrderRelation orderRelation, IpBTaobaoRefund ipBTaobaoRefund, User user) {
        OcBOrder ocBOrder = orderRelation.getOcBOrder();

        //订单状态为待分配时 为防止虚拟商品拆单  对订单暂不拦截 为能及时处理退款 发送1分钟的延时消息 等待再次消费
        if (OmsOrderStatus.ORDER_DEFAULT.toInteger().equals(ocBOrder.getOrderStatus())) {
            this.sendDelayMq(ipBTaobaoRefund);
            throw new NDSException("订单状态为待分配,等待下次转换");
        } else if (OmsOrderStatus.OCCUPY_IN.toInteger().equals(ocBOrder.getOrderStatus())) {
            this.sendDelayMq(ipBTaobaoRefund);
            throw new NDSException("订单状态为寻源中,等待下次转换");
        } else {
            // HOLD单
            OcBOrder order = new OcBOrder();
            //是否退款中
            order.setIsInreturning(1);
            order.setId(ocBOrder.getId());
            omsOrderService.updateOrderInfo(order);
            //是否已经拦截 Hold单统一调用 HOLD单方法
            order.setIsInterecept(1);
            order.setBillNo(ocBOrder.getBillNo());
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
        }
        //淘宝退单中间表的退单状态
        String status = ipBTaobaoRefund.getStatus();
        if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)) {
            //买家已经申请退款，等待卖家同意
            return omsBeforeShipmentReturnService.taoBaoRefundStatusAgree(orderRelation, user);
        } else if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
            //退款成功
            omsBeforeShipmentReturnService.refundStatusIsSuccess(orderRelation, user);
            return false;
        } else if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status)) {
            //退款关闭
            omsBeforeShipmentReturnService.orderStatusIsClosed(orderRelation, user);
            return false;
        }
        return null;
    }

    /**
     * 延时1分钟再次发送退单转换mq
     *
     * @param ipBTaobaoRefund
     */
    private void sendDelayMq(IpBTaobaoRefund ipBTaobaoRefund) {
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
        orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
        orderMqInfo.setChannelType(ChannelType.TAOBAO);
        orderMqInfo.setOrderType(OrderType.REFUND);
        orderMqInfo.setOrderNo(ipBTaobaoRefund.getRefundId());
        mqInfoList.add(orderMqInfo);
        String jsonValue = JSONObject.toJSONString(mqInfoList);
        try {
            // fixme：tag 为空；控制台表示为n/a
            log.info("beforeShipmentReturn.sendDelayMq");
//            r3MqSendHelper.sendDelayMessage(jsonValue, transferOrderMqConfig.getSendMqTopic(), transferOrderMqConfig.getSendMqTag(), 60000L);
            defaultProducerSend.sendDelayTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_TRANSFER, null, jsonValue, null, 60000L);
        } catch (MqException e) {
            e.printStackTrace();
        }
    }
}
