package com.jackrain.nea.oc.oms.process.transfer.impl.lock.lockorder.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OcOrderCheckBoxEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLockLogTypeEnum;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption 处理锁单根据不同的订单状态
 * <AUTHOR>
 * @Date 2019/10/10 14:44
 */
@Step(order = 20, description = "处理锁单根据不同的订单状态")
@Slf4j
@Component
public class Step020LockByOrderStatus extends BaseLockProcessStep
        implements IOmsOrderProcessStep<IpOrderLockRelation> {
    @Override
    public ProcessStepResult<IpOrderLockRelation> startProcess(IpOrderLockRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.info(this.getClass().getName() + " 处理锁单Id{}根据不同的订单状态", orderInfo.getOrderId());
        IpBOrderLock orderLock = orderInfo.getOrderLock();
        List<OcBOrder> orderList = orderInfo.getOcBOrders();
        try {
            for (OcBOrder order : orderList) {
                boolean lockFlag = ipOrderLockService.LockByOrderStatus(order, orderLock, operateUser);
                if (lockFlag) {
                    //1.锁单成功插入中间表日志
                    String errMsg = "订单状态为" + OcOrderCheckBoxEnum.enumToStringByValue(order.getOrderStatus()) + "，锁单成功;";

                    ipOrderLockService.insetIpOrderLockLog(OrderLockLogTypeEnum.LOCK.getKey(), errMsg, orderLock.getId(), order.getId(), operateUser);
                    //2.修改渠道订单锁单状态:锁单成功并插入订单日志
                    ipOrderLockService.updateOrderLockStatus(orderLock, order, operateUser, OcOrderLockStatusEnum.LOCKED.getKey());

                }
            }

            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("锁单异常:{}", "锁单异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipOrderLockService.updateLockErrorLog(orderLock);
            return new ProcessStepResult<>(StepStatus.FAILED, " 锁单异常：" + e.getMessage());
        }
    }
}
