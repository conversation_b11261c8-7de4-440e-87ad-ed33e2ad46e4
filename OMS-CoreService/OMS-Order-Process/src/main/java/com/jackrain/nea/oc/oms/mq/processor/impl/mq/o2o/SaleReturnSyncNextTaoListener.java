package com.jackrain.nea.oc.oms.mq.processor.impl.mq.o2o;

import com.aliyun.openservices.ons.api.Message;
import com.jackrain.nea.oc.request.o2o.SyncReturnResponse;
import org.springframework.stereotype.Component;


/**
 * @Desc : 门店发货销退单同步mq回执
 * <AUTHOR> xiWen
 * @Date : 2020/8/22
 */
@Component
public class SaleReturnSyncNextTaoListener extends AbsOmsSyncThirdSysCallBackListener {


    @Override
    protected String getOrigTableName(String method) {
        return "OC_B_RETURN_ORDER";
    }

    @Override
    protected String getOrigStatusCol(String method) {
        return "RESERVE_BIGINT09";
    }

    @Override
    protected String getOrigSendTimesCol(String method) {
        return "RESERVE_BIGINT10";
    }

    @Override
    protected boolean doCallBack(Message message, SyncReturnResponse response) {
        return super.doCallBack(message, response);
    }
}
