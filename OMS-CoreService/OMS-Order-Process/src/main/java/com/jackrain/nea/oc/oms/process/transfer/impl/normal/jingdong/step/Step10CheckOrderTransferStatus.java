package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-25
 * create at : 2019-04-25 11:33 AM
 * 判断京东订单单据转换状态
 */
@Step(order = 10, description = "判断京东订单单据转换状态")
@Slf4j
@Component
public class Step10CheckOrderTransferStatus extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {

    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo,
                                                                   ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {
        if (orderInfo == null || orderInfo.getJingdongOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者Order.JingdongOrder为空；退出转换");
        }
        Integer currentStatus = orderInfo.getJingdongOrder().getIstrans();
        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERRING.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "正在转换中");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            ProcessStepResult<IpJingdongOrderRelation> result = getHistoryOrderProcessStepResult(orderInfo);
            if (result != null) {
                return result;
            }
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage, Step20CheckOrderTransactionStatus.class);
        }
    }

    /**
     * 历史订单的处理
     *
     * @param orderInfo
     * @return
     */
    private ProcessStepResult<IpJingdongOrderRelation> getHistoryOrderProcessStepResult(IpJingdongOrderRelation orderInfo) {
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        String pullHistoryOrder = config.getProperty("pull_jd_HistoryOrder_shopId", "");
        IpBJingdongOrder jingdongOrder = orderInfo.getJingdongOrder();
        Long cpCShopId = jingdongOrder.getCpCShopId();
        if (StringUtils.isEmpty(pullHistoryOrder) || cpCShopId == null) {
            return null;
        }
        String[] split = pullHistoryOrder.split(",");
        List<String> shopIds = new ArrayList<>(Arrays.asList(split));
        if (shopIds.contains(cpCShopId + "")) {
            String orderState = orderInfo.getJingdongOrder().getOrderState();
            boolean isHistoryOrder = StringUtils.equalsIgnoreCase(JingdongOrderStatus.WAIT_GOODS_RECEIVE_CONFIRM, orderState)
                    || StringUtils.equalsIgnoreCase(JingdongOrderStatus.FINISHED_L, orderState);
            if (!isHistoryOrder) {
                return new ProcessStepResult<>(StepStatus.FINISHED, "历史订单开关已打开,只转换交易完成及等待买家确认收货的状态");
            }
        }
        return null;
    }
}