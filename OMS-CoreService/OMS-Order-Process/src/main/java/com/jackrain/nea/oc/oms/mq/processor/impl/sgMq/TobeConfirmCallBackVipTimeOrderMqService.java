package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsForVipTimeResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsItemForVipTimeResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.TOmsvipfulladdress;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.wing.WingReturnResult;
import com.jackrain.nea.ip.model.wing.yy.JitxSxOrderAddRequest;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.mapper.task.OcBJitxDealerOrderTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBJitxDealerOrderTask;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.services.time.order.IpVipTimeOrderOccupyItemService;
import com.jackrain.nea.oc.oms.services.time.order.SubWarehouseService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.vo.PhyWarehouseVo;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhuxing
 * @Date : 2021-08-23 16:33
 * @Description : 时效订单、寻仓单 - 占单返回结果MQ 逻辑处理
 **/
@Slf4j
@Component
public class TobeConfirmCallBackVipTimeOrderMqService {

    @Autowired
    private IpBTimeOrderVipMapper ipBTimeOrderVipMapper;

    @Autowired
    private IpBCancelTimeOrderVipMapper cancelTimeOrderVipMapper;

    @Autowired
    private IpBTimeOrderVipOccupyItemMapper ipBTimeOrderVipOccupyItemMapper;

    @Autowired
    private IpBJitxDeliveryMapper ipBJitxDeliveryMapper;

    @Autowired
    private IpBJitxDeliveryItemMapper ipBJitxDeliveryItemMapper;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private SubWarehouseService subWarehouseService;
    @Autowired
    protected IpBTimeOrderVipOccupyItemMapper vipOccupyItemMapper;

    @Autowired
    private IpVipTimeOrderOccupyItemService timeOrderOccupyItemService;

    @Autowired
    private OcBJitxDealerOrderTaskMapper dealerOrderTaskMapper;

    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    protected IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private OcBJitxDealerOrderTaskService jitxDealerOrderTaskService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    public static final String TABLE_NAME = "ip_b_time_order_vip_occupy_item";


    /**
     * 时效订单占单返回结果
     * @param sgFindSourceStrategyOmsForVipTimeResult
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void tobeConfirmTimeOrderCallBackService(SgFindSourceStrategyOmsForVipTimeResult sgFindSourceStrategyOmsForVipTimeResult){
        int code = sgFindSourceStrategyOmsForVipTimeResult.getCode();
        //来源单据ID
        Long sourceBillId =  sgFindSourceStrategyOmsForVipTimeResult.getSourceBillId();
        //来源单据单号
        String occupiedOrderSn = sgFindSourceStrategyOmsForVipTimeResult.getSourceBillNo();
        IpBTimeOrderVip timeOrderVip = new IpBTimeOrderVip();
        timeOrderVip.setId(sourceBillId);
        timeOrderVip.setOccupiedOrderSn(occupiedOrderSn);
        if(ResultCode.SUCCESS != code){
            String messge = sgFindSourceStrategyOmsForVipTimeResult.getMessage();
            log.error("唯品会时效订单 occupiedOrderSn:{}，库存中心占单异常 {}",occupiedOrderSn, messge);
            updateTimeOrderStatus(TransferOrderStatus.TRANSFERFAIL.toInteger(),TimeOrderVipStatusEnum.CREATED.getValue(),null,"库存中心占单异常",timeOrderVip);
            return;
        }
        //查询原单
        IpBTimeOrderVip ipBTimeOrderVip = this.ipBTimeOrderVipMapper.selectTimeOrderByOccupiedOrderSn(occupiedOrderSn);
        if (ipBTimeOrderVip ==null){
            log.info("唯品会时效订单 occupiedOrderSn:{}，单据不存在",occupiedOrderSn);
            return;
        }
        //占用明细
        List<SgFindSourceStrategyOmsItemForVipTimeResult> itemResultList = sgFindSourceStrategyOmsForVipTimeResult.getItemResultList();
        if(CollectionUtils.isEmpty(itemResultList)){
            log.error("唯品会时效订单 occupiedOrderSn:{}，占单结果为空",occupiedOrderSn);
            updateTimeOrderStatus(TransferOrderStatus.TRANSFERFAIL.toInteger(),TimeOrderVipStatusEnum.CREATED.getValue(),null,"占单结果为空",ipBTimeOrderVip);
            return;
        }
        String lockRedisKey = BllRedisKeyResources.buildVipTimeOrderLockOrderKey(sourceBillId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try{
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                updateTimeOrderStatus(ipBTimeOrderVip,itemResultList);
            }else {
                throw new NDSException("当前时效订单其他人正在操作,请稍后再试!");
            }
        }catch (Exception e){
            log.error("唯品会时效订单{}，占单处理异常 {}",sourceBillId, Throwables.getStackTraceAsString(e));
            updateTimeOrderStatus(TransferOrderStatus.TRANSFERFAIL.toInteger(),TimeOrderVipStatusEnum.CREATED.getValue(),null,Throwables.getStackTraceAsString(e),ipBTimeOrderVip);
            // fixme ???
//            return new MqProcessResult(true, "消息处理成功");
        }finally {
            redisLock.unlock();
        }
    }

    /**
     * 更新时效订单状态
     * @param timeOrderVip
     * @param itemResultList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTimeOrderStatus(IpBTimeOrderVip timeOrderVip,
                                      List<SgFindSourceStrategyOmsItemForVipTimeResult> itemResultList) {

        BigDecimal qty = BigDecimal.ZERO;
        //获取缺货的明细
        List<SgFindSourceStrategyOmsItemForVipTimeResult> stockList = itemResultList.stream().filter(x -> -1 == x.getSgCShareStoreId().intValue()).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(stockList)){
            BigDecimal qtyPreOut = stockList.get(0).getQtyPreOut();
            qty = qty.add(qtyPreOut);
        }
        //更新主表缺货数量及 单据状态，转换状态:转换成功
        if(qty.compareTo(BigDecimal.ZERO) >0){
            /**缺货*/
            updateTimeOrderStatus(TransferOrderStatus.TRANSFERRED.toInteger(),TimeOrderVipStatusEnum.OUT_STOCK.getValue(),qty,"占单缺货",timeOrderVip);
            //更新明细的缺货数量
            IpBTimeOrderVipOccupyItem updateOccupyItem = new IpBTimeOrderVipOccupyItem();
            updateOccupyItem.setOutStockQuantity(qty);
            vipOccupyItemMapper.update(updateOccupyItem,
                    new QueryWrapper<IpBTimeOrderVipOccupyItem>()
                            .eq("ip_b_time_order_vip_id",timeOrderVip.getId()));
        }else{
            /**占单成功*/
            updateTimeOrderStatus(TransferOrderStatus.TRANSFERRED.toInteger(),TimeOrderVipStatusEnum.OCCUPIED.getValue(),BigDecimal.ZERO,"占单成功",timeOrderVip);

            //判断是否YY经销商发货，是的话通知YY占单
            SgBShareOutSaveRequest request = new SgBShareOutSaveRequest();
            request.setSourceBillType(SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
            request.setSourceBillId(timeOrderVip.getId());
            ValueHolderV14<Map<Long,Boolean>> isYyV14 = sgRpcService.queryShareStoreIsYy(Collections.singletonList(request));
            if(isYyV14.isOK() && !ObjectUtils.isEmpty(isYyV14.getData()) && Boolean.TRUE.equals(isYyV14.getData().get(timeOrderVip.getId()))){
                OcBJitxDealerOrderTask dealerOrderTask = new OcBJitxDealerOrderTask();
                dealerOrderTask.setCpCShopId(timeOrderVip.getCpCShopId());
                dealerOrderTask.setCpCPlatformId(PlatFormEnum.VIP_JITX.getLongVal());
                dealerOrderTask.setBillNo(timeOrderVip.getBillNo());
                dealerOrderTask.setTid(timeOrderVip.getOrderSn());
                dealerOrderTask.setOrderType(JitxDealerTaskOrderTypeEnum.VIP_TIME_ORDER.getCode());

                //查询YY实体仓（虚仓）
                CpCPhyWarehouse phyWarehouse = cpRpcService.queryOmsWarehouseJStore("ZJSD0001");
                if(!ObjectUtils.isEmpty(phyWarehouse)){
                    dealerOrderTask.setCpCPhyWarehouseId(phyWarehouse.getId());
                }
                dealerOrderTask.setFailNumber(0);
                dealerOrderTask.setType(JitxDealerTaskTypeEnum.YY_OCCUPY.getCode());
                dealerOrderTask.setState(JitxDealerTaskStatusEnum.SUCCESS.getCode());
                jitxDealerOrderTaskService.save(dealerOrderTask, SystemUserResource.getRootUser());
                //TODO 调用wing接口
                try {
                    JitxSxOrderAddRequest addRequest = new JitxSxOrderAddRequest();
                    IpBTimeOrderVipOccupyItem orderItem = ipBTimeOrderVipOccupyItemMapper.selectOneOrderOccupyItem(timeOrderVip.getId());
                    addRequest.setVipOrder_sn(timeOrderVip.getOrderSn());
                    addRequest.setSku(orderItem.getPsCSkuEcode());
                    addRequest.setBarCode(orderItem.getBarcode());
                    BigDecimal occupyqty = Optional.ofNullable(orderItem.getAmount()).orElse(BigDecimal.ZERO);
                    addRequest.setQty(occupyqty.intValue());
                    addRequest.setTyWmsType(OcBJitxDealerOrderTaskService.YY_WMS_TYPE);
                    //根据地址编码查询唯品会对应的省市区名称
                    TOmsvipfulladdress tOmsvipfulladdress = cpRpcService.queryProvinceCityAreaNameByCode(timeOrderVip.getAddressCode());
                    if(tOmsvipfulladdress != null){
                        addRequest.setProvince(tOmsvipfulladdress.getProvinceName());
                        addRequest.setCity(tOmsvipfulladdress.getCityName());
                        addRequest.setArea(tOmsvipfulladdress.getAddressName());
                    }else {
                        addRequest.setProvince("");
                        addRequest.setCity("");
                        addRequest.setArea("");
                    }
                    addRequest.setAddress_Code(timeOrderVip.getAddressCode());
                    ValueHolderV14<WingReturnResult> v14 = ipRpcService.addSxJitxOrder(Lists.newArrayList(addRequest));
                    if (v14.isOK()) {
                        WingReturnResult wingReturnResult = v14.getData();
                        if (!wingReturnResult.isOk()) {
                            jitxDealerOrderTaskService.failUpdate(dealerOrderTask.getId(), wingReturnResult.getMsg());
                        } else {
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(wingReturnResult.getFaileds())) {
                                jitxDealerOrderTaskService.failUpdate(dealerOrderTask.getId(), wingReturnResult.getFaileds().get(0).getErrMsg());
                            }
                        }
                    } else {
                        jitxDealerOrderTaskService.failUpdate(dealerOrderTask.getId(), v14.getMessage());
                    }
                } catch (Exception e) {
                    jitxDealerOrderTaskService.failUpdate(dealerOrderTask.getId(), e.getMessage());
                }
            }
        }
    }

    /**
     * 寻仓单占单结果 - 数据校验
     * @param orderInfo
     * @param itemResultList
     * @return
     */
    public ValueHolderV14 checkDeliveryCallBackData(IpJitxDeliveryRelation orderInfo,
                                                    List<SgFindSourceStrategyOmsItemResult> itemResultList){
        ValueHolderV14 vh = ValueHolderV14Utils.getSuccessValueHolder("校验通过");
        String orderSn = orderInfo.getOrderNo();
        //寻仓单
        if(orderInfo == null || orderInfo.getJitxDelivery() == null){
            log.error("寻仓单 orderSn:{}，单据不存在",orderSn);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("寻仓单单据不存在！");
            return vh;
        }
        //寻仓单明细
        List<IpBJitxDeliveryItemEx> ipBJitxDeliveryItemExList = orderInfo.getJitxDeliveryItemList();
        if(CollectionUtils.isEmpty(ipBJitxDeliveryItemExList)){
            log.error("唯品会寻仓单 orderSn:{}，明细为空",orderSn);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("寻仓单占单结果更新失败，寻仓单明细不存在！");
            return vh;
        }
        //时效订单
        List<IpVipTimeOrderRelation> ipVipTimeOrderRelationList= orderInfo.getIpVipTimeOrderRelationList();
        if(CollectionUtils.isEmpty(ipVipTimeOrderRelationList)){
            log.error("唯品会寻仓单 orderSn:{}，对应的时效订单不存在",orderSn);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("寻仓单占单结果更新失败，时效订单不存在！");
            return vh;
        }
        //判断SG返回的库存占用明细与配货单明细是否全部能匹配上
        List<Long> sourceItemIdList = itemResultList.stream().map(SgFindSourceStrategyOmsItemResult::getSourceItemId).distinct().collect(Collectors.toList());
        List<Long> deliveryItemIdList = ipBJitxDeliveryItemExList.stream().map(IpBJitxDeliveryItemEx::getId).collect(Collectors.toList());
        List<Long> differItemIdList = sourceItemIdList.stream().filter(x -> !deliveryItemIdList.contains(x)).collect(Collectors.toList());
        if(sourceItemIdList.size() != deliveryItemIdList.size() || !CollectionUtils.isEmpty(differItemIdList)){
            log.error("唯品会寻仓单 orderSn:{}，SG返回的库存占用明细与寻仓单明细匹配不上",orderSn);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("寻仓单占单结果更新失败，SG返回的库存占用明细与寻仓单明细匹配不上！");
            return vh;
        }
        return vh;
    }


    /**
     * 寻仓单占单返回结果
     * @param sgFindSourceStrategyOmsResult
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void tobeConfirmDeliveryCallBackService(SgFindSourceStrategyOmsResult sgFindSourceStrategyOmsResult){
        int code = sgFindSourceStrategyOmsResult.getCode();
        //来源单据ID
        long sourceBillId = sgFindSourceStrategyOmsResult.getSourceBillId();
        String orderSn = sgFindSourceStrategyOmsResult.getSourceBillNo();

        if(ResultCode.SUCCESS != code){
            String messge = sgFindSourceStrategyOmsResult.getMessage();
            log.error("唯品会寻仓 orderSn:{}单寻源占单，库存中心占单返回异常",orderSn);
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn,
                    SyncStatus.UNSYNC, messge);
            //更新时效订单状态为：缺货
            ipVipTimeOrderService.updateIpBTimeOrderVipByOrderSn(orderSn,TimeOrderVipStatusEnum.OUT_STOCK.getValue(),"唯品会寻仓单寻源占单，库存中心占单异常");
            return;
        }
        //占单反馈占用明细
        List<SgFindSourceStrategyOmsItemResult> itemResultList = sgFindSourceStrategyOmsResult.getItemResultList();
        if(CollectionUtils.isEmpty(itemResultList)){
            String messge = "寻仓单占单结果更新失败，SG返回的占单明细为空！";
            log.error("唯品会寻仓单 orderSn:{}，占单结果为空",orderSn);
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn,
                    SyncStatus.UNSYNC, messge);
            //更新时效订单状态为：缺货
            ipVipTimeOrderService.updateIpBTimeOrderVipByOrderSn(orderSn,TimeOrderVipStatusEnum.OUT_STOCK.getValue(),"唯品会寻仓单寻源占单，库存中心占单异常");
            return;
        }
        //寻仓单
        IpJitxDeliveryRelation orderInfo = ipJitxDeliveryService.selectJitxDelivery(orderSn);

        //基础数据校验
        ValueHolderV14 holderV14 = checkDeliveryCallBackData(orderInfo,itemResultList);
        if(!holderV14.isOK()){
            String message = holderV14.getMessage();
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn,
                    SyncStatus.EXCEPTION, message);
            //更新时效订单状态为：缺货
            ipVipTimeOrderService.updateIpBTimeOrderVipByOrderSn(orderSn,TimeOrderVipStatusEnum.OUT_STOCK.getValue(),message);
            return;
        }
        //是否店发
        Integer isStoreDelivery = orderInfo.getJitxDelivery().getIsStoreDelivery();
        String lockRedisKey = BllRedisKeyResources.buildVipDeliveryLockOrderKey(sourceBillId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try{
            //寻仓单明细
            List<IpBJitxDeliveryItemEx> ipBJitxDeliveryItemExList = orderInfo.getJitxDeliveryItemList();
            //时效订单 《ID,List<IpBTimeOrderVip>》
            Map<Long, IpBTimeOrderVip> ipBTimeOrderVipMap = new HashMap<>();
            //Map<商品条码，时效订单明细>
            Map<String, List<IpBTimeOrderVipOccupyItem>> ipBTimeOrderVipItemMap = new HashMap<>();
            List<IpVipTimeOrderRelation> ipVipTimeOrderRelationList= orderInfo.getIpVipTimeOrderRelationList();
            for(IpVipTimeOrderRelation timeOrderRelation:ipVipTimeOrderRelationList){
                ipBTimeOrderVipMap.put(timeOrderRelation.getOrderId(),timeOrderRelation.getIpBTimeOrderVip());
                List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipItemList = timeOrderRelation.getIpBTimeOrderVipOccupyItemList();
                if(!CollectionUtils.isEmpty(ipBTimeOrderVipItemList)){
                    String barCode = ipBTimeOrderVipItemList.get(0).getBarcode();
                    ipBTimeOrderVipItemMap.put(barCode,ipBTimeOrderVipItemList);
                }
            }
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                Map<Long, List<IpBJitxDeliveryItemEx>> deliveryItemMap = ipBJitxDeliveryItemExList.stream()
                        .collect(Collectors.groupingBy(IpBJitxDeliveryItemEx::getId));
                Map<Long, List<SgFindSourceStrategyOmsItemResult>> resultItemMap = itemResultList.stream()
                        .collect(Collectors.groupingBy(SgFindSourceStrategyOmsItemResult::getSourceItemId));
                for(Long orderItemId:resultItemMap.keySet()){
                    //当前寻仓单明细对应的库存占用明细集合
                    List<SgFindSourceStrategyOmsItemResult> itemList = resultItemMap.get(orderItemId);
                    //寻仓单明细
                    List<IpBJitxDeliveryItemEx> IpBJitxDeliveryItemList = deliveryItemMap.get(orderItemId);
                    IpBJitxDeliveryItemEx ipBJitxDeliveryItemEx = IpBJitxDeliveryItemList.get(0);
                    //商品条码
                    String barCode = ipBJitxDeliveryItemEx.getBarcode();
                    //当前商品条码对应的时效订单明细
                    List<IpBTimeOrderVipOccupyItem> occupyItemList = ipBTimeOrderVipItemMap.get(barCode);
                    if(!CollectionUtils.isEmpty(occupyItemList)){
                        //时效订单ID
                        Long timeOrderVipId = occupyItemList.get(0).getIpBTimeOrderVipId();
                        //时效订单
                        IpBTimeOrderVip timeOrderVips = ipBTimeOrderVipMap.get(timeOrderVipId);
                        updateOutStockOccupyItem(timeOrderVips,itemList,occupyItemList.get(0),isStoreDelivery);
                    }else{
                        log.error("寻仓单占单反馈更新库存占用明细失败，SG返回的明细ID未能查询到时效订单占用明细 sourceItemId:{},barCode:{}",orderItemId,barCode);
                    }
                }
                orderInfo.setCpCWarehouseId(itemResultList.get(0).getWareHouseId());
            }else {
                throw new NDSException("当前寻仓单其他人正在操作,请稍后再试!");
            }
            //寻仓反馈
            ValueHolderV14 v14 = ipVipTimeOrderService.getFeedbackresult(orderInfo);
            log.info("寻仓单 orderSn:{}反馈云枢纽,获取仓库结果：{}",orderSn,
                    v14.toJSONObject().toJSONString());
            if(v14.isOK()){
                //反馈云枢纽
                String jitWarehouseCode = v14.getMessage();
                ValueHolderV14 feedbackV14 = ipJitxDeliveryService.feedbackDeliveryResult(jitWarehouseCode,orderInfo,SystemUserResource.getRootUser());
                if(feedbackV14.isOK()){
                    //更新寻仓单反馈状态为：JTIX
                    ipJitxDeliveryService.updateJitxSyncStatus(orderSn,
                            SyncStatus.SYNCSUCCESS, "反馈成功");
                }else {
                    ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.EXCEPTION, feedbackV14.getMessage());
                }
            } else if(v14.getCode() == ResultCode.FAIL){
                //JIT
                ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.SYNCFAILD, v14.getMessage());
            }else{
                //更新反馈异常
                ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.EXCEPTION, v14.getMessage());
            }
        }catch (Exception e){
            log.error(" 唯品会寻仓单 orderSn:{}，占单处理异常 {}",orderSn, Throwables.getStackTraceAsString(e));
        }finally {
            redisLock.unlock();
        }
    }


    /**
     * 创建占单明细
     * @param timeOrderVip
     * @param itemResultList
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOutStockOccupyItem(IpBTimeOrderVip timeOrderVip,
                                         List<SgFindSourceStrategyOmsItemResult> itemResultList,
                                         IpBTimeOrderVipOccupyItem ipBTimeOrderVipOccupyItem,
                                         Integer isStoreDelivery) {
        //获取root用户
        User user = SystemUserResource.getRootUser();
        //作废状态为 未确认和缺货 的库存占用明细
        vipOccupyItemMapper.updateIsActiveNByStatusAndMainId(
                timeOrderVip.getId(), Long.valueOf(user.getId()), user.getEname(),
                user.getName(), new Date());
        //生成新的库存占用明细，包括占单成功及缺货的
        List<IpBTimeOrderVipOccupyItem> newOccupyItemList = new ArrayList<>();
        BigDecimal qty = BigDecimal.ZERO;
        for(SgFindSourceStrategyOmsItemResult occupyItem:itemResultList){
            IpBTimeOrderVipOccupyItem newOccupyItem = buildOutStockOccupyItem(occupyItem,ipBTimeOrderVipOccupyItem,timeOrderVip,user,isStoreDelivery);
            newOccupyItemList.add(newOccupyItem);
            BigDecimal stockQty = newOccupyItem.getOutStockQuantity() == null?BigDecimal.ZERO:newOccupyItem.getOutStockQuantity();
            qty = qty.add(stockQty);
        }
        if(!CollectionUtils.isEmpty(newOccupyItemList)){
            ipBTimeOrderVipOccupyItemMapper.batchInsert(newOccupyItemList);
        }
        //更新主表缺货数量及 单据状态，转换状态:转换成功
        if(qty.compareTo(BigDecimal.ZERO) >0){
            /**缺货*/
            updateTimeOrderStatus(TransferOrderStatus.TRANSFERRED.toInteger(),TimeOrderVipStatusEnum.SEEKING_STORE_SUCCESS.getValue(),qty,"寻仓成功，结果为缺货",timeOrderVip);
        }else{
            /**寻仓成功*/
            updateTimeOrderStatus(TransferOrderStatus.TRANSFERRED.toInteger(),TimeOrderVipStatusEnum.SEEKING_STORE_SUCCESS.getValue(),BigDecimal.ZERO,"寻仓成功",timeOrderVip);
        }
    }

    /**
     * 组装占用明细
     * @param occupyItem
     * @param ipBTimeOrderVipOccupyItem
     * @param timeOrderVip
     * @param user
     * @return
     */
    public IpBTimeOrderVipOccupyItem buildOutStockOccupyItem(SgFindSourceStrategyOmsItemResult occupyItem,
                                                             IpBTimeOrderVipOccupyItem ipBTimeOrderVipOccupyItem,
                                                             IpBTimeOrderVip timeOrderVip,
                                                             User user,
                                                             Integer isStoreDelivery){
        IpBTimeOrderVipOccupyItem newOccupyItem = new IpBTimeOrderVipOccupyItem();
        BeanUtils.copyProperties(ipBTimeOrderVipOccupyItem, newOccupyItem);
        Long shopId = timeOrderVip.getCpCShopId();
        //判断否有占单成功的明细. WareHouseId = -1 表示缺货
        Long wareHouseId = occupyItem.getWareHouseId();
        if(-1 == wareHouseId.intValue()){
            newOccupyItem.setStatus(TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue());
            newOccupyItem.setAmount(occupyItem.getQtyPreOut());
            newOccupyItem.setOutStockQuantity(occupyItem.getQtyPreOut());
            //查询缺货实体仓
            ValueHolderV14<PhyWarehouseVo> vh =
                    subWarehouseService.getDefaultPhyWarehouse(timeOrderVip.getCpCShopId());
            if (vh.isOK() && vh.getData() != null) {
                PhyWarehouseVo phyWarehouseVo = vh.getData();
                wareHouseId = phyWarehouseVo.getPhyWarehouseId();
                CpCPhyWarehouse phyWarehouse = cpRpcService.queryByWarehouseId(wareHouseId);
                newOccupyItem.setCpCPhyWarehouseId(wareHouseId);
                newOccupyItem.setCpCPhyWarehouseEname(phyWarehouse.getEname());
                newOccupyItem.setCpCPhyWarehouseEcode(phyWarehouse.getEcode());
            }else {
                log.error("时效订单{},店铺{}对应店铺默认仓库不存在",timeOrderVip.getBillNo(),shopId);
            }
        }else{
            newOccupyItem.setStoOutBillNo(occupyItem.getStoOutBillNo());
            newOccupyItem.setCpCPhyWarehouseId(occupyItem.getWareHouseId());
            newOccupyItem.setCpCPhyWarehouseEcode(occupyItem.getWareHouseEcode());
            newOccupyItem.setCpCPhyWarehouseEname(occupyItem.getWareHouseEname());
            newOccupyItem.setStatus(TimeOrderOccupyItemStatusEnum.OCCUPY_SUCCESS.getValue());
            newOccupyItem.setAmount(occupyItem.getQtyPreOut());
            newOccupyItem.setOutStockQuantity(BigDecimal.ZERO);
        }
        /**平台仓库编码*/
        StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryJitxCapacity(shopId, wareHouseId, null);
        if (jitxWarehouse == null) {
            if (log.isDebugEnabled()) {
                log.debug("实体仓没有在JITX仓库对照表中维护JITX仓库编码，店铺id：{}，实体仓：{}", shopId, wareHouseId);
            }
        } else {
            String vipcomWarehouseEcode = jitxWarehouse.getVipcomWarehouseEcode();
            String vipcomUnshopWarehouseEcode = jitxWarehouse.getVipcomUnshopWarehouseEcode();
            if(isStoreDelivery != null && isStoreDelivery.equals(1)){
                newOccupyItem.setWarehouseCode(vipcomWarehouseEcode == null?"":vipcomWarehouseEcode);
            }else{
                newOccupyItem.setWarehouseCode(vipcomUnshopWarehouseEcode == null?"":vipcomUnshopWarehouseEcode);
            }
        }
        newOccupyItem.setId(ModelUtil.getSequence(TABLE_NAME));
        newOccupyItem.setCreationdate(new Date());
        newOccupyItem.setOwnerid(Long.valueOf(user.getId()));
        newOccupyItem.setOwnername(user.getName());
        newOccupyItem.setOwnerename(user.getEname());
        newOccupyItem.setIsactive("Y");
        newOccupyItem.setModifieddate(new Date());
        newOccupyItem.setModifierid(Long.valueOf(user.getId()));
        newOccupyItem.setModifiername(user.getName());
        newOccupyItem.setModifierename(user.getEname());
        return newOccupyItem;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateTimeOrderStatus(int istrans, int orderStatus,BigDecimal qty, String remark,IpBTimeOrderVip ipBTimeOrderVip) {
        try {
            IpBTimeOrderVip updateQty = new IpBTimeOrderVip();
            /**单据状态*/
            updateQty.setStatus(orderStatus);
            /**转换转换*/
            updateQty.setIstrans(istrans);
            if(remark != null && remark.length()>500 ){
                remark = remark.substring(0, 500);
            }
            updateQty.setSysremark(remark);
            if(qty != null){
                updateQty.setOutStockQuantity(qty);
            }
            ipBTimeOrderVipMapper.update(updateQty,
                    new QueryWrapper<IpBTimeOrderVip>()
                            .eq("occupied_order_sn",ipBTimeOrderVip.getOccupiedOrderSn()));
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 更新唯品会时效订单中间表失败,occupiedOrderSn:{}", ipBTimeOrderVip.getOccupiedOrderSn(), e);
            throw new NDSException(e);
        }
    }

}
