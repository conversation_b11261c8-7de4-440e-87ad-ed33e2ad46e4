package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderCancelRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.MultiThreadOrderProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.cancel.AlibabaAscpOrderCancelTransferProcessImpl;
import com.jackrain.nea.oc.oms.services.IpBAlibabaAscpOrderCancelService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 猫超直发取消订单转单消息处理器
 * @author: xtt
 * @date: 2020-09-06 10:24
 **/
@Slf4j
@Component
public class AlibabaAscpOrderCancelTransferMqDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpBAlibabaAscpOrderCancelService orderCancelService;
    @Autowired
    private AlibabaAscpOrderCancelTransferProcessImpl cancelTransferProcess;
    @Autowired
    protected MultiThreadOrderProcessor threadOrderProcessor;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        return getProcessStepResultList(orderMqInfo);
    }

    public ProcessStepResultList getProcessStepResultList(OperateOrderMqInfo orderMqInfo) {
        Long micTime = BllCommonUtil.getmicTime();
        String orderNo = orderMqInfo.getOrderNo();
        String orderIds = orderMqInfo.getOrderIds();
        Long orderId = orderMqInfo.getOrderId();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("猫超直发取消订单转单消息处理器.Start.orderIds: {}", orderNo, orderId), orderIds);
        }
        long starTime = System.currentTimeMillis();
        try {
            List<String> applyids = new ArrayList<>();
            if (StringUtils.isNotBlank(orderNo)) {
                applyids.add(orderNo);
            }
            if (StringUtils.isNotBlank(orderIds)) {
                String[] ids = orderIds.split(",");
                applyids = java.util.Arrays.asList(ids);//字符串转为list
            }
            //去重
            applyids = applyids.stream().distinct().collect(Collectors.toList());
            List<IpBAlibabaAscpOrderCancelRelation> relations = new ArrayList<>();
            for (String id : applyids) {
                IpBAlibabaAscpOrderCancelRelation ipBAlibabaAscpOrderCancelRelation = orderCancelService.getIpBAlibabaAscpOrderCancelRelation(id);
                if (ipBAlibabaAscpOrderCancelRelation != null) {
                    relations.add(ipBAlibabaAscpOrderCancelRelation);
                } else {
                    String errorMessage = Resources.getMessage(this.getClass().getName() + " alibabaAscpOrderCancel Received OrderMqInfo Not Exist!bizOrderCode=" + id);
                    log.error(LogUtil.format(errorMessage, orderNo));
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.cancelTransferProcess, relations);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("猫超直发取消订单转单消息处理器耗时:{}ms", orderNo, orderId), Time);
            }
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("猫超直发取消订单转单消息处理器.异常: {}", orderNo, orderId), Throwables.getStackTraceAsString(e));
            }
        }
        return new ProcessStepResultList();
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.ALIBABAASCP
                && orderMqInfo.getOrderType() == OrderType.CANCELORDER;
    }
}
