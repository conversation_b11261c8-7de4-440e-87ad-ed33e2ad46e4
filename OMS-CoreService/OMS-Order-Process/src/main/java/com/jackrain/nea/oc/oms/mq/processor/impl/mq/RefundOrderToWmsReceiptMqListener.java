package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.jackrain.nea.oc.oms.services.refund.ReturnOrderWmsReceiptService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 零售退单发wms回执MQ
 *
 * @author: 秦雄飞
 * @time: 2021/12/30 17:43 下午
 * @description: 监听云枢纽
 *
 *
 * 无用废弃
 */

@Deprecated
@Slf4j
@RocketMqMessageListener(name = "RefundOrderToWmsReceiptMqListener", type = MqTypeEnum.CLOUD)
public class RefundOrderToWmsReceiptMqListener implements BaseMessageListener {

    private final static String methodName = "taobao.qimen.returnorder.create";

    @Autowired
    private ReturnOrderWmsReceiptService returnOrderWmsReceiptService;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object obj) {
        try {
            if (log.isDebugEnabled()) {
                log.debug("RefundOrderToWmsReceiptMqListener.start");
            }
            if (log.isDebugEnabled()) {
                log.debug("RefundOrderToWmsReceiptMqListener.接收wms回执mq消息:messageBody{},messageKey{},messageId{},messageTopic{};",
                        messageBody, messageKey, messageKey, messageTopic);
            }

            JSONObject object = JSONObject.parseObject(messageBody);
            String method = object.getString("method");
            if (methodName.equalsIgnoreCase(method)) {
                boolean consumed = returnOrderWmsReceiptService.consume(messageBody);
                if (!consumed) {
                    throw new MqException("returnOrderWmsReceiptService.consumedFailed");
                }
            }
        } catch (Exception e) {
            log.error("RefundOrderToWmsReceiptMqListener.consume.error", e);
            throw new MqException(e);
        }
    }
}
