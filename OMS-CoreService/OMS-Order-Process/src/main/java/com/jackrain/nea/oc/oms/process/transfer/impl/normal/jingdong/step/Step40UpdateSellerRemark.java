package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-25
 * create at : 2019-04-25 11:39 AM
 * 判断“卖家备注”是否与【全渠道订单】中的“卖家备注”一致,不一致则更新
 */
@Step(order = 40, description = "判断“卖家备注”是否与【全渠道订单】中的“卖家备注”一致")
@Slf4j
@Component
public class Step40UpdateSellerRemark extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongOrder jingdongOrder = orderInfo.getJingdongOrder();

        //仅用来更新
        IpBJingdongOrder updateOrder = new IpBJingdongOrder();
        //updateOrder.setId(jingdongOrder.getId());
        Long orderId = jingdongOrder.getOrderId();
        List<OcBOrder> ocBOrderList = orderInfo.getOcBOrderList();
        for (OcBOrder ocBOrder : ocBOrderList) {
            // 异常巡检，处理当为null时，更新报错的问题 1102
            if (StringUtils.isNotBlank(jingdongOrder.getVenderremark())
                    && !StringUtils.equalsIgnoreCase(jingdongOrder.getVenderremark(), ocBOrder.getSellerMemo())) {
                OcBOrder ocBOrder1 = new OcBOrder();
                ocBOrder1.setId(ocBOrder.getId());
                ocBOrder1.setSellerMemo(jingdongOrder.getVenderremark());
                ocBOrder1.setModifieddate(new Date());
                if (operateUser != null) {
                    ocBOrder1.setModifierid(Long.valueOf(operateUser.getId()));
                    ocBOrder1.setModifiername(operateUser.getName());
                    ocBOrder1.setModifierename(operateUser.getEname());
                }
                orderService.updateOrderInfo(ocBOrder1);
                // 增加操作日志
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.SELLERMEMO_UPDATE.getKey(),
                        "修改卖家备注为:" + jingdongOrder.getVenderremark(),
                        "", null, SystemUserResource.getRootUser());
            }
        }
        Boolean operateObj = (Boolean) preStepResult.getNextStepOperateObj();
        //更新京东中间表，程序结束
        updateOrder.setTransCount(jingdongOrder.getTransCount() + 1L);
        updateOrder.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
        updateOrder.setTransdate(new Date());
        updateOrder.setSysremark("订单已存在，标记为已转换");
        if (operateObj == null) {
            updateOrder.setSysremark("订单已取消作废，标记为已转换");
        }
        if (operateObj != null && operateObj) {
            updateOrder.setSysremark("取消订单拦截，标记为已转换");
        }
        ipJingdongOrderService.updateIpJingdongOrderInfo(updateOrder, orderId);
        return new ProcessStepResult<>(StepStatus.SUCCESS,
                "订单已存在，标记为已转换",
                Step43UpdateAddress.class);
    }
}
