package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2020/12/3 8:46 下午
 * @Version 1.0
 */
@Step(order = 80, description = "取消退换货单以及换货订单 并重新生成")
@Slf4j
@Component
public class Step080CancelToGenerateOrder extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        try {
            //调用标记取消 取消换货订单
            boolean flag = omsTaobaoExchangeService.cancelExchangeOrderAndReturnOrder(orderInfo, operateUser);
            if (flag) {
                //取消成功后 直接跳转 重新生成
                return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step100SaveExchangeOrder.class);
            } else {
                String message = "退换货单的状态不是等待退货入库,无法取消";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FAILED, message);
            }
        }catch (Exception e){
            ipTaobaoExchangeService.updateExchangeIsTransError(ipBTaobaoExchange, "取消退换货单以及换货订单 并重新生成失败"+e.getMessage());
            log.error(LogUtil.format("取消退换货单以及换货订单 并重新生成失败:{}", "取消退换货单以及换货订单并重新生成"), Throwables.getStackTraceAsString(e));
            String errorMessage = "取消退换货单以及换货订单 并重新生成!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
