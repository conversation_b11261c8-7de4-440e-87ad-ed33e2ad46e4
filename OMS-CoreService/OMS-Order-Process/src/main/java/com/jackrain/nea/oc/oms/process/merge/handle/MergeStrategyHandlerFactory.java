package com.jackrain.nea.oc.oms.process.merge.handle;

import com.google.common.collect.Maps;
import com.jackrain.nea.oc.oms.model.MergeOrderInfo;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * <AUTHOR> ruan.gz
 * @Description : 合单引擎
 * @Date : 2020/6/18
 **/
@Slf4j
@Service
public class MergeStrategyHandlerFactory {

    /**
     *
     */
    @Autowired
    private Map<String, MergeStrategyHandler> stateHashMap = new HashMap<>();

    @Autowired
    private OmsAuditTaskService auditTaskService;

    /**
     * 执行策略引擎
     *
     * @param ids
     * @param info
     * @param dbOcBOrderList
     */
    public void doHandle(List<Long> ids, MergeOrderInfo info, List<OcBOrder> dbOcBOrderList) {
        //策略排序
        List<Map.Entry<Integer, MergeStrategyHandler>> listMap = getSortMap();
        Iterator<OcBOrder> iterator = dbOcBOrderList.iterator();
        while (iterator.hasNext()) {
            OcBOrder order = iterator.next();
            int index = 0;
            for (Map.Entry<Integer, MergeStrategyHandler> integerHandlerEntry : listMap) {
                if (index > 0) {
                    continue;
                }
                MergeStrategyHandler value = integerHandlerEntry.getValue();
                info.setOcBOrder(order);
                //不符合策略的直接删除
                Boolean aBoolean = value.doSingleHandle(info);
                if (null != aBoolean && aBoolean) {
                    index++;
                    iterator.remove();
                    Long id = order.getId();
                    ids.remove(id);
                    //不满足条件合并的订单开启自动审核
                    auditTaskService.updateAuditTaskByOrderId(Arrays.asList(id));
                    if (log.isDebugEnabled()) {
                        log.debug("OrderId={},合单策略删除" , order.getId());
                        log.debug(LogUtil.format("合单策略删除",
                                "合单策略删除", order.getId()));
                    }
                    //跳出策略循环
                }
            }
        }
        if (CollectionUtils.isEmpty(ids)) {
            info.setOrderIds(null);
            info.setOrderList(null);
        } else {
            info.setOrderList(dbOcBOrderList);
            info.setOrderIds(ids);
        }
    }

    private List<Map.Entry<Integer, MergeStrategyHandler>> getSortMap() {
        Map<Integer, MergeStrategyHandler> map = Maps.newHashMap();
        for (Map.Entry<String, MergeStrategyHandler> stringHandlerEntry : stateHashMap.entrySet()) {
            MergeStrategyHandler value = stringHandlerEntry.getValue();
            map.put(value.getSort(stringHandlerEntry.getKey()), value);
        }
        List<Map.Entry<Integer, MergeStrategyHandler>> list = new ArrayList<Map.Entry<Integer, MergeStrategyHandler>>(map.entrySet());
        Collections.sort(list, Comparator.comparing(Map.Entry::getKey));
        return list;
    }
}
