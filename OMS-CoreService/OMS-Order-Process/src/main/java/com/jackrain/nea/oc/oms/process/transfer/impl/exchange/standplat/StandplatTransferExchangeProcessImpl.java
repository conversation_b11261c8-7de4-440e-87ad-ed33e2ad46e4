package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/7  18:10
 * @description: 中间表转换到退换货订单服务
 */
@Component
public class StandplatTransferExchangeProcessImpl extends AbstractOrderProcess<OmsStandPlatRefundRelation> {
    public StandplatTransferExchangeProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "standplat";
    }

    @Override
    protected long getProcessOrderId(OmsStandPlatRefundRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(OmsStandPlatRefundRelation orderInfo) {
        return orderInfo.getOrderNo();
    }


    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_STANDPLAT_EXCHANGE;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.STANDARD;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    /**
     * 通用订单接口，平台单号
     *
     * @param orderInfo 订单单据
     * @return
     */
    @Override
    protected String getSourceTid(OmsStandPlatRefundRelation orderInfo) {
        return orderInfo.getSourceTid();
    }
}
