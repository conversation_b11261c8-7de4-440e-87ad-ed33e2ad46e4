package com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.step;

import com.jackrain.nea.oc.oms.services.IpVipReturnOrderService;

import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sg.service.SgTransferService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date ：Created in 15:55 2020/6/9
 * description ：唯评会退供单
 * @ Modified By：
 */
public abstract class BaseVipReturnOrderProcessStep {
    @Autowired
    protected IpVipReturnOrderService ipVipReturnOrderService;

    @Autowired
    protected SgTransferService sgTransferService;

    @Autowired
    protected PsRpcService psRpcService;
}
