package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobao.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description:判断转换条码是否存在
 *
 * <AUTHOR> sunies
 * @since : 2020-11-30
 * create at : 2020-11-30 11:30
 */
@Step(order = 10, description = "判断转换条码是否存在")
@Slf4j
@Component
public class Step015CheckSkuIsExist extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<IpTaobaoExchangeRelation> {


    @Override
    public ProcessStepResult<IpTaobaoExchangeRelation> startProcess(IpTaobaoExchangeRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        List<ProductSku> exchangeProductList = orderInfo.getExchangeProductDetailList();
        if (log.isDebugEnabled()) {
            log.debug("Step015CheckSkuIsExist判断转换条码是否存在;OrderNo={}", orderInfo.getOrderNo());
        }
        if (CollectionUtils.isEmpty(exchangeProductList)) {
            ipTaobaoExchangeService.updateExchangeSkuNotExistRemark(orderInfo);
            return new ProcessStepResult<>(StepStatus.FINISHED);
        } else {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "条码存在，进入下一步");
        }
    }
}
