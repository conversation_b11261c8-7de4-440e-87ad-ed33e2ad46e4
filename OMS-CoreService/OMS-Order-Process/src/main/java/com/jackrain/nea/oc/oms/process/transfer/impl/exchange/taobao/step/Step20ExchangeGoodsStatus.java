package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobao.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoExchangeOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 换货中间表状态为 状态是否为 待买家退货(2), 买家已退货，待收货(3)
 *
 * <AUTHOR>
 */
@Step(order = 20, description = "判断淘宝换货状态")
@Slf4j
@Component
public class Step20ExchangeGoodsStatus extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<IpTaobaoExchangeRelation> {
    @Override
    public ProcessStepResult<IpTaobaoExchangeRelation> startProcess(IpTaobaoExchangeRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        String status = orderInfo.getTaobaoExchange().getStatus();
        OcBOrder originalValidOrderInfo = orderInfo.getOriginalValidOrderInfo();
        IpBTaobaoExchange taobaoExchange = orderInfo.getTaobaoExchange();

        try {
            //状态是否为 待买家退货(2), 买家已退货，待收货(3)
            if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.BUYER_RETURN_COLLECT_GOODS.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.STAY_BUYER_RETURN.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.WAIT_ISSUE_EXCHANGE_GOODS.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_STAY_HANDLE.getName().equals(status)) {
                //判断原单是否存在
                if (originalValidOrderInfo == null) {
                    return new ProcessStepResult<>(StepStatus.SUCCESS, null,
                            Step050OriginalOrderNoExist.class);
                } else {
                    //判断是否为福袋商品
                    Boolean isFortuneBag = orderInfo.getIsFortuneBag();
                    if (isFortuneBag) {
                        //更新为已转换
                        ipTaobaoExchangeService.updateExchangeIsTransTransferred(taobaoExchange);
                        String message = "当前条码为福袋商品,不允许换货,标记已转换";
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                    //判断是否需要保存换货单
                    boolean flag = ipTaobaoExchangeService.exchangeStatusIsReceivingGoods(orderInfo, operateUser);
                    if (flag) {
                        return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step900ExchangeSaveReturnOrder.class);
                    } else {
                        String message = "退换单已存在,转换完成";
                        //更新完成
                        ipTaobaoExchangeService.updateExchangeIsTransTransferring(taobaoExchange, TransferOrderStatus.TRANSFERRED.toInteger());
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(taobaoExchange, e.getMessage());
            log.error(this.getClass().getName() + " 退换货转换异常", e);
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
