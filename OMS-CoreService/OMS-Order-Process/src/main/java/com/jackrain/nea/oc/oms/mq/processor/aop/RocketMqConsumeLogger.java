package com.jackrain.nea.oc.oms.mq.processor.aop;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.model.table.OcBMessageConsumeLog;
import com.jackrain.nea.oc.oms.mq.processor.aop.parser.IMqMessageParser;
import com.jackrain.nea.oc.oms.mq.processor.aop.parser.MqMessageParserFactory;
import com.jackrain.nea.oc.oms.nums.MessageConsumeStatusEnum;
import com.jackrain.nea.oc.oms.resource.ProcessRedisKeyResources;
import com.jackrain.nea.oc.oms.services.OcBMessageConsumeLogService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description： 消息消费记录器
 * Author: RESET
 * Date: Created in 2020/8/26 2:59
 * Modified By:
 */
@Component
@Slf4j
public class RocketMqConsumeLogger {

    // apollo控制日志监听开关
    static final String KEY_SWITCHER_APOLLO = "r3.oc.oms.mq.log.switch";
    // 开关默认值
    static final String VAL_DEFAULT_SWITCH = "true";

    private static final int LOCK_LOG_AUTO_TIMEOUT = 60 * 1000;

    /**
     * 不抛出异常
     *
     * @param message
     * @param method
     * @param throwable
     */
    public void saveErrorLogNoException(Message message, String method, Throwable throwable) {
        try {
            if (getLogSwitch()) {
                saveErrorLogWithLock(message, method, throwable);
            }
        } catch (Throwable e) {
            log.error(LogUtil.format("RocketMqConsumeLogger.saveErrorLogNoException:{}",
                    "saveErrorLogNoException"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 带锁的操作
     *
     * @param message
     * @param method
     * @param throwable
     */
    public void saveErrorLogWithLock(Message message, String method, Throwable throwable) {
        if (Objects.nonNull(message)) {
            String lockRedisKey = ProcessRedisKeyResources.buildLockMessageLogKey(message.getTopic(), message.getMsgID());
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);

            try {
                if (redisLock.tryLock(LOCK_LOG_AUTO_TIMEOUT, TimeUnit.MILLISECONDS)) {
                    saveErrorLog(message, method, throwable);
                }
            } catch (InterruptedException e) {
                log.error(LogUtil.format("RocketMqConsumeLogger.saveErrorLogWithLock:{}", "saveErrorLogWithLock"), Throwables.getStackTraceAsString(e));
            } finally {
                redisLock.unlock();
            }
        }
    }

    /**
     * 保存错误信息
     *
     * @param message
     * @param method
     * @param throwable
     */
    public void saveErrorLog(Message message, String method, Throwable throwable) {
        if (Objects.nonNull(message) && Objects.nonNull(method)) {
            IMqMessageParser parser = MqMessageParserFactory.getInstance().getParser(method);
            // 解析单据
            OcBMessageConsumeLog consumeLog = parser.doParse(message);
            // 保存信息
            if (Objects.nonNull(consumeLog)) {
                if (Objects.nonNull(throwable)) {
                    String stackTrace = ExceptionUtils.getStackTrace(throwable);
                    consumeLog.setErrorMsg(stackTrace);
                }

                if (message.getReconsumeTimes() > 0) {
                    consumeLog.setConsumeStatus(MessageConsumeStatusEnum.RETRY_ERROR.getValue());
                } else {
                    consumeLog.setConsumeStatus(MessageConsumeStatusEnum.ERROR.getValue());
                }

                // 记录日志
                log.debug(LogUtil.format("RocketMqConsumeLogger.saveErrorLog.log:{}","saveErrorLog"), JSON.toJSONString(consumeLog));

                // 保存信息
                OcBMessageConsumeLogService.getInstance().save(consumeLog, null);
            }
        }
    }

    /**
     * 更新成成功状态，不抛异常
     *
     * @param message
     */
    public void updateSuccessStatusNoException(Message message) {
        try {
            if (getLogSwitch()) {
                updateSuccessStatusWithLock(message);
            }
        } catch (Throwable t) {
            log.error(LogUtil.format("RocketMqConsumeLogger.updateSuccessStatusNoException:{}",
                    "updateSuccessStatusNoException"), Throwables.getStackTraceAsString(t));
        }
    }

    /**
     * 待锁控并发
     *
     * @param message
     */
    public void updateSuccessStatusWithLock(Message message) {
        if (Objects.nonNull(message)) {
            String lockRedisKey = ProcessRedisKeyResources.buildLockMessageLogKey(message.getTopic(), message.getMsgID());
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);

            try {
                if (redisLock.tryLock(LOCK_LOG_AUTO_TIMEOUT, TimeUnit.MILLISECONDS)) {
                    updateSuccessStatus(message);
                }
            } catch (InterruptedException e) {
                log.error(LogUtil.format("RocketMqConsumeLogger.updateSuccessStatusWithLock:{}",
                        "updateSuccessStatusWithLock"), Throwables.getStackTraceAsString(e));
            } finally {
                redisLock.unlock();
            }
        }
    }

    /**
     * 更新成功状态
     *
     * @param message
     */
    public void updateSuccessStatus(Message message) {
        if (Objects.nonNull(message)) {
            Integer retryCount = message.getReconsumeTimes();

            // 只有大于0，才有前面失败的可能，才需要更新
            if (retryCount > 0) {
                String topic = message.getTopic();
                String messageId = message.getMsgID();
                // 更新状态
                OcBMessageConsumeLogService.getInstance().updateStatusToRetrySuccessByUnique(topic, messageId, retryCount, null);
            }
        }
    }

    /**
     * 日志开关
     *
     * @return
     */
    private boolean getLogSwitch() {
        // KEY_SWITCHER_APOLLO
        PropertiesConf env = ApplicationContextHandle.getBean(PropertiesConf.class);
        String val = env.getProperty(KEY_SWITCHER_APOLLO, VAL_DEFAULT_SWITCH);
        return VAL_DEFAULT_SWITCH.equalsIgnoreCase(val);
    }

    public static RocketMqConsumeLogger getInstance() {
        return ApplicationContextHandle.getBean(RocketMqConsumeLogger.class);
    }

}
