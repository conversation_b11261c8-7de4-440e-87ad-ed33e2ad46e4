package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefundType;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat.StandplatTransferExchangeProcessImpl;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.StandplatRefundRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.OmsStandplatRefundService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Descroption 通用退单消息处理器
 * <AUTHOR>
 * @Date 2019/5/17 20:30
 */
@Slf4j
@Component
public class CurrencyTransferMqRefundDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private OmsStandplatRefundService omsStandplatRefundService;

    @Autowired
    private StandplatRefundRefundProcessImpl standplatRefundRefundProcess;

    @Autowired
    private StandplatTransferExchangeProcessImpl standplatTransferExchangeProcess;

    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {

        String orderNo = orderMqInfo.getOrderNo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("通用退单消息处理器.Start", orderNo));
        }

        OmsStandPlatRefundRelation standplatRefundRelation = omsStandplatRefundService.selectStandplatRefundRelation(orderNo);
        if (standplatRefundRelation == null) {

            String errorMessage = Resources.getMessage
                    ("CurrencyTransfer Received OrderMqInfo Not Exist!OrderNo=" + orderNo);
            log.error(LogUtil.format(errorMessage, orderNo));

            return new ProcessStepResultList();

        } else {
            ProcessStepResultList resultList;
            if (IpBStandplatRefundType.EXCHANGE_GOODS == standplatRefundRelation.getIpBStandplatRefund().getRefundType() && businessSystemParamService.standlatExchange()) {
                resultList = standplatTransferExchangeProcess.start(standplatRefundRelation,
                        false, SystemUserResource.getRootUser());
            } else {
                resultList = standplatRefundRefundProcess.start(standplatRefundRelation,
                        false, SystemUserResource.getRootUser());
            }

            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("通用退单消息处理器.Result:{}", orderNo),resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.STANDPLAT
                && orderMqInfo.getOrderType() == OrderType.REFUND;
    }
}
