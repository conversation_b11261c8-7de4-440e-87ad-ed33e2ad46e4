package com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.step;

import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipOccupyItemMapper;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderItemService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.time.order.IpVipTimeOrderOccupyItemService;
import com.jackrain.nea.oc.oms.util.LockOrderType;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 * @Description 时效订单转单分步实现基类
 * @Date 2019-8-19
 **/
public abstract class BaseVipTimeOrderProcessStep {

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    protected IpBTimeOrderVipMapper timeOrderVipMapper;

    @Autowired
    protected IpBTimeOrderVipOccupyItemMapper vipOccupyItemMapper;

    @Autowired
    protected IpVipTimeOrderOccupyItemService vipTimeOrderOccupyItemService;

    @Autowired
    protected IpVipTimeOrderItemService ipVipTimeOrderItemService;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.VIPJITX;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JITX_TIMEORDER;
    }


}
