package com.jackrain.nea.oc.oms.mq.processor.aop.parser;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 奇门标准接口类型
 * Author: RESET
 * Date: Created in 2020/6/15 21:39
 * Modified By:
 */
public enum MetaDataOrderTypeEnum {

    // 匹配策略类型
    B2C_ORDER(3, "b2c order", "B2C订单处理"),
    B2C_RETURN_ORDER(4, "b2c return order", "B2C退货处理"),
    OUT_NOTICE(10, "out notice", "出库通知单");

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    MetaDataOrderTypeEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static MetaDataOrderTypeEnum fromValue(Integer v) {
        for (MetaDataOrderTypeEnum c : MetaDataOrderTypeEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

}
