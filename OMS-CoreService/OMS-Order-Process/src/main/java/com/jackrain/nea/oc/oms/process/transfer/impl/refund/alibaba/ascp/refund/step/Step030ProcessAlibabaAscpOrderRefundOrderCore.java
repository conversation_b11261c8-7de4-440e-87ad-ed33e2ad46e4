package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.util.AlibabaAscpOrderCommonUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 5:07 下午
 * @Version 1.0
 */
@Step(order = 30, description = "处理订单")
@Slf4j
@Component
public class Step030ProcessAlibabaAscpOrderRefundOrderCore extends BaseAlibabaAscpOrderRefundProcessStep
        implements IOmsOrderProcessStep<IpBAlibabaAscpOrderRefundRelation> {
    @Override
    public ProcessStepResult<IpBAlibabaAscpOrderRefundRelation> startProcess(IpBAlibabaAscpOrderRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        try {
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            IpBAlibabaAscpOrderRefund orderRefund = orderInfo.getOrderRefund();
            for (OmsOrderRelation orderRelation : omsOrderRelation) {
                OcBOrder ocBOrder = orderRelation.getOcBOrder();
                Integer orderStatus = ocBOrder.getOrderStatus();
                //判断订单是否为发货后
                boolean statusAfter = AlibabaAscpOrderCommonUtil.checkOrderStatusAfter(orderStatus);
                if (statusAfter) {
                    //发货后
                    orderRelation.setOrderMark(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode());
                } else {
                    //发货前
                    orderRelation.setOrderMark(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode());
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "订单处理成功,进入下一阶段");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 订单处理异常!", e);
            return new ProcessStepResult<>(StepStatus.FAILED, "订单处理失败");
        }
    }
}
