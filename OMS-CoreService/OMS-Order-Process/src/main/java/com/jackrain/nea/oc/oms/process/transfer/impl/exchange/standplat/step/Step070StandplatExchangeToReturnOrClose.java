package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat.step;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 9:59 下午
 * @Version 1.0
 */
@Step(order = 70, description = "换货变退货")
@Slf4j
@Component
public class Step070StandplatExchangeToReturnOrClose extends BaseStandplatExchangeProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {
    @Value("${exchange.close.to.return.enable:true}")
    private boolean exchangeCloseToReturnEnable;

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        if(log.isDebugEnabled()){
            log.debug("BaseStandplatExchangeProcessStep.Step070StandplatExchangeToReturnOrClose start >>> orderInfo：{}", JSON.toJSONString(orderInfo));
        }
        IpBStandplatRefund ipBTaobaoExchange = orderInfo.getIpBStandplatRefund();
        Integer returnStatus = ipBTaobaoExchange.getReturnStatus();
        try {
            OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();
            //调用标记取消 取消换货订单
            String message = "";
            boolean flag = standplatExchangeService.cancelExchangeOrder(orderInfo, operateUser);
            if (!flag) {
                message = "换货订单存在仓库或者平台发货,无法取消,转换失败";
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
            if (IpBStandplatRefudStatusEnum.CLOSED.getVal().equals(returnStatus)) {
                //取消退换货单
                if (TaobaoReturnOrderExt.BillType.EXCHANGE.getCode().equals(ocBReturnOrder.getBillType())) {
                    List<Long> returnOrderIds = new ArrayList<>();
                    returnOrderIds.add(ocBReturnOrder.getId());
                    ValueHolderV14 v14 = omsRefundOrderService.refundOrderClose(returnOrderIds, null, null, operateUser);
                    message = "换货关闭,转换成功!";
                    if (exchangeCloseToReturnEnable) {
                        if (!v14.isOK()) {
                            standplatExchangeService.exchangeToReturn(ocBReturnOrder, operateUser);
                            message = "退换货单无法取消,换货转退货,转换成功!";
                        }
                    }
                } else {
                    standplatExchangeService.exchangeToReturn(ocBReturnOrder, operateUser);
                    message = "退换货单无法取消,换货转退货,转换成功!";
                }
            }
            if (IpBStandplatRefudStatusEnum.PLEASE_REFUND.getVal().equals(returnStatus)) {
                //删除换货明细  更改退换货单未退货单
                standplatExchangeService.exchangeToReturn(ocBReturnOrder, operateUser);
                message = "换货转退货,转换成功!";
            }
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message, ipBTaobaoExchange);
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        } catch (Exception e) {
//            ipStandplatRefundService.updateExchangeIsTransError(ipBTaobaoExchange, "处理跳转失败"+e.getMessage());
            log.error("{}, 换货变退货或换货关闭:{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
            String errorMessage = " 换货变退货或换货关闭!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
