package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpAlibabaAscpOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.LockOrderType;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: 秦雄飞
 * @date 2020/9/3 下午4:32
 */
public abstract class BaseAlibabaAscpOrderProcessStep {

    @Autowired
    protected IpAlibabaAscpOrderService ipAlibabaAscpOrderService;

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected PsRpcService psRpcService;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.ALIBABAASCP;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.ALIBABA_ASCP_ORDER;
    }

}