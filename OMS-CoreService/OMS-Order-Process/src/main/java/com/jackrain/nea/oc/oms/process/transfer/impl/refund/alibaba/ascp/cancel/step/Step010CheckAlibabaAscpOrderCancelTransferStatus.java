package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.cancel.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderCancelRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description: 判断猫超直发取消订单单据转换状态
 * @author: xtt
 * @date: 2020-09-04 11:23
 **/
@Step(order = 10, description = "判断猫超直发取消订单单据转换状态")
@Slf4j
@Component
public class Step010CheckAlibabaAscpOrderCancelTransferStatus extends BaseAlibabaAscpOrderCancelProcessStep implements IOmsOrderProcessStep<IpBAlibabaAscpOrderCancelRelation> {

    @Override
    public ProcessStepResult<IpBAlibabaAscpOrderCancelRelation> startProcess(IpBAlibabaAscpOrderCancelRelation orderInfo,
                                                                             ProcessStepResult preStepResult,
                                                                             boolean isAutoMakeup, User operateUser) {
        if (orderInfo == null || orderInfo.getIpBAlibabaAscpOrderCancel() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者猫超直发订单取消中间表为空；退出转换");
        }
        Integer currentStatus = orderInfo.getIpBAlibabaAscpOrderCancel().getIsTrans();
        if (null == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态为空，需再次转换");
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERRING.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "正在转换中");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERFAIL.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态=转换失败，需人工转换");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage, Step020CheckAlibabaAscpOrderCancelOriginalStatus.class);
        }
    }
}