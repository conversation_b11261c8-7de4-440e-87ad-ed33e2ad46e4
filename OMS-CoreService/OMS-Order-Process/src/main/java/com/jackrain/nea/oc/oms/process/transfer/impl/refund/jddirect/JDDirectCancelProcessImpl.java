package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * @Desc : 京东厂直取消
 * <AUTHOR> xiWen
 * @Date : 2022/3/26
 */
@Component
public class JDDirectCancelProcessImpl extends AbstractOrderProcess<OmsJDDirectCancelRelation> {


    @Override
    protected String getChildPackageName() {
        return "jddirect";
    }

    @Override
    protected long getProcessOrderId(OmsJDDirectCancelRelation execInfo) {
        return execInfo.getIpOrderId();
    }

    @Override
    protected String getProcessOrderNo(OmsJDDirectCancelRelation execInfo) {
        return execInfo.getIpRefundNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JD_DIRECT_CANCEL;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.JD_DIRECT;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }


}
