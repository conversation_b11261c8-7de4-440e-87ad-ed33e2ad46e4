package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobao.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2019/5/23 11:30 AM
 * @Version 1.0
 */
@Step(order = 9, description = "更新换货中间表状态为转换中")
@Slf4j
@Component
public class Step009UpdateExchangeOrderStatus extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<IpTaobaoExchangeRelation> {

    @Override
    public ProcessStepResult<IpTaobaoExchangeRelation> startProcess(IpTaobaoExchangeRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        if (orderInfo == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空；退出转换");
        }

        // @20200731 退单查询（原在消息消费的地方查询，现改为这里查询）
        ipTaobaoExchangeService.selectOrderExchange(orderInfo);

        IpBTaobaoExchange taobaoExchange = orderInfo.getTaobaoExchange();
        try {
            Long disputeId = taobaoExchange.getDisputeId();
            IpBTaobaoExchange reSelectExchangeInfo = ipTaobaoExchangeService.selectIpBTaobaoExchange(String.valueOf(disputeId));
            if (reSelectExchangeInfo != null) {
                orderInfo.setTaobaoExchange(reSelectExchangeInfo);
                String isTrans = reSelectExchangeInfo.getIstrans();
                if (!"0".equals(isTrans)) {
                    if (log.isDebugEnabled()) {
                        log.debug("{} 当前状态为:{}", this.getClass().getName(), isTrans);
                    }
                    return new ProcessStepResult<>(StepStatus.FAILED, "当前状态为:" + isTrans + " 不满足转换条件");
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug("{} 换货中间表数据不存在,disputeId:{}", this.getClass().getName(), disputeId);
                }
                return new ProcessStepResult<>(StepStatus.FAILED, "换货中间表数据不存在,disputeId" + disputeId);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(taobaoExchange, e.getMessage());
            log.error("{} 退换货转换异常", this.getClass().getName(), e);
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
