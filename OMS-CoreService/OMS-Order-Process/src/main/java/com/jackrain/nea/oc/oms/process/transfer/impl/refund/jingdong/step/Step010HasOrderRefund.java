package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.step;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.JdCustomerExEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.util.JdRefundUtil;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.refund.JdReturnUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.OmsRefundTransferUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @Descroption 判断关联退货单状态
 * <AUTHOR>
 * @Date 2019/4/26 14:14
 */
@Step(order = 10, description = "判断关联退货单状态")
@Slf4j
@Component
public class Step010HasOrderRefund extends BaseJingdongRefundProcessStep
        implements IOmsOrderProcessStep<IpJingdongRefundRelation> {
    @Override
    public ProcessStepResult<IpJingdongRefundRelation> startProcess(IpJingdongRefundRelation orderInfo,
                                                                    ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongRefund jingdongRefund = orderInfo.getJingdongRefund();
        OcBOrder ocBOrder = orderInfo.getOcBOrder();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东退单参数.{}","京东退单参数",orderInfo.getOrderNo()), JSONObject.toJSONString(orderInfo));
        }
        try {
            String afsservicestatusname = jingdongRefund.getAfsservicestatusname();
            String refundStatus = JdReturnUtil.RefundStatus.transTaobaoRefundStatus(afsservicestatusname);
            omsRefundOrderService.updateRefundSlip(jingdongRefund.getAfsserviceid()+"", refundStatus);
            jingdongRefund.setTransFailReason(TransNodeTipEnum.DEFAULT.val());
            // 订单状态判断-传wms不允许继续
            if (OmsRefundTransferUtil.isForbidOrderTransfer(orderInfo.getOcBOrder())) {
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                        SysNotesConstant.SYS_REMARK90, jingdongRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, SysNotesConstant.SYS_REMARK90);
            }
            if (orderInfo.getOcBOrderItems() == null) {
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK1, jingdongRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, SysNotesConstant.SYS_REMARK1);
            }
            //1.客户期望为非退货(!=10)更新为已转换,添加系统备注:退单的类型为非退货，系统自动标记为已转换
            // todo  暂时放开20
            boolean isAllowedExc = jingdongRefund.getCustomerexpect().equals(JdCustomerExEnum.Return.getVal())
                    || jingdongRefund.getCustomerexpect().equals(JdCustomerExEnum.Exchange.getVal());
            if (!isAllowedExc) {
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK48, jingdongRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED,
                        "单据" + orderInfo.getOrderNo() + "转换完成");
            }
            //判断状态是不是待审核
            if (JdRefundUtil.RefundStatus.NOT_REVIEWED.getCode().equals(jingdongRefund.getAfsservicestatusname())) {
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK25, jingdongRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED,
                        "单据" + orderInfo.getOrderNo() + "转换完成");
            }

            OcBReturnOrder ocReturnOrder = orderInfo.getOcReturnOrder();
            if (ocReturnOrder != null) {
                //todo 判断状态是不是取消  如果是取消 则取消退货单
                if (JdRefundUtil.RefundStatus.CANCEL.getCode().equals(jingdongRefund.getAfsservicestatusname())
                        || JdRefundUtil.RefundStatus.EXAMINE_CLOSE.getCode().equals(jingdongRefund.getAfsservicestatusname())) {
                    List<Long> refundOrderIds = new ArrayList<>();
                    refundOrderIds.add(ocReturnOrder.getId());
                    omsRefundOrderService.closedRefundSlip(jingdongRefund.getAfsserviceid()+"");
                    omsRefundOrderService.refundOrderClose(refundOrderIds, null, null, operateUser);

                    ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            "取消退换货货单成功!", jingdongRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED,
                            "单据" + orderInfo.getOrderNo() + "转换完成");
                }
                //2.更新退货单物流公司和物流单号
               /* ocReturnOrder.setLogisticsCode(jingdongRefund.getExpresscode());
                String expresscompany = jingdongRefund.getExpresscompany();
                //根据物流公司名称填充物流信息数据
                if (StringUtil.isNotEmpty(expresscompany)) {
                    LogisticsInfo logisticsInfo = logisticsService.selectLogisticsInfoByName(expresscompany);
                    if (logisticsInfo != null) {
                        ocReturnOrder.setCpCLogisticsId(logisticsInfo.getId());
                        ocReturnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                    }
                }
                boolean updateReturnFlag = ipJingdongRefundService.updateReturnOrderLogisticsInfo(ocReturnOrder);*/
                boolean isUpdateLgInfo = modifyLogisticInfo(ocReturnOrder, jingdongRefund);
                //3.插入退换货单的更新日志
                if (isUpdateLgInfo) {
                    if (operateUser == null) {
                        operateUser = SystemUserResource.getRootUser();
                    }
                    ipJingdongRefundService.insetOcReturnOrderLog(SysNotesConstant.UPDATE_EXCHANGEORDER_LOG_TYPE,
                            SysNotesConstant.UPDATE_EXCHANGEORDER_LOG_MESSAGE_FOR_LOGISTICS, ocReturnOrder.getId(), operateUser);
                }
                if(orderInfo.getMilkCardOrder()){
                    ipJingdongRefundService.foundRefundSlipAfterNoUpdate(orderInfo,jingdongRefund, operateUser);
                }else {
                    ipJingdongRefundService.foundRefundSlipAfterNoUpdate(ocReturnOrder.getId(), orderInfo, jingdongRefund, operateUser);
                }

                //4.修改退单的状态为已转换，添加京东退单表的系统备注“退货单中已经存在，修改退单状态为已转换
                TransRefundNodeTipUtil.jinDongTransTipCAS(jingdongRefund, TransNodeTipEnum.DEFAULT);
                ipJingdongRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        SysNotesConstant.SYS_REMARK6, jingdongRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderNo() + "转换完成");
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("京东退单转换异常:{}", "京东退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJingdongRefundService.updateRefundIsTransError(jingdongRefund,e.getMessage());
            return new ProcessStepResult<>(StepStatus.FAILED, "退单转换异常：" + e.getMessage());
        }

    }


    /**
     * 物流信息更新
     *
     * @param omsOrder 退换货单
     * @param midOrder 京东退款单
     * @return 更新结果
     */
    private boolean modifyLogisticInfo(OcBReturnOrder omsOrder, IpBJingdongRefund midOrder) {

        String midLgNum = midOrder.getExpresscode();
        String logisticNum = omsOrder.getLogisticsCode();
        OcBReturnOrder preUpdateOrder = null;
        if (StringUtils.isNotBlank(midLgNum) && !StringUtils.equalsIgnoreCase(midLgNum, logisticNum)) {
            preUpdateOrder = new OcBReturnOrder();
            preUpdateOrder.setId(omsOrder.getId());
            preUpdateOrder.setLogisticsCode(midOrder.getExpresscode());
        }

        String midLgCName = midOrder.getExpresscompany();
        String companyName = omsOrder.getCpCLogisticsEname();
        if (StringUtils.isNotBlank(midLgCName) && !StringUtils.equalsIgnoreCase(midLgCName, companyName)) {
            LogisticsInfo newLgInfo = cpRpcService.selectLogisticsInfoByName(midLgCName);
            if (newLgInfo != null) {
                if (preUpdateOrder == null) {
                    preUpdateOrder = new OcBReturnOrder();
                    preUpdateOrder.setId(omsOrder.getId());
                }
                preUpdateOrder.setCpCLogisticsId(newLgInfo.getId());
                preUpdateOrder.setCpCLogisticsEcode(newLgInfo.getCode());
                preUpdateOrder.setCpCLogisticsEname(newLgInfo.getName());
            }
        }

        if (preUpdateOrder != null) {
            //加入“空运单号延迟推单有效时间”字段
            preUpdateOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(preUpdateOrder));
            return ipJingdongRefundService.modifyReturnOrderLgInfo(preUpdateOrder);
        }
        return false;
    }

}
