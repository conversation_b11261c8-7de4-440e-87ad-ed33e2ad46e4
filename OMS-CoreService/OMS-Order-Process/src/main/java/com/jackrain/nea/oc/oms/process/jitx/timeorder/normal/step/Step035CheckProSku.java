package com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.step;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderOccupyItemStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 检查商品是否存在
 **/
@Step(order = 35, description = "检查商品是否存在")
@Slf4j
@Component
public class Step035CheckProSku extends BaseVipTimeOrderProcessStep
        implements IOmsOrderProcessStep<IpVipTimeOrderRelation> {


    @Override
    public ProcessStepResult<IpVipTimeOrderRelation> startProcess(IpVipTimeOrderRelation orderInfo, ProcessStepResult preStepResult
            , boolean isAutoMakeup, User operateUser) {
        IpBTimeOrderVip timeOrderVip = orderInfo.getIpBTimeOrderVip();
        List<IpBTimeOrderVipItemEx> timeOrderVipItemExList = orderInfo.getIpBTimeOrderVipItemExList();
        List<IpBTimeOrderVipOccupyItem> orderVipOccupyItemList = orderInfo.getIpBTimeOrderVipOccupyItemList();
        boolean hasErrorInfo = false;
        StringBuilder sbErrorInfo = new StringBuilder();
        try {
            //判断订单明细中商品条码是否在商品条码表中存在
            if (CollectionUtils.isEmpty(timeOrderVipItemExList)) {
                ipVipTimeOrderService.updateTimeOrderTransferStatus(TransferOrderStatus.TRANSFERFAIL.toInteger()
                        , "时效订单中间表明细为空，转换失败", timeOrderVip);
                return new ProcessStepResult<>(StepStatus.FAILED, "时效订单中间表明细为空，转换失败！");
            }
            for (IpBTimeOrderVipItemEx bTimeOrderVipItemEx : timeOrderVipItemExList) {
                String barcode = bTimeOrderVipItemEx.getBarcode();
                ProductSku skuInfo = null;
                if (StringUtils.isEmpty(barcode)) {
                    hasErrorInfo = true;
                    sbErrorInfo.append("商品条码为空;");
                    sbErrorInfo.append("\r\n");
                } else {
                    PsSkuResult psSkuResult = psRpcService.selectSkuInfoByforCodes(barcode);
                    if (psSkuResult != null) {
                        List<ProSku> barcodeList = psSkuResult.getProSkus();
                        if(CollectionUtils.isEmpty(barcodeList)){
                            hasErrorInfo = true;
                            String msg = Resources.getMessage("在系统中未查询到商品条码;");
                            sbErrorInfo.append(barcode);
                            sbErrorInfo.append(msg);
                            sbErrorInfo.append("\r\n");
                        }else if(barcodeList.size() >1){
                            hasErrorInfo = true;
                            String msg = Resources.getMessage("在系统中查询到多个商品条码;");
                            sbErrorInfo.append(barcode);
                            sbErrorInfo.append(msg);
                            sbErrorInfo.append("\r\n");
                        }else{
                            skuInfo = psRpcService.selectProductSku(barcodeList.get(0).getEcode());
                            if (skuInfo == null) {
                                hasErrorInfo = true;
                                String msg = Resources.getMessage("查询商品信息，不存在;");
                                sbErrorInfo.append(barcode);
                                sbErrorInfo.append(msg);
                                sbErrorInfo.append("\r\n");
                            } else {
                                bTimeOrderVipItemEx.setProdSku(skuInfo);
                            }
                        }
                    } else {
                        hasErrorInfo = true;
                        String msg = Resources.getMessage("在系统中未查询到商品条码;");
                        sbErrorInfo.append(barcode);
                        sbErrorInfo.append(msg);
                        sbErrorInfo.append("\r\n");
                    }
                }
                if (hasErrorInfo) {
                    //1：业务数据异常 2：系统程序异常
                    timeOrderVip.setExceptionType("1");
                    String errorMessage = "商品数据异常，退出转单操作";
                    boolean isUpdate = false;
                    // 此时 未确认或已缺货只会有一条
                    isUpdate = ipVipTimeOrderService.updateIsTransException(timeOrderVip, sbErrorInfo.toString());
                    if (!isUpdate) {
                        errorMessage += ";更新状态失败=False";
                    }
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("Step035CheckProSku end result：{}",
                                "Step035CheckProSku",timeOrderVip.getId()), errorMessage);

                    }
                    return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
                } else {
                    //转换商品
                    mappingProductInfo(orderVipOccupyItemList, barcode, skuInfo);
                }
            }
            //return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getBillNo() + "判断条码明细成功，进入下一阶段！");
            //跳过寻仓占单
            return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getBillNo() + "判断条码明细成功，进入下一阶段！",Step050TimeOrderOccupyStock.class);

        } catch (Exception e) {
            //1：业务数据异常 2：系统程序异常
            timeOrderVip.setExceptionType("2");
            log.error(LogUtil.format("时效订单订单转换异常,异常信息:{}", "时效订单订单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipVipTimeOrderService.updateIsTransException(timeOrderVip, e.getMessage());
            String errorMessage = "时效订单订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

    private void mappingProductInfo(List<IpBTimeOrderVipOccupyItem> orderVipOccupyItemList, String barcode, ProductSku skuInfo) {
        for (IpBTimeOrderVipOccupyItem item : orderVipOccupyItemList) {
            if (item.getBarcode().equals(barcode)) {
                IpBTimeOrderVipOccupyItem updateItem = new IpBTimeOrderVipOccupyItem();
                // 条码id
                updateItem.setPsCSkuId(skuInfo.getId());
                // 条码编码
                updateItem.setPsCSkuEcode(skuInfo.getSkuEcode());
                // 商品编码
                updateItem.setPsCProEcode(skuInfo.getProdCode());
                updateItem.setPsCProId(skuInfo.getProdId());
                updateItem.setPsCSizeId(skuInfo.getSizeId());
                updateItem.setPsCClrEcode(skuInfo.getColorCode());
                updateItem.setPsCSizeEcode(skuInfo.getSizeCode());
                updateItem.setPsCSizeEname(skuInfo.getSizeName());
                // 吊牌价
                updateItem.setPriceList(skuInfo.getPricelist());
                updateItem.setPsCClrId(skuInfo.getColorId());
                updateItem.setPsCClrEname(skuInfo.getColorName());
                // 商品名称
                updateItem.setPsCProEname(skuInfo.getName());
                UpdateWrapper<IpBTimeOrderVipOccupyItem> wrapper = new UpdateWrapper<>();
                wrapper.eq("id", item.getId());
                wrapper.eq("ip_b_time_order_vip_id", item.getIpBTimeOrderVipId());
                vipOccupyItemMapper.update(updateItem, wrapper);
            }
        }
    }

    private IpBTimeOrderVipOccupyItem buildVipOccupyItem(IpBTimeOrderVipOccupyItem vipOccupyItem) {
        IpBTimeOrderVipOccupyItem updateVipOccupyItem = new IpBTimeOrderVipOccupyItem();
        updateVipOccupyItem.setId(vipOccupyItem.getId());
        updateVipOccupyItem.setOutStockQuantity(vipOccupyItem.getOutStockQuantity());
        updateVipOccupyItem.setStatus(TimeOrderOccupyItemStatusEnum.OUT_OF_STOCK.getValue());
        return updateVipOccupyItem;
    }

    private IpBTimeOrderVip buildUpdateIpBTimeOrderVip(IpBTimeOrderVip ipBTimeOrderVip, StringBuilder sbErrorInfo) {
        IpBTimeOrderVip updateTimeOrder = new IpBTimeOrderVip();
        updateTimeOrder.setOccupiedOrderSn(ipBTimeOrderVip.getOccupiedOrderSn());
        updateTimeOrder.setIstrans(TransferOrderStatus.TRANSFERFAIL.toInteger());
        updateTimeOrder.setSysremark(sbErrorInfo.toString());
        updateTimeOrder.setOutStockQuantity(ipBTimeOrderVip.getOutStockQuantity());
        return updateTimeOrder;
    }
}
