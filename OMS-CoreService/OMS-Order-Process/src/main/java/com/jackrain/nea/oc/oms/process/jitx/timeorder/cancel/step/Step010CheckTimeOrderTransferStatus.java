package com.jackrain.nea.oc.oms.process.jitx.timeorder.cancel.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 判断取消时效订单据转单状态
 *
 * @author: chenxiulou
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 10, description = "判断取消时效订单据转单状态")
@Slf4j
@Component
public class Step010CheckTimeOrderTransferStatus extends BaseVipTimeOrderCancelProcessStep
        implements IOmsOrderProcessStep<IpBCancelTimeOrderVipRelation> {

    @Override
    public ProcessStepResult<IpBCancelTimeOrderVipRelation> startProcess(IpBCancelTimeOrderVipRelation timeOrderInfo,
                                                                         ProcessStepResult preStepResult,
                                                                         boolean isAutoMakeup, User operateUser) {
        if (timeOrderInfo == null || timeOrderInfo.getCancelTimeOrderVip() == null || timeOrderInfo.getCancelTimeOrderItemList() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "timeOrderInfo为空或者timeOrderInfo.getCancelTimeOrderVip或者timeOrderInfo.getCancelTimeOrderVipList为空；退出取消时效订单转单");
        }
        IpBCancelTimeOrderVipRelation orderInfo = timeOrderCancelService.selectCancelTimeOrder(timeOrderInfo.getOrderNo());
        if (orderInfo == null || orderInfo.getCancelTimeOrderVip() == null || orderInfo.getCancelTimeOrderItemList() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo为空或者timeOrderInfo.getCancelTimeOrderVip或者timeOrderInfo.getCancelTimeOrderVipList为空；退出取消时效订单转单");
        }
        timeOrderInfo.setCancelTimeOrderVip(orderInfo.getCancelTimeOrderVip());
        timeOrderInfo.setCancelTimeOrderItemList(orderInfo.getCancelTimeOrderItemList());
        timeOrderInfo.setRemarks(orderInfo.getRemarks());
        int currentStatus = timeOrderInfo.getCancelTimeOrderVip().getIstrans();
        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + timeOrderInfo.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            String operateMessage = Resources.getMessage("单据" + timeOrderInfo.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        }

    }
}
