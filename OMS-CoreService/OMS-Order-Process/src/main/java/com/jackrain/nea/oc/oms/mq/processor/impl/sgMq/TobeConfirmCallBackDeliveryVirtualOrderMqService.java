package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDelivery;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.process.jitx.feedback.JitxFeedBackDeliveryProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-03-14 14:43
 * @Description : 虚拟寻源占单结果
 **/
@Slf4j
@Component
public class TobeConfirmCallBackDeliveryVirtualOrderMqService {

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private IpBTimeOrderVipMapper ipBTimeOrderVipMapper;

    @Autowired
    private IpBJitxDeliveryMapper ipBJitxDeliveryMapper;
    @Autowired
    private IpBJitxDeliveryItemMapper ipBJitxDeliveryItemMapper;

    @Autowired
    private JitxFeedBackDeliveryProcessImpl jitxFeedBackDeliveryProcess;

    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    public void tobeConfirmDeliveryVirtualCallBackService(SgFindSourceStrategyOmsResult sgFindSourceStrategyOmsResult){
        int code = sgFindSourceStrategyOmsResult.getCode();
        String rootOrderSn = null;
        String orderSnStr = sgFindSourceStrategyOmsResult.getSourceBillNo();
        if(StringUtils.isBlank(orderSnStr)){
            log.error(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单结果处理，SourceBillNo为空","TobeConfirmCallBackDeliveryVirtualOrderMqService",rootOrderSn),
                    rootOrderSn);
            return;
        }

        //寻仓单单号集合
        List<String> orderSnList = Arrays.asList(orderSnStr.split(","));
        //查询对应时效单信息
        List<IpBTimeOrderVip> timeOrderList = ipBTimeOrderVipMapper.listTimeOrderByOrderSns(orderSnList);
        if(CollectionUtils.isEmpty(timeOrderList)){
            log.error(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单结果处理，orderSnStr:{}查询时效单信息为空","TobeConfirmCallBackDeliveryVirtualOrderMqService",rootOrderSn),
                    rootOrderSn,orderSnStr);
            //自动转换同批寻仓单
            transformDelivery(orderSnList);
            return;
        }
        rootOrderSn = timeOrderList.get(0).getRootOrderSn();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单结果处理orderSnStr:{}",
                    "TobeConfirmCallBackDeliveryVirtualOrderMqService",rootOrderSn), rootOrderSn,orderSnStr);
        }
        Map<String,List<IpBTimeOrderVip>> timeOrderMap = timeOrderList.stream()
                .collect(Collectors.groupingBy(IpBTimeOrderVip::getOrderSn));
        //时效单号集合
        List<String> occupiedOrderSnList =  timeOrderList.stream().map(IpBTimeOrderVip::getOccupiedOrderSn).collect(Collectors.toList());
        //时效单ID集合
        List<Long> timeOrderIdList =  timeOrderList.stream().map(IpBTimeOrderVip::getId).collect(Collectors.toList());


        if(ResultCode.SUCCESS != code){
            String messge = sgFindSourceStrategyOmsResult.getMessage();
            log.error(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单，库存中心占单返回状态失败:{}","TobeConfirmCallBackDeliveryVirtualOrderMqService",rootOrderSn),
                    rootOrderSn,messge);
            //更新所有时效订单虚拟寻仓结果：实体仓信息
            ipVipTimeOrderService.updateTimeOrderVirtualOccupy(timeOrderIdList,-1L,null,null);
            //更新所有时效订单虚拟寻仓标识：虚拟寻仓完成
            ipBTimeOrderVipMapper.updateInVirtualOccupyFlag(occupiedOrderSnList, VipConstant.JITX_DELIVERY_IS_VIRTUAL_OCCUPY_02);
            //自动转换同批寻仓单
            transformDelivery(orderSnList);
            return;
        }

        List<SgFindSourceStrategyOmsItemResult> itemResultList = sgFindSourceStrategyOmsResult.getItemResultList();
        if(CollectionUtils.isEmpty(itemResultList)){
            String messge = "寻仓单虚拟占单结果更新失败，SG返回的占单明细为空！";
            log.error(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单，库存中心占单明细返回为空:{}","TobeConfirmCallBackDeliveryVirtualOrderMqService",rootOrderSn),
                    rootOrderSn,messge);
            //更新所有时效订单虚拟寻仓结果：实体仓信息
            ipVipTimeOrderService.updateTimeOrderVirtualOccupy(timeOrderIdList,-1L,null,null);
            //更新所有时效订单虚拟寻仓标识：虚拟寻仓完成
            ipBTimeOrderVipMapper.updateInVirtualOccupyFlag(occupiedOrderSnList, VipConstant.JITX_DELIVERY_IS_VIRTUAL_OCCUPY_02);
            //自动转换同批寻仓单
            transformDelivery(orderSnList);
            return;
        }
        //根据明细ID分组
        Map<Long,List<SgFindSourceStrategyOmsItemResult>> itemResultMap = itemResultList.stream()
                .collect(Collectors.groupingBy(SgFindSourceStrategyOmsItemResult::getSourceItemId));
        for(String orderSn:orderSnList){
            IpBJitxDelivery orderInfo = ipBJitxDeliveryMapper.selectJitxOrderByOrderSn(orderSn);
            if (orderInfo == null) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单结果处理，根据寻仓单orderSn:{}查询寻仓单为空",
                            "TobeConfirmCallBackDeliveryVirtualOrderMqService",rootOrderSn,orderSn), rootOrderSn,orderSn);
                }
                continue;
            }
            List<IpBJitxDeliveryItemEx> orderItemList = this.ipBJitxDeliveryItemMapper.selectDeliveryItemList(orderInfo.getId());
            Long sourceItemId = orderItemList.get(0).getId();
            List<SgFindSourceStrategyOmsItemResult> itemResults = itemResultMap.get(sourceItemId);
            if(CollectionUtils.isEmpty(itemResults)){
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单结果处理，根据寻仓单orderSn:{}获取占单明细为空",
                            "TobeConfirmCallBackDeliveryVirtualOrderMqService",rootOrderSn,orderSn), rootOrderSn,orderSn);
                }
                continue;
            }
            List<IpBTimeOrderVip> timeOrderVips = timeOrderMap.get(orderSn);
            if(CollectionUtils.isEmpty(timeOrderVips)){
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单结果处理，根据寻仓单orderSn:{}查询时效单为空",
                            "TobeConfirmCallBackDeliveryVirtualOrderMqService",rootOrderSn,orderSn), rootOrderSn,orderSn);
                }
                continue;
            }
            List<Long> timeOrderIds =  timeOrderVips.stream().map(IpBTimeOrderVip::getId).collect(Collectors.toList());
            SgFindSourceStrategyOmsItemResult itemResult = itemResults.get(0);
            //更新所有时效订单虚拟寻仓结果：实体仓信息
            ipVipTimeOrderService.updateTimeOrderVirtualOccupy(timeOrderIds,itemResult.getWareHouseId(),itemResult.getWareHouseEname(),itemResult.getWareHouseEcode());
        }
        //更新所有时效订单虚拟寻仓标识：虚拟寻仓完成
        updateInVirtualOccupyFlag(occupiedOrderSnList, VipConstant.JITX_DELIVERY_IS_VIRTUAL_OCCUPY_02,rootOrderSn);
        //自动转换同步寻仓单
        transformDelivery(orderSnList);
    }

    /**
     * 更新所有时效订单虚拟寻仓标识：虚拟寻仓完成
     * @param occupiedOrderSnList
     * @param flag
     * @param rootOrderSn
     */
    public void updateInVirtualOccupyFlag(List<String> occupiedOrderSnList,Integer flag,String rootOrderSn){
        ipBTimeOrderVipMapper.updateInVirtualOccupyFlag(occupiedOrderSnList, flag);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("唯品会寻仓单根单号rootOrderSn:{}虚拟寻源占单结果处理，更新时效单虚拟寻仓标识occupiedOrderSnList:{}",
                    "TobeConfirmCallBackDeliveryVirtualOrderMqService",rootOrderSn), rootOrderSn,occupiedOrderSnList);
        }
    }

    /**
     * 对寻仓单进行转单
     * @param orderSnList
     */
    public void transformDelivery(List<String> orderSnList){
        for (String orderNo:orderSnList){
            IpJitxDeliveryRelation jitxDeliveryRelation = ipJitxDeliveryService.selectJitxDelivery(orderNo);
            if (jitxDeliveryRelation == null || jitxDeliveryRelation.getJitxDelivery() == null) {
                log.error(LogUtil.format("寻仓单orderSn:{}虚拟寻源占单完成后自动转换寻仓单失败，查询原单信息为空","TobeConfirmCallBackDeliveryVirtualOrderMqService.transformDelivery"),
                        orderNo);
            } else {
                ProcessStepResultList resultList = jitxFeedBackDeliveryProcess.start(jitxDeliveryRelation,
                        false, SystemUserResource.getRootUser());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("寻仓单orderSn:{}虚拟寻源占单完成后自动转换Result:{}",
                            "TobeConfirmCallBackDeliveryVirtualOrderMqService.transformDelivery"),orderNo,resultList);
                }
            }
        }
    }
}
