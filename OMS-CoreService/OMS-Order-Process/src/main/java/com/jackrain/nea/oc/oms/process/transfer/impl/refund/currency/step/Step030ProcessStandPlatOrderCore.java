package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefundType;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.util.OrderStatusUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 5:07 下午
 * @Version 1.0
 */
@Step(order = 30, description = "处理订单")
@Slf4j
@Component
public class Step030ProcessStandPlatOrderCore extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {
    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        try {
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
            Integer hasGoodReturn = ipBStandplatRefund.getHasGoodReturn();
            Integer refundType = ipBStandplatRefund.getRefundType();
            for (OmsOrderRelation orderRelation : omsOrderRelation) {
                OcBOrder ocBOrder = orderRelation.getOcBOrder();
                Integer orderStatus = ocBOrder.getOrderStatus();
                //判断订单是否为发货前
                boolean statusFront = OrderStatusUtil.checkOrderStatusFront(orderStatus);
                if (statusFront) {
                    //发货前
                    orderRelation.setOrderMark(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode());
                } else {
                    List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
                    ocBOrderItems =ocBOrderItems.stream().filter(p -> p.getRefundStatus() != null && !p.getRefundStatus().equals(6)).collect(Collectors.toList());
                    orderRelation.setOcBOrderItems(ocBOrderItems);
                    List<OcBOrderItem> itemList = ocBOrderItems.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
                    orderRelation.setOcBOrderItems(itemList);
                    //  小平台可能退款类型和has_good_return 会是空的 ，打发货后退款标记 ,或者是换货
                    if (Objects.isNull(ipBStandplatRefund.getRefundType())
                            || (Objects.equals(refundType, IpBStandplatRefundType.RETURN_GOODS_RERUEN))
                            || (Objects.equals(refundType, IpBStandplatRefundType.EXCHANGE_GOODS))) {
                        //发货后
                        orderRelation.setOrderMark(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode());
                    } else if (TaobaoReturnOrderExt.HasGoodReturnStatus.NO_RETURN.getCode().equals(hasGoodReturn) ||
                            IpBStandplatRefundType.ONLY_REFUND == refundType) {
                        orderRelation.setOrderMark(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode());
                    } else {
                        // 默认拦截
                        //发货后
                        orderRelation.setOrderMark(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode());
                    }
                }
            }
            //判断是否存在发货前订单也存在发货后订单
            List<OmsOrderRelation> bfOrderRelation = omsOrderRelation.stream().filter(x -> TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode().equals(x.getOrderMark())).collect(Collectors.toList());
            List<Integer> orderMarkList = new ArrayList<>();
            orderMarkList.add(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode());
            orderMarkList.add(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode());
            List<OmsOrderRelation> afOrderRelation = omsOrderRelation.stream().filter(x -> orderMarkList.contains(x.getOrderMark())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(bfOrderRelation) && CollectionUtils.isNotEmpty(afOrderRelation)){
                ipBStandplatRefund.setReserveVarchar10("T");
            }

            return new ProcessStepResult<>(StepStatus.SUCCESS, "订单处理成功,进入下一阶段");
        } catch (Exception e) {
            log.error(LogUtil.format("订单处理异常:{}", "订单处理异常"), Throwables.getStackTraceAsString(e));
            return new ProcessStepResult<>(StepStatus.FAILED, "订单处理失败");
        }
    }
}
