package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobao.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpTaobaoExchangeService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;

//import com.jackrain.nea.oc.oms.services.OmsOrderMakeupService;

/**
 * 基础淘宝换货单转单处理阶段
 *
 * @author: 孙勇生
 * @since: 2019-03-07
 * create at : 2019-03-07 23:13
 */
public abstract class BaseTaobaoExchangeProcessStep {

    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

//    @Autowired
//    protected RedisLockOrderUtil redisUtil;

//    /**
//     * OMS订单补偿服务操作Service
//     */
//    @Autowired
//    protected OmsOrderMakeupService orderMakeupService;

    @Autowired
    protected IpTaobaoExchangeService ipTaobaoExchangeService;

    //    @Autowired
//    protected OmsOrderService orderService;
//
    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;

}
