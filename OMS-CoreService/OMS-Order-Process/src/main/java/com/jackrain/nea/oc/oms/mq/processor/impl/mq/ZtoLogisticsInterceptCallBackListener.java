package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.services.ZtoLogisticsInterceptCallBackService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2023/3/6 21:22
 * @Description
 */
@Slf4j
@RocketMqMessageListener(name = "ZtoLogisticsInterceptCallBackListener", type = MqTypeEnum.DEFAULT)
public class ZtoLogisticsInterceptCallBackListener implements BaseMessageListener {


    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        try {
            if (log.isInfoEnabled()) {
                log.info(LogUtil.format("ZtoLogisticsInterceptCallBackListener.consume messageBody{},messageKey{},messageId{},messageTopic{}",
                        "ZtoLogisticsInterceptCallBackListener.consume"), messageBody, messageKey, messageKey, messageTopic);
            }
            ZtoLogisticsInterceptCallBackService bean = ApplicationContextHandle.getBean(ZtoLogisticsInterceptCallBackService.class);
            bean.execute(messageBody);
        } catch (Exception e) {
            log.error(LogUtil.format("ZtoLogisticsInterceptCallBackListener.consume.error：{}"), Throwables.getStackTraceAsString(e));
        }
    }
}
