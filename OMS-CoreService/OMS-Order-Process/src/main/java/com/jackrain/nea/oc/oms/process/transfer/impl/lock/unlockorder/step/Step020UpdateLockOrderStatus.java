package com.jackrain.nea.oc.oms.process.transfer.impl.lock.unlockorder.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.IpOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @Descroption 更新锁单中间表状态
 * <AUTHOR>
 * @Date 2019/10/10 14:14
 */
@Step(order = 20, description = "更新锁单中间表状态")
@Slf4j
@Component
public class Step020UpdateLockOrderStatus extends BaseUnlockProcessStep
        implements IOmsOrderProcessStep<IpOrderLockRelation> {
    @Override
    public ProcessStepResult<IpOrderLockRelation> startProcess(IpOrderLockRelation orderInfo, ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        IpBOrderLock orderLock = orderInfo.getOrderLock();
        try {
            orderLock.setUnlockTime(new Date());
            ipOrderLockService.updateLockOrder(IpOrderLockStatusEnum.UNLOCK.getKey(), null, orderInfo.getRemarks(), orderLock);
            orderInfo.setRemarks(null);
        } catch (Exception e) {
            log.error(LogUtil.format("锁单设定解锁状态异常:{}", "锁单设定解锁状态异常"), Throwables.getStackTraceAsString(e));
            orderInfo.setRemarks("更新锁单中间表状态失败，解锁失败");
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
