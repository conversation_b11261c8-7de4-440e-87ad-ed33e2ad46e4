package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.step;

import com.google.common.base.Throwables;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 转换全渠道订单表状态待审核、缺货、配货中且“WMS撤回状态”为已撤回,执行标记为退款完成服务
 * @Date 2019-6-26
 **/
@Step(order = 70, description = "全渠道订单表状态待审核、缺货、配货中")
@Slf4j
@Component
public class Step070RefundFinishStatus extends BaseJitxRefundProcessStep implements IOmsOrderProcessStep<IpJitxOrderRelation> {
    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJitxOrder ipBJitxOrder = orderInfo.getJitxOrder();
        if (log.isDebugEnabled()) {
            log.debug("Step070RefundFinishStatus{}", orderInfo.getOrderNo());
        }
        try {
            List<OcBOrder> ocBOrderList = ipJitxRefundService.selectOmsOrder(orderInfo.getOrderNo());
            //1、待审核、缺货、配货中  UNCONFIRMED  BE_OUT_OF_STOCK IN_DISTRIBUTION
            for (OcBOrder ocBOrder : ocBOrderList) {
                Integer ocBOrderStatus = ocBOrder.getOrderStatus();
                if ((OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrderStatus)
                        || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(ocBOrderStatus))) {
                    //2、执行执行标记为退款完成服务
                    ValueHolderV14 vh14 = ipJitxRefundService.markRefund(ipBJitxOrder.getOrderSn(), ocBOrder, operateUser);
                    if (!vh14.isOK()) {
                        ipJitxRefundService.updateRefundIsTrans(TransferOrderStatus.NOT_TRANSFER, vh14.getMessage(), ipBJitxOrder);
                        return new ProcessStepResult<>(StepStatus.FAILED, "标记为退款完成失败，退出转单！");
                    }

                    releaseHoldOrder(operateUser, ocBOrder);

                    //4、调用订单日志服务
//                    omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
//                            OrderLogTypeEnum.ORDER_CANCLE.getKey(), "订单取消成功！", null, null, operateUser);
                }
            }
            ipJitxRefundService.updateRefundIsTrans(TransferOrderStatus.TRANSFERRED, "", ipBJitxOrder);
            return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderNo() + "退款关闭，转换成功！");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 退单转换异常！", Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJitxRefundService.updateRefundIsTransError(ipBJitxOrder, e.getMessage());
            String errorMessage = "退单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

    /**
     * 释放hold单
     *
     * @param operateUser
     * @param ocBOrder
     */
    private void releaseHoldOrder(User operateUser, OcBOrder ocBOrder) {
        OcBOrder ocBOrderDto = new OcBOrder();
        // 分库键
        ocBOrderDto.setId(ocBOrder.getId());
        // 是否已经拦截：0
        ocBOrderDto.setIsInterecept(0);
        // 是否退款中：0
        ocBOrderDto.setIsInreturning(0);
        // 修改人：当前操作人
        ocBOrderDto.setModifierid(Long.valueOf(operateUser.getId()));
        ocBOrderDto.setModifierename(operateUser.getEname());
        ocBOrderDto.setModifiername(operateUser.getName());
        // 修改时间：当前操作时间
        ocBOrderDto.setModifieddate(new Date());
        ipJitxRefundService.updateOmsOrder(ocBOrderDto);
    }
}
