package com.jackrain.nea.oc.oms.process.transfer.impl.lock.unlockorder;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * @Descroption 解锁
 * <AUTHOR>
 * @Date 2019/10/9 18:27
 */
@Component
public class OrderUnlockProcessImpl extends AbstractOrderProcess<IpOrderLockRelation> {

    public OrderUnlockProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return null;
    }

    @Override
    protected long getProcessOrderId(IpOrderLockRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpOrderLockRelation orderInfo) {
        return null;
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.LOCK_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return null;
    }
}
