package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrderItemEx;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.api.result.PsSkuResult;
import com.jackrain.nea.ps.api.table.ProSku;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 检查订单商品是否在系统中
 *
 * @author: 黄超
 * @since: 2019-06-26
 * create at : 2019-06-26 9:00
 */
@Step(order = 30, description = "检查订单商品是否在系统中")
@Component
@Slf4j
public class Step30JitxCheckProductExist extends BaseJitxOrderProcessStep implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Autowired
    private PsRpcService psRpcService;

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        boolean hasErrorInfo = false;
        List<String> errors = Lists.newArrayList();

        ProductSku skuInfo = null;
        for (IpBJitxOrderItemEx tbOrderItem : orderInfo.getJitxOrderItemList()) {
            String barcode = tbOrderItem.getBarcode();
            if (StringUtils.isEmpty(barcode)) {
                hasErrorInfo = true;
                errors.add("商品条码为空");
                continue;
            }

            PsSkuResult psSkuResult = psRpcService.selectJitxSkuInfo(Lists.newArrayList(barcode));
            if (Objects.isNull(psSkuResult)) {
                //查商品强绑定信息
                hasErrorInfo = this.highMer(orderInfo, hasErrorInfo, errors, tbOrderItem, barcode);
                continue;
            }

            //结果有值，查询商品信息
            List<ProSku> barcodeList = psSkuResult.getProSkus();
            if (!CollectionUtils.isEmpty(barcodeList) && barcodeList.get(0).getEcode() != null) {
                skuInfo = psRpcService.selectProductSku(barcodeList.get(0).getEcode());
            }
            if (Objects.isNull(skuInfo)) {
                //查商品强绑定信息
                hasErrorInfo = this.highMer(orderInfo, hasErrorInfo, errors, tbOrderItem, barcode);
            } else if (YesNoEnum.N.getKey().equals(skuInfo.getIsactive())) {
                hasErrorInfo = true;
                errors.add("商品条码已作废");
            } else {
                tbOrderItem.setProdSku(skuInfo);
            }
        }

        if (hasErrorInfo) {
            String errorMessage = "商品数据不存在或已作废，退出转单操作";
            boolean updateStatusRes = ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(),
                    TransferOrderStatus.NOT_TRANSFER, JSON.toJSONString(errors));
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else {
            return new ProcessStepResult<>(StepStatus.SUCCESS, null, "检查商品是否存在成功，进入下一阶段",
                    Step35JitxRedeliverQueryLogisticsNo.class);
        }
    }

    /**
     * 查询强绑定信息
     *
     * @param orderInfo
     * @param hasErrorInfo
     * @param errors
     * @param tbOrderItem
     * @param barcode
     * @return
     */
    private boolean highMer(IpJitxOrderRelation orderInfo, boolean hasErrorInfo, List<String> errors, IpBJitxOrderItemEx tbOrderItem, String barcode) {
        List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(barcode, orderInfo.getJitxOrder().getCpCShopId());
        if (CollectionUtils.isEmpty(productSkus)) {
            if (log.isDebugEnabled()) {
                log.debug("psRpcService.selectJitxSkuInfo 查询的数据为空");
            }
            hasErrorInfo = true;
            skuNoExist(errors, barcode);
        } else if (!CollectionUtils.isEmpty(productSkus) && productSkus.size() == 1) {
            if (YesNoEnum.N.getKey().equals(productSkus.get(0).getIsactive())) {
                hasErrorInfo = true;
                skuNoActive(errors, barcode);
            } else {
                tbOrderItem.setProdSku(productSkus.get(0));
            }
        } else {
            log.error("psRpcService.selectJitxSkuInfo 查询的数据productSkus多条,orderNo:{},barcode:{}", orderInfo.getOrderNo(), barcode);
            hasErrorInfo = true;
            skuNoExist(errors, barcode);
        }
        return hasErrorInfo;
    }

    private void skuNoExist(List<String> errors, String barcode) {
        String msg = Resources.getMessage("不存在");
        String errorMsg = barcode + msg;
        errors.add(errorMsg);
    }

    private void skuNoActive(List<String> errors, String barcode) {
        String msg = Resources.getMessage("已作废");
        String errorMsg = barcode + msg;
        errors.add(errorMsg);
    }
}
