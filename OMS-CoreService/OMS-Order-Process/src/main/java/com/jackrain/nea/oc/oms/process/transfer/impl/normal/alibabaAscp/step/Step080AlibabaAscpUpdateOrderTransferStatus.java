package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 释放当前行，更新转换状态为2
 *
 * @author: 秦雄飞
 * @date 2020/9/3 下午4:32
 */
@Step(order = 80, description = "释放当前行，更新转换状态为2")
@Component
@Slf4j
public class Step080AlibabaAscpUpdateOrderTransferStatus extends BaseAlibabaAscpOrderProcessStep
        implements IOmsOrderProcessStep<IpAlibabaAscpOrderRelation> {

    @Override
    public ProcessStepResult<IpAlibabaAscpOrderRelation> startProcess(IpAlibabaAscpOrderRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        this.ipAlibabaAscpOrderService.updateAlibabaAscpOrderTransStatus(orderNo,
                TransferOrderStatus.TRANSFERRED, "转单成功");
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}