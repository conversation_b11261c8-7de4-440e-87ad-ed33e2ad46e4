package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefundType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.util.OrderStatusUtil;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @Author: yangrui
 * @Date: 2024-10-09 10:50
 */
@Step(order = 61, description = "关键词小额仅退款自动退款")
@Slf4j
@Component
public class Step061OrderFlagReturnOnlyAutoRefund extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Resource
    private OmsSystemConfig omsSystemConfig;
    @Resource
    protected OmsRefundOrderService omsRefundOrderService;
    private static final String REFUND_FLAG_KEY_WORD = "小额退";
    // 电商销售仅退款
    private static final String BUSINESS_TYPE_CODE = "RYTK03";

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup,
                                                                      User operateUser) {
        if (!omsSystemConfig.isOrderFlagAutoReturn()) {
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        }
        try {
            IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            IpBStandplatOrder ipBStandplatOrder = orderInfo.getIpBStandplatOrder();
            OcBOrder ocBOrder = omsOrderRelation.get(0).getOcBOrder();
            Integer platform = standplatRefundOrderTransferUtil.getStandplatRefundPlatmform(ipBStandplatRefund);
            boolean isDouYin = PlatFormEnum.DOU_YIN.getCode().equals(platform);
            if (!isDouYin) {
                // 目前只处理抖音平台
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            // 判断已发货仅退款
            Integer refundType = ipBStandplatRefund.getRefundType();
            boolean onlyRefund = Objects.equals(IpBStandplatRefundType.ONLY_REFUND, refundType);
            if (!onlyRefund) {
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            for (OmsOrderRelation orderRelation : omsOrderRelation) {
                OcBOrder order = orderRelation.getOcBOrder();
                Integer orderStatus = order.getOrderStatus();
                boolean afterShip = OrderStatusUtil.checkOrderStatusAfter(orderStatus);
                if (!afterShip) {
                    return new ProcessStepResult<>(StepStatus.SUCCESS);
                }
            }
            // 状态需要为等待卖家同意
            Integer returnStatus = ipBStandplatRefund.getReturnStatus();
            String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
            boolean waitSellerAgree = TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status);
            if (!waitSellerAgree) {
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            // 退款金额小于实付金额
            BigDecimal refundAmt = ipBStandplatRefund.getRefundAmount();
            BigDecimal payAmount = ipBStandplatOrder.getPayment();
            boolean part = Objects.nonNull(refundAmt) && Objects.nonNull(payAmount)
                    && refundAmt.compareTo(payAmount) < 0;
            if (!part) {
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }

            // 业务类型判断
            OcBReturnAfSend returnAfSend = omsRefundOrderService.selectOcBReturnAfSend(ipBStandplatRefund.getReturnNo());
            if (Objects.isNull(returnAfSend)
                    || ObjectUtil.notEqual(returnAfSend.getBusinessTypeCode(), BUSINESS_TYPE_CODE)) {
                log.info(" Step061OrderFlagReturnOnlyAutoRefund returnAfSend为空 或业务类型不符合 {}",
                        JsonUtils.toJsonString(returnAfSend));
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }

            // 检查关键词
            if (!checkSellerMemo(ocBOrder.getSellerMemo(), ipBStandplatRefund.getRefundAmount())) {
                // 关键词匹配失败
                log.info(" Step061OrderFlagReturnOnlyAutoRefund 关键词匹配失败 {},{}",
                        ocBOrder.getSellerMemo(), ipBStandplatRefund.getRefundAmount());
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            // 判断店铺是否对接AG
            boolean isJointAg = standPlatAutoRefundService.isToAgByShopStrategy(ocBOrder.getCpCShopId());
            if (!isJointAg) {
                // 未对接AG
                log.info(" Step061OrderFlagReturnOnlyAutoRefund 店铺未对接AG {}", ocBOrder.getCpCShopId());
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            // 是否已经进行过关键词AG退款
            boolean hasFlag = omsRefundOrderService.orderHasFlagAutoReturn(ipBStandplatOrder.getTid());
            if (hasFlag) {
                log.info(" Step061OrderFlagReturnOnlyAutoRefund 订单已经进行过关键词AG退款 {}", ipBStandplatOrder.getTid());
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            // 关键词匹配成功，调用AG退款
            String redisKey = BllRedisKeyResources.getOnlyReturnOrderFlagAutoRefund(ipBStandplatOrder.getTid());
            CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
            Long incr = objRedisTemplate.opsForValue().increment(redisKey, 1L);
            objRedisTemplate.expire(redisKey, 60, TimeUnit.DAYS);
            // 确保只会调用一次
            if (Objects.equals(incr, 1L)) {
                // 调用退款
                log.info(" Step061OrderFlagReturnOnlyAutoRefund  满足关键词退款条件，开始调用AG退款 {}",
                        ipBStandplatRefund.getReturnNo());
                boolean isToAg = standPlatAutoRefundService
                        .executeAutoRefund(orderInfo, operateUser,
                                null, null, false, true);
                if (isToAg) {
                    // 更新已发货退款单
                    log.info(" Step061OrderFlagReturnOnlyAutoRefund 调用AG退款成功 更新已发货退款单 {}",
                            ipBStandplatRefund.getReturnNo());
                    omsRefundOrderService.updateAutoRefundFlag(1, ipBStandplatRefund.getReturnNo());
                }
            } else {
                log.info(" Step061OrderFlagReturnOnlyAutoRefund 订单已经进行过关键词AG退款（count>1） {}",
                        ipBStandplatOrder.getTid());
            }
        } catch (Exception e) {
            log.error(LogUtil.format("小额仅退款处理失败={}",
                    "Step061OrderFlagReturnOnlyAutoRefund"), Throwables.getStackTraceAsString(e));
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }

    private boolean checkSellerMemo(String sellerMemo, BigDecimal refundAmount) {
        if (StrUtil.isBlank(sellerMemo)) {
            return false;
        }
        // 包含小额退关键词
        String amountRegx ="(\\d+|\\d+\\.\\d+)";
        String regx = REFUND_FLAG_KEY_WORD + amountRegx + "元";
        String match = ReUtil.get(regx, sellerMemo, 0);
        if (Objects.isNull(match)) {
            return false;
        }
        String remarkAmount = match.replaceFirst(REFUND_FLAG_KEY_WORD, "")
                .replaceFirst("元", "");
        try {
            BigDecimal remarkAmt = new BigDecimal(remarkAmount);
            return remarkAmt.compareTo(refundAmount) == 0;
        } catch (Exception e) {
            return false;
        }
    }

}
