package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Maps;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.util.OrderStatusUtil;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.services.OmsBeforeShipmentReturnService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/3/2 9:52 上午
 * @Version 1.0
 */
@Step(order = 60, description = "拦截处理")
@Slf4j
@Component
public class Step060OrderStandPlatGoodsAfterRefundOnly extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Autowired
    private OmsSystemConfig omsSystemConfig;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsBeforeShipmentReturnService omsBeforeShipmentReturnService;

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        try {
            OcBOrder ocBOrder = null;
            List<Long> refundIds = new ArrayList<>();
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            Integer returnStatus = ipBStandplatRefund.getReturnStatus();
            String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
            boolean isAgree = TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)
                    || TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status);
            boolean closed = TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status);
            // 拒绝是否取消退换货单
            boolean refuseControl = TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(status)
                    && !omsRefundOrderService.checkReturnOrder(ipBStandplatRefund.getCpCShopId());
            //组合商品是否生成退单
            boolean isCombinationFReturnOrder = false;
            int isSuccess = 0;
            //记录原订单信息
            List<OmsOrderRelation> orderRelationList = new ArrayList<>();
            if (isAgree) {
                for (OmsOrderRelation orderRelation : omsOrderRelation) {
                    if (TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode().equals(orderRelation.getOrderMark())) {
                        orderRelationList.add(orderRelation);
                        ocBOrder = orderRelation.getOcBOrder();
                        List<OcBOrderDelivery> orderDeliveries = orderRelation.getOrderDeliveries();
                        if (CollectionUtils.isEmpty(orderDeliveries)) {
                            isSuccess++;
                            continue;
                        }
                        AtomicBoolean isCreatedReturnOrder = new AtomicBoolean(true);
                        // 是否整单退
                        if (!orderInfo.isFullRefund()) {
                            Map<String, BigDecimal> feeMap = Maps.newHashMap();
                            orderInfo.getIpBStandplatRefundItem().forEach(current -> {
                                feeMap.put(current.getSubOrderId(), current.getRefundFee());
                            });
                            // 找到原单交易金额
                            orderRelation.getOcBOrderItems().forEach(current -> {
                                // 假如能匹配到子单据明细 并且 金额不等
                                if (feeMap.containsKey(current.getOoid())) {
                                    // 如果退货金额大于订单金额也生成退货单
                                    BigDecimal orderRealAmt = current.getRealAmt() == null ? BigDecimal.ZERO : current.getRealAmt();
                                    BigDecimal returnRealAmt = feeMap.get(current.getOoid()) == null ? BigDecimal.ZERO : feeMap.get(current.getOoid());
                                    if (returnRealAmt.compareTo(orderRealAmt) < 0) {
                                        isCreatedReturnOrder.set(false);
                                    }
                                }
                            });
                        }
                        // 如果上面判断为不是全额退  但是因为有的平台会有平台补贴金额的问题，导致计算出问题。现在对抖音平台做特殊校验
                        if ((!isCreatedReturnOrder.get()) && PlatFormEnum.DOU_YIN.getCode().equals(Integer.valueOf(ipBStandplatRefund.getCpCPlatformEcode()))) {
                            // 需要根据平台单号 查找通用订单与通用退单
                            IpBStandplatOrder ipBStandplatOrder = orderInfo.getIpBStandplatOrder();
                            if (ObjectUtil.isNotNull(ipBStandplatOrder) && ObjectUtil.isNotNull(ipBStandplatRefund)) {
                                // 获取ipBStandplatRefund的退款金额与ipBStandplatOrder实付金额
                                BigDecimal refundAmount = ipBStandplatRefund.getRefundAmount();
                                BigDecimal payAmount = ipBStandplatOrder.getPayment();
                                // 比较refundAmount是否大于等于payAmount
                                if (refundAmount.compareTo(payAmount) >= 0) {
                                    // 打印日志
                                    log.info("退款金额大于付款金额，需要生成退换货单{}", orderInfo.getOrderNo());
                                    isCreatedReturnOrder.set(true);
                                }
                            }
                        }

                        if (isCreatedReturnOrder.get()) {
                            if (log.isDebugEnabled()) {
                                log.info("买家未收到货，仅退款{}", orderInfo.getOrderNo());
                            }
                            List<OcBOrderItem> orderItems = new ArrayList<>();
                            for (OmsOrderRelation relation : omsOrderRelation) {
                                // 需要过滤掉平台发货、仓库发货状态的单据
                                OcBOrder order = relation.getOcBOrder();
                                boolean statusFront = OrderStatusUtil.checkOrderStatusFront(order.getOrderStatus());
                                if (!statusFront) {
                                    orderItems.addAll(relation.getOcBOrderItems());
                                }
                            }
                            omsRefundOrderService.foundRefundSlipAfterRefundOnly(orderItems,
                                    orderRelation.getOcBOrder(), orderInfo, SystemUserResource.getRootUser(),true);
                            //保险期间搞个开关
                            boolean flag = omsSystemConfig.isCreatedReturnOrder();
                            if (log.isDebugEnabled()) {
                                log.debug("isCreatedReturnOrder.flag:{}", flag);
                            }
                            List<OcBReturnOrderRelation> orderRelations;

                            //判断是否为组合商品 查询原单是否有组合商品，防止组合商品拆单
                            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectList(new QueryWrapper<OcBOrder>().lambda()
                                    .eq(OcBOrder::getTid, ipBStandplatRefund.getOrderNo()));
                            //打有组合标的订单
                            List<OcBOrder> zhOrderList = ocBOrderList.stream().filter(bOrder -> bOrder.getIsCombination() == 1).collect(Collectors.toList());
                            if (flag){
                                //买家未收到货
                                if (CollectionUtils.isNotEmpty(zhOrderList)){
                                    log.info(" 组合商品发货后退单生成 {},isCombinationFReturnOrder{}", JSON.toJSONString(orderInfo),isCombinationFReturnOrder);
                                    if (isCombinationFReturnOrder){
                                        continue;
                                    }
                                    List<OcBOrderItem> refundNoNeedOrderItemList = new ArrayList<>();
                                    orderRelations = standplatRefundOrderTransferUtil.
                                            standplatRefundOrderToReturnOid(orderInfo, omsOrderRelation, ipBStandplatRefund, orderInfo.getIpBStandplatRefundItem(), TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT, operateUser, refundNoNeedOrderItemList);
                                    log.info(" 组合商品发货后仅退款退单生成.orderRelations {}", JSON.toJSONString(orderRelations));
                                    if (CollectionUtils.isEmpty(orderRelations)) {
                                        String remark = "申请数量大于可退数量,转换结束";
                                        TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.QTY_NOT_ENOUGH);
                                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                                remark, ipBStandplatRefund);
                                        return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                                    }
                                    isCombinationFReturnOrder = true;
                                }else {
                                    orderRelations =  omsStandPlatAfterRefundOnlyService.goodsAfterRefundOnly(orderRelation, ipBStandplatRefund, operateUser);
                                }
                                if (CollectionUtils.isNotEmpty(orderRelations)) {
                                    //插入数据
                                    List<Long> refundId = omsReturnOrderService.insertOmsReturnOrderInfo(orderRelations, operateUser);
                                    refundIds.addAll(refundId);
                                    log.info("=========== insert data refundIds ========");
                                }
                            }else {
                            }
                        } else {

                            if (log.isDebugEnabled()) {
                                log.info("生成发货后退款单(仅退款),{}", orderInfo.getOrderNo());
                            }
                            List<OcBOrderItem> orderItems = new ArrayList<>();
                            for (OmsOrderRelation relation : omsOrderRelation) {
                                // 需要过滤掉平台发货、仓库发货状态的单据
                                OcBOrder order = relation.getOcBOrder();
                                boolean statusFront = OrderStatusUtil.checkOrderStatusFront(order.getOrderStatus());
                                if (!statusFront) {
                                    orderItems.addAll(relation.getOcBOrderItems());
                                }
                            }
                            // 仅退款
                            omsRefundOrderService.foundRefundSlipAfterRefundOnly(orderItems,
                                    orderRelation.getOcBOrder(), orderInfo, SystemUserResource.getRootUser(),false);
//                           String remark = "生成发货后退款单(仅退款)成功";
//                            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
//                                    remark, ipBStandplatRefund);
//                            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                        }
                    }
                }
                if (isSuccess == omsOrderRelation.size()) {
                    String remark = "发货后仅退款,未查询到发货信息,转换结束";
                    TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.AF_SEND_NOT_FOUND_SEND_INFO);
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                            remark, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FAILED, remark);
                }

                //通用订单（如抖音）申请退款，后取消退款，但是赠品订单被取消问题修复
                if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)) {
                    //如果售后状态为等待卖家同意，赠品订单也需要hold单，不能取消
                    if (CollectionUtils.isNotEmpty(orderInfo.getIsGiftOrderRelation())) {
                        for (OmsOrderRelation orderRelation : orderInfo.getIsGiftOrderRelation()) {
                            OcBOrder giftOrder = null;
                            try {
                                giftOrder = orderRelation.getOcBOrder();
                                List<OcBOrderItem> giftItems = orderRelation.getOcBOrderItems();
                                Integer giftOrderStatus = giftOrder.getOrderStatus();
                                if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(giftOrderStatus) || OmsOrderStatus.UNCONFIRMED.toInteger().equals(giftOrderStatus) || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(giftOrderStatus)) {
                                    // HOLD单
                                    OcBOrder order = new OcBOrder();
                                    //是否退款中
                                    order.setIsInreturning(1);
                                    order.setId(giftOrder.getId());
                                    omsOrderService.updateOrderInfo(order);
                                    //是否已经拦截 Hold单统一调用 HOLD单方法
                                    order.setIsInterecept(1);
                                    order.setBillNo(giftOrder.getBillNo());
                                    ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
                                    //买家已经申请退款，等待卖家同意
                                    omsBeforeShipmentReturnService.taoBaoRefundStatusAgree(orderRelation, SystemUserResource.getRootUser());
                                    //更新订单明细的退款状态
                                    for (OcBOrderItem item : giftItems) {
                                        ocBOrderItemMapper.updateOrderItemPtReturnStatusByOrderId(giftOrder.getId(), status, item.getId());
                                    }
                                }
                            } catch (Exception e) {
                                log.error(" Step060OrderStandPlatGoodsAfterRefundOnly gift businessHold error id:{}", giftOrder.getId(), e);
                            }
                        }
                    }
                } else {
                    //执行赠品后发
                    omsReturnOrderService.giftsThenSend(orderRelationList, orderInfo.getIsGiftOrderRelation(), orderInfo.getIntermediateTableRelation(), operateUser);
                }
            } else if (closed || refuseControl) {
                //赠品处理
                if (closed && CollectionUtils.isNotEmpty(orderInfo.getIsGiftOrderRelation())) {
                    for (OmsOrderRelation orderRelation : orderInfo.getIsGiftOrderRelation()) {
                        OcBOrder giftOrder = orderRelation.getOcBOrder();
                        if (giftOrder.getIsInreturning() == 1 && giftOrder.getIsInterecept() == 1) {
                            OcBOrder order = new OcBOrder();
                            //是否退款中
                            order.setIsInreturning(0);
                            order.setId(giftOrder.getId());
                            omsOrderService.updateOrderInfo(order);
                            //是否已经拦截 Hold单统一调用 HOLD单方法
                            order.setIsInterecept(0);
                            order.setBillNo(giftOrder.getBillNo());
                            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);

                            List<OcBOrderItem> ocBOrderItemAll = orderRelation.getOcBOrderItemAll();
                            // 未拆分的组合商品信息
                            List<Long> updateItemIds = ocBOrderItemAll.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(updateItemIds)){
                                ocBOrderItemMapper.updateOcBOrderItemById(giftOrder.getId(), updateItemIds, OcOrderRefundStatusEnum.NOTREFUND.getVal());
                            }
                            //更新订单明细的退款状态
                            for (OcBOrderItem item : ocBOrderItemAll) {
                                ocBOrderItemMapper.updateOrderItemPtReturnStatusByOrderId(giftOrder.getId(), TaobaoReturnOrderExt.RefundStandPlatStatus.CLOSED.getName(), item.getId());
                            }
                        }
                    }
                }

                //关闭退换货单及退款单(根据退款单号查询)
                List<Long> existReturnOrder =
                        omsStandPlatRefundOrderService.interceptOrderIsExist(ipBStandplatRefund, orderInfo.getIpBStandplatRefundItem());
                if (CollectionUtils.isNotEmpty(existReturnOrder)) {
                    omsStandPlatRefundOrderService.refundOrderClose(existReturnOrder, omsOrderRelation, ipBStandplatRefund, operateUser);
                } else {
                    //关闭发货后退款单
                    omsRefundOrderService.closedRefundSlip(ipBStandplatRefund.getReturnNo());
                }
                String remark = "退款关闭及卖家拒绝退款转换完成";
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, remark);
            } else {
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            if (CollectionUtils.isNotEmpty(refundIds)) {
                omsStandPlatRefundOrderService.foundRefundSlipAfter(refundIds, ocBOrder, ipBStandplatRefund, operateUser, null);
                //发起拦截
                //omsRefundOrderService.sendIntercept(refundIds, operateUser);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("退单转换异常:{}", "退单转换异常", ipBStandplatRefund.getReturnNo()), e);
            //修改中间表状态及系统备注
            ipStandplatRefundService.updateRefundIsTransError(ipBStandplatRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
