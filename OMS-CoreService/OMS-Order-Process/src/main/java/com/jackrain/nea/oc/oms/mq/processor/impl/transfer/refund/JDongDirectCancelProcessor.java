package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.JDDirectCancelProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Desc : 京东厂直取消单
 * <AUTHOR> xiWen
 * @Date : 2022/3/28
 */
@Slf4j
public class JDongDirectCancelProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private JDDirectCancelProcessImpl jDDirectCancelProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String no = orderMqInfo.getOrderNo();
        if (StringUtils.isBlank(no)) {
            log.error("JDDirectCancelTransfer.error,orderNo is blank {}", JSON.toJSONString(orderMqInfo));
        }
        if (log.isDebugEnabled()) {
            log.debug("JDDirectCancelTransfer.Start.ShardKey.{}; Param={}", no, JSON.toJSONString(orderMqInfo));
        }
        OmsJDDirectCancelRelation model = OmsJDDirectCancelRelation.buildRelation(orderMqInfo.getOrderId(), no);
        ProcessStepResultList results = jDDirectCancelProcess.start(model, false, SystemUserResource.getRootUser());
        if (log.isDebugEnabled()) {
            log.debug("JDDirectCancelTransfer.Finished.ShardKey.{}; Result={}", no, JSON.toJSONString(results));
        }
        return results;
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.JINGDONG_DIRECT
                && orderMqInfo.getOrderType() == OrderType.REFUND;
    }
}
