package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OcBOrderTheAuditService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * description：禁发撤回处理 is_forbidden_delivery
 *
 * <AUTHOR>
 * @date 2021/10/14
 */
@Step(order = 85, description = "判断是否禁发,禁发要进行撤回订单")
@Component
@Slf4j
public class Step85JitxOrderFrobiddenDelivery extends BaseJitxOrderProcessStep implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxOrderRelation> stepResult = new ProcessStepResult<>();

        if (orderInfo == null || orderInfo.getJitxOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo is null or orderInfo.JitxOrder is null");
        }
        String errorMessage = "";
        // 未转换
        TransferOrderStatus transferOrderStatus = TransferOrderStatus.NOT_TRANSFER;
        try {
            //上一步改地址有对订单进行处理  这一步需重新查询最新数据
            List<OcBOrder> transferOrderList = orderService.selectOmsOrderList(orderInfo.getOrderNo());
            for (OcBOrder transferOrder : transferOrderList) {
                if (transferOrder == null) {
                    continue;
                }
                if (log.isDebugEnabled()) {
                    log.debug("Step85JitxOrderFrobiddenDelivery[判断是否禁发,禁发要从wms撤回]{}", orderInfo.getJitxOrder().getOrderSn());
                }
                if (YesNoEnum.Y.getVal().equals(orderInfo.getJitxOrder().getIsForbiddenDelivery())) {
                    if (log.isDebugEnabled()) {
                        log.debug("Step85JitxOrderFrobiddenDelivery,当前是禁发{}", orderInfo.getJitxOrder().getOrderSn());
                    }
                    // 订单状态为待分配或传WMS中时，JITX订单不允许转换
                    if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(transferOrder.getOrderStatus())
                            || OmsOrderStatus.PENDING_WMS.toInteger().equals(transferOrder.getOrderStatus())) {
                        if (log.isDebugEnabled()) {
                            log.debug("零售发货单状态为：待分配或待传WMS，不操作！, jitxOrder:{}", orderInfo.getJitxOrder());
                        }
                        errorMessage = "零售发货单状态为：待分配或待传WMS，不操作！";
                    }
                    // 订单状态为"待审核"，"缺货"，"已审核"，"配货中并且WMS撤回状态已撤回"时，执行hold策略
                    else if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(transferOrder.getOrderStatus())
                            || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(transferOrder.getOrderStatus())) {
                        ValueHolder vh = execOrderHold(transferOrder);
                        if (log.isDebugEnabled()) {
                            log.debug("Step85JitxOrderFrobiddenDelivery.holdAndTheAudit正常状态:{},{}",
                                    orderInfo.getJitxOrder().getOrderSn(), JSONObject.toJSONString(vh));
                        }
                        if (!vh.isOK()) {
                            transferOrderStatus = TransferOrderStatus.TRANSFEREXCEPTION;
                            errorMessage = String.valueOf(vh.get("message"));
                        }
                    }// 订单状态为"配货中并且WMS撤回状态未撤回"，执行wms撤回成功后，在执行hold策略
                    else if (OmsOrderStatus.CHECKED.toInteger().equals(transferOrder.getOrderStatus()) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(transferOrder.getOrderStatus())) {
                        ValueHolder vh = execOrderHold(transferOrder);
                        if (log.isDebugEnabled()) {
                            log.debug("Step85JitxOrderFrobiddenDelivery.execOrderHold：{}/{}",
                                    orderInfo.getJitxOrder().getOrderSn(), JSON.toJSONString(vh));
                        }
                        if (!vh.isOK()) {
                            Object message = vh.get("message");
                            orderInfo.setRemarks("JITX禁发hold单失败：" + SplitMessageUtil.splitErrMsgBySize(message.toString(), SplitMessageUtil.SIZE_100));
                        }
                        try {
                            ValueHolderV14 v14 = new ValueHolderV14();
                            //订单反审核;
                            ocBOrderTheAuditService.updateOrderInfo(SystemUserResource.getRootUser(), v14, transferOrder.getId(),
                                    false, LogTypeEnum.JITX_FORBIDDEN_DELIVERY.getType(),true);
                            OcBOrder selectOrderInfo = orderService.selectOrderInfo(transferOrder.getId());
                            if (selectOrderInfo != null
                                    && selectOrderInfo.getWmsCancelStatus() != OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()) {
                                orderInfo.setRemarks("JITX订单禁发从WMS撤回发货单失败");
                            }
                        } catch (Exception e) {
                            log.error("JITX禁发，WMS撤回异常", Throwables.getStackTraceAsString(e));
                            orderInfo.setRemarks("JITX禁发WMS撤回发生异常");
                        }
                    }
                } else {
                    log.info("Step85JitxOrderFrobiddenDelivery JITX订单禁发,零售发货单:{},状态:{},WMS撤回状态:{}",
                            transferOrder.getSourceCode(), transferOrder.getOrderStatus(), transferOrder.getWmsCancelStatus());
                    String remark = String.format("转换完成，退出装换，零售发货单的状态:%d,WMS撤回状态:%d",
                            transferOrder.getOrderStatus(), transferOrder.getWmsCancelStatus());
                    orderInfo.setRemarks(remark);
                }
            }
        } catch (Exception ex) {
            log.error("Step85JitxOrderFrobiddenDelivery PreStep：{}", preStepResult.getCurrentStep().getClass());
            log.error("Step85JitxOrderFrobiddenDelivery StartProcess Error:{}", Throwables.getStackTraceAsString(ex));
        }

        if (StringUtils.isNotBlank(errorMessage)) {
            boolean updateStatusRes = ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(),
                    transferOrderStatus, errorMessage);
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        } else {
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step90JitxUpdateOrderTransferStatus.class);
        }
        return stepResult;
    }

    /**
     * JITX订单hold策略执行
     *
     * @param transferOrder
     * @return
     */
    private ValueHolder execOrderHold(OcBOrder transferOrder) {
        //调用Hold单策略
        return ocBOrderHoldService.businessHold(transferOrder.getId(), OrderHoldReasonEnum.JITX_FORBIDDEN_DELIVERY);
    }
}
