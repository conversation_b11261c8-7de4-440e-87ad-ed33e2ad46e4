package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoExchangeOrderExt;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 9:59 下午
 * @Version 1.0
 */
@Step(order = 70, description = "换货变退货")
@Slf4j
@Component
public class Step070ExchangeToReturnOrClose extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {
    @Value("${exchange.close.to.return.enable:true}")
    private boolean exchangeCloseToReturnEnable;
    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        String status = ipBTaobaoExchange.getStatus();
        try {
            OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();
            //调用标记取消 取消换货订单
            String message = "";
            boolean flag = omsTaobaoExchangeService.cancelExchangeOrder(orderInfo, operateUser);
            if (!flag) {
                message = "换货订单存在仓库或者平台发货,无法取消,转换失败";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
            if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_CLOSE.getName().equals(status)) {
                //取消退换货单
                if (TaobaoReturnOrderExt.BillType.EXCHANGE.getCode().equals(ocBReturnOrder.getBillType())) {
                    List<Long> returnOrderIds = new ArrayList<>();
                    returnOrderIds.add(ocBReturnOrder.getId());
                    ValueHolderV14 v14 = omsRefundOrderService.refundOrderClose(returnOrderIds, null, null, operateUser);
                    message = "换货关闭,转换成功!";
                    if (exchangeCloseToReturnEnable) {
                        if (!v14.isOK()) {
                            omsTaobaoExchangeService.exchangeToReturn(ocBReturnOrder, operateUser);
                            message = "退换货单无法取消,换货转退货,转换成功!";
                        }
                    }
                } else {
                    omsTaobaoExchangeService.exchangeToReturn(ocBReturnOrder, operateUser);
                    message = "退换货单无法取消,换货转退货,转换成功!";
                }
            }
            if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.REFUND_PLEASE.getName().equals(status)) {
                //删除换货明细  更改退换货单未退货单
                omsTaobaoExchangeService.exchangeToReturn(ocBReturnOrder, operateUser);
                message = "换货转退货,转换成功!";
            }
            ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message, ipBTaobaoExchange);
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(ipBTaobaoExchange, "处理跳转失败"+e.getMessage());
            log.error(LogUtil.format("换货变退货或换货关闭:{}", "换货变退货或换货关闭"), Throwables.getStackTraceAsString(e));
            String errorMessage = " 换货变退货或换货关闭!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
