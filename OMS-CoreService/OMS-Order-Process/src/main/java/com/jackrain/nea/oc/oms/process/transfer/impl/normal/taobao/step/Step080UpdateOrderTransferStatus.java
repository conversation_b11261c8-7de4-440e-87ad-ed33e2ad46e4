package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 释放当前行，更新转换状态为2
 *
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 22:33
 */
@Step(order = 80, description = "释放当前行，更新转换状态为2")
@Component
@Slf4j
public class Step080UpdateOrderTransferStatus extends BaseTaobaoOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        this.ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo,
                TransferOrderStatus.TRANSFERRED, "转单成功");
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
