package com.jackrain.nea.oc.oms.process.wms.providewms.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;

/**
 * 调用订单库存释放和扣减服务
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:21
 */
@Step(order = 30, description = "调用订单库存释放和扣减服务")
public class Step030CallStorageService implements IOmsOrderProcessStep<OcBOrderRelation> {
    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
