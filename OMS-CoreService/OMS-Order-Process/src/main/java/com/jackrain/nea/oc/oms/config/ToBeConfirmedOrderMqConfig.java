package com.jackrain.nea.oc.oms.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 定时任务占单tag
 *
 * @author: heliu
 * @since: 2019/7/29
 * create at : 2019/7/29 9:59
 */
@Configuration
@Data
public class ToBeConfirmedOrderMqConfig {

    @Value("${r3.oc.oms.tobeConfirmed.mq.topic:}")
    private String sendToBeConfirmMqTopic;

    @Value("${r3.oc.oms.tobeConfirmed.mq.tag:}")
    private String sendToBeConfirmTag;
}