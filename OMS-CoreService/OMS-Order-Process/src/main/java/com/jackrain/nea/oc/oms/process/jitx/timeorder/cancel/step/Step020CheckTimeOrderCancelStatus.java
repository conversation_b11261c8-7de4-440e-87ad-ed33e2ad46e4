package com.jackrain.nea.oc.oms.process.jitx.timeorder.cancel.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 判断时效订单取消状态
 *
 * @author: chenxiulou
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 20, description = "判断时效订单取消状态")
@Slf4j
@Component
public class Step020CheckTimeOrderCancelStatus extends BaseVipTimeOrderCancelProcessStep
        implements IOmsOrderProcessStep<IpBCancelTimeOrderVipRelation> {

    @Override
    public ProcessStepResult<IpBCancelTimeOrderVipRelation> startProcess(IpBCancelTimeOrderVipRelation timeOrderInfo,
                                                                         ProcessStepResult preStepResult,
                                                                         boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpBCancelTimeOrderVipRelation> stepResult = new ProcessStepResult<>();
        Integer orderStatus = timeOrderInfo.getCancelTimeOrderVip().getStatus();
        boolean isCanceledStatus = TimeOrderVipStatusEnum.CANCELLED.getValue().equals(orderStatus);
        String orderNo = timeOrderInfo.getOrderNo();
        if (!isCanceledStatus) {
            String operateMessage = "取消时效订单状态非已取消，标记为已转换！";
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step50UpdateTimeOrderTransferStatus.class);
        } else {
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setMessage("订单状态=" + orderStatus + "" + ";新建。单据编号=" + orderNo + "，进入下一阶段");
        }

        return stepResult;


    }
}
