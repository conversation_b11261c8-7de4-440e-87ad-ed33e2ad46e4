package com.jackrain.nea.oc.oms.process.transfer.impl.lock.lockorder.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.IpOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Descroption 判断锁单是否为待锁单状态
 * <AUTHOR>
 * @Date 2019/10/9 14:14
 */
@Step(order = 5, description = "判断锁单是否为待锁单状态")
@Slf4j
@Component
public class Step005CheckOrderLockStatus extends BaseLockProcessStep
        implements IOmsOrderProcessStep<IpOrderLockRelation> {
    @Override
    public ProcessStepResult<IpOrderLockRelation> startProcess(IpOrderLockRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {

        IpBOrderLock orderLock = orderInfo.getOrderLock();
        try {

            if (orderInfo == null || orderLock == null) {
                return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者Order.getOrderLock为空；退出转换");

            }
            String currentStatus = orderLock.getBillStatus();
            //待锁单、部分锁单
            if (!IpOrderLockStatusEnum.WAIT_LOCK.getKey().equals(currentStatus) && !IpOrderLockStatusEnum.PART_LOCKED.getKey().equals(currentStatus)) {
                String operateMessage = Resources.getMessage("锁单单据" + orderInfo.getOrderId() + "状态=" + IpOrderLockStatusEnum.enumToStringBykey(currentStatus) + "，转换完成");
                return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
            } else {
                String operateMessage = Resources.getMessage("锁单单据" + orderInfo.getOrderId() + "检查状态成功，进入下一阶段");
                return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("锁单异常:{}", "锁单异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipOrderLockService.updateLockErrorLog(orderLock);
            return new ProcessStepResult<>(StepStatus.FAILED, "锁单异常：" + e.getMessage());
        }
    }
}
