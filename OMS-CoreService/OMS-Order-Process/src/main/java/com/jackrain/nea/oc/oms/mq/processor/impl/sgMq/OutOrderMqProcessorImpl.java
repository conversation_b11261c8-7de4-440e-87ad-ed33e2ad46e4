package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.error.MqException;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultItemMqResult;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultMilkCardItemMqResult;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultMqResult;
import com.burgeon.r3.sg.store.model.result.out.SgOutResultSendMsgResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCShopItem;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.eventListener.SmsSendEvent;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.constant.NaiKaTypeConstant;
import com.jackrain.nea.oc.oms.mapper.CommonIdempotentMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderAddServiceReportMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaUnfreezeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaikaVoidMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBJitxDealerOrderTaskMapper;
import com.jackrain.nea.oc.oms.mapperservice.OcBOrderNaiKaMapperService;
import com.jackrain.nea.oc.oms.model.SmsSendStrategyInfo;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.CommonIdempotentTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.NaikaVoidStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.UnFreezeEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.CommonIdempotent;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAddServiceReport;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaUnfreeze;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
import com.jackrain.nea.oc.oms.model.table.task.OcBJitxDealerOrderTask;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordAddTaskService;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.OcBOrderLinkService;
import com.jackrain.nea.oc.oms.services.OcBShopSkuBatchInfoService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderOutService;
import com.jackrain.nea.oc.oms.services.OmsOrderPlatformLogisticsResendService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryProcessor;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.WmsUserCreateUtil;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.retail.service.RetailNotifyService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.StAddedServiceStrategyQueryResult;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单"仓库发货"MQ消息处理器
 *
 * @author: 胡林洋
 * @since: 2019-05-08
 * create at : 2019-05-08 18:08
 */
// fixme tag:sg_to_oms_out_result_verify_postback
@Component
@Slf4j
public class OutOrderMqProcessorImpl {

    @Autowired
    private OmsOrderOutService omsOrderOutService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private WmsUserCreateUtil wmsUserCreateUtil;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;

    @Autowired
    private OcBOrderLinkService ocBOrderLinkService;

    @Autowired
    private RetailNotifyService retailNotifyService;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private OrderDeliveryProcessor orderDeliveryProcessor;

    @Autowired
    private OmsOrderPlatformLogisticsResendService logisticsResendService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private OcBJitxDealerOrderTaskMapper ocBJitxDealerOrderTaskMapper;
    @Autowired
    private OcBOrderNaiKaUnfreezeMapper unfreezeMapper;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBOrderNaiKaMapperService ocBOrderNaiKaMapperService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBSapSalesDataRecordAddTaskService sapSalesDataRecordAddTaskService;
    @Autowired
    private CommonIdempotentMapper commonIdempotentMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBOrderAddServiceReportMapper addServiceReportMapper;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private DefaultProducerSend defaultProducerSend;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;
    @Autowired
    private OcBOrderNaikaVoidMapper ocBOrderNaikaVoidMapper;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private OcBShopSkuBatchInfoService ocBShopSkuBatchInfoService;
    @Autowired
    private OcBOrderNaiKaMapper naiKaMapper;
    @Autowired
    private OcBOrderNaiKaUnfreezeMapper ocBOrderNaiKaUnfreezeMapper;

    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        //先查看否是物流二次回传
        ValueHolderV14 check = checkAndSaveLogisticsInfo(messageTopic, messageKey, messageBody, messageTag);
        if (check.getCode() == ResultCode.SUCCESS) {
            //修改物流信息后直接返回
            return;
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OutOrderMqProcessorImpl.startProcess方法开始.MsgBody: {}", messageKey), messageBody);
        }
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        boolean outOrderMqProcessorTestSwitch = config.getPropertyBoolean("OutOrderMqProcessorTestSwitch");
        List<OcBOrder> orders = new ArrayList<>();
        if (outOrderMqProcessorTestSwitch) {
            // 执行压测逻辑
            getMqProcessResult(messageKey, messageBody);
        } else {
            try {
                User user = wmsUserCreateUtil.initWmsUser();
                /**
                 * 非压测逻辑，正常订单出库流程
                 */
                List<SgOutResultSendMsgResult> mqOutOrderList = JSON.parseArray(messageBody, SgOutResultSendMsgResult.class);
                if (CollectionUtils.isEmpty(mqOutOrderList)) {
                    log.info("outOrderMqInfo解析为null");
                    return;
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("Start Process Consumed Number={}"), mqOutOrderList.size());
                }
                List<Long> idsList = new ArrayList<>();
                List<SgOutResultSendMsgResult> JITXResultSendMsgList = new ArrayList<>();
                for (SgOutResultSendMsgResult sgOutResultSendMsgResult : mqOutOrderList) {
                    //物流二次回传不再进行处理
                    Boolean isLogisticsBack = sgOutResultSendMsgResult.getIsLogisticsBack();
                    if (isLogisticsBack != null && isLogisticsBack) {
                        continue;
                    }
                    //SgPhyOutDeliverySaveRequest
                    List<SgOutResultItemMqResult> deliveryItem = sgOutResultSendMsgResult.getMqResultItem();
                    SgOutResultMqResult sgOutResultMqResult = sgOutResultSendMsgResult.getMqResult();
                    Long orderId = sgOutResultMqResult.getSourceBillId();
                    idsList.add(orderId);
                    if (CollectionUtils.isEmpty(deliveryItem)) {
                        omsOrderLogService.addUserOrderLog(orderId,
                                sgOutResultMqResult.getBillNo(), OrderLogTypeEnum.STOCK_SEND.getKey(), orderId + "接收到回传mq,但发货信息deliveryItem为空！", null, "", user);
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("接收到回传mq,但发货信息deliveryItem为空", messageKey, orderId));
                        }
                        return;
                    }
                    OcBOrder ocBOrder = omsOrderService.selectOrderInfo(orderId);
                    if (ocBOrder != null) {
                        if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())
                                || OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())) {
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("订单状态为“仓库发货或平台发货”，mq消费结束", messageKey, orderId));
                            }
                            return;
                        }

                        OrderBusinessTypeCodeEnum businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(ocBOrder.getBusinessTypeCode());
                        if (ObjectUtil.isNotNull(businessTypeCodeEnum) && ObjectUtil.equals("entity", businessTypeCodeEnum.getNaiKaType())) {
                            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                            {
                                try {
                                    if (redisLock.tryLock(BllRedisLockOrderUtil.LOCK_ORDER_ONE_SECOND, TimeUnit.MILLISECONDS)) {
                                        applicationContext.getBean(OutOrderMqProcessorImpl.class).extracted(sgOutResultSendMsgResult, ocBOrder);
                                    }
                                } catch (Exception e) {
                                    log.error(this.getClass().getName() + " 订单发货MQ处理异常", e);
                                    throw new NDSException(e.getMessage());
                                } finally {
                                    redisLock.unlock();
                                }
                            }
                        }

                        String expressCode = sgOutResultMqResult.getLogisticNumber();
                        Long logisticsId = sgOutResultMqResult.getCpCLogisticsId();
                        String logisticsEcode = sgOutResultMqResult.getCpCLogisticsEcode();
                        String logisticsEname = sgOutResultMqResult.getCpCLogisticsEname();
                        //出库通知单号,
                        //String outBillNo = sgOutResultMqResult.getOutBillNo();
                        Date outDate = sgOutResultMqResult.getOutTime();
                        List<String> expressCodeList = new ArrayList<>();
                        List<OcBOrderDelivery> ocBOrderDeliveryList = new ArrayList<>();

                        //TOB部分发货
                        if (OmsBusinessTypeUtil.isToBOrder(ocBOrder)) {
                            try {
                                this.tobPartOut(ocBOrder, deliveryItem);
                                // 增值服务
                                orderAddService(ocBOrder, sgOutResultMqResult);
                            } catch (Exception e) {
                                log.warn(LogUtil.format("tob部分发货失败 orderId:{}", "tobPartOut"), ocBOrder.getId(), e);
                                DingTalkUtil.dingTobOutPart(ocBOrder.getId(), "部分出库处理失败:" + Throwables.getStackTraceAsString(e));
                            }
                        } else {
                            try {
                                //TOC/TOB残次订单回写实发数量
                                orderItemMapper.updateRealNumSourceQtyByOrderIds(Lists.newArrayList(ocBOrder.getId()));
                            } catch (Exception e) {
                                log.warn(LogUtil.format("TOCTOB残次订单回写实发数量失败 orderId:{}", "tobPartOut"), ocBOrder.getId(), e);
                            }
                        }

                        for (SgOutResultItemMqResult sgPhyOutDeliverySaveRequest : deliveryItem) {
                            OcBOrderDelivery ocBOrderDelivery = getOcBOrderDelivery(orderId, sgPhyOutDeliverySaveRequest);
                            //调用插入发货信息表的公共方法
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("调用插入发货信息表的公共方法.开始", messageKey, orderId));
                            }
                            if (ocBOrderDeliveryList.size() > 0) {
                                if (!ocBOrderDeliveryList.contains(ocBOrderDelivery)) {
                                    ocBOrderDeliveryList.add(ocBOrderDelivery);
                                }
                            } else {
                                ocBOrderDeliveryList.add(ocBOrderDelivery);
                            }
                        }
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("batchInsert方法执行前,ocBOrderDeliveryList的值为：{}", messageKey, orderId), ocBOrderDeliveryList);
                        }
                        //插入发货明细前，先删除残留发货明细数据
                        ocBOrderDeliveryMapper.delete(new QueryWrapper<OcBOrderDelivery>().lambda()
                                .eq(OcBOrderDelivery::getOcBOrderId, ocBOrder.getId()));
                        ocBOrderDeliveryMapper.batchInsert(ocBOrderDeliveryList);
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("更新物流单号，物流公司id，物流公司名称，物流公司ecode到订单信息表.开始"));
                        }
                        OcBOrder ocBOrderTmp = new OcBOrder();
                        ocBOrderTmp.setId(orderId);
                        ocBOrderTmp.setScanTime(outDate);
                        ocBOrderTmp.setExpresscode(expressCode);
                        ocBOrderTmp.setCpCLogisticsId(logisticsId);
                        ocBOrderTmp.setCpCLogisticsEcode(logisticsEcode);
                        ocBOrderTmp.setCpCLogisticsEname(logisticsEname);
//                        ocBOrderTmp.setOutWmsOutTime(sgOutResultMqResult.getOutWmsOutTime());
//                        ocBOrderTmp.setOutWingToSgTime(sgOutResultMqResult.getOutWingToSgTime());
                        // @20201123 给原单赋值出库通知单编号
                        /// ocBOrderTmp.setSgBOutBillNo(outBillNo);
                        Set<String> collect = ocBOrderDeliveryList.stream().map(OcBOrderDelivery::getLogisticNumber).collect(Collectors.toSet());
                        if (collect.size() > 1) {
                            ocBOrderTmp.setIsMultiPack(1L);
                        } else {
                            ocBOrderTmp.setIsMultiPack(0L);
                        }
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("updateOrderInfo,更新前,ocBOrderTmp的值为：{}"), ocBOrderTmp);
                        }
                        omsOrderService.updateOrderInfo(ocBOrderTmp);
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("updateOrderInfo,更新后"));
                        }

                        // 记录配置店铺的SKU和生产日期信息
                        recordSkuProductionDateForConfiguredShops(ocBOrder, deliveryItem);
                        //异步执行库存接口调用:JITX的单子执行
                        if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                            JITXResultSendMsgList.add(sgOutResultSendMsgResult);
                        }
                        orders.add(ocBOrder);
                        /**
                         * 220111 经销商发货
                         */
                        try {
                            if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                                OcBJitxDealerOrderTaskService service = ApplicationContextHandle.getBean(OcBJitxDealerOrderTaskService.class);
                                Long warehouseId = ocBOrder.getCpCPhyWarehouseId();
                                if (service.isJitxDealerYYWarehouse(warehouseId)) {
                                    //发货仓库为YY经销商仓库，零售发货库发货后,添加经销商任务记录
                                    OcBJitxDealerOrderTask orderTask = new OcBJitxDealerOrderTask();
                                    orderTask.setId(ModelUtil.getSequence(SystemTableNames.OC_B_JITX_DEALER_ORDER_TASK));
                                    orderTask.setCpCShopId(ocBOrder.getCpCShopId());
                                    orderTask.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform()));
                                    orderTask.setTid(ocBOrder.getTid());
                                    orderTask.setBillNo(ocBOrder.getBillNo());
                                    orderTask.setType(JitxDealerTaskTypeEnum.YY_BILLS_UPDATE.getCode());
                                    orderTask.setState(JitxDealerTaskStatusEnum.NOT.getCode());
                                    BaseModelUtil.makeBaseCreateField(orderTask, user);
                                    ocBJitxDealerOrderTaskMapper.insert(orderTask);
                                }
                            }
                        } catch (Exception e) {
                            log.error("OutOrderMqProcessorImpl，添加至经销商任务表失败 订单id为：" + orderId);
                        }
                    } else {
                        log.error(LogUtil.format("接收到Mq成功消费，但未在数据库中查询到此订单,订单id为={}"), orderId);
                        continue;
                    }
                }
                ValueHolderV14 vh = omsOrderOutService.batchOrderOutOfStock(idsList, mqOutOrderList);
                if (vh.getCode() == 0) {
                    for (Long orderId : idsList) {
                        OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(orderId);
                        OcBOrder orderInfo = orderRelation.getOrderInfo();
                        if (orderInfo != null) {
                            Integer platform = orderInfo.getPlatform();
                            if (PlatFormEnum.POS.getCode().equals(platform)) {
                                ValueHolder valueHolder = retailNotifyService.notifyRetailByMq(orderRelation, 2);
                                if (log.isDebugEnabled()) {
                                    log.debug(LogUtil.format("订单id为：{}，仓库发货通知线下pos结果：{}", orderId), orderId, valueHolder);
                                }
                            }
                            // 出库生成销售数据信息
                            sapSalesDataRecordAddTaskService.addTask(0, orderRelation.getOrderId(), user);
                        } else {
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("orderRelation.OrderInfo=null，订单id：{}", orderId), orderId);
                            }
                        }

                    }
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("sendNote.执行监听发送信息方法"));
                    }
                    //发送短信
                    sendNote(orders);
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("接收到Mq成功消费，并调用订单出库服务成功"));
                    }
                    return;
                } else if (vh.getCode() == -1) {
                    throw new MqException("订单出库服务调用失败");
                }
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("接收到Mq成功消费，并调用订单出库服务成功"));
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error(LogUtil.format("OutOrderMqProcessorImpl.StartProcess Error: {}"), Throwables.getStackTraceAsString(ex));
                throw new MqException(ex);
            }
        }
    }

    private void orderAddService(OcBOrder ocBOrder, SgOutResultMqResult sgOutResultMqResult) {
        // 往增值服务表中写数据
        // 根据零售发货单查询明细
        List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectOrderItemContainsCombination(ocBOrder.getId());
        ValueHolderV14<CpShop> cpShopValueHolderV14 = cpRpcService.queryByShopCode(ocBOrder.getCpCShopEcode());
        CpShop cpShop = cpShopValueHolderV14.getData();
        if (ObjectUtil.isNull(cpShop)) {
            log.error(LogUtil.format("店铺信息为空 orderId:{}", "orderAddService"), ocBOrder.getId());
            return;
        }

        List<OcBOrderAddServiceReport> ocBOrderAddServiceReportList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            // 如果实发数量是0的话 那么就不往增值服务费用报表插入数据
            if (ObjectUtil.isNull(ocBOrderItem.getRealOutNum()) ||
                    ObjectUtil.equal(BigDecimal.ZERO, ocBOrderItem.getRealOutNum())) {
                continue;
            }
            // 是否有增值服务
            if (ObjectUtil.isEmpty(ocBOrderItem.getLabelingRequirements())) {
                continue;
            }
            String labelingRequirements = ocBOrderItem.getLabelingRequirements();
            String[] labelingRequirementArr = labelingRequirements.split(";");
            //  去重增值服务
            Set<String> labelingRequirementSet = new HashSet<>();
            for (String labelingRequirement : labelingRequirementArr) {
                if (StringUtils.isEmpty(labelingRequirement)) {
                    continue;
                }
                labelingRequirementSet.add(labelingRequirement);
            }
            if (CollectionUtils.isNotEmpty(labelingRequirementSet)) {
                for (String labelingRequirement : labelingRequirementSet) {
                    OcBOrderAddServiceReport addServiceReport = new OcBOrderAddServiceReport();
                    addServiceReport.setId(sequenceUtil.buildAddServiceSequenceId());
                    addServiceReport.setOrderBillNo(ocBOrder.getBillNo());
                    addServiceReport.setOcBOrderId(ocBOrder.getId());
                    addServiceReport.setTid(ocBOrder.getTid());
                    addServiceReport.setIsMatch(0);
                    ValueHolderV14<StAddedServiceStrategyQueryResult> result = stRpcService.selectByTypeDocNameAndCpCStoreId(labelingRequirement, ocBOrder.getCpCPhyWarehouseId());
                    log.debug(LogUtil.format("开始根据增值服务与仓库信息获取增值服务策略:{},{},返回结果{}", "selectByTypeDocNameAndCpCStoreId"),
                            labelingRequirement, ocBOrder.getCpCPhyWarehouseId(), JSONUtil.toJsonStr(result));
                    if (result.isOK()) {
                        StAddedServiceStrategyQueryResult addedServiceStrategyQueryResult = result.getData();
                        if (ObjectUtil.isNotNull(addedServiceStrategyQueryResult)) {
                            addServiceReport.setIsMatch(1);
                            addServiceReport.setAddserviceStrategyUnitPrice(addedServiceStrategyQueryResult.getUnitPrice());
                            // 总价 用单价*数量
                            addServiceReport.setAddserviceStrategyPrice(addedServiceStrategyQueryResult.getUnitPrice().multiply(ocBOrderItem.getRealOutNum()));
                        } else {
                            addServiceReport.setRemark("未维护对应的增值服务报价");
                        }
                    } else {
                        addServiceReport.setRemark("未维护对应的增值服务报价");
                    }

                    addServiceReport.setStoOutBillNo(ocBOrder.getSgBOutBillNo());
                    addServiceReport.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
                    addServiceReport.setPsCProEname(ocBOrderItem.getPsCProEname());
                    addServiceReport.setPsCProEcode(ocBOrderItem.getPsCProEcode());
                    addServiceReport.setCpCShopId(ocBOrder.getCpCShopId());
                    addServiceReport.setCpCShopEcode(ocBOrder.getCpCShopEcode());
                    addServiceReport.setCpCShopTitle(ocBOrder.getCpCShopTitle());
                    addServiceReport.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
                    addServiceReport.setCpCPhyWarehouseEcode(ocBOrder.getCpCPhyWarehouseEcode());
                    addServiceReport.setCpCPhyWarehouseEname(ocBOrder.getCpCPhyWarehouseEname());
                    BaseModelUtil.initialBaseModelSystemField(addServiceReport);

                    // 成本中心维护
                    if (ObjectUtil.isNotNull(cpShop.getGeneralOrganizationCode())) {
                        CpCShopItem cpCShopItem = cpRpcService.queryShopItem(cpShop.getId(), cpShop.getGeneralOrganizationCode());
                        if (ObjectUtil.isNotNull(cpCShopItem)) {
                            addServiceReport.setCostCenterCode(cpCShopItem.getCostCenterDescription());
                            addServiceReport.setCostCenterId(cpCShopItem.getCostCenter());
                            // 成本中心名称
                            List<String[]> arrList = new ArrayList<>();
                            String[] costCenter = {"C_STOREATTRIB4_ID", cpCShopItem.getCostCenterDescription()};
                            arrList.add(costCenter);
                            Map<String, JSONObject> resultMap = cpRpcService.findStoredimItemIdByeCodeList(arrList);
                            if (resultMap != null) {
                                addServiceReport.setCostCenterName(resultMap.get("C_STOREATTRIB4_ID") != null ? resultMap.get("C_STOREATTRIB4_ID").getString("ename") : null);
                            }
                        }
                    }
                    addServiceReport.setRealOutNum(ocBOrderItem.getRealOutNum());
                    addServiceReport.setLabelNum(ocBOrderItem.getRealOutNum());
                    addServiceReport.setQty(ocBOrderItem.getQty());
                    addServiceReport.setIsArchived(0);
                    addServiceReport.setDeliveryTime(sgOutResultMqResult.getOutTime());
                    addServiceReport.setLabelingRequirements(labelingRequirement);
                    ocBOrderAddServiceReportList.add(addServiceReport);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(ocBOrderAddServiceReportList)) {
            addServiceReportMapper.batchInsert(ocBOrderAddServiceReportList);
        }
    }

    /**
     * TOB订单部分出库处理
     * 1.实发数量计算
     * 2.生成新订单任务
     *
     * @param ocBOrder
     * @param deliveryItem
     */
    private void tobPartOut(OcBOrder ocBOrder, List<SgOutResultItemMqResult> deliveryItem) {
        log.info(LogUtil.format("tob部分发货实发计算开始 orderId:{},deliveryItem:{}", "tobPartOut"), ocBOrder.getId(), JSON.toJSONString(deliveryItem));

        Long orderId = ocBOrder.getId();
        Date createDate = ocBOrder.getCreationdate();

        List<OcBOrderItem> itemList = orderItemMapper.selectOrderItemContainsCombination(orderId);
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }

        //防止查询出的实发数量有值导致计算有误
        itemList.forEach(p -> p.setRealOutNum(BigDecimal.ZERO));

        Map<String, List<OcBOrderItem>> skuItemsMap = itemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));

        Map<Long, OcBOrderItem> itemIdMap = itemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, x -> x, (a, b) -> a));

        //仓库出库的明细按照sku分组
        Map<String, List<SgOutResultItemMqResult>> outSkuItemsMap = deliveryItem.stream().collect(
                Collectors.groupingBy(SgOutResultItemMqResult::getPsCSkuEcode));

        for (Map.Entry<String, List<SgOutResultItemMqResult>> entry : outSkuItemsMap.entrySet()) {
            //未匹配到效期的sku对应数量，最后按先主后赠统一处理
            BigDecimal noMatchNum = BigDecimal.ZERO;

            String outSkuCode = entry.getKey();
            List<SgOutResultItemMqResult> outValues = entry.getValue();
            //相同批次分为一组
            Map<String, List<SgOutResultItemMqResult>> sameDateMap = outValues.stream().collect(
                    Collectors.groupingBy(SgOutResultItemMqResult::getProduceDate));
            for (Map.Entry<String, List<SgOutResultItemMqResult>> listEntry : sameDateMap.entrySet()) {
                //数量
                BigDecimal outItemQty = listEntry.getValue().stream().map(SgOutResultItemMqResult::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
                //效期
                String produceDate = listEntry.getKey();
                if (StringUtils.isBlank(produceDate) || "00000000".equals(produceDate)) {
                    //仓库出库sku没有批次，走最后的先主后赠
                    noMatchNum = noMatchNum.add(outItemQty);
                    continue;
                }

                List<OcBOrderItem> orderItems = skuItemsMap.get(outSkuCode);
                if (CollectionUtils.isEmpty(orderItems)) {
                    continue;
                }

                //排序，兼容'合'&非'合'
                orderItems.sort(Comparator.comparing(OcBOrderItem::getQty));

                if (orderItems.size() == 1) {
                    //只有一条明细，直接加，确认仓库不会多出库
                    OcBOrderItem ocBOrderItem = orderItems.get(0);
                    OcBOrderItem orderItem = itemIdMap.get(ocBOrderItem.getId());
                    orderItem.setRealOutNum(orderItem.getRealOutNum().add(outItemQty));
                } else {
                    //处理主品
                    outItemQty = dealRealNum(createDate, outItemQty, produceDate, orderItems.stream().filter(p -> p.getIsGift().equals(0)).collect(Collectors.toList()));
                    if (outItemQty.compareTo(BigDecimal.ZERO) == 0) {
                        continue;
                    }
                    //处理赠品
                    List<OcBOrderItem> giftItems = orderItems.stream().filter(p -> p.getIsGift().equals(1)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(giftItems)){
                        outItemQty = dealRealNum(createDate, outItemQty, produceDate, giftItems);
                    }
                    if (outItemQty.compareTo(BigDecimal.ZERO) > 0) {
                        //如果有剩余未分配出库数量，留到最后处理
                        noMatchNum = noMatchNum.add(outItemQty);
                    }
                }
            }
            //终极处理(先主后赠)
            if (noMatchNum.compareTo(BigDecimal.ZERO) > 0) {
                List<OcBOrderItem> orderItems = skuItemsMap.get(outSkuCode);
                //排序，兼容'合'&非'合'
                orderItems.sort(Comparator.comparing(OcBOrderItem::getQty));
                //处理主品
                List<OcBOrderItem> mainItems = orderItems.stream().filter(p -> p.getIsGift().equals(0)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(mainItems)) {
                    noMatchNum = lastDealReal(noMatchNum, mainItems);
                }
                //处理赠品
                List<OcBOrderItem> giftItems = orderItems.stream().filter(p -> p.getIsGift().equals(1)).collect(Collectors.toList());
                if (noMatchNum.compareTo(BigDecimal.ZERO) > 0 && CollectionUtils.isNotEmpty(giftItems)) {
                    noMatchNum = lastDealReal(noMatchNum, giftItems);
                }
            }

            if (noMatchNum.compareTo(BigDecimal.ZERO) > 0) {
                DingTalkUtil.dingTobOutPart(orderId, "部分出库计算完成，未分配数量大于0");
                throw new NDSException("部分出库计算完成，未分配数量大于0");
            }
        }

        log.info(LogUtil.format("tob部分发货实发计算完成 orderId:{},skuItemsMap:{}", "tobPartOut"), ocBOrder.getId(), JSON.toJSONString(skuItemsMap));

        endOutPart(ocBOrder, orderId, skuItemsMap);
    }

    private void endOutPart(OcBOrder ocBOrder, Long ocBOrderId, Map<String, List<OcBOrderItem>> skuItemsMap) {
        //确保实发数量不会超过数量，校验
        realNumCheck(ocBOrder, ocBOrderId, skuItemsMap);

        //明细实发数量等于数量
        List<Long> itemIds = Lists.newArrayList();
        //明细实发数量不同
        Map<Long, BigDecimal> diffMap = Maps.newHashMap();

        for (Map.Entry<String, List<OcBOrderItem>> entry : skuItemsMap.entrySet()) {
            List<OcBOrderItem> itemList = entry.getValue();
            for (OcBOrderItem ocBOrderItem : itemList) {
                if (ocBOrderItem.getRealOutNum().compareTo(ocBOrderItem.getQty()) == 0) {
                    itemIds.add(ocBOrderItem.getId());
                } else {
                    diffMap.put(ocBOrderItem.getId(), ocBOrderItem.getRealOutNum());
                }
            }
        }

        //更新实发数量&新单生成任务
        batchUpdateRealNum(ocBOrderId, itemIds, diffMap);
    }

    private void batchUpdateRealNum(Long ocBOrderId, List<Long> itemIds, Map<Long, BigDecimal> diffMap) {
        log.info(LogUtil.format("tob部分发货实发数量开始更新 ocBOrderId:{},itemIds:{},diffMap:{}", "tobPartOut"), ocBOrderId, itemIds, JSON.toJSONString(diffMap));

        //数量与实发数量一致，批量更新实发数量为数量
        if (CollectionUtils.isNotEmpty(itemIds)) {
            orderItemMapper.updateRealNumSourceQtyByItemIds(itemIds);
        }

        //数量与实发数量不一致，循环更新实发数量为仓库实际出库数量
        if (MapUtils.isNotEmpty(diffMap)) {
            for (Map.Entry<Long, BigDecimal> entry : diffMap.entrySet()) {
                orderItemMapper.updateRealNumByItemId(entry.getKey(), entry.getValue());
            }

            //新增未出库明细的订单幂等任务
            CommonIdempotent commonIdempotent = new CommonIdempotent();
            commonIdempotent.setId(sequenceUtil.buildCommonIdempotentSequenceId());
            commonIdempotent.setBusinessCode(String.valueOf(ocBOrderId));
            commonIdempotent.setBatchNo("0");
            commonIdempotent.setType(CommonIdempotentTypeEnum.TOB_PART_OUT.getKey());
            commonIdempotent.setAdClientId(SystemUserResource.AD_CLIENT_ID);
            commonIdempotent.setAdOrgId(SystemUserResource.AD_ORG_ID);
            commonIdempotent.setCreationdate(new Date());
            commonIdempotent.setModifieddate(new Date());
            commonIdempotent.setOwnerid(SystemUserResource.ROOT_USER_ID);
            commonIdempotent.setOwnername(SystemUserResource.ROOT_USER_NAME);
            commonIdempotent.setModifierid(SystemUserResource.ROOT_USER_ID);
            commonIdempotent.setModifiername(SystemUserResource.ROOT_USER_NAME);
            commonIdempotent.setIsactive("Y");
            try {
                commonIdempotentMapper.insert(commonIdempotent);
            } catch (Exception e) {
                if (e instanceof DuplicateKeyException) {
                    log.warn(LogUtil.format("tob部分发货被幂等 orderId:{}", "tobPartOut"), ocBOrderId);
                } else {
                    log.error(LogUtil.format("tob部分发货数据存储失败 orderId:{}", "tobPartOut"), ocBOrderId, e);
                    throw e;
                }
            }
        }
    }

    private void realNumCheck(OcBOrder ocBOrder, Long ocBOrderId, Map<String, List<OcBOrderItem>> skuItemsMap) {
        List<Long> errorItemIds = Lists.newArrayList();
        for (List<OcBOrderItem> value : skuItemsMap.values()) {
            for (OcBOrderItem ocBOrderItem : value) {
                if (ocBOrderItem.getRealOutNum().compareTo(ocBOrderItem.getQty()) > 0) {
                    errorItemIds.add(ocBOrderItem.getId());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorItemIds)) {
            log.error(LogUtil.format("部分发货计算实发数量有误 orderId:{},errorItemIds:{}", "tobPartOut"), ocBOrder.getId(), errorItemIds);
            DingTalkUtil.dingTobOutPart(ocBOrderId, "部分发货计算实发数量有误");
            throw new NDSException("部分发货计算实发数量有误");
        }
    }

    private BigDecimal lastDealReal(BigDecimal noMatchNum, List<OcBOrderItem> items) {
        for (OcBOrderItem item : items) {
            if (item.getRealOutNum().compareTo(item.getQty()) == 0) {
                //相同，全出，跳过
                continue;
            }
            if (noMatchNum.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            BigDecimal canNum = item.getQty().subtract(item.getRealOutNum());
            if (noMatchNum.compareTo(canNum) >= 0) {
                item.setRealOutNum(item.getRealOutNum().add(canNum));
                noMatchNum = noMatchNum.subtract(canNum);
            } else {
                item.setRealOutNum(item.getRealOutNum().add(noMatchNum));
                noMatchNum = noMatchNum.subtract(noMatchNum);
            }
        }
        return noMatchNum;
    }

    private BigDecimal dealRealNum(Date creationdate, BigDecimal outItemQty, String produceDate, List<OcBOrderItem> items) {
        for (OcBOrderItem giftItem : items) {
            if (outItemQty.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            Integer expiryDateType = giftItem.getExpiryDateType();
            String expiryDateRange = giftItem.getExpiryDateRange();
            //可发货数量
            BigDecimal canNum = giftItem.getQty().subtract(giftItem.getRealOutNum());
            if (canNum.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            if (Objects.isNull(expiryDateType) || expiryDateType <= 0 || StringUtils.isBlank(expiryDateRange)) {
                //明细未指定批次，暂不处理，最后先主后赠
                continue;
            }

            //订单明细的效期是否与仓库出库效期匹配
            boolean dateMatch = timeIfMatch(expiryDateType, expiryDateRange, creationdate, produceDate);
            if (dateMatch) {
                //匹配效期，处理实发数量
                if (outItemQty.compareTo(canNum) >= 0) {
                    giftItem.setRealOutNum(giftItem.getRealOutNum().add(canNum));
                    outItemQty = outItemQty.subtract(canNum);
                } else {
                    giftItem.setRealOutNum(giftItem.getRealOutNum().add(outItemQty));
                    outItemQty = BigDecimal.ZERO;
                }
            }
        }
        return outItemQty;
    }

    /**
     * 判断目标日期是否在效期范围内
     *
     * @param expiryDateType
     * @param expiryDateRange
     * @param creationDate
     * @return
     */
    private boolean timeIfMatch(Integer expiryDateType, String expiryDateRange, Date creationDate, String produceDate) {
        String[] split = expiryDateRange.split("-");
        String start = split[0];
        String end = split[1];

        if (expiryDateType == 1) {
            //范围
            try {
                Date startDate = DateUtils.parseDate(start, "yyyyMMdd");
                Date endDate = DateUtils.parseDate(end, "yyyyMMdd");
                Date targetDate = DateUtils.parseDate(produceDate, "yyyyMMdd");

                if (dealDate(startDate, endDate, targetDate)) {
                    return true;
                }
            } catch (ParseException e) {
                log.error(LogUtil.format("timeIfMatch error expiryDateType:{},expiryDateRange:{},creationDate:{}", "timeIfMatch"), expiryDateType, expiryDateRange, creationDate, e);
                return false;
            }
        }

        if (expiryDateType == 2) {
            //天数
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

            Calendar calendarEnd = Calendar.getInstance();
            calendarEnd.setTime(creationDate);

            Calendar calendarStart = Calendar.getInstance();
            calendarStart.setTime(creationDate);

            // 减去天数
            calendarEnd.add(Calendar.DAY_OF_YEAR, -Integer.parseInt(start));
            calendarStart.add(Calendar.DAY_OF_YEAR, -Integer.parseInt(end));

            SimpleDateFormat sdff = new SimpleDateFormat("yyyyMMdd");
            try {
                Date startDate = sdff.parse(sdf.format(calendarStart.getTime()));
                Date endDate = sdff.parse(sdf.format(calendarEnd.getTime()));
                Date targetDate = DateUtils.parseDate(produceDate, "yyyyMMdd");

                if (dealDate(startDate, endDate, targetDate)) {
                    return true;
                }
            } catch (ParseException e) {
                log.error(LogUtil.format("timeIfMatch error expiryDateType:{},expiryDateRange:{},creationDate:{}", "timeIfMatch"), expiryDateType, expiryDateRange, creationDate, e);
                return false;
            }
        }
        return false;
    }

    private boolean dealDate(Date startDate, Date endDate, Date produceDate) {
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(startDate);

        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);

        Calendar targetCalendar = Calendar.getInstance();
        targetCalendar.setTime(produceDate);

        return (targetCalendar.after(startCalendar) && targetCalendar.before(endCalendar)) || targetCalendar.equals(startCalendar) || targetCalendar.equals(endCalendar);
    }

    @Transactional(rollbackFor = Exception.class)
    public void extracted(SgOutResultSendMsgResult sgOutResultSendMsgResult, OcBOrder ocBOrder) {

        Long ocBOrderId = ocBOrder.getId();
        OcBOrderNaikaUnfreeze ocBOrderNaikaUnfreeze = new OcBOrderNaikaUnfreeze();
        BaseModelUtil.initialBaseModelSystemField(ocBOrderNaikaUnfreeze);
        ocBOrderNaikaUnfreeze.setOcBOrderId(ocBOrderId);
        ocBOrderNaikaUnfreeze.setId(sequenceUtil.buildOrderNaiKaUnfreezeSequenceId());
        ocBOrderNaikaUnfreeze.setTid(ocBOrder.getTid());
        ocBOrderNaikaUnfreeze.setUnfreezeStatus(UnFreezeEnum.UN_FREEZE.getStatus());
        ocBOrderNaikaUnfreeze.setUnfreezeTimes(0);
        unfreezeMapper.insert(ocBOrderNaikaUnfreeze);

        // 更新零售发货单 推送奶卡的状态
        OcBOrder updateOcBOrder = new OcBOrder();
        updateOcBOrder.setId(ocBOrder.getId());
        updateOcBOrder.setModifieddate(new Date());
        updateOcBOrder.setToNaikaStatus(OmsOrderNaiKaStatusEnum.FREEZE.getStatus());
        ocBOrderMapper.updateById(updateOcBOrder);

        List<SgOutResultMilkCardItemMqResult> mqResultMilkCardItems = sgOutResultSendMsgResult.getMqResultMilkCardItems();
        if (CollectionUtils.isNotEmpty(mqResultMilkCardItems)) {
            // 1 先对mqResultMilkCardItems 中的商品id+sku编码进行group by
            // 2 根据分组后的 零售发货单id+商品id+sku编码获取明细表中的明细数据 如果只能拿到一个明细 直接可以赋值这个明细 如果拿到两个明细 则需要维护一个list list里面根据具体明细的数量
            // 会不会出现

            Map<String, List<SgOutResultMilkCardItemMqResult>> milkCardItemResultMap =
                    mqResultMilkCardItems.stream().collect(Collectors.groupingBy(SgOutResultMilkCardItemMqResult::getPsCSkuEcode));
            Set<String> skuSet = milkCardItemResultMap.keySet();
            Map<String, List<Long>> orderItemIdMap = new HashMap<>();
            for (String skuCode : skuSet) {
                // 根据零售发货单id +skucode 查询明细 可能会存在多条  需要考虑是否要根据什么条件来过滤数据
                List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectUnSuccessRefundBySku(ocBOrderId, skuCode);
                List<Long> orderItemIds = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(ocBOrderItemList)) {
                    for (OcBOrderItem orderItem : ocBOrderItemList) {
                        Integer qty = orderItem.getQty().intValue();
                        for (int i = 0; i < qty; i++) {
                            orderItemIds.add(orderItem.getId());
                        }
                        orderItemIdMap.put(skuCode, orderItemIds);
                    }
                }
            }

            for (String skuCode : milkCardItemResultMap.keySet()) {
                List<SgOutResultMilkCardItemMqResult> milkCardItemMqResultList = milkCardItemResultMap.get(skuCode);
                List<Long> orderItemIds = orderItemIdMap.get(skuCode);
                for (int i = 0; i < milkCardItemMqResultList.size(); i++) {
                    SgOutResultMilkCardItemMqResult milkCardItemMqResult = milkCardItemMqResultList.get(i);
                    ProductSku productSku = psRpcService.selectProductSku(milkCardItemMqResult.getPsCSkuEcode());
                    List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaList(ocBOrderId, milkCardItemMqResult.getMilkCard());
                    if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
                        OcBOrderNaiKa ocBOrderNaiKa = new OcBOrderNaiKa();
                        ocBOrderNaiKa.setNaikaStatus(OmsOrderNaiKaStatusEnum.FREEZE.getStatus());
                        ocBOrderNaiKa.setOcBOrderId(ocBOrderId);
                        ocBOrderNaiKa.setBusinessTypeName(ocBOrder.getBusinessTypeName());
                        ocBOrderNaiKa.setBusinessTypeId(ocBOrder.getBusinessTypeId());
                        ocBOrderNaiKa.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
                        ocBOrderNaiKa.setBusinessType(NaiKaTypeConstant.ENTITY);
                        ocBOrderNaiKa.setCardCode(milkCardItemMqResult.getMilkCard().trim().toUpperCase());
                        ocBOrderNaiKa.setSkuSpec(productSku.getSkuSpec());
                        ocBOrderNaiKa.setPsCSkuEname(milkCardItemMqResult.getPsCProEname());
                        ocBOrderNaiKa.setPsCSkuEname(productSku.getSkuName());
                        ocBOrderNaiKa.setPsCSkuEcode(productSku.getSkuEcode());
                        ocBOrderNaiKa.setPsCSkuId(productSku.getId());
                        ocBOrderNaiKa.setPsCProEname(productSku.getName());
                        ocBOrderNaiKa.setPsCProEcode(productSku.getEcode());
                        ocBOrderNaiKa.setPsCProId(productSku.getProdId());
                        ocBOrderNaiKa.setTid(ocBOrder.getTid());
                        ocBOrderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                        ocBOrderNaiKa.setOcBOrderItemId(orderItemIds.get(i));
                        ocBOrderNaiKaMapper.insert(ocBOrderNaiKa);
                    }
                }
            }
        }

        // 检查通用订单中间表的平台状态，如果是"已取消&关闭"，则自动生成奶卡作废数据
        checkPlatformStatusAndCreateVoidData(ocBOrder);
    }

    /**
     * 检查通用订单中间表的平台状态，如果是"已取消&关闭"，则自动生成奶卡作废数据
     *
     * @param ocBOrder 订单信息
     */
    private void checkPlatformStatusAndCreateVoidData(OcBOrder ocBOrder) {
        try {
            // 根据平台单号查询通用订单中间表
            IpBStandplatOrder standplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(ocBOrder.getTid());
            if (standplatOrder == null) {
                log.debug("未找到通用订单中间表数据，订单ID={}，平台单号={}", ocBOrder.getId(), ocBOrder.getTid());
                return;
            }

            String platformStatus = standplatOrder.getStatus();
            log.info("订单出库后检查平台状态，订单ID={}，平台单号={}，平台状态={}",
                    ocBOrder.getId(), ocBOrder.getTid(), platformStatus);

            // 检查平台状态是否为"已取消&关闭"
            if (isPlatformOrderCancelledOrClosed(platformStatus)) {
                log.info("订单平台状态为已取消或关闭，开始生成奶卡作废数据，订单ID={}，平台单号={}，平台状态={}",
                        ocBOrder.getId(), ocBOrder.getTid(), platformStatus);

                // 查询该订单的奶卡信息
                List<OcBOrderNaiKa> naikaList = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(ocBOrder.getId());
                if (CollectionUtils.isNotEmpty(naikaList)) {
                    // 为每个奶卡生成作废数据
                    createNaikaVoidData(ocBOrder, naikaList);
                } else {
                    log.debug("订单无奶卡信息，无需生成作废数据，订单ID={}", ocBOrder.getId());
                }
            }
        } catch (Exception e) {
            log.error("检查平台状态并生成奶卡作废数据异常，订单ID={}，平台单号={}",
                    ocBOrder.getId(), ocBOrder.getTid(), e);
        }
    }

    /**
     * 判断平台状态是否为"已取消&关闭"
     *
     * @param platformStatus 平台状态
     * @return true-已取消或关闭，false-其他状态
     */
    private boolean isPlatformOrderCancelledOrClosed(String platformStatus) {
        return TaoBaoOrderStatus.TRADE_CANCELED.equals(platformStatus)
                || TaoBaoOrderStatus.TRADE_CLOSED.equals(platformStatus)
                || TaoBaoOrderStatus.TRADE_CLOSED_BY_TAOBAO.equals(platformStatus);
    }

    /**
     * 创建奶卡作废数据
     *
     * @param ocBOrder 订单信息
     * @param naikaList 奶卡列表
     */
    private void createNaikaVoidData(OcBOrder ocBOrder, List<OcBOrderNaiKa> naikaList) {
        try {
            User user = SystemUserResource.getRootUser();

            // 按订单明细分组，为每个明细创建一条作废记录
            Map<Long, List<OcBOrderNaiKa>> itemNaikaMap = naikaList.stream()
                    .collect(Collectors.groupingBy(OcBOrderNaiKa::getOcBOrderItemId));

            for (Map.Entry<Long, List<OcBOrderNaiKa>> entry : itemNaikaMap.entrySet()) {
                Long orderItemId = entry.getKey();
                List<OcBOrderNaiKa> itemNaikaList = entry.getValue();

                // 检查是否已存在作废记录
                List<OcBOrderNaikaVoid> existingVoids = ocBOrderNaikaVoidMapper.getByOcBOrderIdAndItemId(
                        ocBOrder.getId(), orderItemId);

                if (CollectionUtils.isEmpty(existingVoids)) {
                    // 创建奶卡作废记录
                    OcBOrderNaikaVoid naikaVoid = new OcBOrderNaikaVoid();
                    naikaVoid.setId(sequenceUtil.buildCardCodeVoidSequenceId());
                    naikaVoid.setOcBOrderId(ocBOrder.getId());
                    naikaVoid.setOcBOrderItemId(orderItemId);
                    naikaVoid.setTid(ocBOrder.getTid());
                    naikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID.getStatus());
                    naikaVoid.setVoidTimes(0);
                    naikaVoid.setPlatformId(Long.valueOf(ocBOrder.getPlatform()));
                    BaseModelUtil.makeBaseCreateField(naikaVoid, user);

                    ocBOrderNaikaVoidMapper.insert(naikaVoid);

                    log.info("成功创建奶卡作废记录，订单ID={}，订单明细ID={}，作废记录ID={}",
                            ocBOrder.getId(), orderItemId, naikaVoid.getId());

                    // 发送钉钉告警消息
                    try {
                        String additionalInfo = String.format("订单ID:%d，明细ID:%d，作废记录ID:%d",
                                ocBOrder.getId(), orderItemId, naikaVoid.getId());
                        DingTalkUtil.dingNaikaVoid(ocBOrder.getBillNo(), additionalInfo);
                        log.info("成功发送奶卡作废钉钉告警，订单编号={}", ocBOrder.getBillNo());
                    } catch (Exception dingException) {
                        log.error("发送奶卡作废钉钉告警失败，订单编号={}，订单ID={}",
                                ocBOrder.getBillNo(), ocBOrder.getId(), dingException);
                    }
                } else {
                    log.debug("订单明细已存在奶卡作废记录，跳过创建，订单ID={}，订单明细ID={}",
                            ocBOrder.getId(), orderItemId);
                }
            }
        } catch (Exception e) {
            log.error("创建奶卡作废数据异常，订单ID={}", ocBOrder.getId(), e);
        }
    }

    private OcBOrderDelivery getOcBOrderDelivery(Long orderId, SgOutResultItemMqResult sgPhyOutDeliverySaveRequest) {
        OcBOrderDelivery ocBOrderDelivery = new OcBOrderDelivery();
        //设置物流公司id，code，name
        ocBOrderDelivery.setId(ModelUtil.getSequence("oc_b_order_delivery"));
        ocBOrderDelivery.setOcBOrderId(orderId);
        ocBOrderDelivery.setPsCClrId(sgPhyOutDeliverySaveRequest.getPsCSpec1Id());
        ocBOrderDelivery.setPsCClrEcode(sgPhyOutDeliverySaveRequest.getPsCSpec1Ecode());
        ocBOrderDelivery.setPsCClrEname(sgPhyOutDeliverySaveRequest.getPsCSpec1Ename());
        ocBOrderDelivery.setPsCSizeId(sgPhyOutDeliverySaveRequest.getPsCSpec2Id());
        ocBOrderDelivery.setPsCSizeEcode(sgPhyOutDeliverySaveRequest.getPsCSpec2Ecode());
        ocBOrderDelivery.setPsCSizeEname(sgPhyOutDeliverySaveRequest.getPsCSpec2Ename());
        ocBOrderDelivery.setPsCProId(sgPhyOutDeliverySaveRequest.getPsCProId());
        ocBOrderDelivery.setPsCProEcode(sgPhyOutDeliverySaveRequest.getPsCProEcode());
        ocBOrderDelivery.setPsCProEname(sgPhyOutDeliverySaveRequest.getPsCProEname());
        ocBOrderDelivery.setPkgProducts(String.valueOf(sgPhyOutDeliverySaveRequest.getPsCSkuId()));
        ocBOrderDelivery.setPsCSkuId(sgPhyOutDeliverySaveRequest.getPsCSkuId());
        ocBOrderDelivery.setPsCSkuEcode(sgPhyOutDeliverySaveRequest.getPsCSkuEcode());
        ocBOrderDelivery.setIsactive("Y");
        ocBOrderDelivery.setCreationdate(new Date());
        ocBOrderDelivery.setAdOrgId(27L);
        ocBOrderDelivery.setAdClientId(37L);
        ocBOrderDelivery.setGbcode(sgPhyOutDeliverySaveRequest.getGbcode());
        ocBOrderDelivery.setQty(sgPhyOutDeliverySaveRequest.getQty());
        ocBOrderDelivery.setWeight(sgPhyOutDeliverySaveRequest.getWeight());
        ocBOrderDelivery.setSize(sgPhyOutDeliverySaveRequest.getSize());
        ocBOrderDelivery.setCpCLogisticsId(Long.valueOf(sgPhyOutDeliverySaveRequest.getCpCLogisticsId()));
        ocBOrderDelivery.setCpCLogisticsEcode(sgPhyOutDeliverySaveRequest.getCpCLogisticsEcode());
        ocBOrderDelivery.setCpCLogisticsEname(sgPhyOutDeliverySaveRequest.getCpCLogisticsEname());
        //设置快递单号
        ocBOrderDelivery.setLogisticNumber(sgPhyOutDeliverySaveRequest.getLogisticNumber());
        return ocBOrderDelivery;
    }

    private void getMqProcessResult(String messageKey, String messageBody) {
        try {
            /**
             * 压测20191022专用
             */
            List<SgOutResultSendMsgResult> mqOutOrderList = JSON.parseArray(messageBody, SgOutResultSendMsgResult.class);
            if (CollectionUtils.isEmpty(mqOutOrderList)) {
                return;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Consumed Number={}", messageKey), mqOutOrderList.size());
            }
            List<Long> idsList = new ArrayList<>();
            HashMap<Long, SgOutResultMqResult> orderMap = new HashMap<>();

            for (SgOutResultSendMsgResult sgOutResultSendMsgResult : mqOutOrderList) {
                //物流二次回传不再进行处理
                Boolean isLogisticsBack = sgOutResultSendMsgResult.getIsLogisticsBack();
                if (isLogisticsBack != null && isLogisticsBack) {
                    continue;
                }
                SgOutResultMqResult sgOutResultMqResult = sgOutResultSendMsgResult.getMqResult();
                Long orderId = sgOutResultMqResult.getSourceBillId();
                idsList.add(orderId);
                orderMap.put(orderId, sgOutResultMqResult);
            }
            /**
             * 压测20191022专用
             */
            //传全链路日志注释掉【20191019压测】
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectOrderListByIdsList(idsList);
            List<OcBOrder> allLinkList = new ArrayList<>();
            List<Long> updateList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ocBOrders)) {
                for (OcBOrder ocBOrder : ocBOrders) {
                    if (OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus())) {
                        allLinkList.add(ocBOrder);
                        updateList.add(ocBOrder.getId());
                    }
                }
            } else {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("接收到Mq成功消费，在数据库中未查询到该批数据，查询参数idsList的值为：{}", messageKey), idsList);
                }
            }
            ocBOrderLinkService.addOrderFinkLogs(allLinkList, BackflowStatus.QIMEN_CP_OUT.parseValue());

            updateList.forEach(x -> {
                SgOutResultMqResult request = orderMap.get(x);
                OcBOrder update = new OcBOrder();

                /*if (request != null) {
                    update.setOutWmsOutTime(request.getOutWmsOutTime());
                    update.setOutWingToSgTime(request.getOutWingToSgTime());
                }*/
                update.setId(x);
                update.setOrderStatus(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());


                ocBOrderMapper.updateById(update);
            });
            ocBOrderMapper.updateOrderStatusList(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger(), updateList);

            /**
             * 压测20191022专用
             */
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("接收到Mq成功消费，并调用订单出库服务成功", messageKey));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("StartProcess Error: {}"), Throwables.getStackTraceAsString(ex));
            throw new MqException(ex);
        }
    }

    /**
     * @param messageTopic
     * @param messageKey
     * @param messageBody
     * @param messageTag
     * @return com.jackrain.nea.sys.domain.ValueHolderV14
     * <AUTHOR>
     * @Description 校验并保存多包裹物流信息
     * @Date 15:32 2020/6/29
     **/
    private ValueHolderV14 checkAndSaveLogisticsInfo(String messageTopic, String messageKey,
                                                     String messageBody, String messageTag) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "success");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("校验并保存多包裹物流信息开始.MsgBody: {}", messageKey), messageBody);
        }
        try {
            User user = wmsUserCreateUtil.initWmsUser();
            List<SgOutResultSendMsgResult> mqOutOrderList = JSON.parseArray(messageBody, SgOutResultSendMsgResult.class);
            if (CollectionUtils.isEmpty(mqOutOrderList)) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("outOrderMqInfo解析为null!");
                return vh;
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Consumed Number={}", messageKey), mqOutOrderList.size());
            }

            for (SgOutResultSendMsgResult SgOutResultSendMsgResult : mqOutOrderList) {
                if (SgOutResultSendMsgResult == null) {
                    continue;
                }
                Boolean isLogisticsBack = SgOutResultSendMsgResult.getIsLogisticsBack();
                if (isLogisticsBack == null || !isLogisticsBack) {
                    vh.setCode(ResultCode.FAIL);
                    vh.setMessage("outOrderMqInfo解析不是物流二次回传");
                    return vh;
                }
                List<SgOutResultItemMqResult> deliveryItem = SgOutResultSendMsgResult.getMqResultItem();
                SgOutResultMqResult sgOutResultMqResult = SgOutResultSendMsgResult.getMqResult();
                Long orderId = sgOutResultMqResult.getSourceBillId();
                if (CollectionUtils.isEmpty(deliveryItem)) {
                    omsOrderLogService.addUserOrderLog(orderId, sgOutResultMqResult.getBillNo(),
                            OrderLogTypeEnum.LOGISTICS_UPDATE.getKey(), orderId + "物流二次回传接收到回传mq,但发货信息deliveryItem为空！", null, "", user);
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("二次物流回传接收到回传mq,但发货信息deliveryItem为空,订单id为{}", messageKey, orderId), orderId);
                    }
                    continue;
                }
                OcBOrder ocBOrder = omsOrderService.selectOrderInfo(orderId);
                if (ocBOrder != null) {
                    if (!Objects.equals(ocBOrder.getOrderStatus(), OmsOrderStatus.PLATFORM_DELIVERY.toInteger())
                            && !Objects.equals(ocBOrder.getOrderStatus(), OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())) {
                        omsOrderLogService.addUserOrderLog(orderId, sgOutResultMqResult.getBillNo(),
                                OrderLogTypeEnum.LOGISTICS_UPDATE.getKey(),
                                orderId + "仓库快递信息二次更新失败，仓库发货、平台发货的订单才可以做物流二次更新！", null, "", user);
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("仓库发货、平台发货的订单才可以做物流二次处理，mq消费结束，订单id为{}", messageKey, orderId), orderId);
                        }
                        continue;
                    }
                    OutOrderMqProcessorImpl service = ApplicationContextHandle.getBean(OutOrderMqProcessorImpl.class);
                    service.updateLogisticsInfo(sgOutResultMqResult, deliveryItem, ocBOrder);
                    omsOrderLogService.addUserOrderLog(orderId, sgOutResultMqResult.getBillNo(),
                            OrderLogTypeEnum.LOGISTICS_UPDATE.getKey(),
                            orderId + "仓库快递信息二次更新成功", null, "", user);
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("仓库快递信息二次更新成功，mq消费结束，订单id为{}", orderId));
                    }
                    if (Objects.equals(ocBOrder.getOrderStatus(), OmsOrderStatus.PLATFORM_DELIVERY.toInteger())) {
                        //平台发货状态的订单需要传到平台
                        platformSend(ocBOrder, sgOutResultMqResult.getBillNo(), user);
                    }
                } else {
                    log.error(LogUtil.format("接收到Mq成功消费，但未在数据库中查询到此订单，订单id为{}", orderId), orderId);
                    continue;
                }
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("checkAndSaveLogisticsInfo异常: {}"), Throwables.getStackTraceAsString(ex));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("OutOrderMqProcessorImpl.checkAndSaveLogisticsInfo异常");
            return vh;
        }
        return vh;
    }

    /**
     * @param ocBOrder
     * @param billNo
     * @param user
     * @return void
     * <AUTHOR>
     * @Description 二次物流更新，平台发货状态的订单需要更新物流信息
     * @Date 18:56 2020/6/30
     **/
    private void platformSend(OcBOrder ocBOrder, String billNo, User user) {
        Long orderId = ocBOrder.getId();
        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
        ocBOrderRelation.setOrderInfo(ocBOrder);
        List<OcBOrderItem> orderItemList = orderItemMapper.selectOrderItemList(orderId);
        if (orderItemList.size() == 0) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单出库服务,该订单不存在明细，不允许订单出库", orderId));
            }
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.STOCK_SEND.getKey(), ocBOrder.getId() + "仓库发货服务,订单不存在明细，不允许订单出库!", null, "订单不存在明细，不允许订单出库", user);
            return;
        }
        boolean flag = false;
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("物流二次回传调用订单平台发货服务开始", orderId));
        }
        //如果下单店铺的店铺渠道为：一件代发经销平台 则不调用平台发货
        boolean isCheckOrderIssuing = omsOrderService.checkOrderIssuing(ocBOrder.getId(), ocBOrder.getCpCShopId());
        if (isCheckOrderIssuing) {
            //订单店铺的店铺渠道为一件代发经销平台,二次更新物流不同步平台
            omsOrderLogService.addUserOrderLog(orderId, billNo,
                    OrderLogTypeEnum.WMS_LOGISTICS_BACK.getKey(),
                    orderId + "更新平台快递成功", null, "", user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("订单id为：{}的店铺为一件代发经销平台，二次更新物流不同步平台",
                        "OutOrderMqProcessorImpl.platformSend",
                        orderId), orderId);
            }
            return;
        }
        if (PlatFormEnum.TAOBAO.getCode().equals(ocBOrder.getPlatform()) || PlatFormEnum.ALIBABAASCP.getCode().equals(ocBOrder.getPlatform())) {
            //淘宝单独调用物流更新接口
            flag = logisticsResendService.callOrderPlatformLogisticsResend(ocBOrder);
        } else {
            ocBOrderRelation.setOrderItemList(orderItemList);
            ocBOrderRelation.setAutomaticOperation(true);
            flag = orderDeliveryProcessor.platformSend(ocBOrderRelation);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("物流二次回传调用订单平台发货服务结束", orderId));
        }
        if (flag) {
            omsOrderLogService.addUserOrderLog(orderId, billNo,
                    OrderLogTypeEnum.WMS_LOGISTICS_BACK.getKey(),
                    orderId + "更新平台快递成功", null, "", user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("更新平台快递成功，mq消费结束，订单id为：{}", orderId), orderId);
            }
        } else {
            omsOrderLogService.addUserOrderLog(orderId, billNo,
                    OrderLogTypeEnum.WMS_LOGISTICS_BACK.getKey(),
                    orderId + "更新平台快递失败", null, "", user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("更新平台快递失败，mq消费结束，订单id为：{}", orderId), orderId);
            }
        }

    }


    /**
     * @param sgOutResultMqResult
     * @param deliveryItem
     * @param ocBOrder
     * @return void
     * <AUTHOR>
     * @Description 修改物流信息
     * @Date 17:26 2020/6/29
     **/
    @Transactional(rollbackFor = Exception.class)
    public void updateLogisticsInfo(SgOutResultMqResult sgOutResultMqResult,
                                    List<SgOutResultItemMqResult> deliveryItem,
                                    OcBOrder ocBOrder) {
        String expressCode = sgOutResultMqResult.getLogisticNumber();
        Long logisticsId = sgOutResultMqResult.getCpCLogisticsId();
        String logisticsEcode = sgOutResultMqResult.getCpCLogisticsEcode();
        String logisticsEname = sgOutResultMqResult.getCpCLogisticsEname();
        List<String> expressCodeList = new ArrayList<>();
        List<OcBOrderDelivery> ocBOrderDeliveryList = new ArrayList<>();
        for (SgOutResultItemMqResult sgPhyOutDeliverySaveRequest : deliveryItem) {
            OcBOrderDelivery ocBOrderDelivery = new OcBOrderDelivery();
            ocBOrderDelivery.setId(ModelUtil.getSequence("oc_b_order_delivery"));
            ocBOrderDelivery.setOcBOrderId(ocBOrder.getId());
            ocBOrderDelivery.setPsCClrId(sgPhyOutDeliverySaveRequest.getPsCSpec1Id());
            ocBOrderDelivery.setPsCClrEcode(sgPhyOutDeliverySaveRequest.getPsCSpec1Ecode());
            ocBOrderDelivery.setPsCClrEname(sgPhyOutDeliverySaveRequest.getPsCSpec1Ename());
            ocBOrderDelivery.setPsCSizeId(sgPhyOutDeliverySaveRequest.getPsCSpec2Id());
            ocBOrderDelivery.setPsCSizeEcode(sgPhyOutDeliverySaveRequest.getPsCSpec2Ecode());
            ocBOrderDelivery.setPsCSizeEname(sgPhyOutDeliverySaveRequest.getPsCSpec2Ename());
            ocBOrderDelivery.setPsCProId(sgPhyOutDeliverySaveRequest.getPsCProId());
            ocBOrderDelivery.setPsCProEcode(sgPhyOutDeliverySaveRequest.getPsCProEcode());
            ocBOrderDelivery.setPsCProEname(sgPhyOutDeliverySaveRequest.getPsCProEname());
            ocBOrderDelivery.setPkgProducts(String.valueOf(sgPhyOutDeliverySaveRequest.getPsCSkuId()));
            ocBOrderDelivery.setPsCSkuId(sgPhyOutDeliverySaveRequest.getPsCSkuId());
            ocBOrderDelivery.setPsCSkuEcode(sgPhyOutDeliverySaveRequest.getPsCSkuEcode());
            ocBOrderDelivery.setIsactive("Y");
            ocBOrderDelivery.setCreationdate(new Date());
            ocBOrderDelivery.setAdOrgId(27L);
            ocBOrderDelivery.setAdClientId(37L);
            ocBOrderDelivery.setGbcode(sgPhyOutDeliverySaveRequest.getGbcode());
            ocBOrderDelivery.setQty(sgPhyOutDeliverySaveRequest.getQty());
//            weight = sgOutResultMqResult.getTheoreticalWeight();
            ocBOrderDelivery.setWeight(sgPhyOutDeliverySaveRequest.getWeight());
            ocBOrderDelivery.setSize(sgPhyOutDeliverySaveRequest.getSize());

            if (StringUtils.isNotEmpty(sgPhyOutDeliverySaveRequest.getCpCLogisticsId())) {
                logisticsId = Long.valueOf(sgPhyOutDeliverySaveRequest.getCpCLogisticsId());
                ocBOrderDelivery.setCpCLogisticsId(logisticsId);
            }
            logisticsEcode = sgPhyOutDeliverySaveRequest.getCpCLogisticsEcode();
            ocBOrderDelivery.setCpCLogisticsEcode(logisticsEcode);
            if (StringUtils.isNotEmpty(sgPhyOutDeliverySaveRequest.getCpCLogisticsEname())) {
                logisticsEname = sgPhyOutDeliverySaveRequest.getCpCLogisticsEname();
                ocBOrderDelivery.setCpCLogisticsEname(logisticsEname);
            }
            //设置快递单号
            expressCode = sgPhyOutDeliverySaveRequest.getLogisticNumber();
            if (expressCodeList.size() > 0) {
                if (!expressCodeList.contains(expressCode)) {
                    expressCodeList.add(expressCode);
                }
            } else {
                expressCodeList.add(expressCode);
            }
            ocBOrderDelivery.setLogisticNumber(expressCode);
            //调用插入发货信息表的公共方法
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("调用插入发货信息表的公共方法开始"));
            }
            if (ocBOrderDeliveryList.size() > 0) {
                if (!ocBOrderDeliveryList.contains(ocBOrderDelivery)) {
                    ocBOrderDeliveryList.add(ocBOrderDelivery);
                }
            } else {
                ocBOrderDeliveryList.add(ocBOrderDelivery);
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("ocBOrderDeliveryMapper.batchInsert方法执行前,ocBOrderDeliveryList的值为：{}"), ocBOrderDeliveryList);
        }
        ocBOrderDeliveryMapper.delete(new QueryWrapper<OcBOrderDelivery>().lambda()
                .eq(OcBOrderDelivery::getOcBOrderId, ocBOrder.getId()));
        ocBOrderDeliveryMapper.batchInsert(ocBOrderDeliveryList);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("更新物流单号，物流公司id，物流公司名称，物流公司ecode到订单信息表开始"));
        }
        ocBOrder.setExpresscode(expressCode);
        ocBOrder.setCpCLogisticsId(logisticsId);
        ocBOrder.setCpCLogisticsEcode(logisticsEcode);
        ocBOrder.setCpCLogisticsEname(logisticsEname);
        if (expressCodeList.size() > 1) {
            ocBOrder.setIsMultiPack(1L);
        } else {
            ocBOrder.setIsMultiPack(0L);
        }
        omsOrderService.updateOrderInfo(ocBOrder);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("omsOrderService.updateOrderInfo,更新后"));
        }
    }


    /**
     * 发送短信
     *
     * @param ocBOrders 订单集合
     */
    public void sendNote(final List<OcBOrder> ocBOrders) throws InterruptedException {
        /*给予集合拆分策略*/
        List<List<OcBOrder>> groupList = groupList(ocBOrders, 1000);
        for (List<OcBOrder> orders : groupList) {
            orders.forEach(ocBOrder -> {
                //出库触发短信发送事件
                SmsSendStrategyInfo smsSendStrategyInfo = new SmsSendStrategyInfo() {{
                    setOcBOrder(ocBOrder);
                    setServiceType("smsSendOutOrder");
                }};
                applicationContext.publishEvent(new SmsSendEvent<>(this, smsSendStrategyInfo));
            });
            TimeUnit.MILLISECONDS.sleep(3);
        }
    }

    private static <T> List<List<T>> groupList(List<T> list, int toIndex) {
        int listSize = list.size();
        ArrayList<List<T>> arrayList = new ArrayList<>();
        int keyToken = 0;
        for (int i = 0; i < list.size(); i += toIndex) {
            if (i + toIndex > listSize) {
                toIndex = listSize - i;
            }
            arrayList.add(list.subList(i, i + toIndex));
            keyToken++;
        }
        return arrayList;
    }

    /**
     * 记录配置店铺的SKU和生产日期信息
     * 限定业务类型：RYCK16、RYCK17
     * 记录逻辑出库单中所有的 SKU 以及生产日期（实际生产日期）
     * 如果同一个品的出库通知单中有多个 SKU，取最新的一条
     *
     * @param ocBOrder 订单信息
     * @param deliveryItem 出库明细列表
     */
    private void recordSkuProductionDateForConfiguredShops(OcBOrder ocBOrder, List<SgOutResultItemMqResult> deliveryItem) {
        try {
            // 检查业务类型是否为 RYCK16 或 RYCK17
            String businessTypeCode = ocBOrder.getBusinessTypeCode();
            if (!OrderBusinessTypeCodeEnum.SAP_CONSIGN_SALE.getCode().equals(businessTypeCode) &&
                !OrderBusinessTypeCodeEnum.SAP_STANDARD_SALE.getCode().equals(businessTypeCode)) {
                return;
            }

            if (StringUtils.isNotEmpty(ocBOrder.getSaleProductAttr())){
                return;
            }

            // 检查店铺是否在配置的批量反转列表中
            String shopCode = ocBOrder.getCpCShopEcode();
            Long shopId = ocBOrder.getCpCShopId();
            if (!businessSystemParamService.isShopCodeInBatchInversion(shopCode)) {
                return;
            }

            // 获取店铺完整信息
            CpShop cpShop = null;
            try {
                cpShop = cpRpcService.selectCpCShopById(shopId);
                if (cpShop == null) {
                    log.warn("未找到店铺信息，跳过SKU生产日期记录，订单ID={}，店铺编码={}", ocBOrder.getId(), shopCode);
                    return;
                }
            } catch (Exception e) {
                log.error("查询店铺信息异常，跳过SKU生产日期记录，订单ID={}，店铺编码={}", ocBOrder.getId(), shopCode, e);
                return;
            }

            log.info("开始记录配置店铺的SKU生产日期信息，订单ID={}，店铺编码={}，店铺名称={}，业务类型={}",
                    ocBOrder.getId(), shopCode, cpShop.getCpCShopTitle(), businessTypeCode);

            // 按商品编码分组，如果同一个品有多个SKU，取最新的一条（按生产日期排序）
            Map<String, SgOutResultItemMqResult> latestSkuByProduct = new HashMap<>();

            for (SgOutResultItemMqResult item : deliveryItem) {
                String productCode = item.getPsCProEcode(); // 商品编码
                String skuCode = item.getPsCSkuEcode(); // SKU编码
                String produceDate = item.getProduceDate(); // 生产日期

                if (StringUtils.isBlank(productCode) || StringUtils.isBlank(skuCode)) {
                    log.warn("商品编码或SKU编码为空，跳过记录，订单ID={}，商品编码={}，SKU编码={}",
                            ocBOrder.getId(), productCode, skuCode);
                    continue;
                }

                // 如果生产日期为空或无效，跳过记录
                if (StringUtils.isBlank(produceDate) || "00000000".equals(produceDate)) {
                    log.debug("生产日期为空或无效，跳过记录，订单ID={}，SKU编码={}，生产日期={}",
                            ocBOrder.getId(), skuCode, produceDate);
                    continue;
                }

                // 检查是否已存在该商品的记录
                SgOutResultItemMqResult existingItem = latestSkuByProduct.get(productCode);
                if (existingItem == null) {
                    // 第一次遇到该商品，直接记录
                    latestSkuByProduct.put(productCode, item);
                } else {
                    // 比较生产日期，保留最新的
                    String existingProduceDate = existingItem.getProduceDate();

                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                        Date currentDate = sdf.parse(produceDate);
                        Date existingDate = sdf.parse(existingProduceDate);

                        if (currentDate.after(existingDate)) {
                            // 当前记录的生产日期更新，替换
                            latestSkuByProduct.put(productCode, item);
                            log.debug("更新商品最新SKU记录，订单ID={}，商品编码={}，新SKU={}，新生产日期={}，旧SKU={}，旧生产日期={}",
                                    ocBOrder.getId(), productCode, skuCode, produceDate,
                                    existingItem.getPsCSkuEcode(), existingProduceDate);
                        }
                    } catch (ParseException e) {
                        log.error("生产日期解析失败，订单ID={}，商品编码={}，当前生产日期={}，已存在生产日期={}",
                                ocBOrder.getId(), productCode, produceDate, existingProduceDate, e);
                    }
                }
            }

            // 记录最终的SKU和生产日期信息到数据库
            if (!latestSkuByProduct.isEmpty()) {
                log.info("成功筛选出最新SKU记录，订单ID={}，记录数量={}", ocBOrder.getId(), latestSkuByProduct.size());

                int successCount = 0;
                for (Map.Entry<String, SgOutResultItemMqResult> entry : latestSkuByProduct.entrySet()) {
                    String productCode = entry.getKey();
                    SgOutResultItemMqResult item = entry.getValue();
                    String skuCode = item.getPsCSkuEcode();
                    String produceDateStr = item.getProduceDate();

                    try {
                        // 直接使用生产日期字符串（yyyyMMdd格式）
                        // 保存到数据库（包含店铺ID和名称）
                        OcBShopSkuBatchInfoService.SaveResult saveResult =
                                ocBShopSkuBatchInfoService.saveOrUpdateSkuBatchInfoWithResult(
                                        cpShop.getId(), shopCode, cpShop.getCpCShopTitle(), skuCode, produceDateStr, ocBOrder.getReceiverAddress());

                        if (saveResult.isSuccess()) {
                            successCount++;
                            if (saveResult.getOldProduceDate() != null) {
                                log.info("成功更新SKU生产日期信息：订单ID={}，订单编号={}，店铺ID={}，店铺编码={}，店铺名称={}，商品编码={}，SKU编码={}，新生产日期={}，原生产日期={}，数量={}",
                                        ocBOrder.getId(), ocBOrder.getBillNo(), cpShop.getId(), shopCode, cpShop.getCpCShopTitle(), productCode,
                                        skuCode, produceDateStr, saveResult.getOldProduceDate(), item.getQty());
                            } else {
                                log.info("成功插入SKU生产日期信息：订单ID={}，订单编号={}，店铺ID={}，店铺编码={}，店铺名称={}，商品编码={}，SKU编码={}，生产日期={}，数量={}",
                                        ocBOrder.getId(), ocBOrder.getBillNo(), cpShop.getId(), shopCode, cpShop.getCpCShopTitle(), productCode,
                                        skuCode, produceDateStr, item.getQty());
                            }
                        } else {
                            if (saveResult.getOldProduceDate() != null) {
                                log.debug("SKU生产日期信息未保存，原因：{}，订单ID={}，店铺ID={}，店铺编码={}，SKU编码={}，新生产日期={}，数据库生产日期={}",
                                        saveResult.getReason(), ocBOrder.getId(), cpShop.getId(), shopCode, skuCode, produceDateStr,
                                        saveResult.getOldProduceDate());
                            } else {
                                log.warn("SKU生产日期信息保存失败，原因：{}，订单ID={}，店铺ID={}，店铺编码={}，SKU编码={}，生产日期={}",
                                        saveResult.getReason(), ocBOrder.getId(), cpShop.getId(), shopCode, skuCode, produceDateStr);
                            }
                        }
                    }catch (Exception e) {
                        log.error("保存SKU生产日期信息异常，订单ID={}，SKU编码={}，生产日期={}",
                                ocBOrder.getId(), skuCode, produceDateStr, e);
                    }
                }

                log.info("SKU生产日期信息保存完成，订单ID={}，总数={}，成功数={}",
                        ocBOrder.getId(), latestSkuByProduct.size(), successCount);
            } else {
                log.warn("未找到有效的SKU记录，订单ID={}", ocBOrder.getId());
            }

        } catch (Exception e) {
            log.error("记录配置店铺SKU生产日期信息异常，订单ID={}", ocBOrder.getId(), e);
        }
    }
}
