package com.jackrain.nea.oc.oms.process.audit;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * 订单审核服务
 *
 * @author: 易邵峰
 * @since: 2019-01-18
 * create at : 2019-01-18 15:57
 */
@Component
public class OrderAuditProcess extends AbstractOrderProcess<OcBOrderRelation> {


    public OrderAuditProcess() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "";
    }

    @Override
    protected long getProcessOrderId(OcBOrderRelation orderInfo) {
        return orderInfo.getOrderInfo().getId();
    }

    @Override
    protected String getProcessOrderNo(OcBOrderRelation orderInfo) {
        return orderInfo.getOrderInfo().getSourceCode();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.AUDIT_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.DEFAULT;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }

}
