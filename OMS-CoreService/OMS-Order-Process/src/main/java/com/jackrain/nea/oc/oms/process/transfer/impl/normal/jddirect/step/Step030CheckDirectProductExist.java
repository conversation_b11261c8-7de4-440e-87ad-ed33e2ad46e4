package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jddirect.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectItem;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 检查订单商品是否在系统中
 * /**
 *
 * @Author: 黄世新
 * @Date: 2022/3/28 下午3:43
 * @Version 1.0
 */
@Step(order = 30, description = "检查订单商品是否在系统中")
@Slf4j
@Component
public class Step030CheckDirectProductExist extends BaseJingdongDirectOrderProcessStep implements IOmsOrderProcessStep<IpJingdongDirectOrderRelation> {


    @Override
    public ProcessStepResult<IpJingdongDirectOrderRelation> startProcess(IpJingdongDirectOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        boolean hasErrorInfo = false;
        StringBuilder sbErrorInfo = new StringBuilder();
        IpBJingdongDirect ipBJingdongDirect = orderInfo.getIpBJingdongDirect();
        Long shopId = ipBJingdongDirect.getCpCShopId();
        Map<String, ProductSku> productSkuMap = new HashMap<>(16);
        boolean flag = false;
        String presaleTransfer = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:pro_sku_or_platform_sku");
        if (StringUtils.isNotEmpty(presaleTransfer)) {
            flag = "是".equals(presaleTransfer.trim());
        }
        for (IpBJingdongDirectItem directItem : orderInfo.getIpBJingdongDirectItems()) {
            String skuId = directItem.getSkuId();
            if (StringUtils.isEmpty(skuId)) {
                hasErrorInfo = true;
                sbErrorInfo.append("商品SkuId为空;");
                sbErrorInfo.append("\r\n");
                break;
            } else {
                //todo 查询第三方映射关系表
                ProductSku skuInfo = null;
                if (flag) {
                    List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuId, shopId);
                    if (CollectionUtils.isNotEmpty(productSkus) && productSkus.size() == 1) {
                        skuInfo = productSkus.get(0);
                    }
                    if (skuInfo == null) {
                        skuInfo = psRpcService.selectProductSku(skuId);
                    }
                } else {
                    skuInfo = psRpcService.selectProductSku(skuId);
                    if (skuInfo == null) {
                        List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuId, shopId);
                        if (CollectionUtils.isNotEmpty(productSkus) && productSkus.size() == 1) {
                            skuInfo = productSkus.get(0);
                        }
                    }
                }
                if (skuInfo == null) {
                    hasErrorInfo = true;
                    String msg = Resources.getMessage("不存在;");
                    sbErrorInfo.append(directItem.getSkuId() + "--");
                    sbErrorInfo.append(msg);
                    sbErrorInfo.append("\r\n");
                    break;
                } else if (YesNoEnum.N.getKey().equals(skuInfo.getIsactive())) {
                    hasErrorInfo = true;
                    String msg = Resources.getMessage("已作废;");
                    sbErrorInfo.append(directItem.getSkuId() + "--");
                    sbErrorInfo.append(msg);
                    sbErrorInfo.append("\r\n");
                    break;
                } else {
                    productSkuMap.put(skuId, skuInfo);
                }
            }
        }
        orderInfo.setProductSkuMap(productSkuMap);
        if (hasErrorInfo) {
            String errorMessage = "商品数据不存在或已作废，退出转单操作";
            ipJingdongDirectService.updateIpBJingdongDirectIstrans(orderNo, TransferOrderStatus.TRANSFEREXCEPTION, sbErrorInfo.toString());
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "检查商品数据成功,进入下一步");
    }
}
