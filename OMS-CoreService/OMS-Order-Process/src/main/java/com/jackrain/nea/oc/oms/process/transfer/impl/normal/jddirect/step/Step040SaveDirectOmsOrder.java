package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jddirect.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.JingDongDirectOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2022/3/30 下午3:31
 * @Version 1.0
 */
@Step(order = 40, description = "保存订单")
@Slf4j
@Component
public class Step040SaveDirectOmsOrder extends BaseJingdongDirectOrderProcessStep implements IOmsOrderProcessStep<IpJingdongDirectOrderRelation> {
    @Override
    public ProcessStepResult<IpJingdongDirectOrderRelation> startProcess(IpJingdongDirectOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongDirect ipBJingdongDirect = orderInfo.getIpBJingdongDirect();
        Integer orderState = ipBJingdongDirect.getOrderState();
        //历史订单
        boolean isHistoryOrder = JingDongDirectOrderStatus.TRADE_FINISHED.getCode().equals(orderState)
                || JingDongDirectOrderStatus.WAIT_BUYER_CONFIRM_GOODS.getCode().equals(orderState);
        //发生退款  或者订单删除
        boolean isRefund = JingDongDirectOrderStatus.HAPPEN_REFUND.getCode().equals(orderState)
                || JingDongDirectOrderStatus.ORDER_DELETE.getCode().equals(orderState);
        try {
            OcBOrderRelation ocBOrderRelation = jdDirectOrderTransferUtil.directOrderToOmsOrder(orderInfo, isHistoryOrder, isRefund);
            omsOrderService.saveOmsOrderInfo(ocBOrderRelation, isHistoryOrder, operateUser);
            return new ProcessStepResult<>(StepStatus.SUCCESS, "数据保存成功,进入下一步");
        } catch (Exception e) {
            log.error("订单号:{}, 京东厂直订单存储失败", orderInfo.getOrderNo(), e);
            return new ProcessStepResult<>(StepStatus.FAILED, "京东厂直订单存储失败" + e.getMessage());
        }
    }

}
