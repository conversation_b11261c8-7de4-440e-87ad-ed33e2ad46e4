package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.audit;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.OmsMethod;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.MultiThreadOrderProcessor;
import com.jackrain.nea.oc.oms.process.audit.OrderAuditProcess;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单审单消息处理器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @author: heliu
 * @since: 2019/5/29
 * create at : 2019/5/29 10:17
 */
@Slf4j
public class AuditOrderMqDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OrderAuditProcess auditProcess;

    @Autowired
    protected MultiThreadOrderProcessor threadOrderProcessor;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {

        try {
            String ids = orderMqInfo.getOrderIds();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("AuditOrderMqDetailProcessor.Start.OrderIds: {}"), ids);
            }
            if (StringUtils.isNotEmpty(ids)) {
                String[] idArrayValues = ids.split(",");
                List<OcBOrderRelation> orderRelationList = new ArrayList<>();
                for (String paramId : idArrayValues) {
                    OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(Long.parseLong(paramId));
                    if (orderRelation == null) {
                        log.error(LogUtil.format("AuditOrderMqDetailProcessor.Error.Not.Exist", paramId));
                    } else {
                        // 只处理：待审核的订单
                        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderRelation.getOrderInfo().getOrderStatus())) {
                            orderRelation.setOmsMethod(OmsMethod.AUTO);
                            orderRelationList.add(orderRelation);
                        }
                    }
                }
                threadOrderProcessor.startMultiThreadExecute(this.auditProcess, orderRelationList);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("AuditOrderMqDetailProcessor.Error: {}"), Throwables.getStackTraceAsString(e));
        }
        return new ProcessStepResultList();
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.AUDIT_ORDER;
    }
}
