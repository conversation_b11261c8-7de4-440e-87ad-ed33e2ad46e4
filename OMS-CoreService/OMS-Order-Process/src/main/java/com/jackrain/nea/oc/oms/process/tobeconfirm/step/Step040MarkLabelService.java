package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OmsOrderMarkStService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: 黄世新
 * @Date: 2022/8/1 下午2:19
 * @Version 1.0
 */
@Step(order = 40, description = "订单打标策略")
@Slf4j
public class Step040MarkLabelService extends BaseTobeConfirmedProcessStep
        implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsOrderMarkStService omsOrderMarkStService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        try {
            OcBOrderParam param = new OcBOrderParam();
            param.setOcBOrder(orderInfo.getOrderInfo());
            param.setOrderItemList(orderInfo.getOrderItemList());
            omsOrderMarkStService.orderMarkStService(param, operateUser);
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("订单打标失败:{}", ocBOrder.getId(), "订单打标失败"), Throwables.getStackTraceAsString(e));
            OcBOrder errorOrderInfo = new OcBOrder();
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage("订单打标失败"));
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_5);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, "订单打标失败");
        }
    }
}
