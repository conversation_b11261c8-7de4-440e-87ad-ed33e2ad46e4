package com.jackrain.nea.oc.oms.process.tobeconfirm;

import com.jackrain.nea.oc.oms.mapper.task.OcBToBeConfirmedTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 待分配订单转成待审核订单服务
 * 把最初的订单执行赠品活动，分配发货仓库，分配物流，占用库存等策略 完善整个订单信息
 *
 * @author: 易邵峰
 * @since: 2019-01-17
 * create at : 2019-01-17 17:12
 */
@Component
public class ToBeConfirmedOrderProcess extends AbstractOrderProcess<OcBOrderRelation> {

    @Autowired
    private OmsToBeConfirmedTaskService omsToBeConfirmedTaskService;

    public ToBeConfirmedOrderProcess() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "";
    }

    @Override
    protected long getProcessOrderId(OcBOrderRelation orderInfo) {
        return orderInfo.getOrderInfo().getId();
    }

    @Override
    protected String getProcessOrderNo(OcBOrderRelation orderInfo) {
        return orderInfo.getOrderInfo().getSourceCode();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.UNCONFIRMED_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.DEFAULT;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }

    @Override
    public void updateTaskStatus(long orderId){
        omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderId);
    }

}
