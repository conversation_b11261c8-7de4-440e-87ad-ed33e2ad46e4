package com.jackrain.nea.oc.oms.process.jitx.timeorder.cancel.step;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.nums.TimeOrderOutEnum;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderCancelService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.services.TimeOrderVoidSgSendService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 判断时效订单状态
 *
 * @author: chenxiulou
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 40, description = "判断时效订单状态")
@Slf4j
@Component
public class Step040ReleaseTimeOrderStock extends BaseVipTimeOrderCancelProcessStep
        implements IOmsOrderProcessStep<IpBCancelTimeOrderVipRelation> {
    @Autowired
    private IpBTimeOrderVipMapper timeOrderMapper;
    @Autowired
    private IpVipTimeOrderCancelService cancelService;
    @Autowired
    private IpVipTimeOrderCancelService vipTimeOrderCanselService;

    @Autowired
    private TimeOrderVoidSgSendService timeOrderVoidSgSendService;

    @Autowired
    protected IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Override
    public ProcessStepResult<IpBCancelTimeOrderVipRelation> startProcess(IpBCancelTimeOrderVipRelation timeOrderInfo,
                                                                         ProcessStepResult preStepResult,
                                                                         boolean isAutoMakeup, User operateUser) {


        ProcessStepResult<IpBCancelTimeOrderVipRelation> stepResult = new ProcessStepResult<>();
        String orderNo = timeOrderInfo.getOrderNo();
        //释放时效订单库存
        ValueHolderV14 v14 = cancelService.releaseTimeOrderStock(orderNo, SystemUserResource.getRootUser(), TimeOrderOutEnum.TRANSFORMATION.getKey());
        if (v14 != null && v14.getCode() == ResultCode.SUCCESS) {
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setMessage("取消时效订单成功，转换完成。");
        } else {
            String errorMessage = v14 != null ? v14.getMessage() : "";
            boolean updateStatusRes = vipTimeOrderCanselService.updateTimeOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFERFAIL, "转单异常" + errorMessage);
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
        return stepResult;
    }
}
