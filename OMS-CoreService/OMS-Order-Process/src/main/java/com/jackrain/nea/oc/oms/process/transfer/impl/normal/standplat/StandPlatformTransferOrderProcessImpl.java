package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;


/**
 * 订单中间表转成待分配订单服务
 * <p>
 * 完成从平台订单到订单的转换
 *
 * @author: ming.fz
 * @since: 2019-07-04
 * create at : 2019-07-04
 */

@Component
public class StandPlatformTransferOrderProcessImpl extends AbstractOrderProcess<IpStandplatOrderRelation> {

    public StandPlatformTransferOrderProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "standplat";
    }

    @Override
    protected long getProcessOrderId(IpStandplatOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpStandplatOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_STANDPLAT_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.STANDPLAT;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }

    /**
     * 通用订单接口，平台单号
     *
     * @param orderInfo 订单单据
     * @return
     */
    @Override
    protected String getSourceTid(IpStandplatOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }
}
