package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 判断“卖家备注”是否与【全渠道订单】中的“卖家备注”一致,不一致则更新  “锁定”（LOCKED）、“取消”（TRADE_CANCELED）
 * @Date 2019-11-21
 **/
@Step(order = 45, description = "判断“卖家备注”是否与【全渠道订单】中的“卖家备注”一致")
@Slf4j
@Component
public class Step45UpdateSellerRemarkExt extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {
    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongOrder jdOrder = orderInfo.getJingdongOrder();
        List<OcBOrder> ocBOrderList = orderInfo.getOcBOrderList();

        // 更新: 1.卖家备注, 2.平台状态
        for (OcBOrder order : ocBOrderList) {
            boolean isSameSellerMemo = StringUtils.equalsIgnoreCase(jdOrder.getVenderremark(), order.getSellerMemo());
            boolean isSamePlatStatus = StringUtils.equalsIgnoreCase(jdOrder.getOrderState(), order.getPlatformStatus());
            if (isSameSellerMemo && isSamePlatStatus) {
                continue;
            }
            OcBOrder newOrder = new OcBOrder();
            newOrder.setId(order.getId());
            if (!isSameSellerMemo) {
                newOrder.setSellerMemo(jdOrder.getVenderremark());
            }
            if (!isSamePlatStatus) {
                newOrder.setPlatformStatus(jdOrder.getOrderState());
            }
            orderService.updateOrderInfo(newOrder);
        }
        return new ProcessStepResult<>(StepStatus.FINISHED, "取消和锁定状态同步卖家备注，程序结束！");
    }
}
