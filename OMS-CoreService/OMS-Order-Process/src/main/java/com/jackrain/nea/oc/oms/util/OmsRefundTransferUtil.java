package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * @Desc : 退款转单
 * <AUTHOR> xiWen
 * @Date : 2020/9/17
 */
@Slf4j
public class OmsRefundTransferUtil {

    /**
     * 退单转单时,传WMS中的订单不允许转换
     * 不关注单据合法性
     *
     * @param omsOrderRelations 订单
     * @return 是否允许操作
     */
    public static boolean isForbidOrderRelationTransfer(List<OmsOrderRelation> omsOrderRelations) {

        if (CollectionUtils.isEmpty(omsOrderRelations)) {
            return false;
        }

        for (OmsOrderRelation omsOrderRelation : omsOrderRelations) {
            OcBOrder order = omsOrderRelation.getOcBOrder();
            if (order == null) {
                continue;
            }
            if (judgeStatus(order)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isForbidOrderTransfer(List<OcBOrder> ocBOrders) {

        if (CollectionUtils.isEmpty(ocBOrders)) {
            return false;
        }

        for (OcBOrder order : ocBOrders) {
            if (order == null) {
                continue;
            }
            if (judgeStatus(order)) {
                return true;
            }
        }
        return false;
    }


    public static boolean isForbidOrderTransfer(OcBOrder order) {

        if (order == null) {
            return false;
        }
        return judgeStatus(order);
    }

    /**
     * 判断状态
     *
     * @param order 订单
     * @return 禁止继续 ?
     */
    private static boolean judgeStatus(OcBOrder order) {

        if (order.getOrderStatus() == null) {
            return false;
        }
        boolean allowTransfer = OmsOrderStatus.PENDING_WMS.toInteger().equals(order.getOrderStatus())
                || OmsOrderStatus.ORDER_DEFAULT.toInteger() .equals(order.getOrderStatus())
                || OmsOrderStatus.OCCUPY_IN.toInteger() .equals(order.getOrderStatus());
        return allowTransfer;
    }

}
