package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.share.model.request.out.SgBShareOutSaveRequest;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryRecordMapper;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryItemEx;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryRecord;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 判断时效订单状态
 *
 * @author: zhuxing
 * @since: 2021-08-29
 */
@Step(order = 25, description = "判断时效订单状态")
@Slf4j
@Component
public class Step025CheckTimeOrderStatus extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private IpBJitxDeliveryRecordMapper deliveryRecordMapper;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation deliveryInfo,
                                                                  ProcessStepResult preStepResult,
                                                                  boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxDeliveryRelation> stepResult = new ProcessStepResult<>();
        List<IpBJitxDeliveryItemEx> jitxDeliveryItemList = deliveryInfo.getJitxDeliveryItemList();
        String orderSn = deliveryInfo.getOrderNo();
        //时效订单
        List<IpVipTimeOrderRelation> ipVipTimeOrderRelationList = deliveryInfo.getIpVipTimeOrderRelationList();
        List<IpBTimeOrderVip> timeOrderVipList = new ArrayList<>();
        //查询时效订单占用明细集合
        List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipOccupyItemList = new ArrayList<>();

        //匹配时效订单
        if(CollectionUtils.isEmpty(ipVipTimeOrderRelationList)){
            String operateMessage = "寻仓单 OrderNo:" + orderSn + "时效订单为空，暂不处理";
            deliveryInfo.setRemarks("时效订单为空，暂不处理");
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.FAILED);
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, "时效订单为空，暂不处理");
            return stepResult;
        }
        for(IpVipTimeOrderRelation relation:ipVipTimeOrderRelationList){
            timeOrderVipList.add(relation.getIpBTimeOrderVip());
            ipBTimeOrderVipOccupyItemList.addAll(relation.getIpBTimeOrderVipOccupyItemList());
        }

        //寻仓单条码集合
        List<String> skuCodeList = jitxDeliveryItemList.stream().map(IpBJitxDeliveryItemEx::getBarcode).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(ipBTimeOrderVipOccupyItemList)){
            String operateMessage = "寻仓单 orderSn:" + orderSn + "时效订单占用明细为空，暂不处理";
            deliveryInfo.setRemarks("时效订单明细，暂不处理");
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.FAILED);
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, "时效订单占用明细为空，暂不处理");
            return stepResult;
        }

        //时效订单占用明细 商品条码去重集合,一对一匹配商品条码
        int skuCodeSize = skuCodeList.size();
        List<String> occupyItemSkuCodeList = ipBTimeOrderVipOccupyItemList.stream().map(IpBTimeOrderVipOccupyItem::getBarcode).distinct().collect(Collectors.toList());
        //取交集
        skuCodeList.retainAll(occupyItemSkuCodeList);
        if(occupyItemSkuCodeList.size() < skuCodeSize || skuCodeList.size() < skuCodeSize){
            String operateMessage = "寻仓单 OrderNo:" + orderSn + "时效订单暂未下载完，暂不处理";
            deliveryInfo.setRemarks("时效订单暂未下载完，暂不处理");
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.FAILED);
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, "时效订单暂未下载完，暂不处理");
            return stepResult;
        }

        //时效订单单据状态集合
        List<Integer> timeOrderStatusList = timeOrderVipList.stream().map(IpBTimeOrderVip::getStatus).collect(Collectors.toList());

        //状态存在：取消或者已完成则确认为JIT
        if(timeOrderStatusList.contains(TimeOrderVipStatusEnum.CANCELLED.getValue()) || timeOrderStatusList.contains(TimeOrderVipStatusEnum.MATCHED.getValue())){
            //确认为JIT
            String operateMessage = "时效订单状态存在取消或者已完成，确认为JIT";
            deliveryInfo.setRemarks(operateMessage);
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.FINISHED);
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.SYNCFAILD, operateMessage);
            return stepResult;
        }

        //时效订单单据状态存在：待占单、占单中、寻仓中 则不处理
        List<Integer> createStatusList = new ArrayList<>();
        createStatusList.add(TimeOrderVipStatusEnum.CREATED.getValue());
        createStatusList.add(TimeOrderVipStatusEnum.IN_SINGLE.getValue());
        createStatusList.add(TimeOrderVipStatusEnum.IN_SEEKING_STORE.getValue());
        createStatusList.retainAll(timeOrderStatusList);
        if(!CollectionUtils.isEmpty(createStatusList)){
            //不处理
            String operateMessage = "时效订单单据状态存在：待占单、占单中、寻仓中,不处理";
            deliveryInfo.setRemarks(operateMessage);
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.FINISHED);
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, operateMessage);
            return stepResult;
        }

        //时效单已占配销层且为YY仓库，判断JITX寻仓结果是否有记录，没有记录且未达到寻源间隔时间，则等待下一次寻源
        if(log.isDebugEnabled()){
            log.debug(" 寻仓单寻源校验，参数：{}", JSON.toJSONString(deliveryInfo));
        }
        if(timeOrderVipList.size() == 1){
            Long timeId = timeOrderVipList.get(0).getId();
            SgBShareOutSaveRequest request = new SgBShareOutSaveRequest();
            request.setSourceBillId(timeId);
            request.setSourceBillType(SgConstantsIF.BILL_TYPE_VIPSHOP_TIME);
            ValueHolderV14<Map<Long,Boolean>> v14 = sgRpcService.queryShareStoreIsYy(Collections.singletonList(request));
            if(v14.isOK() && !ObjectUtils.isEmpty(v14.getData()) && Boolean.TRUE.equals(v14.getData().get(timeId))){

                //查询JITX寻仓结果
                List<IpBJitxDeliveryRecord> deliveryRecordList = deliveryRecordMapper
                        .selectList(new LambdaQueryWrapper<IpBJitxDeliveryRecord>()
                                .eq(IpBJitxDeliveryRecord::getTid,orderSn)
                                .eq(IpBJitxDeliveryRecord::getIsactive, R3CommonResultConstants.VALUE_Y));

                //判断寻仓单是否达到寻源间隔
                Integer minConf = propertiesConf.getProperty("r3.delivery.occupy.delay.min", 60);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(deliveryInfo.getJitxDelivery().getCreationdate());
                calendar.add(Calendar.MINUTE,minConf);
                if(log.isDebugEnabled()){
                    log.debug(" 寻仓单寻源校验，时间间隔：{}，校验失效时间：{}，JITX寻仓结果：{}", minConf,
                            DateUtil.dateTimeSecondsFormatter.format(calendar.getTime()), JSON.toJSONString(deliveryInfo));
                }
                if(calendar.after(Calendar.getInstance()) && CollectionUtils.isEmpty(deliveryRecordList)){
                    String operateMessage = "JITX寻仓结果未返回且未达到寻仓间隔时间，等待下一次寻源";
                    deliveryInfo.setRemarks(operateMessage);
                    ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, operateMessage);
                    stepResult.setMessage(operateMessage);
                    stepResult.setStatus(StepStatus.FINISHED);
                    return stepResult;
                }
                if(!CollectionUtils.isEmpty(deliveryRecordList)){
                    Optional<IpBJitxDeliveryRecord> failRecordOption = deliveryRecordList.stream()
                            .filter(r -> VipConstant.JITX_DELIVERY_RECORD_STATUS_OCCUPY_FAIL.equals(r.getOccupyStatus()))
                            .max(Comparator.comparing(IpBJitxDeliveryRecord::getApplicationTime));
                    failRecordOption.ifPresent(ipBJitxDeliveryRecord -> {
                        List<CpCStore> stores = cpRpcService.selectStoresByCodes(Collections.singletonList(ipBJitxDeliveryRecord.getStoreCode()));
                        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(stores)){
                            List<String> warehouseCodeList = stores.stream()
                                    .map(CpCStore::getCpCPhyWarehouseEcode).collect(Collectors.toList());
                            deliveryInfo.setExcludeWarehouseCode(String.join(",",warehouseCodeList));
                        }
                    });
                }
            }
        }

        //时效订单单据状态都为：占单成功或者缺货 则寻源占单，平台反馈
        List<Integer> occupiedStatusList = new ArrayList<>();
        occupiedStatusList.add(TimeOrderVipStatusEnum.OCCUPIED.getValue());
        occupiedStatusList.add(TimeOrderVipStatusEnum.OUT_STOCK.getValue());
        if(occupiedStatusList.containsAll(timeOrderStatusList)){
            //继续寻仓
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setMessage("寻仓单寻仓，单据编号=" + orderSn + "，进入下一阶段");
            return stepResult;
        }

        //时效订单状态都为寻仓成功的 则进行平台反馈
        List<Integer> orderStatusList = timeOrderStatusList.stream().distinct().collect(Collectors.toList());
        if(orderStatusList.size() == 1 && TimeOrderVipStatusEnum.SEEKING_STORE_SUCCESS.getValue().equals(orderStatusList.get(0))){
            //不进行寻源占单，直接进行平台反馈
            String operateMessage = "时效订单状态全为寻仓成功，进行平台反馈";
            deliveryInfo.setRemarks(operateMessage);
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step040FeedBackDeliveryResult.class);
            return stepResult;
        }

        stepResult.setStatus(StepStatus.FAILED);
        stepResult.setMessage("寻仓单寻仓，单据编号=" + orderSn + "转换异常，转换终止！");
        return stepResult;
    }

}
