package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OcBorderUpdateService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 更新地址信息
 *
 * @author: 黄超
 * @since: 2019-06-27
 * create at : 2019-06-27 9:00
 */
@Step(order = 80, description = "更新地址信息")
@Component
@Slf4j
public class Step80JitxUpdateBuyerAddress extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Autowired
    private RegionNewService regionService;
    @Autowired
    private OcBorderUpdateService ocBorderUpdateService;
    @Autowired
    private OmsOrderLogService orderLogService;

    @SuppressWarnings("unchecked")
    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxOrderRelation> stepResult = new ProcessStepResult<>();

        if (preStepResult.getNextStepOperateObj() != null) {
            try {
                List<OcBOrder> transferOrderList = (List<OcBOrder>) preStepResult.getNextStepOperateObj();
                for (OcBOrder transferOrder : transferOrderList) {
                    if (transferOrder == null) {
                        continue;
                    }
                    String newAddress = "";
                    if (orderInfo != null && orderInfo.getJitxOrder() != null) {
                        //            newAddress = orderInfo.getJitxOrder().getBuyerAddress().replaceAll(",", "::::");
                        OcBOrder order = new OcBOrder();
                        order.setReceiverAddress(orderInfo.getJitxOrder().getBuyerAddress());
                        newAddress = OrderAddressConvertUtil.convert(order);
                    }
                    String transferAddress = transferOrder.getReceiverAddress();
                    if (!StringUtils.equalsIgnoreCase(newAddress, transferAddress) && orderInfo != null) {

                        //取消拦截
//                        JSONObject jsonObject = new JSONObject();
//                        jsonObject.put("id",transferOrder.getId());
//                        orderInterceptionService.cancelInterception(jsonObject, operateUser);

                        JSONObject copyObject = new JSONObject();
                        copyObject.put("id", transferOrder.getId());
                        copyObject.put("flag", 1);
                        JSONObject updateInfo = this.parseRegionInfo(orderInfo.getJitxOrder());
                        updateInfo.put("receiver_address", newAddress);
                        copyObject.put("updateInfo", updateInfo);

//                        ValueHolderV14 vh = ocBorderUpdateService.updateReceiveAddress(copyObject, operateUser);

                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("Step80JitxUpdateBuyerAddress.ocBorderUpdateService.jitxUpdateReceiveAddress更新地址信息{}","jitxUpdateReceiveAddress更新地址信息",orderInfo.getJitxOrder().getOrderSn()));
                        }
                        ValueHolderV14 vh = ocBorderUpdateService.jitxUpdateReceiveAddress(copyObject, operateUser);

                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("Step80JitxUpdateBuyerAddress.jitxUpdateReceiveAddress更新地址返回信息：{}","jitxUpdateReceiveAddress更新地址返回信息",orderInfo.getJitxOrder().getOrderSn()), JSONObject.toJSONString(vh));
                        }
                        if (vh.isOK()) {
                            orderInfo.setRemarks("地址更新完成");
                            orderLogService.addUserOrderLog(transferOrder.getId(), transferOrder.getBillNo(), OrderLogTypeEnum.ADDRESS_UPDATE.getKey(), "JITX订单修改地址，JITX订单地址：" + newAddress + "，零售发货单地址：" + transferOrder.getReceiverAddress(), null, null,
                                    SystemUserResource.getRootUser());

                            //更新修改地址状态 edit by lan.wf 2020/7/24 15:30
                            ipJitxOrderService.updateChangeAddrStatus(transferOrder.getSourceCode(), 0);

                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("Step80JitxUpdateBuyerAddress.ocBOrderHoldService.businessUnHold：{}","businessUnHold",orderInfo.getJitxOrder().getOrderSn()), JSONObject.toJSONString(vh));
                            }

                            //取消Hold单
                            ValueHolder holdVh = ocBOrderHoldService.businessUnHold(transferOrder.getId(), OrderHoldReasonEnum.JITX_HOLD);

                            if (holdVh.isOK()) {
                                log.info(LogUtil.format("Step80JitxUpdateBuyerAddress取消Hold成功:{}","Step80JitxUpdateBuyerAddress取消Hold成功",orderInfo.getJitxOrder().getOrderSn()), orderInfo.getJitxOrder().getOrderSn());
                            } else {
                                log.info(LogUtil.format("Step80JitxUpdateBuyerAddress取消Hold失败{},失败原因:{}","Step80JitxUpdateBuyerAddress取消Hold成功",orderInfo.getJitxOrder().getOrderSn()), orderInfo.getJitxOrder().getOrderSn(), holdVh.get("message"));
                            }
                        } else {
                            String errorMessage = vh.getMessage();
                            boolean updateStatusRes = ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(),
                                    TransferOrderStatus.NOT_TRANSFER, errorMessage);
                            if (!updateStatusRes) {
                                errorMessage += ";更新地址失败";
                            }
                            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
                        }
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("Step080JitxUpdateBuyerAddress PreStep=:{}", "Step080JitxUpdateBuyerAddress"), Throwables.getStackTraceAsString(ex));
            }

            stepResult.setMessage("更新订单地址信息成功，进入下一阶段");
        } else {
            stepResult.setMessage("未进行更新订单地址信息，进入下一阶段");
        }
        stepResult.setNextStepClass(Step85JitxOrderFrobiddenDelivery.class);
        stepResult.setStatus(StepStatus.SUCCESS);
        return stepResult;
    }

    /**
     * 省市区匹配
     *
     * @param jitxOrder 订单表
     */
    private JSONObject parseRegionInfo(IpBJitxOrder jitxOrder) {

        JSONObject updateInfo = new JSONObject();
        try {
            ProvinceCityAreaInfo regionInfo = regionService.selectProvinceCityAreaInfo(jitxOrder.getBuyerProvince()
                    , jitxOrder.getBuyerCity()
                    , jitxOrder.getBuyerCounty());
            if (regionInfo.getProvinceInfo() != null) {
                updateInfo.put("cp_c_region_province_id", regionInfo.getProvinceInfo().getId());
            }
            if (regionInfo.getCityInfo() != null) {
                updateInfo.put("cp_c_region_city_id", regionInfo.getCityInfo().getId());
            }
            if (regionInfo.getAreaInfo() != null) {
                updateInfo.put("cp_c_region_area_id", regionInfo.getAreaInfo().getId());
            }
        } catch (Exception ex) {
            log.error("调用省市区服务异常" + ex);
        }
        updateInfo.put("receiver_name", jitxOrder.getBuyer());
        updateInfo.put("receiver_mobile", jitxOrder.getBuyerMobile());
        updateInfo.put("receiver_phone", jitxOrder.getBuyerTel());
        updateInfo.put("receiver_zip", jitxOrder.getBuyerPostcode());
        updateInfo.put("ship_amt", BigDecimal.ZERO);

        return updateInfo;
    }
}
