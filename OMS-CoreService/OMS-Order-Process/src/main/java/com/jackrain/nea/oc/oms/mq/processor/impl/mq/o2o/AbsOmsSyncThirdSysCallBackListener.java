package com.jackrain.nea.oc.oms.mq.processor.impl.mq.o2o;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.sap.Oms2SapMapper;
import com.jackrain.nea.oc.oms.sap.Oms2SapStatusEnum;
import com.jackrain.nea.oc.request.o2o.SyncReturnResponse;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 同步第三方系统回传
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/22
 */
@Slf4j
public abstract class AbsOmsSyncThirdSysCallBackListener implements MessageListener {

    @Autowired
    private Oms2SapMapper oms2SapMapper;

    protected abstract String getOrigTableName(String method);

    protected abstract String getOrigStatusCol(String method);

    protected abstract String getOrigSendTimesCol(String method);

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {

        log.debug(LogUtil.format("consume.msgTag-{},msgKey-{}, msgTopic-{}", message.getKey()));
        String msgBody;
        try {
            msgBody = MsgConvertUtil.objectDeserialize(message.getBody()).toString();
            log.debug(LogUtil.format("Receive msgBody-{}", message.getKey()), JSON.toJSONString(msgBody));
            SyncReturnResponse response = checkMsg(msgBody);
            if (Objects.isNull(response)) {
                return Action.CommitMessage;
            }

            boolean isSuccess = doCallBack(message, response);

            if (isSuccess) {
                log.debug(LogUtil.format("Update Success, msgKey- ", message.getKey()));
                return Action.CommitMessage;
            } else {
                log.error(LogUtil.format("Update Failed, msgKey- ", message.getKey()));
                return Action.ReconsumeLater;
            }

        } catch (Exception e) {
            log.error(LogUtil.format("Receive CallBack Msg Exception, ExpMsg {}",
                    message.getKey()), Throwables.getStackTraceAsString(e));
            return Action.CommitMessage;
        }

    }

    /**
     * validate
     *
     * @param msgBody
     * @return jsn
     */
    private SyncReturnResponse checkMsg(String msgBody) {
        SyncReturnResponse response = null;
        if (msgBody == null) {
            log.error(LogUtil.format("checkMsg.DeSerialize msgBody Result ->　Null"));
            return null;
        }
        try {
            response = JSON.parseObject(msgBody, SyncReturnResponse.class);
            if (response == null) {
                log.error(LogUtil.format("checkMsg.msgBody　Parse To Entity, Result Was Null"));
            }
            List<SyncReturnResponse.ItemResult> list = response.getList();
            if (list == null) {
                log.error(LogUtil.format("checkMsg.msgBody　Parse To Entity ItemResult, Result Was Null"));
            }

            List<SyncReturnResponse.ItemResult> successItems = new ArrayList<>();
            List<SyncReturnResponse.ItemResult> failedItems = new ArrayList<>();
            for (SyncReturnResponse.ItemResult item : list) {
                if (item == null) {
                    continue;
                }
                if (item.getCode() == 0) {
                    successItems.add(item);
                } else {
                    failedItems.add(item);
                }
            }
            response.setSuccessItems(successItems);
            response.setFailedItems(failedItems);

        } catch (Exception e) {
            log.error(LogUtil.format("checkMsg.msgBody parse Exp, ExpMsg {}"), Throwables.getStackTraceAsString(e));
        }
        return response;
    }


    protected boolean doCallBack(Message message, SyncReturnResponse response) {

        List<SyncReturnResponse.ItemResult> failedItems = response.getFailedItems();
        List<SyncReturnResponse.ItemResult> successItems = response.getSuccessItems();

        if (CollectionUtils.isNotEmpty(successItems)) {
            List<Long> ids = successItems.stream().filter(o -> o != null).map(SyncReturnResponse.ItemResult::getLineId)
                    .collect(Collectors.toList());
            int i = oms2SapMapper.updateDynamicOrigOrder(getOrigTableName(response.getMethod()),
                    getOrigStatusCol(response.getMethod()), Oms2SapStatusEnum.FINISH.val(), ids);
            log.debug(LogUtil.format("Update Success OriginOrder-{},{} Items"), getOrigTableName(response.getMethod()), i);
        }

        if (CollectionUtils.isNotEmpty(failedItems)) {
            List<Long> ids = failedItems.stream().filter(o -> o != null).map(SyncReturnResponse.ItemResult::getLineId)
                    .collect(Collectors.toList());
            int k = oms2SapMapper.updateDynamicOrigOrderCallBack(getOrigTableName(response.getMethod()),
                    getOrigStatusCol(response.getMethod()), Oms2SapStatusEnum.FAILED.val(),
                    ids, getOrigSendTimesCol(response.getMethod()));
            log.debug(LogUtil.format("Update Failed OriginOrder-{},{} Items"), getOrigTableName(response.getMethod()), k);
        }
        return true;
    }


}
