package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.step;

import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpStandplatOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.LockOrderType;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.awt.*;

//import com.jackrain.nea.oc.oms.services.OmsOrderMakeupService;

/**
 * 基础通用Order处理阶段
 *
 * @author: ming.fz
 * @since: 2019-07-04
 * create at : 2019-07-04
 */
public abstract class BaseStandplatOrderProcessStep {

//    /**
//     * OMS订单补偿服务操作Service
//     */
//    @Autowired
//    protected OmsOrderMakeupService orderMakeupService;

    @Autowired
    protected IpStandplatOrderService ipStandplatOrderService;

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected PsRpcService psRpcService;
    @Autowired
    protected OcBOrderMapper ocBOrderMapper;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.STANDPLAT;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_STANDPLAT_ORDER;
    }


}
