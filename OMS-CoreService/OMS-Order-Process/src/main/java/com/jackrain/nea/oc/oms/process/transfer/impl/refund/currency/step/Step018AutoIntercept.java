package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefundType;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.services.LogisticsInterceptService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName Step018AutoIntercept
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/11/13 16:30
 * @Version 1.0
 */
@Step(order = 18, description = "订单自动拦截")
@Slf4j
@Component
public class Step018AutoIntercept extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Autowired
    private LogisticsInterceptService logisticsInterceptService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Step018AutoIntercept.startProcess.orderInfo:{};",
                    "Step018AutoIntercept"), JSON.toJSONString(orderInfo));
        }
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();

        if (ipBStandplatRefund.getReturnStatus() == null) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "售后状态不符合自动拦截");
        }
        // 只有三个状态才生成拦截单
        if (!(ObjectUtil.equal(ipBStandplatRefund.getReturnStatus(), IpBStandplatRefudStatusEnum.WAIT_SELLER_AGREE.getVal()) ||
                ObjectUtil.equal(ipBStandplatRefund.getReturnStatus(), IpBStandplatRefudStatusEnum.WAIT_BUYER_RETURN_GOODS.getVal()) ||
                ObjectUtil.equal(ipBStandplatRefund.getReturnStatus(), IpBStandplatRefudStatusEnum.SUCCESS.getVal()))) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "售后状态不符合自动拦截");
        }


        IpBStandplatOrder ipBStandplatOrder = orderInfo.getIpBStandplatOrder();
        List<OmsOrderRelation> omsOrderRelationList = orderInfo.getOmsOrderRelation();
        List<OmsOrderRelation> isGiftOrderRelationList = orderInfo.getIsGiftOrderRelation();
        // 只拦截申请退款与退款完成状态的

        // 判断通用订单是否存在 如果不存在 往下走
        if (ObjectUtil.isNull(ipBStandplatOrder)) {
            // 后面会校验订单的合法性
            log.info("通用中间表无数据，进入下一阶段 returnNo {}", ipBStandplatRefund.getReturnNo());
            return new ProcessStepResult<>(StepStatus.SUCCESS, "通用中间表无数据，进入下一阶段");
        }
        // 判断订单金额
        if (ObjectUtil.notEqual(ipBStandplatOrder.getPayment(), ipBStandplatRefund.getRefundAmount())) {
            log.info("不是全额退，进入下一阶段 returnNo {}", ipBStandplatRefund.getReturnNo());
            return new ProcessStepResult<>(StepStatus.SUCCESS, "不是全额退，进入下一阶段");
        }
        //20240109佳哥让通用订单中间表的的交易状态为订单完成状态时直接跳过
        if (TaoBaoOrderStatus.TRADE_FINISHED.equals(ipBStandplatOrder.getStatus())) {
            log.info("交易状态为订单完成，进入下一阶段 returnNo {}", ipBStandplatRefund.getReturnNo());
            return new ProcessStepResult<>(StepStatus.SUCCESS, "交易状态为订单完成，进入下一阶段");
        }
        // 判断是不是仅退款

        Integer refundType = ipBStandplatRefund.getRefundType();
        if (ObjectUtil.isNull(refundType) || ObjectUtil.notEqual(refundType, IpBStandplatRefundType.ONLY_REFUND)) {
            log.info("不是仅退款，进入下一阶段 returnNo {}", ipBStandplatRefund.getReturnNo());
            return new ProcessStepResult<>(StepStatus.SUCCESS, "不是仅退款，进入下一阶段");
        }

        List<OmsOrderRelation> allOrderRelation = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(omsOrderRelationList)) {
            allOrderRelation.addAll(omsOrderRelationList);
        }
        if (CollectionUtils.isNotEmpty(isGiftOrderRelationList)) {
            allOrderRelation.addAll(isGiftOrderRelationList);
        }

        if (CollectionUtils.isEmpty(allOrderRelation)) {
            // 此时订单还没转下去 肯定还没发货 无法进行拦截 往下走
            log.info("无有效零售发货单，进入下一阶段 returnNo {}", ipBStandplatRefund.getReturnNo());
            return new ProcessStepResult<>(StepStatus.SUCCESS, "无有效零售发货单，进入下一阶段");
        }
        // 可能存在omsOrderRelation为空 不过isGiftOrderRelation 不为空的情况。
        // 反正两个集合的订单都要看 只要订单整单退了  并且申请的是仅退款 就可以看中台是不是已发货 发货的都执行拦截
        for (OmsOrderRelation orderRelation : allOrderRelation) {
            OcBOrder ocBOrder = orderRelation.getOcBOrder();
            if (ObjectUtil.isNull(ocBOrder)) {
                continue;
            }

            // 判断订单状态 如果是仓库发货或者平台发货 则生成拦截单
            Integer orderStatus = ocBOrder.getOrderStatus();
            if (ObjectUtil.equal(orderStatus, OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger()) || ObjectUtil.equal(orderStatus, OmsOrderStatus.PLATFORM_DELIVERY.toInteger())) {
                ValueHolderV14<Void> valueHolder = logisticsInterceptService.autoIntercept(ocBOrder.getId(), ipBStandplatRefund.getReturnNo());
                if (!valueHolder.isOK()) {
                    log.info("Step018AutoIntercept startProcess err {}, tid:{}", valueHolder.getMessage(), ipBStandplatOrder.getTid());
                }
            }
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "自动拦截结束，进入下一阶段");
    }
}
