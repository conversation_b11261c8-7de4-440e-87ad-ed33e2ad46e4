package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OmsConstituteSplitService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 判断订单明细是否有组合商品或者福袋商品
 *
 * @Author: 黄世新
 * @Date: 2019-07-12 18:14
 * @Version 1.0
 */
@Step(order = 50, description = "判断订单明细是否有组合商品或者福袋商品")
@Slf4j
public class Step050CheckComposeOrder extends BaseTobeConfirmedProcessStep
        implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private OmsConstituteSplitService omsConstituteSplitService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo,
                                                            ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        try {
            // 2020-12-24易邵峰修改：通过上阶段的OperateObj进行判断是否有值，因为上阶段已经查询过selectOrderItemListOccupy，再查浪费效率
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            if (CollectionUtils.isEmpty(orderItemList)) {
                orderItemList = orderItemMapper.selectOrderItemListOccupy(orderInfo.getOrderId());
            }

            //猫超订单不进行拆单操作
            if (PlatFormEnum.ALIBABAASCP.getCode().equals(orderInfo.getOrderInfo().getPlatform())) {
                String message = "OrderID=" + orderInfo.getOrderId() + ";OccupyStatus=" + orderInfo.getOrderInfo().getOccupyStatus() + " 猫超订单不进行拆单";
                return new ProcessStepResult<>(StepStatus.SUCCESS, message);
            }

            boolean hasCombineOrGiftProd = false;
            for (OcBOrderItem orderItemInfo : orderItemList) {
                //判断当前订单是否存在组合商品或者福袋商品
                //商品类型
                Long prodType = orderItemInfo.getProType();
                String giftBagSku = orderItemInfo.getGiftbagSku();
                if (prodType == null) {
                    continue;
                }
                boolean isCombineOrGiftProd = (prodType == SkuType.COMBINE_PRODUCT || prodType == SkuType.GIFT_PRODUCT || prodType == SkuType.NO_SPLIT_COMBINE)
                        && StringUtils.isEmpty(giftBagSku);
                if (isCombineOrGiftProd) {
                    hasCombineOrGiftProd = true;
                    break;
                }
            }
            if (hasCombineOrGiftProd) {
                //存在组合商品
                String message = "OrderID=" + orderInfo.getOrderId() + ";OccupyStatus=" + orderInfo.getOrderInfo().getOccupyStatus();
                OcBOrderRelation orderRelationInfo = new OcBOrderRelation();
                orderRelationInfo.setOrderInfo(orderInfo.getOrderInfo());
                orderRelationInfo.setOrderItemList(orderItemList);
                boolean isSuccess = omsConstituteSplitService.startExchangeCombineGiftProduct(orderRelationInfo, operateUser);
                if (isSuccess) {
                    //拆分成功后重新查一次
                    orderItemList = orderItemMapper.selectOrderItemListOccupy(orderInfo.getOrderId());
                    orderInfo.setOrderItemList(orderItemList);
                    message = message + " 订单明细存在组合商品或者福袋商品,拆分成功!";
                    return new ProcessStepResult<>(StepStatus.SUCCESS, message);
                }
                message = message + "订单明细存在组合商品或者福袋商品,拆分失败!";
                OcBOrder updateOrderInfo = new OcBOrder();
                updateOrderInfo.setId(orderInfo.getOrderId());
                updateOrderInfo.setSysremark(SplitMessageUtil.splitMesssage(message));
                omsOrderService.updateOrderInfo(updateOrderInfo);
                return new ProcessStepResult<>(StepStatus.FAILED, message);
            } else {
                String message = "OrderID=" + orderInfo.getOrderId() + ";OccupyStatus=" + orderInfo.getOrderInfo().getOccupyStatus() + " 订单明细不存在组合商品或者福袋商品进行下一步转换";
                //不存在组合商品  继续正常的占单流程
                return new ProcessStepResult<>(StepStatus.SUCCESS, message);
            }
        } catch (Exception ex) {
            OcBOrder updateOrderInfo = new OcBOrder();
            String operateMessage = "OrderId=" + orderInfo.getOrderId() + "执行组合商品解析服务异常,异常信息-->" + ex.getMessage();
            log.error(LogUtil.format("执行组合商品解析服务异常,异常信息:{}", "执行组合商品解析服务异常"), Throwables.getStackTraceAsString(ex));
            updateOrderInfo.setId(orderInfo.getOrderId());
            updateOrderInfo.setSysremark(SplitMessageUtil.splitMesssage(operateMessage));
            //组合商品拆分失败
            updateOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_10);
            omsOrderService.updateOrderInfo(updateOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
    }
}
