package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderUpdate4PreSaleService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.third.enums.OmsBillTypeEnum;
import com.jackrain.nea.third.service.OmsToThirdService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: xiWen.z
 * create at: 2019/10/13 0013
 */
@Step(order = 7, description = "卖家备注信息是否一致，不一致更新卖家备注信息; 更新订单状态,金额")
@Component
@Slf4j
public class Step007UpdateSellerRemark implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Autowired
    private OmsOrderUpdate4PreSaleService omsUpdateService;
    @Autowired
    private OmsOrderService omsOrderService;

    @SuppressWarnings("unchecked")
    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();
        IpBTaobaoOrder taobaoOrder = orderInfo.getTaobaoOrder();
        String orderStatus = taobaoOrder.getStatus();
        boolean status = TaoBaoOrderStatus.TRADE_CLOSED.equalsIgnoreCase(orderStatus)
                || TaoBaoOrderStatus.TRADE_CLOSED_BY_TAOBAO.equalsIgnoreCase(orderStatus)
                || TaoBaoOrderStatus.TRADE_FINISHED.equalsIgnoreCase(orderStatus);
        if (preStepResult.getNextStepOperateObj() != null) {
            try {
                List<OcBOrder> afterTransferOrderList = (List<OcBOrder>) preStepResult.getNextStepOperateObj();
                for (OcBOrder omsOrder : afterTransferOrderList) {
                    if (omsOrder != null) {
                        boolean isAddThird = (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(omsOrder.getOrderStatus())
                                || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(omsOrder.getOrderStatus())) && status;
                        if (isAddThird) {
                            //OmsToThirdService.addOmsToThirdPlatformStatus(omsOrder.getTid(), OmsBillTypeEnum.UPDATE_PLATFORM_STATUS, "AC", orderStatus, operateUser);
                        }
                        ValueHolderV14 vh = omsUpdateService.updateEachOmsInfo(orderInfo, omsOrder, operateUser);
                        if (vh.getCode() == ResultCode.FAIL) {
                            return new ProcessStepResult<>(StepStatus.FAILED, vh.getMessage());
                        } else {
//                            List<IpBTaobaoOrderItemEx> ipBTaobaoOrderItemExs = orderInfo.getTaobaoOrderItemList();
//                            if (CollectionUtils.isNotEmpty(ipBTaobaoOrderItemExs)) {
//                                for (IpBTaobaoOrderItemEx ipBTaobaoOrderItemEx : ipBTaobaoOrderItemExs) {
//                                    String statusSub = ipBTaobaoOrderItemEx.getStatus();
//                                    String oid = ipBTaobaoOrderItemEx.getOid().toString();
//                                    if (TaobaoReturnOrderExt.SuborderStatus.TRADE_CLOSED.getCode().equals(statusSub)) {
//                                        boolean flag = omsOrderService.handlePlatformGift(omsOrder, oid, operateUser);
//                                        //如果失败  则将订单
//                                        if (!flag) {
//                                            return new ProcessStepResult<>(StepStatus.FAILED, "处理平台赠品退款失败!");
//                                        }
//                                    }
//                                }
//                            }
                        }
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("Step007UpdateSellerRemark.PreStep.error=:{}", "PreStep"), Throwables.getStackTraceAsString(ex));
                return new ProcessStepResult<>(StepStatus.FAILED, "订单信息更新阶段发生异常");
            }
            stepResult.setMessage("更新卖家备注,订单信息成功,进入下一阶段");
        } else {
            stepResult.setMessage("未进行更新卖家备注,订单信息，进入下一阶段");
        }
        stepResult.setNextStepClass(Step008UpdateOrderTransferStatus.class);
        stepResult.setStatus(StepStatus.SUCCESS);
        return stepResult;
    }

}
