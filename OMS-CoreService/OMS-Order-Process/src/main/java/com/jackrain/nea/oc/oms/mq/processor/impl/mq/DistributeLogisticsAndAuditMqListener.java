package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.constant.MqConstants;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.mq.processor.impl.sgMq.TobeConfirmCallBackMqService;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * 分物流和审核任务MQ处理器
 *
 * @author: 用户要求抽取
 * @since: 2024-07-01
 */
@Slf4j
@Component
@RocketMqMessageListener(name = "DistributeLogisticsAndAuditMqListener", type = MqTypeEnum.DEFAULT)
public class DistributeLogisticsAndAuditMqListener implements BaseMessageListener {

    @Autowired
    private TobeConfirmCallBackMqService tobeConfirmCallBackMqService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.info("DistributeLogisticsAndAuditMqProcessor.topic-tag-key-body: {}-{}-{}-{}",
                messageTopic, messageTag, messageKey, messageBody);

        // 只处理特定的tag
        if (!MqConstants.TAG_DISTRIBUTE_LOGISTICS_AND_AUDIT.equals(messageTag)) {
            return;
        }
        // 解析消息体
        JSONObject msgBody = JSON.parseObject(messageBody);
        Long orderId = msgBody.getLong("orderId");
        String billNo = msgBody.getString("billNo");
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (orderId == null) {
                log.error("DistributeLogisticsAndAuditMqProcessor.consume 订单ID为空");
                return;
            }

            // 查询订单信息
            OcBOrder order = ocBOrderMapper.selectByID(orderId);
            if (order == null) {
                log.error("DistributeLogisticsAndAuditMqProcessor.consume 订单不存在, orderId={}", orderId);
                return;
            }

            // 检查订单状态，确保订单仍然需要处理分物流和审核任务
            // 如果订单状态已经不是待审核状态，则不需要再处理
            if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus())) {
                log.error("DistributeLogisticsAndAuditMqProcessor.consume 订单状态已变更，抛异常进行重试, orderId={}, orderStatus={}",
                        orderId, order.getOrderStatus());
                throw new MqException("订单状态已变更，抛异常进行重试");
            }

            // 尝试获取锁，如果获取不到，说明有其他实例正在处理该订单
            if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                log.info("DistributeLogisticsAndAuditMqProcessor.consume 订单正在被处理，抛异常进行重试, orderId={}", orderId);
                // 抛异常 进行重试
                throw new MqException("订单正在被处理，抛异常进行重试");
            }

            // fixme 如果此处存在物流公司id 有以下几种可能性。哪一种是可以往下走的
            // 1、用户指定了物流公司。用的是appointId
            // 2、因为消息堆积 导致用户已经页面触发了重新寻物流(手动重新寻源 是如何判断是之前寻物流到 还是用户指定的)
            // 3、重新寻源 触发了寻物流？
            tobeConfirmCallBackMqService.executeDistributeLogisticsAndAuditTask(order);
            // 记录处理日志
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.OCCUPY.getKey(),
                    "异步处理分物流和审核任务成功", "", "", SystemUserResource.getRootUser());
        } catch (Exception e) {
            log.error(LogUtil.format("DistributeLogisticsAndAuditMqProcessor.consume 异常: {}"),
                    Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }finally {
            // 释放锁
            redisLock.unlock();
        }
    }
}
