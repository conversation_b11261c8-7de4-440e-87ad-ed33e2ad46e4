package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Message;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mq.processor.aop.RocketMqConsumeLogger;
import com.jackrain.nea.oc.oms.services.LackOrderToWmsBackService;
import com.jackrain.nea.oc.oms.services.RefundOrderToWmsBackService;
import com.jackrain.nea.resource.WmsUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 零售退单发wms回传MQ
 *
 * @author: 郑立轩
 * @since: 2019/5/7
 * create at : 2019/5/7 10:14
 *
 * 无用废弃
 */

@Deprecated
@Slf4j
@RocketMqMessageListener(name = "RefundOrderToWmsReceiptBackMq", type = MqTypeEnum.CLOUD)
public class RefundOrderToWmsReceiptBackMq implements BaseMessageListener {

    /**
     * 更新订单为实缺
     */
    private static final String ITEMLACK_REPORT_METHOD = "itemlack.report";

    @Autowired
    private RefundOrderToWmsBackService refundOrderToWmsBackService;

    @Autowired
    private LackOrderToWmsBackService lackOrderToWmsBackService;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object o) {
        String method = null;
        Message message = (Message) o;
        boolean saveErrLogFlag = false;
        Throwable ex = null;
        try {
            log.info("零售退单发wms回传MQ.接收wms回传mq消息: {}-{}-{}",messageKey, messageBody, JSONObject.toJSONString(message));
            JSONObject object = JSONObject.parseObject(messageBody);
            method = object.getString("method");
            if (StringUtils.equals(ITEMLACK_REPORT_METHOD, method)) {
                //更新订单为实缺
                User user = WmsUserResource.getWmsUser();
                lackOrderToWmsBackService.handle(messageBody, user);
            } else {
                refundOrderToWmsBackService.handleBefore(messageBody);
            }
            // 成功的情况，需要更新成成功
            RocketMqConsumeLogger.getInstance().updateSuccessStatusNoException(message);
        } catch (Exception e) {
            log.error(LogUtil.format("零售退单发wms回传MQ.异常: {}"), Throwables.getStackTraceAsString(e));
            saveErrLogFlag = true;
            ex = e;
            // 记录日志
            throw new MqException(e);
        } finally {
            if (saveErrLogFlag) {
                RocketMqConsumeLogger.getInstance().saveErrorLogNoException(message, method, ex);
            }
        }
    }
}
