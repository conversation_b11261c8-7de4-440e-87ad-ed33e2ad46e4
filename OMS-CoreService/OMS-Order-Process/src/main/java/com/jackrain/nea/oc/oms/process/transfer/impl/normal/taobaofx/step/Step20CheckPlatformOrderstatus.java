package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 周琳胜
 * @since : 2019-07-10
 * create at : 2019-07-10 16:24
 * 判断平台订单的状态是否为“待发货”或“预付定金”
 */
@Step(order = 20, description = "判断平台订单的状态是否为待发货或预付定金")
@Slf4j
@Component
public class Step20CheckPlatformOrderstatus extends BaseTaobaoFxOrderProcessStep implements IOmsOrderProcessStep<IpTaobaoFxOrderRelation> {

    @Override
    public ProcessStepResult<IpTaobaoFxOrderRelation> startProcess(IpTaobaoFxOrderRelation orderInfo,
                                                                   ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {
        log.debug("TaobaoFxTransferOrder.step02" + orderInfo.toString());
        // 平台订单状态
        String currentStatus = orderInfo.getIpBTaobaoFxOrder().getStatus();
        IpBTaobaoFxOrder order = orderInfo.getIpBTaobaoFxOrder();
        // 待发货(WAIT_SELLER_SEND_GOODS)”、交易完成（TRADE_FINISHED）、等待买家收货（WAIT_BUYER_CONFIRM_GOODS）、交易关闭（TRADE_CLOSED）
        if (TaoBaoOrderStatus.WAIT_SELLER_SEND_GOODS.equals(currentStatus)
                || TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equals(currentStatus)
                || TaoBaoOrderStatus.TRADE_FINISHED.equals(currentStatus)
                || (TaoBaoOrderStatus.TRADE_CLOSED.equals(currentStatus) && order.getLogisticsId() != null)) {
            // 若是，继续下一步
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        } else {
            // 若否，则更新“转换状态”为已转换（2）,系统备注：订单状态不是待发货,标记为已转,转换次数=原次数+1
            IpBTaobaoFxOrder ipBTaobaoFxOrder = new IpBTaobaoFxOrder();
            ipBTaobaoFxOrder.setId(orderInfo.getOrderId());
            ipBTaobaoFxOrder.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
            ipBTaobaoFxOrder.setSysremark("订单状态不是待发货,标记为已转");
            if (order.getTransCount() == null) {
                ipBTaobaoFxOrder.setTransCount(1L);
            } else {
                ipBTaobaoFxOrder.setTransCount(order.getTransCount() + 1L);
            }
            ipTaobaoFxService.updateTransferStatus(ipBTaobaoFxOrder);
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态为非未转换，不执行逻辑");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        }

    }
}
