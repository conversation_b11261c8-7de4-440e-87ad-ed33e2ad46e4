package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobao.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoExchangeOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 根据淘宝换货状态走不同的分支
 *
 * @author: 孙勇生
 * @since: 2019-03-07
 * create at : 2019-01-21 14:44
 */
@Step(order = 10, description = "根据淘宝换货状态走不同的分支")
@Slf4j
@Component
public class Step010CheckExchangeStatus extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<IpTaobaoExchangeRelation> {


    @Override
    public ProcessStepResult<IpTaobaoExchangeRelation> startProcess(IpTaobaoExchangeRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange taobaoExchange = orderInfo.getTaobaoExchange();
        try {
            if (taobaoExchange != null) {
                //先更新为转换中
                String status = taobaoExchange.getStatus();
                if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.BUYER_RETURN_COLLECT_GOODS.getName().equals(status)
                        || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.STAY_BUYER_RETURN.getName().equals(status)
                        || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.WAIT_ISSUE_EXCHANGE_GOODS.getName().equals(status)
                        || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_STAY_HANDLE.getName().equals(status)) {
                    //状态是否为 待买家退货(2), 买家已退货，待收货(3),待发出换货商品(12)，换货待处理(1)
                    return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step20ExchangeGoodsStatus.class);
                } else if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_CLOSE.getName().equals(status)
                        || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.REFUND_PLEASE.getName().equals(status)) {
                    //换货关闭(4),请退款(14)
                    return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step30ExchangeGoodsStatus.class);
                } else {
                    //更新已转换
                    ipTaobaoExchangeService.statusDissatisfaction(orderInfo.getTaobaoExchange());
                    return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderId() + "转换完成");
                }
            }
            if (log.isDebugEnabled()) {
                log.debug(this.getClass().getName() + " 换货中间表未查询到数据;OrderNo={}", orderInfo.getOrderNo());
            }
            return new ProcessStepResult<>(StepStatus.FINISHED);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(taobaoExchange, e.getMessage());
            log.error(this.getClass().getName() + " 退换货转换异常", e);
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
