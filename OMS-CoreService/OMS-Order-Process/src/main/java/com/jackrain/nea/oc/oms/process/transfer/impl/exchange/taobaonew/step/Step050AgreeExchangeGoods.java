package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.ip.model.IpCTaobaoProductItem;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 9:56 下午
 * @Version 1.0
 */
@Step(order = 50, description = "同意换货")
@Slf4j
@Component
public class Step050AgreeExchangeGoods extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        try {
            //获取当前
            Long cpCShopId = ipBTaobaoExchange.getCpCShopId();
            StCExchangeStrategyOrderDO stCExchangeStrategyOrderDO = stRpcService.queryExchangeStrategyByShopId(cpCShopId);
            if (stCExchangeStrategyOrderDO == null) {
                String message = "未查询到该店铺的换货策略";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
            if (ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getIsOffAgree()) || stCExchangeStrategyOrderDO.getIsOffAgree() == 0) {
                String message = "未开启自动同意换货，处理失败";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
            String description = ipBTaobaoExchange.getDescription();
            if (StringUtils.isNotEmpty(description)) {
                if (ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getIsNoRemarkAgree()) || stCExchangeStrategyOrderDO.getIsNoRemarkAgree() == 0) {
                    //未勾选有备注
                    String message = "未开启有备注自动审核，处理失败";
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, ipBTaobaoExchange);
                    return new ProcessStepResult<>(StepStatus.FINISHED, message);
                }
            }
            //判断价格
            BigDecimal amt = ObjectUtils.isEmpty(stCExchangeStrategyOrderDO.getDeviationAmtAgree()) ? BigDecimal.ZERO :
                    stCExchangeStrategyOrderDO.getDeviationAmtAgree();
            //查询中间表金额
            IpCTaobaoProductItem exchangeProductItem =
                    cpRpcService.selectIpCTaobaoProductItemBySkuId(ipBTaobaoExchange.getExchangeSku());
            IpCTaobaoProductItem boughtProductItem =
                    cpRpcService.selectIpCTaobaoProductItemBySkuId(ipBTaobaoExchange.getBoughtSku());

            String message = "";
            if (null == exchangeProductItem || null == boughtProductItem) {
                message = "不自动同意换货,商品条码ID查询不存在!";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                        ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
            if (ObjectUtils.isEmpty(exchangeProductItem.getPrice()) || ObjectUtils.isEmpty(boughtProductItem.getPrice())) {
                message = "不自动同意换货,商品价格查询不存在!";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                        ipBTaobaoExchange);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
            if (exchangeProductItem.getPrice().compareTo(boughtProductItem.getPrice()) == 0
                    || exchangeProductItem.getPrice().subtract(boughtProductItem.getPrice()).abs().compareTo(amt) <= 0) {
                boolean agree = omsTaobaoExchangeService.agreeExchange(ipBTaobaoExchange);
                if (agree) {
                    message = "调用同意换货成功!";
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                            ipBTaobaoExchange);
                    return new ProcessStepResult<>(StepStatus.FINISHED, message);
                }else {
                    message = "调用同意换货失败!";
                    ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(), message,
                            ipBTaobaoExchange);
                    return new ProcessStepResult<>(StepStatus.FAILED, message);
                }
            }
            message = "不满足同意换货条件";
            ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                    ipBTaobaoExchange);
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(ipBTaobaoExchange, "处理同意换货失败 " + e.getMessage());
            log.error(LogUtil.format("处理同意换货失败失败:{}", "处理同意换货失败失败"), Throwables.getStackTraceAsString(e));
            String errorMessage = " 处理同意换货失败失败!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
