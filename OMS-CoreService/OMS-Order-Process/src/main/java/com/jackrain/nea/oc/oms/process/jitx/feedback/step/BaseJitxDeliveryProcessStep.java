package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.util.LockOrderType;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * 基础唯品会Jitx待寻仓订单处理阶段
 *
 * @author: chenxiulou
 * @since: 2019-06-26
 * create at : 2019-06-26 10:00
 */
public abstract class BaseJitxDeliveryProcessStep {

    @Autowired
    protected IpJitxDeliveryService ipJitxDeliveryService;

//    @Autowired
//    protected OmsOrderService orderService;

    @Autowired
    protected PsRpcService psRpcService;


    protected ChannelType getCurrentChannelType() {
        return ChannelType.VIPJITX;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JITX_DELIVERY;
    }


}
