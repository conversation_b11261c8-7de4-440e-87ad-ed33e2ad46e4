package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.log.LogCat;
import com.jackrain.nea.log.LogEvent;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.matcher.MatchStrategyManager;
import com.jackrain.nea.oc.oms.model.enums.AbnormalTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.services.OrderPriceValidate;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @author: xiWen.z
 * create at: 2019/10/14 0014
 */
@Step(order = 6, description = "保存订单信息")
@Component
@Slf4j
public class Step006SaveOmsOrder extends BaseTaobaoOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    OmsOrderAdvanceParseService omsOrderAdvanceParseService;

//    @Autowired
//    private R3MqSendHelper sendHelper;
//
//    @Autowired
//    private ToBeConfirmedOrderMqConfig toBeConfirmedOrderMqConfig;
//
//    /**
//     * 发送MQ消息
//     *
//     * @param saveOrderInfo 保存的OrderInfo订单信息
//     * @param logEventList  logEvent列表
//     */
//    private void sendMQ(OcBOrderRelation saveOrderInfo, List<LogEvent> logEventList) {
//        LogEvent eventMQ = LogCat.newEvent(Step006SaveOmsOrder.class.getSimpleName(), "StartSendMQ");
//        if (saveOrderInfo != null) {
//            long saveOrderId = saveOrderInfo.getOrderInfo().getId();
//            String billNo = saveOrderInfo.getOrderInfo().getBillNo();
//            String msgKey = "TB_TR_" + saveOrderId + "_" + billNo;
//            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
//            orderMqInfo.setChannelType(this.getCurrentChannelType());
//            orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
//            orderMqInfo.setOrderId(saveOrderId);
//            orderMqInfo.setOrderNo(billNo);
//            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
//            mqInfoList.add(orderMqInfo);
//            Object jsonValue = JSONObject.toJSONString(mqInfoList);
//
//            String messageId = null;
//            try {
//                messageId = sendHelper.sendMessage(jsonValue, toBeConfirmedOrderMqConfig.getSendToBeConfirmMqTopic(),
//                        toBeConfirmedOrderMqConfig.getSendToBeConfirmTag(),
//                        msgKey);
//            } catch (SendMqException e) {
//                e.printStackTrace();
//            }
//
//            eventMQ.addData("MQId", messageId);
//            eventMQ.addData("MQKey", msgKey);
//        }
//        eventMQ.complete();
//        logEventList.add(eventMQ);
//    }

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        String orderStatus = orderInfo.getTaobaoOrder().getStatus();
        boolean isHistoryOrder = TaoBaoOrderStatus.TRADE_FINISHED.equalsIgnoreCase(orderStatus)
                || TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equalsIgnoreCase(orderStatus);

        String orderNo = orderInfo.getOrderNo();
        try {
            List<LogEvent> logEventList = new ArrayList<>();
            LogEvent eventConvert = LogCat.newEvent(Step006SaveOmsOrder.class.getSimpleName(), "ConvertOrder");
            OcBOrderRelation saveOrderInfo = orderService.
                    convertTaobaoOrderToOrder(orderInfo, isHistoryOrder);
            eventConvert.complete();
            logEventList.add(eventConvert);

            // @20200616 策略接匹配
            strategyMatch(orderInfo, saveOrderInfo);
            // @20200710 打标
            //TaggerManager.get().doTag(saveOrderInfo.getOrderInfo(), saveOrderInfo.getOrderItemList());

            if (saveOrderInfo.getOrderInfo().getCpCRegionCityId() == null || saveOrderInfo.getOrderInfo().getCpCRegionProvinceId() == null) {
                String errorMessage = "省市区匹配异常!";
                boolean updateStatusRes = ipTaoBaoOrderService.updateTaobaoOrderTransAndAbnormal(orderNo,
                        TransferOrderStatus.TRANSFEREXCEPTION, errorMessage, AbnormalTypeEnum.MATE_ABNORMAL.getKey());
                if (!updateStatusRes) {
                    errorMessage += ";更新状态失败=False";
                }
                return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
            }
            LogEvent eventStartSave = LogCat.newEvent(Step006SaveOmsOrder.class.getSimpleName(), "SaveOrder");
            // 双11预售标签
            //saveOrderInfo.getOrderInfo().setDouble11PresaleStatus(1);
            String stepTradeStatus = orderInfo.getTaobaoOrder().getStepTradeStatus();
            saveOrderInfo.getOrderInfo().setStatusPayStep(stepTradeStatus);
            //saveOrderInfo.getOrderInfo().setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());

//            if (!TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equalsIgnoreCase(stepTradeStatus)) {
//                saveOrderInfo.getOrderInfo().setPayTime(null);
//            }
            //已支付金额  如果是预售就 = 阶段已付金额  如果不是 就能与实付金额
            BigDecimal receivedAmt = orderInfo.getTaobaoOrder().getStepPaidFee() ==null ? orderInfo.getTaobaoOrder().getPayment(): orderInfo.getTaobaoOrder().getStepPaidFee();
            saveOrderInfo.getOrderInfo().setReceivedAmt(receivedAmt == null ? BigDecimal.ZERO : receivedAmt);
            //转单前进行预售解析
            omsOrderAdvanceParseService.advanceParse(saveOrderInfo,operateUser,orderStatus);
            log.info("预售解析结束--");
            boolean saveResult = orderService.saveOmsOrderInfo(saveOrderInfo, orderInfo, isHistoryOrder, operateUser);
            if (saveResult) {
                if (this.omsSystemConfig.isTransferValidatePriceEnabled()) {
                    OrderPriceValidate.isSendDingTalk(saveOrderInfo);
                }
            }
            eventStartSave.complete();
            logEventList.add(eventStartSave);
            ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();
            stepResult.setMessage("存储数据成功，进入下一阶段");
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step008UpdateOrderTransferStatus.class);
            stepResult.setLogEventList(logEventList);
            return stepResult;
        } catch (Exception ex) {
            log.error(LogUtil.format("存储数据失败，退出转单服务:{}", "Step006SaveOmsOrder"), Throwables.getStackTraceAsString(ex));
            String errorMessage = "存储数据失败，退出转单服务;" + ex.getMessage();
            boolean updateStatusRes = ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo,
                    TransferOrderStatus.NOT_TRANSFER, errorMessage);
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

    /**
     * 策略解析匹配
     *
     * @param orderInfo
     * @param orderRelation
     */
    private void strategyMatch(IpTaobaoOrderRelation orderInfo, OcBOrderRelation orderRelation) {
        if (Objects.nonNull(orderInfo) && Objects.nonNull(orderRelation)) {
            MatchStrategyManager.get().match(orderInfo, this.getCurrentChannelType(), orderRelation.getOrderInfo(), orderRelation.getOrderItemList());
        }
    }
}
