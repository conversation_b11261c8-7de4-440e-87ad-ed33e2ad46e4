package com.jackrain.nea.oc.oms.process.merge.handle;


import com.jackrain.nea.oc.oms.model.MergeOrderInfo;

/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/6/18
 **/
public interface MergeStrategyHandler {

    /**
     * 单个订单策略 不满足返回true
     *
     * @param info
     * @return
     */
    Boolean doSingleHandle(MergeOrderInfo info);

    /**
     * 排序
     *
     * @param name
     * @return
     */
    Integer getSort(String name);

    /**
     * 所有订单策略，不满足返回true
     *
     * @param info
     * @return
     */
    Boolean doWholeHandle(MergeOrderInfo info);
}
