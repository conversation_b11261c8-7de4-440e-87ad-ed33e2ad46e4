package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.sourcing.model.request.SgOmsSplitOrderRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOmsSplitSubOrderItemRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgOmsSplitSubOrderRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsItemResult;
import com.burgeon.r3.sg.sourcing.model.result.SgFindSourceStrategyOmsResult;
import com.burgeon.r3.sg.sourcing.model.result.SgOccupyItemDistInfo;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderOutStockRecordMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBJitxDealerOrderTaskMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToBeConfirmedTaskMapper;
import com.jackrain.nea.oc.oms.mapperservice.OcBOrderOutStockRecordMapperService;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBJitxDealerOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderOutStockRecord;
import com.jackrain.nea.oc.oms.model.table.task.OcBJitxDealerOrderTask;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.OcBJitxModifyWarehouseLogService;
import com.jackrain.nea.oc.oms.services.OcBOrderItemExtService;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OmsOccupyTaskService;
import com.jackrain.nea.oc.oms.services.OmsOrderDistributeLogisticsService;
import com.jackrain.nea.oc.oms.services.OmsOrderJdSplitService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsStickerService;
import com.jackrain.nea.oc.oms.services.OrderExceptionTagService;
import com.jackrain.nea.oc.oms.services.SplitBeforeSourcingStService;
import com.jackrain.nea.oc.oms.services.UpdateOrderInfoService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceDetentionService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.services.task.OcBJitxDealerOrderTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OmsOrderSplitReasonUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.StCTraceabilityStrategyMarkingService;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.util.ComputeEqualExchangeQtyUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.OrderTagUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jackrain.nea.oc.oms.constant.MqConstants.TAG_DISTRIBUTE_LOGISTICS_AND_AUDIT;
import static com.jackrain.nea.oc.oms.constant.MqConstants.TOPIC_DISTRIBUTE_LOGISTICS_AND_AUDIT;


/**
 * @program: r3-oc-oms
 * @description: 处理寻源mq业务层
 * @author: liuwj
 * @create: 2021-08-08 17:23
 **/
@Slf4j
@Component
public class TobeConfirmCallBackMqService {

    @Value("${gw.order.shopid.value:78}")
    private String gwShopIdListStr;
    @Autowired
    private UpdateOrderInfoService updateOrderInfoService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBJitxModifyWarehouseLogService ocBJitxModifyWarehouseLogService;
    @Autowired
    private VipcomJitxWarehouseService vipcomJitxWarehouseService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private SgOccupiedInventoryService sgOccupiedInventoryService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;
    @Autowired
    private OrderAmountUtil orderAmountUtil;
    @Autowired
    private OmsOrderAdvanceDetentionService omsOrderAdvanceDetentionService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsAuditTaskService omsAuditTaskService;
    @Autowired
    private OcBToBeConfirmedTaskMapper toBeConfirmedTaskMapper;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private OmsOrderJdSplitService omsOrderJdSplitService;
    @Autowired
    OmsOrderAdvanceParseService omsOrderAdvanceParseService;
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private OcBJitxDealerOrderTaskService dealerOrderTaskService;
    @Autowired
    private OcBJitxDealerOrderTaskMapper taskMapper;
    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private OmsOrderDistributeLogisticsService omsOrderDistributeLogisticsService;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private TobeConfirmCallBackMqService tobeConfirmCallBackMqService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private SplitBeforeSourcingStService splitBeforeSourcingStService;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OrderExceptionTagService orderExceptionTagService;
    @Autowired
    private OcBOrderOutStockRecordMapper outStockRecordMapper;
    @Autowired
    private OcBOrderOutStockRecordMapperService outStockRecordMapperService;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;
    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private OmsStickerService omsStickerService;
    @Autowired
    private StCTraceabilityStrategyMarkingService stCTraceabilityStrategyMarkingService;


    @Transactional(rollbackFor = Exception.class)
    public void TobeConfirmCallBackService(SgFindSourceStrategyOmsResult sgFindSourceStrategyOmsResult,
                                           OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList) {
        log.info(LogUtil.format("TobeConfirmCallBackMqService.TobeConfirmCallBackService billNo:{},sgFindSourceStrategyOmsResult:{}",
                        "TobeConfirmCallBackMqService.TobeConfirmCallBackService"),
                ocBOrder.getBillNo(), JSONObject.toJSONString(sgFindSourceStrategyOmsResult));
        User user = SystemUserResource.getRootUser();
        String message = sgFindSourceStrategyOmsResult.getMessage();

        // 先记录分货信息
        Map<Long, OcBOrderItem> mapOldItem = ocBOrderItemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, e -> e, (i1, i2) -> i1));
        List<SgOccupyItemDistInfo> sgOccupyItemDistInfoList = sgFindSourceStrategyOmsResult.getSgOccupyItemDistInfoList();
        Map<Long, List<SgOccupyItemDistInfo>> sgOuuppyItemDistInfoListMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sgOccupyItemDistInfoList)) {
            sgOuuppyItemDistInfoListMap = sgOccupyItemDistInfoList.stream().collect(Collectors.groupingBy(SgOccupyItemDistInfo::getSourceItemId));
            if (sgOuuppyItemDistInfoListMap != null) {
                // 先更新一波数据
                for (Long key : sgOuuppyItemDistInfoListMap.keySet()) {
                    List<SgOccupyItemDistInfo> sgOccupyItemDistInfos = sgOuuppyItemDistInfoListMap.get(key);
                    ocBOrderItemExtService.updateByItemId(sgOccupyItemDistInfos.get(0), ocBOrder.getId());
                }
            }
        }
        if (sgFindSourceStrategyOmsResult.getCode() == ResultCode.SUCCESS) {
            //寻源成功
            List<SgFindSourceStrategyOmsItemResult> itemResultList = sgFindSourceStrategyOmsResult.getItemResultList();
            //检查数据
            if (cherkData(itemResultList)) {
                log.info(LogUtil.format("obeConfirmCallBackMqService.TobeConfirmCallBackService 寻源返回信息数据不正确:{}",
                        "obeConfirmCallBackMqService.TobeConfirmCallBackService"), JSON.toJSONString(itemResultList));
                OcBOrder errOrder = new OcBOrder();
                errOrder.setId(ocBOrder.getId());
                errOrder.setSysremark("orderId = " + ocBOrder.getId() + "寻源返回信息数据不正确：" + JSON.toJSONString(itemResultList));
                omsOrderService.updateOrderInfo(errOrder);
                throw new NDSException("orderId = " + ocBOrder.getId() + "寻源返回信息数据不正确：" + JSON.toJSONString(itemResultList));
            }
            if (CollectionUtils.isEmpty(itemResultList)) {
                return;
            }
            Map<Long, List<SgFindSourceStrategyOmsItemResult>> svalue =
                    itemResultList.stream().collect(Collectors.groupingBy(SgFindSourceStrategyOmsItemResult::getWareHouseId));
            if (svalue == null) {
                return;
            }
            //判断是否满足拆单
            if (svalue.size() == 1) {
                //不拆单
                executeForNoSpiltOrder(ocBOrder, ocBOrderItemList, user, message, itemResultList, svalue);
            } else {
                //拆单处理后续逻辑
                executeForSpiltOrder(ocBOrder, ocBOrderItemList, user, mapOldItem, svalue, sgOuuppyItemDistInfoListMap);
            }
        } else {
            //寻源失败
            OcBOrder lOrder = new OcBOrder();
            lOrder.setId(ocBOrder.getId());
            lOrder.setBillNo(ocBOrder.getBillNo());
            lOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            String logMsg1 = "OrderId=" + lOrder.getId() + "库存占用失败,原因为：" + sgFindSourceStrategyOmsResult.getMessage();
            lOrder.setSysremark(SplitMessageUtil.splitMsgBySize(logMsg1, 600));
            lOrder.setIsOutStock(OcBOrderConst.IS_STATUS_IY);
            //清空仓库  id为null
            if (ocBOrder.getCpCPhyWarehouseId() != null) {
                orderMapper.updateOrderEmptyWarehouseById(ocBOrder.getId());
            }
            //是否寻源失败
            lOrder.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IY);
            lOrder.setExceptionExplain(ocBOrder.getExceptionExplain());
            String msg = "库存占用失败,原因为：" + sgFindSourceStrategyOmsResult.getMessage();
            //寻源，异常单据打异常标
            orderExceptionTagService.checkMateException(lOrder, msg, OcBOrderConst.OCCUPY);
            //更新订单信息
            omsOrderService.updateOrderInfo(lOrder);
            //更新订单明细缺货数量
            stockOutLost(ocBOrderItemList);
            //记录寻源失败日志
            omsOrderLogService.addUserOrderLog(lOrder.getId(), lOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), logMsg1, "", "", user);
            //对等换货明细还原
            this.buildEqualExchange(ocBOrder, ocBOrderItemList, user);
            //新增或更新缺货订单记录
            insertOrUpdateOcBOrderOutStockRecord(ocBOrder, message);
        }
    }

    /**
     * 拆单处理后续逻辑
     *
     * @param ocBOrder
     * @param ocBOrderItemList
     * @param user
     * @param mapOldItem
     * @param svalue
     */
    private void executeForSpiltOrder(OcBOrder ocBOrder,
                                      List<OcBOrderItem> ocBOrderItemList,
                                      User user, Map<Long, OcBOrderItem> mapOldItem,
                                      Map<Long, List<SgFindSourceStrategyOmsItemResult>> svalue,
                                      Map<Long, List<SgOccupyItemDistInfo>> sgOuuppyItemDistInfoListMap) {
        log.info("orderId={}订单寻源结束！开始占单拆单", ocBOrder.getId());
        StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());
        String isPlatformSplit = Optional.ofNullable(shopStrategy.getIsPlatformSplit()).orElse("Y");
        List<OcBOrderRelation> newOcBOrderRelationList = new ArrayList<>();
        List<OcBOrderRelation> newOcBOrderRelationListBySticker = new ArrayList<>();
        //未拆分的组合商品
        List<OcBOrderItem> groupItems = ocBOrderItemList.stream().filter(p -> p.getProType() != null && p.getProType() == SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        Map<String, OcBOrderItem> groupMap = new HashMap<>();
        Map<String, List<OcBOrderItem>> subGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(groupItems)) {
            groupMap = groupItems.stream().collect(Collectors.toMap(OcBOrderItem::getGroupGoodsMark, Function.identity(), (key1, key2) -> key2));
            List<OcBOrderItem> itemList = ocBOrderItemList.stream().filter(p -> p.getProType() != null && (p.getProType() == SkuType.COMBINE_PRODUCT
                    || p.getProType() == SkuType.GIFT_PRODUCT)).collect(Collectors.toList());
            subGroupMap = itemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
        }
        for (Long aLong : svalue.keySet()) {
            List<OcBOrderEqualExchangeItem> exchangeItems1 = new ArrayList<>();
            OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
            // 构建订单头信息
            OcBOrder newOcBOrder = new OcBOrder();
            BeanUtils.copyProperties(ocBOrder, newOcBOrder);
            setOrderEncryptionCodeDate(newOcBOrder, ocBOrder);
            List<SgFindSourceStrategyOmsItemResult> itemResults = svalue.get(aLong);
            //更新状态
            setOrderColumn(newOcBOrder, itemResults);
            newOcBOrder.setSplitReason(SplitReason.SPLIT_BY_INVENTORY);
            List<OcBOrderItem> orderItemList = new ArrayList<>();
            List<OcBOrderItemExt> orderItemExtList = new ArrayList<>();
            List<OcBOrderItem> oldOrderItemList = new ArrayList<>();
            for (SgFindSourceStrategyOmsItemResult itemResult : itemResults) {
                OcBOrderItem ocBOrderItem = mapOldItem.get(itemResult.getSourceItemId());
                List<SgOccupyItemDistInfo> skuItemS2LList = sgOuuppyItemDistInfoListMap.get(itemResult.getSourceItemId());
                // 构建
                oldOrderItemList.add(ocBOrderItem);
                // 构建订单明细信息
                OcBOrderItem newOrderItem = new OcBOrderItem();
                BeanUtils.copyProperties(ocBOrderItem, newOrderItem);
                if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(newOcBOrder.getOrderStatus())) {
                    newOrderItem.setQtyLost(itemResult.getQtyPreOut());
                } else {
                    newOrderItem.setQtyLost(BigDecimal.ZERO);
                }
                //加入组合商品实际编码
                newOrderItem.setOldSourceItemId(itemResult.getSourceItemId());
                //通过数量计算金额
                newOrderItem.setQty(itemResult.getQtyPreOut());
                splitBeforeSourcingStService.computeAmtItem(newOrderItem, ocBOrderItem);
                orderItemList.add(newOrderItem);

                if (CollectionUtils.isNotEmpty(skuItemS2LList)) {
                    OcBOrderItemExt ocBOrderItemExt = ocBOrderItemExtService.queryByOrderItemId(itemResult.getSourceItemId());
                    OcBOrderItemExt newOcBOrderItemExt = new OcBOrderItemExt();
                    BeanUtils.copyProperties(ocBOrderItemExt, newOcBOrderItemExt);
                    newOcBOrderItemExt.setId(sequenceUtil.buildOrderItemExtSequenceId());
                    newOcBOrderItemExt.setOrderItemId(newOrderItem.getId());
                    newOcBOrderItemExt.setOcBOrderId(newOcBOrder.getId());
                    BaseModelUtil.initialBaseModelSystemField(newOcBOrderItemExt);
                    orderItemExtList.add(newOcBOrderItemExt);
                }
            }
            //判断组合商品是否拆分 如果拆分则变成普通商品
            Map<String, List<OcBOrderItem>> orderItemGroupMark =
                    orderItemList.stream().filter(p -> StringUtils.isNotEmpty(p.getGroupGoodsMark()))
                            .collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
            boolean orderIsGroup = false;
            for (String groupGoodsMark : orderItemGroupMark.keySet()) {
                List<OcBOrderItem> collect = orderItemGroupMark.get(groupGoodsMark);
                boolean group = isGroup(subGroupMap, collect);
                if (group) {
                    //按照组合商品标记拆未拆分组合商品订单明细
                    OcBOrderItem orderItem = groupMap.get(groupGoodsMark);
                    if (orderItem != null) {
                        orderItemList.add(orderItem);
                    }
                    orderIsGroup = true;
                }
                if (!group && !groupMap.isEmpty()) {
                    List<Long> normalProItems = collect.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
                    for (OcBOrderItem item : orderItemList) {
                        if (normalProItems.contains(item.getId())) {
                            Long proType = item.getProType();
                            if (proType != null && proType == SkuType.COMBINE_PRODUCT) {
                                item.setProType((long) SkuType.NORMAL_PRODUCT);
                            }
                        }
                    }
                }
            }
            if (orderIsGroup) {
                newOcBOrder.setIsCombination(1);
            } else {
                newOcBOrder.setIsCombination(0);
            }

            // 设置新单子是否有对等换货
            List<OcBOrderItem> equalExchangeItems =
                    oldOrderItemList.stream().filter(p -> p.getIsEqualExchange() != null && p.getIsEqualExchange() == 1).collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(equalExchangeItems)) {
                newOcBOrder.setIsEqualExchange(1);
            } else {
                newOcBOrder.setIsEqualExchange(0);
            }
            List<OcBOrderEqualExchangeItem> exchangeItems =
                    ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemList(Collections.singletonList(ocBOrder.getId()));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(exchangeItems)) {
                for (OcBOrderEqualExchangeItem exchangeItem : exchangeItems) {
                    OcBOrderEqualExchangeItem item = new OcBOrderEqualExchangeItem();
                    BeanUtils.copyProperties(exchangeItem, item);
                    long l = sequenceUtil.buildEqualExchangeItemSequenceId();
                    item.setId(l);
                    item.setOcBOrderId(newOcBOrder.getId());
                    exchangeItems1.add(item);
                }
            }

            ocBOrderRelation.setOrderInfo(newOcBOrder);
            ocBOrderRelation.setOrderItemList(orderItemList);
            ocBOrderRelation.setExchangeItems(exchangeItems1);
            ocBOrderRelation.setOcBOrderItemExtList(orderItemExtList);
            newOcBOrderRelationList.add(ocBOrderRelation);
        }
        if (CollectionUtils.isEmpty(newOcBOrderRelationList)) {
            return;
        }
        List<OcBOrderRelation> addOcBOrderRelationList = new ArrayList<>();
        //逻辑占用单构造
        SgOmsSplitOrderRequest sgOmsSplitOrderRequest = buildSgOmsSplitOrderRequest(ocBOrder);
        List<SgOmsSplitSubOrderRequest> sgOmsSplitSubOrderRequestList = new ArrayList<>();
        log.info("生成的新的单据信息集合为{}，开始处理金额重算，加入占单中间表", JSONObject.toJSONString(newOcBOrderRelationList));
        //自定义拆单赋值
        omsOrderSplitReasonUtil.setCustomReason(newOcBOrderRelationList);
        int suffixInfo = 0;
        //是否是京东拆单
        if (PlatFormEnum.JINGDONG.getCode().equals(ocBOrder.getPlatform())
                && !"手工新增".equals(ocBOrder.getOrderSource()) && R3CommonResultConstants.VALUE_Y.equals(isPlatformSplit)) {
            try {
                for (OcBOrderRelation relation : newOcBOrderRelationList) {
                    orderAmountUtil.recountOrderAmount(relation);
                    suffixInfo++;
                    relation.getOrderInfo().setSuffixInfo(relation.getOrderId() + "-OUTOFSTOCK-SP-" + suffixInfo);
                    OrderTagUtil.orderTags(relation);
                }
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                ocBOrder.setIsOutStock(0);
                //是否寻源失败
                ocBOrder.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IN);
                //去除订单异常标签
                ocBOrder.setIsException(OcBOrderConst.IS_STATUS_IN);
                ocBOrder.setExceptionType("");
                ocBOrder.setExceptionExplain("");
                omsOrderJdSplitService.splitOrderByJingdong(ocBOrder, newOcBOrderRelationList, user);
                String logMsg = "OrderId=" + ocBOrder.getId() + "调用京东拆单接口成功！";
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), logMsg, "", "", user);
            } catch (Exception e) {
                String logMsg = "OrderId=" + ocBOrder.getId() + "调用京东拆单接异常！原因是：" + e.getMessage();
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), logMsg, "", "", user);
                throw new NDSException(e.getMessage());
            }
        } else {
            int num = 0;
            //生成新的订单
            List<OcBOrderNaiKa> orderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(ocBOrder.getId());
            for (OcBOrderRelation relation : newOcBOrderRelationList) {
                num++;
                orderAmountUtil.recountOrderAmount(relation);
                suffixInfo++;
                relation.getOrderInfo().setSuffixInfo(relation.getOrderId() + "-OUTOFSTOCK-SP-" + suffixInfo);
                OrderTagUtil.orderTags(relation);
                //埋点占用成功
                relation.getOrderInfo().setOccupySuccessDate(new Date());
                //是否寻源失败
                relation.getOrderInfo().setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IY);
                if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(relation.getOrderInfo().getOrderStatus())) {
                    //是否寻源失败
                    relation.getOrderInfo().setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IN);
                    //去除订单异常标签
                    relation.getOrderInfo().setIsException(OcBOrderConst.IS_STATUS_IN);
                    relation.getOrderInfo().setExceptionType("");
                    relation.getOrderInfo().setExceptionExplain("");
                    if (sgOccupiedInventoryService.isO2OOrder(relation.getOrderInfo())) {
                        relation.getOrderInfo().setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
                    } else {
                        relation.getOrderInfo().setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
                    }
                }
                //邮费分在第一个
                if (num != 1) {
                    relation.getOrderInfo().setShipAmt(BigDecimal.ZERO);
                }
                List<OcBOrderItem> orderItemList = relation.getOrderItemList();
                OcBOrderRelation newOcBOrderRelation = omsOrderAdvanceDetentionService.saveOrder(relation);
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.OCCUPY_SUCCESS_DATE, new Date(), relation.getOrderInfo().getId(), user);
                log.info("orderId={}订单寻源结束！开始后续业务", ocBOrder.getId());
                sgOccupiedInventoryService.occupyOtherService(newOcBOrderRelation, SystemUserResource.getRootUser());
                //暂存，用于库存平移之后贴纸方案
                newOcBOrderRelationListBySticker.add(newOcBOrderRelation);
                //新单据
                OcBOrder newOrder = newOcBOrderRelation.getOrderInfo();
                Long mainId = newOrder.getId();
                List<OcBOrderNaiKa> ocBOrderNaiKaList = handleNaika(orderNaiKas, orderItemList, mainId);
                if (CollectionUtils.isNotEmpty(ocBOrderNaiKaList)) {
                    ocBOrderNaiKaMapper.batchInsert(ocBOrderNaiKaList);
                }
                List<OcBOrderEqualExchangeItem> exchangeItems1 = relation.getExchangeItems();
                if (CollectionUtils.isNotEmpty(exchangeItems1)) {
                    for (OcBOrderEqualExchangeItem exchangeItem : exchangeItems1) {
                        exchangeItem.setOcBOrderId(mainId);
                    }
                    ocBOrderEqualExchangeItemMapper.batchInsert(exchangeItems1);
                }

                String logMsg1 = "OrderId=" + newOrder.getId() + "缺货拆单,原单id=" + ocBOrder.getId();
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.LACK_RE_SPLIT.getKey(), logMsg1, "", "", user);
                // 拆单后加入新的占单中间表
                //createToBeConfirmedTask(newOrder.getId());
                if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(newOrder.getOrderStatus())) {
                    omsOccupyTaskService.addOcBOccupyTask(newOrder, null);
                }
                //新明细
                List<OcBOrderItem> newOrderItem = newOcBOrderRelation.getOrderItemList();
                if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(newOrder.getOrderStatus())) {
                    //预下沉解析
                    orderAdvanceSinkParse(newOcBOrderRelation);
                    SgOmsSplitSubOrderRequest sgOmsSplitSubOrderRequest = buildSgOmsSplitSubOrderRequest(newOrder);
                    List<SgOmsSplitSubOrderItemRequest> sgOmsSplitSubOrderItemRequestList = new ArrayList<>();
                    //解析明细传给sg逻辑占用单
                    for (OcBOrderItem ocBOrderItem : newOrderItem) {
                        //排出组合商品虚拟条码
                        if (ocBOrderItem.getProType() != null && ocBOrderItem.getProType() == SkuType.NO_SPLIT_COMBINE) {
                            continue;
                        }
                        SgOmsSplitSubOrderItemRequest sgOmsSplitSubOrderItemRequest = buildSgOmsSplitSubOrderItemRequest(ocBOrderItem);
                        sgOmsSplitSubOrderItemRequestList.add(sgOmsSplitSubOrderItemRequest);
                    }
                    sgOmsSplitSubOrderRequest.setOrderItemList(sgOmsSplitSubOrderItemRequestList);
                    sgOmsSplitSubOrderRequestList.add(sgOmsSplitSubOrderRequest);
                }
                addOcBOrderRelationList.add(newOcBOrderRelation);
            }
            //先作废原单
            ocBOrder.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
            ocBOrder.setSysremark("");
            omsOrderService.updateOrderInfo(ocBOrder);
            log.info("占单拆单结束！作废原单流程完成，新单传给库存做逻辑占用处理");
            sgOmsSplitOrderRequest.setSubOrderList(sgOmsSplitSubOrderRequestList);
            log.info("回传给库存进行拆单的数据为:{}", JSON.toJSONString(sgOmsSplitOrderRequest));
            //调用逻辑占用单方法
            ValueHolderV14 v14 = sgRpcService.sgCallBackSplitOrder(sgOmsSplitOrderRequest);
            if (v14.isOK()) {
                String logMsg = "OrderId=" + ocBOrder.getId() + "调用回传库存拆单接口成功！";
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), logMsg, "", "", user);

                // 库存中心回传拆单成功后，异步处理分物流和审核任务
                for (OcBOrderRelation relation : addOcBOrderRelationList) {
                    if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(relation.getOrderInfo().getOrderStatus())) {
                        sendDistributeLogisticsAndAuditTaskMq(relation.getOrderInfo());
                    }
                }
            } else {
                String logMsg = "OrderId=" + ocBOrder.getId() + "调用回传库存接口失败！原因是:" + v14.getMessage();
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(), logMsg, "", "", user);
                throw new NDSException(v14.getMessage());
            }
            if (CollectionUtils.isNotEmpty(newOcBOrderRelationListBySticker)) {
                for (OcBOrderRelation relation : newOcBOrderRelationListBySticker) {
                    if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(relation.getOrderInfo().getOrderStatus())) {
                        OcBOrderParam param = new OcBOrderParam();
                        param.setOcBOrder(relation.getOrderInfo());
                        param.setOrderItemList(relation.getOrderItemList());
                        //贴纸方案
                        omsStickerService.handelStickerService(param, SystemUserResource.getRootUser());
                        //溯源策略
                        stCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService(param, SystemUserResource.getRootUser());
                    }
                }
            }
        }
    }

    /**
     * 不需要拆单处理后续逻辑
     *
     * @param ocBOrder
     * @param ocBOrderItemList
     * @param user
     * @param message
     * @param itemResultList
     * @param svalue
     */
    private void executeForNoSpiltOrder(OcBOrder ocBOrder,
                                        List<OcBOrderItem> ocBOrderItemList,
                                        User user, String message,
                                        List<SgFindSourceStrategyOmsItemResult> itemResultList,
                                        Map<Long, List<SgFindSourceStrategyOmsItemResult>> svalue) {
        log.info("orderId={}订单寻源结束！开始占单不拆单", ocBOrder.getId());
        stockOutLost(ocBOrderItemList);
        //加入后续业务集合
        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
        ocBOrderRelation.setOrderInfo(ocBOrder);
        ocBOrderRelation.setOrderItemList(ocBOrderItemList);
        log.info("orderId={}订单寻源结束！开始后续业务", ocBOrder.getId());
        sgOccupiedInventoryService.occupyOtherService(ocBOrderRelation, SystemUserResource.getRootUser());
        OcBOrderParam param = new OcBOrderParam();
        param.setOcBOrder(ocBOrderRelation.getOrderInfo());
        setOrderColumn(param.getOcBOrder(), itemResultList);
        param.setOrderItemList(ocBOrderRelation.getOrderItemList());
        //贴纸方案
        omsStickerService.handelStickerService(param, SystemUserResource.getRootUser());
        //溯源策略
        stCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService(param, SystemUserResource.getRootUser());
        //根据是否缺货按不同逻辑处理，缺货就重新占，不缺货就进入待审核状态
        restockOutStock(ocBOrder, user, itemResultList, ocBOrderRelation, message);
    }

    /**
     * 新增或更新缺货订单记录
     *
     * @param ocBOrder
     * @param message
     * @return
     */
    private OcBOrderOutStockRecord insertOrUpdateOcBOrderOutStockRecord(OcBOrder ocBOrder, String message) {
        // 先查询之前是否已存在 如果已存在 则更新
        List<OcBOrderOutStockRecord> ocBOrderOutStockRecordList = outStockRecordMapper.selectByOcBOrderId(ocBOrder.getId());
        if (CollectionUtils.isNotEmpty(ocBOrderOutStockRecordList)) {
            OcBOrderOutStockRecord updateOcBOrderOutStockRecord = new OcBOrderOutStockRecord();
            OcBOrderOutStockRecord ocBOrderOutStockRecord = ocBOrderOutStockRecordList.get(0);
            updateOcBOrderOutStockRecord.setId(ocBOrderOutStockRecord.getId());
            updateOcBOrderOutStockRecord.setOcBOrderId(ocBOrderOutStockRecord.getOcBOrderId());
            updateOcBOrderOutStockRecord.setOutstockDate(new Date());
            updateOcBOrderOutStockRecord.setErrorMsg(message);
            updateOcBOrderOutStockRecord.setModifieddate(new Date());
            outStockRecordMapperService.updateById(updateOcBOrderOutStockRecord);
            return updateOcBOrderOutStockRecord;
        }

        OcBOrderOutStockRecord outStockRecord = new OcBOrderOutStockRecord();
        outStockRecord.setId(sequenceUtil.buildOcBOrderOutStockRecordId());
        outStockRecord.setOcBOrderId(ocBOrder.getId());
        outStockRecord.setTid(ocBOrder.getTid());
        outStockRecord.setOutstockDate(new Date());
        outStockRecord.setErrorMsg(message);
        BaseModelUtil.initialBaseModelSystemField(outStockRecord);
        outStockRecordMapper.insert(outStockRecord);
        return outStockRecord;
    }

    private List<OcBOrderNaiKa> handleNaika(List<OcBOrderNaiKa> orderNaiKas, List<OcBOrderItem> orderItemList, Long mainId) {
        if (CollectionUtils.isEmpty(orderNaiKas)) {
            return null;
        }
        List<OcBOrderNaiKa> naiKaList = new ArrayList<>();
        List<OcBOrderNaiKa> ocBOrderNaiKaList = orderNaiKas.stream().filter(p -> p.getOcBOrderItemId() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
            for (OcBOrderNaiKa naiKa : orderNaiKas) {
                OcBOrderNaiKa orderNaiKa = new OcBOrderNaiKa();
                BeanUtils.copyProperties(naiKa, orderNaiKa);
                orderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                orderNaiKa.setOcBOrderId(mainId);
                naiKaList.add(orderNaiKa);
            }
            return naiKaList;
        }
        Map<Long, List<OcBOrderNaiKa>> naikaMap = ocBOrderNaiKaList.stream().collect(Collectors.groupingBy(OcBOrderNaiKa::getOcBOrderItemId));

        for (OcBOrderItem orderItem : orderItemList) {
            Long id = orderItem.getId();
            List<OcBOrderNaiKa> naiKaList1 = naikaMap.get(id);
            if (CollectionUtils.isEmpty(naiKaList1)) {
                continue;
            }
            for (OcBOrderNaiKa naiKa : naiKaList1) {
                OcBOrderNaiKa orderNaiKa = new OcBOrderNaiKa();
                BeanUtils.copyProperties(naiKa, orderNaiKa);
                orderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                orderNaiKa.setOcBOrderId(mainId);
                naiKaList.add(orderNaiKa);
            }
        }
        return naiKaList;
    }


    private boolean isGroup(Map<String, List<OcBOrderItem>> subGroupMap, List<OcBOrderItem> collect) {
        if (CollectionUtils.isNotEmpty(collect)) {
            Map<String, List<OcBOrderItem>> stringListMap = collect.stream().collect(Collectors.groupingBy(OcBOrderItem::getGroupGoodsMark));
            for (String s : stringListMap.keySet()) {
                List<OcBOrderItem> itemList = stringListMap.get(s);
                List<OcBOrderItem> itemList1 = subGroupMap.get(s);
                if (CollectionUtils.isEmpty(itemList) || CollectionUtils.isEmpty(itemList1)) {
                    return false;
                }
                //判断是否一样 数量一样  就判断数量是否一样
                if (itemList.size() == itemList1.size()) {
                    Map<Long, OcBOrderItem> orderItemMap = itemList.stream().collect(Collectors.toMap(OcBOrderItem::getId, Function.identity(), (key1, key2) -> key2));
                    for (OcBOrderItem item : itemList1) {
                        OcBOrderItem item1 = orderItemMap.get(item.getId());
                        if (item1 == null) {
                            return false;
                        }
                        BigDecimal qty = item.getQty();
                        BigDecimal qty1 = item1.getQty();
                        if (qty.compareTo(qty1) != 0) {
                            return false;
                        }
                    }
                } else {
                    return false;
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 对等换货明细还原
     */
    private void buildEqualExchange(OcBOrder order, List<OcBOrderItem> ocBOrderItemList, User user) {
        try {
            List<OcBOrderItem> itemList = ocBOrderItemList.stream().filter(p -> StringUtils.isNotEmpty(p.getEqualExchangeRatio())).collect(Collectors.toList());
            List<OcBOrderItem> orderItemList = ocBOrderItemList.stream().filter(p -> StringUtils.isEmpty(p.getEqualExchangeRatio())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(itemList)) {
                //查询对等换货的数据
                List<Long> itemIds = new ArrayList<>();
                List<OcBOrderEqualExchangeItem> exchangeItems = ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemListByOrderId(order.getId());
                if (CollectionUtils.isEmpty(exchangeItems)) {
                    return;
                }
                Map<String, OcBOrderEqualExchangeItem> orderItemMap = exchangeItems.stream().collect(Collectors.toMap(OcBOrderEqualExchangeItem::getEqualExchangeMark, Function.identity(), (key1, key2) -> key2));
                Map<String, List<OcBOrderItem>> itemMap = itemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getEqualExchangeMark));
                List<OcBOrderItem> orderItems = new ArrayList<>();
                List<OcBOrderItemExt> ocBOrderItemExtList = new ArrayList<>();
                List<OcBOrderNaiKa> ocBOrderNaiKaList = new ArrayList<>();
                List<OcBOrderNaiKa> ocBOrderNaiKaDeleteList = new ArrayList<>();
                List<OcBOrderNaiKa> orderNaiKas = ocBOrderNaiKaMapper.selectNaiKaByOcBOrderId(order.getId());
                Boolean restore = false;
                //缺货不还原明细id记录
                List<Long> restoreIds = Lists.newArrayList();
                for (String mark : itemMap.keySet()) {
                    List<OcBOrderItem> ocBOrderItems = itemMap.get(mark);
                    //计算原数量是多少
                    Long itemId = ocBOrderItems.get(0).getId();
                    BigDecimal qtyCount = BigDecimal.ZERO;
                    for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                        BigDecimal qty = ComputeEqualExchangeQtyUtil.computeQty(ocBOrderItem);
                        qtyCount = qtyCount.add(qty);
                        itemIds.add(ocBOrderItem.getId());
                    }
                    OcBOrderEqualExchangeItem equalExchangeItem = orderItemMap.get(mark);
                    if (equalExchangeItem == null) {
                        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "对等换货订单缺货还原失败", "", "", user);
                        return;
                    }

                    if (equalExchangeItem.getOutStockNoRestore() != null && ObjectUtil.equal("1", equalExchangeItem.getOutStockNoRestore())) {
                        restore = true;
                        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                            OcBOrderItem newOrderItem = new OcBOrderItem();
                            BeanUtils.copyProperties(ocBOrderItem, newOrderItem);
                            long orderItemId = sequenceUtil.buildOrderItemSequenceId();
                            newOrderItem.setId(orderItemId);
                            newOrderItem.setCreationdate(new Date());
                            newOrderItem.setModifieddate(new Date());
                            orderItems.add(newOrderItem);

                            restoreIds.add(orderItemId);
                            OcBOrderItemExt oldItemExt = ocBOrderItemExtService.queryByOrderItemId(ocBOrderItem.getId());

                            OcBOrderItemExt newItemExt = new OcBOrderItemExt();
                            BeanUtils.copyProperties(oldItemExt, newItemExt);
                            newItemExt.setId(sequenceUtil.buildOrderItemExtSequenceId());
                            newItemExt.setOrderItemId(orderItemId);
                            newItemExt.setCreationdate(new Date());
                            newItemExt.setModifieddate(new Date());
                            ocBOrderItemExtList.add(newItemExt);
                        }
                        List<OcBOrderNaiKa> naiKaList = orderNaiKas.stream().filter(p -> p.getOcBOrderItemId() != null && p.getOcBOrderItemId().equals(itemId)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(naiKaList)) {
                            ocBOrderNaiKaDeleteList.addAll(naiKaList);
                            for (OcBOrderNaiKa ocBOrderNaiKa : naiKaList) {
                                OcBOrderNaiKa newOcBOrderNaika = new OcBOrderNaiKa();
                                BeanUtils.copyProperties(ocBOrderNaiKa, newOcBOrderNaika);
                                newOcBOrderNaika.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                                newOcBOrderNaika.setCreationdate(new Date());
                                newOcBOrderNaika.setModifieddate(new Date());
                                ocBOrderNaiKaList.add(newOcBOrderNaika);
                            }
                        }
                        exchangeItems.remove(equalExchangeItem);
                        continue;
                    }

                    BigDecimal qty = equalExchangeItem.getQty();
                    BigDecimal realAmt = equalExchangeItem.getRealAmt() == null ? BigDecimal.ZERO : equalExchangeItem.getRealAmt();
                    BigDecimal adjustAmt = equalExchangeItem.getAdjustAmt() == null ? BigDecimal.ZERO : equalExchangeItem.getAdjustAmt();
                    BigDecimal amtDiscount = equalExchangeItem.getAmtDiscount() == null ? BigDecimal.ZERO : equalExchangeItem.getAmtDiscount();
                    BigDecimal orderSplitAmt = equalExchangeItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : equalExchangeItem.getOrderSplitAmt();

                    OcBOrderItem item = new OcBOrderItem();
                    BeanUtils.copyProperties(equalExchangeItem, item);
                    item.setQty(qtyCount);
                    item.setOcBOrderId(order.getId());
                    item.setIsExchangeItem(0);
                    item.setRealAmt(realAmt.divide(qty, 10, BigDecimal.ROUND_HALF_UP).multiply(qtyCount).setScale(4, BigDecimal.ROUND_HALF_UP));
                    item.setPriceActual(item.getRealAmt().divide(qtyCount, 4, BigDecimal.ROUND_HALF_UP));
                    item.setAdjustAmt(adjustAmt.divide(qty, 10, BigDecimal.ROUND_HALF_UP).multiply(qtyCount).setScale(4, BigDecimal.ROUND_HALF_UP));
                    item.setAmtDiscount(amtDiscount.divide(qty, 10, BigDecimal.ROUND_HALF_UP).multiply(qtyCount).setScale(4, BigDecimal.ROUND_HALF_UP));
                    item.setOrderSplitAmt(orderSplitAmt.divide(qty, 10, BigDecimal.ROUND_HALF_UP).multiply(qtyCount).setScale(4, BigDecimal.ROUND_HALF_UP));

                    item.setEqualExchangeRatio("");
                    item.setEqualExchangeMark("");
                    item.setProType(ocBOrderItems.get(0).getProType());
                    item.setId(sequenceUtil.buildOrderItemSequenceId());
                    orderItems.add(item);
                    List<OcBOrderNaiKa> orderNaiKas1 = this.handleOrderNaika(itemId, orderNaiKas, item);
                    if (CollectionUtils.isNotEmpty(orderNaiKas1)) {
                        ocBOrderNaiKaList.addAll(orderNaiKas1);
                    }
                }

                OcBOrderParam param = new OcBOrderParam();
                OcBOrder ocBOrder = new OcBOrder();
                ocBOrder.setId(order.getId());
                if (!restore) {
                    ocBOrder.setSysremark("");
                    ocBOrder.setIsEqualExchange(0);
                }
                param.setOcBOrder(ocBOrder);

                orderItemList.addAll(orderItems);
                param.setOrderItemList(orderItemList);
                //计算金额
                orderAmountUtil.recountOrderAmountResert(param, restoreIds);
                tobeConfirmCallBackMqService.saveOcBOrderItem(itemList, orderItems, exchangeItems, ocBOrder, itemIds, ocBOrderNaiKaList, ocBOrderNaiKaDeleteList, ocBOrderItemExtList);
                if (!restore) {
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "对等换货订单缺货还原成功", "", "", user);
                } else {
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "对等换货缺货不还原", "", "", user);
                    // 如果缺货不还原的话 不再走寻源前拆单策略 然后寻源的时间增加随机数 之后结束
                    Random random = new Random();
                    int randomNumber = random.nextInt(11) + 40;
                    omsOccupyTaskService.addOcBOccupyOutStockTask(order, randomNumber);
                    return;
                }
                try {
                    OcBOrder order1 = orderMapper.selectById(order.getId());
                    List<OcBOrderItem> bOrderItemList = ocBOrderItemMapper.selectOrderItemListOccupy(order.getId());
                    OcBOrderParam ocBOrderParam = new OcBOrderParam();
                    ocBOrderParam.setOcBOrder(order1);
                    ocBOrderParam.setOrderItemList(bOrderItemList);
                    splitBeforeSourcingStService.splitBeforeSourcingStrategy(ocBOrderParam, user, null, null, false, false);
                } catch (Exception e) {
                    log.error(LogUtil.format("对等换货订单缺货还原成功,拆单失败:{}", "对等换货订单缺货还原成功,拆单失败", order.getId()), Throwables.getStackTraceAsString(e));
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.STRATEGY_SPLIT.getKey(), "对等换货订单缺货还原成功,拆单失败", "", "", user);
                }
            } else {
                Random random = new Random();
                int randomNumber = random.nextInt(11) + 40;
                omsOccupyTaskService.addOcBOccupyOutStockTask(order, randomNumber);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("对等换货订单缺货还原失败:{}", "对等换货订单缺货还原失败", order.getId()), Throwables.getStackTraceAsString(e));
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.EQUAL_EXCHANGE.getKey(), "对等换货订单缺货还原失败", "", "", user);

        }
    }

    public List<OcBOrderNaiKa> handleOrderNaika(Long itemId, List<OcBOrderNaiKa> orderNaiKas, OcBOrderItem item) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderNaiKas)) {
            return null;
        }
        List<OcBOrderNaiKa> naiKaList = orderNaiKas.stream().filter(p -> p.getOcBOrderItemId() != null && p.getOcBOrderItemId().equals(itemId)).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isEmpty(naiKaList)) {
            return null;
        }
        List<OcBOrderNaiKa> orderNaiKaList = new ArrayList<>();
        for (OcBOrderNaiKa orderNaiKa : naiKaList) {
            OcBOrderNaiKa naiKa = new OcBOrderNaiKa();
            BeanUtils.copyProperties(orderNaiKa, naiKa);
            naiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
            naiKa.setOcBOrderItemId(item.getId());
            naiKa.setPsCProEcode(item.getPsCProEcode());
            naiKa.setPsCProEname(item.getPsCProEname());
            naiKa.setPsCProId(item.getPsCProId());
            naiKa.setPsCSkuEcode(item.getPsCSkuEcode());
            naiKa.setPsCSkuEname(item.getPsCSkuEname());
            naiKa.setPsCSkuId(item.getPsCSkuId());
            naiKa.setSkuSpec(item.getSkuSpec());
            orderNaiKaList.add(naiKa);
        }
        return orderNaiKaList;
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveOcBOrderItem(List<OcBOrderItem> delItemList,
                                 List<OcBOrderItem> addItem,
                                 List<OcBOrderEqualExchangeItem> exchangeItems,
                                 OcBOrder order,
                                 List<Long> itemIds,
                                 List<OcBOrderNaiKa> ocBOrderNaiKaList,
                                 List<OcBOrderNaiKa> ocBOrderNaiKaDeleteList,
                                 List<OcBOrderItemExt> ocBOrderItemExtList) {
        List<Long> delItem = delItemList.stream().map(OcBOrderItem::getId).collect(Collectors.toList());
        List<Long> delExchangeItemIds = exchangeItems.stream().map(OcBOrderEqualExchangeItem::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(delItem)) {
            ocBOrderItemMapper.deleteOcBOrderItemById(order.getId(), delItem);
        }
        if (CollectionUtils.isNotEmpty(delExchangeItemIds)) {
            ocBOrderEqualExchangeItemMapper.delOcBOrderEqualExchangeItem(order.getId(), delExchangeItemIds);
        }
        if (CollectionUtils.isNotEmpty(addItem)) {
            ocBOrderItemMapper.batchInsert(addItem);
        }
        orderMapper.updateById(order);
        if (CollectionUtils.isNotEmpty(ocBOrderNaiKaList)) {
            ocBOrderNaiKaMapper.batchInsert(ocBOrderNaiKaList);
            if (CollectionUtils.isNotEmpty(ocBOrderNaiKaDeleteList)) {
                ocBOrderNaiKaMapper.deleteBatchIds(ocBOrderNaiKaDeleteList.stream().map(OcBOrderNaiKa::getId).collect(Collectors.toList()));
            } else {
                ocBOrderNaiKaMapper.deleteNaiKaListByOrderId(itemIds, order.getId());
            }
        }
        if (CollectionUtils.isNotEmpty(ocBOrderItemExtList)) {
            ocBOrderItemExtService.insertList(ocBOrderItemExtList);
        }

    }

    /**
     * <AUTHOR>
     * @Date 22:21 2021/8/5
     * @Description 校验回传mq校验
     */
    private boolean cherkData(List<SgFindSourceStrategyOmsItemResult> itemResultList) {
        for (SgFindSourceStrategyOmsItemResult sgFindSourceStrategyOmsItemResult : itemResultList) {
            //数量小于0的
            if (BigDecimal.ZERO.compareTo(sgFindSourceStrategyOmsItemResult.getQtyPreOut()) > 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * description:预下沉解析
     *
     * @Author: liuwenjin
     * @Date 2021/10/10 5:45 下午
     */
    private void orderAdvanceSinkParse(OcBOrderRelation orderInfo) {
        //执行定金预售预下沉服务
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("占单分物流结束后开始执行定金预售预下沉策略,STATUS_PAY_STEP={}",
                    orderInfo.getOrderId()), orderInfo.getOrderInfo().getStatusPayStep());
        }
        if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(orderInfo.getOrderInfo().getStatusPayStep())) {
            omsOrderAdvanceParseService.OmsOrderAdvanceSinkParse(orderInfo, SystemUserResource.getRootUser());
        }
    }

    /**
     * <AUTHOR>
     * @Date 15:10 2021/7/14
     * @Description 判断是否缺货
     */
    private void restockOutStock(OcBOrder ocBOrder, User user, List<SgFindSourceStrategyOmsItemResult> itemResultList,
                                 OcBOrderRelation ocBOrderRelation, String message) {

        OcBOrder bOrder = new OcBOrder();
        bOrder.setId(ocBOrder.getId());
        bOrder.setCpCShopId(ocBOrder.getCpCShopId());
        bOrder.setBillNo(ocBOrder.getBillNo());
        bOrder.setPlatform(ocBOrder.getPlatform());
        bOrder.setIsExpress(ocBOrder.getIsExpress());
        //赋值合单需要的字段
        setOrderEncryptionCodeDate(bOrder, ocBOrder);
        setOrderColumn(bOrder, itemResultList);
        //指定物流字段赋值
        bOrder.setAppointLogisticsId(ocBOrder.getAppointLogisticsId());
        bOrder.setAppointLogisticsEcode(ocBOrder.getAppointLogisticsEcode());
        bOrder.setAppointLogisticsEname(ocBOrder.getAppointLogisticsEname());
        //判断是否是缺货
        if (OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(bOrder.getOrderStatus())) {
            log.info(LogUtil.format("缺货占单失败！继续走寻源", ocBOrder.getId()));
            bOrder.setSysremark(ocBOrder.getId() + "订单缺货");
            //首次缺货埋点
            if (Objects.isNull(ocBOrder.getStockOccupyDate())) {
                bOrder.setStockOccupyDate(new Date());
                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.STOCK_OCCUPY_DATE, new Date(), bOrder.getId(), user);
            }
            if (ocBOrder.getCpCPhyWarehouseId() != null) {
                orderMapper.updateOrderEmptyWarehouseById(ocBOrder.getId());
            }
            bOrder.setIsOutStock(1);
            //寻源失败标识
            bOrder.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IY);
            omsOrderService.updateOrderInfo(bOrder);
            //寻源，异常单据打异常标 20230222 缺失标记bug修复
            orderExceptionTagService.checkMateException(bOrder, message, OcBOrderConst.OCCUPY);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(),
                    "OrderId=" + ocBOrder.getId() + "库存不足！缺货!!原因:" + message, "", "", user);
            this.buildEqualExchange(ocBOrder, ocBOrderRelation.getOrderItemList(), user);
        } else {
            log.info(LogUtil.format("更新状态为订单信息整理！占单完成！流程继续走", ocBOrder.getId()));
            bOrder.setSysremark("");
            omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
            //1、若【零售发货单】的【来源平台】=POS时，则对订单打标【o2o】；
            //2、若【零售发货单】的【来源平台】非POS时，若订单占用的【发货仓库】的对应【仓库类型】=门店，则对订单打标【o2o】；
            bOrder.setPlatform(ocBOrder.getPlatform());
            if (sgOccupiedInventoryService.isO2OOrder(bOrder)) {
                bOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
            } else {
                bOrder.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
            }
            //埋点占用成功
            bOrder.setOccupySuccessDate(new Date());
            bOrder.setIsOutStock(0);
            //寻源失败标识
            bOrder.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IN);
            //寻源失败标识
            bOrder.setIsException(OcBOrderConst.IS_STATUS_IN);
            bOrder.setExceptionType("");
            bOrder.setExceptionExplain("");
            omsOrderService.updateOrderInfo(bOrder);
            ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.OCCUPY_SUCCESS_DATE, new Date(), bOrder.getId(), user);
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.OCCUPY.getKey(),
                    "OrderId=" + ocBOrder.getId() + "占用库存成功,仓库编码：" + bOrder.getCpCPhyWarehouseEcode() + ",仓库名称：" + bOrder.getCpCPhyWarehouseEname(),
                    "", "", user);
            if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
                //JITX订单判断寻仓与实际占用仓库是否一致  不一致进行改仓
                if (!ObjectUtil.equals(ocBOrder.getJitxRequiresDeliveryWarehouseId(), bOrder.getCpCPhyWarehouseId())) {
                    log.info(LogUtil.format("TobeConfirmCallBackMqService.restockOutStock jitModifyWarehouse,billNo:{},jitWarehouseId:{},warehouseId:{}",
                            "TobeConfirmCallBackMqService.restockOutStock"), ocBOrder.getBillNo(), ocBOrder.getJitxRequiresDeliveryWarehouseId(), bOrder.getCpCPhyWarehouseId());
                    this.jitxModifyWarehouse(bOrder.getId(), ocBOrder.getJitxRequiresDeliveryWarehouseId(),
                            ocBOrder.getJitxRequiresDeliveryWarehouseCode(), ocBOrder.getJitxRequiresDeliveryWarehouseName());
                } else {
                    Long phyWarehouseId = ocBOrder.getCpCPhyWarehouseId();
                    Long beforeWarehouseId = -1L;
                    //如果存在YY占单任务，且无取消任务，更新YY占单任务实体仓id字段
                    List<OcBJitxDealerOrderTask> dealerOrderTasks = taskMapper
                            .selectList(new LambdaQueryWrapper<OcBJitxDealerOrderTask>()
                                    .eq(OcBJitxDealerOrderTask::getTid, ocBOrder.getTid())
                                    .eq(OcBJitxDealerOrderTask::getIsactive, R3CommonResultConstants.VALUE_Y));
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dealerOrderTasks)) {
                        List<OcBJitxDealerOrderTask> occupyList = dealerOrderTasks.stream()
                                .filter(d -> JitxDealerTaskTypeEnum.YY_OCCUPY.getCode().equals(d.getType())).collect(Collectors.toList());
                        List<OcBJitxDealerOrderTask> cancelList = dealerOrderTasks.stream()
                                .filter(d -> JitxDealerTaskTypeEnum.YY_CANCEL_OCCUPY.getCode().equals(d.getType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(occupyList) && CollectionUtils.isEmpty(cancelList)) {
                            occupyList.sort(Comparator.comparing(OcBJitxDealerOrderTask::getCreationdate));
                            beforeWarehouseId = occupyList.get(occupyList.size() - 1).getCpCPhyWarehouseId();
                        }
                    }
                    //占用YY仓库，通知YY占单
                    OcBJitxDealerOrderRelation relation = new OcBJitxDealerOrderRelation();
                    relation.setOcBOrder(ocBOrder);
                    relation.setWarehouseId(beforeWarehouseId);
                    relation.setUser(user);
                    dealerOrderTaskService.orderCancel(relation);
                    relation.setWarehouseId(phyWarehouseId);
                    dealerOrderTaskService.orderAdd(relation);
                }
            }
            //新对象需要重新赋值实体仓
            ocBOrderRelation.getOrderInfo().setCpCPhyWarehouseId(bOrder.getCpCPhyWarehouseId());
            //阶段付款状态
            ocBOrderRelation.getOrderInfo().setStatusPayStep(ocBOrder.getStatusPayStep());
            //预下沉解析
            orderAdvanceSinkParse(ocBOrderRelation);

            // 异步处理分物流和审核任务
            sendDistributeLogisticsAndAuditTaskMq(bOrder);
        }
    }

    /**
     * <AUTHOR>
     * @Date 11:24 2021/7/13
     * @Description 传给sg逻辑占用单 新单明细信息
     */
    private SgOmsSplitSubOrderItemRequest buildSgOmsSplitSubOrderItemRequest(OcBOrderItem ocBOrderItem) {
        SgOmsSplitSubOrderItemRequest sgOmsSplitSubOrderItemRequest = new SgOmsSplitSubOrderItemRequest();
        sgOmsSplitSubOrderItemRequest.setNewSourceItemId(ocBOrderItem.getId());
        sgOmsSplitSubOrderItemRequest.setOldSourceItemId(ocBOrderItem.getOldSourceItemId());
        sgOmsSplitSubOrderItemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
        sgOmsSplitSubOrderItemRequest.setQty(ocBOrderItem.getQty());
        //sgOmsSplitSubOrderItemRequest.setSgStoOutBillNo(ocBOrderItem.getStoOutBillNo());
        return sgOmsSplitSubOrderItemRequest;
    }

    /**
     * <AUTHOR>
     * @Date 11:20 2021/7/13
     * @Description 传给sg逻辑占用单 新单信息
     */
    private SgOmsSplitSubOrderRequest buildSgOmsSplitSubOrderRequest(OcBOrder newOrder) {
        SgOmsSplitSubOrderRequest sgOmsSplitSubOrderRequest = new SgOmsSplitSubOrderRequest();
        sgOmsSplitSubOrderRequest.setNewOrderBillId(newOrder.getId());
        sgOmsSplitSubOrderRequest.setNewOrderBillNo(newOrder.getBillNo());
        sgOmsSplitSubOrderRequest.setNewOrderBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        sgOmsSplitSubOrderRequest.setSgStoOutBillNo(newOrder.getStoOutBillNo());
        return sgOmsSplitSubOrderRequest;
    }

    /**
     * <AUTHOR>
     * @Date 11:20 2021/7/13
     * @Description 传给sg逻辑占用单 原单信息
     */
    private SgOmsSplitOrderRequest buildSgOmsSplitOrderRequest(OcBOrder ocBOrder) {
        SgOmsSplitOrderRequest sgOmsSplitOrderRequest = new SgOmsSplitOrderRequest();
        sgOmsSplitOrderRequest.setSourceBillId(ocBOrder.getId());
        sgOmsSplitOrderRequest.setSourceBillNo(ocBOrder.getBillNo());
        sgOmsSplitOrderRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        return sgOmsSplitOrderRequest;
    }

    /**
     * <AUTHOR>
     * @Date 18:05 2021/7/12
     * @Description 占单赋值
     */
    private void setOrderColumn(OcBOrder ocBOrder, List<SgFindSourceStrategyOmsItemResult> itemResultList) {
        SgFindSourceStrategyOmsItemResult sgFindSourceStrategyOmsItemResult = itemResultList.get(0);
        long wareHouseId = sgFindSourceStrategyOmsItemResult.getWareHouseId();
        ocBOrder.setSysremark("订单信息整理中");
        if (wareHouseId == -1) {
            ocBOrder.setCpCPhyWarehouseId(null);
            ocBOrder.setCpCPhyWarehouseEcode(null);
            ocBOrder.setCpCPhyWarehouseEname(null);
            ocBOrder.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
            ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_13);
            ocBOrder.setIsOutStock(1);
        } else {
            ocBOrder.setCpCPhyWarehouseId(wareHouseId);
            ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            ocBOrder.setCpCPhyWarehouseEcode(sgFindSourceStrategyOmsItemResult.getWareHouseEcode());
            ocBOrder.setCpCPhyWarehouseEname(sgFindSourceStrategyOmsItemResult.getWareHouseEname());
            ocBOrder.setIsOutStock(0);
            ocBOrder.setStoOutBillNo(sgFindSourceStrategyOmsItemResult.getStoOutBillNo());
            // 需要传有地址信息的订单参数
            MD5Util.encryptOrderInfo4Merge(ocBOrder);
        }
    }

    //赋值
    private void setOrderEncryptionCodeDate(OcBOrder newOcBOrder, OcBOrder oldOcBOrder) {
        newOcBOrder.setUserNick(oldOcBOrder.getUserNick());
        newOcBOrder.setCpCShopId(oldOcBOrder.getCpCShopId());
        newOcBOrder.setPlatform(oldOcBOrder.getPlatform());
        newOcBOrder.setCpCPhyWarehouseId(oldOcBOrder.getCpCPhyWarehouseId());
        newOcBOrder.setOrderType(oldOcBOrder.getOrderType());
        newOcBOrder.setReceiverName(oldOcBOrder.getReceiverName());
        newOcBOrder.setReceiverMobile(oldOcBOrder.getReceiverMobile());
        newOcBOrder.setReceiverPhone(oldOcBOrder.getReceiverPhone());
        newOcBOrder.setCpCRegionProvinceId(oldOcBOrder.getCpCRegionProvinceId());
        newOcBOrder.setCpCRegionCityId(oldOcBOrder.getCpCRegionCityId());
        newOcBOrder.setCpCRegionAreaId(oldOcBOrder.getCpCRegionAreaId());
        newOcBOrder.setReceiverAddress(oldOcBOrder.getReceiverAddress());
        newOcBOrder.setMergedCode(oldOcBOrder.getMergedCode());
        newOcBOrder.setBusinessTypeId(oldOcBOrder.getBusinessTypeId());
        newOcBOrder.setGwSourceGroup(oldOcBOrder.getGwSourceGroup());
        newOcBOrder.setSellerMemo(oldOcBOrder.getSellerMemo());
    }

    /**
     * <AUTHOR>
     * @Date 19:27 2021/7/12
     * @Description 占单结束后继续走流程
     */
    private void updateToBeConfirmedTask(Long orderId) {
        List<Long> ids = new ArrayList<>();
        ids.add(orderId);
        toBeConfirmedTaskService.updateToBeConfirmedTask(ids);
    }

    /**
     * <AUTHOR>
     * @Date 13:38 2021/7/16
     * @Description 赋值缺货数量
     */
    private void stockOutLost(List<OcBOrderItem> ocBOrderItemList) {
        List<OcBOrderItem> orderItemList = ocBOrderItemList.stream().filter(i -> i.getProType() != null && i.getProType() != SkuType.COMBINE_PRODUCT).collect(Collectors.toList());
        for (OcBOrderItem ocBOrderItem : orderItemList) {
            if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrderItem.getIsLackstock()) && ocBOrderItem.getProType() != null && ocBOrderItem.getProType() != SkuType.COMBINE_PRODUCT) {
                ocBOrderItem.setQtyLost(ocBOrderItem.getQty());
                orderItemMapper.updateById(ocBOrderItem);
            }
        }

    }

    /**
     * description：JITX订单改仓
     *
     * <AUTHOR>
     * @date 2021/9/26
     */
    public void jitxModifyWarehouse(Long orderId, Long originWarehouseId, String originWarehouseCode, String originWarehouseEname) {
        if (originWarehouseId == null) {
            throw new NDSException("唯品会要求发货仓为空！");
        }
        //重新查询最新数据
        OcBOrder orderInfo = orderMapper.selectByID(orderId);
        if (orderInfo == null) {
            return;
        }
        String jitWarehouseEcode = null;
        StCVipcomJitxWarehouse newWarehouse = vipcomJitxWarehouseService.queryJitxCapacity(orderInfo.getCpCShopId(), orderInfo.getCpCPhyWarehouseId(), null);
        AssertUtil.assertException(ObjectUtils.isEmpty(newWarehouse),
                "实体仓id[" + orderInfo.getCpCPhyWarehouseId() + "]未维护唯品会仓库对照表");
        StCVipcomJitxWarehouse originWarehouse = vipcomJitxWarehouseService.queryJitxCapacity(orderInfo.getCpCShopId(), originWarehouseId, null);
        AssertUtil.assertException(ObjectUtils.isEmpty(originWarehouse),
                "实体仓id[" + originWarehouseId + "]未维护唯品会仓库对照表");
        if (!ObjectUtil.equals(newWarehouse.getVipcomUnshopWarehouseEcode(), originWarehouse.getVipcomUnshopWarehouseEcode())) {
            jitWarehouseEcode = newWarehouse.getVipcomUnshopWarehouseEcode();
        }
        if (StringUtils.isEmpty(jitWarehouseEcode)) {
            log.info(LogUtil.format("TobeConfirmCallBackMqService.jitxModifyWarehouse 唯品会仓库编码一致无需改仓，billNo:{}",
                    "TobeConfirmCallBackMqService.jitxModifyWarehouse"), orderInfo.getBillNo());
            return;
        }
        // 创建JITX订单改仓日志表
        ValueHolderV14 v14 = ocBJitxModifyWarehouseLogService.createByOrder(
                orderInfo, false, originWarehouseId, originWarehouseCode, originWarehouseEname, jitWarehouseEcode, SystemUserResource.getRootUser());
        omsOrderLogService.addUserOrderLog(orderInfo.getId(), orderInfo.getBillNo(),
                OrderLogTypeEnum.JITX_ORDER_UPDATE_WAREHOUSE.getKey(),
                String.format("退回改仓,创建JITX订单改仓中间表:%s", v14.getMessage()), "", v14.getMessage(), SystemUserResource.getRootUser());
    }

    /**
     * 执行分物流和审核任务
     *
     * @param order 订单信息
     */
    public void executeDistributeLogisticsAndAuditTask(OcBOrder order) {
        try {
            log.info(LogUtil.format("执行分物流和审核任务，orderId={}", "executeDistributeLogisticsAndAuditTask"), order.getId());
            // 分物流
            omsOrderDistributeLogisticsService.distributeLogisticsDistributeLogistics(order);
            // 加入审核
            omsAuditTaskService.createOcBAuditTask(order, OmsAuditTimeCalculateReason.OCCUPY);
        } catch (Exception e) {
            log.error(LogUtil.format("执行分物流和审核任务异常，orderId={}，异常信息：{}", "executeDistributeLogisticsAndAuditTask"),
                    order.getId(), Throwables.getStackTraceAsString(e));
            throw new NDSException("执行分物流和审核任务异常：" + e.getMessage());
        }
    }

    /**
     * 发送分物流和审核任务的MQ消息
     *
     * @param order 订单信息
     */

    public void sendDistributeLogisticsAndAuditTaskMq(OcBOrder order) {
        try {
            log.info(LogUtil.format("发送分物流和审核任务MQ消息，orderId={}", "sendDistributeLogisticsAndAuditTaskMq"), order.getId());
            // 构建消息体
            JSONObject msgBody = new JSONObject();
            msgBody.put("orderId", order.getId());
            msgBody.put("billNo", order.getBillNo());

            // 发送MQ消息
            String topic = TOPIC_DISTRIBUTE_LOGISTICS_AND_AUDIT;
            String tag = TAG_DISTRIBUTE_LOGISTICS_AND_AUDIT;
            // 添加订单ID和时间戳以确保消息唯一性
            String msgKey = "DistributeLogisticsAndAudit_" + order.getId() + "_" + BllCommonUtil.getmicTime();

            MqSendResult result = defaultProducerSend.sendDelayTopic(topic, tag, msgBody.toJSONString(), msgKey, 3000L);
            log.info(LogUtil.format("发送分物流和审核任务MQ消息结果，orderId={}，messageId={}", "sendDistributeLogisticsAndAuditTaskMq"),
                    order.getId(), result.getMessageId());
        } catch (Exception e) {
            log.error(LogUtil.format("发送分物流和审核任务MQ消息异常，orderId={}，异常信息：{}", "sendDistributeLogisticsAndAuditTaskMq"),
                    order.getId(), Throwables.getStackTraceAsString(e));
            // 如果MQ发送失败，则直接执行方法
            executeDistributeLogisticsAndAuditTask(order);
        }
    }
}
