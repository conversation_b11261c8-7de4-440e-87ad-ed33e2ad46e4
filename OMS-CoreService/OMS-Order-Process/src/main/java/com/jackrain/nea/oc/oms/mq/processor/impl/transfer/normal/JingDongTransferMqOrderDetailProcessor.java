package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.normal;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.JingdongTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJingdongOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 京东转单消息处理器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * @author: 易邵峰
 * @since: 2019-03-06
 * create at : 2019-03-06 17:40
 */
@Slf4j
public class JingDongTransferMqOrderDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private IpJingdongOrderService ipJingdongOrderService;

    @Autowired
    private JingdongTransferOrderProcessImpl jingdongTransferOrderProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("JingDongTransferMqOrderDetailProcessor.Start", orderNo));
        }

        IpJingdongOrderRelation jingdongOrderRelation = this.ipJingdongOrderService.selectJingdongOrder(orderNo);
        if (jingdongOrderRelation == null || jingdongOrderRelation.getJingdongOrder() == null) {
            log.error(LogUtil.format("JingDongTransferMqOrderDetailProcessor.Error.Not.Exist", orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = jingdongTransferOrderProcess.start(jingdongOrderRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("JingDongTransferMqOrderDetailProcessor.Finished,Result={}", orderNo), resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.JINGDONG
                && orderMqInfo.getOrderType() == OrderType.NORMAL;
    }
}
