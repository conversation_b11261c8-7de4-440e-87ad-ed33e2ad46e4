package com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.step;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrder;
import com.jackrain.nea.oc.oms.services.IpVipReturnOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description: 唯品会退供单-校验寄售仓和退供仓是否存在
 * @create 2021-06-21 17:15
 */
@Step(order = 20, description = "校验寄售仓和退供仓是否存在")
@Slf4j
@Component
public class Step020CheckwarehouseIsExist extends BaseVipReturnOrderProcessStep implements IOmsOrderProcessStep<IpVipReturnOrderRelation> {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private IpVipReturnOrderService ipVipReturnOrderService;

    @Override
    public ProcessStepResult<IpVipReturnOrderRelation> startProcess(IpVipReturnOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBVipReturnOrder vipReturnOrder = orderInfo.getVipReturnOrder();
        Long cpCShopId = vipReturnOrder.getCpCShopId();
        String errorMsg = "";
        String orderNo = orderInfo.getOrderNo();
        // 获取平台店铺档案
        CpShop cpShop = cpRpcService.selectShopById(cpCShopId);
        if (cpShop == null) {
            errorMsg = "该店铺的已不存在！";
            ipVipReturnOrderService.updateRemark(errorMsg, orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            return new ProcessStepResult(StepStatus.FAILED, errorMsg);
        }
       /* if (cpShop.getCpCStoreId() == null) {
            return new ProcessStepResult(StepStatus.FAILED, "该店铺的寄售仓为空，请至【平台店铺档案】处设置！");
        }*/
        if (cpShop.getVipReturns() == null) {
            errorMsg = "该店铺的唯品会退供仓为空，请至【平台店铺档案】处设置！";
            ipVipReturnOrderService.updateRemark(errorMsg, orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            return new ProcessStepResult(StepStatus.FAILED, errorMsg);
        }
        return new ProcessStepResult(StepStatus.SUCCESS,
                "校验寄售仓和退供仓是否存在成功，进入下一阶段");
    }
}
