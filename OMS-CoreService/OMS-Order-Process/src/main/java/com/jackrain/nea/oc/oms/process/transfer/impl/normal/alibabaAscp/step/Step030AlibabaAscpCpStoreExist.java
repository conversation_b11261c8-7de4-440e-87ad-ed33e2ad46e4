package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.step;

import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 秦雄飞
 * @date 2020/9/3 下午4:32
 */
@Step(order = 30, description = "发货仓库是否能找到对应逻辑仓")
@Component
@Slf4j
public class Step030AlibabaAscpCpStoreExist extends BaseAlibabaAscpOrderProcessStep
        implements IOmsOrderProcessStep<IpAlibabaAscpOrderRelation> {

    @Autowired
    private CpRpcService cpRpcService;

    @SuppressWarnings("unchecked")
    @Override
    public ProcessStepResult<IpAlibabaAscpOrderRelation> startProcess(IpAlibabaAscpOrderRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        String storeCode = orderInfo.getAlibabaAscpOrder().getStoreCode();
        String orderNo = orderInfo.getOrderNo();
        List<CpCStore> cpStoreList;

        if (StringUtils.isNotBlank(storeCode)) {
            cpStoreList = cpRpcService.queryStoreByTmallStoreCode(storeCode);
        } else {
            String message = "发货仓编码为空" + storeCode;
            ipAlibabaAscpOrderService.updateAlibabaAscpOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFERFAIL, message);
            return new ProcessStepResult<>(StepStatus.FAILED, message);
        }

        if (cpStoreList != null && cpStoreList.size() > 0) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "orderId =" + orderInfo.getOrderId() + ",发货仓编码=" + storeCode + ",逻辑仓=" + cpStoreList.get(0).getId() + "进入下一阶段");
        } else {
            String message = "找不到仓库编码为" + storeCode + "的逻辑仓";
            ipAlibabaAscpOrderService.updateAlibabaAscpOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFERFAIL, message);
            return new ProcessStepResult<>(StepStatus.FAILED, message);
        }
    }
}