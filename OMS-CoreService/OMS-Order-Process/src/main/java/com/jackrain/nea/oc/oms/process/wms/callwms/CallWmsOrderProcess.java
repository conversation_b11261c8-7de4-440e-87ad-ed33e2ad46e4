package com.jackrain.nea.oc.oms.process.wms.callwms;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * 订单传wms后更新配货中服务
 *
 * @author: 易邵峰
 * @since: 2019-01-18
 * create at : 2019-01-18 16:15
 */
@Component
public class CallWmsOrderProcess extends AbstractOrderProcess<OcBOrderRelation> {


    public CallWmsOrderProcess() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "";
    }

    @Override
    protected long getProcessOrderId(OcBOrderRelation orderInfo) {
        return orderInfo.getOrderInfo().getId();
    }

    @Override
    protected String getProcessOrderNo(OcBOrderRelation orderInfo) {
        return orderInfo.getOrderInfo().getSourceCode();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.CALL_WMS_PROCESS;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.DEFAULT;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }

}