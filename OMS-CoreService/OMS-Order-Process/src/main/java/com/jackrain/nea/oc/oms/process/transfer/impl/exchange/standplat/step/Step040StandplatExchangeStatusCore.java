package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat.step;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.WmsWithdrawalState;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/12/1 3:29 下午
 * @Version 1.0
 */
@Step(order = 40, description = "根据换货状态以及相关是否存在换货订单, 跳转不同的逻辑")
@Slf4j
@Component
public class Step040StandplatExchangeStatusCore extends BaseStandplatExchangeProcessStep implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        if(log.isDebugEnabled()){
            log.debug("BaseStandplatExchangeProcessStep.Step040StandplatExchangeStatusCore start >>> orderInfo：{}", JSON.toJSONString(orderInfo));
        }
        IpBStandplatRefund standplatRefund = orderInfo.getIpBStandplatRefund();
        try {
            //换货状态
            Integer returnStatus = standplatRefund.getReturnStatus();
            //换货的sku信息
            OcBReturnOrder ocBReturnOrder = orderInfo.getOcBReturnOrder();
            //换货待处理
            boolean isReturnExit = (IpBStandplatRefudStatusEnum.WAIT_SELLER_CONFIRM_GOODS.getVal().equals(returnStatus)
                    || IpBStandplatRefudStatusEnum.WAIT_BUYER_RETURN_GOODS.getVal().equals(returnStatus));

            if (isReturnExit && ocBReturnOrder == null) {
                //跳转到生成退换货单
                return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step100StandplatSaveExchangeOrder.class);
            } else if (isReturnExit && ocBReturnOrder.getTbDisputeId() == null) {
                //跳转到 更新退换货单和换货订单 如果为生成换货订单  则生成
                return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step090StandplatUpdateExchangeOrder.class);
            } else if (isReturnExit && ocBReturnOrder.getTbDisputeId() != null) {
                if (IpBStandplatRefudStatusEnum.WAIT_BUYER_RETURN_GOODS.getVal().equals(returnStatus)){
                    if (String.valueOf(ocBReturnOrder.getTbDisputeId()).equals(standplatRefund.getReturnNo())){
                        String message = "当前状态下已有退换货单,标记为已转换";
                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message, standplatRefund);
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }else {
                        if (!ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(ocBReturnOrder.getReturnStatus())) {
                            String message = "退换货取消失败,只有【等待退货入库】状态才可以操作取消";
                            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), message, standplatRefund);
                            return new ProcessStepResult<>(StepStatus.FINISHED, message);
                        }
                        if (WmsWithdrawalState.PASS.toInteger().equals(ocBReturnOrder.getIsTowms())) {
                            String message = "退单状态传WMS中不允许取消，请稍后操作！";
                            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), message, standplatRefund);
                            return new ProcessStepResult<>(StepStatus.FINISHED, message);
                        }
                        return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step080StandplatCancelToGenerateOrder.class);
                    }
                }
                String sku = orderInfo.getProductSku().getSkuEcode();
                BigDecimal qty = orderInfo.getIpBStandplatRefundItem().get(0).getExchangeQty();
                OcBReturnOrderExchange ocBReturnOrderExchange = orderInfo.getOcBReturnOrderExchanges().get(0);
                BigDecimal qtyExchange = ocBReturnOrderExchange.getQtyExchange();
                String psCSkuEcode = ocBReturnOrderExchange.getPsCSkuEcode();
                //sku及数量和地址发生变化
                if (!sku.equals(psCSkuEcode) || qtyExchange.compareTo(qty) != 0 || standplatExchangeService.isAddressChange(orderInfo)) {
                    //跳转到取消退换货单 和 作废换货订单 然后重新生成新的 退换货单和换货订单
                    return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step080StandplatCancelToGenerateOrder.class);
                }
                // 判断姓名或者手机号 是否发生变化
                standplatExchangeService.updateConsigneeAndPhone(ocBReturnOrder, standplatRefund, orderInfo.getExchangeRelation(), operateUser);
                //判断订单是否占单成功
                if (IpBStandplatRefudStatusEnum.WAIT_SELLER_AGREE.getVal().equals(returnStatus)) {
                    List<OmsOrderExchangeRelation> exchangeOrder = orderInfo.getExchangeRelation();
                    if (CollectionUtils.isEmpty(exchangeOrder)) {
                        //未查询到换货订单
                        String message = "未查询到换货订单,此换货单异常";
                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), message,
                                standplatRefund);
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                    List<OmsOrderExchangeRelation> middleState = exchangeOrder.stream().filter(p -> OmsOrderStatus.ORDER_DEFAULT.toInteger()
                            .equals(p.getOcBOrder().getOrderStatus())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(middleState)) {
                        String message = "换货订单状态不满足此次转换,等待下一次准换";
                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.NOT_TRANSFER.toInteger(), message,
                                standplatRefund);
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                }
                if (IpBStandplatRefudStatusEnum.WAIT_SELLER_CONFIRM_GOODS.getVal().equals(returnStatus)) {
                    String message = "退换货订单已存在,更新物流单号及物流公司";
                    standplatExchangeService.updateLogisticsCodeAndName(ocBReturnOrder, standplatRefund, operateUser);
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                            standplatRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, message);
                }
                String message = "未修改sku及数量,标记为已转换";
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                        standplatRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            } else if ((IpBStandplatRefudStatusEnum.CLOSED.getVal().equals(returnStatus)
                    || IpBStandplatRefudStatusEnum.PLEASE_REFUND.getVal().equals(returnStatus))
                    && ocBReturnOrder != null) {
                //跳转到取消退换货单 和 作废换货订单    (请退款)则处理换货转退货(删除换货明细)
                return new ProcessStepResult<>(StepStatus.SUCCESS, null, Step070StandplatExchangeToReturnOrClose.class);
            }
            String message = "当前状态暂不满足转换条件";
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), message,
                    standplatRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        } catch (Exception e) {
//            ipStandplatRefundService.updateExchangeIsTransError(standplatRefund, "处理跳转失败"+e.getMessage());
            log.error(" {}处理跳转失败:{}", this.getClass().getSimpleName(), Throwables.getStackTraceAsString(e));
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
