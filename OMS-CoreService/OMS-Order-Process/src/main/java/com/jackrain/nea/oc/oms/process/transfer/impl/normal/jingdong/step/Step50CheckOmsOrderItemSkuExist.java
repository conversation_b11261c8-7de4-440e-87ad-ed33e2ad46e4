package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.IpBJingdongOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJingdongOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AbnormalTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrderItemExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.psext.model.table.PsCSku;
import com.jackrain.nea.psext.request.SkuQueryRequest;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-25
 * create at : 2019-04-25 2:59 PM
 */
@Step(order = 50, description = "判断订单中的明细条码是否在【条码档案】中存在且启用")
@Slf4j
@Component
public class Step50CheckOmsOrderItemSkuExist extends BaseJingdongOrderProcessStep implements IOmsOrderProcessStep<IpJingdongOrderRelation> {

    @Autowired
    private IpBJingdongOrderItemMapper ipBJingdongOrderItemMapper;
    @Autowired
    private IpBJingdongOrderMapper ipBJingdongOrderMapper;

    @Override
    public ProcessStepResult<IpJingdongOrderRelation> startProcess(IpJingdongOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJingdongOrder jingdongOrder = orderInfo.getJingdongOrder();
        List<IpBJingdongOrderItemExt> itemList = orderInfo.getJingdongOrderItems();

        //仅用来更新
        IpBJingdongOrder updateOrder = new IpBJingdongOrder();
        //updateOrder.setId(jingdongOrder.getId());
        Long orderId = jingdongOrder.getOrderId();
        Long shopId = jingdongOrder.getCpCShopId();
        boolean hasErrorInfo = false;
        StringBuilder sbErrorInfo = new StringBuilder();
        boolean emptySku = false;
        if (itemList != null && itemList.size() != 0) {
            for (IpBJingdongOrderItemExt item : itemList) {
                String skuId = item.getOuterSkuId();
                ProductSku skuInfo = null;
                if (StringUtils.isNotEmpty(skuId)) {
                    skuInfo = psRpcService.selectProductSku(skuId);
                    if (skuInfo == null) {
                        List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuId, shopId);
                        if (CollectionUtils.isNotEmpty(productSkus) && productSkus.size() == 1){
                            skuInfo = productSkus.get(0);
                        }
                    }
                } else {
//                    skuId = item.getSkuId()+"";
//                    if (StringUtils.isNotEmpty(skuId)) {
//                        List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuId, shopId);
//                        if (CollectionUtils.isNotEmpty(productSkus) && productSkus.size() == 1){
//                            skuInfo = productSkus.get(0);
//                        }
//                    }
                    if (StringUtils.isNotBlank(item.getWareId())) {
                        String skuInfoId = psRpcService.queryJdProSku(item.getWareId());
                        if (StringUtils.isNotBlank(skuInfoId)) {
                            //更新主单未转换
                            IpBJingdongOrder order = new IpBJingdongOrder();
                            order.setIstrans(TransferOrderStatus.NOT_TRANSFER.toInteger());
                            order.setTransdate(new Date());
                            order.setTransCount(jingdongOrder.getTransCount() + 1L);
                            order.setSysremark("商品编码为空，查到后填写，等后面再转");

                            UpdateWrapper<IpBJingdongOrder> updateWrapper = new UpdateWrapper<>();
                            updateWrapper.eq("order_id", orderId);

                            ipBJingdongOrderMapper.update(order, updateWrapper);

                            //查询sku信息，更新订单明细，跳出转换
                            IpBJingdongOrderItem orderItem = new IpBJingdongOrderItem();
                            orderItem.setOuterSkuId(skuInfoId);
                            orderItem.setModifieddate(new Date());

                            UpdateWrapper<IpBJingdongOrderItem> updateItemWrapper = new UpdateWrapper<>();
                            updateItemWrapper.eq("id", item.getId());
                            ipBJingdongOrderItemMapper.update(orderItem, updateItemWrapper);

                            emptySku = true;
//                            skuInfo = psRpcService.selectProductSku(skuInfoId);
//                            if (skuInfo == null) {
//                                List<ProductSku> productSkus = psRpcService.selectProductSkuByThirdCode(skuInfoId, shopId);
//                                if (CollectionUtils.isNotEmpty(productSkus) && productSkus.size() == 1) {
//                                    skuInfo = productSkus.get(0);
//                                }
//                            }
                        }
                    }
                }
                if (skuInfo == null) {
                    hasErrorInfo = true;
                    String msg = Resources.getMessage(skuId + "不存在，货号为" + item.getProductNo());
                    sbErrorInfo.append(msg);
                    sbErrorInfo.append("\r\n");
                    log.debug("日志服务:    " + skuId + ": + 不存在");
                    break;
                } else if (YesNoEnum.N.getKey().equals(skuInfo.getIsactive())) {
                    hasErrorInfo = true;
                    String msg = Resources.getMessage(skuId + "已作废，货号为" + item.getProductNo());
                    sbErrorInfo.append(msg);
                    sbErrorInfo.append("\r\n");
                    break;
                } else {
                    item.setProdSku(skuInfo);
                }
            }

            if (emptySku){
                return new ProcessStepResult<>(StepStatus.FAILED, "商品编码为空，查到后填写，等后面再转");
            }
        } else {
            hasErrorInfo = true;
        }
        if (!hasErrorInfo) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "商品存在，进入下一步转换", Step60CheckOrderUserExist.class);
        } else {
            updateOrder.setTransdate(new Date());
            updateOrder.setTransCount(jingdongOrder.getTransCount() + 1L);
            updateOrder.setIstrans(TransferOrderStatus.TRANSFEREXCEPTION.toInteger());
            updateOrder.setSysremark(sbErrorInfo.toString());
            updateOrder.setAbnormalType(AbnormalTypeEnum.SKU_ABNORMAL.getKey());
            ipJingdongOrderService.updateIpJingdongOrderInfo(updateOrder, orderId);
            return new ProcessStepResult<>(StepStatus.FAILED, "商品不存在或已作废，不允许单据转换");
        }
    }

    private SkuQueryRequest getRequest(String skuId) {
        SkuQueryRequest queryRequest = new SkuQueryRequest();
        queryRequest.setIsBlur("N");
        PsCSku psCSku = new PsCSku();
        psCSku.setEcode(skuId);
        queryRequest.setPsCSku(psCSku);
        return queryRequest;
    }

}
