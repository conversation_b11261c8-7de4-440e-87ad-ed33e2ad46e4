package com.jackrain.nea.oc.oms.process.transfer.impl.normal.standplat.step;

import cn.hutool.core.util.ObjectUtil;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatOldOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.OmsPlatformUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 检查平台单号
 *
 * @author: ming.fz
 * @since: 2019-07-08
 * create at : 2019-07-08
 */
@Transactional
@Step(order = 20, description = "检查平台单号")
@Component
@Slf4j
public class Step020StandplatCheckOmsOrderExist extends BaseStandplatOrderProcessStep
        implements IOmsOrderProcessStep<IpStandplatOrderRelation> {

    @Autowired
    private OmsOrderLogService orderLogService;

    @Override
    public ProcessStepResult<IpStandplatOrderRelation> startProcess(IpStandplatOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpStandplatOrderRelation> stepResult = null;

        String status = orderInfo.getStandplatOrder().getStatus();
        String tid = orderInfo.getStandplatOrder().getTid();

        Long platformId = orderInfo.getPlatformId();

        OmsPlatformUtil bean = ApplicationContextHandle.getBean(OmsPlatformUtil.class);
        Boolean isSpecialPlatform = bean.isSpecialPlatform(platformId);

        //通过tid获取全渠道所有订单
        List<OcBOrder> findOrderInfoList = orderService.selectOmsOrderInfo(tid);

        if (CollectionUtils.isEmpty(findOrderInfoList)) {
            findOrderInfoList = new ArrayList<>();
        }

        // 特殊订单类型
        if (isSpecialPlatform) {

            //判断订单的交易状态是否为卖家已发货/订单完成/买家已付款
            boolean isRefund = IpBStandplatOldOrderStatusEnum.WAIT_BUYER_CONFIRM_GOODS.getVal().equals(status) ||
                    IpBStandplatOldOrderStatusEnum.TRADE_FINISHED.getVal().equals(status);

            log.info("Step020StandplatCheckOmsOrderExist.startProcess Special type order isRefund={},status={}",
                    isRefund, status);

            //交易状态是否为卖家已发货/订单完成
            if (isRefund) {
                //判断是否存在对应零售发货单
                if (CollectionUtils.isNotEmpty(findOrderInfoList)) {
                    //存在，则转单结束
                    String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() +
                            "是特殊类型订单转单且状态为卖家已发货/订单完成且存在零售发货单,转单结束");
                    String remarks = Resources.getMessage("单据" + orderInfo.getOrderId() +
                            "是特殊类型订单转单且状态为卖家已发货/订单完成且存在零售发货单,标记为已转换");
                    ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
                            TransferOrderStatus.TRANSFERRED, remarks, null);
                    return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
                } else {
                    //不存在，则按照正常转单服务，生成对应零售发货单
                    String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查平台订单状态成功，进入下一阶段");
                    return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
                }
            } else {
                String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() +
                        "是特殊类型订单转单但是状态不为卖家已发货/订单完成,转单结束");

                String remarks = Resources.getMessage("单据" + orderInfo.getOrderId() +
                        "是特殊类型订单转单但是状态不为卖家已发货/订单完成,标记为已转换");
                ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
                        TransferOrderStatus.TRANSFERRED, remarks, null);
                return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
            }
        }

        // 判断通用订单状态是否为 交易完成 如果是的话 将对应的 "仓库发货"状态的零售发货单状态设置为"平台发货"
        if (TaoBaoOrderStatus.TRADE_FINISHED.equals(status)) {
            List<OcBOrder> warehouseDeliveryOrderList = findOrderInfoList.stream().filter(
                    o -> ObjectUtil.equal(o.getOrderStatus(), OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(warehouseDeliveryOrderList)) {
                // 更新状态
                for (OcBOrder ocBOrder : warehouseDeliveryOrderList) {
                    OcBOrder updateOcBOrder = new OcBOrder();
                    updateOcBOrder.setId(ocBOrder.getId());
                    updateOcBOrder.setModifieddate(new Date());
                    updateOcBOrder.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                    ocBOrderMapper.updateById(updateOcBOrder);
                    // 插入一条操作日志
                    orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                            OrderLogTypeEnum.PLATFORM_SEND.getKey(), "由通用订单交易完成转换", "", "", operateUser);
                }
            }
        }

        //判断平台订单的状态是否为“待发货”或退款完成
        // @20200724 取消的也要往下走
        IpBStandplatOrder standplatOrder = orderInfo.getStandplatOrder();
        Long reserveBigint05 = standplatOrder.getReserveBigint05();
        boolean b = TaoBaoOrderStatus.WAIT_BUYER_PAY.equals(status) && reserveBigint05 != null;
        if (TaoBaoOrderStatus.WAIT_SELLER_SEND_GOODS.equals(status) || TaoBaoOrderStatus.REFUND_FINISHED.equals(status) || TaoBaoOrderStatus.TRADE_CANCELED.equals(status)
                || TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equals(status)
                || TaoBaoOrderStatus.TRADE_FINISHED.equals(status) || b) {
            // 订单在平台已取消或退款完成，在订单表中没有记录则直接标记已转换
            if ((TaoBaoOrderStatus.REFUND_FINISHED.equals(status)
                    || TaoBaoOrderStatus.TRADE_CANCELED.equals(status))
                    && CollectionUtils.isEmpty(findOrderInfoList)) {
                String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "订单表不存在原单,标记为已转");
                return new ProcessStepResult<>(StepStatus.SUCCESS, findOrderInfoList, operateMessage, Step080StandplatUpdateOrderTransferStatus.class);
            }
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查平台订单状态成功，进入下一阶段");
            stepResult = new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        } else {
            //其它状态
            String remarks = "订单状态不是待发货,标记为已转";
            ipStandplatOrderService.updateStandPlatOrderTransStatus(orderInfo.getOrderNo(),
                    TransferOrderStatus.TRANSFERRED, remarks, null);
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + remarks);
            //2019-11-21 更新卖家备注
            stepResult = new ProcessStepResult<>(StepStatus.SUCCESS, findOrderInfoList, operateMessage, Step070StandplatUpdateSellerRemark.class);
        }
        return stepResult;
    }
}
