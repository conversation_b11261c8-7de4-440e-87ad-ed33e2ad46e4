package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.ip.model.request.VipJitxRedeliverTransportNoVopRequest;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.JitxOrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrderItemEx;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/3/4 09:41
 * @Description
 */
@Step(order = 35, description = "JITX补寄订单获取仓库、物流、快递单号")
@Component
@Slf4j
public class Step35JitxRedeliverQueryLogisticsNo extends BaseJitxOrderProcessStep implements IOmsOrderProcessStep<IpJitxOrderRelation> {

    @Resource
    private IpRpcService ipRpcService;

    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        IpBJitxOrder ipBJitxOrder = orderInfo.getJitxOrder();
        if (!JitxOrderTypeEnum.REDELIVER_TRANSPORT.getKey().equals(ipBJitxOrder.getOrderType())) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, null, "非Jitx补寄订单，进入下一阶段",
                    Step40JitxCheckLogisticsExist.class);
        }
        if (StringUtils.isNotEmpty(ipBJitxOrder.getTransportNo())) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, null, "Jitx补寄订单物流单号已存在，进入下一阶段",
                    Step40JitxCheckLogisticsExist.class);
        }
        //校验&&获取快递单号
        List<String> errors = Lists.newArrayList();
        checkAndQueryLogisticsNo(ipBJitxOrder, orderInfo.getJitxOrderItemList(), errors);
        if (CollectionUtils.isNotEmpty(errors)) {
            String errorMessage = "补寄订单获取快递单号失败，退出转单操作";
            boolean updateStatusRes = ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(),
                    TransferOrderStatus.NOT_TRANSFER, JSON.toJSONString(errors));
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
        ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(),
                TransferOrderStatus.NOT_TRANSFER, "Jitx补寄订单获取快递单号成功，等待平台再次推送！");
        return new ProcessStepResult<>(StepStatus.FINISHED, "Jitx补寄订单获取快递单号成功，等待平台再次推送！");
    }

    /**
     * 验证补寄单信息及
     *
     * @param ipBJitxOrder
     * @param jitxOrderItemList
     * @param errors
     */
    private void checkAndQueryLogisticsNo(IpBJitxOrder ipBJitxOrder, List<IpBJitxOrderItemEx> jitxOrderItemList,
                                          List<String> errors) {
        if (StringUtils.isEmpty(ipBJitxOrder.getOldOrderSn())) {
            errors.add("Jitx补寄订单的原始单号为空");
            return;
        }
        IpBJitxOrder oldJitxOrder = ipJitxOrderService.selectJitxOrderOnlyMain(ipBJitxOrder.getOldOrderSn());
        if (oldJitxOrder == null) {
            errors.add("Jitx补寄订单的原始订单不存在");
            return;
        }
        if (StringUtils.isEmpty(oldJitxOrder.getCarrierCode())) {
            errors.add("Jitx补寄订单的原始订单的物流编码为空");
            return;
        } else {
            ipBJitxOrder.setCarrierCode(oldJitxOrder.getCarrierCode());
            ipBJitxOrder.setCarrierName(oldJitxOrder.getCarrierName());
        }
        if (StringUtils.isEmpty(oldJitxOrder.getDeliveryWarehouse())) {
            errors.add("Jitx补寄订单的原始订单的仓库编码为空");
            return;
        } else {
            ipBJitxOrder.setDeliveryWarehouse(oldJitxOrder.getDeliveryWarehouse());
        }
        if (StringUtils.isEmpty(ipBJitxOrder.getSellernick())) {
            errors.add("Jitx补寄订单的店铺名称为空");
            return;
        } else if (!ipBJitxOrder.getSellernick().equals(oldJitxOrder.getSellernick())) {
            errors.add("Jitx补寄订单的店铺名称和原始单的店铺名称不一致");
            return;
        }
        //查询物流单号
        VipJitxRedeliverTransportNoVopRequest request = buildParam(ipBJitxOrder, jitxOrderItemList);
        ValueHolderV14<Void> holderV14 = ipRpcService.queryRedeliverTransportLogisticsNo(request);
        log.info(LogUtil.format("IpRpcService.queryRedeliverTransportLogisticsNo request:{},holderV14:{}",
                "IpRpcService.queryRedeliverTransportLogisticsNo"),
                JSONObject.toJSONString(request), JSONObject.toJSONString(holderV14));
        if (!holderV14.isOK()) {
            errors.add(holderV14.getMessage());
        }
    }

    /**
     * 构建查询补寄订单的物流单号
     *
     * @param ipBJitxOrder
     * @param jitxOrderItemList
     * @return
     */
    private VipJitxRedeliverTransportNoVopRequest buildParam(IpBJitxOrder ipBJitxOrder,
                                                             List<IpBJitxOrderItemEx> jitxOrderItemList) {
        VipJitxRedeliverTransportNoVopRequest request = new VipJitxRedeliverTransportNoVopRequest();
        request.setSellerNick(ipBJitxOrder.getSellernick());
        List<VipJitxRedeliverTransportNoVopRequest.PrintDetail> printDetailList = new ArrayList<>();
        request.setData(printDetailList);
        VipJitxRedeliverTransportNoVopRequest.PrintDetail printDetail =
                new VipJitxRedeliverTransportNoVopRequest.PrintDetail();
        printDetailList.add(printDetail);
        printDetail.setOrderSn(ipBJitxOrder.getOrderSn());
        printDetail.setCarrierCode(ipBJitxOrder.getCarrierCode());
        printDetail.setThirdCustCode(ipBJitxOrder.getCarrierCode());
        printDetail.setDeliveryWarehouseCode(ipBJitxOrder.getDeliveryWarehouse());
        List<String> goodsInfoList = new ArrayList<>();
        printDetail.setGoodsInfo(goodsInfoList);
        for (IpBJitxOrderItemEx jitxOrderItemEx : jitxOrderItemList) {
            String goodsInfo = jitxOrderItemEx.getBarcode() + "*" + jitxOrderItemEx.getQuantity();
            goodsInfoList.add(goodsInfo);
        }
        return request;
    }

}
