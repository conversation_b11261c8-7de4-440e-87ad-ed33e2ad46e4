package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.step;

import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 检查店铺信息是否完整
 **/
@Step(order = 25, description = "检查店铺信息是否完整")
@Slf4j
@Component
public class Step025CheckShopInfo extends BaseTaobaoJxOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoJxOrderRelation> {
    @Override
    public ProcessStepResult<IpTaobaoJxOrderRelation> startProcess(
            IpTaobaoJxOrderRelation orderInfo,
            ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoJxOrder taobaoJxOrder = orderInfo.getTaobaoJxOrder();
        try {
            if (taobaoJxOrder.getCpCShopId() == null) {
                ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.NOT_TRANSFER,
                        "无店铺信息，退出转换！", taobaoJxOrder);
                return new ProcessStepResult<>(StepStatus.FINISHED, "无店铺信息，退出转换！");
            }
            //查询店铺信息
            CpShop cpShop = cpRpcService.selectShopById(taobaoJxOrder.getCpCShopId());
            if (cpShop == null) {
                ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.NOT_TRANSFER,
                        "平台店铺id=" + taobaoJxOrder.getCpCShopId() + "不存在，退出转换！", taobaoJxOrder);
                return new ProcessStepResult<>(StepStatus.FINISHED, "平台店铺id=" + taobaoJxOrder.getCpCShopId() + "不存在，退出转换！");
            }
            taobaoJxOrder.setCpCShopTitle(cpShop.getCpCShopTitle());
            return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "查询平台店铺信息成功，进入下一阶段！");
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 淘宝经销订单转换异常！", e);
            //修改中间表状态及系统备注
            ipTaobaoJxOrderService.updateIsTransError(taobaoJxOrder, e.getMessage());
            String errorMessage = "淘宝经销订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
