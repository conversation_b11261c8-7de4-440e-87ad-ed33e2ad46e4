package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.process.tobeconfirm.util.TobeConfirmedUtil;
import com.jackrain.nea.oc.oms.services.OmsOrderCheckHasRefundProductService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderRecountAmountService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 存在退款成功商品处理
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:12
 */
@Step(order = 20, description = "存在退款成功商品处理")
@Slf4j
public class Step020CheckHasRefundProduct extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsOrderCheckHasRefundProductService omsOrderCheckHasRefundProductService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OmsOrderRecountAmountService orderRecountAmountService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsSystemConfig omsSystemConfig;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        try {
            if (!TobeConfirmedUtil.checkIsJcOrder(orderInfo)) {
                //判断订单明细是否全部为退款成功的明细
                List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectOrderItems(orderInfo.getOrderId());

                // 未退款成功的订单明细
                List<OcBOrderItem> unRefundOrderItemList = orderItemList.stream().filter(
                        p -> !p.getRefundStatus().equals(OcOrderRefundStatusEnum.SUCCESS.getVal())
                ).collect(Collectors.toList());

                String message = "";
                if (CollectionUtils.isEmpty(unRefundOrderItemList)) {
                    message = "订单OrderId=" + orderInfo.getOrderId() + "的订单明细全部为退款成功商品，订单状态更新为已取消";
                    if (log.isDebugEnabled()) {
                        log.debug(message);
                    }

                    //若整单明细都已退款成功的明细，则直接将状态修改为订单取消。调用订单日志服务
                    OcBOrder cancelOrderInfo = new OcBOrder();
                    cancelOrderInfo.setId(orderInfo.getOrderId());
                    cancelOrderInfo.setModifierename(SystemUserResource.ROOT_USER_NAME);
                    cancelOrderInfo.setModifieddate(new Date());
                    cancelOrderInfo.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
                    omsOrderService.updateOrderInfo(cancelOrderInfo);
                    omsOrderLogService.addUserOrderLog(orderInfo.getOrderInfo().getId(),
                            orderInfo.getOrderInfo().getBillNo(), OrderLogTypeEnum.ORDER_CANCLE.getKey(),
                            message, null, null, operateUser);
                    return new ProcessStepResult<>(StepStatus.FINISHED, message);
                } else {
                    //已经退款完成的
                    List<OcBOrderItem> refundSuccessOrderItemList = orderItemList.stream().filter(
                            p -> p.getRefundStatus().equals(OcOrderRefundStatusEnum.SUCCESS.getVal())
                    ).collect(Collectors.toList());

                    // 未退款完成的普通商品订单明细
                    List<OcBOrderItem> unRefundNormalProdItemList = unRefundOrderItemList.stream().filter(
                            p -> p.getProType() != SkuType.COMBINE_PRODUCT && p.getProType() != SkuType.GIFT_PRODUCT).collect(Collectors.toList());
                    //爱库存计算金额不过滤组合商品
                    if (PlatFormEnum.AI_KU_CUN.getCode().equals(orderInfo.getOrderInfo().getPlatform()) || PlatFormEnum.DOU_YIN.getCode().equals(orderInfo.getOrderInfo().getPlatform())) {
                        unRefundNormalProdItemList = unRefundOrderItemList.stream().filter(p -> p.getProType() != SkuType.GIFT_PRODUCT).collect(Collectors.toList());
                    }

                    if (CollectionUtils.isNotEmpty(refundSuccessOrderItemList)) {
                        boolean recountResult = orderRecountAmountService.doRecountAmount(orderInfo,
                                refundSuccessOrderItemList, unRefundNormalProdItemList);
                        if (recountResult) {
                            message = "订单OrderId=" + orderInfo.getOrderId() + "的订单为部分退款明细商品";
                            OcBOrder order = ocBOrderMapper.selectById(orderInfo.getOrderInfo().getId());
                            orderInfo.setOrderInfo(order);
                            return new ProcessStepResult<>(StepStatus.SUCCESS, message);
                        } else {
                            message = "订单OrderId=" + orderInfo.getOrderId() + "的订单为部分退款明细商品,调用退款服务执行异常,结束流程!";
                            log.error(message);
                            OcBOrder finishedOrderInfo = new OcBOrder();
                            finishedOrderInfo.setId(orderInfo.getOrderId());
                            finishedOrderInfo.setSysremark(SplitMessageUtil.splitMesssage(message));
                            omsOrderService.updateOrderInfo(finishedOrderInfo);
                            return new ProcessStepResult<>(StepStatus.FINISHED, message);
                        }
                    }
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception ex) {
            log.error(LogUtil.format("订单执行退款重新计算服务异常,异常信息:{}", "发货后退单转换异常", orderInfo.getOrderId()), Throwables.getStackTraceAsString(ex));
            String operateMessage = "订单OrderId" + orderInfo.getOrderId() + "的订单执行退款重新计算服务异常;" + ex.getMessage();
            OcBOrder errorOrderInfo = new OcBOrder();
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage(operateMessage));
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
    }
}
