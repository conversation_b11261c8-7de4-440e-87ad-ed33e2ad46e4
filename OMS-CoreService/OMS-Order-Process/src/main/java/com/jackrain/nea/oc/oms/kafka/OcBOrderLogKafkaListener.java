package com.jackrain.nea.oc.oms.kafka;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.burgeon.kafka.annotation.KafkaMessageListener;
import com.burgeon.kafka.tlog.KafkaTLogAspect;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogErrorMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.utility.LogUtil;
import com.yomahub.tlog.core.rpc.TLogLabelBean;
import com.yomahub.tlog.core.rpc.TLogRPCHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.listener.BatchAcknowledgingMessageListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName OcBOrderLogKafkaListener
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/5/29 09:39
 * @Version 1.0
 */
@Component
@Slf4j
@KafkaMessageListener(name = "OcBOrderLogKafkaListener")
public class OcBOrderLogKafkaListener implements BatchAcknowledgingMessageListener<String, String> {

    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderLogErrorMapper ocBOrderLogErrorMapper;
    @Autowired
    private OcBOrderLogMapper ocBOrderLogMapper;

    @Override
    public void onMessage(List<ConsumerRecord<String, String>> recordList, Acknowledgment acknowledgment) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        log.debug(LogUtil.format("写入订单操作日志，kafka接收消息条数(为0时直接结束)={}",
                "OcBOrderLogKafkaListener.onMessage"), recordList.size());

        if (CollectionUtils.isEmpty(recordList)) {
            return;
        }
        /*批量接收消息，但是独立消费，否则无法传递链路ID*/
        List<List<ConsumerRecord<String, String>>> split = CollUtil.split(recordList, 1);
        for (List<ConsumerRecord<String, String>> consumerRecords : split) {
            genTraceFromHeader(consumerRecords.get(0).headers(), consumerRecords.get(0).key());
            try {
                OcBOrderLog ocBOrderLog = omsOrderLogService.processSingleList(consumerRecords.get(0));
                if (ocBOrderLog != null) {
                    ocBOrderLogMapper.insert(ocBOrderLog);
                }
            } finally {
                new TLogRPCHandler().cleanThreadLocal();
            }
        }

        // 消费成功或异常的都提交 ACK
        acknowledgment.acknowledge();
        stopWatch.stop();
        log.info(LogUtil.format("外层kafka消息处理完成,接收条数:{}，总耗时(毫秒)：{}", "OcBOrderLogKafkaListener.consume"),
                recordList.size(), stopWatch.getTotalTimeMillis());
    }


    private void genTraceFromHeader(Headers headers, String msgKey) {
        Header header = headers.lastHeader(KafkaTLogAspect.TLOG_HEADER_KEY);
        if (Objects.isNull(header)) {
            log.warn(LogUtil.format("写入订单操作日志，kafka消息未传递traceId，messageKey：{}",
                    "SgStorageMessageKafkaRouteListener.genTraceFromHeader"), msgKey);
            new TLogRPCHandler().processProviderSide(new TLogLabelBean());
            return;
        }

        String tlogHeader = new String(header.value());
        new TLogRPCHandler().processProviderSide(JSON.parseObject(tlogHeader, TLogLabelBean.class));
    }
}
