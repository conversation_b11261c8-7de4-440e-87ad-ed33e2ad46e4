package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect;

import com.jackrain.nea.oc.oms.model.enums.StepExeState;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.step.AbstractJDDirectCancelProcessStep;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.step.Step099UpdateBills;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.step.Step100FinalProcess;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/29
 */
@Slf4j
public class JDDirectTransferSupply {


    public static ProcessStepResult<OmsJDDirectCancelRelation> getTransStep(StepExeState stepExeState) {
        ProcessStepResult<OmsJDDirectCancelRelation> stepResult = new ProcessStepResult<>();
        switch (stepExeState) {
            case UPDATE:
                stepResult.setStatus(StepStatus.SUCCESS);
                stepResult.setNextStepClass(Step099UpdateBills.class);
                break;
            case FINAL:
                stepResult.setStatus(StepStatus.SUCCESS);
                stepResult.setNextStepClass(Step100FinalProcess.class);
                break;
            case NEXT:
            case SUCCESS:
                stepResult.setStatus(StepStatus.SUCCESS);
                break;
            case RE_DEAL:
                stepResult.setStatus(StepStatus.FINISHED);
                break;
            case FAIL:
                stepResult.setStatus(StepStatus.FAILED);
                break;
            default:
                break;
        }
        return stepResult;
    }

    public static ProcessStepResult<OmsJDDirectCancelRelation> getAppointStepResult(Class<? extends AbstractJDDirectCancelProcessStep> clz) {
        ProcessStepResult<OmsJDDirectCancelRelation> stepResult = new ProcessStepResult<>();
        stepResult.setStatus(StepStatus.SUCCESS);
        stepResult.setNextStepClass(clz);
        return stepResult;
    }


}
