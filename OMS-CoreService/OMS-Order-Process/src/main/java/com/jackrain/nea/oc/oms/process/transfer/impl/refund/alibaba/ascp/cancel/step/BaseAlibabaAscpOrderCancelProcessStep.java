package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.cancel.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpBAlibabaAscpOrderCancelService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description: 猫超直发取消订单转单处理抽象类
 * @author: xtt
 * @date: 2020-09-04 11:43
 **/
public abstract class BaseAlibabaAscpOrderCancelProcessStep {
    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected IpBAlibabaAscpOrderCancelService orderCancelService;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.ALIBABAASCP;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_ALIBABA_ASCP_CANCEL_ORDER;
    }
}
