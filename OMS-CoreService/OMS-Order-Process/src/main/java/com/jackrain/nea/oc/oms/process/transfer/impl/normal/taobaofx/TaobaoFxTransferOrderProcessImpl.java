package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * 淘宝分销订单中间表转成全渠道订单服务
 *
 * @author: 周琳胜
 * @since: 2019/7/10
 * create at : 2019/7/10 14:00
 */
@Component
public class TaobaoFxTransferOrderProcessImpl extends AbstractOrderProcess<IpTaobaoFxOrderRelation> {
    public TaobaoFxTransferOrderProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "taobaofx";
    }

    @Override
    protected long getProcessOrderId(IpTaobaoFxOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpTaobaoFxOrderRelation orderInfo) {
        return orderInfo.getOrderNo().toString();
    }

    @Override
    protected long getLongProcessOrderNo(IpTaobaoFxOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_FX_TAOBAO_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAOFX;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }
}
