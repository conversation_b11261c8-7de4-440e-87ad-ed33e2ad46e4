package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 转换全渠道订单表状态仓库发货、平台发货、交易完成、物流已送达
 * @Date 2019-6-26
 **/
@Step(order = 50, description = "全渠道订单表状态仓库发货、平台发货、交易完成、物流已送达")
@Slf4j
@Component
public class Step050DeliveryStatus extends BaseJitxRefundProcessStep implements IOmsOrderProcessStep<IpJitxOrderRelation> {
    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJitxOrder ipBJitxOrder = orderInfo.getJitxOrder();
        if (log.isDebugEnabled()) {
            log.debug("Step050DeliveryStatus orderNo:{}", orderInfo.getOrderNo());
        }
        try {
            List<OcBOrder> ocBOrderList = ipJitxRefundService.selectOmsOrder(orderInfo.getOrderNo());
            //1、仓库发货、平台发货、交易完成、物流已送达  WAREHOUSE_DELIVERY  PLATFORM_DELIVERY DEAL_DONE DELIVERED
            int count = 0;
            List<Long> orderIds = new ArrayList<>(ocBOrderList.size());
            for (OcBOrder ocBOrder : ocBOrderList) {
                Integer ocBOrderStatus = ocBOrder.getOrderStatus();
                if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(ocBOrderStatus)
                        || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrderStatus)
                        || OmsOrderStatus.DEAL_DONE.toInteger().equals(ocBOrderStatus)
                        || OmsOrderStatus.DELIVERED.toInteger().equals(ocBOrderStatus)) {
                    orderIds.add(ocBOrder.getId());
                }
            }

            if (CollectionUtils.isNotEmpty(orderIds)) {
                ValueHolderV14 v14 = ocBReturnOrderBatchAddService.generateByOcBOrder(orderIds, true, SystemUserResource.getRootUser());
                if (v14.isOK() && Integer.valueOf(orderIds.size()).equals(v14.getData())) {
                    ipJitxRefundService.updateRefundIsTrans(TransferOrderStatus.TRANSFERRED, "", ipBJitxOrder);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderNo() + "退款关闭，转换成功！!!");
                } else {
                    String msg = "jitx订单取消生成退货单异常：" + SplitMessageUtil.splitErrMsgBySize(v14.getMessage(), SplitMessageUtil.SIZE_200);
                    ipJitxRefundService.updateRefundIsTrans(TransferOrderStatus.NOT_TRANSFER, msg, ipBJitxOrder);
                    return new ProcessStepResult<>(StepStatus.FAILED, "标记为退款完成失败，退出转单！");
                }
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "全渠道订单表状态仓库发货、平台发货、交易完成、物流已送达判断成功，进入下一步！");
        } catch (Exception e) {
            log.error(LogUtil.format("退单转换异常:{}", "退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJitxRefundService.updateRefundIsTransError(ipBJitxOrder, e.getMessage());
            String errorMessage = "退单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
