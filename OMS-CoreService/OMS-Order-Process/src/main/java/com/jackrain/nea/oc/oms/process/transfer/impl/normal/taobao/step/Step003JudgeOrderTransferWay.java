package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: xiWen.z
 * create at: 2019/10/12 0012
 */
@Step(order = 3, description = "判断淘宝订单预售转换")
@Slf4j
@Component
public class Step003JudgeOrderTransferWay extends BaseTaobaoOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();
        if (orderInfo == null || orderInfo.getTaobaoOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者Order.TaobaoOrder为空；退出转换");
        }
        if (CollectionUtils.isEmpty(orderInfo.getTaobaoOrderItemList())) {
            ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderInfo.getOrderNo(),
                    TransferOrderStatus.TRANSFERFAIL,  "订单明细为空转换失败!");
            return new ProcessStepResult<>(StepStatus.FAILED, "orderInfo.getTaobaoOrderItemList为空；退出转换");
        }
        int currentStatus = orderInfo.getTaobaoOrder().getIstrans();
        if (TransferOrderStatus.TRANSFERRED.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态=已转换，转换完成");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else if (TransferOrderStatus.TRANSFERRING.toInteger() == currentStatus) {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "正在转换中");
            return new ProcessStepResult<>(StepStatus.FINISHED, operateMessage);
        } else {
            ProcessStepResult<IpTaobaoOrderRelation> result = getHistoryOrderProcessStepResult(orderInfo);
            if (result != null){
                return result;
            }
            boolean preSaleVal = this.omsSystemConfig.isTransferPreSale4Double11();
            String presaleTransfer = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get("business_system:presale_to_oms_order");
            if (StringUtils.isNotEmpty(presaleTransfer)){
                preSaleVal = "是".equals(presaleTransfer.trim());
            }

            String stepTradeStatus = orderInfo.getTaobaoOrder().getStepTradeStatus();
            if (preSaleVal) {
                String orderStatus = orderInfo.getTaobaoOrder().getStatus();
                boolean isPreSale = StringUtils.isNotEmpty(stepTradeStatus)
                        && (TaoBaoOrderStatus.WAIT_SELLER_SEND_GOODS.equalsIgnoreCase(orderStatus)
                        || TaoBaoOrderStatus.WAIT_BUYER_PAY.equalsIgnoreCase(orderStatus));
                if (isPreSale) {
                    return orderStepTradeStatus(orderInfo);
                } else {
                    String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + ";OrderNo="
                            + orderInfo.getOrderNo() + "非淘宝预售订单，进入下一阶段");
                    stepResult.setMessage(operateMessage);
                }
            } else if (StringUtils.isNotEmpty(stepTradeStatus)) {
                //更新系统备注(预售暂不转单)
                if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(stepTradeStatus)) {
                    String orderNo = orderInfo.getOrderNo();
                    String message = "预售付定金暂不转单";
                    ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo,
                            TransferOrderStatus.TRANSFERRED, message);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "暂未开启预售付定金转单服务");
                }
            } else {
                String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + ";OrderNo="
                        + orderInfo.getOrderNo() + "淘宝预售判断开启状态:" + preSaleVal + "; 进入下一阶段");
                stepResult.setMessage(operateMessage);
            }
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step010CheckOrderTransferStatus.class);
            return stepResult;
        }
    }

    /**
     * 历史订单的处理
     *
     * @param orderInfo
     * @return
     */
    private ProcessStepResult<IpTaobaoOrderRelation> getHistoryOrderProcessStepResult(IpTaobaoOrderRelation orderInfo) {
        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
        String pullHistoryOrder = config.getProperty("pull_taobao_HistoryOrder_shopId", "");
        IpBTaobaoOrder taobaoOrder = orderInfo.getTaobaoOrder();
        Long cpCShopId = taobaoOrder.getCpCShopId();
        if (StringUtils.isEmpty(pullHistoryOrder) || cpCShopId == null) {
            return null;
        }
        String[] split = pullHistoryOrder.split(",");
        List<String> shopIds = new ArrayList<>(Arrays.asList(split)) ;
        if (shopIds.contains(cpCShopId+"")) {
            String orderStatus = orderInfo.getTaobaoOrder().getStatus();
            boolean isTradeClosed = TaoBaoOrderStatus.TRADE_CLOSED.equalsIgnoreCase(orderStatus)
                    || TaoBaoOrderStatus.TRADE_CLOSED_BY_TAOBAO.equalsIgnoreCase(orderStatus);
            if (isTradeClosed) {
                String orderNo = orderInfo.getOrderNo();
                this.ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo,
                        TransferOrderStatus.TRANSFERRED, "转单成功");
                return new ProcessStepResult<>(StepStatus.FINISHED, "历史订单开关已打开,状态为交易关闭,标记为已转换");
            }
            boolean isHistoryOrder = TaoBaoOrderStatus.TRADE_FINISHED.equalsIgnoreCase(orderStatus)
                    || TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equalsIgnoreCase(orderStatus);
            if (!isHistoryOrder){
                return new ProcessStepResult<>(StepStatus.FINISHED, "历史订单开关已打开,只转换交易完成及等待买家确认收货的状态");
            }
        }
        return null;
    }


    /**
     * 阶段付款状态
     *
     * @param orderInfo 淘宝中间关系表
     * @return ProcessStepResult
     */
    private ProcessStepResult<IpTaobaoOrderRelation> orderStepTradeStatus(IpTaobaoOrderRelation orderInfo) {
        String stepTradeStatus = orderInfo.getTaobaoOrder().getStepTradeStatus();
        ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();
        if (TaoBaoOrderStatus.FRONT_NOPAID_FINAL_NOPAID.equalsIgnoreCase(stepTradeStatus)) {

            String operateMessage = "订单付款状态=" + stepTradeStatus + ";进入下一阶段设置已转换状态。OrderId="
                    + orderInfo.getOrderId() + ";OrderNo=" + orderInfo.getOrderNo();
            stepResult.setMessage(operateMessage);
            stepResult.setNextStepClass(Step008UpdateOrderTransferStatus.class);
        } else if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equalsIgnoreCase(stepTradeStatus)) {

            String operateMessage = "订单付款状态=" + stepTradeStatus + ";进入下一阶段。OrderId=" + orderInfo.getOrderId()
                    + ";OrderNo=" + orderInfo.getOrderNo();
            stepResult.setMessage(operateMessage);
        } else if (TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equalsIgnoreCase(stepTradeStatus)) {

            String operateMessage = "订单付款状态=" + stepTradeStatus + ";进入下一阶段。OrderId=" + orderInfo.getOrderId()
                    + ";OrderNo=" + orderInfo.getOrderNo();
            stepResult.setMessage(operateMessage);
        } else {

            String operateMessage = "订单付款状态=" + stepTradeStatus + ";OrderId" + orderInfo.getOrderId() + ";OrderNo="
                    + orderInfo.getOrderNo() + "尾款已付订单，进入下一阶段";
            stepResult.setMessage(operateMessage);
            stepResult.setNextStepClass(Step010CheckOrderTransferStatus.class);
        }
        stepResult.setStatus(StepStatus.SUCCESS);
        return stepResult;
    }
}
