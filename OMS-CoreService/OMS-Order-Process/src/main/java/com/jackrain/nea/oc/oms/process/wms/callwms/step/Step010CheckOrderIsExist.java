package com.jackrain.nea.oc.oms.process.wms.callwms.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;

/**
 * 判断订单是否存在，状态是否正确
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:21
 */
@Step(order = 10, description = "判断订单是否存在，状态是否正确")
public class Step010CheckOrderIsExist implements IOmsOrderProcessStep<OcBOrderRelation> {
    @Override
    public ProcessStepResult startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                          boolean isAutoMakeup, User operateUser) {
        return new ProcessStepResult(StepStatus.SUCCESS);
    }
}
