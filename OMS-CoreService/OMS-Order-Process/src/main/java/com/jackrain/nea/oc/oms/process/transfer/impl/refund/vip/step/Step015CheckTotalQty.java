package com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.step;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrder;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrderItemEx;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 19:22 2020/6/19
 * description ：唯品会退供单-校验退供单中间表商品总数量和子表总条数是否一致
 * @ Modified By：
 */
@Step(order = 15, description = "校验退供单中间表商品总数量和子表总条数是否一致")
@Slf4j
@Component
public class Step015CheckTotalQty extends BaseVipReturnOrderProcessStep
        implements IOmsOrderProcessStep<IpVipReturnOrderRelation> {
    @Override
    public ProcessStepResult<IpVipReturnOrderRelation> startProcess(IpVipReturnOrderRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpVipReturnOrderRelation> stepResult = new ProcessStepResult<>();
        String orderNo = orderInfo.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            stepResult.setMessage("异常请求单据编号为空!");
            stepResult.setStatus(StepStatus.FAILED);
            return stepResult;
        }
        long orderId = orderInfo.getOrderId();
        IpBVipReturnOrder vipReturnOrder = orderInfo.getVipReturnOrder();
        List<IpBVipReturnOrderItemEx> vipReturnOrderItems = orderInfo.getVipReturnOrderItems();
        BigDecimal totalQty = vipReturnOrder.getTotalQty() == null ? BigDecimal.ZERO : vipReturnOrder.getTotalQty();
        if (CollectionUtils.isEmpty(vipReturnOrderItems)) {
            stepResult.setMessage("单据编号=" + orderNo + "，orderId=" + orderId + "，退供单中间表明细为空");
            stepResult.setStatus(StepStatus.FAILED);
            String remark = "退供单中间表明细为空！";
            ipVipReturnOrderService.updateRemark(remark, orderNo, TransferOrderStatus.TRANSFERFAIL.toInteger());
        } else {
            //获取退供单明细的数量和主表对比
            BigDecimal totalQtyItems = vipReturnOrderItems.stream().map(IpBVipReturnOrderItemEx::getQty)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (totalQty.intValue() == totalQtyItems.intValue()) {
                stepResult.setStatus(StepStatus.SUCCESS);
            } else {
                String message = "单据编号={}" + orderNo + "，orderId={}" + orderId + "，退供单中间表商品总数量与明细数量不一致！";
                stepResult.setMessage(message);
                stepResult.setStatus(StepStatus.FINISHED);
                String remark = "退供单中间表商品总数量与明细数量不一致！";
                ipVipReturnOrderService.updateRemark(remark, orderNo, TransferOrderStatus.TRANSFERFAIL.toInteger());
            }
        }

        return stepResult;
    }
}
