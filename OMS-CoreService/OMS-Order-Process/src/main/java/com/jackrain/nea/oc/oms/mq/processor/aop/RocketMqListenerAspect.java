//package com.jackrain.nea.oc.oms.mq.processor.aop;
//
//import com.alibaba.fastjson.JSONObject;
//import com.aliyun.openservices.ons.api.Action;
//import com.aliyun.openservices.ons.api.Message;
//import com.aliyun.openservices.ons.api.impl.util.MsgConvertUtil;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.model.table.OcBMessageConsumeLog;
//import com.jackrain.nea.oc.oms.nums.MessageConsumeStatusEnum;
//import com.jackrain.nea.oc.oms.resource.ProcessRedisKeyResources;
//import com.jackrain.nea.oc.oms.services.OcBMessageConsumeLogService;
//import com.jackrain.nea.redis.tlock.RedisReentrantLock;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.exception.ExceptionUtils;
//import org.aspectj.lang.JoinPoint;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.*;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
//import java.util.Objects;
//import java.util.concurrent.TimeUnit;
//
///**
// * Description： RocketMq消息监听切面
// * Author: RESET
// * Date: Created in 2020/8/23 17:33
// * Modified By:
// */
////@Aspect
////@Component
////@Order(1)
//@Slf4j
//public class RocketMqListenerAspect {
//
//    // 打印日志时间
//    static final String KEY_SPEND_TIME = "MQPROCESSSPENDTIME";
//    // 打印超长时间（超过预设时间）
//    static final String KEY_SPEND_TIME_SLOW = "SPENDTIMEOVERSETTINGTIME";
//    // apollo控制日志监听开关
//    static final String KEY_SWITCHER_APOLLO = "r3.oc.oms.mq.log.switch";
//    // 开关默认值
//    static final String VAL_DEFAULT_SWITCH = "true";
//
//    // 锁超时
//    private static final int LOCK_LOG_AUTO_TIMEOUT = 60 * 1000;
//
//    /**
//     * 切面
//     */
//    @Pointcut("execution(public * com.jackrain.nea.oc.oms.mq.processor.impl.mq.RefundOrderToWmsReceiptBackMq.consume(..))")
//    public void pointcut() {
//    }
//
//    /**
//     * 切面处理
//     * @param joinPoint
//     * @return
//     * @throws Throwable
//     */
//    @AfterReturning(pointcut = "pointcut()", returning = "result")
//    public void doAfterReturning(JoinPoint joinPoint, Object result) {
//        if (getLogSwitch()) {
//            long startTime = System.currentTimeMillis();
//            // 记录
//            log(joinPoint, result, startTime, null);
//        }
//    }
//
//    /**
//     * 异常情况的记录
//     * @param joinPoint
//     * @param throwable
//     */
//    @AfterThrowing(pointcut = "pointcut()", throwing = "throwable")
//    public void doThrowing(JoinPoint joinPoint, Throwable throwable) {
//        if (getLogSwitch()) {
//            long startTime = System.currentTimeMillis();
//            Object result = Action.ReconsumeLater;
//            // 记录
//            log(joinPoint, result, startTime, throwable);
//        }
//    }
//
//    /**
//     * 具体处理逻辑
//     * 职能
//     *  1. 日志打印消息处理时间
//     *  2. 记录错误日志到数据库
//     * @param joinPoint
//     * @param result
//     * @param startTime
//     */
//    private void log(JoinPoint joinPoint, Object result, long startTime, Throwable throwable) {
//        try {
//            Message message = getParameter(joinPoint);
//
//            if (Objects.nonNull(message)) {
//                // 锁信息
//                String lockRedisKey = ProcessRedisKeyResources.buildLockMessageLogKey(message.getTopic(), message.getMsgID());
//                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
//
//                try {
//                    // 上锁
//                    if (redisLock.tryLock(LOCK_LOG_AUTO_TIMEOUT, TimeUnit.MILLISECONDS)) {
//                        // 打印耗时
//                        logSendTime(message, startTime);
//
//                        // 前提：返回的是符合要求的对象
//                        if (result instanceof Action) {
//                            Action act = (Action)result;
//
//                            String stackTrace = null;
//                            if (Objects.nonNull(throwable)) {
//                                stackTrace = ExceptionUtils.getStackTrace(throwable);
//                            }
//
//                            if (Action.ReconsumeLater.equals(act) || Objects.nonNull(throwable)) {
//                                if (message.getReconsumeTimes() > 0) {
//                                    OcBMessageConsumeLogService.getInstance().updateToRetryErrorByUnique(
//                                            message.getTopic(), message.getMsgID(), message.getReconsumeTimes(), stackTrace,null);
//                                } else {
//                                    // 没有消费成功，才需要处理，消费成功的暂不处理
//                                    OcBMessageConsumeLog consumeLog = buildMessageConsumeLogByMessage(message);
//                                    consumeLog.setErrorMsg(stackTrace);
//
//
//                                    if (Objects.nonNull(consumeLog)) {
//                                        // 如无特殊需求，用户对象不用传，里面会取系统默认
//                                        OcBMessageConsumeLogService.getInstance().save(consumeLog, null);
//                                    }
//                                }
//                            } else {
//                                // @20200825 成功可能是重试成功的，需要更新
//                                if (message.getReconsumeTimes() > 0) {
//                                    OcBMessageConsumeLogService.getInstance().updateToRetrySuccessByUnique(
//                                            message.getTopic(), message.getMsgID(), message.getReconsumeTimes(),null);
//                                }
//                            }
//                        }
//                    } else {
//                        log.info("RocketMqListenerAspect.log.info.lock: {}/{}/{}, message in processing.",
//                                message.getTopic(), message.getMsgID(), message.getReconsumeTimes());
//                    }
//                } catch (Exception e) {
//                    log.error("RocketMqListenerAspect.log.error", e);
//                } finally {
//                    redisLock.unlock();
//                }
//            }
//        } catch (Throwable e) {
//            // 错误不影响主体流程，记录错误日志
//            log.error("RocketMqListenerAspect.log.error", e);
//        }
//    }
//
////    /**
////     * 构建消息日志对象
////     * @param joinPoint
////     * @return
////     */
////    private OcBMessageConsumeLog buildMessageConsumeLogByMessage(ProceedingJoinPoint joinPoint) {
////        Message message = getParameter(joinPoint);
////
////        if (Objects.nonNull(message)) {
////            return buildMessageConsumeLogByMessage(message);
////        }
////
////        return null;
////    }
//
//    /**
//     * 构建消息日志对象
//     * @param message
//     * @return
//     */
//    private OcBMessageConsumeLog buildMessageConsumeLogByMessage(Message message) {
//        if (Objects.nonNull(message)) {
//            String topic = message.getTopic();
//            String tag = message.getTag();
//            String messageId = message.getMsgID();
//            String messageKey = message.getKey();
//            Object body = null;
//            String method = null;
//
//            if (Objects.nonNull(message.getBody())) {
//                try {
//                    // 反序列化
//                    String messageBody = MsgConvertUtil.objectDeserialize(message.getBody()).toString();
//                    JSONObject result = JSONObject.parseObject(messageBody);
//
//                    body = result;
//                    method = result.getString("method");
//                } catch (Exception e) {
//                    log.error("objectDeserialize.error", e);
//                    // 保存堆栈，方便查找问题
//                    body = ExceptionUtils.getStackTrace(e);
//                }
//            }
//
//            // 封参数
//            OcBMessageConsumeLog consumeLog = new OcBMessageConsumeLog();
//
//            consumeLog.setTopic(topic);
//            consumeLog.setTag(tag);
//            consumeLog.setMessageId(messageId);
//            consumeLog.setMessageKey(messageKey);
//            consumeLog.setMessageKey(method);
//            consumeLog.setBody(Objects.isNull(body)?null:body.toString());
//            consumeLog.setRetryCount(0);
//            consumeLog.setConsumeStatus(MessageConsumeStatusEnum.ERROR.getValue());
//
//            return consumeLog;
//        }
//
//        return null;
//    }
//
//    /**
//     * 解析请求参数
//     * @param joinPoint
//     * @return
//     */
//    private Message getParameter(JoinPoint joinPoint) {
//        Object[] args = joinPoint.getArgs();
//
//        if (args != null && args.length > 0) {
//            if (args[0] instanceof  Message) {
//                return (Message)args[0];
//            }
//        }
//
//        return null;
//    }
//
//    /**
//     * 打印耗时
//     * @param message
//     * @param startTime
//     */
//    private void logSendTime(Message message, long startTime) {
//        String topic = null;
//        String tag = null;
//        String messageId = null;
//
//        if (Objects.nonNull(message)) {
//            topic = message.getTopic();
//            tag = message.getTag();
//            messageId = message.getMsgID();
//        }
//
//        logSendTime(topic, tag, messageId, startTime);
//    }
//
//    /**
//     * 打印耗时
//     * @param startTime
//     */
//    private void logSendTime(String topic, String tag, String messageId, long startTime) {
//        long spendTime = System.currentTimeMillis() - startTime;
//        log.info("{}.{}.{}.{}:{}", topic, tag, messageId, KEY_SPEND_TIME, spendTime);
//
//        long overTime = getOverTime();
//        if (spendTime > overTime) {
//            log.error("{}.{}.{}.{}:{}/{}", topic, tag, messageId, KEY_SPEND_TIME_SLOW, spendTime, overTime);
//        }
//    }
//
//    /**
//     * 获取预设时间
//     * @return
//     */
//    private long getOverTime() {
//        // 超过此时间将打印一条错误日志
//        return 500L;
//    }
//
//    /**
//     * 是否监听日志的开关，配置在apollo
//     * @return
//     */
//    private boolean getLogSwitch() {
//        // KEY_SWITCHER_APOLLO
//        PropertiesConf env = ApplicationContextHandle.getBean(PropertiesConf.class);
//        String val = env.getProperty(KEY_SWITCHER_APOLLO, VAL_DEFAULT_SWITCH);
//        return VAL_DEFAULT_SWITCH.equalsIgnoreCase(val);
//    }
//
//}
