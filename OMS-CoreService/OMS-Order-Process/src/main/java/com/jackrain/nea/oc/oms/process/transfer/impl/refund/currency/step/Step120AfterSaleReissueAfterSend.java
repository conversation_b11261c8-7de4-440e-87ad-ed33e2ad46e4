package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2023/3/31
 */
@Slf4j
@Component
@Step(order = 120, description = "售后补寄-发货后")
public class Step120AfterSaleReissueAfterSend extends BaseStanPlatRefundProcessStep implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {
    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        IpBStandplatRefund ipStandardRefund = orderInfo.getIpBStandplatRefund();
        String orderNo = ipStandardRefund.getOrderNo();
        String returnNo = ipStandardRefund.getReturnNo();


        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("afterSend.start", orderNo, returnNo));
        }

        ProcessStepResult stepResult = handleEachReturnStatusProcess(orderInfo);
        return stepResult;

    }

    /**
     * 售后流程处理
     *
     * @param orderInfo
     * @return
     */
    private ProcessStepResult handleEachReturnStatusProcess(OmsStandPlatRefundRelation orderInfo) {
        IpBStandplatRefund ipStandardRefund = orderInfo.getIpBStandplatRefund();
        Integer returnStatus = ipStandardRefund.getReturnStatus();
        IpBStandplatRefudStatusEnum statusEnum = IpBStandplatRefudStatusEnum.val2Enum(returnStatus);
        ProcessStepResult stepResult = null;
        switch (statusEnum) {
            case CLOSED:
                // 售后关闭
                afterSaleReissueCloseProcess(orderInfo);
                break;
            case WAIT_BUYER_RETURN_GOODS:
            case WAIT_SELLER_CONFIRM_GOODS:
                stepResult = handleOmsOrderIsAllPlatSend(orderInfo);
                if (StepStatus.SUCCESS != stepResult.getStatus()) {
                    break;
                }
                stepResult = afterSaleReissueSellerAgreeProcess(orderInfo);
                break;
            case SUCCESS:
            case SELLER_REFUSE_BUYER:
                // 卖家拒绝 | 售后成功
                stepResult = finishTransProcess(ipStandardRefund, TransferOrderStatus.TRANSFERRED, statusEnum.getKey());
                break;
            default:
                stepResult = finishTransProcess(ipStandardRefund, TransferOrderStatus.TRANSFERFAIL, statusEnum.getKey());
                break;
        }
        return stepResult;
    }

    /**
     * 售后补寄流程
     * 售后关闭
     *
     * @param orderInfo
     * @return
     */
    private ProcessStepResult afterSaleReissueCloseProcess(OmsStandPlatRefundRelation orderInfo) {

        ValueHolderV14 result = omsAfterSaleReissueService.afterSaleReissueCloseProcess(orderInfo);
        ProcessStepResult<Object> stepResult;
        IpBStandplatRefund refund = orderInfo.getIpBStandplatRefund();
        if (result.isOK()) {
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), result.getMessage(), refund);
            stepResult = new ProcessStepResult<>(StepStatus.SUCCESS);
        } else {
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), result.getMessage(), refund);
            stepResult = new ProcessStepResult<>(StepStatus.FAILED, result.getMessage());
        }
        return stepResult;
    }

    /**
     * 售后补寄流程
     * 卖家同意
     *
     * @param orderInfo
     * @return
     */
    private ProcessStepResult afterSaleReissueSellerAgreeProcess(OmsStandPlatRefundRelation orderInfo) {

        ValueHolderV14 result = omsAfterSaleReissueService.afterSaleReissueSellerAgreeProcess(orderInfo);
        ProcessStepResult<Object> stepResult = new ProcessStepResult<>();
        IpBStandplatRefund refund = orderInfo.getIpBStandplatRefund();
        if (result.isOK()) {
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), result.getMessage(), refund);
            stepResult.setStatus(StepStatus.SUCCESS);
        } else {
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), result.getMessage(), refund);
            stepResult.setStatus(StepStatus.FAILED);
            stepResult.setMessage(result.getMessage());
        }
        return stepResult;
    }

    /**
     * 判断是否全部平台发货
     *
     * @param orderInfo
     * @return
     */
    private ProcessStepResult handleOmsOrderIsAllPlatSend(OmsStandPlatRefundRelation orderInfo) {
        List<OmsOrderRelation> omsRelations = orderInfo.getOmsOrderRelation();
        boolean isAllPlatSend = true;
        for (OmsOrderRelation omsRelation : omsRelations) {
            Integer status = omsRelation.getOcBOrder().getOrderStatus();
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(status)
                    || OmsOrderStatus.DEAL_DONE.toInteger().equals(status)) {
                continue;
            }
            isAllPlatSend = false;
            break;
        }
        if (isAllPlatSend) {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "");
        }
        String tipMessage = "存在未平台发货的订单";
        IpBStandplatRefund refund = orderInfo.getIpBStandplatRefund();
        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERFAIL.toInteger(), tipMessage, refund);
        return new ProcessStepResult<>(StepStatus.FAILED, tipMessage);
    }

    /**
     * 结束转换
     *
     * @param refund
     * @param message
     * @return
     */
    private ProcessStepResult finishTransProcess(IpBStandplatRefund refund, TransferOrderStatus status, String message) {
        message = message + ", 转换结束";
        ipStandplatRefundService.updateReturnOrder(status.toInteger(), message, refund);
        return new ProcessStepResult<>(StepStatus.FINISHED, message);
    }

}
