package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jitx.step;

import com.google.common.collect.Lists;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * description：检查关联时效订单状态
 *
 * <AUTHOR>
 * @date 2021/8/31
 */
@Step(order = 25, description = "检查关联时效订单状态")
@Slf4j
@Component
public class Step25JitxCheckTimeOrderStatus extends BaseJitxOrderProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {


    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {

        ProcessStepResult<IpJitxOrderRelation> stepResult = new ProcessStepResult<>();
        IpBJitxOrder jitxOrder = orderInfo.getJitxOrder();
        String orderSn = jitxOrder.getOrderSn();
        if (log.isDebugEnabled()) {
            log.debug("自动流程----Step25JitxCheckTimeOrderStatus [检查关联时效订单状态],orderSn:{}", orderSn);
        }
        List<IpBTimeOrderVip> timeOrderVipList = orderInfo.getTimeOrderVipList();
        if (CollectionUtils.isNotEmpty(timeOrderVipList)) {
            //判断时效订单状态
            IpBTimeOrderVip firstTimeOrder = timeOrderVipList.get(0);
            if (log.isDebugEnabled()) {
                log.debug("Step25JitxCheckTimeOrderStatus 当前关联时效订单状态firstTimeOrder.status:{}", firstTimeOrder.getStatus());
            }
            Integer firstStatus = firstTimeOrder.getStatus();
            if (timeOrderVipList.size() > 1) {
                //存在多个时效订单且状态不一致 直接标记为未转换,退出转换
                if (firstStatus == null) {
                    String msg = "关联时效订单状态异常!";
                    this.ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(), TransferOrderStatus.TRANSFERFAIL, msg);
                    return new ProcessStepResult<>(StepStatus.FAILED, msg);
                }
                Optional<IpBTimeOrderVip> existDiffentStatusOrder = timeOrderVipList.stream().filter(x -> !firstStatus.equals(x.getStatus())).findAny();
                if (existDiffentStatusOrder.isPresent()) {
                    //存在占单中 或者 寻仓中 标记为未转换
                    Optional<IpBTimeOrderVip> statusInSingleOrSeekIng = timeOrderVipList.stream().filter(x -> TimeOrderVipStatusEnum.IN_SINGLE.getValue().equals(x.getStatus()) || TimeOrderVipStatusEnum.IN_SEEKING_STORE.getValue().equals(x.getStatus())).findAny();
                    //占单成功 缺货 待占单 已寻仓 全部在
                    List<Integer> statusList = Lists.newArrayList(TimeOrderVipStatusEnum.CREATED.getValue(), TimeOrderVipStatusEnum.OCCUPIED.getValue(), TimeOrderVipStatusEnum.OUT_STOCK.getValue(), TimeOrderVipStatusEnum.SEEKING_STORE_SUCCESS.getValue());
                    Optional<IpBTimeOrderVip> statusArr = timeOrderVipList.stream().filter(x -> !statusList.contains(x.getStatus())).findAny();
                    //存在已完成
                    Optional<IpBTimeOrderVip> statusMatched = timeOrderVipList.stream().filter(x -> TimeOrderVipStatusEnum.MATCHED.getValue().equals(x.getStatus())).findAny();
                    if (statusInSingleOrSeekIng.isPresent()) {
                        String msg = "关联时效订单占单中、寻仓中标记为未转换，退出转换！";
                        //占单中 寻仓中
                        this.ipJitxOrderService.updateJitxOrderTransStatus(orderSn, TransferOrderStatus.NOT_TRANSFER, msg);
                        return new ProcessStepResult<>(StepStatus.FINISHED, msg);
                    } else if (!statusArr.isPresent()) {
                        //发货单记录时效订单
                        this.setSkuTimeOrderItemMap(orderInfo);
                        //需要更新时效订单为完成
                        orderInfo.setNeedUpdateTimeOrder(true);
                        String operateMessage = Resources.getMessage("单据" + orderSn + "关联时效订单全部在[待占单|占单成功|缺货|已寻仓]，进入下一阶段");
                        return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
                    } else if (statusMatched.isPresent()) {
                        //已完成
                        String msg = "关联时效订单已完成，标记为转换完成，退出转换！";
                        this.ipJitxOrderService.updateJitxOrderTransStatus(orderSn, TransferOrderStatus.TRANSFERRED, msg);
                        return new ProcessStepResult<>(StepStatus.FINISHED, msg);
                    }
                    String msg = "关联时效订单多个且状态不一致，标记为未转换，退出转换!";
                    this.ipJitxOrderService.updateJitxOrderTransStatus(orderInfo.getOrderNo(), TransferOrderStatus.NOT_TRANSFER, msg);
                    return new ProcessStepResult<>(StepStatus.FINISHED, msg);
                } else {
                    return this.checkTimeOrderStatus(orderSn, firstStatus, jitxOrder.getDeliveryWarehouse(), firstTimeOrder.getId(), orderInfo);
                }

            } else {
                return this.checkTimeOrderStatus(orderSn, firstStatus, jitxOrder.getDeliveryWarehouse(), firstTimeOrder.getId(), orderInfo);
            }
        } else {
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "不存在时效订单，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        }
    }

    private ProcessStepResult checkTimeOrderStatus(String orderNo, Integer status, String jitxWarehouseCode, Long timeOrderId, IpJitxOrderRelation orderInfo) {
        if (log.isDebugEnabled()) {
            log.debug("Step25JitxCheckTimeOrderStatus 检查时效单状态：timeOrderId:{},orderNo:{},status:{}", timeOrderId, orderNo, status);
        }
        //占单成功 缺货 待占单 全部在
        List<Integer> statusList = Lists.newArrayList(TimeOrderVipStatusEnum.CREATED.getValue(), TimeOrderVipStatusEnum.OCCUPIED.getValue(), TimeOrderVipStatusEnum.OUT_STOCK.getValue());
        if (TimeOrderVipStatusEnum.IN_SINGLE.getValue().equals(status) || TimeOrderVipStatusEnum.IN_SEEKING_STORE.getValue().equals(status)) {
            String msg = "关联时效订单占单中、寻仓中标记为未转换，退出转换！";
            //占单中 寻仓中
            this.ipJitxOrderService.updateJitxOrderTransStatus(orderNo, TransferOrderStatus.NOT_TRANSFER, msg);
            return new ProcessStepResult<>(StepStatus.FINISHED, msg);
        } else if (statusList.contains(status)) {
            //发货单记录时效订单
            this.setSkuTimeOrderItemMap(orderInfo);
            //需要更新时效订单为完成
            orderInfo.setNeedUpdateTimeOrder(true);
            String operateMessage = Resources.getMessage("单据" + orderNo + "关联时效订单待占单|占单成功|缺货，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        } else if (TimeOrderVipStatusEnum.SEEKING_STORE_SUCCESS.getValue().equals(status)) {
            //判断所有的时效订单缺货情况
            int timeOrderOutStockCount = this.getOutStockCount(orderInfo);
            //已寻仓
            //发货单记录时效订单
            this.setSkuTimeOrderItemMap(orderInfo);
            //全部不缺货才需要判断仓库是否一致
            if (timeOrderOutStockCount == 0) {
                //判断时效订单占用仓库是否与JITX订单仓库一致
                IpBTimeOrderVipOccupyItem ipBTimeOrderVipOccupyItem = ipVipTimeOrderService.selectOneTimeOrderOccupyItemByOrderId(timeOrderId);
                if (log.isDebugEnabled()) {
                    log.debug("Step25JitxCheckTimeOrderStatus 关联时效订单寻仓成功,判断与JITX仓库是否一致:jitxWarehouseCode:{},timeOrderOccupyItemWarehouse:{}", jitxWarehouseCode, ipBTimeOrderVipOccupyItem.getWarehouseCode());
                }

                if(!ObjectUtils.isEmpty(ipBTimeOrderVipOccupyItem)){
                    orderInfo.setTimOrderItemWareHouseId(ipBTimeOrderVipOccupyItem.getCpCPhyWarehouseId());
                }

                if (StringUtils.isEmpty(jitxWarehouseCode)) {
                    this.ipJitxOrderService.updateJitxOrderTransStatus(orderNo, TransferOrderStatus.NOT_TRANSFER, "JITX发货仓库为空");
                    return new ProcessStepResult<>(StepStatus.FAILED, "JITX发货仓库为空");
                }

                String oldJitxWarehouseCode = ipBTimeOrderVipOccupyItem.getWarehouseCode();
                List<IpBJitxDeliveryRecord> deliveryRecordList = deliveryRecordService.selectRecordOccupied(orderInfo.getJitxOrder().getOrderSn());
                if(CollectionUtils.isNotEmpty(deliveryRecordList)){
                    Optional<IpBJitxDeliveryRecord> recordOptional = deliveryRecordList.stream().max(Comparator.comparing(IpBJitxDeliveryRecord::getApplicationTime));
                    if(recordOptional.isPresent()){
                        oldJitxWarehouseCode = recordOptional.get().getVipStoreCode();
                    }
                }

                //设置仓库是否一致标识
                orderInfo.setSameWarehouse(jitxWarehouseCode.equals(oldJitxWarehouseCode));
            }

            //需要更新时效订单为完成
            orderInfo.setNeedUpdateTimeOrder(true);
            String operateMessage = Resources.getMessage("单据" + orderNo + "关联时效订单已寻仓，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);

        } else if (TimeOrderVipStatusEnum.MATCHED.getValue().equals(status)) {
            String msg = "转换异常,未找到零售发货单且时效订单状态已完成";
            this.ipJitxOrderService.updateJitxOrderTransStatus(orderNo, TransferOrderStatus.NOT_TRANSFER, msg);
            return new ProcessStepResult<>(StepStatus.FINISHED, msg);

        } else if (TimeOrderVipStatusEnum.CANCELLED.getValue().equals(status)) {
            //已取消
            String msg = "关联时效订单已取消，标记为转换异常，退出转换！";
            this.ipJitxOrderService.updateJitxOrderTransStatus(orderNo, TransferOrderStatus.TRANSFERRED, msg);
            return new ProcessStepResult<>(StepStatus.FINISHED, msg);
        } else {
            String msg = "关联时效订单状态异常，标记为转换异常，退出转换！";
            this.ipJitxOrderService.updateJitxOrderTransStatus(orderNo, TransferOrderStatus.TRANSFERRED, msg);
            return new ProcessStepResult<>(StepStatus.FINISHED, msg);
        }
    }

    private void setSkuTimeOrderItemMap(IpJitxOrderRelation orderInfo) {
        List<IpBTimeOrderVip> timeOrderVipList = orderInfo.getTimeOrderVipList();
        if (CollectionUtils.isNotEmpty(timeOrderVipList)) {
            if (log.isDebugEnabled()) {
                log.debug("Step25JitxCheckTimeOrderStatus 发货单记录时效订单ID");
            }
            List<Long> timeOrderList = timeOrderVipList.stream().map(IpBTimeOrderVip::getId).distinct().collect(Collectors.toList());
            List<IpBTimeOrderVipItem> ipBTimeOrderVipItems = ipVipTimeOrderService.selectTimeOrderItemByOrderIdList(timeOrderList);
            Map<String, List<IpBTimeOrderVipItem>> skuTimeOrderItemMap = ipBTimeOrderVipItems.stream().collect(Collectors.groupingBy(IpBTimeOrderVipItem::getBarcode));
            orderInfo.setSkuTimeOrderItemMap(skuTimeOrderItemMap);
        }
    }

    private int getOutStockCount(IpJitxOrderRelation orderInfo) {
        List<IpBTimeOrderVip> timeOrderVipList = orderInfo.getTimeOrderVipList();
        if (log.isDebugEnabled()) {
            log.debug("判断时效订单缺货状态开始,时效订单数量：{}", timeOrderVipList.size());
        }
        List<Long> timeOrderIdList = timeOrderVipList.stream().map(IpBTimeOrderVip::getId).collect(Collectors.toList());
        List<IpBTimeOrderVipItem> ipBTimeOrderVipItems = ipVipTimeOrderService.selectTimeOrderItemByOrderIdList(timeOrderIdList);
        Map<Long, List<IpBTimeOrderVipItem>> timeOrderItemMap = ipBTimeOrderVipItems.stream().collect(Collectors.groupingBy(IpBTimeOrderVipItem::getIpBTimeOrderVipId));
        int outStockCount = 0;
        boolean isPartTimeOrderOutStock = false;
        for (int i = 0; i < timeOrderVipList.size(); i++) {
            IpBTimeOrderVip ipBTimeOrderVip = timeOrderVipList.get(i);
            //主表缺货数量
            BigDecimal outStock = ipBTimeOrderVip.getOutStockQuantity();
            List<IpBTimeOrderVipItem> vipItemList = timeOrderItemMap.get(ipBTimeOrderVip.getId());
            if (CollectionUtils.isNotEmpty(vipItemList)) {
                IpBTimeOrderVipItem ipBTimeOrderVipItem = vipItemList.get(0);
                BigDecimal vipItemAmount = ipBTimeOrderVipItem.getAmount();
                //主表缺货数量存在大于0 则表示存在缺货 不走仓库是否一致判断
                if (outStock.compareTo(BigDecimal.ZERO) > 0) {
                    outStockCount++;
                }
                //存在部分缺货 即占用了部分 需要取消占用
                if (outStock.compareTo(BigDecimal.ZERO) > 0 && outStock.compareTo(vipItemAmount) != 0) {
                    isPartTimeOrderOutStock = true;
                }
            }
        }
        orderInfo.setNeedCancelStockOccupy(isPartTimeOrderOutStock);
        if (log.isDebugEnabled()) {
            log.debug("判断时效订单缺货状态结束,时效订单缺货数量：{},时效订单部分缺货：{}", outStockCount, isPartTimeOrderOutStock);
        }
        return outStockCount;
    }
}
