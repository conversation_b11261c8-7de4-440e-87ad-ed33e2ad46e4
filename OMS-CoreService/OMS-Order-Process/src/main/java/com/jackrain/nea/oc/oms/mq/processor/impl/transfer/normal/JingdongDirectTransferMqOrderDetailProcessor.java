package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.normal;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.jddirect.JingdongDirectTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: 黄世新
 * @Date: 2022/3/28 下午4:06
 * @Version 1.0
 */
@Slf4j
public class JingdongDirectTransferMqOrderDetailProcessor implements IMqOrderDetailProcessor  {


    @Autowired
    private JingdongDirectTransferOrderProcessImpl jingdongDirectTransferOrderProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        IpJingdongDirectOrderRelation relation = new IpJingdongDirectOrderRelation();
        IpBJingdongDirect direct = new IpBJingdongDirect();
        direct.setId(orderMqInfo.getOrderId());
        direct.setCustomOrderId(orderMqInfo.getOrderNo());
        relation.setIpBJingdongDirect(direct);
        return jingdongDirectTransferOrderProcess.start(relation,false, SystemUserResource.getRootUser());
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.JINGDONG_DIRECT
                && orderMqInfo.getOrderType() == OrderType.NORMAL;
    }
}
