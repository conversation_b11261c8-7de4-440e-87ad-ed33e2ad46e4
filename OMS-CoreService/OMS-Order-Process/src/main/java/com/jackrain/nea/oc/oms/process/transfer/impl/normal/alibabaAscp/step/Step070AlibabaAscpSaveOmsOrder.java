package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.step;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.log.LogCat;
import com.jackrain.nea.log.LogEvent;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.ToBeConfirmedOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.tag.TaggerManager;
import com.jackrain.nea.resource.BllSystemParameterKeyResources;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 保存订单信息
 *
 * @author: 秦雄飞
 * @date 2020/9/3 下午4:32
 */
@Step(order = 70, description = "保存订单信息")
@Slf4j
@Component
public class Step070AlibabaAscpSaveOmsOrder extends BaseAlibabaAscpOrderProcessStep
        implements IOmsOrderProcessStep<IpAlibabaAscpOrderRelation> {

//    @Autowired
//    private R3MqSendHelper sendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private ToBeConfirmedOrderMqConfig toBeConfirmedOrderMqConfig;

    /**
     * 发送MQ消息
     *
     * @param saveOrderInfo 保存的OrderInfo订单信息
     * @param logEventList  logEvent列表
     */
    private void sendMQ(OcBOrderRelation saveOrderInfo, List<LogEvent> logEventList) {
        LogEvent eventMQ = LogCat.newEvent(Step070AlibabaAscpSaveOmsOrder.class.getSimpleName(), "StartSendMQ");
        if (saveOrderInfo != null) {
            long saveOrderId = saveOrderInfo.getOrderInfo().getId();
            String billNo = saveOrderInfo.getOrderInfo().getBillNo();
            String msgKey = "ALIASCP_TR_" + saveOrderId + "_" + billNo;
            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
            orderMqInfo.setChannelType(this.getCurrentChannelType());
            orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
            orderMqInfo.setOrderId(saveOrderId);
            orderMqInfo.setOrderNo(billNo);
            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
            mqInfoList.add(orderMqInfo);
            String jsonValue = JSONObject.toJSONString(mqInfoList);

            String messageId = null;
            try {
//                messageId = sendHelper.sendMessage(jsonValue, toBeConfirmedOrderMqConfig.getSendToBeConfirmMqTopic(),
//                        toBeConfirmedOrderMqConfig.getSendToBeConfirmTag(),
//                        msgKey);
                MqSendResult result = defaultProducerSend.sendTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_TOBECONFIRMED, Mq5Constants.TAG_R3_OC_OMS_CALL_TOBECONFIRMED, jsonValue, msgKey);
                messageId = result.getMessageId();
            } catch (Exception e) {
                log.error("发送mq消息异常");
            }

            eventMQ.addData("MQId", messageId);
            eventMQ.addData("MQKey", msgKey);
        }
        eventMQ.complete();
        logEventList.add(eventMQ);
    }

    @Override
    public ProcessStepResult<IpAlibabaAscpOrderRelation> startProcess(IpAlibabaAscpOrderRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        try {
            List<LogEvent> logEventList = new ArrayList<>();
            LogEvent eventConvert = LogCat.newEvent(Step070AlibabaAscpSaveOmsOrder.class.getSimpleName(), "ConvertOrder");
            OcBOrderRelation saveOrderInfo = orderService.
                    convertAlibabaAscpOrderToOrder(orderInfo);
            TaggerManager.get().doTag(saveOrderInfo.getOrderInfo(), saveOrderInfo.getOrderItemList());

            eventConvert.complete();
            logEventList.add(eventConvert);

            LogEvent eventStartSave = LogCat.newEvent(Step070AlibabaAscpSaveOmsOrder.class.getSimpleName(), "SaveOrder");
            boolean saveResult = orderService.saveOmsOrderInfoAlibabaAscp(saveOrderInfo, false, operateUser);
            if (!saveResult) {
                saveOrderInfo = null;
            }
            eventStartSave.complete();
            logEventList.add(eventStartSave);
            if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
                this.sendMQ(saveOrderInfo, logEventList);
            }

            ProcessStepResult<IpAlibabaAscpOrderRelation> stepResult = new ProcessStepResult<>();
            stepResult.setMessage("存储数据成功，进入下一阶段");
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step080AlibabaAscpUpdateOrderTransferStatus.class);
            stepResult.setLogEventList(logEventList);
            return stepResult;
        } catch (Exception ex) {
            log.error(LogUtil.format("Step070AlibabaAscpSaveOmsOrder:{}", "Step070AlibabaAscpSaveOmsOrder"), Throwables.getStackTraceAsString(ex));
            String errorMessage = "存储数据失败，退出转单服务;" + ex.getMessage();
            boolean updateStatusRes = ipAlibabaAscpOrderService.updateAlibabaAscpOrderTransStatus(orderNo,
                    TransferOrderStatus.NOT_TRANSFER, errorMessage);
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}