package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.st.model.enums.CalculationTypeEnum;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 反馈寻仓结果
 *
 * @author: chenxiulou
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 40, description = "反馈寻仓结果")
@Slf4j
@Component
public class Step040FeedBackDeliveryResult extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {
    @Autowired
    private IpVipTimeOrderService timeOrderService;
    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;


    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation deliveryRelation,
                                                                  ProcessStepResult preStepResult,
                                                                  boolean isAutoMakeup, User operateUser) {
        debugFormat("自动流程----Step040FeedBackDeliveryResult [调用分仓服务，订单分配到发货仓,反馈寻仓结果，更新中间表反馈结果]");
        try {
            String orderSn = deliveryRelation.getOrderNo();
            //  调用时效订单发货实体仓服务
            //ValueHolderV14 v14 = timeOrderService.getWarehouseCodeByOrderSn(orderSn, deliveryRelation);
            //获取反馈平台的仓库信息
            ValueHolderV14 v14 = timeOrderService.getFeedbackresult(deliveryRelation);
            log.info("自动流程----timeOrderService.getFeedbackresult  匹配时效订单实体仓结果：{}",
                    v14.toJSONObject().toJSONString());
            if (isCallbackSuccess(v14)) {
                String jitWarehouseCode = v14.getMessage();
                //反馈寻仓结果到线上
                ValueHolderV14 v1 = ipJitxDeliveryService.feedbackDeliveryResult(jitWarehouseCode, deliveryRelation, operateUser);
                log.info("ipJitxDeliveryService.feedbackDeliveryResult  反馈云枢纽结果：{}",
                        v1.toJSONObject().toJSONString());
                if (v1.getCode() == ResultCode.FAIL) {
                    ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.EXCEPTION, v1.getMessage());
                    return new ProcessStepResult<>(StepStatus.FAILED, v1.getMessage());
                } else {
                    ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.SYNCSUCCESS, "寻仓反馈完成，结束流程");
                    return new ProcessStepResult<>(StepStatus.FINISHED, "寻仓反馈完成，结束流程");
                }
            } else if (v14.getCode() == ResultCode.FAIL) {
                // 寻仓失败时，也反馈结果 edit by lwf 2020/06/11 13:55  [需求]自动下载，自动寻仓，寻仓结果回传（失败和成功时都传）
                ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.SYNCFAILD, v14.getMessage());
                return new ProcessStepResult<>(StepStatus.FAILED, v14.getMessage());
            } else {
                // 其它状态更新为反馈异常
                ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.EXCEPTION, v14.getMessage());
                return new ProcessStepResult<>(StepStatus.FAILED, v14.getMessage());
            }

        } catch (Exception ex) {
            String operateMessage = "寻仓订单OrderId" + deliveryRelation.getOrderId() + "反馈寻仓结果异常,异常信息-->" + ex.getMessage();
            log.error(LogUtil.format("反馈寻仓结果异常,异常信息:{}", "反馈寻仓结果异常", deliveryRelation.getOrderId()), Throwables.getStackTraceAsString(ex));
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
    }

    private boolean isCallbackSuccess(ValueHolderV14 v14) {
        return v14.getCode() == ResultCode.SUCCESS && v14.getMessage() != null;
    }

    private void debugFormat(String s) {
        if (log.isDebugEnabled()) {
            log.debug(s);
        }
    }
}
