package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.Standplat.IpBStandplatRefudStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2023/3/31
 */
@Slf4j
@Component
@Step(order = 110, description = "售后补寄-发货前")
public class Step110AfterSaleReissueBeforeSend extends BaseStanPlatRefundProcessStep implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    // scene 1
    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {

        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        String orderNo = ipBStandplatRefund.getOrderNo();
        String returnNo = ipBStandplatRefund.getReturnNo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("beforeSend.start...", orderNo, returnNo));
        }
        // check oms has before send order
        boolean hasBeforeSendOrder = checkHasBeforeSendOrder(orderInfo);
        if (hasBeforeSendOrder) {
            // close reissue
            boolean isCloseReissueBfSend = hasBeforeSendJudgeStep(orderInfo);
            if (isCloseReissueBfSend) {
                return new ProcessStepResult<>(StepStatus.SUCCESS, "success");
            }
            String remark = "原单不为仓库发货或平台发货";
            IpBStandplatRefund standPlatRefund = orderInfo.getIpBStandplatRefund();
            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), remark, standPlatRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "success");
    }

    /**
     * 判断是否存在发货前原始订单
     *
     * @param orderInfo
     * @return
     */
    private boolean checkHasBeforeSendOrder(OmsStandPlatRefundRelation orderInfo) {
        List<OmsOrderRelation> omsRelations = orderInfo.getOmsOrderRelation();
        for (OmsOrderRelation omsRelation : omsRelations) {
            OcBOrder order = omsRelation.getOcBOrder();
            Integer status = order.getOrderStatus();
            boolean isDelivery = OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(status)
                    || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(status)
                    || OmsOrderStatus.DELIVERED.toInteger().equals(status)
                    || OmsOrderStatus.DEAL_DONE.toInteger().equals(status);
            if (!isDelivery) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否存在发货前补寄单, 且退款关闭
     *
     * @param orderInfo
     * @return
     */
    private boolean hasBeforeSendJudgeStep(OmsStandPlatRefundRelation orderInfo) {
        List<OmsOrderRelation> omsRelations = orderInfo.getOmsOrderRelation();
        for (OmsOrderRelation omsRelation : omsRelations) {
            OcBOrder order = omsRelation.getOcBOrder();
            Integer status = order.getOrderStatus();
            boolean isDelivery = OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(status)
                    || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(status)
                    || OmsOrderStatus.DELIVERED.toInteger().equals(status)
                    || OmsOrderStatus.DEAL_DONE.toInteger().equals(status);
            if (isDelivery) {
                continue;
            }
            Integer orderType = order.getOrderType();
            Integer isResetShip = order.getIsResetShip();
            boolean isReissueOrder = OrderTypeEnum.REISSUE.getVal().equals(orderType) && OcBOrderConst.IS_STATUS_IY.equals(isResetShip);
            IpBStandplatRefund ipStandardRefund = orderInfo.getIpBStandplatRefund();
            Integer returnStatus = ipStandardRefund.getReturnStatus();
            boolean isClose = IpBStandplatRefudStatusEnum.CLOSED.getVal().equals(returnStatus);
            if (isReissueOrder && isClose) {
                return true;
            }
        }
        return false;
    }

}
