package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.ZtoLogisticsInterceptService;
import com.jackrain.nea.oc.oms.services.delivery.impl.OrderDeliveryOfStandplatmpl;
import com.jackrain.nea.oc.oms.util.OcBOrderDeliveryFailUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 拼多多苏宁MQ监控
 *
 * @program: R3-OC-OMS-V1.4
 * @author: Lijp
 * @create: 2019-07-19 13:36
 */
@Slf4j
@RocketMqMessageListener(name = "PddAndSNplatformMq", type = MqTypeEnum.CLOUD)
public class PddAndSNplatformMq implements BaseMessageListener {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderPlatformDeliveryService omsOrderPlatformDeliveryService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private OcBOrderDeliveryFailUtil deliveryFailUtil;
    @Resource
    private ZtoLogisticsInterceptService ztoLogisticsInterceptService;


    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object obj) {
        User user = SystemUserResource.getRootUser();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("拼多多苏宁MQ监控: {}", messageBody), messageBody);
            }
            JSONObject object = JSON.parseArray(messageBody).getJSONObject(0);
            boolean result = object.getBoolean("IS_SUCCESS");
            Long id = object.getLongValue("ID");
            String tid = object.getString("TID");
            String returnMessage = object.getString("MESSAGE");
            OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
            //新增日志信息
            String logMsg = "订单" + id + "(平台单号=" + tid + ")平台发货结果" + returnMessage;
            String errorMessage = object.get("MESSAGE") == null ? "" : object.get("MESSAGE").toString();
            omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, null, returnMessage, user);

            // 根据平台返回错误信息,不进行重试
            if (PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform())
                    && businessSystemParamService.getDouyinDeliveryMarkFail(errorMessage)) {
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setMakeupFailNum(999L);
                updateOrder.setSysremark("根据平台返回错误信息,不进行重试");
                updateOrder.setId(id);
                updateOrder.setModifieddate(new Date());
                ocBOrderMapper.updateById(updateOrder);
                return;
            }

            // 抖音特殊处理，抖音平台返回配置信息时直接标记平台发货
            if (PlatFormEnum.DOU_YIN.getCode().equals(ocBOrder.getPlatform())
                    && businessSystemParamService.getDouyinDeliveryMarkSuccess(errorMessage)) {
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setSysremark("不允许再次执行发货,标记平台发货");
                updateOrder.setId(id);
                ocBOrderMapper.updateById(updateOrder);
                result = true;
            }
            // 如果是 中台周期购的订单 此时下推的订单是母单的单号。需要找到子单的订单信息。发货失败表的数据也要写第一期子单的
            String businessTypeCode = ocBOrder.getBusinessTypeCode();
            boolean isKeywordsIntercept = true;
            if (OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(businessTypeCode) ||
                    OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER.getCode().equals(businessTypeCode)) {
                //王佳：中台周期购订单、中台周期购提货不调用关键字拦截
                isKeywordsIntercept = false;
            }
            OcBOrder pickUpOcBOrder = new OcBOrder();
            if (OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(businessTypeCode)) {
                // 找到第一期 仓库发货  提奶的订单
                List<OcBOrder> ocBOrderList = ocBOrderMapper.selectDeliveryFailOrderByTid(ocBOrder.getTid());
                if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                    pickUpOcBOrder = ocBOrderList.get(0);
                }
            }

            if (result) {
                ocBOrder.setIsForce(1L);
                //更新订单主表状态推ES 并作废退单推es
                omsOrderPlatformDeliveryService.updateMasterOrderStatusPushES(ocBOrder, user);
                if (pickUpOcBOrder != null && pickUpOcBOrder.getId() != null) {
                    pickUpOcBOrder.setIsForce(1L);
                    omsOrderPlatformDeliveryService.updateMasterOrderStatusPushES(pickUpOcBOrder, user);
                }
            } else {
                if (pickUpOcBOrder != null && pickUpOcBOrder.getId() != null) {
                    ocBOrder = pickUpOcBOrder;
                }
                deliveryFailUtil.addOcBOrderDeliveryFail(ocBOrder);
                //失败打失败的标记 失败信息
                ocBOrder.setIsForce(0L);
                ocBOrder.setForceSendFailReason(StringUtils.substring(errorMessage, 0, 200));
                omsOrderService.updateOrderInfo(ocBOrder);
                //调用主表更新失败次数接口
                ocBOrderMapper.updateMasterFailTimesById(ocBOrder.getId());
                // 根据抖音平台配置信息重试
                if (businessSystemParamService.getDouyinDeliveryFailRetry(errorMessage)
                        && ocBOrder.getMakeupFailNum() < 5
                        && ocBOrder.getPlatform().equals(PlatFormEnum.DOU_YIN.getCode())) {
                    douyinDeliverRetry(ocBOrder.getId());
                }
                if (isKeywordsIntercept) {
                    ztoLogisticsInterceptService.keywordsIntercept(ocBOrder, errorMessage);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("平台发货错误信息: {}", messageKey), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }

    /**
     * 抖音返回没有能发货的订单重试
     *
     * @param orderId
     */
    private void douyinDeliverRetry(Long orderId) {
        OrderDeliveryOfStandplatmpl bean = ApplicationContextHandle.getBean(OrderDeliveryOfStandplatmpl.class);
        bean.douyinRetry(orderId);
    }

}
