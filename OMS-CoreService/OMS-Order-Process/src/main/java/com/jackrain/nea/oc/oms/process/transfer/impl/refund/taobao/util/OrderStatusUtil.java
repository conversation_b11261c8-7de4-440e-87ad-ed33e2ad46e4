package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.util;

import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;

/**
 * @Author: 黄世新
 * @Date: 2020/2/27 4:35 下午
 * @Version 1.0
 * 退单转单的工具类
 */
public class OrderStatusUtil {

    /**
     * 发货前的状态
     *
     * @param orderStatus
     * @return
     */
    public static boolean checkOrderStatusFront(Integer orderStatus) {
        return OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderStatus)
                || OmsOrderStatus.UNCONFIRMED.toInteger().equals(orderStatus)
                || OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)
                || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderStatus)
                || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(orderStatus)
                || OmsOrderStatus.OCCUPY_IN.toInteger().equals(orderStatus)
                || OmsOrderStatus.PENDING_WMS.toInteger().equals(orderStatus);
    }

    /**
     * 发货后的状态
     *
     * @param orderStatus
     * @return
     */
    public static boolean checkOrderStatusAfter(Integer orderStatus) {
        return OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                || OmsOrderStatus.DEAL_DONE.toInteger().equals(orderStatus);
    }


    public static boolean checkOrderWarehouseDelivery(Integer orderStatus) {
        // @20200706 需求，仓库发货和平台发货一样的处理
        // 减：4. 零售发货单状态是仓库发货，则更新“转换状态”为已转换，创建或更新未发货退款单。
        // 加：6. 若订单状态为仓库发货或者平台发货，则进行下一步判断（判断订单类型）；
        return false;//OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger() == orderStatus;
    }

    /**
     * 判断订单是否取消或者作废
     *
     * @param orderStatus
     * @return
     */
    public static boolean checkOrderIsCancelAndVoid(Integer orderStatus) {
        return OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus)
                || OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus);
    }
}
