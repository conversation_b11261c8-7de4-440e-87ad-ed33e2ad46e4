package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 更新JITX订单中间表转换状态为退款转换中
 * @Date 2019-6-26
 **/
@Step(order = 10, description = "更改中间表状态为退款转换中")
@Slf4j
@Component
public class Step010UpdateReturnOrderStatus extends BaseJitxRefundProcessStep
        implements IOmsOrderProcessStep<IpJitxOrderRelation> {
    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo,
                                                               ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        IpBJitxOrder ipBJitxOrder = orderInfo.getJitxOrder();
        if (log.isDebugEnabled()) {
            log.debug("Step010UpdateReturnOrderStatus orderNo:{}", orderInfo.getOrderNo());
        }

        if (null == ipBJitxOrder) {
            return new ProcessStepResult<>(StepStatus.FAILED, "单据为空，更新转换中失败，退出转换！");
        }
        if (JitxOrderStatus.ORDER_COLLECTED_REFUND.equalsIgnoreCase(ipBJitxOrder.getOrderStatus())) {
            //、已揽收取消状态的标记为已转换
            ipJitxRefundService.updateRefundIsTrans(TransferOrderStatus.TRANSFERRED,
                    "已发货取消、已揽收取消的标记为已转换，退出转换！", ipBJitxOrder);
            return new ProcessStepResult<>(StepStatus.FINISHED, "已发货取消、已揽收取消的标记为已转换，退出转换！");
        }

        //1、转换状态为0，订单状态为97_10:未发货取消 97_22 已发货取消
        if (!ipBJitxOrder.getIstrans().equals(TransferOrderStatus.NOT_TRANSFER.toInteger())
                || !(JitxOrderStatus.ORDER_UNSEND_REFUND.equals(ipBJitxOrder.getOrderStatus())||JitxOrderStatus.ORDER_SEND_REFUND.equalsIgnoreCase(ipBJitxOrder.getOrderStatus()))) {
            return new ProcessStepResult<>(StepStatus.FINISHED, "转换状态不为0或订单状态不为97_10:未发货取消，退出转换！");
        }
        try {
            //2、更新转换状态为3退款转换中
            boolean flag = ipJitxRefundService.updateRefundIsTrans(
                    TransferOrderStatus.REFUNDTRANSFERRING, "", ipBJitxOrder);
            if (!flag) {
                ipJitxRefundService.updateRefundIsTrans(
                        TransferOrderStatus.NOT_TRANSFER, "更新转换中状态失败,退出转换！", ipBJitxOrder);
                return new ProcessStepResult<>(StepStatus.FAILED, "更新转换中状态失败,退出转换！");
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "退款转换中成功，进入下一步！");
        } catch (Exception e) {
            log.error(LogUtil.format("退单转换异常:{}", "退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJitxRefundService.updateRefundIsTransError(ipBJitxOrder, e.getMessage());
            String errorMessage = "退单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
