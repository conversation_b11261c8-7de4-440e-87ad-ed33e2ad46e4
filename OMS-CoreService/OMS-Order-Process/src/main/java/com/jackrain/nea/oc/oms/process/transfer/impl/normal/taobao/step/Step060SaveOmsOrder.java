package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import com.jackrain.nea.log.LogCat;
import com.jackrain.nea.log.LogEvent;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.matcher.MatchStrategyManager;
import com.jackrain.nea.oc.oms.model.enums.AbnormalTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderCycleBuyAmount;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderCycleBuy;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OrderPriceValidate;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.util.AmountUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 保存订单信息
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:49
 */
@Step(order = 60, description = "保存订单信息")
@Slf4j
@Component
public class Step060SaveOmsOrder extends BaseTaobaoOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    private final static String CYCLE_BUY = "CYCLE_BUY";

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    OmsOrderAdvanceParseService omsOrderAdvanceParseService;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

//    @Autowired
//    private R3MqSendHelper sendHelper;
//
//    @Autowired
//    private ToBeConfirmedOrderMqConfig toBeConfirmedOrderMqConfig;

//    /**
//     * 发送MQ消息
//     *
//     * @param saveOrderInfo 保存的OrderInfo订单信息
//     * @param logEventList  logEvent列表
//     */
//    private void sendMQ(OcBOrderRelation saveOrderInfo, List<LogEvent> logEventList) {
//        LogEvent eventMQ = LogCat.newEvent(Step060SaveOmsOrder.class.getSimpleName(), "StartSendMQ");
//        if (saveOrderInfo != null) {
//            long saveOrderId = saveOrderInfo.getOrderInfo().getId();
//            String billNo = saveOrderInfo.getOrderInfo().getBillNo();
//            String msgKey = "TB_TR_" + saveOrderId + "_" + billNo;
//            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
//            orderMqInfo.setChannelType(this.getCurrentChannelType());
//            orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
//            orderMqInfo.setOrderId(saveOrderId);
//            orderMqInfo.setOrderNo(billNo);
//            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
//            mqInfoList.add(orderMqInfo);
//            Object jsonValue = JSONObject.toJSONString(mqInfoList);
//
//            String messageId = null;
//            try {
//                messageId = sendHelper.sendMessage(jsonValue, toBeConfirmedOrderMqConfig.getSendToBeConfirmMqTopic(),
//                        toBeConfirmedOrderMqConfig.getSendToBeConfirmTag(),
//                        msgKey);
//            } catch (SendMqException e) {
//                e.printStackTrace();
//            }
//
//            eventMQ.addData("MQId", messageId);
//            eventMQ.addData("MQKey", msgKey);
//        }
//        eventMQ.complete();
//        logEventList.add(eventMQ);
//    }

    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        log.info(LogUtil.format("转单保存:{}","转单保存",orderInfo.getOrderId()), JSON.toJSONString(orderInfo));
        String orderStatus = orderInfo.getTaobaoOrder().getStatus();
        boolean isHistoryOrder = TaoBaoOrderStatus.TRADE_FINISHED.equalsIgnoreCase(orderStatus)
                || TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equalsIgnoreCase(orderStatus);

        String orderNo = orderInfo.getOrderNo();
        try {
            List<LogEvent> logEventList = new ArrayList<>();
            LogEvent eventConvert = LogCat.newEvent(Step060SaveOmsOrder.class.getSimpleName(), "ConvertOrder");
            OcBOrderRelation saveOrderInfo = orderService.
                    convertTaobaoOrderToOrder(orderInfo, isHistoryOrder);
            // @20200616 策略接匹配
            log.info(LogUtil.format("策略接匹配:{}","策略接匹配",orderInfo.getOrderId()), orderInfo.getOrderId());
            strategyMatch(orderInfo, saveOrderInfo);
            // @20200710 打标
           // TaggerManager.get().doTag(saveOrderInfo.getOrderInfo(), saveOrderInfo.getOrderItemList());

            eventConvert.complete();
            logEventList.add(eventConvert);
            if (saveOrderInfo.getOrderInfo().getCpCRegionCityId() == null || saveOrderInfo.getOrderInfo().getCpCRegionProvinceId() == null ) {
                String errorMessage = "省市区匹配异常!";
                boolean updateStatusRes = ipTaoBaoOrderService.updateTaobaoOrderTransAndAbnormal(orderNo,
                        TransferOrderStatus.TRANSFEREXCEPTION, errorMessage, AbnormalTypeEnum.MATE_ABNORMAL.getKey());
                if (!updateStatusRes) {
                    errorMessage += ";更新状态失败=False";
                }
                return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
            }
            // 双11预售标签
            //saveOrderInfo.getOrderInfo().setDouble11PresaleStatus(1);
            String stepTradeStatus = orderInfo.getTaobaoOrder().getStepTradeStatus();
            log.info(LogUtil.format("阶段付款状态为:{}","阶段付款状态",orderInfo.getOrderId()), stepTradeStatus);
            saveOrderInfo.getOrderInfo().setStatusPayStep(stepTradeStatus);
            //saveOrderInfo.getOrderInfo().setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
//            if (!TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID.equalsIgnoreCase(stepTradeStatus)) {
//                saveOrderInfo.getOrderInfo().setPayTime(null);
//            }
            //已支付金额  如果是预售就 = 阶段已付金额  如果不是 就取实付金额
            BigDecimal receivedAmt = orderInfo.getTaobaoOrder().getStepPaidFee() ==null ? saveOrderInfo.getOrderInfo().getOrderAmt(): orderInfo.getTaobaoOrder().getStepPaidFee();
            saveOrderInfo.getOrderInfo().setReceivedAmt(receivedAmt == null ? BigDecimal.ZERO : receivedAmt);
            log.info(LogUtil.format("预售解析开始:orderId{}","预售解析开始",orderInfo.getOrderId()), orderInfo.getOrderId());
            //转单前进行预售解析
            omsOrderAdvanceParseService.advanceParse(saveOrderInfo,operateUser,orderStatus);
            log.info("预售解析结束--");

            //天猫周期购订单
            if (CYCLE_BUY.equals(orderInfo.getTaobaoOrder().getBusinessType())) {
                List<IpBTaobaoOrderCycleBuy> taobaoOrderCycleBuyList = orderInfo.getTaobaoOrderCycleBuyList();
                Map<Integer, OcBOrderCycleBuyAmount> cycleBuyAmountMap = getBuyAmount(saveOrderInfo, taobaoOrderCycleBuyList);
                log.info(" 天猫周期购订单orderId:{},saveOrderInfo:{},cycleBuyAmountMap:{}", saveOrderInfo.getOrderInfo().getId(), JSON.toJSONString(saveOrderInfo), JSON.toJSONString(cycleBuyAmountMap));
                for (IpBTaobaoOrderCycleBuy cycleBuy : taobaoOrderCycleBuyList) {
                    Long orderId = 0L;
                    try {
                        orderId = cycleBuyOrderReload(saveOrderInfo, cycleBuy, cycleBuyAmountMap);
                        log.info(" 天猫周期购保存订单信息orderId:{},saveOrderInfo:{},isHistoryOrder:{},cycleBuy:{}", orderId, JSON.toJSONString(saveOrderInfo), isHistoryOrder, JSON.toJSONString(cycleBuy));
                        orderService.saveOmsOrderInfo(saveOrderInfo, orderInfo, isHistoryOrder, operateUser);
                        ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.ORDER_CREAT, new Date(), saveOrderInfo.getOrderInfo().getId(), operateUser);
                    } catch (Exception e) {
                        log.error(" 周期购订单生成失败，订单号:{},期数:{}", saveOrderInfo.getOrderInfo().getTid(), cycleBuy.getCurrPhase(), e);
                        DingTalkUtil.dingTmallCycle(orderId, "周期购订单生成失败,tid:" + saveOrderInfo.getOrderInfo().getTid() + ",期数:" + cycleBuy.getCurrPhase());
                    }
                }
            }else {
                //转单前进行订单打标解析
                LogEvent eventStartSave = LogCat.newEvent(Step060SaveOmsOrder.class.getSimpleName(), "SaveOrder");
                boolean saveResult = orderService.saveOmsOrderInfo(saveOrderInfo, orderInfo, isHistoryOrder, operateUser);
                if (saveResult) {
                    if (this.omsSystemConfig.isTransferValidatePriceEnabled()) {
                        OrderPriceValidate.isSendDingTalk(saveOrderInfo);
                    }
                    //埋点订单创建
                    ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.ORDER_CREAT,new Date(),saveOrderInfo.getOrderInfo().getId(),operateUser);
                }
                eventStartSave.complete();
                logEventList.add(eventStartSave);
//            if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
//                this.sendMQ(saveOrderInfo, logEventList);
//            }
            }

            ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();
            stepResult.setMessage("存储数据成功，进入下一阶段");
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step080UpdateOrderTransferStatus.class);
            stepResult.setLogEventList(logEventList);
            return stepResult;
        } catch (Exception ex) {
            log.error(LogUtil.format("Step060SaveOmsOrder:{}", "Step060SaveOmsOrder"), Throwables.getStackTraceAsString(ex));
            String errorMessage = "存储数据失败，退出转单服务;" + ex.getMessage();
            boolean updateStatusRes = ipTaoBaoOrderService.updateTaobaoOrderTransStatus(orderNo,
                    TransferOrderStatus.NOT_TRANSFER, errorMessage);
            if (!updateStatusRes) {
                errorMessage += ";更新状态失败=False";
            }
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

    /**
     * 期数，金额
     * @param saveOrderInfo
     * @param taobaoOrderCycleBuyList
     * @return
     */
    private Map<Integer, OcBOrderCycleBuyAmount> getBuyAmount(OcBOrderRelation saveOrderInfo, List<IpBTaobaoOrderCycleBuy> taobaoOrderCycleBuyList) {
        Map<Integer, OcBOrderCycleBuyAmount> map = Maps.newHashMap();
        OcBOrder orderInfo = saveOrderInfo.getOrderInfo();
        int size = taobaoOrderCycleBuyList.size();

        for (IpBTaobaoOrderCycleBuy ipBTaobaoOrderCycleBuy : taobaoOrderCycleBuyList) {
            int currPhase = ipBTaobaoOrderCycleBuy.getCurrPhase().intValue();
            OcBOrderCycleBuyAmount cycleBuyAmount = new OcBOrderCycleBuyAmount();
            cycleBuyAmount.setProductAmt(AmountUtil.cycleBuyPartAmount(size, currPhase, orderInfo.getProductAmt()));
            cycleBuyAmount.setProductAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getProductAmt()));
            cycleBuyAmount.setProductDiscountAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getProductDiscountAmt()));
            cycleBuyAmount.setOrderDiscountAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getOrderDiscountAmt()));
            cycleBuyAmount.setAdjustAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getAdjustAmt()));
            cycleBuyAmount.setShipAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getShipAmt()));
            cycleBuyAmount.setServiceAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getServiceAmt()));
            cycleBuyAmount.setOrderAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getOrderAmt()));
            cycleBuyAmount.setReceivedAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getReceivedAmt()));
            cycleBuyAmount.setConsignAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getConsignAmt()));
            cycleBuyAmount.setConsignShipAmt(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getConsignShipAmt()));
            cycleBuyAmount.setAmtReceive(AmountUtil.cycleBuyPartAmount(size,currPhase,orderInfo.getAmtReceive()));

            //周期购明细只有一条，一个商品
            OcBOrderItem ocBOrderItem = saveOrderInfo.getOrderItemList().get(0);
            BigDecimal sizeB = new BigDecimal(size);
            BigDecimal goodNum = new BigDecimal(ipBTaobaoOrderCycleBuy.getGoodsNum());
            cycleBuyAmount.setDetailPrice(ocBOrderItem.getPrice());
            cycleBuyAmount.setDetailAdjustAmt(AmountUtil.divideThree(ocBOrderItem.getAdjustAmt(), sizeB, goodNum));
            cycleBuyAmount.setDetailAmtDiscount(AmountUtil.divideThree(ocBOrderItem.getAmtDiscount(), sizeB, goodNum));
            cycleBuyAmount.setDetailRealAmt(AmountUtil.divide(ocBOrderItem.getRealAmt(), sizeB));
            cycleBuyAmount.setDetailOrderSplitAmt(AmountUtil.divide(ocBOrderItem.getOrderSplitAmt(), sizeB));
            cycleBuyAmount.setDetailPriceActual(ocBOrderItem.getPriceActual());

            map.put(ipBTaobaoOrderCycleBuy.getCurrPhase().intValue(), cycleBuyAmount);
        }
        return map;
    }

    private Long cycleBuyOrderReload(OcBOrderRelation saveOrderInfo, IpBTaobaoOrderCycleBuy cycleBuy, Map<Integer, OcBOrderCycleBuyAmount> cycleBuyAmountMap) {
        int currPhase = cycleBuy.getCurrPhase().intValue();
        OcBOrderCycleBuyAmount cycleBuyAmount = cycleBuyAmountMap.get(currPhase);
        if (cycleBuyAmount == null) {
            cycleBuyAmount = new OcBOrderCycleBuyAmount();
        }
        long ocOrderId = sequenceUtil.buildOrderSequenceId();
        saveOrderInfo.getOrderInfo().setId(ocOrderId);
        saveOrderInfo.getOrderInfo().setBillNo(sequenceUtil.buildBillNo());
        saveOrderInfo.getOrderInfo().setSuffixInfo(CYCLE_BUY + "_" + ocOrderId);
        //打标,周期购标识
        saveOrderInfo.getOrderInfo().setIsCycle(OcBOrderConst.IS_STATUS_IY);
        saveOrderInfo.getOrderInfo().setTid(cycleBuy.getOrderId());
        saveOrderInfo.getOrderInfo().setCurrentCycleNumber(currPhase);
        saveOrderInfo.getOrderInfo().setEstimateConTime(cycleBuy.getShipTimeBegin());
        if (saveOrderInfo.getOrderInfo().getEstimateConTime() != null) {
            saveOrderInfo.getOrderInfo().setLatestDeliveryTime(saveOrderInfo.getOrderInfo().getEstimateConTime());
        }
        saveOrderInfo.getOrderInfo().setOaid(cycleBuy.getOaid());

        //收货信息
        saveOrderInfo.getOrderInfo().setReceiverName(cycleBuy.getReceiverName());
        saveOrderInfo.getOrderInfo().setReceiverMobile(cycleBuy.getReceiverMobile());
        saveOrderInfo.getOrderInfo().setReceiverPhone(cycleBuy.getReceiverPhone());
        saveOrderInfo.getOrderInfo().setReceiverAddress(cycleBuy.getReceiverAddress());
        saveOrderInfo.getOrderInfo().setPlatformProvince(cycleBuy.getReceiverState());
        saveOrderInfo.getOrderInfo().setPlatformCity(cycleBuy.getReceiverCity());
        saveOrderInfo.getOrderInfo().setPlatformArea(cycleBuy.getReceiverDistrict());

        //金额分摊
        saveOrderInfo.getOrderInfo().setOrderAmt(AmountUtil.getNoDefault(cycleBuyAmount.getOrderAmt()));
        saveOrderInfo.getOrderInfo().setAmtReceive(AmountUtil.getNoDefault(cycleBuyAmount.getAmtReceive()));
        saveOrderInfo.getOrderInfo().setShipAmt(AmountUtil.getNoDefault(cycleBuyAmount.getShipAmt()));
        saveOrderInfo.getOrderInfo().setProductAmt(AmountUtil.getNoDefault(cycleBuyAmount.getProductAmt()));
        saveOrderInfo.getOrderInfo().setProductDiscountAmt(AmountUtil.getNoDefault(cycleBuyAmount.getProductDiscountAmt()));
        saveOrderInfo.getOrderInfo().setOrderDiscountAmt(AmountUtil.getNoDefault(cycleBuyAmount.getOrderDiscountAmt()));
        saveOrderInfo.getOrderInfo().setAdjustAmt(AmountUtil.getNoDefault(cycleBuyAmount.getAdjustAmt()));
        saveOrderInfo.getOrderInfo().setReceivedAmt(AmountUtil.getNoDefault(cycleBuyAmount.getReceivedAmt()));

        for (OcBOrderItem item : saveOrderInfo.getOrderItemList()) {
            item.setId(sequenceUtil.buildOrderItemSequenceId());
            item.setTid(cycleBuy.getOrderId());
            item.setOcBOrderId(saveOrderInfo.getOrderInfo().getId());
            item.setQty(new BigDecimal(cycleBuy.getGoodsNum()));
            item.setPrice(AmountUtil.getNoDefault(cycleBuyAmount.getDetailPrice()));
            item.setAmtDiscount(BigDecimalUtil.multiply(AmountUtil.getNoDefault(cycleBuyAmount.getDetailAmtDiscount()), item.getQty()));
            item.setAdjustAmt(AmountUtil.getNoDefault(cycleBuyAmount.getAdjustAmt()));
            item.setRealAmt(AmountUtil.getNoDefault(cycleBuyAmount.getDetailRealAmt()));
            item.setPriceActual(AmountUtil.getNoDefault(cycleBuyAmount.getDetailPriceActual()));
            item.setOrderSplitAmt(AmountUtil.getNoDefault(cycleBuyAmount.getDetailOrderSplitAmt()));
        }

        saveOrderInfo.getOrderTaobao().setId(sequenceUtil.buildOrderTaobaoSequenceId());

        //优惠信息是否需要处理
        if (CollectionUtils.isNotEmpty(saveOrderInfo.getOrderPromotionList())) {
            saveOrderInfo.getOrderPromotionList().forEach(p -> p.setId(sequenceUtil.buildOrderPromotionSequenceId()));
        }

        //支付信息处理
        if (CollectionUtils.isNotEmpty(saveOrderInfo.getOrderPaymentList())) {
            saveOrderInfo.getOrderPaymentList().forEach(p -> p.setId(sequenceUtil.buildOrderPaymentSequenceId()));
        }

        return ocOrderId;
    }

    /**
     * 策略解析匹配
     *
     * @param orderInfo
     * @param orderRelation
     */
    private void strategyMatch(IpTaobaoOrderRelation orderInfo, OcBOrderRelation orderRelation) {
        if (Objects.nonNull(orderInfo) && Objects.nonNull(orderRelation)) {
            MatchStrategyManager.get().match(orderInfo, this.getCurrentChannelType(), orderRelation.getOrderInfo(), orderRelation.getOrderItemList());
        }
    }
}
