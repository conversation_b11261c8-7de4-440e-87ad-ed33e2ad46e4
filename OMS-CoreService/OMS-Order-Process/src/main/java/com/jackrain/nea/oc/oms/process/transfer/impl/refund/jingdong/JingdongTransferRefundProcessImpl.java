package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * @Descroption 京东退货转换
 * <AUTHOR>
 * @Date 2019/4/24 18:27
 */
@Component
public class JingdongTransferRefundProcessImpl extends AbstractOrderProcess<IpJingdongRefundRelation> {
    @Override
    protected String getChildPackageName() {
        return null;
    }

    @Override
    protected long getProcessOrderId(IpJingdongRefundRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpJingdongRefundRelation orderInfo) {
        return null;
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JINGDONG_REFUND;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.JINGDONG;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return null;
    }

    /**
     * 京东退单接口，平台单号
     *
     * @param orderInfo 订单单据
     * @return
     */
    @Override
    protected String getSourceTid(IpJingdongRefundRelation orderInfo) {
        return orderInfo.getOrderNo();
    }
}
