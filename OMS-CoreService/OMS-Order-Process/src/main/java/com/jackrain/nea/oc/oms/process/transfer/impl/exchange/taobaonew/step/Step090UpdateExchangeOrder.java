package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2020/12/2 9:01 下午
 * @Version 1.0
 */
@Step(order = 90, description = "更新退换货单以及换货订单")
@Slf4j
@Component
public class Step090UpdateExchangeOrder extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {
    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        try {
            boolean flag = omsTaobaoExchangeService.updateExchangeOrderAndReturnOrder(orderInfo, operateUser);
            String message;
            if (flag) {
                message = "更新退换货单以及换货订单成功";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.TRANSFERRED.toInteger(), message, ipBTaobaoExchange);
            } else {
                message = "更新退换货单以及换货订单成功,之前存在换货订单,修改为未转换";
                ipTaobaoExchangeService.updateExchangeRemarkAndIsTrans(TransferOrderStatus.NOT_TRANSFER.toInteger(), message, ipBTaobaoExchange);
            }
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(ipBTaobaoExchange, "更新退换货单以及换货订单失败" + e.getMessage());
            log.error(LogUtil.format("更新退换货单以及换货订单失败:{}", "更新退换货单以及换货订单失败"), Throwables.getStackTraceAsString(e));
            String errorMessage = "更新退换货单以及换货订单失败!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
