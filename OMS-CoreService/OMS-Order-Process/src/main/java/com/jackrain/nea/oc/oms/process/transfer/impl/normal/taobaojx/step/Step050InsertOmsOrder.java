package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.step;

import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.jackrain.nea.log.LogCat;
import com.jackrain.nea.log.LogEvent;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.ToBeConfirmedOrderMqConfig;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.BllSystemParameterKeyResources;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 插入订单管理表
 **/
@Step(order = 50, description = "插入订单管理表")
@Slf4j
@Component
public class Step050InsertOmsOrder extends BaseTaobaoJxOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoJxOrderRelation> {
//    @Autowired
//    private R3MqSendHelper sendHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Autowired
    private ToBeConfirmedOrderMqConfig orderMqConfig;

    @Override
    public ProcessStepResult<IpTaobaoJxOrderRelation> startProcess(IpTaobaoJxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoJxOrder taobaoJxOrder = orderInfo.getTaobaoJxOrder();
        try {

            List<LogEvent> logEventList = new ArrayList<>();
            LogEvent eventConvert = LogCat.newEvent(Step060UpdateLog.class.getSimpleName(), "ConvertOrder");
            OcBOrderRelation saveOrderInfo = ipTaobaoJxOrderService.convertOrder(orderInfo);
            eventConvert.complete();
            logEventList.add(eventConvert);

            LogEvent eventStartSave = LogCat.newEvent(Step060UpdateLog.class.getSimpleName(), "SaveOrder");
            boolean saveResult = ipTaobaoJxOrderService.saveOmsOrderInfo(saveOrderInfo, false, operateUser);
            if (!saveResult) {
                saveOrderInfo = null;
            }
            eventStartSave.complete();
            logEventList.add(eventStartSave);
            if (BllCommonUtil.isOpen(null, BllSystemParameterKeyResources.OMS_TRANSFER_AUTO_MQ)) {
                //this.sendMQ(saveOrderInfo, logEventList);
            }

            ProcessStepResult<IpTaobaoJxOrderRelation> stepResult = new ProcessStepResult<>();
            stepResult.setMessage("存储数据成功，进入下一阶段");
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setNextStepClass(Step060UpdateLog.class);
            stepResult.setLogEventList(logEventList);
            return stepResult;

        } catch (Exception e) {
            log.error(this.getClass().getName() + " 淘宝经销订单转换异常！", e);
            //修改中间表状态及系统备注
            ipTaobaoJxOrderService.updateIsTransError(taobaoJxOrder, e.getMessage());
            String errorMessage = "淘宝经销订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }


    /**
     * 发送MQ消息
     *
     * @param saveOrderInfo 保存的OrderInfo订单信息
     * @param logEventList  logEvent列表
     */
    private void sendMQ(OcBOrderRelation saveOrderInfo, List<LogEvent> logEventList) {
        LogEvent eventMQ = LogCat.newEvent(Step060UpdateLog.class.getSimpleName(), "StartSendMQ");
        if (saveOrderInfo != null) {
            long saveOrderId = saveOrderInfo.getOrderInfo().getId();
            String billNo = saveOrderInfo.getOrderInfo().getBillNo();
            String msgKey = "TB_JX_" + saveOrderId + "_" + billNo;
            OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
            orderMqInfo.setChannelType(this.getCurrentChannelType());
            orderMqInfo.setOperateType(OperateType.TOBE_CONFIRMED);
            orderMqInfo.setOrderId(saveOrderId);
            orderMqInfo.setOrderNo(billNo);
            List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
            mqInfoList.add(orderMqInfo);
            String jsonValue = JSONObject.toJSONString(mqInfoList);

            String messageId = null;
            try {
//                messageId = sendHelper.sendMessage(jsonValue, orderMqConfig.getSendToBeConfirmMqTopic(),
//                        orderMqConfig.getSendToBeConfirmTag(),
//                        msgKey);
                MqSendResult result = defaultProducerSend.sendTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_TOBECONFIRMED, Mq5Constants.TAG_R3_OC_OMS_CALL_TOBECONFIRMED, jsonValue, msgKey);
                messageId = result.getMessageId();
            } catch (Exception e) {
                e.printStackTrace();
            }

            eventMQ.addData("MQId", messageId);
            eventMQ.addData("MQKey", msgKey);
        }
        eventMQ.complete();
        logEventList.add(eventMQ);
    }
}
