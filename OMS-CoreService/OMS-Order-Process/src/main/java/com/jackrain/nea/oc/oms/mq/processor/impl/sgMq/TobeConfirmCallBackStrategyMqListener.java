package com.jackrain.nea.oc.oms.mq.processor.impl.sgMq;

import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @program: ryytn-oc-oms-v3.0
 * @description:
 * @author: haiyang
 * @create: 2024-02-01 10:30
 **/

@Slf4j
@RocketMqMessageListener(name = "TobeConfirmCallBackStrategyMqListener", type = MqTypeEnum.DEFAULT)
public class TobeConfirmCallBackStrategyMqListener implements BaseMessageListener {

    @Autowired
    private TobeConfirmCallBackDeliveryMqProcessorImpl tobeConfirmCallBackDeliveryMqProcessor;

    @Autowired
    private TobeConfirmCallBackMqProcessorImpl tobeConfirmCallBackMqProcessor;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.info("TobeConfirmCallBackStrategyMqListener.topic-tag-body: {}-{}-{}", messageTopic, messageTag, messageBody);
        switch (messageTag) {
            case "OperateTobeConfirmCallBack_Vip_Delivery":
                tobeConfirmCallBackDeliveryMqProcessor.consume(messageBody, messageTopic, messageKey, messageTag, object);
                break;
            case "OperateTobeConfirmCallBack":
                tobeConfirmCallBackMqProcessor.consume(messageBody, messageTopic, messageKey, messageTag, object);
                break;
            default:
                log.error("TobeConfirmCallBackStrategyMqListener未监听mq，tag: {}", messageTag);
        }
    }
}
