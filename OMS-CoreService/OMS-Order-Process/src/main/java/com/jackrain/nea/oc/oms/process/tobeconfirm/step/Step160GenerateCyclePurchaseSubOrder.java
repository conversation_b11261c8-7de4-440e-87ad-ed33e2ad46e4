package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.CycleBuyInfoService;
import com.jackrain.nea.oc.oms.services.GenerateCyclePurchaseSubOrderService;
import com.jackrain.nea.oc.oms.services.OcBorderDetailService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/4
 * description : 生成周期购子订单
 */
@Step(order = 160, description = "生成周期购子订单")
@Slf4j
public class Step160GenerateCyclePurchaseSubOrder extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private GenerateCyclePurchaseSubOrderService generateCyclePurchaseSubOrderService;
    @Autowired
    private CycleBuyInfoService cycleBuyInfoService;
    @Autowired
    private OcBorderDetailService ocBorderDetailService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.info("Start.Step160GenerateCyclePurchaseSubOrder.startProcess");
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        try {
            generateCyclePurchaseSubOrderService.generateCyclePurchaseSubOrder(ocBOrder.getSourceCode(), operateUser);

            //记录周期购额外信息
            cycleBuyInfoService.generateCycleBuyInfo(Lists.newArrayList(ocBOrder.getSourceCode()));

            //周期购订单 最晚发货时间
            ocBorderDetailService.updatgeDeliveryTime(ocBOrder.getTid());

            return new ProcessStepResult<>(StepStatus.FINISHED);
        } catch (Exception e) {
            log.error(LogUtil.format("生成周期购子订单失败:{}", ocBOrder.getId(), "生成周期购子订单失败"), Throwables.getStackTraceAsString(e));
            OcBOrder errorOrderInfo = new OcBOrder();
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage("生成周期购子订单失败：" + e.getMessage()));
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_160);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.SUCCESS, "生成周期购子订单失败");
        }
    }

}
