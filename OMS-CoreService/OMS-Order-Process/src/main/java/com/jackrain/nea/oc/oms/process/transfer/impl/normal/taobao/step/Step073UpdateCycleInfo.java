package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobao.step;

import com.alibaba.fastjson.JSON;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBOccupyTaskMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderDetentionEnum;
import com.jackrain.nea.oc.oms.model.order.address.ReceiverAddressDto;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 天猫周期购信息变动更新
 *
 * <AUTHOR>
 */
@Step(order = 73, description = "天猫周期购信息变动更新")
@Component
@Slf4j
public class Step073UpdateCycleInfo extends BaseTaobaoOrderProcessStep implements IOmsOrderProcessStep<IpTaobaoOrderRelation> {

    /**
     * 修改预计发货时间的状态限制条件
     */
    private static final List<Integer> modifyShipTimes = Lists.newArrayList(
            OmsOrderStatus.UNCONFIRMED.toInteger(),
            OmsOrderStatus.BE_OUT_OF_STOCK.toInteger(),
            OmsOrderStatus.CHECKED.toInteger(),
            OmsOrderStatus.IN_DISTRIBUTION.toInteger());

    @Autowired
    private OmsOrderService orderService;
    @Autowired
    private OcBorderUpdateService service;
    @Autowired
    private OcBOrderUpdateAddressService ocBOrderUpdateAddressService;
    @Autowired
    private OcBOccupyTaskMapper ocBOccupyTaskMapper;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    @SuppressWarnings("unchecked")
    @Override
    public ProcessStepResult<IpTaobaoOrderRelation> startProcess(IpTaobaoOrderRelation orderInfo,
                                                                 ProcessStepResult preStepResult,
                                                                 boolean isAutoMakeup, User operateUser) {
        log.info(" Step073UpdateCycleInfo orderInfo:{}", JSON.toJSONString(orderInfo));
        ProcessStepResult<IpTaobaoOrderRelation> stepResult = new ProcessStepResult<>();

        List<IpBTaobaoOrderCycleBuy> taobaoOrderCycleBuyList = orderInfo.getTaobaoOrderCycleBuyList();

        Map<String, IpBTaobaoOrderCycleBuy> cycleOrderCurrPhaseMap = taobaoOrderCycleBuyList.stream().collect(
                Collectors.toMap(x -> x.getOrderId() + x.getCurrPhase(), x -> x, (a, b) -> a));

        List<OcBOrder> afterTransferOrderList = (List<OcBOrder>) preStepResult.getNextStepOperateObj();
        for (OcBOrder ocBOrder : afterTransferOrderList) {
            try {
                String oaid = StringUtils.defaultString(ocBOrder.getOaid());
                String tidNumber = ocBOrder.getTid() + ocBOrder.getCurrentCycleNumber();
                IpBTaobaoOrderCycleBuy ipBTaobaoOrderCycleBuy = cycleOrderCurrPhaseMap.get(tidNumber);
                if (ipBTaobaoOrderCycleBuy == null) {
                    log.error(" Step073UpdateCycleInfo ipBTaobaoOrderCycleBuy is cycleOrderCurrPhaseMap:{},tidNumber:{}", JSON.toJSONString(cycleOrderCurrPhaseMap), tidNumber);
                    continue;
                }
                if (!oaid.equals(ipBTaobaoOrderCycleBuy.getOaid())) {
                    //oaid变动，认为收货人信息有更改，则修改收货信息
                    this.modifyAddress(operateUser, ocBOrder, ipBTaobaoOrderCycleBuy);
                }

                //补发/复制单没有预计发货时间，无需接收用户修改信息而变动
                Date estimateConTime = ocBOrder.getEstimateConTime();
                if (!Objects.isNull(estimateConTime) && ipBTaobaoOrderCycleBuy.getShipTimeBegin().compareTo(estimateConTime) != 0) {
                    //预计发货时间变动
                    if (modifyShipTimes.contains(ocBOrder.getOrderStatus()) || OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsDetention())) {
                        //更新预计发货时间
                        OcBOrder ocBOrderU = new OcBOrder();
                        ocBOrderU.setId(ocBOrder.getId());
                        ocBOrderU.setEstimateConTime(ipBTaobaoOrderCycleBuy.getShipTimeBegin());
                        ocBOrderU.setDetentionReleaseDate(ipBTaobaoOrderCycleBuy.getShipTimeBegin());
                        if (!OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsDetention())) {
                            ProcessStepResult<IpTaobaoOrderRelation> processStepResult = this.cardOrder(operateUser, stepResult, ocBOrder, ocBOrderU);
                            if (processStepResult != null) {
                                return processStepResult;
                            }
                        }
                        this.orderService.updateOrderInfo(ocBOrderU);
                        // 删除占单中间表
                        ocBOccupyTaskMapper.deleteOcBOccupyTaskByOrderId(ocBOrder.getId());
                        // 加入中间表
                        omsOccupyTaskService.addOcBOccupyTask(ocBOrder, ipBTaobaoOrderCycleBuy.getShipTimeBegin());
                    } else if (OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(ocBOrder.getOrderStatus())){
                        //待分配状态修改发货时间处理
                        OcBOrder ocBOrderU = new OcBOrder();
                        ocBOrderU.setId(ocBOrder.getId());
                        ocBOrderU.setEstimateConTime(ipBTaobaoOrderCycleBuy.getShipTimeBegin());
                        this.orderService.updateOrderInfo(ocBOrderU);
                    } else {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                                OrderLogTypeEnum.ADDRESS_UPDATE.getKey(), "天猫周期购,当前订单状态不允许修改预计发货时间", "", "", operateUser);
                    }
                }
            } catch (Exception e) {
                log.error(" Step073UpdateCycleInfo update error orderId:{}", orderInfo.getOrderId(), e);
                DingTalkUtil.dingTmallCycle(ocBOrder.getId(), "周期购信息修改失败:" + e.getMessage());
            }
        }

        stepResult.setMessage("天猫周期购信息变动处理完成，进入下一阶段");
        stepResult.setNextStepClass(Step080UpdateOrderTransferStatus.class);
        stepResult.setStatus(StepStatus.SUCCESS);
        return stepResult;
    }

    private ProcessStepResult<IpTaobaoOrderRelation> cardOrder(User operateUser, ProcessStepResult<IpTaobaoOrderRelation> stepResult, OcBOrder ocBOrder, OcBOrder ocBOrderU) {
        //非卡单状态，卡单
        ocBOrderU.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
        ocBOrderU.setDetentionReason(OrderDetentionEnum.TMALL_CYCLE_BUY_MODIFY_DATE_CARD.getVal());
        ocBOrderU.setDetentionReasonId(OrderDetentionEnum.TMALL_CYCLE_BUY_MODIFY_DATE_CARD.getKey());
        ocBOrderU.setOrderStatus(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger());
        //待审核需要释放库存
        if (OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
            if (cancelStock(operateUser, stepResult, ocBOrder, ocBOrderU)) {
                return stepResult;
            }
        }
        if (OmsOrderStatus.CHECKED.toInteger().equals(ocBOrder.getOrderStatus()) || OmsOrderStatus.IN_DISTRIBUTION.toInteger().equals(ocBOrder.getOrderStatus())) {
            //反审核
            if (!service.toExamineOrder(ocBOrder, operateUser, LogTypeEnum.TMALL_CYCLE_BUY_REVERSE_AUDIT.getType())) {
                //反审核失败
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.ORDER_RE_EXAMINE.getKey(), "天猫周期购修改预计发货时间失败（反审核失败）", "", "", operateUser);
                stepResult.setMessage("天猫周期购,当前订单不允许修改预计发货时间,反审核失败");
                stepResult.setNextStepClass(Step080UpdateOrderTransferStatus.class);
                stepResult.setStatus(StepStatus.SUCCESS);
                return stepResult;
            } else {
                //反审核成功，释放库存，卡单
                if (cancelStock(operateUser, stepResult, ocBOrder, ocBOrderU)) {
                    return stepResult;
                }
            }
        }
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "天猫周期购修改配送时间后重新卡单", "", "", operateUser);
        ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.DETENTION_DATE, new Date(), ocBOrder.getId(), operateUser);
        return null;
    }

    private boolean cancelStock(User operateUser, ProcessStepResult<IpTaobaoOrderRelation> stepResult, OcBOrder ocBOrder, OcBOrder ocBOrderU) {
        List<OcBOrderItem> ocBOrderItemList = orderItemMapper.selectOrderItemListOccupy(ocBOrderU.getId());
        SgOmsShareOutRequest request = ocBOrderHoldService.buildSgOmsShareOutRequest(ocBOrder, ocBOrderItemList, operateUser);
        log.info(" Step073UpdateCycleInfo 调用sg取消库存封装数据为：{}", JSON.toJSONString(request));
        ValueHolderV14 sgValueHolder = sgRpcService.voidSgOmsShareOut(request, ocBOrder, ocBOrderItemList);
        log.info(" Step073UpdateCycleInfo 调用sg取消库存返回接口数据为：{}", JSON.toJSONString(sgValueHolder));
        if (!sgValueHolder.isOK()) {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.RELEASE_STOCK_FAIL.getKey(), "天猫周期购修改预计发货时间失败（取消库存失败）", "", sgValueHolder.getMessage(), operateUser);
            stepResult.setMessage("天猫周期购,当前订单不允许修改预计发货时间,释放库存失败");
            stepResult.setNextStepClass(Step080UpdateOrderTransferStatus.class);
            stepResult.setStatus(StepStatus.SUCCESS);
            return true;
        }
        return false;
    }

    private void modifyAddress(User operateUser, OcBOrder ocBOrder, IpBTaobaoOrderCycleBuy ipBTaobaoOrderCycleBuy) {
        //更新收件人信息
        ReceiverAddressDto addressDto = new ReceiverAddressDto();
        addressDto.setId(ocBOrder.getId());
        addressDto.setReceiverName(ipBTaobaoOrderCycleBuy.getReceiverName());
        addressDto.setReceiverMobile(ipBTaobaoOrderCycleBuy.getReceiverMobile());
        addressDto.setReceiverPhone(ipBTaobaoOrderCycleBuy.getReceiverPhone());
        addressDto.setReceiverAddress(ipBTaobaoOrderCycleBuy.getReceiverAddress());
        addressDto.setOaid(ipBTaobaoOrderCycleBuy.getOaid());
        addressDto.setCpCRegionTownEname(ipBTaobaoOrderCycleBuy.getReceiverTown());
        ocBOrderUpdateAddressService.setOrderRegoin(ipBTaobaoOrderCycleBuy.getReceiverState(), ipBTaobaoOrderCycleBuy.getReceiverCity(), ipBTaobaoOrderCycleBuy.getReceiverDistrict(), addressDto);
        ValueHolderV14 vhRaw = service.updateReceiveAddressNew(addressDto, operateUser, true, true);
        if (!vhRaw.isOK()) {
            log.error(" Step073UpdateCycleInfo modifyAddress update address error addressDto:{},msg:{}", JSON.toJSONString(addressDto), vhRaw.getMessage());
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                    OrderLogTypeEnum.ADDRESS_UPDATE.getKey(), "天猫周期购修改地址失败", "", vhRaw.getMessage(), operateUser);
            if (!OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus()) && !OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus())) {
                log.warn("天猫周期购修改地址失败 id:{},message:{}", ocBOrder.getId(), vhRaw.getMessage());
            }
        }
    }
}
