package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 转换全渠道订单表状态已取消或系统作废
 * @Date 2019-6-26
 **/
@Step(order = 30, description = "全渠道订单表状态已取消或系统作废")
@Slf4j
@Component
public class Step030CancleAndVoidStatus extends BaseJitxRefundProcessStep implements IOmsOrderProcessStep<IpJitxOrderRelation> {
    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJitxOrder ipBJitxOrder = orderInfo.getJitxOrder();
        if (log.isDebugEnabled()) {
            log.debug("Step030CancleAndVoidStatus orderNo:{}", orderInfo.getOrderNo());
        }
        try {
            List<OcBOrder> ocBOrderList = ipJitxRefundService.selectOmsOrder(orderInfo.getOrderNo());
            //1、已取消或系统作废  SYS_VOID  CANCELLED
            int cancelledCount = 0;
            for (OcBOrder ocBOrder : ocBOrderList) {
                Integer ocBOrderStatus = ocBOrder.getOrderStatus();
                if (OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrderStatus)
                        || OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrderStatus)) {
                    cancelledCount++;
                }
            }
            if (cancelledCount == ocBOrderList.size()) {
                ipJitxRefundService.updateRefundIsTrans(TransferOrderStatus.TRANSFERRED, "所有原单已取消或系统作废，订单退款转换失败！", ipBJitxOrder);
                return new ProcessStepResult<>(StepStatus.FINISHED, "所有原单已取消或系统作废，订单退款转换失败！");
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "全渠道订单表状态已取消或系统作废判断成功，进入下一步！");
        } catch (Exception e) {
            log.error(LogUtil.format("退单转换异常:{}", "退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJitxRefundService.updateRefundIsTransError(ipBJitxOrder, e.getMessage());
            String errorMessage = "退单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
