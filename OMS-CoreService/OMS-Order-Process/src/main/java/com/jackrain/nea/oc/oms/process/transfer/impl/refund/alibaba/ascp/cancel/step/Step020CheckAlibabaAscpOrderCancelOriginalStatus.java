package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.cancel.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderCancelRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderCancel;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.util.AlibabaAscpOrderCommonUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @description: 检查猫超直发取消订单平台单号在全渠道是否存在
 * @author: xtt
 * @date: 2020-09-04 11:37
 **/
@Step(order = 20, description = "检查猫超直发取消订单平台单号在全渠道是否存在")
@Slf4j
@Component
public class Step020CheckAlibabaAscpOrderCancelOriginalStatus extends BaseAlibabaAscpOrderCancelProcessStep
        implements IOmsOrderProcessStep<IpBAlibabaAscpOrderCancelRelation> {

    @Override
    public ProcessStepResult<IpBAlibabaAscpOrderCancelRelation> startProcess(IpBAlibabaAscpOrderCancelRelation orderInfo,
                                                                             ProcessStepResult preStepResult,
                                                                             boolean isAutoMakeup, User operateUser) {
        IpBAlibabaAscpOrderCancel ipBAlibabaAscpOrderCancel = orderInfo.getIpBAlibabaAscpOrderCancel();
        OcBOrder ocBOrder = orderInfo.getOcBOrder();

        try {
            String bizOrderCode = ipBAlibabaAscpOrderCancel.getBizOrderCode();
            Long id = ipBAlibabaAscpOrderCancel.getId();
            AlibabaAscpOrderCommonUtil.printDebugLog(this.getClass().getName() + " 检查订单管理存在原单，猫超直发取消id：{},bizOrderCode:{}", id, bizOrderCode);
            if (null == ocBOrder) {
                boolean isExpire = AlibabaAscpOrderCommonUtil.isOrderPassAppointTime(ocBOrder.getCreationdate());
                if (isExpire) {
                    orderCancelService.updateOrderCancel(TransferOrderStatus.TRANSFERRED.toInteger(), "猫超直发-零售发货单不存在，且取消中间表超过配置时间-标记已转换", ipBAlibabaAscpOrderCancel);
                } else {
                    orderCancelService.updateOrderCancel(TransferOrderStatus.NOT_TRANSFER.toInteger(), "猫超直发-零售发货单不存在，下次转换", ipBAlibabaAscpOrderCancel);
                }
                return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderId() + "退出转换");
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("猫超直发取消订单转换异常:{}", "猫超直发取消订单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            orderCancelService.updateSaRefundIsTransError(ipBAlibabaAscpOrderCancel, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
