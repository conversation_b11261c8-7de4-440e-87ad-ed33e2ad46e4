package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jingdong.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.IpJingdongOrderService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.oc.oms.util.LockOrderType;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-25
 * create at : 2019-04-25 11:26 AM
 * 基础京东Order处理阶段
 */
public abstract class BaseJingdongOrderProcessStep {

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;

    @Autowired
    protected IpJingdongOrderService ipJingdongOrderService;

    protected ChannelType getCurrentChannelType() {
        return ChannelType.JINGDONG;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }

    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JINGDONG_ORDER;
    }
}
