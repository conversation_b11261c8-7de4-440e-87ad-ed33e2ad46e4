package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.normal;

import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.TaobaoJxTransferOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpTaobaoJxOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 淘宝经销转单消息处理器
 * <p>
 * 2020-11-11易邵峰检查
 *
 * <AUTHOR>
 * @Description 淘宝经销转单消息处理器
 * @Date 2019-7-22
 **/
@Slf4j
public class TaobaoJxTransferMqOrderDetailProcessor implements IMqOrderDetailProcessor {
    @Autowired
    private IpTaobaoJxOrderService ipTaobaoJxOrderService;

    @Autowired
    private TaobaoJxTransferOrderProcessImpl taobaoJxTransferOrderProcess;


    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("TaobaoJxTransferMqOrderDetailProcessor.Start", orderNo));
        }
        IpTaobaoJxOrderRelation ipTaobaoJxOrderRelation = ipTaobaoJxOrderService.selectTaobaoJxOrder(orderNo);
        if (ipTaobaoJxOrderRelation == null || ipTaobaoJxOrderRelation.getTaobaoJxOrder() == null) {
            log.error(LogUtil.format("TaobaoJxTransferMqOrderDetailProcessor.Error.Not.Exist", orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = taobaoJxTransferOrderProcess.start(ipTaobaoJxOrderRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("TaobaoJxTransferMqOrderDetailProcessor.Finish", orderNo));
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.TAOBAO
                && orderMqInfo.getOrderType() == OrderType.JXORDER;
    }
}
