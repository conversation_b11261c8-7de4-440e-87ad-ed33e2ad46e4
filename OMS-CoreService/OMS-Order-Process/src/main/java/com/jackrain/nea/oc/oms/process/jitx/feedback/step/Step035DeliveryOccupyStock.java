package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.burgeon.r3.sg.sourcing.model.request.SgFindSourceStrategyC2SRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.services.TimeOrderVoidSgSendService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 寻仓单占单
 *
 * @author: zhuxing
 * @since: 2021-08-29
 */
@Step(order = 35, description = "寻仓单占单")
@Slf4j
@Component
public class Step035DeliveryOccupyStock extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {

    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    private IpJitxDeliveryService ipJitxDeliveryService;

    @Autowired
    private TimeOrderVoidSgSendService timeOrderVoidSgSendService;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation deliveryInfo,
                                                                  ProcessStepResult preStepResult,
                                                                  boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxDeliveryRelation> stepResult = new ProcessStepResult<>();
        String orderNo = deliveryInfo.getOrderNo();
        Integer isVirtualOccupy = deliveryInfo.getIsVirtualOccupy();
        try{
            SgFindSourceStrategyC2SRequest sgFindSourceStrategyC2SRequest = ipJitxDeliveryService.buildDeliveryOccupyStockRequest(deliveryInfo,operateUser);
            sgFindSourceStrategyC2SRequest.setSkuItems(ipJitxDeliveryService.buildDeliveryOccupyStockRequestItem(deliveryInfo));
            if(VipConstant.JITX_DELIVERY_IS_VIRTUAL_OCCUPY_02.equals(isVirtualOccupy)){
                List<IpVipTimeOrderRelation> ipVipTimeOrderRelationList = deliveryInfo.getIpVipTimeOrderRelationList();
                IpVipTimeOrderRelation timeOrderRelation = ipVipTimeOrderRelationList.get(0);
                List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipOccupyItemList = timeOrderRelation.getIpBTimeOrderVipOccupyItemList();
                String warehouseEcode = ipBTimeOrderVipOccupyItemList.get(0).getCpCPhyWarehouseEcode();
                //指定仓库占单
                sgFindSourceStrategyC2SRequest.setWarehouseEcode(warehouseEcode);
                //释放时效单占用
                timeOrderVoidSgSendService.assignStoreCancelShareOccupy(timeOrderRelation,operateUser);
            }
            ValueHolderV14 v14 = ipJitxDeliveryService.deliveryOccupyStockSendMq(sgFindSourceStrategyC2SRequest);
            if(v14.isOK()){
                stepResult.setStatus(StepStatus.FINISHED);
                stepResult.setMessage("寻源占单中");
                return stepResult;
            }
        }catch (Exception e){
            log.error(LogUtil.format("寻仓单orderSn:{}占单消息发送失败:{}","Step035DeliveryOccupyStock",orderNo),
                    orderNo,Throwables.getStackTraceAsString(e));
            //更新寻仓反馈状态为：未处理
            ipJitxDeliveryService.updateJitxSyncStatus(orderNo, SyncStatus.UNSYNC, "寻仓占单消息发送失败，手动置为未处理");
        }
        stepResult.setStatus(StepStatus.FAILED);
        stepResult.setMessage("寻仓单占单消息发送失败，转换终止");
        return stepResult;
    }
}
