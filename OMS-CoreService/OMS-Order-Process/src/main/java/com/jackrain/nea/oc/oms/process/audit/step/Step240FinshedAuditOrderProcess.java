package com.jackrain.nea.oc.oms.process.audit.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderAutoAuditService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * 订单状态更新为3，添加审核日志
 *
 * @author: heliu
 * @since: 2019-03-20
 * create at : 2019-03-20 02:04
 */
@Step(order = 240, description = "订单状态更新为3，添加审核日志")
@Slf4j
public class Step240FinshedAuditOrderProcess extends BaseAuditOrderProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsOrderAutoAuditService omsOrderAutoAuditService;

    @Autowired
    private OmsOrderAdvanceParseService orderAdvanceParseService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("自动流程.Step240FinshedAuditOrderProcess[订单状态更新为3，添加审核日志:id：{}",
                    "订单状态更新为3，添加审核日志", orderInfo.getOrderId()), orderInfo.getOrderId());
        }

        boolean flag = omsOrderAutoAuditService.updateOrderStatus(orderInfo, operateUser);
        if (flag) {
            //审核时间埋点  类型自动
            OcBOrder order = new OcBOrder();
            order.setId(orderInfo.getOrderInfo().getId());
            order.setAuditType("auto");
            order.setAuditSuccessDate(new Date());
            //自动审核新增审核人信息
            order.setAuditId(operateUser.getId().longValue());
            order.setAuditName(operateUser.getEname());
            ocBOrderMapper.updateById(order);
            ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.AUDIT_SUCCESS_DATE,new Date(),order.getId(),operateUser);
            boolean b = orderAdvanceParseService.checkIsDepositPreSale(orderInfo.getOrderInfo());
            if (b){
                String reserveVarchar03 = orderInfo.getOrderInfo().getStatusPayStep();
                if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(reserveVarchar03) && TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(orderInfo.getOrderInfo().getSuggestPresinkStatus())) {
                    OcBOrder updateOcBOrder = new OcBOrder();
                    updateOcBOrder.setId(orderInfo.getOrderInfo().getId());
                    updateOcBOrder.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_NOTIFIED);
                    ocBOrderMapper.updateById(updateOcBOrder);
                }
            }
            String message = "审核成功. 终止审核流程!";
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("审核成功. 终止审核流程：{}",
                        "审核成功", orderInfo.getOrderId()), message);
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS, message, Step260CallFullLinkLogService.class);
        } else {
            String message = "订单状态异常, 无法更新订单状态。审核失败,异常终止审核流程!";
            log.error(LogUtil.format("审核失败,异常终止审核流程,异常信息:{}", "审核失败", orderInfo.getOrderId()));
            return new ProcessStepResult<>(StepStatus.FAILED, message);
        }
    }
}
