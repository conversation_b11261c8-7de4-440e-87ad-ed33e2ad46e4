package com.jackrain.nea.oc.oms.mq.processor.aop.parser;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description： 解析器获取工厂
 * Author: RESET
 * Date: Created in 2020/8/26 2:41
 * Modified By:
 */
@Component
public class MqMessageParserFactory {

    // 暂存
    private final Map<String, IMqMessageParser> matcherMap = new ConcurrentHashMap<>();

    /**
     * 注册
     *
     * @param parsers
     */
    @Autowired
    public void init(List<IMqMessageParser> parsers) {
        if (!CollectionUtils.isEmpty(parsers)) {
            parsers.forEach(m -> {
                matcherMap.put(m.getMethod(), m);
            });
        }
    }

    /**
     * 按类型获取解析器
     *
     * @param method
     * @return
     */
    public IMqMessageParser getParser(String method) {
        IMqMessageParser parser = matcherMap.get(method);

        if (Objects.isNull(method)) {
            throw new NDSException("获取解析器出错，无对应的解析器[" + method + "]");
        }

        return parser;
    }

    public static MqMessageParserFactory getInstance() {
        return ApplicationContextHandle.getBean(MqMessageParserFactory.class);
    }

}
