package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.OmsRefundTransferUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 锁定淘宝退款单
 *
 * @author: 周琳胜
 * @since: 2019-07-15
 * create at : 2019-07-15 18:44
 */
@Step(order = 20, description = "判断原单状态")
@Slf4j
@Component
public class Step020OrderCheckStatus extends BaseTaobaoFxRefundProcessStep
        implements IOmsOrderProcessStep<IpTaobaoFxRefundRelation> {

    @Override
    public ProcessStepResult<IpTaobaoFxRefundRelation> startProcess(
            IpTaobaoFxRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.debug("TaobaoFxReturnTransferOrder.step02" + orderInfo);
        IpBTaobaoFxRefund taobaoFxRefund = orderInfo.getTaobaoFxRefund();
        try {
            if (orderInfo != null) {
                boolean effectiveOrder = orderInfo.isEffectiveOrder();
                OcBOrder ocBOrder = orderInfo.getOcBOrder();

                // 订单状态判断-传wms不允许继续
                if (OmsRefundTransferUtil.isForbidOrderTransfer(ocBOrder)) {
                    ipTaobaoFxRefundService.updateRefundIsTransError(taobaoFxRefund, SysNotesConstant.SYS_REMARK90);
                    return new ProcessStepResult<>(StepStatus.FINISHED, SysNotesConstant.SYS_REMARK90);
                }

                boolean flag = ipTaobaoFxRefundService.noOriginalOrder(taobaoFxRefund,
                        ocBOrder, effectiveOrder);
                if (flag) {
                    return new ProcessStepResult<>(StepStatus.FINISHED,
                            "淘宝分销单据" + orderInfo.getOrderId() + "转换完成");
                }
                //存在原单并且原单不是作废或者取消 判断原单的状态
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 退单转换异常!", e);
            //修改中间表状态及系统备注
            ipTaobaoFxRefundService.updateRefundIsTransError(taobaoFxRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
