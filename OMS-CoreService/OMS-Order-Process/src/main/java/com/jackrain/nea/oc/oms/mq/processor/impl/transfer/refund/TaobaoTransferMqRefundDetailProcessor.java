package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import cn.hutool.json.JSONUtil;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.TaobaoTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.OmsTaobaoRefundService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> 孙勇生
 * create at:  19/3/6  20:33
 * @description: 淘宝退换货单退款单消息处理器
 */
@Slf4j
public class TaobaoTransferMqRefundDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private TaobaoTransferRefundProcessImpl taobaoTransferRefundProcess;
    @Autowired
    private OmsTaobaoRefundService omsTaobaoRefundService;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("淘宝退换货单退款单消息处理器.Start", orderNo));
        }

        OmsTaobaoRefundRelation omsTaobaoRefundRelation = this.omsTaobaoRefundService.selectTaoBaoRefundRelation(orderNo);
        log.info(LogUtil.format("淘宝退换货单退款单消息处理器.omsTaobaoRefundRelation:{}", orderNo), JSONUtil.toJsonStr(omsTaobaoRefundRelation));

        if (omsTaobaoRefundRelation == null) {
            String errorMessage = Resources.getMessage("TaobaoExchange Received OrderMqInfo Not Exist!OrderNo=" + orderNo);
            log.error(LogUtil.format(errorMessage, orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = taobaoTransferRefundProcess.start(omsTaobaoRefundRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("淘宝退换货单退款单消息处理器.Result:{}", orderNo), resultList.isProcessSuccess());
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.TAOBAO
                && orderMqInfo.getOrderType() == OrderType.REFUND;
    }

}
