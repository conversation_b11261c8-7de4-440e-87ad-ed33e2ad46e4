package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.StepExeState;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.StepExecInfo;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.JDDirectTransferSupply;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.web.face.User;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/30
 */
@Component
@Step(order = 30, description = "un audit process")
public class Step030UnAuditProcess extends AbstractJDDirectCancelProcessStep {


    @Override
    public ProcessStepResult<OmsJDDirectCancelRelation> startProcess(OmsJDDirectCancelRelation omsRelation,
                                                                     ProcessStepResult preStepResult,
                                                                     boolean isAutoMakeup, User operateUser) {

        StepExecInfo stepExecInfo = omsRelation.getStepExecInfo();
        List<OmsOrderRelation> ocRelations = omsRelation.getOcOrderRelations();
        for (OmsOrderRelation ocRelation : ocRelations) {
            Integer status = ocRelation.getOcBOrder().getOrderStatus();
            OmsOrderStatus orderStatus = OmsOrderStatus.convert2Enum(status == null ? 50 : status);
            switch (orderStatus) {
                case UNCONFIRMED:
                case BE_OUT_OF_STOCK:
                    boolean isSuccess = omsJDDirectCancelService.holdOcOrderProcess(ocRelation, stepExecInfo);
                    if (isSuccess) {
                        omsJDDirectCancelService.assignReturningStatus(ocRelation, stepExecInfo);
                    }
                    break;
                case IN_DISTRIBUTION:
                case CHECKED:
                    boolean isHoldSuccess = omsJDDirectCancelService.holdOcOrderProcess(ocRelation, stepExecInfo);
                    if (isHoldSuccess){
                        omsJDDirectCancelService.assignReturningStatus(ocRelation, stepExecInfo);
                        omsJDDirectCancelService.deAuditOcOrderProcess(ocRelation, stepExecInfo, operateUser);
                    }
                    break;
                case WAREHOUSE_DELIVERY:
                case PLATFORM_DELIVERY:
                    omsJDDirectCancelService.warehouseOrPlatformDeliveryProcess(ocRelation, stepExecInfo, operateUser);
                    break;
                case CANCELLED:
                case SYS_VOID:
                    omsJDDirectCancelService.cancelOrVoidProcess(ocRelation, stepExecInfo);
                    break;
                case TO_BE_ASSIGNED:
                case PENDING_WMS:
                    omsJDDirectCancelService.tobeConfirmOrderProcess(ocRelation, stepExecInfo);
                    break;
            }
        }
        return JDDirectTransferSupply.getTransStep(StepExeState.UPDATE);
    }


}
