package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @author: 秦雄飞
 * @date 2020/9/3 下午4:32
 */
@Step(order = 60, description = "是否已有平台单号相同的零售发货单")
public class Step060AlibabaAscpIsTransfered extends BaseAlibabaAscpOrderProcessStep
        implements IOmsOrderProcessStep<IpAlibabaAscpOrderRelation> {

    @Autowired
    private OmsOrderService omsOrderService;

    @Override
    public ProcessStepResult<IpAlibabaAscpOrderRelation> startProcess(IpAlibabaAscpOrderRelation orderInfo,
                                                                      ProcessStepResult preStepResult,
                                                                      boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        List<OcBOrder> ocBOrders = omsOrderService.selectOmsOrderRecord(orderNo, null, false);
        if (CollectionUtils.isNotEmpty(ocBOrders)) {
            String message = "订单已存在";
            ipAlibabaAscpOrderService.updateAlibabaAscpOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFERRED, message);
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        } else {
            return new ProcessStepResult<>(StepStatus.SUCCESS, "零售发货单未生成，进入下一阶段");
        }
    }
}