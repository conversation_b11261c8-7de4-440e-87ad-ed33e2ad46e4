package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 发货后退款判断
 *
 * @author: 周琳胜
 * @since: 2019-07-16
 * create at : 2019-07-16 1:22
 */
@Step(order = 40, description = "发货后")
@Slf4j
@Component
public class Step040HasGoodsReturnAfter extends BaseTaobaoFxRefundProcessStep
        implements IOmsOrderProcessStep<IpTaobaoFxRefundRelation> {

    @Override
    public ProcessStepResult<IpTaobaoFxRefundRelation> startProcess(IpTaobaoFxRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.debug("TaobaoFxReturnTransferOrder.step04" + orderInfo);
        //【5,仓库发货】【6,平台发货】【11,物流已送达】【12,交易完成】
        IpBTaobaoFxRefund taobaoFxRefund = orderInfo.getTaobaoFxRefund();
        try {
            OcBOrder ocBOrder = orderInfo.getOcBOrder();
            if (ocBOrder != null) {
                //仓库发货5、平台发货6、完成
                Integer orderStatus = ocBOrder.getOrderStatus(); //主表订单状态
                if (OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(orderStatus)
                        || OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(orderStatus)
                        || OmsOrderStatus.DEAL_DONE.toInteger().equals(orderStatus)
                        || OmsOrderStatus.DELIVERED.toInteger().equals(orderStatus)) {
                    Boolean aBoolean = ipTaobaoFxRefundService.orderStatusHasGoodReturnStatusAfter(orderInfo, operateUser);
                    if (aBoolean == null) {
                        String message = "订单id:" + ocBOrder.getId() + ";订单状态为:" + orderStatus + ";转换完成";
                        return new ProcessStepResult<>(StepStatus.FINISHED, message);
                    }
                    if (aBoolean) {
                        //下一步转换 【退单发货后转换服务】
                        return new ProcessStepResult<>(StepStatus.SUCCESS);
                    } else {
                        //todo  【仅退款应收调整单服务】
                        log.debug("淘宝分销转单调用应收调整单服务开始" + taobaoFxRefund.getSubOrderId());
                        boolean flag = ipTaobaoFxRefundService.receivablesAdjustment(orderInfo, operateUser);
                        if (flag) {
                            String message = "订单id:" + ocBOrder.getId() + ";订单状态为:" + orderStatus + ";调用仅退款应收调整单,转换完成";
                            return new ProcessStepResult<>(StepStatus.FINISHED, message);
                        }
                    }
                }
            }
            return new ProcessStepResult<>(StepStatus.FINISHED);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 退单转换异常!", e);
            //修改中间表状态及系统备注
            ipTaobaoFxRefundService.updateRefundIsTransError(taobaoFxRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }

}
