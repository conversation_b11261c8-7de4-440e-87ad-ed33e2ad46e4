package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.standplat.step;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * description：判断转换状态
 *
 * <AUTHOR>
 * @date 2022/1/7
 */
@Step(order = 10, description = "判断通用退单换货转换状态")
@Slf4j
@Component
public class Step010StandplatCheckExchangeTransStatus extends BaseStandplatExchangeProcessStep implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        if(log.isDebugEnabled()){
            log.debug("BaseStandplatExchangeProcessStep.StepStandplat010CheckExchangeTransStatus start >>> orderInfo：{}", JSON.toJSONString(orderInfo));
        }
        IpBStandplatRefund ipBTaobaoExchange = orderInfo.getIpBStandplatRefund();
        Integer isTrans = ipBTaobaoExchange.getIstrans();
        if (TransferOrderStatus.NOT_TRANSFER.toInteger() != isTrans) {
            return new ProcessStepResult<>(StepStatus.FAILED, "退单不是未转换状态,转换失败");
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
