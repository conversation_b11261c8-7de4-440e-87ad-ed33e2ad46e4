package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp.step;

import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 秦雄飞
 * @date 2020/9/3 下午4:32
 */

@Step(order = 50, description = "判断省市县是否能匹配")
@Slf4j
@Component
public class Step050AlibabaAscpCheckProvinceCityAreaInfo extends BaseAlibabaAscpOrderProcessStep
        implements IOmsOrderProcessStep<IpAlibabaAscpOrderRelation> {

    @Autowired
    private RegionNewService regionService;

    @Override
    public ProcessStepResult<IpAlibabaAscpOrderRelation> startProcess(IpAlibabaAscpOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        IpBAlibabaAscpOrder alibabaAscpOrder = orderInfo.getAlibabaAscpOrder();
        //买家所在省
        String provinceName = alibabaAscpOrder.getReceiverProvince();
        //买家所在市
        String cityName = alibabaAscpOrder.getReceiverCity();
        //买家所在区
        String areaName = alibabaAscpOrder.getReceiverArea();
        ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(provinceName,
                cityName, areaName);

        if (provinceCityAreaInfo == null
                || provinceCityAreaInfo.getProvinceInfo() == null) {
            String message = "省市区信息缺失";
            ipAlibabaAscpOrderService.updateAlibabaAscpOrderTransStatus(orderNo,
                    TransferOrderStatus.TRANSFERFAIL, message);
            return new ProcessStepResult<>(StepStatus.FAILED, message);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "省市区匹配正常，进入下一阶段");
    }
}