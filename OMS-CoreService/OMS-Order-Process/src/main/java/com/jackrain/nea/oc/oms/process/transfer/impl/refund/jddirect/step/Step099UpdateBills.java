package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.StepExeState;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.model.relation.StepExecInfo;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.JDDirectTransferSupply;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.util.OmsTransferSupply;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/28
 */
@Slf4j
@Component
@Step(order = 999, description = "update")
public class Step099UpdateBills extends AbstractJDDirectCancelProcessStep {

    @Override
    public ProcessStepResult<OmsJDDirectCancelRelation> startProcess(
            OmsJDDirectCancelRelation cancelRelation,
            ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        StepExecInfo stepExecInfo = cancelRelation.getStepExecInfo();
        if (  stepExecInfo.getIsNeedReTrans() > 0) {
            stepExecInfo.setStepExeState(StepExeState.UPDATE);
            stepExecInfo.setTransMessage(OmsTransferSupply.optimizeMsg.apply(stepExecInfo.getGlobalMessage()));
            if (TransferOrderStatus.TRANSFERRED == stepExecInfo.getTransStatus()){
                stepExecInfo.reviseTransStatus(TransferOrderStatus.NOT_TRANSFER);
            }
        }
        switch (stepExecInfo.getStepExeState()) {
            case UPDATE:
                omsJDDirectCancelService.updateIpBilInfo(stepExecInfo);
            default:
                break;
        }
    //    omsJDDirectCancelService.updateOcBilInfo(stepExecInfo);
        return JDDirectTransferSupply.getTransStep(StepExeState.SUCCESS);
    }
}
