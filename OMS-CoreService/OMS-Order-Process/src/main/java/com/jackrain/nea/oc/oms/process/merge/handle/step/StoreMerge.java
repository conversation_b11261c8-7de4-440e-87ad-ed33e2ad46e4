package com.jackrain.nea.oc.oms.process.merge.handle.step;

import com.jackrain.nea.oc.oms.model.MergeOrderInfo;
import com.jackrain.nea.oc.oms.model.StCMergeOrderInfo;
import com.jackrain.nea.oc.oms.model.enums.MergeTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.process.merge.handle.MergeEnum;
import com.jackrain.nea.oc.oms.process.merge.handle.MergeStrategyHandler;
import com.jackrain.nea.oc.oms.services.OcMergeOrderService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * <AUTHOR> ruan.gz
 * @Description : 店铺策略检查
 * @Date : 2020/6/20
 **/
@Slf4j
@Service("StoreMergeStrategy")
class StoreMerge implements MergeStrategyHandler {


    @Autowired
    private OcMergeOrderService ocMergeOrderService;

    @Override
    public Boolean doSingleHandle(MergeOrderInfo info) {
        OcBOrder ocBOrder = info.getOcBOrder();
        StCMergeOrderInfo stCMergeOrderDO = info.getStCMergeOrderDO();
        Set<Integer> typeSet = ocMergeOrderService.getSplitMergeSt(stCMergeOrderDO);
        String isPassCheck = ocMergeOrderService.checkMergeStrategy(ocBOrder, stCMergeOrderDO, typeSet, MergeTypeEnum.AUTO);

        if (StringUtils.isNotBlank(isPassCheck)){
            if (log.isDebugEnabled()){
                log.debug(LogUtil.format("合单剔除原因:{}",
                        "合单剔除原因", ocBOrder.getId()), isPassCheck);
            }
            return true;
        }
        return false;
    }

    @Override
    public Integer getSort(String name) {
        return MergeEnum.getValueFromValueTag(name);
    }

    @Override
    public Boolean doWholeHandle(MergeOrderInfo info) {
        return false;
    }


}




