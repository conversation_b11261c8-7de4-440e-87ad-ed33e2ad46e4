package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jddirect.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 释放当前行，更新转换状态为2
 *
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 22:33
 */
@Step(order = 50, description = "释放当前行，更新转换状态为2")
@Component
@Slf4j
public class Step050UpdateOrderTransferStatus extends
        BaseJingdongDirectOrderProcessStep implements IOmsOrderProcessStep<IpJingdongDirectOrderRelation> {


    @Override
    public ProcessStepResult<IpJingdongDirectOrderRelation> startProcess(IpJingdongDirectOrderRelation orderInfo,
                                                                         ProcessStepResult preStepResult, boolean isAutoMakeup,
                                                                         User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        ipJingdongDirectService.updateIpBJingdongDirectIstrans(orderNo,
                TransferOrderStatus.TRANSFERRED, "转单成功");
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
