package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx.step;

import com.jackrain.nea.log.LogCat;
import com.jackrain.nea.log.LogEvent;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 周琳胜
 * @since : 2019-07-11
 * create at : 2019-07-11 10:24
 * 淘宝分销中间表转换为全渠道订单
 */
@Step(order = 50, description = "淘宝分销中间表转换为全渠道订单")
@Slf4j
@Component
public class Step50SaveOmsOrder extends BaseTaobaoFxOrderProcessStep implements IOmsOrderProcessStep<IpTaobaoFxOrderRelation> {

    @Override
    public ProcessStepResult<IpTaobaoFxOrderRelation> startProcess(IpTaobaoFxOrderRelation orderInfo,
                                                                   ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {
        IpBTaobaoFxOrder order = orderInfo.getIpBTaobaoFxOrder();
        String orderState = order.getState();
        boolean isHistoryOrder = StringUtils.equalsIgnoreCase(JingdongOrderStatus.WAIT_GOODS_RECEIVE_CONFIRM, orderState)
                || StringUtils.equalsIgnoreCase(JingdongOrderStatus.FINISHED_L, orderState);

        try {
            List<LogEvent> logEventList = new ArrayList<>();
            LogEvent eventConvert = LogCat.newEvent(Step50SaveOmsOrder.class.getSimpleName(), "ConvertOrder");
            IpTaobaoFxOrderRelation relation = ipTaobaoFxService.convertTaobaoFxToOrder(orderInfo, isHistoryOrder);
            eventConvert.complete();
            logEventList.add(eventConvert);

            ipTaobaoFxService.saveOrderInfo(relation, isHistoryOrder);
            LogEvent eventStartSave = LogCat.newEvent(Step50SaveOmsOrder.class.getSimpleName(), "SaveOrder");
            eventStartSave.complete();
            logEventList.add(eventStartSave);
            //更新转单状态,已转换
            IpBTaobaoFxOrder ipBTaobaoFxOrder = new IpBTaobaoFxOrder();
            ipBTaobaoFxOrder.setId(orderInfo.getOrderId());
            ipBTaobaoFxOrder.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
            ipBTaobaoFxOrder.setSysremark("转单成功");
            if (order.getTransCount() == null) {
                ipBTaobaoFxOrder.setTransCount(1L);
            } else {
                ipBTaobaoFxOrder.setTransCount(order.getTransCount() + 1L);
            }
            ipTaobaoFxService.updateTransferStatus(ipBTaobaoFxOrder);
            return new ProcessStepResult<>(StepStatus.SUCCESS, "转单成功");
        } catch (Exception ex) {
            //转单失败，更新为未转换
            String str = ex.getMessage();
            IpBTaobaoFxOrder ipBTaobaoFxOrder = new IpBTaobaoFxOrder();
            ipBTaobaoFxOrder.setId(orderInfo.getOrderId());
            // 转换失败
            ipBTaobaoFxOrder.setIstrans(4);
            ipBTaobaoFxOrder.setSysremark("存储数据失败，退出转单服务,原因:" + str);
            if (order.getTransCount() == null) {
                ipBTaobaoFxOrder.setTransCount(1L);
            } else {
                ipBTaobaoFxOrder.setTransCount(order.getTransCount() + 1L);
            }
            ipTaobaoFxService.updateTransferStatus(ipBTaobaoFxOrder);
            log.error(order.getFenxiaoId() + "存储数据失败，退出转单服务,原因:", ex);
            return new ProcessStepResult<>(StepStatus.FAILED, "存储数据失败，退出转单服务,原因:" + str);
        }
    }
}
