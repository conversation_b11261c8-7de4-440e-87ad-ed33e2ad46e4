package com.jackrain.nea.oc.oms.process.jitx.feedback.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpJitxDeliveryRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.oc.oms.services.IpJitxDeliveryService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-03-10 14:30
 * @Description : 检查寻仓单同批次时效订单状态
 **/
@Step(order = 70, description = "检查寻仓单同批次时效订单状态")
@Slf4j
@Component
public class Step070CheckDeliveryTimeOrderStatus extends BaseJitxDeliveryProcessStep
        implements IOmsOrderProcessStep<IpJitxDeliveryRelation> {
    @Autowired
    protected IpVipTimeOrderService ipVipTimeOrderService;

    @Autowired
    protected IpJitxDeliveryService ipJitxDeliveryService;

    @Override
    public ProcessStepResult<IpJitxDeliveryRelation> startProcess(IpJitxDeliveryRelation deliveryInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpJitxDeliveryRelation> stepResult = new ProcessStepResult<>();
        stepResult.setStatus(StepStatus.FAILED);
        String orderSn = deliveryInfo.getOrderNo();
        //时效订单
        List<IpVipTimeOrderRelation> ipVipTimeOrderRelationList = deliveryInfo.getIpVipTimeOrderRelationList();
        List<IpBTimeOrderVip> timeOrderVipList = new ArrayList<>();
        //查询时效订单占用明细集合
        List<IpBTimeOrderVipOccupyItem> ipBTimeOrderVipOccupyItemList = new ArrayList<>();
        RedisReentrantLock redisLock = deliveryInfo.getRedisLock();
        try{
            for(IpVipTimeOrderRelation relation:ipVipTimeOrderRelationList){
                timeOrderVipList.add(relation.getIpBTimeOrderVip());
                ipBTimeOrderVipOccupyItemList.addAll(relation.getIpBTimeOrderVipOccupyItemList());
            }

            //匹配时效订单
            if(CollectionUtils.isEmpty(timeOrderVipList)){
                String operateMessage = "寻仓单 OrderNo:" + orderSn + "同批次时效订单为空，暂不处理";
                deliveryInfo.setRemarks("同批次时效订单为空，暂不处理");
                stepResult.setMessage(operateMessage);
                stepResult.setStatus(StepStatus.FAILED);
                ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, "同批次时效订单为空，暂不处理");
                return stepResult;
            }

            if(CollectionUtils.isEmpty(ipBTimeOrderVipOccupyItemList)){
                String operateMessage = "寻仓单 orderSn:" + orderSn + "同批次时效订单占用明细为空，暂不处理";
                deliveryInfo.setRemarks("同批次时效订单占用明细为空，暂不处理");
                stepResult.setMessage(operateMessage);
                stepResult.setStatus(StepStatus.FAILED);
                ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, "同批次时效订单占用明细为空，暂不处理");
                return stepResult;
            }
            //时效订单单据状态集合
            List<Integer> timeOrderStatusList = timeOrderVipList.stream().map(IpBTimeOrderVip::getStatus).collect(Collectors.toList());

            //时效订单单据状态存在：待占单、占单中、寻仓中 则不处理
            List<Integer> createStatusList = new ArrayList<>();
            createStatusList.add(TimeOrderVipStatusEnum.CREATED.getValue());
            createStatusList.add(TimeOrderVipStatusEnum.IN_SINGLE.getValue());
            createStatusList.add(TimeOrderVipStatusEnum.IN_SEEKING_STORE.getValue());
            createStatusList.retainAll(timeOrderStatusList);
            if(!CollectionUtils.isEmpty(createStatusList)){
                //不处理
                String operateMessage = "时效订单单据状态存在：待占单、占单中、寻仓中,不处理";
                deliveryInfo.setRemarks(operateMessage);
                stepResult.setMessage(operateMessage);
                stepResult.setStatus(StepStatus.FINISHED);
                ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, operateMessage);
                return stepResult;
            }
            //继续寻仓
            stepResult.setStatus(StepStatus.SUCCESS);
            stepResult.setMessage("寻仓单OrderNo=" + orderSn + "同批次时效订单状态校验通过，进入下一阶段！");
        }catch (Exception e){
            log.error(LogUtil.format("寻仓单OrderSn:{}转换失败:{}","Step070CheckDeliveryTimeOrderStatus"),
                    orderSn, Throwables.getStackTraceAsString(e));
            ipJitxDeliveryService.updateJitxSyncStatus(orderSn, SyncStatus.UNSYNC, "转换失败"+e.getMessage());
        }finally {
            if(!StepStatus.SUCCESS.equals(stepResult.getStatus())){
                redisLock.unlock();
            }
        }
        return stepResult;
    }
}
