package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobao.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoExchangeOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 原始订单不存在所走的逻辑
 */

@Step(order = 50, description = "不存在原始订单的服务")
@Slf4j
@Component
public class Step050OriginalOrderNoExist extends BaseTaobaoExchangeProcessStep
        implements IOmsOrderProcessStep<IpTaobaoExchangeRelation> {

    @Override
    public ProcessStepResult<IpTaobaoExchangeRelation> startProcess(IpTaobaoExchangeRelation orderInfo,
                                                                    ProcessStepResult preStepResult,
                                                                    boolean isAutoMakeup,
                                                                    User operateUser) {
        IpBTaobaoExchange taobaoExchange = orderInfo.getTaobaoExchange();
        try {
            String status = taobaoExchange.getStatus();
            Date created = taobaoExchange.getCreated();
            OcBOrder ocBOrder = orderInfo.getOriginalValidOrderInfo();
            //跟新状态
            if (TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.BUYER_RETURN_COLLECT_GOODS.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.STAY_BUYER_RETURN.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_CLOSE.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.REFUND_PLEASE.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.EXCHANGE_STAY_HANDLE.getName().equals(status)
                    || TaobaoExchangeOrderExt.ExchangeOrderCentreStatus.WAIT_ISSUE_EXCHANGE_GOODS.getName().equals(status)) {
                if (ocBOrder == null) {
                    ipTaobaoExchangeService.noOriginalOrder(created, orderInfo.getTaobaoExchange());
                    return new ProcessStepResult<>(StepStatus.FINISHED, "单据" + orderInfo.getOrderId() + "转换完成");
                }

            }
            return new ProcessStepResult<>(StepStatus.FINISHED);
        } catch (Exception e) {
            ipTaobaoExchangeService.updateExchangeIsTransError(taobaoExchange, e.getMessage());
            log.error(this.getClass().getName() + " 退换货转换异常", e);
            String errorMessage = "退换货转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
