package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.config.ScanIncomingMqConfig;
import com.jackrain.nea.oc.oms.services.ScanIncomingMqDealService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-08-22
 * create at : 2019-08-22 4:25 PM
 * 扫描入库mq处理接收
 *
 * 无用废弃
 */
@Deprecated
@Slf4j
@RocketMqMessageListener(name = "ScanIncomingReceiverMqService", type = MqTypeEnum.DEFAULT)
public class ScanIncomingReceiverMqService implements BaseMessageListener {

    @Autowired
    ScanIncomingMqConfig mqConfig;

    @Autowired
    private ScanIncomingMqDealService scanIncomingMqDealService;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        log.debug(LogUtil.format("扫描入库mq处理接收", messageKey));
        try {
            log.debug(LogUtil.format("扫描入库messageBody: {}", messageKey), messageBody);
            scanIncomingMqDealService.execute(messageBody);
        } catch (Exception e) {
            log.error(LogUtil.format("扫描入库mq处理接收.异常: {}"), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }
}
