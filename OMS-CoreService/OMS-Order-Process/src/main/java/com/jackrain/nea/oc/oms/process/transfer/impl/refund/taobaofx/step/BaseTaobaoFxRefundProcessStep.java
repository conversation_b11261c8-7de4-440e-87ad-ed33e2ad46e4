package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx.step;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.services.BeforeShipmentReturnService;
import com.jackrain.nea.oc.oms.services.IpTaobaoExchangeService;
import com.jackrain.nea.oc.oms.services.IpTaobaoFxRefundService;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.oc.oms.util.TaobaoFxReturnTransferUtil;

import com.jackrain.nea.rpc.PsRpcService;
import org.springframework.beans.factory.annotation.Autowired;

//import com.jackrain.nea.oc.oms.services.OmsOrderMakeupService;

/**
 * 基础淘宝退款单转单处理阶段
 *
 * @author: 周琳胜
 * @since: 2019-07-15
 * create at : 2019-07-15 16:38
 */
public abstract class BaseTaobaoFxRefundProcessStep {

    protected ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAOFX;
    }

    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }


    @Autowired
    protected IpTaobaoExchangeService taoBaoExchangeServiceIp;

    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    protected IpTaobaoFxRefundService ipTaobaoFxRefundService;

//    @Autowired
//    protected IpTaobaoFxRefundSendBeforeService ipTaobaoFxRefundSendBeforeService;


    @Autowired
    protected OmsReturnOrderService omsReturnOrderService;

    @Autowired
    protected BeforeShipmentReturnService beforeShipmentReturnService;

    @Autowired
    protected TaobaoFxReturnTransferUtil taobaoFxReturnTransferUtil;

}
