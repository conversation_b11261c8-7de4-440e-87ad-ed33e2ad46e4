package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jddirect;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2022/3/28 下午2:06
 * @Version 1.0
 */
@Component
public class JingdongDirectTransferOrderProcessImpl extends AbstractOrderProcess<IpJingdongDirectOrderRelation> {
    @Override
    protected String getChildPackageName() {
        return "jddirect";
    }

    @Override
    protected long getProcessOrderId(IpJingdongDirectOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpJingdongDirectOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.TRANSFER_JINGDONG_DIRECT_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.JINGDONG;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.TRANSFER_ORDER;
    }
}
