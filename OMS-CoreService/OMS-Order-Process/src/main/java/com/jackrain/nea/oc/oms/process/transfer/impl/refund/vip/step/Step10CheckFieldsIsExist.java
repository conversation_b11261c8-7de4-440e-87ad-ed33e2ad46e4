package com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.step;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBVipReturnOrder;
import com.jackrain.nea.oc.oms.services.IpVipReturnOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description: 校验退供单中间表的必须字段是否为空
 * @create 2021-07-01 17:29
 */
@Step(order = 10, description = "校验退供单中间表的必须字段是否为空")
@Slf4j
@Component
public class Step10CheckFieldsIsExist extends BaseVipReturnOrderProcessStep implements IOmsOrderProcessStep<IpVipReturnOrderRelation> {

    @Autowired
    private IpVipReturnOrderService ipVipReturnOrderService;

    @Override
    public ProcessStepResult<IpVipReturnOrderRelation> startProcess(IpVipReturnOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBVipReturnOrder vipReturnOrder = orderInfo.getVipReturnOrder();
        String errorMsg = "";
        String orderNo = orderInfo.getOrderNo();
        if (vipReturnOrder.getReturnSn() == null) {
            errorMsg = "退供单号为空，不允许转换！";
            ipVipReturnOrderService.updateRemark(errorMsg, orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            return new ProcessStepResult(StepStatus.FAILED, errorMsg);
        }
        if (vipReturnOrder.getWarehouse() == null) {
            errorMsg = "退供发货仓为空，不允许转换！";
            ipVipReturnOrderService.updateRemark(errorMsg, orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            return new ProcessStepResult(StepStatus.FAILED, errorMsg);
        }
        if (vipReturnOrder.getConsignee() == null) {
            errorMsg = "退供收货人为空，不允许转换！";
            ipVipReturnOrderService.updateRemark(errorMsg, orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            return new ProcessStepResult(StepStatus.FAILED, errorMsg);
        }
        if (vipReturnOrder.getState() == null) {
            errorMsg = "省/州为空，不允许转换！";
            ipVipReturnOrderService.updateRemark(errorMsg, orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            return new ProcessStepResult(StepStatus.FAILED, errorMsg);
        }
        if (vipReturnOrder.getCity() == null) {
            errorMsg = "城市人为空，不允许转换！";
            ipVipReturnOrderService.updateRemark(errorMsg, orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            return new ProcessStepResult(StepStatus.FAILED, errorMsg);
        }
        if (vipReturnOrder.getRegion() == null) {
            errorMsg = "区/县为空，不允许转换！";
            ipVipReturnOrderService.updateRemark(errorMsg, orderNo,
                    TransferOrderStatus.TRANSFERFAIL.toInteger());
            return new ProcessStepResult(StepStatus.FAILED, errorMsg);
        }
        return new ProcessStepResult(StepStatus.SUCCESS,
                "校验寄售仓和退供仓是否存在成功，进入下一阶段");
    }
}
