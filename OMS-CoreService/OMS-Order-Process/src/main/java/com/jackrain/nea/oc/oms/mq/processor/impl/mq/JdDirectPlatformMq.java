package com.jackrain.nea.oc.oms.mq.processor.impl.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.annotation.RocketMqMessageListener;
import com.burgeon.mq.core.BaseMessageListener;
import com.burgeon.mq.enums.MqTypeEnum;
import com.burgeon.mq.error.MqException;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.util.OcBOrderDeliveryFailUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author: 秦雄飞
 * @time: 2022/10/18 23:26
 * @description: 京东厂直发货回传
 *
 *
 * 无用废弃
 */

@Deprecated
@Slf4j
@RocketMqMessageListener(name = "JdDirectPlatformMq", type = MqTypeEnum.CLOUD)
public class JdDirectPlatformMq implements BaseMessageListener {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsOrderPlatformDeliveryService omsOrderPlatformDeliveryService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderDeliveryFailUtil deliveryFailUtil;

    @Override
    public void consume(String messageBody, String messageTopic, String messageKey, String messageTag, Object object) {
        User user = SystemUserResource.getRootUser();
        try {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("京东厂直发货: {}", messageKey), messageBody);
            }
            JSONArray array = JSON.parseArray(messageBody);
            for (int i = 0; i < array.size(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                boolean result = jsonObject.getBoolean("isSuccess");
                Long id = jsonObject.getLongValue("ID");
                String customorderId = jsonObject.getString("CUSTOMORDERID");
                String returnMessage = jsonObject.getString("ERRMSG");
                OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
                //新增日志信息
                String logMsg = "订单" + id + "(平台单号=" + customorderId + ")平台发货结果" + returnMessage;
                String errorMessage = jsonObject.get("ERRMSG") == null ? "" : jsonObject.get("ERRMSG").toString();
                omsOrderLogService.addUserOrderLog(id, ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, null, returnMessage, user);
                if (errorMessage.contains("发货完成")) {
                    OcBOrder updateOrder = new OcBOrder();
                    updateOrder.setSysremark("不允许再次执行发货,标记平台发货");
                    updateOrder.setId(id);
                    ocBOrderMapper.updateById(updateOrder);
                    result = true;
                }
                if (result) {
                    ocBOrder.setIsForce(1L);
                    //更新订单主表状态推ES 并作废退单推es
                    omsOrderPlatformDeliveryService.updateMasterOrderStatusPushES(ocBOrder, user);
                } else {
                    //失败打失败的标记 失败信息
                    ocBOrder.setIsForce(0L);
                    ocBOrder.setForceSendFailReason(StringUtils.substring(errorMessage, 0, 200));
                    omsOrderService.updateOrderInfo(ocBOrder);
                    //调用主表更新失败次数接口
                    ocBOrderMapper.updateMasterFailTimesById(ocBOrder.getId());
                    deliveryFailUtil.addOcBOrderDeliveryFail(ocBOrder);
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("平台发货错误信息: {}", messageKey), Throwables.getStackTraceAsString(e));
            throw new MqException(e);
        }
    }
}