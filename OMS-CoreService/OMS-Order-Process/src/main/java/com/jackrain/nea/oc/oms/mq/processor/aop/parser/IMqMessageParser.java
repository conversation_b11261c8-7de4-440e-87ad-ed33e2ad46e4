package com.jackrain.nea.oc.oms.mq.processor.aop.parser;

import com.aliyun.openservices.ons.api.Message;
import com.jackrain.nea.oc.oms.model.table.OcBMessageConsumeLog;

/**
 * Description： 消息解析：为了减少业务代码嵌入，独立解析服务
 * Author: RESET
 * Date: Created in 2020/8/26 1:43
 * Modified By:
 */
public interface IMqMessageParser {

    /**
     * 解析器
     *
     * @param message
     * @return
     */
    OcBMessageConsumeLog doParse(Message message);

    /**
     * 标准单据类型
     *
     * @return
     */
    String getMethod();

}
