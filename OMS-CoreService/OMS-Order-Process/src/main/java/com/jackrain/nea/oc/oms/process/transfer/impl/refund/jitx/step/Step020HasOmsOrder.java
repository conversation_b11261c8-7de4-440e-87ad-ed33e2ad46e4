package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jitx.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.request.TimeOrderVoidSgSendRequest;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.OmsRefundTransferUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 唯品会中间表单据是否存在全渠道订单中
 * @Date 2019-6-26
 **/
@Step(order = 20, description = "根据唯品会订单号判断是否存在全渠道订单中")
@Slf4j
@Component
public class Step020HasOmsOrder extends BaseJitxRefundProcessStep implements IOmsOrderProcessStep<IpJitxOrderRelation> {
    @Override
    public ProcessStepResult<IpJitxOrderRelation> startProcess(IpJitxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBJitxOrder ipBJitxOrder = orderInfo.getJitxOrder();
        if (log.isDebugEnabled()) {
            log.debug("Step020HasOmsOrder orderNo:{}", orderInfo.getOrderNo());
        }
        try {
            //1、根据唯品会订单号判断订单是否存在
            List<OcBOrder> orders = ipJitxRefundService.selectOmsOrder(ipBJitxOrder.getOrderSn());
            boolean flag = CollectionUtils.isNotEmpty(orders);
            if (!flag) {
                //2、不存在，则更新中间表“转换状态”：已转换（2），“转换时间”：当前时间 “系统备注“：无有效的全渠道订单，订单退款转换失败
                String remark="JITX订单交易状态为未发货取消，标记为已转换!";
                ipJitxRefundService.updateRefundIsTrans(TransferOrderStatus.TRANSFERRED, remark, ipBJitxOrder);
//                // todo 2020-08-28 添加释放时效订单
//                TimeOrderVoidSgSendRequest request = new TimeOrderVoidSgSendRequest();
//                request.setOrderSn(ipBJitxOrder.getOrderSn());
//                request.setUser(operateUser);
//                request.setRemark("JITX订单未发货前取消，时效订单释放库存成功");
//                request.setIsCancel(true);
//                timeOrderVoidSgSendService.voidSgSendV14(request);
                return new ProcessStepResult<>(StepStatus.FINISHED, remark);
            }

            // 原订单状态位于待分配,传wms中不允许操作
            boolean forbidOrderTransfer = OmsRefundTransferUtil.isForbidOrderTransfer(orders);
            if (forbidOrderTransfer) {
                ipJitxRefundService.updateRefundIsTransError(ipBJitxOrder, SysNotesConstant.SYS_REMARK90);
                return new ProcessStepResult<>(StepStatus.FINISHED, SysNotesConstant.SYS_REMARK90);
            }

            return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "判断是否存在全渠道订单成功，执行下一步！");
        } catch (Exception e) {
            log.error(LogUtil.format("退单转换异常:{}", "退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipJitxRefundService.updateRefundIsTransError(ipBJitxOrder, e.getMessage());
            String errorMessage = "退单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
