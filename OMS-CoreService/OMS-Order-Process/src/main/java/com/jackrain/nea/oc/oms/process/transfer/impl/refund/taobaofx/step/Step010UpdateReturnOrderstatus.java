package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobaofx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxRefund;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 周琳胜
 * @Date: 2019/7/15 16:43
 * @Version 1.0
 */
@Step(order = 10, description = "更改中间表状态为转换中")
@Slf4j
@Component
public class Step010UpdateReturnOrderstatus extends BaseTaobaoFxRefundProcessStep
        implements IOmsOrderProcessStep<IpTaobaoFxRefundRelation> {


    @Override
    public ProcessStepResult<IpTaobaoFxRefundRelation> startProcess(IpTaobaoFxRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        log.debug("TaobaoFxReturnTransferOrder.step01" + orderInfo);
        if (orderInfo == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空；退出转换");
        }
        IpBTaobaoFxRefund taobaoFxRefund = orderInfo.getTaobaoFxRefund();
        if (taobaoFxRefund.getIstrans() == TransferOrderStatus.NOT_TRANSFER.toInteger()) {
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } else {
            String errorMessage = "状态为非未转换,不允许转换!";
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
