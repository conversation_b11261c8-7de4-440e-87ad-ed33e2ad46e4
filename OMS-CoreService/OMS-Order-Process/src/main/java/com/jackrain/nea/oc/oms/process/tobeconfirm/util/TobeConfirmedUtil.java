package com.jackrain.nea.oc.oms.process.tobeconfirm.util;

import cn.hutool.core.util.ObjectUtil;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderWareHouseKeyword;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.psext.api.ValidityDefinitionQueryCmd;
import com.jackrain.nea.psext.model.request.ValidityDefinitionQueryRequest;
import com.jackrain.nea.psext.model.table.PsCValidityDefinition;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 占单流程中工具类
 *
 * @author: 易邵峰
 * @since: 2019-03-20
 * create at : 2019-03-20 18:16
 */
public class TobeConfirmedUtil {

    /**
     * 构造函数
     */
    private TobeConfirmedUtil() {

    }

    /**
     * 判断是否为京仓订单
     *
     * @param orderInfo 订单信息
     * @return true=订单信息
     */
    public static boolean checkIsJcOrder(OcBOrderRelation orderInfo) {
        return OrderWareHouseKeyword.IS_JD_WAREHOUSE_KEYWORD.equals(orderInfo.getOrderInfo().getIsJcorder());
    }

    /**
     * 判断订单是否待分配，缺货
     *
     * @param orderInfo 订单信息
     * @return true=待分配
     */
    public static boolean checkCanDistributeOrder(OcBOrderRelation orderInfo) {
        return OmsOrderStatus.TO_BE_ASSIGNED.toInteger().equals(orderInfo.getOrderInfo().getOrderStatus()) || OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(orderInfo.getOrderInfo().getOrderStatus());
    }

    /**
     * 判断订单是否周期购订单
     *
     * @param orderInfo 订单信息
     * @return true=周期购
     */
    public static boolean checkIsCyclePurchaseOrder(OcBOrder orderInfo) {
        return OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(orderInfo.getBusinessTypeCode())
                || OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER.getCode().equals(orderInfo.getBusinessTypeCode());
    }

}
