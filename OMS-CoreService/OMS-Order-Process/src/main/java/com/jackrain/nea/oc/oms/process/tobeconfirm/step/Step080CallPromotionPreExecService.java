package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.process.tobeconfirm.util.TobeConfirmedUtil;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsPromotionPreService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 调用促销预处理接口，更新占单状态为3
 *
 * @author: 易邵峰
 * @since: 2019-01-20
 * create at : 2019-01-20 02:15
 */
@Step(order = 80, description = "调用促销预处理接口，更新占单状态为3")
@Slf4j
public class Step080CallPromotionPreExecService extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OmsPromotionPreService omsPromotionPreService;

    @Autowired
    private OmsSystemConfig omsSystemConfig;
    @Autowired
    private OmsOrderLogService omsOrderLogService;


    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult,
                                                            boolean isAutoMakeup, User operateUser) {
        boolean skipStep081 = omsSystemConfig.isTobeConfirmedSkipCallPromotionEnabled();
        //暂时关闭 单独开关
        if (skipStep081 || PlatFormEnum.POS.getCode().equals(orderInfo.getOrderInfo().getPlatform())) {
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        }

        //toc残次订单跳过
        if (OmsOrderUtil.isToCCcOrder(orderInfo.getOrderInfo())){
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        }
        try {
            OcBOrder order = orderInfo.getOrderInfo();
            if (order == null) {
                return new ProcessStepResult<>(StepStatus.FINISHED, "订单OrderId" + orderInfo.getOrderId() + "的订单状态异常,退出调用赠品服务");
            }
            boolean toBOrderPt = OmsBusinessTypeUtil.isToBOrderPt(order);
            boolean toCOrderPt = OmsBusinessTypeUtil.isToCOrderPt(order);
            if(toBOrderPt || toCOrderPt) {
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                        OrderLogTypeEnum.ORDER_PROM.getKey(), "该业务类型订单不支持促销策略", "", "", operateUser);
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            String orderSource = order.getOrderSource();
            // 导入的订单,不走促销
            if ("手工新增".equals(orderSource)) {
                return new ProcessStepResult<>(StepStatus.SUCCESS);
            }
            // 目前商品类型 是未拆分的组合和未拆分的福袋及正常商品走促销，预售商品不走
            List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
            orderItemList = orderItemList.stream().filter(p -> p.getProType() == SkuType.NO_SPLIT_COMBINE
                    || p.getProType() == SkuType.NORMAL_PRODUCT).collect(Collectors.toList());
            if (!TobeConfirmedUtil.checkIsJcOrder(orderInfo)) {
                ValueHolder holder = omsPromotionPreService.executePromotionPre(order, orderItemList);
                boolean flag = omsPromotionPreService.handleResult(holder, order);
                if (!flag) {
                    OcBOrder ocBOrder = new OcBOrder();
                    ocBOrder.setId(order.getId());
                    ocBOrder.setOccupyStatus(OrderOccupyStatus.STATUS_45);
                    omsOrderService.updateOrderInfo(ocBOrder);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "正在执行促销,请等待...");
                }
            }
        } catch (Exception ex) {
            OcBOrder errorOrderInfo = new OcBOrder();
            String operateMessage = "订单OrderID" + orderInfo.getOrderId() + "的订单调用促销预执行服务失败!" + ex.getMessage();
            log.error(LogUtil.format("的订单调用促销预执行服务失败,异常信息:{}", "的订单调用促销预执行服务失败"), Throwables.getStackTraceAsString(ex));
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage(operateMessage));
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_40);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
