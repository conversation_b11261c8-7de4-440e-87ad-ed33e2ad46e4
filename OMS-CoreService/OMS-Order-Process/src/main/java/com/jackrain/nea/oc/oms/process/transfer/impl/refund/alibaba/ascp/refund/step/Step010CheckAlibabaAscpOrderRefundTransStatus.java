package com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.step;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefund;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefundItem;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.util.AlibabaAscpOrderCommonUtil;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;


/**
 * @Author: 黄世新
 * @Date: 2020/2/27 2:53 下午
 * @Version 1.0
 */
@Step(order = 10, description = "判断退单转换状态")
@Slf4j
@Component
public class Step010CheckAlibabaAscpOrderRefundTransStatus extends BaseAlibabaAscpOrderRefundProcessStep
        implements IOmsOrderProcessStep<IpBAlibabaAscpOrderRefundRelation> {


    @Override
    public ProcessStepResult<IpBAlibabaAscpOrderRefundRelation> startProcess(IpBAlibabaAscpOrderRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {

        if (orderInfo == null || orderInfo.getOrderRefund() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者猫超直发订单退单中间表为空；退出转换");
        }

        IpBAlibabaAscpOrderRefund orderRefund = orderInfo.getOrderRefund();
        Integer currentStatus = orderRefund.getIstrans();
        String bizOrderCode = orderRefund.getBizOrderCode();
        String forwardOrderCode = orderRefund.getForwardOrderCode();
        if (null == currentStatus) {
            AlibabaAscpOrderCommonUtil.printDebugLog(this.getClass().getName() + "bizOrderCode={},forwardOrderCode={},转换状态为{}", bizOrderCode, forwardOrderCode, currentStatus);
            orderRefundService.updateAlibabaAscpOrderRefund(TransferOrderStatus.NOT_TRANSFER.toInteger(),
                    "转换状态为空改为未转换", orderRefund);
            currentStatus = TransferOrderStatus.NOT_TRANSFER.toInteger();
        }
        //子订单号
        List<String> subOrderId = Lists.newArrayList();
        if (!orderInfo.isFullRefund()) {
            subOrderId = orderInfo.getIpBAlibabaAscpOrderRefundItems().stream().filter(item -> item.getSubOrderCode() != null)
                    .map(IpBAlibabaAscpOrderRefundItem::getSubOrderCode).collect(Collectors.toList());
        }
        //退款状态
//        Integer returnStatus = orderRefund.getReturnStatus();
//        String status = AlibabaAscpOrderRefundRefundEnum.getValueByKey(returnStatus);
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        //更新退货单的物流信息
        orderRefundService.updateRefundLogicNumber(orderInfo.getOrderRefund().getTmsOrderCode(), bizOrderCode, operateUser, orderRefund.getStoreCode());
        boolean isGoOn = false;
        if (subOrderId.isEmpty()) {
            // 假如子单号为空,根据平台单号去查找相应的退换货单/发货后退款单
            isGoOn = orderRefundService.isExistReturnRelevantInfo(orderRefund.getBizOrderCode());
        }
        //更新退款单的退款状态
//        ipTmallZfRefundService.updateRefundSlip(returnNo, status);
        //更新退货单的退款状态
//        ipTmallZfRefundService.updateReturnOrderItem(returnNo, subOrderId, status);
        //更新订单明细的退款状态
//        ipTmallZfRefundService.updateOcOrderStatusInfo(omsOrderRelation, status);
        if (TransferOrderStatus.NOT_TRANSFER.toInteger() != currentStatus || isGoOn) {
            String remark = "退换货单已经转换过！仅更新相关单据状态！";
            orderRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    remark, orderRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }
        log.info(LogUtil.format("run transfer alibabaAscpOrderRefund process 010 step success. the order NO :{}","阶段付款状态",orderInfo.getOrderNo()), orderInfo.getOrderNo());
        return new ProcessStepResult<>(StepStatus.SUCCESS, "退单转换状态校验成功,进入下一阶段!");
    }
}
