package com.jackrain.nea.oc.oms.process.transfer.impl.refund.taobao.step;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.mq.error.MqException;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * @Author: 黄世新
 * @Date: 2020/2/27 2:53 下午
 * @Version 1.0
 */
@Step(order = 10, description = "判断退单转换状态")
@Slf4j
@Component
public class Step010CheckTransStatus extends BaseTaobaoRefundProcessStep
        implements IOmsOrderProcessStep<OmsTaobaoRefundRelation> {

    private static final Integer SIXTY = 60;

    @Override
    public ProcessStepResult<OmsTaobaoRefundRelation> startProcess(OmsTaobaoRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoRefund ipBTaobaoRefund = orderInfo.getIpBTaobaoRefund();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("taoBaoRefundTrans.{},start"), ipBTaobaoRefund.getRefundId());
        }
        if (orderInfo.isTransactionClosure()) {
            // 如果退单中间表退款成功 发一个延时的消息
            if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(ipBTaobaoRefund.getStatus())) {
                if (ObjectUtil.isNull(ipBTaobaoRefund.getTransCount()) || ipBTaobaoRefund.getTransCount() <= SIXTY) {
                    sendDelayMq(ipBTaobaoRefund);
                    // 修改转换次数
                    IpBTaobaoRefund updateIpBTaobaoRefund = new IpBTaobaoRefund();
                    updateIpBTaobaoRefund.setId(ipBTaobaoRefund.getId());
                    updateIpBTaobaoRefund.setModifieddate(new Date());
                    updateIpBTaobaoRefund.setTransCount(ipBTaobaoRefund.getTransCount() == null ? 0 : ipBTaobaoRefund.getTransCount() + 1);
                    ipTaobaoRefundService.updateIpBTaobaoRefund(updateIpBTaobaoRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "订单中间表状态为交易关闭并且不存在原单并且订单为交易关闭 发送延时消息 下次转换");
                }
            }

            //判断订单中间表的的数据是否为交易关闭 判断订单是否抓换过
            String message = "订单中间表状态为交易关闭并且不存在原单,直接标记已转换";
            ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                    message, ipBTaobaoRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }

        if (ObjectUtil.isNotNull(ipBTaobaoRefund.getCreated())
                && ObjectUtil.isNotNull(ipBTaobaoRefund.getModifieddate())
                && ObjectUtil.equal(ipBTaobaoRefund.getStatus(), TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode())
                && DateUtil.between(ipBTaobaoRefund.getCreated(), ipBTaobaoRefund.getModifieddate(), DateUnit.DAY) > 30L) {
            log.info("Step010CheckStandPlatTransStatus.startProcess create too orderInfo ={}",
                    JSON.toJSONString(orderInfo));
            String remark = "更新时间较申请时间超过30天，且已退款完成，标记为已转换";
            IpBTaobaoRefund updateIpBTaobaoRefund = new IpBTaobaoRefund();
            updateIpBTaobaoRefund.setId(ipBTaobaoRefund.getId());
            updateIpBTaobaoRefund.setModifieddate(new Date());
            updateIpBTaobaoRefund.setSysremark(remark);
            updateIpBTaobaoRefund.setIstrans("2");
            ipTaobaoRefundService.updateIpBTaobaoRefund(updateIpBTaobaoRefund);
            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
        }


        //将此字段置0 方便后面赋值
        ipBTaobaoRefund.setTransFailReason(TransNodeTipEnum.DEFAULT.val());
        //更新退款单的退款状态
        String refundId = ipBTaobaoRefund.getRefundId();
        String status = ipBTaobaoRefund.getStatus();
        Long oid = ipBTaobaoRefund.getOid();
        omsRefundOrderService.updateRefundSlip(refundId, status);
        //更新退货单的退款状态
        omsRefundOrderService.updateReturnOrderItem(refundId, oid + "", status);
        //更新订单明细的退款状态
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        omsRefundOrderService.updateOcOrderStatusInfo(omsOrderRelation, status);
        String isTrans = ipBTaobaoRefund.getIstrans();
        if (!(TransferOrderStatus.NOT_TRANSFER.toInteger() + "").equals(isTrans)) {
            return new ProcessStepResult<>(StepStatus.FAILED, "退单不是未转换状态,转换失败");
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "退单转换状态校验成功,进入下一阶段!");
    }

    private void sendDelayMq(IpBTaobaoRefund ipBTaobaoRefund) {
        List<OperateOrderMqInfo> mqInfoList = new ArrayList<>();
        OperateOrderMqInfo orderMqInfo = new OperateOrderMqInfo();
        orderMqInfo.setOperateType(OperateType.TRANSFER_ORDER);
        orderMqInfo.setChannelType(ChannelType.TAOBAO);
        orderMqInfo.setOrderType(OrderType.REFUND);
        orderMqInfo.setOrderNo(ipBTaobaoRefund.getRefundId());
        mqInfoList.add(orderMqInfo);
        String jsonValue = JSONObject.toJSONString(mqInfoList);
        try {
            log.info("Step010CheckTransStatus.isTransactionClosure.sendDelayMq,ipBTaobaoRefund:{}", JSONUtil.toJsonStr(ipBTaobaoRefund));
            defaultProducerSend.sendDelayTopic(Mq5Constants.TOPIC_R3_OC_OMS_CALL_TRANSFER, Mq5Constants.TAG_OPERATEMQORDER, jsonValue, null, 60000L);
        } catch (MqException e) {
            e.printStackTrace();
        }
    }
}
