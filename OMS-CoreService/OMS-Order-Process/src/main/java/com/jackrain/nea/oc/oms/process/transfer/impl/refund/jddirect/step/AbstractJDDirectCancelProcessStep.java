package com.jackrain.nea.oc.oms.process.transfer.impl.refund.jddirect.step;

import com.jackrain.nea.oc.oms.model.relation.OmsJDDirectCancelRelation;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.refund.direct.OmsJDDirectCancelService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/26
 */
@Slf4j
public abstract class AbstractJDDirectCancelProcessStep implements IOmsOrderProcessStep<OmsJDDirectCancelRelation> {

    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    protected OmsJDDirectCancelService omsJDDirectCancelService;


    @Override
    public ProcessStepResult<OmsJDDirectCancelRelation> startProcess(
            OmsJDDirectCancelRelation orderInfo,
            ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {

        ProcessStepResult<OmsJDDirectCancelRelation> stepResult = new ProcessStepResult<>();

        return stepResult;
    }
}
