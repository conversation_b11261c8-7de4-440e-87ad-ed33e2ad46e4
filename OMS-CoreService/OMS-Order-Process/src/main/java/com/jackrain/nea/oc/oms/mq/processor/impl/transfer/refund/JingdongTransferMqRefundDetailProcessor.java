package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.ErrorLogType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongRefundRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.jingdong.JingdongTransferRefundProcessImpl;
import com.jackrain.nea.oc.oms.services.IpJingdongRefundService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Descroption 京东退货单退款单消息处理器
 * <AUTHOR>
 * @Date 2019/5/17 20:30
 */
@Slf4j
public class JingdongTransferMqRefundDetailProcessor implements IMqOrderDetailProcessor {
    @Autowired
    private IpJingdongRefundService ipJingdongRefundService;
    @Autowired
    private JingdongTransferRefundProcessImpl jingdongTransferRefundProcess;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东退货单退款单消息处理器.Start", orderNo));
        }
        IpJingdongRefundRelation ipJingdongRefundRelation = ipJingdongRefundService.getJingdongRefundRelation(orderNo);
        if (ipJingdongRefundRelation == null) {
            String errorMessage = Resources.getMessage
                    ("JingdongExchange Received OrderMqInfo Not Exist!OrderNo=" + orderNo);
            log.error(LogUtil.format(errorMessage, orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = jingdongTransferRefundProcess.start(ipJingdongRefundRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("京东退货单退款单消息处理器.Result:{}", orderNo),resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.JINGDONG
                && orderMqInfo.getOrderType() == OrderType.REFUND;
    }
}
