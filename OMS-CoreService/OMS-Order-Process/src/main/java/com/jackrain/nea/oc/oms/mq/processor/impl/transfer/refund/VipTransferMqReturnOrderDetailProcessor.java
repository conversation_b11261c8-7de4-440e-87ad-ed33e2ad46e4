package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.ErrorLogType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpVipReturnOrderRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.vip.VipTransferReturnOrderProcessImpl;
import com.jackrain.nea.oc.oms.services.IpVipReturnOrderService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date ：Created in 11:16 2020/6/19
 * description ：唯品会退供单转单
 * @ Modified By：
 */
@Slf4j
public class VipTransferMqReturnOrderDetailProcessor implements IMqOrderDetailProcessor {

    @Autowired
    private VipTransferReturnOrderProcessImpl vipTransferReturnOrderProcess;

    @Autowired
    private IpVipReturnOrderService ipVipReturnOrderService;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        String orderNo = orderMqInfo.getOrderNo();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("唯品会退供单转调拨单.Start", orderNo));
        }

        IpVipReturnOrderRelation vipReturnOrderRelation = this.ipVipReturnOrderService.selectVipReturnOrderRelation(orderNo);
        if (vipReturnOrderRelation == null || vipReturnOrderRelation.getVipReturnOrder() == null) {
            String errorMessage = Resources.getMessage("Received OrderMqInfo ReturnOrder Not Exist!OrderNo=" + orderNo);
            log.error(LogUtil.format(errorMessage, orderNo));
            return new ProcessStepResultList();
        } else {
            ProcessStepResultList resultList = vipTransferReturnOrderProcess.start(vipReturnOrderRelation,
                    false, SystemUserResource.getRootUser());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("唯品会退供单转调拨单.Result:{}", orderNo),resultList);
            }
            return resultList;
        }
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.VIPJITX
                && orderMqInfo.getOrderType() == OrderType.REFUND;
    }
}
