package com.jackrain.nea.oc.oms.process.transfer.impl.normal.alibabaAscp;

import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.MakeupOrderType;
import com.jackrain.nea.oc.oms.model.relation.IpAlibabaAscpOrderRelation;
import com.jackrain.nea.oc.oms.process.AbstractOrderProcess;
import com.jackrain.nea.oc.oms.util.LockOrderType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/9/3 下午4:32
 * @description 猫超直发转单
 **/
@Component
public class AlibabaAscpTransferOrderProcessImpl extends AbstractOrderProcess<IpAlibabaAscpOrderRelation> {

    public AlibabaAscpTransferOrderProcessImpl() {
        super();
    }

    @Override
    protected String getChildPackageName() {
        return "alibabaAscp";
    }

    @Override
    protected long getProcessOrderId(IpAlibabaAscpOrderRelation orderInfo) {
        return orderInfo.getOrderId();
    }

    @Override
    protected String getProcessOrderNo(IpAlibabaAscpOrderRelation orderInfo) {
        return orderInfo.getOrderNo();
    }

    @Override
    protected LockOrderType getCurrentLockOrderType() {
        return LockOrderType.ALIBABA_ASCP_ORDER;
    }

    @Override
    protected ChannelType getCurrentChannelType() {
        return ChannelType.ALIBABAASCP;
    }

    @Override
    protected MakeupOrderType getCurrentMakeupOrderType() {
        return MakeupOrderType.DO_NOT_MAKEUP;
    }


}
