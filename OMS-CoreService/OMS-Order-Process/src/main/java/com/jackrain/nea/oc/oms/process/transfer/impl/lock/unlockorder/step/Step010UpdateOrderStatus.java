package com.jackrain.nea.oc.oms.process.transfer.impl.lock.unlockorder.step;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OcOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpOrderLockRelation;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLock;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLockLogTypeEnum;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Descroption 更新渠道订单锁单状态
 * <AUTHOR>
 * @Date 2019/10/10 14:14
 */
@Step(order = 10, description = "更新渠道订单锁单状态")
@Slf4j
@Component
public class Step010UpdateOrderStatus extends BaseUnlockProcessStep
        implements IOmsOrderProcessStep<IpOrderLockRelation> {
    @Override
    public ProcessStepResult<IpOrderLockRelation> startProcess(IpOrderLockRelation orderInfo, ProcessStepResult preStepResult,
                                                               boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpOrderLockRelation> stepResult = new ProcessStepResult<>();
        IpBOrderLock orderLock = orderInfo.getOrderLock();
        List<OcBOrder> orderList = orderInfo.getOcBOrders();

        stepResult.setStatus(StepStatus.SUCCESS);
        StringBuilder orderLockStatusBuf = new StringBuilder();
        if (CollectionUtils.isNotEmpty(orderList)) {
            for (OcBOrder ocBOrder : orderList) {
                if (ocBOrder.getLockStatus() == null || OcOrderLockStatusEnum.UNLOCK.getKey() != ocBOrder.getLockStatus()) {
                    if ((ocBOrder.getIsInreturning() == null || ocBOrder.getIsInreturning() != 1)
                            && (ocBOrder.getIsInterecept() != null && ocBOrder.getIsInterecept() == 1)
                            && !OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus())
                            && !OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus())) {
                        //进行订单拦截
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("id", ocBOrder.getId());
                        ValueHolder vh = orderInterceptionService.cancelInterception(jsonObject, operateUser);

                        if (vh == null || ResultCode.FAIL == (int) vh.get("code")) {
                            orderInfo.setRemarks(String.format("全渠道订单[%s]取消拦截失败，解锁失败", ocBOrder.getId()));
                            stepResult.setNextStepClass(Step90UpdateOrderLockLog.class);
                            break;
                        }
                        ocBOrder = ocBOrderMapper.selectById(ocBOrder.getId());
                    }
                    //更新成已解锁
                    Boolean successFlg = ipOrderLockService.updateOrderLockStatus(orderLock, ocBOrder,
                            operateUser, OcOrderLockStatusEnum.UNLOCK.getKey());
                    if (successFlg) {
                        orderLockStatusBuf.append(String.format("[%d]已解锁", ocBOrder.getId()));
                        ipOrderLockService.insetIpOrderLockLog(OrderLockLogTypeEnum.UNLOCK.getKey(),
                                String.format("全渠道订单[%s]锁单状态更新为已解锁", ocBOrder.getId()),
                                orderLock.getId(), ocBOrder.getId(), operateUser);
                    } else {
                        orderInfo.setRemarks(String.format("全渠道订单[%s]原锁单状态更新失败，解锁失败", ocBOrder.getId()));
                        stepResult.setNextStepClass(Step90UpdateOrderLockLog.class);
                        break;
                    }
                } else {
                    orderLockStatusBuf.append(String.format("[%d]已解锁", ocBOrder.getId()));
                }
                orderInfo.setRemarks(orderLockStatusBuf.toString());
            }
        } else {
            orderInfo.setRemarks("匹配不到全渠道订单，解锁失败");
            stepResult.setNextStepClass(Step90UpdateOrderLockLog.class);
        }
        return stepResult;
    }
}
