package com.jackrain.nea.oc.oms.process.jitx.timeorder.normal.step;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 判断时效订单寻仓
 * @Date 2019-9-2
 **/
@Step(order = 40, description = "判断时效订单寻仓")
@Slf4j
@Component
public class Step041TimeOrderBranchWarehouse extends BaseVipTimeOrderProcessStep
        implements IOmsOrderProcessStep<IpVipTimeOrderRelation> {

    @Override
    public ProcessStepResult<IpVipTimeOrderRelation> startProcess(IpVipTimeOrderRelation timeOrderInfo
            , ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTimeOrderVip timeOrderVip = timeOrderInfo.getIpBTimeOrderVip();
        List<IpBTimeOrderVipOccupyItem> orderVipOccupyItemList = timeOrderInfo.getIpBTimeOrderVipOccupyItemList();
        try {
            IpBCancelTimeOrderVip cancelTimeOrderVip = timeOrderInfo.getCancelTimeOrderVip();
            if (cancelTimeOrderVip != null) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("时效订单存在取消时效订单，退出转换，订单ID：{}",
                            "时效订单存在取消时效订单",timeOrderVip.getId()), timeOrderVip.getId());
                }
                timeOrderVipMapper.updateVipOrderStatusAndTrans(cancelTimeOrderVip.getOccupiedOrderSn()
                        , Long.valueOf(operateUser.getId()), operateUser.getName(), operateUser.getEname()
                        , new Date(), "存在取消时效订单"
                        , TimeOrderVipStatusEnum.CANCELLED.getValue(), TransferOrderStatus.TRANSFERRED.toInteger());
                return new ProcessStepResult<>(StepStatus.FINISHED, "已存在取消时效订单，取消时效订单成功！");
            } else {
                ValueHolderV14 v14 =
                        vipTimeOrderOccupyItemService.vipTimeOrderBranchWarehouse(timeOrderVip,
                                orderVipOccupyItemList, operateUser);
                //分仓失败
                if (!v14.isOK()) {
                    ipVipTimeOrderService.updateIsTransError(timeOrderVip, v14.getMessage());
                    return new ProcessStepResult<>(StepStatus.FAILED, v14.getMessage());

                } else {
                    return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + timeOrderInfo.getBillNo() + "分仓成功，进入下一阶段！");
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("订单转换异常,异常信息:{}", "发货后退单转换异常"), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipVipTimeOrderService.updateIsTransError(timeOrderVip, e.getMessage());
            String errorMessage = "订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
