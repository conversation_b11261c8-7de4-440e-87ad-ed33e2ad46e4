package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.ReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.SysNotesConstant;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.refund.util.TransRefundNodeTipUtil;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2020/3/2 9:50 上午
 * @Version 1.0
 */
@Step(order = 70, description = "发货处理")
@Slf4j
@Component
public class Step070OrderStandPlatGoodsAfterReturn extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Autowired
    protected OmsRefundOrderService omsRefundOrderService;

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        try {
            //退单中间表的退单状态
            Integer returnStatus = ipBStandplatRefund.getReturnStatus();
            String status = TaobaoReturnOrderExt.RefundStandPlatStatus.getValueByKey(returnStatus);
            List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
            for (int i = 0; i < omsOrderRelation.size(); i++) {
                OmsOrderRelation omsOrderRelation1 = omsOrderRelation.get(i);
                if (!TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode().equals(omsOrderRelation1.getOrderMark())) {
                    omsOrderRelation.remove(omsOrderRelation1);
                    i--;
                }
            }
            if (CollectionUtils.isEmpty(omsOrderRelation)) {
                TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        "转换完成", ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, "转换结束");
            }
            ReturnOrderRelation relation = omsStandPlatRefundOrderService.goodsAfterOrderIsExist(ipBStandplatRefund, orderInfo.getIpBStandplatRefundItem());
            List<Long> existReturnOrder = relation.getIds();
            OcBOrder ocBOrder = omsOrderRelation.get(0).getOcBOrder();
            if (TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode().equals(status)) {
                //退款关闭
                if (CollectionUtils.isNotEmpty(existReturnOrder)) {
                    omsStandPlatRefundOrderService.refundOrderClose(existReturnOrder, omsOrderRelation, ipBStandplatRefund, operateUser);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "退款关闭,转换结束");
                } else {
                    String remark = SysNotesConstant.SYS_REMARK57;
                    TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.ORDER_NOT_FOUND);
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "未找到退换货单,转换结束");
                }
            } else if (TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode().equals(status)
                    || TaobaoReturnOrderExt.RefundStatus.SELLER_REFUSE_BUYER.getCode().equals(status)) {
                //如果平台状态为(买家已经申请退款，等待卖家同意）(卖家拒绝退款)，则不进行处理，
                String remark = SysNotesConstant.SYS_REMARK27;
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBStandplatRefund);
                return new ProcessStepResult<>(StepStatus.FINISHED, remark);
            } else if (TaobaoReturnOrderExt.RefundStatus.WAIT_BUYER_RETURN_GOODS.getCode().equals(status)
                    || TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_CONFIRM_GOODS.getCode().equals(status)
                    || TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
                List<OcBOrderItem> refundNoNeedOrderItemList = new ArrayList<>();
                if (CollectionUtils.isEmpty(existReturnOrder) || relation.isFlag()) {
                    BigDecimal refundAmount = ipBStandplatRefund.getRefundAmount();
                    omsStandPlatRefundOrderService.setAfOrderAmount(orderInfo, ipBStandplatRefund);

                    //不存在 按子订单维度生成退换货单
                    List<OcBReturnOrderRelation> orderRelations = standplatRefundOrderTransferUtil.
                            standplatRefundOrderToReturnOid(orderInfo, omsOrderRelation, ipBStandplatRefund, orderInfo.getIpBStandplatRefundItem(), TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER, operateUser, refundNoNeedOrderItemList);
                    if (CollectionUtils.isNotEmpty(orderRelations)) {
                        List<Long> refundIds = omsReturnOrderService.insertOmsReturnOrderInfo(orderRelations, operateUser);

                        List<OmsOrderRelation> isGiftOrderRelations = orderInfo.getIsGiftOrderRelation();
                        if (CollectionUtils.isNotEmpty(isGiftOrderRelations)) {
                            for (OmsOrderRelation isGiftOrderRelation : isGiftOrderRelations) {
                                if (CollectionUtils.isNotEmpty(isGiftOrderRelation.getOcBOrderItems())) {
                                    refundNoNeedOrderItemList.addAll(isGiftOrderRelation.getOcBOrderItems());
                                }
                            }
                        }
                        log.info("step070executeRedPush");
                        omsStandPlatRefundOrderService.foundRefundSlipAfter(refundIds, ocBOrder, ipBStandplatRefund, operateUser, refundNoNeedOrderItemList);
                        //赠品后发
                        omsReturnOrderService.giftsThenSend(omsOrderRelation, orderInfo.getIsGiftOrderRelation(), orderInfo.getIntermediateTableRelation(), operateUser);

                        OcBReturnOrder returnOrderInfo = orderRelations.get(0).getReturnOrderInfo();

                        boolean isPinduoduo = PlatFormEnum.PINDUODUO.getCode().equals(ocBOrder.getPlatform());

                        if (ReturnStatusEnum.COMPLETION.getVal().equals(returnOrderInfo.getReturnStatus())) {
                            if (!isPinduoduo) {
                                //发货后ag
                                standPlatAutoRefundService.executeAutoRefund(orderInfo, operateUser, null, null, false, false);
                            }
                        }
                        String remark = SysNotesConstant.SYS_REMARK29;
                        ipBStandplatRefund.setRefundAmount(refundAmount);
                        TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                remark, ipBStandplatRefund);
                        if (isPinduoduo) {
                            return new ProcessStepResult<>(StepStatus.SUCCESS);
                        }
                        return new ProcessStepResult<>(StepStatus.FINISHED, "生成退换货单成功!转换完成!");

                    } else {
                        if (CollectionUtils.isNotEmpty(refundNoNeedOrderItemList)) {
                            // 可能会存在不需要生成退换货单 但是需要生成已发货退款单的数据
                            omsRefundOrderService.foundRefundSlipAfterRefundOnly(refundNoNeedOrderItemList,
                                    omsOrderRelation.get(0).getOcBOrder(), orderInfo, SystemUserResource.getRootUser(), false);
                            String remark = "用户申请退货退款的明细，无需生成退换货单 生成已发货退款单数据完成";
                            ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                    remark, ipBStandplatRefund);
                            return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                        }
                        String remark = "申请数量大于可退数量,转换结束";
                        TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.QTY_NOT_ENOUGH);
                        ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                                remark, ipBStandplatRefund);
                        return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                    }
                } else {
                    //存在退换货单 更新
                    String companyName = ipBStandplatRefund.getCompanyName();
                    String logisticsNo = ipBStandplatRefund.getLogisticsNo();
                    omsRefundOrderService.saveExistReturnOrder(existReturnOrder, logisticsNo, companyName, status, operateUser);
                    omsStandPlatRefundOrderService.foundRefundSlipAfter(existReturnOrder, ocBOrder, ipBStandplatRefund, operateUser, null);
                    String remark = SysNotesConstant.SYS_REMARK5;
                    TransRefundNodeTipUtil.standardTransTipCAS(ipBStandplatRefund, TransNodeTipEnum.DEFAULT);
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, remark);
                }
            } else {
                String remark = SysNotesConstant.SYS_REMARK25;
                ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                        remark, ipBStandplatRefund);
            }
//            return new ProcessStepResult<>(StepStatus.FINISHED);
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("退单转换异常ReturnNo={}, 异常信息:{}", "退单转换异常"), ipBStandplatRefund.getReturnNo(), Throwables.getStackTraceAsString(e));
            //修改中间表状态及系统备注
            ipStandplatRefundService.updateRefundIsTransError(ipBStandplatRefund, e.getMessage());
            String errorMessage = "退单转换异常!" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
