package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaofx.step;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 周琳胜
 * @since : 2019-07-10
 * create at : 2019-07-10 14:50
 * 判断淘宝分销订单单据转换状态
 */
@Step(order = 10, description = "判断淘宝分销订单单据转换状态")
@Slf4j
@Component
public class Step10CheckTransferStatus extends BaseTaobaoFxOrderProcessStep implements IOmsOrderProcessStep<IpTaobaoFxOrderRelation> {


    @Override
    public ProcessStepResult<IpTaobaoFxOrderRelation> startProcess(IpTaobaoFxOrderRelation orderInfo,
                                                                   ProcessStepResult preStepResult,
                                                                   boolean isAutoMakeup, User operateUser) {
        log.debug("TaobaoFxTransferOrder.step01" + orderInfo.toString());
        if (orderInfo == null || orderInfo.getIpBTaobaoFxOrder() == null) {
            return new ProcessStepResult<>(StepStatus.FAILED, "OrderInfo为空或者Order.TaobaoFxOrder为空；退出转换");
        }
        Integer currentStatus = orderInfo.getIpBTaobaoFxOrder().getIstrans();
        if (TransferOrderStatus.NOT_TRANSFER.toInteger() == currentStatus) {
            // 若状态为未转换，则状态更新为转换中
//            IpBTaobaoFxOrder ipBTaobaoFxOrder = new IpBTaobaoFxOrder();
//            ipBTaobaoFxOrder.setId(orderInfo.getOrderId());
//            ipBTaobaoFxOrder.setIstrans(TransferOrderStatus.TRANSFERRING.toInteger());
//            ipTaobaoFxService.updateTransferStatus(ipBTaobaoFxOrder);
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "检查状态成功，进入下一阶段");
            return new ProcessStepResult<>(StepStatus.SUCCESS, operateMessage);
        } else {
            // 若状态为非未转换，则不执行逻辑
            String operateMessage = Resources.getMessage("单据" + orderInfo.getOrderId() + "状态为非未转换，不执行逻辑");
            return new ProcessStepResult<>(StepStatus.FAILED, operateMessage);
        }
    }
}