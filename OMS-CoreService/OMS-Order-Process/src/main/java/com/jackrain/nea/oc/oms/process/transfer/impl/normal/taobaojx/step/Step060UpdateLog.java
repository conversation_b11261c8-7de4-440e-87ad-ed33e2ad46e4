package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 更新Log信息
 **/
@Step(order = 60, description = "更新Log信息")
@Slf4j
@Component
public class Step060UpdateLog extends BaseTaobaoJxOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoJxOrderRelation> {
    @Override
    public ProcessStepResult<IpTaobaoJxOrderRelation> startProcess(IpTaobaoJxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.TRANSFERRED,
                "单据转换成功", orderInfo.getTaobaoJxOrder());
        return new ProcessStepResult<>(StepStatus.SUCCESS, "转单成功，退出转换！");
    }
}
