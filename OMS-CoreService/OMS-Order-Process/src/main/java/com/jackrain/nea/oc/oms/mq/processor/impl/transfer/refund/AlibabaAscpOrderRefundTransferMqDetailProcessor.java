package com.jackrain.nea.oc.oms.mq.processor.impl.transfer.refund;

import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.oc.oms.model.OperateOrderMqInfo;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.OperateType;
import com.jackrain.nea.oc.oms.model.enums.OrderType;
import com.jackrain.nea.oc.oms.model.relation.IpBAlibabaAscpOrderRefundRelation;
import com.jackrain.nea.oc.oms.mq.processor.IMqOrderDetailProcessor;
import com.jackrain.nea.oc.oms.process.MultiThreadOrderProcessor;
import com.jackrain.nea.oc.oms.process.transfer.impl.refund.alibaba.ascp.refund.AlibabaAscpOrderRefundTransferProcessImpl;
import com.jackrain.nea.oc.oms.services.IpBAlibabaAscpOrderRefundService;
import com.jackrain.nea.oc.oms.step.ProcessStepResultList;
import com.jackrain.nea.util.BllCommonUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Descroption 京东退货单退款单消息处理器
 * <AUTHOR>
 * @Date 2020/09/06 20:30
 */
@Slf4j
@Component
public class AlibabaAscpOrderRefundTransferMqDetailProcessor implements IMqOrderDetailProcessor {
    @Autowired
    private IpBAlibabaAscpOrderRefundService orderRefundService;
    @Autowired
    private AlibabaAscpOrderRefundTransferProcessImpl refundTransferProcess;
    @Autowired
    protected MultiThreadOrderProcessor threadOrderProcessor;

    @Override
    public ProcessStepResultList start(OperateOrderMqInfo orderMqInfo) {
        return getProcessStepResultList(orderMqInfo);
    }

    public ProcessStepResultList getProcessStepResultList(OperateOrderMqInfo orderMqInfo) {
        Long micTime = BllCommonUtil.getmicTime();
        long starTime = System.currentTimeMillis();
        try {
            String orderNo = orderMqInfo.getOrderNo();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("猫超直发退款消息处理器", orderNo));
            }
            String orderIds = orderMqInfo.getOrderIds();
            List<String> applyids = new ArrayList<>();
            if (StringUtils.isNotBlank(orderNo)) {
                applyids.add(orderNo);
            }
            if (StringUtils.isNotBlank(orderIds)) {
                String[] ids = orderIds.split(",");
                applyids = java.util.Arrays.asList(ids);//字符串转为list
            }
            //去重
            applyids = applyids.stream().distinct().collect(Collectors.toList());
            List<IpBAlibabaAscpOrderRefundRelation> relations = new ArrayList<>();
            for (String id : applyids) {
                IpBAlibabaAscpOrderRefundRelation ascpRefundRelation = orderRefundService.getAlibabaAscpRefundRelation(id);
                if (ascpRefundRelation != null) {
                    relations.add(ascpRefundRelation);
                } else {
                    String errorMessage = Resources.getMessage(this.getClass().getName() + " alibabaAscpOrderRefund Received OrderMqInfo Not Exist!bizOrderCode=" + id);
                    log.error(LogUtil.format(errorMessage, orderNo));
                }
            }
            threadOrderProcessor.startMultiThreadExecute(this.refundTransferProcess, relations);
            long endTime = System.currentTimeMillis();
            long Time = endTime - starTime;
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("猫超直发退款消息处理器.耗时:{}ms", orderNo), Time);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("猫超直发退款消息处理器.异常: {}"), Throwables.getStackTraceAsString(e));
        }
        return new ProcessStepResultList();
    }

    @Override
    public boolean checkMqIsCanExecute(OperateOrderMqInfo orderMqInfo) {
        return orderMqInfo.getOperateType() == OperateType.TRANSFER_ORDER
                && orderMqInfo.getChannelType() == ChannelType.ALIBABAASCP
                && orderMqInfo.getOrderType() == OrderType.REFUND;
    }
}
