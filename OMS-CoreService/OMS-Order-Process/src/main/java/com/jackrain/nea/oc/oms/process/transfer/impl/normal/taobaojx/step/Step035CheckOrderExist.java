package com.jackrain.nea.oc.oms.process.transfer.impl.normal.taobaojx.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 判断全渠道订单是否存在
 **/
@Step(order = 35, description = "判断全渠道订单是否存在")
@Slf4j
@Component
public class Step035CheckOrderExist extends BaseTaobaoJxOrderProcessStep
        implements IOmsOrderProcessStep<IpTaobaoJxOrderRelation> {
    @Override
    public ProcessStepResult<IpTaobaoJxOrderRelation> startProcess(IpTaobaoJxOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        //判断全渠道订单是否存在
        IpBTaobaoJxOrder ipBTaobaoJxOrder = orderInfo.getTaobaoJxOrder();
        try {
            List<OcBOrder> ocBOrderList = omsOrderService.selectOmsOrderInfo(ipBTaobaoJxOrder.getDealerOrderId().toString());
            if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                //判断订单是否都为已作废或取消订单
                int count = 0;
                OcBOrder vaildOcBOrder = new OcBOrder();
                for (OcBOrder ocBOrder : ocBOrderList) {
                    if (OmsOrderStatus.CANCELLED.toInteger().equals(ocBOrder.getOrderStatus())
                            || OmsOrderStatus.SYS_VOID.toInteger().equals(ocBOrder.getOrderStatus())) {
                        count++;
                    } else {
                        vaildOcBOrder = ocBOrder;
                    }
                }
                if (ocBOrderList.size() == count) {
                    //订单都已作废或者取消
                    ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.TRANSFERRED,
                            "订单已取消或已作废，退出转换！", ipBTaobaoJxOrder);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "订单已取消或已作废，退出转换！");
                } else {
                    //有效的全渠道订单
                    orderInfo.setOcBOrder(vaildOcBOrder);
                    //判断备注是否一致
                    String supplierMemo = ipBTaobaoJxOrder.getSupplierMemo();
                    if (StringUtils.isEmpty(supplierMemo)) {
                        ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.TRANSFERRED,
                                "单据转换成功", orderInfo.getTaobaoJxOrder());
                        return new ProcessStepResult<>(StepStatus.FINISHED, "转单成功，退出转换！");
                    }
                    String sellerMemo = vaildOcBOrder.getSellerMemo();
                    if (supplierMemo.equals(sellerMemo)) {
                        ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.TRANSFERRED,
                                "转换成功！", ipBTaobaoJxOrder);
                        return new ProcessStepResult<>(StepStatus.FINISHED, "卖家备注一致，退出转换！");
                    } else {
                        //更新订单的【卖家备注】更新为【经销订单中间表】中【卖家备注】
                        OcBOrder ocBOrderDto = new OcBOrder();
                        ocBOrderDto.setId(vaildOcBOrder.getId());
                        ocBOrderDto.setSellerMemo(ipBTaobaoJxOrder.getSupplierMemo());
                        boolean flag = omsOrderService.updateOrderInfo(ocBOrderDto);
                        if (!flag) {
                            ipTaobaoJxOrderService.updateIsTransError(ipBTaobaoJxOrder, "更新卖家备注信息失败，退出转换！");
                            return new ProcessStepResult<>(StepStatus.FAILED, "更新卖家备注信息失败，退出转换！");
                        }
                        ipTaobaoJxOrderService.updateIsTrans(TransferOrderStatus.TRANSFERRED,
                                "卖家备注不一致已更新", ipBTaobaoJxOrder);
                        return new ProcessStepResult<>(StepStatus.FINISHED, "卖家备注不一致已更新，退出转换！");
                    }
                }
            } else {
                return new ProcessStepResult<>(StepStatus.SUCCESS, "单据" + orderInfo.getOrderNo() + "判断全渠道订单成功，，进入下一阶段！");
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 淘宝经销订单转换异常！", e);
            //修改中间表状态及系统备注
            ipTaobaoJxOrderService.updateIsTransError(ipBTaobaoJxOrder, e.getMessage());
            String errorMessage = "淘宝经销订单转换异常！" + e.getMessage();
            return new ProcessStepResult<>(StepStatus.FAILED, errorMessage);
        }
    }
}
