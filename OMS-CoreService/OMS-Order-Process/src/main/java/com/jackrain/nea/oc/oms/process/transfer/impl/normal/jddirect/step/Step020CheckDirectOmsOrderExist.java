package com.jackrain.nea.oc.oms.process.transfer.impl.normal.jddirect.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.JingDongDirectOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongDirectOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/3/28 下午3:43
 * @Version 1.0
 */
@Slf4j
@Component
@Step(order= 20, description = "校验是否存在零售发货单")
public class Step020CheckDirectOmsOrderExist extends BaseJingdongDirectOrderProcessStep implements IOmsOrderProcessStep<IpJingdongDirectOrderRelation> {




    @Override
    public ProcessStepResult<IpJingdongDirectOrderRelation> startProcess(IpJingdongDirectOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        String orderNo = orderInfo.getOrderNo();
        IpBJingdongDirect ipBJingdongDirect = orderInfo.getIpBJingdongDirect();
        //订单撞他
        Integer orderState = ipBJingdongDirect.getOrderState();
        if (!JingDongDirectOrderStatus.isExist(orderState)) {
            //更新转换状态为2,备注：订单状态为非出库状态，标记为已转换
            String message = "订单状态为非出库状态，标记为已转换";
            ipJingdongDirectService.updateIpBJingdongDirectIstrans(orderNo, TransferOrderStatus.TRANSFERRED, message);
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }
        //若为 7新订单；10等待发货；时，判断中间表退款来源
        boolean isstate = JingDongDirectOrderStatus.NEW_ORDER.getCode().equals(orderState) || JingDongDirectOrderStatus.WAIT_SELLER_SEND_GOODS.getCode().equals(orderState);
        if (isstate) {
            Integer refundsourceflag = ipBJingdongDirect.getRefundSourceFlag();
            //当退款来源= 1售前退款；2售后退款 时，更新转换状态为2，备注：订单状态不是待发货状态，标记已转换；
            if (REFUND_1.equals(refundsourceflag) || REFUND_2.equals(refundsourceflag)) {
                String message = "订单状态不是待发货状态，标记已转换";
                ipJingdongDirectService.updateIpBJingdongDirectIstrans(orderNo, TransferOrderStatus.TRANSFERRED, message);
                return new ProcessStepResult<>(StepStatus.FINISHED, message);
            }
        }
        List<OcBOrder> ocBOrderList = omsOrderService.selectOmsOrderInfo(orderNo);
        if (CollectionUtils.isNotEmpty(ocBOrderList)) {
            List<OcBOrder> ocBOrders = ocBOrderList.stream().filter(p -> OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())
                    || OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());

            String message;
            if (ocBOrderList.size() == ocBOrders.size()){
                //订单全部删除
                message = "订单已取消或者作废";
            } else {
                message = "订单已存在";
            }
            ipJingdongDirectService.updateIpBJingdongDirectIstrans(orderNo, TransferOrderStatus.TRANSFERRED, message);
            return new ProcessStepResult<>(StepStatus.FINISHED, message);
        }
        //为查到订单 跳转下一步
        return new ProcessStepResult<>(StepStatus.SUCCESS, "未查询有效订单,进入下一步");
    }
}
