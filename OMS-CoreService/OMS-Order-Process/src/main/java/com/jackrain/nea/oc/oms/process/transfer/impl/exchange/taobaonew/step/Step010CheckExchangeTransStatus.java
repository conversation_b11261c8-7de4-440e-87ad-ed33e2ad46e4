package com.jackrain.nea.oc.oms.process.transfer.impl.exchange.taobaonew.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OmsTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: 黄世新
 * @Date: 2020/11/30 8:49 下午
 * @Version 1.0
 */
@Step(order = 10, description = "判断换货转换状态")
@Slf4j
@Component
public class Step010CheckExchangeTransStatus extends BaseTaobaoExchangeProcessStep
                      implements IOmsOrderProcessStep<OmsTaobaoExchangeRelation> {

    @Override
    public ProcessStepResult<OmsTaobaoExchangeRelation> startProcess(OmsTaobaoExchangeRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBTaobaoExchange ipBTaobaoExchange = orderInfo.getIpBTaobaoExchange();
        String isTrans = ipBTaobaoExchange.getIstrans();
        if (!(TransferOrderStatus.NOT_TRANSFER.toInteger() + "").equals(isTrans)) {
            return new ProcessStepResult<>(StepStatus.FAILED, "退单不是未转换状态,转换失败");
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS);
    }
}
