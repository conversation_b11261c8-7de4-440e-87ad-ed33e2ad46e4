package com.jackrain.nea.oc.oms.process.tobeconfirm.step;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.spiltorder.OmsOrderSpiltRuleService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.oc.oms.util.SplitMessageUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 寻源前拆单处理逻辑
 * <p>
 * 1.tob部分发货需求-处理toc订单不走仓库发货的订单，订单状态为平台发货的，更新订单明细实发数量字段为数量字段的值
 *
 * <AUTHOR>
 */
@Step(order = 145, description = "处理寻源前拆单的一些前置逻辑")
@Slf4j
public class Step145BeforeSplitService extends BaseTobeConfirmedProcessStep implements IOmsOrderProcessStep<OcBOrderRelation> {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Override
    public ProcessStepResult<OcBOrderRelation> startProcess(OcBOrderRelation orderInfo, ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        Long orderId = orderInfo.getOrderInfo().getId();
        try {
            OcBOrder order = ocBOrderMapper.selectById(orderId);
            //平台发货状态订单，不管tob/toc,更新实发数量=数量
            if (OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(order.getOrderStatus())) {
                //更新实发数量为数量字段值
                orderItemMapper.updateRealNumSourceQtyByOrderIds(Lists.newArrayList(orderId));
            }
            return new ProcessStepResult<>(StepStatus.SUCCESS);
        } catch (Exception e) {
            log.error(LogUtil.format("平台发货实发数量更新失败:{}", orderId, "平台发货订单实发数量更新"), Throwables.getStackTraceAsString(e));
            OcBOrder errorOrderInfo = new OcBOrder();
            errorOrderInfo.setId(orderInfo.getOrderId());
            errorOrderInfo.setSysremark(SplitMessageUtil.splitMesssage("平台发货实发数量更新失败"));
            errorOrderInfo.setOccupyStatus(OrderOccupyStatus.STATUS_50);
            omsOrderService.updateOrderInfo(errorOrderInfo);
            omsToBeConfirmedTaskService.updateToBeConfirmedTaskByOrderId(orderInfo.getOrderId());
            return new ProcessStepResult<>(StepStatus.FAILED, "平台发货实发数量更新失败");
        }
    }
}
