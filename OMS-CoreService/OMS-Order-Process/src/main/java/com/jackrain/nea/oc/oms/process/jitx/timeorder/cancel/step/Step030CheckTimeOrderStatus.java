package com.jackrain.nea.oc.oms.process.jitx.timeorder.cancel.step;

import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.IpBCancelTimeOrderVipRelation;
import com.jackrain.nea.oc.oms.model.relation.IpVipTimeOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderCancelService;
import com.jackrain.nea.oc.oms.services.IpVipTimeOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 判断时效订单状态
 *
 * @author: chenxiulou
 * @since: 2019-06-25
 * create at : 2019-06-25 19:00
 */
@Step(order = 30, description = "判断时效订单状态")
@Slf4j
@Component
public class Step030CheckTimeOrderStatus extends BaseVipTimeOrderCancelProcessStep
        implements IOmsOrderProcessStep<IpBCancelTimeOrderVipRelation> {
    @Autowired
    private IpBTimeOrderVipMapper timeOrderMapper;

    @Autowired
    private IpVipTimeOrderCancelService vipTimeOrderCanselService;

    @Autowired
    private IpVipTimeOrderService ipVipTimeOrderService;

    @Override
    public ProcessStepResult<IpBCancelTimeOrderVipRelation> startProcess(IpBCancelTimeOrderVipRelation cancelTimeOrderInfo,
                                                                         ProcessStepResult preStepResult,
                                                                         boolean isAutoMakeup, User operateUser) {
        ProcessStepResult<IpBCancelTimeOrderVipRelation> stepResult = new ProcessStepResult<>();
        String orderNo = cancelTimeOrderInfo.getOrderNo();
        //查询对应的时效订单
        IpVipTimeOrderRelation orderInfo = this.ipVipTimeOrderService.selectTimeOrder(orderNo);
        if (orderInfo == null || orderInfo.getIpBTimeOrderVip() == null) {
            String operateMessage = "未找到对应的时效订单，等待下次转换！";
            /**更新备注信息*/
            vipTimeOrderCanselService.updateCancelTimeOrderData(cancelTimeOrderInfo.getCancelTimeOrderVip(),null,null,operateMessage,false,operateUser);
            stepResult.setMessage(operateMessage);
            stepResult.setStatus(StepStatus.FINISHED);
            return stepResult;
            //stepResult.setNextStepClass(Step50UpdateTimeOrderTransferStatus.class);
        } else {
            IpBTimeOrderVip timeOrderVip = orderInfo.getIpBTimeOrderVip();
            String pickNo = timeOrderVip.getPickNo();
            if (pickNo == null || pickNo.equals("1")) {
                Integer orderStatus = timeOrderVip.getStatus();
                /**订单状态为：占单中或者寻仓中 -》跳过当前转换*/
                if(TimeOrderVipStatusEnum.IN_SINGLE.getValue().equals(orderStatus) || TimeOrderVipStatusEnum.IN_SEEKING_STORE.getValue().equals(orderStatus)){
                    String operateMessage = "当前时效订单正在占单中，等待下次转换！";
                    /**更新备注信息*/
                    vipTimeOrderCanselService.updateCancelTimeOrderData(cancelTimeOrderInfo.getCancelTimeOrderVip(),null,null,operateMessage,false,operateUser);
                    stepResult.setMessage(operateMessage);
                    stepResult.setStatus(StepStatus.FINISHED);
                    return stepResult;
                }

                /**单状态为：取消、已完成-》完成当前转换，*/
                if(TimeOrderVipStatusEnum.CANCELLED.getValue().equals(orderStatus) || TimeOrderVipStatusEnum.MATCHED.getValue().equals(orderStatus)){
                    String operateMessage = "转换完成！";
                    stepResult.setMessage(operateMessage);
                    stepResult.setStatus(StepStatus.SUCCESS);
                    stepResult.setNextStepClass(Step50UpdateTimeOrderTransferStatus.class);
                    return stepResult;
                }

                /**单状态为：待占单、占单成功、缺货、寻仓成功 -》执行下一步*/
                stepResult.setStatus(StepStatus.SUCCESS);
                stepResult.setMessage("订单状态=" + orderStatus + "" + ";单据编号=" + orderNo + "，进入下一阶段");
                return stepResult;
            } else {
                String operateMessage = "关联的时效订单已被拣货单关联，标记为已转换";
                stepResult.setMessage(operateMessage);
                this.vipTimeOrderCanselService.updateTimeOrderTransStatus(orderNo,
                        TransferOrderStatus.TRANSFERRED, operateMessage);
                stepResult.setStatus(StepStatus.FINISHED);
                return stepResult;
                //stepResult.setNextStepClass(Step50UpdateTimeOrderTransferStatus.class);
            }
        }
    }

}
