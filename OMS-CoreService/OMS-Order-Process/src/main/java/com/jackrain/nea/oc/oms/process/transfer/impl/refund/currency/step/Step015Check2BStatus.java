package com.jackrain.nea.oc.oms.process.transfer.impl.refund.currency.step;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.annotation.Step;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStandPlatRefundRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.services.OmsBusinessTypeDistinguishService;
import com.jackrain.nea.oc.oms.services.OmsStandPlatRefundOrderService;
import com.jackrain.nea.oc.oms.step.IOmsOrderProcessStep;
import com.jackrain.nea.oc.oms.step.ProcessStepResult;
import com.jackrain.nea.oc.oms.step.StepStatus;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-08-08 17:40
 * @Description : 2B逆向 - 逻辑处理
 **/
@Step(order = 15, description = "判断是否为2B逆向")
@Slf4j
@Component
public class Step015Check2BStatus extends BaseStanPlatRefundProcessStep
        implements IOmsOrderProcessStep<OmsStandPlatRefundRelation> {

    @Autowired
    private OmsBusinessTypeDistinguishService omsBusinessTypeDistinguishService;
    @Autowired
    private OmsStandPlatRefundOrderService omsStandPlatRefundOrderService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Override
    public ProcessStepResult<OmsStandPlatRefundRelation> startProcess(OmsStandPlatRefundRelation orderInfo,
                                                                      ProcessStepResult preStepResult, boolean isAutoMakeup, User operateUser) {
        IpBStandplatRefund ipBStandplatRefund = orderInfo.getIpBStandplatRefund();
        List<IpBStandplatRefundItem> ipBStandplatRefundItem = orderInfo.getIpBStandplatRefundItem();
        //获取订单信息
        List<OmsOrderRelation> omsOrderRelation = orderInfo.getOmsOrderRelation();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Step015Check2BStatus.startProcess.orderInfo={};",
                    "Step015Check2BStatus"),JSON.toJSONString(orderInfo));
        }
        Integer platmform = standplatRefundOrderTransferUtil.getStandplatRefundPlatmform(ipBStandplatRefund);

        if (PlatFormEnum.SAP.getCode().equals(platmform) || PlatFormEnum.DMS.getCode().equals(platmform)) {
            try {
                List<OcBOrderItem> orderItemList = standplatRefundOrderTransferUtil.buildOrderItems(ipBStandplatRefundItem);
                OmsBusinessTypeDistinguishService.BusinessTypeResult businessTypeResult = omsBusinessTypeDistinguishService.querySapBusinessType(ipBStandplatRefund.getBusinessTypeCode(), platmform, ipBStandplatRefund.getReserveVarchar05(), orderItemList, operateUser);
                if (businessTypeResult == null) {
                    String remarks = "匹配SAP退单业务类型失败！";
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remarks, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FAILED, remarks);
                }
                String businessTypeCode = businessTypeResult.getCode();
                Integer isCheckSource = businessTypeResult.getCheckSource();
                log.debug(LogUtil.format("Step015Check2BStatus.businessTypeCode={},checkSource={};",
                        "Step015Check2BStatus"),businessTypeCode,isCheckSource);
                //OcOmsReturnOrderConstant.BUSINESS_TYPE_CODE_RYTH11.equals(businessTypeCode) || OcOmsReturnOrderConstant.BUSINESS_TYPE_CODE_RYTH12.equals(businessTypeCode)
                if(isCheckSource == null || isCheckSource.intValue() == 0){
                    //不校验原单直接生成退换货单
                    OcBReturnOrderRelation ocBReturnOrderRelation = new OcBReturnOrderRelation();
                    //判断是否已存在退换货单,存在则更新
                    List<Long> existReturnOrder = omsStandPlatRefundOrderService.isExistReturnOrderRefundByReturnId(ipBStandplatRefund.getReturnNo());
                    if(CollectionUtils.isNotEmpty(existReturnOrder)){
                        List<OcBReturnOrder> ocBReturnOrders = ocBReturnOrderMapper.selectReturnOrderListByOrderIds(existReturnOrder);
                        List<OcBReturnOrderRefund> orderRefunds = ocBReturnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(existReturnOrder);
                        omsStandPlatRefundOrderService.updateOcBReturnOrder(ipBStandplatRefund, ocBReturnOrders, orderRefunds);
                    }else{
                        //直接转单生成退换货单
                        ocBReturnOrderRelation = standplatRefundOrderTransferUtil.buildRefundOrderInfo(omsOrderRelation,ipBStandplatRefund,ipBStandplatRefundItem,operateUser);
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("构建退货单主表数据:{}", "Step015Check2BStatus"), JSONObject.toJSONString(ocBReturnOrderRelation));
                        }
                        ocBReturnOrderRelation.getReturnOrderInfo().setBusinessTypeId(businessTypeResult.getId());
                        ocBReturnOrderRelation.getReturnOrderInfo().setBusinessTypeCode(businessTypeResult.getCode());
                        ocBReturnOrderRelation.getReturnOrderInfo().setBusinessTypeName(businessTypeResult.getName());
                        //保存
                        standplatRefundOrderTransferUtil.insertOmsReturnOrderInfo(ocBReturnOrderRelation,operateUser);
                    }
                    String remark = "SAP线下销售退货转换完成";
                    ipStandplatRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(),
                            remark, ipBStandplatRefund);
                    return new ProcessStepResult<>(StepStatus.FINISHED, "转换完成！");
                }
            }catch (Exception e){
                log.error(LogUtil.format("Step015Check2BStatus.ocBReturnOrderRelation.error={};",
                        "Step015Check2BStatus"), Throwables.getStackTraceAsString(e));
                ipStandplatRefundService.updateRefundIsTransError(ipBStandplatRefund, e.getMessage());
                return new ProcessStepResult<>(StepStatus.FAILED, "转换失败！");
            }
        }
        return new ProcessStepResult<>(StepStatus.SUCCESS, "判断原单状态成功,进入下一阶段");
    }
}
