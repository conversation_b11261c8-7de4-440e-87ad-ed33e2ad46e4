<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志文件 输入位置 -->
    <property name="log_dir" value="./app/log"/>
    <!-- 配置日志清理时间，日志最大的历史3天 -->
    <property name="maxHistory" value="3"/>
    <!-- 配置日志文件限制 -->
    <property name="totalSizeCap" value="1GB"/>
    <!-- 设置单个日志文件的大小限制 -->
    <property name="maxFileSize" value="300MB"/>
    <property name="PATTERN"
        value="[%date{yyyy-MM-dd HH:mm:ss.SSS}] [%thread] [%-5level] [%logger{15}#%line] %msg%n"/>

    <!-- ConsoleAppender 控制台输出日志 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- Only log level WARN and above -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
    </appender>

    <!-- INFO级别日志 -->
    <appender name="INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log_dir}/info.log</file>
        <!-- 输出INFO级别及以上的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log_dir}/%d{yyyy-MM-dd}/info.%i.log</fileNamePattern>
            <maxHistory>${maxHistory}</maxHistory>
            <maxFileSize>${maxFileSize}</maxFileSize>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <root level="DEBUG">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="INFO"/>
    </root>

    <springProfile name="dev, test, prod">
        <logger name="jackrain.nea.web.common.ObjectSingle" level="OFF"/>
        <logger name="org.apache.dubbo" level="INFO"/>
        <logger name="com.alibaba.dubbo" level="WARN"/>
        <logger name="org.springframework" level="WARN"/>
        <logger name="com.jackrain.nea.vp.job" level="ERROR"/>
        <logger name="com.github.ltsopensource" level="ERROR"/>
        <logger name="RocketmqCommon" level="ERROR"/>
        <logger name="RocketmqRemoting" level="ERROR"/>
        <logger name="RocketmqClient" level="ERROR"/>
        <logger name="com.jackrain" level="DEBUG"/>
        <logger name="com.burgeon" level="DEBUG"/>
    </springProfile>

</configuration>
