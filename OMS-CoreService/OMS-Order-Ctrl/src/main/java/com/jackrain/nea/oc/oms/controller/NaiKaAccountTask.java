//package com.jackrain.nea.oc.oms.controller;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.alibaba.dubbo.config.annotation.Reference;
//import com.alibaba.fastjson.JSONObject;
//import com.google.common.base.Throwables;
//import com.jackrain.nea.cpext.model.table.CpCPlatform;
//import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
//import com.jackrain.nea.hub.request.naika.NaiKaChangeAmountRequest;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
//import com.jackrain.nea.oc.oms.model.enums.naika.AccountToNaiKaEnum;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
//import com.jackrain.nea.rpc.CpRpcService;
//import com.jackrain.nea.sys.domain.ValueHolderV14;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.Data;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.Callable;
//import java.util.concurrent.Future;
//
///**
// * @ClassName NaiKaAccountTask
// * @Description 奶卡对账
// * <AUTHOR>
// * @Date 2022/9/7 20:46
// * @Version 1.0
// */
//@Component
//@Slf4j
//public class NaiKaAccountTask {
//
//    @Autowired
//    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private CpRpcService cpRpcService;
//    @Autowired
//    private ThreadPoolTaskExecutor naikaAccountThreadPoolExecutor;
//    @Reference(group = "hub", version = "1.0")
//    private NaiKaOrderCmd naiKaOrderCmd;
//
//    public void execute(JSONObject params) {
//        try {
//            long start = System.currentTimeMillis();
//            final String taskTableName = "oc_b_order_naika";
//            Map<String, String> topMap = null;
//            Set<String> nodes = topMap.keySet();
//            if (CollectionUtils.isEmpty(nodes)) {
//                return;
//            }
//            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
//
//            //线程执行结果获取
//            for (Future<Boolean> futureResult : results) {
//                try {
//                    if (log.isDebugEnabled()) {
//                        log.debug(LogUtil.format("UnFreezeNaiKaOrderTask------>线程结果:{}"), futureResult.get().toString());
//                    }
//                } catch (Exception e) {
//                    log.error(LogUtil.format("UnFreezeNaiKaOrderTask多线程获取InterruptedException异常：{}", "UnFreezeNaiKaOrderTask"), Throwables.getStackTraceAsString(e));
//                }
//            }
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("UnFreezeNaiKaOrderTask 解冻奶卡任务完成 useTime : {}"), (System.currentTimeMillis() - start));
//            }
//        } catch (Exception e) {
//            log.error(LogUtil.format("解冻奶卡任务执行失败,异常！{}", "UnFreezeNaiKaOrderTask"), Throwables.getStackTraceAsString(e));
//        }
//        return;
//    }
//
//    @Data
//    public class CallableNaiKaAccountTaskWithResult implements Callable<Boolean> {
//
//        private String nodeName;
//
//        private String taskTableName;
//
//        public CallableNaiKaAccountTaskWithResult(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//            Integer limit = 100;
//            List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectAccountToNaiKa(nodeName, limit, taskTableName, 6);
//            if (CollectionUtils.isEmpty(ocBOrderNaiKaList)) {
//                return true;
//            }
//            for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKaList) {
//                OcBOrderNaiKa updateOcBOrderNaiKa = new OcBOrderNaiKa();
//                String errorMsg = "";
//                try {
//                    updateOcBOrderNaiKa.setOcBOrderId(ocBOrderNaiKa.getOcBOrderId());
//                    updateOcBOrderNaiKa.setId(ocBOrderNaiKa.getId());
//                    updateOcBOrderNaiKa.setModifieddate(new Date());
//                    OcBOrder naiKaOrder = ocBOrderMapper.get4NaiKaOrder(ocBOrderNaiKa.getOcBOrderId());
//                    Integer platFormId = naiKaOrder.getPlatform();
//                    CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(platFormId));
//                    // 取出金额 准备同步给奶卡系统
//                    NaiKaChangeAmountRequest request = new NaiKaChangeAmountRequest();
//                    request.setCardNumber(ocBOrderNaiKa.getCardCode());
//                    request.setPlatformCode(cpCPlatform.getEcode());
//                    request.setReconciliationAmount(ocBOrderNaiKa.getWipeAmt());
//                    request.setShopCode(naiKaOrder.getCpCShopEcode());
//                    request.setReconciliationTime(new Date());
//
//                    ValueHolderV14 valueHolderV14 = naiKaOrderCmd.changeAmount(request);
//                    if (valueHolderV14.isOK()) {
//                        // 更新奶卡状态
//                        updateOcBOrderNaiKa.setAccountToNaika(AccountToNaiKaEnum.SUCCESS.getStatus());
//                    } else {
//                        errorMsg = valueHolderV14.getMessage();
//                        updateOcBOrderNaiKa.setAccountToNaika(AccountToNaiKaEnum.FAIL.getStatus());
//                        updateOcBOrderNaiKa.setAccountToNaikaTimes(ocBOrderNaiKa.getAccountToNaikaTimes() + 1);
//                    }
//                } catch (Exception e) {
//                    errorMsg = e.getMessage();
//                    updateOcBOrderNaiKa.setAccountToNaika(AccountToNaiKaEnum.FAIL.getStatus());
//                    updateOcBOrderNaiKa.setAccountToNaikaTimes(ocBOrderNaiKa.getAccountToNaikaTimes() + 1);
//                }
//                if (ObjectUtil.isNotEmpty(errorMsg) && errorMsg.length() > 255) {
//                    errorMsg = errorMsg.substring(0, 254);
//                }
//                updateOcBOrderNaiKa.setAccountErrorMsg(errorMsg);
//                ocBOrderNaiKaMapper.updateById(updateOcBOrderNaiKa);
//            }
//            return true;
//        }
//    }
//}
