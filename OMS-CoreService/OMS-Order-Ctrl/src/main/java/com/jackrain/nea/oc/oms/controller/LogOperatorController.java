package com.jackrain.nea.oc.oms.controller;

import com.jackrain.nea.log.model.LogTransactionInfo;
import com.jackrain.nea.log.util.LogOperateUtil;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: 易邵峰
 * @since: 2019-03-16
 * create at : 2019-03-16 10:57
 */
@RestController
@RequestMapping("/p")
public class LogOperatorController {


    /**
     * 查找日志内容
     *
     * @param keyValue  订单关键字内容
     * @param startDate 开始时间。yyyyMMdd格式
     * @param endDate   结束时间。yyyyMMdd格式
     * @return 日志内容
     */
    @RequestMapping("/selectLog")
    public List<LogTransactionInfo> selectLogTransactionList(@RequestParam("keyValue") String keyValue,
                                                             @RequestParam("startDate") String startDate,
                                                             @RequestParam("endDate") String endDate) {
        try {
            Date startDateTime = FastDateFormat.getInstance("yyyyMMdd").parse(startDate);
            Date endDateTime = FastDateFormat.getInstance("yyyyMMdd").parse(endDate);
            return LogOperateUtil.getInstance().selectLogTransactionList(keyValue, startDateTime, endDateTime);
        } catch (Exception ex) {
            ex.printStackTrace();
            return new ArrayList<>();
        }
    }
}
