package com.jackrain.nea.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: 易邵峰
 * @since: 2019-04-22
 * create at : 2019-04-22 20:24
 */
@RestController
@RequestMapping("/p")
public class DynamicConfigController {

    @Autowired
    private PropertiesConf propertiesConf;

    /**
     * 查找配置信息
     *
     * @param configName 配置名称
     * @return 配置信息
     */
    @RequestMapping("/selectConfig")
    public String selectConfig(@RequestParam("configName") String configName) {
        return propertiesConf.getProperty(configName);
    }

}
