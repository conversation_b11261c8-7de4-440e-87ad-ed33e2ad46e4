//package com.jackrain.nea.oc.oms.controller;
//
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryFailMapper;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderDeliveryFail;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.List;
//import java.util.concurrent.Callable;
//import java.util.stream.Collectors;
//
///**
// * @ClassName OrderDeliveryFailRetryTask
// * @Description TODO
// * <AUTHOR>
// * @Date 2023/4/6 15:17
// * @Version 1.0
// */
//@Slf4j
//@Component
//public class OrderDeliveryFailRetryTask {
//
//    @Autowired
//    private OcBOrderDeliveryFailMapper deliveryFailMapper;
//    @Resource
//    private ThreadPoolTaskExecutor doDeliveryFailRetryPollExecutor;
//
//    public void execute(JSONObject params) {
//
////        if (log.isDebugEnabled()) {
////            log.debug(LogUtil.format("OrderDeliveryFailRetryTask execute start"));
////        }
////        long start = System.currentTimeMillis();
////        final String taskTableName = "oc_b_order_delivery_fail";
////        Set<String> nodes = topMap.keySet();
////        if (CollectionUtils.isEmpty(nodes)) {
////            return;
////        }
////        List<Future<Boolean>> results = new ArrayList<>();
////        for (String nodeName : nodes) {
////            results.add(doDeliveryFailRetryPollExecutor.submit(new RetryDeliveryTaskWithResult(nodeName, topMap.get(nodeName))));
////        }
////        //线程执行结果获取
////        for (Future<Boolean> futureResult : results) {
////            try {
////                if (log.isDebugEnabled()) {
////                    log.debug(LogUtil.format("OrderDeliveryFailRetryTask------>线程结果:{}"), futureResult.get().toString());
////                }
////            } catch (Exception e) {
////                log.error(LogUtil.format("OrderDeliveryFailRetryTask多线程获取InterruptedException异常：{}", "OrderDeliveryFailRetryTask"), Throwables.getStackTraceAsString(e));
////            }
////        }
////        if (log.isDebugEnabled()) {
////            log.debug(LogUtil.format("OrderDeliveryFailRetryTask 发货失败重试 useTime : {}"), (System.currentTimeMillis() - start));
////        }
////        return;
//    }
//
//    class RetryDeliveryTaskWithResult implements Callable<Boolean> {
//
//        private final String nodeName;
//        private final String taskTableName;
//
//        public RetryDeliveryTaskWithResult(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//            List<OcBOrderDeliveryFail> ocBOrderDeliveryFailList = deliveryFailMapper.selectDeliveryFail4Retry(nodeName, 100, taskTableName);
//            if (CollectionUtils.isEmpty(ocBOrderDeliveryFailList)) {
//                return true;
//            }
//            List<Long> orderIds = ocBOrderDeliveryFailList.stream().map(OcBOrderDeliveryFail::getOcBOrderId).collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(orderIds)) {
//                return true;
//            }
////            try {
////                // 直接发送ID，不封装对象
////                // topic -- 定时任务共享的topic
////                String topic = propertiesConf.getProperty("oms.oc.order.autotask.mq.topic");
////                // tag -- 平台发货tag
////                String tag = propertiesConf.getProperty("oms.oc.order.autotask.platformDelivery.mq.tag");
////
////                if (Objects.nonNull(topic) && Objects.nonNull(tag)) {
////                    sendHelper.sendMessage(JSON.toJSONString(orderIds), topic, tag);
////                } else {
////                    log.error("platform.delivery.task.topicOrTagIsNull:{}/{}/{}", topic, tag, orderIds);
////                }
////            } catch (Exception e) {
////                e.printStackTrace();
////                log.error(LogUtil.format("platform.delivery.fail.retry.task.sendMqError", "平台发货补偿定时任务sendMqError"), Throwables.getStackTraceAsString(e));
////                return false;
////            }
//            // 批量更新状态为成功
//            deliveryFailMapper.updateStatus(orderIds);
//            return true;
//        }
//    }
//}
