package com.jackrain.nea.oc.oms.controller;

import com.jackrain.nea.oc.oms.services.patrol.ClearRedisCacheService;
import com.jackrain.nea.oc.oms.services.patrol.RetryAgRefundService;
import com.jackrain.nea.oc.oms.services.patrol.WithdrawZtWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: 黄世新
 * @Date: 2019/11/12 4:14 下午
 * @Version 1.0
 */

@RestController
@RequestMapping("/p")
@Slf4j
public class RetryAgRefundController {

    @Autowired
    private RetryAgRefundService retryAgRefundService;
    @Autowired
    private ClearRedisCacheService clearRedisCacheService;

    @Autowired
    private WithdrawZtWarehouseService withdrawZtWarehouseService;

    @RequestMapping("/retryAgRefund")
    public String retryAgRefund(@RequestParam("pageSize") Integer pageSize) {
        if (pageSize == null) {
            pageSize = 200;
        }
        ValueHolderV14 valueHolderV14 = retryAgRefundService.selectAgRefundFail(pageSize);
        if (valueHolderV14 != null) {
            return valueHolderV14.toJSONObject().toString();
        }
        return "重试失败";
    }

    @RequestMapping("/clearRedisCache")
    public String clearRedisCache(String redisKey, String type, String env) {
        ValueHolderV14 valueHolderV141 = clearRedisCacheService.clearRedisCache(redisKey, type, env);
        if (valueHolderV141 != null) {
            return valueHolderV141.toJSONObject().toString();
        }
        return "清除失败";
    }


    @RequestMapping("/clearWarehouse")
    public String clearWarehouse(@RequestParam("pageSize") Integer pageSize) {
        ValueHolderV14 valueHolderV141 = withdrawZtWarehouseService.withdrawZtWarehouse(pageSize);
        if (valueHolderV141 != null) {
            return valueHolderV141.toJSONObject().toString();
        }
        return "清除失败";
    }

}
