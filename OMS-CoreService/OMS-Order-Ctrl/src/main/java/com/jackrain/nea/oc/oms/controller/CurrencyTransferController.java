package com.jackrain.nea.oc.oms.controller;

import com.jackrain.nea.oc.oms.api.CurrencyTransferOrderCmd;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.request.TransferOrderRequest;
import com.jackrain.nea.oc.oms.model.result.TransferOrderResult;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 夏继超
 * 通用商品退單 轉單
 */
@RestController
@RequestMapping("/p")
public class CurrencyTransferController {

    @Reference(group = "oc-core", version = "1.0")
    private CurrencyTransferOrderCmd currencyTransferOrderCmd;

    /**
     * 转换单据内容
     *
     * @param orderNo 转换单据号
     * @return 转换单据结果
     */
    @RequestMapping("/currencyTransfer")
    public ValueHolderV14<TransferOrderResult> startCurrencyTransfer(@RequestParam("orderNo") String orderNo) {
        TransferOrderRequest orderRequest = new TransferOrderRequest();
        orderRequest.setChannelType(ChannelType.STANDPLAT);
        orderRequest.setOperateUser(SystemUserResource.getRootUser());
        List<String> orderNoList = new ArrayList<>();
        orderNoList.add(orderNo);
        orderRequest.setOrderNoList(orderNoList);
        return currencyTransferOrderCmd.startTransferOrder(orderRequest);
    }
}
