//package com.jackrain.nea.oc.oms.controller;
//
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.json.JSONUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderNaikaVoidMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
//import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
//import com.jackrain.nea.oc.oms.model.enums.CardAutoVoidEnum;
//import com.jackrain.nea.oc.oms.model.enums.NaikaVoidStatusEnum;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
//import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
//import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
//import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
//import com.jackrain.nea.util.OperateUserUtils;
//import com.jackrain.nea.utility.LogUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.util.Date;
//import java.util.List;
//import java.util.concurrent.Callable;
//
///**
// * @ClassName CardAutoVoidStatusTask
// * @Description 奶卡自动作废状态维护
// * <AUTHOR>
// * @Date 2023/3/2 14:19
// * @Version 1.0
// */
//@Component
//@Slf4j
//public class CardAutoVoidStatusTask {
//
//    @Resource
//    private ThreadPoolTaskExecutor cardAutoVoidPollExecutor;
//
//    @Autowired
//    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
//    @Autowired
//    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;
//    @Autowired
//    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
//    @Autowired
//    private OcBOrderItemMapper ocBOrderItemMapper;
//    @Autowired
//    private OcBOrderNaikaVoidMapper naikaVoidMapper;
//
//    @Autowired
//    private BuildSequenceUtil sequenceUtil;
//
//
//    public void execute(JSONObject params) {
//
//    }
//
//    private void insertOcBOrderNaikaVoid(OcBReturnAfSend ocBReturnAfSend, OcBReturnAfSendItem ocBReturnAfSendItem, OcBOrderItem ocBOrderItem) {
////        if (ObjectUtil.equal(ocBReturnAfSend.getCpCPlatformId(), Long.valueOf(PlatFormEnum.DOU_YIN.getCode()))) {
//        OcBOrderNaikaVoid ocBOrderNaikaVoid = new OcBOrderNaikaVoid();
//        ocBOrderNaikaVoid.setOcBOrderId(ocBReturnAfSendItem.getRelationBillId());
//        ocBOrderNaikaVoid.setOcBOrderItemId(ocBReturnAfSendItem.getRelationBillItemId());
//        ocBOrderNaikaVoid.setTid(ocBOrderItem.getTid());
//        ocBOrderNaikaVoid.setVoidStatus(NaikaVoidStatusEnum.VOID.getStatus());
//        ocBOrderNaikaVoid.setVoidTimes(0);
//        ocBOrderNaikaVoid.setPlatformId(ocBReturnAfSend.getCpCPlatformId());
//        ocBOrderNaikaVoid.setId(sequenceUtil.buildCardCodeVoidSequenceId());
//        ocBOrderNaikaVoid.setOcBReturnAfSendId(ocBReturnAfSend.getId());
//        OperateUserUtils.saveOperator(ocBOrderNaikaVoid, null);
//        // insert
//        naikaVoidMapper.insert(ocBOrderNaikaVoid);
////        }
//
//    }
//
//    class CardAutoVoidTaskWithResult implements Callable<Boolean> {
//
//        private final String nodeName;
//
//        private final String taskTableName;
//
//        public CardAutoVoidTaskWithResult(String nodeName, String taskTableName) {
//            this.nodeName = nodeName;
//            this.taskTableName = taskTableName;
//        }
//
//        @Override
//        public Boolean call() throws Exception {
//            List<OcBReturnAfSend> ocBReturnAfSendList = ocBReturnAfSendMapper.selectCardAutoVoid(nodeName, 100, taskTableName);
//            if (log.isDebugEnabled()) {
//                log.debug(LogUtil.format("CardAutoVoidStatusTask execute start ocBReturnAfSendList:{}", JSONUtil.toJsonStr(ocBReturnAfSendList)));
//            }
//            if (CollectionUtils.isEmpty(ocBReturnAfSendList)) {
//                return true;
//            }
//            for (OcBReturnAfSend ocBReturnAfSend : ocBReturnAfSendList) {
//                List<OcBReturnAfSendItem> ocBReturnAfSendItemList = ocBReturnAfSendItemMapper.selectByOcBReturnAfSendIdListBySendId(ocBReturnAfSend.getId());
//                OcBReturnAfSend updateOcBReturnAfSend = new OcBReturnAfSend();
//                updateOcBReturnAfSend.setId(ocBReturnAfSend.getId());
//                String sysMark = ocBReturnAfSend.getSysremark() == null ? "" : ocBReturnAfSend.getSysremark();
//                for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItemList) {
//                    boolean naikaVoid = false;
//                    if (ObjectUtil.equal(ocBReturnAfSendItem.getAmtActual(), ocBReturnAfSendItem.getAmtReturn())) {
//                        // 根据订单id+sku信息查询出来奶卡表 奶卡的数量
//                        // 根据已发货退款单明细中关联的订单明细id中的数量
//                        List<OcBOrderNaiKa> ocBOrderNaiKaList =
//                                ocBOrderNaiKaMapper.selectIdByOcBOrderIdAndSku(ocBReturnAfSendItem.getRelationBillId(), ocBReturnAfSendItem.getPsCSkuEcode());
//                        OcBOrderItem ocBOrderItem =
//                                ocBOrderItemMapper.queryOrderItemByIdPro(ocBReturnAfSendItem.getRelationBillItemId(), ocBReturnAfSendItem.getRelationBillId());
//                        if (CollectionUtils.isNotEmpty(ocBOrderNaiKaList)) {
//                            if (ObjectUtil.equal(new BigDecimal(ocBOrderNaiKaList.size()), ocBOrderItem.getQty())) {
//                                naikaVoid = true;
//                                // 往自动作废表里写入数据(目前有赞/抖音/天猫/京东 将这几个渠道的数据写入)
//                                insertOcBOrderNaikaVoid(ocBReturnAfSend, ocBReturnAfSendItem, ocBOrderItem);
//                                updateOcBReturnAfSend.setCardAutoVoid(CardAutoVoidEnum.TO_DO_VOID.getCode());
//                            } else {
//                                sysMark = sysMark + "卡号数量与退款数量不一致，不触发自动作废";
//                            }
//                        }
//                    } else {
//                        sysMark = sysMark + "退款金额与成交金额不一致，不触发自动作废！";
//                    }
//                }
//                updateOcBReturnAfSend.setSysremark(sysMark);
//                updateOcBReturnAfSend.setModifieddate(new Date());
//                ocBReturnAfSendMapper.updateById(updateOcBReturnAfSend);
//            }
//            return true;
//        }
//    }
//}
