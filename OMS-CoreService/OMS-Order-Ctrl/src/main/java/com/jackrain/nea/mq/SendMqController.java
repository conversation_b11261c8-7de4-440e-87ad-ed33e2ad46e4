package com.jackrain.nea.mq;

import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: 易邵峰
 * @since: 2019-04-22
 * create at : 2019-04-22 13:32
 */
@RestController
@RequestMapping("/p")
public class SendMqController {

//    @Autowired
//    private R3MqSendHelper sendMqHelper;

    @Autowired
    private DefaultProducerSend defaultProducerSend;

    /**
     * 查找日志内容
     *
     * @param body       消息内容体
     * @param topic      消息Topic
     * @param tag        消息Tag
     * @param msgKey     消息Message Key内容
     * @param configName 发送消息Config配置名称
     * @return 日志内容
     */
    @RequestMapping("/sendMq")
    public String sendMq(@RequestParam("body") String body,
                         @RequestParam("topic") String topic,
                         @RequestParam("tag") String tag,
                         @RequestParam(value = "msgKey", required = false) String msgKey,
                         @RequestParam(value = "configName", defaultValue = "default") String configName)
             {

//        return sendMqHelper.sendMessage(configName, body, topic, tag, msgKey);
        // TODO className 待补充
                 MqSendResult result = defaultProducerSend.sendTopic(topic, tag, body, msgKey);
                 return result.getMessageId();
             }
}
