package com.jackrain.nea.redis;

import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.RuntimeCompute;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

/**
 * Redis Controller测试
 *
 * @author: 易邵峰
 * @since: 2019-04-19
 * create at : 2019-04-19 10:04
 */
@RestController
@RequestMapping("/p")
public class RedisController {

    private static final int OMS_ORDER_REDIS_TIMEOUT = 24 * 60 * 60 * 1000;

    /**
     * 获取指定商品序号值
     *
     * @param tableName 表名称
     * @return 序号值
     */
    @RequestMapping("/selectSequence")
    public String selectProd(@RequestParam("tableName") String tableName) {
        RuntimeCompute runtimeCompute = new RuntimeCompute();
        runtimeCompute.startRuntime();
        Long seq = ModelUtil.getSequence(tableName);
        double usedTime = runtimeCompute.endRuntime();
        return "Seq=" + seq + ";UsedTime=" + usedTime;
    }

    @RequestMapping("/setRedis")
    public String setRedis(@RequestParam(value = "key", required = false) String redisKey) {
        if (StringUtils.isEmpty(redisKey)) {
            redisKey = "TestYYYYY";
        }
        CusRedisTemplate<String, Long> redisOpsUtil = RedisOpsUtil.getStrRedisTemplate();
        redisOpsUtil.opsForValue().set(redisKey, 12345678L, OMS_ORDER_REDIS_TIMEOUT,
                TimeUnit.MILLISECONDS);
        return "ok";

    }

    @RequestMapping("/getRedis")
    public Long getRedis(@RequestParam(value = "key", required = false) String redisKey) {
        if (StringUtils.isEmpty(redisKey)) {
            redisKey = "TestYYYYY";
        }
        CusRedisTemplate<String, Long> redisOpsUtil = RedisOpsUtil.getStrRedisTemplate();
        return redisOpsUtil.opsForValue().get(redisKey);

    }

}
