package com.jackrain.nea.oc.oms.controller;

import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: heliu
 * @since: 2019/7/2
 * create at : 2019/7/2 13:11
 */
@RestController
@RequestMapping("/p")
@Slf4j
public class OrderOperatorController {

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OmsOrderService omsOrderService;


    /**
     * 测试调试接口
     *
     * @param orderId
     * @return
     */
    @RequestMapping("/cs/orderOperator")
    public ValueHolderV14 orderOperator(@RequestParam("orderId") String orderId) {

        return new ValueHolderV14();
    }
}