package com.jackrain.nea.oc.oms.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName NaiKaTestController
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/8/27 14:39
 * @Version 1.0
 */
@RestController
@RequestMapping("/p")
@Slf4j
public class NaiKaTestController {
    @Autowired
    private ReturnInToNaiKaTask returnInToNaiKaTask;
    @Autowired
    private AutoStandplatRefundTask autoStandplatRefundTask;


    @RequestMapping("/naika/returnin")
    public void testReturnIn() {
        returnInToNaiKaTask.execute(null);
    }
    @RequestMapping("/standplat/refund")
    public void testRefund() {
        autoStandplatRefundTask.execute(null);
    }
}
