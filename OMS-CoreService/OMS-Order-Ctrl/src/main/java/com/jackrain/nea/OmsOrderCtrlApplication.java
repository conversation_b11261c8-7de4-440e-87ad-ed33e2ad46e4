package com.jackrain.nea;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.burgeon.mq.annotation.EnableMq;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * @author: 易邵峰
 * @since: 2019-03-30
 * create at : 2019-03-30 20:45
 */
@SpringBootApplication
@Slf4j
@EnableAsync
@EnableDubbo(scanBasePackages = {"com.burgeon.r3", "com.jackrain.nea"})
@EnableSpringUtil
@EnableMq
public class OmsOrderCtrlApplication extends SpringBootServletInitializer {
    private static final Class<OmsOrderCtrlApplication> applicationClass = OmsOrderCtrlApplication.class;

    static {
        System.setProperty("dubbo.application.logger", "slf4j");
        System.setProperty("dubbo.config-center.namespace", "public");
        System.setProperty("dubbo.config-center.group", "r3-oms");
        System.setProperty("dubbo.config-center.config-file", "dubbo");
    }

    /**
     * 运行程序
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        // 若将devtools.enabled设置为true，会导致无法加载Dubbo
        System.setProperty("spring.devtools.restart.enabled", "false");
        ApplicationContext context = SpringApplication.run(applicationClass, args);
        System.out.println("Start SprintBoot Success ContextId=" + context.getId());
        String appServer = System.getProperty("app.id");
        System.out.println("Start Up app " + appServer);
        MDC.put("SERVER_NAME", appServer);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(applicationClass);
    }
}
