package com.jackrain.nea.thread;

import com.jackrain.nea.util.ApplicationContextHandle;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.scheduling.annotation.Async;

import java.util.Date;

/**
 * @author: 易邵峰
 * @since: 2019-04-22
 * create at : 2019-04-22 21:11
 */
public class TestAsyncService {

    public static TestAsyncService instance;

    public TestAsyncService() {

    }

    public static TestAsyncService getInstance() {
        if (instance == null) {
            instance = ApplicationContextHandle.getApplicationContext().getAutowireCapableBeanFactory().createBean(TestAsyncService.class);
        }
        return instance;
    }

    @Async
    public void start() {
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss:SSS").format(new Date()) + " --- Async");
    }


}
