FROM --platform=linux/amd64 registry.cn-hangzhou.aliyuncs.com/ryytn-r3-uat/openjdk:8u351

RUN echo "start add jar"
ADD OMS-CoreService/OMS-Order-Ctrl/target/r3-oc-oms-core-ctrl-3.0.0-SNAPSHOT.jar /acs/user/src/
RUN echo "end add jar"

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo 'Asia/Shanghai' >/etc/timezone

EXPOSE 8080
ENV APP_NAME=r3-oc-oms-core-ctrl-3.0.0-SNAPSHOT

ENV JAVA_OPTS=""
ENTRYPOINT [ "sh", "-c", "java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar /acs/user/src/${APP_NAME}.jar" ]