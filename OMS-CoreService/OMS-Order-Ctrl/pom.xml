<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.burgeon.r3</groupId>
        <artifactId>r3-oc-oms</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>r3-oc-oms-core-ctrl</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <dependencies>
        <!-- r3.project -->
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-core-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-bll</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>r3-mq</artifactId>
                    <groupId>com.burgeon.r3</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- r3.framework -->
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-model-query</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.burgeon.r3</groupId>-->
<!--            <artifactId>r3-mq</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.burgeon.mq</groupId>
            <artifactId>mq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-service-util</artifactId>
        </dependency>

        <!-- other -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>