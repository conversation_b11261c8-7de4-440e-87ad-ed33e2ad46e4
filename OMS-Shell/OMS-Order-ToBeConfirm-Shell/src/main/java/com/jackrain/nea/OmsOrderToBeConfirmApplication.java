package com.jackrain.nea;

import cn.hutool.extra.spring.EnableSpringUtil;
import com.burgeon.mq.annotation.EnableMq;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.slf4j.MDC;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * OMS Order 后台处理程序
 *
 * @author: 易邵峰
 * @since: 2019-01-17
 * create at : 2019-01-7 14:36
 */
@SpringBootApplication()
@Slf4j
@EnableAspectJAutoProxy
@EnableAsync
@EnableDubbo(scanBasePackages = {"com.burgeon.r3", "com.jackrain.nea"})
@EnableSpringUtil
@EnableMq
public class OmsOrderToBeConfirmApplication extends SpringBootServletInitializer {
    private static final Class<OmsOrderToBeConfirmApplication> applicationClass = OmsOrderToBeConfirmApplication.class;

    static {
        System.setProperty("dubbo.application.logger", "slf4j");
        System.setProperty("dubbo.config-center.namespace","public");
        System.setProperty("dubbo.config-center.group","r3-oms");
        System.setProperty("dubbo.config-center.config-file","dubbo");
    }

    /**
     * 运行程序
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        // 若将devtools.enabled设置为true，会导致无法加载Dubbo
        System.setProperty("spring.devtools.restart.enabled", "false");
        ApplicationContext context = SpringApplication.run(applicationClass, args);
        MDC.put("SERVER_NAME", context.getId());
        System.out.println(" /$$$$$$$   /$$$$$$           /$$$$$$  /$$      /$$  /$$$$$$ \n" +
                           "| $$__  $$ /$$__  $$         /$$__  $$| $$$    /$$$ /$$__  $$\n" +
                           "| $$  \\ $$|__/  \\ $$        | $$  \\ $$| $$$$  /$$$$| $$  \\__/\n" +
                           "| $$$$$$$/   /$$$$$/ /$$$$$$| $$  | $$| $$ $$/$$ $$|  $$$$$$ \n" +
                           "| $$__  $$  |___  $$|______/| $$  | $$| $$  $$$| $$ \\____  $$\n" +
                           "| $$  \\ $$ /$$  \\ $$        | $$  | $$| $$\\  $ | $$ /$$  \\ $$\n" +
                           "| $$  | $$|  $$$$$$/        |  $$$$$$/| $$ \\/  | $$|  $$$$$$/\n" +
                           "|__/  |__/ \\______/          \\______/ |__/     |__/ \\______/ ");

    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(applicationClass);
    }
}
