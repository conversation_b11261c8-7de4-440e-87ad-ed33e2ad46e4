<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.burgeon.r3</groupId>
        <artifactId>r3-oc-oms</artifactId>
        <version>3.0.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>r3-oc-oms-tobeconfirm-shell</artifactId>
    <version>3.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <dependencies>
<!--        <dependency>-->
<!--            <groupId>com.burgeon.r3</groupId>-->
<!--            <artifactId>r3-mq</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.burgeon.mq</groupId>
            <artifactId>mq-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-core-process</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-task</artifactId>
        </dependency>
        <dependency>
            <groupId>com.burgeon.r3</groupId>
            <artifactId>r3-oc-oms-core-srv</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <!-- 指定ognl版本用于AD单号解析-->
        <dependency>
            <groupId>ognl</groupId>
            <artifactId>ognl</artifactId>
        </dependency>
        <!--tlog-->
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>tlog-dubbo-spring-boot-starter</artifactId>
            <version>1.5.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>tlog-web-spring-boot-starter</artifactId>
            <version>1.5.2-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>