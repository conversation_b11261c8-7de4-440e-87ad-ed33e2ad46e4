package com.jackrain.nea.ip.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ip.model.CancelGoodsModel;
import com.jackrain.nea.ip.model.jingdong.RefundapplyUpdateWarehouseStatusRequest;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongSaRefund;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.st.service.OmsOrderStCAutocheckService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: 黄世新
 * @Date: 2019/5/9 11:00 AM
 * @Version 1.0
 */
@Slf4j
@Component
public class IpOrderCancelToAgService {

    @Autowired
    OmsOrderStCAutocheckService omsOrderStCAutocheckService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    /**
     * @param ocBOrder
     * @param taobaoRefund
     * @param user
     */
    public boolean orderCancelToAg(OcBOrder ocBOrder, IpBTaobaoRefund taobaoRefund, User user) {
        try {
            CancelGoodsModel model = new CancelGoodsModel();
            model.setOperateUser(user);
            model.setOid(taobaoRefund.getOid());
            model.setRefundId(NumberUtils.toLong(taobaoRefund.getRefundId()));
            model.setOperateTime(new Date());
            model.setRefundFee(NumberUtils.toLong(
                    taobaoRefund.getRefundFee().multiply(new BigDecimal(100)).toString()));
            model.setStatus("SUCCESS");
            model.setTid(NumberUtils.toLong(ocBOrder.getTid()));
            Long cpCShopId = ocBOrder.getCpCShopId();
            CpShop cpShop = cpRpcService.selectShopById(cpCShopId);
            String shopSecretKey = cpShop.getShopSecretKey();
            //匹配出sessionkey
            String regex = "sessionkey:(.+)";
            Matcher m = Pattern.compile(regex).matcher(shopSecretKey);
            if (m.find()) {
                String sessionkey = m.group(1);
                model.setSessionKey(sessionkey);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("IpOrderCancelToAgService.orderCancelToAg 订单传AG取消发货传入参数:{}", "orderCancelToAg", model.getRefundId()), model);
            }
            return ipRpcService.cancelGoodsToAg(model);
        } catch (Exception e) {
            log.error(LogUtil.format("订单传AG取消发货失败:{}", "orderCancelToAg",
                    taobaoRefund.getRefundId(), ocBOrder.getBillNo()), Throwables.getStackTraceAsString(e));
        }
        return false;
    }


    /**
     * @param
     * @param taobaoRefund
     * @param user
     */
    public ValueHolderV14 orderCancelToAgRetry(IpBTaobaoRefund taobaoRefund, User user) {
        try {
            CancelGoodsModel model = new CancelGoodsModel();
            model.setOperateUser(user);
            model.setOid(taobaoRefund.getOid());
            model.setRefundId(NumberUtils.toLong(taobaoRefund.getRefundId()));
            model.setOperateTime(new Date());
            model.setRefundFee(NumberUtils.toLong(
                    taobaoRefund.getRefundFee().multiply(new BigDecimal(100)).toString()));
            model.setStatus("SUCCESS");
            model.setTid(taobaoRefund.getTid());
            Long cpCShopId = taobaoRefund.getCpCShopId();
            CpShop cpShop = cpRpcService.selectShopById(cpCShopId);
            String shopSecretKey = cpShop.getShopSecretKey();
            //匹配出sessionkey
            String regex = "sessionkey:(.+)";
            Matcher m = Pattern.compile(regex).matcher(shopSecretKey);
            if (m.find()) {
                String sessionkey = m.group(1);
                model.setSessionKey(sessionkey);
            }
            log.info(LogUtil.format("IpOrderCancelToAgService.orderCancelToAg 订单传AG取消发货传入参数:{}",
                    "orderCancelToAgRetry", model.getRefundId()), model);
            return ipRpcService.cancelGoodsToAgRetry(model);
        } catch (Exception e) {
            log.error(LogUtil.format("订单传AG取消发货失败:{}", "orderCancelToAg",
                    taobaoRefund.getRefundId()), Throwables.getStackTraceAsString(e));
        }
        return null;
    }

    /**
     * 京东取消订单回传出库接口
     *
     * @param saRefund
     * @param status
     */
    public ValueHolderV14 jdCancelUpdateWarehouseStatus(Long shopid, IpBJingdongSaRefund saRefund, Integer status) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东取消订单更新出库状态接口:{}",
                    "京东取消订单回传出库", "jdCancelUpdateWarehouseStatus"), JSONObject.toJSONString(saRefund));
        }
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "");
        try {
            boolean f = omsOrderStCAutocheckService.isToAgByShopStrategy(shopid);
            if (!f) {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("京东取消订单回传出库状态,店铺id:{}, 未开启启用AG,f{}",
                            "京东取消订单回传出库", "jdCancelUpdateWarehouseStatus"), shopid, f);
                }
                v14.setCode(ResultCode.FAIL);
                v14.setMessage(" 京东取消订单回传出库状态,店铺id" + shopid + "未开启启用AG");
                return v14;
            }
            RefundapplyUpdateWarehouseStatusRequest statusRequest = new RefundapplyUpdateWarehouseStatusRequest();
            statusRequest.setOrderId(Long.valueOf(saRefund.getOrderid()));
            statusRequest.setRefId(saRefund.getPopafsrefundapplyid());
            statusRequest.setStatus(status);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("京东取消订单更新出库状态接口入参:{}", "京东取消订单回传出库",
                        "jdCancelUpdateWarehouseStatus"), JSONObject.toJSONString(statusRequest));
            }
            v14 = ipRpcService.updateWarehouseStatus(statusRequest, saRefund.getSellerNick());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("京东取消订单更新出库状态接口出参:{}", "京东取消订单回传出库",
                        "jdCancelUpdateWarehouseStatus"), v14.toString());
            }
        } catch (Exception e) {
            log.debug(LogUtil.format("京东取消订单更新出库状态接口异常:{}", "京东取消订单回传出库",
                    "jdCancelUpdateWarehouseStatus"), Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("京东取消订单更新出库状态接口异常:" + e.getMessage());
        }
        return v14;
    }

    /**
     * 京东取消订单回传出库接口
     *
     * @param saRefund
     * @return
     */
    public ValueHolderV14 jdOrderCancelToSa(IpBJingdongSaRefund saRefund) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("京东取消订单更新出库状态接口:{}",
                    "jdOrderCancelToSa", saRefund.getOrderid()), JSONObject.toJSONString(saRefund));
        }
        ValueHolderV14 v14 = new ValueHolderV14(ResultCode.SUCCESS, "");

        if (saRefund.getReturnstatus() == null || !saRefund.getReturnstatus().equals(2)) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("京东取消订单回传出库状态不是回传失败！", "jdOrderCancelToSa",
                        saRefund.getOrderid()));
            }
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(" 京东取消订单回传出库状态不是回传失败！");
            return v14;
        }

        //    List<Long> ids = ES4Order.getIdsBySourceCode(saRefund.getOrderid());
        List<Long> ids = GSI4Order.getIdListBySourceCode(saRefund.getOrderid());

        //查询订单
        if (CollectionUtils.isEmpty(ids)) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("京东取消订单回传出库状态不是回传失败！",
                        "jdOrderCancelToSa", saRefund.getOrderid()));
            }
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(" 京东取消订单回传出库状态ES查询全渠道订单为空,平台单号=" + saRefund.getOrderid());
            return v14;
        }
        List<OcBOrder> orderList = ocBOrderMapper.selectByIdsList(ids);
        Long shopid = orderList.get(0).getCpCShopId();
        if (null == shopid) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("京东取消订单回传出库状态订单店铺为空,订单id{}",
                        "jdOrderCancelToSa", saRefund.getOrderid()), saRefund.getOrderid());
            }
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(" 京东取消订单回传出库状态订单店铺为空,订单id=" + orderList.get(0).getId());
            return v14;
        }
        return jdCancelUpdateWarehouseStatus(shopid, saRefund, saRefund.getStatus());
    }
}
