package com.jackrain.nea.ip.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4IpJingDongOrder;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.IpBJdOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsWmsCancelStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.table.IpBJdOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.MarkRefundService;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.services.OcBOrderTheAuditService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 京东取消订单转单服务
 *
 * @author: 孙继东
 * @since: 2019-04-22
 * create at : 2019-04-22 9:34
 */
@Component
@Slf4j
public class JdCancelOrderTransferService {

    @Autowired
    private IpBJdOrderMapper jdOrderMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private MarkRefundService markRefundService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;

    /**
     * 京东取消订单转单
     *
     * @return
     */
    public void cancelOrderTransfer() {

        //转换状态=0 且“交易状态”为TRADE_CANCELED或LOCKED
        JSONArray array = new JSONArray();
        array.add("TRADE_CANCELED");
        array.add("LOCKED");

        JSONArray data = ES4IpJingDongOrder
                .findIdByTransAndOrderState(TransferOrderStatus.NOT_TRANSFER.toInteger(), array);

        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        List<Long> ipIds = data.stream().map(a -> ((JSONObject) a).getLong("ID")).collect(Collectors.toList());
        List<IpBJdOrder> orders = jdOrderMapper.selectBatchIds(ipIds);
        if (CollectionUtils.isNotEmpty(orders)) {
            orders.stream().forEach(order -> {
                //4.根据【京东订单中间表】的平台单号查找在【全渠道订单】中是否存在订单
                //   JSONArray datas = ES4Order.queryIdBySourceCode(order.getOrderId());;
                List<Long> orderIds = GSI4Order.getIdListBySourceCode(String.valueOf(order.getOrderId()));
                if (CollectionUtils.isEmpty(orderIds)) {
                    //1)不存在，则更新京东订单接口，转换状态：已转换（2），转换时间,系统备注：无有效的全渠道订单，订单退款转换失败
                    order.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
                    order.setTransdate(new Date());
                    order.setSysremark("无有效的全渠道订单，订单退款转换失败");
                    updateIpAndPushES(order);
                } else {
                    //    List<Long> orderIds = datas.stream().map(a -> ((JSONObject) a).getLong("ID")).collect(Collectors.toList());
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("JdCancelOrderTransferService.cancelOrderTransfer.订单id集合:{}",
                                "cancelOrderTransfer"), orderIds.toString());
                    }
                    existDeal(order, orderIds);
                }
            });
        }
    }


    /**
     * 原始订单存在的的处理方法
     *
     * @param order    京东订单中间表
     * @param orderIds 全渠道订单表
     */
    private void existDeal(IpBJdOrder order, List<Long> orderIds) {
        orderIds = orderIds.stream().filter(id -> {
            Integer orderStatus = ocBOrderMapper.selectById(id).getOrderStatus();
            return !(orderStatus.equals(OmsOrderStatus.CANCELLED.toInteger()) || orderStatus.equals(OmsOrderStatus.SYS_VOID.toInteger()));
        }).collect(Collectors.toList());
        int newSize = orderIds.size();
        if (newSize == 0) {
            //2)存在，但订单状态都为已取消或系统作废，则更新京东订单接口数据，“转换状态”：已转换（2），转换时间，系统备注：所有原单已取消或系统作废，订单退款转换失败
            order.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
            order.setTransdate(new Date());
            order.setSysremark("所有原单已取消或系统作废，订单退款转换失败");
            updateIpAndPushES(order);
        } else {
            //3)存在，剔除已取消或系统作废的订单做如下处理
            //仓库发货、平台发货、交易完成、物流已送达的订单数量
            int count1 = 0;
            //待审核、缺货、配货中的订单数量和订单集合
            int count3 = 0;
            List<Long> list = new ArrayList<>();
            for (int i = newSize - 1; i >= 0; i--) {
                Long ocBOrderId = orderIds.get(i);
                OcBOrder ocBOrder = ocBOrderMapper.selectOne(new QueryWrapper<OcBOrder>().select("order_status").eq("id", ocBOrderId));
                Integer orderStatus = ocBOrder.getOrderStatus();
                if (orderStatus.equals(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DEAL_DONE.toInteger())
                        || orderStatus.equals(OmsOrderStatus.DELIVERED.toInteger())) {
                    count1++;
                }
                if (orderStatus.equals(OmsOrderStatus.TO_BE_ASSIGNED.toInteger()) || orderStatus.equals(OmsOrderStatus.PENDING_WMS.toInteger())) {
                    //有待分配、传wms中
                    //更新京东订单接口：转换状态为未转换（0），转换时间，系统备注：订单状态为待分配或传WMS中，待订单处理成功后再进行订单退款转换
                    order.setIstrans(TransferOrderStatus.NOT_TRANSFER.toInteger());
                    order.setTransdate(new Date());
                    order.setSysremark("订单状态为待分配或传WMS中，待订单处理成功后再进行订单退款转换");
                    updateIpAndPushES(order);
                    //转换失败，程序结束
                    return;
                }
                if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())
                        || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())
                        || orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger())
                        || orderStatus.equals(OmsOrderStatus.CHECKED.toInteger())) {
                    list.add(ocBOrderId);
                    count3++;
                }
            }

            if (count1 == newSize) {
                //更新京东订单接口：转换状态为已转换（2），转换时间,系统备注：原订单已出库，需生成退货单，订单退款转换失败
                order.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
                order.setTransdate(new Date());
                order.setSysremark("原订单已出库，需生成退货单，订单退款转换失败");
                updateIpAndPushES(order);
                //转换成功，程序结束
                return;
            }
            if (count3 > 0) {
                //判断对应京东订单接口中单据的交易状态
                String orderState = order.getOrderState();
                if ("LOCKED".equalsIgnoreCase(orderState)) {
                    locked(order, list);
                }

                if ("TRADE_CANCELED".equalsIgnoreCase(orderState)) {
                    tradeCanceled(order, list);
                }
            }
        }
    }

    /**
     * 交易状态为LOCKED的处理方法
     *
     * @param order    接口平台订单表
     * @param orderIds 全渠道订单集
     */
    public void locked(IpBJdOrder order, List<Long> orderIds) {
        Long ipOrderId = order.getOrderId();
        for (int i = 0; i < orderIds.size(); i++) {
            Long orderId = orderIds.get(i);
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                    Integer orderStatus = ocBOrder.getOrderStatus();
                    Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                    //订单拦截，更新主表：是否已经拦截：是，修改人,修改时间，插入订单日志
                    interceptAndLog(ocBOrder);
                    User operateUser = SystemUserResource.getRootUser();
                    if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                        boolean flag = this.toExamineOrder(ocBOrder, SystemUserResource.getRootUser());
                        if (flag) {
                            orderStatus = ocBOrder.getOrderStatus();
                            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核成功", null, null, operateUser);
                        } else {
                            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核失败", null, null, operateUser);
                            return;
                        }
                    }
                    //订单状态”为配货中且”WMS撤回状态”不是已撤回，则调用WMS撤回服务
                    if (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger()) && !wmsCancelStatus.equals(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger())) {
                        //调用作废出库通知单并从wms撤回接口
                        boolean flag = this.toExamineOrder(ocBOrder, SystemUserResource.getRootUser());
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("JdCancelOrderTransferService.locked. 调用反审核是否成功:{}", ocBOrder.getBillNo()), flag);
                        }
                        if (flag) {
                            wmsCancelStatus = OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger();
                        }
                    }
                    if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger()) || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())
                            || (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger()) && (wmsCancelStatus.equals(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger())))) {
                        //待审核、缺货或者（配货中且WMS撤回状态为已撤回）
                        //更新订单明细的退款状态,根据平台单号将明细表中同平台单号的明细退款状态修改为1
                        updateItem(ipOrderId, orderId);
                        //订单主表更新,是否退款中”：退款中
                        OcBOrder updateOrder = new OcBOrder();
                        updateOrder.setId(orderId);
                        updateOrder.setIsInreturning(InreturningStatus.INRETURNING);
                        updateDeal(order, updateOrder);
                    }
                } else {
                    order.setIstrans(TransferOrderStatus.NOT_TRANSFER.toInteger());
                    order.setTransdate(new Date());
                    order.setSysremark("对应的全渠道订单其他人正在在操作，稍后将重试");
                    updateIpAndPushES(order);
                    throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!"));
                }
            } catch (InterruptedException ex) {
                log.error(LogUtil.format("京东取消订单转单服务:{}", orderId, "京东取消订单转单"), Throwables.getStackTraceAsString(ex));
                Thread.currentThread().interrupt();
            } finally {
                redisLock.unlock();
            }
        }
    }


    /**
     * 交易状态为TRADE_CANCELED的处理方法
     *
     * @param jdOrder  接口平台订单表
     * @param orderIds 全渠道订单集
     */
    public void tradeCanceled(IpBJdOrder jdOrder, List<Long> orderIds) {
        for (int i = 0; i < orderIds.size(); i++) {
            Long orderId = orderIds.get(i);
            User operateUser = SystemUserResource.getRootUser();
            try {
                OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                //先拦截订单，在对订单进行状态判断处理，更新主表：是否已经拦截：是，修改人,修改时间，插入订单日志
                interceptAndLog(ocBOrder);
                Integer orderStatus = ocBOrder.getOrderStatus();
                Integer wmsCancelStatus = ocBOrder.getWmsCancelStatus();
                if (OmsOrderStatus.CHECKED.toInteger().equals(orderStatus)) {
                    boolean flag = this.toExamineOrder(ocBOrder, SystemUserResource.getRootUser());
                    if (flag) {
                        orderStatus = ocBOrder.getOrderStatus();
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核成功", null, null, operateUser);
                    } else {
                        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_INTERCEPTION.getKey(), "买家申请退款,反审核失败", null, null, operateUser);
                        return;
                    }
                }
                //1)配货中且WMS撤回状态不是已撤回，再调用WMS撤回服务
                if (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger()) && !(wmsCancelStatus.equals(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()))) {
                    //调用作废出库通知单并从wms撤回接口
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("调用作废出库通知单并从wms撤回,入参为:{}",
                                "tradeCanceled", ocBOrder.getBillNo(), ocBOrder.getId()), JSON.toJSONString(ocBOrder));
                    }
                    this.lockUpBackExamine(ocBOrder, SystemUserResource.getRootUser());

                    //调用成功，根据状态判断是否为合订
                    wmsCancelStatus = OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger();
                }
                //若为合单
                if (ocBOrder.getIsMerge().equals(1)) {
                    //配货中且WMS撤回状态为已撤回
                    if (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger()) && (wmsCancelStatus.equals(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger()))) {
                        //根据来源单号查询订单中同来源单号明细，执行标记为退款完成服务
                        markRefund(jdOrder, ocBOrder);
                        //京东订单接口数据更新，转换状态：已转换（2）
                        jdOrder.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
                        jdOrder.setTransdate(new Date());
                        updateIpAndPushES(jdOrder);
                        //待审核、缺货
                    } else if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())
                            || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())) {
                        //执行标记为退款完成服务
                        markRefund(jdOrder, ocBOrder);
                        //更新主表数据,是否已经拦截”：0、“是否退款中”：0。更新订单接口,转换状态：已转换（2）、转换时间：当前时间
                        //防止脏数据
                        OcBOrder updateOrder = new OcBOrder();
                        updateOrder.setId(orderId);
                        updateOrder.setIsInterecept(OmsOrderIsInterceptEnum.NO.getVal());//是否已经拦截 使用HOLD单方法修改
                        updateOrder.setIsInreturning(InreturningStatus.INRETURN_NO);
                        updateDeal(jdOrder, updateOrder);
                    }
                    //非合单
                } else if (ocBOrder.getIsMerge().equals(0)) {
                    if (orderStatus.equals(OmsOrderStatus.UNCONFIRMED.toInteger())
                            || orderStatus.equals(OmsOrderStatus.BE_OUT_OF_STOCK.toInteger())
                            || (orderStatus.equals(OmsOrderStatus.IN_DISTRIBUTION.toInteger()) && (wmsCancelStatus.equals(OmsWmsCancelStatus.OMS_WMS_CANCEL_STATUS_YES.toInteger())))) {
                        //标记为退款完成服务
                        markRefund(jdOrder, ocBOrder);
                        //更新订单主表，京东订单接口数据更新
                        //防止脏数据
                        OcBOrder updateOrder = new OcBOrder();
                        updateOrder.setId(orderId);
                        updateOrder.setIsInterecept(OmsOrderIsInterceptEnum.NO.getVal());//是否已经拦截 使用HOLD单方法修改
                        updateOrder.setIsInreturning(InreturningStatus.INRETURN_NO);
                        updateDeal(jdOrder, updateOrder);
                    }
                }
            } catch (Exception ex) {
                log.error(LogUtil.format("状态为取消操作异常:{}", "tradeCanceled", orderId), Throwables.getStackTraceAsString(ex));
            }
        }
    }


    /**
     * 更新订单明细并推送ES
     *
     * @param ipOrderId 接口平台单号
     * @param orderId   订单
     */
    private void updateItem(Long ipOrderId, Long orderId) {
        ocBOrderItemMapper.updateRefundStatusByTidAndOrderId(OmsOrderRefundStatus.WAITSELLERAGREE.toInteger(), ipOrderId, orderId);
    }

    /**
     * 调用执行标记退款服务
     *
     * @param order    接口平台
     * @param ocBOrder 全渠道订单
     */
    private void markRefund(IpBJdOrder order, OcBOrder ocBOrder) {
        List<Long> itemIds = ocBOrderItemMapper.queryIdByTidAndOrderId(order.getOrderId().toString(), ocBOrder.getId());
        if (CollectionUtils.isEmpty(itemIds)) {
            order.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
            order.setTransdate(new Date());
            order.setSysremark("转换失败，全渠道订单没有明细");
            updateIpAndPushES(order);
        } else {
            String join = StringUtils.join(itemIds, ",");
            JSONObject ids = new JSONObject();
            ids.put("IDS", join);
            try {
                ValueHolderV14 valueHolderV14 = markRefundService.markRefund(ids, SystemUserResource.getRootUser());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("调用执行标记退款服务:{}", "markRefund"), valueHolderV14);
                }
            } catch (Exception e) {
                log.debug(LogUtil.format("标记退款完成服务：{}", "markRefund"), Throwables.getStackTraceAsString(e));
            }
        }
    }

    /**
     * 更新全渠道主表和接口平台中间表
     *
     * @param order    接口平台中间表
     * @param ocBOrder 全渠道订单
     */
    private void updateDeal(IpBJdOrder order, OcBOrder ocBOrder) {
        // 订单hold 或释放hold单
        ocBOrderHoldService.holdOrUnHoldOrder(ocBOrder, OrderHoldReasonEnum.REFUND_HOLD);
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setId(ocBOrder.getId());
        updateOrder.setIsInreturning(ocBOrder.getIsInreturning());
        updateOrder.setModifiername(SystemUserResource.getRootUser().getName());
        updateOrder.setModifierename(SystemUserResource.getRootUser().getEname());
        updateOrder.setModifieddate(new Date());
        ocBOrderMapper.updateById(updateOrder);
        //更新京东订单接口，转换状态：已转换（2） 转换时间
        order.setIstrans(TransferOrderStatus.TRANSFERRED.toInteger());
        order.setTransdate(new Date());
        updateIpAndPushES(order);
    }

    /**
     * 挂起订单并记录日志
     *
     * @param ocBOrder 全渠道订单
     */
    private void interceptAndLog(OcBOrder ocBOrder) {
        // 调用通用退款HOLD单
        ocBOrderHoldService.businessHold(ocBOrder.getId(), OrderHoldReasonEnum.REFUND_HOLD);
    }

    /**
     * 更新接口平台数据并推送至ES
     *
     * @param order 接口平台数据
     */
    private void updateIpAndPushES(IpBJdOrder order) {
        Long orderId = order.getOrderId();
        order.setOrderId(null);
        order.setModifiername(SystemUserResource.getRootUser().getName());
        order.setModifierename(SystemUserResource.getRootUser().getEname());
        order.setModifieddate(new Date());
        jdOrderMapper.updateById(order);
        order.setOrderId(orderId);
    }


    /**
     * 配货中加锁调用反审核
     *
     * @param ocBOrder
     * @param operateUser
     */
    private void lockUpBackExamine(OcBOrder ocBOrder, User operateUser) {

        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                boolean isSuccess = this.toExamineOrder(ocBOrder, operateUser);
                if (!isSuccess) {
                    throw new NDSException("配货中反审核操作失败!");
                }
            } else {
                throw new NDSException("当前订单正在反审核操作!");
            }
        } catch (Exception e) {
            log.error(LogUtil.format(" 配货中调用反审核出错:{}", "lockUpBackExamine",
                    ocBOrder.getBillNo(), ocBOrder.getId(), ocBOrder.getSourceCode()), Throwables.getStackTraceAsString(e));
            throw new NDSException("订单锁单错误!");
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    private boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            if (isSuccess) {
                //反审核成功  将订单状态改为 待审核
                ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
            }
            return isSuccess;
        } catch (Exception e) {
            log.error(LogUtil.format("调用反审核失败:{}", "toExamineOrder",
                    ocBOrder.getBillNo(), ocBOrder.getId(), ocBOrder.getSourceCode()), Throwables.getStackTraceAsString(e));
            return false;
        }

    }
}
