package com.jackrain.nea.es.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.result.EsQueryByPageResult;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class ESHandleService {

    /**
     * @param indexName
     * @param type
     * @param pageIndex
     * @param pageSize
     * @param whereKeys
     * @param filterKeys
     * @param modelClass
     * @return filterKeys
     * @Description ES查询，返回分库键
     */
    public EsQueryByPageResult selectPrimaryKeyByES(String indexName, String type, int pageIndex, int pageSize,
                                                    JSONObject whereKeys, JSONObject filterKeys, String[] returnFileds, Class modelClass) {
        List<Object> list = new ArrayList<>();
        if (!ElasticSearchUtil.indexExists(indexName)) {
            try {
                ElasticSearchUtil.indexCreate(modelClass);
            } catch (IOException e) {
                log.error(LogUtil.format(" ES创建索引出错，索引名字{}", indexName, "ES创建索引"), indexName);
            }
        }
        int startIndex = (pageIndex - 1) * pageSize;
        JSONObject search = ElasticSearchUtil.search(indexName, type, whereKeys, filterKeys,
                null, pageSize, startIndex, returnFileds);
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getString(returnFileds[0]));
            }
        }
        //返回的总数
        Integer total = search.getInteger("total");
        Integer totalPage = 0;
        //计算循环的次数
        if (total % pageSize == 0) {
            //整除，页数=总数/每页的条数
            totalPage = total / pageSize;
        } else {
            //不整除，页数=总数/每页的条数+1
            totalPage = total / pageSize + 1;
        }
        EsQueryByPageResult esQueryByPageResult = new EsQueryByPageResult();
        esQueryByPageResult.setBillNoPrimaryList(list);
        esQueryByPageResult.setTotal(total);
        esQueryByPageResult.setTotalPage(totalPage);
        return esQueryByPageResult;
    }

    public List<Object> selectPrimaryKey2ByES(String indexName, String type, int pageIndex,
                                              JSONObject whereKeys, JSONObject filterKeys, String[] returnFileds) {
        Integer startIndex = pageIndex * OcBOrderConst.PAGE_SIZE;
        JSONObject jsonSearch = ElasticSearchUtil.search(indexName, type, whereKeys, filterKeys,
                null, OcBOrderConst.PAGE_SIZE, startIndex, returnFileds);
        List<Object> list = new ArrayList<>();
        if (jsonSearch.containsKey("rowcount") && jsonSearch.getInteger("rowcount") > 0) {
            JSONArray jsonArray = jsonSearch.getJSONArray("data");
            //分页循环
            for (Object obj : jsonArray) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getString(returnFileds[0]));
            }
        }
        return list;
    }

    //    /**
//     * @author: tianqinhua
//     * @since: 2019-05-24
//     * create at : 2019-05-24
//     * @param indexName ：索引index名，例如：ST_C_PRODUCT_STRATEGY_INDEX = "st_c_product_strategy"
//     * @param childClass ：推es的明细集合（推主表下关联的明细，明细可多个）例如：            ESHandleService.createEsIndex(EsConstant.OC_B_VIPCOM_DELIVERY_INDEX,OcBVipcomDeliveryDO.class, OcBVipcomDeliveryItemDO.class,...);
//     * @param mainClass：主表的实体class
//     */
    public Boolean createEsIndex(String indexName, Class mainClass, Class... childClass) {
        Boolean flag = true;
        log.info(LogUtil.format("传入的index:{}", indexName, "ES创建索引"), indexName);
        if (!ElasticSearchUtil.indexExists(indexName)) {
            try {
                List<Class> childs = new ArrayList<>();
                if (childClass != null) {
                    for (Class child : childClass) {
                        childs.add(child);
                    }
                }
                if (mainClass != null) {
                    if (CollectionUtils.isEmpty(childs)) {
                        ElasticSearchUtil.indexCreate(mainClass);
                    } else {
                        ElasticSearchUtil.indexCreate(childs, mainClass);
                    }
                }
            } catch (Exception e) {
                log.error(LogUtil.format(" ES创建索引失败,{},{}", indexName, "ES创建索引"),
                        indexName, Throwables.getStackTraceAsString(e));
                flag = false;
            }
        }
        return flag;
    }
}
