package com.jackrain.nea.oc.oms.services.naika;

import cn.hutool.core.collection.CollectionUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OmsNaiKaReturnDetailModel;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.util.ValueHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName OmsNaiKaReturnDetailService
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/25 19:55
 * @Version 1.0
 */

@Component
@Slf4j
public class OmsNaiKaReturnDetailService {

    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private OcBReturnAfSendItemMapper ocBReturnAfSendItemMapper;
    @Autowired
    private OcBOrderNaiKaMapper naiKaMapper;

    public ValueHolder getNaiKaReturn(Long id) {
        ValueHolder vh = new ValueHolder();
        OmsNaiKaReturnDetailModel model = new OmsNaiKaReturnDetailModel();

        // 根据id查询已发货退款单
        List<OcBReturnAfSend> ocBReturnAfSendList = ocBReturnAfSendMapper.selectOcBReturnAfSendListById(Collections.singletonList(id));
        if (CollectionUtil.isEmpty(ocBReturnAfSendList)) {
            vh.put("data", null);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "success");
            return vh;
        }
        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendList.get(0);
        // 获取商品明细表
        List<OcBReturnAfSendItem> ocBReturnAfSendItemList = ocBReturnAfSendItemMapper.selectByOcBReturnAfSendIdListBySendId(id);
        List<Long> skuIdList = ocBReturnAfSendItemList.stream().map(OcBReturnAfSendItem::getPsCSkuId).collect(Collectors.toList());
        String billNo = ocBReturnAfSend.getSourceBillNo();
        Long ocBOrderId = Long.valueOf(billNo);

        // 根据零售发货单id+skuid 获取到卡号信息
        model.setId(ocBReturnAfSend.getId());
        model.setBillNo(ocBReturnAfSend.getBillNo());
        model.setTid(ocBReturnAfSend.getTid());
        List<OmsNaiKaReturnDetailModel.NaiKaModel> naiKaModelList = new ArrayList<>();
        List<OcBOrderNaiKa> ocBOrderNaiKaList = naiKaMapper.selectNaiKa(ocBOrderId, skuIdList);
        if (CollectionUtil.isEmpty(ocBOrderNaiKaList)) {
            model.setNaiKaModels(naiKaModelList);
            vh.put("data", model);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "success");
            return vh;
        }
        for (OcBOrderNaiKa ocBOrderNaiKa : ocBOrderNaiKaList) {
            OmsNaiKaReturnDetailModel.NaiKaModel naiKaModel = new OmsNaiKaReturnDetailModel.NaiKaModel();
            buildNaiKaModel(naiKaModel, ocBOrderNaiKa);
            naiKaModelList.add(naiKaModel);
        }
        model.setNaiKaModels(naiKaModelList);
        vh.put("data", model);
        vh.put("code", ResultCode.SUCCESS);
        vh.put("message", "success");
        return vh;
    }

    private void buildNaiKaModel(OmsNaiKaReturnDetailModel.NaiKaModel naiKaModel, OcBOrderNaiKa ocBOrderNaiKa) {

        naiKaModel.setCardCode(ocBOrderNaiKa.getCardCode());
        naiKaModel.setId(ocBOrderNaiKa.getId());
        naiKaModel.setOperateTime(ocBOrderNaiKa.getOperateTime());
        naiKaModel.setSkuSpec(ocBOrderNaiKa.getSkuSpec());
        naiKaModel.setPsCProEname(ocBOrderNaiKa.getPsCProEname());
        naiKaModel.setSkuEcode(ocBOrderNaiKa.getPsCSkuEcode());
        OrderBusinessTypeCodeEnum orderBusinessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(ocBOrderNaiKa.getBusinessTypeCode());
        naiKaModel.setBusinessTypeName(orderBusinessTypeCodeEnum == null ? "" : orderBusinessTypeCodeEnum.getMassage());
        OmsOrderNaiKaStatusEnum statusEnum = OmsOrderNaiKaStatusEnum.getOrderStatusByStatus(ocBOrderNaiKa.getNaikaStatus());
        naiKaModel.setStatusName(statusEnum == null ? "" : statusEnum.getDesc());

    }
}
