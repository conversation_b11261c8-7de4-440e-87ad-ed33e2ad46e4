package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.oc.oms.model.table.task.OcBToWmsTask;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单传WMS任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-03
 */
@Mapper
public interface OcBToWmsTaskMapper extends ExtentionMapper<OcBToWmsTask> {

    @Select("select * from oc_b_to_wms_task where order_id = #{orderId} limit 1")
    OcBToWmsTask selectOcBToWmsTaskByOrderId(@Param("orderId") Long orderId);

    @Update("<script> "
            + "UPDATE oc_b_to_wms_task SET STATUS = #{status},modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void batchUpdateOcBToWmsTask(@Param("ids") List<Long> orderIds, @Param("status")Integer status);

    @SelectProvider(type = OcBToWmsTaskSql.class, method = "selectByNodeSql")
    List<Long> selectTaskIdList(@Param(value = "nodeName") String nodeName, @Param(value = "limit") int limit,
                                @Param(value = "taskTableName") String taskTableName, @Param("status") int status);

    @Update("UPDATE oc_b_to_wms_task SET STATUS = #{status}, modifieddate = now() where order_id = #{orderId} limit 1")
    void updateOcBToWmsTaskStatusByOrderId(@Param("status") int status, @Param("orderId") Long orderId);

    @Update("UPDATE oc_b_to_wms_task SET STATUS = #{status}, modifieddate = now(),push_delay_date=#{delayTime} where order_id = #{orderId} limit 1")
    void updateOcBToWmsTaskStatusAndDelayTimeByOrderId(@Param("status") int status, @Param("orderId") Long orderId,@Param("delayTime") Date delayTime);
}
