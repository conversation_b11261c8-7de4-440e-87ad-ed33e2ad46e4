package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSkuSplitTask;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSplitTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;



@Mapper
@Component
public interface OcBOrderSkuSplitTaskMapper extends ExtentionMapper<OcBOrderSkuSplitTask> {

    @InsertProvider(type = SqlProvider.class, method = "batchInsertOcBOrderSkuSplitTask")
    void batchInsertOcBOrderSkuSplitTask(@Param("list") List<OcBOrderSkuSplitTask> list);

    class SqlProvider {

        public String batchInsertOcBOrderSkuSplitTask(Map<String, Object> para) {
            List<OcBOrderSkuSplitTask> list = (List<OcBOrderSkuSplitTask>) para.get("list");
            StringBuffer sql = new StringBuffer();
            sql.append("insert ignore into `oc_b_order_sku_split_task` (`id`,`oc_b_order_id`,`ps_c_sku_id`, `status`, `next_time`," +
                    "`isactive`,`version`,`ad_org_id`,`ad_client_id`,`ownerid`,`ownerename`,`ownername`,`creationdate`," +
                    "`modifierid`,`modifierename`,`modifiername`,`modifieddate`) VALUES ");
            MessageFormat mf = new MessageFormat("(#'{'list[{0}].id},#'{'list[{0}].ocBOrderId},#'{'list[{0}].psCSkuId},#'{'list[{0}].status},#'{'list[{0}].nextTime},#'{'list[{0}].isactive},#'{'list[{0}].version},#'{'list[{0}].adOrgId},#'{'list[{0}].adClientId},#'{'list[{0}].ownerid},#'{'list[{0}].ownerename},#'{'list[{0}].ownername},#'{'list[{0}].creationdate},#'{'list[{0}].modifierid},#'{'list[{0}].modifierename},#'{'list[{0}].modifiername},#'{'list[{0}].modifieddate})");
            for (int i = 0; i < list.size(); i++) {
                if (i > 0) {
                    sql.append(",");
                }
                sql.append(mf.format(new Object[]{i}));
            }
            return sql.toString();
        }

    }

    @Select("select DISTINCT ps_c_sku_id from oc_b_order_sku_split_task where status = #{status} and exec_times < #{execTimes} and next_time <= #{nextTime} ORDER BY creationdate ASC,ps_c_sku_id ASC limit #{limitNum}")
    List<Long> selectSkuIdList(@Param("status") int status, @Param("limitNum") int limitNum, @Param("execTimes") int execTimes, @Param("nextTime") long nextTime);

    @Select("select count(*) from oc_b_order_sku_split_task where oc_b_order_id = #{orderId} and ps_c_sku_id = #{skuId}")
    int countBypSkuIdAndOrderId(@Param("orderId") Long orderId, @Param("skuId") Long skuId);

    @Update("update oc_b_order_sku_split_task set status = #{status} where oc_b_order_id = #{orderId} and ps_c_sku_id = #{skuId}")
    int updateStatus(@Param("orderId") Long orderId, @Param("skuId") Long skuId, @Param("status") int status);

    @Select("<script> "
            + "SELECT DISTINCT oc_b_order_id FROM oc_b_order_sku_split_task where status = #{status} and ps_c_sku_id in "
            + "<foreach item='item' index='index' collection='skuIdList' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<Long> selectOrderIdList(@Param("status") int status, @Param("skuIdList") List<Long> skuIdList);


    @Update("<script> "
            + "UPDATE oc_b_order_sku_split_task SET status = #{newStatus}, exec_times = exec_times + 1, modifieddate = now()  "
            + " where status in "
            + "<foreach item='item' index='index' collection='oldStatusList' open='(' separator=',' close=')'> #{item} </foreach>"
            + " and "
            + " oc_b_order_id in "
            + "<foreach item='item' index='index' collection='orderIdList' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int batchUpdate(@Param("orderIdList") List<Long> orderIdList, @Param("newStatus") int newStatus, @Param("oldStatusList") List<Integer> oldStatusList);

    @Update("<script> "
            + "UPDATE oc_b_order_sku_split_task SET next_time = #{nextTime}, modifieddate = now() where ps_c_sku_id in "
            + "<foreach item='item' index='index' collection='skuIdList' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int batchUpdateNextTime(@Param("skuIdList") List<Long> skuIdList, @Param("nextTime") long nextTime);
}