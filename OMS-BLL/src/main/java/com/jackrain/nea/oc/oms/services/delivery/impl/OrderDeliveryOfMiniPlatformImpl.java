package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.CpCLogisticsItem;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.model.minipt.OrderDeliveryReq;
import com.jackrain.nea.oc.oms.config.OmsMiniPlatformConfig;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.HubRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/14
 * description : 小平台发货
 */
@Slf4j
@Component
public class OrderDeliveryOfMiniPlatformImpl implements OrderDeliveryCmd {
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper itemMapper;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private HubRpcService hubRpcService;
    @Autowired
    OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsMiniPlatformConfig omsMiniPlatformConfig;

    @Autowired
    private OmsMiniPlatformDelivery omsMiniPlatformDelivery;

    @Override
    public boolean deliveryDeal(OcBOrderRelation orderRelation, List<String> tips) {

        Integer sendRoute = omsMiniPlatformConfig.getSendRoute();
        if (sendRoute == 0) {
            return omsMiniPlatformDelivery.delivery(orderRelation,tips);
        }

        Long orderId = orderRelation.getOrderId();
        String billNo = orderRelation.getOrderInfo().getBillNo();

        //判断是否为手工单，如果为手工单直接标记平台发货
        if ("手工新增".equals(orderRelation.getOrderInfo().getOrderSource())) {
            //更新发货状态，插入日志
            String logMsg = "OrderId=" + orderId + "为手工新增单，直接标记平台发货";
            orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                    null, null);
            OcBOrder update = new OcBOrder();
            update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            update.setId(orderId);
            orderMapper.updateById(update);
            return true;
        }
        List<ValueHolderV14> v14s = Lists.newArrayList();
        List<OrderDeliveryReq> deliveryReqList = this.build(orderRelation);
        for (OrderDeliveryReq deliveryReq : deliveryReqList) {
            try {
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("deliveryReq:{}", "OrderDeliveryOfMiniPlatformImpl.deliveryDeal"), JSON.toJSONString(deliveryReq));
                }
                ValueHolderV14 v14 = hubRpcService.deliveryOrder(deliveryReq);
                v14.setData(deliveryReq.getOrderCode());
                v14s.add(v14);
            } catch (Exception e) {
                log.error(LogUtil.format("errorInfo:{}", "OrderDeliveryOfMiniPlatformImpl.deliveryDeal.error"), Throwables.getStackTraceAsString(e));
                v14s.add(new ValueHolderV14(deliveryReq.getOrderCode(), ResultCode.FAIL, e.getMessage()));
            }
        }
        boolean result = false;

        boolean allSuccess = true;
        for (ValueHolderV14 v14 : v14s) {
            if (v14.getCode() == ResultCode.FAIL) {
                //更新发货状态，插入日志
                String logMsg = "OrderId=" + orderId + ",平台单号=" + v14.getData() + "发货通知平台失败," + v14.getMessage();
                orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                        null, null);
                allSuccess = false;
                tips.add(v14.getMessage());
            } else {
                //更新发货状态，插入日志
                String logMsg = "OrderId=" + orderId + ",平台单号=" + v14.getData() + "发货通知平台成功";
                orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                        null, null);
                result = true;
                itemMapper.updateItemsWhenDeliverySuccess(orderId, (String) v14.getData());
            }
        }

        if (allSuccess) {
            OcBOrder update = new OcBOrder();
            update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            update.setId(orderId);
            orderMapper.updateById(update);
        }
        return result;
    }


    private List<OrderDeliveryReq> build(OcBOrderRelation orderRelation) {

        OcBOrder orderInfo = orderRelation.getOrderInfo();

        List<OcBOrderItem> orderItemList = orderRelation.getOrderItemList();
        // 过滤出明细未发货或者发货失败的
        Map<String, List<OcBOrderItem>> itemMap =
                orderItemList.stream().filter(item -> !NumberUtils.INTEGER_ONE.equals(item.getIsSendout())).collect(Collectors.groupingBy(OcBOrderItem::getTid));

        List<OrderDeliveryReq> deliveryReqList = new ArrayList<>();

        CpCLogisticsItem cpLogisticsItem = cpRpcService.selectCpCLogisticsEcode(Long.valueOf(orderInfo.getPlatform()), orderInfo.getCpCLogisticsId());
        if (cpLogisticsItem == null) {
            throw new NDSException("查询物流档案信息为空！");
        }
        Map<String, OcBOrderItem> groupMap = new HashMap<>();
        //是组合商品发送原单数量
        if (OcBOrderConst.IS_STATUS_IY.equals(orderInfo.getIsCombination())) {
            List<OcBOrderItem> ocBOrderItemList = omsOrderItemService.selectUnSuccessRefundAndGroupOrderItem(orderInfo.getId());
            if (CollectionUtils.isNotEmpty(ocBOrderItemList)) {
                groupMap = ocBOrderItemList.stream().collect(Collectors.toMap(o -> getKey(o), Function.identity()));
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("build:{}", "OrderDeliveryOfMiniPlatformImpl.deliveryDeal"), JSON.toJSONString(groupMap));
            }
        }
        for (Map.Entry<String, List<OcBOrderItem>> entry : itemMap.entrySet()) {
            String tid = entry.getKey();
            List<OcBOrderItem> orderItems = entry.getValue();
            OrderDeliveryReq deliveryReq = this.buildDeliveryReq(orderInfo, orderItems, tid, cpLogisticsItem, groupMap);
            deliveryReqList.add(deliveryReq);
        }

        return deliveryReqList;
    }

    /**
     * description:多个明细区别组合商品
     *
     * @Author: liuwenjin
     * @Date 2022/9/27 12:42
     */
    private String getKey(OcBOrderItem o) {
        return o.getGiftbagSku() + "-" + o.getPsCSkuPtEcode();
    }

    /**
     * description:  groupMap组合商品map
     *
     * @Author: liuwenjin
     * @Date 2022/9/27 12:45
     */
    private OrderDeliveryReq buildDeliveryReq(OcBOrder order, List<OcBOrderItem> orderItemList, String tid, CpCLogisticsItem cpLogisticsItem, Map<String, OcBOrderItem> groupMap) {
        OrderDeliveryReq deliveryReq = new OrderDeliveryReq();
        deliveryReq.setPtId(Long.valueOf(order.getPlatform()));
        deliveryReq.setShopId(order.getCpCShopId());
        deliveryReq.setOrderCode(tid);
        deliveryReq.setPtExpressCode(cpLogisticsItem.getCpCLogisticsEcode());
        deliveryReq.setPtExpressCompany(cpLogisticsItem.getCpCLogisticsEname());
        deliveryReq.setLogisticsCode(order.getExpresscode());
        deliveryReq.setIsSplit(order.getIsSplit());
        deliveryReq.setBillNo(order.getBillNo());
        List<OrderDeliveryReq.DataDto> items = new ArrayList<>();

        for (OcBOrderItem orderItem : orderItemList) {
            OrderDeliveryReq.DataDto item = new OrderDeliveryReq.DataDto();
            if (PlatFormEnum.YUNHUO.getCode().equals(order.getPlatform())) {
                // 部分平台使用中台条码
                item.setSpuId(orderItem.getPsCProEcode());
                item.setSkuCode(orderItem.getPsCSkuEcode());
            } else if (PlatFormEnum.TMALL_DDD.getCode().equals(order.getPlatform())
                    || PlatFormEnum.ALIPAY_MIN_APP.getCode().equals(order.getPlatform())) {
                item.setSpuId(orderItem.getSkuNumiid());
                item.setSkuCode(orderItem.getPsCSkuEcode());
            } else {
                item.setSpuId(orderItem.getNumIid());
                item.setSkuCode(orderItem.getSkuNumiid());
            }
            item.setOrderCode(orderItem.getOoid());
            item.setPtExpressCode(cpLogisticsItem.getCpCLogisticsEcode());
            item.setPtExpressCompany(cpLogisticsItem.getCpCLogisticsEname());
            item.setLogisticsCode(order.getExpresscode());

            //组合商品，对等换货逻辑处理数量 todo 组合商品传未拆分的数量
            Integer count = Integer.valueOf(orderItem.getQty().stripTrailingZeros().toPlainString());
            if (Objects.nonNull(orderItem.getOriginSkuQty())) {
                count = orderItem.getOriginSkuQty().intValue();
            }
            //是否是组合商品
            else if (OcBOrderConst.IS_STATUS_IY.equals(order.getIsCombination())) {
                OcBOrderItem groupItme = groupMap.get(getKey(orderItem));
                if (Objects.nonNull(groupItme)) {
                    count = Objects.isNull(groupItme.getQty()) ? NumberUtils.INTEGER_ZERO : groupItme.getQty().intValue();
                }
            } else {
                //判断是否是对等换&&明细有对等换货标   todo 对等换货未对等换货的数量
                if (OcBOrderConst.IS_STATUS_IY.equals(order.getIsEqualExchange()) && OcBOrderConst.IS_STATUS_IY.equals(orderItem.getIsEqualExchange())) {
                    //对等比例
                    String ratio = orderItem.getEqualExchangeRatio();
                    //计算对等换货原数量
                    count = paresEqualExchangeCount(ratio, orderItem.getQty());
                }
            }

            if (PlatFormEnum.KAI_ER_DE_LE.getCode().equals(order.getPlatform()) || PlatFormEnum.HEMAOS.getCode().equals(order.getPlatform())) {
                count = Objects.isNull(orderItem.getQty()) ? NumberUtils.INTEGER_ZERO : orderItem.getQty().intValue();
            }

            item.setCount(count);
            item.setGoodsName(orderItem.getPsCProEname());

            // 单创重量必传
            if (PlatFormEnum.DANCHUANG.getCode().equals(order.getPlatform()) || PlatFormEnum.DOUCHAO.getCode().equals(order.getPlatform())) {
                if (orderItem.getStandardWeight() != null) {
                    item.setWeight(orderItem.getStandardWeight().multiply(orderItem.getQty()));
                } else {
                    item.setWeight(BigDecimal.ZERO);
                }

            }
            items.add(item);
        }
        deliveryReq.setDetail(items);
        return deliveryReq;
    }

    /**
     * description:解析未对等换货之前的数量    例如 原来的 8  新的 4 比例 2：1  计算 4*2 / 1 = 8
     *
     * @Author: liuwenjin
     * @Date 2022/9/27 12:58
     */
    private Integer paresEqualExchangeCount(String ratioStr, BigDecimal qty) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" paresEqualExchange,ratioStr{},qty{}"), ratioStr, qty);
        }
        String[] ratio = ratioStr.split(":");
        if (ratio.length > 0) {
            List<String> list = Arrays.asList(ratio);
            //分母
            Integer num1 = Integer.valueOf(list.get(0));
            //分子
            Integer num2 = Integer.valueOf(list.get(1));
            //获取原数量 分母 * 数量 / 分子
            return num1 * qty.intValue() / num2;
        }
        return qty.intValue();
    }
}
