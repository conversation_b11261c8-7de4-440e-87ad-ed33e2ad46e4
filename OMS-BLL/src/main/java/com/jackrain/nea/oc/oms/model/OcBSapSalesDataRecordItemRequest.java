package com.jackrain.nea.oc.oms.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Description:
 *
 * @Author: guo.kw
 * @Since: 2022/8/29
 * create at: 2022/8/29 14:35
 */
@Data
public class OcBSapSalesDataRecordItemRequest {

    @JSONField(name = "OC_B_SAP_SALES_DATA_RECORD_ID")
    private Long ocBSapSalesDataRecordId;

    @JSONField(name = "SKU")
    private String sku;

    @JSONField(name = "LINE_TYPE")
    private String lineType;

    @JSONField(name = "LINE_CATEGORY")
    private String lineCategory;

    @JSONField(name = "QTY")
    private Integer qty;

    @JSONField(name = "UNIT")
    private String unit;

    @JSONField(name = "CP_C_STORE_ID")
    private Integer cpCStoreId;

    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @J<PERSON><PERSON>ield(name = "FACTORY_CODE")
    private String factoryCode;

    @JSONField(name = "AMT")
    private BigDecimal amt;

    @JSONField(name = "PRO_TYPE")
    private String proType;

    @JSONField(name = "BATCH")
    private String batch;

    //汇总码
    @JSONField(name = "MERGE_CODE")
    private String mergeCode;
    //汇总类型
    @JSONField(name = "SUM_TYPE")
    private String sumType;

    @JSONField(name = "CYCLE_QTY")
    private Long cycleQty;

    @JSONField(name = "RESIDUE_QTY")
    private Long residueQty;

    /**
     * 成本
     */
    @JSONField(name = "PRICE_COST")
    private BigDecimal priceCost;
}
