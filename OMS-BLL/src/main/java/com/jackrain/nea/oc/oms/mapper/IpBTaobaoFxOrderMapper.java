package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface IpBTaobaoFxOrderMapper extends ExtentionMapper<IpBTaobaoFxOrder> {

    /**
     * 依据FenxiaoID进行查询淘宝订单数据
     *
     * @param orderNo 淘宝订单数据。平台订单数据
     * @return 淘宝订单数据
     */
    @Select("SELECT * FROM ip_b_taobao_fx_order WHERE fenxiao_id=#{orderNo}")
    IpBTaobaoFxOrder selectTaobaoFxOrderByFenxiaoId(@Param("orderNo") Long orderNo);
}