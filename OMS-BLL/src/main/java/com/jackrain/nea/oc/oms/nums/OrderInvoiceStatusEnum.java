package com.jackrain.nea.oc.oms.nums;


/**
 * 订单开票状态枚举
 *
 * @author: huang.z<PERSON><PERSON>
 * create at: 2019/7/27 13:20
 */
public enum OrderInvoiceStatusEnum {

    UN_REGISTER(0, "未登记"),
    REGISTER_UNINVOICE(1, "已登记未开票"),
    UNINVOICED(2, "已开票"),
    PARTIAL_INVOICE(3, "部分开票");

    int val;
    String text;

    OrderInvoiceStatusEnum(Integer v, String t) {
        this.val = v;
        this.text = t;
    }

    public Integer getVal() {
        return val;
    }

    public String getText() {
        return text;
    }
}


