package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 1:57 下午
 */
public class ES4IpTaoBaoJXOrder {

    private ES4IpTaoBaoJXOrder() {
    }


    /**
     * 业务：淘宝经销订单转换补偿定时任务
     * 根据转换状态查询tid
     *
     * @param pageIndex 起始页
     * @param pageSize  每业显示条数
     * @return List tid
     */
    public static List<String> findTidByTransStatus(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ISTRANS", "0");
        String[] returnFieldNames = new String[]{"TID"};
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        // 按照倒序进行查询
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TAOBAO_JX_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.IP_B_TAOBAO_JX_ORDER_TYPE_NAME,
                whereKeys, null, orderKeys,
                pageSize, startIndex, returnFieldNames);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                String orderNo = jsonObject.getString("TID");
                orderNoList.add(orderNo);
            }
        }
        return orderNoList;
    }
}
