package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefundItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IpBStandplatRefundItemMapper extends ExtentionMapper<IpBStandplatRefundItem> {

    @Select("select * from ip_b_standplat_refund_item where ip_b_standplat_refund_id = #{ids}")
    List<IpBStandplatRefundItem> selectRefundItemByRefundId(@Param("ids") Long ids);

}