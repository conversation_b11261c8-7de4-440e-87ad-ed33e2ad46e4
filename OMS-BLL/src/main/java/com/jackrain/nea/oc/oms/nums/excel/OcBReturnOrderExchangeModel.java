package com.jackrain.nea.oc.oms.nums.excel;

import com.jackrain.nea.util.excel.XlsAno;
import com.jackrain.nea.util.excel.XlsDBAno;
import com.jackrain.nea.util.excel.XlsSt;
import com.jackrain.nea.util.excel.XlsTyp;

import java.math.BigDecimal;

/**
 * @author: xiWen.z
 * create at: 2019/8/15 0015
 */
@XlsDBAno(name = "oc_b_return_order_exchange", desc = "换货明细", index = 2, sort = "id:asc", st = {XlsSt.DB, XlsSt.ES, XlsSt.R3})
public class OcBReturnOrderExchangeModel {


    @XlsAno(name = "oc_b_return_order_id", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 0, desc = "退单编号")
    private Long ocBReturnOrderId;

    @XlsAno(name = "ps_c_sku_ecode", value = {XlsSt.NOTNULL}, type = XlsTyp.STRING, index = 10, desc = "条码")
    private String psCSkuEcode;

    @XlsAno(name = "barcode", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 20, desc = "国标码")
    private String barcode;

    @XlsAno(name = "ps_c_pro_ecode", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 30, desc = "商品编码")
    private String psCProEcode;

    @XlsAno(name = "ps_c_pro_ename", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 40, desc = "商品名称")
    private String psCProEname;

    @XlsAno(name = "price", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 70, desc = "吊牌价")
    private BigDecimal price;

    @XlsAno(name = "amt_refund", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 90, desc = "换货金额")
    private BigDecimal amtRefund;

    @XlsAno(name = "qty_exchange", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 100, desc = "换货数量")
    private BigDecimal qtyExchange;


}
