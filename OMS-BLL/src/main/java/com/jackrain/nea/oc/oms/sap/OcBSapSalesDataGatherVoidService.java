package com.jackrain.nea.oc.oms.sap;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.constant.SapSalesDateConstant;
import com.jackrain.nea.oc.oms.mapper.MilkCardAmountOffsetOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherSourceItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordMapper;
import com.jackrain.nea.oc.oms.model.constant.OcBSapSalesDataRecordConstant;
import com.jackrain.nea.oc.oms.model.table.MilkCardAmountOffsetOrder;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGather;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecord;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * sap common
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class OcBSapSalesDataGatherVoidService {

    @Autowired
    OcBSapSalesDataGatherMapper ocBSapSalesDataGatherMapper;

    @Autowired
    OcBSapSalesDataRecordMapper ocBSapSalesDataRecordMapper;

    @Autowired
    MilkCardAmountOffsetOrderMapper milkCardAmountOffsetOrderMapper;

    @Autowired
    OcBSapSalesDataGatherSourceItemMapper ocBSapSalesDataGatherSourceItemMapper;

    /**
     * 作废
     *
     * @param session
     * @return
     */
    public ValueHolder voidSalesDataGather(QuerySession session) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(session, SgR3BaseRequest.class);
        request.setR3(true);
        OcBSapSalesDataGatherVoidService service = ApplicationContextHandle.getBean(OcBSapSalesDataGatherVoidService.class);
        return R3ParamUtils.convertV14WithResult(service.voidSalesDataGather(request));
    }

    public ValueHolderV14<SgR3BaseResult> voidSalesDataGather(SgR3BaseRequest request) {
        if (log.isDebugEnabled()) {
            log.debug("Start OcBSapSalesDataGatherVoidService.voidSalesDataGather.ReceiveParams:request{}"
                    , JSONObject.toJSONString(request));
        }
        try {
            List<Long> idList = request.getIds();
            checkParams(request, idList);
            OcBSapSalesDataGather ocBSapSalesDataGather = new OcBSapSalesDataGather();
            ocBSapSalesDataGather.setModifierid(request.getLoginUser().getId().longValue());
            ocBSapSalesDataGather.setModifiername(request.getLoginUser().getName());
            ocBSapSalesDataGather.setModifieddate(new Date());
            ocBSapSalesDataGather.setIsactive(SgConstants.IS_ACTIVE_N);
            ocBSapSalesDataGatherMapper.update(ocBSapSalesDataGather, new LambdaQueryWrapper<OcBSapSalesDataGather>().in(OcBSapSalesDataGather::getId, idList));
            updateSalesDataRecordStatus(idList);
            updateMilkCardStatus(idList);
        } catch (Exception e) {
            AssertUtils.logAndThrowException(e.getMessage(), e, request.getLoginUser().getLocale());
        }
        return new ValueHolderV14<>(ResultCode.SUCCESS, "作废成功！");
    }

    public void checkParams(SgR3BaseRequest request, List<Long> idList) {

        if (idList == null || idList.size() == 0) {
            AssertUtils.logAndThrow("请选择需要作废的单据", request.getLoginUser().getLocale());
        }
        List<OcBSapSalesDataGather> ocBSapSalesDataGatherInfos = ocBSapSalesDataGatherMapper.selectList
                (new LambdaQueryWrapper<OcBSapSalesDataGather>().in(OcBSapSalesDataGather::getId, idList));

        List<String> isActiveInfo = ocBSapSalesDataGatherInfos.parallelStream().map(OcBSapSalesDataGather::getIsactive).collect(Collectors.toList());
        if (isActiveInfo.contains(SgConstants.IS_ACTIVE_N)) {
            AssertUtils.logAndThrow("存在已作废单据，不允许重复作废", request.getLoginUser().getLocale());
        }
        List<String> middleStatusInfo = ocBSapSalesDataGatherInfos.parallelStream().map(OcBSapSalesDataGather::getGatherMiddleStatus).collect(Collectors.toList());
        if (middleStatusInfo.contains(SapSalesDateConstant.GATHER_MIDDLE_STATUS_01)) {
            AssertUtils.logAndThrow("存在未汇总完成的单据，不允许作废", request.getLoginUser().getLocale());
        }
    }

    /**
     * 更新销售记录表汇总状态为未汇总
     *
     * @param idList
     */
    public void updateSalesDataRecordStatus(List<Long> idList) {
        List<OcBSapSalesDataRecord> recordInfo=ocBSapSalesDataRecordMapper.selectRecordDataInfo(idList);
        if (CollectionUtils.isNotEmpty(recordInfo)){
            List<Long> recordMainId=new ArrayList<>();
            for (OcBSapSalesDataRecord ocBSapSalesDataRecord:recordInfo){
                if (OcBSapSalesDataRecordConstant.SUM_TYPE_XS.equals(ocBSapSalesDataRecord.getSumType())
                        ||OcBSapSalesDataRecordConstant.SUM_TYPE_NK.equals(ocBSapSalesDataRecord.getSumType())
                        ||OcBSapSalesDataRecordConstant.SUM_TYPE_WMJ.equals(ocBSapSalesDataRecord.getSumType())
                        ||OcBSapSalesDataRecordConstant.SUM_TYPE_CWMJ.equals(ocBSapSalesDataRecord.getSumType())
                        ||OcBSapSalesDataRecordConstant.SUM_TYPE_BCTZ.equals(ocBSapSalesDataRecord.getSumType())){
                    recordMainId.add(ocBSapSalesDataRecord.getId());
                }
            }
            if (CollectionUtils.isNotEmpty(recordMainId)){
                ocBSapSalesDataRecordMapper.recordCount(recordMainId);
            }

        }

    }

    /**
     * 更新奶卡冲抵表汇总状态为未汇总
     *
     * @param idList
     */
    public void updateMilkCardStatus(List<Long> idList) {
        List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrders=milkCardAmountOffsetOrderMapper.selectMilkInfo(idList);
        if (CollectionUtils.isNotEmpty(milkCardAmountOffsetOrders)){
            List<Long> milkId=new ArrayList<>();
            for (MilkCardAmountOffsetOrder milkCardAmountOffsetOrder:milkCardAmountOffsetOrders){
                if (OcBSapSalesDataRecordConstant.SUM_TYPE_RYCD01.equals(milkCardAmountOffsetOrder.getSumType())
                        ||OcBSapSalesDataRecordConstant.SUM_TYPE_RYCD02.equals(milkCardAmountOffsetOrder.getSumType())
                        ||OcBSapSalesDataRecordConstant.SUM_TYPE_RYCD03.equals(milkCardAmountOffsetOrder.getSumType())
                        ||OcBSapSalesDataRecordConstant.SUM_TYPE_RYCD04.equals(milkCardAmountOffsetOrder.getSumType())
                ){
                    milkId.add(milkCardAmountOffsetOrder.getId());
                }
            }
            milkCardAmountOffsetOrderMapper.recordCount(milkId);
        }
    }
}
