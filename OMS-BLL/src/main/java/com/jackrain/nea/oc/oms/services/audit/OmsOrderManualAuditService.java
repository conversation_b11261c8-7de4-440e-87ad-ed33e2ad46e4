package com.jackrain.nea.oc.oms.services.audit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.result.ReginQueryResult;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.request.VipJitxGetChangeWarehouseWorkflowsRequest;
import com.jackrain.nea.ip.model.result.VipJitxGetChangeWarehouseWorkflowsResult;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBAuditTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.result.CheckOrderResult;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoModifyAddr;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.VipJitxWorkflowStateEnum;
import com.jackrain.nea.oc.oms.services.*;
import com.jackrain.nea.oc.oms.services.task.OmsWmsTaskService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.oc.oms.util.SplitOrderUtils;
import com.jackrain.nea.ps.api.request.ProSkuListCmdRequest;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCVipcomJitxWarehouse;
import com.jackrain.nea.st.service.OmsOrderPriceSchemeService;
import com.jackrain.nea.st.service.VipcomJitxWarehouseService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 订单手动审核按钮
 *
 * @author: heliu
 * @since: 2019/4/18
 * create at : 2019/4/18 13:58
 */
@Component
@Slf4j
public class OmsOrderManualAuditService {

    @Autowired
    private OmsOrderDistributeWarehouseService omsOrderDistributeWarehouseService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OrderHeavySingleService orderHeavySingleService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderPriceSchemeService omsOrderPriceSchemeService;

    @Autowired
    private OcBReturnOrderService ocBReturnOrderService;

    @Autowired
    private IpTaobaoOrderService ipTaobaoOrderService;

    @Autowired
    private OrderStrategyPriceComputeService orderStrategyPriceComputeService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private IpRpcService ipRpcService;

    @Autowired
    private OmsWmsTaskService wmsTaskService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private OcBOrderMapper orderedMap;

    @Autowired
    private UpdateOrderInfoService updateOrderInfoService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBAuditTaskMapper auditTaskMapper;

    @Autowired
    private OmsOrderAutoAuditService omsOrderAutoAuditService;

    @Autowired
    private VipcomJitxWarehouseService jitxWarehouseService;

    @Autowired
    private SplitOrderUtils splitOrderUtils;

    /**
     * 全渠道订单手动审核
     *
     * @param param 传入参数
     * @param user  用户对象
     * @return ValueHolderV14
     */
    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 doManualAuditOrder(OrderICheckRequest param, User user) {

        ValueHolderV14 vh = new ValueHolderV14();
        if (param == null || param.getIds().length < 1) {
            throw new NDSException(Resources.getMessage("请选择需要手动审核的订单！"));
        }
        Long[] ids = param.getIds();
        if (ids.length > 1) {
            int success = 0;
            int failed = 0;
            //列表界面审核
            for (int i = 0; i < ids.length; i++) {
                Long orderId = ids[i];
                String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                try {
                    if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                        if (checkOrderInfo(user, orderId)) {
                            success++;
                        } else {
                            failed++;
                        }
                    } else {
                        throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!"));
                    }
                } catch (Exception ex) {
                    failed++;
                } finally {
                    redisLock.unlock();
                }
            }
            vh.setCode(0);
            vh.setMessage("订单审核成功 " + success + "，订单审核失败 " + failed);
            return vh;
        } else {
            //单对象页面审核
            Long orderId = ids[0];
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(orderId);
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    if (checkOrderInfo(user, ids[0])) {
                        vh.setCode(0);
                        vh.setMessage("OrderId=" + ids[0] + ",订单审核成功! ");
                        return vh;
                    } else {
                        vh.setCode(-1);
                        vh.setMessage("OrderId=" + ids[0] + ",订单审核失败! ");
                        return vh;
                    }
                } else {
                    throw new NDSException(Resources.getMessage(" 当前订单其他人在操作，请稍后再试!"));
                }
            } catch (Exception ex) {
                throw new NDSException(Resources.getMessage(ex.getMessage()));
            } finally {
                redisLock.unlock();
            }
        }
    }


    public boolean updateChecked(OcBOrder ocBorderDto, User user, Boolean mandatoryAudit) {
        if (omsOrderService.updateAuditSuccess(ocBorderDto.getId(), user.getName())) {
            updateTaskAndAddLog(ocBorderDto, user, mandatoryAudit);
//            if (splitOrderUtils.isOpenWareHouseSplitOrder(ocBorderDto)) {
//                // 插入仓库拆单任务表
//                wmsTaskService.saveOrUpdateOcBWarehouseSplitTask(ocBorderDto.getId(), user);
//            } else {
//                // 插入传wms表
//                wmsTaskService.saveOrUpdateOcBToWmsTask(ocBorderDto, user);
//            }
            //03191434新增逻辑

            //判断订单平台类型是否为淘宝（天猫）2，如果是，则异步调用全链路服务
            //if (PlatFormEnum.TAOBAO.getCode() == ocBorderDto.getPlatform()) {
            //
            //}
//                long startTime = System.currentTimeMillis();
//                //调用订单传wms
//                sgOutStockNoticeService.addOutStockNotice(ocBorderDto.getId(), user);
//                if (log.isDebugEnabled()) {
//                    long endTime = System.currentTimeMillis();
//                    log.debug(this.getClass().getName()+" 已审核调用订单wms耗时:{}ms,订单id:{}", endTime - startTime,ocBorderDto.getId());
//                }

            return true;
        } else {
            log.error(LogUtil.format("审核失败: {}", ocBorderDto.getId()));
            return false;

        }
    }

    public void updateTaskAndAddLog(OcBOrder ocBorderDto, User user, Boolean mandatoryAudit) {
        if (mandatoryAudit) {
            omsOrderLogService.addUserOrderLog(ocBorderDto.getId(), ocBorderDto.getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                    "订单手动强制审核成功!", "", "", user);
        } else {
            omsOrderLogService.addUserOrderLog(ocBorderDto.getId(), ocBorderDto.getBillNo(), OrderLogTypeEnum.ORDER_EXAMINE.getKey(),
                    "订单手动审核成功!", "", "", user);
        }
        // 更新审核表的任务状态
        List<Long> idList = new ArrayList<>();
        idList.add(ocBorderDto.getId());
        auditTaskMapper.updateTaskStatus(idList);
        // 当开关开启走仓库拆单 （插入拆单task表），不开启 则不走仓库拆单*（插入传wms表）
        //todo wms在wing传
        log.info(LogUtil.format("订单手动审核成功，插入传wms表", ocBorderDto.getId()));
        // 插入传wms表
        wmsTaskService.saveOrUpdateOcBToWmsTask(ocBorderDto, user);
    }

    /**
     * @param user 用户对象
     * @param id   订单Id
     * @return boolean
     */
    public boolean checkOrderInfo(User user, Long id) {

        //查询订单数据
        QueryWrapper orderQuery = new QueryWrapper();
        orderQuery.eq("isactive", IsActiveEnum.Y.getKey());
        //orderQuery.eq("order_status",1);
        orderQuery.eq("id", id);
        OcBOrder ocBorderDto = orderedMap.selectOne(orderQuery);
        if (ocBorderDto == null) {
            return false;
        }
        if (ocBorderDto.getOrderStatus() != OcOrderCheckBoxEnum.CHECKBOX_UNCONFIRMED.getVal()) {
            if (log.isDebugEnabled()) {
                log.debug("当前订单状态非待审核，不可审核!");
            }
            return false;
        }
        if (OmsOrderIsInterceptEnum.YES.getVal().equals(ocBorderDto.getIsInterecept())) {
            if (log.isDebugEnabled()) {
                log.debug("当前订单被HOLD单，不可审核!");
            }
            return false;
        }
        //先查找订单对象
//        OcBOrder ocBorderDto = omsOrderService.selectAuditOrderInfo(id);
//        if (ocBorderDto == null) {
//            throw new NDSException(Resources.getMessage("当前订单被挂起，不可审核!"));
//        }
        //在查找为退款明细
        List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(ocBorderDto.getId());
        //单对象界面审核
        CheckOrderResult orderResult = checkOrder(ocBorderDto, orderItemList, user);
        if (orderResult.getId() == null) {
            return updateChecked(ocBorderDto, user, false);
        } else {
            OcBOrder ocBOrderDto = new OcBOrder();
            ocBOrderDto.setId(ocBorderDto.getId());
            ocBOrderDto.setAuditTime(new Date(System.currentTimeMillis()));
            //自动审核状态
            ocBOrderDto.setAutoAuditStatus(AutoAuditStatus.Audit_FAIL.toInteger());
            //审核失败类型
            ocBOrderDto.setAuditFailedType(orderResult.getFailedReason().getVal());
            ocBOrderDto.setAuditFailedReason(orderResult.getFailedReason().getKey());
            omsOrderService.updateOrderInfo(ocBOrderDto);
            log.error("OrderId={},审核失败！{}", ocBorderDto.getId(), orderResult.getMessage());
            return false;
        }
    }

    private void setCheckAuditFailedReason(CheckOrderResult result, OmsAuditFailedReason reason) {
        if (result == null || reason == null) {
            return;
        }
        result.setFailedReason(reason);
    }

    /**
     * 审核校验
     *
     * @param ocBorderDto   订单对象
     * @param orderItemList 为退款明细
     * @param user          用户对象
     * @return CheckOrderResult
     * @throws NDSException
     */
    private CheckOrderResult checkOrder(OcBOrder ocBorderDto, List<OcBOrderItem> orderItemList, User user) {

        CheckOrderResult result = new CheckOrderResult();
        Long orderId = ocBorderDto.getId();
        Integer orderType = ocBorderDto.getOrderType();
        Integer platform = ocBorderDto.getPlatform();
        Long shopId = ocBorderDto.getCpCShopId();
        Integer payType = ocBorderDto.getPayType();
        //判断明细是否为空
        if (CollectionUtils.isEmpty(orderItemList)) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单明细不能为空或者是全退款明细，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_02);
            return result;
        }

        //订单类型
        if (orderType == null) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单类型不能为空，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_14);
            return result;
        }
        if (payType == null) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单付款类型不能为空，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_10);
            return result;
        }
        //判断店铺是否为空
        if (shopId == null) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单下单店铺不能为空，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_03);
            return result;
        }

        //下单日期为空结束
        if (ocBorderDto.getOrderDate() == null) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单下单日期为空，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_03);
            return result;
        }

        // 判断唯品会JITX是否修改了仓库
        if (ocBorderDto.getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())
                && checkRedisJitxChangeWarehouseFlag(ocBorderDto)) {
            log.info("JITX订单平台改仓,不允许审核。orderId:{}", orderId);
            result.setId(orderId);
            result.setMessage("JITX订单OrderId=" + orderId + ",正在平台改仓,不允许审核。");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_29);
            return result;
        }

        //判断淘宝预售是否修改地址
        if (checkOrderAddressAwaitSys(ocBorderDto, user)) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",在淘宝待修改表中待修改，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_28);
            return result;
        }
        //校验预售尾款状态
        if (checkPresellStatus(ocBorderDto)) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单为预售尾款未付，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_15);
            return result;
        }
        //订单中省、市未维护，不允许审核！
        if (ocBorderDto.getCpCRegionProvinceEname() == null || ocBorderDto.getCpCRegionCityEname() == null) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单中省或者市未维护，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_08);
            return result;
        }

        //判断发货仓库是否为空
        CpCPhyWarehouse cpCPhyWarehouseInfo = omsOrderDistributeWarehouseService.queryByWarehouseId(ocBorderDto.getCpCPhyWarehouseId());

        if (cpCPhyWarehouseInfo == null || cpCPhyWarehouseInfo.getId() == null || cpCPhyWarehouseInfo.getId() == 0) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + "订单没有分配发货仓库，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_09);
            return result;
        }
        //判断物流公司是否为空
        CpLogistics cpLogistics = cpRpcService.queryLogisticsById(ocBorderDto.getCpCLogisticsId());
        if (cpLogistics == null || cpLogistics.getId() == null || cpLogistics.getId() == 0) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单没有分配物流公司，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_11);
            return result;
        }


        //判断订单总额是否为空
        if (ocBorderDto.getOrderAmt() == null) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单总额为空，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_11);
            return result;
        }

        //判断平台类型是否为空
        if (platform == null || platform < 0) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单没有平台类型，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_13);
            return result;
        }
        //“平台”是京东，且“付款方式”为货到付款订单，若物流公司非京东，则提示：”货到付款京东订单，物流必须是京东！
        //京东平台，2 货到付款 物流公司为京东
        if (!checkOrderPlatform(ocBorderDto)) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单为货到付款京东订单，物流必须是京东，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_23);
            return result;
        }
        //校验双11预售尾款状态
        if (!checkPresaleStatus(ocBorderDto)) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单为预售订单，且尾款未付，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_26);
            return result;
        }
        //若“订单来源”为手工新增，且“付款方式”为货到付款，则判断“代收货款”（表字段叫到付代收金额）是否大于0
        if (!checkOrderSource(ocBorderDto)) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单为货到付款的手工新增订单，代收货款必须大于0！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_27);
            return result;
        }
        //付款方式”为在线支付，且“服务费”、“代收货款”（表字段叫到付代收金额）都为0
        if (!checkOrderServiceAmtAndCodeAmt(ocBorderDto)) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单为在线支付订单，代收货款及服务费必须为0，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_28);
            return result;
        }
        //“订单类型”为换货，则判断换货类型的订单在原【退换货单】
        //20210117版本 黄志优修改，京东订单无需校验是否退货完成
//        if (!checkOrderIsChangeProd(ocBorderDto)) {
//            result.setId(orderId);
//            result.setMessage("OrderId=" + orderId + ",订单为换货订单，原退换单尚未售后审核成功，不允许审核！");
//            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_34);
//            return result;
//        }
        //判断【淘宝待修改地址表】是否存在同平台单号的数据
        if (!updateTaobaoSourceStatus(ocBorderDto)) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单存在待更新地址记录，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_30);
            return result;
        }
        //判断主表“商品金额”是否和明细金额（明细的标准价*数量）合计一致
        if (!checkItemAmount(ocBorderDto)) {
            result.setId(orderId);
            result.setMessage("OrderId=" + orderId + ",订单主表商品金额与明细不一致，不允许审核！");
            setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_32);
            return result;
        }
        //判断“平台”非淘宝和淘宝分销，则更新发货省市区名称
        if (!(ocBorderDto.getPlatform().equals(PlatFormEnum.TAOBAO.getCode())
                || ocBorderDto.getPlatform().equals(PlatFormEnum.TAOBAO_DISTRIBUTION.getCode()))) {
            try {
                updateRegion(ocBorderDto);
            } catch (Exception e) {
                log.error(LogUtil.format("更新发货省市区名称异常: {}", orderId), Throwables.getStackTraceAsString(e));
                result.setId(orderId);
                result.setMessage("OrderId=" + orderId + ",更新发货省市区名称异常，不允许审核！");
                setCheckAuditFailedReason(result, OmsAuditFailedReason.ERROR_32);
                return result;
            }
        }
        //判断发货仓库和服务公司是否在仓库物流规则中启用
        //liqb jitx 排除此判断，2020/3/24注释掉此段逻辑
/*        if (ocBorderDto.getPlatform() != PlatFormEnum.VIPJITX.getCode()&& !checkLogisticsRule(ocBorderDto)) {
            throw new NDSException(Resources.getMessage("OrderId=" + orderId + "的物流公司发货仓库在仓库物流规则中不存在或者未启用，不允许审核！"));
        }*/
        /*2020.11.09 去除判断 开始*/
        /*2020.4.4 增加判断 开始*/
        //CpCPhyWarehouse cpCPhyWarehouse = cpRpcExtService.queryByWarehouseId(ocBorderDto.getCpCPhyWarehouseId());
        //if (cpCPhyWarehouse == null) {
        //    String message = "OrderId=" + orderId + "实体仓信息不存在,不允许审核!";
        //    throw new NDSException(Resources.getMessage(message));
        //} else {
        //    /**
        //     * 订单下sku条数 > 该订单对应实体仓【最大商品行数】不允许审核
        //     */
        //    Integer linePkgMax = cpCPhyWarehouse.getLinePkgMax();
        //    int skuSize = orderItemList.size();
        //    // 订单下sku条数 > 该订单对应实体仓【最大商品行数】
        //    if (linePkgMax != null && linePkgMax.compareTo(skuSize) < 0) {
        //        String message = "OrderId=" + orderId + "商品条数(" + skuSize + ")不能大于实体仓的最大商品行数（" + linePkgMax + "）,不允许审核!";
        //        throw new NDSException(Resources.getMessage(message));
        //    }
        //    /**
        //     * 订单下sku总数量 > 该订单对应实体仓【最大商品数】不允许审核
        //     */
        //    BigDecimal qtyPkgMax = cpCPhyWarehouse.getQtyPkgMax();
        //    if (qtyPkgMax != null) {
        //        // 累加sku数量
        //        BigDecimal skuNum = orderItemList.stream().map(o -> o.getQty()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //        if (skuNum.compareTo(qtyPkgMax) > 0) {
        //            String message = "OrderId=" + orderId + "商品总数量(" + skuNum + ")不能大于实体仓的最大商品数（" + qtyPkgMax.toPlainString() + "）,不允许审核!";
        //            throw new NDSException(Resources.getMessage(message));
        //        }
        //    }
        //}
        /*2020.4.4 增加判断 结束*/
        /*2020.11.09 去除判断 结束*/

        //判断成交价
        // 2020-07-11 易邵峰修改：订单明细商品成交价金额进行判断是否符合要求
        OcBOrderRelation orderInfo = new OcBOrderRelation();
        orderInfo.setOrderInfo(ocBorderDto);
        boolean priceAmount = omsOrderAutoAuditService.checkItemPriceAmount(orderInfo);
        if (!priceAmount) {
            result.setId(orderId);
            result.setMessage("订单的明细商品成交价不正确,审核失败!");
            result.setFailedReason(OmsAuditFailedReason.ERROR_21);
            return result;
        }

        //检测鞋类商品的具体数量
        //checkOrderItemSku(ocBorderDto);

        //调用价格方案服务
        try {
            checkPriceItem(ocBorderDto);
        } catch (Exception e) {
            result.setId(orderId);
            result.setMessage(OmsAuditFailedReason.ERROR_33.getKey());
            result.setFailedReason(OmsAuditFailedReason.ERROR_33);
            return result;
        }

        // 调用查询重单服务
        orderHeavySingleService.heavySingleSveice(ocBorderDto, user);

        //调用线上代销资金占用变动服务
//        try {
//            RuntimeCompute runtimeCompute = new RuntimeCompute();
//            runtimeCompute.startRuntime();
//            checkStrategyPriceComputeOrder(ocBorderDto, orderItemList);
//            double usedTime = runtimeCompute.endRuntime();
//            if (log.isDebugEnabled()) {
//                log.debug("OrderId={},调用线上代销资金占用变动服务耗时!UsedTime={}", orderId, usedTime);
//            }
//
//        } catch (Exception e) {
//            result.setId(orderId);
//            result.setMessage("代销资金占用失败");
//            result.setFailedReason(OmsAuditFailedReason.ERROR_36);
//            return result;
//        }
        return result;
    }

    /**
     * 订单来源”为手工新增
     *
     * @param ocBorderDto 订单对象
     * @return boolean
     */
    private boolean checkOrderSource(OcBOrder ocBorderDto) {
        if (OmsStOrderType.MANUAL_ADD.getKey().equals(ocBorderDto.getOrderSource())
                && ocBorderDto.getPayType() == OmsPayType.CASH_ON_DELIVERY.toInteger()
                && ocBorderDto.getCodAmt().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 检查预售状态
     *
     * @param ocBorderDto 订单对象
     * @return boolean
     */
    private boolean checkPresaleStatus(OcBOrder ocBorderDto) {

        if (ocBorderDto.getDouble11PresaleStatus() != null
                && (ocBorderDto.getDouble11PresaleStatus() == OcOrderDoublellPresaleStatus.NOT_PRESALL.toInteger())) {
            return false;
        }
        return true;
    }

    /**
     * 京东物流
     *
     * @param ocBorderDto 订单对象
     * @return boolean
     */
    private boolean checkOrderPlatform(OcBOrder ocBorderDto) {

        //判断平台”是京东，且“付款方式”为货到付款订单
        if (PlatFormEnum.JINGDONG.getCode().equals(ocBorderDto.getPlatform()) && OmsPayType.CASH_ON_DELIVERY.toInteger() == ocBorderDto.getPayType()) {
            //物流公司非京东(根据物流公司ID为-2进行判断)
            if (LogisticsCompany.JINGDONG.getLongId().compareTo(ocBorderDto.getCpCLogisticsId()) == 0) {
                return true;
            }
            return false;
        } else {
            return true;
        }
    }

    /**
     * “付款方式”为在线支付，且“服务费”、“代收货款”（表字段叫到付代收金额）都为0
     *
     * @param ocBorderDto 订单对象
     * @return boolean
     */
    public boolean checkOrderServiceAmtAndCodeAmt(OcBOrder ocBorderDto) {

        BigDecimal serviceAmt = (ocBorderDto.getServiceAmt() == null ? BigDecimal.ZERO : ocBorderDto.getServiceAmt());
        BigDecimal codeAmt = (ocBorderDto.getCodAmt() == null ? BigDecimal.ZERO : ocBorderDto.getCodAmt());
        //在线支付
        if (ocBorderDto.getPayType() == OmsPayType.ON_LINE_PAY.toInteger()
                //“服务费”、“代收货款”（表字段叫到付代收金额）都为0
                && (serviceAmt.compareTo(BigDecimal.ZERO) != 0 || codeAmt.compareTo(BigDecimal.ZERO) != 0)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 判断是否为换货单
     *
     * @param ocBorderDto 订单对象
     * @return boolean
     */
    public boolean checkOrderIsChangeProd(OcBOrder ocBorderDto) {
        if (!ocBorderDto.getOrderType().equals(OrderTypeEnum.EXCHANGE.getVal())) {

            return true;
        }

        // 20210117版本 黄志优修改 京东平台不需要判断换货完成
        if (ocBorderDto.getPlatform().equals(PlatFormEnum.JINGDONG.getCode())) {

            return true;
        }

        //查询换货订单
        OcBReturnOrder ocBReturnOrder = ocBReturnOrderService.queryOcBReturnOrder(ocBorderDto.getOrigReturnOrderId());
        if (ocBReturnOrder == null
                || !ocBReturnOrder.getReturnStatus().equals(ReturnStatusEnum.COMPLETION.getVal())) {
            return false;
        } else {
            //换货类型已完成
            return true;
        }
    }

    /**
     * 淘宝待修改地址表
     *
     * @param ocBorderDto 订单对象
     * @return boolean
     */
    public boolean updateTaobaoSourceStatus(OcBOrder ocBorderDto) {

        String sourcecode = ocBorderDto.getSourceCode();
        if (StringUtils.isEmpty(sourcecode)) {
            return true;
        } else {
            IpBTaobaoModifyAddr ipBTaobaoModifyAddr = ipTaobaoOrderService.selectSourcecodeByIsUpdate(ocBorderDto.getSourceCode());
            if (ipBTaobaoModifyAddr == null) {
                return true;
            } else {
                if (ipBTaobaoModifyAddr.getIsUpdate() == null || ipBTaobaoModifyAddr.getIsUpdate() == 0) {
                    return false;
                } else {
                    return true;
                }
            }
        }
    }

//    /**
//     * 明细成交价计算
//     *
//     * @param ocBorderDto 订单对象
//     */
//    public void checkItemPriceAmount(OcBOrder ocBorderDto) {
//        /**
//         * 判断订单明细中金额是否正确，若明细中（标准价*数量）-优惠金额+调整金额-（成交价*数量）
//         * 的值小于0.1且 （成交价*数量）-平摊金额=单行成交金额，若不等于，则提示“XXX订单的
//         * 明细商品成交价不正确，不允许审核！” （XXX为订单编号）
//         */
//        List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(ocBorderDto.getId());
//        //明细中（标准价*数量）-优惠金额+调整金额-（成交价*数量）的值小于0.1
//        for (OcBOrderItem ocBOrderItem : orderItemList) {
//            //标准价
//            BigDecimal priceList = (ocBOrderItem.getPriceList() == null ? BigDecimal.ZERO : ocBOrderItem.getPriceList());
//            //数量
//            BigDecimal qty = (ocBOrderItem.getQty() == null ? BigDecimal.ZERO : ocBOrderItem.getQty());
//            //优惠金额
//            BigDecimal amtDiscount = (ocBOrderItem.getAmtDiscount() == null ? BigDecimal.ZERO : ocBOrderItem.getAmtDiscount());
//            //调整金额
//            BigDecimal adjustAmt = (ocBOrderItem.getAdjustAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getAdjustAmt());
//            //成交价格
//            BigDecimal price = (ocBOrderItem.getPrice() == null ? BigDecimal.ZERO : ocBOrderItem.getPrice());
//            //单行实际成交金额
//            BigDecimal realAmt = ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt();
//            //平摊金额
//            BigDecimal orderSplitAmt = (ocBOrderItem.getOrderSplitAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getOrderSplitAmt());
//            //(标准价*数量) - 优惠金额 + 调整金额 - 成交价*数量
//            BigDecimal out5 = ((price.multiply(qty)).subtract(amtDiscount).add(adjustAmt)).subtract((price.multiply(qty))).setScale(2, BigDecimal.ROUND_CEILING);
//            log.debug("订单明细Id" + ocBOrderItem.getId() + "(标准价*数量)-优惠金额+调整金额-(成交价*数量)--> " + out5);
//            //计算单行实际成交价
//            BigDecimal consult = ((price.multiply(qty)).subtract(orderSplitAmt));
//            //单行成交金额-[（成交价*数量）-平摊金额)]
//            BigDecimal secondTemp = realAmt.subtract(consult).setScale(2, BigDecimal.ROUND_CEILING);
//            log.debug("订单明细Id" + ocBOrderItem.getId() + "(单行成交金额-[(成交价*数量)-平摊金额)]--> " + secondTemp);
//            //设置0.1默认值作为比较变量
//            BigDecimal out6 = new BigDecimal("0.1");
//            //是否满足条件
//            boolean result = (out6.compareTo(out5) > 0 && (out6.compareTo(secondTemp) > 0));
//            if (!result) {
//                throw new NDSException(Resources.getMessage("OrderId" + ocBorderDto.getId() + "的订单明细商品成交金额不正确，不允许审核！"));
//            }
//        }
//    }

    /**
     * 判断主表“商品金额”是否和明细金额[成交价]合计一致
     *
     * @param ocBorderDto 订单对象
     * @return boolean
     */
    public boolean checkItemAmount(OcBOrder ocBorderDto) {
        BigDecimal priceCount = BigDecimal.ZERO;
        List<OcBOrderItem> orderItemList = ocBOrderItemMapper.selectUnSuccessRefundAndNoSplit(ocBorderDto.getId());
        List<OcBOrderItem> itemList = orderItemList.stream().filter(p -> p.getProType() != SkuType.NO_SPLIT_COMBINE).collect(Collectors.toList());
        for (OcBOrderItem orderItem : itemList) {
            //标准价*数量
            BigDecimal price = (orderItem.getPrice() == null ? BigDecimal.ZERO : orderItem.getPrice());
            priceCount = priceCount.add(price.multiply(orderItem.getQty()));
        }
        BigDecimal productAmt = (ocBorderDto.getProductAmt() == null) ? BigDecimal.ZERO
                : ocBorderDto.getProductAmt();
        // 主表商品金额与明细金额差值大于0.1审核不让过
        BigDecimal dValue = productAmt.subtract(priceCount).abs();
        if (productAmt == null || dValue.compareTo(BigDecimal.valueOf(0.1)) > 0) {
            return false;
        } else {
            return true;
        }

    }

    /**
     * 检测鞋类限制数量
     *
     * @param ocBorderDto 订单对象
     */
    public void checkOrderItemSku(OcBOrder ocBorderDto) {

        //统计订单中“物料类型”为鞋且该类型条码数量
        BigDecimal countQty = BigDecimal.ZERO;
        //计算发货仓库 + 物流公司在【物流分配比例设置】中的限制数量
        Long itemTotal = omsOrderPriceSchemeService.queryPriceList(ocBorderDto.getCpCPhyWarehouseId(), ocBorderDto.getCpCLogisticsId());
        //当维护了限制数量时
        if (itemTotal != null && itemTotal != 0L) {
            BigDecimal itemQty = new BigDecimal(itemTotal);
            List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(ocBorderDto.getId());
            for (OcBOrderItem orderItem : orderItemList) {
                ProSkuListCmdRequest proSkuListCmdRequest = new ProSkuListCmdRequest();
                List<Long> skuIdList = new ArrayList<>();
                skuIdList.add(orderItem.getPsCSkuId());
                proSkuListCmdRequest.setSkuids(skuIdList);
                try {
                    ValueHolder valueHolder = psRpcService.selectProdSkuInfoBySkuIds(skuIdList);
                    JSONObject jo = valueHolder.toJSONObject();
                    JSONObject dataJson = jo.getJSONObject("data");
                    JSONArray jsonArrayString = dataJson.getJSONArray("proSkuList");
                    if (jsonArrayString.isEmpty()) {
                        throw new NDSException(Resources.getMessage("订单Id为" + ocBorderDto.getId() + "的明细sku[" + orderItem.getPsCSkuEcode() + "]在商品档案中没有记录！"));
                    }
                    for (int i = 0; i < jsonArrayString.size(); i++) {
                        JSONObject result = jsonArrayString.getJSONObject(i);
                        String materielType = result.getString("MATERIELTYPE");
                        if (MaterielType.ADULT.getEcode().equals(materielType) || MaterielType.CHILD.getEcode().equals(materielType)) {
                            countQty = countQty.add(orderItem.getQty());
                        }
                    }
                } catch (Exception e) {
                    throw new NDSException("OrderId" + ocBorderDto.getId() + "的订单审核物料类型为鞋类异常！" + e.getMessage());
                }
            }
            if (countQty.compareTo(itemQty) > 0) {
                String temp = countQty.stripTrailingZeros().toPlainString();
                throw new NDSException(Resources.getMessage("OrderId" + ocBorderDto.getId() + "的订单中含有鞋类商品" + temp + "双，且大于限制数量" + itemTotal + "双，不允许审核！"));
            }
        }
    }

    /**
     * 更新省市区
     *
     * @param ocBorderDto 订单对象
     */
    public void updateRegion(OcBOrder ocBorderDto) {

        if (!(PlatFormEnum.TAOBAO.getCode().equals(ocBorderDto.getPlatform())
                || PlatFormEnum.TAOBAO_DISTRIBUTION.getCode().equals(ocBorderDto.getPlatform()))) {
            try {
                ValueHolder valueHolder = cpRpcService.getRegionNameByid(ocBorderDto.getCpCRegionProvinceId()
                        , ocBorderDto.getCpCRegionCityId(), ocBorderDto.getCpCRegionAreaId());
                JSONObject jsonObject = valueHolder.toJSONObject();
                ReginQueryResult reginQueryResult = (ReginQueryResult) jsonObject.get("data");
                if (reginQueryResult != null) {
                    if (reginQueryResult.getProvName() != null) {
                        ocBorderDto.setCpCRegionProvinceEname(reginQueryResult.getProvName());
                    }
                    if (reginQueryResult.getCityName() != null) {
                        ocBorderDto.setCpCRegionCityEname(reginQueryResult.getCityName());
                    }
                    if (reginQueryResult.getRegionName() != null) {
                        ocBorderDto.setCpCRegionAreaEname(reginQueryResult.getRegionName());
                    }
                    omsOrderService.updateOrderInfo(ocBorderDto);
                }
            } catch (NDSException e) {
                log.error(LogUtil.format("订单调用更新发货省市区名称服务异常: {}", ocBorderDto.getId()), Throwables.getStackTraceAsString(e));
                throw new NDSException(Resources.getMessage("OrderId" + ocBorderDto.getId() + "的订单调用更新发货省市区名称服务异常" + e.getMessage()));
            }
        }
    }

    /**
     * 调用线上代销资金占用变动服务
     *
     * @param ocBorderDto   订单对象
     * @param orderItemList 订单明细对象
     */
    private void checkStrategyPriceComputeOrder(OcBOrder ocBorderDto, List<OcBOrderItem> orderItemList) {

        List<OcBOrderParam> orderInfos = new ArrayList<>();
        OcBOrderParam ocBOrderParam = new OcBOrderParam();
        ocBOrderParam.setOcBOrder(ocBorderDto);
        ocBOrderParam.setOrderItemList(orderItemList);
        orderInfos.add(ocBOrderParam);
//        ValueHolderV14 v14 = orderStrategyPriceComputeService.onlineFundOccupy(orderInfos, AcScConstantsIF.BIll_VARIETY_NODE_AUDIT, SystemUserResource.getRootUser());
//        if (log.isDebugEnabled()) {
//            log.debug("OrderId[" + ocBorderDto.getId() + "],订单手动审核调用线上代销资金占用变动服务出参:" + v14.toJSONObject().toJSONString() + ";");
//        }
//        if (v14.getCode() == -1) {
//            String message = "OrderId" + ocBorderDto.getId() + v14.getMessage();
//            log.error(message);
//            throw new NDSException(Resources.getMessage(message));
//        }
    }


    /**
     * 检测商品明细是否满足商品价格服务
     *
     * @param ocBorderDto 订单对象
     */
    private void checkPriceItem(OcBOrder ocBorderDto) {
        List<Long> stCPriceList = stRpcService.queryPriceList(ocBorderDto.getCpCShopId());
        if (CollectionUtils.isNotEmpty(stCPriceList)) {
            //根据明细去比对价格方案策略
            List<OcBOrderItem> orderItemList = omsOrderItemService.selectUnSuccessRefund(ocBorderDto.getId());
            for (OcBOrderItem orderItem : orderItemList) {
                //商品价格
                BigDecimal price = orderItem.getRealAmt() == null ? BigDecimal.ZERO : orderItem.getRealAmt();
                omsOrderPriceSchemeService.checkNoAutoPriceScheme(stCPriceList, ocBorderDto, orderItem.getPsCSkuEcode(), orderItem.getPsCProId(), price, orderItem.getPsCProEcode());
            }
        }
    }


    /**
     * 判断淘宝预售是否修改地址
     *
     * @param order
     * @param user
     * @return
     */
    public boolean checkOrderAddressAwaitSys(OcBOrder order, User user) {
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        String provinceRedisKey = BllRedisKeyResources.getUpdateOrderAddressKey(order.getSourceCode());
        return objRedisTemplate.hasKey(provinceRedisKey);
    }


    /**
     * 检验未付尾款状态
     *
     * @param order
     * @return
     */
    public boolean checkPresellStatus(OcBOrder order) {
        String reserveVarchar03 = order.getStatusPayStep();
        if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(reserveVarchar03)) {
            return true;
        }
        return false;
    }

    /**
     * 判断redis中是否有JITX改仓标识
     *
     * @param ocBOrder 订单对象
     * @return
     */
    public boolean checkRedisJitxChangeWarehouseFlag(OcBOrder ocBOrder) {
        String redisKey = BllRedisKeyResources.getJitxChangeWarehouseFlagKey(ocBOrder.getId());
        CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
        if (log.isDebugEnabled()) {
            log.debug("唯品会JITX修改仓库redis KEY :{}", redisKey);
        }
        return objRedisTemplate.hasKey(redisKey) || (ocBOrder.getIsVipUpdateWarehouse() != null && ocBOrder.getIsVipUpdateWarehouse() == 1);
    }

    /**
     * 判断redis中是否有JITX发货重置标识
     *
     * @return
     */
    public boolean checkRedisJitxReseShipFlag(List<OcBOrderItem> itemList) {
        for (OcBOrderItem item : itemList) {
            String redisKey = BllRedisKeyResources.getJitxResetShipFlagKey(item.getTid());
            CusRedisTemplate<String, String> objRedisTemplate = RedisMasterUtils.getObjRedisTemplate();
            if (log.isDebugEnabled()) {
                log.debug("唯品会JITX发货重置redis key :{}", redisKey);
            }
            if (objRedisTemplate.hasKey(redisKey)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断JITX是否修改仓库
     *
     * @param order
     * @return
     */
    public ValueHolderV14 callVipJitxGetChangeWarehouseWorkflowsCmd(OcBOrder order, User user) {
        ValueHolderV14 rtnModel = new ValueHolderV14();
        rtnModel.setCode(ResultCode.FAIL);
        rtnModel.setMessage("返回数据异常");
        String requestOrderSn = order.getSourceCode();
        long start = System.currentTimeMillis();
        VipJitxGetChangeWarehouseWorkflowsRequest request = new VipJitxGetChangeWarehouseWorkflowsRequest();
        request.setOperateUser(user);
        request.setSellerNick(order.getCpCShopSellerNick());
        //request.setVendorId();
        List<String> orderSns = new ArrayList<>(1);
        orderSns.add(requestOrderSn);
        request.setOrderSns(orderSns);
        request.setPage(1);
        request.setPageSize(1);
        if (StringUtils.isNotEmpty(order.getVipWorkflowSn())) {
            List<String> workflowSns = new ArrayList<>();
            workflowSns.add(order.getVipWorkflowSn());
            request.setWorkflowSns(workflowSns);
        }
        ValueHolderV14<List<VipJitxGetChangeWarehouseWorkflowsResult>> response =
                ipRpcService.getChangeWarehouseWorkflows(request);
        if (response.isOK()) {
            List<VipJitxGetChangeWarehouseWorkflowsResult> resultList = response.getData();
            if (resultList != null && resultList.size() == 1) {
                VipJitxGetChangeWarehouseWorkflowsResult result = resultList.get(0);
                log.debug("调用【获取改仓工单接口】单条返回结果：" + JSON.toJSONString(result));
                if (requestOrderSn.equals(result.getOrderSn())) {
                    if (VipJitxWorkflowStateEnum.PASS.getKey().equals(result.getWorkflowState())) {
                        log.debug("调用【获取改仓工单接口】成功");
                        // 判断jitx中间表的物流单号，和订单的物流单号是否一致，不一致时，更新订单物流单号。
                        ValueHolderV14 valueHolderV14 = ipRpcService.getJitxOrderByOrderSn(requestOrderSn, user, order.getCpCShopId());
                        if (valueHolderV14.getCode() == ResultCode.SUCCESS) {
                            JSONArray data = (JSONArray) valueHolderV14.getData();
                            if (CollectionUtils.isNotEmpty(data)) {
                                JSONObject o = (JSONObject) data.get(0);
                                String orderSn = o.getString("order_sn");
                                String transportNo = o.getString("transport_no");
                                if (requestOrderSn.equals(orderSn)) {
                                    String deliveryWarehouse = o.getString("delivery_warehouse");
                                    //CpCPhyWarehouse cpCPhyWarehouse = cpRpcExtService.queryByWarehouseId(order.getCpCPhyWarehouseId());
                                    StCVipcomJitxWarehouse jitxWarehouse = jitxWarehouseService.queryJitxCapacity(order.getCpCShopId(),
                                            order.getCpCPhyWarehouseId(), null);
                                    if (jitxWarehouse != null) {
                                        String jitWarehouseEcode = jitxWarehouse.getVipcomWarehouseEcode();
                                        if (!YesNoEnum.Y.getVal().equals(order.getIsStoreDelivery())) {
                                            jitWarehouseEcode = jitxWarehouse.getVipcomUnshopWarehouseEcode();
                                        }
                                        if (jitWarehouseEcode.equals(deliveryWarehouse)) {
                                            // 删除改仓成功的redisKey
                                            String redisKey = BllRedisKeyResources.getJitxChangeWarehouseFlagKey(order.getId());
                                            CusRedisTemplate<String, String> objRedisTemplate =
                                                    RedisMasterUtils.getObjRedisTemplate();
                                            boolean flag = objRedisTemplate.delete(redisKey);
                                            if (log.isDebugEnabled()) {
                                                log.debug("删除改仓的redisKey：{}, 结果：{}", redisKey, flag);
                                            }
                                            updateOrderInfoService.updateOrderVipUpdateWarehouseStatusOrExpressCode(order, user, 0, transportNo);
                                            rtnModel.setCode(ResultCode.SUCCESS);
                                            rtnModel.setMessage(null);
                                            rtnModel.setData(result);
                                        }
                                    } else {
                                        if (log.isDebugEnabled()) {
                                            log.debug("平台订单仓库未修改成功，orderSn：{}，shopId：{}，data：{}",
                                                    requestOrderSn, order.getCpCShopId(), data);
                                        }
                                        rtnModel.setCode(99);
                                        rtnModel.setMessage("平台订单仓库未修改成功");
                                        rtnModel.setData(result);
                                    }
                                } else {
                                    if (log.isDebugEnabled()) {
                                        log.debug("调用IP服务获取JITX订单信息错误，orderSn：{}，shopId：{}，data：{}",
                                                requestOrderSn, order.getCpCShopId(), data);
                                    }
                                    rtnModel.setCode(99);
                                    rtnModel.setMessage("调用IP服务获取JITX订单信息错误");
                                    rtnModel.setData(result);
                                }
                            } else {
                                if (log.isDebugEnabled()) {
                                    log.debug("调用IP服务获取JITX订单信息为空，orderSn：{}，shopId：{}",
                                            requestOrderSn, order.getCpCShopId());
                                }
                                rtnModel.setCode(99);
                                rtnModel.setMessage("调用IP服务获取JITX订单信息为空");
                                rtnModel.setData(result);
                            }
                        } else {
                            if (log.isDebugEnabled()) {
                                log.debug("调用IP服务获取JITX订单信息失败，orderSn：{}，shopId：{}",
                                        requestOrderSn, order.getCpCShopId());
                            }
                            rtnModel.setCode(99);
                            rtnModel.setMessage("调用IP服务获取JITX订单信息失败，" + valueHolderV14.getMessage());
                            rtnModel.setData(result);
                        }
                        return rtnModel;
                    } else if (VipJitxWorkflowStateEnum.CREATE.getKey().equals(result.getWorkflowState())
                            || VipJitxWorkflowStateEnum.DOING.getKey().equals(result.getWorkflowState())) {
                        if (log.isDebugEnabled()) {
                            log.debug("调用【获取改仓工单接口】, 结果：{}， workflowState:{}",
                                    VipJitxWorkflowStateEnum.getNameByKey(result.getWorkflowState()), result.getWorkflowState());
                        }
                        rtnModel.setCode(99);
                        rtnModel.setMessage("处理中");
                        rtnModel.setData(result);
                        return rtnModel;
                    } else {
                        if (log.isDebugEnabled()) {
                            log.debug("调用【获取改仓工单接口】, 结果：{}， workflowState:{}",
                                    VipJitxWorkflowStateEnum.getNameByKey(result.getWorkflowState()), result.getWorkflowState());
                        }
                        rtnModel.setCode(99);
                        rtnModel.setMessage("未成功");
                        rtnModel.setData(result);
                        return rtnModel;
                    }
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("返回订单号（order_sn）和请求订单号（order_sn）不一致。返回订单号（order_sn）:{}, 请求订单号（order_sn）:{}", result.getOrderSn(), requestOrderSn);
                    }
                    rtnModel.setMessage("请求唯品会JITX平台订单号和返回订单号不一致。");
                    return rtnModel;
                }
            } else {
                if (resultList == null) {
                    if (log.isDebugEnabled()) {
                        log.debug("调用【获取改仓工单接口】返回数据异常：resultList->{}", resultList);
                    }
                    return rtnModel;
                }
                if (resultList.size() != 1) {
                    if (log.isDebugEnabled()) {
                        log.debug("调用【获取改仓工单接口】返回数据异常：条数异常->{}", resultList.size());
                    }
                    return rtnModel;
                }
            }
        } else {
            if (log.isDebugEnabled()) {
                log.debug("【获取改仓工单接口】失败：{}", response.getMessage());
            }
            rtnModel.setMessage(response.getMessage());
            return rtnModel;
        }
        return rtnModel;
    }
}