package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * 开票通知抬头类型枚举
 *
 * @author: huang.zaizai
 * create at: 2019/7/23 19:20
 */
public enum OcHeaderTypeEnum {

    PERSONAL(0, "个人"),
    ENTERPRISE(1, "企业");

    OcHeaderTypeEnum(Integer key, String name) {
        this.key = key;
        this.name = name;
    }

    @Getter
    private Integer key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String enumToStringBykey(Integer key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (OcHeaderTypeEnum e : OcHeaderTypeEnum.values()) {
            if (Integer.valueOf(e.getKey()).equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return null;
    }
}


