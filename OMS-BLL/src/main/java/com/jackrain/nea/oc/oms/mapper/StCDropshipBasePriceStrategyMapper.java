package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.dto.StCDropshipBasePriceStrategyDTO;
import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 一件代发客户基价策略Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StCDropshipBasePriceStrategyMapper extends ExtentionMapper<StCDropshipBasePriceStrategy> {

    /**
     * 查询策略列表（包含店铺信息）
     *
     * @param strategy 查询条件
     * @return 策略列表
     */
    @Select("<script>" +
            "SELECT " +
            "s.id, s.shop_id, s.sku_count, s.remark, s.audit_status, " +
            "s.ownerid, s.ownername, s.ownerename, s.creationdate, " +
            "s.modifierid, s.modifiername, s.modifierename, s.modifieddate, " +
            "shop.shop_name, shop.shop_code " +
            "FROM st_c_dropship_base_price_strategy s " +
            "LEFT JOIN cp_c_shop shop ON s.shop_id = shop.id " +
            "WHERE s.isactive = 'Y' " +
            "<if test='strategy.shopId != null'> AND s.shop_id = #{strategy.shopId} </if>" +
            "<if test='strategy.auditStatus != null'> AND s.audit_status = #{strategy.auditStatus} </if>" +
            "<if test='strategy.shopName != null and strategy.shopName != \"\"'> AND shop.shop_name LIKE CONCAT('%', #{strategy.shopName}, '%') </if>" +
            "ORDER BY s.creationdate DESC" +
            "</script>")
    List<StCDropshipBasePriceStrategyDTO> selectStrategyList(@Param("strategy") StCDropshipBasePriceStrategyDTO strategy);

    /**
     * 根据ID查询策略详情（包含店铺信息）
     *
     * @param id 策略ID
     * @return 策略详情
     */
    @Select("SELECT " +
            "s.id, s.shop_id, s.sku_count, s.remark, s.audit_status, " +
            "s.ownerid, s.ownername, s.ownerename, s.creationdate, " +
            "s.modifierid, s.modifiername, s.modifierename, s.modifieddate, " +
            "shop.shop_name, shop.shop_code " +
            "FROM st_c_dropship_base_price_strategy s " +
            "LEFT JOIN cp_c_shop shop ON s.shop_id = shop.id " +
            "WHERE s.id = #{id} AND s.isactive = 'Y'")
    StCDropshipBasePriceStrategyDTO selectStrategyById(@Param("id") Long id);

    /**
     * 根据店铺ID查询策略
     *
     * @param shopId 店铺ID
     * @return 策略信息
     */
    @Select("SELECT * FROM st_c_dropship_base_price_strategy " +
            "WHERE shop_id = #{shopId} AND isactive = 'Y' " +
            "ORDER BY creationdate DESC LIMIT 1")
    StCDropshipBasePriceStrategy selectByShopId(@Param("shopId") Long shopId);

    /**
     * 检查店铺是否已存在已审核的策略
     *
     * @param shopId 店铺ID
     * @param excludeId 排除的策略ID（编辑时使用）
     * @return 存在的策略数量
     */
    @Select("<script>" +
            "SELECT COUNT(1) " +
            "FROM st_c_dropship_base_price_strategy " +
            "WHERE shop_id = #{shopId} " +
            "AND audit_status = 1 " +
            "AND isactive = 'Y' " +
            "<if test='excludeId != null'> AND id != #{excludeId} </if>" +
            "</script>")
    int checkShopExists(@Param("shopId") Long shopId, @Param("excludeId") Long excludeId);

    /**
     * 查询所有策略（不限制审核状态）
     *
     * @return 所有策略列表
     */
    @Select("SELECT * " +
            "FROM st_c_dropship_base_price_strategy " +
            "WHERE isactive = 'Y' " +
            "ORDER BY modifieddate DESC")
    List<StCDropshipBasePriceStrategy> selectAllStrategies();

    /**
     * 根据店铺ID和策略编码查询策略
     *
     * @param shopId 店铺ID
     * @param strategyCode 策略编码
     * @return 策略信息
     */
    @Select("SELECT * FROM st_c_dropship_base_price_strategy " +
            "WHERE shop_id = #{shopId} AND strategy_code = #{strategyCode} AND isactive = 'Y' ")
    List<StCDropshipBasePriceStrategy> selectByShopIdAndStrategyCode(@Param("shopId") Long shopId, @Param("strategyCode") String strategyCode);

    /**
     * 根据策略编码查询策略
     *
     * @param strategyCode 策略编码
     * @return 策略信息
     */
    @Select("SELECT * FROM st_c_dropship_base_price_strategy " +
            "WHERE strategy_code = #{strategyCode} AND isactive = 'Y' ")
    List<StCDropshipBasePriceStrategy> selectByStrategyCode(@Param("strategyCode") String strategyCode);
}
