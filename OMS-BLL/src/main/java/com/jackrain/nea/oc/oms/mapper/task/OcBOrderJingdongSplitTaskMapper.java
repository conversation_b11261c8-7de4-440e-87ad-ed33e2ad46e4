package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderJingdongSplitTask;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Component;

import java.util.List;


@Mapper
@Component
public interface OcBOrderJingdongSplitTaskMapper extends ExtentionMapper<OcBOrderJingdongSplitTask> {


    @SelectProvider(type = OcBOrderSplitTaskSql.class, method = "selectByNodeSql")
    List<OcBOrderJingdongSplitTask> selectTaskIdList(@Param(value = "nodeName") String nodeName, @Param(value = "limit") int limit,
                                @Param(value = "taskTableName") String taskTableName,
                                @Param("splitTimes") int splitTimes);

}