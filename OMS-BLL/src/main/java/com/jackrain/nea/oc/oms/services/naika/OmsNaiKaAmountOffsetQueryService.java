package com.jackrain.nea.oc.oms.services.naika;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.MilkCardAmountOffsetOrderMapper;
import com.jackrain.nea.oc.oms.model.request.naika.NaiKaAmountOffsetQueryRequest;
import com.jackrain.nea.oc.oms.model.table.MilkCardAmountOffsetOrder;
import com.jackrain.nea.oc.oms.util.Permission4ESUtil;
import com.jackrain.nea.oc.oms.util.Permission4SlfEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName OmsNaiKaAmountOffsetQueryService
 * @Description 奶卡金额冲抵查询接口
 * <AUTHOR>
 * @Date 2022/9/14 11:16
 * @Version 1.0
 */
@Component
@Slf4j
public class OmsNaiKaAmountOffsetQueryService {

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private MilkCardAmountOffsetOrderMapper milkCardAmountOffsetOrderMapper;

    public ValueHolder naiKaAmountOffsetQuery(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject resultData = new JSONObject();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        JSONObject fixColumn = param.getJSONObject("fixedcolumns");
        Integer range = param.getInteger("range") == null ? QueryUtils.getdefalutrange() : param.getInteger("range");
        Integer startIndex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        NaiKaAmountOffsetQueryRequest offsetQueryRequest = fixColumn.toJavaObject(NaiKaAmountOffsetQueryRequest.class);
        return getValueHolder(querySession, vh, resultData, range, startIndex, offsetQueryRequest);
    }

    public ValueHolder getValueHolder(QuerySession querySession, ValueHolder vh, JSONObject resultData, Integer range, Integer startIndex, NaiKaAmountOffsetQueryRequest offsetQueryRequest) {
        // 根据参数 查询es数据
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKey = new JSONObject();
        JSONArray orderByKey = this.getOrderByKey();
        try {
            Integer totalCount = 0;
            List<Long> ids = new ArrayList<>();
            buildWhereKeys(whereKeys, offsetQueryRequest);
            buildFilterKey(filterKey, offsetQueryRequest);
            boolean result = Permission4ESUtil.permissionHandler(querySession.getUser(), whereKeys, Permission4SlfEnum.SHOP);
            if (!result) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", 0);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }
            JSONObject esResult = ElasticSearchUtil.search(OcElasticSearchIndexResources.MILK_CARD_AMOUNT_OFFSET_ORDER_INDEX_NAME, OcElasticSearchIndexResources.MILK_CARD_AMOUNT_OFFSET_ORDER_TYPE_NAME,
                    whereKeys, filterKey, orderByKey, range, startIndex, new String[]{"ID"});

            if (null == esResult) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", 0);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }
            JSONArray aryIds = esResult.getJSONArray("data");
            totalCount = esResult.getInteger("total");
            if (CollectionUtils.isEmpty(aryIds)) {
                resultData.put("start", startIndex);
                resultData.put("row", "");
                resultData.put("totalRowCount", totalCount);
                vh.put("data", resultData);
                vh.put("code", 0);
                vh.put("message", "success");
                return vh;
            }
            ids = Lists.newArrayList();
            for (int i = 0; i < aryIds.size(); i++) {
                Map<String, Long> map = (Map<String, Long>) aryIds.get(i);
                ids.add(map.get("ID"));
            }
            extracted(vh, resultData, range, startIndex, totalCount, ids);
            return vh;
        } catch (Exception e) {
            log.error(LogUtil.format("查询奶卡管理页面异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return vh;
        }
    }


    private void extracted(ValueHolder vh, JSONObject resultData, Integer range, Integer startIndex, Integer totalCount, List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return;
        }
        List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrders = milkCardAmountOffsetOrderMapper.selectByIdList(ids);
        for (MilkCardAmountOffsetOrder offsetOrder : milkCardAmountOffsetOrders) {
            if (ObjectUtil.equals(1, offsetOrder.getCollectStatus())) {
                offsetOrder.setCollectStatusName("已汇总");
            } else {
                offsetOrder.setCollectStatusName("未汇总");
            }
        }
        JSONArray jsonArray = (JSONArray) JSONArray.toJSON(milkCardAmountOffsetOrders);
        List<JSONObject> jsonObjectList = JSONObject.parseArray(
                JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
        JSONArray getFrameDataFormat = getFrameDataFormat(jsonObjectList);
        resultData.put("start", startIndex);
        resultData.put("rowCount", range);
        resultData.put("row", getFrameDataFormat);
        resultData.put("totalRowCount", totalCount);
        vh.put("data", resultData);
        vh.put("code", 0);
        vh.put("message", "success");
    }

    private void buildFilterKey(JSONObject filterKey, NaiKaAmountOffsetQueryRequest request) {
        if (ObjectUtil.isNotEmpty(request.getCreationDate())) {
            String orderDate = request.getCreationDate();
            String[] orderSplitDate = orderDate.split("~");
            String orderDateResult = convertDate(orderSplitDate[0], orderSplitDate[1]);
            filterKey.put("CREATIONDATE", orderDateResult);
        }
    }

    private void buildWhereKeys(JSONObject whereKeys, NaiKaAmountOffsetQueryRequest request) {

        if (CollectionUtil.isNotEmpty(request.getCpCShopId())) {
            whereKeys.put("CP_C_SHOP_ID", request.getCpCShopId());
        }

        if (ObjectUtil.isNotEmpty(request.getBillNo())) {
            String billNo = request.getBillNo();
            String billNoReplace = billNo.replaceAll("\\s*", "");
            String[] splitBillNoCode = billNoReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitBillNoCode));
            whereKeys.put("BILL_NO", jsonArray);
        }

        if (ObjectUtil.isNotEmpty(request.getMiddlegroundBillTypeCode())) {
            String middlegroundBillTypeCode = request.getMiddlegroundBillTypeCode();
            String middlegroundBillTypeCodeReplace = middlegroundBillTypeCode.replaceAll("\\s*", "");
            String[] splitMiddlegroundBillTypeCode = middlegroundBillTypeCodeReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitMiddlegroundBillTypeCode));
            whereKeys.put("MIDDLEGROUND_BILL_TYPE_CODE", jsonArray);
        }

        if (ObjectUtil.isNotEmpty(request.getOcBSapSalesDataGatherId())) {
            String ocBSapSalesDataGatherId = request.getOcBSapSalesDataGatherId();
            String ocBSapSalesDataGatherIdReplace = ocBSapSalesDataGatherId.replaceAll("\\s*", "");
            String[] splitoOBSapSalesDataGatherId = ocBSapSalesDataGatherIdReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitoOBSapSalesDataGatherId));
            whereKeys.put("OC_B_SAP_SALES_DATA_GATHER_ID", jsonArray);
        }

        if (ObjectUtil.isNotEmpty(request.getSumType())) {
            String sumType = request.getSumType();
            String sumTypeReplace = sumType.replaceAll("\\s*", "");
            String[] splitSumType = sumTypeReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitSumType));
            whereKeys.put("SUM_TYPE", jsonArray);
        }

        if (ObjectUtil.isNotEmpty(request.getCardCode())) {
            String cardCode = request.getCardCode();
            String cardCodeReplace = cardCode.replaceAll("\\s*", "");
            String[] splitCardCode = cardCodeReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitCardCode));
            whereKeys.put("CARD_CODE", jsonArray);
        }

    }

    /**
     * 框架格式返回
     *
     * @param dataList
     * @return
     */
    private static JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                Set<String> keySet = emp.keySet();
                JSONObject json = new JSONObject();
                for (String key : keySet) {
                    JSONObject val = new JSONObject();
                    val.put("val", emp.get(key));
                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }

    /**
     * 日期转成ES需要的格式
     *
     * @return
     */
    public String convertDate(String begindate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isEmpty(begindate) || StringUtils.isEmpty(endDate)) {
            return "";
        }
        try {
            return sdf.parse(begindate).getTime() + "~" + sdf.parse(endDate).getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * ES 查询orderby条件
     *
     * @return
     */
    public JSONArray getOrderByKey() {
        JSONArray orderKeys = new JSONArray();
        JSONObject orderByKey = new JSONObject();
        orderByKey.put("asc", false);
        orderByKey.put("name", "CREATIONDATE");
        orderKeys.add(orderByKey);
        return orderKeys;
    }
}
