package com.jackrain.nea.oc.oms.tag;

/**
 * Description： 抽象类，提供一些默认值
 * Author: RESET
 * Date: Created in 2020/7/8 22:20
 * Modified By:
 */
public abstract class AbstractTagger implements ITagger {

    /**
     * 打标开关
     *
     * @return
     */
    @Override
    public boolean shouldTag() {
        return true;
    }

    /**
     * 打标顺序
     *
     * @return
     */
    @Override
    public int taggerOrder() {
        return 0;
    }

    /**
     * 打标类型
     *
     * @return
     */
    @Override
    public int taggerType() {
        return 0;
    }

}
