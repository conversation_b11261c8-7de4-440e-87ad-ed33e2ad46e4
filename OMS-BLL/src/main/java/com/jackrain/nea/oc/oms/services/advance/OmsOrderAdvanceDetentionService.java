package com.jackrain.nea.oc.oms.services.advance;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.SplitReason;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.resources.OrderOccupyStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.AgainOccupyStockService;
import com.jackrain.nea.oc.oms.services.AutoReleaseHangTaskService;
import com.jackrain.nea.oc.oms.services.OcBOrderItemExtService;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OmsOccupyTaskService;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.task.OmsToBeConfirmedTaskService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.DateConversionUtil;
import com.jackrain.nea.oc.oms.util.OmsOrderSplitReasonUtil;
import com.jackrain.nea.oc.oms.util.OrderAmountUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.StDetentionPolicyRequest;
import com.jackrain.nea.st.model.table.StCDetentionPolicy;
import com.jackrain.nea.st.model.table.StCDetentionPolicyItem;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 预售卡单业务
 * @author: liuwj
 * @create: 2021-06-19 14:48
 **/
@Slf4j
@Component
public class OmsOrderAdvanceDetentionService {

    @Autowired
    protected OmsOrderService orderService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private OmsToBeConfirmedTaskService toBeConfirmedTaskService;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OrderAmountUtil orderAmountUtil;

    @Autowired
    OmsOrderSplitReasonUtil omsOrderSplitReasonUtil;


    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OmsOrderItemService omsOrderItemService;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    OmsOrderSpiltUtill omsOrderSpiltUtill;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    @Autowired
    private AgainOccupyStockService againOccupyStockService;

    @Autowired
    private AutoReleaseHangTaskService autoReleaseHangTaskService;
    @Autowired
    private OmsOccupyTaskService omsOccupyTaskService;
    @Autowired
    private OcBOrderItemExtService ocBOrderItemExtService;

    /**
     * <AUTHOR>
     * @Date 14:40 2021/7/13
     * @Description 判断订单是满足卡单
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean checkDetention(OcBOrderRelation orderInfo, User operateUser) {
        log.info("OcBOrderHoldItemService_autoHoldOrder 预售卡单业务 start orderInfo={}",
                JSON.toJSONString(orderInfo));
        OcBOrder order = orderInfo.getOrderInfo();
        List<OcBOrderItem> ocBOrderItems = orderInfo.getOrderItemList();
        //不包含增品 获取明细包含组合商品虚拟条码
        List<OcBOrderItem> orderItemList = omsOrderSpiltUtill.getOcBOrderItem(ocBOrderItems);
        //查询卡单策略
        StDetentionPolicyRequest detentionPolicyRequest = getStDetentionPolicyList(orderInfo);
        if (detentionPolicyRequest == null) {
            log.info(LogUtil.format("在当前时间没有符合的卡单单策略.店铺: {}", "checkDetention",
                    orderInfo.getOrderId()), orderInfo.getOrderInfo().getCpCShopTitle());
            return false;
        }
        log.info(LogUtil.format("查询的卡单单策略.店铺: {}, 策略: {}", "checkDetention",
                orderInfo.getOrderId()), orderInfo.getOrderInfo().getCpCShopTitle(), JSON.toJSONString(detentionPolicyRequest));
        //需要卡单的
        List<OcBOrderItem> isDetentionList = new ArrayList<>();
        //不需要卡单的
        List<OcBOrderItem> isNotDetentionList = new ArrayList<>();
        //获取所以挂靠赠品Map
        Map<String, List<OcBOrderItem>> isHangGiftMap = omsOrderSpiltUtill.getGiftListMap(ocBOrderItems);
        //获取所有组合商品map（真实条码）
        Map<String, List<OcBOrderItem>> isGroupItemMap = omsOrderSpiltUtill.getIsGroupItemMap(ocBOrderItems);
        if (log.isInfoEnabled()) {
            log.info(LogUtil.format("卡单循环数据明细为: {}", "checkDetention",
                    orderInfo.getOrderId()), JSON.toJSONString(orderItemList));
        }
        String detentionName = "";
        for (OcBOrderItem ocBOrderItem : orderItemList) {
            if (checkDetentionFlag(detentionPolicyRequest, ocBOrderItem)) {
                detentionName = detentionPolicyRequest.getStCDetentionPolicy().getName();
                isDetentionList.add(ocBOrderItem);
                //加入赠品
                if (isHangGiftMap != null) {
                    List<OcBOrderItem> giftList = isHangGiftMap.get(omsOrderSpiltUtill.getPsCSkuEcodeKey(ocBOrderItem));
                    if (CollectionUtils.isNotEmpty(giftList)) {
                        isDetentionList.addAll(giftList);
                    }
                }
                //加入组合商品
                if (isGroupItemMap != null) {
                    List<OcBOrderItem> groupList = isGroupItemMap.get(ocBOrderItem.getPsCSkuEcode());
                    if (CollectionUtils.isNotEmpty(groupList)) {
                        isDetentionList.addAll(groupList);
                    }
                }
            } else {
                isNotDetentionList.add(ocBOrderItem);
                //加入赠品
                if (isHangGiftMap != null) {
                    List<OcBOrderItem> giftList = isHangGiftMap.get(omsOrderSpiltUtill.getPsCSkuEcodeKey(ocBOrderItem));
                    if (CollectionUtils.isNotEmpty(giftList)) {
                        isDetentionList.addAll(giftList);
                    }
                }
                //加入组合商品
                if (isGroupItemMap != null) {
                    List<OcBOrderItem> groupList = isGroupItemMap.get(ocBOrderItem.getPsCSkuEcode());
                    if (CollectionUtils.isNotEmpty(groupList)) {
                        isDetentionList.addAll(groupList);
                    }
                }
            }

        }
        //不满足卡单 退出
        if (CollectionUtils.isEmpty(isDetentionList)) {
            if (log.isInfoEnabled()) {
                log.info(LogUtil.format("不满足卡单 退出,orderId={}", order.getId()), order.getId());
            }
            return false;
        }
        String logMsg = "OrderId=" + order.getId() + "开始预售卡单。";
        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), logMsg, "", null, operateUser);
        try {
            //店铺策略卡单拆单按钮
            boolean flag = true;
            StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(order.getCpCShopId());
            if (stCShopStrategyDO != null) {
                flag = stCShopStrategyDO.getIsDetentionSplit() != null && "Y".equals(stCShopStrategyDO.getIsDetentionSplit());
            }
            log.info("店铺策略卡单拆单按钮是否勾选,orderId={},flag={},isDetentionList.size()={},ocBOrderItems.size()={}", order.getId(), flag, isDetentionList.size(), ocBOrderItems.size());
            //整单卡 不需要拆单 && 判断店铺策略卡单拆单按钮不勾选
            if (isDetentionList.size() == ocBOrderItems.size() || !flag) {
                log.info("订单整单卡,orderId={}", order.getId());
                order.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
                order.setDetentionReason(detentionName);
                order.setOccupyStatus(OrderOccupyStatus.STATUS_11);
                order.setSysremark("orderId=" + order.getId() + "订单整单卡");
                ocBOrderMapper.updateById(order);
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "OrderId=" + order.getId() + "订单整单卡成功。", "", null, operateUser);
                autoReleaseHangTaskService.save(order.getId(), this.getReleaseTime(orderInfo.getOrderInfo(), detentionPolicyRequest.getStCDetentionPolicy()), operateUser);
                //有卡的 有不卡的 需要拆单
            } else {
                if (log.isInfoEnabled()) {
                    log.info(LogUtil.format("订单需要卡单拆单,orderId={}", order.getId()), order.getId());
                }
                List<OcBOrderRelation> ocBOrderRelationList = new ArrayList<>();
                //卡单
                if (CollectionUtils.isNotEmpty(isDetentionList)) {
                    OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                    // 构建订单头信息
                    OcBOrder ocBOrder = new OcBOrder();
                    BeanUtils.copyProperties(order, ocBOrder);
                    ocBOrder.setDetentionReason(detentionName);
                    //拆单赋值对应的预售类型，明细类型的综合
                    StringBuilder advanceTypeAll = new StringBuilder();
                    Date estimateConTimeMax = null;
                    for (OcBOrderItem orderItem : isDetentionList) {
                        if (orderItem.getAdvanceType() != null) {
                            if (!advanceTypeAll.toString().contains(orderItem.getAdvanceType())) {
                                advanceTypeAll.append(orderItem.getAdvanceType()).append(",");
                            }
                        }
                        //预计发货时间取最大
                        if (orderItem.getEstimateConTime() != null) {
                            if (estimateConTimeMax == null) {
                                estimateConTimeMax = orderItem.getEstimateConTime();
                            } else {
                                if (orderItem.getEstimateConTime().before(estimateConTimeMax)) {
                                    estimateConTimeMax = orderItem.getEstimateConTime();
                                }
                            }
                        }
                    }
                    //预售 类型
                    if (advanceTypeAll.length() > 0) {
                        ocBOrder.setAdvanceType(advanceTypeAll.substring(0, advanceTypeAll.length() - 1));
                        //订单=普通商品，不显示预售标
                        if (AdvanceConstant.ORDINARY_GOODS.equals(advanceTypeAll.substring(0, advanceTypeAll.length() - 1))) {
                            ocBOrder.setDouble11PresaleStatus(0);
                            ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
                        }
                    } else {
                        ocBOrder.setAdvanceType("");
                    }
                    ocBOrder.setEstimateConTime(estimateConTimeMax);
                    if (ocBOrder.getEstimateConTime() != null) {
                        ocBOrder.setLatestDeliveryTime(ocBOrder.getEstimateConTime());
                    }
                    //卡单标识
                    ocBOrder.setIsDetention(AdvanceConstant.DETENTION_STATUS_1);
                    order.setOccupyStatus(OrderOccupyStatus.STATUS_11);
                    ocBOrder.setSplitReason(SplitReason.SPLIT_BY_DETENTION);
                    ocBOrderRelation.setOrderInfo(ocBOrder);
                    ocBOrderRelation.setOrderItemList(isDetentionList);
                    ocBOrderRelationList.add(ocBOrderRelation);
                }
                //不需要卡单的
                if (CollectionUtils.isNotEmpty(isNotDetentionList)) {
                    OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
                    // 构建订单头信息
                    OcBOrder ocBOrder = new OcBOrder();
                    BeanUtils.copyProperties(order, ocBOrder);
                    //拆单赋值对应的预售类型，明细类型的综合
                    StringBuilder advanceTypeAll = new StringBuilder();
                    for (OcBOrderItem orderItem : isNotDetentionList) {
                        if (orderItem.getAdvanceType() != null) {
                            if (!advanceTypeAll.toString().contains(orderItem.getAdvanceType())) {
                                advanceTypeAll.append(orderItem.getAdvanceType()).append(",");
                            }
                        }
                    }
                    //预售 类型
                    if (advanceTypeAll.length() > 0) {
                        ocBOrder.setAdvanceType(advanceTypeAll.substring(0, advanceTypeAll.length() - 1));
                        //订单=普通商品，不显示预售标
                        if (AdvanceConstant.ORDINARY_GOODS.equals(advanceTypeAll.substring(0, advanceTypeAll.length() - 1))) {
                            ocBOrder.setDouble11PresaleStatus(0);
                            ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
                        }
                    } else {
                        ocBOrder.setAdvanceType("");
                    }
                    ocBOrder.setSplitReason(SplitReason.SPLIT_BY_DETENTION);
                    ocBOrderRelation.setOrderInfo(ocBOrder);
                    //所有非挂靠的赠品挂在卡单上
                    List<OcBOrderItem> noGiftRelationList = omsOrderSpiltUtill.getNoGiftRelationList(ocBOrderItems);
                    if (CollectionUtils.isNotEmpty(noGiftRelationList)) {
                        isNotDetentionList.addAll(noGiftRelationList);
                    }
                    ocBOrderRelation.setOrderItemList(isNotDetentionList);
                    ocBOrderRelationList.add(ocBOrderRelation);
                }
                //自定义拆单赋值
                omsOrderSplitReasonUtil.setCustomReason(ocBOrderRelationList);
                int suffixInfo = 0;
                for (OcBOrderRelation relation : ocBOrderRelationList) {
                    orderAmountUtil.recountOrderAmount(relation);
                    // 补充信息
                    suffixInfo++;
                    relation.getOrderInfo().setSuffixInfo(relation.getOrderId() + "-CHECK-SP-" + suffixInfo);
                    //OrderTagUtil.orderTags(relation);
                    OcBOrderRelation newOcBOrderRelation = this.saveOrder(relation);
                    String logMsg1 = "OrderId=" + newOcBOrderRelation.getOrderInfo().getId() + "进行卡单拆单,原单id=" + order.getId();
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ADVANCE_DETENTION_SPLIT.getKey(), logMsg1, "", null, operateUser);
                    if (!Objects.equals(relation.getOrderInfo().getIsDetention(), NumberUtils.INTEGER_ONE)) {
                        // 插入占单中间表
                        creatToBeConfirmedTask(newOcBOrderRelation.getOrderInfo());
                    } else {
                        // 卡单的插入卡单释放中间表
                        autoReleaseHangTaskService.save(newOcBOrderRelation.getOrderInfo().getId(), this.getReleaseTime(orderInfo.getOrderInfo(), detentionPolicyRequest.getStCDetentionPolicy()), operateUser);
                    }
                }
                order.setOrderStatus(OmsOrderStatus.SYS_VOID.toInteger());
                //作废原单
                order.setSysremark("orderId=" + order.getId() + "订单卡单拆单");
                ocBOrderMapper.updateById(order);
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.ADVANCE_DETENTION.getKey(), "OrderId=" + order.getId() + "订单卡单拆单成功。", "", null, operateUser);
            }
        } catch (Exception e) {
            String erroMsg = "OrderId=" + order.getId() + "预售卡单异常,异常信息为";
            log.error(erroMsg, e);
            throw new NDSException(e);
        }

        return true;
    }

    /**
     * <AUTHOR>
     * @Date 19:20 2021/6/19
     * @Description 判断是否满足卡单
     */
    private boolean checkDetentionFlag(StDetentionPolicyRequest detentionPolicyRequest, OcBOrderItem ocBOrderItem) {
        List<StCDetentionPolicyItem> stCDetentionPolicyItemList = detentionPolicyRequest.getStCDetentionPolicyItem();
        if (CollectionUtils.isNotEmpty(stCDetentionPolicyItemList)) {
            for (StCDetentionPolicyItem stCDetentionPolicyItem : stCDetentionPolicyItemList) {
                if (AdvanceConstant.PTSKU.equals(stCDetentionPolicyItem.getParsingRuleType()) && stCDetentionPolicyItem.getParsingContext().equals(ocBOrderItem.getSkuNumiid())) {
                    return true;
                }
                if (AdvanceConstant.TPID.equals(stCDetentionPolicyItem.getParsingRuleType()) && stCDetentionPolicyItem.getParsingContext().equals(ocBOrderItem.getNumIid())) {
                    return true;
                }
                if (AdvanceConstant.MPRO.equals(stCDetentionPolicyItem.getParsingRuleType()) && stCDetentionPolicyItem.getParsingContext().equals(ocBOrderItem.getPsCProEcode())) {
                    return true;
                }
                if (AdvanceConstant.MSKU.equals(stCDetentionPolicyItem.getParsingRuleType()) && stCDetentionPolicyItem.getParsingContext().equals(ocBOrderItem.getPsCSkuEcode())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * <AUTHOR>
     * @Date 15:29 2021/6/19
     * @Description 过滤出有效的卡单策略
     */
    private StDetentionPolicyRequest getStDetentionPolicyList(OcBOrderRelation orderInfo) {
        // 1.查询hold单策略
        StDetentionPolicyRequest stDetentionPolicyRequest =
                stRpcService.queryStDetentionPolicyByShopId(orderInfo.getOrderInfo().getCpCShopId());
        if (stDetentionPolicyRequest == null) {
            log.info("店铺[{}]没有配置卡单单策略,orderId={}",
                    orderInfo.getOrderInfo().getCpCShopTitle(), orderInfo.getOrderInfo().getId());
            return null;
        }
        Date createTime = orderInfo.getOrderInfo().getOrderDate();
        Date payTime = orderInfo.getOrderInfo().getPayTime();
        // 2.根据hold单策略 生成hold单明细，订单主表增加释放时间，修改is_interecept字段标识
        // 筛选出有效的策略（时间类型 下单时间 判断下单是否在方案生效时间内， 时间类型：支付时间 判断支付时间是否在方案生效时间内）
        StCDetentionPolicy stCDetentionPolicy = stDetentionPolicyRequest.getStCDetentionPolicy();
        if (stCDetentionPolicy != null) {
            if ((AdvanceConstant.ORDER_DATE.equals(stCDetentionPolicy.getPolicyTimeType()) && createTime.before(stCDetentionPolicy.getEndTime()) && createTime.after(stCDetentionPolicy.getStartTime()))
                    || (AdvanceConstant.PAY_TIME.equals(stCDetentionPolicy.getPolicyTimeType()) && payTime.before(stCDetentionPolicy.getEndTime()) && payTime.after(stCDetentionPolicy.getStartTime()))) {
                return stDetentionPolicyRequest;
            }
        }

        return null;
    }

    /**
     * <AUTHOR>
     * @Date 18:08 2021/6/19
     * @Description 卡单拆单后插入占单中间表
     */
    public void creatToBeConfirmedTask(OcBOrder ocBOrder) {
        omsOccupyTaskService.addOcBOccupyTask(ocBOrder, null);
    }

    /**
     * <AUTHOR>
     * @Date 16:22 2021/6/23
     * @Description 构建明细
     */
    private List<OcBOrderItem> bulidOrderItemList(Long orderId, List<OcBOrderItem> orderItemList, List<OcBOrderItemExt> ocBOrderItemExtList) {
        List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
        for (OcBOrderItem ocbItemDto : orderItemList) {
            OcBOrderItem orderItem = new OcBOrderItem();
            BeanUtils.copyProperties(ocbItemDto, orderItem);
            //重新生成Id
            orderItem.setId(ModelUtil.getSequence("oc_b_order_item"));
            //设置订单Id
            orderItem.setOcBOrderId(orderId);
            //修改人
            orderItem.setModifierename(SystemUserResource.ROOT_USER_NAME);
            //修改时间
            orderItem.setModifieddate(new Date());

            if (CollectionUtils.isNotEmpty(ocBOrderItemExtList)) {
                for (OcBOrderItemExt ocBOrderItemExt : ocBOrderItemExtList) {
                    if (ocbItemDto.getId().equals(ocBOrderItemExt.getOrderItemId())) {
                        ocBOrderItemExt.setOrderItemId(orderItem.getId());
                        ocBOrderItemExt.setOcBOrderId(orderId);
                    }
                }
            }
            ocBOrderItemList.add(orderItem);
        }
        return ocBOrderItemList;
    }

    /**
     * 构造新订单
     *
     * @param ocBorderDto      原始订单对象
     * @param orderNewId       新订单Id
     * @param newOrderItemList 订单明细信息
     * @return 拆分后的订单对象
     */
    private OcBOrder bulidOcBOrder(OcBOrder ocBorderDto, Long orderNewId, List<OcBOrderItem> newOrderItemList) {

        OcBOrder ocBOrder = new OcBOrder();
        //复制其他属性
        BeanUtils.copyProperties(ocBorderDto, ocBOrder);
        //设置ID
        ocBOrder.setId(orderNewId);
        //订单编号
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        //是否拆分原单
        ocBOrder.setIsSplit(1);
        //拆分原订单号
        ocBOrder.setSplitOrderId(ocBorderDto.getId());
        //平台单号
        ocBOrder.setSourceCode(ocBorderDto.getSourceCode());
        //设置创建人
        ocBOrder.setOwnername(SystemUserResource.ROOT_USER_NAME);
        //创建时间
        ocBOrder.setCreationdate(new Date());
        //修改人
        ocBOrder.setModifierename(SystemUserResource.ROOT_USER_NAME);
        //修改时间
        ocBOrder.setModifieddate(new Date());
        //系统备注
        ocBOrder.setSysremark("");

        // 根据明细的退款状态 判断主表的退款标记 1205
        boolean isHashReturn = false;
        if (CollectionUtils.isNotEmpty(newOrderItemList)) {
            for (OcBOrderItem item : newOrderItemList) {
                // 如果存在退款状态，并且状态不为：关闭，则需要打标
                if (StringUtils.isNotBlank(item.getPtReturnStatus())
                        && OcOrderRefundStatusEnum.CLOSED.getVal() != item.getRefundStatus()) {
                    isHashReturn = true;
                    break;
                }
            }
        }
        // 如果不退则取消标记
        if (!isHashReturn) {
            //是否已经拦截
            ocBOrder.setIsInterecept(0);
            //是否已经退款中
            ocBOrder.setIsInreturning(0);
        }
        return ocBOrder;
    }

    /**
     * <AUTHOR>
     * @Date 14:18 2021/6/23
     * @Description 生成新的订单
     */
    public OcBOrderRelation saveOrder(OcBOrderRelation relation) {
        OcBOrderRelation newRelation = new OcBOrderRelation();
        Long orderId = ModelUtil.getSequence("oc_b_order");
        List<OcBOrderItemExt> ocBOrderItemExtList = relation.getOcBOrderItemExtList();
        OcBOrder ocBOrder = bulidOcBOrder(relation.getOrderInfo(), orderId, relation.getOrderItemList());
        List<OcBOrderItem> ocBOrderItemList = bulidOrderItemList(orderId, relation.getOrderItemList(), relation.getOcBOrderItemExtList());
        // tagger 查单生成新单，需要打标：在单生成后保存前
        newRelation.setOrderInfo(ocBOrder);
        newRelation.setOrderItemList(ocBOrderItemList);
        // OrderTagUtil.orderTags(newRelation);
        //保存订单对象
        List<OcBOrderItem> equalExchangeItems = ocBOrderItemList.stream().filter(p -> p.getIsEqualExchange() != null && p.getIsEqualExchange() == 1).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(equalExchangeItems)) {
            ocBOrder.setIsEqualExchange(1);
        } else {
            ocBOrder.setIsEqualExchange(0);
        }
        omsOrderService.saveOrderInfo(ocBOrder);
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            omsOrderItemService.saveOcBOrderItem(ocBOrderItem, orderId);
        }
        if (CollectionUtils.isNotEmpty(ocBOrderItemExtList)) {
            ocBOrderItemExtService.insertList(ocBOrderItemExtList);
        }
        try {
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.ORDER_SPLIT.getKey(), "自动拆单新生成订单", "", "", SystemUserResource.getRootUser());
        } catch (Exception e) {
            log.error(LogUtil.format("保存订单日志报错.异常: {}"), Throwables.getStackTraceAsString(e));
        }
        return newRelation;
    }

    /**
     * <AUTHOR>
     * @Date 14:08 2021/6/21
     * @Description 判断卡单能不能释放
     */
    public boolean isReleaseOrder(OcBOrderRelation orderInfo, User operateUser) {
        log.info("isReleaseOrder 判断卡单能不能释放 start orderInfo={}",
                JSON.toJSONString(orderInfo));
        OcBOrder order = orderInfo.getOrderInfo();
        //查询卡单策略
        StDetentionPolicyRequest detentionPolicyRequest = getStDetentionPolicyList(orderInfo);
        if (detentionPolicyRequest == null) {
            log.info("店铺[{}]在当前时间没有符合的卡单策略,orderId={}",
                    orderInfo.getOrderInfo().getCpCShopTitle(), orderInfo.getOrderInfo().getId());
            return true;
        }
        StCDetentionPolicy stCDetentionPolicy = detentionPolicyRequest.getStCDetentionPolicy();
        //判断卡单能不能释放
        try {
            if (isRelease(order, stCDetentionPolicy)) {
                if (log.isInfoEnabled()) {
                    log.info(LogUtil.format("订单满足卡单,自动释放: {}", order.getId()), order.getId());
                }
                order.setIsDetention(AdvanceConstant.DETENTION_STATUS_2);
                //更新状态为分仓
                order.setSysremark("卡单释放成功");
                order.setDetentionReason("");
                //最后一次卡单释放埋点
                order.setDetentionReleaseDate(new Date());
                ocBOrderMapper.update(order, Wrappers.<OcBOrder>lambdaUpdate()
                        .set(OcBOrder::getDetentionReasonId, null)
                        .eq(OcBOrder::getId, order.getId()));

                ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.DETENTION_RELEASE_DATE, new Date(), order.getId(), operateUser);
                //加入占单表
                omsOccupyTaskService.addOcBOccupyTask(order, null);
                String logMsg = "OrderId=" + order.getId() + "卡单释放成功。";
                omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.DETENTION_RELEASE.getKey(), logMsg, "", null, operateUser);
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            String erroMsg = "OrderId=" + order.getId() + "卡单释放异常,异常信息为" + e.getMessage();
            log.error(LogUtil.format(erroMsg));
            OcBOrder lOrder = new OcBOrder();
            lOrder.setId(order.getId());
            lOrder.setSysremark(erroMsg);
            omsOrderService.updateOrderInfo(lOrder);
        }
        if (log.isInfoEnabled()) {
            log.info(LogUtil.format("订单不满足卡单释放: {}", order.getId()), order.getId());
        }
        return false;
    }

    // 根据策略计算是否满足 释放
    private boolean isRelease(OcBOrder orderInfo, StCDetentionPolicy stCDetentionPolicy) {
        log.info("判断卡单能不能释放{},策略为{}", orderInfo.getId(), stCDetentionPolicy);
        Date date = new Date();
        if (!AdvanceConstant.YES.equalsIgnoreCase(stCDetentionPolicy.getIsAutoRelease())) {
            return false;
        }
        // 指定时点释放
        if (AdvanceConstant.RELEASE_TIME_TYPE_1.equals(stCDetentionPolicy.getReleaseType())) {
            return date.after(stCDetentionPolicy.getReeleaseTime());
        }
        // 固定时长释放 需要根据配置计算释放时间
        if (AdvanceConstant.RELEASE_TIME_TYPE_2.equals(stCDetentionPolicy.getReleaseType())) {
            Date time = AdvanceConstant.ORDER_DATE.equals(stCDetentionPolicy.getPolicyTimeType()) ? orderInfo.getOrderDate() :
                    orderInfo.getPayTime();
            if (AdvanceConstant.TIME_UNIT_MINUTE.equals(stCDetentionPolicy.getReleaseTimeUnit())) {
                return date.after(DateConversionUtil.plusMinutes(time, stCDetentionPolicy.getDuration()));
            } else if (AdvanceConstant.TIME_UNIT_HOUR.equals(stCDetentionPolicy.getReleaseTimeUnit())) {
                return date.after(DateConversionUtil.plusHours(time, stCDetentionPolicy.getDuration()));
            } else if (AdvanceConstant.TIME_UNIT_DAY.equals(stCDetentionPolicy.getReleaseTimeUnit())) {
                return date.after(DateConversionUtil.plusDays(time, stCDetentionPolicy.getDuration()));
            }
        }
        return false;
    }

    //获取当前订单的挂靠赠品
    public List<OcBOrderItem> judgeIsGifts(String skuCode, List<OcBOrderItem> isHangGiftList) {
        List<OcBOrderItem> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(isHangGiftList)) {
            return null;
        } else {
            for (OcBOrderItem ocBOrderItem : isHangGiftList) {
                //判断挂靠关系
                if (skuCode.equals(ocBOrderItem.getGiftRelation())) {
                    list.add(ocBOrderItem);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        return null;
    }

    /**
     * <AUTHOR>
     * @Date 11:19 2021/5/17
     * @Description 赠品中的挂靠赠品、费挂靠赠品
     */
    public List<OcBOrderItem> getGiftList(List<OcBOrderItem> giftItemList, boolean flag) {
        List<OcBOrderItem> list = new ArrayList<>();
        if (flag) {
            //挂靠关系赠品
            List<OcBOrderItem> isHangGiftList = giftItemList.stream()
                    .filter(it -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift())
                            && StringUtils.isNotEmpty(it.getGiftRelation()))
                    .collect(Collectors.toList());
            list.addAll(isHangGiftList);
        } else {
            //非挂靠关系赠品
            List<OcBOrderItem> isNotHangGiftList = giftItemList.stream()
                    .filter(it -> OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(it.getIsGift())
                            && StringUtils.isEmpty(it.getGiftRelation()))
                    .collect(Collectors.toList());
            list.addAll(isNotHangGiftList);

        }
        return list;
    }

    private Date getReleaseTime(OcBOrder orderInfo, StCDetentionPolicy stCDetentionPolicy) {
        log.info("getReleaseTime 判断卡单能不能释放{},策略为{}", orderInfo.getId(), stCDetentionPolicy);
        Date date = new Date();
        if (!AdvanceConstant.YES.equalsIgnoreCase(stCDetentionPolicy.getIsAutoRelease())) {
            return null;
        }
        // 指定时点释放
        if (AdvanceConstant.RELEASE_TIME_TYPE_1.equals(stCDetentionPolicy.getReleaseType())) {
            return stCDetentionPolicy.getReeleaseTime();
        }
        // 固定时长释放 需要根据配置计算释放时间
        if (AdvanceConstant.RELEASE_TIME_TYPE_2.equals(stCDetentionPolicy.getReleaseType())) {
            Date time = AdvanceConstant.ORDER_DATE.equals(stCDetentionPolicy.getPolicyTimeType()) ? orderInfo.getOrderDate() :
                    orderInfo.getPayTime();
            if (AdvanceConstant.TIME_UNIT_MINUTE.equals(stCDetentionPolicy.getReleaseTimeUnit())) {
                return DateConversionUtil.plusMinutes(time, stCDetentionPolicy.getDuration());
            } else if (AdvanceConstant.TIME_UNIT_HOUR.equals(stCDetentionPolicy.getReleaseTimeUnit())) {
                return DateConversionUtil.plusHours(time, stCDetentionPolicy.getDuration());
            } else if (AdvanceConstant.TIME_UNIT_DAY.equals(stCDetentionPolicy.getReleaseTimeUnit())) {
                return DateConversionUtil.plusDays(time, stCDetentionPolicy.getDuration());
            }
        }
        return null;
    }
}
