package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCBusinessTypeMatchStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCBusinessTypeMatchStrategyMapper extends ExtentionMapper<StCBusinessTypeMatchStrategy> {

    @Select("SELECT * FROM st_c_business_type_match_strategy where bill_type = #{billType} and status = 2")
    List<StCBusinessTypeMatchStrategy> selectStCBusinessTypeMatchStrategyByType(Integer billType);
}


