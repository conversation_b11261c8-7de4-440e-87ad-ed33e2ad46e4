package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4InvoiceNotice;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBInvoiceNoticeMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OrderInvoiceStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: chenxiulou
 * @description: 开票通知 业务处理公共类
 * @since: 2019-07-22
 * create at : 2019-07-22 9:24
 */
@Component
@Slf4j
public class InvoiceNoticeDealUtil {

    private static final String LOG_TABLE_NAMAE = "oc_b_invoice_notice_log";

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBInvoiceNoticeLogMapper logMapper;

    @Autowired
    private OcBInvoiceNoticeMapper invoiceNoticeMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    /**
     * @param invoicId 开票单据id
     * @param orderIds 全渠道订单id
     * @param action   操作类型
     * @param user     用户
     * @return
     * @Description 全渠道订单批量  开票状态更新
     * <AUTHOR>
     * @date 2019-07-22 2019-07-22
     */
    public ValueHolderV14<List<OcBOrder>> updateOcBOrderList(Long invoicId, String orderIds, String action, User user) {
        ValueHolderV14<List<OcBOrder>> v14 = new ValueHolderV14<List<OcBOrder>>();
        List<String> idList = Arrays.asList(orderIds.split(","));
        if (!CollectionUtils.isEmpty(idList)) {
            List<OcBOrder> newOrders = new ArrayList<>();
            List<OcBOrder> orders = orderMapper.selectByIdsStrList(idList);
            for (OcBOrder order : orders) {

                String ocBInvoiceNoticeIds = order.getOcBInvoiceNoticeId();
                List<String> invoicIdList = new ArrayList<>();
                if (StringUtils.isNotEmpty(ocBInvoiceNoticeIds)) {
                    String[] idArrys = order.getOcBInvoiceNoticeId().split(",");
                    invoicIdList = Arrays.asList(idArrys);
                }
                updateOcBOderSingle(order.getId(), invoicId, invoicIdList, action, user,
                        action.equals("void") ? OrderInvoiceStatusEnum.UN_REGISTER :
                                OrderInvoiceStatusEnum.REGISTER_UNINVOICE);
                newOrders.add(orderMapper.selectByID(order.getId()));
            }
            if (!CollectionUtils.isEmpty(newOrders)) {
                v14.setCode(ResultCode.SUCCESS);
                v14.setData(newOrders);
                return v14;
            }
        }
        v14.setCode(ResultCode.FAIL);
        return v14;
    }

    /**
     * @param orderId
     * @return
     * @Description 更新全渠道订单 单条
     * <AUTHOR>
     * @date 2019-07-25 2019-07-25
     */
    public void updateOcBOderSingle(Long orderId, Long invoicId,
                                    List<String> noticeIdList, String action,
                                    User user, OrderInvoiceStatusEnum status) {
        OcBOrder order = new OcBOrder();
        if (!OrderInvoiceStatusEnum.UNINVOICED.equals(status) && invoicId != null) {
            String noticeIds = buildNewIdArrys(noticeIdList, invoicId, action);
            order.setOcBInvoiceNoticeId(noticeIds);
        }
        order.setId(orderId);
        order.setInvoiceStatus(status.getVal());
        order.setModifierid(Long.valueOf(user.getId()));
        order.setModifiername(user.getName());
        order.setModifierename(user.getEname());
        order.setModifieddate(new Date());
        orderMapper.updateById(order);

        OcBOrder orderNew = orderMapper.selectByID(order.getId());
        if (orderNew != null) {
            omsOrderLogService.addUserOrderLog(orderNew.getId(), orderNew.getBillNo(), OrderLogTypeEnum.INVOICE.getKey(),
                    "订单开票状态修改为" + status.getText(), null, null, user);
        }
    }

    /**
     * @param ids 旧列表
     * @param id  新的id
     * @param ids 操作类型
     * @return
     * @Description 重组发票id列表
     * <AUTHOR>
     * @date 2019-07-26 2019-07-26
     */
    private String buildNewIdArrys(List<String> ids, Long id, String action) {
        List<String> newIds = new ArrayList<>();
        newIds.addAll(ids);
        if (newIds.contains(id.toString())) {
            if (action.equals("void")) {
                newIds.remove(id.toString());
            }
        } else {
            if (action.equals("insert")) {
                newIds.add(id.toString());
            }
        }
        return newIds.stream().collect(Collectors.joining(","));
    }

    /**
     * 用户日志信息动态获取保存日志
     *
     * @param invoiceNoticeId ID
     * @param logType         日志类型
     * @param logMessage      日志消息
     * @param operateUser     用户对象
     */
    public void addInvoiceNoticeLog(Long invoiceNoticeId, Integer logType, String logMessage, User operateUser) {

        OcBInvoiceNoticeLog invoiceNoticeLog = new OcBInvoiceNoticeLog();
        long autoId = ModelUtil.getSequence(LOG_TABLE_NAMAE);
        invoiceNoticeLog.setId(autoId);
        invoiceNoticeLog.setOcBInvoiceNoticeId(invoiceNoticeId);
        invoiceNoticeLog.setLogType(logType);
        invoiceNoticeLog.setLogContent(logMessage);
        if (operateUser != null) {
            invoiceNoticeLog.setAdOrgId((long) operateUser.getOrgId());
            invoiceNoticeLog.setOwnername(operateUser.getEname());
            invoiceNoticeLog.setOwnerename(operateUser.getEname());
            invoiceNoticeLog.setAdClientId((long) operateUser.getClientId());
            invoiceNoticeLog.setOwnerid(Long.valueOf(operateUser.getId()));
            invoiceNoticeLog.setCreationdate(new Date());
            invoiceNoticeLog.setModifierid(Long.valueOf(operateUser.getId()));
            invoiceNoticeLog.setModifieddate(new Date());
            invoiceNoticeLog.setModifiername(operateUser.getName());
        } else {
            User rootUser = SystemUserResource.getRootUser();
            invoiceNoticeLog.setAdOrgId((long) rootUser.getOrgId());
            invoiceNoticeLog.setOwnername(rootUser.getEname());
            invoiceNoticeLog.setOwnerename(rootUser.getEname());
            invoiceNoticeLog.setAdClientId((long) rootUser.getClientId());
            invoiceNoticeLog.setOwnerid(Long.valueOf(rootUser.getId()));
            invoiceNoticeLog.setCreationdate(new Date());
            invoiceNoticeLog.setModifierid(Long.valueOf(rootUser.getId()));
            invoiceNoticeLog.setModifiername(rootUser.getName());
            invoiceNoticeLog.setModifierename(rootUser.getName());
            invoiceNoticeLog.setModifieddate(new Date());
        }
        int result = this.logMapper.insert(invoiceNoticeLog);
        //将日志信息推至ES
        if (result > 0) {
            boolean flag;
            String indexName = OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME;
//            try {
//                flag = SpecialElasticSearchUtil.indexDocument(indexName,
//                        OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_LOG_TYPE_NAME,
//                        invoiceNoticeLog, autoId, invoiceNoticeId);
//                log.debug("_返回状态" + flag);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
        }
    }

    /**
     * @param invoiceNotice
     * @param invoiceNoticeItems
     * @param invoiceNoticePros
     * @return
     * @Description 开票通知 推送ES
     * <AUTHOR>
     * @date 2019-07-24 2019-07-24
     */
    public void pushInvoiceNoticeToEs(OcBInvoiceNotice invoiceNotice, List<OcBInvoiceNoticeItem> invoiceNoticeItems,
                                      List<OcBInvoiceNoticePro> invoiceNoticePros, List<OcBOrder> orders) {
       /* if (invoiceNotice != null) {
            createInvoiceNoticeEsIndex();
            boolean flag;
            String indexName = OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME;
            try {
                flag = SpecialElasticSearchUtil.indexDocument(indexName,
                        OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_TYPE_NAME,
                        invoiceNotice, invoiceNotice.getId());
                log.debug(this.getClass().getName() + " {}返回状态:{}", OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_TYPE_NAME, flag);
            } catch (IOException e) {
                log.debug(this.getClass().getName() + " {}推送ES异常", OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_TYPE_NAME);
            }
        }

        if (!CollectionUtils.isEmpty(invoiceNoticeItems)) {
            String indexItemName = OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME;
            try {
                SpecialElasticSearchUtil.indexDocuments(indexItemName,
                        OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_ITEM_TYPE_NAME,
                        invoiceNoticeItems);
            } catch (IOException e) {
                log.debug(this.getClass().getName() + "{}推ES异常：{}", OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_ITEM_TYPE_NAME, e);
            }
        }
        if (!CollectionUtils.isEmpty(invoiceNoticePros)) {
            String indexItemName = OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME;
            try {
                SpecialElasticSearchUtil.indexDocuments(indexItemName,
                        OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_PRO_TYPE_NAME,
                        invoiceNoticePros);
            } catch (IOException e) {
                log.debug(this.getClass().getName() + "{}推ES异常：{}", OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_PRO_TYPE_NAME, e);
            }
        }
        if (!CollectionUtils.isEmpty(orders)) {
            String indexItemName = OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME;
            try {
                SpecialElasticSearchUtil.indexDocuments(indexItemName,
                        OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                        orders);
            } catch (IOException e) {
                log.debug(this.getClass().getName() + "{}推ES异常：{}", OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME, e);
            }
        }*/
    }

//    private static Boolean createInvoiceNoticeEsIndex() {
//        Boolean isExists = false;
//        if (!SpecialElasticSearchUtil.indexExists(OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME)) {
//            try {
//                //建索引
//                List<Class> childs = new ArrayList<>();
//                childs.add(OcBInvoiceNoticeItem.class);
//                childs.add(OcBInvoiceNoticePro.class);
//                childs.add(OcBInvoiceNoticeLog.class);
//                //SpecialElasticSearchUtil.indexCreate(childs, OcBInvoiceNotice.class);
//                isExists = true;
//            } catch (IOException e) {
//                log.debug("创建索引 失败！" + e.getMessage());
//            }
//        } else {
//            isExists = true;
//        }
//        return isExists;
//    }

    /**
     * @Description 根据条件查询开票通知所有数据
     */
    public List<OcBInvoiceNotice> selectAllInvoiceNoticeByEs(JSONObject whereKeys, JSONObject filterKeys) {
        Integer range = 1000;
        String[] returnFileds = {"ID"};
        JSONArray orderKeys = new JSONArray();
        JSONObject order = new JSONObject();
        order.put("asc", true);
        order.put("name", "CREATIONDATE");
        orderKeys.add(order);

        List<OcBInvoiceNotice> invoiceNoticeList = Lists.newArrayList();
        esSearchInvoiceNotice(whereKeys, filterKeys, orderKeys, range, 0, returnFileds, invoiceNoticeList);
        return invoiceNoticeList;
    }

    /**
     * @Description 开票通知 ES递归查询
     */
    private void esSearchInvoiceNotice(JSONObject whereKeys, JSONObject filterKeys, JSONArray orderKeys
            , Integer range, Integer startIndex, String[] returnFileds
            , List<OcBInvoiceNotice> invoiceNoticeAllList) {
        List<Long> list = Lists.newArrayList();
        JSONObject search = ES4InvoiceNotice.findInvoiceNoticeByEs(whereKeys, filterKeys, orderKeys, range, startIndex, returnFileds);
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getLong(returnFileds[0]));
            }
        }
        if (!CollectionUtils.isEmpty(list)) {
            List<OcBInvoiceNotice> invoiceNoticeList = invoiceNoticeMapper.selectList(new QueryWrapper<OcBInvoiceNotice>()
                    .lambda().in(OcBInvoiceNotice::getId, list));
            invoiceNoticeAllList.addAll(invoiceNoticeList);
        }
        //返回的总数
        Integer total = search.getInteger("total");
        startIndex = startIndex + range;
        if (total > startIndex) {
            esSearchInvoiceNotice(whereKeys, filterKeys, orderKeys, range, startIndex, returnFileds, invoiceNoticeAllList);
        }
    }
}
