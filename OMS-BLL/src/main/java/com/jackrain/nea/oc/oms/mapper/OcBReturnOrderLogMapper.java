package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

@Mapper
@Component
public interface OcBReturnOrderLogMapper extends ExtentionMapper<OcBReturnOrderLog> {

    @Select("SELECT * FROM OC_B_RETURN_ORDER_LOG WHERE OC_B_RETURN_ORDER_ID=#{returnId} ORDER BY MODIFIEDDATE DESC LIMIT 1")
    OcBReturnOrderLog queryLatestByReturnId(@Param("returnId") Long returnId);


    @Update("UPDATE OC_B_RETURN_ORDER_LOG SET MODIFIEDDATE=NOW() WHERE OC_B_RETURN_ORDER_ID=#{returnId} AND `ID`=#{id}")
    int updateLatestLogModifiedDate(@Param("returnId") Long returnId, @Param("id") Long id);
}