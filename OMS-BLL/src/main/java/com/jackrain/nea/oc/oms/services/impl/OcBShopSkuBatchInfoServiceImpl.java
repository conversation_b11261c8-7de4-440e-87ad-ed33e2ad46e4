package com.jackrain.nea.oc.oms.services.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.oc.oms.mapper.OcBShopSkuBatchInfoMapper;
import com.jackrain.nea.oc.oms.model.table.OcBShopSkuBatchInfo;
import com.jackrain.nea.oc.oms.services.OcBShopSkuBatchInfoService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.BaseModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 店铺最新发货效期表 Service 实现类
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Slf4j
@Service
public class OcBShopSkuBatchInfoServiceImpl implements OcBShopSkuBatchInfoService {

    @Autowired
    private OcBShopSkuBatchInfoMapper ocBShopSkuBatchInfoMapper;

    @Autowired
    private BuildSequenceUtil buildSequenceUtil;

    @Override
    public OcBShopSkuBatchInfo getByShopCodeAndSku(String shopCode, String sku) {
        if (StringUtils.isBlank(shopCode) || StringUtils.isBlank(sku)) {
            return null;
        }
        try {
            return ocBShopSkuBatchInfoMapper.selectByShopCodeAndSku(shopCode, sku);
        } catch (Exception e) {
            log.error("查询店铺SKU批次信息异常，shopCode={}，sku={}", shopCode, sku, e);
            return null;
        }
    }

    @Override
    public OcBShopSkuBatchInfo getByShopIdAndSku(Long shopId, String sku) {
        if (shopId == null || StringUtils.isBlank(sku)) {
            return null;
        }
        try {
            return ocBShopSkuBatchInfoMapper.selectByShopIdAndSku(shopId, sku);
        } catch (Exception e) {
            log.error("查询店铺SKU批次信息异常，shopId={}，sku={}", shopId, sku, e);
            return null;
        }
    }

    @Override
    public List<OcBShopSkuBatchInfo> getByShopCode(String shopCode) {
        if (StringUtils.isBlank(shopCode)) {
            return null;
        }
        try {
            return ocBShopSkuBatchInfoMapper.selectByShopCode(shopCode);
        } catch (Exception e) {
            log.error("查询店铺SKU批次信息列表异常，shopCode={}", shopCode, e);
            return null;
        }
    }

    @Override
    public List<OcBShopSkuBatchInfo> getByShopId(Long shopId) {
        if (shopId == null) {
            return null;
        }
        try {
            return ocBShopSkuBatchInfoMapper.selectByShopId(shopId);
        } catch (Exception e) {
            log.error("查询店铺SKU批次信息列表异常，shopId={}", shopId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSkuBatchInfo(String shopCode, String sku, String produceDate) {
        if (StringUtils.isBlank(shopCode) || StringUtils.isBlank(sku) || StringUtils.isBlank(produceDate)) {
            log.warn("保存SKU批次信息参数无效，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate);
            return false;
        }

        try {
            // 查询是否已存在记录
            OcBShopSkuBatchInfo existingRecord = ocBShopSkuBatchInfoMapper.selectByShopCodeAndSku(shopCode, sku);

            if (existingRecord != null) {
                // 比较生产日期，只有新的生产日期比数据库中的更新才更新
                String existingProduceDate = existingRecord.getProduceDate();
                if (StringUtils.isNotBlank(existingProduceDate) && produceDate.compareTo(existingProduceDate) <= 0) {
                    log.debug("新生产日期不比数据库中的更新，跳过更新，shopCode={}，sku={}，新生产日期={}，数据库生产日期={}",
                            shopCode, sku, produceDate, existingProduceDate);
                    return false;
                }

                // 更新现有记录
                int updateCount = ocBShopSkuBatchInfoMapper.updateSkuBatchInfo(
                        shopCode, sku, produceDate,
                        SystemUserResource.ROOT_USER_ID,
                        SystemUserResource.ROOT_USER_NAME,
                        SystemUserResource.ROOT_USER_NAME
                );

                if (updateCount > 0) {
                    log.info("更新SKU批次信息成功，shopCode={}，sku={}，新生产日期={}，原生产日期={}",
                            shopCode, sku, produceDate, existingProduceDate);
                    return true;
                } else {
                    log.warn("更新SKU批次信息失败，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate);
                    return false;
                }
            } else {
                // 插入新记录
                OcBShopSkuBatchInfo newRecord = new OcBShopSkuBatchInfo();
                newRecord.setId(buildSequenceUtil.buildShopSkuBatchInfoSequenceId());
                newRecord.setShopCode(shopCode);
                newRecord.setSku(sku);
                newRecord.setProduceDate(produceDate);
                newRecord.setVersion(1L);

                BaseModelUtil.makeBaseCreateField(newRecord, SystemUserResource.getRootUser());

                int insertCount = ocBShopSkuBatchInfoMapper.insert(newRecord);

                if (insertCount > 0) {
                    log.info("插入SKU批次信息成功，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate);
                    return true;
                } else {
                    log.warn("插入SKU批次信息失败，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("保存或更新SKU批次信息异常，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveResult saveOrUpdateSkuBatchInfoWithResult(String shopCode, String sku, String produceDate) {
        if (StringUtils.isBlank(shopCode) || StringUtils.isBlank(sku) || StringUtils.isBlank(produceDate)) {
            log.warn("保存SKU批次信息参数无效，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate);
            return new SaveResult(false, "参数无效");
        }

        try {
            // 查询是否已存在记录
            OcBShopSkuBatchInfo existingRecord = ocBShopSkuBatchInfoMapper.selectByShopCodeAndSku(shopCode, sku);

            if (existingRecord != null) {
                // 比较生产日期，只有新的生产日期比数据库中的更新才更新
                String existingProduceDate = existingRecord.getProduceDate();
                if (StringUtils.isNotBlank(existingProduceDate) && produceDate.compareTo(existingProduceDate) <= 0) {
                    log.debug("新生产日期不比数据库中的更新，跳过更新，shopCode={}，sku={}，新生产日期={}，数据库生产日期={}",
                            shopCode, sku, produceDate, existingProduceDate);
                    return new SaveResult(false, "新生产日期不比数据库中的更新", existingProduceDate, produceDate);
                }

                // 更新现有记录
                int updateCount = ocBShopSkuBatchInfoMapper.updateSkuBatchInfo(
                        shopCode, sku, produceDate,
                        SystemUserResource.ROOT_USER_ID,
                        SystemUserResource.ROOT_USER_NAME,
                        SystemUserResource.ROOT_USER_NAME
                );

                if (updateCount > 0) {
                    log.info("更新SKU批次信息成功，shopCode={}，sku={}，新生产日期={}，原生产日期={}",
                            shopCode, sku, produceDate, existingProduceDate);
                    return new SaveResult(true, "更新成功", existingProduceDate, produceDate);
                } else {
                    log.warn("更新SKU批次信息失败，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate);
                    return new SaveResult(false, "更新失败", existingProduceDate, produceDate);
                }
            } else {
                // 插入新记录
                OcBShopSkuBatchInfo newRecord = new OcBShopSkuBatchInfo();
                newRecord.setId(buildSequenceUtil.buildShopSkuBatchInfoSequenceId());
                newRecord.setShopCode(shopCode);
                newRecord.setSku(sku);
                newRecord.setProduceDate(produceDate);
                newRecord.setVersion(1L);

                BaseModelUtil.makeBaseCreateField(newRecord, SystemUserResource.getRootUser());

                int insertCount = ocBShopSkuBatchInfoMapper.insert(newRecord);

                if (insertCount > 0) {
                    log.info("插入SKU批次信息成功，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate);
                    return new SaveResult(true, "插入成功", null, produceDate);
                } else {
                    log.warn("插入SKU批次信息失败，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate);
                    return new SaveResult(false, "插入失败", null, produceDate);
                }
            }
        } catch (Exception e) {
            log.error("保存或更新SKU批次信息异常，shopCode={}，sku={}，produceDate={}", shopCode, sku, produceDate, e);
            return new SaveResult(false, "系统异常: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSaveOrUpdateSkuBatchInfo(String shopCode, List<SkuBatchInfo> skuBatchInfoList) {
        if (StringUtils.isBlank(shopCode) || CollectionUtils.isEmpty(skuBatchInfoList)) {
            log.warn("批量保存SKU批次信息参数无效，shopCode={}，skuBatchInfoList size={}", 
                    shopCode, skuBatchInfoList == null ? 0 : skuBatchInfoList.size());
            return 0;
        }

        int successCount = 0;
        for (SkuBatchInfo skuBatchInfo : skuBatchInfoList) {
            try {
                if (saveOrUpdateSkuBatchInfo(shopCode, skuBatchInfo.getSku(), skuBatchInfo.getProduceDate())) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("批量保存SKU批次信息单条记录处理异常，shopCode={}，sku={}，produceDate={}", 
                        shopCode, skuBatchInfo.getSku(), skuBatchInfo.getProduceDate(), e);
            }
        }

        log.info("批量保存SKU批次信息完成，shopCode={}，总数={}，成功数={}", shopCode, skuBatchInfoList.size(), successCount);
        return successCount;
    }

    @Override
    public List<OcBShopSkuBatchInfo> getByShopCodeAndSkuList(String shopCode, List<String> skuList) {
        if (StringUtils.isBlank(shopCode) || CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        try {
            return ocBShopSkuBatchInfoMapper.selectByShopCodeAndSkuList(shopCode, skuList);
        } catch (Exception e) {
            log.error("批量查询店铺SKU批次信息异常，shopCode={}，skuList size={}", shopCode, skuList.size(), e);
            return null;
        }
    }

    @Override
    public List<OcBShopSkuBatchInfo> getByShopIdAndSkuList(Long shopId, List<String> skuList) {
        if (shopId == null || CollectionUtils.isEmpty(skuList)) {
            return null;
        }
        try {
            return ocBShopSkuBatchInfoMapper.selectByShopIdAndSkuList(shopId, skuList);
        } catch (Exception e) {
            log.error("批量查询店铺SKU批次信息异常，shopId={}，skuList size={}", shopId, skuList.size(), e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSkuBatchInfo(Long shopId, String shopCode, String shopTitle, String sku, String produceDate) {
        if (shopId == null || StringUtils.isBlank(shopCode) || StringUtils.isBlank(sku) || StringUtils.isBlank(produceDate)) {
            log.warn("保存SKU批次信息参数无效，shopId={}，shopCode={}，sku={}，produceDate={}",
                    shopId, shopCode, sku, produceDate);
            return false;
        }

        try {
            // 查询是否已存在记录（优先使用shopId查询）
            OcBShopSkuBatchInfo existingRecord = ocBShopSkuBatchInfoMapper.selectByShopIdAndSku(shopId, sku);

            if (existingRecord != null) {
                // 比较生产日期，只有新的生产日期比数据库中的更新才更新
                String existingProduceDate = existingRecord.getProduceDate();
                if (StringUtils.isNotBlank(existingProduceDate) && produceDate.compareTo(existingProduceDate) <= 0) {
                    log.debug("新生产日期不比数据库中的更新，跳过更新，shopId={}，sku={}，新生产日期={}，数据库生产日期={}",
                            shopId, sku, produceDate, existingProduceDate);
                    return false;
                }

                // 更新现有记录（同时更新店铺信息）
                existingRecord.setShopId(shopId);
                existingRecord.setShopCode(shopCode);
                existingRecord.setShopTitle(shopTitle);
                existingRecord.setProduceDate(produceDate);

                BaseModelUtil.makeBaseModifyField(existingRecord, SystemUserResource.getRootUser());

                int updateCount = ocBShopSkuBatchInfoMapper.updateById(existingRecord);

                if (updateCount > 0) {
                    log.info("更新SKU批次信息成功，shopId={}，shopCode={}，sku={}，新生产日期={}，原生产日期={}",
                            shopId, shopCode, sku, produceDate, existingProduceDate);
                    return true;
                } else {
                    log.warn("更新SKU批次信息失败，shopId={}，shopCode={}，sku={}，produceDate={}",
                            shopId, shopCode, sku, produceDate);
                    return false;
                }
            } else {
                // 插入新记录
                OcBShopSkuBatchInfo newRecord = new OcBShopSkuBatchInfo();
                newRecord.setId(buildSequenceUtil.buildShopSkuBatchInfoSequenceId());
                newRecord.setShopId(shopId);
                newRecord.setShopCode(shopCode);
                newRecord.setShopTitle(shopTitle);
                newRecord.setSku(sku);
                newRecord.setProduceDate(produceDate);
                newRecord.setVersion(1L);

                BaseModelUtil.initialBaseModelSystemField(newRecord, SystemUserResource.getRootUser());

                int insertCount = ocBShopSkuBatchInfoMapper.insert(newRecord);

                if (insertCount > 0) {
                    log.info("插入SKU批次信息成功，shopId={}，shopCode={}，shopTitle={}，sku={}，produceDate={}",
                            shopId, shopCode, shopTitle, sku, produceDate);
                    return true;
                } else {
                    log.warn("插入SKU批次信息失败，shopId={}，shopCode={}，sku={}，produceDate={}",
                            shopId, shopCode, sku, produceDate);
                    return false;
                }
            }
        } catch (Exception e) {
            log.error("保存或更新SKU批次信息异常，shopId={}，shopCode={}，sku={}，produceDate={}",
                    shopId, shopCode, sku, produceDate, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveResult saveOrUpdateSkuBatchInfoWithResult(Long shopId, String shopCode, String shopTitle, String sku, String produceDate, String receiverAddress) {
        if (shopId == null || StringUtils.isBlank(shopCode) || StringUtils.isBlank(sku) || StringUtils.isBlank(produceDate)) {
            log.warn("保存SKU批次信息参数无效，shopId={}，shopCode={}，sku={}，produceDate={}，receiverAddress={}",
                    shopId, shopCode, sku, produceDate, receiverAddress);
            return new SaveResult(false, "参数无效");
        }

        try {
            // 查询是否已存在记录（按店铺+SKU+收货地址的维度查询）
            OcBShopSkuBatchInfo existingRecord = ocBShopSkuBatchInfoMapper.selectByShopIdAndSkuAndAddress(shopId, sku, receiverAddress);

            if (existingRecord != null) {
                // 比较生产日期，只有新的生产日期比数据库中的更新才更新
                String existingProduceDate = existingRecord.getProduceDate();
                if (StringUtils.isNotBlank(existingProduceDate) && produceDate.compareTo(existingProduceDate) <= 0) {
                    log.debug("新生产日期不比数据库中的更新，跳过更新，shopId={}，sku={}，receiverAddress={}，新生产日期={}，数据库生产日期={}",
                            shopId, sku, receiverAddress, produceDate, existingProduceDate);
                    return new SaveResult(false, "新生产日期不比数据库中的更新", existingProduceDate, produceDate);
                }

                // 更新现有记录（同时更新店铺信息和收货人地址）
                existingRecord.setProduceDate(produceDate);
                BaseModelUtil.makeBaseModifyField(existingRecord, SystemUserResource.getRootUser());

                int updateCount = ocBShopSkuBatchInfoMapper.updateById(existingRecord);

                if (updateCount > 0) {
                    log.info("更新SKU批次信息成功，shopId={}，shopCode={}，sku={}，receiverAddress={}，新生产日期={}，原生产日期={}",
                            shopId, shopCode, sku, receiverAddress, produceDate, existingProduceDate);
                    return new SaveResult(true, "更新成功", existingProduceDate, produceDate);
                } else {
                    log.warn("更新SKU批次信息失败，shopId={}，shopCode={}，sku={}，receiverAddress={}，produceDate={}",
                            shopId, shopCode, sku, receiverAddress, produceDate);
                    return new SaveResult(false, "更新失败", existingProduceDate, produceDate);
                }
            } else {
                // 插入新记录
                OcBShopSkuBatchInfo newRecord = new OcBShopSkuBatchInfo();
                newRecord.setId(buildSequenceUtil.buildShopSkuBatchInfoSequenceId());
                newRecord.setShopId(shopId);
                newRecord.setShopCode(shopCode);
                newRecord.setShopTitle(shopTitle);
                newRecord.setSku(sku);
                newRecord.setProduceDate(produceDate);
                newRecord.setReceiverAddress(receiverAddress);
                newRecord.setVersion(1L);

                BaseModelUtil.makeBaseCreateField(newRecord, SystemUserResource.getRootUser());

                int insertCount = ocBShopSkuBatchInfoMapper.insert(newRecord);

                if (insertCount > 0) {
                    log.info("插入SKU批次信息成功，shopId={}，shopCode={}，shopTitle={}，sku={}，receiverAddress={}，produceDate={}",
                            shopId, shopCode, shopTitle, sku, receiverAddress, produceDate);
                    return new SaveResult(true, "插入成功", null, produceDate);
                } else {
                    log.warn("插入SKU批次信息失败，shopId={}，shopCode={}，sku={}，receiverAddress={}，produceDate={}",
                            shopId, shopCode, sku, receiverAddress, produceDate);
                    return new SaveResult(false, "插入失败", null, produceDate);
                }
            }
        } catch (Exception e) {
            log.error("保存或更新SKU批次信息异常，shopId={}，shopCode={}，sku={}，receiverAddress={}，produceDate={}",
                    shopId, shopCode, sku, receiverAddress, produceDate, e);
            return new SaveResult(false, "系统异常: " + e.getMessage());
        }
    }

    @Override
    public boolean existsByShopAndSkuAndAddress(Long shopId, String sku, String receiverAddress) {
        if (shopId == null || StringUtils.isBlank(sku)) {
            return false;
        }

        try {
            QueryWrapper<OcBShopSkuBatchInfo> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("SHOP_ID", shopId)
                       .eq("SKU", sku);

            // 如果收件人地址不为空，则加入地址条件
            if (StringUtils.isNotBlank(receiverAddress)) {
                queryWrapper.eq("RECEIVER_ADDRESS", receiverAddress);
            } else {
                // 如果收件人地址为空，则查询地址为空的记录
                queryWrapper.and(wrapper -> wrapper.isNull("RECEIVER_ADDRESS").or().eq("RECEIVER_ADDRESS", ""));
            }

            long count = ocBShopSkuBatchInfoMapper.selectCount(queryWrapper);

            log.debug("检查SKU批次信息存在性，shopId={}，sku={}，receiverAddress={}，结果={}",
                     shopId, sku, receiverAddress, count > 0);

            return count > 0;

        } catch (Exception e) {
            log.error("检查SKU批次信息存在性异常，shopId={}，sku={}，receiverAddress={}，异常信息={}",
                     shopId, sku, receiverAddress, e.getMessage(), e);
            return false;
        }
    }
}
