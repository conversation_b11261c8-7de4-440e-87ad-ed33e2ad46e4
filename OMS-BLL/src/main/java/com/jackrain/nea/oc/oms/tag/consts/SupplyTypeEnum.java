package com.jackrain.nea.oc.oms.tag.consts;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 轻供标
 * Author: RESET
 * Date: Created in 2020/7/10 13:48
 * Modified By:
 */
public enum SupplyTypeEnum {

    // 匹配策略类型
    NORMAL(0, "live", "普通商品"),
    PROXY(1, "sale by proxy", "代销轻供"),
    CONSIGN(2, " consign for sale", "寄售轻供");

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    SupplyTypeEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static SupplyTypeEnum fromValue(Integer v) {
        for (SupplyTypeEnum c : SupplyTypeEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

}
