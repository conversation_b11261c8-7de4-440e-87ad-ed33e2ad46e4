package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/9/3 下午4:38
 * @description 猫超直发mapper
 **/
@Component
@Mapper
public interface IpBAlibabaAscpOrderMapper extends ExtentionMapper<IpBAlibabaAscpOrder> {

    @Select("SELECT * FROM ip_b_alibaba_ascp_order WHERE biz_order_code = #{bizOrderCode} LIMIT 1")
    IpBAlibabaAscpOrder selectIpBAlibabaAscpOrderBybizOrderCode(@Param("bizOrderCode") String bizOrderCode);

    /**
     * 依据biz_order_code进行查询猫超直发订单数据
     *
     * @param orderNo 平台订单code
     * @return 猫超订单数据
     */
    @Select("SELECT * FROM ip_b_alibaba_ascp_order WHERE biz_order_code=#{orderNo}")
    IpBAlibabaAscpOrder selectAlibabaAscpOrderByTid(@Param("orderNo") String orderNo);

    /**
     * 猫超订单SQL创建器
     */
    class AlibabaAscpOrderSqlBuilder {
        /**
         * 创建更新订单转换状态SQL
         *
         * @param orderNo          订单编号
         * @param isTrans          转换状态
         * @param isUpdateTransNum 是否更新转换数量
         * @param remarks          转换备注信息
         * @param date             转换日期
         * @return 更新SQL语句
         */
        public String buildUpdateOrderTransSQL(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                                               @Param("isUpdateTransNum") boolean isUpdateTransNum,
                                               @Param("remarks") String remarks,
                                               @Param("date") Date date) {
            return new SQL() {
                {
                    UPDATE("ip_b_alibaba_ascp_order");
                    SET("istrans=#{isTrans} ");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                    }
                    SET("sysremark=#{remarks}");
                    SET("transdate = #{date}");
                    WHERE("biz_order_code=#{orderNo}");
                }
            }.toString();
        }
    }

    /**
     * 更新订单转换状态
     *
     * @param orderNo          订单编号
     * @param isTrans          转换状态
     * @param isUpdateTransNum 是否更新转换数量
     * @param remarks          转换备注信息
     * @param date             转换日期
     * @return 更新结果
     */
    @UpdateProvider(type = AlibabaAscpOrderSqlBuilder.class, method = "buildUpdateOrderTransSQL")
    int updateOrderIsTrans(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                           @Param("isUpdateTransNum") boolean isUpdateTransNum, @Param("remarks") String remarks, @Param("date") Date date);

}