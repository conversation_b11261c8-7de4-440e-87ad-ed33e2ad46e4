package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 发货后退款单
 *
 * <AUTHOR>
 * @date 2020/11/11 3:20 下午
 */
public class ES4ReturnAfSend {

    private ES4ReturnAfSend() {
    }

    /**
     * 业务：结算补偿操作
     * 按照退单查询已发货退款单分库键
     *
     * 根据退款状态，单据来源来查询平台退款单号
     *
     * RETURN_STATUS 退款状态
     * REFUND_ORDER_SOURCE_TYPE 单据来源 1 手动 2自动',
     * T_RETURN_ID 平台退款单号
     * @return List tReturnIds
     */
    public static List<String> findTReturnIdByToAcStatus(List<Integer> toAcStatus, int pageIndex, int pageSize) {
        List<String> tReturnIds = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(toAcStatus)) {
            // 起始行
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            // 查询条件
            JSONObject whereKey = new JSONObject();
            whereKey.put("TO_SETTLE_STATUS", toAcStatus);
            // 退款成功
            whereKey.put("RETURN_STATUS", ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
            // 单据类型,自动,非手工单
            whereKey.put("REFUND_ORDER_SOURCE_TYPE", RefundOrderSourceTypeEnum.AUTO.getValue());
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            orderKey.put("asc", true);
            orderKey.put("name", "MODIFIEDDATE");
            orderKeys.add(orderKey);

            JSONObject result = ElasticSearchUtil.search(
                    OcElasticSearchIndexResources.OC_B_RETURN_AF_SEND_INDEX_NAME,
                    OcElasticSearchIndexResources.OC_B_RETURN_AF_SEND_TYPE_NAME,
                    whereKey, null, orderKeys, pageSize, startIndex, new String[]{"T_RETURN_ID"});

            // 解析结果
            if (Objects.nonNull(result)) {
                if (result.containsKey("rowcount") && result.getInteger("rowcount") > 0) {
                    JSONArray rs = result.getJSONArray("data");
                    if (CollectionUtils.isNotEmpty(rs)) {
                        rs.forEach(o -> {
                            if (Objects.nonNull(o)) {
                                JSONObject jo = (JSONObject) o;
                                String tReturnId = jo.getString("T_RETURN_ID");

                                if (StringUtils.isNotEmpty(tReturnId)) {
                                    tReturnIds.add(tReturnId);
                                }
                            }
                        });
                    }
                }
            }
        }
        return tReturnIds;
    }
}
