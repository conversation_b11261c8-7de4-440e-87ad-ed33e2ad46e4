package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.mapper.task.CardAutoVoidTaskSql;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderNaikaVoidMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/3 14:55
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderNaikaVoidMapper extends ExtentionMapper<OcBOrderNaikaVoid> {

    @SelectProvider(type = CardAutoVoidTaskSql.class, method = "select4Void")
    List<OcBOrderNaikaVoid> selectCardAutoVoid(@Param(value = "limit") int limit,
                                               @Param(value = "taskTableName") String taskTableName);


    @Select("SELECT * FROM oc_b_order_naika_void WHERE oc_b_order_id=#{ocBOrderId} and oc_b_order_item_id=#{ocBOrderItemId} and isactive='Y' ")
    List<OcBOrderNaikaVoid> getByOcBOrderIdAndItemId(@Param("ocBOrderId") Long ocBOrderId, @Param("ocBOrderItemId") Long ocBOrderItemId);

    @Select("SELECT * FROM oc_b_order_naika_void WHERE oc_b_order_id=#{ocBOrderId} and isactive='Y' ")
    List<OcBOrderNaikaVoid> getByOcBOrderId(@Param("ocBOrderId") Long ocBOrderId);

    @Select("SELECT * FROM oc_b_order_naika_void WHERE oc_b_return_af_send_id=#{ocBReturnAfSendId} and isactive='Y' ")
    List<OcBOrderNaikaVoid> getByAfReturnId(@Param("ocBReturnAfSendId") Long ocBReturnAfSendId);
}
