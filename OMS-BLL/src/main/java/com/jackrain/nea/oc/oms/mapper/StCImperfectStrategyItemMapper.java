package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.model.table.StCImperfectStrategyItem;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 残次策略明细
 *
 * <AUTHOR>
 */
@Mapper
public interface StCImperfectStrategyItemMapper extends BaseMapper<StCImperfectStrategyItem> {

    @Select("SELECT * FROM st_c_imperfect_strategy_item WHERE imperfect_strategy_id = #{strategyId} and isactive = 'Y'")
    List<StCImperfectStrategyItem> selectByStrategyIdWithActive(Long strategyId);

    @Select("SELECT * FROM st_c_imperfect_strategy_item WHERE imperfect_strategy_id = #{strategyId}")
    List<StCImperfectStrategyItem> selectByStrategyIdWithOutActive(Long strategyId);

    @Select("<script> "
            + "SELECT * FROM st_c_imperfect_strategy_item WHERE isactive = 'Y' and imperfect_strategy_id "
            + "in <foreach item='item' index='index' collection='strategyIdList' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<StCImperfectStrategyItem> selectByStrategyIdList(@Param("strategyIdList") List<Long> strategyIdList);

    @Delete("DELETE FROM st_c_imperfect_strategy_item WHERE imperfect_strategy_id = #{strategyId}")
    int deleteByStrategyId(@Param("strategyId") Long strategyId);

}
