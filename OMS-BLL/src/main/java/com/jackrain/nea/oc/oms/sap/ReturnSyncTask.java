package com.jackrain.nea.oc.oms.sap;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/8/25
 */
@Data
@Accessors(chain = true)
public class ReturnSyncTask {

    /**
     * id
     */
    @J<PERSON><PERSON>ield(name = "ID")
    private Long id;

    /**
     * 退单id
     */
    @JSONField(name = "ORDER_ID")
    private Long orderId;

    /**
     * 状态
     */
    @JSONField(name = "STATUS")
    private int status;

    /**
     * 启用状态
     */
    @JSONField(name = "ISACTIVE")
    private String isactive;

    /**
     * 传互道状态
     */
    @JSONField(name = "IS_NEXT_TAO")
    private int isNextTao;

    /**
     * 传互道状态
     */
    @JSONField(name = "NEXT_TAO_STATUS")
    private int nextTaoStatus;

    /**
     * task表
     */
    @J<PERSON>NF<PERSON>(name = "TASK_TABLE_NAME")
    private String taskTableName;
}
