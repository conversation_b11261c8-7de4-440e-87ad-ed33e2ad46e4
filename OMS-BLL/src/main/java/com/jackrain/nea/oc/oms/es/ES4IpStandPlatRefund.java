package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.currency.StandplatRefundExt;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用退单
 *
 * <AUTHOR>
 * @date 2020/11/11 1:35 下午
 */
public class ES4IpStandPlatRefund {

    private ES4IpStandPlatRefund() {
    }

    /**
     * 业务：通用退单 发货后 转单服务
     * 根据转换状态 isTrans 查询 refundNo
     *
     * @param pageIndex 起始页
     * @param pageSize 每业条数
     *   isTrans 转换状态
     *  refundNo 退单号
     * @return List refundNo
     */
    public static List<String> findRefundNoByTransStatus(int pageIndex, int pageSize){
        List<String> refundNos = new ArrayList<>();

        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject whereKeys = new JSONObject();
        String[] returnFields = {"RETURN_NO"};
        whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());

        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        // 按照升序进行查询
        orderKey.put("asc", true);
        orderKey.put("name", "MODIFIEDDATE");
        orderKeys.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(StandplatRefundExt.IP_B_STANDPLAT_REFUND,
                StandplatRefundExt.IP_B_STANDPLAT_REFUND, whereKeys, null,
                orderKeys, pageSize, startIndex, returnFields);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                refundNos.add(jsonObject.getString("RETURN_NO"));
            }
        }

        return refundNos;
    }
}
