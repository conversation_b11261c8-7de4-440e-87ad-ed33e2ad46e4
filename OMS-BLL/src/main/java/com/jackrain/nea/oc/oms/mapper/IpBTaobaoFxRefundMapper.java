package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxRefund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface IpBTaobaoFxRefundMapper extends ExtentionMapper<IpBTaobaoFxRefund> {


//    @UpdateProvider(type = IpBTaobaoFxRefundMapper.TaobaoFxRefundSqlBuilder.class,method = "update")
//    int updateRefundOrder(JSONObject jsonObject);

    @Select("SELECT * FROM ip_b_taobao_fx_refund WHERE sub_order_id=#{orderNo} and istrans = 0")
    IpBTaobaoFxRefund selectTaobaoFxRefundByRefundId(@Param("orderNo") String orderNo);

    @Select("SELECT * FROM ip_b_taobao_fx_refund WHERE sub_order_id=#{orderNo}")
    IpBTaobaoFxRefund selectTaobaoFxRefundBySubOrderId(@Param("orderNo") String orderNo);
}