package com.jackrain.nea.oc.oms.matcher.live.impl;

import com.jackrain.nea.oc.oms.matcher.live.ILiveMatcher;
import com.jackrain.nea.oc.oms.matcher.live.LiveStrategyTypeEnum;
import com.jackrain.nea.oc.oms.matcher.vo.ParamInputVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Description： 按备注匹配
 * Author: RESET
 * Date: Created in 2020/6/15 21:43
 * Modified By:
 */
@Component
public class LiveMatcherByRemark implements ILiveMatcher {

    /**
     * 解析匹配
     *
     * @param inputVO
     */
    @Override
    public boolean doMatch(ParamInputVO inputVO) {
        String ruleContext = inputVO.getRuleContext();
        String targetText = inputVO.getOriginalRemark();

        if (Objects.nonNull(ruleContext) && Objects.nonNull(targetText)) {
            return targetText.indexOf(ruleContext) >= 0;
        }

        return false;
    }

    /**
     * 策略类型
     *
     * @return
     */
    @Override
    public Integer getLiveStrategyType() {
        return LiveStrategyTypeEnum.REMARK.getValue();
    }

}
