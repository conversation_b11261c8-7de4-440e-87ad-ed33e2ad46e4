package com.jackrain.nea.oc.oms.config;

/**
 * @Auther: chenhao
 * @Date: 2022-07-08 11:32
 * @Description:
 */


public class OmsDeliveryOrderConfig {

    /**
     * 发货确认单中间表状态 0未处理 1成功 -1失败
     */
    public final static Integer TOBEDELIVERYORDER_CONFIRM_TASK_ZENO = 0;
    public final static Integer TOBEDELIVERYORDER_CONFIRM_TASK_SUCCESS = 1;
    public final static Integer TOBEDELIVERYORDER_CONFIRM_TASK_FIAL = -1;

    /**
     * 最大失败次数
     */
    public final static Integer FAIL_COUNT = 6;


    /**
     * 退货入库中间表 订单类型
     */
    public final static String OREDER_TYPE_B2BRK = "B2BRK";
    public final static String OREDER_TYPE_THRK = "THRK";
    public final static String OREDER_TYPE_GCXT = "GCXT";


}
