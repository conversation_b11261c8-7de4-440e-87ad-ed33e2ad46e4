package com.jackrain.nea.oc.oms.services.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.AppendObjectRequest;
import com.aliyun.oss.model.AppendObjectResult;
import com.aliyun.oss.model.CannedAccessControlList;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.Date;
import java.util.Objects;

/**
 * <p>oss服务</p>
 *
 * <AUTHOR>
 * @since 2023/1/16
 */
@Component
@Slf4j
public class OssService {

    @Value("${r3.oss.bucketName}")
    private String bucketName;

    @Value("${r3.oss.endpoint}")
    private String endpoint;

    @Resource
    private OSS ossClient;

    /**
     * 普通下载文件
     *
     * @param objectName 文件名称
     * @return 文件流
     */
    public InputStream downloadFile(String objectName) {
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, objectName);
        OSSObject ossObject = ossClient.getObject(getObjectRequest);
        return ossObject.getObjectContent();
    }


    /**
     * 上传文件,上传完oss自动会将inputStream 关闭
     *
     * @param inputStream
     * @param objectKey    文件路径
     * @param isPublicRead 是否公共读
     * @return 可以直接访问的http路径
     */
    public String uploadFile(InputStream inputStream, String objectKey, Boolean isPublicRead) {
        if (Objects.isNull(inputStream)) {
            throw new NDSException("文件为空");
        }
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setHeader("Pragma", "no-cache");
            // 设置公有读权限
            if (isPublicRead) {
                objectMetadata.setObjectAcl(CannedAccessControlList.PublicRead);
            }
            ossClient.putObject(bucketName, objectKey, inputStream, objectMetadata);
            return  "https://" + bucketName + "."
                    + endpoint + "/" + objectKey;
        } catch (Exception e) {
            log.error(LogUtil.format("文件上传失败"), e);
            throw new NDSException("文件上传失败");
        }
    }


    /**
     * 是否存在该文件
     */
    public boolean existObject(String objectName) {
        return ossClient.doesObjectExist(bucketName, objectName);
    }

    /**
     * 删除文件
     */
    public boolean deleteObject(String objectName) {
        try {
            ossClient.deleteObject(bucketName, objectName);
            return true;
        } catch (Exception e) {
            log.error(LogUtil.format("删除oss文件失败,objectKey:{}"), objectName, e);
            return false;
        }
    }

    /**
     * 获取文件大小
     */
    public Long getFileSize(String objectKey) {
        ObjectMetadata metadata = ossClient.headObject(bucketName, objectKey);
        return metadata.getContentLength();
    }
}
