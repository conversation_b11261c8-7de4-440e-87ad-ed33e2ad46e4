package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.MilkCardAmountOffsetOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName MilkCardAmountOffsetOrderMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/31 20:45
 * @Version 1.0
 */
@Mapper
@Component
public interface MilkCardAmountOffsetOrderMapper extends ExtentionMapper<MilkCardAmountOffsetOrder> {


    @Select("<script> "
            + "select * FROM milk_card_amount_offset_order where id in "
            + "<foreach item='item' index='index' collection='idList' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by id "
            + "</script>")
    List<MilkCardAmountOffsetOrder> selectByIdList(@Param("idList") List<Long> idList);

    @Update("<script> "
            + "UPDATE milk_card_amount_offset_order SET OC_B_SAP_SALES_DATA_GATHER_ID=0\n" +
            ",collect_status='0'\n" +
            ",MODIFIEDDATE=now()\n" +
            "where id in "
            + "<foreach item='ids' index='index' collection='ids' open='(' separator=',' close=')'>"
            + " #{ids} "
            + "</foreach>"
            + "</script>")
    Integer recordCount(@Param("ids") List<Long> ids);

    @Select("<script>" +
            "select * from milk_card_amount_offset_order where oc_b_sap_sales_data_gather_id in"
            + "<foreach item='ids' index='index' collection='ids' open='(' separator=',' close=')'>"
            + " #{ids} "
            + "</foreach>"
            + "</script>")
    List<MilkCardAmountOffsetOrder> selectMilkInfo(@Param("ids") List<Long> ids);

    @SelectProvider(type = MilkCardAmountOffsetOrderSql.class, method = "selectByNodeSql")
    List<MilkCardAmountOffsetOrder> selectSalesDataIdList(@Param("taskTableName") String taskTableName,
                                                          @Param("limit") int limit,
                                                          @Param("queryDate") String queryDate);

    @Slf4j
    class MilkCardAmountOffsetOrderSql {
        public String selectByNodeSql(@Param("taskTableName") String taskTableName,
                                      @Param("limit") int limit,
                                      @Param("queryDate") String queryDate) {
            StringBuffer sql = new StringBuffer();
            StringBuffer limitStr = new StringBuffer(" LIMIT ");
            limitStr.append(limit);
            sql.append("select * from ")
                    .append(taskTableName)
                    .append(" where collect_status= 0 ")
                    .append(" and OC_B_SAP_SALES_DATA_GATHER_ID = 0 and isactive='Y'")
                    .append(" and ifnull(collect_code, '') <> ''")
                    .append(" ")
                    .append(limitStr);
            return sql.toString();
        }
    }
}
