package com.jackrain.nea.oc.oms.services.patrol;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @author: 李杰
 * @since: 2019/6/4
 * create at : 2019/6/4 13:43
 */
@Slf4j
@Component
public class GetorderIdsService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;

    /**
     * 主表明细金额不等的订单
     *
     * @return
     */
    public List getAmtUneqItemsIds() {
        List list = new ArrayList();

        Long now = System.currentTimeMillis() - 300000;
        String dastr = "~" + now;

        JSONArray arrayObj = ES4Order.getIdsByOrderStatusAndFilterByMfDate(OmsOrderStatus.UNCONFIRMED.toInteger(),
                dastr, 1000);

        for (Object obj : arrayObj) {
            JSONObject jsonObject = (JSONObject) obj;
            Long id = jsonObject.getLong("ID");
            OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
            if (ocBOrder == null) {
                continue;
            }
            QueryWrapper<OcBOrderItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("oc_b_order_id", id).ne("refund_status", 6);
            List<OcBOrderItem> ocBPurchaseList = ocBorderItemMapper.selectList(queryWrapper);
            //商品金额(明细标准价*数量之和)
            BigDecimal priceListSum = new BigDecimal("0");
            //商品优惠金额（明细优惠金额之和）
            BigDecimal amtDiscountSum = new BigDecimal("0");
            //调整金额（明细调整金额之和）
            BigDecimal adjustAmtSum = new BigDecimal("0");
            //总金额（明细成交金额之和）
            BigDecimal realAmtSum = new BigDecimal("0");
            //订单优惠金额（明细平摊金额之和）
            BigDecimal orderSplitAmtSum = new BigDecimal("0");
            for (OcBOrderItem ocBOrderItem : ocBPurchaseList) {
                if (ocBOrderItem.getPriceList() == null) {
                    ocBOrderItem.setPriceList(new BigDecimal("0"));
                }
                if (ocBOrderItem.getAmtDiscount() == null) {
                    ocBOrderItem.setAmtDiscount(new BigDecimal("0"));
                }
                if (ocBOrderItem.getAdjustAmt() == null) {
                    ocBOrderItem.setAdjustAmt(new BigDecimal("0"));
                }
                if (ocBOrderItem.getRealAmt() == null) {
                    ocBOrderItem.setRealAmt(new BigDecimal("0"));
                }
                if (ocBOrderItem.getOrderSplitAmt() == null) {
                    ocBOrderItem.setOrderSplitAmt(new BigDecimal("0"));
                }
                priceListSum = priceListSum.add(ocBOrderItem.getPriceList().multiply(ocBOrderItem.getQty()));
                amtDiscountSum = amtDiscountSum.add(ocBOrderItem.getAmtDiscount());
                adjustAmtSum = adjustAmtSum.add(ocBOrderItem.getAdjustAmt());
                realAmtSum = realAmtSum.add(ocBOrderItem.getRealAmt());
                orderSplitAmtSum = orderSplitAmtSum.add(ocBOrderItem.getOrderSplitAmt());
            }
            if (priceListSum.compareTo(ocBOrder.getProductAmt()) != 0 || amtDiscountSum.compareTo(ocBOrder.getProductDiscountAmt()) != 0
                    || adjustAmtSum.compareTo(ocBOrder.getAdjustAmt()) != 0 || realAmtSum.compareTo(ocBOrder.getOrderAmt()) != 0
                    || orderSplitAmtSum.compareTo(ocBOrder.getOrderDiscountAmt()) != 0) {
                list.add(id);
            }
        }
        return list;
    }

    /**
     * 订单总金额和已收金额不等的订单
     *
     * @return
     */
    public List getReceiveUneqAmtIds() {
        List list = new ArrayList();

        JSONArray arrayObj = ES4Order.getIdsByOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());

        for (Object obj : arrayObj) {
            JSONObject jsonObject = (JSONObject) obj;
            Long id = jsonObject.getLong("ID");
            OcBOrder ocBOrder = ocBOrderMapper.selectById(id);
            if (ocBOrder == null) {
                continue;
            }
            if (ocBOrder.getOrderAmt().compareTo(ocBOrder.getReceivedAmt()) != 0) {
                list.add(id);
            }
        }
        return list;
    }

    /**
     * 平台单号重复的订单
     *
     * @return
     */
    public List getTidRepeatIds() {
        List list = new ArrayList();
        List tids = new ArrayList();

        //当天0点时间
        Date todayStartTime = getTodayStartTime();
        Long ts = todayStartTime.getTime();
        //当前时间
        Long l = System.currentTimeMillis();
        String dastr = ts + "~" + l;

        JSONObject objJsonList = ES4Order.findTidGroupByCreationDate(dastr);

        if (null != objJsonList) {
            Object data = objJsonList.get("data");
            List results = JSONArray.parseArray(data.toString());
            for (Object result : results) {
                JSONObject json = JSON.parseObject(result.toString());
                JSONObject key = json.getJSONObject("key");
                String tid = key.getString("TID");
                tids.add(tid);
            }
        }

        for (Object tid : tids) {
            String tid1 = (String) tid;
            JSONArray arrayObj = ES4Order.findIdByTid(tid1);
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                Long id = jsonObject.getLong("ID");
                list.add(id + "," + tid);
            }
        }
        return list;
    }

    public static Date getTodayStartTime() {
        Calendar todayStart = Calendar.getInstance();
        todayStart.set(Calendar.HOUR_OF_DAY, 0);
        todayStart.set(Calendar.MINUTE, 0);
        todayStart.set(Calendar.SECOND, 0);
        return todayStart.getTime();
    }

}
