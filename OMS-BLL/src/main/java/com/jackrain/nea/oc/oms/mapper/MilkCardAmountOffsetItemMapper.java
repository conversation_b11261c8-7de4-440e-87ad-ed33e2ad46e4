package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.MilkCardAmountOffsetItemResult;
import com.jackrain.nea.oc.oms.model.table.MilkCardAmountOffsetItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName MilkCardAmountOffsetItemMapper
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/31 20:44
 * @Version 1.0
 */
@Mapper
@Component
public interface MilkCardAmountOffsetItemMapper extends ExtentionMapper<MilkCardAmountOffsetItem> {
    //批量查询明细
    @Select("<script>\n" +
            "select a.id,\n" +
            "       a.card_code,\n" +
            "       a.ps_c_sku_id,\n" +
            "       a.ps_c_sku_ecode,\n" +
            "       a.ps_c_pro_id,\n" +
            "       a.ps_c_pro_ecode,\n" +
            "       a.ps_c_pro_ename,\n" +
            "       a.row_item_type,\n" +
            "       a.qty,\n" +
            "       a.unit,\n" +
            "       a.offset_price,\n" +
            "       a.item_type,\n" +
            "       a.offset_order_id,\n" +
            "       b.collect_code,\n" +
            "       b.sum_type" +
            " from milk_card_amount_offset_item a, milk_card_amount_offset_order b where b.isactive = 'Y'\n" +
            " and a.isactive = 'Y' and a.offset_order_id = b.id" +
            " and b.id in\n" +
            " <foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>" +
            " #{item}\n" +
            " </foreach>\n" +
            "</script>")
    List<MilkCardAmountOffsetItemResult> batchSelectItem(@Param("ids")List<Long> ids);

    //批量查询明细
    @Select("<script>\n" +
            "select a.id,\n" +
            "       a.card_code,\n" +
            "       a.ps_c_sku_id,\n" +
            "       a.ps_c_sku_ecode,\n" +
            "       a.ps_c_pro_id,\n" +
            "       a.ps_c_pro_ecode,\n" +
            "       a.ps_c_pro_ename,\n" +
            "       a.row_item_type,\n" +
            "       a.qty,\n" +
            "       a.unit,\n" +
            "       a.offset_price,\n" +
            "       a.item_type,\n" +
            "       a.offset_order_id,\n" +
            "       a.factory_code\n" +
            " from milk_card_amount_offset_item a where \n" +
            " a.isactive = 'Y'\n" +
            " and a.offset_order_id in\n" +
            " <foreach collection = 'ids' item = 'item' open = '(' separator = ',' close = ')'>" +
            " #{item}\n" +
            " </foreach>\n" +
            "</script>")
    List<MilkCardAmountOffsetItemResult> batchSelectItems(@Param("ids")List<Long> ids);
}
