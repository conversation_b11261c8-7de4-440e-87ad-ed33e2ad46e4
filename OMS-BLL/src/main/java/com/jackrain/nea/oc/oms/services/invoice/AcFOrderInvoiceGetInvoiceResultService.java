package com.jackrain.nea.oc.oms.services.invoice;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.service.InvoiceApplyService;
import com.jackrain.nea.ac.service.OrderInvoiceGetResultTaskService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.api.HubInvoicingCmd;
import com.jackrain.nea.hub.model.HXInvoicingModel.DataType;
import com.jackrain.nea.hub.model.HXInvoicingModel.JsonRootObject;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultData;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultType;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 */
@Slf4j
@Component
public class AcFOrderInvoiceGetInvoiceResultService {
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private AcFOrderInvoiceMapper acOrderInvoiceMapper;

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Reference(group = "hub", version = "1.0")
    private HubInvoicingCmd hubInvoicingCmd;

    @Autowired
    private InvoiceLogService invoiceLogService;

    @Autowired
    private OrderInvoiceGetResultTaskService getResultTaskService;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        request.setR3(true);
        AcFOrderInvoiceGetInvoiceResultService service = ApplicationContextHandle.getBean(this.getClass());
        return service.getInvoiceResult(request);
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder getInvoiceResult(SgR3BaseRequest request) {
        log.info(LogUtil.format("AcFOrderInvoiceGetInvoiceResultService.getInvoiceResult.request={}", "info"),
                JSONObject.toJSONString(request));
        List<Long> batchObjIds = R3ParamUtils.getBatchObjIds(request);
        // 存储错误的Map
        Map<Long, Object> errorMap = new HashMap<>(batchObjIds.size());
        for (Long objId : batchObjIds) {

            String lockRedisKey = InvoiceConst.AC_F_ORDER_INVOICE + ":" + request.getObjId();
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    AcFOrderInvoice acOrderInvoice = acOrderInvoiceMapper.selectById(objId);
                    if (acOrderInvoice == null) {
                        errorMap.put(objId, "当前记录已不存在！");
                        continue;
                    }
                    //20220918调整逻辑如果【开票状态】!=开票成功状态，则提示：“选择的订单发票状态不能获取开票结果！”
                    if (InvoiceConst.InvoiceStatus.NOT_INVOICE.equals(acOrderInvoice.getInvoiceStatus())) {
                        errorMap.put(objId, "选择的订单发票状态不能获取开票结果！");
                        continue;
                    }
                    JsonRootObject jsonRootObject = new JsonRootObject();
                    jsonRootObject.setDocNum(acOrderInvoice.getBillNo());
                    ResultType resultType = hubInvoicingCmd.queryInvoicing(jsonRootObject);
                    log.info(LogUtil.format("AcFOrderInvoiceGetInvoiceResultService.getInvoiceResult.resultType={}",
                            "AcFOrderInvoiceGetInvoiceResultService.queryInvoicing"),
                            JSONObject.toJSONString(resultType));
                    /*默认-老版本的返回信息*/
                    String logMessage = resultType.getMsg();
                    if (InvoiceConst.getInvoiceResultApiCode.SUCCESS.equals(resultType.getCode())) {
                        /*老版本-成功*/
                        logMessage = invoiceResultSuccessV1(request, acOrderInvoice, resultType);
                    } else if ("200".equals(resultType.getCode())) {
                        /*新版本-成功*/
                        logMessage = invoiceResultSuccessV2(request, acOrderInvoice, resultType);
                    } else if (InvoiceConst.getInvoiceResultApiCode.FAIL.equals(resultType.getCode())) {
                        /*老版本-失败*/
                        if (StringUtils.isNotBlank(resultType.getMsg()) && resultType.getMsg().contains("暂未")) {
                            invoiceLogService.addUserOrderLog(acOrderInvoice.getId(), "获取开票结果", logMessage, request.getLoginUser());
                            continue;
                        }
                        acOrderInvoice.setInvoiceStatus(InvoiceConst.InvoiceStatus.INVOICE_FAIL);
                        acOrderInvoice.setFailReason(resultType.getMsg());
                        logMessage = "获取开票结果失败:" + resultType.getMsg();
                        acOrderInvoiceMapper.updateById(acOrderInvoice);
                    } else {
                        /*其他-新版本-失败「新版本的失败码：500」*/
                        if (StringUtils.isNotBlank(resultType.getMessage()) && resultType.getMessage().contains("暂未")) {
                            invoiceLogService.addUserOrderLog(acOrderInvoice.getId(), "获取开票结果", logMessage, request.getLoginUser());
                            continue;
                        }
                        acOrderInvoice.setInvoiceStatus(InvoiceConst.InvoiceStatus.INVOICE_FAIL);
                        acOrderInvoice.setFailReason(resultType.getMessage());
                        logMessage = "获取开票结果失败:" + resultType.getMessage();
                        acOrderInvoiceMapper.updateById(acOrderInvoice);
                    }
                    invoiceLogService.addUserOrderLog(acOrderInvoice.getId(), "获取开票结果", logMessage,
                            request.getLoginUser());


                } else {
                    errorMap.put(objId, "当前发票处于锁定状态！");
                }
            } catch (Exception e) {
                log.error(LogUtil.format("AcFOrderInvoiceCancelService.getInvoiceResult.error={}", "error"),
                        Throwables.getStackTraceAsString(e));
                throw new NDSException("发票管理获取开票结果异常!");
            } finally {
                redisLock.unlock();
            }
        }
        return R3ParamUtils.getExcuteValueHolder(batchObjIds.size(), errorMap);
    }

    private String invoiceResultSuccessV1(SgR3BaseRequest request, AcFOrderInvoice acOrderInvoice, ResultType resultType) throws ParseException {
        String logMessage;
        DataType data = resultType.getData();
        String invoiceUrl = Objects.isNull(data) ? "" : data.getEPdfUrl();
        String eXmlUrl = Objects.isNull(data) ? "" : data.geteXmlUrl();
        acOrderInvoice.setInvoiceLinkAddress(invoiceUrl);
        acOrderInvoice.setEXmlUrl(eXmlUrl);
        acOrderInvoice.setInvoiceDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(resultType.getData().getBillGdate()));
        acOrderInvoice.setInvoiceStatus(OmsParamConstant.TWO);
        acOrderInvoice.setInvoiceCode(resultType.getData().getGoldtaxCode());
        acOrderInvoice.setInvoiceNumber(resultType.getData().getGoldtaxNum());
        acOrderInvoice.setFailReason(""); // 开票成功将失败信息置空
        BaseModelUtil.setupUpdateParam(acOrderInvoice, request.getLoginUser());
        logMessage = "获取开票结果成功";
        // 将原蓝票更新为已红冲
        if (InvoiceConst.TicketType.RED.equals(acOrderInvoice.getTicketType()) && Objects.nonNull(acOrderInvoice.getBlueTicketId())) {
            UpdateWrapper<AcFOrderInvoice> blueInvoiceUpdate = new UpdateWrapper<>();
            blueInvoiceUpdate.lambda().eq(AcFOrderInvoice::getId, acOrderInvoice.getBlueTicketId()).set(AcFOrderInvoice::getRedRushStatus, InvoiceConst.RedRushStatus.RED_RUSHED);
            acOrderInvoiceMapper.update(null, blueInvoiceUpdate);
            if (Objects.nonNull(acOrderInvoice.getInvoiceApplyId()) && acOrderInvoice.getInvoiceApplyId() > 0) {
                invoiceApplyService.updateInvoiceApplyItemTidSuffix(acOrderInvoice.getInvoiceApplyId());
            }
        }
        acOrderInvoiceMapper.updateById(acOrderInvoice);
        return logMessage;
    }

    private String invoiceResultSuccessV2(SgR3BaseRequest request, AcFOrderInvoice acOrderInvoice, ResultType resultType) throws ParseException {
        String logMessage;
        ResultData data = resultType.getResult();
        String invoiceUrl = Objects.isNull(data) ? "" : data.getPdfUrl();
        String eXmlUrl = Objects.isNull(data) ? "" : data.getXmlUrl();
        acOrderInvoice.setInvoiceLinkAddress(invoiceUrl);
        acOrderInvoice.setEXmlUrl(eXmlUrl);
        acOrderInvoice.setInvoiceDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(data.getBillGdate()));
        acOrderInvoice.setInvoiceStatus(OmsParamConstant.TWO);
        acOrderInvoice.setInvoiceCode(data.getGoldtaxCode());
        acOrderInvoice.setInvoiceNumber(data.getGoldtaxNum());
        acOrderInvoice.setFailReason(""); // 开票成功将失败信息置空
        BaseModelUtil.setupUpdateParam(acOrderInvoice, request.getLoginUser());
        logMessage = "获取开票结果成功";
        // 将原蓝票更新为已红冲
        if (InvoiceConst.TicketType.RED.equals(acOrderInvoice.getTicketType()) && Objects.nonNull(acOrderInvoice.getBlueTicketId())) {
            UpdateWrapper<AcFOrderInvoice> blueInvoiceUpdate = new UpdateWrapper<>();
            blueInvoiceUpdate.lambda().eq(AcFOrderInvoice::getId, acOrderInvoice.getBlueTicketId()).set(AcFOrderInvoice::getRedRushStatus, InvoiceConst.RedRushStatus.RED_RUSHED);
            acOrderInvoiceMapper.update(null, blueInvoiceUpdate);
            if (Objects.nonNull(acOrderInvoice.getInvoiceApplyId()) && acOrderInvoice.getInvoiceApplyId() > 0) {
                invoiceApplyService.updateInvoiceApplyItemTidSuffix(acOrderInvoice.getInvoiceApplyId());
            }
        }
        acOrderInvoiceMapper.updateById(acOrderInvoice);
        return logMessage;
    }

}
