package com.jackrain.nea.oc.oms.matcher;

import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.matcher.parser.LiveMatchInfoParserFactory;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Description： 解析匹配管理器
 * Author: RESET
 * Date: Created in 2020/6/16 14:23
 * Modified By:
 */
@Component
public class MatchStrategyManager {

    private OmsSystemConfig omsSystemConfig;

    @Autowired
    public MatchStrategyManager(OmsSystemConfig omsSystemConfig) {
        this.omsSystemConfig = omsSystemConfig;
    }

    /**
     * 匹配
     *
     * @param originalOrderRelation
     * @param channelType
     * @param order
     * @param items
     * @param <T>
     */
    public <T> void match(T originalOrderRelation, ChannelType channelType, OcBOrder order, List<OcBOrderItem> items) {
        // 解析，信息提取
        boolean toBOrderPt = OmsBusinessTypeUtil.isToBOrderPt(order);
        boolean toCOrderPt = OmsBusinessTypeUtil.isToCOrderPt(order);
        if (!(toBOrderPt || toCOrderPt) && omsSystemConfig.isTransferLiveStrategyEnabled()) {
            LiveMatchInfoParserFactory.getInstance().getParser(channelType).doParser(originalOrderRelation, order, items);
        }
    }

    /**
     * 取个实例
     *
     * @return
     */
    public static MatchStrategyManager getInstance() {
        return ApplicationContextHandle.getBean(MatchStrategyManager.class);
    }

    /**
     * 极简
     *
     * @return
     */
    public static MatchStrategyManager get() {
        return getInstance();
    }

}
