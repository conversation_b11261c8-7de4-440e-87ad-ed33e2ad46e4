package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ip.model.result.OrderCreateBaseResult;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.extmodel.OcBOrderExtToHya;
import com.jackrain.nea.oc.oms.model.relation.TaskParam;
import com.jackrain.nea.oc.oms.model.request.UpdateReturnOrderRequest;
import com.jackrain.nea.oc.oms.model.result.OrderReturnResult;
import com.jackrain.nea.oc.oms.model.result.QueryReturnOrderResult;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * 转单主表mapper
 */

@Mapper
@Component
public interface OcBReturnOrderMapper extends ExtentionMapper<OcBReturnOrder> {

    /**
     * 根据物流单号查询退换单
     * @param logisticsCode 物流单号
     * @return
     */
    @Select("SELECT * FROM OC_B_RETURN_ORDER WHERE LOGISTICS_CODE = #{logisticsCode} AND ISACTIVE = 'Y'")
    List<OcBReturnOrder> selectByLogisticsCode(@Param("logisticsCode") String logisticsCode);

    @Update("<script> "
            + "update oc_b_return_order set IS_SEND_TMS_LOGISTIC=#{isSendTmsLogistic} where id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int batchUpdateIsSendTmsLogistic(@Param("isSendTmsLogistic") String isSendTmsLogistic,  @Param("ids") List<Long> ids);

    @Update("<script> "
            + "update oc_b_return_order ro set IS_SEND_TMS_LOGISTIC=#{isSendTmsLogistic} "+
            "<set>" +
            " ro.LOGISTICS_TRACE_ID = " +
            "<trim prefix='case ro.id' suffix='end,'>" +
            " <foreach collection='returnOrderList' item='returnOrder' index='index' separator=' '>" +
            "<if test = 'returnOrder.logisticsTraceId != null'>" +
            " when #{returnOrder.id} then #{returnOrder.logisticsTraceId}" +
            "</if>" +
            "</foreach>" +
            "</trim>"+
            "</set>"
            + "  where ro.id in <foreach item='returnOrder' index='index' collection='returnOrderList' open='(' separator=',' close=')'> #{returnOrder.id} </foreach> "
            + "</script>")
    int batchUpdateLogisticsTraceId(@Param("isSendTmsLogistic") String isSendTmsLogistic, @Param("returnOrderList") List<OcBReturnOrder> returnOrderList);

    /**
     * 修改审核失败次数
     *
     * @param id
     * @param auditFail
     * @return int
     * @Date 2021/9/9 22:54
     */
    @Update(" UPDATE OC_B_RETURN_ORDER SET AUDIT_FAIL = #{auditFail} where id = #{id}")
    int updateAuditFail(@Param("id") Long id, @Param("auditFail") Integer auditFail);

    @Update("<script> "
            + "update oc_b_return_order set is_towms=#{toWmsStatus},to_drp_status=#{toDrpStatus} where id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateToDrpStatusSuccess(@Param("toWmsStatus") int toWmsStatus, @Param("toDrpStatus") String toDrpStatus, @Param("ids") List<Long> ids);

    @Update("UPDATE oc_b_return_order SET logistics_code = #{logisticsCode}, cp_c_logistics_id=#{cpCLogisticsId} WHERE ID=#{id}")
    int updateReturnOrderLogisticsInfo(@Param("logisticsCode") String logisticsCode, @Param("cpCLogisticsId") Long cpCLogisticsId, @Param("id") Long id);

    @Select("select cp_c_shop_id from oc_b_return_order where seller_nick=#{seller_nick}")
    String findShopId(@Param("seller_nick") String seller_nick);

    @Update("UPDATE oc_b_return_order SET oc_b_refund_in_id = #{refundInId} WHERE ID=#{id}")
    int updateReturnOrderRefundInId(@Param("refundInId") Long refundInId, @Param("id") Long id);

    /**
     * 根据换货单号获取换货单单据信息
     *
     * @param disputeId 换货单号
     * @return 返回退单结果
     */
    @Select("select * from  oc_b_return_order  where tb_dispute_id=#{id} and isactive='Y'")
    List<OcBReturnOrder> selectByDisputeId(@Param("id") Long disputeId);

    @Select("select * from  oc_b_return_order  where bill_no=#{billNo} and isactive='Y'")
    OcBReturnOrder selectByBillNo(@Param("billNo") String billNo);

    @Select("select * from  oc_b_return_order  where id=#{id} ")
    JSONObject selectJsonById(@Param("id") String id);

    @SelectProvider(type = OcBReturnOrderMapper.OcReturnOrder.class, method = "select")
    List<OrderReturnResult> findByIds(@Param("join") String join, @Param("isActive") String isActive);

    @UpdateProvider(type = OcReturnOrder.class, method = "update")
    int updateByid(JSONObject jsonObject);

    @SelectProvider(type = OcBReturnOrderMapper.OcReturnOrder.class, method = "selectJoin")
    List<OcBReturnOrder> findByJoin(String selectJoin);

    @Select("select * from oc_b_return_order where id=#{origRetunOrderId}")
    OcBReturnOrder queryOcBReturnOrder(@Param("origRetunOrderId") Long origRetunOrderId);

    @Select("select * from oc_b_return_order where id=#{id} and isactive='Y'")
    OcBReturnOrder selectByid(@Param("id") Long id);

    @Select("select * from oc_b_return_order where id=#{orderReturnId} ")
    OcBOrderExtToHya SelectExtById(@Param("orderReturnId") Long orderReturnId);

    @Select("select id from oc_b_return_order where return_status= #{return_status}")
    List<Long> SelectListByReturnStatus(@Param("return_status") Integer return_status);

    @Update("update oc_b_return_order set cp_c_phy_warehouse_in_id= #{wareHouseId} where id= #{id}")
    Integer updateBycpCPhyWarehouseInId(@Param("wareHouseId") Long wareHouseId, @Param("id") Long id);


    @Select("select sum(amt_refund) from oc_b_return_order_refund where oc_b_return_order_id=#{orderReturnId}")
    BigDecimal selectAmtprice(@Param("orderReturnId") Long returnId);


    /**
     * 30,50 待售后确认,退款完成
     *
     * @param node
     * @param limit
     * @return
     */
    @SelectProvider(type = OcBReturnOrderMapper.OcReturnOrder.class, method = "select2SettlementByNode")
    List<OcBReturnOrder> selectOrderListByIdsAndToSettleStatus(@Param("node") String node, @Param("tableName") String tableName
            , @Param("limit") Integer limit);

    @Update("UPDATE OC_B_RETURN_ORDER SET LOGISTICS_STATUS = #{status}  WHERE BILL_NO=#{billNo}")
    int updateByThId(@Param("billNo") String billNo,@Param("status") String logisticsStatus);

    @Select("select ID from OC_B_RETURN_ORDER where BILL_NO=#{billNo} AND isactive='Y'")
    Long selectBySgBOutBillNo(String billNo);

    @Select("<script>" +
            "select count(*) from OC_B_RETURN_ORDER where return_status = #{status} and isactive = 'Y' and tid in " +
            "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    long countByReturnStatus(@Param("status") int status, @Param("tids") List<String> tids);

    @Update("UPDATE oc_b_return_order SET wms_cancel_status = #{model.wmsCancelStatus}, is_towms = #{model.isTowms}, confirm_status = #{model.confirmStatus}, " +
            "modifieddate=#{model.modifieddate}, modifierid = #{model.modifierid}, modifiername = #{model.modifiername}, sto_in_notices_id = null, sto_in_notices_no = null  WHERE id = #{model.id}")
    void updateOcBReturnOrder(@Param("model") OcBReturnOrder updatereturnOrder);

    @Select("<script>" +
            "select id from oc_b_return_order where tid in " +
            "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<Long> queryIdsByTids(@Param("tids") List<String> tids);

    class OcReturnOrder {

        public String select2SettlementByNode(@Param("node") String node, @Param("tableName") String tableName
                , @Param("limit") Integer limit) {
            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=")
                    .append(node)
                    .append("*/ ")
                    .append("SELECT * FROM ")
                    .append(tableName)
                    .append(" where return_status in (30,50) and to_settle_status in(0,1,3) and platform = 2 limit ")
                    .append(limit);
            return sb.toString();
        }

        public String selectJoin(String selectJoin) {
            return "SELECT * FROM OC_B_RETURN_ORDER WHERE ID IN ( " + selectJoin + " ) ";
        }

        public String select(@Param("join") String join, @Param("isActive") String isActive) {
            return "SELECT * FROM OC_B_RETURN_ORDER WHERE ID IN ( " + join + " ) AND ISACTIVE ='"
                    + isActive + "'  ORDER BY MODIFIEDDATE DESC";
        }


        public String update(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("oc_b_return_order");
                    for (String key : map.keySet()) {
                        if (!"id".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("id = #{id}");
                }
            }.toString();
        }
    }

    @Select("SELECT COUNT(1) FROM  OC_B_RETURN_ORDER  WHERE ID=#{ID} ")
    int selectIdByCount(Long id);

    @Select("SELECT * FROM OC_B_RETURN_ORDER WHERE `ID`=#{id}")
    QueryReturnOrderResult queryReturnOrderResultById(@Param("id") String id);

    /**
     * 批量修改仓库
     *
     * @param wareHouseId 仓库
     * @param rtnIds      id
     * @return int
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateCpWarehouseInId")
    int updateCpWarehouseInId(@Param("wareHouseId") Long wareHouseId, @Param("rtnIds") String rtnIds);

    /**
     * @param rtnReq UpdateReturnOrderRequest
     * @param rtnIds ids
     * @return int
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateCpcLogisticsInfo")
    int updateCpcLogisticsInfo(@Param("rtnReq") UpdateReturnOrderRequest rtnReq, @Param("rtnIds") String rtnIds);


    /**
     * 修改仓库.查询
     *
     * @param ids id
     * @return list
     */
    @SelectProvider(type = SqlProvider.class, method = "queryReturnOrderByIds")
    List<OcBReturnOrder> queryReturnOrderByIds(@Param("ids") String ids);

    /**
     * 1.2
     * 查询退换货订单信息集合
     *
     * @param sqlParam ids
     * @param isActive 是否启用
     * @return List<OcBReturnOrder> //RETURN_STATUS,INVENTED_STATUS,RETURN_AMT_LIST,RETURN_AMT_SHIP, RETURN_AMT_OTHER
     */
    @SelectProvider(type = SqlProvider.class, method = "queryOcBreturnOrderListByIds")
    List<OcBReturnOrder> queryOcBReturnOrderListByIds(@Param("sqlParam") String sqlParam,
                                                      @Param("isActive") String isActive);

    /**
     * 入库匹配专用
     * 查询退换货订单信息集合
     *
     * @param sqlParam
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "queryOcBReturnOrderListByIds4Match")
    List<OcBReturnOrder> queryOcBReturnOrderListByIds4Match(@Param("sqlParam") String sqlParam);

    @Select("SELECT * FROM OC_B_RETURN_ORDER WHERE `ID`=#{id} AND ISACTIVE='Y' ")
    OcBReturnOrder queryOcBReturnOrderById4Match(@Param("id") Long id);

    /**
     * 1.2
     * 查询退换货订单信息集合 4 Sap
     */
    @SelectProvider(type = SqlProvider.class, method = "selectListForSapByIds")
    List<OcBReturnOrder> selectListForSapByIds(@Param("sqlParam") String sqlParam);

    /**
     * 1.3
     * 查询退换货订单信息集合- 第三方
     */
    @SelectProvider(type = SqlProvider.class, method = "selectListForThirdSysByIds")
    List<OcBReturnOrder> selectListForThirdSysByIds(TaskParam taskParam);


    /**
     * 查询:平台单号,备注
     *
     * @param sqlParam
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "querySourceCodeRemarkByIds")
    List<JSONObject> querySourceCodeRemarkByIds(@Param("sqlParam") String sqlParam);

    @Select("SELECT `ID`,`ORIG_SOURCE_CODE`,`BACK_MESSAGE`,`PLATFORM`,`SELLER_NICK`,`ORDERFLAG`,`CP_C_SHOP_ID` "
            + "FROM OC_B_RETURN_ORDER WHERE `ID`=#{id}")
    OcBReturnOrder querySourceCodeRemarkById(@Param("id") Long id);

    /**
     * 查询退换货订单
     *
     * @param paramJo JSONObject
     * @return OcBReturnOrder
     */
    @Select("SELECT * FROM OC_B_RETURN_ORDER WHERE `ID`=#{id} AND ISACTIVE=#{isActive} AND "
            + "RETURN_STATUS=#{returnStatus} OR `ID`=#{id} AND ISACTIVE=#{isActive} AND "
            + "INVENTED_STATUS=#{inventedStatus} AND RETURN_STATUS!=#{returnStatusSec}")
    OcBReturnOrder queryOcBReturnOrderByCdt(JSONObject paramJo);

    /**
     * 退货入库. 更新退换货单
     *
     * @param ocBReturnOrder 退单
     * @return 结果
     */
    @Update("UPDATE OC_B_RETURN_ORDER SET IS_TODRP=#{isTodrp},CP_C_STORE_ID=#{cpCStoreId},"
            + "CP_C_STORE_ECODE=#{cpCStoreEcode},CP_C_STORE_ENAME=#{cpCStoreEname},OC_B_REFUND_IN_ID=#{ocBRefundInId}"
            + " WHERE `ID`=#{id}")
    int updateOcBreturnOrderById(OcBReturnOrder ocBReturnOrder);

    /**
     * 更新卖家备注
     *
     * @param ocBReturnOrder ocBReturnOrder
     * @return int
     */
    @Update("UPDATE OC_B_RETURN_ORDER SET BACK_MESSAGE=#{backMessage},MODIFIERID=#{modifierid},"
            + "MODIFIERENAME=#{modifierename},MODIFIERNAME=#{modifiername},MODIFIEDDATE=#{modifieddate}, "
            + "ORDERFLAG=#{orderflag} WHERE `ID`=#{id}")
    int updateSellerRemark(OcBReturnOrder ocBReturnOrder);

    /**
     * 退货入库. 更新入库结果单
     *
     * @param ocBReturnOrder 退单
     * @return int
     */
    @Update("UPDATE OC_B_RETURN_ORDER SET RETURN_STATUS=#{returnStatus},INVENTED_STATUS=#{inventedStatus},"
            + "RETURN_AMT_LIST=#{returnAmtList},RETURN_AMT_ACTUAL=#{returnAmtActual},ALL_SKU=#{allSku},"
            + "CP_C_PHY_WAREHOUSE_IN_ID=#{cpCPhyWarehouseInId}, INER_ID=#{inerId}, INER_ENAME=#{inerEname}, "
            + "INER_NAME=#{inerName},IN_TIME=#{inTime},IS_TODRP=#{isTodrp},CP_C_STORE_ID=#{cpCStoreId},"
            + "CP_C_STORE_ECODE=#{cpCStoreEcode},CP_C_STORE_ENAME=#{cpCStoreEname},PRO_RETURN_STATUS=#{proReturnStatus}"
            + " WHERE ID=#{id}")
    int updateOcBreturnOrderInfoById(OcBReturnOrder ocBReturnOrder);

    /**
     * 更新传结算标识：批量更新
     *
     * @param toACStatus
     * @param ids
     * @return
     */
    @Update("<script> "
            + "update oc_b_return_order set TO_SETTLE_STATUS = #{toACStatus}, modifieddate = now() where id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateToACStatusByIds(@Param("toACStatus") Integer toACStatus, @Param("ids") List<Long> ids);

    /**
     * 更新时间戳
     *
     * @param ids
     * @return
     */
    @Update("<script> "
            + "update oc_b_return_order set modifieddate = now() where id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateModifiedDateByIds(@Param("ids") List<Long> ids);

    /**
     * sql
     * author by zhang.xiwen
     */
    class SqlProvider {
        /**
         * 查询退换货订单信息集合
         *
         * @param sqlParam ids
         * @param isActive y/n
         * @return list<OC_B_RETURN_ORDER>
         */
        public String queryOcBreturnOrderListByIds(@Param("sqlParam") String sqlParam,
                                                   @Param("isActive") String isActive) {
            StringBuilder sb = new StringBuilder("SELECT  * FROM OC_B_RETURN_ORDER WHERE `ID` IN (");
            sb.append(sqlParam);
            sb.append(" )");
            sb.append(" AND ISACTIVE ='" + isActive + "'");
            return sb.toString();
        }

        /**
         * 入库匹配专用
         * 查询退换货订单信息集合
         *
         * @param sqlParam ids
         * @return list<OC_B_RETURN_ORDER>
         */
        public String queryOcBReturnOrderListByIds4Match(@Param("sqlParam") String sqlParam) {
            StringBuilder sb = new StringBuilder("SELECT  * FROM OC_B_RETURN_ORDER WHERE `ID` IN (");
            sb.append(sqlParam)
                    .append(" ) AND ISACTIVE ='")
                    .append("Y' ORDER BY MODIFIEDDATE DESC");
            return sb.toString();
        }

        /**
         * 查询退换货订单信息集合.FOR SAP
         *
         * @param sqlParam ids
         */
        public String selectListForSapByIds(@Param("sqlParam") String sqlParam) {
            StringBuilder sb = new StringBuilder("SELECT  * FROM OC_B_RETURN_ORDER WHERE `ID` IN (");
            sb.append(sqlParam).append(" )")
                    .append(" AND IFNULL(TO_SAP_STATUS,0) =0 AND ISACTIVE='Y' ");
            return sb.toString();
        }

        /**
         * 查询退换货订单,平台单号,备注
         *
         * @param sqlParam ids
         * @return sql
         */
        public String querySourceCodeRemarkByIds(@Param("sqlParam") String sqlParam) {
            StringBuilder sb = new StringBuilder("SELECT `ID`,`ORIG_SOURCE_CODE`,`BACK_MESSAGE`,`PLATFORM`,"
                    + "`SELLER_NICK`,`ORDERFLAG`,`CP_C_SHOP_ID` FROM OC_B_RETURN_ORDER WHERE `ID` IN (");
            sb.append(sqlParam);
            sb.append(" );");
            return sb.toString();
        }

        /**
         * 批量修改仓库.查询状态
         *
         * @param ids ids
         * @return string
         */
        public String queryReturnOrderByIds(@Param("ids") String ids) {
            StringBuilder sb = new StringBuilder("SELECT * FROM OC_B_RETURN_ORDER  ");
            sb.append("WHERE `ID` IN (");
            sb.append(ids);
            sb.append(")");
            return sb.toString();
        }

        /**
         * 批量修改仓库
         *
         * @param wareHouseId long
         * @param rtnIds      ids
         * @return string
         */
        public String updateCpWarehouseInId(@Param("wareHouseId") Long wareHouseId, @Param("rtnIds") String rtnIds) {
            StringBuilder sb = new StringBuilder("UPDATE OC_B_RETURN_ORDER SET CP_C_PHY_WAREHOUSE_IN_ID=");
            sb.append(wareHouseId);
            sb.append("  WHERE `ID` IN (");
            sb.append(rtnIds);
            sb.append(" )");
            return sb.toString();
        }

        /**
         * 批量修改物流(并修改是否传WMS)
         *
         * @param rtnReq UpdateReturnOrderRequest
         * @param rtnIds ids
         * @return string
         */
        public String updateCpcLogisticsInfo(@Param("rtnReq") UpdateReturnOrderRequest rtnReq,
                                             @Param("rtnIds") String rtnIds) {
            StringBuilder sb = new StringBuilder("UPDATE OC_B_RETURN_ORDER SET CP_C_LOGISTICS_ID=");
            sb.append(rtnReq.getCpCLogisticsId());
            sb.append(", CP_C_LOGISTICS_ECODE='");
            sb.append(rtnReq.getCpCLogisticsEcode());
            sb.append("', CP_C_LOGISTICS_ENAME='");
            sb.append(rtnReq.getCpCLogisticsEname());
            sb.append("'  WHERE `ID` IN (");
            sb.append(rtnIds);
            sb.append(" )");
            return sb.toString();
        }


        public String selectListForThirdSysByIds(TaskParam taskParam) {
            StringBuilder sb = new StringBuilder("SELECT  * FROM OC_B_RETURN_ORDER WHERE `ID` IN (");
            sb.append(taskParam.getKeyStrings()).append(" ) AND IFNULL(").append(taskParam.getOrigStatusCol())
                    .append(",0) =0 AND ISACTIVE='Y' ");
            return sb.toString();
        }
    }

    // ********** zhang.xiwen end

    /**
     * ljp add
     *
     * @param id
     * @param origOrderId
     * @param refundStatus
     * @return
     */
    @Select("SELECT * FROM OC_B_RETURN_ORDER " +
            "WHERE `ID`=#{id} AND "
            + " RETURN_STATUS=#{refundStatus} AND "
            + " ORIG_ORDER_ID=#{origOrderId}")
    OcBReturnOrder getRefundOrder(@Param("id") Long id,
                                  @Param("origOrderId") Long origOrderId, @Param("refundStatus") Integer refundStatus);

    /**
     * 查询结算日志的退换货信息.查询
     *
     * @return list
     */
    @Select("select * from oc_b_return_order a where a.return_status in (50) and a.to_settle_status in (1,3) LIMIT #{size}")
    List<OcBReturnOrder> queryReturnOrderToSettlement(@Param("size") Integer size);

    @Select("SELECT * FROM OC_B_RETURN_ORDER WHERE ID=#{id} AND (RETURN_STATUS=30 OR (RETURN_STATUS=50 AND INVENTED_STATUS!=1))")
    OcBReturnOrder getMatchReturnOrderById(@Param("id") Long id);


    @Select("<script> "
            + "SELECT * FROM oc_b_return_order WHERE id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnOrder> selectOcBReturnOrderByOrderIds(@Param("ids") List<Long> ids);


    @Update("<script> "
            + "update oc_b_return_order set intercerpt_status = #{interceptMark}, return_pro_type = #{returnProType} WHERE id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateOcBReturnOrderByOrderIds(@Param("interceptMark") Integer interceptMark, @Param("ids") List<Long> ids, @Param("returnProType") Integer returnProType);


    @Select("<script> "
            + "SELECT * FROM oc_b_return_order WHERE intercerpt_status = #{interceptMark} and id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnOrder> selectOcBReturnOrderListByOrderIds(@Param("ids") List<Long> ids, @Param("interceptMark") Integer interceptMark);


    @Select("<script> "
            + "SELECT * FROM oc_b_return_order WHERE id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnOrder> selectReturnOrderListByOrderIds(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT * FROM oc_b_return_order WHERE isactive = 'Y' AND id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnOrder> selectActiveReturnOrderListByIds(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT id,logistics_code,bill_no FROM oc_b_return_order WHERE isactive = 'Y' AND id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<JSONObject> selectActiveReturnOrderLogisticsCodeListByIds(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT * FROM oc_b_return_order WHERE return_status != 60 and id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnOrder> selectOcBReturnOrderByOrder(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT * FROM oc_b_return_order WHERE return_status != 60 and id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + " and return_id =#{returnId} "
            + "</script>")
    List<OcBReturnOrder> selectOcBReturnOrderByOrderInfo(@Param("ids") List<Long> ids, @Param("returnId") String returnId);


    @Select("SELECT * FROM  OC_B_RETURN_ORDER  WHERE return_id=#{returnId} and return_status != 60")
    List<OcBReturnOrder> selectByReturnId(@Param("returnId") String returnId);

    /***
     * @param origSourceCode 原始平台单号信息
     * @return 退单
     */
    @Select("SELECT * FROM OC_B_RETURN_ORDER WHERE "
            + "ORIG_SOURCE_CODE = #{origSourceCode}")
    List<OcBReturnOrder> getReturnOrderUnionGsiByOrigSourceCode(@Param("origSourceCode") String origSourceCode);

    /***
     * @param origSourceCode 原始平台单号信息
     * @return 退单
     */
    @Select("SELECT * FROM oc_b_return_order WHERE ORIG_SOURCE_CODE = #{origSourceCode}")
    List<OcBReturnOrder> getReturnOrderFromGsiByOrigSourceCode(@Param("origSourceCode") String origSourceCode);

    /***
     * @param origSourceCode 原始平台单号信息
     * @return 退单编号集
     */
    @Select("SELECT `ID` FROM oc_b_return_order WHERE ORIG_SOURCE_CODE = #{origSourceCode}")
    List<Long> listIdFromGsiByOrigSourceCode(@Param("origSourceCode") String origSourceCode);

    /**
     * @param origSourceCodes 原始平台单号信息集合
     * @return 退单编号集
     */
    @Select("<script> SELECT `ID` FROM oc_b_return_order WHERE ORIG_SOURCE_CODE IN "
            + "<foreach item='item' index='index' collection='origSourceCodes' "
            + "open='(' separator=',' close=')'> #{item} </foreach>  </script>")
    List<Long> listIdFromGsiByOrigSourceCodes(@Param("origSourceCodes") List<String> origSourceCodes);

    /**
     * @param returnIdList 退换单id集合
     * @param billType     退换单类型
     * @return 执行数量
     */
    @Update("<script> update oc_b_return_order SET bill_type = #{billType} where id in"
            + "<foreach item='item' index='index' collection='returnIdList' "
            + "open='(' separator=',' close=')'> #{item} </foreach>  </script>")
    int updateReturnOrderBillType(@Param("returnIdList") List<Long> returnIdList, @Param("billType") int billType);

    /**
     * 查询传wms记录
     *
     * @param limit
     * @param maxCompensateNum
     * @return
     */
    @Select("<script> "+
            "SELECT * FROM OC_B_RETURN_ORDER " +
            "WHERE RETURN_STATUS = 20 " +
            "AND IS_NEED_TO_WMS = 1 " +
            "AND (IS_TOWMS = 0 OR IS_TOWMS = 3) AND QTY_WMS_FAIL &lt; #{maxCompensateNum} " +
            "AND CP_C_PHY_WAREHOUSE_IN_ID IS NOT NULL " +
            "AND CP_C_LOGISTICS_ID IS NOT NULL " +
            "AND LOGISTICS_CODE IS NOT NULL " +
            "AND TRIM(LOGISTICS_CODE) !='' " +
            "<if test = 'ifControl == true'> AND CONFIRM_STATUS = 1 </if> " +
            "LIMIT #{limit} "+
            "</script> ")
    List<OcBReturnOrder> selectReturnOrderToWmsTask(@Param("limit") Integer limit,
                                                    @Param("ifControl") Boolean ifControl,
                                                    @Param("maxCompensateNum") Integer maxCompensateNum);

    /**
     * 更新退单的传wms信息
     *
     * @param ids            退单ID
     * @param wmsIssueStatus 传wms状态
     * @param wmsIssueCount  失败次数
     * @param wmsIssueReason 失败原因
     */
    @Update("UPDATE OC_B_RETURN_ORDER SET IS_TOWMS = #{wmsIssueStatus}, MODIFIEDDATE = " +
            "NOW(),QTY_WMS_FAIL= IFNULL(QTY_WMS_FAIL,0) + #{wmsIssueCount}, WMS_FAILREASON=#{wmsIssueReason} " +
            "WHERE ID IN (${ids})")
    int updateWmsStatusByIds(@Param("ids") String ids,
                             @Param("wmsIssueStatus") Integer wmsIssueStatus,
                             @Param("wmsIssueCount") Integer wmsIssueCount,
                             @Param("wmsIssueReason") String wmsIssueReason);

    /**
     * @param returnId 原始平台退款单号
     * @return
     */
    @Select("SELECT `ID` FROM OC_B_RETURN_ORDER WHERE return_id = #{returnId}")
    List<Long> listIdFromGsiByReturnId(@Param("returnId") String returnId);

    @Select("SELECT id FROM OC_B_RETURN_ORDER where bill_type = 1 and RETURN_ID is not null  and `creationdate` >= #{fromDate}  and `return_status` = 20 and ORIG_ORDER_ID is not null order by modifieddate asc limit #{limitNum}")
    List<Long> selectForCancel(@Param("fromDate") Date fromDate, @Param("limitNum") Integer limitNum);

    /**
     * 入库通知单 库存调整单 传wms失败信息根据id更新
     *
     * @param params wms回传信息
     * @return Integer
     */
    @Update("<script>" +
            "UPDATE oc_b_return_order p " +
            "<set>" +
            " p.IS_TOWMS = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.flag == \"failure\"'>" +
            " when #{param.returnOrderId} then 3" +
            "</if>" +
            "<if test = 'param.flag == \"success\"'>" +
            " when #{param.returnOrderId} then 2" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.wms_failreason = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.flag == \"failure\"'>" +
            " when #{param.returnOrderId} then #{param.message}" +
            "</if>" +
            "<if test = 'param.flag == \"success\"'>" +
            " when #{param.returnOrderId} then null" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "p.QTY_WMS_FAIL = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            "<if test = 'param.flag == \"failure\"'>" +
            " when #{param.returnOrderId} then p.QTY_WMS_FAIL+1" +
            "</if>" +
            "<if test = 'param.flag == \"success\"'>" +
            " when #{param.returnOrderId} then  p.QTY_WMS_FAIL+0" +
            "</if>" +
            "</foreach>" +
            "</trim>" +
            "</set>" +
            "<where>" +
            " p.id in " +
            " <foreach collection='params' item='param' open='(' separator=',' close=')'>" +
            " #{param.returnOrderId} " +
            "</foreach>" +
            "</where>" +
            "</script>")
    Integer batchUpdateWmsInfoByList(@Param("params") List<OrderCreateBaseResult> params);

    /**
     * 根据tid查询退款单
     * @param returnStatus 退款单状态
     * @param isConfirmPre 是否确认预退款
     * @param tids 退款单tid
     * @return
     */
    @Select("<script>" +
            "select * from oc_b_return_order where return_status = #{returnStatus} and is_confirm_pre = #{isConfirmPre} and tid in " +
            "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<OcBReturnOrder> selectReturnOrdersByTids(@Param("returnStatus") Integer returnStatus, @Param("isConfirmPre") String isConfirmPre, @Param("tids") List<String> tids);

    /**
     * 根据id更新退款单
     *
     * @param params
     * @return
     */
    @Update("<script>" +
            "UPDATE oc_b_return_order p " +
            "<set>" +
            " p.MODIFIEDDATE = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            " when #{param.id} then #{param.modifieddate} " +
            "</foreach>" +
            "</trim>" +
            "p.IS_CONFIRM_PRE = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            " when #{param.id} then #{param.isConfirmPre} " +
            "</foreach>" +
            "</trim>" +
            "p.CONFIRM_BILL_ID = " +
            "<trim prefix='case p.id' suffix='end,'>" +
            " <foreach collection='params' item='param' index='index' separator=' '>" +
            " when #{param.id} then #{param.confirmBillId} " +
            "</foreach>" +
            "</trim>" +
            "</set>" +
            "<where>" +
            " p.id in " +
            " <foreach collection='params' item='param' open='(' separator=',' close=')'>" +
            " #{param.id} " +
            "</foreach>" +
            "</where>" +
            "</script>")
    Integer batchUpdateReturnOrder(@Param("params") List<OcBReturnOrder> params);


    @Select("select * from oc_b_return_order where platform = #{platform} and return_status = #{returnStatus} and deal_type is null")
    List<OcBReturnOrder> selectPlatformNoDealOrders(@Param("returnStatus") Integer returnStatus, @Param("platform") Integer platform);

    @Select("select * from oc_b_return_order where orig_order_id = #{origOrderId}")
    List<OcBReturnOrder> selectOrigOrderId(@Param("origOrderId") Long origOrderId);

    /**
     * 根据原订单ID列表批量查询原订单ID
     *
     * @param origOrderIds 原订单ID列表
     * @return 已存在的原订单ID集合
     */
    @Select("<script>"
            + "SELECT DISTINCT orig_order_id FROM oc_b_return_order WHERE orig_order_id IN "
            + "<foreach item='item' index='index' collection='origOrderIds' open='(' separator=',' close=')'>"
            + "#{item}"
            + "</foreach>"
            + "</script>")
    Set<Long> selectExistingOrigOrderIds(@Param("origOrderIds") List<Long> origOrderIds);
    /**
     * 查询需要创建班牛工单的退换货单
     * 条件：当前时间超过要求入库时间，是否超时入库不等于“是”，单据状态=等待退换入库，通用标记为空
     * 按照要求入库时间升序排序，优先处理最早需要入库的退换货单
     *
     * @param currentTime 当前时间
     * @param limit 查询数量限制
     * @return 退换货单列表
     */
    @Select("SELECT * FROM oc_b_return_order WHERE required_storage_time < #{currentTime} " +
            "AND (overdue_storage_status IS NULL OR overdue_storage_status = 0) " +
            "AND return_status = 20 " +  /* 20表示等待退换入库 */
            "AND (generic_mark IS NULL OR generic_mark = '') " +
            "AND isactive = 'Y' " +
            "ORDER BY required_storage_time ASC, id ASC " +
            "LIMIT #{limit}")
    List<OcBReturnOrder> selectReturnOrdersForBnTask(@Param("currentTime") Date currentTime, @Param("limit") Integer limit);

    /**
     * 批量更新退换货单的超时入库状态
     * 用于标记已处理过的数据，避免重复查询
     *
     * @param ids 退换货单ID列表
     * @param overdueStorageStatus 超时入库状态：0-否，1-是
     * @return 更新行数
     */
    @Update("<script> "
            + "UPDATE oc_b_return_order SET overdue_storage_status = #{overdueStorageStatus}, modifieddate = NOW() WHERE id "
            + "IN <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateOverdueStorageStatusByIds(@Param("ids") List<Long> ids, @Param("overdueStorageStatus") Integer overdueStorageStatus);

}
