package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderNaiKaTaskSql;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderNaiKaMapper
 * @Description 奶卡单据数据
 * <AUTHOR>
 * @Date 2022/6/24 13:51
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderNaiKaMapper extends ExtentionMapper<OcBOrderNaiKa> {

    @SelectProvider(type = OcBOrderNaiKaTaskSql.class, method = "selectToNaiKaAccount")
    List<OcBOrderNaiKa> selectAccountToNaiKa(@Param(value = "limit") int limit, @Param(value = "taskTableName") String taskTableName, @Param(value = "accountToNaikaTimes") Integer accountToNaikaTimes);

    @Select("SELECT id FROM oc_b_order_naika WHERE oc_b_order_id=#{ocBOrderId} and isactive='Y' and ps_c_sku_id = #{psCSkuId}")
    List<Long> selectIdByOcBOrderId(@Param("ocBOrderId") Long ocBOrderId, @Param("psCSkuId") Long psCSkuId);

    @Select("SELECT * FROM oc_b_order_naika WHERE oc_b_order_id=#{ocBOrderId} and isactive='Y' and ps_c_sku_ecode = #{skuEcode}")
    List<OcBOrderNaiKa> selectIdByOcBOrderIdAndSku(@Param("ocBOrderId") Long ocBOrderId, @Param("skuEcode") String skuEcode);

    @Select("SELECT * FROM oc_b_order_naika WHERE oc_b_order_id=#{ocBOrderId} and isactive='Y' ")
    List<OcBOrderNaiKa> selectNaiKaByOcBOrderId(Long ocBOrderId);

    @Select("SELECT * FROM oc_b_order_naika WHERE oc_b_order_id=#{ocBOrderId} and oc_b_order_item_id = #{ocBOrderItemId} and isactive='Y' ")
    List<OcBOrderNaiKa> selectNaiKaByOcBOrderIdAndItemId(@Param("ocBOrderId") Long ocBOrderId, @Param("ocBOrderItemId") Long ocBOrderItemId);

    @Select("SELECT * FROM oc_b_order_naika WHERE oc_b_order_id=#{ocBOrderId} and oc_b_order_item_id = #{ocBOrderItemId} and naika_status in (5, 6) and isactive='Y' ")
    List<OcBOrderNaiKa> selectToVoidNaiKaByOcBOrderIdAndItemId(@Param("ocBOrderId") Long ocBOrderId, @Param("ocBOrderItemId") Long ocBOrderItemId);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_naika WHERE oc_b_order_id=#{ocBOrderId} and isactive='Y' and ps_c_sku_id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderNaiKa> selectNaiKa(@Param("ocBOrderId") Long ocBOrderId, @Param("ids") List<Long> psCSkuId);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_naika WHERE id in"
            + "<foreach item='item' index='index' collection='idList' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderNaiKa> selectNaiKaByIdList(@Param("idList") List<Long> idList);

    @Update("<script> "
            + "update  oc_b_order_naika set naika_status = #{status} , modifieddate = now(), operate_time = now() WHERE id in"
            + "<foreach item='item' index='index' collection='idList' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    void updateNaiKaStatusByIdList(@Param("idList") List<Long> idList, @Param("status") Integer status);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_naika WHERE oc_b_order_id = #{ocBOrderId} "
            + "</script>")
    List<OcBOrderNaiKa> selectNaiKaListByOrderId(@Param("ocBOrderId") Long ocBOrderId);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_naika WHERE oc_b_order_id = #{ocBOrderId} and card_code = #{cardCode} "
            + "</script>")
    List<OcBOrderNaiKa> selectNaiKaList(@Param("ocBOrderId") Long ocBOrderId, @Param("cardCode") String cardCode);


    @Select("SELECT * FROM oc_b_order_naika WHERE card_code = #{cardCode} and isactive='Y' ")
    List<OcBOrderNaiKa> selectBySourceCodebyGsi(@Param("cardCode") String cardCode);


    @Select("<script> "
            + "SELECT oc_b_order_id FROM oc_b_order_naika"
            + " WHERE card_code = #{cardCode} order by id asc </script>")
    List<Long> selectByCode(@Param("cardCode") String cardCode);


    @Select("<script> "
            + "SELECT * FROM OC_B_ORDER_NAIKA"
            + " WHERE isactive = 'Y' and OC_B_ORDER_ID in "
            + "<foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderNaiKa> getOrderNaiKaListByOrderId(@Param("ids") List<Long> ids);


    @Delete("<script> "
            + "DELETE FROM OC_B_ORDER_NAIKA"
            + " WHERE isactive = 'Y' AND oc_b_order_id = #{ocBOrderId} and oc_b_order_item_id in "
            + "<foreach item='item' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int deleteNaiKaListByOrderId(@Param("itemIds") List<Long> itemIds, @Param("ocBOrderId") Long ocBOrderId);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_naika WHERE card_code = #{cardCode} and business_type != 'pickup'"
            + "</script>")
    List<OcBOrderNaiKa> selectNaiKaByCardCode(@Param("cardCode") String cardCode);
}
