package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCInvoiceStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface StCInvoiceStrategyMapper extends ExtentionMapper<StCInvoiceStrategy> {

    @Select("select * from st_c_invoice_strategy where cp_c_shop_id = #{shopId}")
    StCInvoiceStrategy queryByShopId(@Param("shopId") Long cpCShopId);

    @Select("select * from st_c_invoice_strategy where cp_c_shop_id is null or cp_c_shop_id = ''")
    StCInvoiceStrategy queryByShopNull();
}