package com.jackrain.nea.oc.oms.services;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.st.service.StCTraceabilityStrategyMarkingService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @program: r3-oc-oms
 * @description: 订单取消溯源标记服务
 * @author: lijin
 * @create: 2024-12-19
 **/
@Slf4j
@Component
public class OcBOrderTraceabilityCancelService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Resource
    private OmsOrderLogService orderLogService;

    @Autowired
    private StCTraceabilityStrategyMarkingService stCTraceabilityStrategyMarkingService;

    /**
     * 取消溯源标记
     *
     * @param orderIds 订单ID列表
     * @param user     用户
     * @return 执行结果
     */
    public ValueHolderV14<Void> cancelTraceability(List<Long> orderIds, User user) {
        log.info(LogUtil.format("开始取消溯源标记，订单数量：{}", "开始取消溯源标记"), orderIds.size());

        int successNum = 0;

        // 一单一单处理，避免并发问题
        for (Long orderId : orderIds) {
            try {
                OcBOrderTraceabilityCancelService bean =
                        ApplicationContextHandle.getBean(OcBOrderTraceabilityCancelService.class);
                bean.singleOrderCancelTraceability(orderId, user);
                successNum++;
            } catch (Exception e) {
                // 记录失败日志
                String message = e.getMessage();
                if (e.getMessage() != null && message.length() > 1000) {
                    message = message.substring(0, 1000);
                }
                insertOrderLog(orderId, null, OrderLogTypeEnum.CANCEL_TRACEABILITY.getKey(),
                        message, null, null, user);
            }
        }
        log.info(LogUtil.format("取消溯源标记完成，成功数量：{}", "取消溯源标记完成"), successNum);
        if (successNum == 0) {
            return new ValueHolderV14<>(ResultCode.FAIL, "所有订单取消溯源标记均失败！");
        } else if (successNum == orderIds.size()) {
            return new ValueHolderV14<>(ResultCode.SUCCESS,
                    String.format("成功取消%d个订单的溯源标记！", successNum));
        } else {
            return new ValueHolderV14<>(ResultCode.SUCCESS,
                    String.format("部分成功：成功取消%d个订单的溯源标记，失败%d个！",
                            successNum, orderIds.size() - successNum));
        }
    }

    /**
     * 单个订单取消溯源标记（加锁处理）
     *
     * @param orderId 订单ID
     * @param user    用户
     */
    @Transactional(rollbackFor = Exception.class)
    public void singleOrderCancelTraceability(Long orderId, User user) {
        // 查询订单信息（加锁）
        OcBOrder order = ocBOrderMapper.selectById(orderId);
        if (order == null) {
            throw new NDSException("订单不存在！");
        }

        // 校验订单状态：仅待审核允许取消溯源标记
        if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(order.getOrderStatus())) {
            throw new NDSException("订单状态不是待审核，不允许取消溯源标记！");
        }

        // 检查是否有溯源标记
        if (order.getIsTraceability() == null || order.getIsTraceability() != 1) {
            throw new NDSException("订单无溯源标记，无需取消！");
        }

        // 清除订单及明细的溯源标记
        stCTraceabilityStrategyMarkingService.clearOrderTraceabilityStatus(order);
        // 记录成功日志
        insertOrderLog(orderId, null, OrderLogTypeEnum.CANCEL_TRACEABILITY.getKey(),
                "取消溯源标记成功", null, null, user);

    }

    /**
     * 订单日志
     *
     * @param orderId
     * @param billNo
     * @param logType
     * @param logMessage
     * @param param
     * @param errorMessage
     * @param operateUser
     */
    private void insertOrderLog(long orderId, String billNo, String logType, String logMessage, String param,
                                String errorMessage, User operateUser) {
        //调用添加订单日志
        try {
            orderLogService.addUserOrderLog(orderId, billNo, logType, logMessage, null, null, operateUser);
        } catch (Exception e) {
            log.error(LogUtil.format("新增订单日志失败，失败原因:{}", orderId), Throwables.getStackTraceAsString(e));
        }
    }
}
