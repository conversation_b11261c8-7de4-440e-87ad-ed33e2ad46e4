package com.jackrain.nea.oc.oms.services.audit;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.web.face.User;

/**
 * 审核策略
 *
 * @Auther: 黄志优
 * @Date: 2020/11/3 10:21
 * @Description:
 */
public interface AuditStrategyHandler {

    /**
     * 处理审核
     *
     * @param orderInfo
     * @return
     */
    boolean doHandle(OcBOrderRelation orderInfo, User operateUser);

    /**
     * 排序
     *
     * @param name
     * @return
     */
    Integer getSort(String name);
}
