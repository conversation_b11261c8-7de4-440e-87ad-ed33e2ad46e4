package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.st.model.StCShortStockNoSplitStrategyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;


@Mapper
public interface StCShortStockNoSplitStrategyMapper extends BaseMapper<StCShortStockNoSplitStrategyEntity> {

    /**
     * 查询启用的不拆单策略
     *
     * @return StCShortStockNoSplitStrategyEntity
     */
    @Select("select * from st_c_short_stock_no_split_strategy where SHOP_ID = #{shopId} and ISACTIVE='Y' and STRATEGY_TYPE = 2 ORDER BY id DESC LIMIT 1")
    StCShortStockNoSplitStrategyEntity getStrategyByShopId(Long shopId);

    /**
     * 获取公用不拆单策略
     *
     * @return
     */
    @Select("select * from st_c_short_stock_no_split_strategy where  ISACTIVE='Y' and STRATEGY_TYPE = 1 ORDER BY id DESC LIMIT 1")
    StCShortStockNoSplitStrategyEntity getCommonStrategy();
}

