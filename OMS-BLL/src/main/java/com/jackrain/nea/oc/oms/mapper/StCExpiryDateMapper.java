package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCExpiryDate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCExpiryDateMapper extends ExtentionMapper<StCExpiryDate> {

    @Select("SELECT COUNT(1) FROM st_c_expiry_date WHERE EXPIRY_TYPE = 1 AND SUBMIT_STATUS not in (3,4)")
    int selectStCExpiryDateExpiryType();
    @Select("SELECT * FROM st_c_expiry_date WHERE EXPIRY_TYPE = 2 and shop_id = #{shopId} and SUBMIT_STATUS in (1,2)")
    List<StCExpiryDate> selectStCExpiryDateByShopId(@Param("shopId") Long shopId);


    @Select("SELECT * FROM st_c_expiry_date WHERE EXPIRY_TYPE = 2 and shop_id = #{shopId} and SUBMIT_STATUS = 2")
    List<StCExpiryDate> selectStCExpiryDateListByShopId(@Param("shopId") Long shopId);

    @Select("SELECT * FROM st_c_expiry_date WHERE EXPIRY_TYPE = 3 and customer_grouping = #{customerGroup} and SUBMIT_STATUS = 2")
    List<StCExpiryDate> selectStCExpiryDateListByCustomerGroup(@Param("customerGroup") Integer customerGroup);

    @Select("SELECT * FROM st_c_expiry_date WHERE EXPIRY_TYPE = 1 and SUBMIT_STATUS = 2")
    List<StCExpiryDate> selectStCExpiryDateCommon();


    @Select("SELECT * FROM st_c_expiry_date WHERE EXPIRY_TYPE = 4 and SUBMIT_STATUS = 2")
    List<StCExpiryDate> selectStCExpiryDateMemberMatch();


}