package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBJitxModifyWarehouseLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-12-01 14:41
 * @desc JITX订单改仓日志表
 **/
@Mapper
@Component
public interface OcBJitxModifyWarehouseLogMapper extends ExtentionMapper<OcBJitxModifyWarehouseLog> {
    /**
     * 分库查询
     *
     * @param nodeName
     * @param tableName
     * @param where
     * @param limit
     * @return selectByNodeSql
     */
    @SelectProvider(type = SqlProvider.class, method = "selectBySql")
    List<OcBJitxModifyWarehouseLog> select(@Param(value = "tableName") String tableName,
                                           @Param(value = "where") String where, @Param(value = "order") String order,
                                           @Param(value = "limit") int limit);

    @Slf4j
    class SqlProvider {
        public String selectBySql(@Param(value = "tableName") String tableName,
                                  @Param(value = "where") String where, @Param(value = "order") String order,
                                  @Param(value = "limit") int limit) {
            limit = limit != 0 ? limit : 1000;
            StringBuilder limitStr = new StringBuilder(" LIMIT ").append(limit);

            StringBuilder sql = new StringBuilder();
            sql.append("select * from ").append(tableName);
            if (StringUtils.isNotEmpty(where)) {
                sql.append(where);
            }
            if (StringUtils.isNotEmpty(order)) {
                sql.append(order);
            }
            sql.append(limitStr);
            return sql.toString();
        }
    }

    /**
     * 更新为未创建状态
     *
     * @param log
     * @return
     */
    @Update("update oc_b_jitx_modify_warehouse_log " +
            "set created_status = 0, workflow_state = null, " +
            "modifierid = #{log.modifierid}, modifiername = #{log.modifiername}, " +
            "modifierename = #{log.modifierename}, modifieddate = now() " +
            "where id = #{log.id}")
    int updateUnCreate(@Param("log") OcBJitxModifyWarehouseLog log);

    /**
     * 更新失败原因,失败次数
     * @param idList
     * @param log
     * @return
     */
    @Update("<script> " +
            "update oc_b_jitx_modify_warehouse_log " +
            "set fail_number = fail_number + 1, " +
            "fail_reason = #{log.failReason}, " +
            "modifierid = #{log.modifierid}, modifiername = #{log.modifiername}, " +
            "modifierename = #{log.modifierename}, modifieddate = now() " +
            "where id in " +
            "<foreach collection='idList' item='item' open='(' separator=',' close=')'> " +
            "#{item} " +
            "</foreach> " +
            "</script> ")
    Integer updateFailByIdList(@Param("idList") List<Long> idList
            , @Param("log") OcBJitxModifyWarehouseLog log);
}
