package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.StepExecInfo;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/29
 */
@Slf4j
public class OmsTransferSupply {

    /**
     * judge map options
     *
     * @param map
     * @return
     */
    public static boolean isEmpty(Map map) {
        return map == null || map.size() < 1;
    }

    /**
     * judge map not empty
     *
     * @param map
     * @return
     */
    public static boolean isNotEmpty(Map map) {
        return !isEmpty(map);
    }

    /**
     * optimize message length
     */
    public static Function<String, String> optimizeMsg = e -> {
        if (e == null) {
            return "Message Null";
        }
        return e.length() > 200 ? e.substring(0, 200) : e;
    };

    /**
     * deal exception message
     */
    public static Function<Exception, String> expMsgFun = e -> {
        if (e == null) {
            return "Exception Null";
        }
        String message = e.getMessage();
        if (message == null) {
            return "Exception Message Was Null";
        }
        return message.length() > 200 ? message.substring(0, 200) : message;
    };

    /**
     * record log message
     *
     * @param message
     */
    public static void logMsg(String message) {
        OmsTransferThreadLocalUtil.stepMsg.get().add(message);
    }

    public static void logMsgThenThrowException(String message) {
        OmsTransferThreadLocalUtil.stepMsg.get().add(message);
        throw new NDSException(message);
    }

    /**
     * print logs
     *
     * @param key
     */
    public static void logs(String key) {
        if (log.isDebugEnabled()) {
            log.debug(key + ", {}", JSON.toJSONString(OmsTransferThreadLocalUtil.stepMsg.get()));
        }
    }

    /**
     * lock oc order
     *
     * @param list
     * @return lock fail list
     */
    public static List<Long> lockOcOrders(List<Long> list) {
        List<String> logs = OmsTransferThreadLocalUtil.stepMsg.get();
        logs.add("lockStart");
        List<Long> lockFailIds = null;
        Iterator<Long> iterator = list.iterator();
        int lockSuccess = 0;
        while (iterator.hasNext()) {
            Long next = iterator.next();
            String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(next);
            RedisReentrantLock lock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (lock.tryLock(0, TimeUnit.MILLISECONDS)) {
                    OmsTransferThreadLocalUtil.locks.get().add(lock);
                    lockSuccess++;
                    continue;
                }
                logs.add(String.format("%d:lockFail", next));
            } catch (Exception e) {
                logs.add(String.format("%d:lockException", next));
            }
            if (lockFailIds == null) {
                lockFailIds = new ArrayList<>();
            }
            lockFailIds.add(next);
            iterator.remove();
        }
        logs.add(String.format("lockSuccess:%d, lockEnd", lockSuccess));
        return lockFailIds;
    }

    /**
     * unlock oc order
     *
     * @return
     */
    public static int unlockOcOrders() {
        List<String> logs = OmsTransferThreadLocalUtil.stepMsg.get();
        logs.add("unlockStart");
        List<RedisReentrantLock> ocLocks = OmsTransferThreadLocalUtil.locks.get();
        int success = 0;
        if (CollectionUtils.isEmpty(ocLocks)) {
            return 0;
        }
        int num = ocLocks.size();
        for (RedisReentrantLock lock : ocLocks) {
            try {
                lock.unlock();
                success++;
            } catch (Exception ex) {
                num--;
                logs.add(String.format("%d:unlockException", lock.getLockId()));
            }
        }
        logs.add(String.format("unlockSuccess:%d,unlockEnd", success));
        return num;
    }

    /**
     * @param id
     * @param key
     * @return
     */
    public static StepExecInfo initStepExecInfo(Long id, String key) {
        StepExecInfo stepExecInfo = StepExecInfo.build(id, key);
        OmsTransferThreadLocalUtil.execInfo.set(stepExecInfo);
        return stepExecInfo;
    }

    /**
     * @return
     */
    public static StepExecInfo getStepExecInfo() {
        return OmsTransferThreadLocalUtil.execInfo.get();
    }

    /**
     * release threadLocal resources
     */
    public static void releaseResources() {
        OmsTransferThreadLocalUtil.locks.remove();
        OmsTransferThreadLocalUtil.stepMsg.remove();
        OmsTransferThreadLocalUtil.execInfo.remove();
    }


}
