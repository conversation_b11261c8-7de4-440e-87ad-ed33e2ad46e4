package com.jackrain.nea.oc.oms.util;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

/**
 * @Description: 日期换算工具类
 * @author: 江家雷
 * @since: 2020/7/3
 * create at : 2020/7/3 15:43
 */
public class DateConversionUtil {

    private DateConversionUtil() {
    }

    public static LocalDateTime date2LocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static Date localDateTime2Date(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date plusMinutes(Date date, long minutes) {
        if (date == null) {
            return null;
        }
        return Date.from(date2LocalDateTime(date).plusMinutes(minutes).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date plusHours(Date date, long hours) {
        if (date == null) {
            return null;
        }
        return Date.from(date2LocalDateTime(date).plusHours(hours).atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date plusDays(Date date, long days) {
        if (date == null) {
            return null;
        }
        return Date.from(date2LocalDateTime(date).plusDays(days).atZone(ZoneId.systemDefault()).toInstant());
    }
}