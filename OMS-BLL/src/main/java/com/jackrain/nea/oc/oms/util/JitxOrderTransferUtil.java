package com.jackrain.nea.oc.oms.util;

import com.google.common.base.Throwables;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpJitxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.OrderAddressConvertUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 唯品会Jitx订单中间表转换成全渠道订单
 *
 * @author: 黄超
 * @since: 2019-06-26
 * create at : 2019-06-26 9:00
 */
@Component
@Slf4j
public class JitxOrderTransferUtil {

    private RegionNewService regionService;

    private CpRpcService cpRpcService;

    private BuildSequenceUtil sequenceUtil;

    public JitxOrderTransferUtil() {
    }

    @Autowired
    public JitxOrderTransferUtil(RegionNewService regionService, CpRpcService cpRpcService,
                                 BuildSequenceUtil sequenceUtil) {
        this.regionService = regionService;
        this.cpRpcService = cpRpcService;
        this.sequenceUtil = sequenceUtil;
    }

    /**
     * 转换成零售发货单对象
     *
     * @param jitxOrderRelation 订单中间表关联对象
     * @param isHistoryOrder    是否为历史订单
     * @return 零售发货单对象
     */
    private OcBOrder buildOcBOrderFromIpJitxOrder(IpJitxOrderRelation jitxOrderRelation,
                                                  boolean isHistoryOrder) {
        log.debug("Start BuildOcOrder OrderNo:{}", jitxOrderRelation.getOrderNo());
        IpBJitxOrder jitxOrder = jitxOrderRelation.getJitxOrder();
        OcBOrder order = new OcBOrder();
        //id自增长
        order.setId(sequenceUtil.buildOrderSequenceId());
        order.setModifierename(SystemUserResource.ROOT_USER_NAME);
        order.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        order.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(order);
        //20210602 斯凯奇项目追加合包业务
        //合包码
        order.setMergedCode(jitxOrder.getMergedCode());
        //合包单号
        order.setMergedSn(jitxOrder.getMergedSn());
        order.setJitxRequiresMerge(YesNoEnum.N.getVal()+"");
        if(StringUtils.isNotEmpty(jitxOrder.getMergedCode())){
            order.setJitxRequiresMerge(YesNoEnum.Y.getVal()+"");
        }
        order.setIsForbiddenDelivery(jitxOrder.getIsForbiddenDelivery());
        order.setIsInterecept(jitxOrder.getIsForbiddenDelivery());
        //订单表.tid
        order.setTid(jitxOrder.getOrderSn());
        // 合并单据后生成的订单,默认平台单号
        order.setMergeSourceCode(jitxOrder.getOrderSn());
        //单据编号
        order.setBillNo(sequenceUtil.buildBillNo());
        //平台单号信息
        order.setSourceCode(jitxOrder.getOrderSn());
        //下单店铺id.
        order.setCpCShopId(jitxOrder.getCpCShopId());
        //平台店铺标题
        CpShop shopInfo = null;
        if (jitxOrder.getCpCShopId() != null) {
            shopInfo = cpRpcService.selectShopById(jitxOrder.getCpCShopId());
        }
        if (shopInfo != null) {
            order.setCpCShopTitle(shopInfo.getCpCShopTitle());
            /*order.setCpCStoreEcode(shopInfo.getCpCStoreEcode());
            order.setCpCStoreEname(shopInfo.getCpCStoreEname());
            order.setCpCStoreId(shopInfo.getCpCStoreId());*/
            order.setCpCShopEcode(shopInfo.getEcode());
            order.setCpCShopSellerNick(shopInfo.getSellerNick());
        }
        //订单状态. 默认状态为50
        order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
        //配送费用。如果为空，则赋值0.
        order.setShipAmt(BigDecimal.ZERO);
        //货到付款服务费。如果为空，则赋值0.
        order.setServiceAmt(BigDecimal.ZERO);
        //到付代收金额. 如果是支付方式=到付，则赋值订单金额
        order.setCodAmt(BigDecimal.ZERO);
        order.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        //应收平台金额
        order.setJdReceiveAmt(BigDecimal.ZERO);
        //结算金额
        order.setJdSettleAmt(BigDecimal.ZERO);
        //是否开票.若【开票抬头】为空，则为否（0），若有值，则为是（1）
        order.setIsInvoice(0);
        //商品重量
        order.setWeight(BigDecimal.ZERO);
        //物流公司信息
        LogisticsInfo logisticsInfo = jitxOrderRelation.getLogisticsInfo();
        if (logisticsInfo != null) {
            order.setCpCLogisticsEcode(logisticsInfo.getCode());
            order.setCpCLogisticsEname(logisticsInfo.getName());
            order.setCpCLogisticsId(logisticsInfo.getId());
        }
        //发货实体仓
        CpCPhyWarehouse cpCPhyWarehouse = jitxOrderRelation.getCpCPhyWarehouse();
        if (cpCPhyWarehouse != null) {
            order.setCpCPhyWarehouseId(cpCPhyWarehouse.getId());
            order.setCpCPhyWarehouseEcode(cpCPhyWarehouse.getEcode());
            order.setCpCPhyWarehouseEname(cpCPhyWarehouse.getEname());
            //斯凯奇项目：将JITX要求发货仓赋值 在改仓成功后会清空 改仓失败则保留 发货单仓库依然变更 但是传平台发货时传原仓库
            order.setJitxRequiresDeliveryWarehouseId(cpCPhyWarehouse.getId());
            order.setJitxRequiresDeliveryWarehouseCode(cpCPhyWarehouse.getEcode());
            order.setJitxRequiresDeliveryWarehouseName(cpCPhyWarehouse.getEname());
            //如果实体仓是o2o仓库，对订单进行打标
            if (StringUtils.equals(cpCPhyWarehouse.getWhType(), OcBOrderConst.CP_C_PHY_WAREHOUSE_TYPE)) {
                order.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
            } else {
                order.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
            }
        }
        //物流编码
        order.setExpresscode(jitxOrder.getTransportNo());
        //下单时间
        order.setOrderDate(jitxOrder.getAddTime());

        OcBOrder addressOrder = new OcBOrder();
        addressOrder.setReceiverAddress(jitxOrder.getBuyerAddress());
        String newAddress = OrderAddressConvertUtil.convert(addressOrder);
        //买家收货详细地址
        order.setReceiverAddress(newAddress);
        //其他买家信息
        order.setReceiverName(jitxOrder.getBuyer());
        order.setReceiverMobile(jitxOrder.getBuyerMobile());
        order.setReceiverPhone(jitxOrder.getBuyerTel());
        order.setReceiverZip(jitxOrder.getBuyerPostcode());

        //省市区匹配
        this.parseRegionInfo(jitxOrder, order);

        order.setLogisticsCost(BigDecimal.ZERO);
        order.setBuyerMessage(jitxOrder.getRemark());

        //订单来源
        order.setOrderSource("唯品会JITX");
        //预售状态
        // order.setSysPresaleStatus(0);
        //wms撤回状态
        order.setWmsCancelStatus(0);
        //自动审核状态
        order.setAutoAuditStatus(0);
        //订单占单状态
        order.setOccupyStatus(0);
        //平台
        order.setPlatform(PlatFormEnum.VIP_JITX.getCode());
        //订单类型
        order.setOrderType(OrderTypeEnum.NORMAL.getVal());
        //付款时间
        order.setPayTime(jitxOrder.getAddTime());
        //是否店发
        order.setIsStoreDelivery(jitxOrder.getIsStoreDelivery());

        //商品数量 明细数量之和
        order.setQtyAll(buildTotalProductNum(jitxOrderRelation.getJitxOrderItemList()));
        //明细数据
        order.setSkuKindQty(jitxOrderRelation.getJitxOrderItemList() == null ?
                BigDecimal.ZERO : new BigDecimal(jitxOrderRelation.getJitxOrderItemList().size()));

        return order;
    }

    /**
     * 省市区匹配
     *
     * @param jitxOrder 订单表
     * @param order     全渠道订单表
     */
    private void parseRegionInfo(IpBJitxOrder jitxOrder, OcBOrder order) {
        ProvinceCityAreaInfo regionInfo = null;
        try {
            regionInfo = regionService.selectProvinceCityAreaInfo(jitxOrder.getBuyerProvince()
                    , jitxOrder.getBuyerCity()
                    , jitxOrder.getBuyerCounty());
            if (regionInfo.getProvinceInfo() != null) {
                order.setCpCRegionProvinceId(regionInfo.getProvinceInfo().getId());
                order.setCpCRegionProvinceEcode(regionInfo.getProvinceInfo().getCode());
            }
            if (regionInfo.getCityInfo() != null) {
                order.setCpCRegionCityId(regionInfo.getCityInfo().getId());
                order.setCpCRegionCityEcode(regionInfo.getCityInfo().getCode());
            }
            if (regionInfo.getAreaInfo() != null) {
                order.setCpCRegionAreaId(regionInfo.getAreaInfo().getId());
                order.setCpCRegionAreaEcode(regionInfo.getAreaInfo().getCode());
            }
            order.setCpCRegionProvinceEname(jitxOrder.getBuyerProvince());
            order.setCpCRegionCityEname(jitxOrder.getBuyerCity());
            order.setCpCRegionAreaEname(jitxOrder.getBuyerCounty());
        } catch (Exception ex) {
            log.error(LogUtil.format("调用省市区服务异常:{}"), Throwables.getStackTraceAsString(ex));
        }
    }

    /**
     * Sum
     * (订单明细中的商品价格)
     * PRICE
     *
     * @param list 订单明细
     * @return 商品总额
     */
    private BigDecimal buildTotalReceivedAmt(List<IpBJitxOrderItemEx> list) {
        BigDecimal total = BigDecimal.ZERO;
        if (list != null && list.size() != 0) {
            for (IpBJitxOrderItemEx item : list) {
                BigDecimal singlePrice = item.getPromotionPrice();
                if (singlePrice == null) {
                    singlePrice = item.getPrice();
                }
                singlePrice = singlePrice == null ? BigDecimal.ZERO : singlePrice;
                total = total.add(singlePrice);
            }

        }
        return total;
    }

    /**
     * 明细数量之和
     *
     * @param list 明细列表
     * @return 商品数量
     */
    private BigDecimal buildTotalProductNum(List<IpBJitxOrderItemEx> list) {
        Long num = 0L;
        if (list != null && list.size() != 0) {
            for (IpBJitxOrderItemEx item : list) {
                Long itemTotal = item.getQuantity();
                if (itemTotal == null) {
                    item.setQuantity(0L);
                    itemTotal = 0L;
                }
                num = num + itemTotal;
            }
        }
        return BigDecimal.valueOf(num);
    }

    /**
     * 转换订单明细表
     *
     * @param ocBOrder      全渠道订单
     * @param jitxOrderItem 中间表明细
     * @return 订单明细表
     */
    private OcBOrderItem buildOrderItemFromJitxOrderItem(OcBOrder ocBOrder, IpBJitxOrderItemEx jitxOrderItem) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(item);

        //退货金额
        item.setAmtRefund(BigDecimal.ZERO);
        //国际码
        item.setBarcode(jitxOrderItem.getBarcode());
        int productType = SkuType.NORMAL_PRODUCT; //正常商品
        ProductSku prodSku = jitxOrderItem.getProdSku();
        if (prodSku != null) {
            item.setPsCProId(prodSku.getProdId());
            item.setPsCProEcode(prodSku.getProdCode());
            item.setPsCProEname(prodSku.getName());
            item.setPsCSkuId(prodSku.getId());
            item.setPsCSkuEcode(prodSku.getEcode());
            item.setPsCClrEcode(prodSku.getColorCode());
            item.setPsCClrEname(prodSku.getColorName());
            item.setPsCClrId(prodSku.getColorId());
            item.setPsCSizeEcode(prodSku.getSizeCode());
            item.setPsCSizeEname(prodSku.getSizeName());
            item.setPsCSizeId(prodSku.getSizeId());
            item.setPsCProMaterieltype(prodSku.getMaterialType());
            item.setSkuSpec(prodSku.getSkuSpec());
            item.setStandardWeight(prodSku.getWeight());
            //标准价
            item.setPriceList(prodSku.getPricelist());
            //2019-08-29 增加吊牌价和性别
            item.setSex(prodSku.getSex());
            item.setPriceTag(prodSku.getPricelist());
            String isEnableExpiry = prodSku.getIsEnableExpiry();
            item.setMDim4Id(prodSku.getMDim4Id());
            item.setMDim6Id(prodSku.getMDim6Id());
            if ("Y".equals(isEnableExpiry)) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            //组合商品类型 断平台订单明细对应的商品是否为组合商品或者福袋商品？ 记录对应的状态
            productType = combinationProductType(prodSku.getSkuType());
            if (productType == SkuType.COMBINE_PRODUCT) {
                ocBOrder.setIsCombination(1);
            }
        } else {
            item.setStandardWeight(BigDecimal.ZERO);
            item.setPriceList(BigDecimal.ZERO);
        }
        item.setProType(Long.valueOf(productType));
        //标题
        item.setTitle(jitxOrderItem.getProductName());
        //优惠金额
        item.setAmtDiscount(BigDecimal.ZERO);
        //退货数量
        item.setQtyRefund(BigDecimal.ZERO);
        item.setRefundStatus(0);

        //数量
        item.setQty(jitxOrderItem.getQuantity() != null ?
                BigDecimal.valueOf(jitxOrderItem.getQuantity()) : BigDecimal.ZERO);
        //单行实际成交金额
        BigDecimal singlePrice = jitxOrderItem.getPromotionPrice();
        if (singlePrice == null) {
            singlePrice = jitxOrderItem.getPrice();
        }
        singlePrice = singlePrice == null ? BigDecimal.ZERO : singlePrice;
        item.setRealAmt(item.getQty().multiply(singlePrice).setScale(4,
                RoundingMode.HALF_EVEN));
        //成交价格
        item.setPrice(singlePrice);
        //调整金额
        BigDecimal priceListAmt = item.getQty().multiply(item.getPrice()).setScale(4,
                RoundingMode.HALF_EVEN);
        item.setAdjustAmt(item.getRealAmt().subtract(priceListAmt));

        //平台单号
        item.setTid(ocBOrder.getTid());
        //子订单编号(明细编号)
//        item.setOoid(ocBOrder.getTid() != null ? Long.valueOf(ocBOrder.getTid()): null);
        //liqb 更改ooid类型从Long类型改成String类型
        item.setOoid(StringUtils.isNotEmpty(ocBOrder.getTid()) ? ocBOrder.getTid() : null);

        //转单把PO号带入订单管理 edit by lwf 2020/6/9 17:17
        item.setJitxPoNo(jitxOrderItem.getPoNo());
        return item;
    }

    /**
     * 获取商品类型
     *
     * @param skuType 通用item
     * @return 是否是组合商品
     */
    private int combinationProductType(int skuType) {
        if (skuType == SkuType.COMBINE_PRODUCT) {
            return SkuType.COMBINE_PRODUCT;
        } else if (skuType == SkuType.GIFT_PRODUCT) {
            return SkuType.GIFT_PRODUCT;
        } else if (skuType == SkuType.PRE_SALE_PRODUCT) {
            return SkuType.PRE_SALE_PRODUCT;
        } else {
            return SkuType.NORMAL_PRODUCT;
        }
    }

    /**
     * 订单
     *
     * @param jitxOrder      订单中间表关联关系
     * @param isHistoryOrder 是否为历史订单信息
     * @return 零售发货单关联对象
     */
    public OcBOrderRelation jitxOrderToOrder(IpJitxOrderRelation jitxOrder,
                                             boolean isHistoryOrder) {
        OcBOrderRelation orderRelation = new OcBOrderRelation();
        OcBOrder orderInfo = this.buildOcBOrderFromIpJitxOrder(jitxOrder, isHistoryOrder);
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        //获取根据条码分组的时效订单明细
        Map<String, List<IpBTimeOrderVipItem>> skuTimeOrderItemMap = jitxOrder.getSkuTimeOrderItemMap();
        for (IpBJitxOrderItemEx jitxItem : jitxOrder.getJitxOrderItemList()) {
            OcBOrderItem item = this.buildOrderItemFromJitxOrderItem(orderInfo, jitxItem);
            if (MapUtils.isNotEmpty(skuTimeOrderItemMap)) {
                List<IpBTimeOrderVipItem> timeOrderVipItems = skuTimeOrderItemMap.get(jitxItem.getBarcode());
                if (CollectionUtils.isNotEmpty(timeOrderVipItems)) {
                    List<Long> skuTimeOrderIdList = timeOrderVipItems.stream().filter(x -> x.getIpBTimeOrderVipId() != null).map(IpBTimeOrderVipItem::getIpBTimeOrderVipId).distinct().collect(Collectors.toList());
                    item.setTimeOrderId(StringUtils.join(skuTimeOrderIdList, ","));
                }
            }
            orderItemList.add(item);
        }
        //商品总额
        orderInfo.setProductAmt(buildTotalProductAmt(orderItemList));
        //应收金额
        orderInfo.setAmtReceive(orderInfo.getProductAmt());
        // 已收金额
        orderInfo.setReceivedAmt(orderInfo.getProductAmt());
        //调整金额
        orderInfo.setAdjustAmt(buildTotalAdjustAmt(orderItemList));
        //订单优惠金额
        orderInfo.setOrderDiscountAmt(buildTotalOrderDiscountAmt(orderItemList));
        //商品优惠金额
        orderInfo.setProductDiscountAmt(buildTotalProductDiscountAmt(orderItemList));
        //订单总额.“商品总额”+“物流费用”+“调整金额”-“订单优惠金额”-“商品优惠金额”
        BigDecimal orderAmt = orderInfo.getProductAmt().add(orderInfo.getShipAmt()).add(orderInfo.getAdjustAmt())
                .subtract(orderInfo.getOrderDiscountAmt()).subtract(orderInfo.getProductDiscountAmt());
        orderInfo.setOrderAmt(orderAmt);
        //jitx订单增加合单加密信息
        MD5Util.encryptOrderInfo4Merge(orderInfo);
        orderRelation.setOrderInfo(orderInfo);
        orderRelation.setOrderItemList(orderItemList);

        return orderRelation;
    }

    /**
     * Sum
     * (订单明细的商品标准价*数量)
     * PriceList * Qty
     *
     * @param list 订单明细
     * @return 商品总额
     */
    private BigDecimal buildTotalProductAmt(List<OcBOrderItem> list) {
        BigDecimal total = BigDecimal.ZERO;
        if (list != null && list.size() != 0) {
            for (OcBOrderItem item : list) {
                total = total.add(item.getPrice().multiply(item.getQty())).setScale(2,
                        BigDecimal.ROUND_CEILING);
            }

        }
        return total;
    }

    /**
     * Sum
     * (订单明细的调整金额)
     * PRICE
     *
     * @param list 订单明细
     * @return 调整总额
     */
    private BigDecimal buildTotalAdjustAmt(List<OcBOrderItem> list) {
        BigDecimal total = BigDecimal.ZERO;
        if (list != null && list.size() != 0) {
            for (OcBOrderItem item : list) {
                if (item.getAdjustAmt() == null) {
                    item.setAdjustAmt(BigDecimal.ZERO);
                }
                total = total.add(item.getAdjustAmt());
            }

        }
        return total;
    }

    /**
     * Sum
     * (订单明细的优惠金额)
     * AmtDiscount
     *
     * @param list 订单明细
     * @return 商品优惠金额总额
     */
    private BigDecimal buildTotalProductDiscountAmt(List<OcBOrderItem> list) {
        BigDecimal total = BigDecimal.ZERO;
        if (list != null && list.size() != 0) {
            for (OcBOrderItem item : list) {
                if (item.getAmtDiscount() == null) {
                    item.setAmtDiscount(BigDecimal.ZERO);
                }
                total = total.add(item.getAmtDiscount());
            }

        }
        return total;
    }

    /**
     * Sum
     * (订单明细的整单平摊金额)
     * OrderSplitAmt
     *
     * @param list 订单明细
     * @return 订单优惠金额总额
     */
    private BigDecimal buildTotalOrderDiscountAmt(List<OcBOrderItem> list) {
        BigDecimal total = BigDecimal.ZERO;
        if (list != null && list.size() != 0) {
            for (OcBOrderItem item : list) {
                if (item.getOrderSplitAmt() == null) {
                    item.setOrderSplitAmt(BigDecimal.ZERO);
                }
                total = total.add(item.getOrderSplitAmt());
            }

        }
        return total;
    }
}
