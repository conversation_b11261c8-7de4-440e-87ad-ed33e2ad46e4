package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

/**
 * 订单付款信息表
 *
 * @date 2019/3/14
 * @author: ming.fz
 */
@Mapper
@Component
public interface OcBOrderPaymentFiMapper extends ExtentionMapper<OcBOrderPayment> {

    class UpdateRecord {
        public String updateSql(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("OC_B_ORDER_PAYMENT");
                    for (String key : map.keySet()) {
                        if (!"id".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("ID = #{id}");
                }
            }.toString();
        }
    }

    /**
     * 更新订单信息
     *
     * @param jsonObject
     * @return
     */
    @UpdateProvider(type = OcBOrderPaymentFiMapper.UpdateRecord.class, method = "updateSql")
    int updateRecord(JSONObject jsonObject);

    /**
     * 查询 付款信息表 是否存在记录
     *
     * @param orderId 订单Id
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order_payment WHERE oc_b_order_id=#{orderId}")
    Integer getCountbyId(Long orderId);

    /**
     * 获取 付款信息表主键
     *
     * @param orderId
     * @return
     */
    @Select("SELECT id FROM oc_b_order_payment WHERE oc_b_order_id=#{orderId}")
    Long getOrderPaymentId(Long orderId);

}
