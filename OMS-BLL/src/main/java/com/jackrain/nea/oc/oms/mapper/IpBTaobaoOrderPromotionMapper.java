package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderPromotion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper
public interface IpBTaobaoOrderPromotionMapper extends ExtentionMapper<IpBTaobaoOrderPromotion> {

    @Select("SELECT * FROM ip_b_taobao_order_promotion WHERE ip_b_taobao_order_id=#{orderId}")
    List<IpBTaobaoOrderPromotion> selectOrderPromotionList(long orderId);
}