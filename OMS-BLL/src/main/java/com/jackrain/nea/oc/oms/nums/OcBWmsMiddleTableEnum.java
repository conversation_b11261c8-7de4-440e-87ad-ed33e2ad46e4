package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2025/3/31 17:20
 * @Description
 */
@Getter
public enum OcBWmsMiddleTableEnum {

    OC_B_REFUND_IN_TASK(1, "OC_B_REFUND_IN_TASK", "B2C退货回传中间表"),
    OC_B_REFUND_PACKAGE_TASK(2, "OC_B_REFUND_PACKAGE_TASK", "退货包裹状态中间表");

    Integer value;
    String table;
    String desc;

    OcBWmsMiddleTableEnum(Integer value, String table, String desc) {
        this.value = value;
        this.table = table;
        this.desc = desc;
    }
}
