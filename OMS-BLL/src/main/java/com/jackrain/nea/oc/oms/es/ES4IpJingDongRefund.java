package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.es.util.SpecialElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongReturnOrderExt;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 11:32 上午
 */
public class ES4IpJingDongRefund {

    /**
     *
     *根据转换状态查询服务单号
     *
     * @param pageIndex 页数
     * @param pageSize 每页大小
     * @return java.util.List<java.lang.String>
     * @Descroption 获取京东中间表分库键
     */
    public static List<String> selectJingdongRefundKey(int pageIndex, int pageSize) {
        List<String> list = new ArrayList<>();
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject whereKeys = new JSONObject();
        // 服务单号
        String[] returnFields = {"AFSSERVICEID"};
        // 未转换0
        whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());
        JSONObject search = ElasticSearchUtil.search(JingdongReturnOrderExt.TABLENAM_IPBJINGDONGREFUND,
                JingdongReturnOrderExt.TABLENAM_IPBJINGDONGREFUND, whereKeys, null,
                null, pageSize, startIndex, returnFields);
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getString("AFSSERVICEID"));
            }
        }
        return list;
    }


}
