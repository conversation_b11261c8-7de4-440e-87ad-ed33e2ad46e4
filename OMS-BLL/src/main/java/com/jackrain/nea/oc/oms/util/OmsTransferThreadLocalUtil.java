package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.oc.oms.model.relation.ReissueRelation;
import com.jackrain.nea.oc.oms.model.relation.StepExecInfo;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/2/23
 */
public class OmsTransferThreadLocalUtil {

    public static final ThreadLocal<StepExecInfo> execInfo = new ThreadLocal<>();

    public static final ThreadLocal<List<String>> stepMsg = ThreadLocal.withInitial(() -> new ArrayList<>());

    public static final ThreadLocal<List<RedisReentrantLock>> locks = ThreadLocal.withInitial(() -> new ArrayList<>());

    /**
     * 退货补寄关系
     */
    public static final ThreadLocal<Map<Long, ReissueRelation>> returnReissueLocal = ThreadLocal.withInitial(() -> new HashMap<>());

}
