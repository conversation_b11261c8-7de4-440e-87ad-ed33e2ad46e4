package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchangeExt;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
@Mapper
public interface OcBReturnOrderExchangeMapper extends ExtentionMapper<OcBReturnOrderExchange> {
    @Update("UPDATE oc_b_return_order_exchange  SET qty_exchange=#{qtyExchange} WHERE id=#{id} and oc_b_return_order_id=#{ocBreturnOrderId}")
    int updateByID(@Param("qtyExchange") BigDecimal qtyExchange, @Param("id") Long id, @Param("ocBreturnOrderId") Long ocBreturnOrderId);


    @Select("select * from oc_b_return_order_exchange where oc_b_return_order_id =#{id} limit #{index},#{count} ")
    List<OcBReturnOrderExchangeExt> selectByReturnId(@Param("id") String id, @Param("count") Integer count, @Param("index") int startIndex);

    @Select("select count(1) from oc_b_return_order_exchange where oc_b_return_order_id =#{id}")
    Integer selectExchangeCount(@Param("id") String id);

    @Select("select id ,ps_c_pro_ecode,sku_spec,amt_refund,qty_in,qty_exchange from oc_b_return_order_exchange where oc_b_return_order_id =#{id}")
    List<JSONObject> selectSomeFile(@Param("id") Long id);

    @Delete("DELETE FROM oc_b_return_order_exchange WHERE oc_b_return_order_id =#{id}")
    int deleteByReturnOrderId(@Param("id") Long id);

    @Select("select * from oc_b_return_order_exchange where oc_b_return_order_id =#{id}")
    List<OcBReturnOrderExchange> selectByReturnIdList(@Param("id") Long id);

    @SelectProvider(type = SqlProvider.class, method = "selectReturnExchageByPIds")
    List<OcBReturnOrderExchange> selectReturnExchageByPIds(@Param("pIds") String pIds);

    @SelectProvider(type = SqlProvider.class, method = "selectReturnExchangeSomeFile")
    List<JSONObject> selectReturnExchangeSomeFile(@Param("pIds") String pIds);

    class SqlProvider {

        /**
         * 退换货查询优化
         *
         * @param pIds
         * @return
         */
        public String selectReturnExchageByPIds(@Param("pIds") String pIds) {
            StringBuilder sb = new StringBuilder();
            sb.append("select * from oc_b_return_order_exchange where oc_b_return_order_id in (");
            sb.append(pIds);
            sb.append(" )");
            return sb.toString();
        }

        public String selectReturnExchangeSomeFile(@Param("pIds") String pIds) {
            StringBuilder sb = new StringBuilder();
            sb.append("select oc_b_return_order_id,id,ps_c_pro_ecode,sku_spec,amt_refund,qty_in,qty_exchange from ");
            sb.append("oc_b_return_order_exchange where oc_b_return_order_id in (");
            sb.append(pIds);
            sb.append(" )");
            return sb.toString();
        }

    }


}
