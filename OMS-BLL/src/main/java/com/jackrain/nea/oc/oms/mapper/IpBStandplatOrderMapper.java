package com.jackrain.nea.oc.oms.mapper;

import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.Date;
import java.util.List;

@Mapper
public interface IpBStandplatOrderMapper extends ExtentionMapper<IpBStandplatOrder> {

    //--------- ming.fz begin

    /**
     * REMARK最长数量。默认200字符
     */
    int MAX_REMARK_LENGTH = 200;

    /**
     * 依据TID进行查通用订单数据
     *
     * @param tid 平台交易单号
     * @return 通用订单数据
     */
    @Select("SELECT * FROM IP_B_STANDPLAT_ORDER WHERE TID=#{tid}")
    IpBStandplatOrder selectStandplatOrderByTid(@Param("tid") String tid);

    @Select("SELECT id FROM IP_B_STANDPLAT_ORDER WHERE TID=#{tid}")
    Long selectIdByTid(@Param("tid") String tid);

    /**
     * @param tid
     * @return
     * @20200830 查询活动ID字段（通用平台-爱库存）
     */
    @Select("SELECT activityid FROM IP_B_STANDPLAT_ORDER WHERE TID=#{tid}")
    String selectActivityidByTid(@Param("tid") String tid);

    /**
     * 依据TIDS进行查通用订单数据
     *
     * @param tids 平台交易单号
     * @return 通用订单数据
     */
    @SelectProvider(type = IpBStandplatOrderMapper.standplatOrderSqlBuilder.class, method = "selectStandplatOrderByTids")
    List<IpBStandplatOrder> selectStandplatOrderByTids(@Param("tids") List<String> tids);


    /**
     * 更新订单转换状态
     *
     * @param orderNo 订单编号
     * @param isTrans 转换状态
     * @param remarks 转换备注信息
     * @return 更新结果
     */
    @UpdateProvider(type = IpBStandplatOrderMapper.standplatOrderSqlBuilder.class, method = "buildUpdateOrderTransSQL")
    int updateOrderIsTrans(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                           @Param("remarks") String remarks, @Param("date") Date date, @Param("abnormalType") Integer abnormalType);

    /**
     * 淘宝订单SQL创建器
     */
    class standplatOrderSqlBuilder {

        public String selectStandplatOrderByTids(@Param("tids") List<String> tids) {
            StringBuilder sql = new StringBuilder();
            sql.append(" SELECT * FROM IP_B_STANDPLAT_ORDER WHERE TID IN");
            sql.append("('").append(StringUtils.join(tids, "','")).append("')");
            return sql.toString();
        }

        /**
         * 创建更新订单转换状态SQL
         *
         * @param orderNo 订单编号
         * @param isTrans 转换状态
         * @param remarks 转换备注信息
         * @return 更新SQL语句
         */
        public String buildUpdateOrderTransSQL(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                                               @Param("remarks") String remarks, @Param("date") Date date, @Param("abnormalType") Integer abnormalType) {

            return new SQL() {
                {
                    UPDATE("ip_b_standplat_order");
                    SET("istrans=#{isTrans}", "abnormal_type = NULL ");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    SET("sysremark=#{remarks}");
                    SET("transdate = #{date}");
                    SET("modifieddate = #{date}");
                    SET("abnormal_type=#{abnormalType}");
                    WHERE("tid=#{orderNo}");
                }
            }.toString();
        }
    }
}