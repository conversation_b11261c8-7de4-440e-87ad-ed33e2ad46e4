package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.model.table.StCBoxStrategyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface StCBoxStrategyMapper extends BaseMapper<StCBoxStrategyEntity> {

    @Select("select max(id) from st_c_box_strategy")
    Integer getMaxId();

    @Select("SELECT * FROM st_c_box_strategy WHERE order_goods = #{orderGoods} and ISACTIVE = 'Y'")
    StCBoxStrategyEntity selectEnableStraetgy(@Param("orderGoods") String orderGoods);

}

