package com.jackrain.nea.oc.oms.constant;

/**
 * @ClassName NaiKaOrderTypeConstant
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/7/10 16:59
 * @Version 1.0
 */
public class NaiKaOrderTypeConstant {

    /**
     * 电子奶卡销售 已废弃
     */
    @Deprecated
    public static final Integer VIRTUAL_CARD = 1;

    /**
     * 实体奶卡销售 已废弃
     */
    @Deprecated
    public static final Integer CARD_ORDER = 2;

    /**
     * 小程序单品订单
     */
    public static final Integer PRODUCT_ORDER = 3;

    /**
     * 周期购订单
     */
    public static final Integer PVLE_ORDER = 4;

    /**
     * 奶卡提货订单
     */
    public static final Integer CARD_PLAN = 5;

    /**
     * 周期购提货订单
     */
    public static final Integer CYLE_PLAN = 6;

    /**
     * 电子奶卡销售
     */
    public static final Integer VIRTUAL_CARD_NEW = 7;

    /**
     * 实体奶卡销售
     */
    public static final Integer CARD_ORDER_NEW = 8;

    /**
     * 线上免费奶卡(在8的基础上 明细行成交单价<=0.5元)
     */
    public static final Integer FREE_CARD_ORDER = 9;

    /**
     * 免费周期购(在4的基础上 明细行成交单价<=0.5元)
     */
    public static final Integer FREE_PVLE_ORDER = 10;

}
