package com.jackrain.nea.oc.oms.services.advance;

/**
 * @program: r3-oc-oms
 * @description: 预售的常量类
 * @author: liuwj
 * @create: 2021-06-11 14:24
 **/
public class AdvanceConstant {

    //解析预计发货时间 方式1
    public  static  final String  way_1 ="点前";

    //解析预计发货时间 方式2
    public  static  final String  way_2 ="付款后";


    //预售类型  平台定金预售
    public  static  final String PLATFORM_DEPOSIT_PRE_SALE  ="平台定金预售";

    //预售类型  平台全款预售
    public  static  final String PLATFORM_FULL_PRE_SALE  ="平台全款预售";

    //预售类型  店铺预售
    public  static  final String  STORE_PRE_SALE  ="商家自定义预售";

    //预售类型  普通商品
    public  static  final String ORDINARY_GOODS  ="普通商品";

    //调用sg 预售活动传入的类型
    public  static  final Integer TYPE_1  =1;

    //卡单状态
    public  static  final Integer DETENTION_STATUS_0  =0;  //未卡单

    public  static  final Integer DETENTION_STATUS_1  =1;  //卡单

    public  static  final Integer DETENTION_STATUS_2  =2;  //卡单释放

    // 时间类型
    // 下单时间
    public final static String ORDER_DATE = "1";
    // 支付时间
    public final static String PAY_TIME = "2";

    //识别类型
    public  static  final String PTSKU  ="PTSKU";
    public  static  final String TPID  ="TPID";
    public  static  final String MPRO  ="MPRO";
    public static final String MSKU = "MSKU";
    public static final String PNUM = "PNUM";
    public static final String MNAM = "MNAM";
    public static final String ARESS = "ARESS";
    public static final String NUTL = "NUTL";
    //省
    public static final String PRO = "PRO";
    public static final String NUM = "NUM";
    public static final String PRICE = "PRICE";
    public static final String SKU = "SKU";

    public static final String ANCHOR = "ANCHOR";

    //卖家备注
    public static final String SEREMARK = "SEREMARK";

    //买家昵称
    public static final String NICK = "NICK";
    //商品数量大于
    public static final String SKUQTY_GT = "SKUQTY_GT";
    //商品数量大于等于
    public static final String SKUQTY_GE = "SKUQTY_GE";
    //商品数量小于
    public static final String SKUQTY_LT = "SKUQTY_LT";
    //商品数量小于等于
    public static final String SKUQTY_LE = "SKUQTY_LE";
    //金额大于
    public  static  final String AMT_GT  ="AMT_GT";
    //金额大于等于
    public  static  final String AMT_GE  ="AMT_GE";
    //金额小于
    public  static  final String AMT_LT  ="AMT_LT";
    //金额小于等于
    public  static  final String AMT_LE  ="AMT_LE";
    //款色数量大于
    public  static  final String SKU_GT  ="SKU_GT";
    //款色数量大于等于
    public  static  final String SKU_GE  ="SKU_GE";
    //款色数量小于
    public  static  final String SKU_LT  ="SKU_LT";
    //款色数量小于等于
    public  static  final String SKU_LE  ="SKU_LE";



    // 是否
    // Y 是
    public final static String YES = "Y";
    // N 否
    public final static String NO = "N";

    // 时点类型
    // 指定时点释放
    public final static String RELEASE_TIME_TYPE_1 = "1";
    // 固定时长后释放
    public final static String RELEASE_TIME_TYPE_2 = "2";

    // 分钟
    public final static String TIME_UNIT_MINUTE = "1";
    // 小时
    public final static String TIME_UNIT_HOUR = "2";
    // 天
    public final static String TIME_UNIT_DAY = "3";

    // 0  淘宝中间给的有发货时间
    public final static String IS_EXIST_CON_TIME_0 = "0";
    // 1  预售活动发货时间
    public final static String IS_EXIST_CON_TIME_1 = "1";
}
