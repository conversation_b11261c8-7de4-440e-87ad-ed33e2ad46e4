package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemTableNames;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.springframework.boot.autoconfigure.AutoConfigureOrder;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @author: 易邵峰
 * @since: 2019-03-14
 * create at : 2019-03-14 10:15
 */
@Component
@AutoConfigureOrder(Integer.MIN_VALUE)
public class BuildSequenceUtil {

    private static final int ONE_DAY_SECONDS = 24 * 60 * 60;

    public long buildOrderLinkSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_LINK_TABLE_NAME);
    }

    public long buildOrderSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_TABLE_NAME);
    }

    public long buildOrderExtSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_EXT_TABLE_NAME);
    }

    public long buildOrderItemSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_ITEM_TABLE_NAME);
    }

    public long buildEqualExchangeItemSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_EQUAL_EXCHANGE_ITEM);
    }


    public long buildOrderNaiKaSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_NAIKA_TABLE_NAME);
    }

    public long buildPreOrderSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_PRE_ORDER_TABLE_NAME);
    }

    public long buildPreOrderItemSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_PRE_ORDER_ITEM_TABLE_NAME);
    }

    public long buildOrderPaymentSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_PAYMENT_TABLE_NAME);
    }

    public long buildOrderPromotionSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_PROMOTION_TABLE_NAME);
    }

    public long buildOrderTaobaoSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_TAOBAO_TABLE_NAME);
    }

    public long buildOrderNaiKaUnfreezeSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_NAIKA_UNFREEZE_TABLE_NAME);
    }

    public long buildOrderSendLogId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_SEND_LOG);
    }

    public long buildOrderItemExtSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_ITEM_EXT_TABLE_NAME);
    }

    private Long getBillSequence() {
        String currentDayRedisKey = BllRedisKeyResources.getCurrentBillNoSequence();
        CusRedisTemplate<String, String> redisTemplate = RedisMasterUtils.getStrRedisTemplate();
        Boolean isExist = redisTemplate.hasKey(currentDayRedisKey);
        if (!isExist) {
            redisTemplate.opsForValue().set(currentDayRedisKey, "0", ONE_DAY_SECONDS, TimeUnit.SECONDS);
        }

        return redisTemplate.opsForValue().increment(currentDayRedisKey, 1L);
    }

    public String buildBillNo() {
        // 生成规则：序号生成器OM+年月日+8位流水
        long id = this.getBillSequence();
        String seq = String.format("%08d", id);
        return "OM" + Tools.dayFormatter.format(new Date()) + seq;
    }

    public String buildPayableAdjustmentBillNo() {
        long id = this.getBillSequence();
        String seq = String.format("%08d", id);
        return "AP01" + Tools.dayFormatter.format(new Date()) + seq;
    }

    /**
     * 生成退货的单据编号
     *
     * @return
     */
    public String buildReturnBillNo() {
        // 生成规则：序号生成器OM+年月日+8位流水
        long id = this.getBillSequence();
        String seq = String.format("%08d", id);
        return "TH" + Tools.dayFormatter.format(new Date()) + seq;
    }

    /**
     * 发后后退款单单据编号生成规则
     *
     * @return
     */
    public String aFbuildBillNo() {
        // 生成规则：序号生成器OM+年月日+8位流水
        long id = this.getBillSequence();
        String seq = String.format("%07d", id);
        return "AF01" + Tools.dayFormatter.format(new Date()) + seq;
    }

    public long buildSequenceId(String tableName) {
        return ModelUtil.getSequence(tableName);
    }

    public long buildToBeConfirmedTaskId() {
        return ModelUtil.getSequence(SystemTableNames.OC_TOBECONFIRMED_TASK_NAME);
    }

    public long buildAuditTaskId() {
        return ModelUtil.getSequence(SystemTableNames.OC_AUDIT_TASK_NAME);
    }

    public long buildJitxDealerOrderTaskId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_JITX_DEALER_ORDER_TASK);
    }

    public long buildOcBToWmsTaskId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_TO_WMS_TASK_NAME);
    }

    public long buildOcBWarehouseSplitTaskId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_WAREHOUSE_SPLIT_TASK_NAME);
    }

    public long buildSplitOrderTaskId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_SPLIT_TASK_NAME);
    }

    public long buildSplitSkuOrderTaskId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_SKU_SPLIT_TASK_NAME);
    }

    public long buildJingdongSplitOrderTaskId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_JINGDONG_SPLIT_TASK_NAME);
    }

    /**
     * @return
     * @20200721 获取实例，懒得autoware
     */
    public static BuildSequenceUtil getInstance() {
        return ApplicationContextHandle.getBean(BuildSequenceUtil.class);
    }

    /**
     * description:wing处理发货的Sequence
     * @Author: liuwenjin
     * @Date 2021/9/26 5:32 下午
     */
    public long buildOcBToWingDeliveryTaskId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_TOWING_DELIVERY_TASK);
    }

    public long buildAutoReleaseHangId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_AUTO_RELEASE_HANG_TASK_NAME);
    }


    public long buildCardCodeVoidSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.CARD_CODE_VOID);
    }

    public long buildOcBOrderOutStockRecordId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_OUTSTOCK_RECORD);
    }

    public long buildDeliveryFailId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_DELIVERY_FAIL);
    }

    public long buildCommonIdempotentSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.COMMON_IDEMPOTENT);
    }

    public long buildAddServiceSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_TABLE_NAME);
    }

    public long buildOrderNoSplitSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_NO_SPLIT);
    }

    public long buildShortStockNoSplitStrategySequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY);
    }

    public long buildShortStockNoSplitStrategyDetailSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY_DETAIL);
    }

    public long buildOrderSourceRelationSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_ORDER_SOURCE_RELATION);
    }

    public long buildStCCycleStrategySequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_CYCLE_STRATEGY);
    }

    public long buildStCCycleRuleStrategySequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_CYCLE_RULE_STRATEGY);
    }

    public long buildStCCycleItemStrategySequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_CYCLE_ITEM_STRATEGY);
    }

    public long buildToBOrderSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_TO_B_ORDER);
    }

    public long buildOrderBnTaskSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_ORDER_BN_TASK);
    }

    public long buildStCImperfectStrategySequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_IMPERFECT_STRATEGY);
    }

    public long buildStCImperfectStrategyItemSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_IMPERFECT_STRATEGY_ITEM);
    }

    public long buildStCRemarkGiftStrategySequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_REMARK_GIFT_STRATEGY);
    }

    public long buildStCDropshipBasePriceStrategySequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_DROPSHIP_BASE_PRICE_STRATEGY);
    }

    public long buildStCDropshipBasePriceStrategyDetailSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.ST_C_DROPSHIP_BASE_PRICE_STRATEGY_DETAIL);
    }

    public long buildShopSkuBatchInfoSequenceId() {
        return ModelUtil.getSequence(SystemTableNames.OC_B_SHOP_SKU_BATCH_INFO);
    }
}
