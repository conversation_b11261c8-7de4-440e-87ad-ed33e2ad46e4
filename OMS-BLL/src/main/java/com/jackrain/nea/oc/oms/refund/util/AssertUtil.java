package com.jackrain.nea.oc.oms.refund.util;

import com.jackrain.nea.exception.NDSException;

import java.util.List;
import java.util.Map;

/**
 * @Desc : 断言工具
 * <AUTHOR> xiWen
 * @Date : 2020/11/3
 */
public class AssertUtil {

    /**
     * 是否异常
     *
     * @param isThrow boolean
     * @param msg     exception message
     */
    public static void assertException(boolean isThrow, String msg) {
        if (isThrow) {
            throw new NDSException(msg);
        }
    }

    public static void isTrue(boolean val, String msg) {
        if (!val) {
            throw new NDSException(msg);
        }
    }

    /**
     * 非null
     *
     * @param obj Object
     * @param msg exception message
     */
    public static void notNull(Object obj, String msg) {
        if (obj == null) {
            throw new NDSException(msg);
        }
    }

    /**
     * 非空
     *
     * @param list List
     * @param msg  Exception message
     */
    public static void notEmpty(List list, String msg) {
        if (list == null || list.size() < 1) {
            throw new NDSException(msg);
        }
    }

    /**
     * 非空
     *
     * @param map Map
     * @param msg Exception message
     */
    public static void notEmpty(Map map, String msg) {
        if (map == null || map.size() < 1) {
            throw new NDSException(msg);
        }
    }

    /**
     * 不包含Null值
     *
     * @param list List
     * @param msg  Exception message
     */
    public static void notContainNullVal(List list, String msg) {
        if (containNullVal(list)) {
            throw new NDSException(msg);
        }
    }

    /**
     * 是否包含null
     *
     * @param list List
     * @return true/false
     */
    public static boolean containNullVal(List list) {
        for (Object next : list) {
            if (next == null) {
                return true;
            }
        }
        return false;
    }

    /**
     * 空 ?  不等于1
     *
     * @param list List
     * @param msg  Exception message
     */
    public static void nEOneException(List list, String msg) {
        if (list == null || list.size() != 1) {
            throw new NDSException(msg);
        }
    }

    private AssertUtil() {
    }
}
