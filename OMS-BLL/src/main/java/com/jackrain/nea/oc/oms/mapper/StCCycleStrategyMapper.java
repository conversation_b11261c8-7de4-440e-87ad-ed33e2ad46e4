package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.model.table.StCCycleStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * @ClassName StCCycleStrategyMapper
 * @Description 周期购促销策略
 * <AUTHOR>
 * @Date 2024/8/19 14:08
 * @Version 1.0
 */
@Mapper
public interface StCCycleStrategyMapper extends BaseMapper<StCCycleStrategy> {

    @Select("SELECT * FROM st_c_cycle_strategy WHERE shop_id = #{shopId} and ISACTIVE = 'Y'")
    List<StCCycleStrategy> selectByShopId(Long shopId);

    @Select("SELECT * FROM st_c_cycle_strategy WHERE shop_id = #{shopId} and type = #{type} and ISACTIVE = 'Y'")
    List<StCCycleStrategy> selectByShopIdAndType(@Param("shopId") Long shopId, @Param("type") Integer type);

    @Update("UPDATE st_c_cycle_strategy SET ISACTIVE = 'Y',modifieddate = now() WHERE ID = #{id}")
    int activeStrategy(Long id);

    @Update("UPDATE st_c_cycle_strategy SET ISACTIVE = 'N',modifieddate = now() WHERE ID = #{id}")
    int voidStrategy(Long id);

    @Select("SELECT * FROM st_c_cycle_strategy WHERE ID = #{strategyId} and ISACTIVE = 'Y'")
    StCCycleStrategy selectByStrategyId(Long strategyId);

    @Select("SELECT * FROM st_c_cycle_strategy WHERE shop_id = #{shopId} and  #{payTime} >=  start_time AND #{payTime} <= end_time and type = #{type} and ISACTIVE = 'Y'")
    List<StCCycleStrategy> selectByShopIdAndPayTimeAndType(@Param("shopId") Long shopId, @Param("payTime") Date payTime, @Param("type") Integer type);

}
