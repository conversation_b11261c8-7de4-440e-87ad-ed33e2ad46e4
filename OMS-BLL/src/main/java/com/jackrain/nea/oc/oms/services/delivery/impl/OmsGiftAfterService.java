package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.gsi.GSI4OrderItem;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2022/8/11 下午3:36
 * @Version 1.0
 */
@Slf4j
@Component
public class OmsGiftAfterService {
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;


    /**
     * 处理赠品后发订单
     *
     * @param ocBOrderRelation
     */
    public void handelGiftAfter(OcBOrderRelation ocBOrderRelation) {
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        try {
            List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
            //通过tid查询订单明细表  看是否有赠品后发的明细
            String tid = orderInfo.getTid();
            List<Long> orderIdList = GSI4OrderItem.selectOcBOrderItemByTid(tid + "");
            orderIdList.remove(orderInfo.getId());
            if (CollectionUtils.isEmpty(orderIdList)) {
                return;
            }
            //查询明细
            List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItemsByOrderIds(orderIdList);
            //判断是否有赠品后发的订单
            itemList = itemList.stream().filter(p -> p.getIsGiftSplit() != null && p.getIsGiftSplit() == 3).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemList)) {
                return;
            }
            List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
            List<OcBOrderItem> noGiftRelation = itemList.stream().filter(p -> StringUtils.isEmpty(p.getGiftRelation())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noGiftRelation)) {
                ocBOrderItemList.addAll(noGiftRelation);
            }
            List<OcBOrderItem> ocBOrderItems = handelGiftRelation(itemList, orderItemList);
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                ocBOrderItemList.addAll(ocBOrderItems);
            }
            if (CollectionUtils.isEmpty(ocBOrderItemList)) {
                return;
            }
            Map<Long, List<OcBOrderItem>> itemMap = ocBOrderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
            List<OcBOrder> ocBOrderList = ocBOrderMapper.selectByIdsList(new ArrayList<>(itemMap.keySet()));
            if (CollectionUtils.isEmpty(ocBOrderList)) {
                return;
            }
            ocBOrderList = ocBOrderList.stream().filter(p -> p.getOrderStatus() != 7 && p.getOrderStatus() != 8).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(ocBOrderList)) {
                return;
            }
            for (OcBOrder order : ocBOrderList) {
                List<OcBOrderItem> orderItems = itemMap.get(order.getId());
                handleHoldGiftOrder(order, orderItems);
            }
        } catch (Exception e){
            log.error(LogUtil.format("赠品后发失败:{}", "赠品后发失败", orderInfo.getId()), Throwables.getStackTraceAsString(e));
        }
    }


    private void handleHoldGiftOrder(OcBOrder order, List<OcBOrderItem> ocBOrderItems) {
        //判断是否有延时发货
        List<OcBOrderItem> orderItems = ocBOrderItems.stream().filter(p -> p.getGiftDeliverNode() != null && p.getGiftDeliverNode() == 3).collect(Collectors.toList());
        Integer giftIntervalTime = null;
        if (CollectionUtils.isNotEmpty(orderItems)) {
            giftIntervalTime = orderItems.get(0).getGiftIntervalTime();
        }
        if (giftIntervalTime == null || giftIntervalTime ==0) {
            //直接将订单解hold
            order.setIsInterecept(0);
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.GIFT_AFTER);
            return;
        }
        //计算解hold时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.HOUR, giftIntervalTime);
        Date time = calendar.getTime();
        //变成延时姐hold时间
        ocBOrderHoldService.businessHold(order.getId(), OrderHoldReasonEnum.GIFT_AFTER, time);
    }


    /**
     * @param itemList
     * @param orderItemList
     */
    private List<OcBOrderItem> handelGiftRelation(List<OcBOrderItem> itemList, List<OcBOrderItem> orderItemList) {
        //判断是否为挂靠赠品
        List<OcBOrderItem> giftRelation = itemList.stream().filter(p -> StringUtils.isNotEmpty(p.getGiftRelation())).collect(Collectors.toList());
        List<OcBOrderItem> giftRelationList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(giftRelation)) {
            //判断挂靠赠品是不是挂靠在这个主品上
            Map<String, List<OcBOrderItem>> giftRelationMap = giftRelation.stream().collect(Collectors.groupingBy(OcBOrderItem::getGiftRelation));
            for (OcBOrderItem item : orderItemList) {
                String giftRelation1 = item.getGiftRelation();
                if (StringUtils.isNotEmpty(giftRelation1)) {
                    List<OcBOrderItem> itemList1 = giftRelationMap.get(giftRelation1);
                    if (CollectionUtils.isNotEmpty(itemList1)) {
                        giftRelationList.addAll(itemList1);
                    }
                }
            }
        }
        return giftRelationList;
    }
}
