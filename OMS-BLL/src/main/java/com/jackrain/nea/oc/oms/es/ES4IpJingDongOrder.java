package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongReturnOrderExt;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/11/11 11:04 上午
 * @description
 * @since version -1.0
 */


@Slf4j
public class ES4IpJingDongOrder {


    /**
     * 从ES中查询未转换成功的单据信息
     * 根据转换状态和转换备注查询履约单号
     * @param pageIndex 页码
     * @param pageSize  每页大小
     *
     *内部参数
     *ISTRANS 转换状态
     *SYSREMARK 系统备注
     *@return 单据编号列表
     */
    public static List<String> selectUnTransferredOrderFromEs(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        try {

            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "0");
//            whereKeys.put("SYSREMARK", null);


            String[] returnFieldNames = new String[]{"ORDER_ID"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            orderKey.put("asc", true);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JINGDONG_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_JINGDONG_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("ORDER_ID");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("selectUnTransferredOrderFromEs.异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return orderNoList;
    }

    /**
     * 根据orderId查询id
     *
     * @param orderId 平台单号
     * @return JSONArray
     * es查询京东订单中间表返回数据
     */
    public static JSONArray findIdByOrderId(Object orderId) {
        String[] returnFieldsByJdOrder = new String[]{"ID"};
        JSONObject whereKeysByJdOrder = new JSONObject();
        whereKeysByJdOrder.put("ORDER_ID", orderId);
        JSONObject searchByJdOrder = ElasticSearchUtil.search(JingdongReturnOrderExt.TABLENAM_IPBJINGDONGORDER,
                JingdongReturnOrderExt.TABLENAM_IPBJINGDONGORDER, whereKeysByJdOrder, null,
                null, 50, 0, returnFieldsByJdOrder);
        return searchByJdOrder.getJSONArray("data");

    }

    /**
     * @return
     */
    public static JSONObject findOrderId() {
        JSONObject whereKey = new JSONObject();
        JSONObject filterKey = new JSONObject();
        JSONArray orderKey = new JSONArray();
        Integer range = 100000;
        Integer start = 0;
        String[] fieldKey = {"ID", "ORDER_ID"};
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JINGDONG_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.IP_B_JINGDONG_ORDER_TYPE_NAME,
                whereKey, filterKey, orderKey, start, range, fieldKey);
        return search;
    }

    /**
     * 根据 isTrans、orderStatus 查询 id
     *
     * @param transStatus 转换状态
     * @param orderState  订单状态
     * @return JSONArray
     */
    public static JSONArray findIdByTransAndOrderState(Integer transStatus, JSONArray orderState) {
        JSONArray data = null;
        JSONObject whereKey = new JSONObject();
        whereKey.put("ISTRANS", transStatus);
        whereKey.put("ORDER_STATE", orderState);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JINGDONG_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.IP_B_JINGDONG_ORDER_TYPE_NAME,
                whereKey, null, null, 500, 0, new String[]{"ID"});
        if (Objects.nonNull(search)) {
            data = search.getJSONArray("data");
        }
        return data;
    }

}
