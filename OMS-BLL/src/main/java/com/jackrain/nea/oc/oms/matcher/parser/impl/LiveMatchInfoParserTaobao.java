package com.jackrain.nea.oc.oms.matcher.parser.impl;

import com.jackrain.nea.oc.oms.matcher.live.LiveMatchManager;
import com.jackrain.nea.oc.oms.matcher.parser.AbstractLiveMatchInfoParser;
import com.jackrain.nea.oc.oms.matcher.vo.OcMatcherInfoVO;
import com.jackrain.nea.oc.oms.matcher.vo.ParamInputVO;
import com.jackrain.nea.oc.oms.matcher.vo.ParamOutputVO;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrder;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.ProductSku;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/06/15
 * 淘宝原单解析
 */
@Component
public class LiveMatchInfoParserTaobao extends AbstractLiveMatchInfoParser<IpTaobaoOrderRelation> {

    /**
     * 校验
     *
     * @param channelOrigOrder
     * @param order
     * @param items
     * @return
     */
    @Override
    protected boolean check(IpTaobaoOrderRelation channelOrigOrder, OcBOrder order, List<OcBOrderItem> items) {
        return super.check(channelOrigOrder, order, items)
                && Objects.nonNull(channelOrigOrder.getTaobaoOrder());
    }

    /**
     * 匹配
     *
     * @param matcherInfoVO
     * @param item
     * @param tbOrder
     * @param originalItem
     */
    private void matchItems(OcMatcherInfoVO<IpTaobaoOrderRelation> matcherInfoVO, OcBOrderItem item, IpBTaobaoOrder tbOrder, IpBTaobaoOrderItemEx originalItem) {
        ParamInputVO inputVO = new ParamInputVO();
        inputVO.setOriginalRemark(tbOrder.getBuyerMessage());
        if (Objects.nonNull(originalItem)) {
            inputVO.setOriginalId(originalItem.getNumIid());
            inputVO.setOriginalTitle(originalItem.getTitle());
            ProductSku productSku = originalItem.getProdSku();
            if (productSku !=null){
                //赋值spu
                inputVO.setOriginalSpu(productSku.getProdCode());
            }
        }

        // 每次覆盖这个对象
        matcherInfoVO.setInputVO(inputVO);
        ParamOutputVO outputVO = LiveMatchManager.getInstance().doMatch(matcherInfoVO);

        // 赋值
        if (Objects.nonNull(outputVO) && outputVO.isResult()) {
            setResult(item, outputVO);
        }
    }

    /**
     * 渠道原单解析，得出规则匹配锁需要的信息
     *
     * @param channelOrigOrder
     * @return
     */
    @Override
    public void doParser(IpTaobaoOrderRelation channelOrigOrder, OcBOrder order, List<OcBOrderItem> items) {
        if (check(channelOrigOrder, order, items)) {
            IpBTaobaoOrder tbOrder = channelOrigOrder.getTaobaoOrder();
            List<IpBTaobaoOrderItemEx> tbOrderItems = channelOrigOrder.getTaobaoOrderItemList();

            if (tbOrderItems == null) {
                tbOrderItems = new ArrayList<>();
            }

            // 原单明细转map
            Map<Long, List<IpBTaobaoOrderItemEx>> tbItemMap = tbOrderItems.stream().collect(Collectors.groupingBy(i -> i.getOid()));

            // 基础部分赋值
            OcMatcherInfoVO<IpTaobaoOrderRelation> matcherInfoVO = new OcMatcherInfoVO<>();
            // 原单对象
            matcherInfoVO.setOriginalOrderRelation(channelOrigOrder);
            // 店铺ID
            matcherInfoVO.setCpCShopId(tbOrder.getCpCShopId());
            // 下单时间：交易创建时间
            matcherInfoVO.setOrderDate(tbOrder.getCreated());
            // 支付时间
            matcherInfoVO.setPayTime(tbOrder.getPayTime());

            // 循环
            items.forEach(item -> {
                // 取原单明细
                List<IpBTaobaoOrderItemEx> oItems = tbItemMap.get(Long.valueOf(item.getOoid()));
                IpBTaobaoOrderItemEx oItem = null;

                if (CollectionUtils.isNotEmpty(oItems)) {
                    oItem = oItems.get(0);
                }

                matchItems(matcherInfoVO, item, tbOrder, oItem);
            });
        }

        // 明细完了要重整下服务
        setOrderByItem(order, items);
    }

    /**
     * 渠道类型
     *
     * @return
     */
    @Override
    public ChannelType getCurrentChannelType() {
        return ChannelType.TAOBAO;
    }

}
