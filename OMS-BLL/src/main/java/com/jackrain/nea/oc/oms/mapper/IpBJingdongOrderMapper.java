package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface IpBJingdongOrderMapper extends ExtentionMapper<IpBJingdongOrder> {

    @Select("SELECT * FROM ip_b_jingdong_order WHERE order_id = #{orderId}")
    IpBJingdongOrder selectJingdongOrderByOrderId(@Param("orderId") Long orderId);

    /**
     * @param isTrans
     * @param orderId
     * @param sysremark
     * @param abnormalType
     * @return
     */
    @Select("UPDATE IP_B_JINGDONG_ORDER SET ISTRANS=#{isTrans},ABNORMAL_TYPE=#{abnormalType}, TRANSDATE=NOW(),MODIFIEDDATE=NOW(),TRANS_COUNT=IFNULL(TRANS_COUNT,0) +1 ,"
            + " SYSREMARK=#{sysremark} WHERE ORDER_ID = #{orderId}")
    IpBJingdongOrder updateIpJingdongExchangeOrderInfo(@Param("isTrans") Integer isTrans, @Param("orderId") Long orderId,
                                                       @Param("sysremark") String sysremark, @Param("abnormalType") Integer abnormalType);

    /**
     *
     * @param orderId
     */
    @Update("update ip_b_jingdong_order set istrans=2 , transdate=now(),trans_count=ifnull(trans_count,0)+1,sysremark='转单成功',abnormal_type=null where order_id=#{orderId}  ")
    void updateIpJdOrderAfterTransSuccess(@Param("orderId") Long orderId);

}