package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategy;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.Set;

/**
 * 对等换货策略
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-16 13:26:40
 */
@Mapper
public interface StCEquityBarterStrategyMapper extends ExtentionMapper<StCEquityBarterStrategy> {
    public class StCEquityBarterStrategyUpdateSql {
        public String update(final JSONObject jsonObject) {
            return new SQL() {
                {
                    UPDATE("st_c_equity_barter_strategy");
                    Set<String> keySet = jsonObject.keySet();
                    for (String key : keySet) {
                        SET(key + "=#{" + key + "}");
                    }
                    WHERE("ID=#{ID}");
                }
            }.toString();
        }

        public String insert(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    INSERT_INTO("st_c_equity_barter_strategy");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }
    }

    @InsertProvider(type = StCEquityBarterStrategyUpdateSql.class, method = "insert")
    int insertStCEquityBarterStrategy(JSONObject jsonObject);

    /**
     * 更新
     *
     * @param jsonObject
     * @return
     */
    @UpdateProvider(type = StCEquityBarterStrategyUpdateSql.class, method = "update")
    int updateStCEquityBarterStrategy(JSONObject jsonObject);


    @Select("SELECT * FROM st_c_equity_barter_strategy WHERE CP_C_SHOP_ID = #{shopId}  and isactive = 'Y' ")
    List<StCEquityBarterStrategy> selectStCEquityBarterStrategyByShopId(@Param("shopId") Long shopId);


    @Select("SELECT * FROM st_c_equity_barter_strategy WHERE type = 1 and isactive = 'Y' ")
    List<StCEquityBarterStrategy> selectStCEquityBarterStrategyCommon();

    @Select("<script> "
            + "SELECT * FROM st_c_equity_barter_strategy WHERE isactive = 'Y' and id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "order by `type` "
            + "</script>")
    List<StCEquityBarterStrategy> selectStCEquityBarterStrategyByIds(@Param("ids") List<Long> ids);

}
