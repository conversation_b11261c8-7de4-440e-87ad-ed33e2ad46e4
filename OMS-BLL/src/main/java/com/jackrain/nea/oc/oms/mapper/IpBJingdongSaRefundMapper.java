package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongSaRefund;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface IpBJingdongSaRefundMapper extends ExtentionMapper<IpBJingdongSaRefund> {


    @Update("<script> "
            + "UPDATE ip_b_jingdong_sa_refund SET istrans = #{istrans} where popafsrefundapplyid in "
            + "<foreach item='item' index='index' collection='appyids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateSaRefundIstransList(@Param("istrans") int istrans, @Param("appyids") List<Long> appyids);


    @Select("SELECT * FROM ip_b_jingdong_sa_refund WHERE POPAFSREFUNDAPPLYID=#{applyid} LIMIT 1 ")
    IpBJingdongSaRefund selectJingdongSaRefundByApplyId(@Param("applyid") Long applyid);


    @UpdateProvider(type = IpBJingdongSaRefundSqlBuilder.class, method = "update")
    int updateSaRefundOrder(JSONObject jsonObject);

    /**
     * 更新状态及次数
     */
    class IpBJingdongSaRefundSqlBuilder {
        public String buildUpdateRefundTransSQL(@Param("id") long id, @Param("isTrans") int isTrans,
                                                @Param("isUpdateTransNum") boolean isUpdateTransNum, @Param("refundId") String refundId) {
            return new SQL() {
                {
                    UPDATE("ip_b_taobao_refund");
                    SET("istrans=#{isTrans}");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                    }
                    WHERE("ID=#{id} and refund_id = #{refundId}");
                }
            }.toString();
        }

        public String update(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_jingdong_sa_refund");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    for (String key : map.keySet()) {
                        if (!"popafsrefundapplyid".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("popafsrefundapplyid = #{popafsrefundapplyid}");
                }
            }.toString();
        }
    }

}