package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * 开票通知发票类型枚举
 *
 * @author: huang.zaizai
 * create at: 2019/7/23 19:20
 */
public enum OcInvoiceTypeEnum {

    ELE_INVOICE(0, "电子发票"),
    PAPER_INVOICE(1, "纸质发票"),
    SPECIAL_INVOICE(2, "专用发票");

    OcInvoiceTypeEnum(Integer key, String name) {
        this.key = key;
        this.name = name;
    }

    @Getter
    private Integer key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String enumToStringBykey(Integer key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (OcInvoiceTypeEnum e : OcInvoiceTypeEnum.values()) {
            if (Integer.valueOf(e.getKey()).equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return null;
    }
}


