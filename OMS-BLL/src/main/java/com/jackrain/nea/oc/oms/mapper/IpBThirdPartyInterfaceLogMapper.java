package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBThirdPartyInterfaceLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName IpBThirdPartyInterfaceLogMapper
 * @Description 第三方接口日志Mapper
 * <AUTHOR>
 * @Date 2025/4/22 20:15
 * @Version 1.0
 */
@Mapper
public interface IpBThirdPartyInterfaceLogMapper extends ExtentionMapper<IpBThirdPartyInterfaceLog> {

    /**
     * 根据ID列表查询第三方接口日志
     *
     * @param ids ID列表
     * @return 第三方接口日志列表
     */
    @Select("<script> " +
            "SELECT * FROM IP_B_THIRD_PARTY_INTERFACE_LOG WHERE ID IN " +
            "<foreach item='item' index='index' collection='ids' " +
            "open='(' separator=',' close=')'> #{item} </foreach> " +
            "ORDER BY ID DESC" +
            "</script>")
    List<IpBThirdPartyInterfaceLog> selectByIds(@Param("ids") List<Long> ids);
}
