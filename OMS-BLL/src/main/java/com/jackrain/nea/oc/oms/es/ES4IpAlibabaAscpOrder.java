package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 11:44 上午
 * @description
 * @since version -1.0
 */

@Slf4j
public class ES4IpAlibabaAscpOrder {

    /**
     * 从ES中查询未转换成功的单据信息
     * 根据转换状态和转换备注查询履约单号
     * @param pageIndex 页码
     * @param pageSize 每页大小
     *
     * 内部参数
     *ISTRANS 转换状态
     *SYSREMARK 转换备注
     *BIZ_ORDER_CODE 履约单号
     *
     * @return 单据编号列表
     */
    public static List<String> selectUnTransferredOrderFromEs(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        try {

            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "0");
//            whereKeys.put("SYSREMARK", null);

            String[] returnFieldNames = new String[]{"BIZ_ORDER_CODE"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_ALIBABA_ASCP_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_ALIBABA_ASCP_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("BIZ_ORDER_CODE");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            log.error(LogUtil.format("selectUnTransferredOrderFromEs.异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return orderNoList;
    }
}
