package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.ip.model.LogisticsSendModel;
import com.jackrain.nea.ip.model.TbLogisticsSendBySplitLineModel;
import com.jackrain.nea.ip.model.result.LogisticsSendResult;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * Description:淘宝平台发货实现类
 *
 * <AUTHOR> sunies
 * @since : 2020-11-03
 * create at : 2020-11-03 20:02
 */
@Slf4j
@Component
public class OrderDeliveryOfTaoBaoImpl implements OrderDeliveryCmd {

    /**
     * 淘宝发货切换开关。0:走原多包裹发货；1：新逻辑发货，不走多包裹
     */
    @NacosValue(value = "${r3.oc.oms.taobao.delivery.switch:0}", autoRefreshed = true)
    public Integer deliverySwitch;

    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;

    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("淘宝平台发货服务,订单id为={}", "淘宝平台发货服务", ocBOrderRelation.getOrderId()), ocBOrderRelation.getOrderId());
        }

        //新发货逻辑（多包裹发货下线改造）
        if (!Objects.isNull(deliverySwitch) && deliverySwitch.equals(1)) {
            return newDelivery(ocBOrderRelation);
        }

        //原发货（含多包裹），稳定后可废弃
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        List<OcBOrderItem> ocBOrderItemList = ocBOrderRelation.getOrderItemList();
        //ooid集合
        ocBOrderItemList = ocBOrderItemList.stream().filter(s -> StringUtils.isNotEmpty(s.getOoid()) && !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(s.getIsSendout())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ocBOrderItemList)) {
            return orderPlatformDeliveryService.updateOrderAfterPlatDeliverySuccess(ocBOrder);
        }
        // 标识是否全部成功
        AtomicBoolean result = new AtomicBoolean(true);
        // 记录直调返回 -1失败 0成功
        List<ValueHolderV14> v14s = Lists.newArrayList();
        boolean virtualOrder = OrderTypeEnum.DIFFPRICE.getVal().equals(ocBOrder.getOrderType());
        Map<String, List<OcBOrderItem>> collect = ocBOrderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));
        collect.forEach((tid, items) -> {
            // ooid拆分发货处理
            Set<String> sendOutOoids = new HashSet<>();
            if(checkIsSplitLine(ocBOrder, items, sendOutOoids)){
                buildAndInvokeIpDeliveryBySplitLine(ocBOrder, items, tid, result, v14s);
                return;
            }
            /**
             * ☆1、获取明细条码id与ooid的对应关系
             */
            Map<String, Long> ooidAndSkuId = Maps.newHashMap();
            items.stream().forEach(s->ooidAndSkuId.put(s.getOoid(),s.getPsCSkuId()));

            /**
             * ☆2、根据 明细条码和订单编号查询发货信息列表，TODO 这里可能会有问题
             */
            Map<String, List<OcBOrderDelivery>> orderDeliveryGroup = orderPlatformDeliveryService.getExpressCodeFromOrderDelivery(ocBOrder, ooidAndSkuId.values());

            /**
             * ☆4、分组结果确定是多物流还是单物流发货
             */
            orderDeliveryGroup.forEach((logisticNumber, delivery) -> {
                boolean exchangeItem = OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(items.get(0).getIsExchangeItem());

                boolean split = ES4Order.splitOrderCheck(tid, ocBOrder.getIsSplit()) || orderDeliveryGroup.keySet().size() > 1;
                /**
                 * ☆5、根据条码信息取货对应的明细ooid（这里建议发货信息表里冗余订单明细的ID或者ooid，暂时先不处理，后期处理）
                 */
                //Collection<String> ooids = virtualOrder ? ooidAndSkuId.keySet() : delivery.stream().map(s -> getOoidBySkuId(ooidAndSkuId, s.getPsCSkuId())).collect(Collectors.toList());
                Set<String> ooids = new HashSet<>();
                if(virtualOrder){
                    ooids = ooidAndSkuId.keySet();
                }else{
                    for (OcBOrderDelivery deliveryItem : delivery) {
                        for (OcBOrderItem orderItem : items) {
                            if(orderItem.getPsCSkuId().equals(deliveryItem.getPsCSkuId())){
                                String ooid = getOoidBySkuId(ooidAndSkuId, deliveryItem.getPsCSkuId());
                                if(ooid != null){
                                    ooids.add(ooid);
                                }
                            }
                        }
                    }
                }
                // 过滤已经平台发货的ooid
                ooids.removeAll(sendOutOoids);
                /**
                 * ☆6、构建云枢纽平台发货接口参数
                 */
                if (exchangeItem) {
                    Set<String> finalOoids = ooids;
                    items.forEach(s -> buildAndInvokeIpDelivery(ocBOrder, result, virtualOrder, tid, items, logisticNumber, exchangeItem, split, finalOoids));
                } else {
                    buildAndInvokeIpDelivery(ocBOrder, result, virtualOrder, tid, items, logisticNumber, exchangeItem, split, ooids);
                }
            });
        });
        // ooid拆分发货处理返回结果
        handleAfterSend(v14s, ocBOrder);
        return result.get();
    }

    /**
     * 多包裹发货下线改造
     *
     * @param ocBOrderRelation
     * @return
     */
    private boolean newDelivery(OcBOrderRelation ocBOrderRelation) {
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        List<OcBOrderItem> ocBOrderItemList = ocBOrderRelation.getOrderItemList();

        // 判断是否是天猫周期购 如果是 根据期数查一下之前是否已经有发货的单据了 如果有 则不调用平台发货
        if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle())) {
            // 根据订单的平台单号+期数 查询是否已经有平台发货的订单
            List<OcBOrder> ocBOrderList = orderMapper.selectDeliveryOrderByTidAndCycleNumber(ocBOrder.getTid(), ocBOrder.getCurrentCycleNumber());
            if (CollectionUtils.isNotEmpty(ocBOrderList)) {
                orderItemMapper.updateItemsWhenDeliverySuccess(ocBOrder.getId(), ocBOrder.getTid());
                OcBOrder update = new OcBOrder();
                update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                update.setId(ocBOrder.getId());
                update.setModifieddate(new Date());
                orderMapper.updateById(update);
                orderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), "相同期数存在其他平台发货成功的单据:" + ocBOrderList.get(0).getBillNo(), null,
                        null, SystemUserResource.getRootUser());
                return true;
            }
        }

        //过滤子单号空、已发货、已退款明细
        ocBOrderItemList = ocBOrderItemList.stream().filter(item -> StringUtils.isNotEmpty(item.getOoid())
                && !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsSendout())
                && !InreturningStatus.INRETURN_YES.equals(item.getRefundStatus())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ocBOrderItemList)) {
            return orderPlatformDeliveryService.updateOrderAfterPlatDeliverySuccess(ocBOrder);
        }

        //虚拟和换货走原多包裹发货
        if (OrderTypeEnum.DIFFPRICE.getVal().equals(ocBOrder.getOrderType()) || OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(ocBOrderItemList.get(0).getIsExchangeItem())) {
            return oldVirtualAndExchangeDelivery(ocBOrder, ocBOrderItemList);
        }

        //已经平台发货的子单号集合
        Set<String> platformOoids = deliveryOoids(orderMapper.selectOcBOrderByTid(ocBOrder.getTid()));

        //发货参数
        LogisticsSendModel tbLogisticsModel = getLogisticsSendModel(ocBOrder, ocBOrderItemList, platformOoids);

        //发货
        ValueHolderV14<List<LogisticsSendResult>> v14 = orderPlatformDeliveryService.packageNormalInterfaceParam(tbLogisticsModel, ocBOrder);
        return v14.getCode() == ResultCode.SUCCESS;
    }

    /**
     * 获取已经平台发货的子单号
     *
     * @param ocBOrders
     * @return
     */
    private Set<String> deliveryOoids(List<OcBOrder> ocBOrders) {
        Set<String> ooids = Sets.newHashSet();
        if (CollectionUtils.isEmpty(ocBOrders)){
            return ooids;
        }
        List<OcBOrder> deliveryOrders = ocBOrders.stream().filter(o -> Objects.equals(OmsOrderStatus.PLATFORM_DELIVERY.toInteger(), o.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deliveryOrders)) {
            List<Long> orderIds = deliveryOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());
            List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectAllStatusOrderItemsByOrderIds(orderIds);
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                Set<String> ooidSets = ocBOrderItems.stream().map(OcBOrderItem::getOoid).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(ooidSets)) {
                    ooids.addAll(ooidSets);
                }
            }
        }
        return ooids;
    }

    /**
     * 淘宝发货参数
     *
     * @param ocBOrder
     * @param ocBOrderItemList
     * @param platformOoids 已发货子单号
     * @return
     */
    private LogisticsSendModel getLogisticsSendModel(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList, Set<String> platformOoids) {
        //当前发货订单明细中已发货数量，去重
        Set<String> itemOoids = ocBOrderItemList.stream().map(OcBOrderItem::getOoid).collect(Collectors.toSet());
        int count = platformCount(itemOoids, platformOoids);

        LogisticsSendModel tbLogisticsModel = new LogisticsSendModel();
        //平台单号
        tbLogisticsModel.setTid(Long.valueOf(ocBOrder.getTid()));
        //0=正常;1=换货;8=虚拟单
        tbLogisticsModel.setOrdertype(0L);
        //默认都是1（拆单）
        tbLogisticsModel.setIsSplit(1L);
        //子单号，随机取一条
        tbLogisticsModel.setSubTid(ocBOrderItemList.get(0).getOoid());
        //运单号
        tbLogisticsModel.setOutSid(ocBOrder.getExpresscode());
        //物流公司代码
        String logisticCompanyCode = orderPlatformDeliveryService.getLogisticCode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
        tbLogisticsModel.setCompanyCode(logisticCompanyCode);
        //卖家发货的快递公司
        tbLogisticsModel.setLogisticsCompanyName(ocBOrder.getCpCLogisticsEname());
        //发货类型
        if (tbLogisticsModel.getOrdertype().equals(0L) && ocBOrder.getPayType().equals(1)) {
            tbLogisticsModel.setConsignType(1);
            if ((count == itemOoids.size()) && (!OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle()))) {
                tbLogisticsModel.setConsignType(3);
            }
        } else {
            tbLogisticsModel.setConsignType(0);
        }

        //包裹信息
        if (tbLogisticsModel.getConsignType().equals(1) || tbLogisticsModel.getConsignType().equals(3)) {
            Set<String> deliveryOoids = ocBOrderItemList.stream().map(OcBOrderItem::getOoid).collect(Collectors.toSet());

            List<LogisticsSendModel.TopConsignPkg> consignPkgs = Lists.newArrayList();
            for (String ooid : deliveryOoids) {
                if (!tbLogisticsModel.getConsignType().equals(3) && platformOoids.contains(ooid) && !OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsCycle())) {
                    continue;
                }
                LogisticsSendModel.TopConsignPkg topConsignPkg = new LogisticsSendModel.TopConsignPkg();
                topConsignPkg.setOutSid(ocBOrder.getExpresscode());
                topConsignPkg.setCompanyCode(logisticCompanyCode);

                //goods
                LogisticsSendModel.TopConsignGoods topConsignGoods = new LogisticsSendModel.TopConsignGoods();
                topConsignGoods.setSubTid(ooid);
                //品类型 0：标品，1：赠品、2：成分品，默认为标品
                topConsignGoods.setItemType(0);
                topConsignPkg.setGoods(Lists.newArrayList(topConsignGoods));

                consignPkgs.add(topConsignPkg);
            }
            tbLogisticsModel.setConsignPkgs(consignPkgs);

            //子订单发货状态
            LogisticsSendModel.ConsignStatus consignStatus = new LogisticsSendModel.ConsignStatus();
            //子订单是否部分发货，true：部分发货；false：全部发货
            consignStatus.setIsPartConsign(false);
            tbLogisticsModel.setConsignStatus(Lists.newArrayList(consignStatus));
        }
        return tbLogisticsModel;
    }

    private int platformCount(Set<String> itemOoids, Set<String> platformOoids) {
        int count = 0;
        for (String itemOoid : itemOoids) {
            if (platformOoids.contains(itemOoid)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 虚拟和换货走原逻辑
     * @param ocBOrder
     * @param ocBOrderItemList
     * @return
     */
    private boolean oldVirtualAndExchangeDelivery(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList) {
        // 标识是否全部成功
        AtomicBoolean result = new AtomicBoolean(true);
        boolean virtualOrder = OrderTypeEnum.DIFFPRICE.getVal().equals(ocBOrder.getOrderType());
        // 记录直调返回 -1失败 0成功
        List<ValueHolderV14> v14s = Lists.newArrayList();
        Map<String, List<OcBOrderItem>> collect = ocBOrderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));
        collect.forEach((tid, items) -> {
            // ooid拆分发货处理
            Set<String> sendOutOoids = new HashSet<>();
            checkIsSplitLine(ocBOrder, items, sendOutOoids);
            /**
             * ☆1、获取明细条码id与ooid的对应关系
             */
            Map<String, Long> ooidAndSkuId = Maps.newHashMap();
            items.stream().forEach(s->ooidAndSkuId.put(s.getOoid(),s.getPsCSkuId()));

            /**
             * ☆2、根据 明细条码和订单编号查询发货信息列表，TODO 这里可能会有问题
             */
            Map<String, List<OcBOrderDelivery>> orderDeliveryGroup = orderPlatformDeliveryService.getExpressCodeFromOrderDelivery(ocBOrder, ooidAndSkuId.values());

            /**
             * ☆4、分组结果确定是多物流还是单物流发货
             */
            orderDeliveryGroup.forEach((logisticNumber, delivery) -> {
                boolean exchangeItem = OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(items.get(0).getIsExchangeItem());

                boolean split = ES4Order.splitOrderCheck(tid, ocBOrder.getIsSplit()) || orderDeliveryGroup.keySet().size() > 1;
                /**
                 * ☆5、根据条码信息取货对应的明细ooid（这里建议发货信息表里冗余订单明细的ID或者ooid，暂时先不处理，后期处理）
                 */
                //Collection<String> ooids = virtualOrder ? ooidAndSkuId.keySet() : delivery.stream().map(s -> getOoidBySkuId(ooidAndSkuId, s.getPsCSkuId())).collect(Collectors.toList());
                Set<String> ooids = new HashSet<>();
                if(virtualOrder){
                    ooids = ooidAndSkuId.keySet();
                }else{
                    for (OcBOrderDelivery deliveryItem : delivery) {
                        for (OcBOrderItem orderItem : items) {
                            if(orderItem.getPsCSkuId().equals(deliveryItem.getPsCSkuId())){
                                String ooid = getOoidBySkuId(ooidAndSkuId, deliveryItem.getPsCSkuId());
                                if(ooid != null){
                                    ooids.add(ooid);
                                }
                            }
                        }
                    }
                }
                // 过滤已经平台发货的ooid
                ooids.removeAll(sendOutOoids);
                /**
                 * ☆6、构建云枢纽平台发货接口参数
                 */
                if (exchangeItem) {
                    Set<String> finalOoids = ooids;
                    items.forEach(s -> buildAndInvokeIpDelivery(ocBOrder, result, virtualOrder, tid, items, logisticNumber, exchangeItem, split, finalOoids));
                } else {
                    buildAndInvokeIpDelivery(ocBOrder, result, virtualOrder, tid, items, logisticNumber, exchangeItem, split, ooids);
                }
            });
        });
        // ooid拆分发货处理返回结果
        handleAfterSend(v14s, ocBOrder);
        return result.get();
    }


    /**
     * 构建发货参数，调用ip接口
     * @param ocBOrder
     * @param result
     * @param virtualOrder
     * @param tid
     * @param items
     * @param logisticNumber
     * @param exchangeItem
     * @param split
     * @param ooids
     */
    private void buildAndInvokeIpDelivery(OcBOrder ocBOrder, AtomicBoolean result, boolean virtualOrder, String tid, List<OcBOrderItem> items,
                                          String logisticNumber, boolean exchangeItem, boolean split, Set<String> ooids) {
        LogisticsSendModel tbLogisticsModel = new LogisticsSendModel();
        //0=正常;1=换货;8=虚拟单
        tbLogisticsModel.setOrdertype(0L);
        tbLogisticsModel.setCompanyCode(orderPlatformDeliveryService.getLogisticCode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform())));
        tbLogisticsModel.setLogisticsCompanyName(ocBOrder.getCpCLogisticsEname());
        if (exchangeItem) {
            //换货单号，这里传ooid
            tbLogisticsModel.setDisputeId(items.get(0).getExchangeBillNo());
            tbLogisticsModel.setLogisticsType(200L);
            tbLogisticsModel.setOrdertype(1L);
        }
        if (virtualOrder) {
            //虚拟订单整单发货
            tbLogisticsModel.setOrdertype(8L);

            //如何开关关闭的，原逻辑不动
//            if (!Objects.isNull(deliverySwitch) && deliverySwitch.equals(0)){
                if (!OcBorderListEnums.YesOrNoEnum.IS_NO.getVal().equals(ocBOrder.getIsSplit())) {
                    tbLogisticsModel.setCompanyCode("OTHER");
                    tbLogisticsModel.setLogisticsCompanyName("OTHER");
                    //虚拟订单拆单发货
                    tbLogisticsModel.setOrdertype(0L);
                }
//            }
        }

        tbLogisticsModel.setIsSplit(split ? 1L : 0L);
        //平台单号
        tbLogisticsModel.setTid(Long.valueOf(tid));
        //运单号
        tbLogisticsModel.setOutSid(logisticNumber);
        //子订单集合
        tbLogisticsModel.setSubTid(split ? StringUtils.join(ooids, ",") : null);
        OrderPlatformDeliveryService.threadSleep(split, 2000);
        ValueHolderV14<List<LogisticsSendResult>> v14 = orderPlatformDeliveryService.packageNormalInterfaceParam(tbLogisticsModel, ocBOrder);
        result.set(result.get() && v14.getCode() == ResultCode.SUCCESS);
    }


    /***
     * 由于发货信息列表没有办法获取ooid，只能通过条码和ooid的对应关系获取ooid
     *
     * @param map
     * @param value
     * @return
     */
    private static String getOoidBySkuId(Map<String, Long> map, Long value) {
        String key = null;
        for (String getKey : map.keySet()) {
            if (map.get(getKey).equals(value)) {
                key = getKey;
                break;
            }
        }
        map.remove(key);
        return key;
    }
    public void buildAndInvokeIpDeliveryBySplitLine(OcBOrder curOrder, List<OcBOrderItem> items, String tid, AtomicBoolean result, List<ValueHolderV14> v14s){
        TbLogisticsSendBySplitLineModel tbLogisticsSendBySplitLineModel = new TbLogisticsSendBySplitLineModel();
        long importType;
        if(curOrder.getIsHasgift() != null && curOrder.getIsHasgift() == 1){
            importType = 2L;
        }else{
            importType = 1L;
        }
        tbLogisticsSendBySplitLineModel.setImport_type(importType);
        tbLogisticsSendBySplitLineModel.setMail_no(curOrder.getExpresscode());
        tbLogisticsSendBySplitLineModel.setTp_code(orderPlatformDeliveryService.getLogisticCode(curOrder.getCpCLogisticsId(), Long.valueOf(curOrder.getPlatform())));
        tbLogisticsSendBySplitLineModel.setParent_order_id(tid);
        List<TbLogisticsSendBySplitLineModel.Item> commodityInfos = new ArrayList<>();
        for (OcBOrderItem item : items) {
            TbLogisticsSendBySplitLineModel.Item commodityInfo = new TbLogisticsSendBySplitLineModel.Item();
            commodityInfo.setItem_id(item.getNumIid());
            if (Objects.nonNull(item.getOriginSkuQty())) {
                commodityInfo.setGoods_quantity(item.getOriginSkuQty().longValue());
            } else {
                commodityInfo.setGoods_quantity(Long.valueOf(item.getQty().stripTrailingZeros().toPlainString()));
            }
            commodityInfos.add(commodityInfo);
        }
        tbLogisticsSendBySplitLineModel.setCommodity_infos(commodityInfos);
        ValueHolderV14 v14 = orderPlatformDeliveryService.packageTbSplitLineInterfaceParam(tbLogisticsSendBySplitLineModel, curOrder);
        result.set(result.get() && v14.getCode() == ResultCode.SUCCESS);
        v14.setData(tid);
        v14s.add(v14);
    }
    public void handleAfterSend(List<ValueHolderV14> v14s, OcBOrder curOrder){
        for (ValueHolderV14 v14 : v14s) {
            String tid = (String) v14.getData();
            if (v14.getCode() == ResultCode.FAIL) {
                //更新发货状态，插入日志
                String logMsg = "OrderId=" + curOrder.getId() + ",平台单号=" + tid + "淘宝物流多包裹通知平台失败," + v14.getMessage();
                orderLogService.addUserOrderLog(curOrder.getId(), curOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                        null, null);
                orderPlatformDeliveryService.failUpdate(curOrder.getExpresscode(), v14.getMessage(), curOrder);
            } else if (v14.getCode() == ResultCode.SUCCESS){
                //更新发货状态，插入日志
                String logMsg = "OrderId=" + curOrder.getId() + ",平台单号=" + tid + "淘宝物流多包裹通知平台成功";
                orderLogService.addUserOrderLog(curOrder.getId(), curOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                        null, null);
                orderPlatformDeliveryService.updateSendGoodsStatusByExpressCode(curOrder.getId(), curOrder.getExpresscode(), 1L);
                orderItemMapper.updateItemsWhenDeliverySuccess(curOrder.getId(), tid);
                List<String> tidList = orderItemMapper.selectCanPlatformItemTidList(curOrder.getId());
                tidList.remove(tid);
                if (tidList.size() > 0) {
                    //先不做处理，只更新明细发货状态，等后面的tid发货消息过来一起更新主表状态
                } else {
                    //直接更新主表状态为平台发货
                    OcBOrder order = new OcBOrder();
                    order.setId(curOrder.getId());
                    order.setIsForce(1L);
                    order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                    //平台发货时间赋值
                    order.setPlatformDeliveryTime(new Date());
                    ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.PLATFORM_DELIVERY_TIME,new Date(),order.getId(), SystemUserResource.getRootUser());
                    //作废退单
                    orderPlatformDeliveryService.updateOrder(order);
                    orderPlatformDeliveryService.updateReturnOrder(curOrder);
                }
            }
        }
    }

    private boolean checkIsSplitLine(OcBOrder curOrder, List<OcBOrderItem> items, Set<String> sendOutOoid){
        // sap的订单不做处理
        if (curOrder.getGwSourceGroup() != null && (PlatFormEnum.SAP.getCode().equals(Integer.valueOf(curOrder.getGwSourceGroup())) || PlatFormEnum.DMS.getCode().equals(Integer.valueOf(curOrder.getGwSourceGroup())))) {
            return false;
        }
        // 无拆单标识不做处理
        if(curOrder.getIsSplit() == null || curOrder.getIsSplit() != 1){
            return false;
        }
        // 判断同tid 同oid 是否存在多单，且有一单已经平台发货
        String tid = items.get(0).getTid();
        List<OcBOrder> orderList = orderMapper.selectBySourceCode(tid);
        orderList = orderList.stream().filter(o-> !Objects.equals(OmsOrderStatus.SYS_VOID.toInteger(), o.getOrderStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orderList)){
            return false;
        }
        // 获取已经平台发货的ooid 本单中如果包含未平台发货过的ooid 走平台发货
        List<Long> orderIds = orderList.stream().map(OcBOrder::getId).collect(Collectors.toList());
        List<OcBOrderItem> orderItemList = orderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().in("oc_b_order_id", orderIds));
        if(CollectionUtils.isNotEmpty(orderItemList)){
            for (OcBOrderItem orderItem : orderItemList) {
                if (orderItem.getIsSendout() != null && orderItem.getIsSendout() == 1 && !"1".equals(orderItem.getGiftType())) {
                    sendOutOoid.add(orderItem.getOoid());
                }
            }
            for (OcBOrderItem item : items) {
                if (item.getOoid() != null && !sendOutOoid.contains(item.getOoid()) && !"1".equals(item.getGiftType())) {
                    return false;
                }
            }
        }
        // 存在已平台发货订单  当前订单包含非平台赠品
        if(curOrder.getIsHasgift() != null && curOrder.getIsHasgift() == 1){
            for (OcBOrderItem item : items) {
                if ("1".equals(item.getGiftType())) {
                    return true;
                }
            }
        }
        // 得出已经发货明细的oid
        for (OcBOrderItem item : items) {
            if(item.getOoid() != null && sendOutOoid.contains(item.getOoid())){
                return true;
            }
        }
        return false;
    }
}

