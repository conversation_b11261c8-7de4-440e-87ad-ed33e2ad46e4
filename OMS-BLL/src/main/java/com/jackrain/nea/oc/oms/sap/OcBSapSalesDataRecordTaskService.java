package com.jackrain.nea.oc.oms.sap;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.constant.SapSalesDateConstant;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordMapper;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGather;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecord;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Description: 汇总销售数据记录表  将数据推送到销售数据汇总表
 *
 * @Author: guo.kw
 * @Since: 2022/8/29
 * create at: 2022/8/29 10:40
 */
@Slf4j
@Component
public class OcBSapSalesDataRecordTaskService {

    @Autowired
    private OcBSapSalesDataRecordSumService service;
    @Autowired
    private OcBSapSalesDataRecordMapper ocBSapSalesDataRecordMapper;
    @Autowired
    private OcBSapSalesDataRecordItemMapper ocBSapSalesDataRecordItemMapper;
    @Autowired
    private OcBSapSalesDataGatherMapper ocBSapSalesDataGatherMapper;
    @Autowired
    private OcBSapSalesDataRecordSumService ocBSapSalesDataRecordSumService;
    private static final SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");


    private static final String TABLE_NAME = "oc_b_sap_sales_data_record";

    public ValueHolderV14 executeThread() {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "销售数据记录表汇总定时任务成功！");
        long startTime = System.currentTimeMillis();

        try {
            PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
            int pageSize = config.getProperty("lts.sapSalesDataRecord.range", 2400);
            int queryPageSize = config.getProperty("lts.sapSalesDataRecordQuery.range", 1000);
            List<OcBSapSalesDataRecord> ocBSapSalesDataRecordList = ocBSapSalesDataRecordMapper.selectOcBSalesDataRecordList(TABLE_NAME, pageSize, SIMPLE_DATE_FORMAT.format(new Date()));
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OcBSapSalesDataRecordTaskService 当前线程池执行完毕 useTime:{}", "OcBSapSalesDataRecordTaskService", (System.currentTimeMillis() - startTime)));
            }
            long startInfo = System.currentTimeMillis();
            // 当前所有节点执行完之后，如果有数据则继续执行下一次线程，如果所有节点执行完数据为空则更新所有汇总中的状态为已汇总
            if (CollectionUtils.isNotEmpty(ocBSapSalesDataRecordList)) {
                List<List<OcBSapSalesDataRecord>> partition = Lists.partition(ocBSapSalesDataRecordList, queryPageSize);
                //分批次执行每次执行一千
                for (List<OcBSapSalesDataRecord> recordInfoList : partition) {
                    ocBSapSalesDataRecordSumService.execute(recordInfoList);
                }
                ocBSapSalesDataRecordList.clear();
                this.executeThread();
            } else {
                updateGatherMiddleStatus();
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OcBSapSalesDataRecordTaskService 奶卡冲抵单汇总任务完成 useTime:{}", "OcBSapSalesDataRecordTaskService", (System.currentTimeMillis() - startInfo)));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OcBSapSalesDataRecordTaskService.Execute Error：{}", Thread.currentThread().getName(), "OcBSapSalesDataRecordTaskService"), Throwables.getStackTraceAsString(ex));
            v14.setMessage(Throwables.getStackTraceAsString(ex));
            v14.setCode(ResultCode.FAIL);
            return v14;
        }
        return v14;
    }

    public void updateGatherMiddleStatus() {
        List<Long> ids=ocBSapSalesDataGatherMapper.selectGatherIdBySumType();
        if(CollectionUtils.isNotEmpty(ids)){
            //最后更新所有汇总中为汇总完成
            OcBSapSalesDataGather ocBSapSalesDataGather = new OcBSapSalesDataGather();
            ocBSapSalesDataGather.setGatherMiddleStatus(SapSalesDateConstant.GATHER_MIDDLE_STATUS_02);
            ocBSapSalesDataGather.setGatherDate(new Date());
            ocBSapSalesDataGatherMapper.update(ocBSapSalesDataGather, new LambdaQueryWrapper<OcBSapSalesDataGather>()
                    .in(OcBSapSalesDataGather::getId, ids));
        }

    }
}
