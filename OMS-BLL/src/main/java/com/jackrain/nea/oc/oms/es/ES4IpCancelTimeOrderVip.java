package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 6:07 下午
 */
public class ES4IpCancelTimeOrderVip {

    private ES4IpCancelTimeOrderVip() {
    }

    /**
     * 业务：唯品会取消时效订单补偿任务
     * 根据转换状态查询唯品会订单号
     *
     * @param pageIndex 页码
     * @param pageSize  现实条数
     * @return List orderSn 单据编号
     */
    public static List<String> findOrderSnByTransStatusAndSysRemark(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONArray whereKeyArray = new JSONArray();
            //转换失败
            whereKeyArray.add(TransferOrderStatus.TRANSFERFAIL.toInteger());
            //未转换
            whereKeyArray.add(TransferOrderStatus.NOT_TRANSFER.toInteger());
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", whereKeyArray);
            //whereKeys.put("SYSREMARK", null);
            JSONObject filterKeys = new JSONObject();
            filterKeys.put("TRANS_COUNT", "~" + 5);


            String[] returnFieldNames = new String[]{"OCCUPIED_ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(
                    OcElasticSearchIndexResources.IP_B_CANCEL_TIME_ORDER_VIP_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_CANCEL_TIME_ORDER_VIP_TYPE_NAME,
                    whereKeys, filterKeys, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("OCCUPIED_ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return orderNoList;
    }
}
