package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBToBOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * @ClassName OcBToBOrderMapper
 * @Description tob订单
 * <AUTHOR>
 * @Date 2024/11/28 11:33
 * @Version 1.0
 */
@Mapper
public interface OcBToBOrderMapper extends ExtentionMapper<OcBToBOrder> {

    @Select("select * from oc_b_to_b_order where oc_b_order_id = #{ocBOrderId} and ISACTIVE ='Y'")
    OcBToBOrder selectByOcBOrderId(Long ocBOrderId);
}
