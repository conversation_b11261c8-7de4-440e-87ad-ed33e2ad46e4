package com.jackrain.nea.oc.oms.services.delivery.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.store.model.result.SgStoFreezeOutResultQueryResult;
import com.burgeon.r3.sg.store.model.result.SgStoOutResultQueryResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.ac.model.result.AcLogisticsFeeInfoBySapItemResult;
import com.jackrain.nea.ac.model.result.AcLogisticsFeeInfoBySapResult;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.ip.model.dms.request.Delivery2DmsItemBatchRequest;
import com.jackrain.nea.ip.model.dms.request.Delivery2DmsItemRequest;
import com.jackrain.nea.ip.model.dms.request.Delivery2DmsRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.rpc.AcRpcService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgNewRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName OrderDeliveryOfDmsImpl
 * @Description DMS订单发货回传
 * <AUTHOR>
 * @Date 2024/6/11 15:07
 * @Version 1.0
 */
@Slf4j
@Component
public class OrderDeliveryOfDmsImpl implements OrderDeliveryCmd {

    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private AcRpcService acRpcService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private SgNewRpcService sgNewRpcService;
    @Autowired
    private OcBOrderItemMapper itemMapper;

    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        Long orderId = orderInfo.getId();
        //判断是否为手工单，如果为手工单直接标记平台发货
        if ("手工新增".equals(ocBOrderRelation.getOrderInfo().getOrderSource())) {
            //更新发货状态，插入日志
            String logMsg = "OrderId=" + orderId + "为手工新增单，直接标记平台发货";
            orderLogService.addUserOrderLog(orderId, orderInfo.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                    null, null);
            OcBOrder update = new OcBOrder();
            update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            update.setId(orderId);
            ocBOrderMapper.updateById(update);
            return true;
        }

        AcLogisticsFeeInfoBySapResult logisticsFeeInfoBySapResult = null;
        if (OmsBusinessTypeUtil.isToBOrder(orderInfo)) {
            try {
                //查询物流费用
                ValueHolderV14<AcLogisticsFeeInfoBySapResult> v14 = acRpcService.calculateLogisticsFee(orderInfo);
                log.info(LogUtil.format("OrderDeliveryOfDmsImpl.build.queryFee orderId:{},v14:{}",
                        "OrderDeliveryOfDmsImpl.build.queryFee"), orderInfo.getId(), JSONObject.toJSONString(v14));
                if (v14.isOK() && v14.getData() != null) {
                    logisticsFeeInfoBySapResult = v14.getData();
                }
            } catch (Exception e) {
                log.error(LogUtil.format("OrderDeliveryOfDmsImpl.build.queryFee error:{}",
                        "OrderDeliveryOfSapImpl.build.queryFee"), Throwables.getStackTraceAsString(e));
            }
        }
        Delivery2DmsRequest delivery2DmsRequest = new Delivery2DmsRequest();
        delivery2DmsRequest.setBillNo(orderInfo.getBillNo());
        delivery2DmsRequest.setStoOutBillNo(orderInfo.getSgBOutBillNo());
        delivery2DmsRequest.setExpresscode(orderInfo.getExpresscode());
        delivery2DmsRequest.setTid(orderInfo.getTid());
        delivery2DmsRequest.setCpCLogisticsEcode(orderInfo.getCpCLogisticsEcode());
        delivery2DmsRequest.setScanTime(orderInfo.getScanTime());
        delivery2DmsRequest.setOmsAddress(orderInfo.getCpCRegionProvinceEname() + "/" +
                orderInfo.getCpCRegionCityEname() + "/" + orderInfo.getCpCRegionAreaEname() + "/" + orderInfo.getReceiverAddress());
        // 根据物流公司id 查询快递公司
        try {
            CpLogistics cpLogistics = cpRpcService.cpLogisticsInfo(orderInfo.getCpCLogisticsId());
            if (cpLogistics != null) {
                delivery2DmsRequest.setDeliveryCompanyCode(cpLogistics.getTmsLogistic());
                delivery2DmsRequest.setDeliveryCompanyName(cpLogistics.getEname());
                if (StrUtil.equals(cpLogistics.getEcode(), "ZT")) {
                    delivery2DmsRequest.setLogisticsType(4);
                }else {
                    delivery2DmsRequest.setLogisticsType(cpLogistics.getType());
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OrderDeliveryOfDmsImpl.cpLogisticsInfo:{}",
                    "OrderDeliveryOfDmsImpl.cpLogisticsInfo"), Throwables.getStackTraceAsString(e));
        }


        Map<Long, AcLogisticsFeeInfoBySapItemResult> itemResultMap = new HashMap<>();
        if (logisticsFeeInfoBySapResult != null) {
            delivery2DmsRequest.setExpressFee(String.valueOf(logisticsFeeInfoBySapResult.getBaseFee()));
            delivery2DmsRequest.setLogisticsSupplierCode(logisticsFeeInfoBySapResult.getLogisticsSupplierCode());
            delivery2DmsRequest.setLogisticsSupplierName(logisticsFeeInfoBySapResult.getLogisticsSupplierName());
            List<AcLogisticsFeeInfoBySapItemResult> itemResultList = logisticsFeeInfoBySapResult.getItemList();
            // itemResultList生成map
            itemResultMap = itemResultList.stream().collect(Collectors.toMap(AcLogisticsFeeInfoBySapItemResult::getItemId, Function.identity()));

        }
        // 构建订单明细数据
        List<Delivery2DmsItemRequest> itemList = new ArrayList<>();
        Set<String> tidSet = new HashSet<>();
        for (OcBOrderItem item : ocBOrderRelation.getOrderItemList()) {
            Delivery2DmsItemRequest itemRequest = new Delivery2DmsItemRequest();
            if (Objects.equals(item.getRealOutNum(), BigDecimal.ZERO)) {
                continue;
            }
            itemRequest.setLine(item.getOoid());
            // 根据子单号 来拆分平台单号 然后进行去重 判断如果有多个的话 就用逗号来拼接
            if (StringUtils.isNotEmpty(item.getOoid())) {
                String ooid = item.getOoid();
                if (ooid.contains("-")) {
                    String[] split = ooid.split("-");
                    tidSet.add(split[0]);
                }
            }
            AcLogisticsFeeInfoBySapItemResult itemResult = itemResultMap.get(item.getId());
            itemRequest.setCpCPhyWarehouseEcode(orderInfo.getCpCPhyWarehouseEcode());
            itemRequest.setRealOutNum(item.getRealOutNum().intValue());
            if (itemResult != null) {
                itemRequest.setExpressFee(String.valueOf(itemResult.getCourierFee()));
            }
            // 过滤掉realooutnum为0的数据
            if (itemRequest.getRealOutNum() == 0) {
                continue;
            }
            itemList.add(itemRequest);
        }
        if (CollectionUtils.isNotEmpty(tidSet)) {
            delivery2DmsRequest.setTid(tidSet.stream().collect(Collectors.joining(",")));
        }
        delivery2DmsRequest.setDetailList(itemList);
        // 判断销售商品属性 如果是残次品的话 去查询冻结出库单
        boolean isCc = OmsBusinessTypeUtil.isToBOrder(orderInfo) && OrderSaleProductAttrEnum.isToBCC(orderInfo.getSaleProductAttr());
        if (isCc) {
            List<SgStoFreezeOutResultQueryResult> sgStoFreezeOutResultQueryResults = sgNewRpcService.querySgBStoFreezeOutResultItemList(orderInfo);
            if (sgStoFreezeOutResultQueryResults != null) {
                List<Delivery2DmsItemBatchRequest> itemBatchRequests = new ArrayList<>();
                for (SgStoFreezeOutResultQueryResult sgStoFreezeOutResultQueryResult : sgStoFreezeOutResultQueryResults) {
                    Delivery2DmsItemBatchRequest itemBatchRequest = new Delivery2DmsItemBatchRequest();
                    itemBatchRequest.setProductDate(sgStoFreezeOutResultQueryResult.getProduceDate());
                    itemBatchRequest.setPsCSkuEcode(sgStoFreezeOutResultQueryResult.getPsCSkuEcode());
                    itemBatchRequest.setQty(sgStoFreezeOutResultQueryResult.getQty().intValue());
                    itemBatchRequests.add(itemBatchRequest);
                }
                delivery2DmsRequest.setBatchList(itemBatchRequests);
            }
        } else {
            // 获取逻辑出库单数据
            List<SgStoOutResultQueryResult> sgStoOutResultQueryResults = sgNewRpcService.querySgBStoOutResultItemList(orderInfo);
            if (sgStoOutResultQueryResults != null) {
                List<Delivery2DmsItemBatchRequest> itemBatchRequests = new ArrayList<>();
                for (SgStoOutResultQueryResult sgStoOutResultQueryResult : sgStoOutResultQueryResults) {
                    Delivery2DmsItemBatchRequest itemBatchRequest = new Delivery2DmsItemBatchRequest();
                    itemBatchRequest.setProductDate(sgStoOutResultQueryResult.getProduceDate());
                    itemBatchRequest.setPsCSkuEcode(sgStoOutResultQueryResult.getPsCSkuEcode());
                    itemBatchRequest.setQty(sgStoOutResultQueryResult.getQty().intValue());
                    itemBatchRequests.add(itemBatchRequest);
                }
                delivery2DmsRequest.setBatchList(itemBatchRequests);
            }
        }


        ValueHolderV14 valueHolderV14 = ipRpcService.delivery2Dms(delivery2DmsRequest);
        if (valueHolderV14.getCode() == ResultCode.FAIL) {
            String logMsg = "OrderId=" + orderId + ",平台单号=" + orderInfo.getTid() + "发货通知平台失败,";
            orderLogService.addUserOrderLog(orderId, orderInfo.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                    null, null);
            return false;
        } else {
            itemMapper.updateItemsWhenDeliverySuccess(orderId, orderInfo.getTid());
            String logMsg = "OrderId=" + orderId + ",平台单号=" + orderInfo.getTid() + "发货通知平台成功";
            orderLogService.addUserOrderLog(orderId, orderInfo.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                    null, null);
            OcBOrder update = new OcBOrder();
            update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            update.setId(orderId);
            update.setModifieddate(new Date());
            ocBOrderMapper.updateById(update);
            return true;
        }
    }
}
