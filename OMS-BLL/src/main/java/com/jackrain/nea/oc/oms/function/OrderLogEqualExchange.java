package com.jackrain.nea.oc.oms.function;

import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OmsEqualExchangeStService;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 对等换货日志优化
 *
 * <AUTHOR>
 * @Date 2024/5/10
 */
public interface OrderLogEqualExchange extends Consumer<List<OmsEqualExchangeStService.EquityExchangeResult>> {

    static OrderLogEqualExchange consumer(Map<Long, OcBOrderItem> orderItemMap, StringBuilder logMessageBuilder) {
        return results -> results.forEach(o -> {
            if (!o.isOriginalItem()) {
                OcBOrderItem ocBOrderItem = orderItemMap.get(o.getItemId());
                String log = ocBOrderItem.getPsCSkuEcode() + "=" + o.getExchangeQty().stripTrailingZeros().toPlainString() +
                        "换" + o.getEquitySkuCode() + "=" + o.getEquityQty().stripTrailingZeros().toPlainString() + "/";
                logMessageBuilder.append(log);
            }
        });
    }


    static String getBuilderToString(StringBuilder logMessageBuilder) {
        if (null == logMessageBuilder) {
            return "";
        }
        if (logMessageBuilder.toString().endsWith("/")) {
            logMessageBuilder.deleteCharAt(logMessageBuilder.toString().length() - 1);
        }
        return logMessageBuilder.toString();
    }
}
