package com.jackrain.nea.oc.oms.nums;

import com.jackrain.nea.exception.NDSException;
import lombok.Getter;

import java.util.Objects;

/**
 * className: ReturnOrderNodeEnum
 * description: 退换货单节点枚举
 *
 * <AUTHOR>
 * create: 2021-12-08
 * @since JDK 1.8
 */
public enum ReturnOrderNodeEnum {

    CREATE_TIME(1,"创建时间"),
    LOGISTIC_MODIFIED_TIME(2,"物流更新时间"),
    FIRST_TO_WING_TIME(3,"首次推送wing时间"),
    WMS_RECEIVE_TIME(4,"wms接收时间"),
    WING_TO_OMS_TIME(5,"wing回传中台时间"),
    WING_TO_WMS_TIME(6,"wing推送wms时间"),
    OMS_RECEIVE_WMS_SUCCESS_TIME(7,"中台接收传wms成功时间"),
    EXPRESS_RECEIVE_TIME(8,"揽收时间"),
    STORE_RECEIVE_TIME(9,"仓库签收时间"),
    WMS_TO_WING_TIME(10,"wms入库回传给wing时间"),
    OMS_STORE_IN_TIME(11,"中台仓库入库时间"),
    OMS_AUDIT_TIME(12,"客服审核时间"),
    PLATFORM_REFUND_TIME(13,"平台退款时间"),
    CANCEL_WMS_TIME(14,"wms手动撤回时间"),
    CONFIRM_TIME(15,"确认时间");

    @Getter
    private Integer value;

    @Getter
    private String desc;

    ReturnOrderNodeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value) {
        ReturnOrderNodeEnum[] values = ReturnOrderNodeEnum.values();
        for (ReturnOrderNodeEnum nodeEnum : values) {
            if (Objects.equals(nodeEnum.value, value)) {
                return nodeEnum.desc;
            }
        }
        throw new NDSException("错误的节点配置:" + value);
    }
}
