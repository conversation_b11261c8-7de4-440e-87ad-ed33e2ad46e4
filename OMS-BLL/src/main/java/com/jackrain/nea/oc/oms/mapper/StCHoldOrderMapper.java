package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.request.StCHoldOrderRequest;
import com.jackrain.nea.oc.oms.model.table.StCHoldOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCHoldOrderMapper extends ExtentionMapper<StCHoldOrderDO> {

    /**
     * 根据shopId查询策略
     * @param shopId
     * @return
     */
    @Select("select * from st_c_hold_order where cp_c_shop_id = #{shopId} and estatus = 2")
    List<StCHoldOrderRequest> queryStCHoldOrderByShopId(@Param("shopId") Long shopId);

    /**
     * 分页查询策略
     * @param pageSize
     * @return
     */
    @Select("select * from st_c_hold_order where estatus = 2 limit #{pageSize}")
    List<StCHoldOrderDO> queryStCHoldOrderLimit(@Param("pageSize") Integer pageSize);


    @Select("select * from st_c_hold_order where cp_c_shop_id = #{shopId} and estatus = 2")
    List<StCHoldOrderDO> selectStCHoldOrderList(@Param("shopId") Long shopId);
}