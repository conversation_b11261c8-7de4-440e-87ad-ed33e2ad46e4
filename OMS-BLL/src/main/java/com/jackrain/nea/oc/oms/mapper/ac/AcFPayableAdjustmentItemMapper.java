package com.jackrain.nea.oc.oms.mapper.ac;


import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface AcFPayableAdjustmentItemMapper extends ExtentionMapper<AcFPayableAdjustmentItemDO> {
    @Select("select * from ac_f_payable_adjustment_item where ac_f_payable_adjustment_id = #{id}")
    List<AcFPayableAdjustmentItemDO> listByItemId(@Param("id") Long id);


    @Select("select * from ac_f_payable_adjustment_item where ac_f_payable_adjustment_id = #{id} and isactive='Y'")
    List<AcFPayableAdjustmentItemDO> selectListByMainId(@Param("id") Long id);

    @Delete("delete from ac_f_payable_adjustment_item where ac_f_payable_adjustment_id = #{id}")
    int deletePayableItem(@Param("id") Long id);

    @Update("UPDATE ac_f_payable_adjustment_item " +
            "SET logical_store_id = #{logicalStoreId}, logical_store_code = #{logicalStoreCode}, logical_store_name = #{logicalStoreName}" +
            "WHERE ac_f_payable_adjustment_id = #{id} AND order_item_id = #{orderItemId}")
    int updateStorePayableItem(@Param("id") Long id, @Param("orderItemId") Long orderItemId,
                               @Param("logicalStoreId") Long logicalStoreId, @Param("logicalStoreCode") String logicalStoreCode,
                               @Param("logicalStoreName") String logicalStoreName);
}