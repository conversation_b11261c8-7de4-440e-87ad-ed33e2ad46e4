package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

/**
 * @ClassName OcBOrderExtendMapper
 * @Description 零售发货单拓展表
 * <AUTHOR>
 * @Date 2022/11/12 16:51
 * @Version 1.0
 */
@Component
@Mapper
public interface OcBOrderExtMapper extends ExtentionMapper<OcBOrderExt> {

    /**
     * 根据零售发货单id查询拓展表数据
     *
     * @param orderId 零售发货单id
     * @return
     */
    @Select("SELECT * FROM oc_b_order_ext WHERE oc_b_order_id=#{orderId} and isactive='Y' limit 1")
    OcBOrderExt selectByOrderId(@Param("orderId") long orderId);

}
