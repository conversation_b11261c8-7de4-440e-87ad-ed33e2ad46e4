package com.jackrain.nea.oc.oms.services.patrol;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.es.ES4IpJingDongOrder;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderRefundStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.ReturnStorageListService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 线上巡查
 *
 * @author: 夏继超
 * @since: 2019/6/4
 * create at : 2019/6/4 11:33
 */
@Slf4j
@Component
public class OnLineForCheckService {
    @Autowired
    OcBOrderItemMapper ocBOrderItemMapper;

    /**
     * 没有明细《占单》       （待分配 ，没有明细）
     *
     * @return
     */
    public String check1() {
        ValueHolderV14 vh = new ValueHolderV14();
        Date date = new Date();//取时间
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Long dateBegin = calendar.getTime().getTime();
        calendar.set(Calendar.HOUR, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Long dateEnd = calendar.getTime().getTime();
        Integer range = 100000;

        JSONObject search = ES4Order.findIdsByOrderStatusFilterByCreationDate(OmsOrderStatus.ORDER_DEFAULT.toInteger(),
                dateBegin + "~" + dateEnd, range);

        if (search == null) {
            vh.setMessage("成功");
            vh.setCode(0);
            vh.setData(null);
            return JSONObject.toJSONString(vh);
        }
        JSONArray data = search.getJSONArray("data");
        if (data.isEmpty()) {
            vh.setMessage("成功");
            vh.setCode(0);
            vh.setData(null);
            return JSONObject.toJSONString(vh);
        }
        //获取IDS编号数组
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < data.size(); i++) {
            String id = "" + data.getJSONObject(i).getString("ID") + "";
            jsonArray.add(id);
        }
        String join = StringUtils.join(jsonArray, ",");
        List list = ReturnStorageListService.stringToList(join);

        JSONArray arr = new JSONArray();
        int count = 0;
        for (int i = 0; i < list.size(); i++) {
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.eq("oc_b_order_id", list.get(i));
            List ocBOrderItems = ocBOrderItemMapper.selectList(wrapper);
            if (ocBOrderItems.size() == 0 && ocBOrderItems == null) {
                arr.set(count, list.get(i));
                count++;
            }
        }
        vh.setMessage("查询成功");
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(arr);
        return JSONObject.toJSONString(vh);
    }

    /**
     * 中间表重复《转单》        （TID、orderId 重复 昌源）
     *
     * @return
     */
    public String check2() {
        ValueHolderV14 vh = new ValueHolderV14();
      /*  Date date=new Date();//取时间
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        calendar.set(Calendar.MILLISECOND,0);
        Long dateBegin= calendar.getTime().getTime();
        calendar.set(Calendar.HOUR,23);
        calendar.set(Calendar.MINUTE,59);
        calendar.set(Calendar.SECOND,59);
        calendar.set(Calendar.MILLISECOND,999);
        Long dateEnd= calendar.getTime().getTime();
        filterKey.put("CREATIONDATE", dateBegin + "~" + dateEnd);*/

        JSONObject search = ES4IpJingDongOrder.findOrderId();

        if (search == null) {
            vh.setMessage("成功");
            vh.setCode(0);
            vh.setData(null);
            return JSONObject.toJSONString(vh);
        }
        JSONArray data = search.getJSONArray("data");
        HashSet set = new HashSet();
        for (int i = 0; i < data.size(); i++) {
            set.add(JSON.parseObject(data.get(i).toString()).getString("ORDER_ID"));
        }
        List<HashMap<String, Object>> hash = new ArrayList<>();
        JSONArray jdList = new JSONArray();
        for (int i = 0; i < data.size(); i++) {
            String order_id = JSON.parseObject(data.get(i).toString()).getString("ORDER_ID");
            if (set.contains(order_id)) {
                HashMap hashMap = new HashMap();
                //hashMap.put("")
            }
        }
        return JSONObject.toJSONString(vh);
    }

    /**
     * 订单不在等待卖家同意退款状态，非人工拦截处于拦截状态的（明细状态和头表的拦截状态   标记退款完整、退款转换、订单拦截）？？？
     *
     * @return
     */
    public String check3() {
        ValueHolderV14 vh = new ValueHolderV14();
        Date date = new Date();//取时间
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Long dateBegin = calendar.getTime().getTime();
        calendar.set(Calendar.HOUR, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Long dateEnd = calendar.getTime().getTime();
        Integer range = 100000;

        JSONObject search = ES4Order.findIdsByIsInterceptAndMfNameFilterByCDate(dateBegin + "~" + dateEnd, range,
                OmsOrderIsInterceptEnum.YES.getVal());

        if (search == null) {
            vh.setMessage("成功");
            vh.setCode(0);
            vh.setData(null);
            return JSONObject.toJSONString(vh);
        }
        JSONArray data = search.getJSONArray("data");
        if (data.isEmpty()) {
            vh.setMessage("成功");
            vh.setCode(0);
            vh.setData(null);
            return JSONObject.toJSONString(vh);
        }
        //获取IDS编号数组
        JSONArray jsonArray = new JSONArray();
        for (int i = 0; i < data.size(); i++) {
            String id = "" + data.getJSONObject(i).getString("ID") + "";
            jsonArray.add(id);
        }
        String join = StringUtils.join(jsonArray, ",");
        List list = ReturnStorageListService.stringToList(join);
        JSONArray arr = new JSONArray();
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("oc_b_order_id", list);
        HashSet set = new HashSet();
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectList(wrapper);
        for (int i = 0; i < ocBOrderItems.size(); i++) {
            OcBOrderItem ocBOrderItem = ocBOrderItems.get(i);
            if (OmsOrderRefundStatus.WAITSELLERAGREE.toInteger() != ocBOrderItem.getRefundStatus()) {
                set.add(ocBOrderItem.getOcBOrderId());
            }
        }
        JSONArray array = new JSONArray();
        Iterator iterator = set.iterator();
        while (iterator.hasNext()) {
            array.add(iterator.next());
        }
        vh.setMessage("查询成功");
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(array);
        return JSONObject.toJSONString(vh);

    }
}
