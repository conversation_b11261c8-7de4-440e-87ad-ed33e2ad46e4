package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBShopSkuBatchInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 店铺最新发货效期表 Mapper 接口
 *
 * <AUTHOR>
 * @date 2024-12-16
 */
@Mapper
public interface OcBShopSkuBatchInfoMapper extends ExtentionMapper<OcBShopSkuBatchInfo> {

    /**
     * 根据店铺编码和SKU查询记录
     *
     * @param shopCode 店铺编码
     * @param sku SKU编码
     * @return 店铺SKU批次信息
     */
    @Select("SELECT * FROM oc_b_shop_sku_batch_info WHERE shop_code = #{shopCode} AND sku = #{sku} AND isactive = 'Y'")
    OcBShopSkuBatchInfo selectByShopCodeAndSku(@Param("shopCode") String shopCode, @Param("sku") String sku);

    /**
     * 根据店铺ID和SKU查询记录
     *
     * @param shopId 店铺ID
     * @param sku SKU编码
     * @return 店铺SKU批次信息
     */
    @Select("SELECT * FROM oc_b_shop_sku_batch_info WHERE shop_id = #{shopId} AND sku = #{sku} AND isactive = 'Y'")
    OcBShopSkuBatchInfo selectByShopIdAndSku(@Param("shopId") Long shopId, @Param("sku") String sku);

    /**
     * 根据店铺ID、SKU和收货地址查询记录
     *
     * @param shopId 店铺ID
     * @param sku SKU编码
     * @param receiverAddress 收货地址
     * @return 店铺SKU批次信息
     */
    @Select("<script>" +
            "SELECT * FROM oc_b_shop_sku_batch_info " +
            "WHERE shop_id = #{shopId} AND sku = #{sku} AND isactive = 'Y' " +
            "<choose>" +
            "<when test='receiverAddress != null and receiverAddress != \"\"'>" +
            "AND receiver_address = #{receiverAddress}" +
            "</when>" +
            "<otherwise>" +
            "AND (receiver_address IS NULL OR receiver_address = '')" +
            "</otherwise>" +
            "</choose>" +
            "</script>")
    OcBShopSkuBatchInfo selectByShopIdAndSkuAndAddress(@Param("shopId") Long shopId,
                                                       @Param("sku") String sku,
                                                       @Param("receiverAddress") String receiverAddress);

    /**
     * 根据店铺编码查询所有SKU记录
     *
     * @param shopCode 店铺编码
     * @return SKU批次信息列表
     */
    @Select("SELECT * FROM oc_b_shop_sku_batch_info WHERE shop_code = #{shopCode} AND isactive = 'Y' ORDER BY modifieddate DESC")
    List<OcBShopSkuBatchInfo> selectByShopCode(@Param("shopCode") String shopCode);

    /**
     * 根据店铺ID查询所有SKU记录
     *
     * @param shopId 店铺ID
     * @return SKU批次信息列表
     */
    @Select("SELECT * FROM oc_b_shop_sku_batch_info WHERE shop_id = #{shopId} AND isactive = 'Y' ORDER BY modifieddate DESC")
    List<OcBShopSkuBatchInfo> selectByShopId(@Param("shopId") Long shopId);

    /**
     * 更新或插入SKU批次信息（如果存在则更新，不存在则插入）
     * 这个方法需要在Service层实现具体逻辑
     *
     * @param shopCode 店铺编码
     * @param sku SKU编码
     * @param produceDate 生产日期（yyyyMMdd格式）
     * @param modifierId 修改人ID
     * @param modifierName 修改人姓名
     * @param modifierEname 修改人用户名
     * @return 影响行数
     */
    @Update("UPDATE oc_b_shop_sku_batch_info SET " +
            "produce_date = #{produceDate}, " +
            "modifierid = #{modifierId}, " +
            "modifiername = #{modifierName}, " +
            "modifierename = #{modifierEname}, " +
            "modifieddate = NOW() " +
            "WHERE shop_code = #{shopCode} AND sku = #{sku} AND isactive = 'Y'")
    int updateSkuBatchInfo(@Param("shopCode") String shopCode,
                          @Param("sku") String sku,
                          @Param("produceDate") String produceDate,
                          @Param("modifierId") Long modifierId,
                          @Param("modifierName") String modifierName,
                          @Param("modifierEname") String modifierEname);

    /**
     * 根据店铺编码和SKU列表批量查询
     *
     * @param shopCode 店铺编码
     * @param skuList SKU编码列表
     * @return SKU批次信息列表
     */
    @Select("<script>" +
            "SELECT * FROM oc_b_shop_sku_batch_info " +
            "WHERE shop_code = #{shopCode} AND isactive = 'Y' " +
            "AND sku IN " +
            "<foreach item='sku' index='index' collection='skuList' open='(' separator=',' close=')'>" +
            "#{sku}" +
            "</foreach>" +
            "</script>")
    List<OcBShopSkuBatchInfo> selectByShopCodeAndSkuList(@Param("shopCode") String shopCode, @Param("skuList") List<String> skuList);

    /**
     * 根据店铺ID和SKU列表批量查询
     *
     * @param shopId 店铺ID
     * @param skuList SKU编码列表
     * @return SKU批次信息列表
     */
    @Select("<script>" +
            "SELECT * FROM oc_b_shop_sku_batch_info " +
            "WHERE shop_id = #{shopId} AND isactive = 'Y' " +
            "AND sku IN " +
            "<foreach item='sku' index='index' collection='skuList' open='(' separator=',' close=')'>" +
            "#{sku}" +
            "</foreach>" +
            "</script>")
    List<OcBShopSkuBatchInfo> selectByShopIdAndSkuList(@Param("shopId") Long shopId, @Param("skuList") List<String> skuList);
}
