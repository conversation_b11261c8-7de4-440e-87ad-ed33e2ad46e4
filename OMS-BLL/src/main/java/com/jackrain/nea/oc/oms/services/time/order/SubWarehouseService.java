package com.jackrain.nea.oc.oms.services.time.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.core.model.ext.SgBStorageInclShare;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.entity.CpCOrgChannelItemEntity;
import com.jackrain.nea.cp.result.CpCStore;
import com.jackrain.nea.data.basic.model.request.StoreInfoQueryRequest;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.ps.api.table.PsCSku;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCSendRuleAddressVipDo;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.st.service.OmsSyncStockStrategyService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ListPageUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.vo.PhyWarehouseVo;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 分仓service
 *
 * <AUTHOR>
 * @date 2020-08-15
 */
@Component
@Slf4j
public class SubWarehouseService {

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private BasicCpQueryService basicCpQueryService;

    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    private OmsSyncStockStrategyService omsSyncStockStrategyService;


    /**
     * 分仓入口service
     *
     * @param shopId 店铺id
     * @param skuMap sku 信息
     * @return Map   key 实体仓id  value Map   key skuEcode  value 数量
     */
    public ValueHolderV14<Map<Long, Map<String, BigDecimal>>> subWarehouseInput(Long shopId
            , Map<PsCSku, BigDecimal> skuMap, String jitCode, User user) {
        if (Objects.isNull(shopId) || skuMap.isEmpty()) {
            return ValueHolderV14Utils.getFailValueHolder("新分仓，当前入参店铺id或sku信息为空");
        }
        //1.查询该店下的逻辑仓-实体仓
        ValueHolder v = this.branchWarehouse(shopId);
        Integer code = (Integer) v.get("code");
        if (code == ResultCode.FAIL) {
            return ValueHolderV14Utils.getFailValueHolder("新分仓，查询店铺档案实体逻辑仓异常");
        }
        //实体仓信息
        List<PhyWarehouseVo> phyWarehouseVoList = (List<PhyWarehouseVo>) v.get("data");
        if (CollectionUtils.isEmpty(phyWarehouseVoList)) {
            return ValueHolderV14Utils.getFailValueHolder("新分仓，查询店铺档案实体逻辑仓为空，全部为空");
        }
        // phyWarehouseVoList 分组
        // 查询唯品会派单规则
        List<StCSendRuleAddressVipDo> ruleVip = stRpcService.findRuleAddressVipByShopId(shopId, jitCode);
        if (CollectionUtils.isNotEmpty(ruleVip)) {
            for (StCSendRuleAddressVipDo item : ruleVip) {
                String warehouseRank = item.getWarehouseRank();
                JSONArray jsonArray = JSON.parseArray(warehouseRank);
                for (Object o : jsonArray) {
                    JSONObject json = (JSONObject) JSON.toJSON(o);
                    Long warehouseId = json.getLong("warehouseId");
                    for (PhyWarehouseVo vo : phyWarehouseVoList) {
                        if (vo.getPhyWarehouseId().equals(warehouseId)) {
                            vo.setRank(Integer.valueOf(json.getString("rank")));
                        }
                    }
                }
            }
        }
        ValueHolderV14<Map<Long, Map<String, BigDecimal>>> mapValueHolderV14 = calculateQuantity(phyWarehouseVoList, skuMap, user);
        return mapValueHolderV14;
    }


    /**
     * 新分仓， 计算没一个实体仓可以发多少数量
     *
     * @param phyWarehouseVoList 实体-逻辑仓信息
     * @param skuMap             需要发货的数量
     * @param user               用户
     * @return Map
     */
    public ValueHolderV14<Map<Long, Map<String, BigDecimal>>> calculateQuantity(List<PhyWarehouseVo> phyWarehouseVoList,
                                                                                Map<PsCSku, BigDecimal> skuMap,
                                                                                User user) {
        if (CollectionUtils.isEmpty(phyWarehouseVoList) || skuMap.isEmpty() || Objects.isNull(user)) {
            return null;
        }

        ValueHolderV14<Map<Long, Map<String, BigDecimal>>> v14 = new ValueHolderV14<>();
        Map<Long, Map<String, BigDecimal>> responseMap = new LinkedHashMap<>(16);
        // 查询库存
        if (CollectionUtils.isNotEmpty(phyWarehouseVoList)) {
            //逻辑仓集合
            List<Long> wareIdList = phyWarehouseVoList.stream().map(PhyWarehouseVo::getPhyWarehouseId).collect(Collectors.toList());
            //寻仓库存集合
            List<SgBStorageInclShare> sgResponse = new ArrayList<>();
            //分页
            ListPageUtil<PsCSku> pager = new ListPageUtil<>(new ArrayList<>(skuMap.keySet()), 500);
            int totalPage = pager.getPageCount();
            for (int i = 1; i <= totalPage; i++) {
                List<PsCSku> psSkuList = pager.getPagedList(i);
                List<Long> skuIdList = psSkuList.stream().map(PsCSku::getId).collect(Collectors.toList());
                try {
                    ValueHolderV14<List<SgBStorageInclShare>> data = sgRpcService.selectStoreWarehouseSkuNum(wareIdList, skuIdList);
                    if (Objects.isNull(data)) {
                    } else {
                        if (ResultCode.SUCCESS == data.getCode() && CollectionUtils.isNotEmpty(data.getData())) {
                            sgResponse.addAll(data.getData());
                        }
                    }
                } catch (Exception e) {
                    log.error(LogUtil.format("捕获查询库存异常:{}"), Throwables.getStackTraceAsString(e));
                }
            }
            if (CollectionUtils.isEmpty(sgResponse)) {
                v14.setCode(ResultCode.FAIL);
                v14.setMessage("新分仓，查询库存为空。全部缺货");
                return v14;
            } else {
                // 将有无优先级 过滤
                List<PhyWarehouseVo> warehouseVos1 = phyWarehouseVoList.stream().filter(f -> Objects.nonNull(f.getRank())).collect(Collectors.toList());
                List<PhyWarehouseVo> warehouseVos2 = phyWarehouseVoList.stream().filter(f -> Objects.isNull(f.getRank())).collect(Collectors.toList());
                // 计算仓库发货数量
                // 派单规则没有实体仓优先级
                if (CollectionUtils.isEmpty(warehouseVos1)) {
                    Map<Long, Map<String, BigDecimal>> longMapMap = warehouseNumber(phyWarehouseVoList, sgResponse, skuMap);
                    if (longMapMap.isEmpty()) {
                        return null;
                    } else {
                        responseMap.putAll(longMapMap);
                    }
                } else {
                    // phyWarehouseVoList 以rank 分组  得到每一个优先级 仓库信息
                    Map<Integer, List<PhyWarehouseVo>> integerListMap = sortMapByKey(warehouseVos1.stream().collect(Collectors.groupingBy(PhyWarehouseVo::getRank)));
                    for (Integer rank : integerListMap.keySet()) {
                        List<PhyWarehouseVo> phyWarehouseVos = integerListMap.get(rank);
                        if (CollectionUtils.isNotEmpty(sgResponse) || MapUtils.isNotEmpty(skuMap)) {
                            // 将每一个排完序的实体仓传入
                            Map<Long, Map<String, BigDecimal>> longMapMap = warehouseNumber(phyWarehouseVos, sgResponse, skuMap);
                            if (MapUtils.isEmpty(longMapMap)) {
                                continue;
                            } else {
                                responseMap.putAll(longMapMap);
                            }
                            if (CollectionUtils.isEmpty(sgResponse) || MapUtils.isEmpty(skuMap)) {
                                break;
                            }
                        }
                    }
                    // 执行完之后   还不满足使用没有优先级的实体仓
                    Map<Long, Map<String, BigDecimal>> longMapMap = warehouseNumber(warehouseVos2, sgResponse, skuMap);
                    if (MapUtils.isEmpty(longMapMap)) {

                    } else {
                        responseMap.putAll(longMapMap);
                    }


                }
            }
        }
        if (MapUtils.isNotEmpty(responseMap)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setData(responseMap);
        } else {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("分仓失败");
        }
        return v14;
    }


    /**
     * 传入一个优先级的仓库信息进行计算每一个仓库发货数量
     * 当 warehouseNumberSum 方法返回的一个实体仓对应的skushu 数量不满足 总发货量时，减去已经匹配上的实体仓和sku 再次进行warehouseNumberSum计算
     * 一直到  所有sku 打完
     *
     * @param phyWarehouse 如果有优先级  则是一个优先级的仓库信息，如果没有，是店铺同步库存策略的仓库信息
     * @param sgResponse   sgResponse  库存信息
     * @param skuMap       需要拣货的sku
     * @return Map key 实体仓id, value Map  key skuEcode, value 数量
     */
    public Map<Long, Map<String, BigDecimal>> warehouseNumber(List<PhyWarehouseVo> phyWarehouse,
                                                              List<SgBStorageInclShare> sgResponse,
                                                              Map<PsCSku, BigDecimal> skuMap) {
        boolean bool = false;
        Map<Long, Map<String, BigDecimal>> returnMap = new HashMap<>(16);
        if (CollectionUtils.isEmpty(sgResponse) || skuMap.isEmpty()) {
            return null;
        }
        int num = 0;
        do {
            num++;
            if (CollectionUtils.isEmpty(phyWarehouse) || CollectionUtils.isEmpty(sgResponse) || skuMap.isEmpty()) {
                break;
            }
            Map<Long, Map<PsCSku, BigDecimal>> longMapMap = warehouseNumberSum(phyWarehouse, sgResponse, skuMap);

            if (longMapMap.isEmpty()) {
                break;
            }
            for (Map.Entry<Long, Map<PsCSku, BigDecimal>> longMapEntry : longMapMap.entrySet()) {
                Long key = longMapEntry.getKey();
                Map<PsCSku, BigDecimal> value = longMapEntry.getValue();
                Map<String, BigDecimal> skuAndNumMap = new HashMap<>();
                for (Map.Entry<PsCSku, BigDecimal> psCSkuBigDecimalEntry : value.entrySet()) {
                    skuAndNumMap.put(psCSkuBigDecimalEntry.getKey().getEcode(), psCSkuBigDecimalEntry.getValue());
                }
                returnMap.put(key, skuAndNumMap);
            }
            //returnMap.putAll(longMapMap);
            // map中 发货总数 与 sku拣货数 对比， 小于则证明 当前仓库 不满足发所有的货。减去sgResponse已匹配sku 再次进入warehouseNumberSum再获取最大发货量
            // sku 总数
            Collection<BigDecimal> skuValues = skuMap.values();
            BigDecimal skuNumber = new BigDecimal(0);
            BigDecimal numBerSgBySku = new BigDecimal(0);
            for (BigDecimal sku : skuValues) {
                skuNumber = skuNumber.add(sku);
            }
            for (Long item : longMapMap.keySet()) {
                Map<PsCSku, BigDecimal> stringBigDecimalMap = longMapMap.get(item);
                Collection<BigDecimal> sgValuesByWarehouse = stringBigDecimalMap.values();
                for (BigDecimal bItem : sgValuesByWarehouse) {
                    numBerSgBySku = numBerSgBySku.add(bItem);
                    if (numBerSgBySku.compareTo(BigDecimal.ZERO) == 0) {
                        break;
                    }
                }
            }
            // 走下一次分仓前  需要减去第一次已经分过的sku数量
            if (MapUtils.isNotEmpty(longMapMap)) {
                // longMapMap 只会返回一个实体仓信息  longMapMap.size 只能是1
                for (Long warehouseId : longMapMap.keySet()) {
                    Map<PsCSku, BigDecimal> stringBigDecimalMap = longMapMap.get(warehouseId);
                    // 删除已经计算过的sku 和 库存信息
                    Set<PsCSku> psCSkus = stringBigDecimalMap.keySet();
                    Iterator<PsCSku> iterator = psCSkus.iterator();
                    while (iterator.hasNext()) {
                        PsCSku psSku = iterator.next();
                        BigDecimal bigDecimal = stringBigDecimalMap.get(psSku);
                        BigDecimal bigDecimal1 = skuMap.get(psSku);
                        if (null != bigDecimal1) {
                            if (bigDecimal.compareTo(bigDecimal1) < 0) {
                                // 已经打过的sku
                                bigDecimal1 = bigDecimal1.subtract(bigDecimal);
                                skuMap.put(psSku, bigDecimal1);
                            } else {
                                iterator.remove();
                            }
                        }
                    }
                    sgResponse = sgResponse.stream().filter(sg -> !sg.getCpCPhyWarehouseId().equals(warehouseId)).collect(Collectors.toList());
                    phyWarehouse = phyWarehouse.stream().filter(phy -> !phy.getPhyWarehouseId().equals(warehouseId)).collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isEmpty(sgResponse) || skuMap.isEmpty()) {
                break;
            }
            // 还需要走下一次分仓， 减去库存和需要拣货的sku
            bool = skuNumber.compareTo(numBerSgBySku) != 0;

        } while (bool);
        return returnMap;
    }


    /**
     * 计算仓库发货数量    传入一个优先级的仓库和sku   每一个发货仓库数量和id  返回一个仓库的 发货数量
     * 优先级相同  比较仓库发货数量    数量相同  比价仓库id
     *
     * @param phyWarehouse 一个优先级量的仓库
     * @param sgResponse   库存
     * @param skuMap       sku
     * @return Map 只返回一个仓库的发货信息  key  仓库id  value sku 发货数量
     */
    public Map<Long, Map<PsCSku, BigDecimal>> warehouseNumberSum(List<PhyWarehouseVo> phyWarehouse,
                                                                 List<SgBStorageInclShare> sgResponse,
                                                                 Map<PsCSku, BigDecimal> skuMap) {
        if (CollectionUtils.isEmpty(phyWarehouse) || CollectionUtils.isEmpty(sgResponse) || MapUtils.isEmpty(skuMap)) {
            return null;
        }
        // 定义每一个仓库的可发货量
        Map<Long, Map<PsCSku, BigDecimal>> returnSumByWarehouseMap = new HashMap<>(16);
        Map<Long, List<SgBStorageInclShare>> sgResponseSumMap = sgResponse.stream().collect(Collectors.groupingBy(SgBStorageInclShare::getCpCPhyWarehouseId));
        Map<Long, BigDecimal> sumWarehouseQtyMap = new HashMap<>(16);
        for (PhyWarehouseVo warehouseVo : phyWarehouse) {
            List<SgBStorageInclShare> sgSumStorageQueryResults = sgResponseSumMap.get(warehouseVo.getPhyWarehouseId());
            if (CollectionUtils.isNotEmpty(sgSumStorageQueryResults)) {
                // 定义当前仓库总量
                BigDecimal sumNumber = new BigDecimal(0);
                // 定义当前仓库 sku 发货信息
                Map<PsCSku, BigDecimal> skuByWarehouseMap = new HashMap<>(16);
                for (SgBStorageInclShare sgItem : sgSumStorageQueryResults) {
                    for (PsCSku psSku : skuMap.keySet()) {
                        if (sgItem.getPsCSkuId().equals(psSku.getId())) {
                            // 当前仓库sku 库存数量
                            BigDecimal qtyAvailable = sgItem.getQtyAvailable();
                            // 需要拣货的sku数量
                            BigDecimal skuNumber = skuMap.get(psSku);
                            if (qtyAvailable.compareTo(skuNumber) > -1) {
                                // 某一个sku 当前仓库 满足
                                skuByWarehouseMap.put(psSku, skuNumber);
                                //sumNumber = sumNumber.add(skuNumber);
                            } else {
                                // 某一个sku 当前仓库 不满足
                                skuByWarehouseMap.put(psSku, qtyAvailable);
                                //sumNumber = sumNumber.add(qtyAvailable);
                            }
                            sumNumber = sumNumber.add(qtyAvailable);
                        }
                    }
                }
                sumWarehouseQtyMap.put(warehouseVo.getPhyWarehouseId(), sumNumber);
                if (MapUtils.isNotEmpty(returnSumByWarehouseMap)) {
                    // 如果不为空   则证明是后续仓库循环  算总数
                    // 证明是第二次以后的仓库计算，returnSumByWarehouseMap 中必须得保证只能有一个仓库的sku信息
                    for (Long map : returnSumByWarehouseMap.keySet()) {
                        BigDecimal sumQty = sumWarehouseQtyMap.get(map);
                        if (sumNumber.compareTo(sumQty) > 0) {
                            returnSumByWarehouseMap = new HashMap<>(16);
                            returnSumByWarehouseMap.put(warehouseVo.getPhyWarehouseId(), skuByWarehouseMap);
                        } else if (sumNumber.compareTo(sumQty) == 0) {
                            // 当前仓库
                            long long1 = warehouseVo.getPhyWarehouseId();
                            long long2 = map;
                            if (long1 > long2) {
                                returnSumByWarehouseMap = new HashMap<>(16);
                                returnSumByWarehouseMap.put(warehouseVo.getPhyWarehouseId(), skuByWarehouseMap);
                            }
                        }
                    }
                } else {
                    // 处理如果第一次就分到 空的情况   如果是空  打印日志   不进returnSumByWarehouseMap
                    if (MapUtils.isNotEmpty(skuByWarehouseMap)) {
                        returnSumByWarehouseMap.put(warehouseVo.getPhyWarehouseId(), skuByWarehouseMap);
                    }
                }
            }
        }

        return returnSumByWarehouseMap;
    }


    /**
     * Map按key进行排序
     *
     * @param map 排序前map
     * @return map 排序后map
     */
    public Map<Integer, List<PhyWarehouseVo>> sortMapByKey(Map<Integer, List<PhyWarehouseVo>> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        Map<Integer, List<PhyWarehouseVo>> sortMap = new TreeMap<>(Integer::compareTo);
        sortMap.putAll(map);
        return sortMap;
    }

    /**
     * 分仓
     *
     * @param shopId
     * @return 返回默认仓库
     */
    public ValueHolder branchWarehouse(Long shopId) {
        ValueHolder valueHolder = new ValueHolder();
        HashMap<Long, CpCStore> hashMap;
        List<PhyWarehouseVo> phyWarehouseVoList = new ArrayList<>();
        // 调用店铺同步库存策略接口
        List<CpCOrgChannelItemEntity> orgChannelItemEntities = null;
        try {
            orgChannelItemEntities = omsSyncStockStrategyService.querySyncStockStrategyItemList(shopId);
        } catch (Exception e) {
            log.error(LogUtil.format("查询店铺供货逻辑仓列表异常:{}"), Throwables.getStackTraceAsString(e));
        }
        // 逻辑仓不存在
        if (CollectionUtils.isEmpty(orgChannelItemEntities)) {
            return ValueHolderUtils.getFailValueHolder("店铺下没有维护供货逻辑仓");
        }
        // 逻辑仓存在
        if (CollectionUtils.isNotEmpty(orgChannelItemEntities)) {
            List<Long> storeIdList = orgChannelItemEntities.stream()
                    .filter(x -> x.getCpCStoreId() != null)
                    .map(CpCOrgChannelItemEntity::getCpCStoreId).collect(Collectors.toList());
            // 逻辑仓为空，店铺同步库存策略保存导致逻辑仓为空
            if (CollectionUtils.isEmpty(storeIdList)) {
                return ValueHolderUtils.getFailValueHolder("店铺下的供货逻辑仓ID没有维护");
            }
            StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
            storeInfoQueryRequest.setIds(storeIdList);
            try {
                hashMap = basicCpQueryService.getStoreInfo(storeInfoQueryRequest);
            } catch (Exception e) {
                log.error(LogUtil.format(" 该店铺【" + shopId + "】逻辑仓查询实体仓出错{}"), Throwables.getStackTraceAsString(e));
                return ValueHolderUtils.getFailValueHolder("逻辑仓查询实体仓出错");
            }
            // 判断逻辑仓的实体仓数量，若就一个实体仓就用这个实体仓作为发货仓
            for (Long cpCStoreId : storeIdList) {
                if (hashMap.get(cpCStoreId) != null) {
                    CpCStore cpCStore = hashMap.get(cpCStoreId);
                    PhyWarehouseVo phyWarehouseVo = new PhyWarehouseVo();
                    // 实体仓
                    phyWarehouseVo.setPhyWarehouseId(cpCStore.getCpCPhyWarehouseId());
                    // 逻辑仓
                    phyWarehouseVo.setStoreId(cpCStoreId);
                    phyWarehouseVoList.add(phyWarehouseVo);
                }
            }
            return ValueHolderUtils.custom(ResultCode.SUCCESS, "分配发货成功！！！", phyWarehouseVoList);
        }
        return valueHolder;
    }

    /**
     * 缺货默认仓库判断
     *
     * @param shopId
     * @return
     */
    public ValueHolderV14<PhyWarehouseVo> getDefaultPhyWarehouse(Long shopId) {
        ValueHolderV14<PhyWarehouseVo> vh = new ValueHolderV14<>();
        List<PhyWarehouseVo> phyWarehouseVoList = new ArrayList<>();
        // 逻辑仓不存在，查询店铺策略的缺货默认仓
        StCShopStrategyDO shopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(shopId);
        // 缺货默认仓为空
        if (shopStrategyDO == null || shopStrategyDO.getDefaultStoreId() == null ||
                shopStrategyDO.getDefaultStoreId() == 0) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("分配发货仓库失败，店铺未设置供货仓库!!!");
            return vh;
        }
        PhyWarehouseVo phyWarehouseVo = new PhyWarehouseVo();
        phyWarehouseVo.setPhyWarehouseId(shopStrategyDO.getDefaultStoreId());
        phyWarehouseVo.setStatus(TimeOrderVipStatusEnum.OUT_STOCK.getValue());
        phyWarehouseVoList.add(phyWarehouseVo);
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("分配发货仓库成功!!!");
        if (CollectionUtils.isNotEmpty(phyWarehouseVoList)) {
            vh.setData(phyWarehouseVoList.get(0));
        }
        return vh;
    }

    private void printLog(String message, Object... argos) {
        if (log.isDebugEnabled()) {
            message = this.getClass().getSimpleName() + message;
            log.debug(message, argos);
        }
    }
}