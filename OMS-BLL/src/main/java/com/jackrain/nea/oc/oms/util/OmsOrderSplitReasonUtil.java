package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.StCSplitReasonRequest;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @program: r3-oc-oms
 * @description: 自定义拆单原因匹配
 * @author: liuwj
 * @create: 2021-06-01 18:58
 **/
@Component
@Slf4j
public class OmsOrderSplitReasonUtil {

    @Autowired
    private StRpcService stRpcService;

    /**
     * <AUTHOR>
     * @Date 19:26 2021/6/1
     * @Description 匹配拆单原因
     */
    public void matchReason(OcBOrder ocBOrder) {
        if (ocBOrder == null || ocBOrder.getSplitReason() == null) {
            log.info("该订单为空，或者没有拆单原因");
            return;
        }
        try {
            String splitReason = ocBOrder.getSplitReason().toString();
            Map<String, StCSplitReasonRequest> requestMap = stRpcService.queryStCSplitReasonAll();
            if (requestMap == null) {
                log.info("没有自定义拆单策略");
                return;
            }
            StCSplitReasonRequest stCSplitReasonRequest = requestMap.get(splitReason);
            if (stCSplitReasonRequest == null) {
                return;
            }
            ocBOrder.setSplitReasonId(stCSplitReasonRequest.getSplitReasonConfigId());
            ocBOrder.setCustomReason(stCSplitReasonRequest.getCustomReason());
        } catch (Exception e) {
            log.error(LogUtil.format("匹配自定义拆单原因异常:{}", ocBOrder.getId()), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * <AUTHOR>
     * @Date 14:41 2021/6/2
     * @Description 赋值自定义拆单原因
     */
    public void setCustomReason(List<OcBOrderRelation> ocBOrderRelationList) {
        if (CollectionUtils.isEmpty(ocBOrderRelationList)) {
            log.info(LogUtil.format("该订单集合为空", "该订单集合为空"));
            return;
        }
        for (OcBOrderRelation ocBOrderRelation : ocBOrderRelationList) {
            OcBOrder order = ocBOrderRelation.getOrderInfo();
            matchReason(order);
        }
    }
}
