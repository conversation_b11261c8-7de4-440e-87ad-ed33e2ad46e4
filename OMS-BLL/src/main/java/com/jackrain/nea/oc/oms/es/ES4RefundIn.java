package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.ReturnInBilStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2020/11/2 9:31 下午
 */
@Slf4j
public class ES4RefundIn {

    /**
     * 根据物流单号es查询退货入库id
     *
     * @param logisticNumber 物流单号
     * @return JSONArray 查询结果
     */
    public static JSONArray getReturnStorageIds(String logisticNumber) {
        String[] arr = {"ID", "REMARK_HANDLE"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("LOGISTIC_NUMBER", logisticNumber);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, whereKeys, null, null, 100, 0, arr);
        return search.getJSONArray("data");
    }


    /**
     * 自动匹配
     *
     * @param orderAsc 排序
     * @param pageSize 条数
     * @return 退货入库单id
     */
    public static List<Long> searchId4Match(boolean orderAsc, int pageSize) {

        JSONObject whereKeys = new JSONObject();
        JSONArray matchStatusAry = new JSONArray();
        matchStatusAry.add(1);
        matchStatusAry.add(0);
        whereKeys.put("MATCH_STATUS", matchStatusAry);
        whereKeys.put("ISACTIVE", "Y");
        whereKeys.put("IS_OFF_MATCH", 0);
        whereKeys.put("IN_STATUS", 2);
        JSONObject filterKey = new JSONObject();

        String arrange = "0~0";
        if (orderAsc) {
            arrange = "1~";
        }
        filterKey.put("QTY_MATCH", arrange);

        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", orderAsc);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);

        JSONObject search = ElasticSearchUtil.search("oc_b_refund_in", "oc_b_refund_in", whereKeys,
                filterKey, orderJAry, pageSize, 0, new String[]{"ID"});
        log.info("searchId4Match={}", JSON.toJSONString(search.toJSONString()));
        JSONArray arrayObj = search.getJSONArray("data");
        List<Long> refundIdLists = new ArrayList<>();
        if (arrayObj == null) {
            return refundIdLists;
        }
        int searchResultSize = arrayObj.size();
        for (int i = 0; i < searchResultSize; i++) {
            JSONObject jsnObj = arrayObj.getJSONObject(i);
            if (jsnObj == null) {
                continue;
            }
            Long id = jsnObj.getLong("ID");
            if (id == null) {
                continue;
            }
            refundIdLists.add(id);
        }

        return refundIdLists;
    }

    /**
     * 自动匹配2
     *
     * @param orderAsc 排序
     * @param pageSize 条数
     * @return 退货入库单id
     */
    public static List<Long> searchRefundId4Match(boolean orderAsc, int pageSize) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("BILL_STATUS", ReturnInBilStatus.INIT.val());
        whereKeys.put("ISACTIVE", "Y");
        JSONObject filterKey = new JSONObject();
        String arrange = "0~0";
        if (orderAsc) {
            arrange = "1~";
        }
        filterKey.put("QTY_MATCH", arrange);

        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", orderAsc);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);
        JSONObject search = ElasticSearchUtil.search("oc_b_refund_in", "oc_b_refund_in", whereKeys,
                filterKey, orderJAry, pageSize, 0, new String[]{"ID"});
        JSONArray arrayObj = search.getJSONArray("data");
        List<Long> refundIdLists = new ArrayList<>();
        if (arrayObj == null) {
            return refundIdLists;
        }
        int searchResultSize = arrayObj.size();
        for (int i = 0; i < searchResultSize; i++) {
            JSONObject jsnObj = arrayObj.getJSONObject(i);
            if (jsnObj == null) {
                continue;
            }
            Long id = jsnObj.getLong("ID");
            if (id == null) {
                continue;
            }
            refundIdLists.add(id);
        }

        return refundIdLists;
    }

    /**
     * 查询退货入库单id - 近3天数据（新增方法）
     *
     * @param orderAsc 排序
     * @param pageSize 条数
     * @return 退货入库单id
     */
    public static List<Long> searchRefundId4MatchRecent3Days(boolean orderAsc, int pageSize) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("BILL_STATUS", ReturnInBilStatus.INIT.val());
        whereKeys.put("MATCH_TYPE", YesNoEnum.ZERO.getVal());
        whereKeys.put("ISACTIVE", "Y");

        // 排除全部匹配(2)和无需匹配(3)的记录，只查询未匹配(0)和部分匹配(1)的记录
        // 注意：这里可能需要使用filterKey而不是whereKeys来处理范围查询

        // 排除品异不为空的记录
        whereKeys.put("PRODUCT_DIFF", "");

        // 排除数异多不为空的记录
        whereKeys.put("NUM_MORE", "");

        // 排除数异少不为空的记录
        whereKeys.put("NUM_LESS", "");

        JSONObject filterKey = new JSONObject();
        String arrange = "0~0";
        if (orderAsc) {
            arrange = "1~";
        }
        filterKey.put("QTY_MATCH", arrange);

        // 只查询匹配状态为0(未匹配)或1(部分匹配)的记录，排除2(全部匹配)和3(无需匹配)
        filterKey.put("MATCH_STATUS", "0~1");

        // 添加创建时间限制：只取最近3天的数据（使用时间戳格式）
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -7); // 7天前
        Date threeDaysAgo = calendar.getTime();
        long threeDaysAgoTimestamp = threeDaysAgo.getTime();

        // 创建时间范围过滤条件：从7天前到现在（时间戳格式）
        filterKey.put("CREATIONDATE", threeDaysAgoTimestamp + "~");

        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", orderAsc);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);
        JSONObject search = ElasticSearchUtil.search("oc_b_refund_in", "oc_b_refund_in", whereKeys,
                filterKey, orderJAry, pageSize, 0, new String[]{"ID"});
        JSONArray arrayObj = search.getJSONArray("data");
        List<Long> refundIdLists = new ArrayList<>();
        if (arrayObj == null) {
            return refundIdLists;
        }
        int searchResultSize = arrayObj.size();
        for (int i = 0; i < searchResultSize; i++) {
            JSONObject jsnObj = arrayObj.getJSONObject(i);
            if (jsnObj == null) {
                continue;
            }
            Long id = jsnObj.getLong("ID");
            if (id == null) {
                continue;
            }
            refundIdLists.add(id);
        }

        return refundIdLists;
    }

    /**
     * 查询退货入库单id - 90天前到当前时间的数据
     *
     * @param orderAsc 排序
     * @param pageSize 条数
     * @return 退货入库单id
     */
    public static List<Long> searchRefundId4MatchRecent90Days(boolean orderAsc, int pageSize) {

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("BILL_STATUS", ReturnInBilStatus.INIT.val());
        whereKeys.put("MATCH_TYPE", YesNoEnum.ZERO.getVal());
        whereKeys.put("ISACTIVE", "Y");

        // 排除全部匹配(2)和无需匹配(3)的记录，只查询未匹配(0)和部分匹配(1)的记录
        // 注意：这里可能需要使用filterKey而不是whereKeys来处理范围查询

        // 排除品异不为空的记录
        whereKeys.put("PRODUCT_DIFF", "");

        // 排除数异多不为空的记录
        whereKeys.put("NUM_MORE", "");

        // 排除数异少不为空的记录
        whereKeys.put("NUM_LESS", "");

        JSONObject filterKey = new JSONObject();
        String arrange = "0~0";
        if (orderAsc) {
            arrange = "1~";
        }
        filterKey.put("QTY_MATCH", arrange);

        // 只查询匹配状态为0(未匹配)或1(部分匹配)的记录，排除2(全部匹配)和3(无需匹配)
        filterKey.put("MATCH_STATUS", "0~1");

        // 添加创建时间限制：查询90天前到当前时间的数据（使用时间戳格式）
        Calendar calendar = Calendar.getInstance();

        // 当前时间
        Date currentTime = calendar.getTime();
        long currentTimestamp = currentTime.getTime();

        // 90天前的时间点
        calendar.add(Calendar.DAY_OF_MONTH, -90);
        Date ninetyDaysAgo = calendar.getTime();
        long ninetyDaysAgoTimestamp = ninetyDaysAgo.getTime();

        // 创建时间范围过滤条件：从90天前到当前时间（时间戳格式）
        filterKey.put("CREATIONDATE", ninetyDaysAgoTimestamp + "~" + currentTimestamp);

        JSONArray orderJAry = new JSONArray();
        JSONObject orderJo = new JSONObject();
        orderJo.put("asc", orderAsc);
        orderJo.put("name", "MODIFIEDDATE");
        orderJAry.add(orderJo);
        JSONObject search = ElasticSearchUtil.search("oc_b_refund_in", "oc_b_refund_in", whereKeys,
                filterKey, orderJAry, pageSize, 0, new String[]{"ID"});
        JSONArray arrayObj = search.getJSONArray("data");
        List<Long> refundIdLists = new ArrayList<>();
        if (arrayObj == null) {
            return refundIdLists;
        }
        int searchResultSize = arrayObj.size();
        for (int i = 0; i < searchResultSize; i++) {
            JSONObject jsnObj = arrayObj.getJSONObject(i);
            if (jsnObj == null) {
                continue;
            }
            Long id = jsnObj.getLong("ID");
            if (id == null) {
                continue;
            }
            refundIdLists.add(id);
        }

        return refundIdLists;
    }

    /**
     * 扫描入库补偿任务 - 根据入库状态批量查询退货入库异常的退货单
     *
     * @param range 批量查询范围
     * @return List 退货入库单id
     */
    public static List<Long> findRefundInIdsByRange(Integer range) {
        JSONObject orderKey = new JSONObject();
        JSONObject whereKey = new JSONObject();
        whereKey.put("ISACTIVE", "Y");
        //5入库异常的
        whereKey.put("IN_STATUS", ReturnStatus.REFUND_EXCEPTION.toInteger());
        orderKey.put("asc", false);
        orderKey.put("name", "MODIFIEDDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);
        String[] filed = {"ID"};
        Integer startIndex = 1;
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, whereKey, null, order, range,
                (startIndex - 1) * range, filed);
        JSONArray data = search.getJSONArray("data");
        List<Long> ids = new ArrayList<>();
        for (int i = 0, l = data.size(); i < l; i++) {
            String id = data.getJSONObject(i).getString("ID");
            ids.add(Long.valueOf(id));
        }
        return ids;
    }

    /**
     * 退货入库单 生成负向库存调整单 补偿任务处理类
     * <p>
     * 根据入库状态和是否可用查询id
     *
     * @param range     批量查询范围
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return JSONObject查询结果
     */
    public static JSONObject findJSONObjectByRangeStartEndTime(Integer range, Integer startIndex, Long startTime, Long endTime) {
        JSONObject orderKey = new JSONObject();
        JSONObject whereKey = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        JSONObject childsKeys = new JSONObject();
        //时间条件，只捞取当天
        filterKeys.put("MODIFIEDDATE", startTime + "~" + endTime);
        //主表条件
        whereKey.put("ISACTIVE", "Y");
        whereKey.put("IN_STATUS", ReturnStatus.WAREHOUSING.toInteger());
        //排序从头取前  range条满足的条件
        orderKey.put("asc", false);
        orderKey.put("name", "MODIFIEDDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);
        // 明细表条件
        // 是否生成调整单为是
        // 是否生成冲无头件为否
        childsKeys.put("IS_GEN_ADJUST", 1);
        childsKeys.put("IS_GEN_MINUS_ADJUST", 0);
        String[] filed = {"ID"};
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_INDEX_NAME, whereKey, filterKeys, order,
                childsKeys, range, startIndex * range, filed);
        return search;
    }

    /**
     * 退货入库单列表
     * <p>
     * 查询id
     *
     * @param eSObject  查询条件
     * @param filter    查询条件
     * @param childKeys 明细查询字段
     * @param count     每页显示的数量
     * @param start     分页显示的页数
     * @return JSONObject查询结果
     */
    public static JSONObject findJSONObjectByeSObjectFilterChildKeys(JSONObject eSObject, JSONObject filter, JSONObject childKeys, Integer count, Integer start) {
        String[] arr = {"ID"};
        JSONArray jsonArray1 = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("asc", false);
        jsonObject.put("name", "CREATIONDATE");
        jsonArray1.add(jsonObject);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_TYPE_NAME
                , eSObject, filter, jsonArray1, childKeys, count, start, arr);
        return search;
    }


    /**
     * 根据所有状态为等待退货入库状态是原退的退货单的查询ids
     *
     * @param whereKeys
     * @return
     * @Description 获取退货入库ES
     * <AUTHOR>
     * @date 2019-09-25 2019-09-25
     */
    public static List<Long> selectRefundInIdListByES(JSONObject whereKeys, JSONObject filterKeys, JSONArray orderKey, JSONObject childsKeys, Integer pageSize, Integer startIndex) {
        List<Long> idList = Lists.newArrayList();
        String[] filed = {"ID"};
        String childIndexName = childsKeys == null ? "" : OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_INDEX_NAME;
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME, childIndexName,
                whereKeys, filterKeys, orderKey, childsKeys, pageSize, (startIndex - 1) * pageSize, filed);
        JSONArray data = search.getJSONArray("data");

        JSONArray array = new JSONArray();

        for (int i = 0; i < data.size(); i++) {
            String id = data.getJSONObject(i).getString("ID");
            if (StringUtils.isNotEmpty(id)) {
                idList.add(Long.valueOf(id));
            }
        }
        return idList;
    }


    /**
     * 根据入库状态以及是否生成错发调整单为否的查询ids
     * <p>
     * 错发调整单补偿任务
     *
     * @param pageSize  查询条件
     * @param startTime 查询条件
     * @param endTime   明细查询字段
     * @return JSONObject查询结果
     */
    public static JSONObject findJSONObjectByPageSizeStartEndTime(int pageSize, Integer startIndex, Long startTime, Long endTime) {
        JSONObject orderKey = new JSONObject();
        JSONObject whereKey = new JSONObject();
        JSONObject filterKeys = new JSONObject();
        JSONObject childsKeys = new JSONObject();
        //时间条件，只捞取当天
        filterKeys.put("MODIFIEDDATE", startTime + "~" + endTime);
        //主表条件
        whereKey.put("ISACTIVE", "Y");
        whereKey.put("IN_STATUS", ReturnStatus.WAREHOUSING.toInteger());
        //排序从头取前  range条满足的条件
        orderKey.put("asc", false);
        orderKey.put("name", "MODIFIEDDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);
        //明细表条件
        childsKeys.put("IS_GEN_WRO_ADJUST", 0);//
        String[] filed = {"ID"};

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_IN_PRODUCT_ITEM_INDEX_NAME, whereKey, filterKeys, order,
                childsKeys, pageSize, startIndex, filed);
        return search;
    }


    /**
     * 扫描入库核心
     * 根据物流单号查询ids
     *
     * @param logisticNumber 物流单号
     * @return JSONObject查询结果
     */
    public static JSONObject findJSONObjectByLogisticNumber(String logisticNumber) {
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("LOGISTIC_NUMBER", logisticNumber);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME,
                whereKeys, null, null,
                10, 0, new String[]{"ID"});
        return search;
    }

    /**
     * 根据 wmsBillNo 单号查询ids
     *
     * @param wmsBillNo wmsBillNo
     * @return JSONObject查询结果
     */
    public static Set<Long> findByWmsBillNo(String wmsBillNo) {
        JSONObject whereKeys = new JSONObject();
        String returnFieldId = "ID";
        whereKeys.put("WMS_BILL_NO", wmsBillNo);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_IN_INDEX_NAME, OcElasticSearchIndexResources.OC_B_RETURN_IN_TYPE_NAME,
                whereKeys, null, null,
                10, 0, new String[]{"ID"});
        Set<Long> idSet = new HashSet<>();
        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (int i = 0; i < returnData.size(); i++) {
                Long orderId = returnData.getJSONObject(i).getLong(returnFieldId);
                idSet.add(orderId);
            }
        }
        return idSet;
    }
}
