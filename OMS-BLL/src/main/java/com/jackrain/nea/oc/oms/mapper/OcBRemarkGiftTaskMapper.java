package com.jackrain.nea.oc.oms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRemarkGiftTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

/**
 * 卖家添加备注任务表 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OcBRemarkGiftTaskMapper extends BaseMapper<OcBRemarkGiftTask> {

    /**
     * 更新任务状态
     */
    @Update("UPDATE oc_b_remark_gift_task SET task_status = #{status}, modifieddate = now() WHERE id = #{id}")
    int updateTaskStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * 批量更新任务状态
     */
    @Update("<script> "
            + "UPDATE oc_b_remark_gift_task SET task_status = #{status}, modifieddate = now() WHERE id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateTaskStatusBatch(@Param("ids") List<Long> ids, @Param("status") Integer status);

    /**
     * 根据状态查询任务列表
     */
    @Select("SELECT * FROM oc_b_remark_gift_task WHERE task_status = #{status} AND isactive = 'Y'")
    List<OcBRemarkGiftTask> selectIdsByStatus(@Param("status") Integer status);

    /**
     * 根据状态和创建时间查询任务列表
     */
    @Select("SELECT * FROM oc_b_remark_gift_task WHERE task_status = #{status} AND creationdate < #{creationdate} AND isactive = 'Y'")
    List<OcBRemarkGiftTask> selectByStatusAndCreationdate(@Param("status") Integer status, @Param("creationdate") Date creationdate);

    @Select("SELECT * FROM oc_b_remark_gift_task WHERE tid = #{tid} AND isactive = 'Y'")
    List<OcBRemarkGiftTask> selectByTid(@Param("tid") String tid);
}