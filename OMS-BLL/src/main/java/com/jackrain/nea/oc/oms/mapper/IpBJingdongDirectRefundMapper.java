package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.relation.StepExecInfo;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectRefund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/28
 */
@Mapper
public interface IpBJingdongDirectRefundMapper extends ExtentionMapper<IpBJingdongDirectRefund> {

    /**
     * @param refundId
     * @return
     */
    @Select("select * from ip_b_jingdong_direct_refund where refund_id=#{refundId}")
    IpBJingdongDirectRefund selectByRefundId(@Param("refundId") String refundId);

    /**
     * @param stepExecInfo
     * @return
     */
    @Update("update ip_b_jingdong_direct_refund SET istrans=#{transStatusVal},trans_count=trans_count+1,"
            + "sysremark=#{transMessage}, transdate=now(),modifierid=#{userId},modifiername=#{userName},"
            + "modifierename=#{userEName},modifieddate=now() where refund_id=#{shardKey} AND id=#{id};")
    int updateTransStatusAndRemark(StepExecInfo stepExecInfo);

}
