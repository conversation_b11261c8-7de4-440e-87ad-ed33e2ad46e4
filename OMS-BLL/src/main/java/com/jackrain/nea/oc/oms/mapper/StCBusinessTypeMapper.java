package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;

/**
 * @program: r3-oc-oms
 * @description: 单据业务类型
 * @author: caomalai
 * @create: 2022-07-09 13:38
 **/
@Mapper
public interface StCBusinessTypeMapper extends ExtentionMapper<StCBusinessType> {


    @Select("SELECT * FROM st_c_business_type WHERE bill_type = #{type} and third_code = #{thirdCode}")
    List<StCBusinessType> selectStCBusinessTypeByThirdCode(@Param("type") Integer type, @Param("thirdCode") String thirdCode);


    @Select("SELECT * FROM st_c_business_type WHERE ecode = #{code}")
    List<StCBusinessType> selectStCBusinessTypeByCode(@Param("code") String code);

    /**
     * 查询TOB主业务类型ID对应子类ID集合
     * @return
     */
    @Select("SELECT id FROM st_c_business_type WHERE parent_code = 'TOB' and isactive = 'Y'")
    List<Long> getBusinessTypeIdByToB();

    @UpdateProvider(type = StCBusinessTypeMapper.SqlBuilder.class, method = "updateWithNull")
    int updateWithNull(JSONObject aftervalue);

    class SqlBuilder {
        public String updateWithNull(JSONObject aftervalue) {
            StringBuilder sql = new StringBuilder();
            sql.append("update st_c_business_type set ");
            int i=0;
            for(String key:aftervalue.keySet()){
                i++;
                if("ID".equals(key)){
                    continue;
                }
                Object value = aftervalue.get(key);
                sql.append(key).append("=");
                if(value instanceof String){
                    if(StringUtils.isNotBlank((String)value)){
                        if("是".equals(value)){
                            sql.append(1);
                        }else if("否".equals(value)){
                            sql.append(0);
                        }else{
                            sql.append("'").append(value).append("'");
                        }
                    }else{
                        sql.append("null");
                    }
                }else{
                    sql.append(value);
                }

                if(i<aftervalue.size()){
                    sql.append(",");
                }
            }
            sql.append(" where ID = ");
            sql.append(aftervalue.getLong("ID"));
            return sql.toString();
        }
    }
}
