package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCSplitBeforeSourceStrategy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * @program: r3-oc-oms
 * @description: 寻源前拆单策略
 * @author: caomalai
 * @create: 2022-07-29 14:02
 **/
@Mapper
public interface StCSplitBeforeSourceStrategyMapper extends ExtentionMapper<StCSplitBeforeSourceStrategy> {
    /**
     * 获取可用且创建时间最近的策略id
     * @return id
     */
    @Select("select id from st_c_split_before_source_strategy where ISACTIVE='Y' ORDER BY creationdate DESC LIMIT 1")
    Long getRecentlySplitBeforeSourceStrategyId();
}
