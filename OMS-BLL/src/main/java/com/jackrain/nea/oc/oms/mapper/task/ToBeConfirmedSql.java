package com.jackrain.nea.oc.oms.mapper.task;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @author: 易邵峰
 * @since: 2020-02-17
 * create at : 2020-02-17 22:48
 */
@Slf4j
public class ToBeConfirmedSql {

    public String selectByNodeSql(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 5000;
        limitStr.append(limit);
        String taskTableName = (String) para.get("taskTableName");
        sql.append("select order_id from ")
                .append(taskTableName)
                .append(" where status=0")
                .append(limitStr);
        return sql.toString();
    }
}
