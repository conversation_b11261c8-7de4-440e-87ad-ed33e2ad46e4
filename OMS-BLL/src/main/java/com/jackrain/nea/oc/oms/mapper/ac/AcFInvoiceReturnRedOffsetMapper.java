package com.jackrain.nea.oc.oms.mapper.ac;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceReturnRedOffset;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName AcFInvoiceReturnRedOffsetMapper
 * @Description
 * @Date 2022/8/31 下午5:06
 * @Created by wuhang
 */
@Mapper
public interface AcFInvoiceReturnRedOffsetMapper extends ExtentionMapper<AcFInvoiceReturnRedOffset> {

    @Select("<script> "
            + "SELECT * FROM ac_f_invoice_return_red_offset WHERE id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<AcFInvoiceReturnRedOffset> selectByIds(@Param("ids") List<Long> idList);

    @Select("select count(1) from ac_f_invoice_return_red_offset where bill_no = #{billNo}")
    long countByBillNo(@Param("billNo") String billNo);
}
