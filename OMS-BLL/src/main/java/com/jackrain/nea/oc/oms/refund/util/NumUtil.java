package com.jackrain.nea.oc.oms.refund.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @Desc : 数字处理
 * <AUTHOR> xiWen
 * @Date : 2020/10/28
 */
public class NumUtil {

    /**
     * 初始化
     *
     * @param num BigDecimal
     * @return BigDecimal
     */
    public static BigDecimal init(BigDecimal num) {
        return num == null ? BigDecimal.ZERO : num;
    }

    public static Integer init(Integer num) {
        return num == null ? 0 : num;
    }

    public static Long init(Long num) {
        return num == null ? 0L : num;
    }

    /**
     * 等于0
     *
     * @param num           BigDecimal
     * @param isNullAllowed 是否允许空值参与比较
     * @return eq =true
     */
    public static boolean eqZero(BigDecimal num, boolean... isNullAllowed) {
        if (num == null) {
            if (isNullAllowed.length > 0) {
                return isNullAllowed[0];
            }
            return false;
        }
        return eq(BigDecimal.ZERO, num);
    }

    /**
     * 等于0
     * 非null
     *
     * @param num BigDecimal
     * @return 等于0
     */
    public static boolean eqZero(BigDecimal num) {
        if (num == null) {
            return false;
        }
        return num.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 不等于0
     *
     * @param num BigDecimal
     * @return 不等于0
     */
    public static boolean neZero(BigDecimal num) {
        if (num == null) {
            return false;
        }
        return num.compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 大于0
     *
     * @param num BigDecimal
     * @return 大于0?
     */
    public static boolean gtZero(BigDecimal num) {
        if (num == null) {
            return false;
        }
        return prevGtNext(num, BigDecimal.ZERO);
    }

    /**
     * 非null,相等比较
     *
     * @param prev BigDecimal
     * @param next BigDecimal
     * @return null = false,  eq=true
     */
    public static boolean notNullAndEq(BigDecimal prev, BigDecimal next) {
        if (prev == null || next == null) {
            return false;
        }
        return eq(prev, next);
    }

    /**
     * 非null, 不等判断
     *
     * @param prev BigDecimal
     * @param next BigDecimal
     * @return null =false, eq = false
     */
    public static boolean nullAndNe(BigDecimal prev, BigDecimal next) {
        if (prev == null || next == null) {
            return true;
        }
        return !eq(prev, next);
    }

    /**
     * 相等比较-1
     * 不允许null值
     *
     * @param prev BigDecimal
     * @param next BigDecimal
     * @return eq=true
     */
    public static boolean eq(BigDecimal prev, BigDecimal next) {
        //    return (prev == null && next == null) || ((prev != null && next != null) && (prev.compareTo(next) == 0));
        return prev.compareTo(next) == 0;
    }

    /**
     * 不相等
     *
     * @param prev BigDecimal
     * @param next BigDecimal
     * @return true: 不相等
     */
    public static boolean ne(BigDecimal prev, BigDecimal next) {
        return !eq(prev, next);
    }

    /**
     * 相等比较-2
     *
     * @param prev          BigDecimal
     * @param next          BigDecimal
     * @param isNullAllowed 是否允许空值参与比较:  空值默认为0
     * @return BigDecimal
     */
    public static boolean eq(BigDecimal prev, BigDecimal next, boolean... isNullAllowed) {
        boolean permit = isNullAllowed.length > 0 && isNullAllowed[0];
        if (!permit && (prev == null || next == null)) {
            return false;
        }
        prev = init(prev);
        next = init(next);
        return prev.compareTo(next) == 0;
    }

    /**
     * convert num
     *
     * @param val
     * @return
     */
    public static int toInt(Object val) {
        if (val == null) {
            return 0;
        }
        if (val instanceof BigDecimal) {
            return ((BigDecimal) val).intValue();
        }
        if (val instanceof Long) {
            return ((Long) val).intValue();
        }
        if (val instanceof String) {
            if (StringUtils.isBlank((CharSequence) val)) {
                return 0;
            }
            return Integer.valueOf((String) val);
        }
        // exception
        return (int) val;
    }

    /**
     * Long 转换为 BigDecimal
     * @param num
     * @return
     */
    public static BigDecimal toBigDecimal(Long num) {
        return num == null ? BigDecimal.ZERO : BigDecimal.valueOf(num);
    }

    /**
     * 逻辑运算
     *
     * @param prev
     * @param next
     * @return
     */
    public static boolean prevGtNext(BigDecimal prev, BigDecimal next) {
        return prev.compareTo(next) > 0;
    }

    public static boolean prevLtNext(BigDecimal prev, BigDecimal next) {
        return prev.compareTo(next) < 0;
    }

    public static boolean prevGtOrEqNext(BigDecimal prev, BigDecimal next) {
        return !prevLtNext(prev, next);
    }

    public static boolean prevLtOrEqNext(BigDecimal prev, BigDecimal next) {
        return !prevGtNext(prev, next);
    }


    private NumUtil() {
    }

}
