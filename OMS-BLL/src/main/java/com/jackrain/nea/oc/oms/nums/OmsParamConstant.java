package com.jackrain.nea.oc.oms.nums;

public class OmsParamConstant {

    /**
     * 字符串固定值
     */

    public static final String MINUS_ONE = "-1";

    public static final String  ZERO = "0";

    public static final String  HALF_OF_ONE = "0.5";

    public static final String ONE  = "1";

    public static final String TWO  = "2";

    public static final String THREE  = "3";

    public static final String FOUR  = "4";

    public static final String FIVE  = "5";

    public static final String SIX  = "6";

    public static final String SEVEN  = "7";

    public static final String EIGHT  = "8";

    public static final String MINUS_NINETY_NINE  = "-99";

    /**
     * int固定值
     */

    public static final int  INT_ZERO = 0;

    public static final int INT_ONE  = 1;

    public static final int INT_TWO  = 2;

    public static final int INT_THREE  = 3;

    public static final int INT_FOUR  = 4;

    public static final int INT_FIVE  = 5;

    //1-是；0-否
    public static final Integer YES = 1;
    public static final Integer NO = 0;
}
