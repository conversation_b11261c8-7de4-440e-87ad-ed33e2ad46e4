package com.jackrain.nea.oc.oms.nums;

import cn.hutool.core.util.ObjectUtil;
import lombok.Getter;

/**
 * @ClassName NaiKaOrderTypeEnum
 * @Description 小程序传过来的类型与奶卡的业务类型的映射
 * <AUTHOR>
 * @Date 2023/7/12 17:45
 * @Version 1.0
 */
public enum NaiKaOrderTypeEnum {

    /**
     * 电商销售
     */

    PRODUCT_ORDER(3, "RYCK14"),
    /**
     * 奶卡周期购
     */

    PVLE_ORDER(4, "RYCK10"),
    /**
     * 电子奶卡销售
     */
    VIRTUAL_CARD_NEW(7, "RYCK21"),

    /**
     * 线上奶卡销售
     */
    CARD_ORDER_NEW(8, "RYCK01"),

    /**
     * 线上免费奶卡
     */
    FREE_CARD_ORDER(9, "RYCK02"),

    /**
     * 免费奶卡周期购
     */
    FREE_PVLE_ORDER(10, "RYCK11");

    @Getter
    /**
     * 小程序传过来的类型
     */
    private Integer naikaOrderType;

    @Getter
    /**
     * 中台的业务类型
     */
    private String businessTypeCode;


    NaiKaOrderTypeEnum(Integer naikaOrderType, String businessTypeCode) {
        this.naikaOrderType = naikaOrderType;
        this.businessTypeCode = businessTypeCode;
    }

    public static NaiKaOrderTypeEnum getByNaiKaOrderType(Integer naikaOrderType) {
        if (ObjectUtil.isNull(naikaOrderType)) {
            return null;
        }
        for (NaiKaOrderTypeEnum naiKaOrderTypeEnum : values()) {
            if (ObjectUtil.equal(naikaOrderType, naiKaOrderTypeEnum.getNaikaOrderType())) {
                return naiKaOrderTypeEnum;
            }
        }
        return null;
    }
}
