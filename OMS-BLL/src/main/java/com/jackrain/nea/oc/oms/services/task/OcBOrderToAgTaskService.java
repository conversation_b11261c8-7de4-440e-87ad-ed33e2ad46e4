package com.jackrain.nea.oc.oms.services.task;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ip.service.IpOrderCancelToAgService;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderToAgTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OrderHoldReasonEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderToAgStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderToAgTask;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.IpTaobaoRefundService;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderRecountAmountService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 淘宝退单转单传AG失败Service
 *
 * <AUTHOR>
 * @date 2020/11/3 4:45 下午
 */
@Slf4j
@Service
public class OcBOrderToAgTaskService {

    @Autowired
    private OcBOrderToAgTaskMapper orderToAgTaskMapper;
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private IpBTaobaoRefundMapper taoBaoRefundMapper;
    @Autowired
    protected OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private IpOrderCancelToAgService ipOrderCancelToAgService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OmsOrderRecountAmountService omsOrderRecountAmountService;
    @Autowired
    protected OmsOrderLogService omsOrderLogService;
    @Autowired
    protected IpTaobaoRefundService ipTaobaoRefundService;

    /**
     * 查询淘宝退单转单传AG失败的订单
     *
     * @param nodeName     分库节点
     * @param tableName    表名
     * @param size         分页条数
     * @param status       处理状态
     * @param retriesTimes 重试次数
     * @return List<OcBOrderToAgTask>
     */
    public List<OcBOrderToAgTask> getTaskOrderToAg(String nodeName, String tableName, int size, int status,
                                                   int retriesTimes) {
        return this.orderToAgTaskMapper.selectDynamicTaskOrderToAg(nodeName, tableName, size, status, retriesTimes);
    }

    /**
     * 请求AG取消发货
     *
     * @param orderToAg  OcBOrderToAgTask
     * @param limitTimes 限制重试次数
     * @return Boolean 更新成功标识
     */
    public Boolean sendOrderToAg(OcBOrderToAgTask orderToAg, int limitTimes) {

        IpBTaobaoRefund taoBaoRefund = this.taoBaoRefundMapper.selectTaobaoRefundByRefundId(orderToAg.getRefundId());

        OcBOrder order = this.orderMapper.selectById(orderToAg.getOcBOrderId());

        String idsJson = orderToAg.getOcBOrderIds();
        if (StringUtils.isEmpty(idsJson)) {
            return false;
        }

        List<Long> ids = JSONObject.parseArray(idsJson, Long.class);
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }

        List<OcBOrder> ocBOrders = this.orderMapper.selectByIdsList(ids);
        if (CollectionUtils.isEmpty(ocBOrders)) {
            return false;
        }

        String remark = "";
        if (this.checkShopHasAg(order.getCpCShopId())) {
            // 重试AG
            boolean bool = this.ipOrderCancelToAgService.orderCancelToAg(order, taoBaoRefund,
                    SystemUserResource.getRootUser());
            if (bool) {
                // 重试AG成功
                remark = String.format("退款单号为:%s,订单AG重试取消发货成功", taoBaoRefund.getRefundId());

                for (OcBOrder ocBOrder : ocBOrders) {
                    // 释放hold单、重算金额
                    this.processingOrder(ocBOrder);
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                            OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), remark,
                            null, null, SystemUserResource.getRootUser());
                }
                this.orderToAgTaskMapper.updateOrderToAgRetriesTimes(orderToAg.getId(),
                        OrderToAgStatus.PROCESSED.getVal(), remark, true, orderToAg.getRefundId());
            } else {
                Integer retriesTimes = orderToAg.getRetriesTimes();
                retriesTimes++;
                if (retriesTimes == limitTimes) {
                    remark = String.format("重试AG失败%d次,已达上限次数,退款单号为:%s", limitTimes, taoBaoRefund.getRefundId());
                } else {
                    remark = String.format("重试AG失败%d次,退款单号为:%s", retriesTimes, taoBaoRefund.getRefundId());
                }

                this.orderToAgTaskMapper.updateOrderToAgRetriesTimes(orderToAg.getId(),
                        OrderToAgStatus.UNTREATED.getVal(), remark, true, orderToAg.getRefundId());
                // 添加日志记录
                this.addLog(ocBOrders, remark);
            }
        } else {
            remark = String.format("订单AG重试取消发货,店铺已关闭AG,退款单号为:%s", taoBaoRefund.getRefundId());

            this.orderToAgTaskMapper.updateOrderToAgRetriesTimes(orderToAg.getId(), OrderToAgStatus.PROCESSED.getVal(),
                    remark, true, orderToAg.getRefundId());

            this.addLog(ocBOrders, remark);
        }
        // 更新中间表记录
        this.ipTaobaoRefundService.updateReturnOrder(TransferOrderStatus.TRANSFERRED.toInteger(), remark, taoBaoRefund);
        return true;
    }

    /**
     * 添加日志记录
     *
     * @param orders 订单
     * @param remark 备注
     */
    private void addLog(List<OcBOrder> orders, String remark) {
        for (OcBOrder item : orders) {
            this.omsOrderLogService.addUserOrderLog(item.getId(), item.getBillNo(),
                    OrderLogTypeEnum.AG_SEND_CANCLE.getKey(), remark,
                    null, null, SystemUserResource.getRootUser());
        }
    }

    /**
     * 查询店铺是否开启AG
     *
     * @param shopId 店铺id
     * @return boolean true：开启AG false：未开启AG
     */
    private boolean checkShopHasAg(Long shopId) {
        StCShopStrategyDO shopStrategy = this.omsStCShopStrategyService.selectOcStCShopStrategy(shopId);
        if (Objects.isNull(shopStrategy)) {
            return false;
        }
        return StringUtils.isNotEmpty(shopStrategy.getIsAg()) && "Y".equals(shopStrategy.getIsAg());
    }

    /**
     * 释放hold单、重算金额
     *
     * @param ocBOrder 零售发货单
     */
    private void processingOrder(OcBOrder ocBOrder) {
        //判断订单是否全部退款完成
        List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItemListAndReturn(ocBOrder.getId());
        if (CollectionUtils.isEmpty(orderItems)) {
            return;
        }
        //判断订单时候还有申请退款的明细
        orderItems = orderItems.stream().filter(p -> (
                p.getRefundStatus().equals(OcOrderRefundStatusEnum.WAIT_SELLER_AGREE.getVal())
        )).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderItems)) {
            OcBOrder order = new OcBOrder();
            order.setIsInreturning(0);
            order.setId(ocBOrder.getId());
            omsOrderService.updateOrderInfo(order);
            //是否已经拦截
            order.setIsInterecept(0);
            //订单hold 或释放hold单调用HOLD单接口
            ocBOrderHoldService.holdOrUnHoldOrder(order, OrderHoldReasonEnum.REFUND_HOLD);
        }

        List<OcBOrderItem> orderItemList = orderItemMapper.selectUnSuccessRefundAndNoSplit(ocBOrder.getId());

        List<OcBOrderItem> items = orderItemList.stream().filter(
                p -> p.getProType() != SkuType.COMBINE_PRODUCT && p.getProType() != SkuType.GIFT_PRODUCT
        ).collect(Collectors.toList());

        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
        ocBOrderRelation.setOrderInfo(ocBOrder);
        List<OcBOrderItem> list = new ArrayList<>();
        // 重新计算金额
        omsOrderRecountAmountService.doRecountAmount(ocBOrderRelation, list, items);
    }

}
