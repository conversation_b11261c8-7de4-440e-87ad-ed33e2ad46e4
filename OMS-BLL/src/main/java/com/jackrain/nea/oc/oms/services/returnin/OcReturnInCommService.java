package com.jackrain.nea.oc.oms.services.returnin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillSaveRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.model.enums.IsGenInEnum;
import com.jackrain.nea.oc.oms.model.enums.IsMatchEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWithoutOrigEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderConfirmStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStockInMatchParam;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.request.GenerateStorageBillRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInActualItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderActual;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordAddTaskService;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/9
 */
@Slf4j
@Component
public class OcReturnInCommService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private SgRpcService sgRpcService;

    @Autowired
    private OcReturnInSupport returnInService;

    @Autowired
    private OcRefundInGenerateStorageBillService ocRefundInGenerateStorageBillService;

    @Autowired
    private OcBSapSalesDataRecordAddTaskService sapSalesDataRecordAddTaskService;

    public static final String B2B_PLAT_TAG = "38";
    public static final String B2B_PLAT_TAG2 = "39";


    /**
     * qty
     *
     * @param map
     * @param ecode
     * @param qty
     */
    public void statisticInQty(Map<String, BigDecimal> map, String ecode, BigDecimal qty) {
        Set<String> keys = map.keySet();
        if (keys.contains(ecode)) {
            BigDecimal inQty = map.get(ecode);
            BigDecimal tmpQty = inQty.add(qty);
            map.put(ecode, tmpQty);
        } else {
            map.put(ecode, qty);
        }
    }

    /**
     * 明细id置换
     *
     * @param id
     * @param newId
     * @param localMapping
     */
    public void swapItemIdUseMapping(Long id, Long newId, Map<Long, Map<String, BigDecimal>> localMapping) {
        Map<String, BigDecimal> map = localMapping.get(id);
        localMapping.put(newId, map);
        localMapping.remove(id);
    }

    public ValueHolderV14<String> generateStockInBills(OcBReturnOrder newReturn, List<OcBReturnOrderRefund> newReturnRefunds,
                                                       GenerateStorageBillRequest.OperationType operationType, User usr) {
        GenerateStorageBillRequest request = new GenerateStorageBillRequest();
        request.setReturnOrder(newReturn);
        request.setReturnOrderRefundList(newReturnRefunds);
        request.setUser(usr);
        request.setOperationType(operationType);
        String reserveVarchar05 = newReturn.getReserveVarchar05();
        if (StringUtils.equalsIgnoreCase(B2B_PLAT_TAG, reserveVarchar05) || StringUtils.equalsIgnoreCase(B2B_PLAT_TAG2, reserveVarchar05)) {
            request.setBillType("B2BRK");
        } else {
            request.setBillType("THRK");
        }
        return ocRefundInGenerateStorageBillService.generateStorageBill(request);
    }

    public boolean generateInNoticeBilAndPassWms(OcBReturnOrder newReturn, List<OcBReturnOrderRefund> newReturnRefunds, User user) {
        String sgBilType = "THRK";
        String reserveVarchar05 = newReturn.getReserveVarchar05();
        if (StringUtils.equalsIgnoreCase(B2B_PLAT_TAG, reserveVarchar05) || StringUtils.equalsIgnoreCase(B2B_PLAT_TAG2, reserveVarchar05)) {
            sgBilType = "B2BRK";
        }
        SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest
                = ocRefundInGenerateStorageBillService.encapsulationStoInNotices(newReturn, newReturnRefunds, true, sgBilType);
        logStep("generateInNoticeBilAndPassWms encapsulate param end");
        ValueHolderV14<SgBStoInNoticesBillSaveResult> saveResultV14 = sgRpcService.createSgBStoInNotice(stoInNoticesBillSaveRequest, user);
        logStep("generateInNoticeBilAndPassWms invoke end");
        AssertUtil.notNull(saveResultV14, "生成入库通知单,返回为null");
        AssertUtil.isTrue(saveResultV14.isOK(), saveResultV14.getMessage());
        SgBStoInNoticesBillSaveResult data = saveResultV14.getData();
        AssertUtil.notNull(data, "生成入库通知单,返回为data为null");
        newReturn.setStoInNoticesNo(data.getBillNo());
        newReturn.setStoInNoticesId(data.getId());
        return true;
    }

    /**
     * 更新退单,入库通知单信息
     *
     * @param sourceReturn source return order
     * @return update result
     */
    public boolean updateReturnOrderNoticeInfo(OcBReturnOrder sourceReturn) {
        OcBReturnOrder target = new OcBReturnOrder();
        target.setId(sourceReturn.getId());
        target.setStoInNoticesId(sourceReturn.getStoInNoticesId());
        target.setStoInNoticesNo(sourceReturn.getStoInNoticesNo());
        try {
            int updateResult = returnInService.updateReturnBil(target);
            logStep("update return notice no,id result " + updateResult);
            if (updateResult > 0) {
                return true;
            } else {
                logStep("update return notice no,id failed");
            }
        } catch (Exception e) {
            log.error(LogUtil.format("更新退单异常. ReturnOrder: {}, Exp:{}"), JSON.toJSONString(target), Throwables.getStackTraceAsString(e));
        }
        return false;
    }

    /**
     * 生成入库结果单
     * OcBRefundIn newRefund, List<OcBRefundInProductItem> newInItems,
     * OcBReturnOrder newReturn, List<OcBReturnOrderRefund> newRnItems,
     * List<OcBReturnOrderActual> actItems, List<OcBRefundInLog> inLogs,
     * OcBReturnOrderLog rnLog, OcBReturnOrder genReturn,
     * List<OcBReturnOrderRefund> genRnItems, User user
     */
    @Transactional
    public void atomicReturnFirstGenInResultAndUpdate(OmsStockInMatchParam param) {
        logStep("atomic update start");
        returnInService.persistReturnNormalMatchResult(param.getModRefundIn(),
                param.getModRefundItems(), param.getModReturn(), param.getModReturnItems(),
                param.getInsActualItems(), param.getInsRefundLogs(), param.getInsReturnLogs().get(0));
        logStep("atomic gen in result start");
        ValueHolderV14<String> noticeInResult = generateStockInBills(param.getMatchedReturn(),
                param.getMatchedReturnItems(), GenerateStorageBillRequest.OperationType.INSERT_RESULT, param.getUser());
        AssertUtil.isTrue(noticeInResult.isOK(), noticeInResult.getMessage());
    }

    /**
     * 更新数据,生成入库通知单,入库结果单
     * newRefundIn       退货入库结果单.更新
     * newInItems        退货入库结果单商品明细.更新
     * updateReturn      原退单.更新
     * updateReturnItems 原退单明细.更新
     * actualItems       退单实际入库明细.新增
     * newReturn         退单.新增
     * newReturnItems    退单.新增
     * matchInLogs       入库结果单日志.新增
     * returnLogs        退单.日志
     * user              入库人
     */
    @Transactional
    public void atomicReturnMultiGenInNoticeResultAndUpdate(OmsStockInMatchParam inParam) {
        logStep("atomic update start");
        returnInService.persistReturnMultiMatchResult(inParam);

        logStep("atomic notice,result start");
        ValueHolderV14<String> noticeInResult = generateStockInBills(inParam.getMatchedReturn(),
                inParam.getMatchedReturnItems(),
                GenerateStorageBillRequest.OperationType.INSERT_NOTICES_AND_RESULT, inParam.getUser());
        AssertUtil.isTrue(noticeInResult.isOK(), noticeInResult.getMessage());
        inParam.getMatchedReturn().setStoInNoticesNo(noticeInResult.getData());
    }

    @Transactional
    public void orderGenInNoticeAndInResult(OcBReturnOrder newReturn, List<OcBReturnOrderRefund> newReturnRefunds,
                                            List<OcBReturnOrderActual> actualItems,
                                            OcBOrder newOrder, List<OcBOrderItem> matchOrderItems,
                                            OcBRefundIn newRefundIn, List<OcBRefundInProductItem> matchInItems,
                                            OcBReturnOrderLog returnLog, OcBOrderLog orderLog, User user) {
        returnInService.persistOrderMatchResult(newReturn, newReturnRefunds, actualItems,
                newOrder, matchOrderItems, newRefundIn, matchInItems, returnLog, orderLog);
        ValueHolderV14<String> noticeInResult = generateStockInBills(newReturn, newReturnRefunds,
                GenerateStorageBillRequest.OperationType.INSERT_NOTICES_AND_RESULT, user);
        AssertUtil.assertException(!noticeInResult.isOK(), noticeInResult.getMessage());
        newReturn.setStoInNoticesNo(noticeInResult.getData());
    }

    /**
     * order  generate notice . pass wms
     *
     * @param newReturn        gen , return
     * @param newReturnRefunds gen, param , return items
     * @param actualItems      return actual items
     * @param newOrder         origin order
     * @param matchOrderItems  origin order items
     * @param newRefundIn      refund in
     * @param matchInItems     refund in items
     * @param orderLog         order log
     * @param refundInLog      refund in log
     * @param returnLogs       return log
     * @param user             user
     */
    @Transactional
    public void orderGenInNoticePassWms(OcBReturnOrder newReturn, List<OcBReturnOrderRefund> newReturnRefunds,
                                        List<OcBReturnOrderActual> actualItems,
                                        OcBOrder newOrder, List<OcBOrderItem> matchOrderItems,
                                        OcBRefundIn newRefundIn, List<OcBRefundInProductItem> matchInItems,
                                        OcBOrderLog orderLog, OcBRefundInLog refundInLog,
                                        List<OcBReturnOrderLog> returnLogs, User user) {
        Date inTime = newReturn.getInTime();
        newReturn.setInTime(null);
        returnInService.persistOrderMatch4InNotice(newReturn, newReturnRefunds, actualItems,
                newOrder, matchOrderItems, newRefundIn, matchInItems, orderLog, refundInLog, returnLogs);
        logStep("persist db success");
        newReturn.setInTime(inTime);
        boolean isSuccess = generateInNoticeBilAndPassWms(newReturn, newReturnRefunds, user);
        AssertUtil.isTrue(isSuccess, "无名件匹配订单,生成入库通知单失败");
    }

    /**
     * nameLess matched return generate notice . pass wms
     *
     * @param genReturn
     * @param genReturnItems
     * @param newRefundIn
     * @param newInItems
     * @param updateReturn
     * @param matchInLogs
     * @param returnLogs
     * @param user
     */
    @Transactional
    public void nameLessGenInNoticeAndUpdateBil(OcBReturnOrder genReturn, List<OcBReturnOrderRefund> genReturnItems,
                                                OcBRefundIn newRefundIn, List<OcBRefundInProductItem> newInItems,
                                                OcBReturnOrder updateReturn,
                                                List<OcBRefundInLog> matchInLogs,
                                                List<OcBReturnOrderLog> returnLogs, User user) {
        returnInService.persistNameLessGenInNotice(newRefundIn, newInItems, updateReturn, matchInLogs, returnLogs);
        boolean genSuccess = generateInNoticeBilAndPassWms(genReturn, genReturnItems, user);
        AssertUtil.isTrue(genSuccess, "无名件匹配退单,生成入库通知单失败");
    }

    /**
     * generate adjust bil
     *
     * @param inRelation       refund in relation
     * @param skuMessage       sku message
     * @param list             refund in items id list
     * @param inType           adjust type
     * @param isNameLessInType in type
     */
    @Transactional
    public void atomicGenerateAndUpdateJust(RefundInRelation inRelation, String skuMessage, List<Long> list,
                                            GenerateStorageBillRequest.OperationType inType, boolean isNameLessInType) {
        User usr = ThreadLocalUtil.users.get();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        List<OcBRefundInProductItem> unAdjustments = inRelation.getUnAdjustments();
        Long id = refundIn.getId();
        String message = "生成调整单[入库]成功, 调整条码:" + skuMessage;
        OcBRefundInLog inLog = buildRefundInLog("生成调整单", message, id, usr);
        if (isNameLessInType) {
            if (inRelation.isFluxWms()) {
                //富勒WMS的无名件是直接新增并审核库存调整单，所以需要将无名件如果结果单虚拟入库状态更新为虚拟入库已入库
                returnInService.updateAdjustAndVirtualStatusForFlux(id, list, inLog);
            } else {
                returnInService.updateAdjustAndVirtualStatus(id, list, inLog);
            }
        } else {
            returnInService.updateAdjustBil(id, list, inLog);
        }
        ValueHolderV14<String> noticeInResult = generateAdjustBil(inRelation.getRefundIn(), unAdjustments,
                inType, Integer.valueOf(String.valueOf(SgConstantsIF.SERVICE_NODE_ADJUST_PROP_NO_SOURCE_IN)), usr);
        AssertUtil.isTrue(noticeInResult.isOK(), noticeInResult.getMessage());
    }

    /**
     * 1
     * generate minus adjust bil. normal
     *
     * @param inRelation         refund in relation
     * @param unMinusAdjustments refund in items
     * @param skuMessage         adjust sku
     * @param list               refund in items id
     */
    @Transactional
    public void atomicGenerateAndUpdateMinusJust(RefundInRelation inRelation,
                                                 List<OcBRefundInProductItem> unMinusAdjustments,
                                                 String skuMessage, List<Long> list) {
        User usr = ThreadLocalUtil.users.get();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        Long id = refundIn.getId();
        updateMinusAdjustBil(id, skuMessage, list, usr);
        ValueHolderV14<String> noticeInResult = generateAdjustBil(inRelation.getRefundIn(),
                unMinusAdjustments, GenerateStorageBillRequest.OperationType.INSERT_ADJUST,
                Integer.valueOf(String.valueOf(SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE)), usr);
        AssertUtil.isTrue(noticeInResult.isOK(), noticeInResult.getMessage());
    }

    /**
     * 2
     * generate minus adjust bil with special sens 4 nameless
     *
     * @param inRelation refund in relation
     * @param skuMessage adjust sku info
     * @param list       refund in item ids
     */
    @Transactional(rollbackFor = Exception.class)
    public void atomicGenAndUpdateNameLessMinusJust(RefundInRelation inRelation, OcBReturnOrder returnOrder,
                                                    String skuMessage, List<Long> list, boolean genCombine) {
        User usr = ThreadLocalUtil.users.get();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        Long id = refundIn.getId();
        //富勒类型的无头件且没有生成过库存调整单不生成反向的库存调整单
        if (!(inRelation.isFluxWms() && !inRelation.isHasAdjustedEle())) {
            updateMinusAdjustBil(id, skuMessage, list, usr);
        }
        JSONObject jsn = new JSONObject();
        jsn.put("ID", id);
        jsn.put("SOURCE_BILL_ID", returnOrder.getId());
        jsn.put("SOURCE_BILL_NO", returnOrder.getBillNo());
        jsn.put("CP_C_PHY_WAREHOUSE_ID", refundIn.getCpCPhyWarehouseId());
        jsn.put("SENDER_ECODE", refundIn.getUserNick());
        jsn.put("SENDER_NAME", refundIn.getUserNick());
        jsn.put("IS_SAVE_IN_NOTICE", genCombine);
        jsn.put("IN_TIME", returnOrder.getInTime());
        boolean genResult;
        //富勒类型的无头件且没有生成过库存调整单不生成反向的库存调整单
        if (inRelation.isFluxWms() && !inRelation.isHasAdjustedEle()) {
            List<OcBRefundInProductItem> refundInProductItemList = inRelation.getItems();
            Map<String, SgBStoAdjustItemSaveRequest> groupMap = new HashMap<>();
            for (OcBRefundInProductItem item : refundInProductItemList) {
                String skuCode = item.getPsCSkuEcode();
                String proDate = item.getProductDate();
                String proMark = item.getProductMark();
                String groupKey = skuCode + proDate + proMark;
                BigDecimal qty = item.getQty();
                SgBStoAdjustItemSaveRequest itemRequest = groupMap.get(groupKey);
                if (itemRequest == null) {
                    itemRequest = new SgBStoAdjustItemSaveRequest();
                    itemRequest.setQty(qty);
                    itemRequest.setSourceBillItemId(item.getId());
                    itemRequest.setPsCSkuId(item.getPsCSkuId());
                    itemRequest.setPsCSkuEcode(skuCode);
                    itemRequest.setPsCProId(item.getPsCProId());
                    itemRequest.setPsCProEcode(item.getPsCProEcode());
                    itemRequest.setPsCProEname(item.getPsCSkuEcode());
                    itemRequest.setPsCSpec1Id(item.getPsCClrId());
                    itemRequest.setPsCSpec2Id(item.getPsCSizeId());
                    itemRequest.setProduceDate(proDate);
                    itemRequest.setGbcode(item.getGbcode());
                    itemRequest.setStorageType(proMark);
                    itemRequest.setId(-1L);
                    groupMap.put(groupKey, itemRequest);
                    continue;
                }
                BigDecimal add = itemRequest.getQty().add(qty);
                itemRequest.setQty(add);
            }
            List<SgBStoAdjustItemSaveRequest> itemList = groupMap.values().stream().collect(Collectors.toList());
            jsn.put("ITEM", JSON.toJSONString(itemList));
            genResult = sgRpcService.generateMinusAdjustForFl(jsn, usr);
        } else {
            genResult = sgRpcService.generateMinusAdjust(jsn, usr);
        }
        AssertUtil.isTrue(genResult, "special combine service generate fail");
        //生成销售记录表数据
        sapSalesDataRecordAddTaskService.addTask(1, returnOrder.getId(), usr);
    }

    /**
     * update minus adjust , refund in items, in log
     *
     * @param id         refund id
     * @param skuMessage sku message
     * @param list       refund items id
     * @param usr        user
     */
    private void updateMinusAdjustBil(Long id, String skuMessage, List<Long> list, User usr) {
        String message = "生成调整单[出库]成功, 调整条码:" + skuMessage;
        OcBRefundInLog inLog = buildRefundInLog("生成调整单", message, id, usr);
        returnInService.updateMinusAdjust(id, list, inLog);
    }

    /**
     * generate adjust bil
     *
     * @param refundIn      refund in bil
     * @param productItems  refund in items
     * @param operationType adjust type
     * @param tag           tag
     * @param usr           user
     * @return gen result
     */
    private ValueHolderV14<String> generateAdjustBil(OcBRefundIn refundIn, List<OcBRefundInProductItem> productItems,
                                                     GenerateStorageBillRequest.OperationType operationType, Integer tag, User usr) {
        GenerateStorageBillRequest request = new GenerateStorageBillRequest();
        request.setRefundIn(refundIn);
        request.setRefundInProductItemList(productItems);
        request.setAdjustPropId(tag);
        request.setUser(usr);
        request.setOperationType(operationType);
        // 220 无头件登记单
        request.setAdjustSourceBillType(220);
        return ocRefundInGenerateStorageBillService.generateStorageBill(request);
    }
//
//    /**
//     * update bil and gen notice bil, result bil
//     *
//     * @param sReturn           source return
//     * @param rItems            source return items
//     * @param newReturn         update return
//     * @param updateReturnItems update return  items
//     * @param newRefundIn       update refund in
//     * @param newInItems        update refund in items
//     * @param actualItems       return actual items
//     * @param rLog2             return logs
//     * @param refundLogs        refund logs
//     */
//    @Transactional
//    public void atomicGenNoticeAndResultBil(OcBReturnOrder sReturn, List<OcBReturnOrderRefund> rItems,
//                                            OcBReturnOrder newReturn,
//                                            List<OcBReturnOrderRefund> updateReturnItems,
//                                            OcBRefundIn newRefundIn, List<OcBRefundInProductItem> newInItems,
//                                            List<OcBReturnOrderActual> actualItems, OcBReturnOrderLog rLog2,
//                                            List<OcBRefundInLog> refundLogs) {
//        User user = ThreadLocalUtil.users.get();
//        returnInService.persistReturnNormalMatchResult(newRefundIn, newInItems, newReturn, updateReturnItems,
//                actualItems, refundLogs, rLog2);
//        ValueHolderV14<String> noticeInResult = generateStockInBills(sReturn, rItems,
//                GenerateStorageBillRequest.OperationType.INSERT_NOTICES_AND_RESULT, user);
//        AssertUtil.isTrue(noticeInResult.isOK(), noticeInResult.getMessage());
//        newReturn.setStoInNoticesNo(noticeInResult.getData());
//        newReturn.setStoInNoticesId(sReturn.getStoInNoticesId());
//    }

    /**
     * update refund in product item match status
     *
     * @param inItem   source product
     * @param returnId matched return id
     * @return new product item
     */
    public OcBRefundInProductItem preUpdateRefundInItem4FistMatch(OcBRefundInProductItem inItem, Long returnId) {
        OcBRefundInProductItem newInItem = new OcBRefundInProductItem();
        newInItem.setId(inItem.getId());
        // 一阶
        newInItem.setOcBRefundInId(inItem.getOcBRefundInId());
        newInItem.setOcBReturnOrderId(returnId);
        newInItem.setIsWithoutOrig(IsWithoutOrigEnum.NOT_WITHOUT_ORIG.getVal());
        // 二阶
        newInItem.setIsMatch(IsMatchEnum.MATCHED.getVal());
        newInItem.setIsGenInOrder(IsGenInEnum.YES.integer());
        return newInItem;
    }

    /**
     * 重新计算金额,状态
     * re calc  多次入, 第一次才计算金额,数量不关心, 新生成的退单, 运费,其它金额不参与生成计算
     *
     * @param returnOrder return order
     * @param returnItems return order items
     */
    public void statisticsReturnBilStatusAndAmt(OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> returnItems) {
        logStep("reCalc.amt.status");
        BigDecimal returnAmtList = BigDecimal.ZERO;
        boolean flag = true;
        for (OcBReturnOrderRefund item : returnItems) {
            BigDecimal qtyRefund = item.getQtyRefund();
            AssertUtil.isTrue(NumUtil.gtZero(qtyRefund), "ReturnItem QtyRefund Null");
            BigDecimal qtyIn = NumUtil.init(item.getQtyIn());
            BigDecimal amtRefund = NumUtil.init(item.getAmtRefund());
            if (NumUtil.prevGtOrEqNext(qtyIn, qtyRefund)) {
                returnAmtList = returnAmtList.add(amtRefund);
            } else if (NumUtil.prevGtNext(qtyRefund, qtyIn)) {
                amtRefund = reCalcReturnItemAmtRefund(item, BigDecimal.ZERO);
                item.setAmtRefund(amtRefund);
                returnAmtList = returnAmtList.add(amtRefund);
                flag = false;
            } else {
                item.setAmtRefund(BigDecimal.ZERO);
                flag = false;
            }
        }
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WHOLE.getVal());
        if (!flag) {
            BigDecimal shipAmt = NumUtil.init(returnOrder.getShipAmt());
            BigDecimal returnAmtOther = NumUtil.init(returnOrder.getReturnAmtOther());
            BigDecimal exchangeAmt = NumUtil.init(returnOrder.getExchangeAmt());
            BigDecimal amtActual = returnAmtList.add(shipAmt).add(returnAmtOther).subtract(exchangeAmt);
            returnOrder.setReturnAmtList(returnAmtList);
            returnOrder.setReturnAmtActual(amtActual);
            returnOrder.setProReturnStatus(ProReturnStatusEnum.PORTION.getVal());
        }
    }

    /**
     * 重新计算退货明细退货金额
     *
     * @param item return item
     * @return refund amt
     */
    public BigDecimal reCalcReturnItemAmtRefund(OcBReturnOrderRefund item, BigDecimal qtyIn) {
        BigDecimal amtRefund = NumUtil.init(item.getAmtRefund());
        BigDecimal amtRfnSingle = NumUtil.init(item.getAmtRefundSingle());
        if (NumUtil.eqZero(amtRfnSingle)) {
            BigDecimal baseQty = NumUtil.gtZero(qtyIn) ? qtyIn : item.getQtyRefund();
            amtRfnSingle = amtRefund.divide(baseQty, 2, RoundingMode.HALF_EVEN);
        }
        return NumUtil.init(item.getQtyIn()).multiply(amtRfnSingle);
    }

    /**
     * 重新统计退货状态
     * re calc  多次入, 第一次才计算金额,数量不关心, 新生成的退单, 运费,其它金额不参与生成计算
     *
     * @param returnOrder return order
     * @param returnItems return order items
     */
    public boolean statisticsProReturnStatus(OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> returnItems) {
        for (OcBReturnOrderRefund item : returnItems) {
            BigDecimal qtyRefund = item.getQtyRefund();
            AssertUtil.isTrue(NumUtil.gtZero(qtyRefund), "ReturnItem QtyRefund Null");
            BigDecimal qtyIn = NumUtil.init(item.getQtyIn());
            if (NumUtil.prevGtNext(qtyRefund, qtyIn)) {
                returnOrder.setProReturnStatus(ProReturnStatusEnum.PORTION.getVal());
                return false;
            }
        }
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WHOLE.getVal());
        return true;
    }

    /**
     * 更新退单明细,入库信息
     *
     * @param returnItem
     * @param isReCalcAmt
     * @return
     */
    public OcBReturnOrderRefund preUpdateReturnItem4FistMatch(OcBReturnOrderRefund returnItem, boolean isReCalcAmt) {
        OcBReturnOrderRefund returnRefund = new OcBReturnOrderRefund();
        returnRefund.setId(returnItem.getId());
        returnRefund.setOcBReturnOrderId(returnItem.getOcBReturnOrderId());
        // 一阶
        returnRefund.setQtyMatch(returnItem.getQtyMatch());
        // 二阶
        returnRefund.setQtyIn(returnItem.getQtyIn());
        if (isReCalcAmt) {
            returnRefund.setAmtRefund(returnItem.getAmtRefund());
        }
        return returnRefund;
    }

    /**
     * refund in write return
     *
     * @param refundIn  refund in bil
     * @param returnBil return bil
     */
    public void preRefundInWriteReturn4StoreInfo(OcBRefundIn refundIn, OcBReturnOrder returnBil) {
        if (returnBil.getStoreId() == null && refundIn.getInStoreId() != null) {
            returnBil.setCpCStoreId(refundIn.getInStoreId());
            returnBil.setCpCStoreEcode(refundIn.getInStoreEcode());
            returnBil.setCpCStoreEname(refundIn.getInStoreEname());
        }
    }

    /**
     * return order confirm info
     *
     * @param source origin return
     * @param target update info
     * @param usr    user
     */
    public void assignReturnConfirmInfo(OcBReturnOrder source, OcBReturnOrder target, User usr) {
        if (ReturnOrderConfirmStatusEnum.CONFIRM.getKey().equals(source.getConfirmStatus())) {
            return;
        }
        target.setConfirmId(Long.valueOf(usr.getId()));
        target.setConfirmName(usr.getName());
        target.setConfirmDate(new Date());
        target.setConfirmStatus(ReturnOrderConfirmStatusEnum.CONFIRM.getKey());
    }

    /**
     * 预.更退单
     *
     * @param source   return order
     * @param refundIn refund in order
     */
    public void preUpdateReturn4FistMatch(OcBReturnOrder source, OcBReturnOrder target, OcBRefundIn refundIn) {
        // 0. return
        User user = ThreadLocalUtil.users.get();

        target.setId(source.getId());
        target.setWmsBillNo(refundIn.getWmsBillNo());
        target.setOcBRefundInId(refundIn.getId());

        // 二阶段
        target.setReturnStatus(ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal());
        target.setProReturnStatus(source.getProReturnStatus());
        target.setIsTodrp(OcBOrderConst.IS_STATUS_IY);

        target.setReturnAmtList(source.getReturnAmtList());
        target.setReturnAmtActual(source.getReturnAmtActual());

        target.setInerId(Long.valueOf(user.getId()));
        target.setInerName(user.getName());
        target.setInerEname(user.getEname());
        target.setInTime(refundIn.getWarehouseInTime());
        source.setInTime(refundIn.getWarehouseInTime());

        target.setCpCPhyWarehouseInId(source.getCpCPhyWarehouseInId());

        this.preRefundInWriteReturn4StoreInfo(refundIn, target);
        setStoreNature(target);
    }

    /**
     * assign return store nature
     *
     * @param returnOrder return order
     */
    private void setStoreNature(OcBReturnOrder returnOrder) {
        Long storeId = returnOrder.getStoreId();
        if (ObjectUtils.isEmpty(storeId)) {
            return;
        }
        try {
            CpStore cpStore = cpRpcService.selectCpCStoreById(storeId);
            if (ObjectUtils.isEmpty(cpStore)) {
                return;
            }
            returnOrder.setStoreNature(cpStore.getStorenature());
        } catch (Exception e) {
            log.error(LogUtil.format("setStoreNature.fail:{}", "ReturnInMatch"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 预.更新入库单.状态操作类
     *
     * @param inRelation in relation
     * @param inItems    match in relation
     * @return refund in order
     */
    public OcBRefundIn preUpdateRefund4FistMatch(RefundInRelation inRelation,
                                                 OcBReturnOrder returnBil, List<OcBRefundInProductItem> inItems) {
        User user = ThreadLocalUtil.users.get();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        OcBRefundIn newRefundIn = new OcBRefundIn();
        // 一阶
        newRefundIn.setId(refundIn.getId());
        newRefundIn.setMatcher(user.getEname());
        newRefundIn.setMatchedTime(new Date());
        // 二阶
        int currentMatchCount = inItems.size();
        int originUnMatchCount = inRelation.getUnMatchItems().size();
        int matchStatus = originUnMatchCount > currentMatchCount ? OcBOrderConst.REFUND_IN_MATCHSTATUS_PORTION : OcBOrderConst.REFUND_IN_MATCHSTATUS_ALL;
        newRefundIn.setMatchStatus(matchStatus);
        returnUpdateRefund(refundIn, returnBil, newRefundIn);
        return newRefundIn;
    }

    /**
     * 预.更新入库单.单据信息类
     *
     * @param srcRefund   OcBRefundIn
     * @param srcReturn   OcBReturnOrder
     * @param newRefundIn OcBRefundIn
     */
    public void returnUpdateRefund(OcBRefundIn srcRefund, OcBReturnOrder srcReturn, OcBRefundIn newRefundIn) {

        // 原平台单号
        if (StringUtils.isBlank(srcRefund.getSourceCode()) && StringUtils.isNotBlank(srcReturn.getTid())) {
            newRefundIn.setSourceCode(srcReturn.getOrigSourceCode());
        }
        // 原单单号
        if (srcRefund.getOrigOrderNo() == null && srcReturn.getOrigOrderId() != null) {
            newRefundIn.setOrigOrderNo(srcReturn.getOrigOrderId());
        }
        // 买家昵称
        if (srcRefund.getUserNick() == null && srcReturn.getBuyerNick() != null) {
            newRefundIn.setUserNick(srcReturn.getBuyerNick());
        }
        // 收件人
        if (StringUtils.isBlank(srcRefund.getReceiverName()) && StringUtils.isNotBlank(srcReturn.getReceiveName())) {
            newRefundIn.setReceiverName(srcReturn.getReceiveName());
        }
        // 发件地址
        if (StringUtils.isBlank(srcRefund.getReceiverAddress()) && StringUtils.isNotBlank(srcReturn.getReceiveAddress())) {
            newRefundIn.setReceiverAddress(srcReturn.getReceiveAddress());
        }
        // 物流公司
        if (srcRefund.getCpCLogisticsId() == null && srcReturn.getCpCLogisticsId() != null) {
            newRefundIn.setCpCLogisticsId(srcReturn.getCpCLogisticsId());
        }
        if (StringUtils.isBlank(srcRefund.getCpCLogisticsEcode()) && StringUtils.isNotBlank(srcReturn.getCpCLogisticsEcode())) {
            newRefundIn.setCpCLogisticsEcode(srcReturn.getCpCLogisticsEcode());
        }
        if (StringUtils.isBlank(srcRefund.getCpCLogisticsEname()) && StringUtils.isNotBlank(srcReturn.getCpCLogisticsEname())) {
            newRefundIn.setCpCLogisticsEname(srcReturn.getCpCLogisticsEname());
        }
    }

    /**
     * generate actual item list
     *
     * @param inRelation refund relation
     * @param rOrder     return order
     * @param usr        user
     * @return item list
     */
    public List<OcBReturnOrderActual> generateActualItems(RefundInRelation inRelation, OcBReturnOrder rOrder, User usr) {

        OcBRefundIn refundIn = inRelation.getRefundIn();
        if (!IsWrongReceive.YES.val().equals(refundIn.getIsWrongReceive())) {
            return null;
        }
        List<OcBRefundInActualItem> actualItems = inRelation.getActualItems();
        if (CollectionUtils.isEmpty(actualItems)) {
            return null;
        }
        List<OcBReturnOrderActual> list = new ArrayList<>();
        Long returnId = rOrder.getId();
        for (OcBRefundInActualItem actualItem : actualItems) {
            OcBReturnOrderActual newActual = new OcBReturnOrderActual();
            BeanUtils.copyProperties(actualItem, newActual);
            newActual.setOcBReturnOrderId(returnId);
            newActual.setId(ModelUtil.getSequence("OC_B_RETURN_ORDER_ACTUAL"));
            BaseModelUtil.initialBaseModelSystemField(newActual, usr);
            list.add(newActual);
        }
        rOrder.setIsWrongReceive(IsWrongReceive.YES.val());
        return list;
    }

    /**
     * 根据退货明细ID分组聚合入库数量
     *
     * @param qty
     * @param subReturn
     * @param inItem
     * @param stockIn
     */
    public void branchInQty(BigDecimal qty, OcBReturnOrderRefund subReturn, OcBRefundInProductItem inItem, OcReturnInRelation stockIn) {
        String groupKey = inItem.getProductDate();
        subReturn.setReserveVarchar01(groupKey);
        String productMark = inItem.getProductMark();
        productMark = StringUtils.isBlank(productMark) ? "null" : productMark;
        groupKey = groupKey + "," + productMark;
        stockIn.matchedMapping(groupKey, qty, subReturn.getId());
    }

    /**
     * @param refundIn OcBRefundIn
     * @param cMap     sku 集合
     * @param user     User
     * @return OcBRefundInLog
     */
    public OcBRefundInLog preInsertRefundLog4FistMatch(OcBRefundIn refundIn, Map<String, BigDecimal> cMap, User user) {
        String contentString = statisticMatchedSkuMessage(cMap);
        contentString = "入库结果单入库完成[入库]，入库条码: " + contentString;
        return buildRefundInLog("退货单入库", contentString, refundIn.getId(), user);
    }

    /**
     * 合并日志
     * sku log qty
     *
     * @param cMap sku 集合
     * @return 日志
     */
    public String statisticMatchedSkuMessage(Map<String, BigDecimal> cMap) {
        String message = "";
        if (MapUtils.isEmpty(cMap)) {
            return message;
        }
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, BigDecimal> e : cMap.entrySet()) {
            sb.append(",").append(e.getKey()).append("(")
                    .append(e.getValue().setScale(0, BigDecimal.ROUND_DOWN)).append(")");
        }
        if (sb.length() > 0) {
            message = sb.substring(OcBOrderConst.IS_STATUS_IY);
        }
        return message;
    }

    /**
     * 入库单入库日志
     *
     * @param type    type
     * @param message 内容
     * @param key     key
     * @return log
     */
    public OcBRefundInLog buildRefundInLog(String type, String message, Long key, User usr) {
        OcBRefundInLog inLog = new OcBRefundInLog();
        inLog.setLogtype(type);
        inLog.setUsername(usr.getName());
        inLog.setOwnername(usr.getName());
        inLog.setOwnerename(usr.getEname());
        inLog.setModifiername(usr.getName());
        inLog.setModifierename(usr.getEname());
        inLog.setOwnerid(Long.valueOf(usr.getId()));
        inLog.setCreationdate(new Date());
        inLog.setModifierid(Long.valueOf(usr.getId()));
        inLog.setModifieddate(new Date());
        inLog.setAdOrgId(Long.valueOf(usr.getOrgId()));
        inLog.setAdClientId(Long.valueOf(usr.getClientId()));
        inLog.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
        inLog.setIpaddress(usr.getLastloginip());
        Long id = Tools.getSequence("oc_b_refund_in_log");
        inLog.setId(id);
        inLog.setLogmessage(message);
        inLog.setOmsonlineorderid(key);
        return inLog;
    }

    /**
     * 退单入库.退单.日志
     *
     * @param type    type
     * @param message 内容
     * @param key     shard key
     * @return log
     */
    public OcBReturnOrderLog buildReturnOderLog(String type, String message, Long key, User usr) {
        OcBReturnOrderLog returnLog = new OcBReturnOrderLog();
        returnLog.setLogType(type);
        returnLog.setUserName(usr.getName());
        returnLog.setOwnername(usr.getName());
        returnLog.setOwnerename(usr.getEname());
        returnLog.setCreationdate(new Date());
        returnLog.setOwnerid(Long.valueOf(usr.getId()));
        returnLog.setModifierid(Long.valueOf(usr.getId()));
        returnLog.setModifiername(usr.getName());
        returnLog.setModifierename(usr.getEname());
        returnLog.setModifieddate(new Date());
        returnLog.setIsactive(OcBOrderConst.IS_ACTIVE_YES);
        returnLog.setIpAddress(usr.getLastloginip());
        returnLog.setAdOrgId(Long.valueOf(usr.getOrgId()));
        returnLog.setAdClientId(Long.valueOf(usr.getClientId()));
        Long id = Tools.getSequence(OcElasticSearchIndexResources.OC_B_RETURN_ORDER_LOG_TYPE_NAME);
        returnLog.setId(id);
        returnLog.setLogMessage(message);
        returnLog.setOcBReturnOrderId(key);
        return returnLog;
    }

    /**
     * order log
     *
     * @param orderId    order id
     * @param billNo     order bill no
     * @param logType    type
     * @param logMessage message
     * @param user       User
     * @return order log
     */
    public OcBOrderLog buildOrderLog(long orderId, String billNo, String logType, String logMessage,
                                     User user) {
        OcBOrderLog newLog = new OcBOrderLog();
        long autoId = ModelUtil.getSequence("oc_b_order_log");
        newLog.setId(autoId);
        newLog.setBillNo(billNo);
        newLog.setOcBOrderId(orderId);
        newLog.setLogType(logType);
        newLog.setLogMessage(logMessage);

        newLog.setOwnername(user.getEname());
        newLog.setOwnerename(user.getEname());
        newLog.setAdOrgId((long) user.getOrgId());
        newLog.setOwnerid(Long.valueOf(user.getId()));
        newLog.setAdClientId((long) user.getClientId());
        newLog.setCreationdate(new Date());
        newLog.setModifieddate(new Date());
        newLog.setUserName(user.getName());
        newLog.setModifiername(user.getName());
        newLog.setModifierid(Long.valueOf(user.getId()));

        if (StringUtils.isNotBlank(user.getLastloginip())) {
            newLog.setIpAddress(user.getLastloginip());
        } else {
            newLog.setIpAddress("127.0.0.1");
        }
        return newLog;
    }

    @Transactional
    public void updateReturnAndLog(OcBReturnOrder updateReturn, OcBReturnOrderLog returnLog) {
        returnInService.updateReturnBil(updateReturn);
        recordReturnOrderLog(updateReturn.getId(), returnLog, true);
    }

    /**
     * 记录退单日志
     *
     * @param returnId  退单id
     * @param returnLog OcBReturnOrderLog
     * @param isAuto    boolean
     */
    private void recordReturnOrderLog(Long returnId, OcBReturnOrderLog returnLog, boolean isAuto) {

        String type = returnLog.getLogType();
        String logMsg = returnLog.getLogMessage();
        logMsg = isAuto ? (logMsg + "[自动]") : (logMsg + "[手动]");
        boolean isUpdate = false;
        OcBReturnOrderLog logOrder = returnInService.queryLatestByReturnId(returnId);
        if (logOrder != null) {
            String logType = logOrder.getLogType();
            String logMessage = logOrder.getLogMessage();
            boolean eqType = StringUtils.equalsIgnoreCase(type, logType);
            boolean eqMsg = StringUtils.equalsIgnoreCase(logMsg, logMessage);
            isUpdate = eqType && eqMsg;
        }
        if (isUpdate) {
            returnInService.updateLatestLogModifiedDate(returnId, logOrder.getId());
        } else {
            returnLog.setLogMessage(logMsg);
            returnInService.insertReturnOrderLog(returnLog);
        }
    }

    /**
     * log
     *
     * @param express message
     * @param obs     key
     */
    private void logStep(String express, Object... obs) {
        ThreadLocalUtil.logStepMsg.get().add(String.format(express, obs));
    }

}
