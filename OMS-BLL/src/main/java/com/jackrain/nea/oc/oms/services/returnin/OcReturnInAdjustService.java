package com.jackrain.nea.oc.oms.services.returnin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.base.Throwables;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.model.enums.MatchProcessState;
import com.jackrain.nea.oc.oms.model.enums.OcReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnInType;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderdifferenceMarkEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ac.ReturnOrderGenericMarkEnum;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.request.GenerateStorageBillRequest;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInShouldRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.services.ReturnOrderAuditService;
import com.jackrain.nea.oc.oms.services.bn.BnService;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/9
 */
@Slf4j
@Component
public class OcReturnInAdjustService {

    @Autowired
    private OcReturnInCommService commService;

    @Autowired
    private OcReturnInSupport returnInService;

    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;
    @Resource
    private PsRpcService psRpcService;

    @Resource
    private BnService bnseService;
    @Resource
    private ReturnOrderAuditService returnOrderAuditService;

    @NacosValue(value = "${r3.oms.return.in.nameLess.isopen:false}", autoRefreshed = true)
    private Boolean IS_OPEN;

    /**
     * @param inRelation
     * @return
     */
    public MatchProcessState generateAdjustBil(RefundInRelation inRelation) {
        logStep("generateAdjustBil.start...");
        //生成应退明细
        if (IS_OPEN) {
            commonTaskExecutor.submit(() -> {
                try {
                    ApplicationContextHandle.getBean(OcReturnInAdjustService.class)
                            .saveNamelessShouldRefundDetail(inRelation);
                } catch (Exception e) {
                    log.error(LogUtil.format("OcReturnInAdjustService.saveNamelessShouldRefundDetail error:{}",
                                    "OcReturnInAdjustService.saveNamelessShouldRefundDetail")
                            , Throwables.getStackTraceAsString(e));
                }
            });
        }
        List<OcBRefundInProductItem> items = inRelation.getUnMatchItems();
        if (CollectionUtils.isEmpty(items)) {
            return MatchProcessState.MINUS;
        }
        List<Long> list = new ArrayList<>();
        Map<String, BigDecimal> skuQtyMap = new HashMap<>();
        for (OcBRefundInProductItem item : items) {
            boolean needAdjust = inRelation.isNeedAdjust(item);
            if (needAdjust) {
                inRelation.collectAdjust(item);
                list.add(item.getId());
                commService.statisticInQty(skuQtyMap, item.getPsCSkuEcode(), item.getQty());
            }
        }
        List<OcBRefundInProductItem> unAdjustments = inRelation.getUnAdjustments();
        if (CollectionUtils.isEmpty(unAdjustments)) {
            return MatchProcessState.MINUS;
        }
        ReturnInType returnInType = inRelation.getReturnInType();
        GenerateStorageBillRequest.OperationType inType = GenerateStorageBillRequest.OperationType.INSERT_ADJUST;
        boolean isNameLessInType = ReturnInType.NAMELESS == returnInType;
        if (isNameLessInType) {
            logStep("returnInType.nameLess pass wms");
            inType = GenerateStorageBillRequest.OperationType.INSERT_ADJUST_PASS_WMS;
            if (inRelation.isFluxWms()) {
                inType = GenerateStorageBillRequest.OperationType.INSERT_ADJUST;
            }
        }
        String skuMessage = commService.statisticMatchedSkuMessage(skuQtyMap);
        boolean genResult = generateAdjustBil(inRelation, skuMessage, list, inType, isNameLessInType);
        if (genResult) {
            unAdjustments.clear();
        }
        logStep("generateAdjustBil.end...");
        return MatchProcessState.MINUS;
    }

    /**
     * 保存无名件入库结果单应退明细
     *
     * @param inRelation
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveNamelessShouldRefundDetail(RefundInRelation inRelation) {
        log.info(LogUtil.format("OcReturnInAdjustService.saveNamelessShouldRefundDetail inRelation:{}",
                "OcReturnInAdjustService.saveNamelessShouldRefundDetail"), JSONObject.toJSONString(inRelation));
        OcBRefundIn refundIn = inRelation.getRefundIn();
        if (Objects.isNull(refundIn)) {
            log.info("saveNamelessShouldRefundDetail 保存无名件入库结果单应退明细,无名件入库结果单为null");
            return;
        }
        if (!(ReturnInType.NAMELESS.val().equals(refundIn.getInType()))) {
            log.info("saveNamelessShouldRefundDetail 退货入库结果单LOGISTIC_NUMBER={}非无名件", refundIn.getLogisticNumber());
            return;
        }
        if (StringUtils.isBlank(refundIn.getLogisticNumber())) {
            log.info("saveNamelessShouldRefundDetail 退货入库结果单id={}物流单号为空", refundIn.getId());
            return;
        }
        boolean exist = returnInService.existRefundInShouldRefundItem(refundIn.getId());
        if (exist) {
            log.info("saveNamelessShouldRefundDetail 应退明细已存在");
            return;
        }
        log.info("saveNamelessShouldRefundDetail 保存无名件入库结果单应退明细LOGISTIC_NUMBER={}", refundIn.getLogisticNumber());
        String logisticNo = refundIn.getLogisticNumber();
        if (logisticNo.contains("-")) {
            int index = logisticNo.indexOf("-");
            logisticNo = logisticNo.substring(0, index);
        }
        List<Long> list = ES4ReturnOrder.queryIdsByLogisticsCode(logisticNo);
        if (CollectionUtils.isEmpty(list)) {
            log.info("saveNamelessShouldRefundDetail 退换货单未查询到记录");
            return;
        }
        List<OcBReturnOrder> updateReturnOrderList = new ArrayList<>();
        List<OcBReturnOrderRefund> itemsAll = new ArrayList<>();

        List<OcBReturnOrder> bnCancelList = Lists.newArrayList();
        for (Long id : list) {
            // 匹配到了 打上无名件已入库标记
            OcBReturnOrder returnOrder = returnInService.getReturnOrder(id);
            if (ReturnStatusEnum.COMPLETION.getVal().equals(returnOrder.getReturnStatus()) ||
                    ReturnStatusEnum.CANCLE.getVal().equals(returnOrder.getReturnStatus())) {
                log.info("saveNamelessShouldRefundDetail保存无名件入库结果单应退明细" +
                        ",无名件入库结果单状态为【完成、取消】returnOrderId={}", returnOrder.getId());
                continue;
            }
            if (!OcReturnBillTypeEnum.RETURN.getVal().equals(returnOrder.getBillType())) {
                log.info("saveNamelessShouldRefundDetail保存无名件入库结果单应退明细" +
                        ",无名件入库结果单单据类型非退货单 returnOrderId={}", returnOrder.getId());
                continue;
            }
            OcBReturnOrder updateReturnOrder = new OcBReturnOrder();
            updateReturnOrder.setId(id);
            updateReturnOrder.setGenericMark(ReturnOrderGenericMarkEnum.NAMELESS_IN.getCode());
            updateReturnOrder.setModifieddate(new Date());
            updateReturnOrderList.add(updateReturnOrder);
            List<OcBReturnOrderRefund> items = returnInService.getReturnItems(returnOrder.getId());
            itemsAll.addAll(items);

            //“是否超时入库”=是
            if ("1".equals(returnOrder.getOverdueStorageStatus())) {
                bnCancelList.add(returnOrder);
            }
        }
        log.info("saveNamelessShouldRefundDetail保存无名件入库结果单应退明细LOGISTIC_NUMBER={}" +
                ",所有应退明细{}", refundIn.getLogisticNumber(), JSON.toJSONString(itemsAll));
        if (CollectionUtils.isEmpty(itemsAll)) {
            log.info("saveNamelessShouldRefundDetail 退换货单未查询到明细记录");
            return;
        }
        List<String> skuExclude = returnInService.getBusinessSystemForOcReturnInExclude();
        // 计算差异
        calculateDifference(inRelation, itemsAll, skuExclude, updateReturnOrderList);
        for (OcBReturnOrder updateReturnOrder : updateReturnOrderList) {
            returnInService.updateReturnBil(updateReturnOrder);
        }
        log.info("saveNamelessShouldRefundDetail排除sku={}", JSON.toJSONString(skuExclude));
        // 整合退换货单数据，并构建应退明细参数&&整理无名件入库单商品明细
        Map<String, OcBRefundInShouldRefundItem> shouldRefundItemMap = new HashMap<>();
        Map<String, OcBRefundInProductItem> productItemMap = new HashMap<>();
        itemGroup(inRelation, itemsAll, shouldRefundItemMap, refundIn, skuExclude, productItemMap);
        // 计算数量差异
        OcBRefundIn updateRefundIn = new OcBRefundIn();
        calculateQtyDiff(productItemMap, shouldRefundItemMap, skuExclude, refundIn, updateRefundIn);
        returnInService.updateRefundInBil(updateRefundIn);
        if (CollectionUtils.isNotEmpty(shouldRefundItemMap.values())) {
            returnInService.insertOcBRefundInShouldRefundItem(shouldRefundItemMap.values());
        }

        //无名件已入库判断是否需要取消班牛工单
        if (CollectionUtils.isNotEmpty(bnCancelList)) {
            for (OcBReturnOrder ocBReturnOrder : bnCancelList) {
                Long id = ocBReturnOrder.getId();
                try {
                    Boolean b = bnseService.batchUpdateBnTask(ocBReturnOrder.getLogisticsCode(), ocBReturnOrder.getCpCLogisticsEname());
                    if (b == null) {
                        continue;
                    }
                    if (b) {
                        returnOrderAuditService.recordReturnOrderLog(id, "班牛工单取消", "超时入库已入库调用班牛取消成功", true,
                                SystemUserResource.getRootUser());
                    } else {
                        returnOrderAuditService.recordReturnOrderLog(id, "班牛工单取消", "超时入库已入库调用班牛取消失败", true,
                                SystemUserResource.getRootUser());
                    }
                } catch (Exception e) {
                    log.error("saveNamelessShouldRefundDetail 名无件入库单取消班牛工单失败 id:{}", id, e);
                    returnOrderAuditService.recordReturnOrderLog(id, "班牛工单取消", "超时入库已入库调用班牛取消异常", true,
                            SystemUserResource.getRootUser());
                }
            }
        }

    }

    /**
     * 计算数量差异
     *
     * @param productItemMap
     * @param shouldRefundItemMap
     * @param skuExclude
     * @param refundIn
     * @param updateRefundIn
     */
    private void calculateQtyDiff(Map<String, OcBRefundInProductItem> productItemMap,
                                  Map<String, OcBRefundInShouldRefundItem> shouldRefundItemMap,
                                  List<String> skuExclude, OcBRefundIn refundIn, OcBRefundIn updateRefundIn) {
        String productDiff = AcConstant.IS_NO;
        String numLess = AcConstant.IS_NO;
        String numMore = AcConstant.IS_NO;
        //判断无名件入库结果不存在，但是退换货单存在的场景,数异少
        Set<String> productItemMapKeys = new HashSet<>(productItemMap.keySet());
        for (Map.Entry<String, OcBRefundInShouldRefundItem> itemEntry : shouldRefundItemMap.entrySet()) {
            String skuCode = itemEntry.getKey();
            OcBRefundInShouldRefundItem item = itemEntry.getValue();
            if (!skuExclude.contains(skuCode) && !productItemMapKeys.contains(skuCode)) {
                item.setDiffLess(item.getApplyNum());
                numLess = AcConstant.IS_YES;
            }
        }
        //以无名件入库结果明细为主判断
        for (Map.Entry<String, OcBRefundInProductItem> itemEntry : productItemMap.entrySet()) {
            String skuCode = itemEntry.getKey();
            OcBRefundInProductItem productItem = itemEntry.getValue();
            OcBRefundInShouldRefundItem item = shouldRefundItemMap.get(skuCode);
            if (Objects.isNull(item)) {
                //无名件存在，退换货单不存在
                productDiff = AcConstant.IS_YES;
                OcBRefundInProductItem updateProductItem = new OcBRefundInProductItem();
                updateProductItem.setProductDiff(AcConstant.IS_YES);
                updateProductItem.setModifieddate(new Date());
                returnInService.updateRefundInProductItem(updateProductItem, refundIn.getId(), skuCode);
            } else {
                BigDecimal subtract = productItem.getQty().subtract(item.getApplyNum());
                if (subtract.compareTo(BigDecimal.ZERO) > 0) {
                    item.setDiffMore(subtract);
                    numMore = AcConstant.IS_YES;
                } else if (subtract.compareTo(BigDecimal.ZERO) < 0) {
                    item.setDiffLess(subtract.negate());
                    numLess = AcConstant.IS_YES;
                }
            }
        }
        updateRefundIn.setId(refundIn.getId());
        updateRefundIn.setProductDiff(productDiff);
        updateRefundIn.setNumMore(numMore);
        updateRefundIn.setNumLess(numLess);
        updateRefundIn.setModifieddate(new Date());
    }

    private static void itemGroup(RefundInRelation inRelation, List<OcBReturnOrderRefund> itemsAll,
                                  Map<String, OcBRefundInShouldRefundItem> shouldRefundItemMap, OcBRefundIn refundIn,
                                  List<String> skuExclude, Map<String, OcBRefundInProductItem> productItemMap) {
        // 应退数量
        for (OcBReturnOrderRefund ocBReturnOrderRefund : itemsAll) {
            String skuCode = ocBReturnOrderRefund.getPsCSkuEcode();
            BigDecimal applyNum = Objects.nonNull(ocBReturnOrderRefund.getQtyRefund()) ?
                    ocBReturnOrderRefund.getQtyRefund() : BigDecimal.ZERO;
            BigDecimal orderNum = Objects.nonNull(ocBReturnOrderRefund.getQtyCanRefund()) ?
                    ocBReturnOrderRefund.getQtyCanRefund() : BigDecimal.ZERO;
            if (!shouldRefundItemMap.containsKey(skuCode)) {
                OcBRefundInShouldRefundItem refundInShouldRefundItem = new OcBRefundInShouldRefundItem();
                refundInShouldRefundItem.setId(ModelUtil.getSequence("oc_b_refund_in_should_refund_item"));
                refundInShouldRefundItem.setOcBRefundInId(refundIn.getId());
                refundInShouldRefundItem.setPsCSkuId(ocBReturnOrderRefund.getPsCSkuId());
                refundInShouldRefundItem.setPsCSkuEcode(ocBReturnOrderRefund.getPsCSkuEcode());
                refundInShouldRefundItem.setPsCProEcode(ocBReturnOrderRefund.getPsCProEcode());
                refundInShouldRefundItem.setPsCProEname(ocBReturnOrderRefund.getPsCProEname());
                refundInShouldRefundItem.setGbcode(ocBReturnOrderRefund.getBarcode());
                refundInShouldRefundItem.setApplyNum(applyNum);
                refundInShouldRefundItem.setOrderNum(orderNum);
                OmsModelUtil.setDefault4Add(refundInShouldRefundItem, SystemUserResource.getRootUser());
                shouldRefundItemMap.put(ocBReturnOrderRefund.getPsCSkuEcode(), refundInShouldRefundItem);
            } else {
                OcBRefundInShouldRefundItem ocBRefundInShouldRefundItem = shouldRefundItemMap.get(skuCode);
                ocBRefundInShouldRefundItem.setApplyNum(ocBRefundInShouldRefundItem.getApplyNum().add(applyNum));
                ocBRefundInShouldRefundItem.setOrderNum(ocBRefundInShouldRefundItem.getOrderNum().add(orderNum));
            }
        }
        // 实退数量
        List<OcBRefundInProductItem> items = inRelation.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            items.stream()
                    .filter(o -> !skuExclude.contains(o.getPsCSkuEcode()))
                    .forEach(o -> {
                        OcBRefundInProductItem value = productItemMap.get(o.getPsCSkuEcode());
                        if (Objects.isNull(value)) {
                            productItemMap.put(o.getPsCSkuEcode(), o);
                        } else {
                            value.setQty(value.getQty().add(o.getQty()));
                        }
                    });
        }
    }

    /**
     * 计算差异
     *
     * @param inRelation
     * @param itemsAll
     * @param skuExclude
     * @param updateReturnOrderList
     */
    private void calculateDifference(RefundInRelation inRelation, List<OcBReturnOrderRefund> itemsAll,
                                     List<String> skuExclude, List<OcBReturnOrder> updateReturnOrderList) {
        // 收集sku信息
        List<String> skuCodeList = itemsAll.stream()
                .map(OcBReturnOrderRefund::getPsCSkuEcode)
                .filter(skuCode -> !skuExclude.contains(skuCode)).distinct().collect(Collectors.toList());
        List<OcBRefundInProductItem> items = inRelation.getItems();
        if (CollectionUtils.isNotEmpty(items)) {
            items.stream()
                    .filter(item -> !skuExclude.contains(item.getPsCSkuEcode()))
                    .forEach(item -> skuCodeList.add(item.getPsCSkuEcode()));
        }
        Map<String, BigDecimal> proSalePriceMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuCodeList)) {
            List<PsCPro> proList = psRpcService.queryProByEcode(skuCodeList);
            if (CollectionUtils.isNotEmpty(proList)) {
                proSalePriceMap = proList.stream().collect(Collectors.toMap(PsCPro::getEcode, PsCPro::getPriceSaleList));
            }
        }
        // 应退金额
        BigDecimal shouldRefundAmount = BigDecimal.ZERO;
        for (OcBReturnOrderRefund returnOrderRefund : itemsAll) {
            if (proSalePriceMap.get(returnOrderRefund.getPsCSkuEcode()) == null) {
                continue;
            }
            BigDecimal proSalePrice = proSalePriceMap.get(returnOrderRefund.getPsCSkuEcode());
            shouldRefundAmount = shouldRefundAmount.add(proSalePrice.multiply(returnOrderRefund.getQtyRefund()));
        }
        // 实退金额
        BigDecimal actualRefundAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(items)) {
            for (OcBRefundInProductItem item : items) {
                if (proSalePriceMap.get(item.getPsCSkuEcode()) == null) {
                    continue;
                }
                BigDecimal proSalePrice = proSalePriceMap.get(item.getPsCSkuEcode());
                actualRefundAmount = actualRefundAmount.add(proSalePrice.multiply(item.getQty()));
            }
        }
        // 应退和实退差异金额
        BigDecimal differenceAmount = actualRefundAmount.subtract(shouldRefundAmount);
        String differenceRatio = null;
        if (shouldRefundAmount.compareTo(BigDecimal.ZERO) != 0) {
            differenceRatio = differenceAmount.divide(shouldRefundAmount, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) + "%";
        }
        Integer differenceMark = null;
        if (differenceAmount.compareTo(BigDecimal.ZERO) > 0) {
            differenceMark = ReturnOrderdifferenceMarkEnum.MORE.getVal();
        } else if (differenceAmount.compareTo(BigDecimal.ZERO) < 0) {
            differenceMark = ReturnOrderdifferenceMarkEnum.LESS.getVal();
        } else {
            differenceMark = ReturnOrderdifferenceMarkEnum.ZERO.getVal();
        }
        for (OcBReturnOrder returnOrder : updateReturnOrderList) {
            returnOrder.setDifferenceAmount(differenceAmount);
            returnOrder.setDifferenceRatio(differenceRatio);
            returnOrder.setDifferenceMark(differenceMark);
        }
    }

    public MatchProcessState generateMinusAdjustBil(RefundInRelation inRelation) {
        if (inRelation.isForbidMinus()) {
            logStep("nameless has adjust forbid minus");
            return MatchProcessState.FINISH;
        }
        logStep("generateMinusAdjustBil.start...");
        List<OcBRefundInProductItem> items = inRelation.getMatchedItems();
        if (CollectionUtils.isNotEmpty(items)) {
            for (OcBRefundInProductItem item : items) {
                if (inRelation.isNeedMinusAdjust(item)) {
                    inRelation.collectMinusAdjust(item);
                }
            }
        }
        List<OcBRefundInProductItem> unMinusAdjustments = inRelation.getUnMinusAdjustments();
        if (CollectionUtils.isEmpty(unMinusAdjustments)) {
            return MatchProcessState.FINISH;
        }
        List<Long> list = new ArrayList<>();
        Map<String, BigDecimal> skuQtyMap = new HashMap<>();
        for (OcBRefundInProductItem item : unMinusAdjustments) {
            list.add(item.getId());
            commService.statisticInQty(skuQtyMap, item.getPsCSkuEcode(), item.getQty());
        }
        String skuMessage = commService.statisticMatchedSkuMessage(skuQtyMap);
        boolean genResult = generateMinusAdjustBil(inRelation, unMinusAdjustments, skuMessage, list);
        if (genResult) {
            unMinusAdjustments.clear();
        }
        logStep("generateMinusAdjustBil.end...");
        return MatchProcessState.FINISH;
    }

    public void generateCustomSpecialAdjust(RefundInRelation inRelation, OcBReturnOrder returnOrder) {
        List<OcBRefundInProductItem> items = inRelation.getMatchedItems();
        if (CollectionUtils.isNotEmpty(items)) {
            for (OcBRefundInProductItem item : items) {
                if (inRelation.isNeedMinusAdjust(item)) {
                    inRelation.collectMinusAdjust(item);
                }
            }
        }
        List<OcBRefundInProductItem> unMinusAdjustments = inRelation.getUnMinusAdjustments();
        List<Long> list = new ArrayList<>();
        String skuMessage = null;
        if (!(inRelation.isFluxWms() && !inRelation.isHasAdjustedEle())) {
            AssertUtil.assertException(CollectionUtils.isEmpty(unMinusAdjustments), "待出库调整入库明细为空");
            Map<String, BigDecimal> skuQtyMap = new HashMap<>();
            for (OcBRefundInProductItem item : unMinusAdjustments) {
                list.add(item.getId());
                commService.statisticInQty(skuQtyMap, item.getPsCSkuEcode(), item.getQty());
            }
            skuMessage = commService.statisticMatchedSkuMessage(skuQtyMap);
        }
        commService.atomicGenAndUpdateNameLessMinusJust(inRelation, returnOrder, skuMessage, list, true);
        if (!(inRelation.isFluxWms() && !inRelation.isHasAdjustedEle())) {
            unMinusAdjustments.clear();
        }
    }

    private boolean generateAdjustBil(RefundInRelation inRelation, String skuMessage, List<Long> list,
                                      GenerateStorageBillRequest.OperationType inType, boolean isNameLessInType) {
        try {
            commService.atomicGenerateAndUpdateJust(inRelation, skuMessage, list, inType, isNameLessInType);
            logStep("gen.adjust success");
            return true;
        } catch (Exception ex) {
            logStep(OcReturnInSupport.expMsgFun.apply(ex));
        }
        return false;
    }

    private boolean generateMinusAdjustBil(RefundInRelation inRelation, List<OcBRefundInProductItem> unMinusAdjustments,
                                           String skuMessage, List<Long> list) {
        try {
            ReturnInType returnInType = inRelation.getReturnInType();
            if (ReturnInType.NORM2C == returnInType) {
                logStep("gen.minus normal");
                if (inRelation.isHasAdjustedEle()) {
                    inRelation.getRefundIn().setWarehouseInTime(new Date());
                }
                commService.atomicGenerateAndUpdateMinusJust(inRelation, unMinusAdjustments, skuMessage, list);
            } else {
                logStep("gen.minus nameless");
                Long currentMatchReturnId = inRelation.getCurrentMatchReturnId();
                if (currentMatchReturnId == null) {
                    currentMatchReturnId = unMinusAdjustments.get(0).getOcBReturnOrderId();
                    inRelation.setCurrentMatchReturnId(currentMatchReturnId);
                }
                AssertUtil.notNull(currentMatchReturnId, "minus current return id is null");
                OcBReturnOrder returnOrder = returnInService.getReturnOrder(currentMatchReturnId);
                AssertUtil.notNull(currentMatchReturnId, "minus current return query is null");
                commService.atomicGenAndUpdateNameLessMinusJust(inRelation, returnOrder, skuMessage, list, false);
            }
            logStep("gen.minus adjust success");
            return true;
        } catch (Exception ex) {
            logStep("gen.minus exception");
            logStep(OcReturnInSupport.expMsgFun.apply(ex));
        }
        return false;
    }

    private void logStep(String express, Object... obs) {
        ThreadLocalUtil.logStepMsg.get().add(String.format(express, obs));
    }

}
