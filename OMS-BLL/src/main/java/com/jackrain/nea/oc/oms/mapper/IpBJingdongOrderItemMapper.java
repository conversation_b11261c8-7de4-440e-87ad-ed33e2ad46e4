package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrderItemExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IpBJingdongOrderItemMapper extends ExtentionMapper<IpBJingdongOrderItem> {

    @Select("SELECT * FROM ip_b_jingdong_order_item WHERE ip_b_jingdong_order_id=#{orderId}")
    List<IpBJingdongOrderItemExt> selectJingdongOrderList(@Param("orderId") Long orderId);


    @Select("SELECT * FROM ip_b_jingdong_order_item WHERE ip_b_jingdong_order_id=#{orderId} where sku_id = #{skuId}")
    List<IpBJingdongOrderItemExt> selectJingdongOrderListBySkuId(@Param("orderId") Long orderId, @Param("skuId") Long skuId);

}