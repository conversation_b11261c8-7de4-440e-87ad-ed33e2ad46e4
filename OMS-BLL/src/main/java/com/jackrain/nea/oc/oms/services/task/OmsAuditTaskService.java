package com.jackrain.nea.oc.oms.services.task;

import com.alibaba.fastjson.JSONArray;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBAuditTaskMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.task.OcBAuditTask;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldItemService;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTime;
import com.jackrain.nea.oc.oms.services.audit.wait.OmsAuditTimeCalculateReason;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.result.StCAutoCheckResult;
import com.jackrain.nea.st.model.table.StCAutoCheckDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @author: 易邵峰
 * @since: 2020-02-17
 * create at : 2020-02-17 15:05
 */
@Slf4j
@Component
public class OmsAuditTaskService {

    @Autowired
    private OcBAuditTaskMapper auditTaskMapper;
    @Autowired
    private StRpcService stRpcService;
    @Autowired
    private BuildSequenceUtil buildSequenceUtil;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderHoldItemService holdItemService;
    @Autowired
    private PropertiesConf propertiesConf;

    public void insertAuditTask(OcBAuditTask auditTask) {
        auditTaskMapper.insert(auditTask);
    }

    public void deleteAuditTask(List<Long> taskIdList) {
        auditTaskMapper.deleteBatchIds(taskIdList);
    }

    public void updateAuditTask(List<Long> taskIdList) {
        auditTaskMapper.updateTaskStatus(taskIdList);
    }

    public void updateTaskMergeStatus(List<Long> taskIdList) {
        auditTaskMapper.updateTaskMergeStatus(taskIdList);
    }

    public List<OcBAuditTask> selectAuditTask(int limit, String taskTableName, String shopIds, Long executeTime) {
        return auditTaskMapper.selectTaskIdList(limit, taskTableName, shopIds, executeTime);
    }

    public List<OcBAuditTask> selectTaskIdListByOrderId(List<Long> taskIdList) {
        return auditTaskMapper.selectTaskIdListByOrderId(taskIdList);
    }

    public void updateAuditTaskByOrderId(List<Long> taskIdList) {
        auditTaskMapper.updateTaskStatusByOrderId(taskIdList);
    }

    public void insertAuditTaskList(JSONArray jsonArray) {
        auditTaskMapper.insertAuditTaskList(jsonArray);
    }

    public void updateAuditTaskModitime(List<Long> orderIds) {
        auditTaskMapper.updateTaskModitimes(orderIds);
    }

    /**
     * 创建或更新审核任务状态
     * @param ocBOrder
     * @param reason
     */
    public void createOcBAuditTask(OcBOrder ocBOrder, OmsAuditTimeCalculateReason reason) {
        if (ocBOrder == null) {
            log.error("{}.createOcBAuditTask.订单信息为空,无法计算审核下次执行时间", this.getClass().getName());
            return;
        }

        OcBOrder order = ocBOrderMapper.selectByID(ocBOrder.getId());

        List<OcBAuditTask> ocBAuditTasks = auditTaskMapper.selectTaskListByOrderId(order.getId());
        if (CollectionUtils.isEmpty(ocBAuditTasks)) {
            OcBAuditTask ocBAuditTask = new OcBAuditTask();
            ocBAuditTask.setOrderId(order.getId());
            ocBAuditTask.setStatus(0);
            ocBAuditTask.setCreationdate(new Date());
            ocBAuditTask.setModifieddate(new Date());
            ocBAuditTask.setShopId(order.getCpCShopId());
            ocBAuditTask.setNextTime(this.nextTime(order, reason));
            ocBAuditTask.setId(buildSequenceUtil.buildAuditTaskId());

            this.insertAuditTask(ocBAuditTask);
        } else {
            // 优化更新审核任务，缺失分库健更新将导致大量的数据库负载
            OcBAuditTask ocBAuditTask = ocBAuditTasks.get(0);
            //错误次数
            Integer retryNumber = ocBAuditTask.getRetryNumber() == null ? 0 : ocBAuditTask.getRetryNumber();
            retryNumber = retryNumber + 1;

            Long nextTime = this.nextTime(order, reason);
            auditTaskMapper.updateTaskByOrderId(order.getId(), retryNumber, nextTime);
        }
    }

    /**
     * @return 审核时间
     */
    public long nextTime(OcBOrder ocBOrder, OmsAuditTimeCalculateReason reason) {

        StCAutoCheckDO stCAutoCheck = getStCAutoCheck(ocBOrder.getCpCShopId());
        Integer waitTime = 0;
        Integer antiAuditWaitTime = 0;
        Integer holdWaitTime = 0;
        if (stCAutoCheck != null) {
            waitTime = stCAutoCheck.getWaitTime();
            antiAuditWaitTime = stCAutoCheck.getAntiAuditWaitTime();
            holdWaitTime = stCAutoCheck.getHoldWaitTime();
        }

        Date holdReleaseTime = holdItemService.calculateHoldReleaseTime(ocBOrder.getId());
        ocBOrder.setHoldReleaseTime(holdReleaseTime);
        //唯品会 时间合包码 不等与tid默认延迟时间
        String shopIds = propertiesConf.getProperty("r3.oms. audit.delay.shopIds", "54,55");
        List<String> shopIdList = Arrays.asList(shopIds.split(","));
        //分钟
        Integer delayTime = propertiesConf.getProperty("r3.oms. audit.delay.time", 5);
        if (log.isDebugEnabled()) {
            log.debug("OrderId={},{}.nextTime,策略等待时间={}min;PayTime={}," +
                            "策略反审核等待时间={}min;Modifieddate={},策略hold单等待时间={}min;HoldReleaseTime={}",
                    ocBOrder, this.getClass().getName(), waitTime, ocBOrder.getPayTime(),
                    antiAuditWaitTime, ocBOrder.getModifieddate(), holdWaitTime, ocBOrder.getHoldReleaseTime());
        }
        List<Long> compareList = new ArrayList<>(OmsAuditTime.values().length);
        compareList.add(OmsAuditTime.AUDIT_WAIT_TIME.audit(ocBOrder, waitTime));
        compareList.add(OmsAuditTime.ANTI_AUDIT_WAIT_TIME.antiAudit(ocBOrder, antiAuditWaitTime));
        compareList.add(OmsAuditTime.HOLD_WAIT_TIME.hold(ocBOrder, holdWaitTime));
        Long max = Collections.max(compareList);
        return max;
    }

    //判断是否满足新增审核延迟
    private boolean checkDelay(OcBOrder ocBOrder, List<String> shopIdList) {
        return ocBOrder.getCpCShopId() != null && shopIdList.contains(ocBOrder.getCpCShopId().toString())
                && ocBOrder.getMergedCode() != null && ocBOrder.getTid() != null
                && !ocBOrder.getMergedCode().equals(ocBOrder.getTid());

    }

    public static void main(String[] args) {
        String shopIds = "45,46";
        Long cpCShopId = 45L;

        List<String> shopIdList = Arrays.asList(shopIds.split(","));
        System.out.println(cpCShopId != null && shopIdList.contains(cpCShopId.toString()));
    }

    public StCAutoCheckDO getStCAutoCheck(Long shopId) {
        StCAutoCheckResult stCAutoCheckResult = stRpcService.queryOcStCAutocheck(shopId);
        return stCAutoCheckResult != null ? stCAutoCheckResult.getStCAutoCheckDO() : null;
    }
}
