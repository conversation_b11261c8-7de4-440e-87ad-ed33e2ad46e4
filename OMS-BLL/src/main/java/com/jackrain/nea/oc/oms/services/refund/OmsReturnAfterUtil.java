package com.jackrain.nea.oc.oms.services.refund;

import cn.afterturn.easypoi.cache.manager.IFileLoader;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: 黄世新
 * @Date: 2021/1/7 下午4:13
 * @Version 1.0
 */
public class OmsReturnAfterUtil {

    /**
     * 根据退单明细封装主表tid
     *
     * @param ocBReturnOrderRefunds
     * @return
     */
    public static String getJointTid(List<OcBReturnOrderRefund> ocBReturnOrderRefunds) {
        if (CollectionUtils.isEmpty(ocBReturnOrderRefunds)) {
            return "";
        }
        Set<String> tids = new HashSet<>();
        for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
            tids.add(ocBReturnOrderRefund.getTid());
        }
        String join = StringUtils.join(tids, ",");
        return join;
    }
}
