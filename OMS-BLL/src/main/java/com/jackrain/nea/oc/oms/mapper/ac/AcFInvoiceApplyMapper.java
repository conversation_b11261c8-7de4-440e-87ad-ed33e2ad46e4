package com.jackrain.nea.oc.oms.mapper.ac;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface AcFInvoiceApplyMapper extends ExtentionMapper<AcFInvoiceApply> {

    @Select("select * from ac_f_invoice_apply where trans_status in ('0','2') and fail_count < 6 and next_time <= NOW() limit #{pageSize}")
    List<AcFInvoiceApply> selectForChange( @Param("pageSize") int pageSize);

    @Select("select count(*) from ac_f_invoice_apply where trans_status = '1' and tid like CONCAT('%',#{0},'%')")
    long countSuccessLikeTid(String tid);

    @SelectProvider(type = AcFInvoiceApplySql.class, method = "selectByNodeSql")
    List<AcFInvoiceApply> selectTaskObjectList(@Param("limit") int limit,
                                               @Param("taskTableName") String taskTableName);

    @Select("<script> "
            + "select tid from ac_f_invoice_apply  where tid in"
            + "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<String> selectLisByTid(@Param("tids") List<String> tidList);

    @Slf4j
    class AcFInvoiceApplySql {
        public String selectByNodeSql(@Param("taskTableName") String taskTableName,
                                      @Param("limit") int limit) {
            StringBuffer sql = new StringBuffer();
            StringBuffer limitStr = new StringBuffer(" LIMIT ");
            limitStr.append(limit);
            sql.append("select * from ")
                    .append(taskTableName)
                    .append(" where trans_status in ('0','2') ")
                    .append(" and (fail_count is null or fail_count < 6) and next_time <= NOW() and isactive='Y' ")
                    .append(limitStr);
            return sql.toString();
        }
    }
}