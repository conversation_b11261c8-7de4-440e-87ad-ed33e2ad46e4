package com.jackrain.nea.oc.oms.services.returnin;

import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.enums.DrpStoreTypeEnum;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInActualItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.model.enums.IsGenMinusAdjustEnum;
import com.jackrain.nea.oc.oms.model.enums.IsGenWroAdjustEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWithoutOrigEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.ReturnInBilStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnInType;
import com.jackrain.nea.oc.oms.model.enums.VirtualInStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBRefundInRelation;
import com.jackrain.nea.oc.oms.model.request.OmsRefundInSaveRequest;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.oc.oms.services.RefundOrderToWmsBackService;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.utility.ExceptionUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc : 退货入库结果单
 * <AUTHOR> xiWen
 * @Date : 2022/7/18
 */
@Slf4j
@Component
public class OcRefundInService {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    protected PsRpcService psRpcService;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private OcBRefundInMapper ocBRefundInMapper;

    @Autowired
    private OcReturnInCommService commService;

    @Autowired
    private OcBRefundInLogMapper ocBRefundInLogMapper;

    @Autowired
    private OcBRefundInActualItemMapper ocBRefundInActualItemMapper;

    @Autowired
    private OcBRefundInProductItemMapper ocBRefundInProductItemMapper;

    @Autowired
    private RefundOrderToWmsBackService refundOrderToWmsBackService;


    public ValueHolderV14<String> save(OmsRefundInSaveRequest request) {
        ValueHolderV14<String> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        String wmsBilNo = null;
        try {
            // validate
            OcBRefundInRelation relation = checkParamAndInit(request);
            wmsBilNo = relation.getWmsBilNo();

            // check exist
            checkExistBil(relation);

            // build
            analysisMessage(relation);

            // persistence
            OcRefundInService bean = ApplicationContextHandle.getBean(OcRefundInService.class);
            bean.persistence(relation);

        } catch (Exception e) {
            boolean isNdsExp = e instanceof NDSException;
            String apply = expMsgFun.apply(e);
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("新增退货入库结果单:" + apply);
            if (!isNdsExp) {
                log.error(LogUtil.format("新增退货入库结果单异常:{}", "OcRefundInService", wmsBilNo),
                        Throwables.getStackTraceAsString(e));
            }
        }
        return vh;
    }

    /**
     * 调整单, 虚拟入库wms入库更新
     *
     * @param id
     * @param usr
     * @return
     */
    public ValueHolderV14<String> updateVirtualStatus(Long id, User usr) {

        ValueHolderV14<String> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        RedisReentrantLock redisLock = null;
        try {
            AssertUtil.notNull(usr, "用户为空");
            AssertUtil.notNull(id, "退货入库结果单ID为空");
            String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(id);
            redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            boolean isLock = redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS);
            AssertUtil.isTrue(isLock, "锁单失败");

            OcBRefundIn newRefund = new OcBRefundIn();
            newRefund.setId(id);
            newRefund.setVirtualInStatus(VirtualInStatusEnum.FINISH.integer());
            OmsModelUtil.setDefault4Upd(newRefund, usr);
            int updateResult = ocBRefundInMapper.updateById(newRefund);
            OcBRefundInLog inLog = commService.buildRefundInLog("虚拟入库", "虚拟入库成功", id, usr);
            ocBRefundInLogMapper.insert(inLog);
            vh.setData("退货入库结果单" + id + ", 虚拟入库状态更新成功" + updateResult);

        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            log.error(LogUtil.format("OcRefundInService.updateVirtualStatus.Error: {}", id), Throwables.getStackTraceAsString(e));
            String apply = OcReturnInSupport.expMsgFun.apply(e);
            if (id != null) {
                usr = usr == null ? SystemUserResource.getRootUser() : usr;
                OcBRefundInLog inLog = commService.buildRefundInLog("虚拟入库", apply, id, usr);
                ocBRefundInLogMapper.insert(inLog);
            }
            vh.setMessage(apply);
        } finally {
            if (redisLock != null) {
                redisLock.unlock();
            }
        }
        return vh;
    }

    /**
     * 重置匹配次数 / 更新匹配次数
     *
     * @param preList
     */
    public void reCircleUpdate4Match(List<Long> preList, boolean isReset) {
        if (CollectionUtils.isEmpty(preList)) {
            return;
        }

        int size = preList.size(), startIndex = 0;
        int length = size, eachSize = 50;
        List<Long> subList;
        while (size > 0) {
            if (size > eachSize) {
                subList = preList.subList(startIndex, startIndex + eachSize);
                startIndex += eachSize;
            } else {
                subList = preList.subList(startIndex, length);
            }
            reSetRefundInMatchTimes(subList, isReset);
            size -= eachSize;
        }
    }

    /**
     * @param request
     */
    private OcBRefundInRelation checkParamAndInit(OmsRefundInSaveRequest request) {
        AssertUtil.notNull(request, "参数为空");

        AssertUtil.notNull(request.getReturnInType(), "入库类型为空");

        OmsRefundInSaveRequest.ReturnOrder returnOrder = request.getReturnOrder();
        AssertUtil.notNull(returnOrder, "参数异常,returnOrder为空");

        String wmsBilNo = returnOrder.getReturnOrderId();
        AssertUtil.assertException(StringUtils.isBlank(wmsBilNo), "参数异常, Wms单据编号returnOrderId为空");

        String warehouseCode = returnOrder.getWarehouseCode();
        AssertUtil.assertException(StringUtils.isBlank(warehouseCode), "参数异常, 仓库编码为空");

        List<OmsRefundInSaveRequest.OrderLine> orderLines = request.getOrderLines();
        AssertUtil.assertException(CollectionUtils.isEmpty(orderLines), "参数异常, 明细为空");

        OcBRefundInRelation relation = new OcBRefundInRelation();
        relation.setWmsBilNo(wmsBilNo);
        relation.setRequest(request);
        return relation;
    }

    private void checkExistBil(OcBRefundInRelation relation) {
        String wmsBillNo = relation.getWmsBilNo();
        OcBRefundIn byWmsBillNo = refundOrderToWmsBackService.findByWmsBillNo(wmsBillNo);
        AssertUtil.assertException(byWmsBillNo != null, String.format("WMS单据编号:%s, 已存在退货入库结果单", wmsBillNo));
    }

    private void analysisMessage(OcBRefundInRelation relation) throws ParseException {
        analysisRefundIn(relation);

        analysisProducts(relation);
        String wmsBillNo = relation.getOcBRefundIn().getWmsBillNo();
        AssertUtil.notEmpty(relation.getProductItems(), String.format("WMS单据编号:%s, 明细构建为空", wmsBillNo));
        if (CollectionUtils.isNotEmpty(relation.getActualItems())) {
            relation.getOcBRefundIn().setIsWrongReceive(IsWrongReceive.YES.val());
        }


    }

    /**
     * @param relation
     */
    private void analysisRefundIn(OcBRefundInRelation relation) throws ParseException {
        OmsRefundInSaveRequest.ReturnOrder returnOrder = relation.getRequest().getReturnOrder();
        // 批次
        OcBRefundIn refundIn = new OcBRefundIn();

        OmsRefundInSaveRequest.SenderInfo senderInfo = returnOrder.getSenderInfo();
        if (senderInfo != null) {
            //收货人姓名
            refundIn.setReceiverName(senderInfo.getName());
            //收货人手机号
            refundIn.setReceiverMobile(senderInfo.getMobile());
            //发件地址
            refundIn.setReceiverAddress(senderInfo.getDetailAddress());
        }

        String warehouseCode = returnOrder.getWarehouseCode();
        String storageLocation = returnOrder.getStorageLocation();
        CpCStore storeInfo = getStoreInfo(warehouseCode, storageLocation);
        //入库仓库ID
        refundIn.setInStoreId(storeInfo.getId());
        //入库仓库编号
        refundIn.setInStoreEcode(storeInfo.getEcode());
        //入库仓库名称
        refundIn.setInStoreEname(storeInfo.getEname());
        //实体仓id
        refundIn.setCpCPhyWarehouseId(storeInfo.getCpCPhyWarehouseId());
        //实体仓编码
        refundIn.setCpCPhyWarehouseEcode(storeInfo.getCpCPhyWarehouseEcode());
        //实体仓名称
        refundIn.setCpCPhyWarehouseEname(storeInfo.getCpCPhyWarehouseEname());
        // 入库类型
        refundIn.setInType(relation.getRequest().getReturnInType());
        refundIn.setVirtualInStatus(0);
        //批次编号
        refundIn.setBatchNo(DateUtil.format(new Date()));
        //批次编号ID
        refundIn.setOcBRefundBatchId(-1L);
        //原平台单号
        // refundIn.setSourceCode(sourceCode);
        //备注
        refundIn.setRemark(returnOrder.getRemark());
        //物流单号
        String expressCode = returnOrder.getExpressCode();
        /*if (expressCode != null && expressCode.contains("-")) {
            int endIndex = expressCode.indexOf("-");
            expressCode = expressCode.substring(0, endIndex);
        }*/
        refundIn.setLogisticNumber(expressCode);
        //物流公司
        String logisticsCode = returnOrder.getLogisticsCode();
        assembleLogisticsInfo(logisticsCode, refundIn);

        //入库状态
        refundIn.setInStatus(2);
        //匹配状态
        refundIn.setMatchStatus(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
        //特殊处理类型
        refundIn.setSpecialType(OcBOrderConst.ORDER_SPECIAL_TYPE_NORMAL);
        //入库单编
        refundIn.setId(ModelUtil.getSequence("OC_B_REFUND_IN"));
        //WMS单据编号
        refundIn.setWmsBillNo(relation.getWmsBilNo());
        String returnOrderCode = returnOrder.getReturnOrderCode();
        refundIn.setSgBNoticeInBillNo(returnOrderCode);
        OperateUserUtils.saveOperator(refundIn, SystemUserResource.getRootUser());
        //是否关闭匹配
        refundIn.setIsOffMatch(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
        refundIn.setIsWrongReceive(IsWrongReceive.NO.val());
        refundIn.setBillStatus(ReturnInBilStatus.INIT.val());
        String orderConfirmTime = returnOrder.getOrderConfirmTime();
        Date inDateTime;
        if (StringUtils.isNotBlank(orderConfirmTime)) {
            inDateTime = DateUtils.parseDate(orderConfirmTime, "yyyy-MM-dd HH:mm:ss");
        } else {
            inDateTime = new Date();
        }
        refundIn.setWarehouseInTime(inDateTime);
        relation.setOcBRefundIn(refundIn);
        relation.setRefundInId(refundIn.getId());
    }

    /**
     * 解析.构建入库明细
     *
     * @param relation
     */
    private void analysisProducts(OcBRefundInRelation relation) {
        // 配置,类型
        int wmsStockInBackQty = omsSystemConfig.getWmsStockInBackQty();
        String wmsBilNo = relation.getWmsBilNo();
        boolean isB2CReturn = ReturnInType.NORM2C.val().equals(relation.getRequest().getReturnInType());
        boolean isB2BReturn = ReturnInType.NORM2B.val().equals(relation.getRequest().getReturnInType());
        List<OmsRefundInSaveRequest.OrderLine> orderLines = relation.getRequest().getOrderLines();
        StringBuilder skuSb = new StringBuilder();
        for (OmsRefundInSaveRequest.OrderLine orderLine : orderLines) {
            if (orderLine == null) {
                log.error(LogUtil.format("明细存在空值:{}", "OcRefundInService", wmsBilNo), wmsBilNo);
                continue;
            }
            String skuCode = orderLine.getItemCode();
            AssertUtil.assertException(StringUtils.isBlank(skuCode), "Sku编码为空");
            // 查询sku
            ProductSku skuInfo = psRpcService.selectProductSku(skuCode);
            AssertUtil.notNull(skuInfo, String.format("条码[%s]未获取到商品信息", skuCode));
            // 构建单据
            /* 由于奇门回传报文不存在此字段, 数量改为取实际数量
            int qty = NumUtil.init(orderLine.getPlanQty()).intValue();*/
            BigDecimal origQty = NumUtil.init(orderLine.getActualQty());
            int inQty = origQty.intValue();
            skuSb.append(",").append(skuCode).append("(").append(inQty).append(")");
            boolean abnormalFlag = NumUtil.init(orderLine.getAbnormalFlag()) == 1;
            if (isB2BReturn) {
                OcBRefundInProductItem subItem = getOcBRefundInProductItem(relation, true, orderLine, skuInfo);
                //数量
                subItem.setQty(origQty);
                // 实际入库明细
                if (abnormalFlag) {
                    subItem.setId(ModelUtil.getSequence("OC_B_REFUND_IN_ACTUAL_ITEM"));
                    relation.addActualItem(subItem);
                    continue;
                }
                subItem.setId(ModelUtil.getSequence("OC_B_REFUND_IN_PRODUCT_ITEM"));
                relation.addProductItem(subItem);
                continue;
            }
            AssertUtil.assertException(inQty > wmsStockInBackQty, String.format("条码[%s]数量超过上限", skuCode));
            for (int i = 0; i < inQty; i++) {
                OcBRefundInProductItem subItem = getOcBRefundInProductItem(relation, isB2CReturn, orderLine, skuInfo);
                //数量
                subItem.setQty(BigDecimal.ONE);
                if (abnormalFlag) {
                    subItem.setId(ModelUtil.getSequence("OC_B_REFUND_IN_ACTUAL_ITEM"));
                    relation.addActualItem(subItem);
                    continue;
                }
                subItem.setId(ModelUtil.getSequence("OC_B_REFUND_IN_PRODUCT_ITEM"));
                relation.addProductItem(subItem);
            }
        }
        int skuLength = skuSb.length();
        if (skuLength > 1) {
            String allSku;
            if (skuLength > 1000) {
                allSku = skuSb.substring(1, 1000);
            } else {
                allSku = skuSb.substring(1);
            }
            relation.getOcBRefundIn().setAllSku(allSku);
        }

    }

    /**
     * 构建入库单明细
     *
     * @param relation
     * @param isNormalInType
     * @param orderLine
     * @param skuInfo
     * @return
     */
    private OcBRefundInProductItem getOcBRefundInProductItem(OcBRefundInRelation relation, boolean isNormalInType,
                                                             OmsRefundInSaveRequest.OrderLine orderLine, ProductSku skuInfo) {
        OcBRefundInProductItem subItem = new OcBRefundInProductItem();
        //条码
        subItem.setPsCSkuEcode(skuInfo.getEcode());
        // @20200718 增加SKUID等信息
        subItem.setPsCSkuId(skuInfo.getId());
        //商品编号 skuInfo.getEcode() "10010"
        subItem.setPsCProEcode(skuInfo.getSku());
        //商品名称 skuInfo.getName() "SM爽肤水"
        subItem.setPsCProEname(skuInfo.getName());
        //国际码 skuInfo.getBarcode69() "10010001S"
        subItem.setGbcode(skuInfo.getBarcode69());
        //商品标记
        String inventoryType = orderLine.getInventoryType();
        if (StringUtils.isBlank(inventoryType)) {
            inventoryType = isNormalInType ? inventoryType : "ZP";
        }
        subItem.setProductMark(inventoryType);
        //是否无原单条码
        subItem.setIsWithoutOrig(IsWithoutOrigEnum.IS_WITHOUT_ORIG.getVal());
        // 批次
        String batchCode = orderLine.getBatchCode();
        if (StringUtils.isNotBlank(batchCode)) {
            batchCode = batchCode.replaceAll("-", "");
        } else {
            batchCode = "00000000";
        }
        subItem.setProductDate(batchCode);
        //是否匹配
        subItem.setIsMatch(OcBOrderConst.REFUND_IN_MATCHSTATUS_UN);
        //是否生成调整单
        subItem.setIsGenAdjust(0);
        //是否生成入库单
        subItem.setIsGenInOrder(0);
        //是否生成错发调整单
        subItem.setIsGenWroAdjust(IsGenWroAdjustEnum.NO.integer());
        //是否生成冲无头件调整单
        subItem.setIsGenMinusAdjust(IsGenMinusAdjustEnum.NO.integer());
        // 颜色id
        subItem.setPsCClrId(skuInfo.getColorId());
        // 颜色编码
        subItem.setPsCClrEcode(skuInfo.getColorCode());
        // 颜色名称
        subItem.setPsCClrEname(skuInfo.getColorName());
        // 尺寸id
        subItem.setPsCSizeId(skuInfo.getSizeId());
        // 尺寸编码
        subItem.setPsCSizeEcode(skuInfo.getSizeCode());
        // 尺寸名称
        subItem.setPsCSizeEname(skuInfo.getSizeName());
        // 实收条码国标码
//        subItem.setGbcode(skuInfo.getEcode());
        // 吊牌价
        subItem.setPriceList(skuInfo.getPricelist());
        OperateUserUtils.saveOperator(subItem, SystemUserResource.getRootUser());
        //退货入库单ID
        subItem.setOcBRefundInId(relation.getRefundInId());
        return subItem;
    }

    @Transactional
    public void persistence(OcBRefundInRelation refundInRelation) {
        ocBRefundInMapper.insert(refundInRelation.getOcBRefundIn());
        ocBRefundInProductItemMapper.batchInsert(refundInRelation.getProductItems());
        if (CollectionUtils.isNotEmpty(refundInRelation.getActualItems())) {
            ocBRefundInActualItemMapper.batchInsert(refundInRelation.getActualItems());
        }
    }

    /**
     * @param warehouseCode
     * @return
     */
    private CpCStore getStoreInfo(String warehouseCode, String storageLocation) {
        CpCStore store = null;
        try {
            CpCPhyWarehouse cpPhyWarehouse;
            if (!StringUtils.isEmpty(storageLocation)) {
                /*库位信息非空，富乐仓，通过ecode查询*/
                cpPhyWarehouse = cpRpcService.selectPhyWarehouseByEcode(storageLocation);
            } else {
                /*其他仓，wmsCode唯一*/
                cpPhyWarehouse = cpRpcService.selectPhyWarehouseByWmsEcode(warehouseCode);
            }

            if (cpPhyWarehouse == null) {
                AssertUtil.notNull(cpPhyWarehouse, String.format("仓库编码[%s]未匹配到实体仓档案", warehouseCode));
            }

            Long cpCPhyWarehouseInId = cpPhyWarehouse.getId();
            AssertUtils.cannot(cpCPhyWarehouseInId == null, "实体仓ID为空！");
            // 根据实体仓查询退货逻辑仓信息
            List<Long> storeIds = cpRpcService.queryStoreList(cpCPhyWarehouseInId);
            AssertUtils.cannot(storeIds == null || storeIds.size() <= 0, "根据实体仓查询逻辑仓ID为空！");
            List<CpCStore> cpCStoreList = cpRpcService.queryStoreInfoByIds(storeIds.stream().map(Long::intValue).collect(Collectors.toList()));
            AssertUtils.cannot(CollectionUtils.isEmpty(cpCStoreList), "根据逻辑仓ID查询逻辑仓信息为空！");

            // 退货逻辑仓
            Map<String, List<CpCStore>> storeMap = cpCStoreList.stream().filter(item -> item.getStoretype() != null)
                    .collect(Collectors.groupingBy(CpCStore::getStoretype));
            List<CpCStore> returnStoreList = storeMap.get(DrpStoreTypeEnum.TYPE_27.getValue());
            if (returnStoreList != null && returnStoreList.size() > 0) {
                store = returnStoreList.get(0);
            } else {
                List<CpCStore> storeInList = storeMap.get(DrpStoreTypeEnum.TYPE_18.getValue());
                if (storeInList != null && storeInList.size() > 0) {
                    store = storeInList.get(0);
                } else {
                    store = cpCStoreList.get(0);
                }
            }

            if (ObjectUtils.isEmpty(store)) {
                AssertUtil.notNull(store, String.format("实体仓库编码[%s]未匹配到逻辑仓档案", warehouseCode));
            }
            return store;
        } catch (Exception e) {
            log.error(LogUtil.format("匹配实体仓档案异常:{}", "OcRefundInService.getStoreInfo"),
                    Throwables.getStackTraceAsString(e));
        }
        return store;
    }

    /**
     * 物流信息获取
     *
     * @param code
     * @param refundIn
     */
    private void assembleLogisticsInfo(String code, OcBRefundIn refundIn) {
        if (StringUtils.isBlank(code)) {
            return;
        }
        LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfo(code);
        if (logisticsInfo == null) {
            return;
        }
        refundIn.setCpCLogisticsId(logisticsInfo.getId());
        refundIn.setCpCLogisticsEcode(logisticsInfo.getCode());
        refundIn.setCpCLogisticsEname(logisticsInfo.getName());
    }

    /**
     * 重置入库匹配次数
     *
     * @param list 入库单ids
     */
    private void reSetRefundInMatchTimes(List<Long> list, boolean isReset) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        List<RedisReentrantLock> locks = new ArrayList<>(50);
        try {

            for (Long id : list) {
                String lockRedisKey = BllRedisKeyResources.buildLockReturnInKey(id);
                RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    sb.append(",").append(id);
                    locks.add(redisLock);
                }
            }
            String sqlIds = sb.substring(1);
            if (StringUtils.isBlank(sqlIds)) {
                return;
            }
            logDebug("Update.MatchTimes.Sql.Ids-{}, CurrentOperateSign-{}", sqlIds, isReset);
            if (isReset) {
                int resetResult = ocBRefundInMapper.updateReSetMatchTimes(sqlIds);
                logDebug("ReSet.MatchTimes.Update.Result-{}", resetResult);
            } else {
                int updateResult = ocBRefundInMapper.updateMatchTimes(sqlIds);
                logDebug("Update.MatchTimes.Update.Result-{}", updateResult);
            }

        } catch (Exception ex) {
            log.error(LogUtil.format("ReturnAutoMatchTask.ReUpdate.MatchTimes.更新匹配次数发生异常-{}"),
                    ExceptionUtil.getMessage(ex));
        } finally {
            for (RedisReentrantLock lock : locks) {
                try {
                    lock.unlock();
                } catch (Exception e) {
                    String key = "";
                    if (lock != null) {
                        key = lock.getLockId();
                    }
                    log.error(LogUtil.format("ReturnAutoMatchTask.ReSet.MatchTimes.解锁异常", key));
                    continue;
                }
            }
        }
    }

    /**
     * optimize exception
     */
    private Function<Exception, String> expMsgFun = e -> {
        if (e == null) {
            return "null exception";
        }
        String message = e.getMessage();
        if (message == null) {
            return "null message";
        }
        return message.length() > 200 ? message.substring(0, 200) : message;
    };

    /**
     * level debug recorder
     *
     * @param msg log message
     */
    private void logDebug(String msg, Object... params) {

        if (log.isDebugEnabled()) {
            if (params.length == 0) {
                log.debug(LogUtil.format("ReturnAutoMatchTask.{}"), msg);
            } else {
                log.debug(LogUtil.format("ReturnAutoMatchTask.") + msg, params);
            }
        }
    }

}
