package com.jackrain.nea.oc.oms.sap;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.model.relation.SgBStoAdjustRelation;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjust;
import com.burgeon.r3.sg.core.model.table.store.adjust.SgBStoAdjustItem;
import com.burgeon.r3.sg.core.model.table.store.sap.SgBSapInformationMapping;
import com.burgeon.r3.sg.store.api.adjust.SgBStoAdjustQueryCmd;
import com.burgeon.r3.sg.store.api.sap.SgBSapInformationMappingR3Cmd;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jackrain.nea.cp.result.CpCSupplier;
import com.jackrain.nea.cpext.api.sap.SapShopApiCmd;
import com.jackrain.nea.cpext.model.table.CpCShopItem;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.request.naika.NaiKaCardQueryRequest;
import com.jackrain.nea.hub.result.naika.NaiKaCardQueryResult;
import com.jackrain.nea.oc.oms.constant.NaiKaTypeConstant;
import com.jackrain.nea.oc.oms.constant.SapSalesDateConstant;
import com.jackrain.nea.oc.oms.mapper.OcBOrderCycleBuyInfoMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordItemMapper;
import com.jackrain.nea.oc.oms.model.constant.OcBSapSalesDataRecordConstant;
import com.jackrain.nea.oc.oms.model.constant.R3ParamConstants;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.enums.sap.LineCategoryEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBSapSalesDataRecordRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsNaiKaReturnDetailModel;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderCycleBuyInfo;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecord;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecordItem;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OmsReturnAfSendService;
import com.jackrain.nea.oc.oms.services.naika.OmsNaiKaReturnDetailService;
import com.jackrain.nea.oc.oms.services.returnin.OcReturnInCommService;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.psext.api.PsCCollShopProMappingQueryCmd;
import com.jackrain.nea.psext.model.table.PsCCollShopProMapping;
import com.jackrain.nea.psext.model.table.PsCPro;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.DateUtil;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QuerySessionImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/8/31
 * description : 销售数据记录相关服务
 */
@Slf4j
@Component
public class OcBSapSalesDataRecordService {

    private static final List<String> PICKUP = Lists.newArrayList("RYCK04", "RYCK05", "RYCK12", "RYCK13", "RYCK25", "RYCK27", "RYCK28", "RYCK30");
    //奶卡退款回传SAP数量(OMS传输新增字段至SAP)
    private static final List<String> NAIKA_SAP = Lists.newArrayList("RYTK01", "RYTK02");
    // 成本中心映射
    private static final Map<String, String> COST_CENTER = MapUtil.builder(new HashMap<String, String>())
            .put("3040100000", "6040003000").put("3040100003", "6040003003").put("3040100004", "6040003004")
            .put("3040100005", "6040003005").put("3040100006", "6040003006").put("3040100007", "6040003007")
            .put("3040100009", "6040003009").put("3040100002", "6040003009").put("3040100001", "6040003009").build();

    @DubboReference(group = "cp-ext", version = "1.0")
    SapShopApiCmd sapShopApiCmd;
    @DubboReference(group = "sg", version = "1.0")
    private SgBSapInformationMappingR3Cmd sgBSapInformationMappingR3Cmd;
    @DubboReference(group = "sg", version = "1.0")
    private SgBStoAdjustQueryCmd adjustQueryCmd;
    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCCollShopProMappingQueryCmd psCCollShopProMappingQueryCmd;
    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private OcBSapSalesDataRecordSaveService sapSalesDataRecordSaveService;
    @Autowired
    private OcReturnInCommService returnInCommService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;
    @Autowired
    private OcBOrderNaiKaMapper orderNaiKaMapper;
    @Autowired
    private OcBReturnOrderLogMapper returnOrderLogMapper;
    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    private OcBReturnOrderRefundMapper returnOrderRefundMapper;
    @Autowired
    private OmsReturnAfSendService omsReturnAfSendService;
    @Autowired
    private OcBReturnAfSendMapper afSendMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsNaiKaReturnDetailService omsNaiKaReturnDetailService;
    @Autowired
    private OcBSapSalesDataRecordItemMapper ocBSapSalesDataRecordItemMapper;
    @Autowired
    private OcBOrderCycleBuyInfoMapper orderCycleBuyInfoMapper;

    /**
     * 根据orderId处理，重新查询获取最新信息
     *
     * @param orderId 订单/退单ID
     * @param type    类型 0:订单 1:退单
     * @param user    用户
     */
    public boolean saveOrder(Long orderId, int type, User user) {
        if (type == 0) {
            OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfo(orderId);
            // 过滤发货前退款明细
            if (CollectionUtils.isNotEmpty(orderRelation.getOrderItemList())) {
                Integer refundStatus = 6;
                orderRelation.setOrderItemList(orderRelation.getOrderItemList().stream().filter(o -> !refundStatus.equals(o.getRefundStatus())).collect(Collectors.toList()));
            }
            return saveOrder(orderRelation, user);
        }
        if (type == 1) {
            return saveRefundOrder(orderId, user);
        }
        if (type == 2) {
            return saveNaiKaRefundOrder(orderId, user);
        }
        if (type == 4) {
            return saveAdjustOrder(orderId, user);
        }
        log.info(LogUtil.format("type 不符合,type:{}"), type);
        return false;
    }

    public boolean saveAdjustOrder(Long orderId, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start 库存调整单审核生成销售数据信息 orderId:{}", "库存调整单生成销售数据信息"), orderId);
        }
        boolean success = true;
        try {
            // 根据ID查询库存调整单信息
            ValueHolderV14<SgBStoAdjustRelation> v14 = adjustQueryCmd.queryAdjustById(orderId);
            AssertUtils.isTrue(v14.isOK(), "查询库存调整单失败，" + v14.getMessage());
            SgBStoAdjust sgBStoAdjust = v14.getData().getAdjust();
            List<SgBStoAdjustItem> itemList = v14.getData().getAdjustItemList();
            JSONObject recordMainJo = new JSONObject();
            JSONArray itemArray = new JSONArray();
            //单据类别（库存调整单）
            recordMainJo.put("BILL_TYPE", SgConstantsIF.SAP_BILL_CATEGORY_04);
            //单据编号
            recordMainJo.put("BILL_NO", sgBStoAdjust.getBillNo());
            //业务类型
            recordMainJo.put("MIDDLEGROUND_BILL_TYPE_NAME", businessTypeName(sgBStoAdjust.getSgBAdjustPropId()));
            //出入库时间
            recordMainJo.put("IN_TIME", sgBStoAdjust.getBillDate());
            //汇总状态
            recordMainJo.put("SUM_STATUS", OcBSapSalesDataRecordConstant.SUM_STATUS_ZERO);
            //单据类别（库存调整单）
            recordMainJo.put("SUM_TYPE", sumType(sgBStoAdjust.getSgBAdjustPropId()));
            CpStore store = getStore(sgBStoAdjust.getCpCStoreId());
            String factoryCode = null;
            if (store != null) {
                factoryCode = store.getWerks();
            }
            for (SgBStoAdjustItem sgBStoAdjustItem : itemList) {
                JSONObject recordMainItemJo = new JSONObject();
                //SKU
                recordMainItemJo.put("SKU", sgBStoAdjustItem.getPsCSkuEcode());
                //数量
                recordMainItemJo.put("QTY", sgBStoAdjustItem.getQty());
                //单位
                ProductSku productSku = psRpcService.selectProductSku(sgBStoAdjustItem.getPsCSkuEcode());
                if (productSku != null && productSku.getProAttributeMap() != null && productSku.getProAttributeMap().get("M_DIM3_ID") != null) {
                    recordMainItemJo.put("UNIT", productSku.getProAttributeMap().get("M_DIM3_ID").getEcode());
                }
                //调整店仓
                recordMainItemJo.put("CP_C_STORE_ID", sgBStoAdjust.getCpCStoreId());
                recordMainItemJo.put("CP_C_STORE_ECODE", sgBStoAdjust.getCpCStoreEcode());
                recordMainItemJo.put("CP_C_STORE_ENAME", sgBStoAdjust.getCpCStoreEname());

                //工厂编码
                recordMainItemJo.put("FACTORY_CODE", factoryCode);
                //批次
                recordMainItemJo.put("BATCH", sgBStoAdjustItem.getProduceDate() + "/" + sgBStoAdjustItem.getQty());
                itemArray.add(recordMainItemJo);
            }
            //工厂编码
            recordMainJo.put("FACTORY_CODE", factoryCode);
            // 判断包材调整单工厂编码与成本中心
            //成本中心
            recordMainJo.put("COST_CENTER", costCenter(sgBStoAdjust.getSourceBillNo(), sgBStoAdjust.getSgBAdjustPropId(), factoryCode));


            //合并码
            recordMainJo.put("MERGE_CODE", mergeCode(sgBStoAdjust.getBillDate(), sgBStoAdjust.getSgBAdjustPropId(),
                    sgBStoAdjust.getCpCStoreId(), recordMainJo.getString("COST_CENTER"), factoryCode));
            QuerySession session = fixFieldValue(-1L, recordMainJo, SgConstantsIF.RECODE_TABLE_NAME, SgConstantsIF.RECODE_TABLE_NAME_ITEM, itemArray, user);
            ValueHolder vh = sapSalesDataRecordSaveService.save(session);
            AssertUtils.isTrue(vh.isOK(), (String) vh.get("message"));
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Finish 库存调整单审核生成销售数据信息 orderId:{}", "库存调整单生成销售数据信息"), orderId);
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("库存调整单审核生成销售数据信息失败:异常信息:{}"), Throwables.getStackTraceAsString(ex));
            success = false;
        }
        return success;
    }

    public boolean saveOrder(OcBOrderRelation orderRelation, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start 全渠道订单出库生成销售数据信息 orderId:{}", "出库生成销售数据信息"), orderRelation.getOrderId());
        }
        String message = "成功";
        boolean success = true;
        try {
            OcBOrder order = orderRelation.getOrderInfo();
            List<OcBOrderItem> orderItems = orderRelation.getOrderItemList();
            OrderBusinessTypeCodeEnum businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(order.getBusinessTypeCode());
            if (OcBSapSalesDataRecordConstant.SUM_TYPE_IGNORE.equals(businessTypeCodeEnum.getSaleSumType())) {
                log.info(LogUtil.format("orderId:{},业务类型:{},不符合生成条件", "出库生成销售数据信息"), orderRelation.getOrderId(), businessTypeCodeEnum.getMassage());
                message = "无需生成";
                return success;
            }
            if (CollectionUtils.isEmpty(orderItems)) {
                message = "无需生成,订单无有效发货明细";
                return success;
            }
            SgBSapInformationMapping sapInformationMapping = selectSapInformationMapping(String.valueOf(order.getBusinessTypeId()));
            List<OcBSapSalesDataRecordRelation> relations = new ArrayList<>();
            OcBSapSalesDataRecordRelation relation = new OcBSapSalesDataRecordRelation();
            OcBSapSalesDataRecord sapSalesDataRecord = new OcBSapSalesDataRecord();
            List<OcBSapSalesDataRecordItem> sapSalesDataRecordItems = new ArrayList<>();
            buildSapSalesDataRecordFromOrder(order, orderItems, sapInformationMapping, businessTypeCodeEnum, sapSalesDataRecord, sapSalesDataRecordItems);
            relation.setSapSalesDataRecord(sapSalesDataRecord);
            relation.setSapSalesDataRecordItems(sapSalesDataRecordItems);
            relations.add(relation);
            // 需要再插一条 销售汇总
            if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE
                    || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD
                    || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_RESET
                    || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_RESET) {
                OcBSapSalesDataRecord sapSalesDataRecordXs = new OcBSapSalesDataRecord();
                BeanUtils.copyProperties(sapSalesDataRecord, sapSalesDataRecordXs);
                sapSalesDataRecordXs.setSumType(OcBSapSalesDataRecordConstant.SUM_TYPE_XS);
                fillMergeCode(sapSalesDataRecordXs);
                relation = new OcBSapSalesDataRecordRelation();
                relation.setSapSalesDataRecord(sapSalesDataRecordXs);
                relation.setSapSalesDataRecordItems(sapSalesDataRecordItems);
                relations.add(relation);
            }
            ApplicationContextHandle.getBean(OcBSapSalesDataRecordService.class).saveWithTransactional(relations, user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Finish 全渠道订单出库生成销售数据信息 orderId:{}", "出库生成销售数据信息"), orderRelation.getOrderId());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("全渠道订单出库生成销售数据信息失败:异常信息:{}"), Throwables.getStackTraceAsString(ex));
            message = "生成失败," + ex.getMessage();
            success = false;
        } finally {
            omsOrderLogService.addUserOrderLog(orderRelation.getOrderId(), orderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.SAVE_SAP_SALE_DATA.getKey(), message, null, null, user);
        }
        return success;
    }

    /**
     * 奶卡退单
     */
    public boolean saveNaiKaRefundOrder(Long returnOrderId, User user) {
        String message = "成功";
        try {
            OcBReturnAfSend ocBReturnAfSend = afSendMapper.selectById(returnOrderId);
            if (Objects.isNull(ocBReturnAfSend)) {
                message = "奶卡退款单不存在 无需汇总";
                log.info(LogUtil.format("奶卡退款单不存在 无需汇总"));
                return true;
            }
            OrderBusinessTypeCodeEnum businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(ocBReturnAfSend.getBusinessTypeCode());
            if (Objects.isNull(businessTypeCodeEnum)
                    || !OcBSapSalesDataRecordConstant.SUM_TYPE_NK.equals(businessTypeCodeEnum.getSaleSumType())) {
                message = "非奶卡退款汇总";
                log.info("非奶卡汇总");
                return true;
            }
            if (!businessTypeCodeEnum.equals(OrderBusinessTypeCodeEnum.MILK_RETURN_ONLY)
                    && !businessTypeCodeEnum.equals(OrderBusinessTypeCodeEnum.CYCLE_RETURN_ONLY)
                    && !businessTypeCodeEnum.equals(OrderBusinessTypeCodeEnum.MIDDLE_CYCLE_RETURN_ONLY)) {
                message = "不符合奶卡汇总要求";
                log.info("不符合奶卡汇总要求,code:{}", businessTypeCodeEnum.getCode());
                return true;
            }
            List<OcBReturnAfSendItem> ocBReturnAfSendItems = omsReturnAfSendService.queryItemsToBeSendingACListByAfSendIds(Collections.singletonList(returnOrderId));
            if (CollectionUtils.isEmpty(ocBReturnAfSendItems)) {
                message = "奶卡退单无明细 无需汇总";
                log.info("奶卡退单无明细无需汇总");
                return true;
            }
            SgBSapInformationMapping sapInformationMapping = selectSapInformationMapping(String.valueOf(ocBReturnAfSend.getBusinessTypeId()));
            log.info("sapInformationMapping,map:{}", JSON.toJSONString(sapInformationMapping));
            OcBSapSalesDataRecordRelation relation = new OcBSapSalesDataRecordRelation();
            OcBSapSalesDataRecord sapSalesDataRecord = new OcBSapSalesDataRecord();
            List<OcBSapSalesDataRecordItem> sapSalesDataRecordItems = new ArrayList<>();
            sapSalesDataRecord.setId(-1L);
            sapSalesDataRecord.setBillNo(ocBReturnAfSend.getBillNo());
            sapSalesDataRecord.setBillType(OmsTypeEnum.RETUEN.getCode().toString());
            sapSalesDataRecord.setMiddlegroundBillType(ocBReturnAfSend.getBusinessTypeId() == null ? null : Integer.valueOf(ocBReturnAfSend.getBusinessTypeId().toString()));
            sapSalesDataRecord.setMiddlegroundBillTypeName(ocBReturnAfSend.getBusinessTypeName());
            sapSalesDataRecord.setMiddlegroundBillTypeCode(ocBReturnAfSend.getBusinessTypeCode());
            StringBuilder abnormalReason = new StringBuilder();
            if (sapInformationMapping == null) {
                abnormalReason.append("SAP单据类型为空;");
            } else {
                sapSalesDataRecord.setSapBillType(sapInformationMapping.getSapBillType());
            }
            sapSalesDataRecord.setSapBillType(ocBReturnAfSend.getBusinessTypeCode());
            CpShop shop = cpRpcService.selectCpCShopById(ocBReturnAfSend.getCpCShopId());
            // 店铺信息
            if (shop != null) {
                sapSalesDataRecord.setCpCShopId(shop.getCpCShopId().intValue());
                sapSalesDataRecord.setCpCShopEcode(shop.getEcode());
                sapSalesDataRecord.setCpCShopEname(shop.getCpCShopTitle());
                sapSalesDataRecord.setSalesOrganization(shop.getGeneralOrganizationCode());
                sapSalesDataRecord.setOrderCpCShopEname(shop.getCpCShopTitle());
                sapSalesDataRecord.setOrderCpCShopEcode(shop.getEcode());
                sapSalesDataRecord.setOrderCpCShopId(shop.getCpCShopId());
            }
            sapSalesDataRecord.setInTime(Optional.ofNullable(ocBReturnAfSend.getCheckTime()).orElse(ocBReturnAfSend.getReturnApplyTime()));
            if (Objects.isNull(sapSalesDataRecord.getInTime())) {
                sapSalesDataRecord.setInTime(ocBReturnAfSend.getCreationdate());
            }
            sapSalesDataRecord.setSumType(businessTypeCodeEnum.getSaleSumType());
            sapSalesDataRecord.setSumStatus(OcBSapSalesDataRecordConstant.SUM_STATUS_ZERO);
            // 构建子表
            for (OcBReturnAfSendItem ocBReturnAfSendItem : ocBReturnAfSendItems) {
                // 获取店铺信息
                CpStore store = null;
                Long storeId = orderMapper.queryStoreIdByBillNo(ocBReturnAfSendItem.getRelationBillNo());
                if (Objects.nonNull(storeId)) {
                    List<CpStore> storeList = cpRpcService.queryListByWarehouseIds(Arrays.asList(storeId));
                    if (CollectionUtils.isNotEmpty(storeList)) {
                        store = storeList.get(0);
                    }
                }
                OcBSapSalesDataRecordItem sapSalesDataRecordItem = new OcBSapSalesDataRecordItem();
                sapSalesDataRecordItem.setId(-1L);
                sapSalesDataRecordItem.setSku(ocBReturnAfSendItem.getPsCSkuEcode());
                ProductSku productSku = psRpcService.selectProductSku(ocBReturnAfSendItem.getPsCSkuEcode());
                if (ocBReturnAfSendItem.getQtyReturnApply() == null
                        || ocBReturnAfSendItem.getQtyReturnApply().stripTrailingZeros().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                sapSalesDataRecordItem.setQty(Integer.valueOf(ocBReturnAfSendItem.getQtyReturnApply().stripTrailingZeros().toPlainString()));
                if (productSku != null && productSku.getProAttributeMap() != null && productSku.getProAttributeMap().get("M_DIM3_ID") != null) {
                    sapSalesDataRecordItem.setUnit(productSku.getProAttributeMap().get("M_DIM3_ID").getEcode());
                }
                if (Objects.nonNull(store)) {
                    sapSalesDataRecordItem.setCpCStoreId(Integer.valueOf(store.getId().toString()));
                    sapSalesDataRecordItem.setCpCStoreEcode(store.getEcode());
                    sapSalesDataRecordItem.setCpCStoreEname(store.getEname());
                    sapSalesDataRecordItem.setFactoryCode(store.getWerks());
                }
                sapSalesDataRecordItem.setAmt(ocBReturnAfSendItem.getAmtHasReturn());
                sapSalesDataRecordItem.setProType("N");
                sapSalesDataRecordItems.add(sapSalesDataRecordItem);
            }
            sapSalesDataRecord.setFactoryCode(sapSalesDataRecordItems.get(0).getFactoryCode());
            if ((OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode())
                    || OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()))
                    && "3010".equals(sapSalesDataRecord.getFactoryCode())) {
                sapSalesDataRecord.setSalesOrganization("3000");
            }

            if ((OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode())
                    || OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()))
                    && "5210".equals(sapSalesDataRecord.getFactoryCode())) {
                sapSalesDataRecord.setSalesOrganization("5200");
            }

            if ((OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode())
                    || OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()))
                    && "6010".equals(sapSalesDataRecord.getFactoryCode())) {
                sapSalesDataRecord.setSalesOrganization("6000");
            }

            // 修改完销售组织后再查询成本中心
            CpCShopItem shopItem = cpRpcService.queryShopItem(sapSalesDataRecord.getCpCShopId().longValue(),
                    sapSalesDataRecord.getSalesOrganization());
            if (shopItem != null) {
                sapSalesDataRecord.setCostCenter(shopItem.getCostCenterDescription());
            }
            fillMergeCode(sapSalesDataRecord);
            relation.setSapSalesDataRecord(sapSalesDataRecord);
            relation.setSapSalesDataRecordItems(sapSalesDataRecordItems);
            List<Long> list = ApplicationContextHandle.getBean(OcBSapSalesDataRecordService.class).saveWithTransactional(Collections.singletonList(relation), user);
            if (NAIKA_SAP.contains(relation.getSapSalesDataRecord().getSapBillType())) {
                updateResidueQty(relation.getSapSalesDataRecord().getBillNo(), list.get(0), Boolean.TRUE);
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("退换货订单出库生成销售数据信息失败:异常信息:{}"), Throwables.getStackTraceAsString(ex));
            message = "生成失败," + ex.getMessage();
            return false;
        } finally {
            OcBReturnOrderLog returnOrderLog = returnInCommService.buildReturnOderLog("奶卡退款入库生成销售数据信息", message, returnOrderId, user);
            returnOrderLogMapper.insert(returnOrderLog);
        }
        return true;
    }

    public boolean saveRefundOrder(Long returnOrderId, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start 退换货订单出库生成销售数据信息 returnOrderId:{}", "入库生成销售数据信息"), returnOrderId);
        }
        String message = "成功";
        boolean success = true;
        OcBReturnOrder returnOrder = returnOrderMapper.selectByid(returnOrderId);
        List<OcBReturnOrderRefund> returnOrderRefundList = returnOrderRefundMapper.selectByOcOrderId(returnOrderId);
        try {
            OrderBusinessTypeCodeEnum businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(returnOrder.getBusinessTypeCode());
            if (OcBSapSalesDataRecordConstant.SUM_TYPE_IGNORE.equals(businessTypeCodeEnum.getSaleSumType())) {
                log.info(LogUtil.format("returnOrderId:{},业务类型:{},不符合生成条件", "入库生成销售数据信息"), returnOrder.getId(), businessTypeCodeEnum.getMassage());
                message = "无需生成";
                return success;
            }
            returnOrderRefundList = returnOrderRefundList.stream()
                    .filter(o -> o.getQtyIn() != null && o.getQtyIn().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(returnOrderRefundList)) {
                message = "无需生成,订单无有效退货明细";
                return success;
            }
            SgBSapInformationMapping sapInformationMapping = selectSapInformationMapping(String.valueOf(returnOrder.getBusinessTypeId()));
            List<OcBSapSalesDataRecordRelation> relations = new ArrayList<>();
            OcBSapSalesDataRecordRelation relation = new OcBSapSalesDataRecordRelation();
            OcBSapSalesDataRecord sapSalesDataRecord = new OcBSapSalesDataRecord();
            List<OcBSapSalesDataRecordItem> sapSalesDataRecordItems = new ArrayList<>();
            buildSapSalesDataRecordFromRefundOrder(returnOrder, returnOrderRefundList, sapInformationMapping, businessTypeCodeEnum, sapSalesDataRecord, sapSalesDataRecordItems);
            relation.setSapSalesDataRecord(sapSalesDataRecord);
            relation.setSapSalesDataRecordItems(sapSalesDataRecordItems);
            relations.add(relation);
            // 需要再插一条 销售汇总
            if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE
                    || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD
                    || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_RESET
                    || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_RESET) {
                OcBSapSalesDataRecord sapSalesDataRecordXs = new OcBSapSalesDataRecord();
                BeanUtils.copyProperties(sapSalesDataRecord, sapSalesDataRecordXs);
                sapSalesDataRecordXs.setSumType(OcBSapSalesDataRecordConstant.SUM_TYPE_XS);
                fillMergeCode(sapSalesDataRecordXs);
                relation = new OcBSapSalesDataRecordRelation();
                relation.setSapSalesDataRecord(sapSalesDataRecordXs);
                relation.setSapSalesDataRecordItems(sapSalesDataRecordItems);
                relations.add(relation);
            }
            ApplicationContextHandle.getBean(OcBSapSalesDataRecordService.class).saveWithTransactional(relations, user);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Finish 退换货订单出库生成销售数据信息 returnOrderId:{}", "入库生成销售数据信息"), returnOrder.getId());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("退换货订单出库生成销售数据信息失败:异常信息:{}"), Throwables.getStackTraceAsString(ex));
            message = "生成失败," + ex.getMessage();
            success = false;
        } finally {
            OcBReturnOrderLog returnOrderLog = returnInCommService.buildReturnOderLog("入库生成销售数据信息", message, returnOrder.getId(), user);
            returnOrderLogMapper.insert(returnOrderLog);
        }
        return success;
    }

    public SgBSapInformationMapping selectSapInformationMapping(String param) {
        ValueHolderV14<SgBSapInformationMapping> result = sgBSapInformationMappingR3Cmd.queryByMiddlegroundBillType(param);
        return result.getData();
    }

    public List<SgBSapInformationMapping> selectSapInformationMappingByEntity(SgBSapInformationMapping entity) {
        if (ObjectUtil.isNull(entity)) {
            return Collections.emptyList();
        }

        try {
            ValueHolderV14<List<SgBSapInformationMapping>> holderV14 = sgBSapInformationMappingR3Cmd.queryByEntity(entity);
            if (Objects.nonNull(holderV14)
                    && holderV14.isOK()
                    && Objects.nonNull(holderV14.getData())) {
                return holderV14.getData();
            }
        } catch (Exception e) {
            log.error(LogUtil.format("查询sap信息映射表报错，入参：{},异常:{}",
                            "OcBSapSalesDataRecordService.selectSapInformationMappingByEntity"),
                    entity, Throwables.getStackTraceAsString(e));
            throw e;
        }
        return Collections.emptyList();
    }

    public void buildSapSalesDataRecordFromOrder(OcBOrder order,
                                                 List<OcBOrderItem> orderItems,
                                                 SgBSapInformationMapping sapInformationMapping,
                                                 OrderBusinessTypeCodeEnum businessTypeCodeEnum,
                                                 OcBSapSalesDataRecord sapSalesDataRecord,
                                                 List<OcBSapSalesDataRecordItem> sapSalesDataRecordItems) {
        log.debug("Start buildSapSalesDataRecordFromOrder order:{},orderItems:{},sapInformationMapping:{},businessTypeCodeEnum:{}",
                JSON.toJSONString(order), JSON.toJSONString(orderItems), JSON.toJSONString(sapInformationMapping), businessTypeCodeEnum.getMassage());
        List<CpStore> storeList = cpRpcService.queryListByWarehouseIds(Arrays.asList(order.getCpCPhyWarehouseId()));
        StringBuilder abnormalReason = new StringBuilder();
        CpStore store = null;
        if (CollectionUtils.isNotEmpty(storeList)) {
            store = storeList.get(0);
        }

        sapSalesDataRecord.setId(-1L);
        sapSalesDataRecord.setBillNo(order.getBillNo());
        sapSalesDataRecord.setBillType(OmsTypeEnum.ORDER.getCode().toString());
        sapSalesDataRecord.setMiddlegroundBillType(order.getBusinessTypeId() == null ? null : Integer.valueOf(order.getBusinessTypeId().toString()));
        sapSalesDataRecord.setMiddlegroundBillTypeName(order.getBusinessTypeName());
        sapSalesDataRecord.setMiddlegroundBillTypeCode(order.getBusinessTypeCode());
        if (sapInformationMapping == null) {
            abnormalReason.append("SAP单据类型为空;");
        } else {
            sapSalesDataRecord.setSapBillType(sapInformationMapping.getSapBillType());
        }
        Integer shopId = order.getCpCShopId() == null ? null : Integer.valueOf(order.getCpCShopId().toString());
        //判断店铺是否在集合店商品结算映射有设置结算关系(店铺取值)
        if (order.getCpCShopId() != null) {
            ValueHolderV14<List<PsCCollShopProMapping>> result = psCCollShopProMappingQueryCmd.queryByShopId(order.getCpCShopId());
            List<PsCCollShopProMapping> proMappingList = result.getData();
            if (CollectionUtils.isNotEmpty(proMappingList)) {
                List<Long> skuInfos = orderItems.stream().map(OcBOrderItem::getPsCSkuId).distinct().collect(Collectors.toList());
                ValueHolderV14<List<PsCCollShopProMapping>> resultProSku = psCCollShopProMappingQueryCmd.queryProMappingByShopIdAndSku(order.getCpCShopId(), skuInfos);
                List<PsCCollShopProMapping> skuProList = resultProSku.getData();
                if (CollectionUtils.isNotEmpty(skuProList)) {
                    if (skuProList.size() == skuInfos.size()) {
                        shopId = proMappingList.get(0).getCpCShopChildId().intValue();
                    } else {
                        abnormalReason.append("商品结算关系设置不正确");
                    }
                }
            }

        }

        // 以下几种类型取原单店铺ID（GW_SOURCE_CODE）
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_PICK_UP
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER_PICK_UP
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_PICK_UP_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_PICK_UP_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE) {
            shopId = order.getGwSourceCode() == null ? null : Integer.valueOf(order.getGwSourceCode());
        }
        CpShop shop = null;
        CpShop orderShop = null;
        if (shopId != null) {
            shop = cpRpcService.selectCpCShopById(shopId.longValue());
            if (shop != null) {
                sapSalesDataRecord.setCpCShopId(shopId);
                sapSalesDataRecord.setCpCShopEcode(shop.getEcode());
                sapSalesDataRecord.setCpCShopEname(shop.getCpCShopTitle());
            }
            //赋值下单店铺
            if (shopId.equals(Integer.valueOf(order.getCpCShopId().toString()))) {
                orderShop = shop;
            } else {
                orderShop = cpRpcService.selectCpCShopById(order.getCpCShopId());
            }
            if (orderShop != null) {
                sapSalesDataRecord.setOrderCpCShopId(orderShop.getId());
                sapSalesDataRecord.setOrderCpCShopEcode(orderShop.getEcode());
                sapSalesDataRecord.setOrderCpCShopEname(orderShop.getEcode());
            }
        }
        OcBOrder origOrder = null;
        // 订单业务类型为【免费奶卡提货、免费奶卡提货补发、免费奶卡提货（补发）退货】+免费奶卡周期购提货&奶卡提货，则查询原奶卡销售订单
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE_RETURN) {
            origOrder = getOrigOrderByPickUpOrder(businessTypeCodeEnum, order);
        }
        fillSalesOrganization(origOrder, shop, sapSalesDataRecord, sapInformationMapping, businessTypeCodeEnum);
        if (StringUtils.isBlank(sapSalesDataRecord.getSalesOrganization())) {
            abnormalReason.append("销售组织为空;");
        }
        sapSalesDataRecord.setInTime(order.getScanTime());
        sapSalesDataRecord.setSumType(businessTypeCodeEnum.getSaleSumType());
        sapSalesDataRecord.setSumStatus(OcBSapSalesDataRecordConstant.SUM_STATUS_ZERO);
        LineCategoryEnum lineCategoryEnum = getLineCategory(order, origOrder, businessTypeCodeEnum);
        // 构建子表
        for (OcBOrderItem orderItem : orderItems) {
            OcBSapSalesDataRecordItem sapSalesDataRecordItem = new OcBSapSalesDataRecordItem();
            sapSalesDataRecordItem.setId(-1L);
            sapSalesDataRecordItem.setSku(orderItem.getPsCSkuEcode());
            if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS
                    || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP
                    || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_PICK_UP) {
                if (BigDecimal.ZERO.compareTo(orderItem.getRealAmt()) == 0) {
                    sapSalesDataRecordItem.setLineType(OmsParamConstant.ZERO);
                } else {
                    sapSalesDataRecordItem.setLineType(OmsParamConstant.ONE);
                }
            }
            // 周期购提数 -订单业务类型为【奶卡周期购订单&免费奶卡周期购订单
            if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_ORDER
                    || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER) {
                sapSalesDataRecordItem.setCycleQty(orderItem.getCycleQty());
            }
            sapSalesDataRecordItem.setLineCategory(lineCategoryEnum.getSaleCode());
            ProductSku productSku = psRpcService.selectProductSku(orderItem.getPsCSkuEcode());
            sapSalesDataRecordItem.setQty(Integer.valueOf(orderItem.getQty().stripTrailingZeros().toPlainString()));
            if (productSku != null && productSku.getProAttributeMap() != null && productSku.getProAttributeMap().get("M_DIM3_ID") != null) {
                sapSalesDataRecordItem.setUnit(productSku.getProAttributeMap().get("M_DIM3_ID").getEcode());
            }
            if (store != null) {
                sapSalesDataRecordItem.setCpCStoreId(Integer.valueOf(store.getId().toString()));
                sapSalesDataRecordItem.setCpCStoreEcode(store.getEcode());
                sapSalesDataRecordItem.setCpCStoreEname(store.getEname());
                sapSalesDataRecordItem.setFactoryCode(store.getWerks());
            }
            sapSalesDataRecordItem.setAmt(orderItem.getRealAmt());
            sapSalesDataRecordItem.setProType(orderItem.getIsGift() == null ? "N" : (orderItem.getIsGift() == 1 ? "Y" : "N"));
            sapSalesDataRecordItems.add(sapSalesDataRecordItem);
        }
        sapSalesDataRecord.setFactoryCode(sapSalesDataRecordItems.get(0).getFactoryCode());
        if ((OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.MILK_CARD_RESET.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()))
                && "3010".equals(sapSalesDataRecord.getFactoryCode())) {
            sapSalesDataRecord.setSalesOrganization("3000");
        }

        if ((OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.MILK_CARD_RESET.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()))
                && "5210".equals(sapSalesDataRecord.getFactoryCode())) {
            sapSalesDataRecord.setSalesOrganization("5200");
        }

        if ((OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.MILK_CARD_RESET.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()))
                && "6010".equals(sapSalesDataRecord.getFactoryCode())) {
            sapSalesDataRecord.setSalesOrganization("6000");
        }

        //中台周期购订单成本金额计算
        if (OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode())) {
            List<OcBOrder> orderList = orderMapper.selectBySourceCode(order.getTid());
            //业务类型为RYCK08,非已取消或者系统作废的订单
            List<OcBOrder> pickedOrders = orderList.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(p.getBusinessTypeCode())
                    && !OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus()) && !OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pickedOrders)) {
                //明细
                List<Long> orderIds = pickedOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());
                List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItemsByOrderIds(orderIds);
                getPriceCostListForRecordItem(sapSalesDataRecordItems, ocBOrderItems, null);
            }
        }

        //中台周期购提货提货金额计算
        if (OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode())) {
            //周期购额外信息
            List<OcBOrderCycleBuyInfo> cycleBuyInfos = orderCycleBuyInfoMapper.getByBillNo(order.getBillNo());
            Map<String, List<OcBOrderCycleBuyInfo>> cycleInfoMap = cycleBuyInfos.stream().collect(Collectors.groupingBy(OcBOrderCycleBuyInfo::getSkuCode));
            for (OcBSapSalesDataRecordItem sapSalesDataRecordItem : sapSalesDataRecordItems) {
                pickAmt(cycleInfoMap, sapSalesDataRecordItem);
            }
        }

        //修改销售组织之后再查成本中心
        fillCostCenter(origOrder, sapSalesDataRecord, sapInformationMapping, businessTypeCodeEnum);
        if (sapInformationMapping != null
                && "Y".equals(sapInformationMapping.getIsTransmissionCostCenter())
                && StringUtils.isBlank(sapSalesDataRecord.getCostCenter())) {
            abnormalReason.append("成本中心数据为空;");
        }
        if (abnormalReason.length() > 0) {
            sapSalesDataRecord.setAbnormalReason(abnormalReason.toString());
        }
        fillMergeCode(sapSalesDataRecord);
        log.debug("Finish buildSapSalesDataRecordFromOrder sapSalesDataRecord:{},sapSalesDataRecordItems:{}", JSON.toJSONString(sapSalesDataRecord), JSON.toJSONString(sapSalesDataRecordItems));
    }

    /**
     * 销售数据提货金额计算
     *
     * @param cycleInfoMap
     * @param sapSalesDataRecordItem
     */
    private void pickAmt(Map<String, List<OcBOrderCycleBuyInfo>> cycleInfoMap, OcBSapSalesDataRecordItem sapSalesDataRecordItem) {
        String skuCode = sapSalesDataRecordItem.getSku();
        String proType = sapSalesDataRecordItem.getProType();
        BigDecimal pickAmt = null;

        List<OcBOrderCycleBuyInfo> cycleBuyInfoList = cycleInfoMap.get(skuCode);
        if (CollectionUtils.isNotEmpty(cycleBuyInfoList)) {
            if (YesNoEnum.Y.getKey().equals(proType)) {
                //获取所有赠品的提货金额
                List<OcBOrderCycleBuyInfo> giftPickAmt = cycleBuyInfoList.stream().filter(p -> YesNoEnum.Y.getVal().equals(p.getIsGift())).collect(Collectors.toList());
                //获取所有赠品提货金额之和
                if (CollectionUtils.isNotEmpty(giftPickAmt)) {
                    pickAmt = giftPickAmt.stream().map(OcBOrderCycleBuyInfo::getPickGoodsAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
            } else {
                //获取所有非赠品的提货金额
                List<OcBOrderCycleBuyInfo> noGiftPickAmt = cycleBuyInfoList.stream().filter(p -> YesNoEnum.N.getVal().equals(p.getIsGift())).collect(Collectors.toList());
                //获取所有非赠品提货金额之和
                if (CollectionUtils.isNotEmpty(noGiftPickAmt)) {
                    pickAmt = noGiftPickAmt.stream().map(OcBOrderCycleBuyInfo::getPickGoodsAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
            }
            if (pickAmt != null) {
                sapSalesDataRecordItem.setAmt(pickAmt);
            }
        }
    }

    public void buildSapSalesDataRecordFromRefundOrder(OcBReturnOrder returnOrder,
                                                       List<OcBReturnOrderRefund> returnOrderRefundList,
                                                       SgBSapInformationMapping sapInformationMapping,
                                                       OrderBusinessTypeCodeEnum businessTypeCodeEnum,
                                                       OcBSapSalesDataRecord sapSalesDataRecord,
                                                       List<OcBSapSalesDataRecordItem> sapSalesDataRecordItems) {
        CpStore store = cpRpcService.selectCpCStoreById(returnOrder.getCpCStoreId());
        AssertUtils.notNull(store, "逻辑仓" + returnOrder.getCpCStoreId() + "查询为空");
        OcBOrder order = orderMapper.selectById(returnOrder.getOrigOrderId());
        AssertUtils.notNull(store, "原单" + returnOrder.getOrigOrderId() + "不存在！");
        StringBuilder abnormalReason = new StringBuilder();
        sapSalesDataRecord.setId(-1L);
        sapSalesDataRecord.setBillNo(returnOrder.getBillNo());
        sapSalesDataRecord.setBillType(OmsTypeEnum.RETUEN.getCode().toString());
        sapSalesDataRecord.setMiddlegroundBillType(returnOrder.getBusinessTypeId() == null ? null : Integer.valueOf(returnOrder.getBusinessTypeId().toString()));
        sapSalesDataRecord.setMiddlegroundBillTypeName(returnOrder.getBusinessTypeName());
        sapSalesDataRecord.setMiddlegroundBillTypeCode(returnOrder.getBusinessTypeCode());
        if (sapInformationMapping == null) {
            abnormalReason.append("SAP单据类型为空;");
        } else {
            sapSalesDataRecord.setSapBillType(sapInformationMapping.getSapBillType());
        }
        Integer shopId = returnOrder.getCpCShopId() == null ? null : Integer.valueOf(returnOrder.getCpCShopId().toString());
        if (returnOrder.getCpCShopId() != null) {
            ValueHolderV14<List<PsCCollShopProMapping>> result = psCCollShopProMappingQueryCmd.queryByShopId(returnOrder.getCpCShopId());
            List<PsCCollShopProMapping> proMappingList = result.getData();
            if (CollectionUtils.isNotEmpty(proMappingList)) {
                List<Long> skuInfos = returnOrderRefundList.stream().map(OcBReturnOrderRefund::getPsCSkuId).distinct().collect(Collectors.toList());
                ValueHolderV14<List<PsCCollShopProMapping>> resultProSku = psCCollShopProMappingQueryCmd.queryProMappingByShopIdAndSku(returnOrder.getCpCShopId(), skuInfos);
                List<PsCCollShopProMapping> skuProList = resultProSku.getData();
                if (CollectionUtils.isNotEmpty(skuProList)) {
                    if (skuProList.size() == skuInfos.size()) {
                        shopId = proMappingList.get(0).getCpCShopChildId().intValue();
                    } else {
                        abnormalReason.append("商品结算关系设置不正确");
                    }
                }
            }
        }

        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_PICK_UP_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_PICK_UP_REISSUE_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_PICK_UP_REISSUE_RETURN) {
            shopId = order.getGwSourceCode() == null ? null : Integer.valueOf(order.getGwSourceCode());
        }
        CpShop shop = null;
        CpShop orderShop = null;
        if (shopId != null) {
            shop = cpRpcService.selectCpCShopById(shopId.longValue());
            if (shop != null) {
                sapSalesDataRecord.setCpCShopId(shopId);
                sapSalesDataRecord.setCpCShopEcode(shop.getEcode());
                sapSalesDataRecord.setCpCShopEname(shop.getCpCShopTitle());
            }
            //赋值下单店铺
            if (shopId.equals(Integer.valueOf(order.getCpCShopId().toString()))) {
                orderShop = shop;
            } else {
                orderShop = cpRpcService.selectCpCShopById(order.getCpCShopId());
            }
            if (orderShop != null) {
                sapSalesDataRecord.setOrderCpCShopId(orderShop.getId());
                sapSalesDataRecord.setOrderCpCShopEcode(orderShop.getEcode());
                sapSalesDataRecord.setOrderCpCShopEname(orderShop.getEcode());
            }
        }
        OcBOrder origOrder = null;
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE_RETURN) {
            origOrder = getOrigOrderByPickUpOrder(businessTypeCodeEnum, order);
        }
        fillSalesOrganization(origOrder, shop, sapSalesDataRecord, sapInformationMapping, businessTypeCodeEnum);
        if (StringUtils.isBlank(sapSalesDataRecord.getSalesOrganization())) {
            abnormalReason.append("销售组织为空;");
        }
        sapSalesDataRecord.setInTime(returnOrder.getInTime());
        sapSalesDataRecord.setSumType(businessTypeCodeEnum.getSaleSumType());
        sapSalesDataRecord.setSumStatus(OcBSapSalesDataRecordConstant.SUM_STATUS_ZERO);
        LineCategoryEnum lineCategoryEnum = getLineCategory(order, origOrder, null);
        // 构建子表
        for (OcBReturnOrderRefund returnOrderRefund : returnOrderRefundList) {
            OcBSapSalesDataRecordItem sapSalesDataRecordItem = new OcBSapSalesDataRecordItem();
            sapSalesDataRecordItem.setId(-1L);
            sapSalesDataRecordItem.setSku(returnOrderRefund.getPsCSkuEcode());
            sapSalesDataRecordItem.setLineCategory(lineCategoryEnum.getReturnCode());
            ProductSku productSku = psRpcService.selectProductSku(returnOrderRefund.getPsCSkuEcode());
            if (returnOrderRefund.getQtyIn() == null || returnOrderRefund.getQtyIn().stripTrailingZeros().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            sapSalesDataRecordItem.setQty(Integer.valueOf(returnOrderRefund.getQtyIn().stripTrailingZeros().toPlainString()));
            if (productSku != null && productSku.getProAttributeMap() != null && productSku.getProAttributeMap().get("M_DIM3_ID") != null) {
                sapSalesDataRecordItem.setUnit(productSku.getProAttributeMap().get("M_DIM3_ID").getEcode());
            }
            sapSalesDataRecordItem.setCpCStoreId(Integer.valueOf(store.getId().toString()));
            sapSalesDataRecordItem.setCpCStoreEcode(store.getEcode());
            sapSalesDataRecordItem.setCpCStoreEname(store.getEname());
            sapSalesDataRecordItem.setFactoryCode(store.getWerks());
            sapSalesDataRecordItem.setAmt(returnOrderRefund.getAmtRefund());
            sapSalesDataRecordItem.setProType(returnOrderRefund.getGiftType() == null ? "N" : ("0".equals(returnOrderRefund.getGiftType()) ? "N" : "Y"));
            sapSalesDataRecordItems.add(sapSalesDataRecordItem);
        }
        sapSalesDataRecord.setFactoryCode(sapSalesDataRecordItems.get(0).getFactoryCode());
        if ((OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()))
                && "3010".equals(sapSalesDataRecord.getFactoryCode())) {
            sapSalesDataRecord.setSalesOrganization("3000");
        }

        if ((OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()))
                && "5210".equals(sapSalesDataRecord.getFactoryCode())) {
            sapSalesDataRecord.setSalesOrganization("5200");
        }

        if ((OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.AFTER_SALES_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()) ||
                OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE_RETURN.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode()))
                && "6010".equals(sapSalesDataRecord.getFactoryCode())) {
            sapSalesDataRecord.setSalesOrganization("6000");
        }

        //中台周期购成本金额计算
        if (OrderBusinessTypeCodeEnum.MIDDLE_CYCLE_RETURN_ONLY.getCode().equals(sapSalesDataRecord.getMiddlegroundBillTypeCode())) {
            List<OcBOrder> orderList = orderMapper.selectBySourceCode(order.getTid());
            //业务类型为RYCK08,已取消
            List<OcBOrder> pickedOrders = orderList.stream().filter(p -> OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(p.getBusinessTypeCode())
                    && OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(pickedOrders)) {
                //明细
                List<Long> orderIds = pickedOrders.stream().map(OcBOrder::getId).collect(Collectors.toList());
                List<OcBOrderItem> ocBOrderItems = orderItemMapper.selectOrderItemsByOrderIds(orderIds);

                //周期购物料所有商品信息
                List<PsCPro> proList = psRpcService.queryByDim2Ecode("10802");
                if (CollectionUtils.isNotEmpty(proList)) {
                    List<String> cycleSkuCodes = proList.stream().map(PsCPro::getEcode).collect(Collectors.toList());
                    getPriceCostListForRecordItem(sapSalesDataRecordItems, ocBOrderItems, cycleSkuCodes);
                }
            }
        }

        //修改完销售组织编码后再查成本中心
        fillCostCenter(origOrder, sapSalesDataRecord, sapInformationMapping, businessTypeCodeEnum);
        if (sapInformationMapping != null
                && "Y".equals(sapInformationMapping.getIsTransmissionCostCenter())
                && StringUtils.isBlank(sapSalesDataRecord.getCostCenter())) {
            abnormalReason.append("成本中心数据为空;");
        }
        if (abnormalReason.length() > 0) {
            sapSalesDataRecord.setAbnormalReason(abnormalReason.toString());
        }
        fillMergeCode(sapSalesDataRecord);
    }

    /**
     * 中台周期购计算成本
     *
     * @param sapSalesDataRecordItems
     */
    private void getPriceCostListForRecordItem(List<OcBSapSalesDataRecordItem> sapSalesDataRecordItems, List<OcBOrderItem> ocBOrderItems, List<String> cycleSkuCodes) {
        Set<String> skuCodes = ocBOrderItems.stream().map(OcBOrderItem::getPsCSkuEcode).collect(Collectors.toSet());
        List<PsCPro> proList = psRpcService.queryProByEcodes(Lists.newArrayList(skuCodes));
        log.info("getPriceCostListForRecordItem skuCodes:{}, proList:{}", skuCodes, JSON.toJSONString(proList));
        Map<String, PsCPro> psCProMap = proList.stream().collect(Collectors.toMap(PsCPro::getEcode, x -> x, (a, b) -> a));

        BigDecimal cost = BigDecimal.ZERO;
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            String psCSkuEcode = ocBOrderItem.getPsCSkuEcode();
            if (CollectionUtils.isNotEmpty(cycleSkuCodes) && !cycleSkuCodes.contains(psCSkuEcode)) {
                continue;
            }

            BigDecimal qty = ocBOrderItem.getQty();

            //商品信息-标准成本价
            PsCPro psCPro = psCProMap.get(psCSkuEcode);
            BigDecimal priceCostList = psCPro == null ? null : psCPro.getPriceCostList();
            if (priceCostList == null) {
                priceCostList = BigDecimal.ZERO;
            }

            //计算成本
            cost = cost.add(priceCostList.multiply(qty).setScale(4, BigDecimal.ROUND_HALF_UP));
        }

        for (OcBSapSalesDataRecordItem sapSalesDataRecordItem : sapSalesDataRecordItems) {
            sapSalesDataRecordItem.setPriceCost(cost);
        }
    }

    /**
     * 填充销售组织
     * 1.销售数据记录表中，单据类型为【免费奶卡提货、免费奶卡周期购提货】，根据奶卡卡号查询原奶卡的销售订单，如类型为【SAP奶卡赠送出库】，则取原单的销售组织；
     * 单据类型为【奶卡提货】，根据奶卡卡号查询原奶卡的销售订单，如类型为【SAP奶卡线下出库】或【SAP员工内购（奶卡）】，则取原单的销售组织；
     * 2.其余情况取店铺的销售组织
     *
     * @param origOrder             原单
     * @param shop                  店铺
     * @param sapSalesDataRecord    销售记录
     * @param sapInformationMapping sap单据类型映射信息
     * @param businessTypeCodeEnum  当前订单的业务类型
     */
    private void fillSalesOrganization(OcBOrder origOrder,
                                       CpShop shop,
                                       OcBSapSalesDataRecord sapSalesDataRecord,
                                       SgBSapInformationMapping sapInformationMapping,
                                       OrderBusinessTypeCodeEnum businessTypeCodeEnum) {
        boolean isNeedGetOrig = false;
        OrderBusinessTypeCodeEnum origBusinessTypeCodeEnum = null;
        if (origOrder != null) {
            origBusinessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(origOrder.getBusinessTypeCode());
        }
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_RETURN) {
            if (origBusinessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_GIVE) {
                isNeedGetOrig = true;
            }
        }
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE_RETURN) {
            if (origBusinessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_MILK_CARD
                    || origBusinessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_UNLINE_MILK_CARD) {
                isNeedGetOrig = true;
            }
        }
        if (isNeedGetOrig) {
            if (origOrder.getSalesOrganizationId() != null) {
                CpCSupplier supplier = cpRpcService.queryCompanyById(origOrder.getSalesOrganizationId());
                if (supplier != null) {
                    sapSalesDataRecord.setSalesOrganization(supplier.getEcode());
                }
            }
            return;
        }
        if (sapInformationMapping == null || shop == null) {
            return;
        }
        if (SapSalesDateConstant.SALES_ORGANIZATION_FREE.equals(sapInformationMapping.getSalesOrganization())) {
            sapSalesDataRecord.setSalesOrganization(shop.getFreeOrganizationCode());
        } else if (SapSalesDateConstant.SALES_ORGANIZATION_GENERAL.equals(sapInformationMapping.getSalesOrganization())) {
            sapSalesDataRecord.setSalesOrganization(shop.getGeneralOrganizationCode());
        }
    }

    private void fillMergeCode(OcBSapSalesDataRecord sapSalesDataRecord) {
        // 无异常原因生成合包码
        if (StringUtils.isEmpty(sapSalesDataRecord.getAbnormalReason())) {
            String mergeCode = sapSalesDataRecord.getSumType()
                    + sapSalesDataRecord.getMiddlegroundBillTypeName()
                    + sapSalesDataRecord.getCpCShopId()
                    + (sapSalesDataRecord.getCostCenter() == null ? "" : sapSalesDataRecord.getCostCenter())
                    + (sapSalesDataRecord.getSalesOrganization() == null ? "" : sapSalesDataRecord.getSalesOrganization())
                    + DateUtil.format(sapSalesDataRecord.getInTime(), DateUtil.dateNumberFormatter.getPattern())
                    + sapSalesDataRecord.getFactoryCode();
            sapSalesDataRecord.setMergeCode(MD5Util.encryptByMD5(mergeCode));
        } else {
            sapSalesDataRecord.setMergeCode("");
        }
    }

    private void fillCostCenter(OcBOrder origOrder, OcBSapSalesDataRecord sapSalesDataRecord, SgBSapInformationMapping sapInformationMapping, OrderBusinessTypeCodeEnum businessTypeCodeEnum) {
        if (sapInformationMapping == null) {
            return;
        }
        if (!"Y".equals(sapInformationMapping.getIsTransmissionCostCenter())) {
            return;
        }
        // 订单业务类型为【免费奶卡提货、免费奶卡提货补发、免费奶卡提货（补发）退货】，则查询原奶卡销售订单
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_RETURN) {
            if (origOrder == null) {
                return;
            }
            // 如原单类型为【SAP奶卡赠送出库】 则取原单的成本中心
            OrderBusinessTypeCodeEnum origBusinessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(origOrder.getBusinessTypeCode());
            if (origBusinessTypeCodeEnum == OrderBusinessTypeCodeEnum.SAP_GIVE) {
                Long costCenterId = origOrder.getCostCenterId();
                if (costCenterId == null) {
                    return;
                }
                Map<String, Object> costCenter = cpRpcService.findStoredimItemById(costCenterId);
                if (costCenter == null || costCenter.isEmpty()) {
                    return;
                }
                sapSalesDataRecord.setCostCenter((String) costCenter.get("ECODE"));
                return;
            }
        }
        if (sapSalesDataRecord.getCpCShopId() == null || StringUtils.isBlank(sapSalesDataRecord.getSalesOrganization())) {
            return;
        }
        CpCShopItem shopItem = cpRpcService.queryShopItem(sapSalesDataRecord.getCpCShopId().longValue(), sapSalesDataRecord.getSalesOrganization());
        if (shopItem != null) {
            sapSalesDataRecord.setCostCenter(shopItem.getCostCenterDescription());
        }
    }

    public LineCategoryEnum getLineCategory(OcBOrder order, OcBOrder origOrder, OrderBusinessTypeCodeEnum businessTypeCodeEnum) {
        if (businessTypeCodeEnum == null) {
            businessTypeCodeEnum = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(order.getBusinessTypeCode());
        }
        //单据类型为【线上免费奶卡、免费奶卡周期购订单、免费奶卡周期购提货】，此字段赋值为Z5+免费奶卡补发
        //单据类型为【线上奶卡销售、电子奶卡销售、奶卡周期购订单】，此字段赋值为Z7+奶卡补发
        //单据类型为【免费奶卡提货、免费奶卡提货补发、免费奶卡提货（补发）退货】，根据奶卡卡号查询原奶卡的销售订单，如如类型为【SAP奶卡赠送出库】，则取原单的计划行类别，如原单为其他类型，则此字段赋值为Z5
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER_PICK_UP
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_RESET) {
            return LineCategoryEnum.GIFT;
        }
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.VIRTUAL_MILK_CARD
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.CYCLE_ORDER
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_RESET) {
            return LineCategoryEnum.MATERIAL_CONSUMPTION;
        }
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_RETURN) {
            if (origOrder != null && OrderBusinessTypeCodeEnum.SAP_GIVE.getCode().equals(origOrder.getBusinessTypeCode())) {
                List<OcBOrderItem> origOrderItems = orderItemMapper.selectAllOrderItem(origOrder.getId());
                if (CollectionUtils.isNotEmpty(origOrderItems)) {
                    return LineCategoryEnum.getLineCategoryEnumBySaleCode(origOrderItems.get(0).getPlanLineCategory());
                }
            } else {
                return LineCategoryEnum.GIFT;
            }
        }
        return LineCategoryEnum.NONE;
    }

    /**
     * 根据奶卡提货订单查询原单
     *
     * @param businessTypeCodeEnum 当前订/退单业务类型
     * @param order                当前订单
     * @return 原奶卡订单
     */
    public OcBOrder getOrigOrderByPickUpOrder(OrderBusinessTypeCodeEnum businessTypeCodeEnum, OcBOrder order) {
        Long pickUpOrderId = null;
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER_PICK_UP
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_PICK_UP_REISSUE
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE
        ) {
            pickUpOrderId = order.getId();
        }
        // [提货补发退] 获取提货订单
        if (businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.FREE_CYCLE_PICK_UP_REISSUE_RETURN
                || businessTypeCodeEnum == OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE_RETURN) {
            List<OcBOrder> orderList = orderMapper.selectBySourceCode(order.getTid());
            orderList = orderList.stream().filter(o -> OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS.getCode().equals(o.getBusinessTypeCode())
                    || OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER_PICK_UP.getCode().equals(o.getBusinessTypeCode())
                    || OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS.getCode().equals(o.getBusinessTypeCode())
                    || OrderBusinessTypeCodeEnum.FREE_MILK_CARD_PICK_UP_GOODS_REISSUE.getCode().equals(o.getBusinessTypeCode())
                    || OrderBusinessTypeCodeEnum.FREE_CYCLE_PICK_UP_REISSUE.getCode().equals(o.getBusinessTypeCode())
                    || OrderBusinessTypeCodeEnum.MILK_CARD_PICK_UP_GOODS_REISSUE.getCode().equals(o.getBusinessTypeCode())
            ).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(orderList)) {
                pickUpOrderId = orderList.get(0).getId();
            }
        }
        if (pickUpOrderId == null) {
            return null;
        }
        // 根据订单ID获取奶卡卡号
        List<OcBOrderNaiKa> orderNaiKaList = orderNaiKaMapper.selectNaiKaListByOrderId(pickUpOrderId);
        if (CollectionUtils.isNotEmpty(orderNaiKaList)) {
            String cardCode = orderNaiKaList.get(0).getCardCode();
            if (StringUtils.isNotBlank(cardCode)) {
                // 根据奶卡卡号查询非提货订单ID，理论上只有一条
                List<OcBOrderNaiKa> naiKaList = orderNaiKaMapper.selectBySourceCodebyGsi(cardCode);
                naiKaList = naiKaList.stream().filter(o -> !NaiKaTypeConstant.PICK_UP.equals(o.getBusinessType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(naiKaList)) {
                    return orderMapper.selectById(naiKaList.get(0).getOcBOrderId());
                }
            }

        }
        return null;
    }

    public String businessTypeName(Integer sgBAdjustPropId) {
        String businessTypeName = null;
        if (SgConstantsIF.SG_B_STO_ADJUST_PROP_155.equals(sgBAdjustPropId)) {
            businessTypeName = SgConstantsIF.SG_B_STO_ADJUST_PROP_BC;
        } else if (SgConstantsIF.SG_B_STO_ADJUST_PROP_64.equals(sgBAdjustPropId)) {
            businessTypeName = SgConstantsIF.SG_B_STO_ADJUST_PROP_WTJRK;
        } else {
            businessTypeName = SgConstantsIF.SG_B_STO_ADJUST_PROP_WTJCK;
        }
        return businessTypeName;
    }

    public QuerySession fixFieldValue(Long id, JSONObject mainJo, String tableName, String itemTableName, JSONArray itemArray, User user) {
        QuerySession session = new QuerySessionImpl(user);
        DefaultWebEvent event = new DefaultWebEvent("save", new HashMap(16));
        JSONObject param = new JSONObject();
        JSONObject fixC = new JSONObject();
        //主表字段
        fixC.put(tableName, mainJo);
        //子表表名为空不传
        if (!org.apache.commons.lang3.StringUtils.isEmpty(itemTableName)) {
            //明细字段
            fixC.put(itemTableName, itemArray);
        }
        param.put("fixcolumn", fixC);
        param.put("objid", id);
        param.put("table", tableName);
        log.info("fixFieldValue param:{}", param);
        event.put("param", param);
        session.setEvent(event);
        return session;
    }

    public String costCenter(String billNo, Integer sgBAdjustPropId, String factoryCode) throws Exception {
        if (SgConstantsIF.SG_B_STO_ADJUST_PROP_155.equals(sgBAdjustPropId)) {
            String costCenter = getAdjustOrderCostCenter(billNo);
            if (StringUtils.isNotBlank(costCenter) && StringUtils.isNotEmpty(factoryCode)) {
                // 判断成本中心跟工厂 前缀(第一位)是否一致
                String costCenterFirst = costCenter.substring(0, 1);
                String factoryCodeFirst = factoryCode.substring(0, 1);
                if (!costCenterFirst.equals(factoryCodeFirst)) {
                    // 不一致 则取映射表
                    String newCostCenter = COST_CENTER.get(costCenter);
                    if (StringUtils.isNotEmpty(newCostCenter)) {
                        return newCostCenter;
                    }
                }
            }
            return costCenter;
        }
        return "";
    }

    //    1.取包材调整单的来源单据编号，并查询来源单的业务类型：
//    2.当来源单据业务类型为：RYCK04、RYCK05、RYCK12、RYCK13、RYCK25、RYCK27、RYCK28、RYCK30时：
//    根据此订单的奶卡卡号信息去找这个订单的原奶卡销售订单：
//    2.1.若原奶卡销售订单成本中心不为空，则直接取原奶卡销售订单的成本中心，作为包材调整单的成本中心；
//    2.2.若原奶卡销售订单成本中心为空，则用原奶卡销售订单的“原单店铺”去查询店铺档案中的组织编码(免费)，再用免费组织编码去匹店铺档案的“成本中心ID”字段作为包材调整单的成本中心。
//    3.当来源单据业务类型不为：RYCK04、RYCK05、RYCK12、RYCK13、RYCK25、RYCK27、RYCK28、RYCK30时：
//    3.1.若来源单据有成本中心，直接取成本中心编码作为包材调整单的成本中心；
//    3.2.若来源单据无成本中心，则用原的“下单店铺（不是原单店铺！）”去查询店铺档案中的组织编码(免费)，再用免费组织编码去匹店铺档案的“成本中心ID”字段作为包材调整单的成本中心。
    private String getAdjustOrderCostCenter(String billNo) {
        OcBOrder ocBOrder = ocBOrderMapper.queryOcBOrderInfo(billNo);
        if (ocBOrder == null) {
            AssertUtils.logAndThrow("查询零售发货单信息异常");
        }
        if (StringUtils.isEmpty(ocBOrder.getBusinessTypeCode())) {
            return "";
        }
        // 提奶的话 需要找原奶卡订单
        if (PICKUP.contains(ocBOrder.getBusinessTypeCode())) {
            // 根据订单ID获取奶卡卡号
            List<OcBOrderNaiKa> orderNaiKaList = orderNaiKaMapper.selectNaiKaListByOrderId(ocBOrder.getId());
            if (CollectionUtils.isNotEmpty(orderNaiKaList)) {
                String cardCode = orderNaiKaList.get(0).getCardCode();
                if (StringUtils.isNotBlank(cardCode)) {
                    // 根据奶卡卡号查询非提货订单ID，理论上只有一条
                    List<OcBOrderNaiKa> naiKaList = orderNaiKaMapper.selectBySourceCodebyGsi(cardCode);
                    naiKaList = naiKaList.stream().filter(o -> !NaiKaTypeConstant.PICK_UP.equals(o.getBusinessType())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(naiKaList)) {
                        OcBOrder ocBOrder1 = ocBOrderMapper.selectByID(naiKaList.get(0).getOcBOrderId());
                        if (ObjectUtil.isNull(ocBOrder1)) {
                            return "";
                        }
                        return getCostCenterCodeByOrder(ocBOrder1);
                    }
                }
            }
        } else {
            return getCostCenterCodeByOrder(ocBOrder);
        }
        return "";
    }

    private String getCostCenterCodeByOrder(OcBOrder ocBOrder) {
        if (ObjectUtil.isNotNull(ocBOrder.getCostCenterId())) {
            // 根据成本中心ID获取成本中心编码
            Map<String, Object> storeDimItemMap = cpRpcService.findStoredimItemById(ocBOrder.getCostCenterId());
            if (ObjectUtil.isNotNull(storeDimItemMap)) {
                return String.valueOf(storeDimItemMap.get("ECODE"));
            }
        }
        // 如果原单成本中心为空 则获取店铺档案上免费组织的成本中心
        try {
            ValueHolderV14 valueHolderV14 = sapShopApiCmd.searchQueryCostCenter(ocBOrder.getCpCShopId());
            if (valueHolderV14.isOK()) {
                return valueHolderV14.getData().toString();
            }
        } catch (Exception e) {
            log.error("searchQueryCostCenter error", e);
        }
        return "";
    }

    public String mergeCode(Date inTime, Integer sgBAdjustPropId, Long cpCStoreId, String costCenter, String factoryCode) {
        String mergeCode = DateUtil.format(inTime, DateUtil.dateNumberFormatter.getPattern()) + sgBAdjustPropId + cpCStoreId + costCenter + factoryCode;
        return MD5Util.encryptByMD5(mergeCode);
    }

    public CpStore getStore(Long cpStoreId) {
        return cpRpcService.selectCpCStoreById(cpStoreId);
    }

    public String sumType(Integer sgBAdjustPropId) {
        String sumType;
        if (SgConstantsIF.SG_B_STO_ADJUST_PROP_155.equals(sgBAdjustPropId)) {
            sumType = OcBSapSalesDataRecordConstant.SUM_TYPE_BCTZ;
        } else if (SgConstantsIF.SG_B_STO_ADJUST_PROP_64.equals(sgBAdjustPropId)) {
            sumType = OcBSapSalesDataRecordConstant.SUM_TYPE_WMJ;
        } else {
            sumType = OcBSapSalesDataRecordConstant.SUM_TYPE_CWMJ;
        }
        return sumType;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<Long> saveWithTransactional(List<OcBSapSalesDataRecordRelation> relations, User user) {
        List<Long> list = Lists.newArrayList();
        for (OcBSapSalesDataRecordRelation relation : relations) {
            ValueHolder vh = sapSalesDataRecordSaveService.save(relation.getSapSalesDataRecord(), relation.getSapSalesDataRecordItems(), -1L, user);
            AssertUtils.cannot(!vh.isOK(), (String) vh.get(R3ParamConstants.MESSAGE));
            Object id = vh.get("id");
            if (!Objects.isNull(id)) {
                list.add((Long) id);
            }
        }
        return list;
    }

    /**
     * 查询奶卡剩余提数
     *
     * @param billNo
     * @param recordId
     */
    public void updateResidueQty(String billNo, Long recordId, Boolean updateResidueQty) {
        //查询销售数据记录
//        OcBSapSalesDataRecord record = ocBSapS alesDataRecordMapper.selectById(recordId);
        List<OcBSapSalesDataRecordItem> recordItems = ocBSapSalesDataRecordItemMapper.selectByRecordId(recordId);

        //奶卡管理-退，查询卡号。卡号-剩余提数
        List<OcBReturnAfSend> ocBReturnAfSends = afSendMapper.selectByBillNo(billNo);
        if (CollectionUtils.isEmpty(ocBReturnAfSends)) {
            log.error(LogUtil.format("未查到发货后退款单billNo:{}"), billNo);
            return;
        }

        OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSends.get(0);

        ValueHolder valueHolder = omsNaiKaReturnDetailService.getNaiKaReturn(ocBReturnAfSend.getId());
        if (Objects.isNull(valueHolder.get("data"))) {
            log.error(LogUtil.format("未查到奶卡-管理数据billNo:{}"), billNo);
            return;
        }
        OmsNaiKaReturnDetailModel model = (OmsNaiKaReturnDetailModel) valueHolder.get("data");
        if (Objects.isNull(model)) {
            log.error(LogUtil.format("查询奶卡-管理数据空billNo:{}"), billNo);
            return;
        }
        List<OmsNaiKaReturnDetailModel.NaiKaModel> naiKaModels = model.getNaiKaModels();
        if (CollectionUtils.isEmpty(naiKaModels)) {
            log.error(LogUtil.format("无奶卡退明细billNo:{}"), billNo);
            return;
        }

        //sku-cardNos
        Map<String, List<String>> skuCardMap = Maps.newHashMap();
        for (OmsNaiKaReturnDetailModel.NaiKaModel naiKaModel : naiKaModels) {
            String skuEcode = naiKaModel.getSkuEcode();
            String cardCode = naiKaModel.getCardCode();
            List<String> list = skuCardMap.get(skuEcode);
            if (CollectionUtils.isEmpty(list)) {
                skuCardMap.put(skuEcode, Lists.newArrayList(cardCode));
            } else {
                list.add(cardCode);
                skuCardMap.put(skuEcode, list);
            }
        }

        //查询小程序剩余提数
        Set<String> cardCodes = naiKaModels.stream().map(OmsNaiKaReturnDetailModel.NaiKaModel::getCardCode).collect(Collectors.toSet());
        NaiKaCardQueryRequest request = new NaiKaCardQueryRequest();
        request.setCardList(Lists.newArrayList(cardCodes));
        ValueHolderV14 valueHolderV14 = naiKaOrderCmd.cardQuery(request);
        if (!valueHolderV14.isOK()) {
            log.error(LogUtil.format("查询小程序失败billNo:{}"), billNo);
            return;
        }

        List<NaiKaCardQueryResult> result = (List<NaiKaCardQueryResult>) valueHolderV14.getData();

        //卡号-剩余提数
        Map<String, Integer> map = Maps.newHashMap();
        for (NaiKaCardQueryResult naiKaCardQueryResult : result) {
            //卡状态 (1-可使用，2-已使用，3-已冻结，4-已作废）
            //当奶卡小程序返回卡的状态为“4-已作废”时，记录当前卡的“剩余提数”
            Integer cardStatus = naiKaCardQueryResult.getCardStatus();
            if (cardStatus != null && cardStatus.equals(4)) {
                map.put(naiKaCardQueryResult.getCardNumber(), naiKaCardQueryResult.getRemainingCount());
            }
        }

        List<OcBSapSalesDataRecordItem> updateItems = Lists.newArrayList();

        Map<String, List<OcBSapSalesDataRecordItem>> detailMap = recordItems.stream().collect(
                Collectors.groupingBy(OcBSapSalesDataRecordItem::getSku));
        for (Map.Entry<String, List<OcBSapSalesDataRecordItem>> entry : detailMap.entrySet()) {
            List<OcBSapSalesDataRecordItem> value = entry.getValue();
            OcBSapSalesDataRecordItem ocBSapSalesDataRecordItem = value.get(0);

            OcBSapSalesDataRecordItem updateItem = new OcBSapSalesDataRecordItem();
            updateItem.setId(ocBSapSalesDataRecordItem.getId());
            updateItem.setModifieddate(new Date());

            String sku = ocBSapSalesDataRecordItem.getSku();
            List<String> cardNos = skuCardMap.get(sku);
            if (CollectionUtils.isNotEmpty(cardNos)) {
                Integer sum = null;
                for (String cardNo : cardNos) {
                    Integer integer = map.get(cardNo);
                    if (integer != null) {
                        if (sum == null) {
                            sum = 0;
                            sum = sum + integer;
                        } else {
                            sum = sum + integer;
                        }
                    }
                }
                if (sum != null) {
                    if (ocBSapSalesDataRecordItem.getResidueQty() == null || updateResidueQty) {
                        updateItem.setResidueQty(sum.longValue());
                    }
                    updateItems.add(updateItem);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(updateItems)) {
            updateItems.forEach(p -> ocBSapSalesDataRecordItemMapper.updateById(p));
        }
    }

}
