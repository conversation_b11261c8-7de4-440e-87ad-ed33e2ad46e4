package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.CpCLogisticsItem;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.hub.model.minipt.OrderDeliveryReq;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderSource;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.HubRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc : 小平台发货
 * <AUTHOR> xiWen
 * @Date : 2022/9/28
 */
@Slf4j
@Component
public class OmsMiniPlatformDelivery {

    @Autowired
    private OcBOrderMapper orderMapper;
    @Autowired
    private OcBOrderItemMapper itemMapper;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private HubRpcService hubRpcService;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Resource
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;

    /**
     * 小平台手工
     *
     * @param id
     * @return
     */
    public ValueHolderV14 appointDelivery(Long id) {
        ValueHolderV14 vh = new ValueHolderV14(ResultCode.SUCCESS, "success");
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsMiniPlatformDelivery.appointDelivery.id.{}, start"), id);
        }
        try {
            OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
            OcBOrder order = orderMapper.selectByID(id);
            AssertUtil.notNull(order, "零售发货单查询为空");
            ocBOrderRelation.setOrderInfo(order);
            List<OcBOrderItem> ocBOrderItems = itemMapper.selectItemListOfUnshippedAndNonGift(ocBOrderRelation.getOrderId());
            AssertUtil.notEmpty(ocBOrderItems, "零售明细发货单查询为空");
            ocBOrderRelation.setOrderItemList(ocBOrderItems);
            boolean delivery = delivery(ocBOrderRelation, Lists.newArrayList());
            vh.setData(delivery);
        } catch (Exception e) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage(optimizeExpMsg.apply(e));
            log.error(LogUtil.format("exp:{}"), Throwables.getStackTraceAsString(e));
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsMiniPlatformDelivery.appointDelivery.id.{}, end"), id);
        }
        return vh;
    }

    /**
     * 小平台发货
     *
     * @param orderRelation
     * @return
     */
    public boolean delivery(OcBOrderRelation orderRelation, List<String> tips) {
        OcBOrder order = orderRelation.getOrderInfo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsMiniPlatformDelivery.id.{}, start", order.getBillNo()), order.getId());
        }

        //手工单判断
        int sendLevel = beforeSend(order);
        if (sendLevel > ResultCode.SUCCESS) {
            return true;
        }

        //支付宝小程序(alipay open mini)发货
        if (PlatFormEnum.ALIPAY_OPEN_MINI.getCode().equals(order.getPlatform())){
            return alipayOpenMiniDelivery(orderRelation, tips, order);
        }

        //尾单
        List<OrderDeliveryReq.AllLogisticsInfo> allLogisticsInfos = Lists.newArrayList();
        if (PlatFormEnum.TMALL_DDD.getCode().equals(order.getPlatform())) {
            List<OcBOrder> ocBOrders = orderMapper.selectOcBOrderByTid(order.getTid());
            if (isTailOrder(tips, order, ocBOrders)) {
                orderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(),
                        "平台发货失败。平台单号:" + order.getId() + ",未全部发货，等待下次全部发货后重试!",
                        "", null, null);
                return false;
            }
            //查询所有订单的物流信息
            allLogisticsInfo(allLogisticsInfos, ocBOrders);
        }

        List<OrderDeliveryReq> deliveryReqs = handleProcessor(orderRelation, allLogisticsInfos);

        //非首单：当发货时发现平台单号下已经存在状态=“平台发货”的订单，即为非首单
        if (PlatFormEnum.TUAN_MAI_MAI.getCode().equals(order.getPlatform())) {
            return tmmDelivery(tips, order, deliveryReqs, allLogisticsInfos);
        }

        boolean sendResult = sendPlatform(deliveryReqs, order, tips, allLogisticsInfos);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsMiniPlatformDelivery.id.{}, end.{}", order.getBillNo()), order.getId(), sendResult);
        }
        return sendResult;
    }

    private boolean tmmDelivery(List<String> tips, OcBOrder order, List<OrderDeliveryReq> deliveryReqs, List<OrderDeliveryReq.AllLogisticsInfo> allLogisticsInfos) {
        String lockRedisKey = BllRedisKeyResources.buildLockDeliveryOrderTidKey(order.getTid());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                //团买买多包裹
                List<OcBOrder> ocBOrders = orderMapper.selectOcBOrderByTid(order.getTid());
                //获取平台发货状态的订单
                List<OcBOrder> collect =
                        ocBOrders.stream().filter(ocBOrder -> OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(ocBOrder.getOrderStatus())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    //首单
                    for (OrderDeliveryReq deliveryReq : deliveryReqs) {
                        //是否首单 0否，1是
                        deliveryReq.setIsFirstOrder(1L);
                    }
                } else {
                    //非首单
                    for (OrderDeliveryReq deliveryReq : deliveryReqs) {
                        //是否首单 0否，1是
                        deliveryReq.setIsFirstOrder(0L);
                    }
                }
                boolean sendResult = sendPlatform(deliveryReqs, order, tips, allLogisticsInfos);
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("tmm OmsMiniPlatformDelivery.id.{}, end.{}", order.getBillNo()), order.getId(), sendResult);
                }
                return sendResult;
            } else {
                ApplicationContextHandle.getBean(OmsOrderLogService.class).addUserOrderLog(
                        order.getId(), order.getBillNo(),
                        OrderLogTypeEnum.PLATFORM_SEND.getKey(),
                        "平台发货失败,当前同时发货数量过多,请稍后再试", null, null, SystemUserResource.getRootUser());
                return false;
            }
        } catch (Exception ex) {
            ApplicationContextHandle.getBean(OmsOrderLogService.class).addUserOrderLog(
                    order.getId(), order.getBillNo(),
                    OrderLogTypeEnum.PLATFORM_SEND.getKey(),
                    "平台发货异常,请稍后再试", null, null, SystemUserResource.getRootUser());
            return false;
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 支付宝小程序(alipay open mini)发货
     *
     * @param orderRelation
     * @param tips
     * @param order
     * @return
     */
    private boolean alipayOpenMiniDelivery(OcBOrderRelation orderRelation, List<String> tips, OcBOrder order) {
        String tid = order.getTid();
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(BllRedisKeyResources.buildAlipayOpenMiniDeliveryTidLockKey(tid));
        try {
            if (!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                throw new NDSException("同平台单号订单多单并发发货,稍后重试!");
            }

            List<OrderDeliveryReq> deliveryReqs = handleProcessor(orderRelation, Lists.newArrayList());
            if (CollectionUtils.isEmpty(deliveryReqs)) {
                throw new NDSException("无可发货数据,稍后重试!");
            }

            OrderDeliveryOfStandplatmpl bean = ApplicationContextHandle.getBean(OrderDeliveryOfStandplatmpl.class);
            boolean tailOrderSingle = bean.isTailOrderSingle(order, orderMapper.selectOcBOrderByTid(order.getTid()));

            for (OrderDeliveryReq deliveryReq : deliveryReqs) {
                if (tailOrderSingle) {
                    //尾单
                    deliveryReq.setIsEndOrder(1L);
                } else {
                    //非尾单
                    deliveryReq.setIsEndOrder(0L);
                }
            }

            boolean sendResult = sendPlatform(deliveryReqs, order, tips, Lists.newArrayList());
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format(PlatFormEnum.ALIPAY_OPEN_MINI.getName() + " OmsMiniPlatformDelivery.id.{}, end.{}", order.getBillNo()),
                        order.getId(), sendResult);
            }
            return sendResult;
        } catch (Exception e) {
            orderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(),
                    "平台发货失败。平台单号:" + order.getId() + "," + e.getMessage(), "", null, null);
            return false;
        } finally {
            redisLock.unlock();
        }
    }


    /**
     * 处理所有订单的物流信息。
     *
     * @param allLogisticsInfos 用于收集所有订单的物流信息的列表
     * @param ocBOrders         所有订单的列表
     *                          该方法会过滤出订单状态为仓库发货的订单，然后为这些订单查询物流信息，并将这些信息填充到allLogisticsInfos中。
     */
    private List<Long> allLogisticsInfo(List<OrderDeliveryReq.AllLogisticsInfo> allLogisticsInfos, List<OcBOrder> ocBOrders) {
        // 过滤出订单状态为仓库发货的订单
        List<OcBOrder> orders = ocBOrders.stream().filter(p -> OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orders)) {
            return Lists.newArrayList();
        }

        List<Long> orderIds = orders.stream().map(OcBOrder::getId).collect(Collectors.toList());
        List<OcBOrderItem> ocBOrderItems = itemMapper.selectAllStatusOrderItemsByOrderIds(orderIds);
        Map<Long, List<OcBOrderItem>> detailMap = ocBOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));

        for (OcBOrder ocBOrder : orders) {
            // 查询对应的物流信息
            CpCLogisticsItem logistics = cpRpcService.queryCpPlatformLogisticsInfo(Long.valueOf(ocBOrder.getPlatform()), ocBOrder.getCpCLogisticsId());
            // 确保物流信息不为空
            AssertUtil.notNull(logistics, ocBOrder.getBillNo() + ",查询物流档案信息为空");

            // 创建物流信息对象并填充数据
            OrderDeliveryReq.AllLogisticsInfo allLogisticsInfo = new OrderDeliveryReq.AllLogisticsInfo();
            allLogisticsInfo.setOrderId(ocBOrder.getId());
            allLogisticsInfo.setBillNo(ocBOrder.getBillNo());
            allLogisticsInfo.setPtExpressCode(logistics.getCpCLogisticsEcode());
            allLogisticsInfo.setLogisticsCode(ocBOrder.getExpresscode());

            List<OcBOrderItem> orderItems = detailMap.get(ocBOrder.getId());
            if (CollectionUtils.isNotEmpty(orderItems)) {
                List<String> ooids = orderItems.stream().map(OcBOrderItem::getOoid).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(ooids)) {
                    allLogisticsInfo.setOoids(ooids);
                }
            }

            allLogisticsInfos.add(allLogisticsInfo);
        }

        return orders.stream().map(OcBOrder::getId).collect(Collectors.toList());
    }

    /**
     * tmallddd 判断是否尾单
     *
     * @param tips
     * @param order
     * @param ocBOrders
     * @return
     */
    private boolean isTailOrder(List<String> tips, OcBOrder order, List<OcBOrder> ocBOrders) {
        //除本单外的所有其他订单
        List<OcBOrder> ocBOrderList = ocBOrders.stream().filter(p -> !p.getId().equals(order.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ocBOrderList)) {
            return false;
        }

        //正常订单
        List<OcBOrder> normalOrders = ocBOrderList.stream().filter(p -> OrderTypeEnum.NORMAL.getVal().equals(p.getOrderType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(normalOrders)) {
            return false;
        }

        //排除取消、系统作废
        List<OcBOrder> orders = normalOrders.stream().filter(p ->
                (!OmsOrderStatus.CANCELLED.toInteger().equals(p.getOrderStatus())
                        && !OmsOrderStatus.SYS_VOID.toInteger().equals(p.getOrderStatus()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orders)) {
            return false;
        }

        //是否都为仓库发货
        List<OcBOrder> bOrders = orders.stream().filter(p -> !OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(p.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bOrders)) {
            tips.add("订单未全部仓库发货，等待下次全部发货后重试!");
            return true;
        }
        return false;
    }


    private int beforeSend(OcBOrder order) {
        boolean isManualAdd = OmsOrderSource.MANUAL_ADD.getEcode().equals(order.getOrderSource());
        if (!isManualAdd) {
            return ResultCode.SUCCESS;
        }
        String logMsg = "手工新增单，直接标记平台发货";
        orderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
        OcBOrder update = new OcBOrder();
        update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        update.setId(order.getId());
        orderMapper.updateById(update);
        return YesNoEnum.Y.getVal();
    }

    private List<OrderDeliveryReq> handleProcessor(OcBOrderRelation orderRelation, List<OrderDeliveryReq.AllLogisticsInfo> allLogisticsInfos) {
        OcBOrder order = orderRelation.getOrderInfo();
        CpCLogisticsItem logistics = cpRpcService.queryCpPlatformLogisticsInfo(Long.valueOf(order.getPlatform()), order.getCpCLogisticsId());
        AssertUtil.notNull(logistics, "查询物流档案信息为空");

        List<OcBOrderItem> items = orderRelation.getOrderItemList();
        Map<String, List<OcBOrderItem>> tipItmMap = items.stream().filter(e -> !NumberUtils.INTEGER_ONE.equals(e.getIsSendout())).collect(Collectors.groupingBy(OcBOrderItem::getTid));
        List<OrderDeliveryReq> deliveryReqs = new ArrayList<>(tipItmMap.size());
        tipItmMap.forEach((k, v) -> buildDeliveryReq(k, v, order, logistics, deliveryReqs, allLogisticsInfos));
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OmsMiniPlatformDelivery.id.{},Requests:{}"), order.getId(), JSON.toJSONString(deliveryReqs));
        }
        return deliveryReqs;
    }

    /**
     * @param tid
     * @param items
     * @param order
     * @param logistics
     * @param deliveryReqs
     */
    private void buildDeliveryReq(String tid, List<OcBOrderItem> items, OcBOrder order, CpCLogisticsItem logistics,
                                  List<OrderDeliveryReq> deliveryReqs, List<OrderDeliveryReq.AllLogisticsInfo> allLogisticsInfos) {
        if (PlatFormEnum.DOUCHAO.getCode().equals(order.getPlatform())) {
            OrderDeliveryReq req = buildRequest(tid, order, logistics, allLogisticsInfos,null);
            List<OrderDeliveryReq.DataDto> reqItems = buildItemRequests(items, order, logistics);

            if (order.getIsSplit().equals(1)) {
                req.setDetail(reqItems);
            } else {
                List<OrderDeliveryReq.DataDto> dataDtos = Lists.newArrayList();
                Map<String, List<OrderDeliveryReq.DataDto>> detailMap = reqItems.stream().collect(Collectors.groupingBy(OrderDeliveryReq.DataDto::getOrderCode));
                for (Map.Entry<String, List<OrderDeliveryReq.DataDto>> entry : detailMap.entrySet()) {
                    List<OrderDeliveryReq.DataDto> value = entry.getValue();
                    //获取value里count最小的数据
                    OrderDeliveryReq.DataDto dataDto = value.stream().min(Comparator.comparing(OrderDeliveryReq.DataDto::getCount)).get();
                    dataDtos.add(dataDto);
                }
                req.setDetail(dataDtos);
            }
            deliveryReqs.add(req);
        } else {
            IpBStandplatOrder ipBStandplatOrder;
            if (PlatFormEnum.ALIPAY_OPEN_MINI.getCode().equals(order.getPlatform())) {
                ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(order.getTid());
            } else {
                ipBStandplatOrder = null;
            }

            Map<String, List<OcBOrderItem>> groupMap = items.stream().collect(Collectors.groupingBy(this::getGroupKey, Collectors.toList()));
            groupMap.forEach((k, v) -> {
                OrderDeliveryReq req = buildRequest(tid, order, logistics, allLogisticsInfos, ipBStandplatOrder);
                List<OrderDeliveryReq.DataDto> reqItems = buildItemRequests(v, order, logistics);
                req.setDetail(reqItems);
                deliveryReqs.add(req);
            });
        }
    }

    /**
     * @param tid
     * @param order
     * @param cpLogisticsItem
     * @return
     */
    private OrderDeliveryReq buildRequest(String tid, OcBOrder order, CpCLogisticsItem cpLogisticsItem,
                                          List<OrderDeliveryReq.AllLogisticsInfo> allLogisticsInfos,IpBStandplatOrder ipBStandplatOrder) {
        OrderDeliveryReq req = new OrderDeliveryReq();
        req.setPtId(Long.valueOf(order.getPlatform()));
        req.setShopId(order.getCpCShopId());
        req.setOrderCode(tid);
        req.setIsSplit(order.getIsSplit());
        req.setLogisticsCode(order.getExpresscode());
        req.setPtExpressCode(cpLogisticsItem.getCpCLogisticsEcode());
        req.setPtExpressCompany(cpLogisticsItem.getCpCLogisticsEname());
        req.setBillNo(order.getBillNo());
        if (CollectionUtils.isNotEmpty(allLogisticsInfos)) {
            req.setAllLogisticsInfos(allLogisticsInfos);
        }
        if (ipBStandplatOrder != null) {
            req.setBuyerNick(ipBStandplatOrder.getBuyerNick());
        }
        return req;
    }

    /**
     * @param items
     * @param order
     * @param logistics
     * @return
     */
    private List<OrderDeliveryReq.DataDto> buildItemRequests(List<OcBOrderItem> items, OcBOrder order, CpCLogisticsItem logistics) {
        List<OrderDeliveryReq.DataDto> reqItems = new ArrayList<>();
        for (OcBOrderItem item : items) {
            OrderDeliveryReq.DataDto itmReq = buildItemRequest(item, order, logistics);
            reqItems.add(itmReq);
            if (!PlatFormEnum.DOUCHAO.getCode().equals(order.getPlatform())) {
                String groupGoodsMark = item.getGroupGoodsMark();
                if (StringUtils.isNotBlank(groupGoodsMark)) {
                    break;
                }
            }
        }
        return reqItems;
    }

    /**
     * @param item
     * @param order
     * @param logistics
     * @return
     */
    private OrderDeliveryReq.DataDto buildItemRequest(OcBOrderItem item, OcBOrder order, CpCLogisticsItem logistics) {
        OrderDeliveryReq.DataDto itmReq = new OrderDeliveryReq.DataDto();
        if (PlatFormEnum.YUNHUO.getCode().equals(order.getPlatform())) {
            itmReq.setSpuId(item.getPsCProEcode());
            itmReq.setSkuCode(item.getPsCSkuEcode());
        }else if (PlatFormEnum.TMALL_DDD.getCode().equals(order.getPlatform())
                || PlatFormEnum.ALIPAY_MIN_APP.getCode().equals(order.getPlatform())) {
            itmReq.setSpuId(item.getSkuNumiid());
            itmReq.setSkuCode(item.getPsCSkuEcode());
        } else {
            itmReq.setSpuId(item.getNumIid());
            itmReq.setSkuCode(item.getSkuNumiid());
        }
        itmReq.setOrderCode(item.getOoid());
        itmReq.setLogisticsCode(order.getExpresscode());
        itmReq.setPtExpressCode(logistics.getCpCLogisticsEcode());
        itmReq.setPtExpressCompany(logistics.getCpCLogisticsEname());

        Integer count = Integer.valueOf(item.getQty().stripTrailingZeros().toPlainString());
        String groupGoodsMark = item.getGroupGoodsMark();
        if (Objects.nonNull(item.getOriginSkuQty())) {
            count = item.getOriginSkuQty().intValue();
        }
        else if (StringUtils.isNotBlank(groupGoodsMark)) {
            count = NumUtil.toInt(item.getQtyGroup());
        } else {
            boolean isEqExchange = OcBOrderConst.IS_STATUS_IY.equals(item.getIsEqualExchange());
            if (isEqExchange) {
                count = paresEqualExchangeCount(item);
            }
        }
        if (PlatFormEnum.KAI_ER_DE_LE.getCode().equals(order.getPlatform())) {
            count = Objects.isNull(item.getQty()) ? NumberUtils.INTEGER_ZERO : item.getQty().intValue();
        }
        itmReq.setCount(count);
        itmReq.setGoodsName(item.getPsCProEname());

        // 单创重量必传, 组合 = (拆分后重量*数量)
        if (PlatFormEnum.DANCHUANG.getCode().equals(order.getPlatform()) || PlatFormEnum.DOUCHAO.getCode().equals(order.getPlatform())) {
            BigDecimal weight = NumUtil.init(item.getStandardWeight());
            itmReq.setWeight(weight.multiply(item.getQty()));
        }
        return itmReq;
    }

    /**
     * @param deliveryReqs
     * @param order
     * @return
     */
    private boolean sendPlatform(List<OrderDeliveryReq> deliveryReqs, OcBOrder order, List<String> tips, List<OrderDeliveryReq.AllLogisticsInfo> allLogisticsInfos) {
        Long orderId = order.getId();
        String billNo = order.getBillNo();
        boolean result = false;
        boolean allSuccess = true;
        for (OrderDeliveryReq deliveryReq : deliveryReqs) {
            String orderCode = deliveryReq.getOrderCode();
            String logMsg = "平台单号:" + orderCode;
            try {
                ValueHolderV14 vh = hubRpcService.deliveryOrder(deliveryReq);
                if (ResultCode.SUCCESS == vh.getCode()) {
                    if (PlatFormEnum.TMALL_DDD.getCode().equals(order.getPlatform())) {
                        //全部仓库发货修改为平台发货
                        for (OrderDeliveryReq.AllLogisticsInfo allLogisticsInfo : allLogisticsInfos) {
                            orderLogService.addUserOrderLog(allLogisticsInfo.getOrderId(), allLogisticsInfo.getBillNo(),
                                    OrderLogTypeEnum.PLATFORM_SEND.getKey(), "平台单号:" + orderCode + "发货通知平台成功",
                                    "", null, null);
                            itemMapper.updateItemsWhenDeliverySuccess(allLogisticsInfo.getOrderId(), orderCode);
                            result = true;
                        }
                        continue;
                    } else {
                        logMsg = logMsg + "发货通知平台成功";
                        orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg,
                                "", null, null);
                        itemMapper.updateItemsWhenDeliverySuccess(orderId, orderCode);
                        result = true;
                        continue;
                    }
                }
                logMsg = logMsg + "发货通知平台失败," + optimizeMsg.apply(vh.getMessage());
                tips.add(vh.getMessage());
            } catch (Exception e) {
                logMsg = logMsg + "发货通知平台异常," + optimizeExpMsg.apply(e);
                log.error(LogUtil.format("exp:{}"), Throwables.getStackTraceAsString(e));
                tips.add(Throwables.getStackTraceAsString(e));
            }
            allSuccess = false;
            orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg,
                    "", null, null);
        }
        if (allSuccess) {
            if (PlatFormEnum.TMALL_DDD.getCode().equals(order.getPlatform())) {
                OcBOrder update = new OcBOrder();
                update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                update.setModifieddate(new Date());
                if (CollectionUtils.isNotEmpty(allLogisticsInfos)) {
                    List<Long> ids = allLogisticsInfos.stream().map(OrderDeliveryReq.AllLogisticsInfo::getOrderId).collect(Collectors.toList());
                    orderMapper.update(update, new LambdaQueryWrapper<OcBOrder>().in(OcBOrder::getId, ids));
                }
            } else {
                OcBOrder update = new OcBOrder();
                update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
                update.setId(orderId);
                orderMapper.updateById(update);
            }
        }
        return result;
    }

    /**
     * @param item
     * @return
     */
    private String getGroupKey(OcBOrderItem item) {
        String groupGoodsMark = item.getGroupGoodsMark();
        if (StringUtils.isNotBlank(groupGoodsMark)) {
            return groupGoodsMark;
        }
        return "_NORMAL";
    }

    /**
     * description:解析未对等换货之前的数量    例如 原来的 8  新的 4 比例 2：1  计算 4*2 / 1 = 8
     *
     * @Author: liuwenjin
     * @Date 2022/9/27 12:58
     */
    private Integer paresEqualExchangeCount(OcBOrderItem item) {
        String[] ratios = item.getEqualExchangeRatio().split(":");
        BigDecimal qty = item.getQty();
        if (ratios.length > 0) {
            Integer num1 = Integer.valueOf(ratios[0]);
            Integer num2 = Integer.valueOf(ratios[1]);
            return num1 * qty.intValue() / num2;
        }
        return qty.intValue();
    }

    private Function<String, String> optimizeMsg = e -> {
        if (e == null) {
            return "null message";
        }
        return e.length() > 200 ? e.substring(0, 200) : e;
    };

    private Function<Exception, String> optimizeExpMsg = e -> {
        if (e == null) {
            return "null exception";
        }
        String message = e.getMessage();
        if (message == null) {
            return "null message";
        }
        return message.length() > 225 ? message.substring(0, 225) : message;
    };

}
