package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectRefundItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/28
 */
@Mapper
public interface IpBJingdongDirectRefundItemMapper extends ExtentionMapper<IpBJingdongDirectRefundItem> {


    @Select("select * from ip_b_jingdong_direct_refund_item where ip_b_jingdong_direct_refund_id=#{refundId};")
    List<IpBJingdongDirectRefundItem> selectRefundItemsByShardKey(@Param("refundId") Long refundId);
}
