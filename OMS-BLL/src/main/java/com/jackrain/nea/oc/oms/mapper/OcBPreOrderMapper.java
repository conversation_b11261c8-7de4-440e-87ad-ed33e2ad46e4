package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.relation.OcBPreOrderQueryModel;
import com.jackrain.nea.oc.oms.model.table.OcBPreOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBPreOrderMapper
 * @Description 订单预导入
 * <AUTHOR>
 * @Date 2022/10/11 14:56
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBPreOrderMapper extends ExtentionMapper<OcBPreOrder> {

    @Update("<script> "
            + "UPDATE OC_B_PRE_ORDER SET cp_c_shop_title = #{shopTitle} where tid in "
            + "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    boolean updateShopTitleByTid(@Param("tids") List<String> tids, @Param("shopTitle") String shopTitle);

    @Select("<script> "
            + "select * from OC_B_PRE_ORDER where tid = #{tid} and isactive = 'Y'"
            + "</script>")
    OcBPreOrder getByTid(@Param("tid") String tid);

    @Select("<script> "
            + "SELECT * FROM OC_B_PRE_ORDER WHERE id in"
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + " order by ID desc</script>")
    List<OcBPreOrderQueryModel> selectByIds(@Param("ids") List<Long> ids);
}
