package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.util.BllWebUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.face.impl.UserImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 郑立轩
 * @since: 2019/5/8
 * create at : 2019/5/8 13:31
 */
@Component
@Slf4j
public class WmsUserCreateUtil {
    @Autowired
    BllWebUtil util;

    public User initWmsUser() {
        User user = new UserImpl();
        ((UserImpl) user).setEname("WMS");
        ((UserImpl) user).setName("WMS");
        ((UserImpl) user).setId(666);
        ((UserImpl) user).setLastloginip(util.getWebRequestIpAddress());
        ((UserImpl) user).setOrgId(27);
        ((UserImpl) user).setClientId(37);


        return user;
    }
}
