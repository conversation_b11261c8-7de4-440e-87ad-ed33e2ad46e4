package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * 唯品会工单状态
 *
 * @author: 王帅
 * create at: 2020/3/26 11:50
 */
public enum VipJitxWorkflowStateEnum {

    CREATE("100", "新建"),
    DOING("200", "处理中"),
    PASS("300", "通过"),
    REJECT("400", "驳回"),
    CANCEL("900", "取消");

    VipJitxWorkflowStateEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    @Getter
    private String key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String getNameByKey(String key) {
        for (VipJitxWorkflowStateEnum e : VipJitxWorkflowStateEnum.values()) {
            if (e.getKey().equals(key)) {
                return e.getName();
            }
        }
        return null;
    }
}


