package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Description:发货服务类集合
 *
 * <AUTHOR> sunies
 * @since : 2020-11-03
 * create at : 2020-11-03 20:02
 */
@Slf4j
public class DeliveryServicesFactory {

    private static final Map<Integer, OrderDeliveryCmd> platFormMap = new HashMap<>();

    static {
        platFormMap.put(PlatFormEnum.TAOBAO.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfTaoBaoImpl.class));
        platFormMap.put(PlatFormEnum.TAOBAO_DEAL.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfTaoBaoImpl.class));
        platFormMap.put(PlatFormEnum.TAOBAO_DISTRIBUTION.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfTaoBaoImpl.class));
        platFormMap.put(PlatFormEnum.VIP_JITX.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfVipJitxImpl.class));
        platFormMap.put(PlatFormEnum.JINGDONG.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfJingDongImpl.class));
        platFormMap.put(PlatFormEnum.ALIBABAASCP.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfAscpImpl.class));
        platFormMap.put(PlatFormEnum.PLATFORM_NONE.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfStandplatmpl.class));
        platFormMap.put(PlatFormEnum.STD_XXY.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfStdImpl.class));
        platFormMap.put(PlatFormEnum.CARD_CODE.getCode(), ApplicationContextHandle.getBean(OrderDeliveryNaiKaImpl.class));
        platFormMap.put(PlatFormEnum.CREATE_CARD_CODE.getCode(), ApplicationContextHandle.getBean(OrderDeliveryNaiKaImpl.class));
        platFormMap.put(PlatFormEnum.WANG_DIAN_TONG.getCode(), ApplicationContextHandle.getBean(OrderDeliveryWangDianTongImpl.class));
        platFormMap.put(PlatFormEnum.SAP.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfSapImpl.class));
        platFormMap.put(PlatFormEnum.ALIPAY_MIN_APP.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.DMS.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfDmsImpl.class));
        platFormMap.put(PlatFormEnum.HAOSHIQI.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.BIGVSTORE.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.MEITUAN_FLASH_SALES.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.HIPAC.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.YANGSC.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.YUNHUO.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.DANCHUANG.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.NICOMAMA.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.DXDOCTOR.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.JINGDONG_CZ.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfDirectImpl.class));
        platFormMap.put(PlatFormEnum.KUAITUANTUAN.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.KAI_ER_DE_LE.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.QUNJIELONG.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.SHIPH.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.FKXIAOYU.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.XIAOXIAOBAO.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.YOUFEN.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.DONGFANGFULI.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.NASCENT.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.YOUHU.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.HEMAOS.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.TMALL_DDD.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.TUAN_MAI_MAI.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.XI_YUN.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.DOUCHAO.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.ALI_1688.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.TAO_CAI_CAI.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.HIC.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));

        platFormMap.put(PlatFormEnum.ALIPAY_OPEN_MINI.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
        platFormMap.put(PlatFormEnum.HAI_ZI_WANG_ONE.getCode(), ApplicationContextHandle.getBean(OrderDeliveryOfMiniPlatformImpl.class));
    }

    /**
     * 发货处理
     *
     * @param ocBOrderRelation
     * @return
     */
    public static Pair<Boolean, String> invokeDelivery(OcBOrderRelation ocBOrderRelation) {
        try {
            //是否虚标
            OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
            Integer plat = orderInfo.getPlatform();
            Integer orderType = orderInfo.getOrderType();
            log.info(LogUtil.format("订单类型:{},平台:{},ID:{}"), orderType, plat, ocBOrderRelation.getOrderId());

            boolean send = PlatFormEnum.TAOBAO.getCode().equals(plat) || PlatFormEnum.TAOBAO_DISTRIBUTION.getCode().equals(plat);

            if (!send && !Objects.isNull(orderType) &&
                    OrderTypeEnum.DIFFPRICE.getVal().equals(orderType)) {
                OrderPlatformDeliveryService platformDeliveryService = ApplicationContextHandle.getBean(OrderPlatformDeliveryService.class);
                platformDeliveryService.orderDeliveryByNoSend(orderInfo);
                return new ImmutablePair<>(true, "");
            }

            /**
             * ☆校验订单状态
             */
            List<Integer> statusList = Arrays.asList(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger(), OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            if (!statusList.contains(ocBOrderRelation.getOrderInfo().getOrderStatus())) {
                log.error(LogUtil.format("订单状态非法，不允许平台发货"));
                return new ImmutablePair<>(false, "");
            }

            log.info(LogUtil.format("DeliveryServicesFactory.invokeDelivery getGwSourceGroup:{},getPlatform:{},platFormMap:{}",
                            "DeliveryServicesFactory.invokeDelivery"), ocBOrderRelation.getOrderInfo().getGwSourceGroup(),
                    ocBOrderRelation.getOrderInfo().getPlatform(), JSONObject.toJSONString(platFormMap));

            String gwSourceGroup = ocBOrderRelation.getOrderInfo().getGwSourceGroup();
            Integer platform = ocBOrderRelation.getOrderInfo().getPlatform();
            int key = StringUtils.isNotEmpty(gwSourceGroup) && (Integer.parseInt(gwSourceGroup) == PlatFormEnum.SAP.getCode() || Integer.parseInt(gwSourceGroup) == PlatFormEnum.DMS.getCode()) ? Integer.parseInt(gwSourceGroup) : platform;
            OrderDeliveryCmd orderDeliveryCmd = platFormMap.get(key);

            Integer infoPlatform = orderInfo.getPlatform();
            if (checkIsCyclePurchasePickUpOrder(orderInfo)
                    && (PlatFormEnum.DOU_YIN.getCode().equals(infoPlatform) || PlatFormEnum.KUAISHOU.getCode().equals(infoPlatform) || PlatFormEnum.HONGSHU_OPEN.getCode().equals(infoPlatform))) {
                orderDeliveryCmd = ApplicationContextHandle.getBean(OrderDeliveryOfCyclePickUpImpl.class);
            }

            OmsGiftAfterService bean = ApplicationContextHandle.getBean(OmsGiftAfterService.class);
            bean.handelGiftAfter(ocBOrderRelation);
            //订单如果是 复制单 或者补发单 或者平台为8888  则不同步平台，直接平台发货
            OrderPlatformDeliveryService platformDeliveryService = ApplicationContextHandle.getBean(OrderPlatformDeliveryService.class);
            boolean isNotSend = platformDeliveryService.orderDeliveryOfNoSend(ocBOrderRelation.getOrderInfo());
            if (isNotSend) {
                return new ImmutablePair<>(true, "");
            }

            List<String> tips = Lists.newArrayList();
            boolean b = Optional.ofNullable(orderDeliveryCmd).orElse(platFormMap.get(PlatFormEnum.PLATFORM_NONE.getCode())).deliveryDeal(ocBOrderRelation, tips);
            return new ImmutablePair<>(b, CollectionUtils.isEmpty(tips) ? "" : JSON.toJSONString(tips));
        } catch (Exception e) {
            ApplicationContextHandle.getBean(OmsOrderLogService.class).addUserOrderLog(ocBOrderRelation.getOrderId(), ocBOrderRelation.getOrderInfo().getBillNo()
                    , OrderLogTypeEnum.PLATFORM_SEND.getKey(), "平台发货失败:" + StringUtils.substring(Throwables.getStackTraceAsString(e), 0, 200)
                    , null, null, SystemUserResource.getRootUser());
            log.error(LogUtil.format("DeliveryServicesFactory.invokeDelivery 参数={}， 异常信息:{}", e), JSON.toJSONString(ocBOrderRelation), Throwables.getStackTraceAsString(e));
            return new ImmutablePair<>(false, e.getMessage());
        }
    }


    private static boolean checkIsCyclePurchasePickUpOrder(OcBOrder orderInfo) {
        return OrderBusinessTypeCodeEnum.CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(orderInfo.getBusinessTypeCode())
                || OrderBusinessTypeCodeEnum.FREE_CYCLE_PURCHASE_ORDER_PICK_UP.getCode().equals(orderInfo.getBusinessTypeCode());
    }


}