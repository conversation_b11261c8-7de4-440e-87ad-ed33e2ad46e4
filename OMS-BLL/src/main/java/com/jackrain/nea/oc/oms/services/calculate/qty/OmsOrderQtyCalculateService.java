package com.jackrain.nea.oc.oms.services.calculate.qty;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description： 订单数量计算服务
 * Author: RESET
 * Date: Created in 2020/8/11 15:07
 * Modified By:
 */
@Component
@Slf4j
public class OmsOrderQtyCalculateService {

    @Autowired
    OmsOrderItemService itemService;

    /**
     * 设置申请退货数量 -- 一般转单时使用
     *
     * @param orderRefunds
     * @param orderId
     * @return
     */
    public boolean setQtyCanReturn(List<OcBReturnOrderRefund> orderRefunds, Long orderId) {
        // 如果同一个SKU有多行，需要记录已经被扣的
        Map<String, BigDecimal> skuQtyHashApply = new HashMap<>();

        for (OcBReturnOrderRefund orderRefund : orderRefunds) {
            Long orderItemId = orderRefund.getOcBOrderItemId();
            String skuEcode = orderRefund.getPsCSkuEcode();
            // 数据准备
            if (!skuQtyHashApply.containsKey(skuEcode)) {
                skuQtyHashApply.put(skuEcode, BigDecimal.ZERO);
            }

            // 计算可退
            BigDecimal qtyCanReturn = calQtyCanReturn4Sku(orderId, orderItemId, skuEcode);
            // 实际可退应该减去已申请数（应对同一个SKU多行的情况）
            qtyCanReturn = qtyCanReturn.subtract(skuQtyHashApply.get(skuEcode));

            if (qtyCanReturn.compareTo(BigDecimal.ZERO) <= 0) {
                // 没有可退数量了
                return false;
            } else {
                orderRefund.setQtyRefund(qtyCanReturn);
                skuQtyHashApply.put(skuEcode, skuQtyHashApply.get(skuEcode).add(qtyCanReturn));
            }
        }

        return true;
    }

    /**
     * 退单保存时校验申请数量是否大于可退数量 -- 一般页面保存时使用
     *
     * @param orderRefunds
     * @param orderId
     * @return
     */
    public boolean checkQtyCanReturnBak(List<OcBReturnOrderRefund> orderRefunds, Long orderId) {
        // 如果同一个SKU有多行，需要记录已经被扣的
        Map<String, BigDecimal> skuQtyHashApply = new HashMap<>();

        for (OcBReturnOrderRefund orderRefund : orderRefunds) {
            Long orderItemId = orderRefund.getOcBOrderItemId();
            String skuEcode = orderRefund.getPsCSkuEcode();
            // 退单上的申请数量
            BigDecimal qtyRefund = Objects.isNull(orderRefund.getQtyRefund()) ? BigDecimal.ZERO : orderRefund.getQtyRefund();
            // 数据准备
            if (!skuQtyHashApply.containsKey(skuEcode)) {
                skuQtyHashApply.put(skuEcode, BigDecimal.ZERO);
            }

            // 计算可退
            BigDecimal qtyCanReturn = calQtyCanReturn4Sku(orderId, orderItemId, skuEcode);
            // 实际可退应该减去已申请数（应对同一个SKU多行的情况）
            qtyCanReturn = qtyCanReturn.subtract(skuQtyHashApply.get(skuEcode));

            if (qtyRefund.compareTo(qtyCanReturn) > 0) {
                // 申请数量大于可退数量了
                return false;
            } else {
                skuQtyHashApply.put(skuEcode, skuQtyHashApply.get(skuEcode).add(qtyRefund));
            }
        }

        return true;
    }

    /**
     * 退单保存时校验申请数量是否大于可退数量 -- 一般页面保存时使用
     *
     * @param orderRefunds
     * @param orderId
     * @return
     */
    public boolean checkQtyCanReturn(List<OcBReturnOrderRefund> orderRefunds, Long orderId) {
        for (OcBReturnOrderRefund orderRefund : orderRefunds) {
            Long orderItemId = orderRefund.getOcBOrderItemId();
            if (orderItemId == null){
                return false;
            }
            String skuEcode = orderRefund.getPsCSkuEcode();
            // 退单上的申请数量
            BigDecimal qtyRefund = Objects.isNull(orderRefund.getQtyRefund()) ? BigDecimal.ZERO : orderRefund.getQtyRefund();
            // 计算可退
            BigDecimal qtyCanReturn = calQtyCanReturn4Sku(orderId, orderItemId, skuEcode);
           /* // 实际可退应该减去已申请数（应对同一个SKU多行的情况）
            qtyCanReturn = qtyCanReturn.subtract(qtyRefund);*/

            if (qtyRefund.compareTo(qtyCanReturn) > 0) {
                // 申请数量大于可退数量了
                return false;
            }
        }
        return true;
    }

    /**
     * 校验可退数量 -- 换货单使用
     *
     * @param orderRefunds
     * @param orderItems
     * @return
     */
    public boolean checkQtyCanReturnBak(List<OcBReturnOrderRefund> orderRefunds, List<OcBOrderItem> orderItems) {
        if (CollectionUtils.isNotEmpty(orderRefunds) && CollectionUtils.isNotEmpty(orderItems)) {
            Map<Long, OcBOrderItem> itemMap = orderItems.stream().collect(Collectors.toMap(OcBOrderItem::getId
                    , Function.identity()));
            // 如果同一个SKU有多行，需要记录已经被扣的
            Map<Long, BigDecimal> skuQtyHashApply = new HashMap<>();

            for (OcBReturnOrderRefund orderRefund : orderRefunds) {
                Long ocBOrderItemId = orderRefund.getOcBOrderItemId();
                // 退单上的申请数量
                BigDecimal qtyRefund = Objects.isNull(ocBOrderItemId) ? BigDecimal.ZERO : orderRefund.getQtyRefund();
                // 数据准备
                if (!skuQtyHashApply.containsKey(ocBOrderItemId)) {
                    skuQtyHashApply.put(ocBOrderItemId, BigDecimal.ZERO);
                }

                // 计算可退
                BigDecimal qtyCanReturn = calQtyCanReturn4Sku(itemMap.get(ocBOrderItemId));
                // 实际可退应该减去已申请数（应对同一个SKU多行的情况）
                qtyCanReturn = qtyCanReturn.subtract(skuQtyHashApply.get(ocBOrderItemId));

                if (qtyRefund.compareTo(qtyCanReturn) > 0) {
                    // 申请数量大于可退数量了
                    return false;
                } else {
                    skuQtyHashApply.put(ocBOrderItemId, skuQtyHashApply.get(ocBOrderItemId).add(qtyRefund));
                }
            }
        }

        return true;
    }

    /**
     * 计算退换货单的可退数量
     *
     * @param orderId
     * @param orderItemId
     * @param skuEcode
     * @return
     */
    public BigDecimal calQtyCanReturn4Sku(Long orderId, Long orderItemId, String skuEcode) {
        List<OcBOrderItem> orderItems = itemService.findOcBOrderItemsByIdOrSkuEcode(orderId, orderItemId, skuEcode);
        return calQtyCanReturn4Sku(orderItems);
    }

    /**
     * 校验可退数量 -- 换货单使用
     *
     * @param orderRefunds
     * @param orderItems
     * @return
     */
    public boolean checkQtyCanReturn(List<OcBReturnOrderRefund> orderRefunds, List<OcBOrderItem> orderItems) {
        if (CollectionUtils.isNotEmpty(orderRefunds) && CollectionUtils.isNotEmpty(orderItems)) {
            // 按sku汇总
            Map<String, List<OcBOrderItem>> skuItems = orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getPsCSkuEcode));
            // 如果同一个SKU有多行，需要记录已经被扣的
            Map<String, BigDecimal> skuQtyHashApply = new HashMap<>();

            for (OcBReturnOrderRefund orderRefund : orderRefunds) {
                String skuEcode = orderRefund.getPsCSkuEcode();
                // 退单上的申请数量
                BigDecimal qtyRefund = Objects.isNull(orderRefund.getQtyRefund()) ? BigDecimal.ZERO : orderRefund.getQtyRefund();
                // 数据准备
                if (!skuQtyHashApply.containsKey(skuEcode)) {
                    skuQtyHashApply.put(skuEcode, BigDecimal.ZERO);
                }

                // 计算可退
                BigDecimal qtyCanReturn = calQtyCanReturn4Sku(skuItems.get(skuEcode));
                if (BigDecimal.ZERO.compareTo(qtyCanReturn) == 0){
                    //可退数量为0 不能生成
                    return false;
                }
                // 实际可退应该减去已申请数（应对同一个SKU多行的情况）
                qtyCanReturn = qtyCanReturn.subtract(skuQtyHashApply.get(skuEcode));

                if (qtyRefund.compareTo(qtyCanReturn) > 0) {
                    // 申请数量大于可退数量了
                    return false;
                } else {
                    skuQtyHashApply.put(skuEcode, skuQtyHashApply.get(skuEcode).add(qtyRefund));
                }
            }
        }

        return true;
    }


    /**
     * 计算可退
     *
     * @param orderItems
     * @return
     */
    public BigDecimal calQtyCanReturn4Sku(List<OcBOrderItem> orderItems) {
        BigDecimal qtyCanReturn = null;

        if (CollectionUtils.isNotEmpty(orderItems)) {
            BigDecimal qtySum = BigDecimal.ZERO;
            BigDecimal qtyReturnApplySum = BigDecimal.ZERO;

            for (int i = 0; i < orderItems.size(); i++) {
                OcBOrderItem orderItem = orderItems.get(i);
                qtySum = qtySum.add(Objects.isNull(orderItem.getQty()) ? BigDecimal.ZERO : orderItem.getQty()).setScale(8, BigDecimal.ROUND_HALF_UP);
                qtyReturnApplySum = qtyReturnApplySum.add(Objects.isNull(orderItem.getQtyReturnApply()) ? BigDecimal.ZERO : orderItem.getQtyReturnApply()).setScale(8, BigDecimal.ROUND_HALF_UP);
            }

            // 计算公式
            qtyCanReturn = qtySum.subtract(qtyReturnApplySum);
        }

        return Objects.isNull(qtyCanReturn) ? BigDecimal.ZERO : qtyCanReturn;
    }

    /**
     * 计算可退
     *
     * @param orderItem
     * @return
     */
    public BigDecimal calQtyCanReturn4Sku(OcBOrderItem orderItem) {
        BigDecimal qtyCanReturn = null;

        if (Objects.nonNull(orderItem)) {
            BigDecimal qtySum = Objects.isNull(orderItem.getQty()) ? BigDecimal.ZERO : orderItem.getQty()
                    .setScale(8, BigDecimal.ROUND_HALF_UP);
            BigDecimal qtyReturnApplySum = Objects.isNull(orderItem.getQtyReturnApply()) ? BigDecimal.ZERO : orderItem
                    .getQtyReturnApply().setScale(8, BigDecimal.ROUND_HALF_UP);
            // 计算公式
            qtyCanReturn = qtySum.subtract(qtyReturnApplySum);
        }
        return Objects.isNull(qtyCanReturn) ? BigDecimal.ZERO : qtyCanReturn;
    }

    /**
     * 获取实例
     *
     * @return
     */
    public static OmsOrderQtyCalculateService getInstance() {
        return ApplicationContextHandle.getBean(OmsOrderQtyCalculateService.class);
    }

}
