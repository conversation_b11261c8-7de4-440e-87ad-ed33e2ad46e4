package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBOrderLockLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface IpBOrderLockLogMapper extends ExtentionMapper<IpBOrderLockLog> {

    /**
     * 依据TID进行查通用订单数据
     *
     * @param orderLockId 平台交易单号
     * @return 通用订单数据
     */
    @Select("SELECT * FROM IP_B_ORDER_LOCK_LOG WHERE IP_B_ORDER_LOCK_ID=#{orderLockId} ORDER BY MODIFIEDDATE DESC  LIMIT 1")
    IpBOrderLockLog selectOrderLockByTopOne(@Param("orderLockId") Long orderLockId);
}