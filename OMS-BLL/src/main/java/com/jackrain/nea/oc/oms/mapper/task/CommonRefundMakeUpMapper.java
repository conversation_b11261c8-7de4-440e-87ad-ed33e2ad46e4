package com.jackrain.nea.oc.oms.mapper.task;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface CommonRefundMakeUpMapper extends BaseMapper {


    /***
     * 查询DRDS的分库 RefundNo
     *
     * @param node node name
     * @param tableName table name
     * @param returnField return fiele 分库键
     * @param size size
     * @param isTrans trans status
     * @param lessThanTransCnt less than trans Count
     * @return RefundNo
     */
    @SelectProvider(type = CommonRefundMakeUpMapper.SqlProvider.class, method = "selectDynamicRefundNo")
    List<String> selectDynamicRefundNo(@Param("node") String node, @Param("tableName") String tableName,
                                       @Param("returnField") String returnField,
                                       @Param("size") int size, @Param("isTrans") int isTrans,
                                       @Param("lessThanTransCnt") int lessThanTransCnt, @Param("minutes") int minutes);


    class SqlProvider {

        /**
         * 查询DRDS的分库RefundId
         *
         * @param node             node name
         * @param tableName        table name
         * @param size             page size
         * @param isTrans          trans status
         * @param lessThanTransCnt trans count
         * @return refund_id
         */
        public String selectDynamicRefundNo(@Param("node") String node, @Param("tableName") String tableName,
                                            @Param("returnField") String returnField,
                                            @Param("size") int size, @Param("isTrans") int isTrans,
                                            @Param("lessThanTransCnt") int lessThanTransCnt, @Param("minutes") int minutes) {

            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=");
            sb.append(node);
            sb.append("*/ ");
            sb.append(" SELECT ");
            sb.append(returnField);
            sb.append(" FROM ");
            sb.append(tableName);
            sb.append(" WHERE istrans = ");
            sb.append(isTrans);
            if (lessThanTransCnt > 0) {
                sb.append(" AND IFNULL(trans_count,0) < ");
                sb.append(lessThanTransCnt);
            }
            if (minutes > 0) {
                sb.append(" AND creationdate > (DATE_SUB(NOW(),INTERVAL ");
                sb.append(minutes);
                sb.append(" MINUTE))");
            }
            sb.append(" ORDER BY modifieddate DESC");
            sb.append(" LIMIT ");
            sb.append(size);
            return sb.toString();
        }
    }

}


