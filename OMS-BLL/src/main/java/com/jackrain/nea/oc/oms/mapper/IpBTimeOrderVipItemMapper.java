package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItem;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipItemEx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface IpBTimeOrderVipItemMapper extends ExtentionMapper<IpBTimeOrderVipItem> {

    @Select("SELECT * FROM ip_b_time_order_vip_item WHERE ip_b_time_order_vip_id=#{orderId}")
    List<IpBTimeOrderVipItemEx> selectOrderItemList(@Param("orderId") long orderId);

    @Select("SELECT * FROM ip_b_time_order_vip_item WHERE ip_b_time_order_vip_id=#{orderId} limit 1")
    IpBTimeOrderVipItem selectOneOrderItem(@Param("orderId") long orderId);
}