package com.jackrain.nea.oc.oms.matcher.live;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description： 直播解析策略匹配器获取工厂
 * Author: RESET
 * Date: Created in 2020/6/15 21:45
 * Modified By:
 */
@Component
public class LiveMatcherFactory {

    // 暂存
    private final Map<Integer, ILiveMatcher> liveMatcherMap = new ConcurrentHashMap<>();

    /**
     * 注册
     *
     * @param matchers
     */
    @Autowired
    public void init(List<ILiveMatcher> matchers) {
        if (!CollectionUtils.isEmpty(matchers)) {
            matchers.forEach(m -> {
                liveMatcherMap.put(m.getLiveStrategyType(), m);
            });
        }
    }

    /**
     * 按类型获取解析器
     *
     * @param strategyType
     * @return
     */
    public ILiveMatcher getLiveMatcher(Integer strategyType) {
        ILiveMatcher matcher = liveMatcherMap.get(strategyType);

        if (Objects.isNull(matcher)) {
            throw new NDSException("获取解析器出错，该渠道无对应的解析器[" + (Objects.isNull(strategyType) ? null : strategyType) + "]");
        }

        return matcher;
    }

    /**
     * 取个实例
     *
     * @return
     */
    public static LiveMatcherFactory getInstance() {
        return ApplicationContextHandle.getBean(LiveMatcherFactory.class);
    }

}
