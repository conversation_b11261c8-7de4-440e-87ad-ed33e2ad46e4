package com.jackrain.nea.oc.oms.services.audit;

import com.google.common.collect.Maps;
import com.jackrain.nea.oc.oms.model.enums.OmsMethod;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @auther: 黄志优
 * @Date: 2020/11/3 17:00
 * @Description: 审单引擎
 */
@Slf4j
@Service
public class AuditStrategyHandlerFactory {

    @Autowired
    private Map<String, AuditStrategyHandler> stateHashMap = new HashMap<>();

    private List<Map.Entry<Integer, AuditStrategyHandler>> sortMap;

    private boolean isInitialled;

    public boolean doHandle(OcBOrderRelation orderInfo, User operateUser) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("执行审单引擎.{}", orderInfo.getOrderId()), orderInfo.getOrderId());
        }

        OmsMethod omsMethod = orderInfo.getOmsMethod();

        if (!this.isInitialled) {
            this.initSortMap();
        }
        for (Map.Entry<Integer, AuditStrategyHandler> auditStrategy : sortMap) {
            AuditStrategyHandler value = auditStrategy.getValue();

            //手动审核掉过策略
            if (OmsMethod.Manual.equals(omsMethod)
                    && auditStrategy.getKey().equals(AuditEnum.STORE_AUDIT_STRATEGY.getValue())) {
                continue;
            }

            boolean auditHandlerFlag = value.doHandle(orderInfo, operateUser);
            if (!auditHandlerFlag) {
                return false;
            }

        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("审单引擎执行完成.{}", orderInfo.getOrderId()), orderInfo.getOrderId());
        }

        return true;
    }

    @PostConstruct
    protected void initSortMap() {
        Map<Integer, AuditStrategyHandler> map = Maps.newHashMap();
        for (Map.Entry<String, AuditStrategyHandler> stringHandlerEntry : stateHashMap.entrySet()) {
            AuditStrategyHandler value = stringHandlerEntry.getValue();
            map.put(value.getSort(stringHandlerEntry.getKey()), value);
        }
        List<Map.Entry<Integer, AuditStrategyHandler>> list = new ArrayList<>(map.entrySet());
        Collections.sort(list, Comparator.comparing(Map.Entry::getKey));
        sortMap = list;

        isInitialled = true;
    }

}
