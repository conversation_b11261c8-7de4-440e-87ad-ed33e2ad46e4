package com.jackrain.nea.oc.oms.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date ：2020/12/7 4:47 下午
 * description ：
 * @ Modified By：
 */
@Configuration
@Data
public class VipcomTimeOrderConfing {
    @Value("${r3.oc.oms.vip.time.order.release.stock.interval:-24}")
    private int timeOrderReleaseStockInterval;

    /**
     * 批量释放时效订单 分批请求sg 条数
     */
    @Value("${r3.oc.oms.vip.time.order.release.stock.num:300}")
    private int timeOrderReleaseStockNum;
}
