package com.jackrain.nea.oc.oms.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/10/5
 */
@Data
@Configuration
public class OmsModifyConfig {

    @NacosValue(value = "${r3.oms.oc.modify.warehouse.mq.tag:}", autoRefreshed = true)
    private String modifyTag;

    @NacosValue(value = "${r3.oms.oc.modify.warehouse.mq.topic:}", autoRefreshed = true)
    private String modifyTopic;


}
