package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.permission.Permissions;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;

/**
 * es 查询,权限过滤
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/13
 */
@Slf4j
public class Permission4ESUtil {


    public static boolean permissionHandler(User usr, JSONObject whereKey, Permission4SlfEnum perEnum, String... col) {

        notNullCsm.accept(usr, "当前登录用户不存在");
        if (usr.isAdmin()) {
            return true;
        }

        notNullCsm.accept(whereKey, "ES查询条件不能为空");

        String key = null;
        String permissionKey = null;
        switch (perEnum) {
            case SHOP:
                key = "CP_C_SHOP_ID";
                permissionKey = "CP_C_SHOP_PERMISSION_ID";
                break;
            case WAREHOUSE:
                key = "CP_C_PHY_WAREHOUSE_ID";
                permissionKey = "CP_C_WAREHOUSE_ID";
                break;
//            case LOGISTICS:
//                key = "CP_C_LOGISTICS_ID";
//                permissionKey = "CP_C_LOGISTICS_ID";
//                break;
            default:
                notNullCsm.accept(null, "未知权限标识");
                break;
        }
        key = col.length > 0 ? col[0] : key;

        boolean handler = new PermissionHandler(permissionKey, key).handler(usr, whereKey);
        return handler;
    }


    private static BiConsumer<Object, String> notNullCsm = (o, s) -> {
        if (o == null) {
            throw new NDSException(s);
        }
    };

    static class PermissionHandler {

        private String columnKey;
        private String permissionKey;

        PermissionHandler(String permissionKey, String key) {
            this.columnKey = key;
            this.permissionKey = permissionKey;
        }

        /**
         * @param usr
         * @param whereKey
         * @return false: no permission
         */
        public boolean handler(User usr, JSONObject whereKey) {

            List<Long> permissions = PermissionFun.apply(usr, permissionKey);
            if (permissions == null || permissions.size() < 1) {
                if (permissions == null) {
                    return false;
                }
            }

            if (!whereKey.keySet().contains(columnKey)) {
                PermissionCsm.accept(whereKey, permissions);
                return true;
            }

            String shopString = whereKey.getString(columnKey);
            if (shopString == null || shopString.length() < 1 || StringUtils.equalsIgnoreCase("null", shopString)) {
                PermissionCsm.accept(whereKey, permissions);
                return true;
            }

            try {

                List<Long> originList = JSON.parseArray(shopString, Long.class);
                JSONArray tmpAry = new JSONArray();
                for (Long o : originList) {
                    if (permissions.contains(o)) {
                        tmpAry.add(o);
                    }
                }
                if (tmpAry.size() < 1) {
                    return false;
                }
                whereKey.put(columnKey, tmpAry);
            } catch (Exception e) {
                if (!permissions.contains(Long.valueOf(shopString))) {
                    return false;
                }
            }
            return true;
        }

        private BiConsumer<JSONObject, List<Long>> PermissionCsm = (jsonObject, longs) -> jsonObject
                .put(this.columnKey, JSONArray.parseArray(JSON.toJSONString(longs)));

        private BiFunction<User, String, List<Long>> PermissionFun = (user, s) -> Permissions
                .getReadableDatas(s, user.getId());

    }

}
