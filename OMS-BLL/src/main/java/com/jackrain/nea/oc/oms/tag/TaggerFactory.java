package com.jackrain.nea.oc.oms.tag;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description： tagger注册工厂
 * Author: RESET
 * Date: Created in 2020/7/8 22:11
 * Modified By:
 */
@Component
public class TaggerFactory {

    // 标签类列表
    private List<ITagger> taggerList = null;
    // 按标签类型分组
    private Map<Integer, ITagger> taggerMap = new HashMap<>();

    @Autowired
    public void register(List<ITagger> taggers) {
        if (CollectionUtils.isNotEmpty(taggers)) {
            // 直接过滤掉shouldTag = false的tagger
            taggerList = taggers.stream().filter(t -> t.shouldTag()).collect(Collectors.toList());
        } else {
            taggerList = new ArrayList<>();
        }

        // 排个序
        taggerList.sort(Comparator.comparing(t -> t.taggerOrder()));
        register2Map(taggerList);
    }

    /**
     * 封成Map
     *
     * @param taggers
     */
    private void register2Map(List<ITagger> taggers) {
        for (int i = 0; i < taggers.size(); i++) {
            ITagger t = taggers.get(i);

            if (!taggerMap.containsKey(t.taggerType())) {
                taggerMap.put(t.taggerType(), t);
            }
        }
    }

    /**
     * 获取列表
     *
     * @return
     */
    public List<ITagger> getTaggers() {
        return taggerList;
    }

    /**
     * 按类型取
     *
     * @param type
     * @return
     */
    public ITagger getTaggerByType(Integer type) {
        ITagger t = taggerMap.get(type);

        if (Objects.nonNull(t)) {
            return t;
        }

        throw new NDSException("找不到对应标签器，type=" + type);
    }

    /**
     * 获取实例
     *
     * @return
     */
    public static TaggerFactory get() {
        return ApplicationContextHandle.getBean(TaggerFactory.class);
    }

}
