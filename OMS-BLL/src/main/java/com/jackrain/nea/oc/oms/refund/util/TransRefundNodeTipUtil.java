package com.jackrain.nea.oc.oms.refund.util;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.IpBJingdongRefundMapper;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatRefundMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTaobaoRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.TransNodeTipEnum;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatRefund;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.web.face.User;

import java.util.Date;

/**
 * @Desc : 逆向转单节点提示
 * <AUTHOR> xiWen
 * @Date : 2020/11/19
 */
public class TransRefundNodeTipUtil {

    private static IpBTaobaoRefundMapper ipBTaobaoRefundMapper;

    private static IpBJingdongRefundMapper ipBJingdongRefundMapper;

    private static IpBStandplatRefundMapper ipBStandplatRefundMapper;

    public static boolean updateMidRefund(BaseModel refund, User user) {

        JSONObject jsn = new JSONObject();
        jsn.put("modifieddate", new Date());
        jsn.put("transdate", new Date());

        if (refund instanceof IpBTaobaoRefund) {

            IpBTaobaoRefund taoBaoRefund = (IpBTaobaoRefund) refund;
            jsn.put("istrans", taoBaoRefund.getIstrans());
            jsn.put("refund_id", taoBaoRefund.getRefundId());
            jsn.put("sysremark", taoBaoRefund.getSysremark());
            jsn.put("trans_fail_reason", taoBaoRefund.getTransFailReason());
            return getTaoBaoMapper().updateRefundOrder(jsn) > 0;
            // return getMapperBean(IpBTaobaoRefundMapper.class, ipBTaobaoRefundMapper).updateRefundOrder(jsn) > 0;

        }
        if (refund instanceof IpBJingdongRefund) {

            IpBJingdongRefund jinDongRefund = (IpBJingdongRefund) refund;
            jsn.put("istrans", jinDongRefund.getIstrans());
            jsn.put("sysremark", jinDongRefund.getSysremark());
            jsn.put("afsserviceid", jinDongRefund.getAfsserviceid());
            jsn.put("trans_fail_reason", jinDongRefund.getTransFailReason());
            return getJinDongMapper().updateRefundOrder(jsn) > 0;
        }

        if (refund instanceof IpBStandplatRefund) {

            IpBStandplatRefund ipStandardRefund = (IpBStandplatRefund) refund;
            jsn.put("return_no", ipStandardRefund.getReturnNo());
            jsn.put("istrans", ipStandardRefund.getIstrans());
            jsn.put("sysremark", ipStandardRefund.getSysremark());
            jsn.put("trans_fail_reason", ((IpBStandplatRefund) refund).getTransFailReason());
            return getStandardMapper().updateFailReason(jsn) > 0;
        }

        return false;
    }

    /**
     * @param model   淘宝退款单
     * @param tipEnum 预设标识
     */
    public static void taoBaoTransTipCAS(IpBTaobaoRefund model, TransNodeTipEnum tipEnum) {
        if (isSetAllow(model.getTransFailReason(), tipEnum)) {
            model.setTransFailReason(tipEnum.val());
        }
    }

    /**
     * @param model   淘宝退款单
     * @param tipEnum 预设标识
     */
    public static void jinDongTransTipCAS(IpBJingdongRefund model, TransNodeTipEnum tipEnum) {
        if (isSetAllow(model.getTransFailReason(), tipEnum)) {
            model.setTransFailReason(tipEnum.val());
        }
    }

    /**
     * @param model   淘宝退款单
     * @param tipEnum 预设标识
     */
    public static void standardTransTipCAS(IpBStandplatRefund model, TransNodeTipEnum tipEnum) {
        if (isSetAllow(model.getTransFailReason(), tipEnum)) {
            model.setTransFailReason(tipEnum.val());
        }
    }

    /**
     * 1. 已仓库发货需要拦截, 该标识不允许被覆盖
     * 2. 其它标识允许默认值0覆盖, 不允许其它值盖
     *
     * @param tipEnum 转换标识枚举
     */
    private static boolean isSetAllow(Integer reason, TransNodeTipEnum tipEnum) {
        if (TransNodeTipEnum.DELIVERY_NEED_INTERCEPT.val().equals(reason)) {
            return false;
        }
        if (TransNodeTipEnum.DEFAULT.val().equals(reason) || reason == null) {
            return true;
        }
        if (TransNodeTipEnum.DEFAULT == tipEnum) {
            return true;
        }
        return false;
    }


    /**
     * 更新淘宝转单节点提示
     *
     * @param taoBaoRefund 淘宝退款
     * @param user         User
     * @return bool
     */
    public static boolean updateTaoBaoTransTip(IpBTaobaoRefund taoBaoRefund, User user) {
        JSONObject jsn = new JSONObject();
        jsn.put("refund_id", taoBaoRefund.getRefundId());
        jsn.put("trans_fail_reason", taoBaoRefund.getTransFailReason());
        jsn.put("transdate", new Date());
        jsn.put("modifieddate", new Date());
        return getTaoBaoMapper().updateRefundOrder(jsn) > 0;
    }

    /**
     * 更新京东转单节点提示
     *
     * @param jinDongRefund 京东退款
     * @param user          User
     * @return boolean
     */
    public static boolean updateJingDongTransTip(IpBJingdongRefund jinDongRefund, User user) {
        JSONObject jsn = new JSONObject();
        jsn.put("afsserviceid", jinDongRefund.getAfsserviceid());
        jsn.put("trans_fail_reason", jinDongRefund.getTransFailReason());
        jsn.put("transdate", new Date());
        jsn.put("modifieddate", new Date());
        return getJinDongMapper().updateTransTip(jsn) > 0;
    }


    /**
     * 获取淘宝mapper
     *
     * @return IpBTaoBaoRefundMapper
     */
    private static IpBTaobaoRefundMapper getTaoBaoMapper() {

        if (ipBTaobaoRefundMapper == null) {
            ipBTaobaoRefundMapper = ApplicationContextHandle.getBean(IpBTaobaoRefundMapper.class);
            if (ipBTaobaoRefundMapper == null) {
                throw new NDSException("IpBTaoBaoRefundMapper Not Found");
            }
        }
        return ipBTaobaoRefundMapper;
    }

    private static IpBJingdongRefundMapper getJinDongMapper() {
        if (ipBJingdongRefundMapper == null) {
            ipBJingdongRefundMapper = ApplicationContextHandle.getBean(IpBJingdongRefundMapper.class);
            if (ipBJingdongRefundMapper == null) {
                throw new NDSException("IpBTaoBaoRefundMapper Not Found");
            }
        }
        return ipBJingdongRefundMapper;
    }

    private static IpBStandplatRefundMapper getStandardMapper() {
        if (ipBStandplatRefundMapper == null) {
            ipBStandplatRefundMapper = ApplicationContextHandle.getBean(IpBStandplatRefundMapper.class);
            if (ipBStandplatRefundMapper == null) {
                throw new NDSException("IpBTaoBaoRefundMapper Not Found");
            }
        }
        return ipBStandplatRefundMapper;
    }

    private static <T> T getMapperBean(Class<T> clz, T e) {
        if (e == null) {
            e = ApplicationContextHandle.getBean(clz);
            if (e == null) {
                throw new NDSException("BeanMapper Not Found");
            }
        }
        return e;
    }


}
