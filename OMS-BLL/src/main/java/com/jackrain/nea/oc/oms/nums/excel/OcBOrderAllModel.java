package com.jackrain.nea.oc.oms.nums.excel;

import com.jackrain.nea.util.excel.XlsAno;
import com.jackrain.nea.util.excel.XlsDBAno;
import com.jackrain.nea.util.excel.XlsSt;
import com.jackrain.nea.util.excel.XlsTyp;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: xiWen.z
 * create at: 2019/8/12 0012
 */
@XlsDBAno(name = "oc_b_order", desc = "全渠道订单", index = 0, sort = "id:asc", st = {XlsSt.DB, XlsSt.ES, XlsSt.R3})
public class OcBOrderAllModel {


    @XlsAno(name = "id", index = 0, desc = "订单ID")
    private Long id;

    @XlsAno(name = "bill_no", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 1, desc = "单据编号")
    private String billNo;

    @XlsAno(name = "order_tag", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 2, desc = "标签")
    private String orderTag;

    @XlsAno(name = "order_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 3, desc = "订单状态")
    private Integer orderStatus;


    @XlsAno(name = "cp_c_shop_title", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 4, desc = "店铺名称")
    private String cpCShopTitle;

    @XlsAno(name = "source_code", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 5, desc = "平台单号")
    private String sourceCode;

    @XlsAno(name = "qty_all", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 6, desc = "商品总数")
    private BigDecimal qtyAll;

    @XlsAno(name = "user_nick", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 7, desc = "买家昵称")
    private String userNick;

    @XlsAno(name = "receiver_name", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 8, desc = "收货人")
    private String receiverName;

    @XlsAno(name = "receiver_mobile", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 9, desc = "收货人手机")
    private String receiverMobile;

    @XlsAno(name = "cp_c_region_province_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 10, desc = "省")
    private String cpCRegionProvinceEname;

    @XlsAno(name = "cp_c_region_city_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 11, desc = "市")
    private String cpCRegionCityEname;

    @XlsAno(name = "cp_c_region_area_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 12, desc = "区")
    private String cpCRegionAreaEname;

    @XlsAno(name = "receiver_address", value = {XlsSt.JOIN, XlsSt.NOTNULL}, type = XlsTyp.STRING, index = 13, desc = "收货人地址")
    private String receiverAddress;

    @XlsAno(name = "cp_c_phy_warehouse_ecode", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 14, desc = "实体仓编码")
    private String  cpCPhyWarehouseEcode;

    @XlsAno(name = "cp_c_phy_warehouse_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 14, desc = "发货仓库")
    private String cpCPhyWarehouseEname;

    @XlsAno(name = "cp_c_logistics_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 16, desc = "快递公司")
    private String cpCLogisticsEname;

    @XlsAno(name = "expresscode", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 17, desc = "快递单号")
    private String expresscode;


    @XlsAno(name = "creationdate", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 18, desc = "创建时间")
    private Date creationdate;

    @XlsAno(name = "presale_deposit_time", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 19, desc = "定金时间")
    private Date presaleDepositTime;

    @XlsAno(name = "pay_time", value = {XlsSt.NORMAL}, type = XlsTyp.DATE, index = 20, desc = "付款时间")
    private Date payTime;

    @XlsAno(name = "audit_time", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 21, desc = "审核时间")
    private Date auditTime;

    @XlsAno(name = "scan_time", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 22, desc = "出库时间")
    private Date scanTime;

    @XlsAno(name = "seller_memo", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 23, desc = "卖家备注")
    private String sellerMemo;

    @XlsAno(name = "buyer_message", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 24, desc = "买家留言")
    private String buyerMessage;

    @XlsAno(name = "order_type", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 25, desc = "订单类型")
    private Integer orderType;

    @XlsAno(name = "double11_presale_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 26, desc = "预售状态")
    private Integer double11PresaleStatus;


    @XlsAno(name = "product_amt", type = XlsTyp.DOUBLE, index = 27, desc = "商品总额")
    private BigDecimal productAmt;

    @XlsAno(name = "distribution_time", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 28, desc = "配货时间")
    private Date distributionTime;


    @XlsAno(name = "wms_cancel_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 29, desc = "wms撤回状态")
    private Integer wmsCancelStatus;

    @XlsAno(name = "return_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 30, desc = "退货状态")
    private Integer returnStatus;

    @XlsAno(name = "product_discount_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 31, desc = "商品优惠金额")
    private BigDecimal productDiscountAmt;

    @XlsAno(name = "adjust_amt", type = XlsTyp.DOUBLE, index = 32, desc = "调整金额")
    private BigDecimal adjustAmt;

    @XlsAno(name = "ship_amt", type = XlsTyp.DOUBLE, index = 33, desc = "配送费用")
    private BigDecimal shipAmt;

    @XlsAno(name = "service_amt", type = XlsTyp.DOUBLE, index = 34, desc = "服务费")
    private BigDecimal serviceAmt;

    @XlsAno(name = "order_amt", type = XlsTyp.DOUBLE, index = 35, desc = "订单总额")
    private BigDecimal orderAmt;

    @XlsAno(name = "received_amt", type = XlsTyp.DOUBLE, index = 36, desc = "已收金额")
    private BigDecimal receivedAmt;

    @XlsAno(name = "amt_receive", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 37, desc = "应收金额")
    private BigDecimal amtReceive;

    @XlsAno(name = "jd_receive_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 48, desc = "应收平台金额(京东)")
    private BigDecimal jdReceiveAmt;

    @XlsAno(name = "jd_settle_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 39, desc = "京东结算金额")
    private BigDecimal jdSettleAmt;

    @XlsAno(name = "cod_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 40, desc = "到付代收金额")
    private BigDecimal codAmt;


    @XlsAno(name = "invoice_header", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 41, desc = "开票抬头")
    private String invoiceHeader;

    @XlsAno(name = "invoice_content", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 42, desc = "开票内容")
    private String invoiceContent;

    @XlsAno(name = "suffix_info", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 43, desc = "订单补充信息")
    private String suffixInfo;

    @XlsAno(name = "sysremark", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 44, desc = "系统备注")
    private String sysremark;

    @XlsAno(name = "inside_remark", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 45, desc = "内部备注")
    private String insideRemark;

    @XlsAno(name = "sg_b_out_bill_no", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 49, desc = "出库通知单")
    private String sgBOutBillNo;

    @XlsAno(name = "ownerename", desc = "创建人", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 60)
    private String ownerename;

    @XlsAno(name = "salesman_name", desc = "业务员", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 61)
    private String salesmanName;

    @XlsAno(name = "sales_group_name", desc = "销售组", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 62)
    private String salesGroupName;

    @XlsAno(name = "business_type_name", desc = "业务类型", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 63)
    private String businessTypeName;

    @XlsAno(name = "anchor_id", desc = "主播ID", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 64)
    private String anchorId;

    @XlsAno(name = "anchor_name", desc = "主播昵称", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 65)
    private String anchorName;

    @XlsAno(name = "item_anchor_id", desc = "明细主播ID", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 64)
    private String itemAnchorId;

    @XlsAno(name = "item_anchor_name", desc = "明细主播昵称", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 65)
    private String itemAnchorName;

    @XlsAno(name = "audit_name", desc = "审核人", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 66)
    private String auditName;

    @XlsAno(name = "sales_department_name", desc = "销售部门", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 67)
    private String salesDepartmentName;

    @XlsAno(name = "tag_name", desc = "标签", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 68)
    private String tagName;

    @XlsAno(name = "main_estimate_con_time", desc = "主表预计发货日期", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 69)
    private String mainEstimateConTime;

    @XlsAno(name = "current_cycle_number", desc = "当前期数", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 70)
    private String currentCycleNumber;

    //------明细-------
    @XlsAno(name = "oc_b_order_id", index = 90, desc = "主表ID")
    private Long ocBOrderId;

    @XlsAno(name = "ps_c_pro_ename", desc = "商品名称", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 100)
    private String psCProEname;

    @XlsAno(name = "ps_c_pro_ecode", desc = "商品编码", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 101)
    private String psCProEcode;

    @XlsAno(name = "pt_pro_name", desc = "平台商品标题", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 102)
    private String ptProName;

    @XlsAno(name = "giftbag_sku", desc = "组合商品SKU", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 103)
    private String giftbagSku;

    @XlsAno(name = "ps_c_sku_ecode", desc = "SKU编码", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 104)
    private String psCSkuEcode;

    @XlsAno(name = "ps_c_sku_ename", desc = "SKU名称", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 105)
    private String psCSkuEname;


    @XlsAno(name = "qty", desc = "数量", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 106)
    private BigDecimal qty;

    @XlsAno(name = "standard_weight", desc = "商品重量", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 106)
    private BigDecimal standardWeight;

    @XlsAno(name = "total_volume", desc = "商品体积", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 106)
    private String totalVolume;

    @XlsAno(name = "qty_lost", desc = "缺货数量", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 107)
    private BigDecimal qtyLost;

    @XlsAno(name = "real_out_num", desc = "明细实发数量", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 107)
    private BigDecimal realOutNum;

    @XlsAno(name = "price", desc = "原价", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 108)
    private BigDecimal price;

    @XlsAno(name = "price_list", desc = "零售价", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 109)
    private BigDecimal priceList;

    @XlsAno(name = "real_amt", desc = "成交金额", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 110)
    private BigDecimal realAmt;

    @XlsAno(name = "amt_discount", desc = "优惠金额", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 111)
    private BigDecimal amtDiscount;

    @XlsAno(name = "adjust_amt_item", desc = "调整金额(明细)", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 112)
    private BigDecimal adjustAmtItem;

    @XlsAno(name = "order_split_amt", desc = "平摊金额", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 113)
    private BigDecimal orderSplitAmt;

    @XlsAno(name = "refund_status", desc = "主表退款状态", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 114)
    private Integer refundStatus;

    @XlsAno(name = "item_refund_status", desc = "明细取消状态", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 114)
    private String itemRefundStatus;

    @XlsAno(name = "is_gift", desc = "是否赠品", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 115)
    private Integer isGift;

    @XlsAno(name = "num_iid", desc = "平台商品ID", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 116)
    private String numIid;

    @XlsAno(name = "sku_numiid", desc = "平台SKUID", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 117)
    private String skuNumiid;

    @XlsAno(name = "estimate_con_time", desc = "明细预计发货日期", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 118)
    private String estimateConTime;

    @XlsAno(name = "labeling_requirements", desc = "增值服务", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 118)
    private String labelingRequirements;

    @XlsAno(name = "merge_source_code", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 119, desc = "合单平台单号")
    private String mergeSourceCode;

    @XlsAno(name = "ooid", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 120, desc = "子订单编号")
    private String ooid;

    @XlsAno(name = "exception_type", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 121, desc = "异常类型")
    private String exceptionType;

    @XlsAno(name = "exception_explain", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 122, desc = "异常说明")
    private String exceptionExplain;

    @XlsAno(name = "expiry_date_range", desc = "商品效期", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 123)
    private String expiryDateRange;

    @XlsAno(name = "reserve_bigint02", desc = "优先最便宜快递", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 123)
    private String reserveBigint02;

    @XlsAno(name = "gw_source_group", desc = "订单来源系统", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 124)
    private String gwSourceGroup;

    @XlsAno(name = "order_source_platform_ecode", desc = "交易来源平台单号", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 125)
    private String orderSourcePlatformEcode;

    @XlsAno(name = "hold_reason_name", desc = "HOLD单原因", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 126)
    private String holdReasonName;

    @XlsAno(name = "detention_reason_name", desc = "卡单原因", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 127)
    private String detentionReasonName;

    @XlsAno(name = "appoint_logistics_ename", desc = "指定快递", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 128)
    private String appointLogisticsEname;

    @XlsAno(name = "generic_mark", desc = "通用标记", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 129)
    private String genericMark;

    @XlsAno(name = "carpool_no", desc = "拼车单号", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 130)
    private String carpoolNo;

    @XlsAno(name = "sale_product_attr", desc = "销售商品属性", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 131)
    private String saleProductAttr;

    @XlsAno(name = "detention_reason", desc = "卡单策略名称", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 132)
    private String detentionReason;

    @XlsAno(name = "the_latest_delivery_time", desc = "最晚发货时间", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 133)
    private String theLatestDeliveryTime;
}

