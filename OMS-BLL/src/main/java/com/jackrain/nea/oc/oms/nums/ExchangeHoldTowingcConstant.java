package com.jackrain.nea.oc.oms.nums;

/**
 * description:枚举值
 * @Author:  l<PERSON><PERSON><PERSON>
 * @Date 2021/10/19 8:05 下午
 */
public class ExchangeHoldTowingcConstant {


    public final static String TAB_ORDER_EXCHANGE_HOLD_TOWING_TASK = "order_exchange_hold_towing_task";

    public final static String STATUS_0 = "0"; //未传

    public final static String STATUS_1 = "1";//转换中

    public final static String STATUS_3 = "3";//失败

    public final static String STATUS_4 = "4";//成功

    /**
     * wing 返回码成功/失败
     */
    public static final String WING_RESULT_SUCCESS_CODE = "1";
    public static final String WING_RESULT_FAIL_CODE = "0";
    public static final String WING_RESULT_FAILALL_CODE = "-1";
}
