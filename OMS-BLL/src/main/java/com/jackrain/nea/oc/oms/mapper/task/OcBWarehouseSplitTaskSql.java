package com.jackrain.nea.oc.oms.mapper.task;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * @author: 易邵峰
 * @since: 2020-02-17
 * create at : 2020-02-17 22:48
 */
@Slf4j
public class OcBWarehouseSplitTaskSql {

    public String selectByNodeSql(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        String nodeName = (String) para.get("nodeName");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int orderStatus = para.get("status") != null ? (int) para.get("status") : 0;

        // 如果是DRDS数据库 必传参节点名称nodeName
        if (StringUtils.isEmpty(nodeName)) {
            return null;
        }
        sql.append("/*!TDDL:NODE=" + nodeName + "*/").append("select order_id from ")
                .append(taskTableName)
                .append("  where status=")
                .append(orderStatus)
                .append(limitStr);
        return sql.toString();
    }

    /**
     * @param para
     * @return java.lang.String
     * <AUTHOR>
     * @Description 查询推送wms订单(推单延时过滤 只有oc_b_to_wms_task可以使用)
     * @Date 13:23 2020/9/17
     **/
    public String selectByNodeSqlWithPushDelay(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int orderStatus = para.get("status") != null ? (int) para.get("status") : 0;
        sql.append("select order_id from ")
                .append(taskTableName)
                .append("  where status=")
                .append(orderStatus)
                .append(" and push_delay_date < now()")
                .append(" order by modifieddate asc")
                .append(limitStr);
        return sql.toString();
    }

    /**
     * @param para
     * @return java.lang.String
     * <AUTHOR>
     * @Description 查询推送wms订单推单延时时间为空的数据（只有oc_b_to_wms_task可以使用）
     * @Date 13:26 2020/9/17
     **/
    public String selectByNodeSqlWithPushDelayNull(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int orderStatus = para.get("status") != null ? (int) para.get("status") : 0;
        sql.append("select order_id from ")
                .append(taskTableName)
                .append("  where status=")
                .append(orderStatus)
                .append("  and push_delay_date is null")
                .append(limitStr);
        return sql.toString();
    }
}
