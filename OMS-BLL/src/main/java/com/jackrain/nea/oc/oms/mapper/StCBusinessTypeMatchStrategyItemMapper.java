package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCBusinessTypeMatchStrategyItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCBusinessTypeMatchStrategyItemMapper extends ExtentionMapper<StCBusinessTypeMatchStrategyItem> {


    @Select("<script> "
            + "SELECT * FROM st_c_business_type_match_strategy_item WHERE st_c_business_type_match_strategy_id "
            + "in <foreach item='item' index='index' collection='mainIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<StCBusinessTypeMatchStrategyItem> selectStCBusinessTypeMatchStrategyItemListByMainId(@Param("mainIds") List<Long> mainIds);
}

