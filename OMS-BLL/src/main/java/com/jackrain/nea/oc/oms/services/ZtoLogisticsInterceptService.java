package com.jackrain.nea.oc.oms.services;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.mq.core.DefaultProducerSend;
import com.burgeon.mq.model.MqSendResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.api.CpcShopQueryCmd;
import com.jackrain.nea.cpext.api.CpCPlatformQueryCmd;
import com.jackrain.nea.cpext.api.GeneralOrganizationCmd;
import com.jackrain.nea.cpext.api.PsCProDimCmd;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.cpext.model.result.WarehouseAddressModel;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.ip.api.intercept.CancelInterceptCmd;
import com.jackrain.nea.ip.api.yunda.YunDaRouterCmd;
import com.jackrain.nea.ip.api.zto.ZtoCancelInterceptCmd;
import com.jackrain.nea.ip.model.request.intercept.CancelInterceptRequest;
import com.jackrain.nea.ip.model.request.zto.ZtoCancelInterceptRequest;
import com.jackrain.nea.ip.model.request.zto.ZtoCreateInterceptRequest;
import com.jackrain.nea.ip.model.result.intercept.CancelInterceptResponse;
import com.jackrain.nea.ip.model.result.yunda.YunDaInterceptResultResponse;
import com.jackrain.nea.ip.model.result.zto.ZtoCancelInterceptResponse;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.constant.Mq5Constants;
import com.jackrain.nea.oc.oms.constant.ZtoLogisticsInterceptTaskTypeConstant;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderLogisticsInterceptMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogisticsServiceTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.request.ZtoLogisticsInterceptTaskRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLogisticsIntercept;
import com.jackrain.nea.oc.oms.nums.LogisticsInterceptStatusEnum;
import com.jackrain.nea.oc.oms.nums.LogisticsInterceptTypeEnum;
import com.jackrain.nea.oc.oms.nums.OrderGenericMarkEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/3/2 17:26
 * @Description
 */
@Slf4j
@Component
public class ZtoLogisticsInterceptService {

    @Resource
    private OcBOrderMapper ocBOrderMapper;

    @Resource
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Getter
    @Resource
    private OcBOrderLogisticsInterceptMapper ocBOrderLogisticsInterceptMapper;

    @Resource
    private CpRpcService cpRpcService;
    @Autowired
    private DefaultProducerSend defaultProducerSend;

    @Resource
    private OmsOrderLogService omsOrderLogService;

    @Resource
    private BusinessSystemParamService businessSystemParamService;

    @Resource
    private StRpcService stRpcService;

    @DubboReference(version = "1.0", group = "cp-ext")
    private GeneralOrganizationCmd generalOrganizationCmd;

    @DubboReference(version = "1.4.0", group = "ip")
    private ZtoCancelInterceptCmd ztoCancelInterceptCmd;

    @DubboReference(version = "1.4.0", group = "ip")
    private YunDaRouterCmd yunDaRouterCmd;

    @DubboReference(version = "1.4.0", group = "ip")
    private CancelInterceptCmd cancelInterceptCmd;

    @DubboReference(version = "1.0", group = "cp")
    private CpcShopQueryCmd cpcShopQueryCmd;

    @DubboReference(version = "1.0", group = "cp-ext")
    private CpCPlatformQueryCmd cpCPlatformQueryCmd;

    @DubboReference(version = "1.0", group = "cp-ext")
    private PsCProDimCmd psCProDimCmd;


    @Autowired
    private ThreadPoolTaskExecutor commonTaskExecutor;

    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    private final String STAND_PLATFORM_AUTOINTERCEPT_EXCLUDE = "business_system:stand_platform_autoIntercept_exclude";

    /**
     * 圆通拦截默认客户编码
     */
    private final String YTO_DEFAULT_CUSTOMERCODE = "K290150774";

    /**
     * 校验拦截订单是否符合条件，有一条或以上符合都返回成功
     *
     * @param orderIds
     * @return
     */
    public ValueHolderV14<List<OcBOrder>> interceptCheck(List<Long> orderIds) {
        return checkData(orderIds, true, true);
    }

    /**
     * 拦截包裹登记
     *
     * @param orderIds
     * @param interceptReason
     * @return
     */
    public ValueHolderV14<Void> interceptCreate(List<Long> orderIds, String interceptReason, User user, String refundNo) {
        log.info(LogUtil.format("ZtoLogisticsInterceptService.interceptCreate,orderIds:{}",
                "ZtoLogisticsInterceptService.interceptCreate"), JSONObject.toJSONString(orderIds));
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        boolean only = orderIds.size() == 1;
        int successNum = 0;
        //校验并过滤出符合条件的订单
        ValueHolderV14<List<OcBOrder>> checkDataV14 = checkData(orderIds, true, false);
        if (!checkDataV14.isOK() || CollectionUtils.isEmpty(checkDataV14.getData())) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("异常包裹登记成功0条，失败" + orderIds.size() + "条！" +
                    "失败原因：订单无物流单号或快递公司不支持拦截或包裹已发起过拦截！");
            return v14;
        }
        List<OcBOrder> ocBOrderList = checkDataV14.getData();
        /*新增时拦截状态为【未拦截】*/
        List<OcBOrderLogisticsIntercept> interceptList = buildInterceptModel(interceptReason, user, ocBOrderList, refundNo);

        //逐条保存并记录日志
        List<OcBOrderLogisticsIntercept> successIntercept = new ArrayList<>();
        ZtoLogisticsInterceptService bean = ApplicationContextHandle.getBean("ztoLogisticsInterceptService");
        for (OcBOrderLogisticsIntercept intercept : interceptList) {
            successNum = bean.saveIntercept(user, successNum, successIntercept, intercept);
        }

        if (successNum == orderIds.size()) {
            //全部成功
            v14.setMessage("异常包裹登记成功，请至【异常包裹登记】查看拦截结果！");
        } else {
            if (only) {
                v14.setMessage("异常包裹登记失败，失败原因：订单无物流单号或快递公司不支持拦截或包裹已发起过拦截！");
            } else {
                v14.setMessage("异常包裹登记成功" + successNum + "条，失败" + (orderIds.size() - successNum) + "条！" +
                        "失败原因：订单无物流单号或快递公司不支持拦截或包裹已发起过拦截！");
            }
        }
        List<Long> successOrderIds = successIntercept.stream()
                .map(OcBOrderLogisticsIntercept::getOrderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(successOrderIds)) {
            makeOrderGenericMark(successOrderIds, OrderGenericMarkEnum.INTERCEPT);
        }
        //异步发送MQ
        commonTaskExecutor.submit(() -> {
            if (CollectionUtils.isNotEmpty(successIntercept)) {
                buildParamAndSendMQ(successIntercept);
            }
        });
        return v14;
    }

    /**
     * 保存拦截登记表
     *
     * @param user
     * @param successNum
     * @param successIntercept
     * @param intercept
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int saveIntercept(User user, int successNum, List<OcBOrderLogisticsIntercept> successIntercept, OcBOrderLogisticsIntercept intercept) {
        int insert = 0;
        try {
            insert = ocBOrderLogisticsInterceptMapper.insert(intercept);
        } catch (Exception e) {
            log.error(LogUtil.format("ZtoLogisticsInterceptService.saveIntercept.error:{},intercept:{}",
                    "ZtoLogisticsInterceptService.saveIntercept.error")
                    , Throwables.getStackTraceAsString(e), JSON.toJSONString(intercept));
        }
        if (insert > 0) {
            successIntercept.add(intercept);
            successNum++;
            //记录按钮日志
            omsOrderLogService.addUserOrderLog(intercept.getOrderId(),
                    intercept.getOrderBillNo(), OrderLogTypeEnum.INTERCEPT_RETURN.getKey(),
                    "包裹拦截退回 异常包裹登记成功", "", "", user);
        }
        return successNum;
    }

    /**
     * 根据零售发货单信息构建拦截登记对象
     *
     * @param interceptReason
     * @param user
     * @param ocBOrderList
     * @return
     */
    private List<OcBOrderLogisticsIntercept> buildInterceptModel(String interceptReason, User user, List<OcBOrder> ocBOrderList, String refundNo) {
        List<OcBOrderLogisticsIntercept> interceptList = new ArrayList<>();
        for (OcBOrder ocBOrder : ocBOrderList) {
            OcBOrderLogisticsIntercept intercept = new OcBOrderLogisticsIntercept();
            String tableName = OcBOrderLogisticsIntercept.class.getDeclaredAnnotation(TableName.class).value().toUpperCase();
            intercept.setId(ModelUtil.getSequence(tableName));
            String billNo = getBillNo("SEQ_" + tableName, tableName, intercept, user.getLocale());
            intercept.setBillNo(billNo);
            intercept.setInterceptType(1);
            intercept.setLogisticsServiceType(ocBOrder.getLogisticsServiceType());
            intercept.setInterceptReason(interceptReason);
            intercept.setPlatformCode(ocBOrder.getSourceCode());
            intercept.setSourcePlatformCode(ocBOrder.getOrderSourcePlatformEcode());
            intercept.setOrderId(ocBOrder.getId());
            intercept.setOrderBillNo(ocBOrder.getBillNo());
            intercept.setOrderAmt(ocBOrder.getOrderAmt());
            intercept.setCpCShopId(ocBOrder.getCpCShopId());
            intercept.setCpCShopEcode(ocBOrder.getCpCShopEcode());
            intercept.setCpCShopTitle(ocBOrder.getCpCShopTitle());
            intercept.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
            intercept.setCpCPhyWarehouseEcode(ocBOrder.getCpCPhyWarehouseEcode());
            intercept.setCpCPhyWarehouseEname(ocBOrder.getCpCPhyWarehouseEname());
            intercept.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
            intercept.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
            intercept.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
            intercept.setExpresscode(ocBOrder.getExpresscode());
            intercept.setScanTime(ocBOrder.getScanTime());
            intercept.setInterceptStatus(LogisticsInterceptStatusEnum.NOT_INTERCEPT.getKey());
            intercept.setTReturnId(refundNo);
            StBeanUtils.makeCreateField(intercept, user);
            interceptList.add(intercept);
        }
        return interceptList;
    }

    /**
     * 构建请求参数并发送MQ
     *
     * @param interceptList
     */
    private void buildParamAndSendMQ(List<OcBOrderLogisticsIntercept> interceptList) {
        log.info(LogUtil.format("ZtoLogisticsInterceptService.buildParamAndSendMQ,interceptList.size():{}",
                "ZtoLogisticsInterceptService.buildParamAndSendMQ"), interceptList.size());
        List<ZtoCreateInterceptRequest> interceptRequestList = new ArrayList<>();

        //查询需要地址的极兔的仓库信息
        List<Long> cpCPhyWarehouseIds = interceptList.stream()
                .filter(u -> u.getLogisticsServiceType() == 3)
                .map(OcBOrderLogisticsIntercept::getCpCPhyWarehouseId)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, WarehouseAddressModel> addressModelMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(cpCPhyWarehouseIds)) {
            List<WarehouseAddressModel> addressModels = generalOrganizationCmd.serchAddressByOrWarehouseIds(cpCPhyWarehouseIds);
            if (!CollectionUtils.isEmpty(addressModels)) {
                addressModelMap = addressModels.stream()
                        .collect(Collectors.toMap(WarehouseAddressModel::getWarehouseId, Function.identity(), (oldValue, newValue) -> oldValue));
            }
        }

        for (OcBOrderLogisticsIntercept intercept : interceptList) {
            ZtoCreateInterceptRequest interceptRequest = new ZtoCreateInterceptRequest();
            interceptRequest.setMainTableId(intercept.getId());
            interceptRequest.setRequestId(intercept.getBillNo() + System.currentTimeMillis());
            interceptRequest.setBillCode(intercept.getExpresscode());
            //【拦截退改类型=1 拦截退回】时，传2
            interceptRequest.setDestinationType(2);
            interceptRequest.setThirdBizNo(intercept.getBillNo());
            interceptRequest.setLogisticsServiceType(intercept.getLogisticsServiceType());
            // 极兔、顺丰
            if ((3 == intercept.getLogisticsServiceType() || 7 == intercept.getLogisticsServiceType())) {
                //查询仓库
                WarehouseAddressModel warehouseAddressModel = addressModelMap.get(intercept.getCpCPhyWarehouseId());
                if (warehouseAddressModel != null) {
                    interceptRequest.setReceiveProvince(warehouseAddressModel.getProvinceEname());
                    interceptRequest.setReceiveCity(warehouseAddressModel.getCityName());
                    interceptRequest.setReceiveDistrict(warehouseAddressModel.getAreaEname());
                    interceptRequest.setReceiveAddress(warehouseAddressModel.getSenderDetail());
                }

                /*顺丰：添加仓库编码，用于查询时匹配月卡卡号*/
                interceptRequest.setWarehouseCode(intercept.getCpCPhyWarehouseEcode());
            }
            if (LogisticsServiceTypeEnum.JD.getVal().equals(intercept.getLogisticsServiceType())) {
                interceptRequest.setVendorCode(getVendorCode(intercept.getCpCShopId()));
                interceptRequest.setDeliveryId(intercept.getExpresscode());
                interceptRequest.setInterceptReason(intercept.getInterceptReason());
                interceptRequest.setCancelOperator(intercept.getOwnername());
            }
            if (LogisticsServiceTypeEnum.YTO.getVal().equals(intercept.getLogisticsServiceType())) {
                interceptRequest.setCustomerId(getYtoCustomerCode(intercept.getCpCPhyWarehouseEcode(), intercept.getCpCLogisticsEcode()));
            }
            interceptRequestList.add(interceptRequest);
        }
        String topic = Mq5Constants.TOPIC_R3_ORDER_LOGISTICS_INTERCEPT;
        String tag = Mq5Constants.TAG_R3_ORDER_LOGISTICS_INTERCEPT;
        try {
            MqSendResult sendResult = defaultProducerSend.sendTopic(topic, tag, JSON.toJSONString(interceptRequestList), null);
            log.debug(LogUtil.format("物流拦截消息发送完成,报文:{}，messageId:{}，messageKey:{}",
                            "ZtoLogisticsInterceptService.interceptCreate.sendMessage"),
                    JSON.toJSONString(interceptRequestList), sendResult.getMessageId(), sendResult.getMessageKey());
        } catch (Exception e) {
            log.error(LogUtil.format("ZtoLogisticsInterceptService.interceptCreate.sendMessage.error:{},messageBody:{}",
                    "ZtoLogisticsInterceptService.interceptCreate.sendMessage.error"),
                    Throwables.getStackTraceAsString(e), JSON.toJSONString(interceptRequestList));
        }
    }

    /**
     * 发起取消拦截
     *
     * @param ids
     * @param user
     * @return
     */
    public ValueHolderV14<Void> interceptCancel(List<Long> ids, User user) {
        log.info(LogUtil.format("ZtoLogisticsInterceptService.interceptCancel,ids:{}",
                "ZtoLogisticsInterceptService.interceptCancel"), JSONObject.toJSONString(ids));
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        List<Integer> statusList = new ArrayList<>();
        statusList.add(LogisticsInterceptStatusEnum.SEND_INTERCEPT_SUCCESS.getKey());
        statusList.add(LogisticsInterceptStatusEnum.INTERCEPTING.getKey());
        statusList.add(LogisticsInterceptStatusEnum.SEND_CANCEL_FAIL.getKey());
        //查询需要符合条件的单据
        List<OcBOrderLogisticsIntercept> interceptList =
                ocBOrderLogisticsInterceptMapper.selectList(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                        .in(OcBOrderLogisticsIntercept::getId, ids)
                        .in(OcBOrderLogisticsIntercept::getInterceptStatus, statusList));
        if (CollectionUtils.isEmpty(interceptList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("仅已发起拦截成功、拦截中和发起取消拦截失败的包裹允许取消拦截！");
            return v14;
        }

        // 目前只有中通、圆通支持取消拦截
        interceptList = interceptList.stream()
                .filter(item -> LogisticsServiceTypeEnum.cancelType(item.getLogisticsServiceType()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(interceptList)) {
            v14.setMessage("该快递公司不支持取消拦截!");
            return v14;
        }
        List<ZtoCancelInterceptRequest> ztoCancelInterceptRequests = new ArrayList<>();
        List<CancelInterceptRequest> cancelInterceptRequests = new ArrayList<>();
        for (OcBOrderLogisticsIntercept logisticsIntercept : interceptList) {
            //构建调用参数
            Integer serviceType =  logisticsIntercept.getLogisticsServiceType();
            if (Objects.equals(LogisticsServiceTypeEnum.ZTO.getVal(), serviceType)) {
                ZtoCancelInterceptRequest request = new ZtoCancelInterceptRequest();
                request.setMainTableId(logisticsIntercept.getId());
                request.setBillCode(logisticsIntercept.getExpresscode());
                request.setThirdBizNo(logisticsIntercept.getBillNo());
                ztoCancelInterceptRequests.add(request);
            }else {
                CancelInterceptRequest request = new CancelInterceptRequest();
                request.setExpressCode(logisticsIntercept.getExpresscode());
                request.setMainTableId(logisticsIntercept.getId());
                request.setLogisticsServiceType(logisticsIntercept.getLogisticsServiceType());
                request.setCustomerId(getYtoCustomerCode(logisticsIntercept.getCpCPhyWarehouseEcode(), logisticsIntercept.getCpCLogisticsEcode()));
                cancelInterceptRequests.add(request);
            }
        }

        int successNum = 0;
        if (CollUtil.isNotEmpty(ztoCancelInterceptRequests)) {
            List<ZtoCancelInterceptResponse> responseList =
                    ztoCancelInterceptCmd.cancelIntercept(ztoCancelInterceptRequests);
            for (ZtoCancelInterceptResponse response : responseList) {
                OcBOrderLogisticsIntercept intercept = new OcBOrderLogisticsIntercept();
                intercept.setId(response.getMainTableId());
                if (response.getStatus()) {
                    intercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_CANCEL_SUCCESS.getKey());
                    successNum++;
                } else {
                    intercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_CANCEL_FAIL.getKey());
                    intercept.setCancelFailureReason(response.getMessage());
                }
                StBeanUtils.makeModifierField(intercept, user);
                ocBOrderLogisticsInterceptMapper.updateById(intercept);
            }
        }
        if (CollUtil.isNotEmpty(cancelInterceptRequests)) {
            List<CancelInterceptResponse> cancelInterceptResponses = cancelInterceptCmd.cancelIntercept(cancelInterceptRequests);
            for (CancelInterceptResponse response : cancelInterceptResponses) {
                OcBOrderLogisticsIntercept intercept = new OcBOrderLogisticsIntercept();
                intercept.setId(response.getMainTableId());
                if (BooleanUtil.isTrue(response.getSuccess())) {
                    intercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_CANCEL_SUCCESS.getKey());
                    successNum++;
                } else {
                    intercept.setInterceptStatus(LogisticsInterceptStatusEnum.SEND_CANCEL_FAIL.getKey());
                    intercept.setCancelFailureReason(response.getMsg());
                }
                StBeanUtils.makeModifierField(intercept, user);
                ocBOrderLogisticsInterceptMapper.updateById(intercept);
            }
        }

        v14.setMessage("发起取消拦截成功" + successNum + "条，失败" + (ids.size() - successNum) + "条！");
        return v14;
    }

    /**
     * 补偿调用拦截创建
     *
     * @return
     */
    public ValueHolderV14<Void> interceptCreateForFail(ZtoLogisticsInterceptTaskRequest request) {
        log.info(LogUtil.format("ZtoLogisticsInterceptService.interceptCreateForFail.request={}",
                "ZtoLogisticsInterceptService.interceptCreateForFail"), JSON.toJSONString(request));
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        List<OcBOrderLogisticsIntercept> interceptList = queryInterceptList(request.getType());

        if (CollectionUtils.isEmpty(interceptList)) {
            v14.setMessage("未查询到需要补偿拦截的单据！");
            return v14;
        }
        //构造参数并发送MQ
        buildParamAndSendMQ(interceptList);
        return v14;
    }

    /**
     * 查询补偿调用拦截创建的单据
     * @param type
     * @return
     */
    public List<OcBOrderLogisticsIntercept> queryInterceptList(String type){
        List<OcBOrderLogisticsIntercept> interceptList = Lists.newArrayList();
        if(type.equals(ZtoLogisticsInterceptTaskTypeConstant.SEND_INTERCEPT_FAIL_AND_NOT_INTERCEPT)){
            //查询失败次数小于6次并且发起拦截失败的数据,更新时间小于当前时间十分钟
            List<Integer> statusList = new ArrayList<>();
            statusList.add(LogisticsInterceptStatusEnum.SEND_INTERCEPT_FAIL.getKey());
            statusList.add(LogisticsInterceptStatusEnum.NOT_INTERCEPT.getKey());
            interceptList = ocBOrderLogisticsInterceptMapper.selectList(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                    .in(OcBOrderLogisticsIntercept::getInterceptStatus, statusList)
                    .le(OcBOrderLogisticsIntercept::getInterceptFailureNum, 6)
                    .le(OcBOrderLogisticsIntercept::getModifieddate, DateUtils.addMinutes(new Date(), -10)));
        } else if(type.equals(ZtoLogisticsInterceptTaskTypeConstant.INTERCEPT_FAIL)){
            //查询拦截失败次数小于24次并且发起拦截失败的数据,更新时间小于当前时间70分钟
            List<Integer> statusList = new ArrayList<>();
            statusList.add(LogisticsInterceptStatusEnum.INTERCEPT_FAIL.getKey());
            statusList.add(LogisticsInterceptStatusEnum.SEND_INTERCEPT_FAIL.getKey());
            interceptList = ocBOrderLogisticsInterceptMapper.selectList(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                    .in(OcBOrderLogisticsIntercept::getInterceptStatus, statusList)
                    .eq(OcBOrderLogisticsIntercept::getLogisticsServiceType, LogisticsServiceTypeEnum.JT.getVal())
                    .lt(OcBOrderLogisticsIntercept::getInterceptFailureCount, 24)
//                    .le(OcBOrderLogisticsIntercept::getModifieddate, DateUtils.addMinutes(new Date(), -10))
                    .le(OcBOrderLogisticsIntercept::getModifieddate, DateUtils.addMinutes(new Date(), -70)) // 时间区间改成前70分钟
                    .in(OcBOrderLogisticsIntercept::getRemark, businessSystemParamService.getLogisticsInterceptFailRetryReason()));
        }
        return interceptList;
    }

    /**
     * 更新韵达拦截结果
     */
    public void updateYunDaInterceptResult() {
        List<OcBOrderLogisticsIntercept> interceptList = ocBOrderLogisticsInterceptMapper.selectList(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                .eq(OcBOrderLogisticsIntercept::getInterceptStatus, LogisticsInterceptStatusEnum.SEND_INTERCEPT_SUCCESS.getKey())
                .eq(OcBOrderLogisticsIntercept::getLogisticsServiceType, LogisticsServiceTypeEnum.YUNDA.getVal())
                .last("limit 500"));
        if (CollectionUtils.isEmpty(interceptList)) {
            return;
        }

        interceptList.forEach(item -> {
            // 拦截状态 韵达 1 失败  2 成功 3 处理中
            YunDaInterceptResultResponse logisticsInterceptResult = yunDaRouterCmd.getLogisticsInterceptResult(item.getExpresscode());
            if (Objects.isNull(logisticsInterceptResult) || Objects.isNull(logisticsInterceptResult.getInterceptState())) {
                log.info(LogUtil.format("韵达快递拦截结果返回空,code:{}"), item.getCpCLogisticsEcode());
            }

            // 拦截中不处理
            if (Objects.equals(logisticsInterceptResult.getInterceptState(), 3)) {
                return;
            }
            String bizData = logisticsInterceptResult.getBizData();
            if (StringUtils.isNotBlank(bizData)) {
                Long mainTbId = JSONObject.parseObject(bizData).getLong("mainTbId");
                if (Objects.nonNull(mainTbId) && !Objects.equals(item.getId(), mainTbId)) {
                    log.info(LogUtil.format("韵达快递拦截结果返回ID不一致,dbId:{},resultId:{}"), item.getId(), mainTbId);
                    return;
                }
            }
            OcBOrderLogisticsIntercept update = new OcBOrderLogisticsIntercept();
            update.setId(item.getId());


            // 拦截失败
            if (Objects.equals(logisticsInterceptResult.getInterceptState(), 1)) {
                update.setInterceptStatus(LogisticsInterceptStatusEnum.INTERCEPT_FAIL.getKey());
//                update.setInterceptFailureReason();
            }
            if (Objects.equals(logisticsInterceptResult.getInterceptState(), 2)) {
                update.setInterceptStatus(LogisticsInterceptStatusEnum.INTERCEPT_SUCCESS.getKey());
            }
            update.setModifieddate(new Date());
            ocBOrderLogisticsInterceptMapper.updateById(update);
        });
    }

    /**
     * 校验订单信息是否符合拦截条件
     *
     * @param orderIds
     * @param checkExists 是否需要检查登记表有数据，因为check需要，补偿任务不需要
     * @param isCheck     是否是检查数据，check是不需要返回订单信息，save需要
     * @return
     */
    private ValueHolderV14<List<OcBOrder>> checkData(List<Long> orderIds, boolean checkExists, boolean isCheck) {
        //用于后面判断是否为一条数据构建errorMSg
        boolean only = orderIds.size() == 1;
        ValueHolderV14<List<OcBOrder>> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "success");
        //查询订单信息
        List<OcBOrder> orderList = queryOrderInfo(orderIds);
        if (CollectionUtils.isEmpty(orderList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("订单信息不存在！");
            return v14;
        }
        //查询物流档案
        Set<Long> logisticsIds = orderList.stream()
                .map(OcBOrder::getCpCLogisticsId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(logisticsIds)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("物流公司信息为空，无法发起异常包裹拦截退回！");
            return v14;
        }
        ValueHolderV14<List<CpLogistics>> holderV14 = cpRpcService.queryLogisticsByIdsIgnoreStatus(logisticsIds);
        if (!holderV14.isOK()) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("查询物流公司信息失败，请稍后重试！");
            return v14;
        }
        List<CpLogistics> logisticsList = holderV14.getData();
        if (CollectionUtils.isEmpty(logisticsList)) {
            v14.setCode(ResultCode.FAIL);
            v14.setMessage("未查询到物流公司信息，无法发起异常包裹拦截退回！");
            return v14;
        }
        Map<Long, CpLogistics> logisticsMap =
                logisticsList.stream().collect(Collectors.toMap(CpLogistics::getId, Function.identity()));

        //查询异常包裹登记是否存在
        Map<Long, List<OcBOrderLogisticsIntercept>> interceptMap = new HashMap<>();
        if (checkExists) {
            List<OcBOrderLogisticsIntercept> interceptList =
                    ocBOrderLogisticsInterceptMapper.selectList(new LambdaQueryWrapper<OcBOrderLogisticsIntercept>()
                            .in(OcBOrderLogisticsIntercept::getOrderId, orderIds));
            if (CollectionUtils.isNotEmpty(interceptList)) {
                interceptMap = interceptList.stream().collect(Collectors.groupingBy(OcBOrderLogisticsIntercept::getOrderId));
            }
        }

        String errorMsg = check(checkExists,
                orderList,
                logisticsMap,
                interceptMap);
        if (CollectionUtils.isEmpty(orderList)) {
            v14.setCode(ResultCode.FAIL);
            if (only) {
                v14.setMessage(errorMsg);
            } else {
                v14.setMessage("订单不符合拦截条件，请检查！");
            }
        }
        if (v14.isOK() && !isCheck) {
            v14.setData(orderList);
        }
        return v14;
    }

    /**
     * 检查数据是否满足条件
     *
     * @param checkExists
     * @param orderList
     * @param logisticsMap
     * @param interceptMap
     * @return
     */
    private String check(boolean checkExists,
                         List<OcBOrder> orderList,
                         Map<Long, CpLogistics> logisticsMap,
                         Map<Long, List<OcBOrderLogisticsIntercept>> interceptMap) {
        String errorMsg = "success";
        Iterator<OcBOrder> iterator = orderList.iterator();
        while (iterator.hasNext()) {
            OcBOrder order = iterator.next();
            //物流单号为空校验
            if (StringUtils.isEmpty(order.getExpresscode())) {
                errorMsg = "订单物流单号为空，无法发起异常包裹拦截退回！";
                iterator.remove();
                continue;
            }
            //物流公司校验
            if (order.getCpCLogisticsId() == null || logisticsMap.get(order.getCpCLogisticsId()) == null) {
                errorMsg = "物流公司信息不存在，无法发起异常包裹拦截退回！";
                iterator.remove();
                continue;
            }
            if (logisticsMap.get(order.getCpCLogisticsId()).getLogisticsServiceType() == null) {
                errorMsg = "物流公司不支持系统拦截退改，物流工单服务类型为空！";
                iterator.remove();
                continue;
            }
            //校验异常包裹登记是否已经存在
            if (checkExists) {
                if (MapUtils.isNotEmpty(interceptMap) && CollectionUtils.isNotEmpty(interceptMap.get(order.getId()))) {
                    for (OcBOrderLogisticsIntercept logisticsIntercept : interceptMap.get(order.getId())) {
                        if (LogisticsInterceptTypeEnum.INTERCEPT.getKey().equals(logisticsIntercept.getInterceptType()) &&
                                !(LogisticsInterceptStatusEnum.INTERCEPT_CANCEL.getKey().equals(logisticsIntercept.getInterceptStatus()) ||
                                        LogisticsInterceptStatusEnum.INTERCEPT_FAIL.getKey().equals(logisticsIntercept.getInterceptStatus()) ||
                                        LogisticsInterceptStatusEnum.INTERCEPT_DISCONTINUE.getKey().equals(logisticsIntercept.getInterceptStatus()))) {
                            errorMsg = "该订单已发起过包裹拦截退回，请至【异常包裹登记】查看拦截结果！";
                            iterator.remove();
                            break;
                        }
                        if(LogisticsInterceptTypeEnum.INTERCEPT.getKey().equals(logisticsIntercept.getInterceptType()) &&
                                LogisticsInterceptStatusEnum.INTERCEPT_FAIL.getKey().equals(logisticsIntercept.getInterceptStatus()) &&
                                logisticsIntercept.getInterceptFailureCount() < 7 &&
                                logisticsIntercept.getLogisticsServiceType().equals(LogisticsServiceTypeEnum.JT.getVal()) &&
                                businessSystemParamService.getLogisticsInterceptFailRetryReason().contains(logisticsIntercept.getRemark())){
                            errorMsg = "该极兔订单已发起过包裹拦截退回，拦截失败且拦截失败次数小于7且系统备注包含参数设置的失败原因关键字，正在补偿，不允许重复提交！";
                            iterator.remove();
                            break;
                        }
                    }
                }
            }
            CpLogistics cpLogistics = logisticsMap.get(order.getCpCLogisticsId());
            if (Objects.nonNull(cpLogistics)) {
                order.setLogisticsServiceType(cpLogistics.getLogisticsServiceType());
            }
        }
        return errorMsg;
    }

    /**
     * 查询订单信息
     *
     * @param orderIds
     * @return
     */
    private List<OcBOrder> queryOrderInfo(List<Long> orderIds) {
        LambdaQueryWrapper<OcBOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(OcBOrder::getId
                , OcBOrder::getSourceCode
                , OcBOrder::getOrderSourcePlatformEcode
                , OcBOrder::getBillNo
                , OcBOrder::getOrderAmt
                , OcBOrder::getCpCShopId
                , OcBOrder::getCpCShopEcode
                , OcBOrder::getCpCShopTitle
                , OcBOrder::getCpCPhyWarehouseId
                , OcBOrder::getCpCPhyWarehouseEcode
                , OcBOrder::getCpCPhyWarehouseEname
                , OcBOrder::getCpCLogisticsId
                , OcBOrder::getCpCLogisticsEcode
                , OcBOrder::getCpCLogisticsEname
                , OcBOrder::getExpresscode
                , OcBOrder::getScanTime
        );
        queryWrapper.in(OcBOrder::getId, orderIds);
        return ocBOrderMapper.selectList(queryWrapper);
    }

    private String getBillNo(String seq, String table, BaseModel sourceModel, Locale locale) {
        // 获取单据编号
        JSONObject obj = new JSONObject();
        obj.put(table, sourceModel);
        return SequenceGenUtil.generateSquence(seq, obj, locale, false);
    }

    /**
     * 获取圆通客户编码
     *
     * @param warehouseCode
     * @param logisticsCode
     * @return
     */
    private String getYtoCustomerCode(String warehouseCode, String logisticsCode) {
        if (StringUtils.isEmpty(warehouseCode) || StringUtils.isEmpty(logisticsCode)) {
            return YTO_DEFAULT_CUSTOMERCODE;
        }
        //key:warehouseCode+logisticeCode;value:customerCode
        Map<String, String> map = businessSystemParamService.getWarehouseCustomerCodeMap();
        String customerCode = map.get(warehouseCode + logisticsCode);
        if (StringUtils.isNotEmpty(customerCode)) {
            return customerCode;
        }
        return YTO_DEFAULT_CUSTOMERCODE;
    }

    /**
     * 获取区分京东自营非自营单子
     *
     * @param shopId 下单店铺id
     * @return
     */
    private String getVendorCode(Long shopId) {
        // 京东平台编码
        List<String> jdPlatformCodeList = Arrays.asList("242", "4", "500");

        ValueHolder valueHolder = cpcShopQueryCmd.queryCpcShopById(shopId);
        log.info("京东物流拦截 获取店铺信息 ===> {}", JSON.toJSONString(valueHolder));

        CpShop cpShop = (CpShop) valueHolder.get("data");
        Assert.notNull(cpShop, () -> "京东物流拦截 获取店铺详情为空");

        List<CpCPlatform> cpCPlatforms = cpCPlatformQueryCmd.queryCpCPlatformByIds(Collections.singletonList(
                Long.parseLong(String.valueOf(cpShop.getCpCPlatformId()))));
        Assert.notEmpty(cpCPlatforms, () -> "京东物流拦截 查询平台数据失败");

        // 区分京东自营与非自营下单
        if (jdPlatformCodeList.contains(cpCPlatforms.get(0).getEcode())) {
            return "021K228330";
        }
        return "021K738022";
    }

    /**
     * 根据【关键字快递拦截策略】拦截
     *
     * @param order
     * @param deliveryFailMsg
     * @return
     */
    public void keywordsIntercept(OcBOrder order, String deliveryFailMsg) {
        log.info(LogUtil.format("ZtoLogisticsInterceptService.keywordsIntercept order:{},deliveryFailMsg:{}",
                "ZtoLogisticsInterceptService.keywordsIntercept"), JSONObject.toJSONString(order), deliveryFailMsg);
        if (OrderGenericMarkEnum.INTERCEPT.getTag().equals(order.getGenericMark())) {
            return;
        }
        if (StringUtils.isEmpty(deliveryFailMsg)) {
            return;
        }
        List<String> keywords = stRpcService.queryKeywordsInterceptByPlatformId(Long.valueOf(order.getPlatform()));
        if (CollectionUtils.isEmpty(keywords)) {
            return;
        }
        if (checkInterceptByKeywords(keywords, deliveryFailMsg)) {
            autoIntercept(order.getId(), null);
        }
    }

    /**
     * 检查失败原因是否命中关键字
     *
     * @param keywords
     * @param deliveryFailMsg
     * @return
     */
    private boolean checkInterceptByKeywords(List<String> keywords, String deliveryFailMsg) {
        for (String keyword : keywords) {
            if (deliveryFailMsg.contains(keyword)) {
                return true;
            }
        }
        return false;
    }

    public ValueHolderV14<Void> autoIntercept(Long orderId, String refundNo) {
        // 加个锁
        String lockRedisKey = BllRedisKeyResources.buildLockOrderInterceptKey(orderId);
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        ValueHolderV14<Void> valueHolderV14 = new ValueHolderV14<>();
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                OcBOrder ocBOrder = ocBOrderMapper.selectByID(orderId);
                if (ObjectUtil.isNull(ocBOrder)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("订单不存在");
                    return valueHolderV14;
                }
                // 补发单跟复制单不能拦截
                Integer isResetShip = ocBOrder.getIsResetShip();
                if (ObjectUtil.equal(isResetShip, YesNoEnum.Y.getVal())) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("补发单不拦截");
                    return valueHolderV14;
                }
                Integer isCopyOrder = ocBOrder.getIsCopyOrder();
                if (ObjectUtil.equal(isCopyOrder, YesNoEnum.Y.getVal())) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("复制单不拦截");
                    return valueHolderV14;
                }

                Long shopId = ocBOrder.getCpCShopId();
                if (ObjectUtil.isNull(shopId)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("订单上的店铺信息不存在");
                    return valueHolderV14;
                }
                StCShopStrategyDO stCShopStrategyDO = stRpcService.selectOcStCShopStrategyByCpCshopId(shopId);
                if (ObjectUtil.isNull(stCShopStrategyDO)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("查询店铺策略失败");
                    return valueHolderV14;
                }
                // 判断此店铺是否有配置
                String isAutoIntercept = stCShopStrategyDO.getIsAutoIntercept();
                if (StringUtils.isEmpty(isAutoIntercept) || ObjectUtil.equal(YesNoEnum.ZERO.getKey(), isAutoIntercept)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("店铺未开启自动拦截");
                    return valueHolderV14;
                }
                // 获取实体仓档案信息 只能拦截巨沃或者富勒
                CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(ocBOrder.getCpCPhyWarehouseId());
                if (ObjectUtil.isNull(cpCPhyWarehouse)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("实体仓不存在");
                    return valueHolderV14;
                }
                String wmsType = cpCPhyWarehouse.getWmsType();
                if (StringUtils.isEmpty(wmsType)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("仓库类型为空");
                    return valueHolderV14;
                }

                if (!(StringUtils.equals(wmsType, ThirdWmsTypeEnum.FLWMS.getCode()) || StringUtils.equals(wmsType, ThirdWmsTypeEnum.QMWMS.getCode()))) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("仓库类型非巨沃或者富勒");
                    return valueHolderV14;
                }
                List<OcBOrderLogisticsIntercept> interceptList =
                        ocBOrderLogisticsInterceptMapper.selectByOrderId(orderId);
                if (CollectionUtils.isNotEmpty(interceptList)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("该订单发起过拦截");
                    return valueHolderV14;
                }

                if (oneCategoryCheck(ocBOrder)) {
                    valueHolderV14.setCode(ResultCode.FAIL);
                    valueHolderV14.setMessage("该订单中存在排除一级类目的商品");
                    return valueHolderV14;
                }

                // 给发起拦截的订单打上标记,挪到interceptCreate里，只要是发起了都打上标
//                if (valueHolderV14.isOK()) {
//                    log.info("自动拦截打通用标记订单={}", JSON.toJSONString(ocBOrder));
//                    makeOrderGenericMark(ocBOrder, OrderGenericMarkEnum.INTERCEPT);
//                }
                return interceptCreate(Collections.singletonList(orderId), "派件前拦截", SystemUserResource.getRootUser(), refundNo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            ValueHolderV14<Void> voidValueHolderV14 = new ValueHolderV14<>();
            voidValueHolderV14.setCode(ResultCode.FAIL);
            voidValueHolderV14.setMessage(e.getMessage());
            return voidValueHolderV14;
        } finally {
            redisLock.unlock();
        }
        valueHolderV14.setCode(ResultCode.SUCCESS);
        valueHolderV14.setMessage("success");
        return valueHolderV14;
    }

    /**
     * 给订单打通用标记
     *
     * @param orderIds
     * @param tagEnum
     * @return
     */
    public void makeOrderGenericMark(List<Long> orderIds, OrderGenericMarkEnum tagEnum) {
        ocBOrderMapper.updateGenericMark(tagEnum.getTag(), orderIds);
    }

    /**
     * 一级类目校验
     * @return
     */
    private boolean oneCategoryCheck(OcBOrder ocBOrder) {
        List<OcBOrderItem> ocBOrderItems = ocBOrderItemMapper.selectOrderItemList(ocBOrder.getId());
        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            log.warn("自动拦截订单明细为空={}", JSON.toJSONString(ocBOrder));
            return false;
        }

        String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(STAND_PLATFORM_AUTOINTERCEPT_EXCLUDE);
        if (StringUtils.isBlank(value)) {
            log.warn("订单id={}，单号={}，自动拦截业务参数查出为空", ocBOrder.getId(), ocBOrder.getBillNo());
            return false;
        }

        List<Object> mDim4Ids = new ArrayList<>();
        ocBOrderItems.stream()
                .map(OcBOrderItem::getMDim4Id)
                .forEach(mDim4Ids::add);
        List<HashMap> proDimItems = psCProDimCmd.getProDimItemByIds(mDim4Ids);
        if (CollectionUtils.isEmpty(proDimItems)) {
            log.warn("订单id={}，单号={}，自动拦截一级分类参数查出为空", ocBOrder.getId(), ocBOrder.getBillNo());
            return false;
        }

        log.info("自动拦截订单明细商品的一级分类={}", JSON.toJSONString(proDimItems));

        for (HashMap proDimItem : proDimItems) {
            // prd逻辑：判断下该包裹明细是否包含 维护的一级类目，若有则不拦截。（实际业务不存在低温和常温同存包裹发货）
            if (value.contains(String.valueOf(proDimItem.get("ENAME")))) {
                return true;
            }
        }

        return false;
    }
}
