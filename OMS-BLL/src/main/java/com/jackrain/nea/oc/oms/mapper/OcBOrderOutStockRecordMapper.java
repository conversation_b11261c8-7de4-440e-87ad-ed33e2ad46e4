package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderOutStockRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderOutStockRecordMapper
 * @Description
 * <AUTHOR>
 * @Date 2023/3/30 14:46
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderOutStockRecordMapper extends ExtentionMapper<OcBOrderOutStockRecord> {

    @Select("<script> "
            + "SELECT * FROM oc_b_order_outstock_record WHERE oc_b_order_id = #{ocBOrderId} "
            + "</script>")
    List<OcBOrderOutStockRecord> selectByOcBOrderId(@Param("ocBOrderId") Long ocBOrderId);

}
