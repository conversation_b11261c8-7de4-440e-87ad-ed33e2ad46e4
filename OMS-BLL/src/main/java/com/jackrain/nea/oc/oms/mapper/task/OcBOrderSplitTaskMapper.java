package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderSplitTask;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;


@Mapper
@Component
public interface OcBOrderSplitTaskMapper extends ExtentionMapper<OcBOrderSplitTask> {

    @InsertProvider(type = OcBOrderSplitTaskSql.class, method = "insertOcBOrderSplitTask")
    void batchInsertOrderSplitTask(@Param("list") List<OcBOrderSplitTask> list);

    @SelectProvider(type = OcBOrderSplitTaskSql.class, method = "selectByNodeSql")
    List<Long> selectTaskIdList(@Param(value = "nodeName") String nodeName, @Param(value = "limit") int limit,
                                @Param(value = "taskTableName") String taskTableName, @Param("splitTimes") int splitTimes);

    @Update("update oc_b_order_split_task set status = #{task.status}, next_time = #{task.nextTime}, split_times = #{task.splitTimes}," +
            "modifieddate=#{task.modifieddate}, remark = #{task.remark} where oc_b_order_id = #{task.ocBOrderId}")
    void updateOcBOrderSplitTask(@Param("task") OcBOrderSplitTask task);


    @SelectProvider(type = OcBOrderSplitTaskSql.class, method = "getNodeSql")
    List<OcBOrderSplitTask> queryList(
            @Param(value = "taskTableName") String taskTableName,
            @Param("status") int status,
            @Param("splitTimes") int splitTimes,
            @Param(value = "limit") int limit);


    @SelectProvider(type = OcBOrderSplitTaskSql.class, method = "getCompensationNodeSql")
    List<OcBOrderSplitTask> getCompensationNodeSql(@Param(value = "nodeName") String nodeName,
                                      @Param(value = "taskTableName") String taskTableName,
                                      @Param("statusList") List<String> statusList,
                                      @Param("splitTimes") int splitTimes,
                                      @Param(value = "limit") int limit);


    @Update("<script> "
            + "UPDATE oc_b_order_split_task SET status = #{newStatus},modifieddate = now() where status = #{oldStatus} and oc_b_order_id in "
            + "<foreach item='item' index='index' collection='orderIdList' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int batchUpdateOne(@Param("newStatus") int newStatus, @Param("orderIdList") List<Long> orderIdList, @Param("oldStatus") int oldStatus);


    @Update("<script> "
            + "UPDATE oc_b_order_split_task SET split_times = #{newSplitTimes}, status = #{newStatus}, modifieddate = now() where status = #{oldStatus} and split_times >= #{limitSplitTimes} and oc_b_order_id in "
            + "<foreach item='item' index='index' collection='orderIdList' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int batchUpdateTwo(@Param("orderIdList") List<Long> orderIdList, @Param("newStatus") int newStatus, @Param("oldStatus") int oldStatus, @Param("newSplitTimes") int newSplitTimes, @Param("limitSplitTimes") int limitSplitTimes);


    @Update("<script> "
            + "UPDATE oc_b_order_split_task SET status = #{newStatus} where oc_b_order_id in "
            + "<foreach item='item' index='index' collection='orderIdList' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int batchUpdateThree(@Param("newStatus") int newStatus, @Param("orderIdList") List<Long> orderIdList);

    @Update("<script> "
            + "UPDATE oc_b_order_split_task SET status = #{newStatus},modifieddate = now(), remark = CONCAT(IFNULL(remark,''), #{remark}) where status in (${oldStatusStr}) and oc_b_order_id in "
            + "<foreach item='item' index='index' collection='orderIdList' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int batchUpdateFour(@Param("newStatus") int newStatus,  @Param("oldStatusStr") String oldStatusStr, @Param("remark") String remark, @Param("orderIdList") List<Long> orderIdList);

    @Select("select count(*) from oc_b_order_split_task where status = #{status} and split_times < #{splitTimes}")
    int countExeNum(@Param("status") int status, @Param("splitTimes") int splitTimes);


    @Select("select count(*) from oc_b_order_split_task where oc_b_order_id = #{orderId}")
    int countNum(@Param("orderId") Long orderId);

}