package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: ganquan
 * @Description: 物流公司电话
 * @Date Create In 2020/7/16 18:02
 */
public enum LogisticsTelEnum {
    ZTO("ZTO", "中通快递", "************"),
    YUNDA("YUNDA", "韵达快递", "************"),
    STO("STO", "申通快递", "************"),
    YTO("YTO", "圆通快递", "95554"),
    SF("SF", "顺丰快递", "************"),
    QFKD("QFKD", "全峰快递", "************"),
    BEST("BEST", "百世汇通快递", "************"),
    TTKDEX("TTKDEX", "天天快递", "4001-888-888"),
    ZJS("ZJS", "宅急送快递", "400-6789-000"),
    GTO("GTO", "国通快递", "************"),
    LB("LB", "龙邦快递", "021-59218889"),
    ANE56("ANE56", "安能快递", "************"),
    UC("UC", "优速快递", "************"),
    EMS("EMS", "EMS", "11185"),
    FAST("FAST", "快捷快递", "************"),
    CRE("CRE", "中铁物流", "************"),
    DBWL("DBWL", "德邦物流", "************"),
    YZ("YZ", "邮政", "11183"),
    KYSY("KYSY", "跨越速运", "4008-098-098"),
    JT("JT", "极兔速递", "************");


    LogisticsTelEnum(String code, String name, String tel) {
        this.code = code;
        this.name = name;
        this.tel = tel;
    }

    @Getter
    private String code;

    @Getter
    private String name;

    @Getter
    private String tel;

    /**
     * @param code
     * @return java.lang.String
     * <AUTHOR>
     * @Description 根据code获取物流电话
     * @Date 19:15 2020/7/16
     **/
    public static String enumToTel(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        for (LogisticsTelEnum e : LogisticsTelEnum.values()) {
            if (StringUtils.equals(code, e.getCode())) {
                return e.getTel();
            }
        }
        return null;
    }
}
