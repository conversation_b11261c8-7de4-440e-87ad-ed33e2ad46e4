package com.jackrain.nea.oc.oms.mapper.ac;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceSystemItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AcFOrderInvoiceSystemItemMapper extends ExtentionMapper<AcFOrderInvoiceSystemItem> {

    @Select("<script>" +
            "select ac_f_order_invoice_id from ac_f_order_invoice_system_item where tid in " +
            "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<Long> selectByTids(@Param("tids") List<String> tidList);

    @Select("select tid from ac_f_order_invoice_system_item where ac_f_order_invoice_id = #{id}")
    List<String> selectTidsByOrderInvoiceId(@Param("id") Long id);

    @Select("select oc_b_order_id from ac_f_order_invoice_system_item where ac_f_order_invoice_id = #{id}")
    List<Long> selectOrderidsByOrderInvoiceId(@Param("id") Long id);

    @Select("select * from ac_f_order_invoice_system_item where ac_f_order_invoice_id = #{id}")
    List<AcFOrderInvoiceSystemItem> selectByOrderInvoiceId(@Param("id") Long id);

    @Select("<script>" +
            "select * from ac_f_order_invoice_system_item where ac_f_order_invoice_id in " +
            "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<AcFOrderInvoiceSystemItem> selectbyOrderInvoiceIds(@Param("ids") List<Long> ids);
}