package com.jackrain.nea.oc.oms.services.delivery;

import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;

import java.util.List;

/**
 * Description:平台发货接口
 *
 * <AUTHOR> sunies
 * @since : 2020-11-03
 * create at : 2020-11-03 20:00
 */
public interface OrderDeliveryCmd {
    /**
     * 平台发货处理接口
     *
     * @param ocBOrderRelation
     * @return
     */
    boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips);
}
