package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderBnTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName OcBReturnOrderBnTaskMapper
 * @Description 退换货单班牛工单关联表Mapper
 * <AUTHOR>
 * @Date 2025/5/15 14:40
 * @Version 1.0
 */
@Mapper
public interface OcBReturnOrderBnTaskMapper extends ExtentionMapper<OcBReturnOrderBnTask> {

    /**
     * 根据退换货单ID查询关联记录
     *
     * @param returnOrderId 退换货单ID
     * @return 关联记录列表
     */
    @Select("SELECT * FROM oc_b_return_order_bn_task WHERE oc_b_return_order_id = #{returnOrderId} AND isactive = 'Y'")
    List<OcBReturnOrderBnTask> selectByReturnOrderId(@Param("returnOrderId") Long returnOrderId);

    /**
     * 根据退换货单号查询关联记录
     *
     * @param billNo 退换货单号
     * @return 关联记录列表
     */
    @Select("SELECT * FROM oc_b_return_order_bn_task WHERE bill_no = #{billNo} AND isactive = 'Y'")
    List<OcBReturnOrderBnTask> selectByBillNo(@Param("billNo") String billNo);

    /**
     * 检查退换货单是否已创建过班牛工单
     *
     * @param returnOrderId 退换货单ID
     * @return 数量
     */
    @Select("SELECT COUNT(1) FROM oc_b_return_order_bn_task WHERE oc_b_return_order_id = #{returnOrderId} AND isactive = 'Y'")
    int countByReturnOrderId(@Param("returnOrderId") Long returnOrderId);
}
