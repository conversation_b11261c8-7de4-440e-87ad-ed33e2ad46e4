package com.jackrain.nea.oc.oms.sap;

import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.model.table.store.sap.SgBSapInformationMapping;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.cpext.model.table.CpStore;
import com.jackrain.nea.enums.SapStatusEnum;
import com.jackrain.nea.ip.SapInterfaceLogCmd;
import com.jackrain.nea.ip.model.sap.RequestParamVo;
import com.jackrain.nea.ip.model.sap.RestTemplateUtil;
import com.jackrain.nea.ip.model.sap.SapUtil;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordMapper;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGather;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGatherItem;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecord;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @title: SapSalesOrderService
 * <AUTHOR>
 * @Date: 2022/5/30 12:00
 * @Version 1.0
 */
@Slf4j
@Component
public class SapSalesDataGatherService {
    @Autowired
    OcBSapSalesDataGatherMapper ocBSapSalesDataGatherMapper;

    @Autowired
    OcBSapSalesDataRecordMapper ocBSapSalesDataRecordMapper;

    @Autowired
    OcBSapSalesDataGatherItemMapper ocBSapSalesDataGatherItemMapper;

    @Autowired
    private OcBSapSalesDataRecordService ocBSapSalesDataRecordService;

    @Autowired
    RestTemplateUtil restTemplateUtil;

    @Autowired
    private CpRpcService cpRpcService;

    @Reference(timeout = 1200000, retries = 0, group = "ip", version = "1.4")
    SapInterfaceLogCmd sapInterfaceLogCmd;
    //销售汇总
    private static final String SUM_TYPE00 = "0";
    //奶卡汇总
    private static final String SUM_TYPE01 = "1";
    //无名件汇总
    private static final String SUM_TYPE_Z74 = "Z74";
    //冲无名件汇总
    private static final String SUM_TYPE_Z73 = "Z73";
    //无名件汇总
    private static final String SUM_TYPE_Z23 = "Z23";

    //奶卡金额冲抵调增
    private static final String SUM_TYPE_RYCD01 = "RYCD01";
    //奶卡金额冲抵调减
    private static final String SUM_TYPE_RYCD02 = "RYCD02";
    //周期购金额冲抵调增
    private static final String SUM_TYPE_RYCD03 = "RYCD03";
    //周期购金额冲抵调减
    private static final String SUM_TYPE_RYCD04 = "RYCD04";

    //销售数据汇总
    private static final Long THIRD_PARTY_LOG_BILL_TYPE_20 = 20L;

    /**
     * 对账结算单结算类型编码
     */
    public static final String CONFIRM_SETTLE_Z01 = "Z01";
    public static final String CONFIRM_SETTLE_ZCR2 = "ZCR2";
    public static final String CONFIRM_SETTLE_ZCR3 = "ZCR3";
    public static final String CONFIRM_SETTLE_ZDR2 = "ZDR2";
    public static final String CONFIRM_SETTLE_ZDR3 = "ZDR3";

    /**
     * SAP自动任务单拉取数量，默认100
     */
    @Value("${sap.gather.num:100}")
    private Integer storageNumber;

    /**
     * SAP自动任务单节点最大拉取数量，默认3000
     */
    @Value("${sap.url:}")
    private String sapUrl;


    /**
     * 查询销售数据汇总表数据(定时任务)
     *
     * @return
     */
    public List<OcBSapSalesDataGather> querySalesDataGather() {
        return ocBSapSalesDataGatherMapper.querySalesDataGather(storageNumber);
    }

    /**
     * 查询销售数据汇总表的数据（传SAP按钮）
     *
     * @return
     */
    public List<OcBSapSalesDataGather> queryButtonSalesDataGather(List<Long> ids) {
        return ocBSapSalesDataGatherMapper.queryButtonSalesDataGather(ids);
    }

    /**
     * 查询销售数据汇总表明细
     *
     * @param mainId
     * @return
     */
    public List<OcBSapSalesDataGatherItem> querySalesDataGatherItem(Long mainId) {
        return ocBSapSalesDataGatherItemMapper.querySaleDataGatherItem(mainId);
    }

    /**
     * 批量更新传SAP状态为“传中”
     *
     * @param ids
     * @param toSapStatus
     * @return
     */
    public Integer batchUpdateSapStatus(List<Long> ids, Integer toSapStatus) {
        return ocBSapSalesDataGatherMapper.batchUpdateSapStatus(ids, toSapStatus);
    }

    /**
     * 批量更新传SAP状态为“传成功”
     *
     * @param ids
     * @param toSapStatus
     * @return
     */
    public Integer batchUpdateSapSuccessStatus(List<Long> ids, Integer toSapStatus) {
        return ocBSapSalesDataGatherMapper.batchUpdateSapSuccessStatus(ids, toSapStatus);
    }

    /**
     * 批量更新传SAP状态为“传失败”
     *
     * @param
     * @param toSapStatus
     * @return
     */
    public Integer updateSapFailStatus(Long id, Integer toSapStatus, String failReason) {
        return ocBSapSalesDataGatherMapper.updateSapFailStatus(id, toSapStatus, failReason);
    }

    /**
     * 销售数据汇总表数据传SAP （汇总类型为销售汇总、奶卡汇总）
     */
    public void salesOrderDataGatherBack(List<Long> ids) {
        List<OcBSapSalesDataGather> sapSalesDataGatherList;
        if (CollectionUtils.isEmpty(ids)) {
            sapSalesDataGatherList = querySalesDataGather();
        } else {
            sapSalesDataGatherList = queryButtonSalesDataGather(ids);
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("salesOrderDataGatherBack.sapSalesDataGatherList={}"), sapSalesDataGatherList);
        }
        if (CollectionUtils.isEmpty(sapSalesDataGatherList)) {
            log.info(LogUtil.format("查询销售数据汇总数据为空，入参:{}",
                    "salesOrderDataGatherBack.sapSalesDataGatherList"), ids);
            return;
        }

        /*退单类型的sap信息映射表*/
        SgBSapInformationMapping entity = new SgBSapInformationMapping();
        entity.setBillType("2");
        List<SgBSapInformationMapping> mappingList = ocBSapSalesDataRecordService.selectSapInformationMappingByEntity(entity);
        Set<String> returnSapBillType = ListUtils.emptyIfNull(mappingList).stream()
                .map(SgBSapInformationMapping::getSapBillType)
                .collect(Collectors.toSet());

        // 根据sapBillType是否是退单来分组
        Map<Boolean, List<OcBSapSalesDataGather>> partitionedGathers = sapSalesDataGatherList.stream()
                .collect(Collectors.partitioningBy(gather -> returnSapBillType.contains(gather.getSapBillType())));
        List<OcBSapSalesDataGather> returnGatherList = partitionedGathers.getOrDefault(true, Collections.emptyList());
        List<OcBSapSalesDataGather> otherGatherList = partitionedGathers.getOrDefault(false, Collections.emptyList());

        log.info(LogUtil.format("执行汇总传SAP完成,总数:{}，退单数量:{},其他单据数量:{}",
                        "SapSalesDataGatherService.salesOrderDataGatherBack"),
                sapSalesDataGatherList.size(), returnGatherList.size(), otherGatherList.size());

        TimeInterval interval = new TimeInterval();
        interval.start("returnGatherList");
        // 先处理退单类型的数据
        if (CollectionUtils.isNotEmpty(returnGatherList)) {
            sapSalesDataGather(returnGatherList);
        }
        log.info(LogUtil.format("执行退单类型传SAP完成,总数:{},耗时:{}",
                        "SapSalesDataGatherService.salesOrderDataGatherBack"),
                returnGatherList.size(), interval.intervalMs("returnGatherList"));

        ThreadUtil.safeSleep(1000 * 60);

        interval.start("otherGatherList");
        // 再处理其他类型的数据
        if (CollectionUtils.isNotEmpty(otherGatherList)) {
            sapSalesDataGather(otherGatherList);
        }
        log.info(LogUtil.format("执行其他单据传SAP完成,总数:{},耗时:{}",
                        "SapSalesDataGatherService.salesOrderDataGatherBack"),
                otherGatherList.size(), interval.intervalMs("otherGatherList"));
    }

    private void sapSalesDataGather(List<OcBSapSalesDataGather> sapSalesDataGatherList) {
        List<Long> successIds = new ArrayList<>();
        List<Map<String, String>> failList = new ArrayList<>();
        List<Long> idList = sapSalesDataGatherList.stream().map(OcBSapSalesDataGather::getId).collect(Collectors.toList());
        //更新单据为传中
        batchUpdateSapStatus(idList, SapStatusEnum.SENDING.getKey());

        List<OcBSapSalesDataRecord> records = ocBSapSalesDataRecordMapper.selectList(new LambdaQueryWrapper<OcBSapSalesDataRecord>()
                .in(OcBSapSalesDataRecord::getOcBSapSalesDataGatherId, idList));

        Map<String, List<OcBSapSalesDataRecord>> recordMap = records.stream().collect(Collectors.groupingBy(e -> e.getMergeCode() + e.getSumType()));
        for (OcBSapSalesDataGather ocBSapSalesDataGather : sapSalesDataGatherList) {
            Map<String, String> map = new HashMap<>();
            try {
                List<OcBSapSalesDataRecord> recordList = recordMap.get(ocBSapSalesDataGather.getMergeCode() + ocBSapSalesDataGather.getSumType());
                OcBSapSalesDataRecord ocBSapSalesDataRecord = null;
                if (CollectionUtils.isNotEmpty(recordList)) {
                    ocBSapSalesDataRecord = recordList.get(0);
                }
                List<OcBSapSalesDataGatherItem> salesDataGatherItem = querySalesDataGatherItem(ocBSapSalesDataGather.getId());
                //奶卡金额冲抵，明细行所有成交金额为0,，不传sap
                if (SUM_TYPE_RYCD01.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE_RYCD02.equals(ocBSapSalesDataGather.getSumType())
                        || SUM_TYPE_RYCD03.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE_RYCD04.equals(ocBSapSalesDataGather.getSumType())
                ) {
                    Integer count = checkConfirmSettleCode(ocBSapSalesDataGather, salesDataGatherItem);
                    if (count == 1) {
                        continue;
                    }
                }

                JSONArray data = new JSONArray();
                JSONObject paramVoJo = sapFunId(ocBSapSalesDataGather.getGatherNo(), ocBSapSalesDataGather.getSumType());
                RequestParamVo requestParamVo = new RequestParamVo(paramVoJo, "", "");
                JSONObject paramJo = null;
                if (SUM_TYPE00.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE01.equals(ocBSapSalesDataGather.getSumType())
                        || SUM_TYPE_RYCD01.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE_RYCD02.equals(ocBSapSalesDataGather.getSumType())
                        || SUM_TYPE_RYCD03.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE_RYCD04.equals(ocBSapSalesDataGather.getSumType())
                ) {
                    paramJo = saleOrderDataGatherParam(ocBSapSalesDataGather, ocBSapSalesDataRecord, salesDataGatherItem);
                } else if (SUM_TYPE_Z74.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE_Z73.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE_Z23.equals(ocBSapSalesDataGather.getSumType())) {
                    paramJo = adjustOrderDataGatherParam(ocBSapSalesDataGather);
                }
                data.add(paramJo);
                requestParamVo.setData(data);
                String inJson = JSON.toJSONString(requestParamVo);
                log.debug(LogUtil.format("salesOrderDataGatherBack.gather.inJson={}"), inJson);
                String dataStr = restTemplateUtil.doPostForm(sapUrl, inJson, "");
                log.debug(LogUtil.format("salesOrderDataGatherBack.gather.dataStr={}"), dataStr);
                dataGatherStr(successIds, failList, map, dataStr, ocBSapSalesDataGather.getId(), ocBSapSalesDataGather.getGatherNo(), inJson);
            } catch (Exception e) {
                map.put("id", String.valueOf(ocBSapSalesDataGather.getId()));
                map.put("msg", e.getMessage());
                failList.add(map);
                log.error(LogUtil.format("SapSalesDataGatherService.error message:{}",
                        "salesOrderDataGatherBack.error"), Throwables.getStackTraceAsString(e));
            }
        }

        //更新传送状态：传成功
        if (CollectionUtils.isNotEmpty(successIds)) {
            batchUpdateSapSuccessStatus(successIds, SapStatusEnum.SEND_SUCCESS.getKey());

        }
        //更新传送状态：传失败
        if (CollectionUtils.isNotEmpty(failList)) {
            failList.forEach(stringMap -> {
                updateSapFailStatus(Long.parseLong(stringMap.get("id")), SapStatusEnum.SEND_FAIL.getKey(), stringMap.get("msg"));
            });
        }
    }

    /**
     * 销售订单创建和奶卡/周期购订单特殊存档参数封装
     *
     * @param ocBSapSalesDataGather
     * @return
     */
    public JSONObject saleOrderDataGatherParam(OcBSapSalesDataGather ocBSapSalesDataGather, OcBSapSalesDataRecord ocBSapSalesDataRecord, List<OcBSapSalesDataGatherItem> salesDataGatherItem) {
        JSONObject paramJo = new JSONObject();
        //中间件单号
        paramJo.put("ZVBELN", ocBSapSalesDataGather.getGatherNo());
        //旺店通订单日期
        paramJo.put("AUDAT", getInDate(ocBSapSalesDataGather.getInTime()));
        //销售凭证类型
        paramJo.put("AUART", ocBSapSalesDataGather.getSapBillType());
        // 销售组织
        paramJo.put("VKORG", ocBSapSalesDataGather.getSalesOrganization());
        if (SUM_TYPE01.equals(ocBSapSalesDataGather.getSumType())) {
            //业务类型
            paramJo.put("ZAUART", ocBSapSalesDataRecord.getMiddlegroundBillTypeCode());
            //业务类型描述
            paramJo.put("ZBEZEI", ocBSapSalesDataRecord.getMiddlegroundBillTypeName());
        }

        //分销渠道
        paramJo.put("VTWEG", "10");
        //产品组
        paramJo.put("SPART", "00");
        //店铺编码
        CpShop cpShop = cpRpcService.selectCpCShopById(ocBSapSalesDataGather.getCpCShopId());
        paramJo.put("KUNAG", cpShop.getEcode());
        //店铺编码
        paramJo.put("KUNWE", cpShop.getEcode());
        //定价日期和汇率
        paramJo.put("PRSDT", getInDate(ocBSapSalesDataGather.getInTime()));
        //成本中心
        paramJo.put("KOSTL", ocBSapSalesDataGather.getCostCenter());
        //明细
        paramJo.put("ITEM", salesOrderDataGatherItem(ocBSapSalesDataGather, salesDataGatherItem));
        return paramJo;
    }

    public JSONArray salesOrderDataGatherItem(OcBSapSalesDataGather ocBSapSalesDataGather, List<OcBSapSalesDataGatherItem> salesDataGatherItem) {
        log.info("salesOrderDataGatherItem salesDataGatherItem:{}", salesDataGatherItem);
        JSONArray gatherItemArray = new JSONArray();
        if (salesDataGatherItem != null && salesDataGatherItem.size() > 0) {
            for (int i = 0; i < salesDataGatherItem.size(); i++) {
                String itemNum = (i + 1) + "0";
                OcBSapSalesDataGatherItem ocBSapSalesDataGatherItem = salesDataGatherItem.get(i);
                if (SUM_TYPE_RYCD01.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE_RYCD02.equals(ocBSapSalesDataGather.getSumType())
                || SUM_TYPE_RYCD03.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE_RYCD04.equals(ocBSapSalesDataGather.getSumType())
                ) {
                    if (CONFIRM_SETTLE_Z01.equals(ocBSapSalesDataGather.getSapBillType()) ||
                            CONFIRM_SETTLE_ZCR2.equals(ocBSapSalesDataGather.getSapBillType()) ||
                            CONFIRM_SETTLE_ZCR3.equals(ocBSapSalesDataGather.getSapBillType()) ||
                            CONFIRM_SETTLE_ZDR2.equals(ocBSapSalesDataGather.getSapBillType()) ||
                            CONFIRM_SETTLE_ZDR3.equals(ocBSapSalesDataGather.getSapBillType())) {
                        if (ocBSapSalesDataGatherItem.getAmt().compareTo(BigDecimal.ZERO) == 0) {
                            continue;
                        }
                    }

                }
                JSONObject gatherItemJo = new JSONObject();
                //行项目号
                gatherItemJo.put("POSNR", itemNum);
                //物料
                gatherItemJo.put("MATNR", ocBSapSalesDataGatherItem.getSku());
                if (SUM_TYPE00.equals(ocBSapSalesDataGather.getSumType()) || SUM_TYPE01.equals(ocBSapSalesDataGather.getSumType())) {
                    //行项目类型
                    gatherItemJo.put("PSTYV", pstyvValue(ocBSapSalesDataGatherItem.getLineType()));
                }
                if (SUM_TYPE01.equals(ocBSapSalesDataGather.getSumType())) {
                    gatherItemJo.put("ZTI", ocBSapSalesDataGatherItem.getCycleQty());
                }

                //计划行类别
                gatherItemJo.put("ETTYP", ocBSapSalesDataGatherItem.getLineCategory());
                //汇总数量
                gatherItemJo.put("KWMENG", ocBSapSalesDataGatherItem.getQty());
                //SKU单位
                gatherItemJo.put("VRKME", ocBSapSalesDataGatherItem.getUnit());
                if (ocBSapSalesDataGatherItem.getCpCStoreId() != null) {
                    CpStore cpStore = cpRpcService.selectCpCStoreById(ocBSapSalesDataGatherItem.getCpCStoreId());
                    log.info("salesOrderDataGatherItem cpStore:{}", cpStore);
                    //工厂
                    gatherItemJo.put("WERKS", ocBSapSalesDataGatherItem.getFactoryCode());
                    //逻辑仓编码
                    gatherItemJo.put("LGORT", cpStore.getEcode());
                } else {
                    gatherItemJo.put("WERKS", ocBSapSalesDataGatherItem.getFactoryCode());
                }
                //销售总价
                gatherItemJo.put("KBETR", ocBSapSalesDataGatherItem.getAmt());
                //特殊处理标准
                if ("Y".equals(ocBSapSalesDataGatherItem.getProType())) {
                    gatherItemJo.put("SDABW", "Z1");
                } else {
                    gatherItemJo.put("SDABW", "");
                }

                //剩余提数
                if (ocBSapSalesDataGatherItem.getResidueQty() != null) {
                    gatherItemJo.put("ZWTI", ocBSapSalesDataGatherItem.getResidueQty());
                }

                //成本
                if (ocBSapSalesDataGatherItem.getPriceCost() != null) {
                    gatherItemJo.put("ZNBTR", ocBSapSalesDataGatherItem.getPriceCost());
                }

                gatherItemArray.add(gatherItemJo);
            }
        }
        log.info("storageChangeItems ItemArray:{}", gatherItemArray);
        return gatherItemArray;
    }

    /**
     * 无头件入库出库冲销参数封装
     *
     * @param ocBSapSalesDataGather
     * @return
     */
    public JSONObject adjustOrderDataGatherParam(OcBSapSalesDataGather ocBSapSalesDataGather) {
        JSONObject adjustJo = new JSONObject();
        //凭证日期
        adjustJo.put("BLDAT", getInDate(ocBSapSalesDataGather.getInTime()));
        //过账日期
        adjustJo.put("BUDAT", getInDate(ocBSapSalesDataGather.getInTime()));
        //创建人
        adjustJo.put("USNAM", "中台");
        //明细
        adjustJo.put("ITEM", adjustOrderDataGatherItem(ocBSapSalesDataGather));
        return adjustJo;
    }

    public JSONArray adjustOrderDataGatherItem(OcBSapSalesDataGather ocBSapSalesDataGather) {
        List<OcBSapSalesDataGatherItem> salesDataGatherItem = querySalesDataGatherItem(ocBSapSalesDataGather.getId());
        log.info("adjustOrderDataGatherItem salesDataGatherItem:{}", salesDataGatherItem);
        JSONArray gatherItemArray = new JSONArray();
        if (salesDataGatherItem != null && salesDataGatherItem.size() > 0) {
            for (OcBSapSalesDataGatherItem ocBSapSalesDataGatherItem : salesDataGatherItem) {
                JSONObject gatherItemJo = new JSONObject();
                //物料编码
                gatherItemJo.put("MATNR", ocBSapSalesDataGatherItem.getSku());
                //汇总数量
                gatherItemJo.put("MENGE", Math.abs(ocBSapSalesDataGatherItem.getQty()));
                //SKU单位
                gatherItemJo.put("MEINS", ocBSapSalesDataGatherItem.getUnit());
                CpStore cpStore = cpRpcService.selectCpCStoreById(ocBSapSalesDataGatherItem.getCpCStoreId());
                log.info("adjustOrderDataGatherItem cpStore:{}", cpStore);
                //工厂
                gatherItemJo.put("WERKS", ocBSapSalesDataGatherItem.getFactoryCode());
                //逻辑仓编码
                gatherItemJo.put("LGORT", cpStore.getEcode());
                //成本中心
                gatherItemJo.put("KOSTL", ocBSapSalesDataGather.getCostCenter());
                //移动类型
                gatherItemJo.put("BWART", ocBSapSalesDataGather.getSumType());
                gatherItemArray.add(gatherItemJo);
            }
        }
        log.info("adjustOrderDataGatherItem ItemArray:{}", gatherItemArray);
        return gatherItemArray;
    }

    private void dataGatherStr(List<Long> successIds, List<Map<String, String>> failList, Map<String, String> map, String dataStr, Long id, String gatherNo, String inJson) {
        int code;
        String interfaceName = null;
        if (dataStr != null) {
            JSONObject dataJo = JSON.parseObject(dataStr);
            interfaceName = dataJo.getJSONObject("CTRL").getString("FUNID");
            JSONArray dataArray = dataJo.getJSONArray("DATA");
            JSONObject msgJo = dataArray.getJSONObject(0);
            if ("S".equals(msgJo.getString("MSGTY"))) {
                successIds.add(id);
                code = 0;
            } else {
                map.put("id", String.valueOf(id));
                map.put("msg", msgJo.getString("MSAGE"));
                failList.add(map);
                code = -1;
            }
        } else {
            map.put("id", String.valueOf(id));
            map.put("msg", "调用SAP接口失败");
            failList.add(map);
            code = -1;
        }
        //新增第三方接口日志
        sapInterfaceLogCmd.insertThirdInterfaceLong(gatherNo, THIRD_PARTY_LOG_BILL_TYPE_20, interfaceName, "SAP", "中台", code, new Date(), inJson, dataStr);
    }


    public JSONObject sapFunId(String gatherNo, String sumType) {
        JSONObject paramVoJo = null;
        if (SUM_TYPE00.equals(sumType)) {
            //销售订单创建
            paramVoJo = SapUtil.paramValue("ZOMSINF036", gatherNo);
        } else if (SUM_TYPE01.equals(sumType)) {
            //奶卡/周期购订单特殊存档
            paramVoJo = SapUtil.paramValue("ZOMSINF032", gatherNo);
        } else if (SUM_TYPE_RYCD01.equals(sumType) || SUM_TYPE_RYCD02.equals(sumType) || SUM_TYPE_RYCD03.equals(sumType) || SUM_TYPE_RYCD04.equals(sumType)) {
            //销售订单创建
            paramVoJo = SapUtil.paramValue("ZOMSINF036", gatherNo);
        } else if (SUM_TYPE_Z74.equals(sumType) || SUM_TYPE_Z73.equals(sumType) || SUM_TYPE_Z23.equals(sumType)) {
            //无头件入库及出库冲销
            paramVoJo = SapUtil.paramValue("ZOMSINF022", gatherNo);
        }
        return paramVoJo;
    }

    public String pstyvValue(String lineType) {
        String pstyv = null;
        if ("0".equals(lineType)) {
            pstyv = "ZTD2";
        } else if ("1".equals(lineType)) {
            pstyv = "ZTD1";
        } else if ("2".equals(lineType)) {
            pstyv = "ZDK1";
        } else if ("3".equals(lineType)) {
            pstyv = "ZDKE";

        }
        return pstyv;
    }

    public String getInDate(Date inDate) {
        if (inDate != null) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            return simpleDateFormat.format(inDate);
        }
        return "";
    }

    /**
     * 判断结算类型  “Z01Z01/ZCR2/ZCR3/ZDR2/ZDR3” ,如果明细行所有成交金额为0,整单不传SAP
     *
     * @param ocBSapSalesDataGather
     */
    public Integer checkConfirmSettleCode(OcBSapSalesDataGather ocBSapSalesDataGather, List<OcBSapSalesDataGatherItem> salesDataGatherItem) {
        List<Long> itemId = salesDataGatherItem.stream().
                filter(e -> (e.getAmt().compareTo(BigDecimal.ZERO)) > 0).
                map(OcBSapSalesDataGatherItem::getId).collect(Collectors.toList());
        if (CONFIRM_SETTLE_Z01.equals(ocBSapSalesDataGather.getSapBillType()) ||
                CONFIRM_SETTLE_ZCR2.equals(ocBSapSalesDataGather.getSapBillType()) ||
                CONFIRM_SETTLE_ZCR3.equals(ocBSapSalesDataGather.getSapBillType()) ||
                CONFIRM_SETTLE_ZDR2.equals(ocBSapSalesDataGather.getSapBillType()) ||
                CONFIRM_SETTLE_ZDR3.equals(ocBSapSalesDataGather.getSapBillType())) {

            if (itemId.size() == 0) {
                //不需要传SAP，直接改成已推送
                List<Long> idList = new ArrayList<>();
                idList.add(ocBSapSalesDataGather.getId());
                batchUpdateSapSuccessStatus(idList, SapStatusEnum.SEND_SUCCESS.getKey());
                return 1;
            }
        }
        return 0;
    }
}
