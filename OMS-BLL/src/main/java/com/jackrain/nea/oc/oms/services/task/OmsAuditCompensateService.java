//package com.jackrain.nea.oc.oms.services.task;
//
//import com.google.common.collect.Lists;
//import com.jackrain.nea.config.PropertiesConf;
//import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
//import com.jackrain.nea.oc.oms.mapper.task.OcBAuditTaskMapper;
//import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
//import com.jackrain.nea.oc.oms.model.table.OcBOrder;
//import com.jackrain.nea.oc.oms.model.table.task.OcBAuditTask;
//import com.jackrain.nea.oc.oms.sap.Oms2SapDBMetaCache;
//import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
//import com.jackrain.nea.util.ApplicationContextHandle;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.stereotype.Component;
//
//import java.text.SimpleDateFormat;
//import java.util.*;
//import java.util.concurrent.ArrayBlockingQueue;
//import java.util.concurrent.Callable;
//import java.util.concurrent.ExecutionException;
//import java.util.concurrent.Future;
//import java.util.stream.Collectors;
//
///**
// * @Author: 黄世新
// * @Date: 2020/9/25 12:01 下午
// * @Version 1.0
// * <p>
// * 自动审核补偿任务(直接入rds数据库捞数据)
// */
//@Slf4j
//@Component
//public class OmsAuditCompensateService {
//
//    private static final String ORDER_TABLE_NAME = "oc_b_order";
//    @Autowired
//    private OcBOrderMapper ocBOrderMapper;
//    @Autowired
//    private OcBAuditTaskMapper ocBAuditTaskMapper;
//    @Autowired
//    protected BuildSequenceUtil buildSequenceUtil;
//
////    /**
////     * 审核补偿任务入口
////     */
////    public void omsAuditCompensate() {
////        ArrayBlockingQueue<Runnable> blockingQueue = new ArrayBlockingQueue<>(16);
////
////        Set<String> nodes = topMap.keySet();
////        if (CollectionUtils.isEmpty(nodes)) {
////            return;
////        }
////        //获取当前时间一个小时之前的时间
////        PropertiesConf config = ApplicationContextHandle.getBean(PropertiesConf.class);
////        int autoTime = config.getProperty("oms_audit_compensate_time", 1);
////        Date date = new Date();
////        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
////        String endTime = sdf.format(date);
////        String startTime = this.getTime(date, autoTime, sdf);
////        List<Future<Boolean>> results = new ArrayList<>();
////        try {
////            for (String nodeName : nodes) {
////                results.add(auditCompensateTaskThreadPoolExecutor.submit(new CallableTobeAuditCompensateWithResult(nodeName, topMap.get(nodeName),
////                        endTime, startTime)));
////            }
////            //线程执行结果获取
////            for (Future<Boolean> futureResult : results) {
////                try {
////                    if (log.isDebugEnabled()) {
////                        log.debug("{}.线程结果={}", this.getClass().getName(), futureResult.get().toString());
////                    }
////                } catch (InterruptedException e) {
////                    log.error("OmsAuditCompensateService多线程获取InterruptedException异常", e);
////                } catch (ExecutionException e) {
////                    log.error("OmsAuditCompensateServicek多线程获取ExecutionException异常", e);
////                }
////            }
////        } catch (Exception ex) {
////            log.error("{}.自动审核补偿任务异常", this.getClass().getName(), ex);
////        }
////
////    }
//
////    /**
////     * 开启线程类
////     */
////    class CallableTobeAuditCompensateWithResult implements Callable<Boolean> {
////        private String nodeName;
////        private String taskTableName;
////        private String endTime;
////        private String startTime;
////
////        @Override
////        public Boolean call() throws Exception {
////            List<OcBOrder> ocBOrders = ocBOrderMapper.selectOrderAudit(nodeName, taskTableName, startTime, endTime);
////            if (log.isDebugEnabled()) {
////                log.debug("OmsAuditCompensateService.nodeName={}.taskTableName={}", nodeName, taskTableName);
////            }
////            if (CollectionUtils.isEmpty(ocBOrders)) {
////                return true;
////            }
////            Map<Long, Long> ids = ocBOrders.stream().collect(Collectors.toMap(OcBOrder::getId, OcBOrder::getCpCShopId));
////            Set<Long> longSet = ids.keySet();
////            List<Long> orderIdList = new ArrayList<>(longSet);
////            List<List<Long>> basePageList = Lists.partition(orderIdList, 200);
////            for (List<Long> pageOrderList : basePageList) {
////                List<OcBAuditTask> auditTaskList = ocBAuditTaskMapper.selectTaskIdListByOrderIds(pageOrderList);
////                if (CollectionUtils.isEmpty(auditTaskList)) {
////                    handleData(pageOrderList, ids);
////                    //全部插入
////                } else {
////                    List<Long> tempOrderIdList = new ArrayList<>(pageOrderList);
////                    // 更新审核任务状态
////                    List<Long> tempUpdateAuditTaskStatusOrderIdList = new ArrayList<>(pageOrderList.size());
////                    for (OcBAuditTask auditTask : auditTaskList) {
////                        if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(auditTask.getStatus())) {
////                            tempUpdateAuditTaskStatusOrderIdList.add(auditTask.getOrderId());
////                        }
////                        tempOrderIdList.remove(auditTask.getOrderId());
////                    }
////                    // 更新审核task状态为未审核
////                    if (CollectionUtils.isNotEmpty(tempUpdateAuditTaskStatusOrderIdList)) {
////                        ocBAuditTaskMapper.updateTaskStatusByOrderId(tempUpdateAuditTaskStatusOrderIdList);
////                    }
////                    handleData(tempOrderIdList, ids);
////                }
////            }
////            return true;
////        }
////
////        public CallableTobeAuditCompensateWithResult(String nodeName, String taskTableName, String endTime, String startTime) {
////            this.nodeName = nodeName;
////            this.taskTableName = taskTableName;
////            this.endTime = endTime;
////            this.startTime = startTime;
////        }
////    }
//
////    private void handleData(List<Long> ids, Map<Long, Long> idMap) {
////        if (CollectionUtils.isNotEmpty(ids)) {
////            List<OcBAuditTask> auditTasks = new ArrayList<>();
////            for (Long id : ids) {
////                OcBAuditTask auditTask = new OcBAuditTask();
////                auditTask.setId(buildSequenceUtil.buildAuditTaskId());
////                auditTask.setOrderId(id);
////                auditTask.setStatus(0);
////                auditTask.setCreationdate(new Date());
////                auditTask.setModifieddate(new Date());
////                auditTask.setShopId(idMap.get(id));
////                auditTask.setRetryNumber(0);
////                auditTask.setNextTime(System.currentTimeMillis());
////                auditTasks.add(auditTask);
////            }
////            ocBAuditTaskMapper.batchInsert(auditTasks);
////        }
////    }
////
////    private String getTime(Date date, int hour, SimpleDateFormat sdf) {
////        Calendar rightNow = Calendar.getInstance();
////        rightNow.setTime(date);
////        //rightNow.add(Calendar.DATE, -1);
////        rightNow.add(Calendar.HOUR, -hour);
////        Date dt1 = rightNow.getTime();
////        String format = sdf.format(dt1);
////        return format;
////    }
//}
