package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

/**
 * @Author: huang.zaizai
 * @Date: 2019/7/24 14:00
 * @Version 1.0
 * 物料类型
 */
public enum MaterieltypeEnum {

    CLOTHES_ADULT("ZF01", "服装-成人"),
    CLOTHES_CHILD("ZF02", "服装-儿童"),
    SHOES_ADULT("ZF03", "鞋-成人"),
    SHOES_CHILD("ZF04", "鞋-儿童"),
    PARTS_ADULT("ZF05", "配件-成人"),
    PARTS_CHILD("ZF06", "配件-儿童"),
    PROMOTIONAL("ZF07", "推广用品"),
    MAIN_MATERIAL("ZF08", "主料"),
    ASSIST_MATERIAL("ZF09", "辅料"),
    SEMI_PRODUCTS("ZF10", "半成品");


    MaterieltypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private String code;

    @Getter
    private String name;


}
