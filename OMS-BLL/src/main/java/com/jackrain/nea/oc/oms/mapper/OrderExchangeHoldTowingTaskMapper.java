package com.jackrain.nea.oc.oms.mapper;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OrderExchangeHoldTowingTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OrderExchangeHoldTowingTaskMapper extends ExtentionMapper<OrderExchangeHoldTowingTask> {


    @Select("SELECT OC_B_ORDER_ID from order_exchange_hold_towing_task where ESTATUS = 0 LIMIT #{limit} ORDER BY modifieddate ASC")
    List<Long> queyList(Integer totalCount);

    @Update("<script> "
            + "UPDATE order_exchange_hold_towing_task set  modifieddate = NOW() where OC_B_ORDER_ID IN "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int batchUpdateDate(@Param("ids") List<Long> orderList);

    @Update("<script> "
            + "UPDATE order_exchange_hold_towing_task set  modifieddate = NOW(),ESTATUS= #{status} ,remake = #{remake} where OC_B_ORDER_ID IN "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateOrderExchangeHoldTowingStatus(@Param("ids") List<Long> orderList,@Param("status")  String status, @Param("remake") String remake);

    @Update("UPDATE order_exchange_hold_towing_task set  modifieddate = NOW(),ESTATUS= #{status} ,remake = #{remake} where bill_no = #{billNo} ")
    int updateStatus(@Param("billNo") String id, @Param("status")String status ,@Param("remake") String errMsg);

    @Update("<script> "
            + "UPDATE order_exchange_hold_towing_task set  modifieddate = NOW(),ESTATUS= #{status} ,remake = #{remake}  where OC_B_ORDER_ID IN "
            + "<foreach item='item' index='index' collection='billNoList' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateStatusBybillNo(@Param("billNoList")List<String> billNoList, @Param("") String remake, @Param("status") String status3);

    @Select("SELECT count(1) from order_exchange_hold_towing_task where ESTATUS =4 and OC_B_ORDER_ID = #{orderId}")
    int selectStatusSuccess(@Param("orderId") Long orderId);
}