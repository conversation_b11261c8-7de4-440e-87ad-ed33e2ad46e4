package com.jackrain.nea.oc.oms.constant;

/**
 * Description: 销售数据常量
 *
 * @Author: guo.kw
 * @Since: 2022/8/29
 * create at: 2022/8/29 10:48
 */
public class SapSalesDateConstant {

    /**
     * 汇总状态
     * 2 汇总失败
     * 1 已汇总
     * 0 未汇总
     */
    public static final String SUM_STATUS_SUMMARIZED = "1";
    public static final String SUM_STATUS_UNSUMMARIZED = "0";
    public static final String SUM_STATUS_FAIL = "2";

    /**
     * 销售组织
     * 1 免费
     * 2 常规
     */
    public static final String SALES_ORGANIZATION_FREE = "1";
    public static final String SALES_ORGANIZATION_GENERAL = "2";

    /**
     * 汇总中间状态 (1、汇总中，2、汇总完成)
     */
    public static final String GATHER_MIDDLE_STATUS_01 = "1";
    public static final String GATHER_MIDDLE_STATUS_02 = "2";
    /**
     * 可用状态
     */
    public static final String ISACTIVE_YES = "Y";
    public static final String ISACTIVE_NO = "N";

    public static final Integer QUERY_SIZE_FIVE = 500;
    public static final Integer QUERY_MAX_SIZE = 1000;

    /**
     * 传sap状态
     * 0 未传、 1 传中、 2 成功、 3 失败、 4 不传
     */
    public static final String TO_SAP_STATUS_INIT = "0";
    public static final String TO_SAP_STATUS_WAIT = "1";
    public static final String TO_SAP_STATUS_SUCCESS = "2";
    public static final String TO_SAP_STATUS_FAIL = "3";
    public static final String TO_SAP_STATUS_NO = "4";

    //失败次数初始化
    public static final Integer TO_SAP_FAIL_NUMBER = 0;

    //销售汇总表
    public static final String OC_B_SAP_SALES_DATA_GATHER = "OC_B_SAP_SALES_DATA_GATHER";
    public static final String OC_B_SAP_SALES_DATA_GATHER_ITEM = "OC_B_SAP_SALES_DATA_GATHER_ITEM";
    public static final String OC_B_SAP_SALES_DATA_GATHER_SOURCE_ITEM = "OC_B_SAP_SALES_DATA_GATHER_SOURCE_ITEM";

    //汇总类型
    public static final String SUM_TYPE_NKCD = "2";

    /**
     * 汇总单号前缀
     * 奶卡冲抵单 N
     */
    public static final String NKCD_PREFIX = "N";

    public static final String BILL_TYPE_NKTH = "奶卡提货";
    public static final String BILL_TYPE_ZTZQGTH = "中台周期购提货";
    public static final String OC_B_SAP_SALES_DATA_NKZQGTH = "奶卡周期购提货";
    public static final String OC_B_SAP_SALES_DATA_DSXS = "电商销售";


    public static final String SUM_SUCCESS = "SUCCESS";
    public static final String SUM_ERROR = "ERROR";
}
