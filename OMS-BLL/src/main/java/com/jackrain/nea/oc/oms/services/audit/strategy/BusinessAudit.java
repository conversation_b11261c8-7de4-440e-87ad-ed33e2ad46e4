package com.jackrain.nea.oc.oms.services.audit.strategy;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.enums.OmsMethod;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.IpBJitxDeliveryRecordService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.oc.oms.services.audit.AuditEnum;
import com.jackrain.nea.oc.oms.services.audit.AuditStrategyHandler;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderAutoAuditService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.util.OmsOrderUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 业务审核
 *
 * @Auther: 黄志优
 * @Date: 2020/11/3 10:31
 * @Description:
 */
@Service
@Slf4j
public class BusinessAudit implements AuditStrategyHandler {

    /**
     * 商品效期范围校验是否开启。0:关闭；1:开启
     */
    @NacosValue(value = "${r3.oc.oms.audit.expire.check:0}", autoRefreshed = true)
    public Integer auditExpireCheck;

    @Autowired
    private OmsOrderAutoAuditService omsOrderAutoAuditService;

    @Autowired
    private OmsOrderAdvanceParseService orderAdvanceParseService;

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private IpBJitxDeliveryRecordService deliveryRecordService;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private CpRpcService cpRpcService;

    @Override
    public boolean doHandle(OcBOrderRelation orderInfo, User operateUser) {
        OmsMethod omsMethod = orderInfo.getOmsMethod();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("audit.开始校验业务数据", orderInfo.getOrderId()));
        }

        // 增加订单总金额和明细金额校验，不能为负数
        if (!omsOrderAutoAuditService.checkOrderTotalAmount(orderInfo)) {
            String message = "订单总金额/已支付金额/明细成交金额,不允许为负数!";
            log.info("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_60);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_60);
            return false;
        }

        //占用仓库为YY实体仓，如果寻仓结果表未返回YY仓库占用结果，不允许审核
        if (Boolean.FALSE.equals(deliveryRecordService.canAudit(orderInfo))) {
            String message = "发货仓库为YY实体仓,寻仓结果表未返回YY仓库占用结果,审核失败!";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_32);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_52);
            return false;
        }


        //订单的主表商品金额与明细不一致
        if (!omsOrderAutoAuditService.checkItemAmount(orderInfo)) {
            String message = "订单的主表商品金额与明细不一致,审核失败!";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_32);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_32);
            return false;
        }

        //检验预售尾款未付订单且【建议预下沉】不为 “Y”
        boolean b = orderAdvanceParseService.checkIsDepositPreSale(orderInfo.getOrderInfo());
        if (b) {
            //检验预售尾款未付订单
            String reserveVarchar03 = orderInfo.getOrderInfo().getStatusPayStep();
            if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(reserveVarchar03)) {
                if (!TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(orderInfo.getOrderInfo().getSuggestPresinkStatus()) && !TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(orderInfo.getOrderInfo().getSuggestPrepackageStatus())) {
                    String message = "订单OrderId=" + orderInfo.getOrderId() + "订单为尾款未付，且无需预下沉或预打包的定金预售订单，不允许审核！";
                    omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_15);
                    orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_47);
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("订单OrderId={},预售尾款未付订单,审核失败", orderInfo.getOrderId()), orderInfo.getOrderId());
                    }
                    return false;
                }
            }
        }

        //检验预售尾款未付订单
        if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(orderInfo.getOrderInfo().getStatusPayStep()) && !TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(orderInfo.getOrderInfo().getSuggestPresinkStatus()) && !TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(orderInfo.getOrderInfo().getSuggestPrepackageStatus())) {
            String message = "预售尾款未付订单,审核失败!";
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_15);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_15);
            return false;
        }

        //检验单据不含退款的和交易关闭的订单
        if (TaoBaoOrderStatus.TRADE_CLOSED.equalsIgnoreCase(orderInfo.getOrderInfo().getPlatformStatus())) {
            String message = "单据平台状态交易关闭,审核失败!";
            log.info("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_51);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_51);
            return false;
        }

        //判断是否为双11预收订单状态
        if (!omsOrderAutoAuditService.checkPresaleStatus(orderInfo)) {
            String message = "订单为预售订单,且尾款未付,审核失败!";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_26);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_26);
            return false;
        }

        //订单明细成交价金额是否正常，该判断最佳阻塞流程
        if (!omsOrderAutoAuditService.checkItemPriceAmount(orderInfo)) {
            String message = "订单的明细商品成交价不正确,审核失败!";
            log.info("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_21);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_21);
            return false;
        }
        //判断组合商品金额是否一致
        if (!omsOrderAutoAuditService.checkItemPriceAmountGroup(orderInfo)) {
            String message = "组合福袋商品成交金额计算错误，不允许审核！";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_37);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_37);
            return false;
        }

        //判断“付款方式”为货到付款订单 && 物流公司非京东(根据物流公司ID为-2进行判断)
        if (!omsOrderAutoAuditService.checkOrderPlatform(orderInfo)) {
            String message = "订单为京东平台货到付款订单物流必须是京东物流,审核失败!";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_23);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_23);
            return false;
        }

        //判断淘宝预售是否修改地址
        if (omsOrderAutoAuditService.checkOrderAddressAwaitSys(orderInfo.getOrderInfo())) {
            String message = "在淘宝待修改表中待修改，不允许审核！";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_17);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_17);
            return false;
        }

        //判断【淘宝待修改地址表】同平台单号的“是否更新”状态为未更新
        if (!omsOrderAutoAuditService.updateTaobaoSourceStatus(orderInfo)) {
            String message = "订单调用更新省市区服务异常,审核失败!";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_30);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_30);
            return false;
        }

        //检查若“订单来源”为手工新增，且“付款方式”为货到付款，则判断代收货款必须大于0
        if (!omsOrderAutoAuditService.checkOrderSource(orderInfo)) {
            String message = "订单为货到付款的手工新增订单,代收货款必须大于0,审核失败!";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_27);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_27);
            return false;
        }

        //“付款方式”为在线支付，且“服务费”、“代收货款”（表字段叫到付代收金额）都为0
        if (!omsOrderAutoAuditService.checkOrderServiceAmtAndCodeAmt(orderInfo)) {
            String message = "订单为在线支付订单，代收货款及服务费必须为0,审核失败!";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_28);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_28);
            return false;
        }

        //判断唯品会JitX是否改仓
        if (!omsOrderAutoAuditService.checkVipJitxChangeWarehouse(orderInfo, operateUser)) {
            return false;
        }
        if (omsSystemConfig.isJitxMergedOrderShipWhenAudit()) {
            OcBOrder order = orderInfo.getOrderInfo();
            //合包后的订单
            if (StringUtils.isNotEmpty(order.getMergedCode())) {
                if (log.isDebugEnabled()) {
                    log.debug("发货重置工单校验开始");
                }
                //判断唯品会JitX是否发货重置成功
                if (!omsOrderAutoAuditService.checkVipJitxResetShip(orderInfo, operateUser)) {
                    return false;
                }
            }
        }

        //调用线上代销资金占用变动服务
//        if (!omsOrderAutoAuditService.checkStrategyPriceComputeOrder(orderInfo)) {
//            return false;
//        }
        //判断订单主表的【整单折扣】，大于等于最低折扣
        if (!OmsOrderUtil.isToCCcOrder(orderInfo.getOrderInfo()) && !omsOrderAutoAuditService.checkOrderDiscount(orderInfo)) {
            String message = "不满足订单主表整单折扣不小于最低折扣,审核失败!！";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_42);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_42);
            return false;
        }
        //判断订单主表的【总金额】，大于等于最低成交金额商品价格策略
        if (!OmsOrderUtil.isToCCcOrder(orderInfo.getOrderInfo()) && !omsOrderAutoAuditService.checkOrderAmount(orderInfo)) {
            String message = "不满足订单主表总金额不小于最低成交金额,审核失败!！";
            log.error("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_41);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_41);
            return false;
        }

        //调用价格方案服务
        boolean mandatoryAudit = orderInfo.getMandatoryAudit();
        if (!mandatoryAudit) {
            //强制审核时，不需要进行价格校验
            boolean toBOrder = OmsBusinessTypeUtil.isToBOrder(orderInfo.getOrderInfo());
            boolean sapToCOrder = OmsBusinessTypeUtil.isSapToCOrder(orderInfo.getOrderInfo());
            if (!OmsOrderUtil.isToCCcOrder(orderInfo.getOrderInfo())){
                if (!(toBOrder || sapToCOrder) && !omsOrderAutoAuditService.checkOrderProPrice(orderInfo)) {
                    String message = "订单中商品条码成交价低于策略方案成交价,审核失败!！";
                    log.info("{}.audit.message={}", orderInfo.getOrderId(), message);
                    omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_43);
                    orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_43);
                    return false;
                }
            }

            //如果冻结店铺不能审核打钩，订单不自动审核
            if (!omsOrderAutoAuditService.checkShop(orderInfo)) {
                // 修改是否逾期字段
                OcBOrder updateOrder = new OcBOrder();
                updateOrder.setModifieddate(new Date());
                updateOrder.setId(orderInfo.getOrderId());
                updateOrder.setIsOverdue(1);
                omsOrderService.updateOrderInfo(updateOrder);

                String message = "店铺已经冻结不能审核!";
                omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_57);
                orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_57);
                return false;
            }
        }

        //商品效期范围是否存在校验(商品效期范围校验是否开启。0:关闭；1:开启)
        if (auditExpireCheck != null && auditExpireCheck == 1) {
            List<String> noRangeSkuCodes = omsOrderAutoAuditService.checkExpiryDate(orderInfo);
            if (noRangeSkuCodes == null) {
                log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), "检查效期范围异常");
                noRangeSkuCodes = Lists.newArrayList("检查效期范围异常");
            }
            if (CollectionUtils.isNotEmpty(noRangeSkuCodes)) {
                // 明细效期范围为空，需要进一步判断WMS仓库类型
                Long warehouseId = orderInfo.getOrderInfo().getCpCPhyWarehouseId();
                boolean isFullerWarehouse = checkIsFullerWarehouse(warehouseId);

                if (isFullerWarehouse) {
                    // WMS仓库类型 = 富勒，不允许审核
                    String message = noRangeSkuCodes + "商品效期范围为空且仓库类型为富勒,无法审核!";
                    log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
                    omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_63);
                    orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_63);

                    noRangeLabelLog(orderInfo, operateUser, message);
                    return false;
                } else {
                    // WMS仓库类型 <> 富勒，允许审核，记录日志但不阻止审核
                    String message = noRangeSkuCodes + "商品效期范围为空但仓库类型非富勒,允许审核";
                    log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
                    // 不设置审核失败，允许继续审核
                }
            }
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("audit.校验业务数据完成: {}", orderInfo.getOrderId()), orderInfo.getOrderId());
        }

        return true;
    }

    /**
     * 商品效期校验无范围打标和添加日志
     *
     * @param orderInfo
     * @param operateUser
     * @param message
     */
    private void noRangeLabelLog(OcBOrderRelation orderInfo, User operateUser, String message) {
        //打标:无范围
        OcBOrder ocBOrder = new OcBOrder();
        ocBOrder.setId(orderInfo.getOrderInfo().getId());
        ocBOrder.setIsNoRange(OcBOrderConst.IS_STATUS_IY);
        ocBOrder.setModifieddate(new Date());
        omsOrderService.updateOrderInfo(ocBOrder);

        //新增订单日志
        omsOrderLogService.addUserOrderLog(orderInfo.getOrderInfo().getId(), orderInfo.getOrderInfo().getBillNo(),
                OrderLogTypeEnum.ORDER_EXAMINE.getKey(), message, null, null, operateUser);
    }

    /**
     * 检查仓库是否为富勒仓库
     *
     * @param warehouseId 仓库ID
     * @return true-富勒仓库，false-非富勒仓库
     */
    private boolean checkIsFullerWarehouse(Long warehouseId) {
        if (warehouseId == null) {
            return false;
        }

        try {
            // 调用仓库服务查询仓库档案信息
            // 这里需要根据实际的仓库服务接口来实现
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);
            if (cpCPhyWarehouse == null) {
                return false;
            }
            log.info("检查仓库类型，仓库编码={}", cpCPhyWarehouse.getEcode());
            if (ThirdWmsTypeEnum.FLWMS.getCode().equals(cpCPhyWarehouse.getWmsType())) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("检查仓库类型异常，仓库ID={}，异常信息={}", warehouseId, e.getMessage(), e);
            // 异常情况下返回false，避免误判为富勒仓库
            return false;
        }
    }

    @Override
    public Integer getSort(String name) {
        return AuditEnum.getValueFromValueTag(name);
    }
}
