package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TaoBaoUpdateAddressStatusEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 4:50 下午
 */
public class ES4TBModifyAddr {

    private ES4TBModifyAddr() {
    }

    /**
     * 业务：淘宝预售地址自动修改
     * 根据IsUpdate获取平台单号
     *
     * @param pageIndex 页码
     * @param pageSize 页条数
     * @return List sourceCode
     */
    public static List<String> findSourceCodeByIsUpdate(int pageIndex, int pageSize){
        List<String> orderIdList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISACTIVE", "Y");
            //3:待同步
            whereKeys.put("IS_UPDATE", TaoBaoUpdateAddressStatusEnum.AWAIT_SYS.getVal());

            String[] returnFieldNames = new String[]{"SOURCECODE"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "MODIFIEDDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TAOBAO_MODIFY_ADDR_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_TAOBAO_MODIFY_ADDR_INDEX_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderId = jsonObject.getString("SOURCECODE");
                    orderIdList.add(orderId);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return orderIdList;
    }
}
