package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.relation.TaskParam;
import com.jackrain.nea.oc.oms.model.result.QueryOrderItemGroupByResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderItemResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Mapper
@Component
public interface OcBOrderItemMapper extends ExtentionMapper<OcBOrderItem> {

    /**
     * 根据平台单号与零售发货单id获取明细表id(考虑到可能是订单进行了合并)
     *
     * @param orderId
     * @param tid
     * @return
     */
    @Select("SELECT id FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and tid=#{tid} and isactive='Y' ")
    List<Long> selectOrderItemIdList(@Param("orderId") long orderId, @Param("tid") String tid);

    /**
     * 整单明细
     *
     * @param orderId 订单ID
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and pro_type != 4")
    List<OcBOrderItem> selectOrderItemList(long orderId);

    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and refund_status != 6 ")
    List<OcBOrderItem> selectOrderItemListOccupy(long orderId);

    @Update("UPDATE oc_b_order_item \n" +
            "  SET barcode = null where id=#{objId}")
    int updateBarcodeById(@Param("objId") Long objId);

    /**
     * 查询可以发货的订单明细
     * 1、非退款完成的
     * 2、非赠品
     *
     * @param orderId
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and (ifnull(is_gift,0) != 1 or ifnull(gift_type,0) != 1) and refund_status != 6 and pro_type !=4 ")
    List<OcBOrderItem> selectItemListOfUnshippedAndNonGift(Long orderId);

    /**
     * 查询未进行发货的明细
     *
     * @param orderId
     * @return
     */
    @Select("SELECT distinct tid FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and ifnull(is_gift,0) != 1 and refund_status != 6 and is_sendout=0")
    List<String> selectCanPlatformItemTidList(Long orderId);

    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y'")
    List<OcBOrderItem> selectOrderItems(long orderId);

    /**
     * 根据订单id查询，未退款且( pro_type = 4 或 pro_type = 0 )
     *
     * @param orderId
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and refund_status != 6 and (pro_type = 4 or pro_type = 0)")
    List<OcBOrderItem> selectOrderItemListCombinationExecPm(long orderId);

    /**
     * 查询赠品明细
     *
     * @param orderId 订单ID
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and is_gift = 1 and isactive='Y' and pro_type != 4")
    List<OcBOrderItem> selectOrderItemFullGiftList(long orderId);

    /**
     * 查询赠品明细
     *
     * @param orderId 订单ID
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and ooid=#{ooid} and is_gift = 1 and isactive='Y' and pro_type != 4")
    List<OcBOrderItem> selectOrderItemFullGiftListWithOid(@Param("orderId") long orderId, @Param("ooid") String ooid);


    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and is_gift = 1 and gift_type = 1 and isactive='Y' and pro_type != 4 and refund_status != 6 and (gift_relation is null or gift_relation = '')")
    List<OcBOrderItem> selectSystemGiftOrderItem(@Param("orderId") long orderId);

    /**
     * 查询赠品信息
     *
     * @param orderId
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and is_gift = 1 and isactive='Y' and pro_type != 4 and (ooid is null or ooid != #{ooid})")
    List<OcBOrderItem> selectOrderItemGiftList(@Param("orderId") long orderId, @Param("ooid") String ooid);

    /**
     * 排除赠品的明细
     *
     * @param orderId
     * @return
     */

    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and is_gift = 0 and isactive='Y'  and pro_type != 4")
    List<OcBOrderItem> selectOrderItemListNotGift(long orderId);


    /**
     * 整单明细
     *
     * @param orderId 订单ID
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and refund_status != 6  and pro_type != 4")
    List<OcBOrderItem> selectOrderItemListAndReturn(long orderId);

    /**
     * 根据主订单编号 和 商品属性编码 查询配件【成人或者儿童】的订单信息
     *
     * @param orderId 订单ID ZF05:配件-成人 ;ZF06:配件-儿童
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y'and ps_c_pro_materieltype = 'ZF05' or ps_c_pro_materieltype = 'ZF06' and pro_type != 4")
    List<OcBOrderItem> selectPartsNum(long orderId);

    /**
     * 判断是否存在退款成功的明细 6 -退款成功
     *
     * @param id 订单ID
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and refund_status='6' and isactive='Y'  and pro_type != 4")
    List<OcBOrderItem> selectRefundItemList(Long id);

    /**
     * 统计明细总额
     *
     * @param orderId 订单Id
     * @return BigDecimal
     */
    @Select("SELECT SUM(price*qty ) AS totalPrice FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} "
            + "and isactive='Y' AND refund_status <> '6' and pro_type != 4")
    BigDecimal queryItemPriceCount(Long orderId);

    /**
     * 判断是否存在退款成功的明细 6 -退款成功
     * 查询明细 ooid+分库键oc_b_order_id
     *
     * @param ooid     ooid
     * @param orderIds orderIds
     * @return List<OcBOrderItem>
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE ooid=#{ooid} and isactive = 'Y' and pro_type != 4 and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectOrderItemListByOoid(@Param("ooid") String ooid, @Param("orderIds") List<Long> orderIds);


    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE isactive='Y' and refund_status != 6 and pro_type != 4 and isactive = 'Y' and id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectByIds(@Param("ids") List<Long> ids);


    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE refund_status='6' and isactive = 'Y'  and pro_type != 4 and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectOrderItemListByOrderIds(@Param("orderIds") List<Long> orderIds);


    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE refund_status != '6' and isactive = 'Y'  and pro_type != 4 and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectOrderItemsByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 根据订单ID列表和oid查询订单明细
     * oid为入参的值、为空或者空字符串时都会返回对应的记录
     *
     * @param orderIds 订单ID列表
     * @param oid oid参数
     * @return 订单明细列表
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE refund_status != '6' and isactive = 'Y' and pro_type != 4 "
            + "and (ooid = #{oid} or ooid is null or ooid = '') "
            + "and oc_b_order_id in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectOrderItemsByOrderIdsAndOid(@Param("orderIds") List<Long> orderIds, @Param("oid") String oid);


    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE isactive = 'Y'  and pro_type != 4 and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectAllStatusOrderItemsByOrderIds(@Param("orderIds") List<Long> orderIds);


    /**
     * @param orderId 订单ID
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and is_gift= 1 and isactive = 'Y' and pro_type != 4 ")
    List<OcBOrderItem> selectOrderGiftListByOrderId(@Param("orderId") long orderId);

    /**
     * 查找正常订单的明细
     *
     * @param orderId          订单Id
     * @param diffpriceskuList 虚拟明细集合
     * @return List<OcBOrderItem>
     */
    @Select("<script> SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y'  and pro_type != 4 and ps_c_sku_ecode not in "
            + "<foreach collection='diffpriceskuList' item='item' open='(' separator=',' close=')'> "
            + "#{item} "
            + "</foreach> "
            + "</script> ")
    List<OcBOrderItem> selectOrderNoramlItemList(
            @Param("orderId") Long orderId,
            @Param("diffpriceskuList") List<String> diffpriceskuList);

    /**
     * 未退款明细
     *
     * @param id 订单Id
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{id} and refund_status != 6 and isactive='Y'  and pro_type != 4")
    List<OcBOrderItem> selectUnSuccessRefund(@Param("id") Long id);

    /**
     * 未退款明细(用proType in(0,4))
     *
     * @param id 订单Id
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{id} and refund_status != 6 and isactive='Y'  and pro_type in (4,0)")
    List<OcBOrderItem> selectUnSuccessRefundAudit(@Param("id") Long id);


    /**
     * @param id
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{id} and refund_status != 6 and isactive='Y'")
    List<OcBOrderItem> selectUnSuccessRefundAndNoSplit(@Param("id") Long id);

    /**
     * 根据订单id查询订单明细列表
     *
     * @param id 订单Id
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{id}  and isactive='Y'")
    List<OcBOrderItem> selectAllOrderItem(@Param("id") Long id);

    @Select("SELECT ps_c_sku_id FROM oc_b_order_item WHERE oc_b_order_id=#{id}  and isactive='Y'")
    List<Long> selectOrderItemSku(@Param("id") Long id);

    /**
     * 未退款明细(包含组合商品)
     *
     * @param id 订单Id
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{id} and refund_status != 6 and isactive='Y'")
    List<OcBOrderItem> selectOrderItemContainsCombination(@Param("id") Long id);

    /**
     * 虚拟明细
     *
     * @param orderId          订单ID
     * @param diffpriceskuList 虚拟明细
     * @return List<OcBOrderItem>
     */
    @Select("<script> SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y'  and pro_type != 4 and ps_c_sku_ecode  in "
            + "<foreach collection='diffpriceskuList' item='item' open='(' separator=',' close=')'> "
            + "#{item} "
            + "</foreach> "
            + "</script> ")
    List<OcBOrderItem> selectDiffenPriceItemList(
            @Param("orderId") Long orderId, @Param("diffpriceskuList") List<String> diffpriceskuList);

    /**
     * 查询订单明细
     *
     * @param id         明细Id
     * @param ocBOrderId 订单ID
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE id=#{id} and oc_b_order_id=#{orderId} and isactive='Y' and pro_type != 4")
    OcBOrderItem queryOrderItemById(@Param("id") Long id, @Param("orderId") Long ocBOrderId);


    @Select("SELECT * FROM oc_b_order_item WHERE id=#{id} and oc_b_order_id=#{orderId} and isactive='Y'")
    OcBOrderItem queryOrderItemByIdPro(@Param("id") Long id, @Param("orderId") Long ocBOrderId);


    @Select("SELECT * FROM oc_b_order_item WHERE id=#{id} and oc_b_order_id=#{orderId} and isactive='Y' and pro_type = 4")
    OcBOrderItem queryOrderItemProTypeById(@Param("id") Long id, @Param("orderId") Long ocBOrderId);

    /**
     * 全渠道订单明细表
     *
     * @date 2019/3/11
     * @author: ming.fz AND wangqiang
     */
    //************整合map  begin

    /**
     * 明细的删除
     *
     * @param id      明细的id
     * @param orderId 订单的id
     * @return
     */
    @Delete("delete from oc_b_order_item where id=#{id} and oc_b_order_id=#{orderId}")
    Integer deleteByItemId(@Param("id") Long id, @Param("orderId") Long orderId);

    /**
     * 明细的删除
     *
     * @param itemIds 明细的id
     * @param orderId 订单的id
     * @return
     */
    @Delete("delete from oc_b_order_item where id in (${itemIds}) and oc_b_order_id=#{orderId}")
    Integer deleteByItemIds(@Param("itemIds") String itemIds, @Param("orderId") Long orderId);

    /**
     * 删除订单下所有明细
     *
     * @param orderId 订单的id
     * @return
     */
    @Delete("delete from oc_b_order_item where oc_b_order_id=#{orderId}")
    Integer deleteItemByorderId(@Param("orderId") Long orderId);

    /**
     * 订单对于明细商品数量
     *
     * @param id
     * @return
     */
    @Select("select count(1) from oc_b_order_item where oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4")
    int selectCountForOrder(@Param("orderId") Long id);

    /**
     * 查询订单明细中 吊牌价格
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT price_list FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4")
    BigDecimal getpriceListBySku(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    /**
     * 查询订单明细中 优惠金额
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT amt_discount FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4 and is_gift = #{isGift} and qty = #{qty} limit 1")
    BigDecimal getamtDiscountBySku(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId, @Param("isGift") Integer isGift, @Param("qty") Integer qty);

    /**
     * 查询订单明细中 分销价格和数量
     *
     * @param pscSkueCode
     * @return
     */

    @Select("SELECT DISTRIBUTION_PRICE,QTY FROM OC_B_ORDER_ITEM WHERE PS_C_SKU_ECODE=#{pscSkueCode} and isactive = 'Y' and pro_type != 4")
    OcBOrderItem getDistributionAndPrice(String pscSkueCode);

    /**
     * 根据 ID 删除 明细信息
     *
     * @param id
     * @return
     */
    @Delete("DELETE FROM oc_b_order_item WHERE id=#{id}")
    Integer deleteOrderItemById(@Param("id") Long id);

    /**
     * 查询订单是否有多条明细
     * param orderId
     *
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order_item WHERE oc_b_order_id =#{orderId} and isactive = 'Y' and pro_type != 4")
    Integer checkOrderItemNum(@Param("orderId") Long orderId);

    /**
     * 查询平台单号
     *
     * @param orderId
     * @return
     */
    @Select("SELECT TID FROM oc_b_order_item WHERE oc_b_order_id =#{orderId} and isactive = 'Y' and pro_type != 4")
    List<String> getTids(Long orderId);

    /**
     * 查询非退款状态的平台单号
     *
     * @param orderId
     * @return
     */
    @Select("SELECT TID FROM oc_b_order_item WHERE oc_b_order_id =#{orderId} and refund_status != 6 and isactive = 'Y' and pro_type != 4")
    List<String> getTidsNoRefundstatus(Long orderId);

    /**
     * @param orderId
     * @return
     * <AUTHOR>
     * <p>
     * 查标准价price_list、数量qty、优惠金额amt_discount、调整金额adjust_amt、成交价格price、单行实际成交金额real_amt
     */
    @Select("SELECT PRICE_LIST,QTY,AMT_DISCOUNT,ADJUST_AMT,PRICE,REAL_AMT FROM OC_B_ORDER_ITEM WHERE OC_B_ORDER_ID=#{orderId} AND ISACTIVE = 'Y' and pro_type != 4")
    List<OcBOrderItem> selectSourcecodeByRecord(Long orderId);

    /**
     * 检查明细中条码是否存在
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4")
    Integer checkSkuNum(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    /**
     * 更换商品
     *
     * @param psCProId
     * @param psCProEcode
     * @param psCProEname
     * @param skuSpec
     * @param psCSkuId
     * @param psCSkuEcode
     * @param id
     * @param ocBOrderId
     * @return
     */
    @Update("UPDATE oc_b_order_item  SET ps_c_pro_id=#{psCProId},ps_c_pro_ecode=#{psCProEcode},ps_c_pro_ename=#{psCProEname},sku_spec=#{skuSpec},ps_c_sku_id=#{psCSkuId},ps_c_sku_ecode=#{psCSkuEcode} WHERE id=#{id} and oc_b_order_id=#{ocBOrderId}")
    int updateGoods(@Param("psCProId") long psCProId, @Param("psCProEcode") String psCProEcode, @Param("psCProEname") String psCProEname, @Param("skuSpec") String skuSpec, @Param("psCSkuId") long psCSkuId, @Param("psCSkuEcode") String psCSkuEcode, @Param("id") long id, @Param("ocBOrderId") long ocBOrderId);

    /**
     * 根据 pscSkueCode  查询数量
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT qty FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4")
    BigDecimal queryQtyBySku(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    @Select("SELECT * FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4")
    OcBOrderItem queryOrderItem(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    /**
     * 更新 条码 数量
     *
     * @param qtyBill
     * @param pscSkueCode
     * @return
     */
    @Update("UPDATE oc_b_order_item SET qty=#{qtyBill},modifiername=#{name},modifieddate=#{time} WHERE ps_c_sku_ecode=#{pscSkueCode} AND oc_b_order_id =#{orderId}")
    Integer updateQty(@Param("qtyBill") BigDecimal qtyBill,
                      @Param("name") String name,
                      @Param("time") Date time,
                      @Param("pscSkueCode") String pscSkueCode,
                      @Param("orderId") Long orderId);

    /**
     * @param qtyBill   数量
     * @param priceList 成交价
     * @param id        明细Id
     * @return
     */
    @Update("UPDATE oc_b_order_item SET qty=#{qtyBill},price_list=#{priceList} WHERE id=#{id}")
    Integer updateQtyNumById(@Param("qtyBill") BigDecimal qtyBill,
                             @Param("priceList") BigDecimal priceList,
                             @Param("id") Long id);

    /**
     * 查询 订单明细中 成交价格
     *
     * @param pscSkueCode
     * @return
     */

    @Select("SELECT price FROM oc_b_order_item WHERE ps_c_sku_ecode=#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4")
    BigDecimal queryPriceBySku(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    /**
     * 根据订单编号,查询订单详情
     *
     * @param isActive 是否启用
     * @param orderIds string
     * @return list<jsonObject>
     */
    @SelectProvider(type = OcBOrderItemFiMapper.SqlProvider.class, method = "orderItemsQueryByOrderIds")
    List<QueryOrderItemResult> orderItemsQueryByOrderIds(@Param("orderIds") String orderIds, @Param("isActive") String isActive);

    /**
     * 查询订单明细平摊金额之和[原订单Id+原skuId]
     *
     * @param orginId 订单主表Id
     * @param psSkuId 订单明细psSkuId
     * @return BigDecimal
     */
    @Select("SELECT SUM(qty) AS totalQty FROM oc_b_order_item WHERE oc_b_order_id=#{orginId} "
            + "and ps_c_sku_id=#{psSkuId} and isactive='Y' AND refund_status <> '6' and pro_type != 4")
    BigDecimal queryItemQtyAmout(@Param("orginId") Long orginId, @Param("psSkuId") Long psSkuId);

    /**
     * 查看原单相同的sku存在几条
     *
     * @param orginId 订单主表Id
     * @param psSkuId 订单明细psSkuId
     * @return Integer
     */
    @Select("SELECT count(*) AS total FROM oc_b_order_item WHERE oc_b_order_id=#{orginId} "
            + "and ps_c_sku_id=#{psSkuId} and isactive='Y' AND refund_status <> '6' and pro_type != 4")
    Integer queryItemBySkuIdCount(@Param("orginId") Long orginId, @Param("psSkuId") Long psSkuId);

    @SelectProvider(type = SqlProvider.class, method = "selectOcOrderItems")
    List<OcBOrderItem> selectOcOrderItems(@Param("ids") List<Long> ids, @Param("cols") String cols);

    @SelectProvider(type = SqlProvider.class, method = "selectItems4SyncThirdSys")
    List<OcBOrderItem> selectItems4SyncThirdSys(TaskParam taskParam);


    @Select("SELECT * FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type = 0 and is_gift = 0 ")
    List<OcBOrderItem> queryOrderItemProTypeIsGiftById(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    /**
     * sql
     */
    class SqlProvider {
        /**
         * @param orderIds string
         * @param isActive string
         * @return string
         */
        public String orderItemsQueryByOrderIds(@Param("orderIds") String orderIds, @Param("isActive") String isActive) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT  ps_c_pro_id proId,ps_c_sku_id skuId,qty,price,real_amt realAmt,standard_weight weight,");
            sb.append(" oc_b_order_id orderId ");
            sb.append("FROM oc_b_order_item WHERE oc_b_order_id IN (");
            sb.append(orderIds);
            sb.append(" ) AND isactive = '");
            sb.append(isActive);
            sb.append("';");
            return sb.toString();
        }

        public String selectOcOrderItems(@Param("ids") List<Long> ids, @Param("cols") String cols) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT ")
                    .append(StringUtils.isNotBlank(cols) ? cols : "*")
                    .append(" FROM ").append("OC_B_ORDER_ITEM").append(" WHERE OC_B_ORDER_ID")
                    .append(" IN (");
            for (int i = 0, l = ids.size(); i < l; i++) {
                if (i > 0) {
                    sb.append(",");
                }
                sb.append(ids.get(i));
            }
            sb.append(" ) AND ISACTIVE='Y' AND PRO_TYPE != 4 AND refund_status <> '6'");
            return sb.toString();
        }

        /**
         * 同步第三方
         *
         * @param taskParam
         * @return
         */
        public String selectItems4SyncThirdSys(TaskParam taskParam) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT *  FROM OC_B_ORDER_ITEM WHERE OC_B_ORDER_ID IN (")
                    .append(taskParam.getKeyStrings())
                    .append(" ) AND ISACTIVE='Y' AND PRO_TYPE != 4 AND refund_status <> '6'");
            return sb.toString();
        }

    }

    /**
     * 查询整单平摊金额
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT ORDER_SPLIT_AMT FROM OC_B_ORDER_ITEM WHERE PS_C_SKU_ECODE =#{pscSkueCode} AND  oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4 and is_gift = #{isGift} and qty = #{qty} limit 1")
    BigDecimal getOrderSplitAmt(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId, @Param("isGift") Integer isGift, @Param("qty") Integer qty);
    //************整合map  end

    /**
     * 查找条码
     *
     * @param orderId 订单Id
     * @return List<Long>
     */
    @Select("SELECT ps_c_sku_id FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y'")
    List<Long> queryOrderItemSkulList(Long orderId);

    /**
     * 通过ooid查询商品明细
     *
     * @param orderId
     * @param ooId
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} AND ooid = #{ooId} and isactive = 'Y' and pro_type != 4")
    List<OcBOrderItem> selectByOrderIdAndOOId(@Param("orderId") Long orderId, @Param("ooId") String ooId);

    /**
     * 根据主订单id，查询明细订单中发货状态【is_sendout = 1】的明细数量
     * 2019/5/14 胡林洋 新增【平台发货服务】用
     *
     * @param ocBOrderId
     * @return int
     */
    @Select("SELECT count(*) FROM oc_b_order_item WHERE oc_b_order_id=#{ocBOrderId} AND is_sendout = 1 AND refund_status != 6 AND is_gift = 0  and isactive = 'Y' and pro_type != 4")
    int selectOrderItemIsSendoutCount(@Param("ocBOrderId") long ocBOrderId);

    /**
     * 2019/5/14 胡林洋 新增【平台发货服务】用
     * 根据主订单id，查询明细订单中所有明细数量
     *
     * @param ocBOrderId 订单id 分库键
     * @return int
     */
    @Select("SELECT count(*) FROM oc_b_order_item WHERE oc_b_order_id=#{ocBOrderId} AND refund_status != 6 AND is_gift = 0 and isactive = 'Y' and pro_type != 4")
    int selectOrderItemAllNum(@Param("ocBOrderId") long ocBOrderId);

    /**
     * 2019/4/3 胡林洋 新增【平台发货服务】
     * 根据主订单id，单条更新明细订单的【is_sendout】发货状态
     *
     * @param ocBOrderId 订单id 分库键
     * @param status     订单状态
     * @param ooid       子订单
     * @return int
     */
    @Update("UPDATE oc_b_order_item  SET is_sendout = #{status} WHERE oc_b_order_id=#{ocBOrderId} AND  id = #{id} AND ooid = #{ooid}")
    int updateOrderItemStatusByOrderidAndOid(@Param("ocBOrderId") long ocBOrderId, @Param("status") int status, @Param("ooid") String ooid);

    /**
     * add at 20200601
     * 更新平台子订单状态
     *
     * @param ocBOrderId
     * @param status
     * @param ooid
     * @return
     */
    @Update("UPDATE oc_b_order_item  SET platform_status = #{status} WHERE oc_b_order_id=#{ocBOrderId} AND  id = #{id} AND ooid = #{ooid}")
    int updateOrderItemPlatFormStatusByOrderidAndOid(@Param("ocBOrderId") long ocBOrderId, @Param("status") String status, @Param("ooid") String ooid);


    @Update("UPDATE oc_b_order_item  SET pt_return_status = #{status} WHERE oc_b_order_id=#{ocBOrderId} and id = #{id}")
    int updateOrderItemPtReturnStatusByOrderId(@Param("ocBOrderId") long ocBOrderId, @Param("status") String status, @Param("id") Long id);


    @Update("UPDATE oc_b_order_item  SET pt_return_status = #{status} ,refund_status=#{refundStatus} WHERE oc_b_order_id=#{ocBOrderId} and id = #{id}")
    int updateOrderItemPtReturnStatusAndReturnStatusByOrderId(@Param("ocBOrderId") long ocBOrderId, @Param("status") String status, @Param("refundStatus") int refundStatus, @Param("id") Long id);


    /**
     * 2019/4/3 胡林洋 新增【平台发货服务】
     * 根据主订单id，批量更新明细订单的【is_sendout】发货状态
     *
     * @param ocBOrderId 订单id 分库键
     * @return int
     */
    @Update("UPDATE oc_b_order_item  SET outerrcount  = outerrcount + 1 WHERE oc_b_order_id=#{ocBOrderId} and pro_type != 4")
    int updateFailTimesByOrderid(@Param("ocBOrderId") long ocBOrderId);

    /**
     * 2019/4/3 胡林洋 新增【平台发货服务】
     * 根据主订单id，单条更新明细订单的【is_sendout】发货状态
     *
     * @param ocBOrderId 订单id 分库键
     * @param ooid       子订单id 分库键
     * @return int
     */
    @Update("UPDATE oc_b_order_item  SET outerrcount  = outerrcount + 1 WHERE oc_b_order_id=#{ocBOrderId} AND ooid = #{ooid}")
    int updateFailTimesByOrderidAndOid(@Param("ocBOrderId") long ocBOrderId, @Param("ooid") String ooid);

    @Update("UPDATE oc_b_order_item  SET gift_relation  =#{giftRelation} WHERE oc_b_order_id=#{ocBOrderId} AND ps_c_sku_ecode = #{skuEcode}")
    int updateGiftReleationByOrderId(@Param("giftRelation") String giftRelation, @Param("ocBOrderId") long ocBOrderId, @Param("skuEcode") String skuEcode);

    /**
     * 根据平台单号更新明细
     *
     * @param refundStatus 退货状态
     * @param tid          平台单号
     * @param ocBOrderId   外键
     * @return int
     */
    @Update("UPDATE oc_b_order_item SET refund_status=#{refundStatus} WHERE tid=#{tid} AND  oc_b_order_id=#{ocBOrderId} and pro_type != 4")
    int updateRefundStatusByTidAndOrderId(@Param("refundStatus") int refundStatus, @Param("tid") Long tid, @Param("ocBOrderId") Long ocBOrderId);

    /**
     * 根据平台单号更新明细
     *
     * @param refundStatus 退货状态
     * @param tid          平台单号
     * @param ocBOrderId   外键
     * @return int
     */
    @Update("UPDATE oc_b_order_item SET refund_status=#{refundStatus} WHERE tid=#{tid} AND  oc_b_order_id=#{ocBOrderId} and pro_type != 4")
    int updateRefundStatusByStringTidAndOrderId(@Param("refundStatus") int refundStatus, @Param("tid") String tid, @Param("ocBOrderId") Long ocBOrderId);

    /**
     * 根据tid和外键查询明细id
     *
     * @param tid        平台单号
     * @param ocBOrderId 主表id
     * @return 明细ids
     */
    @Select("SELECT id FROM oc_b_order_item WHERE tid=#{tid} AND  oc_b_order_id=#{ocBOrderId} and isactive = 'Y' and pro_type != 4")
    List<Long> queryIdByTidAndOrderId(@Param("tid") String tid, @Param("ocBOrderId") Long ocBOrderId);

    /**
     * 查看原订单下相同的sku分组的金额合计
     *
     * @param orderList 订单主表Id
     * @param ooidList  ooidList
     * @return Integer
     */
    @Select("<script>"
            + " SELECT ooid AS ooid,IFNULL(SUM(adjust_amt),0) AS adjustAmtAmount,IFNULL(SUM(amt_discount),0) amtDiscountAmount, "
            + " IFNULL(SUM(distribution_price),0) AS distributionPriceAmount,IFNULL(SUM(order_split_amt),0) as orderSplitAmount "
            + " from oc_b_order_item "
            + " where pro_type != 4 and oc_b_order_id in "
            + " <foreach collection='orderList' item='orderId' open='(' separator=',' close=')'> #{orderId} </foreach>"
            + " and ooid in "
            + " <foreach collection='ooidList' item='ooid' open='(' separator=',' close=')'> #{ooid} </foreach>"
            + " and isactive = 'Y' and is_gift = 0 and refund_status <![CDATA[<>]]> '6' GROUP BY ooid"
            + "</script>")
    List<QueryOrderItemGroupByResult> selectUnSuccessRefundGroupByItemList(@Param("orderList") List<Long> orderList, @Param("ooidList") List<String> ooidList);

    /**
     * 查询订单下不同的ooid集合
     *
     * @param orderId 订单主表Id
     * @return
     */
    @Select("SELECT ooid  FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} "
            + " and isactive='Y' AND refund_status <> '6' and is_gift = 0 and pro_type != 4 GROUP BY ooid")
    List<String> queryOoidList(@Param("orderId") Long orderId);

    /**
     * 查询原单下sku的明细总和
     *
     * @param orginId 原单Id
     * @param ooid    ooid
     * @return QueryOrderItemGroupByResult
     */
    @Select("SELECT ooid AS ooid,IFNULL(SUM(adjust_amt),0) AS adjustAmtAmount,IFNULL(SUM(amt_discount),0) amtDiscountAmount,IFNULL(SUM(distribution_price),0) AS distributionPriceAmount,IFNULL(SUM(order_split_amt),0) as orderSplitAmount "
            + " FROM oc_b_order_item  WHERE oc_b_order_id=#{orginId} and ooid=#{ooid} and is_gift = 0 and isactive='Y' AND refund_status <> '6' and pro_type != 4 GROUP BY ooid")
    QueryOrderItemGroupByResult queryOrderItemGroupByResult(@Param("orginId") Long orginId, @Param("ooid") String ooid);

    /**
     * 根据新的订单ID和明细skuid查询明细
     *
     * @param orderList 原单Id集合
     * @param ooid      ooid
     * @return queryOrderItemBySkuIdList
     */
    @Select("<script> SELECT * FROM oc_b_order_item WHERE pro_type != 4 and oc_b_order_id in "
            + " <foreach collection='orderList' item='orderId' open='(' separator=',' close=')'> #{orderId} </foreach>  "
            + " and isactive='Y' and is_gift = 0 AND refund_status <![CDATA[<>]]> '6' and ooid = #{ooid} </script>")
    List<OcBOrderItem> queryOrderItemBySkuIdList(@Param("orderList") List<Long> orderList, @Param("ooid") String ooid);
    //--------------------------- ming.fz 2019/7/12 bigin

    @Select("<script> SELECT * FROM oc_b_order_item WHERE pro_type != 4 and oc_b_order_id in "
            + " <foreach collection='orderList' item='orderId' open='(' separator=',' close=')'> #{orderId} </foreach>  "
            + " and isactive='Y' and is_gift = 1 AND refund_status <![CDATA[<>]]> '6' and ooid = #{ooid} </script>")
    List<OcBOrderItem> queryOrderItemBySkuIdListWithGift(@Param("orderList") List<Long> orderList, @Param("ooid") String ooid);

    /**
     * 更新订单信息
     *
     * @param jsonObject
     * @return
     * <AUTHOR>
     */
    @UpdateProvider(type = OcBOrderItemMapper.UpdateRecord.class, method = "updateSql")
    int updateRecord(JSONObject jsonObject);

    class UpdateRecord {
        public String updateSql(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("oc_b_order_item");
                    for (String key : map.keySet()) {
                        if (!("id".equals(key) || "oc_b_order_id".equals(key))) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("oc_b_order_id = #{oc_b_order_id} and id = #{id}");
                }
            }.toString();
        }
    }


    //--------------------------- ming.fz end

    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4")
    List<OcBOrderItem> selectOrderItemListByOrderId(long orderId);


    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE ooid = #{ooid} and pro_type != 4 and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectOrderItemListByBigin02(@Param("orderIds") Set<Long> orderIds, @Param("ooid") Long ooid);


    /**
     * 根据主表id查询明细行数量
     *
     * @param orderIds 主表id
     * @return size
     */
    @Select("<script> "
            + "SELECT COUNT(*) FROM oc_b_order_item WHERE  isactive = 'Y' and pro_type != 4 and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectOrderItemListOoid(@Param("ooid") String ooid, @Param("orderIds") List<Long> orderIds);


    @Update("<script>"
            + "update oc_b_order_item set refund_status = #{refundStatus} where oc_b_order_id = #{orderId} and refund_status != 6 and id "
            + "in <foreach item='item' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateOcBOrderItemById(@Param("orderId") Long orderId, @Param("itemIds") List<Long> itemIds, @Param("refundStatus") Integer refundStatus);

    Integer selectItemsCountByOrderIds(@Param("orderIds") List<Long> orderIds);


    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE  isactive = 'Y' and oc_b_order_id = #{orderId} and id "
            + "in <foreach item='item' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectOrderItemListsByIds(@Param("orderId") long orderId, @Param("itemIds") List<Long> itemIds);


    @Select("SELECT * FROM oc_b_order_item WHERE ps_c_sku_ecode=#{skuEcode} and oc_b_order_id=#{orderId} and isactive='Y' and pro_type = 4")
    OcBOrderItem queryOrderItemBySku(@Param("skuEcode") String skuEcode, @Param("orderId") Long ocBOrderId);


    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and giftbag_sku = #{giftbagSku} and isactive = 'Y' and pro_type = 2")
    List<OcBOrderItem> selectOrderItemByBagSku(@Param("orderId") long orderId, @Param("giftbagSku") String giftbagSku);


    /**
     * 根绝group_mark查找组合或者福袋商品的下挂明细
     *
     * @param orderId
     * @param groupMark
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and group_goods_mark = #{groupMark} " +
            " and isactive = 'Y' and pro_type in(1,2)")
    List<OcBOrderItem> selectOrderIteGroupMark(@Param("orderId") long orderId, @Param("groupMark") String groupMark);

    @Select("<script> " +
            "SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId}  AND isactive = 'Y'  AND group_goods_mark IN "
            + "<foreach item='item' index='index' collection='groupMarkList' open='(' separator=',' close=')'> #{item} </foreach>"
            + " </script>")
    List<OcBOrderItem> selectOrderItemByGroupMarks(@Param("orderId") long orderId, @Param("groupMarkList") List<String> groupMarkList);


    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and gift_relation = #{giftRelation} and isactive = 'Y'")
    List<OcBOrderItem> selectOrderItemAnchoredGift(@Param("orderId") Long orderId, String giftRelation);


    @Select("SELECT * FROM oc_b_order_item WHERE ooid=#{ooid} and isactive = 'Y' and oc_b_order_id = #{orderId} AND ifnull(is_gift,0) != 1")
    List<OcBOrderItem> selectOrderItemByOoid(@Param("orderId") Long orderId, @Param("ooid") String ooid);

    /**
     * 通过ooid查询不是赠品 且 平台未退款的明细
     * fixbug 提示订单不存在 添加 sql filter (or is null)
     *
     * @param orderId
     * @param ooid
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE" +
            " ooid=#{ooid} and isactive = 'Y' and " +
            "  oc_b_order_id = #{orderId} and " +
            "  ifnull(is_gift,0) != 1  and " +
            " ('SUCCESS' != PT_RETURN_STATUS or PT_RETURN_STATUS is null ) ")
    List<OcBOrderItem> selectOrderItemByOoidAndNoRefund(@Param("orderId") Long orderId, @Param("ooid") String ooid);

    /**
     * 通用退
     *
     * @param orderId
     * @param ooid
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE ooid=#{ooid} and isactive = 'Y' and oc_b_order_id = #{orderId}")
    List<OcBOrderItem> selectOrderItemByOoid4CommonRefund(@Param("orderId") Long orderId, @Param("ooid") String ooid);


    @Select("SELECT * FROM oc_b_order_item WHERE id=#{id} and oc_b_order_id=#{orderId} and isactive='Y'")
    OcBOrderItem queryOrderById(@Param("id") Long id, @Param("orderId") Long ocBOrderId);

    /**
     * 未退款明细,未拆分的组合商品
     *
     * @param id 订单Id
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{id} and refund_status != 6 and isactive='Y'  and pro_type != 2 and pro_type !=1")
    List<OcBOrderItem> selectUnSuccessRefundAndGroupOrderItem(@Param("id") Long id);


    /**
     * 未退款明细
     *
     * @param id 订单Id
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{id} and refund_status != 6 and isactive='Y' and pro_type !=4 ")
    List<OcBOrderItem> selectUnSuccessRefundItem(@Param("id") Long id);


    /**
     * 更新订单明细取消状态
     *
     * @param refundStatus 取消状态
     * @param ocBOrderId   外键
     * @return int
     */
    @Update("UPDATE oc_b_order_item SET refund_status=#{refundStatus} WHERE oc_b_order_id=#{ocBOrderId} AND isactive = 'Y'")
    int updateRefundStatusByOrderId(@Param("refundStatus") int refundStatus, @Param("ocBOrderId") Long ocBOrderId);

    /**
     * 更新组合商品的申请数量
     *
     * @param ocBOrderId
     * @param goodsMark
     * @return
     */
    @Update("UPDATE oc_b_order_item SET QTY_RETURN_APPLY= 0 WHERE oc_b_order_id=#{ocBOrderId} and group_goods_mark = #{goodsMark} and pro_type = 4 AND isactive = 'Y'")
    int updateApplyQtyByOrderId(@Param("ocBOrderId") Long ocBOrderId, @Param("goodsMark") String goodsMark);


    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{ocBOrderId} and group_goods_mark = #{goodsMark} and pro_type = 4 AND isactive = 'Y' limit 1")
    OcBOrderItem selectOcBOrderItemByGoodsMark(@Param("ocBOrderId") Long ocBOrderId, @Param("goodsMark") String goodsMark);

    /**
     * 通用退
     *
     * @param orderId
     * @return
     */
    @SelectProvider(type = OcBOrderItemMapper.RefundSql.class, method = "selectOrderItemByOoIds4CommonRefund")
    List<OcBOrderItem> selectOrderItemByOoIds4CommonRefund(@Param("orderId") Long orderId, @Param("tid") String tid, @Param("ooIds") Set<String> ooIds);

    /**
     * 查询赠品信息
     *
     * @param orderId
     * @return
     */
    @SelectProvider(type = OcBOrderItemMapper.RefundSql.class, method = "selectOrderItemsGiftList")
    List<OcBOrderItem> selectOrderItemsGiftList(@Param("orderId") long orderId, @Param("tid") String tid, @Param("ooIds") Set<String> ooIds);

    class RefundSql {
        public String selectOrderItemByOoIds4CommonRefund(@Param("orderId") Long orderId, @Param("tid") String tid, @Param("ooIds") Set<String> ooIds) {
            StringBuilder sql = new StringBuilder(" SELECT * FROM oc_b_order_item WHERE ");
            if (!ooIds.isEmpty()) {
                sql.append(" ooid in('");
                sql.append(StringUtils.join(ooIds, "','")).append("') and ");
            }
            if (StringUtils.isNotBlank(tid)) {
                sql.append(" tid = '").append(tid).append("' and ");
            }
            sql.append(" isactive = 'Y' and oc_b_order_id =");
            sql.append(orderId);
            return sql.toString();
        }

        public String selectOrderItemsGiftList(@Param("orderId") long orderId, @Param("tid") String tid, @Param("ooIds") Set<String> ooIds) {
            StringBuilder sql = new StringBuilder("SELECT * FROM oc_b_order_item WHERE oc_b_order_id =");
            sql.append(orderId);
            sql.append(" and is_gift = 1 and isactive='Y' ");
            sql.append(" and pro_type !=4 and refund_status != 6 ");
            if (StringUtils.isNotBlank(tid)) {
                sql.append(" and tid = '").append(tid).append("'");
            }
            if (!ooIds.isEmpty()) {
                sql.append(" and ( ooid is null or ooid  not in('");
                sql.append(StringUtils.join(ooIds, "','")).append("') )");
            }
            return sql.toString();
        }
    }

    /**
     * 京东换新单-》关联退单-》关联退单内的原单明细行
     *
     * @param tid
     * @param ooid
     * @return
     */
    @Select("SELECT *  FROM  oc_b_order_item  WHERE tid = #{tid} AND ISACTIVE = 'Y'  AND OOID = #{ooid}")
    List<OcBOrderItem> selectOrderJdReplacementOrderItem(@Param("tid") String tid, @Param("ooid") Long ooid);

    @Select("SELECT *  FROM  oc_b_order_item  WHERE tid = #{tid} AND ISACTIVE = 'Y'  AND OOID = #{ooid} and refund_status != 6 and pro_type !=4")
    List<OcBOrderItem> selectByOoid(@Param("tid") String tid, @Param("ooid") String ooid);

    /**
     * 平台发货-退单取消
     *
     * @param orderId
     * @return
     */
    @Select("SELECT `ID`,OC_B_ORDER_ID,QTY,QTY_RETURN_APPLY,PS_C_SKU_ID,PS_C_SKU_ECODE FROM OC_B_ORDER_ITEM WHERE "
            + "OC_B_ORDER_ID=#{orderId} AND ISACTIVE='Y'")
    List<OcBOrderItem> selectItemsQtyByOrderId(@Param("orderId") Long orderId);


    /**
     * 平台发货.释放可申请退数量
     *
     * @param qty
     * @param id
     * @param orderId
     * @return
     */
    @Update("UPDATE OC_B_ORDER_ITEM SET QTY_RETURN_APPLY=#{qty} WHERE OC_B_ORDER_ID=#{orderId} AND `ID`=#{id}")
    int updateItemQtyReturnApplyByOrderId(@Param("qty") BigDecimal qty, @Param("id") Long id, @Param("orderId") Long orderId);


    /**
     * 根据订单id查找明细
     *
     * @param ids
     * @return
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE oc_b_order_id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + " and refund_status != 6 and isactive='Y' and pro_type !=4 "
            + "</script>")
    List<OcBOrderItem> selectOrderItemsByIds(@Param("ids") List<Long> ids);


    /**
     * 更新明细的发货状态
     *
     * @param orderId
     * @param tid
     * @return
     */
    @Update("UPDATE OC_B_ORDER_ITEM SET IS_SENDOUT=1 WHERE OC_B_ORDER_ID=#{orderId} AND TID=#{tid}")
    int updateItemsWhenDeliverySuccess(@Param("orderId") Long orderId, @Param("tid") String tid);


    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and refund_status != 6 ")
    List<OcBOrderItem> selectOrderItemsNotRefundFroAppointSku(long orderId);

    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and refund_status != 6  and tid = #{tid}")
    List<OcBOrderItem> selectOrderItemsNotRefundFroAppointSku2(@Param("orderId") long orderId, @Param("tid") String tid);

    @Update("<script>"
            + "update oc_b_order_item set refund_status = 6,price = 0,order_split_amt = 0, amt_discount = 0,"
            + "adjust_amt = 0, reserve_decimal05 = real_amt, real_amt =0,price_actual =0 where oc_b_order_id = #{orderId} and id "
            + "in <foreach item='item' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateOrderItemAmt(@Param("orderId") Long orderId, @Param("itemIds") List<Long> itemIds);

    /**
     * 转单时更新明细发货状态
     *
     * @param orderId
     * @param id
     * @return
     */
    @Update("update oc_b_order_item set is_sendout=1 where oc_b_order_id=#{orderId} and id=#{id};")
    int updateItemSendStatus(@Param("orderId") Long orderId, @Param("id") Long id);

    @Update("UPDATE oc_b_order_item  SET gift_relation = #{giftRelation} WHERE oc_b_order_id = #{ocBOrderId} AND id = #{id}")
    int updateGiftReleationByOrderIdAndId(@Param("giftRelation") String giftRelation, @Param("ocBOrderId") Long orderId, @Param("id") Long id);

    @Update("UPDATE oc_b_order_item  SET gift_relation = #{giftRelation} WHERE oc_b_order_id = #{ocBOrderId} AND group_goods_mark = #{groupGoodsMark}")
    int updateGiftReleationByOrderIdAndGroupGoodsMark(@Param("giftRelation") String giftRelation, @Param("ocBOrderId") Long orderId, @Param("groupGoodsMark") String groupGoodsMark);

    @Select("SELECT oc_b_order_id FROM OC_B_ORDER_ITEM WHERE TID = #{tid}")
    List<Long> selectOcBOrderIdByTid(@Param("tid") String tid);

    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and tid = #{tid} and isactive='Y' and refund_status != 6")
    List<OcBOrderItem> selectOrderItemListByTid(@Param("orderId") long orderId, @Param("tid") String tid);


    @Select("SELECT * FROM oc_b_order_item WHERE ooid=#{ooid} and isactive = 'Y' and oc_b_order_id = #{orderId} and refund_status != 6")
    List<OcBOrderItem> selectOrderItemsByOoid(@Param("orderId") Long orderId, @Param("ooid") String ooid);


    @Select("SELECT m.* FROM oc_b_order o join  oc_b_order_item m on o.id = m.oc_b_order_id  WHERE o.SG_B_OUT_BILL_NO =#{outBillNo} and o.isactive='Y' and m.isactive='Y' and m.refund_status != 6 ")
    List<OcBOrderItem> selectOrderItemListByoutBillNo(@Param("outBillNo") String outBillNo);


    /**
     * 未退款明细
     *
     * @param ids ids
     * @param ooId ooId
     * @return
     */
    @Select("<script> SELECT * FROM oc_b_order_item WHERE oc_b_order_id  "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + "  and refund_status != 6 and isactive='Y'  and pro_type != 4  and OOID = #{ooId}  </script>")
    List<OcBOrderItem> selectUnSuccessRefundByIds(@Param("ids") Set<Long> ids,@Param("ooId") String ooId);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE is_enable_expiry = 1 and refund_status != '6' and isactive = 'Y'  and pro_type != 4 and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectOrderItemsByOrderIdsExpiryDate(@Param("orderIds") List<Long> orderIds);


    @Delete("<script> "
            + "DELETE FROM oc_b_order_item WHERE oc_b_order_id = #{orderId} and id "
            + "in <foreach item='item' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int deleteOcBOrderItemById(@Param("orderId") Long orderId, @Param("itemIds") List<Long> itemIds);

    /**
     * 未退款明细
     *
     * @param id 订单Id
     * @return List<OcBOrderItem>
     */
    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{id}  and ps_c_sku_ecode=#{skuCode} and refund_status != 6 and isactive='Y'  and pro_type != 4")
    List<OcBOrderItem> selectUnSuccessRefundBySku(@Param("id") Long id,@Param("skuCode") String skuCode);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE refund_status !='6' and isactive = 'Y' and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectOrderItemListOccupyByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 根据tid和明细id查询订单明细
     *
     * @param tid
     * @param id
     * @return
     */
    @Select("SELECT * FROM oc_b_order_item WHERE tid=#{tid} and id =#{id} and isactive='Y' ")
    OcBOrderItem selectItemByTid(@Param("tid") String tid, @Param("id") Long id);

    /**
     * 根据订单id查找明细
     *
     * @param ids
     * @return
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE oc_b_order_id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} "
            + "</foreach> </script>")
    List<OcBOrderItem> selectItemsByOrderIdWithOutStatus(@Param("ids") List<Long> ids);

    /**
     * 标记退款状态
     *
     * @param orderId
     * @param itemIds
     * @return
     */
    @Update("<script>"
            + "update oc_b_order_item set refund_status=#{refundStatus}, modifieddate=now() "
            + "where oc_b_order_id = #{orderId} and id "
            + "in <foreach item='item' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int updateRefundStatusByIds(@Param("orderId") Long orderId,
                                @Param("itemIds") List<Long> itemIds, @Param("refundStatus") Integer refundStatus);


    @Select("SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and ooid = #{ooid} and pro_type != 2 and gift_type = 2")
    List<OcBOrderItem> selectAllOrderItemListByOrderIdAndOid(@Param("orderId") long orderId, @Param("ooid") String ooid);

    /**
     * 更新订单明细实发数量字段值为数量字段的值
     *
     * @param orderIds
     * @return
     */
    @Update("<script>"
            + "UPDATE oc_b_order_item set real_out_num = qty, modifieddate = now() "
            + "where oc_b_order_id "
            + "in <foreach item='orderId' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{orderId} </foreach> "
            + "</script>")
    int updateRealNumSourceQtyByOrderIds(@Param("orderIds") List<Long> orderIds);

    /**
     * 更新订单明细实发数量字段值为数量字段的值
     *
     * @param itemIds
     * @return
     */
    @Update("<script>"
            + "UPDATE oc_b_order_item set real_out_num = qty, modifieddate = now() "
            + "where id "
            + "in <foreach item='itemId' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{itemId} </foreach> "
            + "</script>")
    int updateRealNumSourceQtyByItemIds(@Param("itemIds") List<Long> itemIds);

    /**
     * 根据订单明细id更新实发数量
     *
     * @param itemId
     * @param realNum
     * @return
     */
    @Update("update oc_b_order_item set real_out_num = #{realNum}, modifieddate = now() where id = #{itemId}")
    int updateRealNumByItemId(@Param("itemId") Long itemId, @Param("realNum") BigDecimal realNum);

    @Select("<script> SELECT * FROM oc_b_order_item WHERE oc_b_order_id=#{orderId} and isactive='Y' and ooid  in "
            + "<foreach collection='ooids' item='item' open='(' separator=',' close=')'> "
            + "#{item} "
            + "</foreach> "
            + "</script> ")
    List<OcBOrderItem> selectItemList(
            @Param("orderId") Long orderId, @Param("ooids") List<String> ooids);

    @Select("<script>"
            + "SELECT * FROM oc_b_order_item WHERE  oc_b_order_id = #{orderId} and id "
            + "in <foreach item='item' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderItem> selectByOrderAndItem(@Param("orderId") Long orderId, @Param("itemIds") List<Long> itemIds);


    @Update("<script>"
            + "update oc_b_order_item set labeling_requirements = null, modifieddate = now() where oc_b_order_id = #{orderId}"
            + " and id "
            + "in <foreach item='itemId' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{itemId} </foreach> "
            + "</script>")
    int clearLabelingRequirements(@Param("orderId") long orderId, @Param("itemIds") List<Long> itemIds);

    @Update("<script>"
            + "update oc_b_order_item set equal_exchange_mark = null, is_equal_exchange = null, equal_exchange_ratio = null, modifieddate = now() where oc_b_order_id = #{orderId}"
            + " and id "
            + "in <foreach item='itemId' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{itemId} </foreach> "
            + "</script>")
    int clearEqualExchangeMark(@Param("orderId") long orderId, @Param("itemIds") List<Long> itemIds);

    @Update("<script>"
            + "UPDATE oc_b_order_item set m_dim4_id = #{mDim4Id},m_dim6_id = #{mDim6Id}, modifieddate = now() "
            + "where id "
            + "in <foreach item='itemId' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{itemId} </foreach> "
            + "</script>")
    int updateDimByIds(@Param("itemIds") List<Long> itemIds, @Param("mDim4Id") Integer mDim4Id, @Param("mDim6Id") Integer mDim6Id);

    /**
     * 根据订单id查找明细，过滤退款取消和无子单号的订单明细
     *
     * @param ids
     * @return
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order_item WHERE oc_b_order_id "
            + "in <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach> "
            + " and refund_status != 6 and isactive='Y' and ooid is not null "
            + "</script>")
    List<OcBOrderItem> selectOrderItemsNoRefundAndOoidByIds(@Param("ids") List<Long> ids);
}