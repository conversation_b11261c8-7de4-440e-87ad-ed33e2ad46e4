package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TimeOrderVipStatusEnum;
import com.jackrain.nea.oc.oms.model.request.TimeOrderVoidSgSendRequest;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 2:26 下午
 * @description
 * @since version -1.0
 */

@Slf4j
public class ES4IpTimeOrderVip {

    /**
     * 根据唯品会时效订单状态查询时效订单信息
     *
     * @param request 查询信息
     * @param index
     * @param range   return JSONObject 查询结果
     */
    public static JSONObject findTimeOrderVipInfoByEs(TimeOrderVoidSgSendRequest request, Integer index,
                                                      Integer range) {

        JSONObject whereKeys = new JSONObject();
        if (StringUtils.isNotBlank(request.getOrderSn())) {
            whereKeys.put("ORDER_SN", request.getOrderSn());
        } else if (StringUtils.isNotBlank(request.getPickNo())) {
            whereKeys.put("PICK_NO", request.getPickNo());
        } else {
            whereKeys.put("BILL_NO", request.getOccupiedOrderSn());
        }
        List<Integer> statusList = new ArrayList<>();
        statusList.add(TimeOrderVipStatusEnum.OCCUPIED.getValue());
        statusList.add(TimeOrderVipStatusEnum.OUT_STOCK.getValue());
        whereKeys.put("STATUS", statusList);
        whereKeys.put("ISACTIVE", "Y");
        String indexName = OcElasticSearchIndexResources.IP_TIME_ORDER_VIP_INDEX_NAME;

        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "ID");
        orderKes.put("asc", false);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);
        JSONObject search = ElasticSearchUtil.search(indexName,
                OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                whereKeys, null, orderKeys, range, index, new String[]{"OCCUPIED_ORDER_SN"});

        return search;
    }


    /**
     * 根据唯品会时效订单状态从ES中查询超时的单据信息
     *
     * @param pageIndex                     页码
     * @param pageSize                      每页大小
     *                                      <p>
     *                                      ORDER_SN 唯品会订单号
     * @param timeOrderReleaseStockInterval
     * @return 单据编号列表
     */
    public static List<String> selectOvertimeFromEs(int pageIndex, int pageSize, int timeOrderReleaseStockInterval) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            Integer[] whereKey = {TimeOrderVipStatusEnum.OCCUPIED.getValue(),
                    TimeOrderVipStatusEnum.OUT_STOCK.getValue(),
                    TimeOrderVipStatusEnum.SEEKING_STORE_SUCCESS.getValue(),
                    TimeOrderVipStatusEnum.CREATED.getValue()};
            whereKeys.put("STATUS", Lists.newArrayList(whereKey));

            JSONObject filterKeys = new JSONObject();
            Long endTime = DateUtils.addHours(new Date(), timeOrderReleaseStockInterval).getTime();
            filterKeys.put("CREATIONDATE", "~" + endTime);

            String[] returnFieldNames = new String[]{"OCCUPIED_ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照升序进行查询
            orderKey.put("name", "MODIFIEDDATE");
            orderKeys.add(orderKey);

            String indexName = OcElasticSearchIndexResources.IP_TIME_ORDER_VIP_INDEX_NAME;
            JSONObject search = ElasticSearchUtil.search(indexName, OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                    whereKeys, filterKeys, orderKeys, pageSize, pageIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("OCCUPIED_ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("selectOvertimeFromEs.异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return orderNoList;
    }


    /**
     * 业务：唯品会时效订单转单补偿任务
     *
     * @param status    唯品会时效订单状态
     * @param isTrans   转换状态
     * @param pageIndex 起始页
     * @param pageSize  每页条数
     * @return List occupiedOrderSn
     */
    public static List<String> getOccupiedOrderSnByStatusAndIsTrans(Integer status, Integer isTrans,
                                                                    int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("STATUS", status);
        whereKeys.put("ISTRANS", isTrans);

        String[] returnFieldNames = new String[]{"OCCUPIED_ORDER_SN"};
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        // 按照倒序进行查询
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                whereKeys, null, orderKeys,
                pageSize, startIndex, returnFieldNames);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                String orderNo = jsonObject.getString("OCCUPIED_ORDER_SN");
                orderNoList.add(orderNo);
            }
        }
        return orderNoList;
    }

    /**
     * 业务：唯品会时效订单转单补偿任务 - 发送mq形式
     *
     * @param pageIndex           起始页
     * @param pageSize            每页条数
     * @param isTrans             转换状态
     * @param maxCompensationTime 最大补偿次数
     * @param status              状态
     * @return List occupiedOrderSn
     */
    public static List<String> zgetOccupiedOrderSnByStatusArrAndIsTrans(int pageIndex, int pageSize, int isTrans,
                                                                        int maxCompensationTime, int status) {
        List<String> orderNoList = new ArrayList<>();
        JSONObject whereKeys = new JSONObject();
        if (isTrans != -1) {
            whereKeys.put("ISTRANS", isTrans);
        }
        if (status != -1) {
            whereKeys.put("STATUS", status);
        }

        JSONObject filterKeys = new JSONObject();
        Long currentTimeMillis = System.currentTimeMillis();
        // 时间条件，只捞取小于当前时间的
        filterKeys.put("NEXT_COMPENSATION_DATE", "~" + currentTimeMillis);
        // 小于最大补偿次数
        filterKeys.put("COMPENSATION_TIME", "~" + maxCompensationTime);

        String[] returnFieldNames = new String[]{"OCCUPIED_ORDER_SN"};
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        // 按照倒序进行查询
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                whereKeys, filterKeys, orderKeys,
                pageSize, startIndex, returnFieldNames);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");

            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                String orderNo = jsonObject.getString("OCCUPIED_ORDER_SN");
                orderNoList.add(orderNo);
            }
        }
        return orderNoList;
    }

    /**
     * 业务：时效订单发货实体仓查询
     *
     * @param pageIndex 起始页
     * @param pageSize  每页现实条数
     * @param orderSn   发货实体仓
     * @return List occupiedOrderSn
     */
    public static List<String> getOccupiedOrderSnByOrderSn(int pageIndex, int pageSize, String orderSn) {
        List<String> orderNoList = new ArrayList<>();
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ORDER_SN", orderSn);

        String[] returnFieldNames = new String[]{"OCCUPIED_ORDER_SN"};
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        // 按照倒序进行查询
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                OcElasticSearchIndexResources.IP_B_TIME_ORDER_VIP_TYPE_NAME,
                whereKeys, null, orderKeys,
                pageSize, startIndex, returnFieldNames);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                String orderNo = jsonObject.getString("OCCUPIED_ORDER_SN");
                orderNoList.add(orderNo);
            }
        }
        return orderNoList;
    }
}
