package com.jackrain.nea.oc.oms.services.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.store.api.out.SgBStoWarehouseUpdateCmd;
import com.burgeon.r3.sg.store.model.request.out.SgBStoWarehouseUpdateRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.cp.constants.R3CommonResultConstants;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.IpBJitxDeliveryRecordMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBJitxDealerOrderTaskMapper;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.JitxDealerTaskTypeEnum;
import com.jackrain.nea.oc.oms.model.table.IpBJitxDeliveryRecord;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.task.OcBJitxDealerOrderTask;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2021/12/22 15:31
 * 定时任务处理service
 */
@Slf4j
@Component
public class AutoJitxYyUpdateBillsTaskService {

    @Autowired
    private OcBJitxDealerOrderTaskMapper dealerOrderTaskMapper;

    @Autowired
    private IpBJitxDeliveryRecordMapper jitxDeliveryRecordMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private CpRpcService cpRpcService;

    @Reference(group = "sg", version = "1.0")
    private SgBStoWarehouseUpdateCmd sgBStoWarehouseUpdateCmd;


    public void execute() {
        List<OcBJitxDealerOrderTask> orderTasks = dealerOrderTaskMapper.selectList(new LambdaQueryWrapper<OcBJitxDealerOrderTask>()
                .eq(OcBJitxDealerOrderTask::getType, JitxDealerTaskTypeEnum.YY_BILLS_UPDATE.getCode())
                .eq(OcBJitxDealerOrderTask::getState, JitxDealerTaskStatusEnum.NOT.getCode()));
        if (CollectionUtils.isNotEmpty(orderTasks)) {
            List<String> orderTids = orderTasks.stream().map(OcBJitxDealerOrderTask::getTid).distinct().collect(Collectors.toList());
            if (log.isDebugEnabled()) {
                log.debug(" AutoJitxYyUpdateBillsTaskService orderTasks {} ", JSONObject.toJSONString(orderTasks));
            }

            List<OcBOrder> ocBOrders = ocBOrderMapper.selectList(new LambdaQueryWrapper<OcBOrder>()
                    .select(OcBOrder::getId, OcBOrder::getTid, OcBOrder::getBillNo, OcBOrder::getCpCPhyWarehouseId)
                    .in(OcBOrder::getTid, orderTids)
                    .eq(OcBOrder::getIsactive, R3CommonResultConstants.VALUE_Y));

            // key 平台单号
            Map<String, OcBOrder> orderMap = ocBOrders.stream().collect(Collectors.toMap(OcBOrder::getTid, Function.identity(), (o1, o2) -> o1));

            List<IpBJitxDeliveryRecord> ipBJitxDeliveryRecords = jitxDeliveryRecordMapper.selectList(new LambdaQueryWrapper<IpBJitxDeliveryRecord>()
                    .in(IpBJitxDeliveryRecord::getTid, orderTids)
                    .eq(IpBJitxDeliveryRecord::getOccupyStatus, VipConstant.JITX_DELIVERY_RECORD_STATUS_OCCUPIED)
                    .eq(IpBJitxDeliveryRecord::getIsactive, R3CommonResultConstants.VALUE_Y));
            Map<String, IpBJitxDeliveryRecord> deliveryRecordMap = new HashMap<>();

            List<String> erpStoreCodes = new ArrayList<>();
            for (IpBJitxDeliveryRecord ipBJitxDeliveryRecord : ipBJitxDeliveryRecords) {
                String tid = ipBJitxDeliveryRecord.getTid();
                Date time = ipBJitxDeliveryRecord.getApplicationTime();
                if (time == null) {
                    continue;
                }
                String yyStore = ipBJitxDeliveryRecord.getYyStoreCode();
                String storeCode = ipBJitxDeliveryRecord.getStoreCode();
                if (deliveryRecordMap.containsKey(tid)) {
                    IpBJitxDeliveryRecord oldRecord = deliveryRecordMap.get(tid);
                    Date oldTime = oldRecord.getApplicationTime();
                    if (time.before(oldTime)) {
                        continue;
                    }
                    deliveryRecordMap.put(tid, ipBJitxDeliveryRecord);
                } else {
                    deliveryRecordMap.put(tid, ipBJitxDeliveryRecord);
                }
                if (!erpStoreCodes.contains(storeCode)) {
                    erpStoreCodes.add(storeCode);
                }
            }
            List<CpCStore> erpStores = cpRpcService.selectStoresByCodes(erpStoreCodes);
            Map<String, CpCStore> erpStoreMap = erpStores.stream().collect(Collectors.toMap(CpCStore::getEcode, Function.identity()));


            AutoJitxYyUpdateBillsTaskService service = ApplicationContextHandle.getBean(AutoJitxYyUpdateBillsTaskService.class);
            for (OcBJitxDealerOrderTask orderTask : orderTasks) {
                String tid = orderTask.getTid();
                try {
                    if (!orderMap.containsKey(tid)) {
                        throw new NDSException("未查询到零售发货单 tid" + tid);
                    }
                    if (!deliveryRecordMap.containsKey(tid)) {
                        throw new NDSException("未查询到jitx寻仓结果单 tid" + tid);
                    }
                    OcBOrder order = orderMap.get(tid);
                    Long orderId = order.getId();

                    IpBJitxDeliveryRecord deliveryRecord = deliveryRecordMap.get(tid);
                    String erpStoreCode = deliveryRecord.getStoreCode();
                    if (!erpStoreMap.containsKey(erpStoreCode)) {
                        throw new NDSException("未查询到erp店仓信息");
                    }
                    CpCStore erpStore = erpStoreMap.get(erpStoreCode);

                    service.updateBills(orderTask, erpStore, deliveryRecord, orderId);
                } catch (Exception e) {
                    log.error(" AutoJitxYyUpdateBillsTaskService execute exception_has_occured:{}",
                            Throwables.getStackTraceAsString(e));
                    //异常标记为失败
                    service.updateTaskType(orderTask, e.getMessage());
                }
            }
        }
    }

    //更新需要更新的单据信息
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public void updateBills(OcBJitxDealerOrderTask orderTask, CpCStore cpCStore, IpBJitxDeliveryRecord deliveryRecord, Long orderId) {
        String billNo = orderTask.getBillNo();
        if (log.isDebugEnabled()) {
            log.debug(" AutoJitxYyUpdateBillsTaskService updateBills  billNo {} deliveryRecord {}",
                    billNo, JSONObject.toJSONString(deliveryRecord));
        }
        //修改零售发货单实体仓信息 物流单号 面单内容
        OcBOrder order = new OcBOrder();
        order.setId(orderId);
        Long warehouseId = cpCStore.getCpCPhyWarehouseId();
        if (warehouseId == null) {
            throw new NDSException("逻辑仓未维护实体仓信息");
        }
        order.setCpCPhyWarehouseId(warehouseId);
        order.setCpCPhyWarehouseEcode(cpCStore.getCpCPhyWarehouseEcode());
        order.setCpCPhyWarehouseEname(cpCStore.getCpCPhyWarehouseEname());
        //换仓类型  换仓状态
        Integer transType = deliveryRecord.getTransType();
        Integer transStatus = deliveryRecord.getTransStatus();

        SgBStoWarehouseUpdateRequest request = new SgBStoWarehouseUpdateRequest();
        if (transType != null && transStatus != null && transType == 2 && transStatus == 2) {
            String logisticNumber = deliveryRecord.getLogisticNumber();
            String ewaybillContent = deliveryRecord.getEwaybillContent();

            order.setExpresscode(logisticNumber);

            request.setJitxVoucherContent(ewaybillContent);
            request.setLogisticNumber(logisticNumber);
        }
        ocBOrderMapper.updateById(order);

        //调用 sg更新单据信息服务
        request.setBillNo(billNo);
        request.setStoreCode(cpCStore.getEcode());
        ValueHolderV14 v14 = sgBStoWarehouseUpdateCmd.updateStoOutInfo(request);
        if (log.isDebugEnabled()) {
            log.debug(" AutoJitxYyUpdateBillsTaskService updateStoOutInfo v14 {}", JSON.toJSONString(v14));
        }
        if (!v14.isOK()) {
            throw new NDSException("调用库存更新单据信息失败");
        }
        //修改任务状态
        OcBJitxDealerOrderTask update = new OcBJitxDealerOrderTask();
        update.setId(orderTask.getId());
        update.setState(JitxDealerTaskStatusEnum.SUCCESS.getCode());
        update.setModifieddate(new Date());
        dealerOrderTaskMapper.updateById(update);
    }

    //修改当前任务状态
    @Transactional(rollbackFor = Throwable.class, propagation = Propagation.REQUIRES_NEW)
    public void updateTaskType(OcBJitxDealerOrderTask orderTask, String errorMsg) {
        Long id = orderTask.getId();
        OcBJitxDealerOrderTask update = new OcBJitxDealerOrderTask();
        update.setId(id);
        update.setState(JitxDealerTaskStatusEnum.FAIL.getCode());
        update.setMsg(errorMsg);
        update.setModifieddate(new Date());
        dealerOrderTaskMapper.updateById(update);
    }

}
