package com.jackrain.nea.oc.oms.services.audit.wait;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @Auther: 黄志优
 * @Date: 2020/12/1 16:30
 * @Description: 审核时间
 */
public enum OmsAuditTime {

    /**
     * 策略设置审核等待时间，并计算可操作时间
     */
    AUDIT_WAIT_TIME {
        @Override
        public long audit(OcBOrder order, Integer stTime) {
            if (order == null || !check(order.getPayTime(), stTime)) {
                return 0L;
            }
            return calculateWaitTime(order.getPayTime(), stTime);
        }
    },
    /**
     * 策略设置反审核等待时间，并计算可操作时间
     */
    ANTI_AUDIT_WAIT_TIME {
        @Override
        public long antiAudit(OcBOrder order, Integer stTime) {
            if (order == null
                    || StringUtils.isBlank(order.getReserveAuditTag())
                    || !check(order.getModifieddate(), stTime)) {
                return 0L;
            }

            return calculateWaitTime(order.getModifieddate(), stTime);
        }
    },
    /**
     * 策略设置hold单等待时间，并计算可操作时间
     */
    HOLD_WAIT_TIME {
        @Override
        public long hold(OcBOrder order, Integer stTime) {
            if (order == null || !check(order.getHoldReleaseTime(), stTime)) {
                return 0L;
            }

            return calculateWaitTime(order.getHoldReleaseTime(), stTime);
        }
    },CREATE_TIME {
        @Override
        public long createTime(OcBOrder order, Integer stTime) {
            if (order == null || !check(order.getCreationdate(), stTime)) {
                return 0L;
            }
            return calculateWaitTime(order.getCreationdate(), stTime);
        }
    },
    ;

    private static final long A_MINUTE = 60 * 1000L;


    public boolean check(Date orderTime, Integer stTime) {
        if (orderTime == null || stTime == null || stTime == 0) {
            return false;
        }
        return true;
    }

    public long audit(OcBOrder order, Integer stTime) {
        return 0L;
    }

    public long antiAudit(OcBOrder order, Integer stTime) {
        return 0L;
    }

    public long hold(OcBOrder order, Integer stTime) {
        return 0L;
    }

    public long createTime(OcBOrder order, Integer stTime) {
        return 0L;
    }

    public long calculateWaitTime(Date date, Integer waitTime) {

        if (!check(date, waitTime)) {
            return 0L;
        }

        return date.getTime() + (waitTime * A_MINUTE);
    }
}
