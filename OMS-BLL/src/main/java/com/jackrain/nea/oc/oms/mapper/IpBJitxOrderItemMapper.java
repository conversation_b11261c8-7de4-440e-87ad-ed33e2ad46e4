package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrderItemEx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface IpBJitxOrderItemMapper extends ExtentionMapper<IpBJitxOrderItem> {
    @Select("SELECT * FROM ip_b_jitx_order_item WHERE ip_b_jitx_order_id=#{orderId}")
    List<IpBJitxOrderItemEx> selectOrderItemList(@Param("orderId") long orderId);
}