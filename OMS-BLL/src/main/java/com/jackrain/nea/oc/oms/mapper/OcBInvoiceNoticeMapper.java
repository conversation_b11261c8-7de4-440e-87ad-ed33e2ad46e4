package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.result.OcBInvoiceNoticeResult;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNotice;
import com.jackrain.nea.web.utils.ArrayToSUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

import java.util.*;

@Mapper
public interface OcBInvoiceNoticeMapper extends ExtentionMapper<OcBInvoiceNotice> {

    class OcBInvoiceNoticeSelect {
        public String updateSql(Map map) {
            String sql = new SQL() {
                String tableName = (String) map.get("tableName");
                JSONObject setKeys = (JSONObject) map.get("setKeys");
                JSONObject whereKeys = (JSONObject) map.get("whereKeys");
                boolean isIn = whereKeys.getBoolean("is_in") != null;

                {
                    UPDATE(tableName);
                    for (String key : setKeys.keySet()) {
                        if ("id".equalsIgnoreCase(key)) {
                            continue;
                        }
                        SET(key + "= #{setKeys." + key + "}");
                    }
                    if (isIn) {
                        Set<String> keySet = whereKeys.keySet();
                        for (String key : keySet) {
                            if (!Objects.equals(key, "is_in")) {
                                JSONArray retailIds = whereKeys.getJSONArray(key);
                                WHERE(key + " in (" + ArrayToSUtil.join(retailIds.toArray(), ",") + ")");
                            }
                        }
                    } else {
                        for (String key : whereKeys.keySet()) {
                            if (whereKeys.getString(key) == null) {
                                continue;
                            }
                            WHERE(key + "= #{whereKeys." + key + "}");
                        }
                    }
                }
            }.toString();
            return sql;
        }

        public String getMapByIds(Map map) {
            String sql = new SQL() {
                JSONArray ids = (JSONArray) map.get("ids");
                String tableName = (String) map.get("tableName");

                {
                    SELECT("ID,BILL_NO,ESTATUS");
                    FROM(tableName);
                    WHERE("ID in (" + StringUtils.join(ids.toArray(), ',') + ")");
                }
            }.toString();
            return sql;
        }

        public String getInvoiceExportSql(@Param("ids") String ids) {
            StringBuilder sb = new StringBuilder();
            sb.append("        SELECT                                                                   \n");
            sb.append("                a.id,                                                            \n");
            sb.append("                a.bill_no,                                                       \n");
            sb.append("                a.source_code,                                                   \n");
            sb.append("                a.invoice_type,                                                  \n");
            sb.append("                a.cp_c_shop_title,                                               \n");
            sb.append("                a.cp_c_logistics_ename,                                          \n");
            sb.append("                a.logistics_no,                                                  \n");
            sb.append("                a.receive_name,                                                  \n");
            sb.append("                a.receiver_mobile,                                               \n");
            sb.append("                a.receiver_address,                                              \n");
            sb.append("                a.invoice_company,                                               \n");
            sb.append("                a.header_name,                                                   \n");
            sb.append("                a.taxpayer_no,                                                   \n");
            sb.append("                a.company_address,                                               \n");
            sb.append("                a.phone_no,                                                      \n");
            sb.append("                a.opening_bank,                                                  \n");
            sb.append("                a.opening_bank_account,                                          \n");
            sb.append("                a.creationdate,                                                  \n");
            sb.append("                a.seller_remark,                                                 \n");
            sb.append("                a.invoice_no,                                                    \n");
            sb.append("                a.tax_no,                                                        \n");
            sb.append("                a.invoice_time,                                                  \n");
            sb.append("                a.invoice_ename,                                                 \n");
            sb.append("                b.ps_c_pro_ename,                                                \n");
            sb.append("                floor(b.qty) as qty,                                             \n");
            sb.append("                cast(b.amt_taxable as decimal(18,2)) as amt_taxable,             \n");
            sb.append("                b.unit_name,                                                     \n");
            sb.append("                a.invoice_ename,                                                 \n");
            sb.append("                a.email,                                                         \n");
            sb.append("                a.invoice_remark,                                                \n");
            sb.append("                a.ownerename,                                                    \n");
            sb.append("                a.estatus                                                        \n");
            sb.append("        FROM                                                                     \n");
            sb.append("        oc_b_invoice_notice a                                                    \n");
            sb.append("        INNER JOIN oc_b_invoice_notice_item b ON a.id = b.oc_b_invoice_notice_id \n");
            if (StringUtils.isNotEmpty(ids)) {
                sb.append("        WHERE a.id IN (+" + ids + ")                                             \n");
            }
            return sb.toString();
        }
    }

    /**
     * 根据IDS查询ID、BILL_NO、ESTATUS
     *
     * @param ids       ID集合
     * @param tableName 表名
     * @return String
     */
    @SelectProvider(type = OcBInvoiceNoticeSelect.class,
            method = "getMapByIds")
    List<HashMap> getMapByIds(@Param("ids") JSONArray ids, @Param("tableName") String tableName);

    /**
     * 更新
     *
     * @param tableName 表名
     * @param setKeys   更新参数
     * @param whereKeys 条件参数
     */
    @UpdateProvider(type = OcBInvoiceNoticeSelect.class,
            method = "updateSql")
    int updateTable(@Param("tableName") String tableName, @Param("setKeys") JSONObject setKeys, @Param("whereKeys") JSONObject whereKeys);

    @SelectProvider(type = OcBInvoiceNoticeSelect.class, method = "getInvoiceExportSql")
    List<OcBInvoiceNoticeResult> getInvoiceExportSql(@Param("ids") String ids);
}