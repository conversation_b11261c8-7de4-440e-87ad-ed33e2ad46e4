package com.jackrain.nea.oc.oms.sap;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.api.OrderPodHandleCmd;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

/**
 * sap common
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SapOcOrderService {

    @Reference(version = "1.0", group = "oms-fi")
    private OrderPodHandleCmd orderPodHandleCmd;

    public ValueHolderV14 sapOcOrderSignFor(JSONObject params) {
        ValueHolderV14 result = new ValueHolderV14();
        log.info("sapOcOrderSignFor params:{}", params);
        JSONArray dataArray = params.getJSONArray("DATA");
        if (null != dataArray && dataArray.size() > 0) {
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject dataJo = dataArray.getJSONObject(i);
                try {
                    result = orderPodHandleCmd.podHandle(dataJo);
                } catch (Exception e) {
                    e.printStackTrace();
                    result.setMessage(e.getMessage());
                    result.setCode(ResultCode.FAIL);
                }
            }
        }
        return result;
    }


    public ValueHolderV14 sapOcOrder(JSONObject params) {
        ValueHolderV14 result = new ValueHolderV14();
        log.info("sapOcOrderSignFor params:{}", params);

        String ctrl = params.getJSONObject("CTRL").getString("FUNID");
        JSONArray dataArray = params.getJSONArray("DATA");
        if (null != dataArray && dataArray.size() > 0) {
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject dataJo = dataArray.getJSONObject(i);
                try {
                    // temp
                    if ("ORDER".equals(ctrl)) {
                        result = orderPodHandleCmd.cancelOrder(dataJo);
                    } else {
                        result = orderPodHandleCmd.cancelReturn(dataJo);
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    result.setMessage(e.getMessage());
                    result.setCode(ResultCode.FAIL);
                }
            }
        }
        if (result.getCode() == 0) {
            result.setMessage("SAP签收下发成功");
        }
        return result;
    }
}
