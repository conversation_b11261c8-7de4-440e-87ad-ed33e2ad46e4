package com.jackrain.nea.oc.oms.services.task;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBToWingDeliveryTaskMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBToWingDeliveryTask;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Date;
import java.util.List;

/**
 * description:定金预售调用wing发货接口
 * @Author:  liuwenjin
 * @Date 2021/9/26 4:37 下午
 */
@Component
public class OmsToWingDeliveryTaskService {

    @Autowired
    OcBToWingDeliveryTaskMapper ocBToWingDeliveryTaskMapper;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    /**
     * description:有就更新没有就删除
     * @Author:  liuwenjin
     * @Date 2021/9/26 5:08 下午
     */
    public void insterToWingDeliveryTask(OcBToWingDeliveryTask ocBToWingDeliveryTask){
        if (ocBToWingDeliveryTaskMapper.selectCount(new QueryWrapper<OcBToWingDeliveryTask>()
                .lambda().eq(OcBToWingDeliveryTask :: getTid,ocBToWingDeliveryTask.getTid()))>0){
            ocBToWingDeliveryTaskMapper.updateById(ocBToWingDeliveryTask);
        }else {
            ocBToWingDeliveryTaskMapper.insert(ocBToWingDeliveryTask);
        }
    }
    /**
     * description:插入wing调用发货中间表
     * @Author:  liuwenjin
     * @Date 2021/9/26 5:22 下午
     */
    public  void  createToBeConfirmedTask(String tid,String outBillNo){
        OcBToWingDeliveryTask toWingDeliveryTask = new OcBToWingDeliveryTask();
        toWingDeliveryTask.setId(sequenceUtil.buildOcBToWingDeliveryTaskId());
        toWingDeliveryTask.setTid(tid);
        toWingDeliveryTask.setCreationdate(new Date());
        toWingDeliveryTask.setStatus(0);
        toWingDeliveryTask.setOutBillNo(outBillNo);
        //多次进来如果是已经传成功了，就不插入
        if (ocBToWingDeliveryTaskMapper.selectCount(new QueryWrapper<OcBToWingDeliveryTask>()
                .lambda().eq(OcBToWingDeliveryTask :: getTid,tid)
                .eq(OcBToWingDeliveryTask :: getStatus,4))>0){
            return;
        }
        insterToWingDeliveryTask(toWingDeliveryTask);
    }
    /**
     * description: 批量修改时间
     * @Author:  liuwenjin
     * @Date 2021/9/28 10:44 下午
     */
    public void updateToBeConfirmedTaskDate(List<String> tids){
        ocBToWingDeliveryTaskMapper.batchUpdateDate(tids);
    }

    /**
     * description: 批量修改状态
     * @Author:  liuwenjin
     * @Date 2021/9/28 10:44 下午
     */
    public void updateToBeConfirmedTaskStatus(List<String> tids){
        ocBToWingDeliveryTaskMapper.batchUpdateStatus(tids);
    }
}
