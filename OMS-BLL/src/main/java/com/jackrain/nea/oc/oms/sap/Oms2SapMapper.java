package com.jackrain.nea.oc.oms.sap;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.relation.TaskParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/3/23
 */
@Mapper
public interface Oms2SapMapper<T> {

    /**
     * search db node
     *
     * @return
     */
    @Select("SHOW NODE")
    List<HashMap> selectNodeList();


    /***
     * task search dbpartition key
     * @param node
     * @param name table name
     * @param num size
     * @param status status
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "selectDynamicTaskOrder")
    List<Long> selectDynamicTaskOrder(@Param("node") String node, @Param("name") String name, @Param("num") int num,
                                      @Param("status") int status);

    /**
     * update task status
     *
     * @param taskParam TaskSql
     * @return int
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateTaskOrder")
    int updateTaskOrder(TaskParam taskParam);

    /**
     * update task status
     *
     * @param name   table name
     * @param status
     * @param ids
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateDynamicTaskOrder")
    int updateDynamicTaskOrder(@Param("name") String name, @Param("status") int status, @Param("ids") List<Long> ids);

    /**
     * update task wos_status
     *
     * @param name   table name
     * @param status
     * @param ids
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateWosOrderTaskOrder")
    int updateWosOrderTaskOrder(@Param("name") String name, @Param("status") int status, @Param("ids") List<Long> ids);

    /**
     * update task status
     * String keys
     *
     * @param name   table name
     * @param status
     * @param ids
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateDynamicTaskOrderByString")
    int updateDynamicTaskOrderByString(@Param("name") String name, @Param("status") int status, @Param("ids") String ids);

    /**
     * task
     * 补偿任务
     *
     * @param param
     * @return
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateTaskOrderByCompensate")
    int updateTaskOrderByCompensate(TaskParam param);

    /**
     * insert task status
     *
     * @param name   table name
     * @param entity
     * @return
     */
    @InsertProvider(type = SqlProvider.class, method = "insertDynamicTaskOrder")
    int insertDynamicTaskOrder(@Param("name") String name, @Param("list") List<? extends Object> entity);


    /**
     * single
     *
     * @param name
     * @param shardKey
     * @param id
     * @return
     */
    @InsertProvider(type = SqlProvider.class, method = "insertSingleTaskOrder")
    int insertSingleTaskOrder(@Param("name") String name, @Param("shardKey") Long shardKey, @Param("id") Long id);


    /**
     * 多状态新增
     *
     * @param returnSyncTask
     * @return
     */
    @InsertProvider(type = SqlProvider.class, method = "insertTaskOrderWithMultiStatus")
    int insertTaskOrderWithMultiStatus(ReturnSyncTask returnSyncTask);


    /**
     * update origin order
     *
     * @param ids
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateDynamicOrigOrder")
    int updateDynamicOrigOrder(@Param("tableName") String tableName, @Param("fieldName") String fieldName,
                               @Param("status") int status, @Param("ids") List<Long> ids);

    /**
     * update origin order
     *
     * @param ids
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateDynamicOrigOrderByString")
    int updateDynamicOrigOrderByString(@Param("tableName") String tableName, @Param("fieldName") String fieldName,
                                       @Param("status") int status, @Param("ids") String ids);


    /**
     * 更新原单
     * 补偿任务
     *
     * @param taskParam
     * @return
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateOrigOrderByCompensate")
    int updateOrigOrderByCompensate(TaskParam taskParam);


    @SelectProvider(type = SqlProvider.class, method = "selectDynamicOrigOrder")
    List<T> selectDynamicOrigOrder(@Param("tableName") String tableName, @Param("dbpField") String dbpField,
                                   @Param("ids") List<Long> ids, @Param("cols") String cols);

    /**
     * 传sap补偿任务. 查询原单
     *
     * @param
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "selectCompensateDynamicOrigOrder")
    List<Long> selectCompensateDynamicOrigOrder(@Param("tableName") String tableName, @Param("ids") String ids,
                                                @Param("sapStatusField") String sapStatusField,
                                                @Param("sendTimeField") String sendTimeField);

    /**
     * 传第三方补偿任务. 查询原单
     *
     * @param param
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "selectCompensateOrigOrder")
    List<Long> selectCompensateOrigOrder(TaskParam param);

    /**
     * 传sap回执.更新原单
     *
     * @param ids
     */
    @UpdateProvider(type = SqlProvider.class, method = "updateDynamicOrigOrderCallBack")
    int updateDynamicOrigOrderCallBack(@Param("tableName") String tableName, @Param("fieldName") String fieldName,
                                       @Param("status") int status, @Param("ids") List<Long> ids,
                                       @Param("sendTimeField") String sendTimeField);


    /**
     * 查询传SAP中间表的  to_ac_status(传结算状态) or status(传SAP状态)
     *
     * @param tableName
     * @param tids
     * @param statusField
     * @param statusList
     * @return
     */
    @SelectProvider(type = SqlProvider.class, method = "selectToSapStatus")
    List<String> selectToSapStatus(@Param("tableName") String tableName,
                                   @Param("tids") Collection<Long> tids,
                                   @Param("statusField") String statusField,
                                   @Param("statusList") Collection<Long> statusList);


    class SqlProvider {


        /**
         * @param tableName
         * @param tids
         * @param statusField
         * @param statusList
         * @return
         */
        public String selectToSapStatus(@Param("tableName") String tableName,
                                        @Param("tids") Collection<String> tids,
                                        @Param("statusField") String statusField,
                                        @Param("statusList") Collection<Long> statusList) {
            StringBuilder sql = new StringBuilder();
            sql.append(" select tid from ");
            sql.append(tableName);
            sql.append(" where tid");
            sql.append(" in ('");
            sql.append(StringUtils.join(tids, "','"));
            sql.append("')");
            sql.append(" and ");
            sql.append(statusField);
            sql.append(" in ('");
            sql.append(StringUtils.join(statusList, "','"));
            sql.append("')");
            return sql.toString();
        }

        /**
         * update task order status  by appoint column , value
         *
         * @param taskParam TaskOrder
         * @return sql
         */
        public String updateTaskOrder(TaskParam taskParam) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ")
                    .append(taskParam.getTaskTableName())
                    .append(" SET `")
                    .append(taskParam.getTaskStatusCol())
                    .append("`=")
                    .append(taskParam.getTaskStatusVal())
                    .append(", MODIFIEDDATE=NOW()")
                    .append(" WHERE ORDER_ID IN(");
            joinList2String(taskParam.getValidKeys(), sb);
            sb.append(" ) AND `").append(taskParam.getTaskTypeCol()).append("`=").append(taskParam.getTaskTypeVal());
            return sb.toString();
        }

        /**
         * search task order
         *
         * @param node
         * @param name
         * @param num
         * @param status
         * @return
         */
        public String selectDynamicTaskOrder(@Param("node") String node, @Param("name") String name,
                                             @Param("num") int num, @Param("status") int status) {

            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=");
            sb.append(node);
            sb.append("*/ ");
            sb.append("SELECT order_id FROM ");
            sb.append(name);
            sb.append(" WHERE `STATUS`=");
            sb.append(status);
            sb.append(" LIMIT ");
            sb.append(num);
            return sb.toString();
        }

        /**
         * update task order status
         *
         * @param name
         * @param status
         * @param ids
         * @return
         */
        public String updateDynamicTaskOrder(@Param("name") String name, @Param("status") int status, @Param("ids") List<Long> ids) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ");
            sb.append(name);
            sb.append(" SET `STATUS`=");
            sb.append(status);
            sb.append(", MODIFIEDDATE=NOW()");
            sb.append(" WHERE ORDER_ID IN(");
            joinList2String(ids, sb);
            sb.append(" );");
            return sb.toString();
        }

        /**
         * update task order wos_status
         *
         * @param name
         * @param status
         * @param ids
         * @return
         */
        public String updateWosOrderTaskOrder(@Param("name") String name, @Param("status") int status, @Param("ids") List<Long> ids) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ");
            sb.append(name);
            sb.append(" SET `WOS_STATUS`=");
            sb.append(status);
            sb.append(" WHERE ORDER_ID IN(");
            joinList2String(ids, sb);
            sb.append(" );");
            return sb.toString();
        }

        /**
         * update task order status
         *
         * @param name
         * @param status
         * @param ids
         * @return
         */
        public String updateDynamicTaskOrderByString(@Param("name") String name, @Param("status") int status,
                                                     @Param("ids") String ids) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ").append(name)
                    .append(" SET `STATUS`=")
                    .append(status)
                    .append(", MODIFIEDDATE=NOW()")
                    .append(" WHERE ORDER_ID IN(")
                    .append(ids)
                    .append(" );");
            return sb.toString();
        }

        /**
         * task
         * 补偿任务
         * update task order status
         *
         * @return
         */
        public String updateTaskOrderByCompensate(TaskParam param) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ").append(param.getTaskTableName())
                    .append(" SET `").append(param.getTaskStatusCol()).append("`=")
                    .append(param.getTaskStatusVal())
                    .append(", MODIFIEDDATE=NOW()  WHERE ORDER_ID IN(")
                    .append(param.getKeyStrings())
                    .append(" ) AND `")
                    .append(param.getTaskTypeCol()).append("`=").append(param.getTaskTypeVal()).append(";");
            return sb.toString();
        }

        /**
         * update origin order
         *
         * @param tableName
         * @param status
         * @param ids
         * @return
         */
        public String updateDynamicOrigOrder(@Param("tableName") String tableName, @Param("fieldName") String fieldName,
                                             @Param("status") int status, @Param("ids") List<Long> ids) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ")
                    .append(tableName)
                    .append(" SET ")
                    .append(fieldName)
                    .append("=")
                    .append(status)
                    .append(", MODIFIEDDATE=NOW()")
                    .append(" WHERE ID IN(");
            joinList2String(ids, sb);
            sb.append(" );");
            return sb.toString();
        }

        /**
         * 传sap回执.更新原单
         *
         * @param tableName
         * @param status
         * @param ids
         * @return
         */
        public String updateDynamicOrigOrderCallBack(@Param("tableName") String tableName, @Param("fieldName") String fieldName,
                                                     @Param("status") int status, @Param("ids") List<Long> ids,
                                                     @Param("sendTimeField") String sendTimeField) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ")
                    .append(tableName)
                    .append(" SET ")
                    .append(fieldName)
                    .append("=")
                    .append(status)
                    .append(", ")
                    .append(sendTimeField)
                    .append("=IFNULL(").append(sendTimeField).append(",0) +1")
                    .append(", MODIFIEDDATE=NOW()")
                    .append(" WHERE ID IN(");
            joinList2String(ids, sb);
            sb.append(" ) AND ISACTIVE='Y';");
            return sb.toString();
        }

        /**
         * update origin order
         *
         * @param tableName
         * @param status
         * @param ids
         * @return
         */
        public String updateDynamicOrigOrderByString(@Param("tableName") String tableName,
                                                     @Param("fieldName") String fieldName, @Param("status") int status,
                                                     @Param("ids") String ids) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ")
                    .append(tableName)
                    .append(" SET ")
                    .append(fieldName)
                    .append("=")
                    .append(status)
                    .append(", MODIFIEDDATE=NOW()")
                    .append(" WHERE ID IN(")
                    .append(ids)
                    .append(" )")
                    .append(" AND ")
                    .append(fieldName)
                    .append("=3")
                    .append(" AND ISACTIVE='Y';");

            return sb.toString();
        }

        /**
         * 更新原单
         * 补偿任务
         *
         * @return
         */
        public String updateOrigOrderByCompensate(TaskParam taskParam) {
            StringBuilder sb = new StringBuilder();
            sb.append("UPDATE ")
                    .append(taskParam.getOrigTableName())
                    .append(" SET `")
                    .append(taskParam.getOrigStatusCol())
                    .append("`=")
                    .append(taskParam.getOrigStatusVal())
                    .append(", MODIFIEDDATE=NOW()")
                    .append(" WHERE ID IN(")
                    .append(taskParam.getKeyStrings())
                    .append(" )")
                    .append(" AND ")
                    .append(taskParam.getOrigStatusCol())
                    .append("=").append(taskParam.getOrigCompensateFailedVal())
                    .append(" AND ISACTIVE='Y';");

            return sb.toString();
        }

        /**
         * insert task order
         *
         * @param map
         * @return
         */
        public String insertDynamicTaskOrder(Map<Object, Object> map) {
            List<String> propertyList = new ArrayList<>();
            Map<String, String> lineColumnMap = new HashMap<>();
            String name = (String) map.get("name");
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ");
            sb.append(name);
            sb.append(" (");
            batchInsertHandler((List) map.get("list"), propertyList, lineColumnMap, "list", true, sb);
            return sb.toString();
        }

        /**
         * single insert
         *
         * @param name
         * @param shardKey
         * @param id
         * @return
         */
        public String insertSingleTaskOrder(@Param("name") String name, @Param("shardKey") Long shardKey, @Param("id") Long id) {
            StringBuilder sb = new StringBuilder();
            sb.append("INSERT INTO ").append(name)
                    .append(" (ID,ORDER_ID,`STATUS`,CREATIONDATE,MODIFIEDDATE) VALUES(")
                    .append(id).append(",").append(shardKey).append(",")
                    .append(0).append(",NOW(),NOW() );");
            return sb.toString();
        }

        /**
         * 多状态新增 insert
         *
         * @return
         */
        public String insertTaskOrderWithMultiStatus(ReturnSyncTask returnSyncTask) {
            StringBuilder preSb = new StringBuilder();
            preSb.append("INSERT INTO ")
                    .append(returnSyncTask.getTaskTableName())
                    .append(" (ID,ORDER_ID,`STATUS`,IS_NEXT_TAO,NEXT_TAO_STATUS")
                    .append(",CREATIONDATE,MODIFIEDDATE) VALUES(")
                    .append(returnSyncTask.getId()).append(",").append(returnSyncTask.getOrderId()).append(",")
                    .append(returnSyncTask.getStatus()).append(",").append(returnSyncTask.getIsNextTao()).append(",")
                    .append(returnSyncTask.getNextTaoStatus())
                    .append(",NOW(),NOW() );");
            return preSb.toString();
        }

        /**
         * batch insert handler
         *
         * @param var1
         * @param pls
         * @param lcm
         * @param key
         * @param same all element have same properties
         * @param sb
         * @return
         */
        private String batchInsertHandler(List var1, List<String> pls, Map<String, String> lcm, String key, boolean same,
                                          StringBuilder sb) {

            for (int i = 0, l = var1.size(); i < l; i++) {
                Map<String, Object> var2 = fun.apply(var1.get(i));
                convertHump2Lime(pls, lcm, var2);
                if (same) {
                    break;
                }
            }
            String var3 = unionColumns(pls, lcm);
            sb.append(var3);
            sb.append(") VALUES ");
            String var5;
            for (int i = 0, l = var1.size(); i < l; i++) {
                if (i > 0) {
                    sb.append(",");
                }
                Map<String, Object> var2 = fun.apply(var1.get(i));
                Set<String> var4 = var2.keySet();
                sb.append("(");
                for (int k = 0; k < pls.size(); k++) {
                    var5 = pls.get(k);
                    if (k > 0) {
                        sb.append(",");
                    }
                    if (var4.contains(var5)) {
                        sb.append(String.format("#{%s[%d].%s}", key, i, var5));
                    }
                }
                sb.append(")");
            }
            sb.append(";");
            return sb.toString();
        }

        public String selectDynamicOrigOrder(@Param("tableName") String tableName, @Param("dbpField") String dbpField,
                                             @Param("ids") List<Long> ids, @Param("cols") String cols) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT ")
                    .append(StringUtils.isNotBlank(cols) ? cols : "*")
                    .append(" FROM ").append(tableName).append(" WHERE ").append(dbpField)
                    .append(" IN (");
            joinList2String(ids, sb);
            sb.append(" ) AND ISACTIVE='Y'");
            return sb.toString();
        }


        public String selectCompensateDynamicOrigOrder(@Param("tableName") String tableName, @Param("ids") String ids,
                                                       @Param("sapStatusField") String sapStatusField,
                                                       @Param("sendTimeField") String sendTimeField) {

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT `ID`")
                    .append(" FROM ").append(tableName).append(" WHERE `ID`")
                    .append(" IN (")
                    .append(ids)
                    .append(" ) AND ")
                    .append(sapStatusField)
                    .append("=3")
                    .append(" AND ")
                    .append(sendTimeField)
                    .append("<6")
                    .append(" AND ISACTIVE='Y'");
            return sb.toString();
        }

        /**
         * 查询补偿订单
         * 同步第三方,补偿任务
         *
         * @param param
         * @return
         */
        public String selectCompensateOrigOrder(TaskParam param) {

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT `ID`")
                    .append(" FROM ").append(param.getOrigTableName()).append(" WHERE `ID`")
                    .append(" IN (")
                    .append(param.getKeyStrings())
                    .append(" ) AND ")
                    .append(param.getOrigStatusCol())
                    .append("=").append(param.getOrigCompensateFailedVal())
                    .append(" AND ")
                    .append(param.getOrigSendTimesCol())
                    .append("<6")
                    .append(" AND ISACTIVE='Y'");
            return sb.toString();
        }

        /**
         * union columns
         *
         * @param propertyList
         * @param lineColumnMap
         * @return
         */
        private String unionColumns(List<String> propertyList, Map<String, String> lineColumnMap) {
            StringBuilder colSb = new StringBuilder();
            for (int m = 0; m < propertyList.size(); m++) {
                if (m > 0) {
                    colSb.append(",");
                }
                colSb.append(lineColumnMap.get(propertyList.get(m)));
            }
            return colSb.toString();
        }

        /**
         * convert
         *
         * @param propertyList
         * @param lineColumnMap
         * @param var
         * @return
         */
        private String convertHump2Lime(List<String> propertyList, Map<String, String> lineColumnMap, Map var) {
            Set<String> singleJsnCols = var.keySet();
            StringBuilder sb = new StringBuilder();
            for (String key : singleJsnCols) {
                if (propertyList.contains(key)) {
                    continue;
                }
                String upKey = PropertyUtil.hump2Line(key).toUpperCase();
                sb.append(",");
                sb.append(upKey);
                propertyList.add(key);
                lineColumnMap.put(key, upKey);
            }
            if (sb.indexOf(",") > -1) {
                return sb.substring(1);
            }
            return null;
        }

        Function<Object, Map<String, Object>> fun = SqlProvider::obj2Jsn;

        static Map<String, Object> obj2Jsn(Object o) {
            if (o == null) {
                throw new NDSException("Insert Element Cannot Be Null");
            }
            if (o instanceof Map) {
                return (Map<String, Object>) o;
            } else {
                return JSON.parseObject(JSON.toJSONString(o));
            }
        }

    }

    static void joinList2String(List<Long> ids, StringBuilder sb) {
        for (int i = 0, l = ids.size(); i < l; i++) {
            if (i > 0) {
                sb.append(",");
            }
            sb.append(ids.get(i));
        }
    }
}
