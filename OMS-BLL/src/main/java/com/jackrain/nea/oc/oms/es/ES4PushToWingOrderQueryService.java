package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ToDRPStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ac.PayBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ac.ResponsiblePartyEnum;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * description：推送给wing杂费单接口相关单据ES查询
 *
 * <AUTHOR>
 * @date 2021/7/25
 */
@Slf4j
public class ES4PushToWingOrderQueryService {

    private ES4PushToWingOrderQueryService() {
    }

    /**
     * description：查询丢件单 产生退货单
     *
     * <AUTHOR>
     * @date 2021/7/25
     */
    public static List<Long> findAdjustPaymentAddReturn(int startIndex, int range) {
        List<Long> oriOrderIdList = new ArrayList<>();
        //
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        JSONArray typeArray = new JSONArray();
        typeArray.add(PayBillTypeEnum.PAY_BF.getVal());
        typeArray.add(PayBillTypeEnum.PAY_STORE.getVal());
        typeArray.add(PayBillTypeEnum.PAY_TK.getVal());
        whereKeys.put("BILL_TYPE", typeArray);
        //已财审
        whereKeys.put("BILL_STATUS", AcConstant.CON_BILL_STATUS_04);
//        whereKeys.put("RESPONSIBLE_PARTY","!=null");
        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次 平台发货补偿失败次数
        filterKeys.put("TO_DRP_COUNT", "~" + 5);

        JSONArray toDrpSatus = new JSONArray();
        toDrpSatus.add(ToDRPStatusEnum.NOT.getCode());
        toDrpSatus.add(ToDRPStatusEnum.FAIL.getCode());
        whereKeys.put("TO_DRP_STATUS", toDrpSatus);

        JSONObject search = ElasticSearchUtil.search(AcConstant.INDEX_AC_F_PAYABLE_ADJUSTMENT,
                AcConstant.INDEX_AC_F_PAYABLE_ADJUSTMENT, whereKeys, filterKeys,
                null, range, startIndex, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (Object returnDatum : returnData) {
                JSONObject jsonObject = (JSONObject) returnDatum;
                Long orderId = jsonObject.getLong("ID");
                oriOrderIdList.add(orderId);
            }
        }
        return oriOrderIdList;
    }

    /**
     * description：查询丢件单 产生杂费单
     *
     * <AUTHOR>
     * @date 2021/7/25
     */
    public static List<Long> findAdjustPaymentAddIncidentals(int startIndex, int range) {
        List<Long> oriOrderIdList = new ArrayList<>();
        //
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();
        //赔付类型
        JSONArray typeArray = new JSONArray();
        typeArray.add(PayBillTypeEnum.PAY_OTHER.getVal());

        //责任方
        JSONArray partyArray = new JSONArray();
        partyArray.add(ResponsiblePartyEnum.TP.getVal());
        partyArray.add(ResponsiblePartyEnum.EXPRESS_WAREHOU.getVal());
        partyArray.add(ResponsiblePartyEnum.CUSTOMER.getVal());
        partyArray.add(ResponsiblePartyEnum.SKECHERS.getVal());
        partyArray.add(ResponsiblePartyEnum.OTHER.getVal());
        whereKeys.put("BILL_TYPE", typeArray);
        whereKeys.put("RESPONSIBLE_PARTY",partyArray);
        //已财审
        whereKeys.put("BILL_STATUS", AcConstant.CON_BILL_STATUS_04);
        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次 平台发货补偿失败次数
        filterKeys.put("TO_DRP_COUNT", "~" + 5);

        JSONArray toDrpSatus = new JSONArray();
        toDrpSatus.add(ToDRPStatusEnum.NOT.getCode());
        toDrpSatus.add(ToDRPStatusEnum.FAIL.getCode());
        whereKeys.put("TO_DRP_STATUS", toDrpSatus);
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", true);
        orderKey.put("name", "MODIFIEDDATE");
        orderKeys.add(orderKey);
        JSONObject search = ElasticSearchUtil.search(AcConstant.INDEX_AC_F_PAYABLE_ADJUSTMENT,
                AcConstant.INDEX_AC_F_PAYABLE_ADJUSTMENT, whereKeys, filterKeys,
                orderKeys, range, startIndex, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (Object returnDatum : returnData) {
                JSONObject jsonObject = (JSONObject) returnDatum;
                Long orderId = jsonObject.getLong("ID");
                oriOrderIdList.add(orderId);
            }
        }
        return oriOrderIdList;
    }

    /**
     * description：查询发货后退款单
     *
     * <AUTHOR>
     * @date 2021/7/25
     */
    public static List<Long> findReturnAfSendOrder(int startIndex, int range) {
        List<Long> oriOrderIdList = new ArrayList<>();
        //
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();

        // 退款成功
        whereKeys.put("RETURN_STATUS", ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
        //仅退款
        whereKeys.put("BILL_TYPE", TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode());
        // 单据类型,自动,非手工单
        whereKeys.put("REFUND_ORDER_SOURCE_TYPE", RefundOrderSourceTypeEnum.AUTO.getValue());

        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次 平台发货补偿失败次数
        filterKeys.put("TO_DRP_COUNT", "~" + 5);
        //传DRP状态 失败或未传
        JSONArray toDrpSatus = new JSONArray();
        toDrpSatus.add(ToDRPStatusEnum.NOT.getCode());
        toDrpSatus.add(ToDRPStatusEnum.FAIL.getCode());
        whereKeys.put("TO_DRP_STATUS", toDrpSatus);
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", true);
        orderKey.put("name", "MODIFIEDDATE");
        orderKeys.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_AF_SEND_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_AF_SEND_TYPE_NAME, whereKeys, filterKeys,
                orderKeys, range, startIndex, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (Object returnDatum : returnData) {
                JSONObject jsonObject = (JSONObject) returnDatum;
                Long orderId = jsonObject.getLong("ID");
                oriOrderIdList.add(orderId);
            }
        }
        return oriOrderIdList;
    }

    /**
     * description：查询额外退款单
     *
     * <AUTHOR>
     * @date 2021/7/25
     */
    public static List<Long> findReturnAfSendManualOrder(int startIndex, int range) {
        List<Long> oriOrderIdList = new ArrayList<>();
        //
        String[] returnFields = {"ID"};
        JSONObject whereKeys = new JSONObject();

        // 退款成功
        whereKeys.put("RETURN_STATUS", ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
//        // 单据类型,自动,非手工单
        whereKeys.put("REFUND_ORDER_SOURCE_TYPE", RefundOrderSourceTypeEnum.MANUAL.getValue());

        JSONObject filterKeys = new JSONObject();
        //失败次数小于5次 平台发货补偿失败次数
        filterKeys.put("TO_DRP_COUNT", "~" + 5);
        //传DRP状态 失败或未传
        JSONArray toDrpSatus = new JSONArray();
        toDrpSatus.add(ToDRPStatusEnum.NOT.getCode());
        toDrpSatus.add(ToDRPStatusEnum.FAIL.getCode());
        whereKeys.put("TO_DRP_STATUS", toDrpSatus);
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        orderKey.put("asc", true);
        orderKey.put("name", "MODIFIEDDATE");
        orderKeys.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_RETURN_AF_SEND_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_RETURN_AF_SEND_TYPE_NAME, whereKeys, filterKeys,
                orderKeys, range, startIndex, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)) {
            for (Object returnDatum : returnData) {
                JSONObject jsonObject = (JSONObject) returnDatum;
                Long orderId = jsonObject.getLong("ID");
                oriOrderIdList.add(orderId);
            }
        }
        return oriOrderIdList;
    }
}
