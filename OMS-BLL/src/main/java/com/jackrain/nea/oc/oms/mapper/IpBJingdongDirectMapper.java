package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/28
 */
@Mapper
public interface IpBJingdongDirectMapper  extends ExtentionMapper<IpBJingdongDirect> {


    @Select("SELECT * FROM ip_b_jingdong_direct WHERE custom_order_id = #{orderNo}")
    IpBJingdongDirect selectIpBJingdongDirectByOrderNo(String orderNo);


    @Update("update ip_b_jingdong_direct set istrans = #{istrans}, trans_remark =#{transRemark},trans_count = IFNULL(trans_count, 0) + 1,transdate = now() WHERE custom_order_id = #{orderNo}")
    int updateIpBJingdongDirectIstrans(@Param("istrans") Integer istrans, @Param("transRemark") String transRemark, @Param("orderNo") String orderNo);
}
