package com.jackrain.nea.oc.oms.services.delivery;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryFailMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapperservice.OcBOrderDeliveryFailMapperService;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDeliveryFail;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.InreturningStatus;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.delivery.impl.DeliveryServicesFactory;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.third.enums.OmsBillTypeEnum;
import com.jackrain.nea.third.service.OmsToThirdService;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Description:平台发货处理类
 *
 * <AUTHOR> sunies
 * @since : 2020-11-03
 * create at : 2020-11-03 20:05
 */


@Slf4j
@Component
public class OrderDeliveryProcessor {

    @Value("${yk.order.shopid.value:}")
    private String ykShopIdListStr;
    @Autowired
    private OmsStCShopStrategyService omsStCShopStrategyService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OcBOrderDeliveryFailMapper deliveryFailMapper;
    @Autowired
    private OcBOrderDeliveryFailMapperService deliveryFailMapperService;

    /**
     * 订单明细信息，需要排除赠品和退款完成的明细
     *
     * @param ocBOrderRelation
     * @return
     */

    public boolean platformSend(OcBOrderRelation ocBOrderRelation) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OrderDeliveryProcessor.platformSend.OrderId{}",  ocBOrderRelation.getOrderId()));
        }
        OcBOrder order = ocBOrderRelation.getOrderInfo();
        // 更新修改时间
        OcBOrder updateModifyDate = new OcBOrder();
        updateModifyDate.setId(order.getId());
        updateModifyDate.setModifieddate(new Date());
        ocBOrderMapper.updateById(updateModifyDate);
        if (InreturningStatus.INRETURNING.equals(ocBOrderRelation.getOrderInfo().getIsInreturning()) && ocBOrderRelation.isAutomaticOperation()) {
            // 退款中 自动需要校验店铺策略的强制发货
            StCShopStrategyDO stCShopStrategyDO = omsStCShopStrategyService.selectOcStCShopStrategy(ocBOrderRelation.getOrderInfo().getCpCShopId());
            if (Integer.valueOf(0).equals(Optional.ofNullable(stCShopStrategyDO).map(StCShopStrategyDO::getIsForceSend).orElse(0))) {
                return false;
            }
        }
        User rootUser = SystemUserResource.getRootUser();
        //如果下单店铺的店铺渠道为：一件代发经销平台并且不是驿氪平台订单 则不调用平台发货
        boolean isCheckOrderIssuing = omsOrderService.checkOrderIssuing(ocBOrderRelation.getOrderId(),ocBOrderRelation.getOrderInfo().getCpCShopId());
        //驿氪平台订单，需要调用平台发货
        List<String> ykShopIdList = Arrays.asList(ykShopIdListStr.split(","));
        if(isCheckOrderIssuing && !ykShopIdList.contains(String.valueOf(ocBOrderRelation.getOrderInfo().getCpCShopId()))){
            //更新订单平台发货
            OcBOrder updateOrder = new OcBOrder();
            updateOrder.setId(ocBOrderRelation.getOrderInfo().getId());
            updateOrder.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            updateOrder.setPlatformDeliveryTime(new Date());
            omsOrderService.updateOrderInfo(updateOrder);
            omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), "店铺为一件代发经销平台，直接更新订单平台发货", null, null, rootUser);
            return true;
        }
        //各个入口参数不好统一，这里重查下
        List<OcBOrderItem> ocBOrderItems;
        // 淘宝不过滤赠品
        if (PlatFormEnum.TAOBAO.getCode().equals(ocBOrderRelation.getOrderInfo().getPlatform())) {
            ocBOrderItems = ocBOrderItemMapper.selectOrderItemList(ocBOrderRelation.getOrderId());
        } else {
            ocBOrderItems = ocBOrderItemMapper.selectItemListOfUnshippedAndNonGift(ocBOrderRelation.getOrderId());
        }

        ocBOrderRelation.setOrderItemList(ocBOrderItems);
        // 实际需要平台发货的订单
        Pair<Boolean, String> stringPair = DeliveryServicesFactory.invokeDelivery(ocBOrderRelation);
        boolean result = stringPair.getLeft();
        String errorMsg = stringPair.getRight();

        OmsToThirdService.addOmsToThird(ocBOrderRelation.getOrderInfo().getId(), OmsBillTypeEnum.RETAIL_INVOICE_ORDER, new String[]{"AC"}, rootUser);
        if (!result) {
            addOcBOrderDeliveryFail(order);
            //失败
            OcBOrder ocBOrder = new OcBOrder();
            ocBOrder.setId(ocBOrderRelation.getOrderInfo().getId());
            ocBOrder.setIsForce(0L);
            ocBOrder.setForceSendFailReason("平台发货失败，请检查接口是否正常，或当前订单是否满足发货条件;" + StringUtils.substring(errorMsg, 0, 150));
            ocBOrder.setMakeupFailNum(ocBOrderRelation.getOrderInfo().getMakeupFailNum() + 1);
            omsOrderService.updateOrderInfo(ocBOrder);
        }
        return result;
    }

    private OcBOrderDeliveryFail addOcBOrderDeliveryFail(OcBOrder ocBOrder) {
        try {
            List<OcBOrderDelivery> ocBOrderDeliveryList = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
            if (CollectionUtil.isEmpty(ocBOrderDeliveryList)) {
                return null;
            }
            // 校验之前是否已存在 如果已存在 则更新
            List<OcBOrderDeliveryFail> ocBOrderDeliveryFailList = deliveryFailMapper.selectOrderDeliveryFailByOrderId(ocBOrder.getId());
            if (CollectionUtil.isEmpty(ocBOrderDeliveryFailList)) {
                OcBOrderDelivery ocBOrderDelivery = ocBOrderDeliveryList.get(0);
                OcBOrderDeliveryFail deliveryFail = new OcBOrderDeliveryFail();
                deliveryFail.setId(sequenceUtil.buildDeliveryFailId());
                deliveryFail.setOcBOrderId(ocBOrder.getId());
                deliveryFail.setTid(ocBOrder.getTid());
                deliveryFail.setCpCShopId(ocBOrder.getCpCShopId());
                deliveryFail.setCpCShopEcode(ocBOrder.getCpCShopEcode());
                deliveryFail.setCpCShopTitle(ocBOrder.getCpCShopTitle());
                deliveryFail.setCpCLogisticsId(ocBOrderDelivery.getCpCLogisticsId());
                deliveryFail.setCpCLogisticsEcode(ocBOrderDelivery.getCpCLogisticsEcode());
                deliveryFail.setCpCLogisticsEname(ocBOrderDelivery.getCpCLogisticsEname());
                deliveryFail.setLogisticNumber(ocBOrderDelivery.getLogisticNumber());
                // 设置6小时之后
                deliveryFail.setNextTime(DateUtil.offsetHour(new Date(), 6));
                deliveryFail.setRetryNumber(0);
                deliveryFail.setPlatform(ocBOrder.getPlatform());
                deliveryFail.setStatus(0);
                BaseModelUtil.initialBaseModelSystemField(deliveryFail);
                deliveryFailMapper.insert(deliveryFail);
                return deliveryFail;
            }
            OcBOrderDeliveryFail ocBOrderDeliveryFail = ocBOrderDeliveryFailList.get(0);
            OcBOrderDeliveryFail updateOcBOrderDeliveryFail = new OcBOrderDeliveryFail();
            updateOcBOrderDeliveryFail.setModifieddate(new Date());
            updateOcBOrderDeliveryFail.setId(ocBOrderDeliveryFail.getId());
            updateOcBOrderDeliveryFail.setOcBOrderId(ocBOrderDeliveryFail.getOcBOrderId());
            updateOcBOrderDeliveryFail.setRetryNumber(ocBOrderDeliveryFail.getRetryNumber() + 1);
            updateOcBOrderDeliveryFail.setNextTime(DateUtil.offsetHour(new Date(), 6));
            updateOcBOrderDeliveryFail.setStatus(0);
            deliveryFailMapperService.updateById(updateOcBOrderDeliveryFail);
            return updateOcBOrderDeliveryFail;
        } catch (Exception e) {
            e.printStackTrace();
            log.error(LogUtil.format("保存重试订单信息失败:", Throwables.getStackTraceAsString(e)));
            return null;
        }
    }
}
