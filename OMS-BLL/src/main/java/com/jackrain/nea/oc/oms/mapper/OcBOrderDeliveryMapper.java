package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;


@Mapper
@Component
public interface OcBOrderDeliveryMapper extends ExtentionMapper<OcBOrderDelivery> {
    @Select("SELECT * FROM OC_B_ORDER_DELIVERY WHERE OC_B_ORDER_ID = #{orderId}  AND ISACTIVE = 'Y'")
    List<OcBOrderDelivery> selectOrderDeliveryByOrderId(@Param("orderId") Long orderId);


    /**
     * 获取发货信息列表
     *
     * @param orderId
     * @param skuIds
     * @return
     */
    @Select("<script> "
            + "SELECT * FROM OC_B_ORDER_DELIVERY WHERE OC_B_ORDER_ID =#{orderId} AND ISACTIVE = 'Y' and PS_C_SKU_ID in "
            + "<foreach item='item' index='index' collection='skuIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderDelivery> getExpressCodeFromOrderDelivery(@Param("orderId") Long orderId, @Param("skuIds") Collection<Long> skuIds);

    @Select("SELECT * FROM OC_B_ORDER_DELIVERY WHERE OC_B_ORDER_ID = #{orderId} " +
            "AND CP_C_LOGISTICS_ECODE = #{logisticsEcode} AND LOGISTIC_NUMBER = #{logisticNumber} AND PS_C_SKU_ECODE = #{skuCode} AND ISACTIVE = 'Y'")
    List<OcBOrderDelivery> selectOrderDeliveryByCondition(@Param("orderId") String orderId, @Param("logisticsEcode") String logisticsEcode,
                                                          @Param("logisticNumber") String logisticNumber, @Param("skuCode") String skuCode);

    /**
     * 根据物流单号，更新发货信息表中物流单号对应的平台发货的状态
     *
     * @param expressCode 物流单号
     * @return int
     */
    @Update("UPDATE OC_B_ORDER_DELIVERY SET RESERVE_BIGINT01 = #{status} where OC_B_ORDER_ID=#{orderId} and LOGISTIC_NUMBER = #{expressCode}")
    void updateSendGoodsStatusByExpressCode(@Param("orderId") Long orderId, @Param("expressCode") String expressCode, @Param("status") Long status);


    @Select("SELECT * FROM OC_B_ORDER_DELIVERY WHERE OC_B_ORDER_ID = #{orderId} AND ID = #{id} AND ISACTIVE = 'Y'")
    OcBOrderDelivery selectOrderDeliveryById(@Param("id") Long id, @Param("orderId") Long orderId);


    @Update("UPDATE OC_B_ORDER_DELIVERY SET cainiao_wh_status = #{whStatus} where OC_B_ORDER_ID = #{orderId} AND ID = #{id}")
    void updateSendGoodsStatusById(@Param("whStatus") String whStatus, @Param("id") Long id, @Param("orderId") Long orderId);


    @Select("<script> "
            + "SELECT * FROM OC_B_ORDER_DELIVERY WHERE  isactive = 'Y' and OC_B_ORDER_ID in "
            + "<foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderDelivery> selectOrderDeliveryByOrderIdList(@Param("orderIds") List<Long> orderIds);


}