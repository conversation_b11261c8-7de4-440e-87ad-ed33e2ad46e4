package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.services.OmsOrderCheckAndUpdateService;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.BaseModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: 周琳胜
 * @since: 2019/7/12
 * create at : 2019/7/12 9:47
 */
@Component
@Slf4j
public class TaobaoFxOrderTransferUtil {
    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private RegionNewService regionService;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private OmsOrderCheckAndUpdateService omsOrderCheckAndUpdateService;

    @Autowired
    private CpRpcService cpRpcService;

    /**
     * 淘宝分销中间表转换为全渠道订单表
     *
     * @param orderInfo
     * @param isHistoryOrder
     * @return
     */
    public OcBOrder convertTaobaoFxOrderToOmsOrder(IpTaobaoFxOrderRelation orderInfo,
                                                   boolean isHistoryOrder) {
        IpBTaobaoFxOrder taobaoFxOrder = orderInfo.getIpBTaobaoFxOrder();
        OcBOrder order = new OcBOrder();
        // 基础信息
        order.setId(sequenceUtil.buildOrderSequenceId());
        order.setModifierename(SystemUserResource.ROOT_USER_NAME);
        order.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        order.setVersion(0L);
        BaseModelUtil.initialBaseModelSystemField(order);
        //下单店铺标题。需要查个表获取Title（平台店铺信息表）
        //平台店铺标题
        CpShop shopInfo = null;
        if (taobaoFxOrder.getCpCShopId() != null) {
            shopInfo = cpRpcService.selectShopById(taobaoFxOrder.getCpCShopId());
        } else {
            throw new NDSException("平台店铺id为空!");
        }
        if (shopInfo != null) {
            order.setCpCShopTitle(shopInfo.getCpCShopTitle());
            //下单店仓编码. 到平台店铺信息表中获取下单店仓字段ID值；
//           order.setCpCStoreEcode(shopInfo.getCpCStoreEcode());
            //下单店仓名称. 到平台店铺信息表中获取下单店仓字段ID值；
//            order.setCpCStoreEname(shopInfo.getCpCStoreEname());
            //下单店仓id. 到平台店铺信息表中获取下单店仓字段ID值；
//            order.setCpCStoreId(shopInfo.getCpCStoreId());
            //下单店仓id. 到平台店铺信息表中获取下单卖家店铺名称
            // 店铺ecode保存
            order.setCpCShopEcode(shopInfo.getEcode());
            order.setCpCShopSellerNick(shopInfo.getSellerNick());
        } else {
            // 20190727修改：如果 平台店铺不存在，则不再继续保持。而是抛出异常，不允许转单操作
            throw new NDSException("平台店铺id=" + taobaoFxOrder.getCpCShopId() + "不存在");
        }
        //初始平台单号
        order.setTid(taobaoFxOrder.getFenxiaoId().toString());
        //单据编号
        order.setBillNo(sequenceUtil.buildBillNo());
        //平台单号
        order.setSourceCode(taobaoFxOrder.getFenxiaoId().toString());
        order.setMergeSourceCode(taobaoFxOrder.getFenxiaoId().toString());
        //下单店铺
        order.setCpCShopId(taobaoFxOrder.getCpCShopId());
        // 用户昵称
        order.setUserNick(taobaoFxOrder.getDistributorUsername());
        // 订单类型
        order.setOrderType(1);
        // 订单状态
        order = checkOrderStatus(taobaoFxOrder, order);
        //order.setOrderStatus(50);
        // 订单占单状态
        order.setOccupyStatus(0);
        // 订单旗帜
        Long orderFlag = taobaoFxOrder.getSupplierFlag() == null ? 0 : taobaoFxOrder.getSupplierFlag();
        order.setOrderFlag(orderFlag.toString());
//        if (isHistoryOrder) {
//            order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
//        } else {
//            order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
//        }
        //商品总额
        order.setProductAmt(buildTotalProductAmt(orderInfo.getTaobaoFxOrderItems()));
        //商品优惠金额  20191029修改口径 默认为0
        order.setProductDiscountAmt(BigDecimal.ZERO);
        //订单优惠金额
        order.setOrderDiscountAmt(new BigDecimal("0"));
        // 调整金额
        BigDecimal adjustAmt = buildItemAdjustAmt(orderInfo.getTaobaoFxOrderItems());
        order.setAdjustAmt(adjustAmt);
        // 配送费用
        order.setShipAmt(taobaoFxOrder.getPostFee());
        // 服务费
        order.setServiceAmt(new BigDecimal("0"));
        //订单总额 商品总额+配送费用+调整金额-商品优惠金额-订单优惠金额
        BigDecimal orderAmt = order.getProductAmt().add(taobaoFxOrder.getPostFee()).add(adjustAmt).subtract(order.getProductDiscountAmt()).subtract(order.getOrderDiscountAmt());
        order.setOrderAmt(orderAmt);
        // 已收金额
        order.setReceivedAmt(taobaoFxOrder.getDistributorPayment());
        // 代销结算金额
        order.setConsignAmt(new BigDecimal("0"));
        // 代销运费
        order.setConsignShipAmt(new BigDecimal("0"));
        // 应收金额
        order.setAmtReceive(taobaoFxOrder.getDistributorPayment());
        // 到付代收金额
        order.setCodAmt(new BigDecimal("0"));
        // 操作费
//        order.setOperateAmt(new BigDecimal("0"));
        // 应收平台金额
        order.setJdReceiveAmt(new BigDecimal("0"));
        // 京东结算金额
        order.setJdSettleAmt(new BigDecimal("0"));
        // 物流成本
        order.setLogisticsCost(new BigDecimal("0"));
        // 是否开票
        order.setIsInvoice(0);
        // 是否生成开票通知
        order.setIsGeninvoiceNotice(0);
        // 商品重量
        order.setWeight(new BigDecimal("0"));
        // 商品计算重量
        order.setIsCalcweight(0);
        // 下单时间
        order.setOrderDate(taobaoFxOrder.getCreated());
        // 交易结束时间
        order.setEndTime(taobaoFxOrder.getEndTime());
        // 付款时间
        order.setPayTime(taobaoFxOrder.getPayTime());
        // 收货人姓名
        order.setReceiverName(taobaoFxOrder.getName());
        // 收货人手机号码
        order.setReceiverMobile(taobaoFxOrder.getMobilePhone());
        // 收货人电话号码
        order.setReceiverPhone(taobaoFxOrder.getPhone());
        //省市区匹配
        ProvinceCityAreaInfo regionInfo = null;
        try {
            regionInfo = regionService.selectProvinceCityAreaInfo(taobaoFxOrder.getState(), taobaoFxOrder.getCity(), taobaoFxOrder.getDistrict());
            if (regionInfo.getProvinceInfo() != null) {
                order.setCpCRegionProvinceId(regionInfo.getProvinceInfo().getId());
                order.setCpCRegionProvinceEcode(regionInfo.getProvinceInfo().getCode());
                order.setCpCRegionProvinceEname(taobaoFxOrder.getState());
            }
            if (regionInfo.getCityInfo() != null) {
                order.setCpCRegionCityId(regionInfo.getCityInfo().getId());
                order.setCpCRegionCityEcode(regionInfo.getCityInfo().getCode());
                order.setCpCRegionCityEname(regionInfo.getCityInfo().getName());
            }
            if (regionInfo.getAreaInfo() != null) {
                order.setCpCRegionAreaId(regionInfo.getAreaInfo().getId());
                order.setCpCRegionAreaEcode(regionInfo.getAreaInfo().getCode());
                order.setCpCRegionAreaEname(regionInfo.getAreaInfo().getName());
            }
        } catch (Exception ex) {
            log.error("日志服务:调用省市区服务异常" + ex);
        }
        order.setReceiverAddress(taobaoFxOrder.getAddress().replaceAll(",", "::::"));
        // 邮编
        order.setReceiverZip(taobaoFxOrder.getZip());
        // 是否合并订单
        order.setIsMerge(0);
        // 是的拆分订单
        order.setIsSplit(0);
        // 是否已经拦截
        order.setIsInterecept(0);
        // 是否退款中
        order.setIsInreturning(0);
        // 支付方式
        order.setPayType(1);
        // 买家留言
        order.setBuyerMessage(taobaoFxOrder.getMemo());
        // 订单来源
        order.setOrderSource("3");
        // 是否有赠品
        order.setIsHasgift(0);
        // 商品数量 todo
        order.setQtyAll(buildTotalQtyAll(orderInfo.getTaobaoFxOrderItems()));
        order.setSkuKindQty(new BigDecimal(orderInfo.getTaobaoFxOrderItems().size()));
        // 卖家备注
        order.setSellerMemo(taobaoFxOrder.getSupplierMemo());
        // 平台
        order.setPlatform(3);
        // 是否生成调拨零售
//        order.setIsTodrp(0);
        // 是否已给物流
//        order.setIsGiveLogistic(0);
        // wms撤回状态
        order.setWmsCancelStatus(0);
        // 退货状态
        order.setReturnStatus(0);
        // 退单审核状态
        order.setRefundConfirmStatus(0);
        // 自动审核状态
        order.setAutoAuditStatus(0);
        // 京仓订单
        order.setIsJcorder(0);
        //实缺标记
//        order.setIsLackstock(0);
        // 11预售状态
        order.setDouble11PresaleStatus(0);
        // 是否虚拟订单
        order.setIsInvented(0);
        // 是否组合订单
        order.setIsCombination(0);
        // 系统预售状态
//        order.setSysPresaleStatus(0);
        // 是否催发货
        order.setIsOutUrgency(0);
        // 下单店铺是否代销
        //order.setIsShopCommission(0);
        // 卖家昵称
        order.setCpCShopSellerNick(taobaoFxOrder.getSupplierUsername());
        // 是否有工单
        order.setIsHasTicket(0);
        // 邮编 20190827
        order.setReceiverZip(taobaoFxOrder.getZip());
        // 隐私加密信息
        String oaid = taobaoFxOrder.getOaid();
        order.setOaid(oaid);
        if (StringUtils.isNotEmpty(oaid)) {
            order.setIsEncrypted(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
        }
        return order;
    }

    private OcBOrder checkOrderStatus(IpBTaobaoFxOrder taobaoFxOrder, OcBOrder order) {
        String currentStatus = taobaoFxOrder.getStatus();
        String logisticsId = taobaoFxOrder.getLogisticsId();
        if (TaoBaoOrderStatus.WAIT_SELLER_SEND_GOODS.equals(currentStatus)) {
            order.setOrderStatus(OmsOrderStatus.TO_BE_ASSIGNED.toInteger());
            return order;
        } else if (TaoBaoOrderStatus.TRADE_FINISHED.equals(currentStatus)
                || TaoBaoOrderStatus.WAIT_BUYER_CONFIRM_GOODS.equals(currentStatus)
                || TaoBaoOrderStatus.TRADE_CLOSED.equals(currentStatus)) {
            order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            order.setExpresscode(logisticsId);
            order.setCpCLogisticsEname(taobaoFxOrder.getLogisticsCompanyName());
            return order;
        }

        return order;
    }

    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            log.error("checkIsNeedTransferSkuUpperCase", ex);
            return true;
        }
    }


    /**
     * Sum
     * (淘宝分销订单明细中的商品价格
     * PRICE*数量
     *
     * @param list 淘宝分销订单明细
     * @return 商品总额
     */
    private BigDecimal buildTotalProductAmt(List<IpBTaobaoFxOrderItemExt> list) {
        BigDecimal total = BigDecimal.ZERO;
        if (list != null && list.size() != 0) {
            for (IpBTaobaoFxOrderItemExt item : list) {
                if (item.getPrice() == null) {
                    item.setPrice(BigDecimal.ZERO);
                }
                if (item.getNum() == null) {
                    item.setNum(0L);
                }
                total = total.add(item.getPrice().multiply(BigDecimal.valueOf(item.getNum())));
            }

        }
        return total;
    }

    /**
     * 订单明细的调整金额汇总sum  20191029修改口径  distributor_payment- total_fee
     *
     * @param list
     * @return 总调整金额
     */
    private BigDecimal buildItemAdjustAmt(List<IpBTaobaoFxOrderItemExt> list) {
        BigDecimal sumAdjustAmt = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(list)) {
            for (IpBTaobaoFxOrderItemExt item : list) {
                sumAdjustAmt = sumAdjustAmt.add(item.getDistributorPayment().subtract(item.getTotalFee()));
            }
        }
        return sumAdjustAmt;
    }

    /**
     * 订单明细中数量之和（排除退款完成的明细）
     *
     * @param list
     * @return 商品数量
     */
    private BigDecimal buildTotalQtyAll(List<IpBTaobaoFxOrderItemExt> list) {
        BigDecimal totalQtyAll = BigDecimal.ZERO;
        Long count = 0L;
        if (list != null && list.size() != 0) {
            for (IpBTaobaoFxOrderItemExt taobaoFxOrderItemExt : list) {
                Long num = taobaoFxOrderItemExt.getNum();
                count = count + num;
            }
        }

        totalQtyAll = new BigDecimal(count);
        return totalQtyAll;
    }

    /**
     * 淘宝分销中间表明细 转换为全渠道订单明细表
     *
     * @param taobaoFxOrderItemExts
     * @param ocBOrder
     * @return
     */
    public List<OcBOrderItem> convertTaobaoFxOrderItemToOmsOrderItem(List<IpBTaobaoFxOrderItemExt> taobaoFxOrderItemExts, OcBOrder ocBOrder) {
        if (taobaoFxOrderItemExts == null || taobaoFxOrderItemExts.size() == 0) {
            log.info("日志服务：淘宝分销中间表明细为Null");
            return new ArrayList<>();
        }
        List<OcBOrderItem> orderItems = new ArrayList<>();
        for (IpBTaobaoFxOrderItemExt taobaoFxOrderItemExt : taobaoFxOrderItemExts) {
            orderItems.add(covertTaoBaoFxItemToOmsOrderItem(taobaoFxOrderItemExt, ocBOrder));
        }
        return orderItems;
    }

    public OcBOrderItem covertTaoBaoFxItemToOmsOrderItem(IpBTaobaoFxOrderItemExt ipBTaobaoFxOrderItemExt, OcBOrder ocBOrder) {
        OcBOrderItem item = new OcBOrderItem();
        Long itemId = sequenceUtil.buildOrderItemSequenceId();
        item.setId(itemId);
        //设置分库键
        item.setOcBOrderId(ocBOrder.getId());
        item.setCreationdate(new Date());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setModifiername(SystemUserResource.ROOT_USER_NAME);
        item.setModifieddate(new Date());
        item.setModifierid(SystemUserResource.ROOT_USER_ID);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerid(SystemUserResource.ROOT_USER_ID);
        item.setOwnername(SystemUserResource.ROOT_USER_NAME);
        item.setAdClientId(SystemUserResource.AD_CLIENT_ID);
        item.setAdOrgId(SystemUserResource.AD_ORG_ID);
        item.setVersion(0L);

        // 转换数据

        // 实缺标记
        item.setIsLackstock(0);
        // 退货金额
        item.setAmtRefund(new BigDecimal("0"));
        // 组合名称 todo
        //ProSkuResult proSkuResult = getSkuInfo(ipBTaobaoFxOrderItemExt);

        // 商品数字编码
        item.setNumIid(ipBTaobaoFxOrderItemExt.getItemId() + "");
        item.setSkuNumiid(ipBTaobaoFxOrderItemExt.getSkuId() + "");
        // 是否赠品
        item.setIsGift(0);

        //item.setOrderSplitAmt();
        //国标码。SKU 69码。从条码档案中有一个69码字段
        if (ipBTaobaoFxOrderItemExt.getProdSku() != null) {
            item.setBarcode(ipBTaobaoFxOrderItemExt.getProdSku().getBarcode69());
        } else {
            item.setBarcode(null);
        }
        //订单表.tid
        item.setTid(ocBOrder.getSourceCode());
        // 标题
        item.setTitle(ipBTaobaoFxOrderItemExt.getTitle());
        // 规格
        if (null != ipBTaobaoFxOrderItemExt.getFenxiaoId()) {
            //liqb 更改ooid类型从Long类型改成String类型
            item.setOoid(ipBTaobaoFxOrderItemExt.getFenxiaoId().toString());
        }
        // 标准价
        item.setPriceList(ipBTaobaoFxOrderItemExt.getPrice());
        // 一头牛需求优化，转单金额调整 1004 韩菊花
        item.setPrice(ipBTaobaoFxOrderItemExt.getPrice());
        // 成交金额  20191029修改口径  distributor_payment/数量 保留4位小数
        BigDecimal priceActual = ipBTaobaoFxOrderItemExt.getNum() == 0 ? BigDecimal.ZERO : ipBTaobaoFxOrderItemExt.getDistributorPayment()
                .divide(BigDecimal.valueOf(ipBTaobaoFxOrderItemExt.getNum()), 4, BigDecimal.ROUND_HALF_UP);
        item.setPriceActual(priceActual);
        //调整金额  20191029调整口径 distributor_payment- total_fee
        item.setAdjustAmt(ipBTaobaoFxOrderItemExt.getDistributorPayment() == null ? BigDecimal.ZERO : ipBTaobaoFxOrderItemExt.getDistributorPayment()
                .subtract(ipBTaobaoFxOrderItemExt.getTotalFee() == null ? BigDecimal.ZERO : ipBTaobaoFxOrderItemExt.getTotalFee()));
        //单行实际成交金额 20191029修改口径 distributor_payment/数量 保留4位小数
        /*BigDecimal realAmt = ipBTaobaoFxOrderItemExt.getNum() == 0 ? BigDecimal.ZERO : ipBTaobaoFxOrderItemExt.getDistributorPayment()
                .divide(BigDecimal.valueOf(ipBTaobaoFxOrderItemExt.getNum()));*/
        BigDecimal realAmt = ipBTaobaoFxOrderItemExt.getNum() == 0 ? BigDecimal.ZERO : ipBTaobaoFxOrderItemExt.getDistributorPayment();
        item.setRealAmt(realAmt.setScale(4, RoundingMode.HALF_UP));

        //退款状态
        if (ipBTaobaoFxOrderItemExt.getStatus().equals(TaobaoFxOrderStatusEnum.TRADE_REFUNDING.name())) {
            item.setRefundStatus(1);
            // 订单的【是否退款中】更新为1 并进行拦截
            ocBOrder.setIsInreturning(1);
            ocBOrder.setIsInterecept(1);//是否已经拦截 使用HOLD单方法修改
        } else if (ipBTaobaoFxOrderItemExt.getStatus().equals(TaobaoFxOrderStatusEnum.TRADE_REFUNDED.name())) {
            item.setRefundStatus(6);
        } else {
            item.setRefundStatus(0);
        }
        // 2019.7.31修改退款状态赋值
        //if (ipBTaobaoFxOrderItemExt.getOrder200Status())
        // 是否已占用库存
        item.setIsAllocatestock(0);
        // 买家是否已评价
        item.setIsBuyerRate(0);
        // 订单编号
        item.setOcBOrderId(ocBOrder.getId());
        // 已退数量
        item.setQtyRefund(new BigDecimal("0"));
        // 数量
        item.setQty(new BigDecimal(ipBTaobaoFxOrderItemExt.getNum()));
        // 平台单号
        item.setTid(ocBOrder.getTid());
        // 预售状态
        item.setIsPresalesku(0);

        // 优惠金额  20191029修改口径 默认0
        item.setAmtDiscount(BigDecimal.ZERO);
        //isactive
        item.setIsactive("Y");
        //是否实缺
        item.setIsLackstock(0);
        //退货金额
        item.setAmtRefund(BigDecimal.ZERO);
        //是否发货
        item.setIsSendout(0);
        // 发货失败次数
        item.setOuterrcount(0);

        this.initialTaobaoFxOrderItem(ipBTaobaoFxOrderItemExt, item);
        //赋值平台商品名称
        item.setPtProName(StringUtils.defaultString(ipBTaobaoFxOrderItemExt.getTitle()));
        // 虚拟条码为null 20190815修改
        item.setGiftbagSku(null);
        //增加吊牌价和sex传值 20190827
        item.setSex(ipBTaobaoFxOrderItemExt.getProdSku().getSex());
        item.setPriceTag(ipBTaobaoFxOrderItemExt.getProdSku().getPricelist());
        return item;
    }


    /**
     * 初始化TaobaoOrderItem内容
     * 2019-07-30 组合福袋商品修改
     *
     * @param orderItemExt 淘宝中间表数据
     * @param item         需要赋值的taobaoorderItem
     */

    private void initialTaobaoFxOrderItem(IpBTaobaoFxOrderItemExt orderItemExt, OcBOrderItem item) {
        if (orderItemExt.getProdSku() != null) {
            item.setPsCProId(orderItemExt.getProdSku().getProdId());
            // ProECode
            item.setPsCProEcode(orderItemExt.getProdSku().getProdCode());
            item.setPsCSkuId(orderItemExt.getProdSku().getId());

            item.setPsCClrEcode(orderItemExt.getProdSku().getColorCode());
            item.setPsCClrEname(orderItemExt.getProdSku().getColorName());
            item.setPsCClrId(orderItemExt.getProdSku().getColorId());
            item.setPsCSizeEcode(orderItemExt.getProdSku().getSizeCode());
            item.setPsCSizeEname(orderItemExt.getProdSku().getSizeName());
            item.setPsCSizeId(orderItemExt.getProdSku().getSizeId());
            item.setPsCProMaterieltype(orderItemExt.getProdSku().getMaterialType());
            item.setStandardWeight(ProductUtils.getFxWeight(orderItemExt.getProdSku().getWeight()));
            item.setSkuSpec(orderItemExt.getProdSku().getSkuSpec());
            item.setProType(NumberUtils.toLong(orderItemExt.getProdSku().getSkuType() + ""));

            // 设置品类信息 上线优化：20220927 py
            item.setMDim4Id(orderItemExt.getProdSku().getMDim4Id());
            item.setMDim6Id(orderItemExt.getProdSku().getMDim6Id());
            String isEnableExpiry = orderItemExt.getProdSku().getIsEnableExpiry();
            if ("Y".equals(isEnableExpiry)) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }

            // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
            String psSkuEcode = orderItemExt.getSkuOuterId();
            if (StringUtils.isBlank(psSkuEcode)) {
                psSkuEcode = orderItemExt.getItemOuterId();
            }
            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            if (orderItemExt.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT
                    || orderItemExt.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT) {
                //为福袋或者组合商品
                // //20190815 组合商品类型赋值
                item.setProType(1L);
                item.setPsCSkuEcode(psSkuEcode);
                item.setPsCProEname(orderItemExt.getTitle()); //虚拟条码商品名称取中间表的名称
                //由于数据库做了对尺寸code和商品code做了非空限制
                item.setPsCSizeEcode(psSkuEcode);
                item.setPsCProEcode(psSkuEcode);
            } else {
                item.setPsCSkuEcode(psSkuEcode);
                item.setPsCProEname(orderItemExt.getProdSku().getName()); //商品名称
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(ProductUtils.getFxWeight(BigDecimal.ZERO));
        }
    }


    /**
     * @param ocBOrder      订单主表实体
     * @param taobaoFxOrder 淘宝分销主表
     * @return 转好的支付信息表
     */
    public OcBOrderPayment convertTaobaoFxOrderPayToOmsOrderPay(OcBOrder ocBOrder, IpBTaobaoFxOrder taobaoFxOrder) {
        OcBOrderPayment item = new OcBOrderPayment();
        item.setId(sequenceUtil.buildOrderPaymentSequenceId());
        //设置分库键
        item.setOcBOrderId(ocBOrder.getId());
        item.setCreationdate(new Date());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setModifiername(SystemUserResource.ROOT_USER_NAME);
        item.setModifieddate(new Date());
        item.setModifierid(SystemUserResource.ROOT_USER_ID);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerid(SystemUserResource.ROOT_USER_ID);
        item.setOwnername(SystemUserResource.ROOT_USER_NAME);
        item.setAdClientId(SystemUserResource.AD_CLIENT_ID);
        item.setAdOrgId(SystemUserResource.AD_ORG_ID);
        item.setVersion(0L);
        //支付流水号
        item.setPaymentNo(null);
        //交易创建时间
        item.setCreationdate(ocBOrder.getOrderDate());
        //支付时间
        item.setPayTime(ocBOrder.getPayTime());
        //交易完成时间
        item.setEndTime(ocBOrder.getEndTime());
        //支付金额
        item.setPaymentAmt(ocBOrder.getReceivedAmt());
        //订单金额
        item.setAmtOrder(ocBOrder.getOrderAmt());
        //付款方式
        item.setPayType(ocBOrder.getPayType());
        //付款状态
        item.setPayStatus(OmsPayStatus.PAID.toInteger());
        //是否启用
        item.setIsactive("Y");
        return item;
    }

    /**
     * 创建Order全链路日志信息
     *
     * @param ocBOrder      订单信息表
     * @param taobaoFxOrder 淘宝分销订单信息表
     * @return Order全链路日志信息
     */
    public OcBOrderLink buildOrderLink(OcBOrder ocBOrder, IpBTaobaoFxOrder taobaoFxOrder) {
        OcBOrderLink orderLink = new OcBOrderLink();
        orderLink.setId(sequenceUtil.buildOrderSequenceId());
        orderLink.setTid(ocBOrder.getTid());
        orderLink.setOcBOrderId(ocBOrder.getId());
        orderLink.setBackflowStatus(BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        orderLink.setExtAttribute(null);
        orderLink.setSellerNick(taobaoFxOrder.getSupplierUsername());
        orderLink.setSyncStatus(0);
        orderLink.setSyncTime(null);
        orderLink.setErrorInfo(null);
        orderLink.setCreationdate(new Date());
        orderLink.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderLink.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        orderLink.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderLink.setModifieddate(new Date());
        orderLink.setOwnerid(SystemUserResource.ROOT_USER_ID);
        orderLink.setModifierid(SystemUserResource.ROOT_USER_ID);
        orderLink.setPlatform(PlatFormEnum.TAOBAO_DISTRIBUTION.getCode().toString());

        return orderLink;
    }

    /**
     * 判断子表sum（平摊金额）是否与头表订单优惠金额是否一致，若一致，不处理，若不一致 则调用订单平摊金额服务
     *
     * @param ocBOrder 包含所有信息的relation
     */
    public void doCheckAndUpdateBlanceMoney(OcBOrder ocBOrder) {
        OcBOrderRelation ocRelation = new OcBOrderRelation();
        ocRelation.setOrderInfo(ocBOrder);
        try {
            boolean checkAndUpdateBlanceMoney = omsOrderCheckAndUpdateService.doCheckAndUpdateBlanceMoney(ocRelation);
            if (!checkAndUpdateBlanceMoney) {
                throw new NDSException();
            }
        } catch (Exception ex) {
            log.info("日志服务：订单平摊金额服务失败！ ");
            throw new NDSException("保存失败:订单平摊金额服务失败！");
        }
    }
}
