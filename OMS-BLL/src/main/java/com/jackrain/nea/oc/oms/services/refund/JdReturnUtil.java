package com.jackrain.nea.oc.oms.services.refund;

import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import lombok.Getter;

/**
 * @Author: 黄世新
 * @Date: 2020/12/31 上午10:50
 * @Version 1.0
 */
public class JdReturnUtil {

    /**
     * 转换为淘宝的发货前退款状态
     *
     * @param status
     * @return
     */
    public static String transTaobaoRefundStatus(int status) {
        if (status == 0) {
            return TaobaoReturnOrderExt.RefundStatus.WAIT_SELLER_AGREE.getCode();
        } else if (status == 2 || status == 11) {
            return TaobaoReturnOrderExt.RefundStatus.CLOSED.getCode();
        } else if (status == 1 || status == 5 || status == 3 || status == 9 || status == 6) {
            return TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode();
        }
        return "";
    }


    public enum RefundStatus {

        STAY_AUDIT("待审核", "WAIT_SELLER_AGREE"),
        STAY_RECEIVY_GOODS("待收货", "WAIT_BUYER_RETURN_GOODS"),
        STAY_HANDLE("待处理", "WAIT_BUYER_RETURN_GOODS"),
        STAY_USER_CONFIRM("待用户确认", "SUCCESS"),
        COMPLETE("完成", "SUCCESS"),
        CANCEL("取消", "CLOSED"),
        AUDIT_CLOSE("审核关闭", "CLOSED"),
        PICK_UP_APPLY("自营取件申请", "WAIT_BUYER_RETURN_GOODS");

        @Getter
        private String code;
        @Getter
        private String message;

        RefundStatus(String code, String message) {
            this.code = code;
            this.message = message;
        }

        public static String transTaobaoRefundStatus(String status) {
            RefundStatus[] values = RefundStatus.values();
            for (RefundStatus value : values) {
                if (value.getCode().equals(status)) {
                    return value.getMessage();
                }
            }
            return "1";
        }
    }
}
