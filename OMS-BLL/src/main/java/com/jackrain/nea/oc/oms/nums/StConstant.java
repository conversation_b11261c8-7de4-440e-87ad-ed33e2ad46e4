package com.jackrain.nea.oc.oms.nums;

import java.util.HashMap;
import java.util.Map;

/**
 * @Descroption 策略中心常量类
 * <AUTHOR>
 * @Date 2019/3/7 13:38
 */
public class StConstant {
    /**
     * 表名
     */
    public static final String TAB_ST_C_DISTRIBUTION = "ST_C_DISTRIBUTION";//分销代销策略表
    public static final String TAB_ST_C_DISTRIBUTION_ITEM = "ST_C_DISTRIBUTION_ITEM";//分销代销策略商品明细
    public static final String TAB_ST_C_DISTRIBUTION_CUSTOMER = "ST_C_DISTRIBUTION_CUSTOMER";//分销代销策略经销商明细
    //运费方案表
    public static final String TAB_ST_C_POSTFEE = "ST_C_POSTFEE";//运费方案主表
    public static final String TAB_ST_C_POSTFEE_ITEM = "ST_C_POSTFEE_ITEM";//运费方案明细
    public static final String TAB_ST_C_POSTFEE_WAREHOUSE = "ST_C_POSTFEE_WAREHOUSE";//运费方案实体仓明细
    //操作费方案表
    public static final String TAB_ST_C_OPERATIONCOST = "ST_C_OPERATIONCOST";//操作费方案主表
    public static final String TAB_ST_C_OPERATIONCOST_ITEM = "ST_C_OPERATIONCOST_ITEM";//操作费方案实体仓明细
    //商品价格策略表
    public static final String TAB_ST_C_PRICE = "ST_C_PRICE";//商品价格策略主表
    public static final String TAB_ST_C_PRICE_ITEM = "ST_C_PRICE_ITEM";//商品价格策略明细
    public static final String TAB_ST_C_ORDER_PRICE_ITEM = "ST_C_ORDER_PRICE_ITEM";//订单价格策略明细
    public static final String TAB_ST_C_PRICE_EXCLUDE_ITEM = "ST_C_PRICE_EXCLUDE_ITEM";//排除商品明细
    public static final String TAB_ST_C_PRICE_SHOP = "ST_C_PRICE_SHOP";//商品价格策略店铺明细
    //唯品会JIT缺货策略表
    public static final String TAB_ST_C_VIPCOM_STOCKLACK = "ST_C_VIPCOM_STOCKLACK";//唯品会JIT缺货策略主表
    public static final String TAB_ST_C_VIPCOM_STOCKLACK_ITEM = "ST_C_VIPCOM_STOCKLACK_ITEM";//操唯品会JIT缺货策略明细表
    //订单自动审核策略表
    public static final String TAB_ST_C_AUTOCHECK = "ST_C_AUTOCHECK";//订单自动审核策略主表
    public static final String TAB_ST_C_AUTOCHECK_PROVINCE = "ST_C_AUTOCHECK_PROVINCE";//单自动审核策略-排除物流公司明细
    public static final String TAB_ST_C_AUTOCHECK_EXCLUDE_LOGISTICS = "ST_C_AUTOCHECK_EXCLUDE_LOGISTICS";//单自动审核策略-收货人所在省明细
    public static final String TAB_ST_C_AUTOCHECK_AUDIT_MARK = "ST_C_AUTOCHECK_AUDIT_MARK";//订单自动审核-标识审核等待时间
    public static final String TAB_ST_C_AUTOCHECK_EXCLUDE_PRODUCT = "ST_C_AUTOCHECK_EXCLUDE_PRODUCT";
    //店铺策略表名
    public static final String TAB_ST_C_SHOP_STRATEGY = "ST_C_SHOP_STRATEGY";//店铺策略表
    public static final String TAB_ST_C_SHOP_STRATEGY_IEM = "ST_C_SHOP_STRATEGY_ITEM";//店铺策略明细

    public static final String TAB_ST_C_VIPCOM_COOPERATION_NO = "ST_C_VIPCOM_COOPERATION_NO";//常态合作编码

    public static final String TAB_ST_C_VIPCOM_PROJECT = "ST_C_VIPCOM_PROJECT";//日程计划表
    public static final String TAB_ST_C_VIPCOM_PROJECT_ITEM = "ST_C_VIPCOM_PROJECT_ITEM";//日程计划明细
    public static final String TAB_ST_C_VIPCOM_PROJECT_WH_ENTRY_ITEM = "ST_C_VIPCOM_PROJECT_WH_ENTRY_ITEM";//日程计划明细
    public static final String TAB_ST_C_VIPCOM_PROJECT_PICKORDER_ITEM = "ST_C_VIPCOM_PROJECT_PICKORDER_ITEM";//日程计划明细
    public static final String TAB_ST_C_VIPCOM_PROJECT_WAREHOUSE = "ST_C_VIPCOM_PROJECT_WAREHOUSE";//日程计划仓库明细
    public static final String TAB_ST_C_REFUND_ORDER_STRATEGY = "ST_C_REFUND_ORDER_STRATEGY";//退货审核表
    public static final String TAB_ST_C_VIPCOM_ASCRIPTION = "ST_C_VIPCOM_ASCRIPTION";//日程归属表
    public static final String TAB_ST_C_LOCK_STOCK_STRATEGY = "ST_C_LOCK_STOCK_STRATEGY";//店铺库存锁库策略
    public static final String TAB_ST_C_LOCK_STOCK_STRATEGY_ITEM = "ST_C_LOCK_STOCK_STRATEGY_ITEM";//店铺库存锁库策略明细

    //代销运费策略
    public static final String TAB_ST_C_CONSIGN_FEE = "ST_C_CONSIGN_FEE";//代销运费策略主表
    public static final String TAB_ST_C_CONSIGN_FEE_ITEM = "ST_C_CONSIGN_FEE_ITEM";//代销运费策略明细

    //订单派单规则
    public static final String TAB_ST_C_SEND_RULE = "ST_C_SEND_RULE";//订单派单规则主表
    public static final String TAB_ST_C_SEND_RULE_ADDRESS_RENT = "ST_C_SEND_RULE_ADDRESS_RENT";//订单派单规则-按收货地址
    public static final String TAB_ST_C_SEND_RULE_WAREHOUSE_RATE = "ST_C_SEND_RULE_WAREHOUSE_RATE";//订单派单规则-按分仓比例

    //店铺锁库条码特殊设置策略
    public static final String TAB_ST_C_LOCK_SKU_STRATEGY = "ST_C_LOCK_SKU_STRATEGY";//店铺锁库条码特殊设置策略主表
    public static final String TAB_ST_C_LOCK_SKU_STRATEGY_ITEM = "ST_C_LOCK_SKU_STRATEGY_ITEM";//店铺锁库条码特殊设置策略明细
    //实体仓库表
    public static final String TAB_CP_C_PHY_WAREHOUSE = "CP_C_PHY_WAREHOUSE";//实体发货仓
    public static final String TAB_CP_B_VIPCOM_WAHOUSE = "CP_B_VIPCOM_WAHOUSE";//JIT发货仓

    public static final String TAB_ST_C_EXPRESS = "ST_C_EXPRESS";//物流方案
    public static final String TAB_ST_C_EXPRESS_PRO_ITEM = "ST_C_EXPRESS_PRO_ITEM";//物流方案商品
    public static final String TAB_ST_C_EXPRESS_PLAN_AREA_ITEM = "ST_C_EXPRESS_PLAN_AREA_ITEM";//物流方案区域明细
    public static final String TAB_ST_C_EXPRESS_PACKAGE = "ST_C_EXPRESS_PACKAGE";//物流方案包裹明细
    public static final String TAB_ST_C_EXPRESS_LOGISTICS_ITEM = "ST_C_EXPRESS_LOGISTICS_ITEM";//物流方案包裹明细
    public static final String TAB_ST_C_EXPRESS_WAREHOUSE_ITEM = "ST_C_EXPRESS_WAREHOUSE_ITEM";//物流方案包裹明细


    public static final String TAB_ST_C_SYNC_STOCK_STRATEGY = "ST_C_SYNC_STOCK_STRATEGY";//店铺同步库存策略
    public static final String TAB_ST_C_SYNC_STOCK_STRATEGY_CHANNEL = "ST_C_SYNC_STOCK_STRATEGY_CHANNEL";//店铺同步库存策略供货渠道
    public static final String TAB_ST_C_SYNC_STOCK_STRATEGY_ITEM = "ST_C_SYNC_STOCK_STRATEGY_ITEM";//店铺同步库存策略供货仓

    public static final String TAB_ST_C_CHANNEL_STRATEGY = "ST_C_CHANNEL_STRATEGY";//渠道策略
    public static final String TAB_ST_C_CHANNEL_STRATEGY_ITEM = "ST_C_CHANNEL_STRATEGY_ITEM";//渠道策略逻辑仓

    //经销商自有商品表
    public static final String TAB_ST_C_SELL_OWNGOODS = "ST_C_SELL_OWNGOODS";//经销商自有商品表
    public static final String TAB_ST_C_SELL_OWNGOODS_ITEM = "ST_C_SELL_OWNGOODS_ITEM";//经销商自有商品明细表
    public static final String TAB_ST_C_SELL_OWNGOODS_CUSTOMER = "ST_C_SELL_OWNGOODS_CUSTOMER";//经销商明细表

    //唯品会通知邮件
    public static final String TAB_ST_C_VIPCOM_MAIL = "ST_C_VIPCOM_MAIL";//唯品会通知邮件
    //唯品会JITX仓库对照表
    public static final String TAB_ST_C_VIPCOM_JITX_WAREHOUSE = "ST_C_VIPCOM_JITX_WAREHOUSE";//唯品会通知邮件
    public static final String TAB_ST_C_VIPCOM_JITX_WAREHOUSE_LOG = "ST_C_VIPCOM_JITX_WAREHOUSE_LOG";//唯品会通知邮件


    //快递赔付方案
    public static final String TAB_ST_C_COMPENSATE = "ST_C_COMPENSATE";//快递赔付方案
    public static final String TAB_ST_C_COMPENSATE_LOGISTICS = "ST_C_COMPENSATE_LOGISTICS";//快递公司明细
    public static final String TAB_ST_C_COMPENSATE_WAREHOUSE = "ST_C_COMPENSATE_WAREHOUSE";//实体仓明细

    //订单合并策略表
    public static final String TAB_ST_C_MERGE_ORDER = "ST_C_MERGE_ORDER";//订单合并策略表
    //订单合并策略品类限制明细表
    public static final String TAB_ST_C_MERGE_CATEGORY_LIMIT_ITEM = "ST_C_MERGE_CATEGORY_LIMIT_ITEM";

    //店铺商品特殊设置
    public static final String TAB_ST_C_PRODUCT_STRATEGY = "ST_C_PRODUCT_STRATEGY";//店铺商品特殊设置
    public static final String TAB_ST_C_PRODUCT_STRATEGY_ITEM = "ST_C_PRODUCT_STRATEGY_ITEM";//店铺商品特殊设置明细
    public static final String TAB_ST_C_OPERATE_LOG = "ST_C_OPERATE_LOG";

    public static final String TAB_ST_C_EWAYBILL = "ST_C_EWAYBILL";//电子面单策略
    public static final String TAB_ST_C_EWAYBILL_LOGISTICS = "ST_C_EWAYBILL_LOGISTICS";//电子面单物流明细
    public static final String TAB_ST_C_EWAYBILL_SHOP = "ST_C_EWAYBILL_SHOP";//电子面单店铺明细

    public static final String TAB_ST_C_EXPRESS_AREA = "ST_C_EXPRESS_AREA";//物流区域策略
    public static final String TAB_ST_C_EXPRESS_AREA_ITEM = "ST_C_EXPRESS_AREA_ITEM";//物流策略-物流区域设置明细

    public static final String TAB_ST_C_INVENTORY_OWNERSHIP = "ST_C_INVENTORY_OWNERSHIP";//库存归属策略
    public static final String TAB_ST_C_INVENTORY_OWNERSHIP_ITEM = "ST_C_INVENTORY_OWNERSHIP_ITEM";//库存归属策略明细

    public static final String TAB_ST_C_AUTO_INVOICE = "ST_C_AUTO_INVOICE";//自动开票策略
    public static final String TAB_ST_C_AUTO_INVOICE_SHOP = "ST_C_AUTO_INVOICE_SHOP";//自动开票店铺明细

    public static final String TAB_ST_C_OWNERSHIP_WAREHOUSE_SET = "ST_C_OWNERSHIP_WAREHOUSE_SET";//库存归属仓库属性设置
    public static final String TAB_ST_C_PICK_UP_TIME = "ST_C_PICK_UP_TIME";//上门取件时间策略
    public static final String TAB_ST_C_PICK_UP_TIME_ITEM = "ST_C_PICK_UP_TIME_ITEM";//上门取件时间策略明细

    public static final String TAB_ST_C_SYNC_SKUSTOCK_STRATEGY = "ST_C_SYNC_SKUSTOCK_STRATEGY";//店铺条码库存同步策略
    public static final String TAB_ST_C_SYNC_SKUSTOCK_PRO = "ST_C_SYNC_SKUSTOCK_PRO";//店铺条码库存同步策略商品信息
    public static final String TAB_ST_C_SYNC_SKUSTOCK_STORE = "ST_C_SYNC_SKUSTOCK_STORE";//店铺条码库存同步策略店仓信息

    public static final String TAB_ST_C_ROUTER = "ST_C_ROUTER"; // 快递路由策略
    public static final String TAB_ST_C_ROUTER_CODE = "ST_C_ROUTER_CODE"; // 快递路由策略
    public static final String TAB_ST_C_ROUTER_WAREHOUSE = "ST_C_ROUTER_WAREHOUSE"; // 快递路由策略

    //订单换货策略表
    public static final String TAB_ST_C_EXCHANGE_STRATEGY_ORDER = "ST_C_EXCHANGE_ORDER_STRATEGY";//订单换货策略表

    //订单换货拒绝原因
    public static final String TAB_ST_C_EXCHANGE_REFUSE_REASON = "ST_C_EXCHANGE_REFUSE_REASON_ITEM";//订单换货拒绝原因

    //订单加急打标策略
    public static final String TAB_ST_C_ORDER_URGENT_STRATEGY = "ST_C_ORDER_URGENT_STRATEGY";//订单加急打标策略

    //零担费用设置
    public static final String TAB_ST_C_UNFULLCAR_COST = "ST_C_UNFULLCAR_COST"; //零担费用设置
    public static final String TAB_ST_C_UNFULLCAR_COST_ITEM = "ST_C_UNFULLCAR_COST_ITEM"; //零担费用明细设置

    //商品效期策略
    public static final String TAB_ST_C_EXPIRY_DATE = "ST_C_EXPIRY_DATE"; //商品效期策略
    public static final String TAB_ST_C_EXPIRY_DATE_ITEM = "ST_C_EXPIRY_DATE_ITEM"; //商品效期策略明细

    // 物流禁发区域
    public static final String ST_C_BANS_AREA_STRATEGY = "ST_C_BANS_AREA_STRATEGY";

    // 对等换货策略
    public static final String ST_C_EQUITY_BARTER_STRATEGY = "ST_C_EQUITY_BARTER_STRATEGY";
    public static final String ST_C_EQUITY_BARTER_STRATEGY_ITEM = "ST_C_EQUITY_BARTER_STRATEGY_ITEM";

    //单据业务类型档案
    public static final String ST_C_BUSINESS_TYPE = "ST_C_BUSINESS_TYPE";

    //单据业务类型匹配策略
    public static final String ST_C_BUSINESS_TYPE_MATCH_STRATEGY = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY";
    public static final String ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM = "ST_C_BUSINESS_TYPE_MATCH_STRATEGY_ITEM";

    //寻源前拆单策略
    public static final String ST_C_SPLIT_BEFORE_SOURCE_STRATEGY = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY";
    public static final String ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_CATEGORY_ITEM";
    public static final String ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_WEIGHT_ITEM";
    public static final String ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM = "ST_C_SPLIT_BEFORE_SOURCE_STRATEGY_SKU_ITEM";

    // 箱型策略
    public static final String ST_C_BOX_STRATEGY = "ST_C_BOX_STRATEGY";

    // 缺货不拆策略
    public static final String ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY = "ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY";
    public static final String ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY_DETAIL = "ST_C_SHORT_STOCK_NO_SPLIT_STRATEGY_DETAIL";

    // 指定快递
    public static final String ST_C_APPOINT_EXPRESS_STRATEGY = "ST_C_APPOINT_EXPRESS_STRATEGY";
    public static final String ST_C_APPOINT_EXPRESS_STRATEGY_DETAIL = "ST_C_APPOINT_EXPRESS_STRATEGY_DETAIL";

    // 周期购促销策略
    public static final String ST_C_CYCLE_STRATEGY = "ST_C_CYCLE_STRATEGY";
    public static final String ST_C_CYCLE_RULE_STRATEGY = "ST_C_CYCLE_RULE_STRATEGY";
    public static final String ST_C_CYCLE_ITEM_STRATEGY = "ST_C_CYCLE_ITEM_STRATEGY";

    //直发预占单
    public static final String OC_B_DIRECT_REPORT_ORDER = "OC_B_DIRECT_REPORT_ORDER";
    public static final String OC_B_DIRECT_REPORT_ORDER_ITEM = "OC_B_DIRECT_REPORT_ORDER_ITEM";

    /**
     * 策略平台公共操作日志表
     */
    public static final String TAB_OC_B_OPERATION_LOG = "OC_B_OPERATION_LOG";
    /**
     * 标识
     */
    public static final String TRUE_FLAG = "1";
    public static final String FALSE_FLAG = "0";

    /**
     * 固定值-1
     */
    public static final Long MINUS_ONE = -1L;

    /**
     * 类型
     */
    // 固定结算
    public static final long CON_SETTLEMENT_TYPE_01 = 1;
    // 佣金结算
    public static final long CON_SETTLEMENT_TYPE_02 = 2;

    /**
     * 价格策略类型
     */
    public static final String POLICY_TYPE_01 = "1"; //订单金额
    public static final String POLICY_TYPE_02 = "2"; //订单折扣


    //作废状态
    public static final String ISACTIVE_Y = "Y";//启用
    public static final String ISACTIVE_N = "N";//作废


    public static final String TRUE_STR = "true";
    public static final String FALSE_STR = "false";

    //单据状态
    public static final Integer CON_BILL_STATUS_01 = 1;//未审核
    public static final Integer CON_BILL_STATUS_02 = 2;//已审核
    public static final Integer CON_BILL_STATUS_03 = 3;//已作废
    public static final Integer CON_BILL_STATUS_04 = 4;//已结案

    //操作名称
    public static final String CON_BILL_ACTION_VOID = "作废";//作废
    public static final String CON_BILL_ACTION_AUDIT = "审核";//审核
    public static final String CON_BILL_ACTION_CANCLE_AUDIT = "反审";//反审
    public static final String CON_BILL_ACTION_FINISH = "结案";//结案
    public static final String CON_BILL_ACTION_DELAY = "延期";//延期

    //唯品会单据类型
    public static final Integer VIP_BILLTYPE_PRE = 3;//预调拨订单
    public static final Integer VIP_BILLTYPE_COM = 0;//正常订单

    //赔付类型
    public static final Integer COMPENSATE_PRICE = 1;//价格赔付
    public static final Integer COMPENSATE_POSTAGE = 2;//邮费赔付
    public static final Integer COMPENSATE_FIXED = 3;//固定结算

    //赔付标准
    public static final String PRICE = "1";//销售价
    public static final String PRICE_LIST = "2";//标准价
    public static final String POSTAGE = "3";//邮费

    //店铺条码库存同步策略单据状态
    public static final Integer SKUSTOCK_STATUS_01 = 1;//未审核
    public static final Integer SKUSTOCK_STATUS_02 = 2;//已审核
    public static final Integer SKUSTOCK_STATUS_03 = 3;//已作废
    public static final Integer SKUSTOCK_STATUS_04 = 4;//已结案

    // @20200610 直播解析策略相关
    // 1. 直播方案状态
    public static final String LIVE_STRATEGY_STATUS_INIT = "10";     // 未审核
    public static final String LIVE_STRATEGY_STATUS_APPROVED = "20"; // 已审核
    public static final String LIVE_STRATEGY_STATUS_FINISHED = "30"; // 已结案
    public static final String LIVE_STRATEGY_STATUS_VOID = "40";     // 已作废

    // 2. 直播平台
    public static final Integer LIVE_PLATFORM_DOUYIN = 1;   // 抖音
    public static final Integer LIVE_PLATFORM_KUAISHOU = 2; // 快手
    public static final Integer LIVE_PLATFORM_MOMO = 3;     // 陌陌
    public static final Integer LIVE_PLATFORM_MOGUJIE = 4;  // 蘑菇街
    public static final Integer LIVE_PLATFORM_TAOBAO = 5;   // 淘宝

    // 3. 单据时间类型
    public static final Integer BILL_TIME_TYPE_PLACE = 1;   // 下单时间
    public static final Integer BILL_TIME_TYPE_PAY = 2;     // 支付时间

    // 4. 直播单据识别规则
    public static final Integer LIVE_STRATEGY_RULE_TITLE = 1;   // 根据商品标题识别
    public static final Integer LIVE_STRATEGY_RULE_ID = 2;      // 根据平台商品ID识别
    public static final Integer LIVE_STRATEGY_RULE_REMARK = 3;  // 根据订单备注识别

    // 5. 自动结案超时时间设置 默认7天
    public static final Long LIVE_STRATEGY_CLOSE_TIMEOUT = 1000 * 60 * 60 * 24 * 7L;

    // 6. 表名定义
    // 直播解析策略主表
    public static final String TAB_ST_C_LIVE_CAST_STRATEGY = "ST_C_LIVE_CAST_STRATEGY";
    // 直播解析策略明细
    public static final String TAB_ST_C_LIVE_CAST_STRATEGY_ITEM = "ST_C_LIVE_CAST_STRATEGY_ITEM";

    //预到货策略
    public static final String TAB_ST_C_PRE_ARRIVAL = "ST_C_PRE_ARRIVAL";
    public static final String TAB_ST_C_PRE_ARRIVAL_ITEM = "ST_C_PRE_ARRIVAL_ITEM";
    public static final String REDIS_ST_PRE_ARRIVAL = "ST:PRE_ARRIVAL:";
    //预到货策略单据状态
    public static final Integer PRE_ARRIVAL_STATUS_01 = 1;//未审核
    public static final Integer PRE_ARRIVAL_STATUS_02 = 2;//已审核
    public static final Integer PRE_ARRIVAL_STATUS_03 = 3;//已作废
    public static final Integer PRE_ARRIVAL_STATUS_04 = 4;//已结案

    //预售解析策略
    public static final String TAB_ST_C_PRE_SALE = "ST_C_PRE_SALE";
    public static final String TAB_ST_C_PRE_SALE_ITEM = "ST_C_PRE_SALE_ITEM";//预售解析策略方案
    public static final String PRE_SALE_SHOP = "店铺全款预售";// 店铺全款预售
    public static final String PRE_SALE_CUSTOMIZE = "自定义全款预售"; //自定义全款预售
    public static final Integer PRE_SALE_02 = 2;// 店铺全款预售
    public static final Integer PRE_SALE_03 = 3; //自定义全款预售

    //预售解析策略单据状态
    public static final Integer PRE_SALE_STATUS_01 = 1;//未审核
    public static final Integer PRE_SALE_STATUS_02 = 2;//已审核
    public static final Integer PRE_SALE_STATUS_03 = 3;//已作废
    public static final Integer PRE_SALE_STATUS_04 = 4;//已结案

    // 仓库拆单策略
    public static final String TAB_ST_C_WAREHOUSE = "ST_C_WAREHOUSE";
    public static final String TAB_ST_C_WAREHOUSE_BRAND = "ST_C_WAREHOUSE_BRAND";
    public static final String TAB_ST_C_WAREHOUSE_GOODS = "ST_C_WAREHOUSE_GOODS";
    public static final String TAB_ST_C_WAREHOUSE_SKU = "ST_C_WAREHOUSE_SKU";
    public static final String TAB_ST_C_WAREHOUSE_GOODS_CLASS = "ST_C_WAREHOUSE_GOODS_CLASS";

    // 订单HOLD单策略
    public static final String TAB_ST_C_HOLD_ORDER = "ST_C_HOLD_ORDER";
    public static final String TAB_ST_C_HOLD_ORDER_ITEM = "ST_C_HOLD_ORDER_ITEM";
    public static final String TAB_ST_C_HOLD_PROVINCE_ITEM = "ST_C_HOLD_PROVINCE_ITEM";
    //定金预售预下沉策略
    public static final String TAB_ST_C_DEPOSIT_PRE_SALE_SINK = "ST_C_DEPOSIT_PRE_SALE_SINK";
    public static final String TAB_ST_C_DEPOSIT_PRE_SALE_SINK_ITEM = "ST_C_DEPOSIT_PRE_SALE_SINK_ITEM";
    public static final String TAB_ST_C_DEPOSIT_PRE_SALE_SINK_LOGISTICS = "ST_C_DEPOSIT_PRE_SALE_SINK_LOGISTICS";
    // 预售卡单
    public static final String TAB_ST_C_DETENTION_POLICY = "ST_C_DETENTION_POLICY";
    public static final String TAB_ST_C_DETENTION_POLICY_ITEM = "ST_C_DETENTION_POLICY_ITEM";
    //订单打标
    public static final String TAB_ST_C_ORDER_LABEL = "ST_C_ORDER_LABEL";
    public static final String TAB_ST_C_ORDER_LABEL_ITEM = "ST_C_ORDER_LABEL_ITEM";
    public static final String TAB_ST_C_ORDER_LABEL_SHOP_ITEM = "ST_C_ORDER_LABEL_SHOP_ITEM";
    public static final String TAB_ST_C_ORDER_LABEL_STRATEGY_ITEM = "ST_C_ORDER_LABEL_STRATEGY_ITEM";
    public static final String TAB_ST_C_ORDER_LABEL_LOG = "ST_C_ORDER_LABEL_LOG";
    public static final String TAB_ST_C_CUSTOM_LABEL = "ST_C_CUSTOM_LABEL";
    public static final Integer HOLD_ORDER_STATUS_01 = 1;//未审核
    public static final Integer HOLD_ORDER_STATUS_02 = 2;//已审核
    public static final Integer HOLD_ORDER_STATUS_03 = 3;//已作废
    public static final Integer HOLD_ORDER_STATUS_04 = 4;//已结案
    public static final Integer DETENTION_POLICY_STATUS_01 = 1;//未审核
    public static final Integer DETENTION_POLICY_STATUS_02 = 2;//已审核
    public static final Integer DETENTION_POLICY_STATUS_03 = 3;//已作废
    public static final Integer DETENTION_POLICY_STATUS_04 = 4;//已结案

    public static final String DETENTION_RULES_POLICY_TYPE_PTSKU = "PTSKU";
    public static final String DETENTION_RULES_POLICY_TYPE_TPID = "TPID";
    public static final String DETENTION_RULES_POLICY_TYPE_MPRO = "MPRO";
    public static final String DETENTION_RULES_POLICY_TYPE_MSKU = "MSKU";
    //Hold 识别规则
    public static final  Map<String,String> HOLD_ORDER_RULES_RECOGNITION = new HashMap(8){{
        put("0","全部");
        put("1","大类");
        put("2","中类");
        put("3","小类");
        put("4","商品编码");
        put("5","条码");
        put(null,"N/A");
        put("","N/A");
    }};
    public static final  Map<String,String> DETENTION_RULES_POLICY_TYPE = new HashMap(6){{
        put("PTSKU","平台商品编码ID");
        put("TPID","平台商品ID");
        put("MPRO","中台商品款号");
        put("MSKU","中台商品编码");
    }};


    // 订单推单延时策略
    public static final String TAB_ST_C_ORDER_PUSH_DELAY_STRATEGY = "ST_C_ORDER_PUSH_DELAY_STRATEGY";
    //虚高库存主表
    public static final String TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK = "ST_C_SHOP_VIRTUAL_HIGH_STOCK";
    //虚高库存明细表
    public static final String TAB_ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM = "ST_C_SHOP_VIRTUAL_HIGH_STOCK_ITEM";


    //库存同步中间表 类型
    public static final Integer SYNC_STOCK_STRATEGY_TYPE = 1; //店铺同步库存
    public static final Integer PRODUCT_STRATEGY_TYPE = 2;    //特殊商品
    public static final Integer LOCK_SKU_STRATEGY_TYPE = 3;   //锁库条码
    public static final Integer LOCK_STOCK_TYPE = 4;          //锁库
    public static final Integer VIRTUALHIGH_STRATEGY_TYPE = 5;//虚高策略

    //库存同步中间表 状态
    public static final Integer SYNC_STATUS_SUCCESS = 1; //已同步
    public static final Integer NO_SYNC_STATUS = 2; //未同步
    public static final Integer SYNC_STATUS_ERROR = 3; //同步失败
    public static final Integer SYNCING = 4; //同步中

    //短信策略
    public static final String TAB_ST_C_MSG_STRATEGY = "ST_C_MSG_STRATEGY";
    //短信策略-通知类型
    public static final String MSG_ADVICE_TYPE_LOGISTICS_DELIVERY = "1"; //物流发货提醒
    public static final String MSG_ADVICE_TYPE_RETURN = "2"; //退换货提醒
    //短信策略-任务节点
    public static final String MSG_TASK_NODE_01 = "1"; //非平台拆分订单完成仓库发货
    public static final String MSG_TASK_NODE_02 = "2"; //非天猫换货订单完成仓库发货
    public static final String MSG_TASK_NODE_03 = "3"; //退换货完成入库
    public static final String MSG_TASK_NODE_04 = "4"; //无名件完成入库

    //自定义拆单配置
    public static final String TAB_ST_C_SPLIT_REASON_CONFIG = "ST_C_SPLIT_REASON_CONFIG";
    //自定义拆单
    public static final String TAB_ST_C_SPLIT_REASON = "ST_C_SPLIT_REASON";
    public static final String TAB_ST_C_SPLIT_REASON_ITEM = "ST_C_SPLIT_REASON_ITEM";//自定义拆单明细

    // 退供条码差异配置
    public static final String IP_B_VIP_RETURN_ORDER = "ip_b_vip_return_order";
    // 退供条码差异配置值长度
    public static final Integer IP_B_VIP_RETURN_ORDER_VALUE = 2;

    // QuerySession 入参 objid
    public static final String OBJID = "objid";

    // QuerySession 入参 fixcolumn
    public static final String FIXCOLUMN = "fixcolumn";

    // QuerySession 入参 tabitem
    public static final String TABITEM = "tabitem";

    //预售卡单策略key
    public static final String SHOP_DETENTION_ORDER_ST = "st:order:Detention:shopid:";

    //拣货单创建方式 1-按时间点创建 2-按未拣货数创建
    public static final String PICKORDER_CREATE_TYPE_1 = "1";
    public static final String PICKORDER_CREATE_TYPE_2 = "2";

    //唯品会补货加急策略表
    public static final String ST_C_VIP_URGENT_STRATEGY = "ST_C_VIP_URGENT_STRATEGY";

    /** vip四级地址 */
    public static final String TAB_T_OMS_VIP_FULL_ADDRESS = "T_OMSVIPFULLADDRESS";

    //快递报价设置
    public static String TAB_ST_C_EXPRESS_PRICE_STRATEGY = "ST_C_EXPRESS_PRICE_STRATEGY";
    public static String TAB_ST_C_EXPRESS_PRICE_STRATEGY_ITEM = "ST_C_EXPRESS_PRICE_STRATEGY_ITEM";

    //店铺物流设置
    public static String ST_C_SHOP_LOGISTIC_STRATEGY = "ST_C_SHOP_LOGISTIC_STRATEGY";
    public static String ST_C_SHOP_LOGISTIC_STRATEGY_ITEM = "ST_C_SHOP_LOGISTIC_STRATEGY_ITEM";

    //商品物流设置
    public static String ST_C_PRO_LOGISTIC_STRATEGY = "ST_C_PRO_LOGISTIC_STRATEGY";

    // 订单打标策略类型
    public static final String ST_C_ORDER_LABEL_ITEM_RULES_RECOGNITION_PTSKU = "PTSKU";
    public static final String ST_C_ORDER_LABEL_ITEM_RULES_RECOGNITION_MSKU = "MSKU";
    public static final String ST_C_ORDER_LABEL_ITEM_RULES_RECOGNITION_TPID = "TPID";
    public static final String ST_C_ORDER_LABEL_ITEM_RULES_RECOGNITION_MPRO = "MPRO";

}
