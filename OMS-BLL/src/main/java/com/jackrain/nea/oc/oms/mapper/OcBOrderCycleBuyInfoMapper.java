package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderCycleBuyInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 中台周期购额外信息 mapper
 *
 * <AUTHOR>
 */
@Mapper
@Component
public interface OcBOrderCycleBuyInfoMapper extends ExtentionMapper<OcBOrderCycleBuyInfo> {

    /**
     * 根据平台单号和OM单号查询周期购额外信息
     *
     * @param tid
     * @param billNo
     * @return
     */
    @Select("SELECT * FROM oc_b_order_cycle_buy_info WHERE tid = #{tid} AND bill_no = #{billNo} AND isactive = 'Y'")
    List<OcBOrderCycleBuyInfo> getByTidAndBillNo(@Param("tid") String tid, @Param("billNo") String billNo);

    /**
     * 根据OM单号查询周期购额外信息
     *
     * @param billNo
     * @return
     */
    @Select("SELECT * FROM oc_b_order_cycle_buy_info WHERE bill_no = #{billNo} AND isactive = 'Y'")
    List<OcBOrderCycleBuyInfo> getByBillNo(@Param("billNo") String billNo);

    /**
     * 根据平台单号查询周期购额外信息
     *
     * @param tid
     * @return
     */
    @Select("SELECT * FROM oc_b_order_cycle_buy_info WHERE tid = #{tid} AND isactive = 'Y'")
    List<OcBOrderCycleBuyInfo> getByTid(@Param("tid") String tid);

    /**
     * 根据平台单号集合查询周期购额外信息
     *
     * @param tids
     * @return
     */
    @Select("<script> "
            + "SELECT * FROM oc_b_order_cycle_buy_info WHERE isactive = 'Y' AND tid "
            + "in <foreach item='item' index='index' collection='tids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderCycleBuyInfo> getByTids(@Param("tids") List<String> tids);

    /**
     * 作废额外信息
     *
     * @param ids
     */
    @Update("<script> "
            + "UPDATE oc_b_order_cycle_buy_info SET isactive = 'N', modifieddate = now() WHERE isactive = 'Y' AND id IN "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateIsactiveN(@Param("ids") List<Long> ids);

}
