package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoFxRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoFxRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.CpRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: 周琳胜
 * @since: 2019/7/16
 * create at : 2019/7/16 1:50
 */
@Component
@Slf4j
public class TaobaoFxReturnTransferUtil {

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OmsRefundOrderService omsRefundOrderService;

    /**
     * i.先将退单申请金额与原单明细的单行实际成交价real_amt做比较，相等则下一步转换；
     * ii.不相等，再将退单申请金额与原单明细的单件实际成交价（单行实际成交价/数量）做比较，
     * 相等则下一步转换，
     *
     * @param taobaoFxRefundRelation
     * @return
     */
    public boolean isAmtBalanceNew(IpTaobaoFxRefundRelation taobaoFxRefundRelation) {
        IpBTaobaoFxRefund taobaoFxRefund = taobaoFxRefundRelation.getTaobaoFxRefund();
        BigDecimal refundFee = taobaoFxRefund.getRefundFee(); //退还金额
        String oid = String.valueOf(taobaoFxRefund.getSubOrderId()); // todo 申请退款的oid
        List<OcBOrderItem> ocBOrderItems = taobaoFxRefundRelation.getOcBOrderItems();
        List<OcBOrderItem> collect = new ArrayList<>();
        BigDecimal realAmtCount = BigDecimal.ZERO;
        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            String ooid = ocBOrderItem.getOoid();
            if (oid.equals(ooid)) {
                realAmtCount = realAmtCount.add(ocBOrderItem.getRealAmt()); //单行实际成交价
                collect.add(ocBOrderItem);
            }
        }
        if (refundFee.compareTo(realAmtCount) == 0 || realAmtCount.compareTo(refundFee) < 0) {
            return true;
        }
        BigDecimal amtCount = BigDecimal.ZERO;
        for (OcBOrderItem orderItem : collect) {
            BigDecimal qty = orderItem.getQty(); //数量
            BigDecimal realAmt = orderItem.getRealAmt(); //单行实际成交价
            amtCount = amtCount.add(realAmt.divide(qty, 4, BigDecimal.ROUND_HALF_DOWN));
        }
        //单件实际成交价（单行实际成交价/数量）
        return refundFee.compareTo(amtCount) == 0;
    }

    /**
     * 淘宝分销退货接口关系转换OMS退换货关系
     *
     * @param taobaoFxRefundRelation
     * @return
     */
    public List<OcBReturnOrderRelation> taobaoFxRefundOrderToReturnOrder(
            IpTaobaoFxRefundRelation taobaoFxRefundRelation) {
        List<OcBReturnOrderRelation> returnOrderRelations = new ArrayList<>();
        OcBReturnOrderRelation returnOrderRelation = new OcBReturnOrderRelation();
        List<OcBOrderItem> ocbOrderItems = taobaoFxRefundRelation.getOcBOrderItems();
        OcBOrder ocBOrder = taobaoFxRefundRelation.getOcBOrder();
        IpBTaobaoFxRefund taobaoFxRefund = taobaoFxRefundRelation.getTaobaoFxRefund();
        List<OcBOrderItem> ocBOrderGifts = taobaoFxRefundRelation.getOcBOrderGifts();
        if (CollectionUtils.isNotEmpty(ocBOrderGifts)) {
            boolean gift = this.isGift(taobaoFxRefund);
            if (!gift) {
                ocbOrderItems.addAll(ocBOrderGifts);
            }
        }

        OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromTaobaoFxRefund(taobaoFxRefund, ocBOrder);
        if (ocBReturnOrder == null) {
            throw new NDSException("淘宝退款中间表转换到退换货订单结果为空");
        }
        returnOrderRelation.setReturnOrderInfo(ocBReturnOrder);

        List<OcBReturnOrderRefund> returnOrderItems =
                this.buildReturnOrderItemFromRefund(taobaoFxRefund, ocbOrderItems, false);
        this.getAllSku(returnOrderItems, ocBReturnOrder);
        returnOrderRelation.setOrderRefundList(returnOrderItems);
        returnOrderRelations.add(returnOrderRelation);
        return returnOrderRelations;
    }

    /**
     * 判断赠品是否存在退换货订单 根据平台单号判断是否存在退换货订单
     * (按道理此逻辑不应在这里  前期没考虑的问题,目前先加这里)
     */
    private boolean isGift(IpBTaobaoFxRefund taoBaoFxRefund) {
        //通过平台单号查询ES
        Set<Long> ids = ES4ReturnOrder.findIdByTid(taoBaoFxRefund.getSubOrderId());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(ids)) {
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            return org.apache.commons.collections.CollectionUtils.isNotEmpty(list);
        }
        return false;
    }

    /**
     * 淘宝退款中间表转换到退换货订单
     *
     * @param ipBTaobaoFxRefund 退款中间表数据
     * @param ocBOrder          订单原表数据
     * @return 退款主表数据
     */
    private OcBReturnOrder buildOcBReturnOrderFromTaobaoFxRefund(
            IpBTaobaoFxRefund ipBTaobaoFxRefund, OcBOrder ocBOrder) {
        if (null == ipBTaobaoFxRefund || null == ocBOrder) {
            return null;
        }
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        // 单据类型
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        // todo 平台退款单号
        returnOrder.setReturnId(ipBTaobaoFxRefund.getSubOrderId().toString());
        //原始订单编号
        returnOrder.setOrigOrderId(ocBOrder.getId());
        //买家昵称
        returnOrder.setBuyerNick(ocBOrder.getUserNick());
        //申请退款时间
        returnOrder.setReturnCreateTime(ipBTaobaoFxRefund.getXrefundCreateTime());
        //最后修改时间
        returnOrder.setLastUpdateTime(ipBTaobaoFxRefund.getXmodified());
        //等待退货入库(PRD数据对象)
        returnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode());
        //退款说明
        returnOrder.setReturnDesc(ipBTaobaoFxRefund.getRefundReason());
        //下载时间
        returnOrder.setCreationdate(new Date());
        //创建人 todo
//        returnOrder.setOwnerid(ipBTaobaoFxRefund.getsy);
        // all_sku
        //商品应退金额
        returnOrder.setReturnAmtList(ipBTaobaoFxRefund.getRefundFee());
        // 退款金额
        returnOrder.setReturnAmtActual(ipBTaobaoFxRefund.getRefundFee());
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //换货人姓名
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //换货人手机
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //店铺id
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        //邮编
        returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
        //售后/售中
//        returnOrder.setReturnPhase(ipBTaobaoFxRefund.getRefundPhase());
        //发货仓库
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        //平台类型
        returnOrder.setPlatform(ocBOrder.getPlatform());
        //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
        returnOrder.setReturnAmtActual(ipBTaobaoFxRefund.getRefundFee());
        //换货金额
        returnOrder.setExchangeAmt(BigDecimal.ZERO);
        returnOrder.setTid(ocBOrder.getTid());
        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //退单状态，默认20
        //TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
        returnOrder.setIsactive("Y");
        //退款原因
        //returnOrder.setRemark(ipBTaobaoFxRefund.getReason());
        //returnOrder.setReturnReason(ipBTaobaoFxRefund.getReason());
        returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());
        //店铺名称
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        //卖家昵称
//        returnOrder.setSellerNick(ipBTaobaoFxRefund.getSellerNick());
        returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
        returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
        returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
        returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
        returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
        returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());

        //取值为发货实体仓档案中关联的退货待检实体仓仓库
        returnOrder.setCpCPhyWarehouseInId(this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId()));
        //  物流公司 物流单号
        String sid = ocBOrder.getExpresscode();
        String companyName = ocBOrder.getCpCLogisticsEname();
        returnOrder.setLogisticsCode(sid);
        returnOrder.setCpCLogisticsEname(companyName);
        //业务类型
        StCBusinessType stCBusinessType = omsRefundOrderService.queryReturnOrderType(ocBOrder);
        returnOrder.setBusinessTypeId(stCBusinessType.getId());
        returnOrder.setBusinessTypeCode(stCBusinessType.getEcode());
        returnOrder.setBusinessTypeName(stCBusinessType.getEname());
        this.setLogisticInfo(returnOrder, companyName, ocBOrder);
        return returnOrder;
    }

    /**
     * 通过实体仓id查询该实体仓的退货仓id
     */
    private Long selectReturnCPhyWarehouse(Long cPhyWarehouseId) {
        if (cPhyWarehouseId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cPhyWarehouseId);
            if (cpCPhyWarehouse != null && cpCPhyWarehouse.getOriginalReturnPhyWarehouseId() != null) {
                return cpCPhyWarehouse.getOriginalReturnPhyWarehouseId();
            } else {
                return cPhyWarehouseId;
            }
        }
        return null;
    }

    /**
     * 2019-0715新增优化需求
     *
     * @param returnOrder
     * @param buyerLogisticName
     * @param ocBOrder
     */
    public void setLogisticInfo(OcBReturnOrder returnOrder, String buyerLogisticName, OcBOrder ocBOrder) {
        if (StringUtils.isNotEmpty(buyerLogisticName)) {
            // 依据物流名称查询物流公司
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(buyerLogisticName);
            if (logisticsInfo != null) {
                returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                returnOrder.setCpCLogisticsId(logisticsInfo.getId());
            } else {
                returnOrder.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
                returnOrder.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
            }
        } else {
            returnOrder.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
            returnOrder.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
        }
    }

    /**
     * 淘宝退款中间表转换到退货明细
     *
     * @param ipBTaobaoFxRefund orderItem
     * @param orderItems        订单明细
     * @param isGift            是否有赠品
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnOrderItemFromRefund(
            IpBTaobaoFxRefund ipBTaobaoFxRefund, List<OcBOrderItem> orderItems, Boolean isGift) {
        List<OcBReturnOrderRefund> result = null;
        Map<String, String> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderItems)) {
            //实际实收汇总
            BigDecimal allAealAmt = orderItems.stream().map(OcBOrderItem::getRealAmt).
                    reduce(BigDecimal.ZERO, BigDecimal::add);

            //总数数量
            BigDecimal allQty = orderItems.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            //a.单件实际成交价=单行实际成交金额（real_amt）合计/数量合计

            BigDecimal realPrice = allAealAmt.divide(allQty, 4, BigDecimal.ROUND_HALF_UP);
            result = new ArrayList<>();
            // 分摊金额
            BigDecimal totalReceivedAmount = BigDecimal.ZERO;
            int i = 0;
            for (OcBOrderItem orderItem : orderItems) {
                i++;
                Integer isGift1 = orderItem.getIsGift();
                if (isGift1 == 1) {
                    isGift = true;
                }
                OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
                returnOrderRefund.setPsCProEname(orderItem.getPsCProEname()); //商品名称
                returnOrderRefund.setPrice(orderItem.getPriceList());
                //1 qty_can_refund 购买数量 合计所有明细qty
                returnOrderRefund.setQtyCanRefund(orderItem.getQty());
                returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
                returnOrderRefund.setOcBOrderItemId(orderItem.getId());
                //单行数量
                BigDecimal itemQty = orderItem.getQty();
                if (isGift) {
                    returnOrderRefund.setAmtAdjust(BigDecimal.ZERO);
                    //3退还金额=明细数量
                    returnOrderRefund.setAmtRefund(BigDecimal.ZERO);
                    returnOrderRefund.setAmtRefundSingle(BigDecimal.ZERO);
                    //2申请数量=明细数量
                    returnOrderRefund.setQtyRefund(itemQty);
                } else {
                    //退还金额 refund_fee/总数量X单行数量 分摊余额
                    BigDecimal refundFee = ipBTaobaoFxRefund.getRefundFee();
                    BigDecimal refundAmt = refundFee.multiply(itemQty).divide(allQty, 4, BigDecimal.ROUND_HALF_DOWN);
                    if (i != orderItems.size()) {
                        returnOrderRefund.setAmtRefund(refundAmt);
                        // @20201215 以下代码无用先注释
                        // totalReceivedAmount.add(refundAmt);
                    } else {
                        returnOrderRefund.setAmtRefund(refundFee.multiply(totalReceivedAmount));
                    }
                    //2申请数量 公式
                    //b 公式略
                    BigDecimal refundAmtTemp = returnOrderRefund.getAmtRefund();
                    BigDecimal abs = refundAmtTemp.divide(realPrice, 4, BigDecimal.ROUND_HALF_UP).subtract(allQty);
                    abs = abs.abs();
                    if (abs.compareTo(BigDecimal.valueOf(0.01)) > 0) {
                        returnOrderRefund.setQtyRefund(orderItem.getQty());
                    } else {
                        BigDecimal qtyReturn = refundAmtTemp.divide(realPrice, 0, BigDecimal.ROUND_HALF_UP);
                        returnOrderRefund.setQtyRefund(qtyReturn);
                    }
                    //4 调整金额 oc_b_return_order_refund.refund_amt-price_list
                    // 一头牛产线异常报错巡检优化： 调整金额的逻辑计算，与浩哥沟通先调整成与淘宝的逻辑一致，20221118
                    returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
                    //单间退货金额(实际成交金额/数量)
                    returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_DOWN));
                    returnOrderRefund.setAmtRefund(ipBTaobaoFxRefund.getRefundFee());
                }
                //商品单价
                returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
                //国标码
                returnOrderRefund.setBarcode(orderItem.getBarcode());
                //修改人用户名
                returnOrderRefund.setModifierename(orderItem.getModifierename());
                //商品规格
                returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
                //条码id
                returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
                returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
                returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
                returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
                returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
                returnOrderRefund.setPsCProId(orderItem.getPsCProId());
                //颜色尺寸
                returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
                returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
                returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

                returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
                returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
                returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
                returnOrderRefund.setSex(orderItem.getSex());
                //子订单
                if (null != ipBTaobaoFxRefund.getSubOrderId()) {
                    returnOrderRefund.setOid(String.valueOf(ipBTaobaoFxRefund.getSubOrderId()));
                }
                //入库数量
                returnOrderRefund.setQtyIn(BigDecimal.ZERO);
                returnOrderRefund.setVersion(orderItem.getVersion());
                //商品标记
                returnOrderRefund.setProductMark("1");

                //退还金额
                Long reserveBigint01 = orderItem.getProType(); //商品类型
                String giftbagSku = orderItem.getGiftbagSku(); //虚拟条码
                if (reserveBigint01 != null) {
                    if (SkuType.COMBINE_PRODUCT == reserveBigint01 || SkuType.GIFT_PRODUCT == reserveBigint01) {
                        if (!map.containsKey(giftbagSku)) {
                            map.put(giftbagSku, null);
                            returnOrderRefund.setAmtRefund(ipBTaobaoFxRefund.getRefundFee());
                        } else {
                            returnOrderRefund.setAmtRefund(BigDecimal.ZERO);
                        }
                        returnOrderRefund.setQtyRefund(orderItem.getQty());//申请数量
                        returnOrderRefund.setQtyCanRefund(orderItem.getQty());

                    }
                }
                //增加吊牌价和sex传值 20190827
                returnOrderRefund.setSex(orderItem.getSex());
                returnOrderRefund.setPriceList(orderItem.getPriceTag());
                //计算结算金额和结算单价
                setPriceAndTotPrice(orderItem, returnOrderRefund);
                //退单平台sku
                /*if (StringUtils.isNotBlank(orderItem.getNumIid())) {
                    returnOrderRefund.setNumIid(orderItem.getNumIid());
                }
                if (StringUtils.isNotBlank(orderItem.getSkuNumiid())) {
                    returnOrderRefund.setSkuNumiid(orderItem.getSkuNumiid());
                }*/
                result.add(returnOrderRefund);
            }
        }
        return result;
    }

    /**
     * 退货结算单价和结算金额
     */
    private void setPriceAndTotPrice(OcBOrderItem ocBOrderItem, OcBReturnOrderRefund refund) {
        if (log.isInfoEnabled()) {
            log.info(this.getClass().getName() + "计算结算单价和结算金额ocBOrderItem=" + JSONObject.toJSONString(ocBOrderItem)
                    + ",refund=" + JSONObject.toJSONString(refund));
        }
        try {
            BigDecimal sgAmt = BigDecimal.ZERO;
            BigDecimal qtyRefund = refund.getQtyRefund();
            BigDecimal qty = ocBOrderItem.getQty();
            //成交金额
            BigDecimal realAmt = ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt();
            if (realAmt != null && qty != null && qty.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                sgAmt = realAmt.divide(qty, OcBOrderConst.DECIMAL_QTY, BigDecimal.ROUND_HALF_UP);
            }
            BigDecimal settle = ocBOrderItem.getPriceSettle() == null ? sgAmt : ocBOrderItem.getPriceSettle();
            settle = settle.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN ? settle : BigDecimal.ZERO;

            BigDecimal totPrice = settle.multiply(qtyRefund);
            //结算单价
            refund.setPriceSettle(settle);
            //结算金额
            refund.setAmtSettleTot(totPrice);
        } catch (Exception e) {
            log.info(this.getClass().getName() + "淘宝转退货单计算结算单价和金额出错" + e.getMessage());
            refund.setPriceSettle(BigDecimal.ZERO);
            refund.setAmtSettleTot(BigDecimal.ZERO);
        }
    }

    /**
     * 封装主表的all_sku以及 商品数量
     *
     * @param returnOrderItems
     * @return
     */
    private void getAllSku(List<OcBReturnOrderRefund> returnOrderItems, OcBReturnOrder returnOrder) {
        //拼接退货sku加数量
        String skuQyt = "";
        BigDecimal qtyInstore = BigDecimal.ZERO;
        for (OcBReturnOrderRefund returnOrderItem : returnOrderItems) {
            String str = returnOrderItem.getPsCSkuEcode() + "(" + returnOrderItem.getQtyRefund().intValue() + "),";
            skuQyt = skuQyt + str;
            qtyInstore = qtyInstore.add(returnOrderItem.getQtyRefund());
        }
        if (StringUtils.isNotEmpty(skuQyt)) {
            //去掉最后一个,号
            skuQyt = skuQyt.substring(0, skuQyt.length() - 1);
        }
        returnOrder.setQtyInstore(qtyInstore);
        returnOrder.setAllSku(skuQyt);
    }
}
