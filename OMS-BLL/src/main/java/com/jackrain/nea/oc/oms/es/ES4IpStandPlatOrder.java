package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 5:19 下午
 */
public class ES4IpStandPlatOrder {

    private ES4IpStandPlatOrder() {
    }

    /**
     * 根据转换状态和系统备注查询平台交易单号
     *
     * @param pageIndex
     * @param pageSize
     */
    public static List<String> findTidByTransStatusAndSysRemark(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "0");

            String[] returnFieldNames = new String[]{"TID"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(
                    OcElasticSearchIndexResources.IP_B_STANDPLAT_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_STANDPLAT_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");
                if (CollectionUtils.isEmpty(arrayObj)) {
                    return orderNoList;
                }

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("TID");
                    orderNoList.add(orderNo);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return orderNoList;
    }
}
