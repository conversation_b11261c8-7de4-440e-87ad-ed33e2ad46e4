package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderSourceRelation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface OcBOrderSourceRelationMapper extends ExtentionMapper<OcBOrderSourceRelation> {

    @Select("SELECT source_order_id FROM oc_b_order_source_relation WHERE order_id = #{id} AND type = #{type}")
    List<Long> querySourceOrderId(@Param("id") Long id, @Param("type") String type);
}