package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.dto.Pod2BOrderQueryDTO;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.request.OcBOrderToBReportRequest;
import com.jackrain.nea.oc.oms.model.result.OcBOrderToBReportResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @ClassName OcBOrderByAdbMapper
 * @Description 通过adb查询订单、订单明细数据
 * <AUTHOR>
 * @Date 2024/8/30 09:15
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderByAdbMapper extends ExtentionMapper<OcBOrder> {

    @Select("<script>" +
            " SELECT o.tid tid,o.bill_no billNo,o.sg_b_out_bill_no onNo,o.order_status status,o.cp_c_phy_warehouse_ecode warehouseCode," +
            "o.cp_c_logistics_ecode logisticsCode,o.cp_c_logistics_ename logisticsName,i.ps_c_sku_ecode skuCode,i.ps_c_pro_ename skuName,i.qty,i.standard_weight weight,o.receiver_name buyerName," +
            "o.receiver_mobile buyerPhone,o.cp_c_region_province_ename buyerProvince,o.cp_c_region_city_ename buyerCity,o.cp_c_region_area_ename buyerArea," +
            "o.receiver_address buyerAddress,o.buyer_message buyerRemark,o.seller_memo sellerRemark,o.cp_c_shop_title shopName," +
            "o.sales_department_id saleDepartment,o.audit_name auditor," +
            "o.creationdate createTime,o.audit_time auditTime,o.scan_time scanTime," +
            "o.carpool_no carpoolNo,w.cp_c_phy_warehouse_ename warehouseName," +
            "i.real_out_num,c.volume" +
            " FROM oc_b_order o" +
            " LEFT JOIN oc_b_order_item i ON o.id = i.oc_b_order_id left join #{db}.ps_c_pro c on i.ps_c_pro_id =c.id " +
            " left join #{db}.st_c_warehouse_logistic_strategy w on o.cp_c_phy_warehouse_ecode = w.cp_c_phy_warehouse_ecode " +
            " left join #{db}.st_c_warehouse_logistic_strategy_item wi on w.id = wi.st_c_warehouse_logistic_strategy_id and o.cp_c_logistics_ecode = wi.cp_c_logistics_ecode " +
            " WHERE o.business_type_code in ('RYCK16','RYCK17','RYCK18')  and o.creationdate &gt;= #{createDate} and o.order_status in (3,4,5,6) " +
            " AND o.isactive = 'Y'" +
            " AND (`i`.`REFUND_STATUS` != '6') " +
            "<if test='tid != null and tid != \"\"'>" +
            " AND o.tid = #{tid}" +
            "</if>" +
            "<if test='orderStatusList != null and orderStatusList.size() > 0'>" +
            " AND o.order_status in " +
            "<foreach collection='orderStatusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach>" +
            "</if>" +
            "<if test='billNo != null and billNo != \"\"'>" +
            " AND o.BILL_NO = #{billNo}" +
            "</if>" +
            "<if test='onNo != null and onNo != \"\"'>" +
            " AND o.sg_b_out_bill_no = #{onNo}" +
            "</if>" +
            "<if test='warehouseCode != null and warehouseCode != \"\"'>" +
            " AND o.cp_c_phy_warehouse_ecode = #{warehouseCode}" +
            "</if>" +
            "<if test='logisticsCode != null and logisticsCode != \"\"'>" +
            " AND wi.logistics_supplier_ecode in (#{logisticsCode})" +
            "</if>" +
            "<if test='skuCode != null and skuCode != \"\"'>" +
            " AND i.ps_c_sku_ecode = #{skuCode}" +
            "</if>" +
            "<if test='buyerProvince != null and buyerProvince != \"\"'>" +
            " AND o.cp_c_region_province_ename LIKE CONCAT('%', #{buyerProvince}, '%')" +
            "</if>" +
            "<if test='buyerCity != null and buyerCity != \"\"'>" +
            " AND o.cp_c_region_city_ename LIKE CONCAT('%', #{buyerCity}, '%')" +
            "</if>" +
            "<if test='buyerArea != null and buyerArea != \"\"'>" +
            " AND o.cp_c_region_area_ename LIKE CONCAT('%', #{buyerArea}, '%')" +
            "</if>" +
            "<if test='shopCode != null and shopCode != \"\"'>" +
            " AND o.cp_c_shop_ecode = #{shopCode}" +
            "</if>" +
            "<if test='salesDepartment != null and salesDepartment != \"\"'>" +
            " AND o.sales_department_id = #{salesDepartment}" +
            "</if>" +
            "<if test='auditor != null and auditor != \"\"'>" +
            " AND o.audit_name = #{auditor}" +
            "</if>" +
            "<if test='startCreateTime != null'>" +
            " AND o.creationdate &gt;= #{startCreateTime}" +
            "</if>" +
            "<if test='endCreateTime != null'>" +
            " AND o.creationdate &lt;= #{endCreateTime}" +
            "</if>" +
            "<if test='startAuditTime != null'>" +
            " AND o.audit_time &gt;= #{startAuditTime}" +
            "</if>" +
            "<if test='endAuditTime != null'>" +
            " AND o.audit_time &lt;= #{endAuditTime}" +
            "</if>" +
            "<if test='startDistributeTime != null'>" +
            " AND o.scan_time &gt;= #{startDistributeTime}" +
            "</if>" +
            "<if test='endDistributeTime != null'>" +
            " AND o.scan_time &lt;= #{endDistributeTime}" +
            "</if>" +
            "<if test='warehouseName != null and warehouseName != \"\"'>" +
            " AND w.cp_c_phy_warehouse_ename LIKE CONCAT('%', #{warehouseName}, '%')" +
            "</if>" +
            " ORDER BY o.id DESC, i.id desc" +
            " LIMIT #{pageSize} OFFSET #{offset}" +
            "</script>")
    List<Pod2BOrderQueryDTO> selectOrders(@Param("tid") String tid, @Param("billNo") String billNo,
                                          @Param("onNo") String onNo, @Param("warehouseCode") String warehouseCode, @Param("logisticsCode") String logisticsCode,
                                          @Param("skuCode") String skuCode, @Param("buyerProvince") String buyerProvince, @Param("buyerCity") String buyerCity,
                                          @Param("buyerArea") String buyerArea, @Param("shopCode") String shopCode, @Param("salesDepartment") String salesDepartment,
                                          @Param("auditor") String auditor, @Param("startCreateTime") String startCreateTime, @Param("endCreateTime") String endCreateTime,
                                          @Param("startAuditTime") String startAuditTime, @Param("endAuditTime") String endAuditTime, @Param("startDistributeTime") String startDistributeTime,
                                          @Param("endDistributeTime") String endDistributeTime, @Param("orderStatusList") List<String> orderStatusList, @Param("createDate") Date createDate,
                                          @Param("warehouseName") String warehouseName,
                                          @Param("pageSize") Integer pageSize, @Param("offset") Integer offset,
                                          @Param("db") String db);

    @Select("<script>" +
            " SELECT count(*)" +
            " FROM oc_b_order o" +
            " LEFT JOIN oc_b_order_item i ON o.id = i.oc_b_order_id left join #{db}.ps_c_pro c on i.ps_c_pro_id =c.id " +
            " left join #{db}.st_c_warehouse_logistic_strategy w on o.cp_c_phy_warehouse_ecode = w.cp_c_phy_warehouse_ecode " +
            " left join #{db}.st_c_warehouse_logistic_strategy_item wi on w.id = wi.st_c_warehouse_logistic_strategy_id and o.cp_c_logistics_ecode = wi.cp_c_logistics_ecode " +
            " WHERE o.business_type_code in ('RYCK16','RYCK17','RYCK18') and o.creationdate &gt;= #{createDate} and o.order_status in (3,4,5,6) " +
            " AND o.isactive = 'Y'" +
            " AND (`i`.`REFUND_STATUS` != '6') " +
            "<if test='tid != null and tid != \"\"'>" +
            " AND o.tid = #{tid}" +
            "</if>" +
            "<if test='orderStatusList != null and orderStatusList.size() > 0'>" +
            " AND o.order_status in " +
            "<foreach collection='orderStatusList' item='status' open='(' separator=',' close=')'>" +
            "#{status}" +
            "</foreach>" +
            "</if>" +
            "<if test='billNo != null and billNo != \"\"'>" +
            " AND o.BILL_NO = #{billNo}" +
            "</if>" +
            "<if test='onNo != null and onNo != \"\"'>" +
            " AND o.sg_b_out_bill_no = #{onNo}" +
            "</if>" +
            "<if test='warehouseCode != null and warehouseCode != \"\"'>" +
            " AND o.cp_c_phy_warehouse_ecode = #{warehouseCode}" +
            "</if>" +
            "<if test='logisticsCode != null and logisticsCode != \"\"'>" +
            " AND wi.logistics_supplier_ecode in (#{logisticsCode})" +
            "</if>" +
            "<if test='skuCode != null and skuCode != \"\"'>" +
            " AND i.ps_c_sku_ecode = #{skuCode}" +
            "</if>" +
            "<if test='buyerProvince != null and buyerProvince != \"\"'>" +
            " AND o.cp_c_region_province_ename LIKE CONCAT('%', #{buyerProvince}, '%')" +
            "</if>" +
            "<if test='buyerCity != null and buyerCity != \"\"'>" +
            " AND o.cp_c_region_city_ename LIKE CONCAT('%', #{buyerCity}, '%')" +
            "</if>" +
            "<if test='buyerArea != null and buyerArea != \"\"'>" +
            " AND o.cp_c_region_area_ename LIKE CONCAT('%', #{buyerArea}, '%')" +
            "</if>" +
            "<if test='shopCode != null and shopCode != \"\"'>" +
            " AND o.cp_c_shop_ecode = #{shopCode}" +
            "</if>" +
            "<if test='salesDepartment != null and salesDepartment != \"\"'>" +
            " AND o.sales_department_id = #{salesDepartment}" +
            "</if>" +
            "<if test='auditor != null and auditor != \"\"'>" +
            " AND o.audit_name = #{auditor}" +
            "</if>" +
            "<if test='startCreateTime != null'>" +
            " AND o.creationdate &gt;= #{startCreateTime}" +
            "</if>" +
            "<if test='endCreateTime != null'>" +
            " AND o.creationdate &lt;= #{endCreateTime}" +
            "</if>" +
            "<if test='startAuditTime != null'>" +
            " AND o.audit_time &gt;= #{startAuditTime}" +
            "</if>" +
            "<if test='endAuditTime != null'>" +
            " AND o.audit_time &lt;= #{endAuditTime}" +
            "</if>" +
            "<if test='startDistributeTime != null'>" +
            " AND o.scan_time &gt;= #{startDistributeTime}" +
            "</if>" +
            "<if test='endDistributeTime != null'>" +
            " AND o.scan_time &lt;= #{endDistributeTime}" +
            "</if>" +
            "</script>")
    int getTotal(@Param("tid") String tid, @Param("billNo") String billNo,
                 @Param("onNo") String onNo, @Param("warehouseCode") String warehouseCode, @Param("logisticsCode") String logisticsCode,
                 @Param("skuCode") String skuCode, @Param("buyerProvince") String buyerProvince, @Param("buyerCity") String buyerCity,
                 @Param("buyerArea") String buyerArea, @Param("shopCode") String shopCode, @Param("salesDepartment") String salesDepartment,
                 @Param("auditor") String auditor, @Param("startCreateTime") String startCreateTime, @Param("endCreateTime") String endCreateTime,
                 @Param("startAuditTime") String startAuditTime, @Param("endAuditTime") String endAuditTime, @Param("startDistributeTime") String startDistributeTime,
                 @Param("endDistributeTime") String endDistributeTime, @Param("orderStatusList") List<String> orderStatusList, @Param("createDate") Date createDate, @Param("db") String db);

    @Select("<script> " +
            "    SELECT o.id AS id, " +
            "           o.order_date AS createDate, " +
            "           o.tid AS tid, " +
            "           o.bill_no AS billNo, " +
            "           o.cp_c_shop_title, " +
            "           i.ps_c_pro_ename AS skuEname, " +
            "           i.expiry_date_range AS expiryDateRange, " +
            "           o.user_nick, " +
            "           i.qty, " +
            "           i.refund_status, " +
            "           ci.SALE_CENTER, " +
            "           i.reserve_bigint01 giftAttr, " +
            "           o.cp_c_phy_warehouse_ename AS cpCWarehouseName, " +
            "           o.order_status, " +
            "           o.cp_c_region_province_ename, " +
            "           o.cp_c_region_city_ename, " +
            "           o.cp_c_region_area_ename, " +
            "           o.cp_c_logistics_ename, " +
            "           i.ps_c_sku_ecode AS sku_ecode, " +
            "           di.ename AS salesDepartmentName, " +
            "           b.remark " +
            "    FROM oc_b_order o " +
            "    LEFT JOIN oc_b_order_item i ON o.id = i.oc_b_order_id " +
            "    LEFT JOIN oc_b_to_b_order b ON o.id = b.oc_b_order_id " +
            "    LEFT JOIN #{dto.db}.`cp_c_shop` c ON o.`cp_c_shop_id` = c.id " +
            "    LEFT JOIN #{dto.db}.`cp_c_shop_item` ci ON c.`id` = ci.`cp_c_shop_id` " +
            "       AND c.`GENERAL_ORGANIZATION_CODE` = ci.`sale_organization_description` " +
            "    LEFT JOIN #{dto.db}.`cp_c_storedim_item` di ON di.`id` = ci.sale_department " +
            "    WHERE o.id &gt; #{dto.limitId} and o.business_type_code IN ('RYCK16', 'RYCK17', 'RYCK18') " +
            "      AND o.order_status NOT IN (7, 8)  and ci.sale_center_description = 'Z03' " +
            "   and c.GENERAL_ORGANIZATION_CODE is not null and ci.SALES_SELECTION_FREEZE = 'N' " +
            "    <if test='dto.startDate != null'> " +
            "        AND o.order_date &gt;= #{dto.startDate} " +
            "    </if> " +
            "    <if test='dto.endDate != null'> " +
            "        AND o.order_date &lt;= #{dto.endDate} " +
            "    </if> " +
            "    <if test='dto.billNo != null'> " +
            "        AND o.bill_no = #{dto.billNo} " +
            "    </if> " +
            "    <if test='dto.tid != null'> " +
            "        AND o.tid = #{dto.tid} " +
            "    </if> " +
            "    <if test='dto.logisticsId != null'> " +
            "        AND o.cp_c_logistics_id = #{dto.logisticsId} " +
            "    </if> " +
            "    <if test='dto.userNick != null'> " +
            "        AND o.user_nick = #{dto.userNick} " +
            "    </if> " +
            "    <if test='dto.remark != null'> " +
            "        AND b.remark LIKE CONCAT('%', #{dto.remark}, '%') " +
            "    </if> " +
            "    <if test='dto.salesDepartmentId != null'> " +
            "        AND ci.sale_department = #{dto.salesDepartmentId} " +
            "    </if> " +
            "    <if test='dto.salesDepartmentIdList != null and dto.salesDepartmentIdList.size > 0'> " +
            "        AND ci.sale_department IN " +
            "        <foreach item='item' index='index' collection='dto.salesDepartmentIdList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "    <if test='dto.omsOrderStatusList != null and dto.omsOrderStatusList.size > 0'> " +
            "        AND o.order_status IN " +
            "        <foreach item='item' index='index' collection='dto.omsOrderStatusList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "    <if test='dto.cpCShopIdList != null and dto.cpCShopIdList.size > 0'> " +
            "        AND o.cp_c_shop_id IN " +
            "        <foreach item='item' index='index' collection='dto.cpCShopIdList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "    <if test='dto.logisticsIdList != null and dto.logisticsIdList.size > 0'> " +
            "        AND o.cp_c_logistics_id IN " +
            "        <foreach item='item' index='index' collection='dto.logisticsIdList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "    <if test='dto.proIdList != null and dto.proIdList.size > 0'> " +
            "        AND i.ps_c_pro_id IN " +
            "        <foreach item='item' index='index' collection='dto.proIdList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "    ORDER BY o.order_date desc, o.id DESC " +
            "    LIMIT #{dto.startindex}, #{dto.range} " +
            "</script>")
    List<OcBOrderToBReportResult> getOrderList(@Param("dto") OcBOrderToBReportRequest dto);

    @Select("<script> " +
            "    SELECT count(*) " +
            "    FROM oc_b_order o " +
            "    LEFT JOIN oc_b_order_item i ON o.id = i.oc_b_order_id " +
            "    LEFT JOIN oc_b_to_b_order b ON o.id = b.oc_b_order_id " +
            "    LEFT JOIN #{dto.db}.`cp_c_shop` c ON o.`cp_c_shop_id` = c.id " +
            "    LEFT JOIN #{dto.db}.`cp_c_shop_item` ci ON c.`id` = ci.`cp_c_shop_id` " +
            "       AND c.`GENERAL_ORGANIZATION_CODE` = ci.`sale_organization_description` " +
            "    LEFT JOIN #{dto.db}.`cp_c_storedim_item` di ON di.`id` = ci.sale_department " +
            "    WHERE o.id &gt; #{dto.limitId} and o.business_type_code IN ('RYCK16', 'RYCK17', 'RYCK18') " +
            "      AND o.order_status NOT IN (7, 8)  and ci.sale_center_description = 'Z03' " +
            "    and c.GENERAL_ORGANIZATION_CODE is not null and ci.SALES_SELECTION_FREEZE = 'N' " +
            "    <if test='dto.startDate != null'> " +
            "        AND o.order_date &gt;= #{dto.startDate} " +
            "    </if> " +
            "    <if test='dto.endDate != null'> " +
            "        AND o.order_date &lt;= #{dto.endDate} " +
            "    </if> " +
            "    <if test='dto.billNo != null'> " +
            "        AND o.bill_no = #{dto.billNo} " +
            "    </if> " +
            "    <if test='dto.tid != null'> " +
            "        AND o.tid = #{dto.tid} " +
            "    </if> " +
            "    <if test='dto.logisticsId != null'> " +
            "        AND o.cp_c_logistics_id = #{dto.logisticsId} " +
            "    </if> " +
            "    <if test='dto.userNick != null'> " +
            "        AND o.user_nick = #{dto.userNick} " +
            "    </if> " +
            "    <if test='dto.remark != null'> " +
            "        AND b.remark LIKE CONCAT('%', #{dto.remark}, '%') " +
            "    </if> " +
            "    <if test='dto.salesDepartmentId != null'> " +
            "        AND ci.sale_department = #{dto.salesDepartmentId} " +
            "    </if> " +
            "    <if test='dto.salesDepartmentIdList != null and dto.salesDepartmentIdList.size > 0'> " +
            "        AND ci.sale_department IN " +
            "        <foreach item='item' index='index' collection='dto.salesDepartmentIdList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "    <if test='dto.omsOrderStatusList != null and dto.omsOrderStatusList.size > 0'> " +
            "        AND o.order_status IN " +
            "        <foreach item='item' index='index' collection='dto.omsOrderStatusList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "    <if test='dto.cpCShopIdList != null and dto.cpCShopIdList.size > 0'> " +
            "        AND o.cp_c_shop_id IN " +
            "        <foreach item='item' index='index' collection='dto.cpCShopIdList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "    <if test='dto.logisticsIdList != null and dto.logisticsIdList.size > 0'> " +
            "        AND o.cp_c_logistics_id IN " +
            "        <foreach item='item' index='index' collection='dto.logisticsIdList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "    <if test='dto.proIdList != null and dto.proIdList.size > 0'> " +
            "        AND i.ps_c_pro_id IN " +
            "        <foreach item='item' index='index' collection='dto.proIdList' open='(' separator=',' close=')'> " +
            "            #{item} " +
            "        </foreach> " +
            "    </if> " +
            "</script>")
    int count(@Param("dto") OcBOrderToBReportRequest dto);

    @Select("SELECT MAX(id) FROM oc_b_order WHERE order_date <= #{orderDate}")
    Long findMaxIdByOrderDate(@Param("orderDate") String orderDate);
}
