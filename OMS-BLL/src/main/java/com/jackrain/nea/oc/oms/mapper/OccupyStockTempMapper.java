package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.CallableMsg;
import com.jackrain.nea.oc.oms.model.table.OccupyStockTemp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OccupyStockTempMapper extends ExtentionMapper<OccupyStockTemp> {

    @Select("select id from OCCUPY_STOCK_TEMP where STATUS = 0 LIMIT #{limit} ORDER BY modifieddate ASC")
    List<Long> queryList(@Param("limit") int limit);

    @Update("<script> UPDATE OCCUPY_STOCK_TEMP set  modifieddate = NOW() where id IN " +
            "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    void updateOccupyStock(@Param("ids")List<Long>  longList);

    @Update("<script> " +
            "UPDATE OCCUPY_STOCK_TEMP set  modifieddate = NOW(),STATUS=#{status},REMAKE=#{failInfo} where id IN " +
            "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    void updateOccupyStockFailStatus(@Param("ids") List<Long> longs,@Param("status") Integer status ,@Param("failInfo") String failInfo);

    @Update("<script> <foreach collection= 'callableMsgList' item= 'item' separator=';'>" +
            "UPDATE OCCUPY_STOCK_TEMP " +
            "set  modifieddate = NOW(),STATUS=#{item.status},REMAKE=#{item.message}" +
            "where id = #{item.id}" +
            "</foreach>" +
            "</script>")
    int updateOccupyStockStatus(@Param("callableMsgList") List<CallableMsg> callableMsgList);
}