package com.jackrain.nea.oc.oms.services.qimen;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.cpext.model.RedisKeyConstans;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.task.OcBRefundInTaskMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OmsStorageUtils;
import com.jackrain.nea.util.RedisOmsMasterUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 退换货单确认中间表
 *
 * @Author: guo.kw
 * @Since: 2022/7/15
 * create at: 2022/7/15 10:01
 * 修改20220812 用outbizcode做唯一值判断
 */
@Slf4j
@Component
public class OmsWmsBackRefundInTask {
    @Autowired
    private OcBRefundInTaskMapper ocBRefundInTaskMapper;
    @Reference(version = "1.0", group = "cp-ext")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;

    SimpleDateFormat beforeFormat = new SimpleDateFormat("yyyyMMdd");
    SimpleDateFormat afterFormat = new SimpleDateFormat("yyyy-MM-dd");

    public ValueHolderV14<String> apiProcess(String msg) {
        log.info(LogUtil.format("销售退货回传报文:{}", "OmsWmsBackRefundInTask.apiProcess"), msg);
        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisOmsMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;

        try {
            JSONObject request = JSONObject.parseObject(msg);
            JSONObject returnOrder = request.getJSONObject("returnOrder");

            // 单据类型
            String orderType = returnOrder.getString("orderType");
            // 仓库编码
            String warehouseCode = returnOrder.getString("warehouseCode");
            // 入库通知单号
            String returnOrderCode = returnOrder.getString("returnOrderCode");
            // WMS单据编号
            String returnOrderId = returnOrder.getString("returnOrderId");
            // outBizCode做唯一值判断
            String outBizCode = returnOrder.getString("outBizCode");
            if (StringUtils.isEmpty(outBizCode)) {
                throw new NDSException("外部业务编码不能为空");
            }
            lockKsy += outBizCode;
            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");

            OcBRefundInTask ocBRefundInTask1 = ocBRefundInTaskMapper.selectOne(new LambdaQueryWrapper<OcBRefundInTask>()
                    .eq(OcBRefundInTask::getOutBizCode, outBizCode)
                    .eq(OcBRefundInTask::getIsactive, "Y"));

            if (Objects.nonNull(ocBRefundInTask1) || ifAbsent == null || !ifAbsent) {
                log.error(LogUtil.format("退换入库单WMS回传重复.messageBody:{}",
                        "OmsWmsBackRefundInTask.apiProcess", ocBRefundInTask1), msg);
            } else {
                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);
                OcBRefundInTask ocBRefundInTask = new OcBRefundInTask();
                ocBRefundInTask.setId(ModelUtil.getSequence("oc_b_refund_in_task"));
                ocBRefundInTask.setReturnBillNo(returnOrderCode);
                ocBRefundInTask.setWarehouseCode(warehouseCode);
                ocBRefundInTask.setOrderType(orderType);
                ocBRefundInTask.setMsg(msg);
                ocBRefundInTask.setWmsBillNo(returnOrderId);
                ocBRefundInTask.setOutBizCode(outBizCode);
                ocBRefundInTask.setBillStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                ocBRefundInTask.setFailedCount(NumberUtils.INTEGER_ZERO);
                ocBRefundInTask.setIsactive(SgConstants.IS_ACTIVE_Y);
                OmsStorageUtils.setBModelDefalutData(ocBRefundInTask, R3SystemUserResource.getSystemRootUser());
                if (StringUtils.isNotEmpty(warehouseCode)) {
                    CpCPhyWarehouse warehouse = queryWarehouseByWmsWarehouseCode(warehouseCode);
                    if (warehouse != null) {
                        ocBRefundInTask.setWmsWarehouseType(warehouse.getWmsType());
                        /*京东仓转换批次*/
                        if (ThirdWmsTypeEnum.JDWMS.getCode().equals(warehouse.getWmsType())) {
                            JSONArray orderLines = request.getJSONArray("orderLines");
                            JSONArray newOrderLines = new JSONArray();
                            if (orderLines != null && orderLines.size() > 0) {
                                for (int i = 0; i < orderLines.size(); i++) {
                                    JSONObject jsonObject = orderLines.getJSONObject(i);
                                    JSONArray batchs = jsonObject.getJSONArray("batchs");
                                    if (batchs != null && batchs.size() > 0) {
                                        for (int j = 0; j < batchs.size(); j++) {
                                            JSONObject newJsonObject = JSONObject.parseObject(jsonObject.toJSONString());
                                            JSONObject batch = batchs.getJSONObject(j);
                                            if (!StringUtils.isEmpty(batch.getString("productDate"))) {
                                                batch.put("batchCode", batch.getString("productDate"));
                                            }
                                            batch.keySet().forEach(x -> newJsonObject.put(x, batch.get(x)));
                                            newJsonObject.remove("batchs");
                                            newOrderLines.add(newJsonObject);
                                        }
                                    }
                                }
                                request.put("orderLines", newOrderLines);
                            }
                            ocBRefundInTask.setMsg(request.toJSONString());
                            ocBRefundInTask.setWmsWarehouseType(ThirdWmsTypeEnum.JDWMS.getCode());
                        }
                        /*大宝仓商品转换，批次转换*/
                        if (ThirdWmsTypeEnum.DBWMS.getCode().equals(warehouse.getWmsType())) {
                            JSONArray orderLines = request.getJSONArray("orderLines");
                            if (orderLines != null && orderLines.size() > 0) {
                                for (int i = 0; i < orderLines.size(); i++) {
                                    JSONObject orderLine = orderLines.getJSONObject(i);
                                    //productDate转batchCode
                                    if (!StringUtils.isEmpty(orderLine.getString("produceCode"))) {
                                        try {
                                            Date produceCode = beforeFormat.parse(orderLine.getString("produceCode"));
                                            orderLine.put("batchCode", afterFormat.format(produceCode));
                                        } catch (ParseException e) {
                                            log.error(LogUtil.format("OmsWmsBackRefundInTask.apiProcess.error:{}",
                                                    "OmsWmsBackRefundInTask.apiProcess.error"), Throwables.getStackTraceAsString(e));
                                        }
                                    }
                                }
                            }
                            ocBRefundInTask.setMsg(request.toJSONString());
                            ocBRefundInTask.setWmsWarehouseType(ThirdWmsTypeEnum.DBWMS.getCode());
                        }
                        /*富勒WMS取库存地点*/
                        if (ThirdWmsTypeEnum.FLWMS.getCode().equals(warehouse.getWmsType())) {
                            JSONObject extendProps = request.getJSONObject("extendProps");
                            if (extendProps != null) {
                                String storageLocation = extendProps.getString("storageLocation");
                                if (StringUtils.isNotEmpty(storageLocation)) {
                                    ocBRefundInTask.setWarehouseCode(storageLocation);
                                }
                            }
                        }
                    }
                }
                ocBRefundInTask.setMsg(request.toJSONString());
                ocBRefundInTaskMapper.insert(ocBRefundInTask);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("退换入库单WMS回传异常:{}",
                    "OmsWmsBackRefundInTask.apiProcess"), Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        } finally {
            redisMasterTemplate.delete(lockKsy);
        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }

    private CpCPhyWarehouse queryWarehouseByWmsWarehouseCode(String wmsCode) {
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String redisKey = RedisKeyConstans.CP_WAREHOUSE_BY_WMS_CODE_JSON + wmsCode;
        String warehouseStr = redisTemplate.opsForValue().get(redisKey);

        CpCPhyWarehouse warehouse = null;
        if (!org.springframework.util.StringUtils.isEmpty(warehouseStr)) {
            warehouse = JSONObject.parseObject(warehouseStr, CpCPhyWarehouse.class);
            log.info(LogUtil.format("根据wmsCode查询redis仓库信息，warehouse:{}"
                    ,"根据wmsCode查询redis仓库信息"), JSON.toJSONString(warehouse));
        }
        if (warehouse == null) {
            ValueHolderV14<CpCPhyWarehouse> v14 = cpcPhyWareHouseQueryCmd.queryWarehouseByWmsWarehouseCode(wmsCode);
            if (v14.isOK() && v14.getData() != null) {
                warehouse = v14.getData();
                redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(warehouse));
                redisTemplate.expire(redisKey, 30L, TimeUnit.SECONDS);
            }
            log.info(LogUtil.format("根据wmsCode查询mysql仓库信息，warehouse:{}"
                    ,"根据wmsCode查询mysql仓库信息"),JSON.toJSONString(warehouse));
        }
        return warehouse;

    }
}
