package com.jackrain.nea.oc.oms.sap;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/3/23
 */
@Data
@Configuration
public class Oms2SapMqConfig {


    @Value("${r3.oms.order.sap.trans.mq.topic:}")
    private String oms2SapOrderTopic;


    @Value("${r3.oms.order.sap.trans.mq.tag:}")
    private String oms2SapOrderTag;

    @Value("${r3.oms.return.sap.trans.mq.topic:}")
    private String oms2SapReturnTopic;


    @Value("${r3.oms.return.sap.trans.mq.tag:}")
    private String oms2SapReturnTag;

    @Value("${r3.oms.refund.sap.trans.mq.topic:}")
    private String oms2SapRefundTopic;


    @Value("${r3.oms.refund.sap.trans.mq.tag:}")
    private String oms2SapRefundTag;


}
