package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCEquityBarterStrategyItem;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;
import java.util.Set;

/**
 * 对等换货策略明细
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-16 13:26:40
 */
@Mapper
public interface StCEquityBarterStrategyItemMapper extends ExtentionMapper<StCEquityBarterStrategyItem> {
    public class StCEquityBarterStrategyItemUpdateSql {
        public String update(final JSONObject jsonObject) {
            return new SQL() {
                {
                    UPDATE("st_c_equity_barter_strategy_item");
                    Set<String> keySet = jsonObject.keySet();
                    for (String key : keySet) {
                        SET(key + "=#{" + key + "}");
                    }
                    WHERE("ID=#{ID}");
                }
            }.toString();
        }

        public String insert(final JSONObject jsonObject) {
            return new SQL() {
                {
                    Set<String> keySet = jsonObject.keySet();
                    INSERT_INTO("st_c_equity_barter_strategy_item");
                    for (int i = 0; i < jsonObject.size(); i++) {
                        String key = (String) keySet.toArray()[i];
                        VALUES(key, "#{" + key + "}");
                    }
                }
            }.toString();
        }
    }

    @InsertProvider(type = StCEquityBarterStrategyItemUpdateSql.class, method = "insert")
    int insertStCEquityBarterStrategyItem(JSONObject jsonObject);

    /**
     * 更新
     *
     * @param jsonObject
     * @return
     */
    @UpdateProvider(type = StCEquityBarterStrategyItemUpdateSql.class, method = "update")
    int updateStCEquityBarterStrategyItem(JSONObject jsonObject);

    @Select("SELECT * FROM st_c_equity_barter_strategy_item WHERE ST_C_EQUITY_BARTER_STRATEGY_ID = #{mainId} and isactive = 'Y' ")
    List<StCEquityBarterStrategyItem> selectEquityBarterStrategyItemByMainId(@Param("mainId") Long mainId);


    @Select("SELECT * FROM st_c_equity_barter_strategy_item WHERE PS_C_SKU_CODE = #{skuCode} and isactive = 'Y'")
    List<StCEquityBarterStrategyItem> selectEquityBarterStrategyItemBySkuCode(@Param("skuCode") String skuCode);

    @Delete("DELETE FROM st_c_equity_barter_strategy_item WHERE ST_C_EQUITY_BARTER_STRATEGY_ID = #{mainId} and isactive = 'Y' ")
    int deleteEquityBarterStrategyItemByMainIds(@Param("mainId") Long mainId);

}
