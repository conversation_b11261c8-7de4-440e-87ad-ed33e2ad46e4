package com.jackrain.nea.oc.oms.services.returnin;

import com.google.common.base.Throwables;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.model.enums.IsToWmsEnum;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnInType;
import com.jackrain.nea.oc.oms.model.enums.ReturnOrderConfirmStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStockInMatchParam;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderActual;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.refund.util.NumUtil;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/9
 */
@Slf4j
@Component
public class OcRefundInMatchReturnService {

    @Autowired
    private OcReturnInSupport returnInService;

    @Autowired
    private OcReturnInCommService commService;

    /**
     * @param inRelation RefundInRelation
     * @return match result
     */
    public boolean normalMatchReturnProcessor(RefundInRelation inRelation) {
        logStep("normalMatchReturnProcessor.match.return.start...");
        OcBRefundIn refundIn = inRelation.getRefundIn();
        String sgBNoticeInBillNo = refundIn.getSgBNoticeInBillNo();
        if (StringUtils.isBlank(sgBNoticeInBillNo)) {
            logStep("in notice no is blank");
            return false;
        }
        Long returnId = ES4ReturnOrder.searchReturnOrderByNoticeInNo(sgBNoticeInBillNo);
        if (returnId == null) {
            logStep("es query null");
            return false;
        }
        try {
            OcReturnInRelation relation = matchReturnProcessing(returnId, inRelation);
            if (relation == null) {
                logStep("match.return null");
                return false;
            }
            OcBReturnOrder returnOrder = relation.getItem();
            Integer proReturnStatus = returnOrder.getProReturnStatus();
            boolean isFirstIn = ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal().equals(returnOrder.getReturnStatus())
                    || ProReturnStatusEnum.WAIT.getVal().equals(proReturnStatus);
            if (isFirstIn) {
                logStep("first in");
                return normalStockInProcessing(inRelation, relation);
            } else {
                logStep("multi in");
                return normalMultiStockInProcessing(inRelation, relation);
            }
        } catch (Exception e) {
            logStep(OcReturnInSupport.expMsgFun.apply(e));
        }
        logStep("normalMatchReturnProcessor.match.return.end...");
        return false;
    }

    /**
     * 退单匹配流程
     *
     * @param returnId   退单id
     * @param inRelation 入库结果单关系类
     * @return 退单关系类
     */
    public OcReturnInRelation matchReturnProcessing(Long returnId, RefundInRelation inRelation) {
        String lockRedisKey = BllRedisKeyResources.buildLockReturnOrderKey(returnId);
        boolean lockSate = returnInService.lockBil(lockRedisKey);
        logStep(lockRedisKey);
        AssertUtil.isTrue(lockSate, "Return Lock fail");
        OcBReturnOrder returnOrder = returnInService.getReturnOrder(returnId);
        AssertUtil.assertException(Objects.isNull(returnOrder), "Query Return Empty");
        ReturnInType returnInType = inRelation.getReturnInType();

        // 业务类型校验.无名件
        namelessCheckBusinessType(returnInType, returnOrder);
        AssertUtil.assertException(isInvalidReturn(returnOrder), "Return Invalid Status");

        List<OcBReturnOrderRefund> subItems = returnInService.getReturnItems(returnOrder.getId());
        AssertUtil.assertException(CollectionUtils.isEmpty(subItems), "Query ReturnItems Empty");
        OcReturnInRelation relation = normalMatchReturn(inRelation, returnOrder, subItems);
        AssertUtil.notNull(relation, "Match Return Result Null");
        return relation;
    }

    /**
     * 无名件匹配,特殊业务类型校验
     *
     * @param returnInType 入库类型
     * @param returnOrder  退货单
     */
    private void namelessCheckBusinessType(ReturnInType returnInType, OcBReturnOrder returnOrder) {
        if (!(ReturnInType.NAMELESS == returnInType)) {
            return;
        }
        String bilType = returnOrder.getReserveVarchar05();
        logStep("05_source_system=%s", bilType);
        Long origOrderId = returnOrder.getOrigOrderId();
        SPECIAL_BIZ_TYPE:
        if (Objects.nonNull(origOrderId)) {
            OcBOrder order = returnInService.getOrder(origOrderId);
            if (Objects.isNull(order)) {
                logStep("search.order.result.null.origId=%d", origOrderId);
                break SPECIAL_BIZ_TYPE;
            }
            // SAP免费订单-TOC	RYCK19,SAP员工内购订单-TOC	RYCK20
            String bizCode = order.getBusinessTypeCode();
            boolean isAllowedBizType = OrderBusinessTypeCodeEnum.SAP_FREE.getCode().equals(bizCode)
                    || OrderBusinessTypeCodeEnum.SAP_INSIDE.getCode().equals(bizCode);
            if (isAllowedBizType) {
                logStep("origOrder.biz=%s", bizCode);
                return;
            }
        }
//        boolean isB2b = false;
//        if (StringUtils.equalsIgnoreCase(B2B_PLAT_TAG, bilType) || StringUtils.equalsIgnoreCase(B2B_PLAT_TAG2, bilType)) {
//            isB2b = true;
//        }
//        AssertUtil.assertException(isB2b, "nl.cannot.match b2b bil");
    }

    /**
     * @param rtn Return Order
     * @return is legal
     */
    private boolean isInvalidReturn(OcBReturnOrder rtn) {
        boolean inValidReturnStatus = ReturnStatusEnum.CANCLE.getVal().equals(rtn.getReturnStatus());
        boolean wholeProReturnStatus = ProReturnStatusEnum.WHOLE.getVal().equals(rtn.getProReturnStatus());
        return inValidReturnStatus || wholeProReturnStatus;
    }

    /**
     * match detail
     *
     * @param inRelation  refund in relation
     * @param returnOrder return order
     * @param subItems    return order items
     * @return match in relation
     */
    private OcReturnInRelation normalMatchReturn(RefundInRelation inRelation,
                                                 OcBReturnOrder returnOrder, List<OcBReturnOrderRefund> subItems) {
        List<OcBRefundInProductItem> unMatchItems = inRelation.getUnMatchItems();
        Map<String, List<OcBRefundInProductItem>> inSkuMap =
                unMatchItems.stream().collect(Collectors.groupingBy(x -> inRelation.getSkuCode(x), Collectors.toList()));
        Set<Long> matchInKeys = new HashSet<>();
        OcReturnInRelation stockIn = new OcReturnInRelation();
        List<OcBRefundInProductItem> matchInItems = new ArrayList<>();
        List<OcBReturnOrderRefund> matchReturnItems = new ArrayList<>();
        for (OcBReturnOrderRefund subReturn : subItems) {
            BigDecimal qtyIn = NumUtil.init(subReturn.getQtyIn());
            BigDecimal qtyRefund = NumUtil.init(subReturn.getQtyRefund());
            int qtyMatch = NumUtil.toInt(subReturn.getQtyMatch());
            int canMatchQty = NumUtil.toInt(qtyRefund) - qtyMatch;
            if (canMatchQty < OcBOrderConst.IS_STATUS_IY) {
                continue;
            }
            String skuCode = subReturn.getPsCSkuEcode();
            List<OcBRefundInProductItem> subInItems = inSkuMap.get(skuCode);
            if (CollectionUtils.isEmpty(subInItems)) {
                continue;
            }
            BigDecimal inReturnQty = BigDecimal.ZERO;
            for (OcBRefundInProductItem inItem : subInItems) {
                Long id = inItem.getId();
                if (matchInKeys.contains(id)) {
                    continue;
                }
                BigDecimal qty = NumUtil.init(inItem.getQty());
                int inQty = qty.intValue();
                if (inQty < OcBOrderConst.IS_STATUS_IY) {
                    continue;
                }
                canMatchQty = canMatchQty - inQty;
                if (canMatchQty < OcBOrderConst.IS_STATUS_IN) {
                    break;
                }
                qtyMatch = qtyMatch + inQty;
                subReturn.setQtyMatch((long) qtyMatch);
                inReturnQty = inReturnQty.add(qty);
                matchInKeys.add(id);
                matchInItems.add(inItem);
                commService.branchInQty(qty, subReturn, inItem, stockIn);
            } // end of in
            if (NumUtil.gtZero(inReturnQty)) {
                matchReturnItems.add(subReturn);
                subReturn.setQtyIn(qtyIn.add(inReturnQty));
                stockIn.mappingQty(subReturn, inReturnQty);
            }
        }// end of return
        if (CollectionUtils.isNotEmpty(matchInItems)) {
            stockIn.setItem(returnOrder);
            stockIn.setSubItems(subItems);
            stockIn.setSubInMatchItems(matchInItems);
            stockIn.setSubMatchItems(matchReturnItems);
            stockIn.setCurrentMatchFinished(unMatchItems.size() == matchInItems.size());
            OcReturnInSupport.matchedReturn.set(stockIn);
            return stockIn;
        }
        return null;
    }

    /**
     * match return first
     *
     * @param inRelation
     * @param stockIn
     * @return
     */
    private boolean normalStockInProcessing(RefundInRelation inRelation, OcReturnInRelation stockIn) {
        OcBReturnOrder item = stockIn.getItem();
        // refund write return store info
        commService.preRefundInWriteReturn4StoreInfo(inRelation.getRefundIn(), item);
        // 重新计算
        commService.statisticsReturnBilStatusAndAmt(item, stockIn.getSubItems());
        // 更新
        OmsStockInMatchParam omsStockInMatchParam = assignInfo4UpdateGenInResultProgress(inRelation, stockIn);
        boolean isUpdate = generateStockInBilAndUpdateBills(omsStockInMatchParam);
        logStep("update data success");
        if (isUpdate) {
            List<OcBRefundInProductItem> subInMatchItems = stockIn.getSubInMatchItems();
            inRelation.popMatchedItem(subInMatchItems);
            logStep("pop");
        }
        return isUpdate;
    }

    /**
     * match return secondary
     *
     * @param inRelation
     * @param stockIn
     */
    private boolean normalMultiStockInProcessing(RefundInRelation inRelation, OcReturnInRelation stockIn) {

        User user = ThreadLocalUtil.users.get();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        OmsStockInMatchParam param = OmsStockInMatchParam.build(user);
        // 生成
        logStep("prepare new bil");
        OcBReturnOrder sourceReturn = stockIn.getItem();
        OcBReturnOrder newReturn = returnInService.generateBil4MultiInStock(sourceReturn);
        Long newReturnId = newReturn.getId();
        Long srcReturnId = sourceReturn.getId();
        BigDecimal amtActual = BigDecimal.ZERO;
        StringBuilder sb = new StringBuilder();
        Map<Long, BigDecimal> mapping = stockIn.getSubItemQtyMapping();
        List<OcBReturnOrderRefund> subItems = stockIn.getSubMatchItems();
        List<OcBReturnOrderRefund> newReturnItems = new ArrayList<>();
        Map<Long, Map<String, BigDecimal>> localMapping = stockIn.getSubItemMatchedMapping();
        AssertUtil.notEmpty(localMapping, "生成新退单明细,映射关系为空");
        for (OcBReturnOrderRefund subItem : subItems) {
            Long id = subItem.getId();
            BigDecimal qty = mapping.get(id);
            OcBReturnOrderRefund newReturnItem = returnInService.generateReturnItem4MultiInStock(subItem);
            commService.swapItemIdUseMapping(id, newReturnItem.getId(), localMapping);
            BigDecimal subtract = subItem.getQtyIn().subtract(qty);
            newReturnItem.setQtyIn(qty);
            BigDecimal amtRefund = commService.reCalcReturnItemAmtRefund(newReturnItem, subtract);
            newReturnItem.setQtyRefund(qty);
            amtActual = amtActual.add(amtRefund);
            newReturnItem.setAmtRefund(amtRefund);
            newReturnItem.setQtyMatch(qty.longValue());
            newReturnItem.setOcBReturnOrderId(newReturnId);
            newReturnItems.add(newReturnItem);
            OcBReturnOrderRefund updateReturnItem = commService.preUpdateReturnItem4FistMatch(subItem, false);
            updateReturnItem.setQtyIn(null);
            sb.append(",").append(subItem.getPsCSkuEcode());
            param.addModReturnItem(updateReturnItem);
        }
        newReturn.setShipAmt(BigDecimal.ZERO);
        newReturn.setExchangeAmt(BigDecimal.ZERO);
        newReturn.setReturnAmtOther(BigDecimal.ZERO);
        newReturn.setReturnAmtList(amtActual);
        newReturn.setReturnAmtActual(amtActual);
        newReturn.setProReturnStatus(ProReturnStatusEnum.WHOLE.getVal());
        newReturn.setConfirmStatus(ReturnOrderConfirmStatusEnum.NOT_CONFIRM.getKey());
        newReturn.setStoInNoticesId(null);
        newReturn.setStoInNoticesNo(null);
        newReturn.setIsNeedToWms(IsToWmsEnum.NO.val());
        commService.preUpdateReturn4FistMatch(newReturn, newReturn, refundIn);
        commService.assignReturnConfirmInfo(newReturn, newReturn, user);
        param.stockInParam(newReturn, newReturnItems);
        //  更新退
        commService.statisticsProReturnStatus(sourceReturn, stockIn.getSubItems());
        OcBReturnOrder updateReturn = new OcBReturnOrder();
        param.setModReturn(updateReturn);
        updateReturn.setId(srcReturnId);
        updateReturn.setProReturnStatus(sourceReturn.getProReturnStatus());

        // 新增先, 后更新二事务
        List<OcBRefundInProductItem> inItems = stockIn.getSubInMatchItems();
        OcBRefundIn newRefundIn = commService.preUpdateRefund4FistMatch(inRelation, newReturn, inItems);
        param.setModRefundIn(newRefundIn);
        //  refund item
        Map<String, BigDecimal> cMap = new HashMap<>();
        for (OcBRefundInProductItem inItem : inItems) {
            OcBRefundInProductItem item = commService.preUpdateRefundInItem4FistMatch(inItem, newReturnId);
            commService.statisticInQty(cMap, inItem.getPsCSkuEcode(), inItem.getQty());
            param.addModRefundItem(item);
            if (inRelation.isAdjust(inItem)) {
                inRelation.setHasAdjustedEle(true);
            }
        }
        if (inRelation.isHasAdjustedEle()) {
            newReturn.setInTime(new Date());
        }
        // 6. log
        logStep("prepare log bil");
        String inSkuMessage = sb.substring(1);
        String msg = String.format("匹配退单编号:%d 成功,新增入库退货单:%d, 匹配条码:%s", srcReturnId, newReturnId, inSkuMessage);
        OcBRefundInLog matchInLog = commService.buildRefundInLog("自动匹配退单", msg, refundIn.getId(), user);
        OcBRefundInLog inStockLog = commService.preInsertRefundLog4FistMatch(refundIn, cMap, user);
        param.addInsRefundLog(matchInLog).addInsRefundLog(inStockLog);
        // 7. log
        String content = String.format("入库结果单:%d入库完成, 入库条码:%s", refundIn.getId(), inSkuMessage);
        OcBReturnOrderLog returnLog1 = commService.buildReturnOderLog("退货单新增", "入库结果单:" + refundIn.getId() + "匹配新增退单:" + newReturnId, srcReturnId, user);
        OcBReturnOrderLog returnLog2 = commService.buildReturnOderLog("退货单新增", "匹配新增退单,来源退单:" + srcReturnId, newReturnId, user);
        OcBReturnOrderLog returnLog3 = commService.buildReturnOderLog("退货单入库", content, newReturnId, user);

        List<OcBReturnOrderActual> newActualItems = commService.generateActualItems(inRelation, newReturn, user);
        param.setInsActualItems(newActualItems);
        param.addInsReturnLog(returnLog1).addInsReturnLog(returnLog2).addInsReturnLog(returnLog3);
        logStep("update data start");
        try {
            commService.atomicReturnMultiGenInNoticeResultAndUpdate(param);
            List<OcBRefundInProductItem> subInMatchItems = stockIn.getSubInMatchItems();
            inRelation.popMatchedItem(subInMatchItems);
            commService.updateReturnOrderNoticeInfo(newReturn);
            return true;
        } catch (Exception ex) {
            logStep(OcReturnInSupport.expMsgFun.apply(ex));
            log.error(LogUtil.format("匹配退单. 更新数据异常:{}"), Throwables.getStackTraceAsString(ex));
        }
        logStep("update data end");
        return false;
    }

    /**
     * 第一次更新相关信息, 第二次不更新, 二阶段更新. 暂不分离
     *
     * @param inRelation RefundInRelation
     * @param stockIn    OcReturnInRelation
     */
    private OmsStockInMatchParam assignInfo4UpdateGenInResultProgress(RefundInRelation inRelation, OcReturnInRelation stockIn) {

        // 0. common
        OcBReturnOrder returnBil = stockIn.getItem();
        OcBRefundIn refundIn = inRelation.getRefundIn();
        List<OcBRefundInProductItem> inItems = stockIn.getSubInMatchItems();
        List<OcBReturnOrderRefund> returnItems = stockIn.getSubMatchItems();
        User user = ThreadLocalUtil.users.get();
        OmsStockInMatchParam param = OmsStockInMatchParam.build(user);
        // 1. refund
        OcBRefundIn newRefundIn = commService.preUpdateRefund4FistMatch(inRelation, returnBil, inItems);
        param.setModRefundIn(newRefundIn);
        // 2. refund item
        Map<String, BigDecimal> cMap = new HashMap<>();
        for (OcBRefundInProductItem inItem : inItems) {
            OcBRefundInProductItem item = commService.preUpdateRefundInItem4FistMatch(inItem, returnBil.getId());
            param.addModRefundItem(item);
            commService.statisticInQty(cMap, inItem.getPsCSkuEcode(), inItem.getQty());
            if (inRelation.isAdjust(inItem)) {
                inRelation.setHasAdjustedEle(true);
            }
        }

        // 3. return
        OcBReturnOrder newReturn = new OcBReturnOrder();
        commService.preUpdateReturn4FistMatch(returnBil, newReturn, refundIn);
        if (inRelation.isHasAdjustedEle()) {
            newReturn.setInTime(new Date());
            returnBil.setInTime(newReturn.getInTime());
        }
        param.setModReturn(newReturn);
        // 4. return refund item
        StringBuilder sb = new StringBuilder();
        for (OcBReturnOrderRefund returnItem : returnItems) {
            OcBReturnOrderRefund returnRefund = commService.preUpdateReturnItem4FistMatch(returnItem, true);
            param.addModReturnItem(returnRefund);
            sb.append(",").append(returnItem.getPsCSkuEcode());
        }
        // 5. refund actual item
        List<OcBReturnOrderActual> newActualItems = commService.generateActualItems(inRelation, newReturn, user);
        param.setInsActualItems(newActualItems);
        // 6. log
        String inSkuMessage = sb.substring(1);
        String msg = String.format("匹配退单编号:%d 成功, 匹配条码:%s", newReturn.getId(), inSkuMessage);
        OcBRefundInLog matchInLog = commService.buildRefundInLog("自动匹配退单", msg, refundIn.getId(), user);
        OcBRefundInLog inStockLog = commService.preInsertRefundLog4FistMatch(refundIn, cMap, user);
        param.addInsRefundLog(matchInLog);
        param.addInsRefundLog(inStockLog);
        // 7. log
        String content = String.format("入库结果单[%d]入库完成, 入库条码:%s", refundIn.getId(), inSkuMessage);
        OcBReturnOrderLog returnLog = commService.buildReturnOderLog("退货单入库", content, newReturn.getId(), user);
        param.addInsReturnLog(returnLog);
        return param.stockInParam(returnBil, returnItems);

    }

    /**
     * generate bill, update bill
     *
     * @param inParam
     * @return
     */
    private boolean generateStockInBilAndUpdateBills(OmsStockInMatchParam inParam) {
        try {
            commService.atomicReturnFirstGenInResultAndUpdate(inParam);
            return true;
        } catch (Exception ex) {
            String apply = OcReturnInSupport.expMsgFun.apply(ex);
            logStep(apply);
            log.error(LogUtil.format("匹配退单.更新数据,调用库存流程异常:{}"), Throwables.getStackTraceAsString(ex));
        }
        return false;
    }

    private void logStep(String express, Object... obs) {
        ThreadLocalUtil.logStepMsg.get().add(String.format(express, obs));
    }

}
