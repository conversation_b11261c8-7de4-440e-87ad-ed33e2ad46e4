package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * description：
 *
 * <AUTHOR>
 * @date 2021/10/18
 */
public enum VipJitxResetShipReasonEnum {

    OUT_STOCK("1001", "原仓缺货"),
    SYSTEM_MISJUDGMENT("1002", "系统错判"),
    ADD_WAREHOUSE("1003", "新仓发货");

    VipJitxResetShipReasonEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private String code;

    @Getter
    private String name;

    public static String getNameByCode(String code) {
        for (VipJitxResetShipReasonEnum select : VipJitxResetShipReasonEnum.values()) {
            if (select.code.equals(code)) {
                return select.name;
            }
        }
        return "";
    }
}


