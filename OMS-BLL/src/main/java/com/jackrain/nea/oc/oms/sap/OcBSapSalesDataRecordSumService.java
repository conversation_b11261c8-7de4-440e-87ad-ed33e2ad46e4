package com.jackrain.nea.oc.oms.sap;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.constant.SapSalesDateConstant;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherSourceItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordMapper;
import com.jackrain.nea.oc.oms.model.OcBSapSalesDataRecordItemRequest;
import com.jackrain.nea.oc.oms.model.constant.OcBSapSalesDataRecordConstant;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGather;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGatherItem;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecord;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OmsStorageUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Description: 汇总销售数据记录表  将数据推送到销售数据汇总表
 *
 * @Author: guo.kw
 * @Since: 2022/8/29
 * create at: 2022/8/29 10:40
 */
@Slf4j
@Component
public class OcBSapSalesDataRecordSumService {

    @Autowired
    private OcBSapSalesDataRecordMapper ocBSapSalesDataRecordMapper;
    @Autowired
    private OcBSapSalesDataRecordItemMapper ocBSapSalesDataRecordItemMapper;
    @Autowired
    private OcBSapSalesDataGatherMapper ocBSapSalesDataGatherMapper;
    @Autowired
    private OcBSapSalesDataGatherItemMapper ocBSapSalesDataGatherItemMapper;
    @Autowired
    private OcBSapSalesDataGatherSourceItemMapper ocBSapSalesDataGatherSourceItemMapper;
    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

    //销售汇总
    private static final String SUM_TYPE00 = "0";

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 execute(List<OcBSapSalesDataRecord> ocBSapSalesDataRecords) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "销售数据汇总定时任务成功！");
        log.info(LogUtil.format("OcBSapSalesDataRecordSumService.execute start", "OcBSapSalesDataRecordSumService.execute "));
        if (CollectionUtils.isEmpty(ocBSapSalesDataRecords)) {
            return v14;
        }
        //分页查询明细信息
        List<Long> mainIds = ocBSapSalesDataRecords.stream()
                .map(OcBSapSalesDataRecord::getId)
                .collect(Collectors.toList());
        List<List<Long>> partitionIds = Lists.partition(mainIds, SapSalesDateConstant.QUERY_SIZE_FIVE);
        List<OcBSapSalesDataRecordItemRequest> ocBSapSalesDataRecordItemRequests = new ArrayList<>();
        for (List<Long> ids : partitionIds) {
            ocBSapSalesDataRecordItemRequests.addAll(ocBSapSalesDataRecordItemMapper.batchSelectItem(ids));
        }
        Map<String, List<OcBSapSalesDataRecordItemRequest>> ocBSapSalesDataRecordItemsMap = ocBSapSalesDataRecordItemRequests.stream()
                .collect(Collectors.groupingBy(e -> e.getMergeCode() + e.getSumType()));

        User rootUser = SystemUserResource.getRootUser();

        buildParam(ocBSapSalesDataRecords, ocBSapSalesDataRecordItemsMap, rootUser);

        //更新状态为已汇总
        OcBSapSalesDataRecord ocBSapSalesDataRecord = new OcBSapSalesDataRecord();
        ocBSapSalesDataRecord.setSumStatus(SapSalesDateConstant.SUM_STATUS_SUMMARIZED);
        OmsStorageUtils.setBModelDefalutDataByUpdate(ocBSapSalesDataRecord, rootUser);
        ocBSapSalesDataRecordMapper.update(ocBSapSalesDataRecord, new LambdaQueryWrapper<OcBSapSalesDataRecord>()
                .in(OcBSapSalesDataRecord::getId, mainIds));
        log.info(LogUtil.format("OcBSapSalesDataRecordSumService.ValueHolderV14 end ValueHolderV14:{}",
                "OcBSapSalesDataRecordSumService.ValueHolderV14 "), JSONObject.toJSONString(v14));
        return v14;
    }

    public void buildParam(List<OcBSapSalesDataRecord> ocBSapSalesDataRecords,
                           Map<String, List<OcBSapSalesDataRecordItemRequest>> ocBSapSalesDataRecordItemsMap,
                           User rootUser) {
        Map<String, List<OcBSapSalesDataRecord>> listMap = ocBSapSalesDataRecords.stream()
                .filter(e -> StringUtils.isNotBlank(e.getMergeCode()))
                .collect(Collectors.groupingBy(e -> e.getMergeCode() + e.getSumType()));

        List<String> mergeCodeList = ocBSapSalesDataRecords.stream().map(OcBSapSalesDataRecord::getMergeCode).distinct().collect(Collectors.toList());
        List<OcBSapSalesDataGather> gatherList = ocBSapSalesDataGatherMapper.selectSalesDataGatherByMergeCode(mergeCodeList);
        List<Long> gatherId = gatherList.stream().map(OcBSapSalesDataGather::getId).collect(Collectors.toList());
        //根据合并码和汇总类型分组销售汇总数据
        Map<String, List<OcBSapSalesDataGather>> gatherMap = gatherList.stream()
                .collect(Collectors.groupingBy(e -> e.getMergeCode() + e.getSumType()));

        if (!org.springframework.util.CollectionUtils.isEmpty(listMap)) {
            for (String key : listMap.keySet()) {
                List<OcBSapSalesDataRecord> ocBSapSalesDataRecordList = listMap.get(key);
                //获取销售记录表ID
                List<Long> recordId = ocBSapSalesDataRecordList.stream().map(OcBSapSalesDataRecord::getId).collect(Collectors.toList());
                log.info(LogUtil.format("OcBSapSalesDataRecordSumService.buildParam.recordId:{}",
                        "recordId"), recordId);
                OcBSapSalesDataRecord ocBSapSalesDataRecord = ocBSapSalesDataRecordList.get(0);
                Long objId = ModelUtil.getSequence(SapSalesDateConstant.OC_B_SAP_SALES_DATA_GATHER);
                //明细
                List<OcBSapSalesDataRecordItemRequest> ocBSapSalesDataRecordItemRequests = ocBSapSalesDataRecordItemsMap.get(key);
                Map<String, List<OcBSapSalesDataRecordItemRequest>> listMap1 = new HashMap<>();
                if (OcBSapSalesDataRecordConstant.SUM_TYPE_WMJ.equals(ocBSapSalesDataRecord.getSumType())
                        || OcBSapSalesDataRecordConstant.SUM_TYPE_CWMJ.equals(ocBSapSalesDataRecord.getSumType())
                        || OcBSapSalesDataRecordConstant.SUM_TYPE_BCTZ.equals(ocBSapSalesDataRecord.getSumType())) {
                    listMap1 = ocBSapSalesDataRecordItemRequests.stream()
                            .collect(Collectors.groupingBy(OcBSapSalesDataRecordItemRequest::getSku));
                } else if(OcBSapSalesDataRecordConstant.SUM_TYPE_NK.equals(ocBSapSalesDataRecord.getSumType())){
                    listMap1 = ocBSapSalesDataRecordItemRequests.stream()
                            .collect(Collectors.groupingBy(e -> e.getLineCategory() + e.getProType() + e.getSku() + e.getCpCStoreId() + e.getCycleQty()));
                } else {
                    if (OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER.getCode().equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeCode())
                            || OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER_RETURN.getCode().equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeCode())) {
                        listMap1 = ocBSapSalesDataRecordItemRequests.stream()
                                .collect(Collectors.groupingBy(e -> e.getLineCategory() + e.getProType() + e.getSku() +
                                        e.getCpCStoreId() + (e.getAmt().compareTo(BigDecimal.ZERO) == 0 ? "N" : "Y")));
                    } else {
                        listMap1 = ocBSapSalesDataRecordItemRequests.stream()
                                .collect(Collectors.groupingBy(e -> e.getLineCategory() + e.getProType() + e.getSku() + e.getCpCStoreId()));
                    }
                }
                if (org.springframework.util.CollectionUtils.isEmpty(gatherMap) || gatherMap.get(key) == null || gatherMap.get(key).get(0) == null) {
                    //新增
                    insertGatherInfo(ocBSapSalesDataRecord, listMap1, rootUser, objId);
                } else {
                    OcBSapSalesDataGather ocBSapSalesDataGather = gatherMap.get(key).get(0);
                    objId = ocBSapSalesDataGather.getId();
                    List<OcBSapSalesDataGatherItem> gatherItems = ocBSapSalesDataGatherItemMapper.selectList(new LambdaQueryWrapper<OcBSapSalesDataGatherItem>()
                            .in(OcBSapSalesDataGatherItem::getOcBSapSalesDataGatherId, gatherId));

                    Map<String, List<OcBSapSalesDataGatherItem>> gatherAdjustItemMap = gatherItems.stream()
                            .collect(Collectors.groupingBy(e -> e.getSku() + e.getOcBSapSalesDataGatherId()));

                    Map<String, List<OcBSapSalesDataGatherItem>> gatherOrderItemMap = gatherItems.stream()
                            .collect(Collectors.groupingBy(e -> e.getLineCategory() + e.getProType() + e.getSku() + e.getCpCStoreId() + e.getOcBSapSalesDataGatherId()));

                    Map<String, List<OcBSapSalesDataGatherItem>> gatherOrderItemByAmtMap = gatherItems.stream()
                            .collect(Collectors.groupingBy(e -> e.getLineCategory() + e.getProType() + e.getSku() +
                                    e.getCpCStoreId() + (e.getAmt().compareTo(BigDecimal.ZERO) == 0 ? "N" : "Y") + e.getOcBSapSalesDataGatherId()));

                    Map<String, List<OcBSapSalesDataGatherItem>> gatherNaiKaOrderItemMap = gatherItems.stream()
                            .collect(Collectors.groupingBy(e -> e.getLineCategory() + e.getProType() + e.getSku() + e.getCpCStoreId() + e.getCycleQty() + e.getOcBSapSalesDataGatherId()));
                    //修改
                    updateGatherInfo(ocBSapSalesDataRecord, listMap1, rootUser, ocBSapSalesDataGather.getId(), gatherAdjustItemMap, gatherOrderItemMap, gatherNaiKaOrderItemMap, gatherOrderItemByAmtMap);
                }
                //回写记录表汇总单号
                OcBSapSalesDataRecord ocBSapSalesDataRecord2 = new OcBSapSalesDataRecord();
                ocBSapSalesDataRecord2.setOcBSapSalesDataGatherId(objId);
                ocBSapSalesDataRecordMapper.update(ocBSapSalesDataRecord2, new LambdaQueryWrapper<OcBSapSalesDataRecord>().in(OcBSapSalesDataRecord::getId, recordId).eq(OcBSapSalesDataRecord::getIsactive, "Y"));
            }
        }
    }


    public void insertGatherInfo(OcBSapSalesDataRecord ocBSapSalesDataRecord, Map<String, List<OcBSapSalesDataRecordItemRequest>> listMap, User rootUser, Long objId) {
        log.info(LogUtil.format("OcBSapSalesDataRecordSumService.insertGatherInfo.objId:{}",
                "insertGatherInfo"), objId);
        OcBSapSalesDataGather ocBSapSalesDataGather = new OcBSapSalesDataGather();
        List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherItems = new ArrayList<>();
        ocBSapSalesDataGather.setId(objId);
        //获取单据编号
        String billNo = getBillNo(ocBSapSalesDataRecord.getInTime(),ocBSapSalesDataRecord.getSumType(), ocBSapSalesDataRecord.getBillType(),
                ocBSapSalesDataRecord.getCpCShopEcode(), ocBSapSalesDataRecord.getMiddlegroundBillTypeCode() == null ? "" : ocBSapSalesDataRecord.getMiddlegroundBillTypeCode());
        ocBSapSalesDataGather.setGatherNo(billNo);
        //合并码
        ocBSapSalesDataGather.setMergeCode(ocBSapSalesDataRecord.getMergeCode());
        //出入库时间
        ocBSapSalesDataGather.setInTime(ocBSapSalesDataRecord.getInTime());
        //sap单据类型
        ocBSapSalesDataGather.setSapBillType(ocBSapSalesDataRecord.getSapBillType());
        //销售组织
        ocBSapSalesDataGather.setSalesOrganization(ocBSapSalesDataRecord.getSalesOrganization());
        //店铺
        ocBSapSalesDataGather.setCpCShopId(ocBSapSalesDataRecord.getCpCShopId() == null ? null : Long.valueOf(ocBSapSalesDataRecord.getCpCShopId()));
        //成本中心
        ocBSapSalesDataGather.setCostCenter(ocBSapSalesDataRecord.getCostCenter());
        //汇总时间
        ocBSapSalesDataGather.setGatherDate(new Date());
        //汇总类型
        ocBSapSalesDataGather.setSumType(ocBSapSalesDataRecord.getSumType());
        //传sap状态
        ocBSapSalesDataGather.setToSapStatus(SapSalesDateConstant.TO_SAP_STATUS_INIT);
        //传sap次数
        ocBSapSalesDataGather.setToSapFailNum(SapSalesDateConstant.TO_SAP_FAIL_NUMBER);
        //汇总中间状态
        ocBSapSalesDataGather.setGatherMiddleStatus(SapSalesDateConstant.GATHER_MIDDLE_STATUS_01);
        OmsStorageUtils.setBModelDefalutData(ocBSapSalesDataGather, rootUser);
        for (String next : listMap.keySet()) {
            Long itemId = ModelUtil.getSequence(SapSalesDateConstant.OC_B_SAP_SALES_DATA_GATHER_ITEM);
            OcBSapSalesDataGatherItem ocBSapSalesDataGatherItem = new OcBSapSalesDataGatherItem();
            List<OcBSapSalesDataRecordItemRequest> ocBSapSalesDataRecordItemRequests1 = listMap.get(next);
            OcBSapSalesDataRecordItemRequest ocBSapSalesDataRecordItemRequest = ocBSapSalesDataRecordItemRequests1.get(0);

            ocBSapSalesDataGatherItem.setId(itemId);
            ocBSapSalesDataGatherItem.setOcBSapSalesDataGatherId(objId);
            //sku
            ocBSapSalesDataGatherItem.setSku(ocBSapSalesDataRecordItemRequest.getSku());

            int sumQty = ocBSapSalesDataRecordItemRequests1.stream().mapToInt(OcBSapSalesDataRecordItemRequest::getQty).sum();
            //计划行类别
            ocBSapSalesDataGatherItem.setLineCategory(ocBSapSalesDataRecordItemRequest.getLineCategory());
            //汇总数量
            ocBSapSalesDataGatherItem.setQty((long) sumQty);
            //工厂编码
            ocBSapSalesDataGatherItem.setFactoryCode(ocBSapSalesDataRecordItemRequest.getFactoryCode());
            //仓
            ocBSapSalesDataGatherItem.setCpCStoreId(ocBSapSalesDataRecordItemRequest.getCpCStoreId() == null ? null : (long) ocBSapSalesDataRecordItemRequest.getCpCStoreId());
            //商品类型
            ocBSapSalesDataGatherItem.setProType(ocBSapSalesDataRecordItemRequest.getProType());
            //单位
            ocBSapSalesDataGatherItem.setUnit(ocBSapSalesDataRecordItemRequest.getUnit());
            //周期购提数
            ocBSapSalesDataGatherItem.setCycleQty(ocBSapSalesDataRecordItemRequest.getCycleQty());
            //剩余提数
            Long residueQtyAll = salesResidueQtyItem(ocBSapSalesDataRecordItemRequests1);
            if (residueQtyAll != null){
                ocBSapSalesDataGatherItem.setResidueQty(residueQtyAll);
            }
            //成本
            BigDecimal priceCost = priceCostItem(ocBSapSalesDataRecordItemRequests1);
            if (priceCost != null) {
                ocBSapSalesDataGatherItem.setPriceCost(priceCost);
            }
            //汇总金额
            BigDecimal reduceAmt = ocBSapSalesDataRecordItemRequests1.stream().filter(e -> e.getAmt() != null).map(OcBSapSalesDataRecordItemRequest::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            ocBSapSalesDataGatherItem.setAmt(reduceAmt);
            //行项目类型
            ocBSapSalesDataGatherItem.setLineType(ocBSapSalesDataRecordItemRequest.getLineType());
            OmsStorageUtils.setBModelDefalutData(ocBSapSalesDataGatherItem, rootUser);
            ocBSapSalesDataGatherItems.add(ocBSapSalesDataGatherItem);
        }
        saveObject(ocBSapSalesDataGather, ocBSapSalesDataGatherItems);
    }

    /**
     * 成本
     *
     * @param recordItemRequests
     * @return
     */
    private BigDecimal priceCostItem(List<OcBSapSalesDataRecordItemRequest> recordItemRequests) {
        BigDecimal priceCostList = null;
        for (OcBSapSalesDataRecordItemRequest itemRequest : recordItemRequests) {
            BigDecimal priceCost = itemRequest.getPriceCost();
            if (priceCost == null) {
                continue;
            }
            if (priceCostList == null) {
                priceCostList = BigDecimal.ZERO;
                priceCostList = priceCostList.add(priceCost);
            } else {
                priceCostList = priceCostList.add(priceCost);
            }
        }
        return priceCostList;
    }

    private Long salesResidueQtyItem(List<OcBSapSalesDataRecordItemRequest>  recordItemRequests) {
        Long residueQtyAll = null;
        for (OcBSapSalesDataRecordItemRequest itemRequest : recordItemRequests) {
            Long residueQty = itemRequest.getResidueQty();
            if (residueQty != null) {
                if (residueQtyAll == null) {
                    residueQtyAll = 0L;
                    residueQtyAll = residueQtyAll + residueQty;
                } else {
                    residueQtyAll = residueQtyAll + residueQty;
                }
            }
        }
        return residueQtyAll;
    }

    public void updateGatherInfo(OcBSapSalesDataRecord ocBSapSalesDataRecord, Map<String, List<OcBSapSalesDataRecordItemRequest>> listMap, User rootUser, Long objId,
                                 Map<String, List<OcBSapSalesDataGatherItem>> gatherAdjustMap, Map<String, List<OcBSapSalesDataGatherItem>> gatherOrderMap, Map<String,
            List<OcBSapSalesDataGatherItem>> gatherNaiKaOrderMap, Map<String, List<OcBSapSalesDataGatherItem>> gatherOrderItemByAmtMap) {
        log.info(LogUtil.format("OcBSapSalesDataRecordSumService.updateGatherInfo.objId:{}",
                "updateGatherInfo"), objId);
        List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherItems = new ArrayList<>();
        for (String next : listMap.keySet()) {
            Long itemId = ModelUtil.getSequence(SapSalesDateConstant.OC_B_SAP_SALES_DATA_GATHER_ITEM);
            List<OcBSapSalesDataRecordItemRequest> ocBSapSalesDataRecordItemRequests1 = listMap.get(next);
            OcBSapSalesDataRecordItemRequest ocBSapSalesDataRecordItemRequest = ocBSapSalesDataRecordItemRequests1.get(0);
            //数量
            int sumQty = ocBSapSalesDataRecordItemRequests1.stream().mapToInt(OcBSapSalesDataRecordItemRequest::getQty).sum();
            Long residueQtyAll = salesResidueQtyItem(ocBSapSalesDataRecordItemRequests1);
            //汇总金额
            BigDecimal reduceAmt = ocBSapSalesDataRecordItemRequests1.stream().filter(e -> e.getAmt() != null).map(OcBSapSalesDataRecordItemRequest::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);

            List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherAdjustItems = gatherAdjustMap.get(next + objId);
            List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherOrderItems = gatherOrderMap.get(next + objId);
            List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherNaikaOrderItems = gatherNaiKaOrderMap.get(next + objId);
            List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherOrderByAmtItems = gatherOrderItemByAmtMap.get(next + objId);

            OcBSapSalesDataGatherItem updateItem = new OcBSapSalesDataGatherItem();
            if (OcBSapSalesDataRecordConstant.SUM_TYPE_WMJ.equals(ocBSapSalesDataRecord.getSumType())
                    || OcBSapSalesDataRecordConstant.SUM_TYPE_CWMJ.equals(ocBSapSalesDataRecord.getSumType())
                    || OcBSapSalesDataRecordConstant.SUM_TYPE_BCTZ.equals(ocBSapSalesDataRecord.getSumType())) {
                if (CollectionUtils.isNotEmpty(ocBSapSalesDataGatherAdjustItems)) {
                    OcBSapSalesDataGatherItem ocBSapSalesDataGatherItem = ocBSapSalesDataGatherAdjustItems.get(0);
                    int qty = ocBSapSalesDataGatherItem.getQty().intValue() + sumQty;
                    BigDecimal adjustAmt = ocBSapSalesDataGatherItem.getAmt() == null ? BigDecimal.ZERO : ocBSapSalesDataGatherItem.getAmt();
                    BigDecimal amt = adjustAmt.add(reduceAmt);
                    updateSaleItemDate(updateItem, amt, qty, null, null);
                    ocBSapSalesDataGatherItemMapper.update(updateItem, new LambdaQueryWrapper<OcBSapSalesDataGatherItem>()
                            .eq(OcBSapSalesDataGatherItem::getSku, ocBSapSalesDataRecordItemRequest.getSku())
                            .eq(OcBSapSalesDataGatherItem::getOcBSapSalesDataGatherId, objId));
                    continue;
                }
            } else if(OcBSapSalesDataRecordConstant.SUM_TYPE_NK.equals(ocBSapSalesDataRecord.getSumType())){
                if (CollectionUtils.isNotEmpty(ocBSapSalesDataGatherNaikaOrderItems)) {
                    OcBSapSalesDataGatherItem ocBSapSalesDataGatherItem = ocBSapSalesDataGatherNaikaOrderItems.get(0);
                    int qty = ocBSapSalesDataGatherItem.getQty().intValue() + sumQty;

                    //剩余提数
                    Integer reduceQty = null;
                    if (ocBSapSalesDataGatherItem.getResidueQty() != null) {
                        if (reduceQty == null) {
                            reduceQty = 0;
                        }
                        reduceQty = reduceQty + ocBSapSalesDataGatherItem.getResidueQty().intValue();
                    }
                    if (residueQtyAll != null) {
                        if (reduceQty == null) {
                            reduceQty = 0;
                        }
                        reduceQty = reduceQty + residueQtyAll.intValue();
                    }

                    //成本
                    BigDecimal priceCostCallAll = getPriceCostCallAll(ocBSapSalesDataRecordItemRequests1, ocBSapSalesDataGatherNaikaOrderItems);

                    BigDecimal adjustAmt = ocBSapSalesDataGatherItem.getAmt() == null ? BigDecimal.ZERO : ocBSapSalesDataGatherItem.getAmt();
                    BigDecimal amt = adjustAmt.add(reduceAmt);
                    updateSaleItemDate(updateItem, amt, qty, reduceQty, priceCostCallAll);
                    ocBSapSalesDataGatherItemMapper.update(updateItem, new LambdaQueryWrapper<OcBSapSalesDataGatherItem>()
                            .eq(OcBSapSalesDataGatherItem::getSku, ocBSapSalesDataRecordItemRequest.getSku())
                            .eq(ocBSapSalesDataRecordItemRequest.getLineCategory() != null, OcBSapSalesDataGatherItem::getLineCategory, ocBSapSalesDataRecordItemRequest.getLineCategory())
                            .eq(OcBSapSalesDataGatherItem::getProType, ocBSapSalesDataRecordItemRequest.getProType())
                            .isNull(ocBSapSalesDataRecordItemRequest.getLineCategory() == null, OcBSapSalesDataGatherItem::getLineCategory)
                            .eq(ocBSapSalesDataRecordItemRequest.getCpCStoreId() != null, OcBSapSalesDataGatherItem::getCpCStoreId, ocBSapSalesDataRecordItemRequest.getCpCStoreId())
                            .isNull(ocBSapSalesDataRecordItemRequest.getCpCStoreId() == null, OcBSapSalesDataGatherItem::getCpCStoreId)
                            .eq(ocBSapSalesDataRecordItemRequest.getCycleQty() != null, OcBSapSalesDataGatherItem::getCycleQty, ocBSapSalesDataRecordItemRequest.getCycleQty())
                            .isNull(ocBSapSalesDataRecordItemRequest.getCycleQty() == null, OcBSapSalesDataGatherItem::getCycleQty)
                            .eq(OcBSapSalesDataGatherItem::getOcBSapSalesDataGatherId, objId));
                    continue;
                }
            } else {
                if (OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER.getCode().equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeCode())
                        || OrderBusinessTypeCodeEnum.E_COMMERCE_SALE_ORDER_RETURN.getCode().equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeCode())) {
                    if (CollectionUtils.isNotEmpty(ocBSapSalesDataGatherOrderByAmtItems)) {
                        OcBSapSalesDataGatherItem ocBSapSalesDataGatherItem = ocBSapSalesDataGatherOrderByAmtItems.get(0);
                        int qty = ocBSapSalesDataGatherItem.getQty().intValue() + sumQty;
                        BigDecimal adjustAmt = ocBSapSalesDataGatherItem.getAmt() == null ? BigDecimal.ZERO : ocBSapSalesDataGatherItem.getAmt();
                        BigDecimal amt = adjustAmt.add(reduceAmt);
                        updateSaleItemDate(updateItem, amt, qty, null, null);
                        ocBSapSalesDataGatherItemMapper.update(updateItem, new LambdaQueryWrapper<OcBSapSalesDataGatherItem>()
                                .eq(OcBSapSalesDataGatherItem::getSku, ocBSapSalesDataRecordItemRequest.getSku())
                                .eq(ocBSapSalesDataRecordItemRequest.getLineCategory() != null, OcBSapSalesDataGatherItem::getLineCategory, ocBSapSalesDataRecordItemRequest.getLineCategory())
                                .eq(OcBSapSalesDataGatherItem::getProType, ocBSapSalesDataRecordItemRequest.getProType())
                                .isNull(ocBSapSalesDataRecordItemRequest.getLineCategory() == null, OcBSapSalesDataGatherItem::getLineCategory)
                                .eq(ocBSapSalesDataRecordItemRequest.getCpCStoreId() != null, OcBSapSalesDataGatherItem::getCpCStoreId, ocBSapSalesDataRecordItemRequest.getCpCStoreId())
                                .isNull(ocBSapSalesDataRecordItemRequest.getCpCStoreId() == null, OcBSapSalesDataGatherItem::getCpCStoreId)
                                .eq(amt.compareTo(BigDecimal.ZERO) == 0, OcBSapSalesDataGatherItem::getAmt, BigDecimal.ZERO)
                                .ne(amt.compareTo(BigDecimal.ZERO) != 0, OcBSapSalesDataGatherItem::getAmt, BigDecimal.ZERO)
                                .eq(OcBSapSalesDataGatherItem::getOcBSapSalesDataGatherId, objId));
                        continue;
                    }
                } else {
                    if (CollectionUtils.isNotEmpty(ocBSapSalesDataGatherOrderItems)) {
                        OcBSapSalesDataGatherItem ocBSapSalesDataGatherItem = ocBSapSalesDataGatherOrderItems.get(0);
                        int qty = ocBSapSalesDataGatherItem.getQty().intValue() + sumQty;
                        BigDecimal adjustAmt = ocBSapSalesDataGatherItem.getAmt() == null ? BigDecimal.ZERO : ocBSapSalesDataGatherItem.getAmt();
                        BigDecimal amt = adjustAmt.add(reduceAmt);
                        updateSaleItemDate(updateItem, amt, qty, null, null);
                        ocBSapSalesDataGatherItemMapper.update(updateItem, new LambdaQueryWrapper<OcBSapSalesDataGatherItem>()
                                .eq(OcBSapSalesDataGatherItem::getSku, ocBSapSalesDataRecordItemRequest.getSku())
                                .eq(ocBSapSalesDataRecordItemRequest.getLineCategory() != null, OcBSapSalesDataGatherItem::getLineCategory, ocBSapSalesDataRecordItemRequest.getLineCategory())
                                .eq(OcBSapSalesDataGatherItem::getProType, ocBSapSalesDataRecordItemRequest.getProType())
                                .isNull(ocBSapSalesDataRecordItemRequest.getLineCategory() == null, OcBSapSalesDataGatherItem::getLineCategory)
                                .eq(ocBSapSalesDataRecordItemRequest.getCpCStoreId() != null, OcBSapSalesDataGatherItem::getCpCStoreId, ocBSapSalesDataRecordItemRequest.getCpCStoreId())
                                .isNull(ocBSapSalesDataRecordItemRequest.getCpCStoreId() == null, OcBSapSalesDataGatherItem::getCpCStoreId)
                                .eq(OcBSapSalesDataGatherItem::getOcBSapSalesDataGatherId, objId));
                        continue;
                    }
                }
            }
            OcBSapSalesDataGatherItem insertItem = new OcBSapSalesDataGatherItem();
            insertItem.setId(itemId);
            insertItem.setOcBSapSalesDataGatherId(objId);
            //sku
            insertItem.setSku(ocBSapSalesDataRecordItemRequest.getSku());
            //计划行类别
            insertItem.setLineCategory(ocBSapSalesDataRecordItemRequest.getLineCategory());
            //汇总数量
            insertItem.setQty((long) sumQty);
            //工厂编码
            insertItem.setFactoryCode(ocBSapSalesDataRecordItemRequest.getFactoryCode());
            //仓
            insertItem.setCpCStoreId(ocBSapSalesDataRecordItemRequest.getCpCStoreId() == null ? null : (long) ocBSapSalesDataRecordItemRequest.getCpCStoreId());
            //商品类型
            insertItem.setProType(ocBSapSalesDataRecordItemRequest.getProType());
            //周期购提数
            insertItem.setCycleQty(ocBSapSalesDataRecordItemRequest.getCycleQty());
            //剩余提数
            Long residueQtyAll2 = salesResidueQtyItem(ocBSapSalesDataRecordItemRequests1);
            if (residueQtyAll2 != null){
                insertItem.setResidueQty(residueQtyAll2);
            }
            //成本
            BigDecimal priceCost = priceCostItem(ocBSapSalesDataRecordItemRequests1);
            if (priceCost != null) {
                insertItem.setPriceCost(priceCost);
            }
            //单位
            insertItem.setUnit(ocBSapSalesDataRecordItemRequest.getUnit());

            insertItem.setAmt(reduceAmt);
            insertItem.setLineType(ocBSapSalesDataRecordItemRequest.getLineType());

            ocBSapSalesDataGatherItems.add(insertItem);
            OmsStorageUtils.setBModelDefalutData(insertItem, rootUser);
        }
        if (CollectionUtils.isNotEmpty(ocBSapSalesDataGatherItems)) {
            saveObject(null, ocBSapSalesDataGatherItems);

        }

    }

    private BigDecimal getPriceCostCallAll(List<OcBSapSalesDataRecordItemRequest> ocBSapSalesDataRecordItemRequests1,
                                           List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherNaikaOrderItems) {
        BigDecimal priceCostCallAll = null;

        //已存在成本
        BigDecimal existPriceCost = null;
        for (OcBSapSalesDataGatherItem item : ocBSapSalesDataGatherNaikaOrderItems) {
            BigDecimal priceCost = item.getPriceCost();
            if (priceCost == null) {
                continue;
            }

            if (existPriceCost == null) {
                existPriceCost = BigDecimal.ZERO;
            }
            existPriceCost = existPriceCost.add(priceCost);
        }

        //新成本
        BigDecimal priceCostAll = priceCostItem(ocBSapSalesDataRecordItemRequests1);

        if (existPriceCost != null || priceCostAll != null) {
            BigDecimal bigDecimal = existPriceCost == null ? BigDecimal.ZERO : existPriceCost;
            BigDecimal bigDecimal1 = priceCostAll == null ? BigDecimal.ZERO : priceCostAll;
            priceCostCallAll = bigDecimal.add(bigDecimal1);
        }
        return priceCostCallAll;
    }


    /**
     * 获取汇总表的单据编号
     *
     * @return
     */
    public String getBillNo(Date inTime,String sumType, String billType, String shopCode, String middlegroundBillTypeCode) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = simpleDateFormat.format(inTime);
        //订单
        if (OcBSapSalesDataRecordConstant.SAP_BILL_CATEGORY_01.equals(billType)) {
            return "S" + dateStr + shopCode + middlegroundBillTypeCode + getSerialNumber();
        } else if (OcBSapSalesDataRecordConstant.SAP_BILL_CATEGORY_02.equals(billType)) {
            return "R" + dateStr + shopCode + middlegroundBillTypeCode + getSerialNumber();
        } else if (OcBSapSalesDataRecordConstant.SUM_TYPE_WMJ.equals(sumType)) {
            return "T" + dateStr + getSerialNumber();
        } else if (OcBSapSalesDataRecordConstant.SUM_TYPE_CWMJ.equals(sumType)) {
            return "C" + dateStr + getSerialNumber();
        } else if (OcBSapSalesDataRecordConstant.SUM_TYPE_BCTZ.equals(sumType)) {
            return "B" + simpleDateFormat.format(DateUtil.offsetDay(new Date(), -1)) + getSerialNumber();
        }
        return null;
    }

    /**
     * 汇总表新增
     *
     * @param ocBSapSalesDataGather
     * @param ocBSapSalesDataGatherItems
     */
    public void saveObject(OcBSapSalesDataGather ocBSapSalesDataGather,
                           List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherItems) {
        if (Objects.nonNull(ocBSapSalesDataGather)){
            ocBSapSalesDataGatherMapper.insert(ocBSapSalesDataGather);
        }

        ocBSapSalesDataGatherItemMapper.batchInsert(ocBSapSalesDataGatherItems);
    }

    public void updateSaleItemDate(OcBSapSalesDataGatherItem updateItem, BigDecimal reduceAmt, int sumQty, Integer reduceQty, BigDecimal priceCostAll) {
        updateItem.setAmt(reduceAmt);
        //汇总数量
        updateItem.setQty((long) sumQty);
        //剩余提数
        if (reduceQty != null) {
            updateItem.setResidueQty(reduceQty.longValue());
        }
        //成本
        if (priceCostAll != null) {
            updateItem.setPriceCost(priceCostAll);
        }
    }

    public void updateGatherMiddleStatus() {
        //最后更新所有汇总中为汇总完成
        OcBSapSalesDataGather ocBSapSalesDataGather = new OcBSapSalesDataGather();
        ocBSapSalesDataGather.setGatherMiddleStatus(SapSalesDateConstant.GATHER_MIDDLE_STATUS_02);
        ocBSapSalesDataGather.setGatherDate(new Date());
        ocBSapSalesDataGatherMapper.update(ocBSapSalesDataGather, new LambdaQueryWrapper<OcBSapSalesDataGather>()
                .eq(OcBSapSalesDataGather::getGatherMiddleStatus, SapSalesDateConstant.GATHER_MIDDLE_STATUS_01)
                .eq(OcBSapSalesDataGather::getIsactive, SapSalesDateConstant.ISACTIVE_YES));
    }

    /**
     * 根据时间戳获取ba位流水号
     *
     * @return
     */
    public String getSerialNumber() {
        String serialNumber = redisOpsUtil.strRedisTemplate.opsForValue().get("GatherSerialNumber");
        log.info("OcBSapSalesDataRecordSumService serialNumber:{}, getSeconds:{}", serialNumber, getSeconds());
        if (org.apache.commons.lang.StringUtils.isEmpty(serialNumber)) {
            redisOpsUtil.strRedisTemplate.opsForValue().set("GatherSerialNumber", "10000", getSeconds(), TimeUnit.SECONDS);
            serialNumber = "10000";
        } else {
            Long rsbsSerialNumber = redisOpsUtil.strRedisTemplate.opsForValue().increment("GatherSerialNumber", 1L);
            serialNumber = String.valueOf(rsbsSerialNumber);
        }
        log.info(LogUtil.format("OcBSapSalesDataRecordSumService.getSerialNumber:{}",
                "serialNumber"), serialNumber);
        return serialNumber;
    }

    /**
     * 获取当天结束还剩余多少秒
     *
     * @return
     */
    public static int getSeconds() {
        //获取今天当前时间
        Calendar curDate = Calendar.getInstance();
        //获取明天凌晨0点的⽇期
        Calendar tommorowDate = new GregorianCalendar(
                curDate.get(Calendar.YEAR),
                curDate.get(Calendar.MONTH),
                curDate.get(Calendar.DATE) + 1,
                0, 0, 0);
        //返回明天凌晨0点和今天当前时间的差值（秒数）
        return (int) (tommorowDate.getTimeInMillis() - curDate.getTimeInMillis()) / 1000;
    }

    public String pstyvValue(String sumType, BigDecimal amt, OcBSapSalesDataRecord ocBSapSalesDataRecord) {
        String pstyv = null;
        if (SUM_TYPE00.equals(sumType)) {
            if ((SapSalesDateConstant.BILL_TYPE_NKTH.equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeName())
                    || SapSalesDateConstant.BILL_TYPE_ZTZQGTH.equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeName())
                    || SapSalesDateConstant.OC_B_SAP_SALES_DATA_NKZQGTH.equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeName())) &&
                    amt.compareTo(BigDecimal.ZERO) == 0) {
                pstyv = "0";
            } else if ((SapSalesDateConstant.BILL_TYPE_NKTH.equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeName())
                    || SapSalesDateConstant.BILL_TYPE_ZTZQGTH.equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeName())
                    || SapSalesDateConstant.OC_B_SAP_SALES_DATA_NKZQGTH.equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeName())) &&
                    amt.compareTo(BigDecimal.ZERO) < 0) {
                pstyv = "1";
            } else if ((SapSalesDateConstant.OC_B_SAP_SALES_DATA_DSXS.equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeName()) &&
                    amt.compareTo(BigDecimal.ZERO) == 0)) {
                pstyv = "2";
            } else if ((SapSalesDateConstant.OC_B_SAP_SALES_DATA_DSXS.equals(ocBSapSalesDataRecord.getMiddlegroundBillTypeName()) &&
                    amt.compareTo(BigDecimal.ZERO) < 0)) {
                pstyv = "3";
            }
        }
        return pstyv;
    }

    public static String creatDate() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, -1);
        return simpleDateFormat.format(calendar.getTime());
    }

    public static void main(String[] args) {
        System.out.println(creatDate());
    }
}
