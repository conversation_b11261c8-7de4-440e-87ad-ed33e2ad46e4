package com.jackrain.nea.oc.oms.services.patrol;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OcOrderRefundStatusEnum;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Description:检测类
 *
 * <AUTHOR> 孙继东
 * @since : 2019-06-04
 * create at : 2019-06-04 11:19
 */
@Component
@Slf4j
public class CheckDirtyData {
    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private SgRpcService sgRpcService;

    /**
     * 明细全部退款成功,订单没有作废（手动更改订单状态）
     *
     * @return 菲非法数据id集合
     */
    public ValueHolderV14 checkReturn1() {
        JSONArray data = ES4Order.getItemIdsByRfStatusAndFilterByMdDate();
        ValueHolderV14 v14 = new ValueHolderV14<>();
        if (CollectionUtils.isEmpty(data)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("未查到相关订单数据");
        }
        List<Long> ids = new ArrayList<>();
        List<Long> resultList = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            JSONObject jsonObject = data.getJSONObject(i);
            Long orderId = jsonObject.getLong("OC_B_ORDER_ID");
            if (!ids.contains(orderId)) {
                ids.add(orderId);
            }
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            for (int i = 0; i < ids.size(); i++) {
                Long orderId = ids.get(i);
                //所有明细
                List<OcBOrderItem> items = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().eq(
                        "oc_b_order_id", orderId));
                //退款成功明细
                List<OcBOrderItem> items1 = ocBOrderItemMapper.selectList(new QueryWrapper<OcBOrderItem>().eq(
                        "oc_b_order_id", orderId).eq("refund_status", OcOrderRefundStatusEnum.SUCCESS.getVal()));
                if (items.size() == items1.size()) {
                    OcBOrder ocBOrder = ocBOrderMapper.selectById(orderId);
                    if (ocBOrder == null) {
                        resultList.add(-1L);
                    } else if (!ocBOrder.getOrderStatus().equals(OmsOrderStatus.SYS_VOID.toInteger())) {
                        resultList.add(orderId);
                    }
                }
            }
        }
        v14.setCode(ResultCode.SUCCESS);
        v14.setData(resultList);
        if (resultList.size() > 0) {
            v14.setMessage("存在非法数据,明细全部退款成功,订单没有作废，data中为对应的订单id，-1代表脏数据");
        } else {
            v14.setMessage("未查到非法数据");
        }
        return v14;
    }

    /**
     * 退款成功，明细在对应出库通知单存在；（库存pg） （当天数据）
     *
     * @return 菲非法数据id集合
     */
    public ValueHolderV14 checkReturn2() {
        //查询所有退款明细
        JSONArray data = ES4Order.getItemIdsByRfStatusAndFilterByMdDate();
        ValueHolderV14 v14 = new ValueHolderV14<>();
        if (CollectionUtils.isEmpty(data)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("ES中未查到相关订单数据");
        }
        List<Long> resultList = new ArrayList<>();
        //List<SgPhyOutBillBaseRequest> list = new ArrayList<>();
//        List<Long> itemIds = new ArrayList<>();
//        SgPhyOutBillQueryRequest request = new SgPhyOutBillQueryRequest();
//        for (int i = 0; i < data.size(); i++) {
//            JSONObject jsonObject = data.getJSONObject(i);
//            itemIds.add(jsonObject.getLong("ID"));
//            Long orderId = jsonObject.getLong("OC_B_ORDER_ID");
//            SgPhyOutBillBaseRequest sgPhyOutBillBaseRequest = new SgPhyOutBillBaseRequest();
//            sgPhyOutBillBaseRequest.setSourceBillId(orderId);
//            sgPhyOutBillBaseRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
//            list.add(sgPhyOutBillBaseRequest);
//        }
//        //调用出库通知单查询接口
//        request.setBaseRequests(list);
//        request.setIsReturnItem(true);
  //      log.debug("调用出库通知单查询2:入参" + request);
//        ValueHolderV14<List> valueHolderV14 = null;
//
//        valueHolderV14 = sgRpcService.queryOutBySource(request);
//
//
//        log.debug("调用出库通知单查询2:出参" + valueHolderV14);
//
//        List<SgOutQueryResult> queryResults = valueHolderV14.getData();
//        for (int i = 0; i < queryResults.size(); i++) {
//            SgOutQueryResult sgOutQueryResult = queryResults.get(i);
//            if (sgOutQueryResult == null) {
//                continue;
//            }
//            SgOutNoticesByBillNoResult notices = sgOutQueryResult.getNotices();
//            List<SgBPhyOutNoticesItem> items = notices.getItems();
//            for (int k = 0; k < items.size(); k++) {
//                Long itemId = items.get(k).getSourceBillItemId();
//                if (itemId != null && itemIds.contains(itemId)) {
//                    resultList.add(itemId);
//                }
//            }
//        }
        v14.setCode(ResultCode.SUCCESS);
        v14.setData(resultList);
        if (resultList.size() > 0) {
            v14.setMessage("存在非法数据,退款成功的明细对应的出库通知单存在，data中为对应的订单明细id");
        } else {
            v14.setMessage("未查到非法数据");
        }
        return v14;
    }


    /**
     * 订单查询云仓 已发货，但是oms 已取消的单据
     * （出库结果单已出库 但订单已取消wms撤回不成功的问题||wms回执不及时不能更新配货中）--（赔偿）
     *
     * @return
     */
    public ValueHolderV14 checkReturn3() {

        //查询已取消的订单
        long time = System.currentTimeMillis() - 300000;

        JSONArray data = ES4Order.getIdsByOrderStatusAndFilterByMfDate(OmsOrderStatus.CANCELLED.toInteger(),
                "" + (time - 86400000L) + "~" + time + "", 500);

        ValueHolderV14 v14 = new ValueHolderV14<>();
        if (CollectionUtils.isEmpty(data)) {
            v14.setCode(ResultCode.SUCCESS);
            v14.setMessage("未查到相关订单数据");
        }
        List<Long> resultList = new ArrayList<>();

//        List<SgPhyOutBillBaseRequest> list = new ArrayList<>();
//
//        for (int i = 0; i < data.size(); i++) {
//            JSONObject jsonObject = data.getJSONObject(i);
//            Long id = jsonObject.getLong("ID");
//            SgPhyOutBillBaseRequest sgPhyOutBillBaseRequest = new SgPhyOutBillBaseRequest();
//            sgPhyOutBillBaseRequest.setSourceBillId(id);
//            sgPhyOutBillBaseRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
//            list.add(sgPhyOutBillBaseRequest);
//            //调用出库结果单查询接口
//        }
//
//        SgPhyOutBillQueryRequest request = new SgPhyOutBillQueryRequest();
//        request.setBaseRequests(list);
//        request.setIsReturnItem(true);
//        request.setReturnData(2);
//        log.debug("调用出库通知单查询3:入参" + request);
//        ValueHolderV14<List<SgOutQueryResult>> valueHolderV14 = null;
//
//        valueHolderV14 = sgRpcService.queryOutBySource(request);
//
//        log.debug("调用出库通知单查询3:出参" + valueHolderV14);
//        List<SgOutQueryResult> queryResults = valueHolderV14.getData();
//        for (int i = 0; i < queryResults.size(); i++) {
//            SgOutQueryResult sgOutQueryResult = queryResults.get(i);
//            if (sgOutQueryResult == null) {
//                continue;
//            }
//            List<SgOutResultResult> results = sgOutQueryResult.getResults();
//            for (int j = 0; j < results.size(); j++) {
//                SgOutResultResult result = results.get(i);
//                if (result == null) {
//                    continue;
//                }
//                SgBPhyOutResult outResult = result.getOutResult();
//                if (outResult != null) {
//                    resultList.add(outResult.getId());
//                }
//            }
//        }
        v14.setCode(ResultCode.SUCCESS);
        v14.setData(resultList);
        if (resultList.size() > 0) {
            v14.setMessage("存在非法数据,订单已取消的，但出库结果单存在，data中为对应的出库结果单id");
        } else {
            v14.setMessage("未查到非法数据");

        }
        return v14;
    }

}
