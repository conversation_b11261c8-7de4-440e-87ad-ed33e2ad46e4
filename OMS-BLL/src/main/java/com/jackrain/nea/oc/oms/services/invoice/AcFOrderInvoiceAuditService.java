package com.jackrain.nea.oc.oms.services.invoice;

import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFTaxMachineManageMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 */
@Slf4j
@Component
public class AcFOrderInvoiceAuditService {
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private AcFOrderInvoiceMapper acOrderInvoiceMapper;

    @Autowired
    private AcFTaxMachineManageMapper acTaxMachineManageMapper;

    @Autowired
    private InvoiceLogService invoiceLogService;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        request.setR3(true);
        AcFOrderInvoiceAuditService service = ApplicationContextHandle.getBean(this.getClass());
        return service.audit(request);
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder audit(SgR3BaseRequest request) {
        List<Long> batchObjIds = R3ParamUtils.getBatchObjIds(request);
        User loginUser = request.getLoginUser();
        // 存储错误的Map
        Map<Long, Object> errorMap = new HashMap<>(batchObjIds.size());
        for (Long objId : batchObjIds) {

            String lockRedisKey = InvoiceConst.AC_F_ORDER_INVOICE + ":" + request.getObjId();
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    AcFOrderInvoice acOrderInvoice = acOrderInvoiceMapper.selectById(objId);
                    if (acOrderInvoice == null) {
                        errorMap.put(objId, "当前记录已不存在！");
                        continue;
                    }
                    if (OmsParamConstant.ONE.equals(acOrderInvoice.getAuditStatus()) ||
                            OmsParamConstant.ONE.equals(acOrderInvoice.getCancelStatus()) ||
                            OmsParamConstant.ONE.equals(acOrderInvoice.getFreezeStatus())) {
                        errorMap.put(objId, "选择的订单发票状态不能审核！");
                        continue;
                    }
//                    AcFTaxMachineManage acTaxMachineManage =
//                            acTaxMachineManageMapper.selectOne(new LambdaQueryWrapper<AcFTaxMachineManage>()
//                            .eq(AcFTaxMachineManage::getUserId, acOrderInvoice.getCpCSupplierId()));
//                    if (acTaxMachineManage!=null){
//                        acOrderInvoice.setTaxMachineNo(acTaxMachineManage.getTaxMachine().toString());
//                    } else {
//                        acOrderInvoice.setTaxMachineNo("03");
//                    }
                    acOrderInvoice.setAuditStatus(OmsParamConstant.ONE);
                    BaseModelUtil.setupUpdateParam(acOrderInvoice, loginUser);
                    acOrderInvoiceMapper.updateById(acOrderInvoice);

                    invoiceLogService.addUserOrderLog(acOrderInvoice.getId(),"审核","未审核改为已审核", loginUser);
                } else {
                    errorMap.put(objId, "当前发票处于锁定状态！");
                }
            } catch (Exception e) {
                log.error(LogUtil.format("AcFOrderInvoiceCancelService.audit.error={}", "error"),
                        Throwables.getStackTraceAsString(e));
                throw new NDSException("发票管理审核异常!");
            } finally {
                redisLock.unlock();
            }
        }
        return R3ParamUtils.getExcuteValueHolder(batchObjIds.size(), errorMap);
    }

    public List<AcFOrderInvoice> getInvoice(Long applyId) {
        //查询发票
        return acOrderInvoiceMapper.selectByInvoiceApplyId(applyId);


    }

}
