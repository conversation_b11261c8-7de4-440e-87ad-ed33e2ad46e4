package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

/**
 * @program: R3-OC-OMS-V1.4
 * @author: huang.z<PERSON><PERSON>
 * @create: 2019-10-12 10:47
 */
public enum OrderLockLogTypeEnum {

    ADD("ADD", "新增"),
    VOID("VOID", "作废"),
    MODIFY("MODIFY", "修改"),
    LOCK("LOCK", "锁单"),
    UNLOCK("UNLOCK", "解锁"),
    EDITUNLOCK("EDITUNLOCK", "修改解锁时间"),
    CALLBACK("CALLBACK", "反馈千牛"),
    INITIALIZE("INITIALIZE", "解锁时间初始化");


    OrderLockLogTypeEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    @Getter
    private String key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String enumToStringBykey(String key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (OrderLockLogTypeEnum e : OrderLockLogTypeEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return key;
    }
}