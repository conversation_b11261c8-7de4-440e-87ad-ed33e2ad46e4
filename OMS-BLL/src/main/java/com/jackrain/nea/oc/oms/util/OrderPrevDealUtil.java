package com.jackrain.nea.oc.oms.util;

import com.jackrain.nea.config.Resources;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.web.face.User;

/**
 * 全渠道订单.预处理
 *
 * @author: xiWen.z
 * create at: 2019/6/28 0028
 */
public class OrderPrevDealUtil {

    /**
     * 平台.唯品会JITX
     *
     * @param obo 全渠道订单
     * @param usr user
     */
    public static void platformIntercept(OcBOrder obo, User usr) {
        if (obo != null) {
            if (obo.getPlatform() != null
                    && obo.getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())) {
                if (usr == null) {
                    throw new NDSException("当前订单为JITX订单, 不允许修改");
                }
                throw new NDSException(Resources.getMessage("当前订单为JITX订单, 不允许修改", usr.getLocale()));
            }
        }
    }

    /**
     * 平台.唯品会JITX
     *
     * @param obo 全渠道订单
     * @return vh14
     */
    public static ValueHolderV14 platformInterceptVh(OcBOrder obo) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (obo != null) {
            if (obo.getPlatform() != null
                    && obo.getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())) {
                vh.setCode(ResultCode.FAIL);
                vh.setMessage("当前订单为JITX订单, 不允许修改");
                vh.setData(obo.getId());
            }
        }
        return vh;
    }

    /**
     * 平台.唯品会JITX
     *
     * @param obo 全渠道订单
     * @param usr user
     * @return vh14
     */
    public static ValueHolderV14 platformInterceptVh(OcBOrder obo, User usr) {
        ValueHolderV14 vh = new ValueHolderV14();
        if (obo != null) {
            if (obo.getPlatform() != null
                    && obo.getPlatform().equals(PlatFormEnum.VIP_JITX.getCode())) {
                vh.setCode(ResultCode.FAIL);
                String msg = "当前订单为JITX订单, 不允许修改";
                if (usr != null) {
                    msg = Resources.getMessage(msg, usr.getLocale());
                }
                vh.setMessage(msg);
                vh.setData(obo.getId());
            }
        }
        return vh;
    }

    private OrderPrevDealUtil() {
    }
}
