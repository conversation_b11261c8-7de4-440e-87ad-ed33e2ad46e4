package com.jackrain.nea.oc.oms.matcher.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

/**
 * Description： 输出参数
 * Author: RESET
 * Date: Created in 2020/7/9 13:23
 * Modified By:
 */
@Getter
@Setter
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@Builder(toBuilder = true)
public class ParamOutputVO {

    // 出参
    // 主播ID
    String anchorId;
    // 主播名称
    String anchorName;
    // 直播平台
    String livePlatform;

    // 直播主体
    private Long acFManageId;

    // 直播主体 经营主体ecode
    private String acFManageEcode;

    // 直播主体 经营主体ename
    private String acFManageEname;

    // 配合主体
    private Long cooperateId;

    // 配合主体 经营主体ecode
    private String cooperateEcode;

    // 配合主体 经营主体ename
    private String cooperateEname;

    // 直播场次
    private Integer liveEvents;


    // 以下两个参数不支持同一个单多线程并行
    boolean result = false;

}
