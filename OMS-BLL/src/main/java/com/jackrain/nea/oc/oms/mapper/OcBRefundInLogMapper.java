package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.Date;

@Mapper
@Component
public interface OcBRefundInLogMapper extends ExtentionMapper<OcBRefundInLog> {

    @Select("SELECT * FROM OC_B_REFUND_IN_LOG WHERE OMSONLINEORDERID= #{omsonlineorderid} AND logmessage=#{logmessage}")
    OcBRefundInLog selectLogIsExist(@Param("logmessage") String logMessage, @Param("omsonlineorderid") Long refundId);

    @Update("UPDATE OC_B_REFUND_IN_LOG SET creationdate = #{creationdate} WHERE OMSONLINEORDERID = #{omsonlineorderid} AND `ID` = #{id}")
    int updateRefundlog(@Param("creationdate") Date dt, @Param("id") Long id, @Param("omsonlineorderid") Long refundId);
}