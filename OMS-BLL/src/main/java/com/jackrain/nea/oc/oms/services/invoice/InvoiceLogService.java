package com.jackrain.nea.oc.oms.services.invoice;

import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceLogMapper;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceLog;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 */
@Component
@Slf4j
public class InvoiceLogService {

    private static final String LOG_TABLE_NAMAE = "ac_f_order_invoice_log";

    @Autowired
    private AcFOrderInvoiceLogMapper acOrderInvoiceLogMapper;



    /**
     * 用户日志信息动态获取保存日志
     *
     * @param acOrderInvoiceId      订单发票ID
     * @param logType      日志类型
     * @param logMessage   日志消息
     * @param operateUser  用户对象
     */
    public void addUserOrderLog(long acOrderInvoiceId, String logType, String logMessage, User operateUser) {
        if (StringUtils.isNotEmpty(logMessage) && logMessage.length() > 500) {
            logMessage = logMessage.substring(0, 500);
        }
        AcFOrderInvoiceLog orderInvoiceLog = new AcFOrderInvoiceLog();
        long autoId = ModelUtil.getSequence(LOG_TABLE_NAMAE);
        orderInvoiceLog.setId(autoId);
        orderInvoiceLog.setLogType(logType);
        orderInvoiceLog.setLogContent(logMessage);
        orderInvoiceLog.setAcFOrderInvoiceId(acOrderInvoiceId);
        BaseModelUtil.initialBaseModelSystemField(orderInvoiceLog, operateUser);

        this.acOrderInvoiceLogMapper.insert(orderInvoiceLog);
    }

}
