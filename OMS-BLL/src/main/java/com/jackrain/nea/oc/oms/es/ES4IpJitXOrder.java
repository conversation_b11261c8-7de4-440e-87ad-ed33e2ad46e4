package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.IsForbiddenDeliveryEnum;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jitx.JitxOrderStatus;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;

import java.util.ArrayList;
import java.util.List;

/**
 * jitX主表
 *
 * <AUTHOR>
 * @date 2020/11/11 4:09 下午
 */
public class ES4IpJitXOrder {

    private ES4IpJitXOrder() {
    }

    /**
     * 业务：JITX退单补偿定时任务
     * 根据isTrans、orderStatus查询orderSn
     *
     * @param pageIndex   页码
     * @param pageSize    每页大小
     * @param orderStatus 订单状态
     * @return List orderSn
     */
    public static List<String> findOrderSnByTransStatusAndOrderStatus(int pageIndex, int pageSize, String orderStatus) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "0");
            whereKeys.put("ORDER_STATUS", orderStatus);

            String[] returnFieldNames = new String[]{"ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JITX_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_JITX_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return orderNoList;
    }

    /**
     * 发货后取消。发货前取消
     * @param pageIndex
     * @param pageSize
     * @return
     */
    public static List<String> findOrderSnByTransStatus(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "0");
            JSONArray statusAry = new JSONArray();
            statusAry.add(JitxOrderStatus.ORDER_UNSEND_REFUND);
            statusAry.add(JitxOrderStatus.ORDER_SEND_REFUND);
            whereKeys.put("ORDER_STATUS", statusAry);

            String[] returnFieldNames = new String[]{"ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JITX_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_JITX_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return orderNoList;
    }
    /**
     * 业务：根据合包码获取未转换的JITX订单
     * 根据isTrans、orderStatus查询orderSn
     *
     * @return List orderSn
     */
    public static List<String> findExistOtherCanMergedOrder(String mergeCode, Long shopId,String warehouseCode) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            //取JITX订单中间表中的店铺、仓库编码（需映射为系统的仓库编码）、交易状态为已审核、合包码、转换状态为未转换/转换中的、“是否可发货”为是的、
            whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());
            whereKeys.put("MERGED_CODE", mergeCode);
            whereKeys.put("CP_C_SHOP_ID", shopId);
            whereKeys.put("IS_FORBIDDEN_DELIVERY", IsForbiddenDeliveryEnum.PASS.getCode());
            whereKeys.put("ORDER_STATUS", JitxOrderStatus.ORDER_ALREADY_AUDITED);
            whereKeys.put("DELIVERY_WAREHOUSE", warehouseCode);

            String[] returnFieldNames = new String[]{"ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JITX_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_JITX_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    200, 0, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return orderNoList;
    }
}
