/*
package com.jackrain.nea.oc.oms.tag.impl;

import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.tag.AbstractTagger;
import com.jackrain.nea.oc.oms.tag.vo.TaggerRelation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

*/
/**
 * 复制订单打标
 *
 * @Auther: 黄志优
 * @Date: 2020/12/25 15:31
 * @Description:
 *//*

@Component
public class CopyOrderTagger extends AbstractTagger {

    */
/**
     * 打标逻辑
     *
     * @param relation
     *//*

    @Override
    public void doTag(TaggerRelation relation) {
        if (Objects.nonNull(relation)) {
            doTag(relation.getOcBOrder(), relation.getMergeOrders());
        }
    }

    */
/**
     * 打标逻辑
     *
     * @param ocBOrder
     * @param ocBOrders
     *//*

    private void doTag(OcBOrder ocBOrder, List<OcBOrder> ocBOrders) {

        if (Objects.isNull(ocBOrder) || CollectionUtils.isEmpty(ocBOrders)) {

            return;
        }

        boolean isCopyOrder = false;
        for (OcBOrder order : ocBOrders) {
            //合单时打复制标(只要有一个订单复制标，合单后就全部标记为复制标)
            if (order.getIsCopyOrder() != null
                    && order.getIsCopyOrder().equals(OcBOrderConst.IS_STATUS_IY)) {

                isCopyOrder = true;
            }
        }

        if (isCopyOrder) {
            // 打标
            ocBOrder.setIsCopyOrder(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
        } else {
            // 取消标记
            ocBOrder.setIsCopyOrder(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
        }
    }

}
*/
