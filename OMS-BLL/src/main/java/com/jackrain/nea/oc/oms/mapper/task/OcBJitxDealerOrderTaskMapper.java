package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBJitxDealerOrderTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

@Mapper
public interface OcBJitxDealerOrderTaskMapper extends ExtentionMapper<OcBJitxDealerOrderTask> {
    @Update("<script> "
            + "UPDATE oc_b_jitx_dealer_order_task SET STATE = #{state}," +
            "<if test = 'msg != null'>msg=#{msg},</if>" +
            "<if test = 'state == 2'>fail_number=fail_number+1,</if>" +
            "modifieddate = now() where id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskStatus(@Param("ids") List<Long> orderIds, @Param("state") int status, @Param("msg") String msg);

    @SelectProvider(type = OcBJitxDealerOrderTaskMapper.SqlProvider.class, method = "selectByNodeSql")
    List<OcBJitxDealerOrderTask> selectTaskIdList(@Param(value = "nodeName") String nodeName,
                                        @Param(value = "limit") int limit,
                                        @Param(value = "taskTableName") String taskTableName,
                                        @Param(value = "state") Integer state, @Param(value = "type") String type,@Param("failNumber") int failNumber);

    @Slf4j
    class SqlProvider {

        public String selectByNodeSql(Map<String, Object> para) {
            StringBuilder sql = new StringBuilder();
            StringBuilder limitStr = new StringBuilder(" LIMIT ");
            String nodeName = (String) para.get("nodeName");
            int state = (int) para.get("state");
            int failNumber = (int) para.get("failNumber");
            String type = (String) para.get("type");
            String[] typeArr=type.split(",");
            int limit = para.get("limit") != null ? (int) para.get("limit") : 500;
            limitStr.append(limit);
            String taskTableName = (String) para.get("taskTableName");

            if (StringUtils.isEmpty(nodeName)) {
                if (log.isDebugEnabled()) {
                    log.debug("DrdsSql nodeName 没有值！");
                }
                return null;
            }
            sql.append("/*!TDDL:NODE=" + nodeName + "*/")
                    .append("select * from ")
                    .append(taskTableName)
                    .append(" where state = ")
                    .append(state)
                    .append(" and fail_number <")
                    .append(failNumber);
            if (StringUtils.isNotEmpty(type) && typeArr.length > 0) {
                sql.append(" and ")
                        .append(" type in ( ");
                for (String str : typeArr) {
                    if (StringUtils.isNotEmpty(str)) {
                        sql.append(Integer.valueOf(str));
                        sql.append(",");
                    }
                }
                sql.deleteCharAt(sql.length()-1);
                sql.append(" ) ");
            }
            sql.append(" ORDER BY modifieddate asc ")
                    .append(limitStr);
            if (log.isDebugEnabled()) {
                log.debug("DrdsSql sql :{}", sql.toString());
            }

            return sql.toString();
        }
    }
}