package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongDirectItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/3/28
 */
@Mapper
public interface IpBJingdongDirectItemMapper extends ExtentionMapper<IpBJingdongDirectItem> {

    @Select("SELECT * FROM ip_b_jingdong_direct_item WHERE ip_b_jingdong_direct_id = #{orderId}")
    List<IpBJingdongDirectItem> selectIpBJingdongDirectItemList(@Param("orderId") Long orderId);
}
