package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundBatch;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface OcBRefundBatchMapper extends ExtentionMapper<OcBRefundBatch> {
    @Update("UPDATE oc_b_refund_batch SET batch_no=#{sequence} WHERE ID=#{ID}")
    void updateSequence(@Param("sequence") String sequence, @Param("ID") Long ID);
}