package com.jackrain.nea.oc.oms.services.task;

import com.jackrain.nea.oc.oms.mapper.task.CommonRefundMakeUpMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/20 17:16
 * @desc
 */
@Component
@Slf4j
public class CommonRefundMakeUpService {

    @Autowired
    private CommonRefundMakeUpMapper commonRefundMakeUpMapper;

    /***
     * 查询DRDS的分库 RefundNo
     *
     * @param node node name
     * @param tableName table name
     * @param returnField return fiele 分库键
     * @param size size
     * @param isTrans trans status
     * @param lessThanTransCnt less than trans Count
     * @return RefundNo
     */
    public List<String> selectDynamicRefundNo(String node, String tableName, String returnField,
                                              int size, int isTrans, int lessThanTransCnt, int minutes) {
        return commonRefundMakeUpMapper.selectDynamicRefundNo(node, tableName, returnField, size, isTrans, lessThanTransCnt, minutes);
    }
}
