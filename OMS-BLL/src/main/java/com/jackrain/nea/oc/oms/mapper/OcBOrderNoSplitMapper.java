package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaVoid;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNoSplit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderNoSplitMapper
 * @Description 缺货不拆订单中间表
 * <AUTHOR>
 * @Date 2024/2/29 10:29
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderNoSplitMapper extends ExtentionMapper<OcBOrderNoSplit> {

    @Select("SELECT * FROM oc_b_order_no_split WHERE order_id=#{orderId} limit 1 ")
    OcBOrderNoSplit getByOrderId(@Param("orderId") Long orderId);
}
