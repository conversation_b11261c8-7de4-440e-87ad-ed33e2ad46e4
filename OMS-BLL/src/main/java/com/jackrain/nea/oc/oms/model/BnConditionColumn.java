package com.jackrain.nea.oc.oms.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建条件列请求参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BnConditionColumn {
    private Long id;           // 条件ID
    private Integer behaviorType; // 行为类型
    private String searchType;    // 搜索类型
    private Object value;         // 搜索值，当searchType="5"时，这是一个数组
}
