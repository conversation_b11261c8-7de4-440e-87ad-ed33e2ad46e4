package com.jackrain.nea.oc.oms.services.task;

import com.jackrain.nea.oc.oms.nums.OcBWmsMiddleTableEnum;
import com.jackrain.nea.oc.oms.services.OcBRefundInTaskThirdService;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/3/31 17:20
 * @Description
 */
@Slf4j
@Component
public class OcBWmsMiddleTableResetFailCountService {

    public void resetFailCount(OcBWmsMiddleTableEnum tableEnum) {
        Integer value = tableEnum.getValue();
        if (OcBWmsMiddleTableEnum.OC_B_REFUND_IN_TASK.getValue().equals(value)) {
            /*B2C退货回传中间表*/
            OcBRefundInTaskThirdService bean = ApplicationContextHandle.getBean(OcBRefundInTaskThirdService.class);
            bean.resetFailCount();
        } else if (OcBWmsMiddleTableEnum.OC_B_REFUND_PACKAGE_TASK.getValue().equals(value)) {
            /*退货包裹状态中间表*/
            OcBRefundPackageTaskService bean =
                    ApplicationContextHandle.getBean(OcBRefundPackageTaskService.class);
            bean.resetFailCount();
        } else {
            log.error(LogUtil.format("OcBWmsMiddleTableResetFailCountService.resetFailCount 未识别WMS中间表类型",
                    "OcBWmsMiddleTableResetFailCountService.resetFailCount"));
        }
    }
}
