package com.jackrain.nea.oc.oms.services.returnin;

import com.jackrain.nea.oc.oms.model.enums.ReturnStockInTimes;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStockInMatchParam;
import com.jackrain.nea.oc.oms.model.relation.RefundInRelation;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2023/3/7
 */
public interface IReturnMatchStockIn {

    /**
     * 入库次数
     *
     * @return
     */
    ReturnStockInTimes stockInTimes();

    /**
     * 更新数据,入库备参
     *
     * @param inRelation
     * @param stockIn
     * @return
     */
    OmsStockInMatchParam handleBilProcessing(RefundInRelation inRelation, OcReturnInRelation stockIn);

    /**
     * 生成,更新单据, 调用库存中心入库
     *
     * @param inParam
     * @return
     */
    boolean persistDataAndStockIn(OmsStockInMatchParam inParam);

}
