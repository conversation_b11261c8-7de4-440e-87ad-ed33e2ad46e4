package com.jackrain.nea.oc.oms.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.context.annotation.Configuration;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/9/28
 */
@Data
@Configuration
public class OmsMiniPlatformConfig {

    @NacosValue(value = "${r3.oms.delivery.mini.route:0}", autoRefreshed = true)
    private Integer sendRoute;

}
