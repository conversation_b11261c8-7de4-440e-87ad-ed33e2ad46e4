package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategyDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 一件代发客户基价策略明细Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface StCDropshipBasePriceStrategyDetailMapper extends ExtentionMapper<StCDropshipBasePriceStrategyDetail> {

    /**
     * 根据策略ID查询明细列表
     *
     * @param strategyId 策略ID
     * @return 明细列表
     */
    @Select("SELECT " +
            "id, strategy_id, sku_code, base_price, import_content, " +
            "ownerid, ownername, ownerename, creationdate, " +
            "modifierid, modifiername, modifierename, modifieddate " +
            "FROM st_c_dropship_base_price_strategy_detail " +
            "WHERE strategy_id = #{strategyId} AND isactive = 'Y' " +
            "ORDER BY creationdate ASC")
    List<StCDropshipBasePriceStrategyDetail> selectByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 根据策略ID和SKU编码查询明细
     *
     * @param strategyId 策略ID
     * @param skuCode SKU编码
     * @return 明细信息
     */
    @Select("SELECT " +
            "id, strategy_id, sku_code, base_price, " +
            "ownerid, ownername, ownerename, creationdate, " +
            "modifierid, modifiername, modifierename, modifieddate " +
            "FROM st_c_dropship_base_price_strategy_detail " +
            "WHERE strategy_id = #{strategyId} AND sku_code = #{skuCode} AND isactive = 'Y' " +
            "LIMIT 1")
    StCDropshipBasePriceStrategyDetail selectByStrategyIdAndSkuCode(@Param("strategyId") Long strategyId, @Param("skuCode") String skuCode);

    /**
     * 根据策略ID删除明细（逻辑删除）
     *
     * @param strategyId 策略ID
     * @return 删除数量
     */
    @Update("UPDATE st_c_dropship_base_price_strategy_detail " +
            "SET isactive = 'N' " +
            "WHERE strategy_id = #{strategyId}")
    int deleteByStrategyId(@Param("strategyId") Long strategyId);

}
