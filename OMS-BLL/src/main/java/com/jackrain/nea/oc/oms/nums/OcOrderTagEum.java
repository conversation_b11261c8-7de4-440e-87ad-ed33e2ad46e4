package com.jackrain.nea.oc.oms.nums;

import com.jackrain.nea.oc.oms.model.result.QueryOrderTagResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 枚举-订单标签
 *
 * @author: xiwen.z
 * create at: 2019/3/7 0007
 */
public enum OcOrderTagEum {

    TAG_JOIN("Y", "IS_MERGE", "#ff7676", "合", 1),
    TAG_BLOCK("Y", "IS_INTERECEPT", "#84c9e2", "Hold", 2),
    TAG_RETREAT("Y", "IS_INRETURNING", "#9be09b", "退款中", 3),
    TAG_GIFT("Y", "IS_HASGIFT", "#f3c305", "赠", 4),
    TAG_DISMANTLE("Y", "IS_SPLIT", "#e36aee", "拆", 5),
    TAG_CHANGE("2", "ORDER_TYPE", "#6660d2", "换", 6),
    TAG_HAND("手工新增", "ORDER_SOURCE", "#b935f0", "手", 7),
    TAG_TICKET("Y", "IS_INVOICE", "#0489d6", "票", 8),
    TAG_ELE_PRE("1", "DOUBLE11_PRESALE_STATUS", "#eb3333", "11预", 9), // 非0
    // TAG_PRE("1", "SYS_PRESALE_STATUS", "#e6e047", "预", 10), // 非0
    TAG_JD("Y", "IS_JCORDER", "#4cdc4c", "京东", 11),
    TAG_URGE("Y", "IS_OUT_URGENCY", "#e17b87", "催", 12),
    TAG_LACK("Y", "IS_OUT_STOCK", "#0789a3", "缺", 13),
    TAG_CONSIGNMENT("Y", "IS_SHOP_COMMISSION", "#c17f2b", "代销", 14),
    TAG_WORK_ORDER("Y", "IS_HAS_TICKET", "#969596", "工单", 15),
    TAG_TO_PAY("2", "PAY_TYPE", "#5fe9e1", "到付", 16),
    TAG_VILLAGE_AMOY("村淘", "ORDER_SOURCE", "#e9b689", "村淘", 17),
    TAG_FICTITIOUS("Y", "IS_INVENTED", "#e8389c", "虚拟", 18),
    TAG_COMBINATION("Y", "IS_COMBINATION", "#996f25", "组合", 19),
    TAG_LABEL("1", "CP_C_LABEL_ID", "#FFB6C1", "标", 22),
    TAG_O2O("1", "IS_O2O_ORDER", "#6660d2", "O2O", 23),
    TAG_COPY("1", "IS_COPY_ORDER", "#0489d6", "复", 24),
    TAG_DETENTION("1", "IS_DETENTION", "#0489d6", "卡", 34),
    TAG_EQUAL_EXCHANGE("1", "IS_EQUAL_EXCHANGE", "#0489d6", "对等", 39),
    TAG_EXCEPTION("1", "IS_EXCEPTION", "#FF0000", "异常", 50);
    String val; // 值
    String key; // 数据库字段
    String clr; // 颜色
    String text; // 文本
    int sort; // 序号

    OcOrderTagEum(String v, String qn, String c, String s, int r) {
        this.val = v;
        this.key = qn;
        this.clr = c;
        this.text = s;
        this.sort = r;
    }

    public String getVal() {
        return val;
    }

    public String getKey() {
        return key;
    }

    public String getClr() {
        return clr;
    }

    public String getText() {
        return text;
    }

    public int getSort() {
        return sort;
    }

    /**
     * 转化所有枚举值为list<QueryOrderTagResult>
     *
     * @return list<QueryOrderTagResult>
     */
    public static List<QueryOrderTagResult> toQueryOrderTagResult() {
        List<QueryOrderTagResult> list = new ArrayList<>();
        for (OcOrderTagEum e : OcOrderTagEum.values()) {
            QueryOrderTagResult o = new QueryOrderTagResult();
            o.setVal(e.getVal());
            o.setKey(e.getKey());
            o.setClr(e.getClr());
            o.setText(e.getText());
            o.setSort(e.getSort());
            list.add(o);
        }
        return list;
    }

    /**
     * 根据枚举key集合,转化为list<QueryOrderTagResult>
     *
     * @param nlist list<Integer>
     * @return list<QueryOrderTagResult>
     */
    public static List<QueryOrderTagResult> toListQueryOrderTagResult(List<String> nlist) {
        List<QueryOrderTagResult> list = new ArrayList<QueryOrderTagResult>();
        if (nlist == null) {
            return list;
        }
        int nSize = nlist.size();
        for (int i = 0; i < nSize; i++) {
            for (OcOrderTagEum e : OcOrderTagEum.values()) {
                if (e.getKey().equals(nlist.get(i))) {
                    QueryOrderTagResult o = new QueryOrderTagResult();
                    o.setVal(e.getVal());
                    o.setKey(e.getKey());
                    o.setClr(e.getClr());
                    o.setText(e.getText());
                    o.setSort(e.getSort());
                    list.add(o);
                    break;
                }
            }
        }
        return list;
    }

    /**
     * 根据枚举文本转化对应枚举为QueryOrderTagResult
     *
     * @param s string
     * @return QueryOrderTagResult
     */
    public static QueryOrderTagResult getQueryOrderTagResult(String s) {
        QueryOrderTagResult o = new QueryOrderTagResult();
        if (s == null || s.trim().length() == 0) {
            return o;
        }
        for (OcOrderTagEum e : OcOrderTagEum.values()) {
            if (s.equals(e.getText())) {
                o.setVal(e.getVal());
                o.setKey(e.getKey());
                o.setClr(e.getClr());
                o.setText(e.getText());
                o.setSort(e.getSort());
                break;
            }
        }
        return o;
    }

}
