package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;


@Mapper
@Component
public interface OcBToBeConfirmedTaskMapper extends ExtentionMapper<OcBToBeConfirmedTask> {

    @Select("SHOW NODE")
    List<HashMap> selectNodeList();

    @SelectProvider(type = ToBeConfirmedSql.class, method = "selectByNodeSql")
    List<Long> selectTaskIdList(@Param("limit") int limit,
                                @Param("taskTableName") String taskTableName);

    @Update("<script> "
            + "UPDATE OC_B_TOBECONFIRMED_TASK SET STATUS = 1,modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskStatus(@Param("ids") List<Long> orderIds);




    @Update("<script> "
            + "UPDATE OC_B_TOBECONFIRMED_TASK SET STATUS = 0,modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskStatus0(@Param("ids") List<Long> orderIds);


    @Update("<script> "
            + "UPDATE OC_B_TOBECONFIRMED_TASK SET STATUS = 0,modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskStatusByIds(@Param("ids") List<Long> orderIds);


    /**
     * 优化更新状态逻辑，避免产生重复多条状态为0的数据 1101
     */
    @Update("UPDATE OC_B_TOBECONFIRMED_TASK SET STATUS = 0,modifieddate = now() where order_id = #{orderId} limit 1")
    int updateTaskStatusByOrderId(@Param("orderId") Long orderId);



    @Select("<script> "
            + "SELECT order_id from OC_B_TOBECONFIRMED_TASK where order_id in "
            + "<foreach item='item' index='index' collection='orderIds' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<Long> selectOcBToBeConfirmedTaskByOrderIds(@Param("orderIds") List<Long> orderIds);

}