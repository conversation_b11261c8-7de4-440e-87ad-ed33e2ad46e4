package com.jackrain.nea.oc.oms.mapper.ac;

import com.alibaba.fastjson.JSONObject;

import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentExDO;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Mapper
@Component
public interface AcFPayableAdjustmentMapper extends ExtentionMapper<AcFPayableAdjustmentDO> {

    //全渠道订单-【丢单复制】 自动生成应付款调整单 判断单据是否已经生成存在了
    @Select("SELECT COUNT(*)\n" +
            "FROM ac_f_payable_adjustment\n" +
            "WHERE order_no = #{billno}")
    int selectBillNo(@Param("billno") String billno);

    //应付款调整单: 汇总行的应付款金额(payable_price)  更新到主表的总应付金额(payable_price)
    @Update("UPDATE ac_f_payable_adjustment AS mainTable\n" +
            "SET payable_price = price \n" +
            "FROM (SELECT ac_f_payable_adjustment_id,sum(payable_price) AS price\n" +
            "FROM ac_f_payable_adjustment_item\n" +
            "WHERE ac_f_payable_adjustment_id = #{id}\n" +
            "GROUP BY ac_f_payable_adjustment_id) AS itemTable \n" +
            "WHERE mainTable.id = itemTable.ac_f_payable_adjustment_id")
    int updatePayablePrice(@Param("id") Long id);

    //生成单据序列号
    @Update("UPDATE AC_F_PAYABLE_ADJUSTMENT SET BILL_NO=#{sequence} WHERE id=#{id}")
    void updateSequence(@Param("sequence") String sequence, @Param("id") Long id);

    @UpdateProvider(type = AcFPayableAdjustmentMapper.ApAttribute.class, method = "updateSql")
    int updateAtrributes(JSONObject entity);

    class ApAttribute {

        public String insertSql(JSONObject entity) {
            return new SQL() {
                {
                    INSERT_INTO("ac_f_payable_adjustment");
                    for (String key : entity.keySet()) {
                        if (entity.get(key) != null) {
                            VALUES(key, "#{" + key + "}");
                        }
                    }
                }
            }.toString();
        }

        public String updateSql(JSONObject entity) {
            return new SQL() {
                {
                    UPDATE("ac_f_payable_adjustment");
                    for (String key : entity.keySet()) {
                        if (!"id".equalsIgnoreCase(key)) {
                            SET(key + "=" + "#{" + key + "}");
                        }
                    }
                    WHERE("ID = #{ID}");
                }
            }.toString();
        }
    }

    @Select("<script>"
            + "SELECT *, case when pay_type = 1 then '支付宝' when pay_type = 2 then '微信' when pay_type = 3 then '现金' "
            + " when pay_type = 4 then '备用金' when pay_type = 5 then '财付通' else '银行' end PAY_TYPE_NAME, "
            + "case when bill_type = 1 then '丢单赔付-补发' when bill_type = 2 then '仓储赔付' when bill_type = 3 then '其他' else '丢单赔付-仅退款' end BILL_TYPE_NAME,"
            + "case when bill_status = 1 then '未审核' when bill_status = 2 then '已客审' when bill_status = 3 then '已财审' else '已作废' end BILL_STATUS_NAME, "
            + "case when adjust_type = 1 then '线上'  else '线下' end ADJUST_TYPE_NAME, case when reserve_bigint01 = 1 then '直营' else '分销' end channelTypeName"
            + " FROM AC_F_PAYABLE_ADJUSTMENT "
            + "<where> "
            + "<if test='billNo != null'>"
            + "and bill_no in "
            + "<foreach item='item' index='index' collection='billNo' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if>"

            + "<if test='tid != null'>"
            + "and tid in "
            + "<foreach item='item' index='index' collection='tid' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if>"

            + "<if test='billTypeList != null'> "
            + " and bill_type in "
            + "<foreach item='item' index='index' collection='billTypeList' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if> "

            + "<if test='billStatusList != null'> "
            + " and bill_status in "
            + "<foreach item='item' index='index' collection='billStatusList' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if> "

            + "<if test='adjustTypeList != null'> "
            + " and adjust_type in "
            + "<foreach item='item' index='index' collection='adjustTypeList' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if> "

            + "<if test='channelTypeList != null'> "
            + " and reserve_bigint01 in "
            + "<foreach item='item' index='index' collection='channelTypeList' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if> "

            + "<if test='orderNo != null'>"
            + "and order_no = #{orderNo} "
            + "</if>"

            + "<if test='cpCPhyWarehouseId != null'>"
            + "and cp_c_phy_warehouse_id = #{cpCPhyWarehouseId} "
            + "</if>"

            + "<if test='cpCLogisticsId != null'>"
            + "and cp_c_logistics_id = #{cpCLogisticsId} "
            + "</if>"

            + "<if test='psCSkuId != null'>"
            + " and EXISTS(SELECT t2.ID FROM ac_f_payable_adjustment_item t2 where ac_f_payable_adjustment.ID=t2.ac_f_payable_adjustment_id and t2.ps_c_sku_id = #{psCSkuId}) "
            + "</if>"

            + "<if test='gbcode != null'>"
            + " and EXISTS(SELECT t2.ID FROM ac_f_payable_adjustment_item t2 where ac_f_payable_adjustment.ID=t2.ac_f_payable_adjustment_id and t2.gbcode in "
            + "<foreach item='item' index='index' collection='gbcode' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            +") "
            + "</if>"

            + "<if test='creationdateStart != null'>"
            + " and (creationdate between #{creationdateStart} and #{creationdateEnd}) "
            + "</if>"

            + "<if test='guestTrialTimeStart != null'>"
            + " and (guest_trial_time between #{guestTrialTimeStart} and #{guestTrialTimeEnd}) "
            + "</if>"

            + "<if test='financialTrialTimeStart != null'>"
            + " and (financial_trial_time between #{financialTrialTimeStart} and #{financialTrialTimeEnd}) "
            + "</if>"

            + "</where>"
            + " order by id desc"

            + "<if test='pageSize != null'>"
            + " LIMIT #{pageSize} OFFSET #{offset} "
            + "</if>"

            + "</script>")
    List<AcFPayableAdjustmentExDO> selectAcFPayableAdjustmentListByWhere(@Param("billNo") String[] billNo,
                                                                         @Param("tid") String[] tid,
                                                                         @Param("billTypeList") List<Long> billTypeList,
                                                                         @Param("billStatusList") List<Long> billStatusList,
                                                                         @Param("adjustTypeList") List<Long> adjustTypeList,
                                                                         @Param("orderNo") String orderNo,
                                                                         @Param("cpCPhyWarehouseId") Long cpCPhyWarehouseId,
                                                                         @Param("cpCLogisticsId") Long cpCLogisticsId,
                                                                         @Param("psCSkuId") Long psCSkuId,
                                                                         @Param("gbcode") String[] gbcode,
                                                                         @Param("creationdateStart") Date creationdateStart,
                                                                         @Param("creationdateEnd") Date creationdateEnd,
                                                                         @Param("guestTrialTimeStart") Date guestTrialTimeStart,
                                                                         @Param("guestTrialTimeEnd") Date guestTrialTimeEnd,
                                                                         @Param("financialTrialTimeStart") Date financialTrialTimeStart,
                                                                         @Param("financialTrialTimeEnd") Date financialTrialTimeEnd,
                                                                         @Param("channelTypeList") List<Long> channelTypeList,
                                                                         @Param("pageSize") Integer pageSize,
                                                                         @Param("offset") Integer offset);


    @Select("<script>"
            + "SELECT COUNT(1) FROM AC_F_PAYABLE_ADJUSTMENT "
            + "<where> "
            + "<if test='billNo != null'>"
            + "and bill_no in "
            + "<foreach item='item' index='index' collection='billNo' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if>"

            + "<if test='tid != null'>"
            + "and tid in "
            + "<foreach item='item' index='index' collection='tid' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if>"

            + "<if test='billTypeList != null'> "
            + " and bill_type in "
            + "<foreach item='item' index='index' collection='billTypeList' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if> "

            + "<if test='billStatusList != null'> "
            + " and bill_status in "
            + "<foreach item='item' index='index' collection='billStatusList' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if> "

            + "<if test='adjustTypeList != null'> "
            + " and adjust_type in "
            + "<foreach item='item' index='index' collection='adjustTypeList' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if> "

            + "<if test='channelTypeList != null'> "
            + " and reserve_bigint01 in "
            + "<foreach item='item' index='index' collection='channelTypeList' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            + "</if> "

            + "<if test='orderNo != null'>"
            + "and order_no = #{orderNo} "
            + "</if>"

            + "<if test='cpCPhyWarehouseId != null'>"
            + "and cp_c_phy_warehouse_id = #{cpCPhyWarehouseId} "
            + "</if>"

            + "<if test='cpCLogisticsId != null'>"
            + "and cp_c_logistics_id = #{cpCLogisticsId} "
            + "</if>"

            + "<if test='psCSkuId != null'>"
            + " and EXISTS(SELECT t2.ID FROM ac_f_payable_adjustment_item t2 where ac_f_payable_adjustment.ID=t2.ac_f_payable_adjustment_id and t2.ps_c_sku_id = #{psCSkuId}) "
            + "</if>"

            + "<if test='gbcode != null'>"
            + " and EXISTS(SELECT t2.ID FROM ac_f_payable_adjustment_item t2 where ac_f_payable_adjustment.ID=t2.ac_f_payable_adjustment_id and t2.gbcode in "
            + "<foreach item='item' index='index' collection='gbcode' open='(' separator=',' close=')'> "
            + "#{item}"
            + "</foreach>"
            +") "
            + "</if>"

            + "<if test='creationdateStart != null'>"
            + " and (creationdate between #{creationdateStart} and #{creationdateEnd}) "
            + "</if>"

            + "<if test='guestTrialTimeStart != null'>"
            + " and (guest_trial_time between #{guestTrialTimeStart} and #{guestTrialTimeEnd}) "
            + "</if>"

            + "<if test='financialTrialTimeStart != null'>"
            + " and (financial_trial_time between #{financialTrialTimeStart} and #{financialTrialTimeEnd}) "
            + "</if>"

            + "</where>"
            + "</script>")
    Long selectAcFPayableAdjustmentListCount(@Param("billNo") String[] billNo,
                                             @Param("tid") String[] tid,
                                             @Param("billTypeList") List<Long> billTypeList,
                                             @Param("billStatusList") List<Long> billStatusList,
                                             @Param("adjustTypeList") List<Long> adjustTypeList,
                                             @Param("orderNo") String orderNo,
                                             @Param("cpCPhyWarehouseId") Long cpCPhyWarehouseId,
                                             @Param("cpCLogisticsId") Long cpCLogisticsId,
                                             @Param("psCSkuId") Long psCSkuId,
                                             @Param("gbcode") String[] gbcode,
                                             @Param("creationdateStart") Date creationdateStart,
                                             @Param("creationdateEnd") Date creationdateEnd,
                                             @Param("guestTrialTimeStart") Date guestTrialTimeStart,
                                             @Param("guestTrialTimeEnd") Date guestTrialTimeEnd,
                                             @Param("financialTrialTimeStart") Date financialTrialTimeStart,
                                             @Param("financialTrialTimeEnd") Date financialTrialTimeEnd,
                                             @Param("channelTypeList") List<Long> channelTypeList);
}