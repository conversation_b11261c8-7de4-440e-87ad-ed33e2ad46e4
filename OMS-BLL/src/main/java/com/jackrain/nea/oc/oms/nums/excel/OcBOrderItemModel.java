package com.jackrain.nea.oc.oms.nums.excel;

import com.jackrain.nea.util.excel.XlsAno;
import com.jackrain.nea.util.excel.XlsDBAno;
import com.jackrain.nea.util.excel.XlsSt;
import com.jackrain.nea.util.excel.XlsTyp;

import java.math.BigDecimal;

/**
 * @author: xiWen.z
 * create at: 2019/8/12 0012
 */
@XlsDBAno(name = "oc_b_order_item", desc = "订单明细", index = 1, sort = "id:asc", st = {XlsSt.DB, XlsSt.ES, XlsSt.R3})
public class OcBOrderItemModel {


    @XlsAno(name = "oc_b_order_id", index = 0, desc = "订单ID")
    private Long ocBOrderId;

    @XlsAno(name = "ps_c_pro_ename", desc = "商品名称", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 1)
    private String psCProEname;

    @XlsAno(name = "ps_c_pro_ecode", desc = "商品编码", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 2)
    private String psCProEcode;

    @XlsAno(name = "pt_pro_name", desc = "组合商品名称", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 3)
    private String ptProName;

    @XlsAno(name = "giftbag_sku", desc = "组合商品SKU", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 4)
    private String giftbagSku;

    @XlsAno(name = "ps_c_sku_ecode", desc = "SKU编码", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 5)
    private String psCSkuEcode;

    @XlsAno(name = "ps_c_sku_ename", desc = "SKU名称", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 6)
    private String psCSkuEname;


    @XlsAno(name = "qty", desc = "数量", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 7)
    private BigDecimal qty;

    @XlsAno(name = "qty_lost", desc = "缺货数量", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 8)
    private BigDecimal qtyLost;

    @XlsAno(name = "price", desc = "原价", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 9)
    private BigDecimal price;

    @XlsAno(name = "price_list", desc = "零售价", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 10)
    private BigDecimal priceList;

    @XlsAno(name = "real_amt", desc = "成交金额", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 11)
    private BigDecimal realAmt;

    @XlsAno(name = "amt_discount", desc = "优惠金额", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 12)
    private BigDecimal amtDiscount;

    @XlsAno(name = "adjust_amt", desc = "调整金额", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 13)
    private BigDecimal adjustAmt;

    @XlsAno(name = "order_split_amt", desc = "平摊金额", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 14)
    private BigDecimal orderSplitAmt;

    @XlsAno(name = "refund_status", desc = "退款状态", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 15)
    private Integer refundStatus;

    @XlsAno(name = "is_gift", desc = "是否赠品", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 16)
    private Integer isGift;

    @XlsAno(name = "num_iid", desc = "平台商品ID", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 17)
    private String numIid;

    @XlsAno(name = "sku_numiid", desc = "平台SKUID", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 18)
    private String skuNumiid;

    @XlsAno(name = "estimate_con_time", desc = "预计发货日期", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 19)
    private String estimateConTime;

//    @XlsAno(name = "is_lackstock", desc = "实物报缺", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 18)
//    private Integer isLackstock;
//
//    @XlsAno(name = "distribution_price", desc = "分销金额", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 19)
//    private BigDecimal distributionPrice;
    //    @XlsAno(name = "ps_c_clr_ename", desc = "颜色", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 2)
//    private String psCClrEname;
//
//    @XlsAno(name = "ps_c_size_ename", desc = "尺码", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 3)
//    private String psCSizeEname;
//
//    @XlsAno(name = "barcode", desc = "国标码", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 4)
//    private String barcode;


}
