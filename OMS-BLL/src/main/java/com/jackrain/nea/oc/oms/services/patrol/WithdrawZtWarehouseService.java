package com.jackrain.nea.oc.oms.services.patrol;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogTypeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OcBOrderTheAuditService;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.web.face.User;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author: 黄世新
 * @Date: 2019/11/15 1:30 下午
 * @Version 1.0
 */
@Slf4j
@Component
public class WithdrawZtWarehouseService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBOrderTheAuditService ocBOrderTheAuditService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OcBOrderItemMapper orderItemMapper;


    public ValueHolderV14 withdrawZtWarehouse(Integer pageSize) {
        ValueHolderV14 holderV14 = new ValueHolderV14();

        try {
            //从ES上捞订单为配货中订单   仓库为中通仓的仓库//通过es查询退款失败的退单
            List<Long> esIdList = ES4Order.findIdByQtySplitAndPage(9, pageSize);

            if (CollectionUtils.isEmpty(esIdList)) {
                return null;
            }

            List<OcBOrder> ocBOrders = ocBOrderMapper.selectByIdsListDistribution(esIdList);
            if (CollectionUtils.isEmpty(ocBOrders)) {
                holderV14.setCode(-1);
                holderV14.setMessage("没有查到订单数据");
                return holderV14;
            }
            List<OcBOrderItem> orderItemsList = orderItemMapper.selectOrderItemsByOrderIds(esIdList);
            Map<Long, List<OcBOrderItem>> map = new HashMap<>();
            for (OcBOrderItem ocBOrderItem : orderItemsList) {
                Long ocBOrderId = ocBOrderItem.getOcBOrderId();
                List<OcBOrderItem> items = null;
                if (!map.containsKey(ocBOrderId)) {
                    items = new ArrayList<>();
                } else {
                    items = map.get(ocBOrderId);
                }
                items.add(ocBOrderItem);
                map.put(ocBOrderId, items);

            }
            //调用反审核服务
            User rootUser = SystemUserResource.getRootUser();
            for (OcBOrder ocBOrder : ocBOrders) {
                boolean flag = this.lockUpBackExamine(ocBOrder, rootUser);
                Long id = ocBOrder.getId();
                if (flag) {
                    //发审核成功后清空逻辑发货单
                    boolean isSussess = this.emptyLogic(ocBOrder, map, rootUser);
                    if (isSussess) {
                        //清空逻辑发货单成功之后 将订单状态改为 待分配, 清空仓库 和物流单号 将审核状态置为分仓状态  自动审核状态改为0
                        ocBOrderMapper.updateOrderEmptyWarehouse(id);
                    } else {
                        OcBOrder order = new OcBOrder();
                        order.setId(id);
                        order.setPosBillId(14L);
                        ocBOrderMapper.updateById(order);
                    }
                } else {
                    OcBOrder order = new OcBOrder();
                    order.setId(id);
                    order.setPosBillId(13L);
                    ocBOrderMapper.updateById(order);
                }
            }
            holderV14.setCode(0);
            holderV14.setMessage("成不成功我也不知道,请看结果");
        } catch (Exception e) {
            holderV14.setCode(-1);
            holderV14.setMessage("失败我是知道的,看结果也是失败的");
        }
        return holderV14;
    }


    public boolean emptyLogic(OcBOrder ocBOrder, Map<Long, List<OcBOrderItem>> map, User user) {
        List<OcBOrderItem> orderItems = map.get(ocBOrder.getId());
        for (OcBOrderItem orderItem : orderItems) {
            orderItem.setQty(BigDecimal.ZERO);
        }
        //调用释放库存服务
        OcBOrderRelation relation = new OcBOrderRelation();
        relation.setOrderInfo(ocBOrder);
        relation.setOrderItemList(orderItems);
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 调用释放库存服务入参:{}", JSONObject.toJSONString(relation));
        }
        ValueHolderV14 sgValueHolder = sgRpcService.querySearchStockAndModifyGoodsInfo(relation, user);
        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + " 调用释放库存服务返参:{}", sgValueHolder.toJSONObject());
        }
        if (sgValueHolder.getCode() == -1) {
            return false;
        } else {
            return true;
        }
    }


    /**
     * 配货中加锁调用反审核
     *
     * @param ocBOrder
     * @param operateUser
     */
    private boolean lockUpBackExamine(OcBOrder ocBOrder, User operateUser) {
        //todo  对订单加锁 调用反审核
        String lockRedisKey = BllRedisKeyResources.buildLockOrderKey(ocBOrder.getId());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                boolean isSuccess = this.toExamineOrder(ocBOrder, operateUser);
                return isSuccess;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 配货中调用反审核出错", e);
            return false;
        } finally {
            redisLock.unlock();
        }
    }

    /**
     * 已审核订单调用反审核接口
     *
     * @param ocBOrder
     */
    private boolean toExamineOrder(OcBOrder ocBOrder, User user) {
        try {
            Long id = ocBOrder.getId();
            ValueHolderV14 holderV14 = new ValueHolderV14();
            boolean isSuccess = ocBOrderTheAuditService.updateOrderInfo(user, holderV14, id, LogTypeEnum.NOT_CAPTURED_SCENE.getType());
            return isSuccess;
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 调用反审核失败", e);
            return false;
        }

    }
}
