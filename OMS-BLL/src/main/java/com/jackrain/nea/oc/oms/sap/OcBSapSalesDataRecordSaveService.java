package com.jackrain.nea.oc.oms.sap;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataRecordMapper;
import com.jackrain.nea.oc.oms.model.constant.OcBSapSalesDataRecordConstant;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecord;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataRecordItem;
import com.jackrain.nea.util.*;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Description: 销售数据记录表
 *
 * @Author: guo.kw
 * @Since: 2022/8/23
 * create at: 2022/8/23 10:50
 */
@Component
@Slf4j
public class OcBSapSalesDataRecordSaveService {


    @Autowired
    private OcBSapSalesDataRecordMapper ocBSapSalesDataRecordMapper;
    @Autowired
    private OcBSapSalesDataRecordItemMapper ocBSapSalesDataRecordItemMapper;
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");

    /**
     * 前段入口
     *
     * @param session
     * @return
     */
    public ValueHolder save(QuerySession session) {
        User user = session.getUser();
        DefaultWebEvent event = session.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                        event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("start OcBSapSalesDataRecordSaveService objId:{}, param:{}",
                    "OcBSapSalesDataRecordSaveService save:{}", session.getId(), param));
        }

        if (Objects.isNull(param)) {
            return ValueHolderUtils.fail("参数异常!");
        }
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        if (Objects.isNull(fixColumn)) {
            return ValueHolderUtils.fail("参数异常,fixColumn为空!");
        }
        JSONObject jsonObject = fixColumn.getJSONObject(OcBSapSalesDataRecordConstant.MAIN_TABLE);
        if (MapUtils.isEmpty(jsonObject)) {
            return ValueHolderUtils.fail("参数异常,OcBSapSalesDataRecord为空!");
        }

        OcBSapSalesDataRecord ocBSapSalesDataRecord = JSONObject.parseObject(jsonObject.toJSONString(), OcBSapSalesDataRecord.class);
        if (Objects.isNull(ocBSapSalesDataRecord)) {
            return ValueHolderUtils.fail("参数异常,fixColumn转换异常!");
        }
        JSONArray itemTable = fixColumn.getJSONArray(OcBSapSalesDataRecordConstant.ITEM_TABLE);
        List<OcBSapSalesDataRecordItem> ocBSapSalesDataRecordItems = new ArrayList<>();
        if (itemTable != null && itemTable.size() > 0) {
            ocBSapSalesDataRecordItems = itemTable.toJavaList(OcBSapSalesDataRecordItem.class);
        }
        Long objectId = param.getLong("objid");
        OcBSapSalesDataRecordSaveService bean = ApplicationContextHandle.getBean(this.getClass());
        return bean.save(ocBSapSalesDataRecord, ocBSapSalesDataRecordItems, objectId, user);
    }

    /**
     * 数据新增保存
     *
     * @param ocBSapSalesDataRecord      主表信息
     * @param ocBSapSalesDataRecordItems 明细信息
     * @param objectId                   id
     * @param user                       用户信息
     * @return 返回值
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder save(OcBSapSalesDataRecord ocBSapSalesDataRecord, List<OcBSapSalesDataRecordItem> ocBSapSalesDataRecordItems, Long objectId, User user) {
        checkParams(ocBSapSalesDataRecord, ocBSapSalesDataRecordItems, objectId, user);
        if (objectId != null && objectId < 0) {
            return insert(ocBSapSalesDataRecord, ocBSapSalesDataRecordItems, user);
        } else {
            return update(ocBSapSalesDataRecord, ocBSapSalesDataRecordItems, objectId, user);
        }
    }

    /**
     * 参数校验
     *
     * @param ocBSapSalesDataRecord      主表
     * @param ocBSapSalesDataRecordItems 明细
     * @param objectId                   id
     * @param user                       用户信息
     */
    private void checkParams(OcBSapSalesDataRecord ocBSapSalesDataRecord, List<OcBSapSalesDataRecordItem> ocBSapSalesDataRecordItems, Long objectId, User user) {
        //判断汇总状态是否为为汇总
        if (objectId != null && objectId < 0) {
            //新增校验
            AssertUtils.cannot(StringUtils.isBlank(ocBSapSalesDataRecord.getBillType()), "单据类别不可为空！", user.getLocale());
            AssertUtils.cannot(StringUtils.isBlank(ocBSapSalesDataRecord.getBillNo()), "单据编号不可为空！", user.getLocale());
            AssertUtils.cannot(ocBSapSalesDataRecord.getSumType() == null, "汇总类型不可为空！", user.getLocale());
            AssertUtils.cannot(ocBSapSalesDataRecord.getInTime() == null, "出入库时间不可为空！", user.getLocale());
            //库存校验
            if (OcBSapSalesDataRecordConstant.SUM_TYPE_WMJ.equals(ocBSapSalesDataRecord.getSumType())
                || OcBSapSalesDataRecordConstant.SUM_TYPE_CWMJ.equals(ocBSapSalesDataRecord.getSumType())
                || OcBSapSalesDataRecordConstant.SUM_TYPE_BCTZ.equals(ocBSapSalesDataRecord.getSumType())) {
                //AssertUtils.cannot(StringUtils.isBlank(ocBSapSalesDataRecord.getCostCenter()), "成本中心不可为空！", user.getLocale());
                //校验明细
                AssertUtils.cannot(CollectionUtils.isEmpty(ocBSapSalesDataRecordItems), "明细不可为空！", user.getLocale());
                for (OcBSapSalesDataRecordItem object : ocBSapSalesDataRecordItems) {
                    AssertUtils.cannot(StringUtils.isBlank(object.getSku()), "明细sku不可为空！", user.getLocale());
                    AssertUtils.cannot(object.getQty() == null, "明细数量不可为空！", user.getLocale());
                    AssertUtils.cannot(StringUtils.isBlank(object.getUnit()), "明细单位不可为空！", user.getLocale());
                    AssertUtils.cannot(object.getCpCStoreId() == null, "明细仓库不可为空！", user.getLocale());
                    AssertUtils.cannot(StringUtils.isBlank(object.getFactoryCode()), "明细工厂编码不可为空！", user.getLocale());
                    //AssertUtils.cannot(StringUtils.isBlank(object.getBatch()), "明细批次不可为空！", user.getLocale());
                }
            } else {
                AssertUtils.cannot(ocBSapSalesDataRecord.getCpCShopId() == null, "店铺不可为空！", user.getLocale());
                //AssertUtils.cannot(ocBSapSalesDataRecord.getMiddlegroundBillType() == null, "业务类型不可为空！", user.getLocale());
                AssertUtils.cannot(StringUtils.isBlank(ocBSapSalesDataRecord.getMiddlegroundBillTypeName()), "业务类型不可为空！", user.getLocale());
                //AssertUtils.cannot(StringUtils.isBlank(ocBSapSalesDataRecord.getSapBillType()), "SAP单据类型不可为空！", user.getLocale());
                //AssertUtils.cannot(StringUtils.isBlank(ocBSapSalesDataRecord.getSalesOrganization()), "销售组织不可为空！", user.getLocale());
                AssertUtils.cannot(ocBSapSalesDataRecord.getSumStatus() == null, "汇总状态不可为空！", user.getLocale());
                //校验明细
                AssertUtils.cannot(CollectionUtils.isEmpty(ocBSapSalesDataRecordItems), "明细不可为空！", user.getLocale());
                for (OcBSapSalesDataRecordItem object : ocBSapSalesDataRecordItems) {
                    AssertUtils.cannot(StringUtils.isBlank(object.getSku()), "明细sku不可为空！", user.getLocale());
                    //AssertUtils.cannot(StringUtils.isBlank(object.getLineType()), "明细行项目类型不可为空！", user.getLocale());
                    AssertUtils.cannot(object.getQty() == null, "明细数量不可为空！", user.getLocale());
                    AssertUtils.cannot(StringUtils.isBlank(object.getUnit()), "明细单位不可为空！", user.getLocale());
                    if(!OcBSapSalesDataRecordConstant.SUM_TYPE_NK.equals(ocBSapSalesDataRecord.getSumType())){
                        AssertUtils.cannot(object.getCpCStoreId() == null, "明细仓库不可为空！", user.getLocale());
                        AssertUtils.cannot(StringUtils.isBlank(object.getFactoryCode()), "明细工厂编码不可为空！", user.getLocale());
                    }
                    AssertUtils.cannot(object.getAmt() == null, "明细成交金额不可为空！", user.getLocale());
                    AssertUtils.cannot(StringUtils.isBlank(object.getProType()), "明细商品类型不可为空！", user.getLocale());
                    //AssertUtils.cannot(StringUtils.isBlank(object.getBatch()), "明细批次不可为空！", user.getLocale());
                }
            }

        } else {
            //保存校验
            OcBSapSalesDataRecord ocBSapSalesDataRecord1 = ocBSapSalesDataRecordMapper.selectById(objectId);
            AssertUtils.cannot(OcBSapSalesDataRecordConstant.SUM_STATUS_ONE.equals(ocBSapSalesDataRecord1.getSumStatus()), "当前记录已汇总，不允许编辑!", user.getLocale());
        }
    }

    /**
     * 新增
     *
     * @param ocBSapSalesDataRecord      主表
     * @param ocBSapSalesDataRecordItems 明细
     * @param user                       用户小新
     * @return 返回值
     */
    public ValueHolder insert(OcBSapSalesDataRecord ocBSapSalesDataRecord, List<OcBSapSalesDataRecordItem> ocBSapSalesDataRecordItems, User user) {
        Long objectId = ModelUtil.getSequence(OcBSapSalesDataRecordConstant.MAIN_TABLE);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start OcBSapSalesDataRecordSaveService.insert main request={}, item request={}",
                    "OcBSapSalesDataRecordSaveService.insert", objectId), JSONObject.toJSONString(ocBSapSalesDataRecord), JSONObject.toJSONString(ocBSapSalesDataRecordItems));
        }
        ocBSapSalesDataRecord.setId(objectId);
        OmsStorageUtils.setBModelDefalutData(ocBSapSalesDataRecord, user);
        ocBSapSalesDataRecordMapper.insert(ocBSapSalesDataRecord);
        if (CollectionUtils.isNotEmpty(ocBSapSalesDataRecordItems)) {
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("Start OcBSapSalesDataRecordSaveService.insert item.size={}",
                        "OcBSapSalesDataRecordSaveService.insert.size"), ocBSapSalesDataRecordItems.size());
            }
            for (OcBSapSalesDataRecordItem item : ocBSapSalesDataRecordItems) {
                item.setOcBSapSalesDataRecordId(objectId);
                Long itemId = ModelUtil.getSequence(OcBSapSalesDataRecordConstant.ITEM_TABLE);
                item.setId(itemId);
                OmsStorageUtils.setBModelDefalutData(item, user);
            }
        }
        List<List<OcBSapSalesDataRecordItem>> partition = Lists.partition(ocBSapSalesDataRecordItems, OcBSapSalesDataRecordConstant.OMS_COMMON_INSERT_PAGE_SIZE);
        for (List<OcBSapSalesDataRecordItem> listItem : partition) {
            ocBSapSalesDataRecordItemMapper.batchInsert(listItem);
        }

        ValueHolder success = ValueHolderUtils.success();
        success.put("id", objectId);
        return success;
    }

    /**
     * 保存
     *
     * @param ocBSapSalesDataRecord      主表
     * @param ocBSapSalesDataRecordItems 明细
     * @param objectId                   ID
     * @param user                       用户小新
     * @return 返回值
     */
    public ValueHolder update(OcBSapSalesDataRecord ocBSapSalesDataRecord, List<OcBSapSalesDataRecordItem> ocBSapSalesDataRecordItems, Long objectId, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start SgBSapInformationMappingService.update main request={}, item request={}",
                    "SgBSapInformationMappingService.update", objectId), JSONObject.toJSONString(ocBSapSalesDataRecord), JSONObject.toJSONString(ocBSapSalesDataRecordItems));
        }
        //查询原始数据
        OcBSapSalesDataRecord old = ocBSapSalesDataRecordMapper.selectById(objectId);

        ocBSapSalesDataRecord.setId(objectId);
        String mergeCode = "";
        //库存调整单 业务类型  业务类型 + 仓 + 成本中心
        //其他为 汇总类型 + sap类型 + 店铺 + 出入库时间
        if (OcBSapSalesDataRecordConstant.SUM_TYPE_WMJ.equals(old.getSumType())
            || OcBSapSalesDataRecordConstant.SUM_TYPE_CWMJ.equals(old.getSumType())
            || OcBSapSalesDataRecordConstant.SUM_TYPE_BCTZ.equals(old.getSumType())) {
            List<OcBSapSalesDataRecordItem> ocBSapSalesDataRecordItems1 = ocBSapSalesDataRecordItemMapper.selectList(new LambdaQueryWrapper<OcBSapSalesDataRecordItem>()
                    .eq(OcBSapSalesDataRecordItem::getOcBSapSalesDataRecordId, objectId)
                    .eq(OcBSapSalesDataRecordItem::getIsactive, OcBSapSalesDataRecordConstant.ISACTIVE_YES));
            OcBSapSalesDataRecordItem ocBSapSalesDataRecordItem = ocBSapSalesDataRecordItems1.get(0);
            mergeCode = old.getMiddlegroundBillTypeName() + ocBSapSalesDataRecordItem.getCpCStoreId() + old.getCostCenter() + "";
            ocBSapSalesDataRecord.setMergeCode(MD5Util.encryptByMD5(mergeCode));
        } else {
            String sapBillType = ocBSapSalesDataRecord.getSapBillType();
            if (StringUtils.isNotBlank(sapBillType)) {
                mergeCode = old.getSumType() + sapBillType + old.getCpCShopId() + simpleDateFormat.format(old.getInTime());
                ocBSapSalesDataRecord.setMergeCode(MD5Util.encryptByMD5(mergeCode));
                ocBSapSalesDataRecord.setAbnormalReason("");
            }
            if (StringUtils.isBlank(sapBillType) && StringUtils.isNotBlank(old.getSapBillType())) {
                mergeCode = old.getSumType() + old.getSapBillType() + old.getCpCShopId() + simpleDateFormat.format(old.getInTime());
                ocBSapSalesDataRecord.setMergeCode(MD5Util.encryptByMD5(mergeCode));
                ocBSapSalesDataRecord.setAbnormalReason("");
            }
        }
        OmsStorageUtils.setBModelDefalutDataByUpdate(ocBSapSalesDataRecord, user);
        ocBSapSalesDataRecordMapper.updateById(ocBSapSalesDataRecord);
        return ValueHolderUtils.success();
    }

}
