package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderWarning;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OcBReturnOrderWarningMapper extends ExtentionMapper<OcBReturnOrderWarning> {


    @Update("<script> "
            + "UPDATE oc_b_return_order_warning set manage_result = #{manageResult}, modifierid = #{userId}," +
            " modifierename = #{userName}, modifiername = #{userName}, modifieddate = NOW()" +
            " where id IN "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    Integer batchUpdate(@Param("ids") List<Long> ids, @Param("userId") int userId, @Param("userName") String userName, @Param("manageResult") String manageResult);

    @Update("<script> "
            + "UPDATE oc_b_return_order_warning set auto_intercept = '1', manage_result = #{manageResult}, modifierid = #{userId}," +
            " modifierename = #{userName}, modifiername = #{userName}, modifieddate = NOW()" +
            " where id IN "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    Integer batchUpdateNew(@Param("ids") List<Long> ids, @Param("userId") int userId, @Param("userName") String userName, @Param("manageResult") String manageResult);
}