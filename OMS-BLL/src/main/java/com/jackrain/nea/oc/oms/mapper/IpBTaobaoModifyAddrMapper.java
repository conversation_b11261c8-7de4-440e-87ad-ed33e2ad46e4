package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoModifyAddr;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

/**
 * 淘宝修改地址表
 *
 * @date 2019/3/11
 * @author: ming.fz
 */
@Mapper
@Component
public interface IpBTaobaoModifyAddrMapper extends ExtentionMapper<IpBTaobaoModifyAddr> {

    /**
     * 查询平台单号待修改地址表字段是否更新
     *
     * @param sourcecode
     * @return IpBTaobaoModifyAddr
     */
    @Select("SELECT * FROM IP_B_TAOBAO_MODIFY_ADDR WHERE sourcecode=#{sourcecode} AND ISACTIVE = 'Y'")
    IpBTaobaoModifyAddr selectSourcecodeByIsUpdate(@Param("sourcecode") String sourcecode);

    /**
     * 查询平台单号待修改地址表字段是否更新
     *
     * @param sourcecode
     * @return IpBTaobaoModifyAddr
     */
    @Update("update ip_b_taobao_modify_addr set reserve_bigint01=#{status},modifieddate = now(),reserve_varchar01=#{msg} where sourcecode=#{sourcecode}")
    int updateBySourceCode(@Param("sourcecode") String sourcecode, @Param("status") Long status, @Param("msg") String msg);


    /**
     * 查询平台单号待修改地址表字段是否更新
     *
     * @param sourcecode
     * @return IpBTaobaoModifyAddr
     */
    @Update("update ip_b_taobao_modify_addr set IS_UPDATE=#{status},modifieddate = now(),remark=#{remark} where sourcecode=#{sourcecode}")
    int updateR3StatusBySourceCode(@Param("sourcecode") String sourcecode, @Param("status") Integer status, @Param("remark") String remark);
}
