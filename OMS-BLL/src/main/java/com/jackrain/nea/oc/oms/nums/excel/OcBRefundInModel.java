package com.jackrain.nea.oc.oms.nums.excel;

import com.jackrain.nea.util.excel.XlsAno;
import com.jackrain.nea.util.excel.XlsDBAno;
import com.jackrain.nea.util.excel.XlsSt;
import com.jackrain.nea.util.excel.XlsTyp;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: xiWen.z
 * create at: 2019/8/15 0015
 */
@XlsDBAno(name = "oc_b_refund_in", desc = "退货入库单", index = 0, sort = "id:asc", st = {XlsSt.DB, XlsSt.ES, XlsSt.R3})
public class OcBRefundInModel {

    @XlsAno(name = "id", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 10, desc = "入库编号")
    private Long id;

    @XlsAno(name = "orig_order_no", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 20, desc = "原单单号")
    private Long origOrderNo;

    @XlsAno(name = "source_code", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 30, desc = "原平台单号")
    private String sourceCode;

    @XlsAno(name = "in_store_ename", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 40, desc = "入库仓库")
    private String inStoreEname;

    @XlsAno(name = "cp_c_phy_warehouse_ename", type = XlsTyp.STRING, index = 50, desc = "入库实体仓名称")
    private String cpCPhyWarehouseEname;

    @XlsAno(name = "user_nick", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 60, desc = "买家昵称")
    private String userNick;

    @XlsAno(name = "logistic_number", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 70, desc = "物流单号")
    private String logisticNumber;

    @XlsAno(name = "batch_no", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 80, desc = "批次编号")
    private String batchNo;

    @XlsAno(name = "receiver_address", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 90, desc = "发件地址")
    private String receiverAddress;

    @XlsAno(name = "in_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 100, desc = "入库状态")
    private Integer inStatus;

    @XlsAno(name = "receiver_mobile", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 110, desc = "收件人手机")
    private String receiverMobile;

    @XlsAno(name = "receiver_name", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 120, desc = "收件人")
    private String receiverName;

    @XlsAno(name = "modifieddate", value = {XlsSt.NORMAL}, type = XlsTyp.DATE, index = 130, desc = "修改时间")
    private Date modifieddate;

    @XlsAno(name = "creationdate", value = {XlsSt.NORMAL}, type = XlsTyp.DATE, index = 140, desc = "创建时间")
    private Date creationdate;

    @XlsAno(name = "match_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 150, desc = "匹配状态")
    private Integer matchStatus;

    @XlsAno(name = "all_sku", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 160, desc = "商品条码")
    private String allSku;

    @XlsAno(name = "qty_all", value = {XlsSt.STATS}, type = XlsTyp.DOUBLE, index = 170, desc = "入库数量", ignore = 3)
    private BigDecimal qtyAll;

    @XlsAno(name = "remark", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 180, desc = "备注")
    private String remark;

    @XlsAno(name = "matcher", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 190, desc = "匹配人")
    private String matcher;

    @XlsAno(name = "matched_time", value = {XlsSt.NORMAL}, type = XlsTyp.DATE, index = 200, desc = "匹配时间")
    private String matched_time;

    @XlsAno(name = "is_off_match", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 210, desc = "是否关闭匹配")
    private Integer isOffMatch;

    @XlsAno(name = "virtual_in_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 210, desc = "虚拟入库状态")
    private Integer virtualInStatus;

    @XlsAno(name = "num_less", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 220, desc = "数异少")
    private String numLess;

    @XlsAno(name = "num_more", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 230, desc = "数异多")
    private String numMore;

    @XlsAno(name = "product_diff", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 240, desc = "品异")
    private String productDiff;
}
