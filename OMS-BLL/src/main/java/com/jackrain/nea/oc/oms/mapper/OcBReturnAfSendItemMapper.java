package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import org.apache.ibatis.annotations.*;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface OcBReturnAfSendItemMapper extends ExtentionMapper<OcBReturnAfSendItem> {
    @Select("select sum(amt_return+freight) from oc_b_return_af_send_item where oc_b_return_af_send_id =  #{afSendId}")
    BigDecimal calculateApplicationAmount(Long afSendId);


    @Select("<script> "
            + "SELECT * FROM oc_b_return_af_send_item WHERE isactive = 'Y' and oc_b_return_af_send_id "
            + "in <foreach item='item' index='index' collection='ocBReturnAfSendIds' open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBReturnAfSendItem> selectByOcBReturnAfSendIdList(@Param("ocBReturnAfSendIds") List<Long> ocBReturnAfSendIds);

    @Update("UPDATE oc_b_return_af_send_item SET bill_type = #{billType} WHERE oc_b_return_af_send_id = #{sendId} AND" +
            " relation_bill_id = #{billId}")
    int updateOcBReturnAfSendItem(@Param("sendId") Long sendId, @Param("billId") Long billId, @Param("billType") Integer billType);


    @Update("UPDATE oc_b_return_af_send_item SET bill_type = #{billType}, intercept_status = #{interceptStatus} WHERE oc_b_return_af_send_id = #{sendId} AND" +
            " relation_bill_id = #{billId}")
    int updateOcBReturnAfSendItemIntercept(@Param("sendId") Long sendId, @Param("billId") Long billId,
                                           @Param("billType") Integer billType, @Param("interceptStatus") Integer interceptStatus);


    @Select("SELECT * FROM oc_b_return_af_send_item WHERE oc_b_return_af_send_id = #{sendId}")
    List<OcBReturnAfSendItem> selectByOcBReturnAfSendIdListBySendId(@Param("sendId") Long sendId);

    @Delete("delete from oc_b_return_af_send_item WHERE oc_b_return_af_send_id = #{sendId}")
    int del(@Param("sendId") Long sendId);

}