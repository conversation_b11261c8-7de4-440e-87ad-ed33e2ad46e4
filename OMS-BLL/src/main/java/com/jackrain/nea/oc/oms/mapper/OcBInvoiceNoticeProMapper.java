package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticePro;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface OcBInvoiceNoticeProMapper extends ExtentionMapper<OcBInvoiceNoticePro> {

    @Select("select * from oc_b_invoice_notice_pro where oc_b_invoice_notice_id = #{mainid}")
    List<OcBInvoiceNoticePro> listByMainid(@Param("mainid") Long mainid);
}