package com.jackrain.nea.oc.oms.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName : CustomerQueryOrderResult  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-12-21 15:22  
 */
@Data
public class CustomerQueryOrderResult implements Serializable {

    private Integer delivery_status;

    //物流单号
    private String logistic_number;

    //平台订单状态
    private String platform_status;

    //面单内容
    private String jitx_voucher_content;

    //出库通知单号
    private String outBillNo;

    //物流公司
    private String cpCLogisticsEcode;
}
