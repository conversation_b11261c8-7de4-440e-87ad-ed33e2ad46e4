package com.jackrain.nea.oc.oms.services.invoice;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.api.CpCShopAuthQueryCmd;
import com.jackrain.nea.cp.entity.CpCShopOrderAuth;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.others.OrderInvoiceSyncPlatformCmd;
import com.jackrain.nea.ip.model.Invoice.InvoiceUploadJdRequest;
import com.jackrain.nea.ip.model.Invoice.InvoiceUploadPddRequest;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.*;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 */
@Slf4j
@Component
public class AcFOrderInvoiceSyncPlatformService {
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private AcFOrderInvoiceMapper acOrderInvoiceMapper;

    @Reference(group = "cp", version = "1.0")
    private CpCShopAuthQueryCmd cpShopAuthQueryCmd;

    @Reference(group = "ip", version = "1.4.0")
    private OrderInvoiceSyncPlatformCmd orderInvoiceSyncPlatformCmd;

    @Autowired
    private InvoiceLogService invoiceLogService;

    @NacosValue(value = "${r3.oms.oc.acfOrderInvoice.upload.pdd.url:http://gw-api.pinduoduo.com/api/router}", autoRefreshed = true)
    private String pddUploadUrl;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        request.setR3(true);
        AcFOrderInvoiceSyncPlatformService service = ApplicationContextHandle.getBean(this.getClass());
        return service.syncPlatform(request);
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder syncPlatform(SgR3BaseRequest request) {
        List<Long> batchObjIds = R3ParamUtils.getBatchObjIds(request);
        // 存储错误的Map
        Map<Long, Object> errorMap = new HashMap<>(batchObjIds.size());
        for (Long objId : batchObjIds) {
            String lockRedisKey = InvoiceConst.AC_F_ORDER_INVOICE + ":" + request.getObjId();
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    AcFOrderInvoice acOrderInvoice = acOrderInvoiceMapper.selectById(objId);
                    if (acOrderInvoice == null) {
                        errorMap.put(objId, "当前记录已不存在！");
                        continue;
                    }
                    //如果【开票状态】=非开票成功状态或同步平台状态为已同步或是否需同步平台状态是否，则提示：“选择的订单发票状态不能同步平台！”
                    if (!OmsParamConstant.TWO.equals(acOrderInvoice.getInvoiceStatus()) || OmsParamConstant.ZERO.equals(acOrderInvoice.getIsSyncPlatform())
                            || (acOrderInvoice.getSyncPlatformStatus() != null && acOrderInvoice.getSyncPlatformStatus() == 1)) {
                        errorMap.put(objId, "选择的订单发票状态不能同步平台！");
                        continue;
                    }
                    if (acOrderInvoice.getInvoiceLinkAddress() == null) {
                        errorMap.put(objId, "同步平台电子发票链接地址不能为空！");
                        continue;
                    }
                    ValueHolderV14 uploadV14;
                    Long cpShopId = acOrderInvoice.getCpCShopId();
                    Long cpPlatformId = acOrderInvoice.getCpCPlatformId();
                    ValueHolderV14<CpCShopOrderAuth> authValueHolderV14 =
                            cpShopAuthQueryCmd.queryByShopIdAndPlatformId(cpShopId, cpPlatformId);
                    if (!authValueHolderV14.isOK()) {
                        errorMap.put(objId, authValueHolderV14.getMessage());
                        continue;
                    }
                    String invoiceFileContent = getInvoiceFileContent(acOrderInvoice.getInvoiceLinkAddress());
                    CpCShopOrderAuth shopOrderAuth = authValueHolderV14.getData();
                    if (acOrderInvoice.getCpCPlatformId() == 27L) {
                        //拼多多回传
                        uploadV14 = uploadPddInvoice(acOrderInvoice, shopOrderAuth,
                                invoiceFileContent);

                    } else if (acOrderInvoice.getCpCPlatformId() == 4L) {
                        //京东回传
                        uploadV14 = uploadJdInvoice(acOrderInvoice, shopOrderAuth,
                                invoiceFileContent);

                    } else {
                        errorMap.put(objId, "非京东/拼多多平台不允许同步!");
                        continue;
                    }
                    invoiceLogService.addUserOrderLog(acOrderInvoice.getId(), "同步平台", uploadV14.getMessage(),
                            request.getLoginUser());
                    if (uploadV14.isOK()){
                        acOrderInvoice.setSyncPlatformStatus(2);
                        BaseModelUtil.setupUpdateParam(acOrderInvoice, request.getLoginUser());
                        acOrderInvoiceMapper.updateById(acOrderInvoice);
                    }
                } else {
                    errorMap.put(objId, "当前发票处于锁定状态！");
                }
            } catch (InterruptedException e) {
                log.error(LogUtil.format("AcFOrderInvoiceCancelService.syncPlatform.error={}", "error"),
                        Throwables.getStackTraceAsString(e));
                throw new NDSException("发票管理同步平台异常!");
            } finally {
                redisLock.unlock();
            }
        }
        return R3ParamUtils.getExcuteValueHolder(batchObjIds.size(), errorMap);
    }

    public ValueHolderV14 uploadPddInvoice(AcFOrderInvoice acOrderInvoice, CpCShopOrderAuth shopOrderAuth,
                                           String invoiceFileContent) {
        InvoiceUploadPddRequest uploadPddRequest = new InvoiceUploadPddRequest();
        if (InvoiceConst.TicketType.RED.equals(acOrderInvoice.getTicketType())) {
            return new ValueHolderV14<>(ResultCode.FAIL, "拼多多平台暂不支持回传红票!");

            //            Long blueTicketId = acOrderInvoice.getBlueTicketId();
            //            AssertUtils.cannot(blueTicketId == null, "同步红票异常,来源蓝票ID不能为空!");
            //            AcFOrderInvoice invoiceBlue = acOrderInvoiceMapper.selectById(blueTicketId);
            //            AssertUtils.cannot(invoiceBlue == null, "同步红票异常,来源蓝票ID:" + blueTicketId + "对应蓝票已不存在!");
            //            uploadPddRequest.setOriginalInvoiceCode(invoiceBlue.getInvoiceCode());
            //            uploadPddRequest.setOriginalInvoiceNo(invoiceBlue.getInvoiceNumber());
        }
        uploadPddRequest.setAppkey(shopOrderAuth.getAppId());
        uploadPddRequest.setAppSecret(shopOrderAuth.getSecret());
        uploadPddRequest.setToken(shopOrderAuth.getToken());
        String tid = acOrderInvoice.getTid();
        String[] split = tid.split(",");
        //产品沟通多单取其中一个单号
        uploadPddRequest.setOrderSn(split[0]);
        uploadPddRequest.setHttpUrl(pddUploadUrl);
        uploadPddRequest.setType("pdd.invoice.detail.upload");
        uploadPddRequest.setBusinessType(Integer.valueOf(acOrderInvoice.getHeaderType()));
        uploadPddRequest.setInvoiceNo(acOrderInvoice.getInvoiceNumber());
        uploadPddRequest.setInvoiceCode(acOrderInvoice.getInvoiceCode());
        uploadPddRequest.setInvoiceAmount(acOrderInvoice.getInvoiceInclusiveTaxAmt().multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString());
        uploadPddRequest.setInvoiceFileContent(invoiceFileContent);
        //拼多多发票种类定义纸质和电子与中台相反
        String invoiceKind;
        if (InvoiceConst.InvoiceKind.ELECTRONIC.equals(acOrderInvoice.getInvoiceKind())) {
            invoiceKind = InvoiceConst.InvoiceKind.PAPER;
        } else if (InvoiceConst.InvoiceKind.PAPER.equals(acOrderInvoice.getInvoiceKind())) {
            invoiceKind = InvoiceConst.InvoiceKind.ELECTRONIC;
        } else {
            invoiceKind = acOrderInvoice.getInvoiceKind();
        }
        uploadPddRequest.setInvoiceKind(Integer.valueOf(invoiceKind));
        uploadPddRequest.setInvoiceType(Integer.valueOf(acOrderInvoice.getTicketType()));

        uploadPddRequest.setInvoiceTime(acOrderInvoice.getInvoiceDate().getTime());
        uploadPddRequest.setPayeeOperator(acOrderInvoice.getDrawer());
        uploadPddRequest.setPayerRegisterNo(acOrderInvoice.getTaxpayerNo());
        uploadPddRequest.setPayerName(acOrderInvoice.getInvoiceHeader());
        uploadPddRequest.setSumPrice(acOrderInvoice.getInvoiceNoTaxAmt().multiply(BigDecimal.valueOf(100L)).stripTrailingZeros().toPlainString());
        uploadPddRequest.setSumTax(acOrderInvoice.getInvoiceTaxAmt().multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString());
        BigDecimal taxRate =
                acOrderInvoice.getInvoiceTaxAmt().divide(acOrderInvoice.getInvoiceNoTaxAmt(),2,
                        BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100L));
        uploadPddRequest.setTaxRate(taxRate.stripTrailingZeros().toPlainString());
        return orderInvoiceSyncPlatformCmd.invoiceSyncPdd(uploadPddRequest);
    }

    public ValueHolderV14 uploadJdInvoice(AcFOrderInvoice acOrderInvoice, CpCShopOrderAuth shopOrderAuth,
                                          String invoiceFileContent) {
        InvoiceUploadJdRequest uploadJdRequest = new InvoiceUploadJdRequest();
        uploadJdRequest.setAppkey(shopOrderAuth.getAppId());
        uploadJdRequest.setAppSecret(shopOrderAuth.getSecret());
        uploadJdRequest.setToken(shopOrderAuth.getToken());
        String tid = acOrderInvoice.getTid();
        String[] split = tid.split(",");
        //产品沟通多单取其中一个单号
        uploadJdRequest.setOrderSn(split[0]);
        if (InvoiceConst.TicketType.RED.equals(acOrderInvoice.getTicketType())) {
            Long blueTicketId = acOrderInvoice.getBlueTicketId();
            AssertUtils.cannot(blueTicketId == null, "同步红票异常,来源蓝票ID不能为空!");
            AcFOrderInvoice invoiceBlue = acOrderInvoiceMapper.selectById(blueTicketId);
            AssertUtils.cannot(invoiceBlue == null, "同步红票异常,来源蓝票ID:" + blueTicketId + "对应蓝票已不存在!");
            uploadJdRequest.setOriginalInvoiceCode(invoiceBlue.getInvoiceCode());
            uploadJdRequest.setOriginalInvoiceNo(invoiceBlue.getInvoiceNumber());
        }
        uploadJdRequest.setInvoiceType(Integer.valueOf(acOrderInvoice.getTicketType()));
        uploadJdRequest.setBusinessType(Integer.valueOf(acOrderInvoice.getHeaderType()));
        uploadJdRequest.setPayerRegisterNo(acOrderInvoice.getSupplierTaxpayerNo());
        uploadJdRequest.setReceiverName(acOrderInvoice.getCpCSupplierEname());
        uploadJdRequest.setInvoiceCode(acOrderInvoice.getInvoiceCode());
        uploadJdRequest.setInvoiceNo(acOrderInvoice.getInvoiceNumber());
        uploadJdRequest.setInvoiceHeader(acOrderInvoice.getInvoiceHeader());
        uploadJdRequest.setInvoiceAmount(acOrderInvoice.getInvoiceNoTaxAmt().setScale(2,BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        uploadJdRequest.setInvoiceTime(sdf.format(acOrderInvoice.getInvoiceDate()));
        uploadJdRequest.setPdfInfo(invoiceFileContent);
        return orderInvoiceSyncPlatformCmd.invoiceSyncJd(uploadJdRequest);
    }

    public String getInvoiceFileContent(String invoiceLinkAddress) {
        String invoiceFileContent = null;
        try {
            URL url = new URL(invoiceLinkAddress);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            Base64.Encoder encoder = Base64.getEncoder();

            //设置超时间为3秒
            conn.setConnectTimeout(5 * 1000);
            //防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            //得到输入流
            InputStream inputStream = conn.getInputStream();
            //获取自己数组
            byte[] getData = readInputStream(inputStream);
            invoiceFileContent = encoder.encodeToString(getData);
            if (inputStream != null) {
                inputStream.close();
            }
        } catch (Exception e) {
            log.error(LogUtil.format("error={}", "AcFOrderInvoiceSyncPlatformService" +
                            ".getInvoiceFileContent"),
                    Throwables.getStackTraceAsString(e));
            AssertUtils.logAndThrow("下载发票pdf转换异常!");

        }
        log.info(LogUtil.format("invoiceFileContent={}", "AcFOrderInvoiceSyncPlatformService" +
                ".getInvoiceFileContent"), invoiceFileContent);
        return invoiceFileContent;
    }

    /**
     * 从输入流中获取字节数组
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    public byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

}
