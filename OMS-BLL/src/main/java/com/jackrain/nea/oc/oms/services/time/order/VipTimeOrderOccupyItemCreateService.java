package com.jackrain.nea.oc.oms.services.time.order;

import com.alibaba.fastjson.JSON;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipMapper;
import com.jackrain.nea.oc.oms.mapper.IpBTimeOrderVipOccupyItemMapper;
import com.jackrain.nea.oc.oms.model.constant.VipConstant;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVip;
import com.jackrain.nea.oc.oms.model.table.IpBTimeOrderVipOccupyItem;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description JITX退款转换服务类
 * @Date 2019-6-26
 **/
@Component
@Slf4j
public class VipTimeOrderOccupyItemCreateService {

    @Autowired
    private IpBTimeOrderVipMapper ipBTimeOrderVipMapper;

    @Autowired
    private IpBTimeOrderVipOccupyItemMapper orderVipOccupyItemMapper;

    private void printLog(String message, Object... argos) {
        if (log.isDebugEnabled()) {
            message = this.getClass().getSimpleName() + message;
            log.debug(message, argos);
        }
    }

    /**
     * 修改时效订单及增加唯品会时效订单库存占用明细
     */
    @Transactional(rollbackFor = Exception.class)
    public void create(IpBTimeOrderVip timeOrderVip, List<IpBTimeOrderVipOccupyItem> occupyItems, User user) {
        if (CollectionUtils.isEmpty(occupyItems)) {
            return;
        }
        Date date = new Date();
        // 主表id
        Long id = timeOrderVip.getId();
        // 把所有 状态 确认及缺货 唯品会时效订单库存占用明细 作废
        int updateDefaultRecord = orderVipOccupyItemMapper.updateIsActiveNByStatusAndMainId(
                id, Long.valueOf(user.getId()), user.getEname(), user.getName(), date);
        if (updateDefaultRecord > 0) {
            for (IpBTimeOrderVipOccupyItem item : occupyItems) {
                makeCreateField(id, user, date, item);
            }
            orderVipOccupyItemMapper.batchInsert(occupyItems);
        }
    }

    public void makeIpBTimeOrderVipUpdateField(User user, Date date, IpBTimeOrderVip item) {
        item.setModifierid(Long.valueOf(user.getId()));
        item.setModifierename(user.getName());
        item.setModifiername(user.getEname());
        item.setModifieddate(date);
    }

    public void makeIpBTimeOrderVipOccupyItemUpdateField(User user, Date date, IpBTimeOrderVipOccupyItem item) {
        item.setModifierid(Long.valueOf(user.getId()));
        item.setModifierename(user.getName());
        item.setModifiername(user.getEname());
        item.setModifieddate(date);
    }

    public void makeCreateField(long ipBTimeOrderVipId, User user, Date date, IpBTimeOrderVipOccupyItem item) {
        item.setId(ModelUtil.getSequence(VipConstant.TB_NAME_IP_B_TIME_ORDER_VIP_OCCUPY_ITEM));
        item.setIpBTimeOrderVipId(ipBTimeOrderVipId);
        item.setIsactive(VipConstant.ISACTIVE_Y);
        item.setModifierid(Long.valueOf(user.getId()));
        item.setModifierename(user.getName());
        item.setModifiername(user.getEname());
        item.setModifieddate(date);
        item.setOwnerid(Long.valueOf(user.getId()));
        item.setOwnername(user.getName());
        item.setOwnerename(user.getEname());
        item.setCreationdate(date);
    }
}
