package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOccupyTask;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@Mapper
public interface OcBOccupyTaskMapper extends ExtentionMapper<OcBOccupyTask> {

    @SelectProvider(type = OcBOccupyTaskMapperProvider.class, method = "selectOcBOccupyTaskList")
    List<Long> selectOcBOccupyTaskList(@Param("limit") int limit, @Param("taskTableName") String taskTableName, @Param("isStop") boolean isStop);


    @Update("<script> "
            + "UPDATE oc_b_occupy_task SET STATUS = 1,modifieddate = now() where order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateTaskStatus(@Param("ids") List<Long> orderIds);

    @Select("SELECT * from oc_b_occupy_task where order_id = #{orderId}")
    List<OcBOccupyTask> selectOcBOccupyTaskByOrderId(@Param("orderId") Long orderId);

    @Select("SELECT * from oc_b_occupy_task where order_id = #{orderId} and status= 0")
    List<OcBOccupyTask> selectOcBOccupyTaskByOrderIdAndStatus(@Param("orderId") Long orderId);

    @Select("SELECT order_id from oc_b_occupy_task where order_id = #{orderId}")
    List<Long> selectOcBOccupyTaskListByOrderId(@Param("orderId") Long orderId);

    @Update("UPDATE oc_b_occupy_task SET STATUS = 0, next_time = #{date} WHERE order_id = #{orderId} limit 1")
    int updateOcBOccupyTaskListByOrderId(@Param("orderId") Long orderId, @Param("date") Date date);

    /**
     * 根据订单id列表更新订单占用任务 主要改了创建时间。用的时候要慎重
     *
     * @param ids
     * @param date
     * @return
     */
    @Update("<script> "
            + "UPDATE oc_b_occupy_task SET creationdate = #{date} , next_time = now(), is_auto= 0  WHERE status = 0 and order_id in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    int updateByIdList(@Param("ids") List<Long> ids, @Param("date") Date date);

    class OcBOccupyTaskMapperProvider {

        public String selectOcBOccupyTaskList(@Param("limit") int limit,
                                              @Param("taskTableName") String taskTableName, @Param("isStop") boolean isStop) {
            StringBuffer sql = new StringBuffer();
            sql.append("select order_id from ")
                    .append(taskTableName)
                    .append(" where status= 0 ")
                    .append(" and next_time <= now() ");
            if (isStop) {
                sql.append(" and is_auto= 0 ");
            }
            sql.append(" order by creationdate asc ")
                    .append(" limit ").append(limit);
            return sql.toString();

        }
    }

    @Delete("<script> "
            + "delete from oc_b_occupy_task where order_id =#{orderId}"
            + "</script>")
    int deleteOcBOccupyTaskByOrderId(@Param("orderId") Long orderId);

    @Select("<script> "
            + "select * from oc_b_occupy_task where status = 0 and order_id in "
            + "<foreach item='item' index='index' collection='orderIds' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    List<OcBOccupyTask> selectOcBOccupyTaskByOrderIds(@Param("orderIds") List<Long> orderIds);

}