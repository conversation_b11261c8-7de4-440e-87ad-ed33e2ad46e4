package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OcBOrderEqualExchangeItemMapper extends ExtentionMapper<OcBOrderEqualExchangeItem> {


    @Select("<script> "
            + "SELECT * FROM oc_b_order_equal_exchange_item WHERE oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderEqualExchangeItem> selectOcBOrderEqualExchangeItemList(@Param("orderIds") List<Long> orderIds);


    @Select("<script> "
            + "SELECT * FROM oc_b_order_equal_exchange_item WHERE oc_b_order_id "
            + "in <foreach item='item' index='index' collection='orderIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + " and equal_exchange_mark in <foreach item='item1' index='index' collection='equalExchangeMarkList' "
            + "open='(' separator=',' close=')'> #{item1} </foreach> "
            + "</script>")
    List<OcBOrderEqualExchangeItem> selectItemListByOoid(@Param("orderIds") List<Long> orderIds,@Param("equalExchangeMarkList") List<String> equalExchangeMarkList);


    @Select("SELECT * FROM oc_b_order_equal_exchange_item WHERE oc_b_order_id = #{orderId}")
    List<OcBOrderEqualExchangeItem> selectOcBOrderEqualExchangeItemListByOrderId(@Param("orderId") Long orderId);


    @Delete("<script> "
            + "DELETE FROM oc_b_order_equal_exchange_item WHERE oc_b_order_id = #{orderId} and id "
            + "in <foreach item='item' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int delOcBOrderEqualExchangeItem(@Param("orderId") Long orderId, @Param("itemIds") List<Long> itemIds);


    @Delete("<script> "
            + "DELETE FROM oc_b_order_equal_exchange_item WHERE oc_b_order_id = #{orderId} and equal_exchange_mark "
            + "in <foreach item='item' index='index' collection='equalExchangeMarks' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    int delByEqualExchangeMark(@Param("orderId") Long orderId, @Param("equalExchangeMarks") List<String> equalExchangeMarks);

    @Select("SELECT * FROM oc_b_order_equal_exchange_item WHERE oc_b_order_id = #{orderId} and equal_exchange_mark = #{equalExchangeMark}")
    List<OcBOrderEqualExchangeItem> selectOcBOrderEqualExchangeItemByOrderId(@Param("orderId") Long orderId, @Param("equalExchangeMark") String equalExchangeMark);

    @Update("<script>"
            + "UPDATE oc_b_order_equal_exchange_item set m_dim4_id = #{mDim4Id},m_dim6_id = #{mDim6Id}, modifieddate = now() "
            + "where id "
            + "in <foreach item='itemId' index='index' collection='itemIds' "
            + "open='(' separator=',' close=')'> #{itemId} </foreach> "
            + "</script>")
    int updateDimByIds(@Param("itemIds") List<Long> itemIds, @Param("mDim4Id") Integer mDim4Id, @Param("mDim6Id") Integer mDim6Id);

}