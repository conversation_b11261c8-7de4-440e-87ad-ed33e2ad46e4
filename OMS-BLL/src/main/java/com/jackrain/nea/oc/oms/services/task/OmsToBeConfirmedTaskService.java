package com.jackrain.nea.oc.oms.services.task;

import com.google.common.collect.Lists;
import com.jackrain.nea.oc.oms.mapper.task.OcBToBeConfirmedTaskMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBToBeConfirmedTask;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: 易邵峰
 * @since: 2020-02-17
 * create at : 2020-02-17 15:05
 */
@Component
public class OmsToBeConfirmedTaskService {

    @Autowired
    private OcBToBeConfirmedTaskMapper toBeConfirmedTaskMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    public void insertToBeConfirmedTask(OcBToBeConfirmedTask toBeConfirmedTask) {
        toBeConfirmedTaskMapper.insert(toBeConfirmedTask);
    }

    public void batchInsertToBeConfirmedTask(List<OcBToBeConfirmedTask> tasks) {
        toBeConfirmedTaskMapper.batchInsert(tasks);
    }

    public void deleteToBeConfirmedTask(List<Long> taskIdList) {
        toBeConfirmedTaskMapper.deleteBatchIds(taskIdList);
    }

    public void updateToBeConfirmedTask(List<Long> taskIdList) {
        toBeConfirmedTaskMapper.updateTaskStatus(taskIdList);
    }

    public void updateToBeConfirmedTask0(List<Long> taskIdList) {
        toBeConfirmedTaskMapper.updateTaskStatus0(taskIdList);
    }


    public void updateToBeConfirmedTaskByOrderId(Long orderId) {
        toBeConfirmedTaskMapper.updateTaskStatusByOrderId(orderId);
    }


    public List<Long> selectToBeConfirmedTask(int limit, String taskTableName) {
        List<Long> result = toBeConfirmedTaskMapper.selectTaskIdList(limit, taskTableName);
        return result;
    }

    public List<String> selectNodeList() {
        return new ArrayList<>();
//        jdbcTemplate.query("SHOW NODE", new ResultSetExtractor<List<String>>() {
//            @Override
//            public List<String> extractData(ResultSet resultSet) throws SQLException, DataAccessException {
//                List<String> nodeList = new ArrayList<>();
//                while (resultSet.next()) {
//                    nodeList.add(resultSet.getString(1));
//                }
//                return nodeList;
//            }
//        });
//
//        JSONObject jsonNode = toBeConfirmedTaskMapper.selectNodeList();
//        List<String> nodeList = new ArrayList<>();
//
////        for (JSONObject obj : jsonNode.) {
////
////        }
//
//        return nodeList;
    }

    public void insert(Long id) {
        OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
        toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
        toBeConfirmedTask.setOrderId(id);
        toBeConfirmedTask.setCreationdate(new Date());
        toBeConfirmedTask.setStatus(0);
        toBeConfirmedTaskMapper.insert(toBeConfirmedTask);
    }

    public void insert(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        List<OcBToBeConfirmedTask> toBeConfirmedTasks = Lists.newArrayListWithExpectedSize(ids.size());
        for (Long id : ids) {
            OcBToBeConfirmedTask toBeConfirmedTask = new OcBToBeConfirmedTask();
            toBeConfirmedTask.setId(sequenceUtil.buildToBeConfirmedTaskId());
            toBeConfirmedTask.setOrderId(id);
            toBeConfirmedTask.setCreationdate(new Date());
            toBeConfirmedTask.setModifieddate(new Date());
            toBeConfirmedTask.setStatus(0);
            toBeConfirmedTasks.add(toBeConfirmedTask);
        }

        toBeConfirmedTaskMapper.batchInsert(toBeConfirmedTasks);
    }

}
