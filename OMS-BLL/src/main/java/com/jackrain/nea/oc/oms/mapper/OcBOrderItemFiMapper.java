package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.result.OrderItemFinanceResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderItemResult;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 全渠道订单明细表
 *
 * @date 2019/3/11
 * @author: ming.fz AND wangqiang
 */
@Mapper
@Component
public interface OcBOrderItemFiMapper extends ExtentionMapper<OcBOrderItem> {
    /**
     * 明细的删除
     *
     * @param id      明细的id
     * @param orderId 订单的id
     * @return
     */
    @Delete("delete from oc_b_order_item where id=#{id} and oc_b_order_id=#{orderId}")
    Integer deleteByItemId(@Param("id") Long id, @Param("orderId") Long orderId);

    /**
     * 订单对于明细商品数量
     *
     * @param id
     * @return
     */
    @Select("select count(1) from oc_b_order_item where oc_b_order_id=#{orderId} and isactive = 'Y'")
    int selectCountForOrder(@Param("orderId") Long id);

    /**
     * 查询订单明细中 吊牌价格
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT price_list FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y'")
    BigDecimal getpriceListBySku(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    /**
     * 查询订单明细中 优惠金额
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT amt_discount FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y'")
    BigDecimal getamtDiscountBySku(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    /**
     * 查询订单明细中 分销价格和数量
     *
     * @param pscSkueCode
     * @return
     */

    @Select("SELECT DISTRIBUTION_PRICE,QTY FROM OC_B_ORDER_ITEM WHERE PS_C_SKU_ECODE=#{pscSkueCode} and isactive = 'Y'")
    OcBOrderItem getDistributionAndPrice(String pscSkueCode);

    /**
     * 根据 ID 删除 明细信息
     *
     * @param id
     * @return
     */
    @Delete("DELETE FROM oc_b_order_item WHERE  id=#{id}")
    Integer delOrderItemById(Integer id);

    /**
     * 查询订单是否有多条明细
     * param orderId
     *
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order_item WHERE oc_b_order_id =#{orderId} and isactive = 'Y'")
    Integer checkOrderItemNum(Long orderId);

    /**
     * 查询平台单号
     *
     * @param orderId
     * @return
     */
    @Select("SELECT TID FROM oc_b_order_item WHERE oc_b_order_id =#{orderId} and isactive = 'Y'")
    List<String> getTids(Long orderId);

    /**
     * @param orderId
     * @return
     * <AUTHOR>
     * <p>
     * 查标准价price_list、数量qty、优惠金额amt_discount、调整金额adjust_amt、成交价格price、单行实际成交金额real_amt、条码ps_c_sku_id
     */
    @Select("SELECT PRICE_LIST,QTY,AMT_DISCOUNT,ADJUST_AMT,PRICE,REAL_AMT,PS_C_SKU_ID FROM OC_B_ORDER_ITEM " +
            "WHERE OC_B_ORDER_ID=#{orderId} AND ISACTIVE = 'Y'")
    List<OcBOrderItem> selectSourcecodeByRecord(Long orderId);

    /**
     * 检查明细中条码是否存在
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y' ")
    Integer checkSkuNum(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    /**
     * 更换商品
     *
     * @param psCProId
     * @param psCProEcode
     * @param psCProEname
     * @param skuSpec
     * @param psCSkuId
     * @param psCSkuEcode
     * @param id
     * @param ocBOrderId
     * @return
     */
    @Update("UPDATE oc_b_order_item  SET ps_c_pro_id=#{psCProId},ps_c_pro_ecode=#{psCProEcode},ps_c_pro_ename=#{psCProEname},sku_spec=#{skuSpec},ps_c_sku_id=#{psCSkuId},ps_c_sku_ecode=#{psCSkuEcode} WHERE id=#{id} and oc_b_order_id=#{ocBOrderId}")
    int updateByID(@Param("psCProId") long psCProId, @Param("psCProEcode") String psCProEcode, @Param("psCProEname") String psCProEname, @Param("skuSpec") String skuSpec, @Param("psCSkuId") long psCSkuId, @Param("psCSkuEcode") String psCSkuEcode, @Param("id") long id, @Param("ocBOrderId") long ocBOrderId);

    /**
     * 根据 pscSkueCode  查询数量
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT qty FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y'")
    BigDecimal getQtyBySku(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    @Select("SELECT * FROM oc_b_order_item WHERE ps_c_sku_ecode =#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y'")
    OcBOrderItem getOrderItem(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    /**
     * 更新 条码 数量
     *
     * @param qtyBill
     * @param pscSkueCode
     * @return
     */
    @Update("UPDATE oc_b_order_item SET qty=#{qtyBill},modifiername=#{name},modifieddate=#{time} WHERE ps_c_sku_ecode=#{pscSkueCode}")
    Integer updateQty(@Param("qtyBill") BigDecimal qtyBill,
                      @Param("name") String name,
                      @Param("time") Date time,
                      @Param("pscSkueCode") String pscSkueCode,
                      @Param("orderId") Long orderId);

    /**
     * 根据 Id 更新明细 中条码 数量
     *
     * @param id qtyBill
     * @return
     */
    @Update("UPDATE oc_b_order_item SET qty=#{qtyBill} WHERE id=#{id}")
    Integer updateQtyNumById(@Param("qtyBill") BigDecimal qtyBill, @Param("id") Long id);

    /**
     * 查询 订单明细中 成交价格
     *
     * @param pscSkueCode
     * @return
     */

    @Select("SELECT price FROM oc_b_order_item WHERE ps_c_sku_ecode=#{pscSkueCode} AND oc_b_order_id=#{orderId} and isactive = 'Y'")
    BigDecimal getPriceBySku(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);
    //********************* zhang.xiwen start

    /**
     * 根据订单编号,查询订单详情
     *
     * @param jsn      jsonObject
     * @param isActive 是否启用
     * @param orderIds string
     * @return list<jsonObject>
     */
    @SelectProvider(type = SqlProvider.class, method = "orderItemsQueryByOrderIds")
    List<QueryOrderItemResult> orderItemsQueryByOrderIds(@Param("jsn") JSONObject jsn,
                                                         @Param("orderIds") String orderIds,
                                                         @Param("isActive") String isActive);

    @SelectProvider(type = SqlProvider.class, method = "selectExportItemsByOIds")
    List<OcBOrderItem> selectExportItemsByOIds(@Param("orderIds") String orderIds, @Param("isActive") String isActive);


    /**
     * 一商退货入库更新已退数量
     *
     * @param orderItem
     * @return
     */
    @Update("UPDATE OC_B_ORDER_ITEM SET QTY_REFUND= #{qtyRefund}, QTY_RETURN_APPLY=#{qtyReturnApply} WHERE "
            + "OC_B_ORDER_ID=#{ocBOrderId} AND ID=#{id};")
    int updateOrderItemReturnQty(OcBOrderItem orderItem);


    /**
     * 退货入库服务.查询原单明细
     *
     * @param orderId
     * @return
     */
    @Select("SELECT OC_B_ORDER_ID,TID,ID,QTY_RETURN_APPLY,QTY_REFUND,OOID FROM OC_B_ORDER_ITEM WHERE OC_B_ORDER_ID =#{orderId};")
    List<OcBOrderItem> selectItemList(@Param("orderId") Long orderId);

    /**
     * sql
     */
    class SqlProvider {
        /**
         * @param jsn      jsonObject
         * @param orderIds string
         * @param isActive string
         * @return string
         */
        public String orderItemsQueryByOrderIds(@Param("jsn") JSONObject jsn, @Param("orderIds") String orderIds,
                                                @Param("isActive") String isActive) {
            StringBuilder sb = new StringBuilder();
            Set<String> setkeys = jsn.keySet();
            for (String e : setkeys) {
                sb.append(",`" + e + "`");
                sb.append(" ");
                sb.append(jsn.getString(e));
            }
            String fields = sb.toString().substring(1);
            sb = new StringBuilder();
            sb.append("SELECT ");
            sb.append(fields);
            sb.append(" FROM oc_b_order_item WHERE PRO_TYPE != 4 AND OC_B_ORDER_ID IN (");
            sb.append(orderIds);
            sb.append(" ) AND ISACTIVE = '");
            sb.append(isActive);
            sb.append("';");
            return sb.toString();
        }

        /**
         * @param orderIds string
         * @param isActive string
         * @return string
         */
        public String selectExportItemsByOIds(@Param("orderIds") String orderIds, @Param("isActive") String isActive) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT\n" +
                    "\tID,\n" +
                    "\tPS_C_SKU_ID,\n" +
                    "\tPS_C_PRO_ID,\n" +
                    "\tPS_C_CLR_ID,\n" +
                    "\tPS_C_CLR_ECODE,\n" +
                    "\tPS_C_CLR_ENAME,\n" +
                    "\tPS_C_SIZE_ID,\n" +
                    "\tPS_C_SIZE_ECODE,\n" +
                    "\tPS_C_SIZE_ENAME,\n" +
                    "\tQTY,\n" +
                    "\tPRICE,\n" +
                    "\tREAL_AMT,\n" +
                    "\tSTANDARD_WEIGHT,\n" +
                    "\tPS_C_SKU_ECODE,\n" +
                    "\tPS_C_PRO_ENAME,\n" +
                    "\tAMT_REFUND,\n" +
                    "\tBARCODE,\n" +
                    "\tPRICE_LIST,\n" +
                    "\tOC_B_ORDER_ID,\n" +
                    "\tSKU_SPEC,\n" +
                    "\tPS_C_PRO_ECODE,\n" +
                    "\tOOID,\n" +
                    "\tPRICE_SETTLE,\n" +
                    "\tTOT_PRICE_SETTLE,\n" +
                    "\tPRICE_ACTUAL,\n" +
                    "\tQTY_LOST,\n" +
                    "\tSEX,\n" +
                    "\tQTY_REFUND,\n" +
                    "\tis_gift,\n" +
                    "\tgift_type,GIFT_RELATION,\n" +
                    "\tps_c_sku_pt_ecode,\n" +
                    "\tpt_pro_name,\n" +
                    "\tps_c_sku_ename,\n" +
                    "\tpro_type,\n" +
                    "\tqty_has_return ");
            sb.append(" FROM OC_B_ORDER_ITEM WHERE OC_B_ORDER_ID IN (");
            sb.append(orderIds);
            sb.append(" ) AND ISACTIVE = '");
            sb.append(isActive);
            sb.append("' ORDER BY `OC_B_ORDER_ID` DESC;");
            return sb.toString();
        }
    }
    //********************* zhang.xiwen end

    /**
     * 查询整单平摊金额
     *
     * @param pscSkueCode
     * @return
     */
    @Select("SELECT ORDER_SPLIT_AMT FROM OC_B_ORDER_ITEM WHERE PS_C_SKU_ECODE =#{pscSkueCode} AND  oc_b_order_id=#{orderId} and isactive = 'Y' and pro_type != 4")
    BigDecimal getOrderSplitAmt(@Param("pscSkueCode") String pscSkueCode, @Param("orderId") Long orderId);

    @Select("SELECT TID,SUM(REAL_AMT) AS SUMREALAMT FROM OC_B_ORDER_ITEM WHERE OC_B_ORDER_ID=#{orderId} " +
            "AND REFUND_STATUS IN ${refundStatus} AND ISACTIVE='Y' and pro_type != 4 GROUP BY TID")
    List<OrderItemFinanceResult> getTidAndRealAmt(@Param("orderId") Long orderId,
                                                  @Param("refundStatus") String refundStatus);

    /**
     * 查询明细集
     *
     * @param orderId 订单id
     * @return 明细集合
     */
    @Select("SELECT * FROM OC_B_ORDER_ITEM WHERE OC_B_ORDER_ID=#{orderId} ")
    List<OcBOrderItem> queryItemsByOrderId(@Param("orderId") Long orderId);

    /**
     * 更新明细可退,已退数量
     *
     * @param ocBOrderItem
     * @return
     */
    @Update("UPDATE OC_B_ORDER_ITEM SET QTY_RETURN_APPLY=#{qtyReturnApply}, QTY_HAS_RETURN=#{qtyHasReturn} WHERE OC_B_ORDER_ID=#{ocBOrderId} AND `ID`=#{id}")
    int updateOcBOrderItemQty(OcBOrderItem ocBOrderItem);

}
