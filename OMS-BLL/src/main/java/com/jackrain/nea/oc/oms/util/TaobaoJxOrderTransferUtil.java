package com.jackrain.nea.oc.oms.util;

import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoJxOrderStatus;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 淘宝经销订单转换工具类
 **/
@Component
@Slf4j
public class TaobaoJxOrderTransferUtil {
    @Autowired
    private RegionNewService regionService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private CpRpcService cpRpcService;

    /**
     * @return com.jackrain.nea.oc.oms.model.table.OcBOrder
     * <AUTHOR>
     * @Description 全渠道订单主表
     * @Date 2019-7-9
     * @Param [jxOrderRelation]
     **/
    private OcBOrder convertJxOrderToOmsOrder(IpTaobaoJxOrderRelation jxOrderRelation) {
        //淘宝经销商主表
        IpBTaobaoJxOrder taobaoJxOrder = jxOrderRelation.getTaobaoJxOrder();

        OcBOrder ocBOrder = new OcBOrder();
        //id自增长
        ocBOrder.setId(sequenceUtil.buildOrderSequenceId());
        //平台ID
        ocBOrder.setTid(taobaoJxOrder.getDealerOrderId().toString());
        //单据编号
        ocBOrder.setBillNo(sequenceUtil.buildBillNo());
        //系统信息
        BaseModelUtil.initialBaseModelSystemField(ocBOrder);
        //wms撤回状态 未撤回
        ocBOrder.setWmsCancelStatus(0);

        ocBOrder.setSourceCode(taobaoJxOrder.getDealerOrderId().toString());
        ocBOrder.setMergeSourceCode(taobaoJxOrder.getDealerOrderId().toString());
        //订单类型 默认1正常
        ocBOrder.setOrderType(1);
        //店铺信息
        ocBOrder.setCpCShopId(jxOrderRelation.getTaobaoJxOrder().getCpCShopId());
        ocBOrder.setCpCShopTitle(jxOrderRelation.getTaobaoJxOrder().getCpCShopTitle());
        //下单店铺
        ocBOrder.setCpCShopSellerNick(taobaoJxOrder.getSupplierNick());
        //下单店铺标题。需要查个表获取Title（平台店铺信息表）
        //平台店铺标题
        CpShop shopInfo = null;
        if (taobaoJxOrder.getCpCShopId() != null) {
            shopInfo = cpRpcService.selectShopById(taobaoJxOrder.getCpCShopId());
        } else {
            throw new NDSException("平台店铺id为空!");
        }
        if (shopInfo != null) {
            ocBOrder.setCpCShopTitle(shopInfo.getCpCShopTitle());
            //下单店仓编码. 到平台店铺信息表中获取下单店仓字段ID值；
//            ocBOrder.setCpCStoreEcode(shopInfo.getCpCStoreEcode());
            //下单店仓名称. 到平台店铺信息表中获取下单店仓字段ID值；
//            ocBOrder.setCpCStoreEname(shopInfo.getCpCStoreEname());
            //下单店仓id. 到平台店铺信息表中获取下单店仓字段ID值；
//            ocBOrder.setCpCStoreId(shopInfo.getCpCStoreId());
            //下单店仓id. 到平台店铺信息表中获取下单卖家店铺名称
            ocBOrder.setCpCShopEcode(shopInfo.getEcode());
            ocBOrder.setCpCShopSellerNick(shopInfo.getSellerNick());
        } else {
            // 20190727修改：如果 平台店铺不存在，则不再继续保持。而是抛出异常，不允许转单操作
            throw new NDSException("平台店铺id=" + taobaoJxOrder.getCpCShopId() + "不存在");
        }

        //买家昵称
        ocBOrder.setUserNick(taobaoJxOrder.getApplierNick());
        //20190815 新增 待供应商发货(WAIT_FOR_SUPPLIER_DELIVER)时，渠道订单状态赋值为待配货
        //20190815 交易完成（TRADE_FINISHED）、等待分销商收货（WAIT_FOR_APPLIER_STORAGE）时，渠道订单状态赋值为平台发货
        if (TaoBaoJxOrderStatus.WAIT_FOR_SUPPLIER_DELIVER.equals(taobaoJxOrder.getOrderStatus())) {
            ocBOrder.setOrderStatus(50);
        } else if (TaoBaoJxOrderStatus.TRADE_FINISHED.equals(taobaoJxOrder.getOrderStatus()) ||
                TaoBaoJxOrderStatus.WAIT_FOR_APPLIER_STORAGE.equals(taobaoJxOrder.getOrderStatus())) {
            ocBOrder.setOrderStatus(6);
        }
        ocBOrder.setOrderFlag(taobaoJxOrder.getSupplierMemoFlag());
        //商品总额
        ocBOrder.setProductAmt(getPriceCount(jxOrderRelation.getIpBTaobaoJxOrderItemExList()));
        //商品优惠金额
        ocBOrder.setProductDiscountAmt(BigDecimal.ZERO);
        //订单优惠金额  rebate_fee平台取不到都为0
        ocBOrder.setOrderDiscountAmt(BigDecimal.ZERO);
        //商品折扣金额汇总【默认为零】
        ocBOrder.setAdjustAmt(BigDecimal.ZERO);
        //配送费用
        ocBOrder.setShipAmt(taobaoJxOrder.getLogisticsFee() == null ? BigDecimal.ZERO : taobaoJxOrder.getLogisticsFee());
        //服务费
        ocBOrder.setServiceAmt(BigDecimal.ZERO);
        //订单总额  商品总额+配送费用-商品优惠金额+调整金额-订单优惠金额   调整金额子表没有
        BigDecimal orderAmt = ocBOrder.getProductAmt()
                .add(ocBOrder.getShipAmt())
                .subtract(ocBOrder.getProductDiscountAmt())
                .subtract(ocBOrder.getOrderDiscountAmt());
        ocBOrder.setOrderAmt(orderAmt);
        //已收金额 total_price - rebate_fee  rebate_fee平台取不到都为0
        ocBOrder.setReceivedAmt(taobaoJxOrder.getTotalPrice());
        //是否开票
        ocBOrder.setIsInvoice(0);
        //下单时间 对应申请时间
        ocBOrder.setOrderDate(taobaoJxOrder.getAppliedTime());
        //付款时间
        ocBOrder.setPayTime(taobaoJxOrder.getPayTime());
        ocBOrder.setReceiverName(taobaoJxOrder.getName());
        ocBOrder.setReceiverMobile(taobaoJxOrder.getMobilePhone());
        //调用省市区服务
        parseRegionInfo(taobaoJxOrder, ocBOrder);
        ocBOrder.setReceiverAddress(taobaoJxOrder.getAddress().replaceAll(",", "::::"));
        ocBOrder.setReceiverZip(taobaoJxOrder.getZip());
        //是否合并订单
        ocBOrder.setIsMerge(0);
        //是否拆分订单
        ocBOrder.setIsSplit(0);
        //是否已拦截
        ocBOrder.setIsInterecept(0);
        //是否退款中
        ocBOrder.setIsInreturning(0);
        //支付方式
        ocBOrder.setPayType(1);
        //订单来源
        ocBOrder.setPlatform(PlatFormEnum.TAOBAO_DEAL.getCode());
        //来源平台
        ocBOrder.setOrderSource("淘宝经销");
        //是否有赠品
        ocBOrder.setIsHasgift(0);
        //商品数量
        ocBOrder.setQtyAll(this.buildTotalQtyAll(jxOrderRelation.getIpBTaobaoJxOrderItemExList()));
        ocBOrder.setSkuKindQty(new BigDecimal(jxOrderRelation.getIpBTaobaoJxOrderItemExList().size()));
        //卖家备注
        ocBOrder.setSellerMemo(taobaoJxOrder.getSupplierMemo());
        ocBOrder.setWeight(BigDecimal.ZERO);
        //预售状态
        // ocBOrder.setSysPresaleStatus(isPresale(jxOrderRelation.getIpBTaobaoJxOrderItemExList()) ? 1 : 0);
        //邮费 0
        ocBOrder.setConsignShipAmt(BigDecimal.ZERO);
        //20180901 增加默认值 auto_audit_status=0 occupy_status=0
        ocBOrder.setAutoAuditStatus(0);
        ocBOrder.setOccupyStatus(0);
        return ocBOrder;
    }

    /**
     * @return java.util.List<com.jackrain.nea.oc.oms.model.table.OcBOrderItem>
     * <AUTHOR>
     * @Description 淘宝经销订单明细转全渠道订单明细
     * @Date 2019-7-9
     * @Param [taobaoJxOrderItemExList, ocBOrder]
     **/
    private List<OcBOrderItem> convertOrderJxItemToOmsOrderItem(List<IpBTaobaoJxOrderItemEx> taobaoJxOrderItemExList, OcBOrder ocBOrder) {

        if (CollectionUtils.isEmpty(taobaoJxOrderItemExList)) {
            return new ArrayList<>();
        }
        List<OcBOrderItem> orderItems = new ArrayList<>();
        for (IpBTaobaoJxOrderItemEx taobaoJxOrderItemEx : taobaoJxOrderItemExList) {
            orderItems.add(covertTaobaoJxItemToOmsOrderItem(taobaoJxOrderItemEx, ocBOrder));
        }
        return orderItems;
    }

    /**
     * @return com.jackrain.nea.oc.oms.model.table.OcBOrderItem
     * <AUTHOR>
     * @Description 明细转换方法
     * @Date 2019-7-9
     * @Param [taobaoJxOrderItemEx, ocBOrder]
     **/
    private OcBOrderItem covertTaobaoJxItemToOmsOrderItem(IpBTaobaoJxOrderItemEx taobaoJxOrderItemEx, OcBOrder ocBOrder) {
        OcBOrderItem ocBOrderItem = new OcBOrderItem();
        //设置分库键
        ocBOrderItem.setId(sequenceUtil.buildOrderItemSequenceId());
        ocBOrderItem.setModifierename(SystemUserResource.ROOT_USER_NAME);
        ocBOrderItem.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        ocBOrderItem.setVersion(0L);
        //设置公共信息
        BaseModelUtil.initialBaseModelSystemField(ocBOrderItem);
        //实缺标志
        ocBOrderItem.setIsLackstock(0);
        //退货金额
        ocBOrderItem.setAmtRefund(BigDecimal.ZERO);
        //组合名称
        ocBOrderItem.setGroupName("");
        //商品数字编码
        ocBOrderItem.setNumIid(taobaoJxOrderItemEx.getProductId() + "");
        ocBOrderItem.setSkuNumiid(taobaoJxOrderItemEx.getSkuId() + "");
        //是否赠品
        ocBOrderItem.setIsGift(0);
        //平摊金额
//        ocBOrderItem.setOrderSplitAmt();
        //国标码
        ocBOrderItem.setBarcode(taobaoJxOrderItemEx.getProdSku().getBarcode69());
        //商品id
        ocBOrderItem.setPsCProId(taobaoJxOrderItemEx.getProdSku().getProdId());
        ocBOrderItem.setPsCProEcode(taobaoJxOrderItemEx.getProdSku().getProdCode());
        ocBOrderItem.setPsCProEname(taobaoJxOrderItemEx.getProdSku().getName());
        //颜色
        ocBOrderItem.setPsCClrEcode(taobaoJxOrderItemEx.getProdSku().getColorCode());
        ocBOrderItem.setPsCClrId(taobaoJxOrderItemEx.getProdSku().getColorId());
        ocBOrderItem.setPsCClrEname(taobaoJxOrderItemEx.getProdSku().getColorName());
        //尺寸
        ocBOrderItem.setPsCSizeEcode(taobaoJxOrderItemEx.getProdSku().getSizeCode());
        ocBOrderItem.setPsCSizeId(taobaoJxOrderItemEx.getProdSku().getSizeId());
        ocBOrderItem.setPsCSizeEname(taobaoJxOrderItemEx.getProdSku().getSizeName());

        // 增加品类信息 20220923
        ocBOrderItem.setMDim4Id(taobaoJxOrderItemEx.getProdSku().getMDim4Id());
        ocBOrderItem.setMDim6Id(taobaoJxOrderItemEx.getProdSku().getMDim6Id());
        if ("Y".equals(taobaoJxOrderItemEx.getProdSku().getIsEnableExpiry())) {
            ocBOrderItem.setIsEnableExpiry(1);
        } else {
            ocBOrderItem.setIsEnableExpiry(0);
        }
        //商品属性
        ocBOrderItem.setPsCProMaterieltype(taobaoJxOrderItemEx.getProdSku().getMaterialType());
        //规格
        ocBOrderItem.setSkuSpec(taobaoJxOrderItemEx.getSkuSpec());
        //标题
        ocBOrderItem.setTitle(taobaoJxOrderItemEx.getProductTitle());
        //赋值平台商品名称
        ocBOrderItem.setPtProName(StringUtils.defaultString(taobaoJxOrderItemEx.getProductTitle()));
        //商品路径
        //子订单编号(明细编号)
        ocBOrderItem.setOoid(taobaoJxOrderItemEx.getDealerDetailId().toString());
        //条码ID
        ocBOrderItem.setPsCSkuId(taobaoJxOrderItemEx.getProdSku().getId());
        ocBOrderItem.setPsCSkuEcode(taobaoJxOrderItemEx.getProdSku().getSkuEcode());
        //标准重量
        ocBOrderItem.setStandardWeight(taobaoJxOrderItemEx.getProdSku().getWeight());
        //标准价
        ocBOrderItem.setPriceList(taobaoJxOrderItemEx.getFinalPrice());
        //商品优惠金额
        ocBOrderItem.setAmtDiscount(BigDecimal.ZERO);
        //调整金额
        ocBOrderItem.setAdjustAmt(BigDecimal.ZERO);
        //成交价格  （标准价*数量-优惠金额+调整金额）/数量，保留4位小数
        BigDecimal price = ocBOrderItem.getPriceList()
                .multiply(BigDecimal.valueOf(taobaoJxOrderItemEx.getQuantity()))
                .subtract(ocBOrderItem.getAmtDiscount())
                .add(ocBOrderItem.getAdjustAmt());
        ocBOrderItem.setPrice(price.divide(BigDecimal.valueOf(taobaoJxOrderItemEx.getQuantity()), 4, BigDecimal.ROUND_HALF_UP));
        //单行实际成交金额
        BigDecimal realAmt = ocBOrderItem.getPrice()
                .multiply(BigDecimal.valueOf(taobaoJxOrderItemEx.getQuantity()))
                .subtract(BigDecimal.ZERO);
        ocBOrderItem.setRealAmt(realAmt);
        //是否已经占用库存
        ocBOrderItem.setIsAllocatestock(0);
        //买家是否已评价
        ocBOrderItem.setIsBuyerRate(0);
        //订单编号
        ocBOrderItem.setOcBOrderId(ocBOrder.getId());
        //已退数量
        ocBOrderItem.setQtyRefund(BigDecimal.ZERO);
        //数量
        ocBOrderItem.setQty(BigDecimal.valueOf(taobaoJxOrderItemEx.getQuantity()));
        //退款状态
        ocBOrderItem.setRefundStatus(0);
        //平台单号
        ocBOrderItem.setTid(ocBOrder.getTid());
        //预售状态
        ocBOrderItem.setIsPresalesku(0);
        //虚拟条码
        //20190815 新增 商品明细的【组合商品类型】赋值：组合或福袋  商品类型(0:正常,1:福袋,2:组合,3:预售)
        ocBOrderItem.setProType(Long.valueOf(taobaoJxOrderItemEx.getProdSku().getSkuType()));
        //是否已发货
        ocBOrderItem.setIsSendout(0);
        //发货失败次数
        ocBOrderItem.setOuterrcount(0);
        //2019-08-28 添加吊牌价和性别
        ocBOrderItem.setSex(taobaoJxOrderItemEx.getProdSku().getSex());
        ocBOrderItem.setPriceTag(taobaoJxOrderItemEx.getProdSku().getPricelist());

        return ocBOrderItem;
    }

    /**
     * @return com.jackrain.nea.oc.oms.model.table.OcBOrderPayment
     * <AUTHOR>
     * @Description 订单支付表
     * @Date 2019-7-9
     * @Param [ocBOrder, taobaoJxOrder]
     **/
    private OcBOrderPayment convertJxOrderPayToOmsOrderPay(OcBOrder ocBOrder, IpBTaobaoJxOrder taobaoJxOrder) {
        OcBOrderPayment orderPayment = new OcBOrderPayment();
        orderPayment.setId(sequenceUtil.buildOrderPaymentSequenceId());
        orderPayment.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderPayment.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        orderPayment.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(orderPayment);

        orderPayment.setOcBOrderId(ocBOrder.getId());
        //支付流水
        orderPayment.setPaymentNo(taobaoJxOrder.getAlipayNo());
        //支付时间
        orderPayment.setPayTime(taobaoJxOrder.getPayTime());
        //完成时间
        orderPayment.setEndTime(taobaoJxOrder.getPayTime());
        //支付金额
        orderPayment.setPaymentAmt(taobaoJxOrder.getTotalPrice());
        //订单金额
        orderPayment.setAmtOrder(taobaoJxOrder.getTotalPrice());
        //付款方式 默认1 支付宝
        orderPayment.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        return orderPayment;
    }

    /**
     * 创建Order全链路日志信息
     *
     * @param ocBOrder      订单信息表
     * @param taobaoJxOrder 淘宝经销订单信息表
     * @return Order全链路日志信息
     */
    private OcBOrderLink buildOrderLink(OcBOrder ocBOrder, IpBTaobaoJxOrder taobaoJxOrder) {
        OcBOrderLink orderLink = new OcBOrderLink();
        //自增ID
        orderLink.setId(sequenceUtil.buildOrderLinkSequenceId());
        //平台单号
        orderLink.setTid(ocBOrder.getTid());
        //订单编号
        orderLink.setOcBOrderId(ocBOrder.getId());
        //回流状态
        orderLink.setBackflowStatus(BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        //扩展属性
        orderLink.setExtAttribute(null);
        //商家平台编码 main:官方渠道,jd:京东,dd:当当,pp:拍拍,yx:易讯,ebay:ebay,amazon:亚马逊,sn:苏宁,gm:国美,wph:唯品会,
        //jm:聚美,mgj:蘑菇街,yt:银泰,yhd:1号店,1688:1688,pos:pos门店,other:其他
        orderLink.setPlatform("MAIN");
        //卖家昵称
        orderLink.setSellerNick(taobaoJxOrder.getSupplierNick());
        //同步状态
        orderLink.setSyncStatus(0);
        //同步时间
        orderLink.setSyncTime(null);
        //错误信息
        orderLink.setErrorInfo(null);
        orderLink.setCreationdate(new Date());
        orderLink.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderLink.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        orderLink.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderLink.setModifieddate(new Date());
        orderLink.setOwnerid(SystemUserResource.ROOT_USER_ID);
        orderLink.setModifierid(SystemUserResource.ROOT_USER_ID);
        return orderLink;
    }

    /**
     * 省市区匹配
     *
     * @param ipBTaobaoJxOrder 订单表
     * @param order            全渠道订单表
     */
    private void parseRegionInfo(IpBTaobaoJxOrder ipBTaobaoJxOrder, OcBOrder order) {
        ProvinceCityAreaInfo regionInfo = null;
        try {
            regionInfo = regionService.selectProvinceCityAreaInfo(ipBTaobaoJxOrder.getState()
                    , ipBTaobaoJxOrder.getCity()
                    , ipBTaobaoJxOrder.getDistrict());
            if (regionInfo.getProvinceInfo() != null) {
                order.setCpCRegionProvinceId(regionInfo.getProvinceInfo().getId());
                order.setCpCRegionProvinceEcode(regionInfo.getProvinceInfo().getCode());
                order.setCpCRegionProvinceEname(ipBTaobaoJxOrder.getState());
            }
            if (regionInfo.getCityInfo() != null) {
                order.setCpCRegionCityId(regionInfo.getCityInfo().getId());
                order.setCpCRegionCityEcode(regionInfo.getCityInfo().getCode());
                order.setCpCRegionCityEname(regionInfo.getCityInfo().getName());
            }
            if (regionInfo.getAreaInfo() != null) {
                order.setCpCRegionAreaId(regionInfo.getAreaInfo().getId());
                order.setCpCRegionAreaEcode(regionInfo.getAreaInfo().getCode());
                order.setCpCRegionAreaEname(regionInfo.getAreaInfo().getName());
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("调用省市区服务异常:{}"), Throwables.getStackTraceAsString(ex));
        }

    }

    /**
     * 是否预售
     **/
    private boolean isPresale(List<IpBTaobaoJxOrderItemEx> ipBTaobaoJxOrderItemExList) {
        for (IpBTaobaoJxOrderItemEx ipBTaobaoJxOrderItemEx : ipBTaobaoJxOrderItemExList) {
            Integer isPresale = getPresaleSku(ipBTaobaoJxOrderItemEx);
            if (null != isPresale && isPresale.equals(1)) {
                return true;
            } else if (null != isPresale && isPresale.equals(0)) {
                return false;
            } else {
                return false;
            }
        }
        return false;
    }

    /**
     * 是否预售状态
     **/
    private Integer getPresaleSku(IpBTaobaoJxOrderItemEx ipBTaobaoJxOrderItemEx) {
        if (ipBTaobaoJxOrderItemEx.getProdSku() != null) {
            if (ipBTaobaoJxOrderItemEx.getProdSku().getSkuType() == SkuType.PRE_SALE_PRODUCT) {
                return 1;
            } else {
                return 0;
            }
        }
        return null;
    }

    /**
     * 商品总额
     **/
    private BigDecimal getPriceCount(List<IpBTaobaoJxOrderItemEx> ipBTaobaoJxOrderItemExList) {
        if (CollectionUtils.isEmpty(ipBTaobaoJxOrderItemExList)) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = BigDecimal.ZERO;
        for (IpBTaobaoJxOrderItemEx ipBTaobaoJxOrderItemEx : ipBTaobaoJxOrderItemExList) {
            result = result.add(ipBTaobaoJxOrderItemEx.getPriceCount());
        }
        return result;
    }

    /**
     * @return com.jackrain.nea.oc.oms.model.relation.IpTaobaoJxOrderRelation
     * <AUTHOR>
     * @Description 组装订单关系表
     * @Date 2019-7-9
     * @Param [taobaoJxOrderRelation]
     **/
    public OcBOrderRelation taobaoJxOrderToOmsOrder(IpTaobaoJxOrderRelation taobaoJxOrderRelation) {

        OcBOrderRelation ocBOrderRelation = new OcBOrderRelation();
        //全渠道订单主表
        OcBOrder ocBOrder = this.convertJxOrderToOmsOrder(taobaoJxOrderRelation);

        //全渠道订单子表
        List<OcBOrderItem> ocBOrderItemList = this.convertOrderJxItemToOmsOrderItem(taobaoJxOrderRelation.getIpBTaobaoJxOrderItemExList(),
                ocBOrder);
        //订单支付表
        OcBOrderPayment ocBOrderPayment = this.convertJxOrderPayToOmsOrderPay(ocBOrder,
                taobaoJxOrderRelation.getTaobaoJxOrder());
        List<OcBOrderPayment> ocBOrderPaymentList = new ArrayList<>();
        ocBOrderPaymentList.add(ocBOrderPayment);
        //全链路监控表
        OcBOrderLink ocBOrderLink = this.buildOrderLink(ocBOrder, taobaoJxOrderRelation.getTaobaoJxOrder());
        ocBOrderRelation.setOrderInfo(ocBOrder);
        ocBOrderRelation.setOrderItemList(ocBOrderItemList);
        ocBOrderRelation.setOrderPaymentList(ocBOrderPaymentList);
        ocBOrderRelation.setOrderLink(ocBOrderLink);
        return ocBOrderRelation;
    }

    /**
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @Description 商品数量  明细中数量之和，排除已删除的
     * @Date 2019-8-24
     * @Param [list]
     **/
    private BigDecimal buildTotalQtyAll(List<IpBTaobaoJxOrderItemEx> list) {
        BigDecimal totalQtyAll = BigDecimal.ZERO;
        Long count = 0L;
        if (list != null && list.size() != 0) {
            for (IpBTaobaoJxOrderItemEx taobaoJxOrderItemEx : list) {
                Long quantity = taobaoJxOrderItemEx.getQuantity();
                count = count + quantity;
            }
        }
        totalQtyAll = new BigDecimal(count);
        return totalQtyAll;
    }

}
