package com.jackrain.nea.oc.oms.services.invoice;

import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.google.common.base.Throwables;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.util.*;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 */
@Slf4j
@Component
public class AcFOrderInvoiceUnFreezeService {
    @Autowired
    private BllRedisLockOrderUtil redisUtil;

    @Autowired
    private AcFOrderInvoiceMapper acOrderInvoiceMapper;

    @Autowired
    private InvoiceLogService invoiceLogService;

    public ValueHolder execute(QuerySession querySession) throws NDSException {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        request.setR3(true);
        AcFOrderInvoiceUnFreezeService service = ApplicationContextHandle.getBean(this.getClass());
        return service.unFreeze(request);
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolder unFreeze(SgR3BaseRequest request) {
        List<Long> batchObjIds = R3ParamUtils.getBatchObjIds(request);
        // 存储错误的Map
        Map<Long, Object> errorMap = new HashMap<>(batchObjIds.size());
        for (Long objId : batchObjIds) {
            String lockRedisKey = InvoiceConst.AC_F_ORDER_INVOICE + ":" + request.getObjId();
            RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
            try {
                if (redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS)) {
                    AcFOrderInvoice acOrderInvoice = acOrderInvoiceMapper.selectById(objId);
                    if (acOrderInvoice == null) {
                        errorMap.put(objId, "当前记录已不存在！");
                        continue;
                    }
                    if (OmsParamConstant.ZERO.equals(acOrderInvoice.getFreezeStatus())) {
                        errorMap.put(objId, "选择的订单发票状态不能解冻！");
                        continue;
                    }
                    acOrderInvoice.setFreezeStatus(OmsParamConstant.ZERO);
                    BaseModelUtil.setupUpdateParam(acOrderInvoice, request.getLoginUser());
                    acOrderInvoiceMapper.updateById(acOrderInvoice);

                    invoiceLogService.addUserOrderLog(acOrderInvoice.getId(),"解冻","冻结改为未冻结",request.getLoginUser());

                } else {
                    errorMap.put(objId, "当前发票处于锁定状态！");
                }
            } catch (InterruptedException e) {
                log.error(LogUtil.format("AcFOrderInvoiceCancelService.unFreeze.error={}", "error"),
                        Throwables.getStackTraceAsString(e));
                throw new NDSException("发票管理解冻异常!");
            } finally {
                redisLock.unlock();
            }
        }
        return R3ParamUtils.getExcuteValueHolder(batchObjIds.size(), errorMap);
    }
}
