package com.jackrain.nea.oc.oms.hold;

import com.jackrain.nea.exception.NDSException;

import java.util.function.Consumer;

/**
 * @Desc :
 * <AUTHOR> 江家雷
 * @Date : 2020/3/22
 */
public class OrderHoldItemDBMetaCache {

    private static final Consumer<String> stringCsm = o -> {
        if (o == null || o.trim().length() == 0) {
            throw new NDSException("Empty Value");
        }
    };


}
