package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.GsiIpBTimeOrderVipOrderSn;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Component
public interface GsiIpBTimeOrderVipOrderSnMapper extends ExtentionMapper<GsiIpBTimeOrderVipOrderSn> {
    /**
     * 根据唯品会订单号查询
     *
     * @param orderSn
     * @return
     */
    @Select("select occupied_order_sn from ip_b_time_order_vip where order_sn = #{orderSn}")
    List<String> selectByOrderSn(@Param("orderSn") String orderSn);
}