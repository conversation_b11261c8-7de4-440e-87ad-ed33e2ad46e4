package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBNaiKaUnFreezeTaskSql;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaikaUnfreeze;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderNaiKaUnfreezeMapper
 * @Description 奶卡解冻中间表
 * <AUTHOR>
 * @Date 2022/6/30 11:25
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderNaiKaUnfreezeMapper extends ExtentionMapper<OcBOrderNaikaUnfreeze> {

    @SelectProvider(type = OcBNaiKaUnFreezeTaskSql.class, method = "selectUnFreezeNaiKaOrder")
    List<OcBOrderNaikaUnfreeze> selectUnFreezeNaiKaOrder(@Param(value = "limit") int limit,
                                                         @Param(value = "taskTableName") String taskTableName, @Param(value = "unfreezeTimes") Integer unfreezeTimes);

    @Select("SELECT * FROM oc_b_order_naika_unfreeze WHERE oc_b_order_id=#{ocBOrderId} and isactive='Y' ")
    List<OcBOrderNaikaUnfreeze> selectUnFreezeNaiKaOrderByOrderId(Long ocBOrderId);
}
