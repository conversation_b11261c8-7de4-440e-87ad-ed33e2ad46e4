package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoJxOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

@Mapper
public interface IpBTaobaoJxOrderMapper extends ExtentionMapper<IpBTaobaoJxOrder> {
    /**
     * @return com.jackrain.nea.oc.oms.model.table.IpBJitxOrder
     * <AUTHOR>
     * @Description 根据订单号查询订单数据
     * @Date 2019-7-10
     * @Param [orderNo]
     **/
    @Select("SELECT * FROM ip_b_taobao_jx_order WHERE dealer_order_id=#{orderNo}")
    IpBTaobaoJxOrder selectTaobaoJxOrderByOrderSn(@Param("orderNo") String orderNo);

    class TaobaoJxOrderSqlBuilder {
        /**
         * 创建更新订单转换状态SQL
         *
         * @param orderNo          订单编号
         * @param isTrans          转换状态
         * @param isUpdateTransNum 是否更新转换数量
         * @param remarks          转换备注信息
         * @return 更新SQL语句
         */
        public String buildUpdateOrderTransSQL(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                                               @Param("isUpdateTransNum") boolean isUpdateTransNum,
                                               @Param("remarks") String remarks) {

            return new SQL() {
                {
                    UPDATE("ip_b_taobao_jx_order");
                    SET("istrans=#{isTrans}");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                        SET("transdate = SYSDATE()");
                    }
                    SET("sysremark=#{remarks}");
                    WHERE("dealer_order_id=#{orderNo}");
                }
            }.toString();
        }
    }

    /**
     * 更新订单转换状态
     *
     * @param orderNo          订单编号
     * @param isTrans          转换状态
     * @param isUpdateTransNum 是否更新转换数量
     * @param remarks          转换备注信息
     * @return 更新结果
     */
    @UpdateProvider(type = IpBTaobaoJxOrderMapper.TaobaoJxOrderSqlBuilder.class, method = "buildUpdateOrderTransSQL")
    int updateIsTrans(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                      @Param("isUpdateTransNum") boolean isUpdateTransNum, @Param("remarks") String remarks);
}