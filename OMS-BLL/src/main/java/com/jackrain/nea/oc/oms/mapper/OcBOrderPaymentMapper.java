package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface OcBOrderPaymentMapper extends ExtentionMapper<OcBOrderPayment> {
    class UpdateRecord {
        public String updateSql(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("OC_B_ORDER_PAYMENT");
                    for (String key : map.keySet()) {
                        if (!"id".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("ID = #{id}");
                }
            }.toString();
        }
    }
    /**
     * 整单支付明细
     *
     * @param orderId 订单ID
     * @return List<OcBOrderPayment>
     */
    @Select("SELECT * FROM oc_b_order_payment WHERE oc_b_order_id=#{orderId} and isactive='Y' ")
    List<OcBOrderPayment> selectOrderPaymentItemList(long orderId);
    /**
     * 更新订单信息
     *
     * @param jsonObject
     * @return
     */
    @UpdateProvider(type = OcBOrderPaymentFiMapper.UpdateRecord.class, method = "updateSql")
    int updateRecord(JSONObject jsonObject);

    /**
     * 查询 付款信息表 是否存在记录
     *
     * @param orderId 订单Id
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order_payment WHERE oc_b_order_id=#{orderId}")
    Integer queryCountbyId(@Param("orderId") Long orderId);

    /**
     * 获取 付款信息表主键
     *
     * @param orderId
     * @return
     */
    @Select("SELECT id FROM oc_b_order_payment WHERE oc_b_order_id=#{orderId}")
    Long queryOrderPaymentId(@Param("orderId") Long orderId);

    /**
     * 删除订单下的付款信息
     *
     * @param orderId
     * @return
     */
    @Delete("delete from oc_b_order_payment where oc_b_order_id=#{orderId}")
    Integer deletePaymentByorderId(@Param("orderId") Long orderId);
}