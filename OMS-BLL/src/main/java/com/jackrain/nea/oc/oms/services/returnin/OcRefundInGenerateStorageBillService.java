package com.jackrain.nea.oc.oms.services.returnin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.inf.api.oms.SgOmsStoInNoticeAndResultCmd;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsStoInNoticeAndResultRequest;
import com.burgeon.r3.sg.store.api.adjust.SgBStoAdjustSaveAndSubmitCmd;
import com.burgeon.r3.sg.store.api.adjust.SgBStoAdjustSaveR3Cmd;
import com.burgeon.r3.sg.store.api.freeze.SgBStoFreezeSaveAndSubmitCmd;
import com.burgeon.r3.sg.store.api.in.SgBStoInNoticesCmd;
import com.burgeon.r3.sg.store.api.in.SgBStoInResultCmd;
import com.burgeon.r3.sg.store.common.OrderTypeEnum;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustMainSaveRequest;
import com.burgeon.r3.sg.store.model.request.adjust.SgBStoAdjustSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveItemRequest;
import com.burgeon.r3.sg.store.model.request.freeze.SgBStoFreezeSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInNoticesBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultBillSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultItemSaveRequest;
import com.burgeon.r3.sg.store.model.request.in.SgBStoInResultSaveRequest;
import com.burgeon.r3.sg.store.model.result.in.SgBStoInNoticesBillSaveResult;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.enums.DrpStoreTypeEnum;
import com.jackrain.nea.cpext.model.table.CpCStore;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.request.GenerateStorageBillRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordAddTaskService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import utils.AssertUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> lin yu
 * @date : 2022/7/26 上午9:51
 * @describe : 退货库存交互
 */

@Slf4j
@Component
public class OcRefundInGenerateStorageBillService {
    @Reference(group = "sg", version = "1.0")
    private SgBStoInNoticesCmd stoInNoticesCmd;

    @Reference(group = "sg", version = "1.0")
    private SgBStoInResultCmd stoInResultCmd;

    @Reference(group = "sg", version = "1.0")
    private SgOmsStoInNoticeAndResultCmd stoInNoticeAndResultCmd;

    @Reference(group = "sg", version = "1.0")
    private SgBStoAdjustSaveAndSubmitCmd stoAdjustSaveAndSubmitCmd;

    @Reference(group = "sg", version = "1.0")
    private SgBStoAdjustSaveR3Cmd stoAdjustSaveR3Cmd;

    @Reference(group = "sg", version = "1.0")
    private SgBStoFreezeSaveAndSubmitCmd stoFreezeSaveAndSubmitCmd;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBSapSalesDataRecordAddTaskService sapSalesDataRecordAddTaskService;

    public ValueHolderV14<String> generateStorageBill(GenerateStorageBillRequest request) {
        ValueHolderV14<String> vh = new ValueHolderV14<>(ResultCode.SUCCESS, "SUCCESS");

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcRefundInGenerateStorageBillService.generateStorageBill Start:{}", "退货库存交互入参"), JSONObject.toJSONString(request));
        }

        try {

            checkParams(request);

            String billNo = generateStorageBillByType(request);
            vh.setData(billNo);

            // 入库生成销售数据信息
            if (GenerateStorageBillRequest.OperationType.INSERT_RESULT == request.getOperationType()
                    || GenerateStorageBillRequest.OperationType.INSERT_NOTICES_AND_RESULT == request.getOperationType()) {
                sapSalesDataRecordAddTaskService.addTask(1, request.getReturnOrder().getId(), request.getUser());
            }
        } catch (Exception e) {
            String apply = OcReturnInSupport.expMsgFun.apply(e);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("generateStorageBill.exp:{}", "退货库存交互", "ReturnInMatch"), apply);
            }
            log.error(LogUtil.format("OcRefundInGenerateStorageBillService.generateStorageBill. Fail,Reason:{}", "退货库存交互异常", "generateStorageBill"), Throwables.getStackTraceAsString(e));
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("生成库存单据失败:" + e.getMessage());
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("generateStorageBill.result:{}", "退货库存交互", "ReturnInMatch"), JSONObject.toJSONString(vh));
        }

        return vh;
    }

    private String generateStorageBillByType(GenerateStorageBillRequest request) {

        switch (request.getOperationType()) {
            case INSERT_NOTICES_AND_RESULT:
                return generateStoInNoticesAndResult(request.getReturnOrder(), request.getReturnOrderRefundList(), request.getUser(), false, request.getBillType());
            case INSERT_NOTICES_PASS_WMS:
                return generateStoInNotices(request.getReturnOrder(), request.getReturnOrderRefundList(), request.getUser(), true, request.getBillType());
            case INSERT_RESULT:
                return generateStoInResult(request.getReturnOrder(), request.getReturnOrderRefundList(), request.getUser(), request.getBillType());
            case INSERT_ADJUST:
                return generateStoAdjust(request.getRefundIn(), request.getRefundInProductItemList(), request.getAdjustPropId(), request.getUser(), false, request.getAdjustSourceBillType());
            case INSERT_ADJUST_PASS_WMS:
                return generateStoAdjust(request.getRefundIn(), request.getRefundInProductItemList(), request.getAdjustPropId(), request.getUser(), true, request.getAdjustSourceBillType());
            default:
                throw new NDSException("未知的操作类型");
        }
    }


    private String generateStoAdjust(OcBRefundIn refundIn,
                                     List<OcBRefundInProductItem> refundInProductItemList,
                                     Integer adjustPropId,
                                     User user,
                                     boolean isPassWms,
                                     Integer adjustSourceBillType) {

        SgBStoAdjustSaveRequest stoAdjustSaveRequest =
                encapsulationStoAdjust(refundIn, refundInProductItemList, adjustPropId, user, isPassWms, adjustSourceBillType);

        ValueHolderV14<SgR3BaseResult> result;

        if (isPassWms) {
            result = stoAdjustSaveR3Cmd.save(stoAdjustSaveRequest);
            AssertUtils.cannot(!result.isOK() || result.getData() == null, result.getMessage());

        } else {
            result = stoAdjustSaveAndSubmitCmd.saveAndSubmit(stoAdjustSaveRequest);
            AssertUtils.cannot(!result.isOK() || result.getData() == null, result.getMessage());
        }

        return result.getData().getBillNo();
    }

    private SgBStoAdjustSaveRequest encapsulationStoAdjust(OcBRefundIn refundIn,
                                                           List<OcBRefundInProductItem> refundInProductItemList,
                                                           Integer adjustPropId, User user,
                                                           boolean isPassWms,
                                                           Integer adjustSourceBillType) {

        SgBStoAdjustSaveRequest stoAdjustSaveRequest = new SgBStoAdjustSaveRequest();
        SgBStoAdjustMainSaveRequest stoAdjustMainSaveRequest = new SgBStoAdjustMainSaveRequest();

        //库存日期
        Date inTime = refundIn.getWarehouseInTime();
        stoAdjustMainSaveRequest.setBillDate(inTime == null ? new Date() : inTime);
        //单据类型：正常调整
        stoAdjustMainSaveRequest.setBillType(OrderTypeEnum.ADJUST_BILL_TYPE_01.getValue());
        //调整店仓
        stoAdjustMainSaveRequest.setCpCStoreId(refundIn.getInStoreId());
        stoAdjustMainSaveRequest.setCpCStoreEname(refundIn.getInStoreEname());
        stoAdjustMainSaveRequest.setCpCStoreEcode(refundIn.getInStoreEcode());
        //调整性质
        // SgConstantsIF.SERVICE_NODE_ADJUST_PROP_NO_SOURCE_IN   //无名件入库
        // SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE //无头件出库
        stoAdjustMainSaveRequest.setSgBAdjustPropId(adjustPropId);
        //来源单据类型：无头件登记单
        stoAdjustMainSaveRequest.setSourceBillType(adjustSourceBillType);

        //来源单据编号：退货入库结果单表中的退货入库单编号
        stoAdjustMainSaveRequest.setSourceBillNo(refundIn.getId().toString());
        stoAdjustMainSaveRequest.setSourceBillId(refundIn.getId());
        //物流公司：退货入库结果单的物流公司
        stoAdjustMainSaveRequest.setLogisticNumber(refundIn.getLogisticNumber());
        stoAdjustMainSaveRequest.setCpCLogisticsId(refundIn.getCpCLogisticsId());
        stoAdjustMainSaveRequest.setCpCLogisticsEcode(refundIn.getCpCLogisticsEcode());
        stoAdjustMainSaveRequest.setCpCLogisticsEname(refundIn.getCpCLogisticsEname());
        //物流单号：退货入库结果单的物流单号
        //是否传WMS为 0否 1是
        stoAdjustMainSaveRequest.setIsPassWms(isPassWms ? 1 : 0);
        //传wms状态：未传
        stoAdjustMainSaveRequest.setWmsStatus(0);
        stoAdjustMainSaveRequest.setObjId(-1L);

        //    ArrayList<SgBStoAdjustItemSaveRequest> itemList = new ArrayList<>();

        Map<String, SgBStoAdjustItemSaveRequest> groupMap = new HashMap<>();
        for (OcBRefundInProductItem item : refundInProductItemList) {
            String skuCode = item.getPsCSkuEcode();
            String proDate = item.getProductDate();
            String proMark = item.getProductMark();
            String groupKey = skuCode + proDate + proMark;
            BigDecimal qty = item.getQty();
            if ((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE == adjustPropId) {
                qty = qty.negate();
            }
            SgBStoAdjustItemSaveRequest itemRequest = groupMap.get(groupKey);
            if (itemRequest == null) {
                itemRequest = new SgBStoAdjustItemSaveRequest();
                itemRequest.setQty(qty);
                itemRequest.setSourceBillItemId(item.getId());
                itemRequest.setPsCSkuId(item.getPsCSkuId());
                itemRequest.setPsCSkuEcode(skuCode);
                itemRequest.setPsCProId(item.getPsCProId());
                itemRequest.setPsCProEcode(item.getPsCProEcode());
                itemRequest.setPsCProEname(item.getPsCSkuEcode());
                itemRequest.setPsCSpec1Id(item.getPsCClrId());
                itemRequest.setPsCSpec2Id(item.getPsCSizeId());
                itemRequest.setProduceDate(proDate);
                itemRequest.setGbcode(item.getGbcode());
                itemRequest.setStorageType(proMark);
                itemRequest.setId(-1L);
                groupMap.put(groupKey, itemRequest);
                continue;
            }
            BigDecimal add = itemRequest.getQty().add(qty);
            itemRequest.setQty(add);
        }
        List<SgBStoAdjustItemSaveRequest> itemList = groupMap.values().stream().collect(Collectors.toList());

        /*for (OcBRefundInProductItem item : refundInProductItemList) {
            SgBStoAdjustItemSaveRequest itemRequest = new SgBStoAdjustItemSaveRequest();
            itemRequest.setSourceBillItemId(item.getId());
            if ((int) SgConstantsIF.SERVICE_NODE_ADJUST_PROP_FLUSH_NO_SOURCE == adjustPropId) {
                itemRequest.setQty(item.getQty().negate());
            } else {
                itemRequest.setQty(item.getQty());
            }
            itemRequest.setPsCSkuId(item.getPsCSkuId());
            itemRequest.setPsCSkuEcode(item.getPsCSkuEcode());
            itemRequest.setPsCProId(item.getPsCProId());
            itemRequest.setPsCProEcode(item.getPsCProEcode());
            itemRequest.setPsCProEname(item.getPsCSkuEcode());
            itemRequest.setPsCSpec1Id(item.getPsCClrId());
            itemRequest.setPsCSpec2Id(item.getPsCSizeId());
            itemRequest.setProduceDate(item.getProductDate());
            itemRequest.setGbcode(item.getGbcode());
            itemRequest.setStorageType(item.getProductMark());

            itemRequest.setId(-1L);
            itemList.add(itemRequest);
        }*/

        stoAdjustSaveRequest.setMainRequest(stoAdjustMainSaveRequest);
        stoAdjustSaveRequest.setItems(itemList);
        stoAdjustSaveRequest.setLoginUser(user);
        stoAdjustSaveRequest.setObjId(-1L);
        return stoAdjustSaveRequest;
    }

    /**
     * 生成入库通知单
     */
    private String generateStoInNotices(OcBReturnOrder returnOrder,
                                        List<OcBReturnOrderRefund> returnOrderRefundList,
                                        User user,
                                        boolean isPassWms,
                                        String billType) {

        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + ",生成入库通知单");
        }

        SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest
                = encapsulationStoInNotices(returnOrder, returnOrderRefundList, isPassWms, billType);

        ValueHolderV14<SgBStoInNoticesBillSaveResult> result = stoInNoticesCmd.addSgStoInNotices(stoInNoticesBillSaveRequest, user);

        AssertUtils.cannot(!result.isOK() || result.getData() == null, result.getMessage());

        return result.getData().getBillNo();
    }

    /**
     * 生成逻辑入库单
     */
    private String generateStoInResult(OcBReturnOrder returnOrder,
                                       List<OcBReturnOrderRefund> returnOrderRefundList,
                                       User user,
                                       String billType) {

        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + ",生成逻辑入库单");
        }

        SgBStoFreezeBillSaveRequest stoFreezeBillSaveRequest = new SgBStoFreezeBillSaveRequest();

        List<SgBStoInResultBillSaveRequest> stoInResultBillSaveRequests
                = encapsulationStoInResult(returnOrder, returnOrderRefundList, user, billType, stoFreezeBillSaveRequest);
        boolean isNeedGenFreezeBil = stoFreezeBillSaveRequest != null
                && stoFreezeBillSaveRequest.getFreezeSaveRequest() != null
                && CollectionUtils.isNotEmpty(stoFreezeBillSaveRequest.getSgBStoFreezeSaveItemRequests());
        String billNo = returnOrder.getBillNo();
        ValueHolderV14 result;
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("generateStoInResult.isFreezeBil:{},param:{},{}", billNo),
                    isNeedGenFreezeBil, JSON.toJSONString(stoInResultBillSaveRequests), JSON.toJSONString(stoFreezeBillSaveRequest));
        }
        if (isNeedGenFreezeBil) {
             result = stoInResultCmd.inAndFreezeSaveAndAuditBill(stoInResultBillSaveRequests, stoFreezeBillSaveRequest);
        } else {
            result = stoInResultCmd.saveAndAuditBill(stoInResultBillSaveRequests);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("generateStoInResult.result:{}", billNo), JSONObject.toJSONString(result));
        }
        AssertUtils.cannot(Objects.isNull(result) || !result.isOK(), result.getMessage());
    //    generateStoFreezeBill(stoFreezeBillSaveRequest);

        return "";
    }

    private void generateStoFreezeBill(SgBStoFreezeBillSaveRequest stoFreezeBillSaveRequest) {

        if (stoFreezeBillSaveRequest != null
                && stoFreezeBillSaveRequest.getFreezeSaveRequest() != null
                && CollectionUtils.isNotEmpty(stoFreezeBillSaveRequest.getSgBStoFreezeSaveItemRequests())) {

            try {

                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("OcRefundInGenerateStorageBillService.generateStoFreezeBill Start:{}", "生成冻结单入参"), JSONObject.toJSONString(stoFreezeBillSaveRequest));
                }

                ValueHolderV14<SgR3BaseResult> resultValueHolderV14 = stoFreezeSaveAndSubmitCmd.saveAndSubmit(stoFreezeBillSaveRequest);

                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("OcRefundInGenerateStorageBillService.generateStoFreezeBill finish:{}", "生成冻结单出参"), JSONObject.toJSONString(resultValueHolderV14));
                }

            } catch (Exception e) {

                log.error(LogUtil.format("OcRefundInGenerateStorageBillService generateStoFreezeBill Fail,Reason:{}", "生成冻结单异常"), Throwables.getStackTraceAsString(e));

            }
        }
    }


    /**
     * 生成入库通知单 + 逻辑入库单  return：入库通知单编号
     */
    public String generateStoInNoticesAndResult(OcBReturnOrder returnOrder,
                                                List<OcBReturnOrderRefund> returnOrderRefundList,
                                                User user,
                                                boolean isPassWms,
                                                String billType) {

        if (log.isDebugEnabled()) {
            log.debug(this.getClass().getName() + ",生成入库通知单和逻辑入库单");
        }

        SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest
                = encapsulationStoInNotices(returnOrder, returnOrderRefundList, isPassWms, billType);

        SgBStoFreezeBillSaveRequest stoFreezeBillSaveRequest = new SgBStoFreezeBillSaveRequest();

        List<SgBStoInResultBillSaveRequest> stoInResultBillSaveRequests
                = encapsulationStoInResult(returnOrder, returnOrderRefundList, user, billType, stoFreezeBillSaveRequest);

        SgOmsStoInNoticeAndResultRequest stoInNoticeAndResultRequest = new SgOmsStoInNoticeAndResultRequest();
        stoInNoticeAndResultRequest.setStoInNoticesBillSaveRequest(stoInNoticesBillSaveRequest);
        stoInNoticeAndResultRequest.setStoInResultBillSaveRequests(stoInResultBillSaveRequests);
        stoInNoticeAndResultRequest.setUser(user);

        boolean isNeedGenFreezeBil = stoFreezeBillSaveRequest != null
                && stoFreezeBillSaveRequest.getFreezeSaveRequest() != null
                && CollectionUtils.isNotEmpty(stoFreezeBillSaveRequest.getSgBStoFreezeSaveItemRequests());
        ValueHolderV14<String> result;
        String billNo = returnOrder.getBillNo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("generateStoInNoticesAndResult.isFreezeBil:{},param:{},{}", billNo),
                    isNeedGenFreezeBil, JSON.toJSONString(stoInResultBillSaveRequests), JSON.toJSONString(stoFreezeBillSaveRequest));
        }
        if (isNeedGenFreezeBil) {
             result = stoInNoticeAndResultCmd.createInNoticeAndResultAndFreeze(stoInNoticeAndResultRequest, stoFreezeBillSaveRequest);
        } else {
            result = stoInNoticeAndResultCmd.createInNoticeAndResult(stoInNoticeAndResultRequest);
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("generateStoInNoticesAndResult.result:{}", billNo), JSONObject.toJSONString(result));
        }
        AssertUtils.cannot(Objects.isNull(result) || !result.isOK(), result.getMessage());
    //    generateStoFreezeBill(stoFreezeBillSaveRequest);

        return result.getData();
    }


    private List<SgBStoInResultBillSaveRequest> encapsulationStoInResult(OcBReturnOrder returnOrder,
                                                                         List<OcBReturnOrderRefund> returnOrderRefundList,
                                                                         User user,
                                                                         String billType,
                                                                         SgBStoFreezeBillSaveRequest stoFreezeBillSaveRequest) {

        SgBStoInResultSaveRequest stoInResultRequest = new SgBStoInResultSaveRequest();
        List<SgBStoInResultItemSaveRequest> stoInResultItemList = Collections.synchronizedList(new ArrayList<>());

        //单据日期
        stoInResultRequest.setBillDate(new Date());
        //来源单id
        stoInResultRequest.setSourceBillId(returnOrder.getId());
        //来源单据编号
        stoInResultRequest.setSourceBillNo(returnOrder.getBillNo());
        //来源单类型
        stoInResultRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);

        //逻辑仓
        stoInResultRequest.setCpCStoreId(returnOrder.getCpCStoreId());
        stoInResultRequest.setCpCStoreEcode(returnOrder.getCpCStoreEcode());
        stoInResultRequest.setCpCStoreEname(returnOrder.getCpCStoreEname());

        //入库日期
        Date inTime = returnOrder.getInTime();
        stoInResultRequest.setInTime(inTime == null ? new Date() : inTime);
        //是否最后一次入库
        stoInResultRequest.setIsLast(SgConstants.IS_LAST_YES);
        //wms单据编号
        stoInResultRequest.setWmsBillNo(returnOrder.getWmsBillNo());
        //备注
        stoInResultRequest.setRemark(returnOrder.getRemark());

        stoInResultRequest.setSgBStoInNoticesId(returnOrder.getStoInNoticesId());
        stoInResultRequest.setSgBStoInNoticesNo(returnOrder.getStoInNoticesNo());
        stoInResultRequest.setSourceBillSourceBillType(billType);

        OcReturnInRelation returnInRelation = OcReturnInSupport.matchedReturn.get();
        if (returnInRelation != null) {
            buildStoInResultItemsWithinProductDate(returnInRelation, returnOrderRefundList, stoInResultItemList, stoFreezeBillSaveRequest);
        } else {
            // 构建逻辑入库单明细
            buildStoInResultItems(returnOrderRefundList, stoInResultItemList);
        }

        SgBStoInResultBillSaveRequest stoInResultBillSaveRequest = new SgBStoInResultBillSaveRequest();
        stoInResultBillSaveRequest.setInResultSaveRequest(stoInResultRequest);
        stoInResultBillSaveRequest.setInItemResultSaveRequestList(stoInResultItemList);
        stoInResultBillSaveRequest.setIsOneClickLibrary(true);
        stoInResultBillSaveRequest.setLoginUser(user);

        ArrayList<SgBStoInResultBillSaveRequest> stoInResultBillSaveRequests = new ArrayList<>();
        stoInResultBillSaveRequests.add(stoInResultBillSaveRequest);

        if (stoFreezeBillSaveRequest != null && CollectionUtils.isNotEmpty(stoFreezeBillSaveRequest.getSgBStoFreezeSaveItemRequests())) {
            SgBStoFreezeSaveRequest stoFreezeSaveRequest = buildStoFreezeSaveRequest(returnOrder);
            stoFreezeBillSaveRequest.setFreezeSaveRequest(stoFreezeSaveRequest);
            stoFreezeBillSaveRequest.setObjId(-1L);
            stoFreezeBillSaveRequest.setLoginUser(user);
        }
        return stoInResultBillSaveRequests;
    }

    private SgBStoFreezeSaveRequest buildStoFreezeSaveRequest(OcBReturnOrder returnOrder) {
        SgBStoFreezeSaveRequest stoFreezeSaveRequest = new SgBStoFreezeSaveRequest();

        stoFreezeSaveRequest.setId(-1L);

        //单据日期
        stoFreezeSaveRequest.setBillDate(new Date());
        //来源单id
        stoFreezeSaveRequest.setSourceBillId(returnOrder.getId());
        //来源单类型
        stoFreezeSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
        //来源单据编号
        stoFreezeSaveRequest.setSourceBillNo(returnOrder.getBillNo());
        //逻辑仓
        stoFreezeSaveRequest.setCpCStoreId(returnOrder.getCpCStoreId());
        stoFreezeSaveRequest.setCpCStoreEcode(returnOrder.getCpCStoreEcode());
        stoFreezeSaveRequest.setCpCStoreEname(returnOrder.getCpCStoreEname());
        //业务类型:
        stoFreezeSaveRequest.setBillType(1);
        //备注
        stoFreezeSaveRequest.setRemark(returnOrder.getRemark());
        return stoFreezeSaveRequest;
    }

    private void buildStoInResultItems(List<OcBReturnOrderRefund> returnOrderRefundList, List<SgBStoInResultItemSaveRequest> stoInResultItemList) {
        for (OcBReturnOrderRefund item : returnOrderRefundList) {
            SgBStoInResultItemSaveRequest stoInResultItemSaveRequest = buildStoInResultItem(item);
            //入库数量
            stoInResultItemSaveRequest.setQty(item.getQtyIn());
            stoInResultItemSaveRequest.setProduceDate(item.getReserveVarchar01());
            stoInResultItemList.add(stoInResultItemSaveRequest);
        }
    }

    private SgBStoInResultItemSaveRequest buildStoInResultItem(OcBReturnOrderRefund item) {
        SgBStoInResultItemSaveRequest stoInResultItemSaveRequest = new SgBStoInResultItemSaveRequest();

        //来源单据明细ID
        stoInResultItemSaveRequest.setSourceBillItemId(item.getId());

        //条码
        stoInResultItemSaveRequest.setPsCSkuId(item.getPsCSkuId());
        stoInResultItemSaveRequest.setPsCSkuEcode(item.getPsCSkuEcode());
        //国标码
        stoInResultItemSaveRequest.setGbcode(item.getBarcode());
        //商品
        stoInResultItemSaveRequest.setPsCProId(item.getPsCProId());
        stoInResultItemSaveRequest.setPsCProEcode(item.getPsCProEcode());
        stoInResultItemSaveRequest.setPsCProEname(item.getPsCProEname());
        //颜色
        stoInResultItemSaveRequest.setPsCSpec1Id(item.getPsCClrId());
        stoInResultItemSaveRequest.setPsCSpec1Ecode(item.getPsCClrEcode());
        stoInResultItemSaveRequest.setPsCSpec1Ename(item.getPsCClrEname());
        //尺寸
        stoInResultItemSaveRequest.setPsCSpec2Id(item.getPsCSizeId());
        stoInResultItemSaveRequest.setPsCSpec2Ecode(item.getPsCSizeEcode());
        stoInResultItemSaveRequest.setPsCSpec2Ename(item.getPsCSizeEname());
        return stoInResultItemSaveRequest;
    }


    public SgBStoInNoticesBillSaveRequest encapsulationStoInNotices(OcBReturnOrder returnOrder,
                                                                    List<OcBReturnOrderRefund> returnOrderRefundList,
                                                                    boolean isPassWms,
                                                                    String billType) {

        SgBStoInNoticesBillSaveRequest stoInNoticesBillSaveRequest = new SgBStoInNoticesBillSaveRequest();
        SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest inNoticesSaveRequest =
                new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesSaveRequest();
        List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> inNoticesItemSaveRequests = Lists.newArrayList();

        Long origOrderId = returnOrder.getOrigOrderId();
        OcBOrder oldOcBOrder = ocBOrderMapper.selectById(origOrderId);

        //单据日期
        inNoticesSaveRequest.setBillDate(new Date());
        //来源单id
        inNoticesSaveRequest.setSourceBillId(returnOrder.getId());
        //来源单据编号
        inNoticesSaveRequest.setSourceBillNo(returnOrder.getBillNo());
        //来源单类型
        inNoticesSaveRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL_REF);
        //退款申请单号
        inNoticesSaveRequest.setReturnId(returnOrder.getReturnId());
        //实体仓
        //List<CpCStore> stores = cpRpcService.selectStoreById(returnOrder.getStoreId().intValue());
        //AssertUtils.cannot(CollectionUtils.isEmpty(stores), "未查到对应逻辑仓信息！");
        if (oldOcBOrder != null) {
            inNoticesSaveRequest.setSgBStoOutNoticesNo(oldOcBOrder.getSgBOutBillNo());

        }
        inNoticesSaveRequest.setCpCPhyWarehouseId(returnOrder.getCpCPhyWarehouseInId());
        //inNoticesSaveRequest.setCpCPhyWarehouseEcode(stores.get(0).getCpCPhyWarehouseEcode());
        //inNoticesSaveRequest.setCpCPhyWarehouseEname(stores.get(0).getCpCPhyWarehouseEname());
        //店铺ID
        inNoticesSaveRequest.setCpCShopId(returnOrder.getCpCShopId());
        //发货人
        inNoticesSaveRequest.setSender(returnOrder.getReceiveName());
        //发货人手机
        inNoticesSaveRequest.setSenderPhone(returnOrder.getReceivePhone());
        //发货人电话
        inNoticesSaveRequest.setSenderMobile(returnOrder.getReceiveMobile());
        //省
        inNoticesSaveRequest.setCpCRegionProvinceId(returnOrder.getReceiverProvinceId());
        inNoticesSaveRequest.setCpCRegionProvinceEname(returnOrder.getReceiverProvinceName());
        //市
        inNoticesSaveRequest.setCpCRegionCityId(returnOrder.getReceiverCityId());
        inNoticesSaveRequest.setCpCRegionCityEname(returnOrder.getReceiverCityName());
        //区
        inNoticesSaveRequest.setCpCRegionAreaId(returnOrder.getReceiverAreaId());
        inNoticesSaveRequest.setCpCRegionAreaEname(returnOrder.getReceiverAreaName());
        //发货人地址
        inNoticesSaveRequest.setSenderAddress(returnOrder.getReceiveAddress());
        //物流公司
        inNoticesSaveRequest.setCpCLogisticsId(returnOrder.getCpCLogisticsId());
        inNoticesSaveRequest.setCpCLogisticsEcode(returnOrder.getCpCLogisticsEcode());
        inNoticesSaveRequest.setCpCLogisticsEname(returnOrder.getCpCLogisticsEname());
        //物流单号
        inNoticesSaveRequest.setLogisticNumber(returnOrder.getLogisticsCode());
        //是否通知WMS
        inNoticesSaveRequest.setIsPassWms(isPassWms ? 1 : 0);
        //备注
        inNoticesSaveRequest.setRemark(returnOrder.getRemark());
        inNoticesSaveRequest.setInType(1);//电商入库

        //来源单据来源类型
        inNoticesSaveRequest.setSourceBillSourceBillType(billType);
        inNoticesSaveRequest.setSourcecode(returnOrder.getTid());
        inNoticesSaveRequest.setCpCLogisticsId(returnOrder.getCpCLogisticsId());
        inNoticesSaveRequest.setCpCLogisticsEcode(returnOrder.getCpCLogisticsEcode());
        inNoticesSaveRequest.setCpCLogisticsEname(returnOrder.getCpCLogisticsEname());

        //根据实体仓查询退货逻辑仓信息
        List<Long> storeIds = cpRpcService.queryStoreList(returnOrder.getCpCPhyWarehouseInId());
        AssertUtils.cannot(storeIds == null || storeIds.size() <= 0, "根据实体仓查询逻辑仓ID为空！");
        List<CpCStore> cpCStoreList = cpRpcService
                .queryStoreInfoByIds(storeIds.stream().map(Long::intValue).collect(Collectors.toList()));
        AssertUtils.cannot(cpCStoreList == null || cpCStoreList.size() <= 0, "根据逻辑仓ID查询逻辑仓信息为空！");
        //退货逻辑仓
        CpCStore cpCStore = null;
        Map<String, List<CpCStore>> storeMap =
                cpCStoreList.stream().filter(item -> item.getStoretype() != null)
                        .collect(Collectors.groupingBy(CpCStore::getStoretype));
        List<CpCStore> returnStoreList = storeMap.get(DrpStoreTypeEnum.TYPE_27.getValue());
        if (returnStoreList != null && returnStoreList.size() > 0) {
            cpCStore = returnStoreList.get(0);
        } else {
            List<CpCStore> storeInList = storeMap.get(DrpStoreTypeEnum.TYPE_18.getValue());
            if (storeInList != null && storeInList.size() > 0) {
                cpCStore = storeInList.get(0);
            } else {
                cpCStore = cpCStoreList.get(0);
            }
        }

        OcReturnInRelation returnInRelation = OcReturnInSupport.matchedReturn.get();
        if (returnInRelation != null) {
            buildStoInNoticeItemsWithinProductDate(returnInRelation, returnOrder, cpCStore, returnOrderRefundList, inNoticesItemSaveRequests);
        } else {
            buildStoInNoticeItems(returnOrder, cpCStore, returnOrderRefundList, inNoticesItemSaveRequests);
        }
        stoInNoticesBillSaveRequest.setInNoticesSaveRequest(inNoticesSaveRequest);
        stoInNoticesBillSaveRequest.setInNoticesItemSaveRequests(inNoticesItemSaveRequests);
        return stoInNoticesBillSaveRequest;
    }

    /**
     * 构建入库通知单明细
     *
     * @param order
     * @param cpCStore
     * @param refundItems
     * @param noticeItemRequests
     */
    private void buildStoInNoticeItems(OcBReturnOrder order, CpCStore cpCStore, List<OcBReturnOrderRefund> refundItems,
                                       List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> noticeItemRequests) {
        for (OcBReturnOrderRefund item : refundItems) {
            SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest req = buildStoInNoticeItem(order, cpCStore, item);
            //通知数量
            req.setQty(item.getQtyRefund());
            //入库数量
            req.setQtyIn(BigDecimal.ZERO);
            //差异数量
            req.setQtyDiff(item.getQtyRefund());
            req.setProduceDate(item.getReserveVarchar01());
            noticeItemRequests.add(req);
        }
    }

    private void checkParams(GenerateStorageBillRequest request) {

        AssertUtils.notNull(request, "请求参数不能为空");
        AssertUtils.notNull(request.getUser(), "请求用户不能为空");
        AssertUtils.notNull(request.getOperationType(), "操作类型不能为空");

    }

    /**
     * @param returnInRelation
     * @param order
     * @param cpCStore
     * @param refundItems
     * @param noticeItemRequests
     */
    private void buildStoInNoticeItemsWithinProductDate(OcReturnInRelation returnInRelation, OcBReturnOrder order,
                                                        CpCStore cpCStore, List<OcBReturnOrderRefund> refundItems,
                                                        List<SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest> noticeItemRequests) {
        Map<Long, Map<String, BigDecimal>> itemMapping = returnInRelation.getSubItemMatchedMapping();
        AssertUtil.notEmpty(itemMapping, "入库通知明细构建,退货明细批次映射为空");
        for (OcBReturnOrderRefund item : refundItems) {
            Long id = item.getId();
            Map<String, BigDecimal> keyValuePair = itemMapping.get(id);
            AssertUtil.notEmpty(itemMapping, String.format("入库通知明细构建,退货明细[%d]批次数量映射为空", id));
            keyValuePair.forEach((k, v) -> {
                SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest req = buildStoInNoticeItem(order, cpCStore, item);
                //通知数量
                req.setQty(v);
                //入库数量
                req.setQtyIn(BigDecimal.ZERO);
                //差异数量
                req.setQtyDiff(v);
                String[] split = k.split(",");
                req.setProduceDate(split[0]);
                noticeItemRequests.add(req);
            });
        }
    }

    /**
     * 入库通知单明细
     *
     * @param order
     * @param cpCStore
     * @param item
     * @return
     */
    private SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest buildStoInNoticeItem(OcBReturnOrder order,
                                                                                               CpCStore cpCStore,
                                                                                               OcBReturnOrderRefund item) {
        SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest stoInNoticesItemSaveRequest =
                new SgBStoInNoticesBillSaveRequest.SgBStoInNoticesItemSaveRequest();

        //来源单据明细ID
        stoInNoticesItemSaveRequest.setSourceBillItemId(item.getId());
        //逻辑仓
//        stoInNoticesItemSaveRequest.setCpCStoreId(order.getCpCStoreId());
//        stoInNoticesItemSaveRequest.setCpCStoreEcode(order.getCpCStoreEcode());
//        stoInNoticesItemSaveRequest.setCpCStoreEname(order.getCpCStoreEname());
        //条码
        stoInNoticesItemSaveRequest.setPsCSkuId(item.getPsCSkuId());
        stoInNoticesItemSaveRequest.setPsCSkuEcode(item.getPsCSkuEcode());
        //国标码
        stoInNoticesItemSaveRequest.setGbcode(item.getBarcode());
        //商品
        stoInNoticesItemSaveRequest.setPsCProId(item.getPsCProId());
        stoInNoticesItemSaveRequest.setPsCProEcode(item.getPsCProEcode());
        stoInNoticesItemSaveRequest.setPsCProEname(item.getPsCProEname());
        //颜色
        stoInNoticesItemSaveRequest.setPsCSpec1Id(item.getPsCClrId());
        stoInNoticesItemSaveRequest.setPsCSpec1Ecode(item.getPsCClrEcode());
        stoInNoticesItemSaveRequest.setPsCSpec1Ename(item.getPsCClrEname());
        //尺寸
        stoInNoticesItemSaveRequest.setPsCSpec2Id(item.getPsCSizeId());
        stoInNoticesItemSaveRequest.setPsCSpec2Ecode(item.getPsCSizeEcode());
        stoInNoticesItemSaveRequest.setPsCSpec2Ename(item.getPsCSizeEname());
        //退货逻辑仓
        stoInNoticesItemSaveRequest.setCpCStoreId(cpCStore.getId());
        stoInNoticesItemSaveRequest.setCpCStoreEcode(cpCStore.getEcode());
        stoInNoticesItemSaveRequest.setCpCStoreEname(cpCStore.getEname());
        return stoInNoticesItemSaveRequest;
    }

    /**
     * @param returnInRelation
     * @param returnOrderRefundList
     * @param stoInResultItemList
     * @param stoFreezeBillSaveRequest
     */
    private void buildStoInResultItemsWithinProductDate(OcReturnInRelation returnInRelation,
                                                        List<OcBReturnOrderRefund> returnOrderRefundList,
                                                        List<SgBStoInResultItemSaveRequest> stoInResultItemList,
                                                        SgBStoFreezeBillSaveRequest stoFreezeBillSaveRequest) {
        List<SgBStoFreezeSaveItemRequest> stoFreezeSaveItemRequestList = new ArrayList<>();
        Map<Long, Map<String, BigDecimal>> itemMapping = returnInRelation.getSubItemMatchedMapping();

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("OcRefundInGenerateStorageBillService buildStoInResultItemsWithinProductDate :{}", "退货明细批次映射"), JSONObject.toJSONString(itemMapping));
        }

        AssertUtil.notEmpty(itemMapping, "逻辑入库明细构建,退货明细批次映射为空");
        for (OcBReturnOrderRefund item : returnOrderRefundList) {
            Long id = item.getId();
            Map<String, BigDecimal> keyValuePair = itemMapping.get(id);
            AssertUtil.notEmpty(keyValuePair, String.format("逻辑入库明细构建,退货明细[%d]批次数量映射为空", id));
            keyValuePair.forEach((k, v) -> {
                SgBStoInResultItemSaveRequest stoInResultItemSaveRequest = buildStoInResultItem(item);
                //入库数量
                stoInResultItemSaveRequest.setQty(v);
                String[] split = k.split(",");
                stoInResultItemSaveRequest.setProduceDate(split[0]);

                if (split.length == 2
                        && !(SgConstantsIF.STOCK_TYPE_GOODS.equalsIgnoreCase(split[1]) || "null".equalsIgnoreCase(split[1]))
                        && v.compareTo(BigDecimal.ZERO) > 0) {
                    SgBStoFreezeSaveItemRequest stoFreezeSaveItemRequest = buildStoFreezeSaveItemRequest(item, split[0], v, split[1]);
                    stoFreezeSaveItemRequestList.add(stoFreezeSaveItemRequest);
                }

                stoInResultItemList.add(stoInResultItemSaveRequest);
            });
        }

        stoFreezeBillSaveRequest.setSgBStoFreezeSaveItemRequests(stoFreezeSaveItemRequestList);
    }

    private SgBStoFreezeSaveItemRequest buildStoFreezeSaveItemRequest(OcBReturnOrderRefund item,
                                                                      String produceDate,
                                                                      BigDecimal qty,
                                                                      String stockType) {

        SgBStoFreezeSaveItemRequest stoFreezeSaveItemRequest = new SgBStoFreezeSaveItemRequest();
        stoFreezeSaveItemRequest.setId(-1L);
        //来源单据明细ID
        stoFreezeSaveItemRequest.setSourceBillItemId(item.getId());

        //条码
        stoFreezeSaveItemRequest.setPsCSkuId(item.getPsCSkuId());
        stoFreezeSaveItemRequest.setPsCSkuEcode(item.getPsCSkuEcode());
        //国标码
        stoFreezeSaveItemRequest.setGbcode(item.getBarcode());
        //商品
        stoFreezeSaveItemRequest.setPsCProId(item.getPsCProId());
        stoFreezeSaveItemRequest.setPsCProEcode(item.getPsCProEcode());
        stoFreezeSaveItemRequest.setPsCProEname(item.getPsCProEname());
        //颜色
        stoFreezeSaveItemRequest.setPsCSpec1Id(item.getPsCClrId());
        stoFreezeSaveItemRequest.setPsCSpec1Ecode(item.getPsCClrEcode());
        stoFreezeSaveItemRequest.setPsCSpec1Ename(item.getPsCClrEname());
        //尺寸
        stoFreezeSaveItemRequest.setPsCSpec2Id(item.getPsCSizeId());
        stoFreezeSaveItemRequest.setPsCSpec2Ecode(item.getPsCSizeEcode());
        stoFreezeSaveItemRequest.setPsCSpec2Ename(item.getPsCSizeEname());

        //生产日期
        stoFreezeSaveItemRequest.setProduceDate(produceDate);
        //数量
        stoFreezeSaveItemRequest.setQty(qty);
        //类型
        stoFreezeSaveItemRequest.setStockType(stockType);

        return stoFreezeSaveItemRequest;

    }


}
