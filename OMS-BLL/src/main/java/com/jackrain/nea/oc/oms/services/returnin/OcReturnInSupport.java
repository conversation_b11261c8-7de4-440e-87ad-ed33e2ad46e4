package com.jackrain.nea.oc.oms.services.returnin;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemFiMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInActualItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInProductItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBRefundInShouldRefundItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderActualMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderLogMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.model.enums.IsGenAdjustEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.VirtualInStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcReturnInRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsStockInMatchParam;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundIn;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInActualItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInLog;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInProductItem;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInShouldRefundItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderActual;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.util.ThreadLocalUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.slf4j.event.Level;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/5
 */
@Slf4j
@Component
public class OcReturnInSupport {

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBRefundInMapper ocBRefundInMapper;

    @Autowired
    private OcBOrderItemFiMapper ocBorderItemMapper;

    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;

    @Autowired
    private OcBRefundInLogMapper ocBRefundInLogMapper;

    @Autowired
    private OcBReturnOrderLogMapper ocBReturnOrderLogMapper;

    @Autowired
    private OcBReturnOrderRefundMapper ocBReturnOrderRefundMapper;

    @Autowired
    private OcBRefundInActualItemMapper ocBRefundInActualItemMapper;

    @Autowired
    private OcBRefundInProductItemMapper ocBRefundInProductItemMapper;

    @Autowired
    private OcBReturnOrderActualMapper ocBReturnOrderActualMapper;

    @Autowired
    private OcBRefundInShouldRefundItemMapper refundInShouldRefundItemMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    public static final ThreadLocal<OcReturnInRelation> matchedReturn = new ThreadLocal<>();

    private final String STAND_PLATFORM_OCRETURNIN_EXCLUDE = "business_system:stand_platform_ocReturnIn_exclude";

    /**
     * @param id
     * @return
     */
    public OcBRefundIn getRefundIn(Long id) {
        return ocBRefundInMapper.selectById(id);
    }

    /**
     * @param id
     * @return
     */
    public List<OcBRefundInProductItem> getRefundInProducts(Long id) {
        return ocBRefundInProductItemMapper.selectProductItemsByRefundInId(id);
    }

    /**
     * @param id
     * @return
     */
    public List<OcBRefundInActualItem> getActualInItems(Long id) {
        return ocBRefundInActualItemMapper.selectActualInItemsByRefundInId(id);
    }

    /**
     * @param id
     * @return
     */
    public OcBReturnOrder getReturnOrder(Long id) {
        return ocBReturnOrderMapper.queryOcBReturnOrderById4Match(id);
    }

    /**
     * @param id
     * @return
     */
    public List<OcBReturnOrderRefund> getReturnItems(Long id) {
        return ocBReturnOrderRefundMapper.selectByOcOrderId(id);
    }

    public OcBReturnOrderLog queryLatestByReturnId(Long id) {
        return ocBReturnOrderLogMapper.queryLatestByReturnId(id);
    }

    public int updateLatestLogModifiedDate(Long id, Long subId) {
        return ocBReturnOrderLogMapper.updateLatestLogModifiedDate(id, subId);
    }

    public void insertReturnOrderLog(OcBReturnOrderLog returnLog) {
        ocBReturnOrderLogMapper.insert(returnLog);
    }

    /**
     * @param sourceOrder
     * @return
     */
    public OcBReturnOrder generateBil4MultiInStock(OcBReturnOrder sourceOrder) {
        OcBReturnOrder newReturn = new OcBReturnOrder();
        BeanUtils.copyProperties(sourceOrder, newReturn);
        newReturn.setWmsCancelStatus(0);
        newReturn.setIsTowms(0);
        newReturn.setIsWrongReceive(IsWrongReceive.NO.val());
        newReturn.setIsNeedToWms(Long.valueOf(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal()));
        newReturn.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDER));
        newReturn.setBillNo(sequenceUtil.buildReturnBillNo());
        OperateUserUtils.saveOperator(newReturn, ThreadLocalUtil.users.get());
        return newReturn;
    }

    /**
     * @param subItem
     * @return
     */
    public OcBReturnOrderRefund generateReturnItem4MultiInStock(OcBReturnOrderRefund subItem) {
        OcBReturnOrderRefund newReturnItem = new OcBReturnOrderRefund();
        BeanUtils.copyProperties(subItem, newReturnItem);
        newReturnItem.setId(ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCRETURNORDERREFUND));
        OperateUserUtils.saveOperator(newReturnItem, ThreadLocalUtil.users.get());
        return newReturnItem;
    }

    /**
     * 正常匹配退单
     *
     * @param newRefundIn
     * @param newInItems
     * @param newReturn
     * @param newReturnItems
     * @param actualItems
     */
    public void persistReturnNormalMatchResult(OcBRefundIn newRefundIn, List<OcBRefundInProductItem> newInItems,
                                               OcBReturnOrder newReturn, List<OcBReturnOrderRefund> newReturnItems,
                                               List<OcBReturnOrderActual> actualItems, List<OcBRefundInLog> matchInLogs,
                                               OcBReturnOrderLog returnLog) {
        ocBRefundInMapper.updateById(newRefundIn);
        for (OcBRefundInProductItem newInItem : newInItems) {
            ocBRefundInProductItemMapper.updateMatchStatusAndInStatus(newInItem);
        }

        ocBReturnOrderMapper.updateById(newReturn);
        for (OcBReturnOrderRefund newReturnItem : newReturnItems) {
            ocBReturnOrderRefundMapper.updateMatchAndInQtyAndAmt(newReturnItem);
        }

        if (CollectionUtils.isNotEmpty(actualItems)) {
            ocBReturnOrderActualMapper.batchInsert(actualItems);
        }

        ocBRefundInLogMapper.batchInsert(matchInLogs);
        ocBReturnOrderLogMapper.insert(returnLog);
    }

    /**
     * 更新,新增数据
     * 多次入库.退单
     *
     * newRefundIn       退货入库结果单.更新
     * newInItems        退货入库结果单商品明细.更新
     * updateReturn      原退单.更新
     * updateReturnItems 原退单明细.更新
     * actualItems       退单实际入库明细.新增
     * newReturn         退单.新增
     * newReturnItems    退单.新增
     * matchInLogs       入库结果单日志.新增
     * returnLogs        退单.日志
     */
    public void persistReturnMultiMatchResult(OmsStockInMatchParam inParam) {

        ocBRefundInMapper.updateById(inParam.getModRefundIn());
        for (OcBRefundInProductItem newInItem : inParam.getModRefundItems()) {
            ocBRefundInProductItemMapper.updateMatchStatusAndInStatus(newInItem);
        }

        ocBReturnOrderMapper.updateById(inParam.getModReturn());
        for (OcBReturnOrderRefund newReturnItem : inParam.getModReturnItems()) {
            ocBReturnOrderRefundMapper.updateMatchQtyWithOutInAmt(newReturnItem);
        }

        if (CollectionUtils.isNotEmpty(inParam.getInsActualItems())) {
            ocBReturnOrderActualMapper.batchInsert(inParam.getInsActualItems());
        }

        ocBReturnOrderMapper.insert(inParam.getMatchedReturn());
        ocBReturnOrderRefundMapper.batchInsert(inParam.getMatchedReturnItems());

        ocBRefundInLogMapper.batchInsert(inParam.getInsRefundLogs());
        ocBReturnOrderLogMapper.batchInsert(inParam.getInsReturnLogs());
    }

    public void persistNameLessGenInNotice(OcBRefundIn newRefundIn, List<OcBRefundInProductItem> newInItems,
                                           OcBReturnOrder updateReturn,
                                           List<OcBRefundInLog> matchInLogs,
                                           List<OcBReturnOrderLog> returnLogs) {
        ocBRefundInMapper.updateById(newRefundIn);
        for (OcBRefundInProductItem newInItem : newInItems) {
            ocBRefundInProductItemMapper.updateById(newInItem);
        }
        ocBReturnOrderMapper.updateById(updateReturn);
        ocBRefundInLogMapper.batchInsert(matchInLogs);
        ocBReturnOrderLogMapper.batchInsert(returnLogs);
    }

    @Transactional
    public void persistNameLess(OcBRefundIn newRefundIn, List<OcBRefundInProductItem> newInItems,
                                OcBRefundInLog matchInLogs, OcBReturnOrderLog returnLogs) {
        ocBRefundInMapper.updateById(newRefundIn);
        for (OcBRefundInProductItem newInItem : newInItems) {
            ocBRefundInProductItemMapper.updateById(newInItem);
        }
        ocBRefundInLogMapper.insert(matchInLogs);
        ocBReturnOrderLogMapper.insert(returnLogs);
    }

    /**
     * @param id
     * @return
     */
    public OcBOrder getOrder(Long id) {
        return ocBOrderMapper.queryOrder4Match(id);
    }

    /**
     * @param id
     * @return
     */
    public List<OcBOrderItem> getOrderItems(Long id) {
        return ocBorderItemMapper.queryItemsByOrderId(id);
    }

    /**
     * 原单.一阶
     *
     * @param newReturn
     * @param newReturnRefunds
     * @param newOrder
     * @param matchOrderItems
     */
    @Transactional
    public void persistOrderMatchResult(OcBReturnOrder newReturn, List<OcBReturnOrderRefund> newReturnRefunds,
                                        List<OcBReturnOrderActual> actualItems,
                                        OcBOrder newOrder, List<OcBOrderItem> matchOrderItems,
                                        OcBRefundIn newRefundIn, List<OcBRefundInProductItem> matchInItems,
                                        OcBReturnOrderLog returnLog, OcBOrderLog orderLog) {

        ocBReturnOrderMapper.insert(newReturn);
        ocBReturnOrderRefundMapper.batchInsert(newReturnRefunds);
        if (CollectionUtils.isNotEmpty(actualItems)) {
            ocBReturnOrderActualMapper.batchInsert(actualItems);
        }
        ocBOrderMapper.updateById(newOrder);
        for (OcBOrderItem item : matchOrderItems) {
            ocBorderItemMapper.updateOcBOrderItemQty(item);
        }

        ocBRefundInMapper.updateById(newRefundIn);
        for (OcBRefundInProductItem item : matchInItems) {
            ocBRefundInProductItemMapper.updateMatchStatusAndInStatus(item);
        }

        ocBReturnOrderLogMapper.insert(returnLog);
        omsOrderLogService.save(Collections.singletonList(orderLog));
    }


    @Transactional
    public void persistOrderMatch4InNotice(OcBReturnOrder newReturn, List<OcBReturnOrderRefund> newReturnRefunds,
                                           List<OcBReturnOrderActual> actualItems,
                                           OcBOrder newOrder, List<OcBOrderItem> matchOrderItems,
                                           OcBRefundIn newRefundIn, List<OcBRefundInProductItem> matchInItems,
                                           OcBOrderLog orderLog, OcBRefundInLog refundInLog,
                                           List<OcBReturnOrderLog> returnLogs) {
        ocBReturnOrderMapper.insert(newReturn);
        ocBReturnOrderRefundMapper.batchInsert(newReturnRefunds);

        if (CollectionUtils.isNotEmpty(actualItems)) {
            ocBReturnOrderActualMapper.batchInsert(actualItems);
        }

        ocBOrderMapper.updateById(newOrder);
        for (OcBOrderItem item : matchOrderItems) {
            ocBorderItemMapper.updateOcBOrderItemQty(item);
        }
        ocBRefundInMapper.updateById(newRefundIn);
        for (OcBRefundInProductItem item : matchInItems) {
            ocBRefundInProductItemMapper.updateMatchStatusAndInStatus(item);
        }
        omsOrderLogService.save(Collections.singletonList(orderLog));
        ocBRefundInLogMapper.insert(refundInLog);
        ocBReturnOrderLogMapper.batchInsert(returnLogs);
    }

    /**
     * @param updateReturn
     */
    public int updateReturnBil(OcBReturnOrder updateReturn) {
        return ocBReturnOrderMapper.updateById(updateReturn);
    }

    /**
     * @param newRefundIn
     */
    public void updateRefundInBil(OcBRefundIn newRefundIn) {
        ocBRefundInMapper.updateById(newRefundIn);
    }

    /**
     * @param refundInProductItem
     */
    public void updateRefundInProductItem(OcBRefundInProductItem refundInProductItem, Long mainId, String skuCode) {
        LambdaQueryWrapper<OcBRefundInProductItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OcBRefundInProductItem::getOcBRefundInId, mainId);
        wrapper.eq(OcBRefundInProductItem::getPsCSkuEcode, skuCode);
        ocBRefundInProductItemMapper.update(refundInProductItem, wrapper);
    }

    /**
     * @param key
     * @param list
     * @param inLog
     */
    public void updateAdjustBil(Long key, List<Long> list, OcBRefundInLog inLog) {
        ocBRefundInProductItemMapper.updateRefundInProductAdJUST(IsGenAdjustEnum.YES.integer(), key, list);
        ocBRefundInLogMapper.insert(inLog);
    }

    /**
     * @param key
     * @param list
     * @param inLog
     */
    public void updateAdjustAndVirtualStatus(Long key, List<Long> list, OcBRefundInLog inLog) {
        OcBRefundIn newRefund = new OcBRefundIn();
        newRefund.setId(key);
        newRefund.setVirtualInStatus(VirtualInStatusEnum.NOT.integer());
        ocBRefundInMapper.updateById(newRefund);

        ocBRefundInProductItemMapper.updateRefundInProductAdJUST(IsGenAdjustEnum.YES.integer(), key, list);
        ocBRefundInLogMapper.insert(inLog);
    }

    /**
     * 按照富勒业务逻辑更新
     *
     * @param key
     * @param list
     * @param inLog
     */
    public void updateAdjustAndVirtualStatusForFlux(Long key, List<Long> list, OcBRefundInLog inLog) {
        OcBRefundIn newRefund = new OcBRefundIn();
        newRefund.setId(key);
        newRefund.setVirtualInStatus(VirtualInStatusEnum.FINISH.integer());
        ocBRefundInMapper.updateById(newRefund);

        ocBRefundInProductItemMapper.updateRefundInProductAdJUST(IsGenAdjustEnum.YES.integer(), key, list);
        ocBRefundInLogMapper.insert(inLog);
    }

    /**
     * @param key
     * @param list
     * @param inLog
     */
    public void updateMinusAdjust(Long key, List<Long> list, OcBRefundInLog inLog) {
        ocBRefundInProductItemMapper.updateMinusAdjust(IsGenAdjustEnum.YES.integer(), key, list);
        ocBRefundInLogMapper.insert(inLog);
    }

    /**
     * lock by id
     *
     * @param lockRedisKey
     * @return
     */
    public boolean lockBil(String lockRedisKey) {
        RedisReentrantLock lock = new RedisReentrantLock(lockRedisKey);
        try {
            if (lock.tryLock(0, TimeUnit.MILLISECONDS)) {
                ThreadLocalUtil.locks.get().add(lock);
                return true;
            }
            ThreadLocalUtil.logStepMsg.get().add(String.format("%s: 锁单失败", lockRedisKey));
        } catch (Exception e) {
            ThreadLocalUtil.logStepMsg.get().add(String.format("%s: 锁单异常", lockRedisKey));
        }
        return false;
    }

    /**
     * batch lock bil
     *
     * @param reconciliationIds
     * @param fun
     * @return
     */
    public List<Long> lockBillUseId(List<Long> reconciliationIds, Function<Long, String> fun) {

        ThreadLocalUtil.logStepMsg.get().add("锁单开始");

        Iterator<Long> iterator = reconciliationIds.iterator();
        while (iterator.hasNext()) {
            Long next = iterator.next();
            String lockRedisKey = fun.apply(next);
            RedisReentrantLock lock = new RedisReentrantLock(lockRedisKey);
            try {
                if (lock.tryLock(0, TimeUnit.MILLISECONDS)) {
                    ThreadLocalUtil.locks.get().add(lock);
                    continue;
                }
                ThreadLocalUtil.logStepMsg.get().add(String.format("%d: 锁单失败", next));
            } catch (Exception e) {
                ThreadLocalUtil.logStepMsg.get().add(String.format("%d: 锁单异常", next));
            }
            iterator.remove();
        }
        ThreadLocalUtil.logStepMsg.get().add("锁单结束");
        return reconciliationIds;
    }

    /**
     * 解锁
     *
     * @return
     */
    public static int unlockBil() {
        List<RedisReentrantLock> locks = ThreadLocalUtil.locks.get();
        if (CollectionUtils.isEmpty(locks)) {
            return 0;
        }
        int num = locks.size();
        for (RedisReentrantLock lock : locks) {
            try {
                lock.unlock();
            } catch (Exception ex) {
                num--;
                ThreadLocalUtil.logStepMsg.get().add(String.format("%d: 单据解锁异常", lock.getLockId()));
            }
        }
        return num;
    }

    /**
     * if has placeholder must exist value
     * placeholder count can be none, or least than values
     *
     * @param express
     * @param obs
     */
    public static void logStep(String express, Object... obs) {
        ThreadLocalUtil.logStepMsg.get().add(String.format(express, obs));
    }

    /**
     * print log
     *
     * @param keys
     */
    public static void logs(Level logLevel, Object... keys) {
        if (Level.INFO == logLevel) {
            if (log.isInfoEnabled()) {
                log.info(LogUtil.format("{}", keys), JSON.toJSONString(ThreadLocalUtil.logStepMsg.get()));
                return;
            }
        }
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("{}", keys), JSON.toJSONString(ThreadLocalUtil.logStepMsg.get()));
        }
    }

    /**
     * optimize message
     */
    public static Function<Exception, String> expMsgFun = e -> {
        if (e == null) {
            return "null exception";
        }
        String message = e.getMessage();
        if (message == null) {
            return "null message";
        }
        return message.length() > 200 ? message.substring(0, 200) : message;
    };

    /**
     * clear
     */
    public static void clearResources(Level logLevel, Object... keys) {
        logs(logLevel, keys);
        unlockBil();
        ThreadLocalUtil.users.remove();
        ThreadLocalUtil.locks.remove();
        matchedReturn.remove();
        ThreadLocalUtil.logStepMsg.remove();

    }

    public List<String> getBusinessSystemForOcReturnInExclude() {
        String value = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(STAND_PLATFORM_OCRETURNIN_EXCLUDE);
        if (StringUtils.isBlank(value)) {
            log.warn("getBusinessSystemForOcReturnInExclude业务参数查出为空");
            return Lists.newArrayList();
        }

        return Arrays.asList(value.split("/"));
    }

    /**
     * 新增无名件入库结果单应退明细
     *
     * @param refundInShouldRefundItemList
     * @return
     */
    public boolean insertOcBRefundInShouldRefundItem(Collection<OcBRefundInShouldRefundItem> refundInShouldRefundItemList) {
        for (OcBRefundInShouldRefundItem item : refundInShouldRefundItemList) {
            refundInShouldRefundItemMapper.insert(item);
        }
        return true;
    }

    public boolean existRefundInShouldRefundItem(Long ocBRefundInId) {
        List<OcBRefundInShouldRefundItem> items = refundInShouldRefundItemMapper.selectList(Wrappers.lambdaQuery(new OcBRefundInShouldRefundItem())
                .select(OcBRefundInShouldRefundItem::getId)
                .eq(OcBRefundInShouldRefundItem::getOcBRefundInId, ocBRefundInId)
                .last("limit 1"));
        return CollectionUtils.isNotEmpty(items);
    }
}
