package com.jackrain.nea.oc.oms.services.logistics;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.reflect.Proxy;
import java.util.Map;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/6/16
 * description :
 */
@Slf4j
@Component
public class LoadLogisticsInfoQueryApiHandle implements ApplicationContextAware {

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        log.info("Start load logistics info query api handle...... ");
        Map<String, LogisticsInfoQueryApi> beans = applicationContext.getBeansOfType(LogisticsInfoQueryApi.class);
        beans.values().stream().filter(o -> !Proxy.isProxyClass(o.getClass()))
                .forEach(handle -> LogisticsInfoQueryFactory.HANDLE_MAP.put(handle.table(), handle));
        log.info("Finish load logistics info query api handle...... :{}", beans.size());

    }

}