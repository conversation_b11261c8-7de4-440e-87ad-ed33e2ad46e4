package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.OcOrderWmsCancelNumber;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;

import java.util.ArrayList;
import java.util.List;

/**
 * 唯品会退供单
 *
 * <AUTHOR>
 * @date 2020/11/10 3:25 下午
 */
public class ES4IpVipReturnOrder {

    private ES4IpVipReturnOrder() {
    }

    /**
     * 业务：唯品会退供单转换补偿任务
     * 根据 trans_status 查询return_sn（订单编号）按创建时间倒序排序
     * @param pageIndex 页码
     * @param pageSize
     *
     * trans_status 转换状态
     *
     * @return List 订单编号
     */
    public static List<String> getReturnSnByTransStatus(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        JSONObject whereKeys = new JSONObject();
        JSONArray jo = new JSONArray();
        //转换失败
        jo.add(TransferOrderStatus.TRANSFERFAIL.toInteger());
        //未转换
        jo.add(TransferOrderStatus.NOT_TRANSFER.toInteger());
        whereKeys.put("TRANS_STATUS", jo);
        JSONObject filterKeys = new JSONObject();
        filterKeys.put("TRANS_NUMS", "~" + 5);

        String[] returnFieldNames = new String[]{"RETURN_SN"};
        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        // 按照倒序进行查询
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_VIP_RETURN_ORDER_INDEX_NAME,
                OcElasticSearchIndexResources.IP_B_VIP_RETURN_ORDER_TYPE_NAME,
                whereKeys, filterKeys, orderKeys,
                pageSize, startIndex, returnFieldNames);

        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");

            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                String orderNo = jsonObject.getString("RETURN_SN");
                orderNoList.add(orderNo);
            }
        }
        return orderNoList;
    }

    /**
     * 根据id查询 returnSn
     *
     * @param tableName
     * @param id
     * @return
     */
    public static JSONArray findReturnSnById(String tableName, Long id){
        String uniqueKey = "RETURN_SN";
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ID", id);
        String[] returnField = {uniqueKey};
        JSONObject search = ElasticSearchUtil.search(tableName, tableName, whereKeys,
                null, null, 10, 0, returnField);
        return search.getJSONArray("data");
    }

}
