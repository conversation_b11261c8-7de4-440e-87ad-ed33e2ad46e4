package com.jackrain.nea.oc.oms.sap;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.constant.SapSalesDateConstant;
import com.jackrain.nea.oc.oms.mapper.MilkCardAmountOffsetItemMapper;
import com.jackrain.nea.oc.oms.mapper.MilkCardAmountOffsetOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBSapSalesDataGatherMapper;
import com.jackrain.nea.oc.oms.model.MilkCardAmountOffsetItemResult;
import com.jackrain.nea.oc.oms.model.table.MilkCardAmountOffsetOrder;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGather;
import com.jackrain.nea.oc.oms.model.table.OcBSapSalesDataGatherItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.OmsStorageUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description: 汇总奶卡金额冲抵单据到销售数据汇总表
 * todo 汇总单号ID  默认给0， 汇总如果为空默认给空字符串
 *
 * @Author: guo.kw
 * @Since: 2022/9/9
 * create at: 2022/9/9 10:56
 */
@Slf4j
@Component
public class OcBSapSalesDataRecordSumNkService extends ServiceImpl<MilkCardAmountOffsetOrderMapper, MilkCardAmountOffsetOrder> {

    @Autowired
    private MilkCardAmountOffsetOrderMapper milkCardAmountOffsetOrderMapper;

    @Autowired
    private MilkCardAmountOffsetItemMapper milkCardAmountOffsetItemMapper;

    @Autowired
    private OcBSapSalesDataGatherMapper ocBSapSalesDataGatherMapper;

    @Autowired
    private OcBSapSalesDataGatherItemMapper ocBSapSalesDataGatherItemMapper;

    @Autowired
    private RedisOpsUtil<String, String> redisOpsUtil;
    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");

    private static final List<String> sumTypes = Lists.newArrayList("RYCD01", "RYCD02", "RYCD03", "RYCD04");

    @NacosValue(value = "${lts.OcBSapSalesDataRecordSumNkService.range:100}", autoRefreshed = true)
    public Integer range;

    //private static final List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrdersObject = new ArrayList<>();

    private static final String FORMAT_NUM = "00000";//位数
    private static final DecimalFormat formater = new DecimalFormat(FORMAT_NUM);
    private static final User rootUser = SystemUserResource.getRootUser();

    /**
     * 基本线程池常量定义
     */
    int corePoolSize = 16;
    int maxPoolSize = 20;
    long keepAliveThreadTime = 60000;

    //使用线程
    /*public void executeThread() {
        String threadPoolName = "R3_OMS_MILK_CARD_AMOUNT_OFFSET_ORDER_%d";
        ExecutorService executor = OMSThreadPoolFactory.getMilkCardAmountOffsetOrder();
        log.info(LogUtil.format("OcBSapSalesDataRecordSumNkService---->奶卡冲抵单汇总条数:{}", "OcBSapSalesDataRecordSumNkService"), range);
        try {
            long start = System.currentTimeMillis();
            final String taskTableName = "milk_card_amount_offset_order";
            Set<String> nodes = topMap.keySet();
            if (CollectionUtils.isEmpty(nodes)) {
                log.info(LogUtil.format("请检查环境,node获取不到"));
                return;
            }
            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();

            for (String node : nodes) {
                results.add(executor.submit(new CallableTobeConfirmedTaskWithResult(node, topMap.get(node))));
            }

            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    log.debug(LogUtil.format("OcBSapSalesDataRecordSumNkService------>线程结果:{}", "OcBSapSalesDataRecordSumNkService"), futureResult.get().toString());
                } catch (Exception e) {
                    log.error(LogUtil.format("OcBSapSalesDataRecordSumNkService多线程获取ExecutionException异常：{}", threadPoolName, "OcBSapSalesDataRecordSumNkService"), Throwables.getStackTraceAsString(e));
                }
            }
            System.out.println("条数据：" + milkCardAmountOffsetOrdersObject.size());
            // 当前所有节点执行完之后，如果有数据则继续执行下一次线程，如果所有节点执行完数据为空则更新所有汇总中的状态为已汇总
            if (CollectionUtils.isNotEmpty(milkCardAmountOffsetOrdersObject)) {
                List<List<MilkCardAmountOffsetOrder>> partition = Lists.partition(milkCardAmountOffsetOrdersObject, SapSalesDateConstant.QUERY_MAX_SIZE);
                //分批次执行每次执行一千
                for (List<MilkCardAmountOffsetOrder> infoList : partition) {
                    this.execute(infoList);
                }
                milkCardAmountOffsetOrdersObject.clear();
                System.out.println("清空之后数据：" + milkCardAmountOffsetOrdersObject.size());
                if (dddd.equals("123")) {
                    dddd = "321";
                    this.executeThread();
                }
            } else {
                this.updateSaleSumStatus();
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OcBSapSalesDataRecordSumNkService 奶卡冲抵单汇总任务完成 useTime:{}", "OcBSapSalesDataRecordSumNkService", (System.currentTimeMillis() - start)));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OcBSapSalesDataRecordSumNkService.Execute Error：{}", threadPoolName, "OcBSapSalesDataRecordSumNkService"), Throwables.getStackTraceAsString(ex));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } finally {
            //executor.shutdown();
        }
    }*/

    /**
     * 开启线程类
     */
    /*class CallableTobeConfirmedTaskWithResult implements Callable<Boolean> {
        private String nodeName;

        private String taskTableName;

        public CallableTobeConfirmedTaskWithResult(String nodeName, String taskTableName) {
            this.nodeName = nodeName;
            this.taskTableName = taskTableName;
        }

        @Override
        public Boolean call() throws Exception {
            List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrders = new ArrayList<>();
            long start = System.currentTimeMillis();
            if (StringUtils.isNotEmpty(nodeName)) {
                milkCardAmountOffsetOrders = milkCardAmountOffsetOrderMapper.selectSalesDataIdList(nodeName, taskTableName, range, simpleDateFormat.format(new Date()));
            }
            long time1 = System.currentTimeMillis();
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OcBSapSalesDataRecordSumNkService selectWaitDistributeOrder useTime :{},ordersize:{}", "OcBSapSalesDataRecordSumNkService"), (time1 - start), milkCardAmountOffsetOrders.size() + "数量：" + nodeName);
            }
            milkCardAmountOffsetOrdersObject.addAll(milkCardAmountOffsetOrders);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OcBSapSalesDataRecordSumNkService 奶卡汇总单个线程完成 useTime:{}", "OcBSapSalesDataRecordSumNkService"), (System.currentTimeMillis() - start));
            }
            return true;
        }
    }*/

    /**
     * 执行奶卡冲抵汇总
     *
     * @return
     */
    /*@Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 execute(List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrders) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "销售数据汇总定时任务成功！");
        log.info(LogUtil.format("OcBSapSalesDataRecordSumNkService.execute start", "OcBSapSalesDataRecordSumService.execute "));
        String queryDate = simpleDateFormat.format(new Date());
        try {
            Boolean flag = true;
            while (flag) {
                //查询奶卡冲抵单 汇总状态为0，可用状态为Y，汇总码不为空，汇总单号ID为0，且创建时间为小于当前时间的所有单据
                //List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrders = milkCardAmountOffsetOrderMapper.querySalesDataRecord(SapSalesDateConstant.QUERY_MAX_SIZE, queryDate);
                if (CollectionUtils.isEmpty(milkCardAmountOffsetOrders)) {
                    //updateSaleSumStatus();
                    break;
                }
                //分页查询明细信息
                List<Long> mainIds = milkCardAmountOffsetOrders.stream()
                        .map(MilkCardAmountOffsetOrder::getId)
                        .collect(Collectors.toList());
                List<List<Long>> partitionIds = Lists.partition(mainIds, SapSalesDateConstant.QUERY_SIZE_FIVE);
                List<MilkCardAmountOffsetItemResult> milkCardAmountOffsetItems = new ArrayList<>();
                for (List<Long> ids : partitionIds) {
                    milkCardAmountOffsetItems.addAll(milkCardAmountOffsetItemMapper.batchSelectItems(ids));
                }
                //根据ID进行分组
                Map<Long, MilkCardAmountOffsetOrder> longMilkCardAmountOffsetOrderMap = milkCardAmountOffsetOrders.stream()
                        .collect(Collectors.toMap(MilkCardAmountOffsetOrder::getId, Function.identity(), (k1, k2) -> k1));
                // 将对应的合并码和汇总类型赋值
                milkCardAmountOffsetItems.parallelStream().forEach(e -> {
                    MilkCardAmountOffsetOrder milkCardAmountOffsetOrder = longMilkCardAmountOffsetOrderMap.get(e.getOffsetOrderId());
                    e.setCollectCode(milkCardAmountOffsetOrder.getCollectCode());
                    e.setSumType(milkCardAmountOffsetOrder.getSumType());
                });
                // 分组类型 汇总码加上汇总类型
                Map<String, List<MilkCardAmountOffsetItemResult>> milkCardAmountOffsetItemResultMap = milkCardAmountOffsetItems.stream()
                        .collect(Collectors.groupingBy(e -> e.getCollectCode() + e.getSumType()));

                User rootUser = SystemUserResource.getRootUser();

                buildParam(milkCardAmountOffsetOrders, milkCardAmountOffsetItemResultMap, rootUser);

                //更新状态为已汇总
                MilkCardAmountOffsetOrder milkCardAmountOffsetOrder = new MilkCardAmountOffsetOrder();
                milkCardAmountOffsetOrder.setCollectStatus(Integer.parseInt(SapSalesDateConstant.SUM_STATUS_SUMMARIZED));
                OmsStorageUtils.setBModelDefalutDataByUpdate(milkCardAmountOffsetOrder, rootUser);
                milkCardAmountOffsetOrderMapper.update(milkCardAmountOffsetOrder, new LambdaQueryWrapper<MilkCardAmountOffsetOrder>()
                        .in(MilkCardAmountOffsetOrder::getId, mainIds));
                flag = true;
                if (milkCardAmountOffsetOrders.size() < SapSalesDateConstant.QUERY_MAX_SIZE) {
                    updateSaleSumStatus();
                    flag = false;
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("OcBSapSalesDataRecordSumNkService.error message:{}",
                    "OcBSapSalesDataRecordSumNkService.error"), Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        log.info(LogUtil.format("OcBSapSalesDataRecordSumNkService.ValueHolderV14 end ValueHolderV14:{}",
                "OcBSapSalesDataRecordSumNkService.ValueHolderV14 "), JSONObject.toJSONString(v14));
        return v14;
    }*/
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 execute(List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrders) {
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "奶卡冲抵单定时任务成功！");
        log.info(LogUtil.format("OcBSapSalesDataRecordSumNkService.execute start", "OcBSapSalesDataRecordSumService.execute "));

        if (CollectionUtils.isEmpty(milkCardAmountOffsetOrders)) {
            //updateSaleSumStatus();
            return v14;
        }
        //分页查询明细信息
        List<Long> mainIds = milkCardAmountOffsetOrders.stream()
                .map(MilkCardAmountOffsetOrder::getId)
                .collect(Collectors.toList());
        List<List<Long>> partitionIds = Lists.partition(mainIds, SapSalesDateConstant.QUERY_SIZE_FIVE);
        List<MilkCardAmountOffsetItemResult> milkCardAmountOffsetItems = new ArrayList<>();
        for (List<Long> ids : partitionIds) {
            milkCardAmountOffsetItems.addAll(milkCardAmountOffsetItemMapper.batchSelectItems(ids));
        }
        //根据ID进行分组
        Map<Long, MilkCardAmountOffsetOrder> longMilkCardAmountOffsetOrderMap = milkCardAmountOffsetOrders.stream()
                .collect(Collectors.toMap(MilkCardAmountOffsetOrder::getId, Function.identity(), (k1, k2) -> k1));
        // 将对应的合并码和汇总类型赋值
        milkCardAmountOffsetItems.parallelStream().forEach(e -> {
            MilkCardAmountOffsetOrder milkCardAmountOffsetOrder = longMilkCardAmountOffsetOrderMap.get(e.getOffsetOrderId());
            e.setCollectCode(milkCardAmountOffsetOrder.getCollectCode());
            e.setSumType(milkCardAmountOffsetOrder.getSumType());
        });
        // 分组类型 汇总码加上汇总类型
        Map<String, List<MilkCardAmountOffsetItemResult>> milkCardAmountOffsetItemResultMap = milkCardAmountOffsetItems.stream()
                .collect(Collectors.groupingBy(e -> e.getCollectCode() + e.getSumType()));


        buildParam(milkCardAmountOffsetOrders, milkCardAmountOffsetItemResultMap, rootUser);

        //更新状态为已汇总
        MilkCardAmountOffsetOrder milkCardAmountOffsetOrder = new MilkCardAmountOffsetOrder();
        milkCardAmountOffsetOrder.setCollectStatus(Integer.parseInt(SapSalesDateConstant.SUM_STATUS_SUMMARIZED));
        OmsStorageUtils.setBModelDefalutDataByUpdate(milkCardAmountOffsetOrder, rootUser);
        milkCardAmountOffsetOrderMapper.update(milkCardAmountOffsetOrder, new LambdaQueryWrapper<MilkCardAmountOffsetOrder>()
                .in(MilkCardAmountOffsetOrder::getId, mainIds));
        log.info(LogUtil.format("OcBSapSalesDataRecordSumNkService.ValueHolderV14 end ValueHolderV14:{}",
                "OcBSapSalesDataRecordSumNkService.ValueHolderV14 "), JSONObject.toJSONString(v14));
        return v14;
    }

    /**
     * 数据封装
     *
     * @param milkCardAmountOffsetOrders        奶卡冲抵单数据
     * @param milkCardAmountOffsetItemResultMap 明细单据
     * @param rootUser                          用户信息
     */
    public void buildParam(List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrders,
                           Map<String, List<MilkCardAmountOffsetItemResult>> milkCardAmountOffsetItemResultMap,
                           User rootUser) {
        // 分组方式 汇总码
        List<String> stringList = milkCardAmountOffsetOrders.stream().filter(e -> StringUtils.isNotBlank(e.getCollectCode()))
                .distinct().map(MilkCardAmountOffsetOrder::getCollectCode)
                .collect(Collectors.toList());
        // 分组方式 汇总码加上汇总类型
        Map<String, List<MilkCardAmountOffsetOrder>> listMap = milkCardAmountOffsetOrders.stream()
                .filter(e -> StringUtils.isNotBlank(e.getCollectCode()))
                .collect(Collectors.groupingBy(e -> e.getCollectCode() + e.getSumType()));
        if (!org.springframework.util.CollectionUtils.isEmpty(listMap)) {
            //查询汇总表已存在的数据 过滤掉已经推送成功SAP的单据 根据汇总码做查询汇总表信息
            //获取汇总码和汇总类型的键值
            Set<String> strings = listMap.keySet();
            /*List<OcBSapSalesDataGather> ocBSapSalesDataGathers = ocBSapSalesDataGatherMapper.selectList(new LambdaQueryWrapper<OcBSapSalesDataGather>()
                    .eq(OcBSapSalesDataGather::getIsactive, SapSalesDateConstant.ISACTIVE_YES)
                    .eq(OcBSapSalesDataGather::getGatherMiddleStatus, SapSalesDateConstant.GATHER_MIDDLE_STATUS_01)
                    .in(OcBSapSalesDataGather::getSumType, sumTypes)
                    .in(OcBSapSalesDataGather::getMergeCode, stringList));*/
            List<OcBSapSalesDataGather> ocBSapSalesDataGathers = ocBSapSalesDataGatherMapper.selectInfoByMergeCodes(stringList);
            Map<String, OcBSapSalesDataGather> ocBSapSalesDataGather = new HashMap<>();
            Map<String, OcBSapSalesDataGatherItem> stringOcBSapSalesDataGatherItemMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(ocBSapSalesDataGathers)) {
                //根据汇总码加上汇总类型 进行分组
                ocBSapSalesDataGather = ocBSapSalesDataGathers.stream()
                        .collect(Collectors.toMap(e -> e.getMergeCode() + e.getSumType(), Function.identity(), (k1, k2) -> k1));
                //查询汇总表明细 并根据主表ID+SKU进行分组
                List<Long> longList = ocBSapSalesDataGathers.stream().map(OcBSapSalesDataGather::getId).collect(Collectors.toList());
                List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherItems = ocBSapSalesDataGatherItemMapper.selectList(new LambdaQueryWrapper<OcBSapSalesDataGatherItem>()
                        .eq(OcBSapSalesDataGatherItem::getIsactive, SapSalesDateConstant.ISACTIVE_YES)
                        .in(OcBSapSalesDataGatherItem::getOcBSapSalesDataGatherId, longList));
                stringOcBSapSalesDataGatherItemMap = ocBSapSalesDataGatherItems.stream()
                        .collect(Collectors.toMap(e -> e.getOcBSapSalesDataGatherId() + e.getSku(), Function.identity(), (k1, k2) -> k1));
            }
            //批量更新回写汇总单号到奶卡冲抵单list集合
            List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrderList = new ArrayList<>();
            for (String key : strings) {

                /*String sumSuccess = ocBSapSalesDataRecordSumNkThreadService.buildSumParam(key, listMap,
                        milkCardAmountOffsetItemResultMap,
                        ocBSapSalesDataGather,
                        rootUser,
                        stringOcBSapSalesDataGatherItemMap, milkCardAmountOffsetOrderList);

                if (SapSalesDateConstant.SUM_ERROR.equals(sumSuccess)) {
                    List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrdersList = listMap.get(key);
                    List<Long> idList = milkCardAmountOffsetOrdersList.stream().map(MilkCardAmountOffsetOrder::getId).distinct().collect(Collectors.toList());
                    MilkCardAmountOffsetOrder milkCardAmountOffsetOrder = new MilkCardAmountOffsetOrder();
                    milkCardAmountOffsetOrder.setCollectStatus(Integer.parseInt(SapSalesDateConstant.SUM_STATUS_FAIL));
                    milkCardAmountOffsetOrderMapper.update(milkCardAmountOffsetOrder, new LambdaQueryWrapper<MilkCardAmountOffsetOrder>()
                            .in(MilkCardAmountOffsetOrder::getId, idList));

                }*/
                List<MilkCardAmountOffsetOrder> milkCardAmountOffsetOrdersList = listMap.get(key);
                //任意取其中一条
                MilkCardAmountOffsetOrder milkCardAmountOffsetOrder = milkCardAmountOffsetOrdersList.get(0);
                Long objId = ModelUtil.getSequence(SapSalesDateConstant.OC_B_SAP_SALES_DATA_GATHER);

                //获取明细
                List<MilkCardAmountOffsetItemResult> milkCardAmountOffsetItemResults = milkCardAmountOffsetItemResultMap.get(key);
                //汇总已商品SKU进行汇总
                Map<String, List<MilkCardAmountOffsetItemResult>> listMap1 = milkCardAmountOffsetItemResults.stream()
                        .collect(Collectors.groupingBy(MilkCardAmountOffsetItemResult::getPsCSkuEcode));

                OcBSapSalesDataGather ocBSapSalesDataGather1 = ocBSapSalesDataGather.get(key);

                if (Objects.isNull(ocBSapSalesDataGather1)) {
                    //新增
                    insertGatherInfo(milkCardAmountOffsetOrder, listMap1, rootUser, objId);
                } else {
                    //修改
                    updateGatherInfo(milkCardAmountOffsetOrder, listMap1, rootUser, ocBSapSalesDataGather1.getId(), stringOcBSapSalesDataGatherItemMap);
                }

                for (MilkCardAmountOffsetOrder object : milkCardAmountOffsetOrdersList) {
                    MilkCardAmountOffsetOrder milkCardAmountOffsetOrder1 = new MilkCardAmountOffsetOrder();
                    milkCardAmountOffsetOrder1.setOcBSapSalesDataGatherId(objId);
                    milkCardAmountOffsetOrder1.setCollectStatus(Integer.parseInt(SapSalesDateConstant.SUM_STATUS_SUMMARIZED));
                    milkCardAmountOffsetOrder1.setId(object.getId());
                    OmsStorageUtils.setBModelDefalutDataByUpdate(milkCardAmountOffsetOrder1, rootUser);
                    milkCardAmountOffsetOrderList.add(milkCardAmountOffsetOrder1);
                }
            }
            if (CollectionUtils.isNotEmpty(milkCardAmountOffsetOrderList)) {
                OcBSapSalesDataRecordSumNkService bean = ApplicationContextHandle.getBean(OcBSapSalesDataRecordSumNkService.class);
                bean.updateBatchById(milkCardAmountOffsetOrderList);
            }
        }
    }

    /**
     * 新增汇总表
     *
     * @param milkCardAmountOffsetOrder
     * @param listMap
     * @param rootUser
     * @param objId
     */
    public void insertGatherInfo(MilkCardAmountOffsetOrder milkCardAmountOffsetOrder, Map<String, List<MilkCardAmountOffsetItemResult>> listMap, User rootUser, Long objId) {
        OcBSapSalesDataGather ocBSapSalesDataGather = new OcBSapSalesDataGather();
        List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherItems = new ArrayList<>();
        ocBSapSalesDataGather.setId(objId);
        //获取单据编号 前缀N + 日期 + 店铺编码 + 业务编码 + 五位序列号
        String cpCShopEcode = StringUtils.isBlank(milkCardAmountOffsetOrder.getCpCShopEcode()) ? "" : milkCardAmountOffsetOrder.getCpCShopEcode();
        String middlegroundBillTypeCode = StringUtils.isBlank(milkCardAmountOffsetOrder.getMiddlegroundBillTypeCode()) ? "" : milkCardAmountOffsetOrder.getMiddlegroundBillTypeCode();
        String billNo = SapSalesDateConstant.NKCD_PREFIX + simpleDateFormat.format(new Date())
                + cpCShopEcode
                + middlegroundBillTypeCode + getSerialNumber();
        //String billNo = "TEST202209130001";
        ocBSapSalesDataGather.setGatherNo(billNo);
        //合并码
        ocBSapSalesDataGather.setMergeCode(milkCardAmountOffsetOrder.getCollectCode());
        //出入库时间
        ocBSapSalesDataGather.setInTime(milkCardAmountOffsetOrder.getInTime());
        //sap单据类型
        ocBSapSalesDataGather.setSapBillType(milkCardAmountOffsetOrder.getSapOrderType() != null ? String.valueOf(milkCardAmountOffsetOrder.getSapOrderType()) : "");
        //销售组织 todo  未有字段
        ocBSapSalesDataGather.setSalesOrganization(milkCardAmountOffsetOrder.getSalesOrganization());
        //店铺
        ocBSapSalesDataGather.setCpCShopId(milkCardAmountOffsetOrder.getCpCShopId());
        //成本中心 TODO 未有字段
        ocBSapSalesDataGather.setCostCenter(null);
        //汇总时间
        ocBSapSalesDataGather.setGatherDate(new Date());
        //汇总类型
        ocBSapSalesDataGather.setSumType(milkCardAmountOffsetOrder.getSumType());
        //传sap状态
        ocBSapSalesDataGather.setToSapStatus(SapSalesDateConstant.TO_SAP_STATUS_INIT);
        //传sap次数
        ocBSapSalesDataGather.setToSapFailNum(SapSalesDateConstant.TO_SAP_FAIL_NUMBER);
        //汇总中间状态
        ocBSapSalesDataGather.setGatherMiddleStatus(SapSalesDateConstant.GATHER_MIDDLE_STATUS_01);
        OmsStorageUtils.setBModelDefalutData(ocBSapSalesDataGather, rootUser);
        for (String next : listMap.keySet()) {
            Long itemId = ModelUtil.getSequence(SapSalesDateConstant.OC_B_SAP_SALES_DATA_GATHER_ITEM);
            OcBSapSalesDataGatherItem ocBSapSalesDataGatherItem = new OcBSapSalesDataGatherItem();
            List<MilkCardAmountOffsetItemResult> milkCardAmountOffsetItemResults = listMap.get(next);
            MilkCardAmountOffsetItemResult milkCardAmountOffsetItemResult = milkCardAmountOffsetItemResults.get(0);

            ocBSapSalesDataGatherItem.setId(itemId);
            ocBSapSalesDataGatherItem.setOcBSapSalesDataGatherId(objId);
            //sku
            ocBSapSalesDataGatherItem.setSku(milkCardAmountOffsetItemResult.getPsCSkuEcode());
            //行项目类型
            ocBSapSalesDataGatherItem.setLineType(milkCardAmountOffsetItemResult.getRowItemType() != null ? String.valueOf(milkCardAmountOffsetItemResult.getRowItemType()) : "");
            int sumQty = milkCardAmountOffsetItemResults.stream().mapToInt(MilkCardAmountOffsetItemResult::getQty).sum();
            //汇总数量
            ocBSapSalesDataGatherItem.setQty((long) sumQty);
            //工厂编码
            ocBSapSalesDataGatherItem.setFactoryCode(milkCardAmountOffsetItemResult.getFactoryCode());
            //仓
            ocBSapSalesDataGatherItem.setCpCStoreId(null);
            //商品类型
            ocBSapSalesDataGatherItem.setProType(milkCardAmountOffsetItemResult.getItemType() != null ? String.valueOf(milkCardAmountOffsetItemResult.getItemType()) : "");
            //单位
            ocBSapSalesDataGatherItem.setUnit(milkCardAmountOffsetItemResult.getUnit());
            //汇总金额
            BigDecimal reduceAmt = milkCardAmountOffsetItemResults.stream().map(MilkCardAmountOffsetItemResult::getOffsetPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            ocBSapSalesDataGatherItem.setAmt(reduceAmt);
            OmsStorageUtils.setBModelDefalutData(ocBSapSalesDataGatherItem, rootUser);

            ocBSapSalesDataGatherItems.add(ocBSapSalesDataGatherItem);
        }
        saveObject(ocBSapSalesDataGather, ocBSapSalesDataGatherItems);
    }

    /**
     * 修改明细
     *
     * @param milkCardAmountOffsetOrder
     * @param listMap
     * @param rootUser
     * @param objId
     */
    public void updateGatherInfo(MilkCardAmountOffsetOrder milkCardAmountOffsetOrder,
                                 Map<String, List<MilkCardAmountOffsetItemResult>> listMap,
                                 User rootUser,
                                 Long objId,
                                 Map<String, OcBSapSalesDataGatherItem> stringOcBSapSalesDataGatherItemMap) {
        List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherItems = new ArrayList<>();
        for (String next : listMap.keySet()) {
            Long itemId = ModelUtil.getSequence(SapSalesDateConstant.OC_B_SAP_SALES_DATA_GATHER_ITEM);
            List<MilkCardAmountOffsetItemResult> milkCardAmountOffsetItemResults = listMap.get(next);
            MilkCardAmountOffsetItemResult milkCardAmountOffsetItemResult = milkCardAmountOffsetItemResults.get(0);
            //数量
            int sumQty = milkCardAmountOffsetItemResults.stream().mapToInt(MilkCardAmountOffsetItemResult::getQty).sum();
            //汇总金额
            BigDecimal reduceAmt = milkCardAmountOffsetItemResults.stream()
                    .map(MilkCardAmountOffsetItemResult::getOffsetPrice)
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

            //根据主表ID和SKU获取明细
            OcBSapSalesDataGatherItem ocBSapSalesDataGatherItem = stringOcBSapSalesDataGatherItemMap.get(objId + milkCardAmountOffsetItemResult.getPsCSkuEcode());

            OcBSapSalesDataGatherItem item = new OcBSapSalesDataGatherItem();
            // 如果存在则进行更新，并跳出本次循环执行下一次存还
            if (!Objects.isNull(ocBSapSalesDataGatherItem)) {
                // 数量和汇总金额进行相加
                Long qty = ocBSapSalesDataGatherItem.getQty();
                sumQty = new BigInteger(sumQty + "").add(new BigInteger(qty + "")).intValue();
                //汇总数量
                item.setQty((long) sumQty);

                BigDecimal amt = ocBSapSalesDataGatherItem.getAmt();
                reduceAmt = reduceAmt.add(amt != null ? amt : BigDecimal.ZERO);
                item.setAmt(reduceAmt);

                OmsStorageUtils.setBModelDefalutDataByUpdate(item, rootUser);
                ocBSapSalesDataGatherItemMapper.update(item, new LambdaQueryWrapper<OcBSapSalesDataGatherItem>()
                        .eq(OcBSapSalesDataGatherItem::getSku, milkCardAmountOffsetItemResult.getPsCSkuEcode())
                        .eq(OcBSapSalesDataGatherItem::getOcBSapSalesDataGatherId, objId));
                continue;
            }
            item.setId(itemId);
            item.setOcBSapSalesDataGatherId(objId);
            //sku
            item.setSku(milkCardAmountOffsetItemResult.getPsCSkuEcode());
            //行项目类型
            item.setLineType(milkCardAmountOffsetItemResult.getRowItemType() != null ? String.valueOf(milkCardAmountOffsetItemResult.getRowItemType()) : "");
            //汇总数量
            item.setQty((long) sumQty);
            //工厂编码
            item.setFactoryCode(null);
            //仓
            item.setCpCStoreId(null);
            //商品类型
            item.setProType(milkCardAmountOffsetItemResult.getItemType() != null ? String.valueOf(milkCardAmountOffsetItemResult.getItemType()) : "");
            //单位
            item.setUnit(milkCardAmountOffsetItemResult.getUnit());

            item.setAmt(reduceAmt);
            ocBSapSalesDataGatherItems.add(item);
            OmsStorageUtils.setBModelDefalutData(item, rootUser);
        }
        if (CollectionUtils.isNotEmpty(ocBSapSalesDataGatherItems)) {
            saveObject(null, ocBSapSalesDataGatherItems);
        }
    }

    /**
     * 汇总表新增
     *
     * @param ocBSapSalesDataGather
     * @param ocBSapSalesDataGatherItems
     */
    public void saveObject(OcBSapSalesDataGather ocBSapSalesDataGather,
                           List<OcBSapSalesDataGatherItem> ocBSapSalesDataGatherItems) {
        if (Objects.nonNull(ocBSapSalesDataGather)) {
            ocBSapSalesDataGatherMapper.insert(ocBSapSalesDataGather);
        }
        ocBSapSalesDataGatherItemMapper.batchInsert(ocBSapSalesDataGatherItems);
    }


    /**
     * 更新汇总状态
     */
    public void updateSaleSumStatus(List<Long> ids) {
        //最后更新所以汇总中为汇总完成
        OcBSapSalesDataGather ocBSapSalesDataGather = new OcBSapSalesDataGather();
        ocBSapSalesDataGather.setGatherMiddleStatus(SapSalesDateConstant.GATHER_MIDDLE_STATUS_02);
        OmsStorageUtils.setBModelDefalutDataByUpdate(ocBSapSalesDataGather, rootUser);
        ocBSapSalesDataGatherMapper.update(ocBSapSalesDataGather, new LambdaQueryWrapper<OcBSapSalesDataGather>()
                .eq(OcBSapSalesDataGather::getGatherMiddleStatus, SapSalesDateConstant.GATHER_MIDDLE_STATUS_01)
                .in(OcBSapSalesDataGather::getId, ids)
                //.in(OcBSapSalesDataGather::getSumType, sumTypes)
                .eq(OcBSapSalesDataGather::getIsactive, SapSalesDateConstant.ISACTIVE_YES));
    }

    /**
     * 根据时间戳获取ba位流水号
     *
     * @return
     */
    public String getSerialNumber() {
        String serialNumber = redisOpsUtil.strRedisTemplate.opsForValue().get("OcBSapSalesDataRecordSumNkServiceSerialNumber");
        log.info("OcBSapSalesDataRecordSumNkService serialNumber:{}, getSeconds:{}", serialNumber, getSeconds());
        if (org.apache.commons.lang.StringUtils.isEmpty(serialNumber)) {
            redisOpsUtil.strRedisTemplate.opsForValue().set("OcBSapSalesDataRecordSumNkServiceSerialNumber", "1", getSeconds(), TimeUnit.SECONDS);
            serialNumber = formater.format(1);
        } else {
            Long rsbsSerialNumber = redisOpsUtil.strRedisTemplate.opsForValue().increment("OcBSapSalesDataRecordSumNkServiceSerialNumber", 1L);
            serialNumber = formater.format(rsbsSerialNumber);
        }
        log.info(LogUtil.format("OcBSapSalesDataRecordSumNkService.getSerialNumber:{}",
                "serialNumber"), serialNumber);
        return serialNumber;
    }

    /**
     * 获取当天结束还剩余多少秒
     *
     * @return
     */
    public static int getSeconds() {
        //获取今天当前时间
        Calendar curDate = Calendar.getInstance();
        //获取明天凌晨0点的⽇期
        Calendar tommorowDate = new GregorianCalendar(
                curDate.get(Calendar.YEAR),
                curDate.get(Calendar.MONTH),
                curDate.get(Calendar.DATE) + 1,
                0, 0, 0);
        //返回明天凌晨0点和今天当前时间的差值（秒数）
        return (int) (tommorowDate.getTimeInMillis() - curDate.getTimeInMillis()) / 1000;
    }

}
