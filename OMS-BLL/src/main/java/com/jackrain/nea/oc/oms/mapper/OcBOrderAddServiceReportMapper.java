package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAddServiceReport;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDeliveryFail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderAddServiceMapper
 * @Description 订单增值服务
 * <AUTHOR>
 * @Date 2023/10/25 16:20
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderAddServiceReportMapper extends ExtentionMapper<OcBOrderAddServiceReport> {

    /**
     * 查找所有未归档的记录
     *
     * @return
     */
    @Select("SELECT * FROM oc_b_order_addservice_report WHERE is_archived = 0 AND ISACTIVE = 'Y'")
    List<OcBOrderAddServiceReport> selectUnArchivedRecords();

    @Update("UPDATE oc_b_order_addservice_report SET IS_MATCH = 0, REMARK = '未维护对应的增值服务报价',ADDSERVICE_STRATEGY_PRICE=null, ADDSERVICE_STRATEGY_UNIT_PRICE = null, modifieddate = now() WHERE `ID`=#{id}")
    int clearAddServiceInfo(@Param("id") Long id);


}
