package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.Address;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoExchangeRelation;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoRefundRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoExchange;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderExchange;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderLog;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.services.calculate.qty.OmsOrderQtyCalculateService;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllWebUtil;
import com.jackrain.nea.util.Tools;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author:孙勇生
 * @description: 退换货中间表转换到退换货订单
 * @Date: 19/3/9 21:18
 */
@Slf4j
@Component
public class ReturnOrderTransferUtil {


    @Autowired
    private RegionNewService regionService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BllWebUtil webUtil;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;


    /**
     * 淘宝换货中间表转换到退换货订单
     *
     * @param ipBTaobaoExchange 换货中间表数据
     * @param ocBOrder          原单数据
     * @return 换货主表数据
     */
    private OcBReturnOrder buildOcBReturnOrderFromTaobaoExchange(IpBTaobaoExchange ipBTaobaoExchange,
                                                                 OcBOrder ocBOrder) {
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        //淘宝平台换货单号
        returnOrder.setTbDisputeId(ipBTaobaoExchange.getDisputeId());
        //退款金额
        returnOrder.setReturnAmtActual(ipBTaobaoExchange.getPrice());
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.EXCHANGE.getCode());
        //原始订单编号
        returnOrder.setOrigOrderId(ocBOrder.getId());
        //卖家昵称
        returnOrder.setBuyerNick(ipBTaobaoExchange.getBuyerNick());
        //退款创建时间
        returnOrder.setReturnCreateTime(ipBTaobaoExchange.getCreated());
        //最后修改时间
        returnOrder.setLastUpdateTime(ipBTaobaoExchange.getModified());
        //退款状态，默认值:等待退货入ku
        //  TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //退款说明
        returnOrder.setReturnDesc(ipBTaobaoExchange.getReason());
        //物流公司名称
        String buyerLogisticName = ipBTaobaoExchange.getBuyerLogisticName();
        returnOrder.setCpCLogisticsEname(buyerLogisticName);
        this.setLogisticInfo(returnOrder, buyerLogisticName);
        //物流公司单号
        returnOrder.setLogisticsCode(ipBTaobaoExchange.getBuyerLogisticNo());
        //下载时间
        returnOrder.setCreationdate(new Date());
        //商品应退金额(对应商品明细的金额)
        //returnOrder.setReturnAmtActual(); //这个稍后(已加)
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //换货人姓名
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //换货人手机
        returnOrder.setReceiveMobile(ipBTaobaoExchange.getBuyerPhone());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //店铺id
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        //邮编
        returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
        //售后/售中
        returnOrder.setReturnPhase(ipBTaobaoExchange.getRefundPhase());
        //发货仓库
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        //原平台单
        returnOrder.setTid(ocBOrder.getTid());
        //平台
        returnOrder.setPlatform(ocBOrder.getPlatform());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //退单状态，默认20
        //TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        //退款原因
        //returnOrder.setRemark(ipBTaobaoExchange.getReason());
        returnOrder.setReceiveAddress(ipBTaobaoExchange.getBuyerAddress());
        //店铺名称
        returnOrder.setSellerNick(ipBTaobaoExchange.getSellerNick());
        //店铺标题
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());

        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode()); //原始平台单号

        this.returnOrderAddress(returnOrder, ipBTaobaoExchange.getBuyerAddress());
        returnOrder.setIsReserved(1);
        //取值为发货实体仓档案中关联的退货待检实体仓仓库
        // @20200707 修改退货仓取值逻辑
        // this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder);
        // @20200721 设置个默认值
        returnOrder.setIsNeedToWms(Long.valueOf(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal()));
        this.setCpCPhyWarehouseInIdForExchange(ocBOrder, returnOrder);
        return returnOrder;
    }

    /**
     * 从策略取值
     *
     * @param ocBOrder
     * @param returnOrder
     */
    private void setCpCPhyWarehouseInIdForExchange(OcBOrder ocBOrder, OcBReturnOrder returnOrder) {
        if (Objects.nonNull(ocBOrder) && Objects.nonNull(returnOrder)) {
            // 查询策略
            OmsStCShopStrategyService shopStrategyService = ApplicationContextHandle.getBean(OmsStCShopStrategyService.class);
            StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());

            if (Objects.nonNull(shopStrategy)) {
                Integer isMultiReturnWarehouse = shopStrategy.getIsMultiReturnWarehouse();

                // @20200803 bug-prd-淘宝换货入库仓未设置问题：是否有多个退货仓库默认为空引起
                if (Objects.isNull(isMultiReturnWarehouse) || isMultiReturnWarehouse == 0) {
                    returnOrder.setCpCPhyWarehouseInId(shopStrategy.getCpCWarehouseDefId());
                    this.selectReturnCPhyWarehouse2(shopStrategy.getCpCWarehouseDefId(), returnOrder);
                }
            }
        }
    }

    public void setLogisticInfo(OcBReturnOrder returnOrder, String buyerLogisticName) {
        if (StringUtils.isNotEmpty(buyerLogisticName)) {
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(buyerLogisticName);
            if (logisticsInfo != null) {
                returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                returnOrder.setCpCLogisticsId(logisticsInfo.getId());
            }
        }
    }

    /**
     * 2019-0715新增优化需求
     *
     * @param returnOrder
     * @param buyerLogisticName
     */
    public void setLogisticInfo(OcBReturnOrder returnOrder, String buyerLogisticName, IpTaobaoRefundRelation taobaoRefundRelation) {
        IpBTaobaoRefund taobaoRefund = taobaoRefundRelation.getTaobaoRefund();
        OcBOrder ocBOrder = taobaoRefundRelation.getOcBOrder().get(0);
        Integer hasGoodReturn = taobaoRefund.getHasGoodReturn();
        //不是退货
        if (TaobaoReturnOrderExt.HasGoodReturnStatus.NO_RETURN.getCode().equals(hasGoodReturn) && this.isAmtBalanceNew(taobaoRefundRelation)) {
            //判断退款金额与明细金额是否相等
            returnOrder.setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
            returnOrder.setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
            returnOrder.setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
            returnOrder.setLogisticsCode(ocBOrder.getExpresscode());
        } else {
            if (StringUtils.isNotEmpty(buyerLogisticName)) {
                LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(buyerLogisticName);
                if (logisticsInfo != null) {
                    returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                    returnOrder.setCpCLogisticsId(logisticsInfo.getId());
                }
            }
        }

    }

    /**
     * 淘宝退款中间表转换到退换货订单
     *
     * @return 退款主表数据
     */
    private OcBReturnOrder buildOcBReturnOrderFromTaobaoRefund(IpTaobaoRefundRelation taobaoRefundRelation) {
        IpBTaobaoRefund ipBTaobaoRefund = taobaoRefundRelation.getTaobaoRefund();
        OcBOrder ocBOrder = taobaoRefundRelation.getOcBOrder().get(0);
        if (null == ipBTaobaoRefund || null == ocBOrder) {
            return null;
        }
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        //平台退款单号
        returnOrder.setReturnId(ipBTaobaoRefund.getRefundId());
        //原始订单编号
        returnOrder.setOrigOrderId(ocBOrder.getId());
        //卖家昵称
        returnOrder.setBuyerNick(ipBTaobaoRefund.getBuyerNick());
        //申请退款时间
        returnOrder.setReturnCreateTime(ipBTaobaoRefund.getCreated());
        //最后修改时间
        returnOrder.setLastUpdateTime(ipBTaobaoRefund.getModified());
        //等待退货入库(PRD数据对象)
        returnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode());
        //货物退回时间
        returnOrder.setReturnTime(ipBTaobaoRefund.getGoodReturnTime());
        //退款说明
        returnOrder.setReturnDesc(ipBTaobaoRefund.getReason());
        //下载时间
        returnOrder.setCreationdate(new Date());
        //商品应退金额(
        returnOrder.setReturnAmtList(ipBTaobaoRefund.getRefundFee());
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //换货人姓名
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //换货人手机
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //店铺id
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        //邮编
        returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
        //售后/售中
        returnOrder.setReturnPhase(ipBTaobaoRefund.getRefundPhase());
        //发货仓库
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        //平台类型
        returnOrder.setPlatform(ocBOrder.getPlatform());
        //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
        returnOrder.setReturnAmtActual(ipBTaobaoRefund.getRefundFee());
        //换货金额
        returnOrder.setExchangeAmt(BigDecimal.ZERO);
        returnOrder.setTid(ocBOrder.getTid());
        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //退单状态，默认20
        //TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
        //退款原因
        //returnOrder.setRemark(ipBTaobaoRefund.getReason());
        //returnOrder.setReturnReason(ipBTaobaoRefund.getReason());
        returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());
        //店铺名称
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        //卖家呢城
        returnOrder.setSellerNick(ipBTaobaoRefund.getSellerNick());
        returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
        returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
        returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());
        returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
        returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
        returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
        //取值为发货实体仓档案中关联的退货待检实体仓仓库
        this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder);
        //物流公司名称
        String companyName = ipBTaobaoRefund.getCompanyName();
        //退回物流单号
        returnOrder.setLogisticsCode(ipBTaobaoRefund.getSid());
        returnOrder.setCpCLogisticsEname(companyName);
        this.setLogisticInfo(returnOrder, companyName, taobaoRefundRelation);
        return returnOrder;
    }


    /**
     * 通过实体仓id查询该实体仓的退货仓id
     */
    private void selectReturnCPhyWarehouse(Long cPhyWarehouseId, OcBReturnOrder returnOrder) {
        if (cPhyWarehouseId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cPhyWarehouseId);
            if (cpCPhyWarehouse != null) {
                returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getReturnPhyWarehouseId());
                CpCPhyWarehouse cpCPhyWarehouse1 = cpRpcService.queryByWarehouseId(cpCPhyWarehouse.getReturnPhyWarehouseId());
                if (cpCPhyWarehouse1 != null) {
                    Integer wmsControlWarehouse = cpCPhyWarehouse1.getWmsControlWarehouse();
                    // @20200714 空指针问题
                    if (wmsControlWarehouse != null && wmsControlWarehouse == 1) {
                        returnOrder.setIsNeedToWms(1L);
                    }
                }
            }
        }
    }

    /**
     * 设置传wms标识
     *
     * @param warehouseId
     * @param returnOrder
     */
    private void selectReturnCPhyWarehouse2(Long warehouseId, OcBReturnOrder returnOrder) {
        if (warehouseId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(warehouseId);

            if (cpCPhyWarehouse != null && cpCPhyWarehouse.getWmsControlWarehouse() != null && cpCPhyWarehouse.getWmsControlWarehouse() == 1) {
                returnOrder.setIsNeedToWms(1L);
            } else {
                //@20210917是否pos管控仓下发wms
                if (StringUtils.isNotBlank(cpCPhyWarehouse.getIsPos()) && StringUtils.equals(cpCPhyWarehouse.getIsPos(), "1")) {
                    returnOrder.setIsNeedToWms(1L);
                }
            }
        }
    }

    /**
     * 淘宝退款中间表转换到退货明细
     *
     * @param ipBTaobaoRefund orderItem
     * @param orderItems      订单明细
     * @param isGift          是否有赠品
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnOrderItemFromRefund(
            IpBTaobaoRefund ipBTaobaoRefund, List<OcBOrderItem> orderItems, Boolean isGift) {
        List<OcBReturnOrderRefund> result = null;
        Map<String, String> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderItems)) {
            //总数数量
            BigDecimal allQty = orderItems.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
            //实际实收汇总
            BigDecimal allAealAmt = orderItems.stream().map(OcBOrderItem::getRealAmt).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            //a.单件实际成交价=单行实际成交金额（real_amt）合计/数量合计
            BigDecimal realPrice = allAealAmt.divide(allQty, 4, BigDecimal.ROUND_HALF_UP);
            result = new ArrayList<>();
            /**
             * 分摊金额
             */
            BigDecimal totalReceivedAmount = BigDecimal.ZERO;
            int i = 0;
            for (OcBOrderItem orderItem : orderItems) {
                i++;
                Integer isGift1 = orderItem.getIsGift();
                if (isGift1 == 1) {
                    isGift = true;
                }
                OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
                returnOrderRefund.setPsCProEname(orderItem.getPsCProEname()); //商品名称
                returnOrderRefund.setPrice(orderItem.getPriceList());
                //1 qty_can_refund 购买数量 合计所有明细qty
                returnOrderRefund.setQtyCanRefund(orderItem.getQty());
                //单行数量
                BigDecimal itemQty = orderItem.getQty();
                if (isGift) {
                    returnOrderRefund.setAmtAdjust(BigDecimal.ZERO);
                    //2申请数量=明细数量
                    returnOrderRefund.setQtyRefund(itemQty);
                    //3退还金额=明细数量
                    returnOrderRefund.setAmtRefund(BigDecimal.ZERO);
                    returnOrderRefund.setAmtRefundSingle(BigDecimal.ZERO);
                } else {
                    //退还金额 refund_fee/总数量X单行数量 分摊余额
                    BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
                    BigDecimal refundAmt = refundFee.multiply(itemQty).divide(allQty, 4, BigDecimal.ROUND_HALF_DOWN);
                    if (i != orderItems.size()) {
                        returnOrderRefund.setAmtRefund(refundAmt);
                        // @20201215 此行代码无意义
                        // totalReceivedAmount.add(refundAmt);
                    } else {
                        returnOrderRefund.setAmtRefund(refundFee.multiply(totalReceivedAmount));
                    }
                    //2申请数量 公式
                    //b 公式略
                    BigDecimal refundAmtTemp = returnOrderRefund.getAmtRefund();
                    BigDecimal abs = refundAmtTemp.divide(realPrice, 4, BigDecimal.ROUND_HALF_UP).subtract(allQty);
                    abs = abs.abs();
                    if (abs.compareTo(BigDecimal.valueOf(0.01)) > 0) {
                        returnOrderRefund.setQtyRefund(orderItem.getQty());
                    } else {
                        BigDecimal qtyReturn = refundAmtTemp.divide(realPrice, 0, BigDecimal.ROUND_HALF_UP);
                        returnOrderRefund.setQtyRefund(qtyReturn);
                    }
                    //4 调整金额 oc_b_return_order_refund.refund_amt-price_list
                    returnOrderRefund.setAmtAdjust(returnOrderRefund.getAmtRefund().subtract(orderItem.getPriceList()));
                    //单间退货金额(实际成交金额/数量)
                    returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_DOWN));
                    returnOrderRefund.setAmtRefund(ipBTaobaoRefund.getRefundFee());
                }
                //商品单价
                returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
                //国标码
                returnOrderRefund.setBarcode(orderItem.getBarcode());
                //修改人用户名
                returnOrderRefund.setModifierename(orderItem.getModifierename());
                //商品规格
                returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
                //条码id
                returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
                returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
                returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
                returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
                returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
                returnOrderRefund.setPsCProId(orderItem.getPsCProId());
                //颜色尺寸
                returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
                returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
                returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

                returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
                returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
                returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
                returnOrderRefund.setSex(orderItem.getSex());
                returnOrderRefund.setPriceList(orderItem.getPriceTag());
                //子订单
                //liqb 更改ooid类型从Long类型改成String类型
                if (null != ipBTaobaoRefund.getOid()) {
                    returnOrderRefund.setOid(String.valueOf(ipBTaobaoRefund.getOid()));
                }
                //入库数量
                returnOrderRefund.setQtyIn(BigDecimal.ZERO);
                returnOrderRefund.setVersion(orderItem.getVersion());
                //商品标记
                returnOrderRefund.setProductMark("1");
                //退还金额
                Long reserveBigint01 = orderItem.getProType(); //商品类型
                String giftbagSku = orderItem.getGiftbagSku(); //虚拟条码
                if (reserveBigint01 != null) {
                    if (SkuType.COMBINE_PRODUCT == reserveBigint01 || SkuType.GIFT_PRODUCT == reserveBigint01) {
                        if (!map.containsKey(giftbagSku)) {
                            map.put(giftbagSku, null);
                            returnOrderRefund.setAmtRefund(ipBTaobaoRefund.getRefundFee());
                        } else {
                            returnOrderRefund.setAmtRefund(BigDecimal.ZERO);
                        }
                        returnOrderRefund.setQtyRefund(orderItem.getQty());//申请数量
                        returnOrderRefund.setQtyCanRefund(orderItem.getQty());

                    }
                }
                //计算结算金额和结算单价
                setPriceAndTotPrice(orderItem, returnOrderRefund);
                result.add(returnOrderRefund);
            }
        }

        return result;
    }

    /**
     * 退货结算单价和结算金额
     */
    private void setPriceAndTotPrice(OcBOrderItem ocBOrderItem, OcBReturnOrderRefund refund) {
        if (log.isInfoEnabled()) {
            log.info(this.getClass().getName() + "计算结算单价和结算金额ocBOrderItem=" + JSONObject.toJSONString(ocBOrderItem)
                    + ",refund=" + JSONObject.toJSONString(refund));
        }
        try {
            BigDecimal sgAmt = BigDecimal.ZERO;
            BigDecimal qtyRefund = refund.getQtyRefund();
            BigDecimal qty = ocBOrderItem.getQty();
            //成交金额
            BigDecimal realAmt = ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt();
            if (realAmt != null && qty != null && qty.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                sgAmt = realAmt.divide(qty, OcBOrderConst.DECIMAL_QTY, BigDecimal.ROUND_HALF_UP);
            }
            BigDecimal settle = ocBOrderItem.getPriceSettle() == null ? sgAmt : ocBOrderItem.getPriceSettle();
            settle = settle.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN ? settle : BigDecimal.ZERO;

            BigDecimal totPrice = settle.multiply(qtyRefund);
            //结算单价
            refund.setPriceSettle(settle);
            //结算金额
            refund.setAmtSettleTot(totPrice);
        } catch (Exception e) {
            log.error(this.getClass().getName() + " 淘宝转退货单计算结算单价和金额出错" + e.getMessage());
            refund.setPriceSettle(BigDecimal.ZERO);
            refund.setAmtSettleTot(BigDecimal.ZERO);
        }
    }

    /**
     * 换货结算单价和结算金额
     */
    private void setExchangePriceAndTotPrice(OcBOrderItem ocBOrderItem, OcBReturnOrderExchange refund) {
        if (log.isInfoEnabled()) {
            log.info(this.getClass().getName() + "换货计算结算单价和结算金额ocBOrderItem=" + JSONObject.toJSONString(ocBOrderItem)
                    + ",Exchange=" + JSONObject.toJSONString(refund));
        }
        try {
            BigDecimal sgAmt = BigDecimal.ZERO;
            BigDecimal qtyRefund = refund.getQtyExchange();
            BigDecimal qty = ocBOrderItem.getQty();
            //成交金额
            BigDecimal realAmt = ocBOrderItem.getRealAmt() == null ? BigDecimal.ZERO : ocBOrderItem.getRealAmt();
            if (realAmt != null && qty != null && qty.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN) {
                sgAmt = realAmt.divide(qty, OcBOrderConst.DECIMAL_QTY, BigDecimal.ROUND_HALF_UP);
            }
            BigDecimal settle = ocBOrderItem.getPriceSettle() == null ? sgAmt : ocBOrderItem.getPriceSettle();
            settle = settle.compareTo(BigDecimal.ZERO) > OcBOrderConst.IS_STATUS_IN ? settle : BigDecimal.ZERO;

            BigDecimal totPrice = settle.multiply(qtyRefund);
            //结算单价
            refund.setPriceSettle(settle);
            //结算金额
            refund.setAmtSettleTot(totPrice);
        } catch (Exception e) {
            log.info(this.getClass().getName() + "淘宝转换货单计算结算单价和金额出错" + e.getMessage());
            refund.setPriceSettle(BigDecimal.ZERO);
            refund.setAmtSettleTot(BigDecimal.ZERO);
        }
    }

    /**
     * 构建换货商品的明细信息
     *
     * @param psCProSkuResult 商品的sku信息
     * @param orderItems      原单明细数据
     * @param taobaoExchange  换货中间表数据
     * @param isGroupGoods    是否为组合商品
     * @return
     */
    private List<OcBReturnOrderExchange> buildExchangeOrderItemFromExchange(
            List<ProductSku> psCProSkuResult,
            List<OcBOrderItem> orderItems,
            IpBTaobaoExchange taobaoExchange, boolean isGroupGoods) {
        List<OcBReturnOrderExchange> exchangeOrderItem = new ArrayList<>();
        //申请退货明细单行实际成交价格之和
        BigDecimal actualCount = orderItems.stream().map(OcBOrderItem::getRealAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal actualSum = BigDecimal.ZERO;
        //TODO 需完善功能  需要换货商品的sku信息
        for (int i = 0; i < psCProSkuResult.size(); i++) {
            ProductSku cProSkuResult = psCProSkuResult.get(i);
            OcBReturnOrderExchange ocBReturnOrderExchange = new OcBReturnOrderExchange();
            ocBReturnOrderExchange.setPsCSkuEcode(cProSkuResult.getSku()); //退货编码
            //商品规格
            ocBReturnOrderExchange.setSkuSpec(cProSkuResult.getSkuSpec());
            //商品标准价
            ocBReturnOrderExchange.setPrice(cProSkuResult.getPrice());  //取得就是吊牌价
            //调整金额
            ocBReturnOrderExchange.setAmtAdjust(BigDecimal.ZERO);
            ocBReturnOrderExchange.setQtyIn(BigDecimal.ZERO);   //入库数量
            if (!isGroupGoods) {
                //换货金额 不是组合商品商品时换货金额取原订单退货明细的单行实际成交金额(不是组合商品只会对应一条明细)
                BigDecimal realAmt = orderItems.get(0).getRealAmt(); //成交金额
                BigDecimal qty1 = orderItems.get(0).getQty(); //数量
                BigDecimal amtRefund = realAmt.divide(qty1, 2, BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(taobaoExchange.getQty()));
                ocBReturnOrderExchange.setAmtRefund(amtRefund);
                ocBReturnOrderExchange.setQtyExchange(new BigDecimal(taobaoExchange.getQty()));  //换货数量
            } else {
                BigDecimal bigDecimal1 =
                        this.exchangeAmount(psCProSkuResult, actualCount, cProSkuResult);
                if (i == psCProSkuResult.size() - 1) {
                    ocBReturnOrderExchange.setAmtRefund(actualCount.subtract(actualSum));//换货金额
                } else {
                    actualSum = actualSum.add(bigDecimal1);
                    ocBReturnOrderExchange.setAmtRefund(bigDecimal1);//换货金额
                }
                BigDecimal num = cProSkuResult.getNum();
                BigDecimal bigDecimal = new BigDecimal(taobaoExchange.getQty());
                ocBReturnOrderExchange.setQtyExchange(num.multiply(bigDecimal));
            }
            ocBReturnOrderExchange.setIsReturn(1); //退换货标识  默认 0
            ocBReturnOrderExchange.setPsCSkuId(cProSkuResult.getId());   //条码id
            ocBReturnOrderExchange.setPsCSkuEcode(cProSkuResult.getSkuEcode());
            ocBReturnOrderExchange.setBarcode(cProSkuResult.getBarcode69()); //国标码
            ocBReturnOrderExchange.setPsCProId(cProSkuResult.getProdId());  //商品id
            ocBReturnOrderExchange.setPsCProEcode(cProSkuResult.getProdCode()); //商品编码
            ocBReturnOrderExchange.setPsCProEname(cProSkuResult.getName()); //商品名称
            ocBReturnOrderExchange.setOid(String.valueOf(taobaoExchange.getBizOrderId()));

            ocBReturnOrderExchange.setPsCSizeEcode(cProSkuResult.getSizeCode());
            ocBReturnOrderExchange.setPsCSizeEname(cProSkuResult.getSizeName());
            ocBReturnOrderExchange.setPsCSizeId(cProSkuResult.getSizeId());

            ocBReturnOrderExchange.setPsCClrEcode(cProSkuResult.getColorCode());
            ocBReturnOrderExchange.setPsCClrEname(cProSkuResult.getColorName());
            ocBReturnOrderExchange.setPsCClrId(cProSkuResult.getColorId());
            ocBReturnOrderExchange.setPriceList(cProSkuResult.getPricelist());
            ocBReturnOrderExchange.setSex(cProSkuResult.getSex());
            //退货计算结算单价结算金额
            setExchangePriceAndTotPrice(orderItems.get(0), ocBReturnOrderExchange);
            exchangeOrderItem.add(ocBReturnOrderExchange);
        }

        return exchangeOrderItem;
    }


    /**
     * 构建退换货订单的退货数据
     *
     * @param orderItems
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnOrderItemFromExchange(
            List<OcBOrderItem> orderItems, IpBTaobaoExchange taobaoExchange, Boolean isGroupGoods) {
        List<OcBReturnOrderRefund> returnOrderRefunds = new ArrayList<>();
        Long qty = taobaoExchange.getQty(); //换货数量
        for (OcBOrderItem orderItem : orderItems) {
            OcBReturnOrderRefund ocBReturnOrderRefund = new OcBReturnOrderRefund();
            ocBReturnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode()); //退货编码
            ocBReturnOrderRefund.setSkuSpec(orderItem.getSkuSpec()); ////商品规格
            ocBReturnOrderRefund.setPrice(orderItem.getPriceList()); //商品标准价
            ocBReturnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());  //调整金额
            ocBReturnOrderRefund.setQtyIn(BigDecimal.ZERO);  //入库数量
            ocBReturnOrderRefund.setQtyCanRefund(orderItem.getQty());//可退数量
            ocBReturnOrderRefund.setIsReturn(0); //退换货标识  默认 0
            ocBReturnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId()); //条码id
            ocBReturnOrderRefund.setBarcode(orderItem.getBarcode()); //国标码
            ocBReturnOrderRefund.setPsCProId(orderItem.getPsCProId()); //商品id
            ocBReturnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            ocBReturnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode()); //商品编码
            ocBReturnOrderRefund.setPsCProEname(orderItem.getPsCProEname());  //商品名称
            ocBReturnOrderRefund.setOid(orderItem.getOoid());  //子订单id
            ocBReturnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_DOWN));
            //商品标记
            ocBReturnOrderRefund.setProductMark("1");
            ocBReturnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
            ocBReturnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
            ocBReturnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());
            ocBReturnOrderRefund.setSex(orderItem.getSex());
            ocBReturnOrderRefund.setPriceList(orderItem.getPriceTag());
            ocBReturnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
            ocBReturnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
            ocBReturnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
            if (isGroupGoods) {
                ocBReturnOrderRefund.setQtyRefund(orderItem.getQty()); //申请数量
                ocBReturnOrderRefund.setAmtRefund(orderItem.getRealAmt()); //退还金额
            } else {
                BigDecimal realAmt = orderItem.getRealAmt(); //成交金额
                BigDecimal qty1 = orderItem.getQty(); //数量
                BigDecimal amtRefund = realAmt.divide(qty1, 4, BigDecimal.ROUND_HALF_DOWN).multiply(new BigDecimal(qty));
                ocBReturnOrderRefund.setQtyRefund(new BigDecimal(qty)); //申请数量
                ocBReturnOrderRefund.setAmtRefund(amtRefund); //退还金额
            }

            // @20200714 加关联ID
            ocBReturnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
            ocBReturnOrderRefund.setOcBOrderItemId(orderItem.getId());

            //计算结算金额和结算单价
            setPriceAndTotPrice(orderItem, ocBReturnOrderRefund);
            returnOrderRefunds.add(ocBReturnOrderRefund);
        }
        return returnOrderRefunds;

    }


    /**
     * 是组合商品的换货金额的计算
     */

    private BigDecimal exchangeAmount(List<ProductSku> psCProSku,
                                      BigDecimal actualCount,
                                      ProductSku productSku) {
        BigDecimal reduce1 = psCProSku.stream().map(x -> x.getPricelist().multiply(x.getNum())).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        //当前行的数量*吊牌价
        BigDecimal tagCount = productSku.getPricelist().multiply(productSku.getNum());
        //计算当前行的换货价格
        // BigDecimal divide = actualCount.multiply(tagCount.divide(reduce1, 4, BigDecimal.ROUND_HALF_DOWN));
        BigDecimal divide = reduce1.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : actualCount.multiply(tagCount.divide(reduce1, 4, BigDecimal.ROUND_HALF_DOWN));
        return divide;
    }


    /**
     * 封装订单主表实体对象(取退换货订单表数据)
     */
    public OcBOrder allChannelsOcBOrder(OcBReturnOrder ocBReturnOrder,
                                        OcBOrder ocBOrder,
                                        IpBTaobaoExchange taobaoExchange,
                                        User user) {
        OcBOrder order = new OcBOrder();
        order.setSourceCode(ocBReturnOrder.getTbDisputeId() + "");
        order.setUserNick(ocBReturnOrder.getBuyerNick()); //用户昵称
        order.setCpCShopId(ocBReturnOrder.getCpCShopId());

        order.setCpCShopTitle(ocBReturnOrder.getCpCShopTitle()); //店铺标题
        order.setUserId(ocBOrder.getUserId());
        order.setUserNick(ocBReturnOrder.getBuyerNick());
        order.setProductAmt(ocBReturnOrder.getExchangeAmt()); //换货金额
        order.setOrderAmt(ocBReturnOrder.getExchangeAmt());

        order.setReceivedAmt(ocBReturnOrder.getExchangeAmt());
        order.setAmtReceive(ocBReturnOrder.getExchangeAmt());

        order.setReceiverName(ocBOrder.getReceiverName());
        order.setReceiverMobile(ocBReturnOrder.getReceiveMobile());
        order.setReceiverPhone(ocBReturnOrder.getReceivePhone());
        //封装订单的省市区id code name
        this.orderAddress(order, ocBReturnOrder.getReceiveAddress());
        order.setReceiverAddress(ocBReturnOrder.getReceiveAddress());
        order.setReceiverZip(ocBReturnOrder.getReceiveZip());
        order.setCpCRegionTownEname(ocBReturnOrder.getCpCRegionTownEname());

        order.setOrigOrderId(ocBReturnOrder.getOrigOrderId());


        order.setPlatform(ocBOrder.getPlatform());

        order.setTid(ocBOrder.getTid()); //换货订单取换货平台单号
        order.setOrderSource(ocBReturnOrder.getOrdeSource());
        order.setUserId(ocBOrder.getUserId());


        this.setOperateUserInfo(order, user);
        //order.setQtyAll(); //TODO 商品数量
        return order;
    }

    /**
     * 订单明细表数据
     *
     * @param
     * @return
     */
    public List<OcBOrderItem> allChannelsOcBOrderItem(List<OcBReturnOrderExchange> orderExchangeList,
                                                      List<ProductSku> psCProSkuResult,
                                                      IpBTaobaoExchange taobaoExchange,
                                                      User user) {
        Map<String, ProductSku> map = new HashMap();
        for (ProductSku productSku : psCProSkuResult) {
            map.put(productSku.getSkuEcode(), productSku);
        }
        List<OcBOrderItem> orderItems = new ArrayList<>();
        for (OcBReturnOrderExchange ocBReturnOrderExchange : orderExchangeList) {
            OcBOrderItem ocBOrderItem = new OcBOrderItem();
            ocBOrderItem.setBarcode(ocBReturnOrderExchange.getBarcode());
            ocBOrderItem.setPsCProId(ocBReturnOrderExchange.getPsCProId());
            ocBOrderItem.setPsCProEcode(ocBReturnOrderExchange.getPsCProEcode());
            ocBOrderItem.setPsCProEname(ocBReturnOrderExchange.getPsCProEname());
            //ocBOrderItem.setStoreSite(cProSkuResult.); //库位
            ocBOrderItem.setSkuSpec(ocBReturnOrderExchange.getSkuSpec());  //规格
            //ocBOrderItem.setOoid(ocBReturnOrderExchange.getOid());  //子订单id
            ocBOrderItem.setPsCSkuId(ocBReturnOrderExchange.getPsCSkuId());
            ocBOrderItem.setPsCSkuEcode(ocBReturnOrderExchange.getPsCSkuEcode());
            ocBOrderItem.setPrice(ocBReturnOrderExchange.getPrice());//换货金额
            //标准价*数量
            BigDecimal multiply = ocBReturnOrderExchange.getPrice().multiply(ocBReturnOrderExchange.getQtyExchange());
            ocBOrderItem.setQty(ocBReturnOrderExchange.getQtyExchange());// 换货数量
            ocBOrderItem.setTid(taobaoExchange.getTid() + ""); //平台单号
            ocBOrderItem.setRealAmt(ocBReturnOrderExchange.getAmtRefund()); //成交金额
            ocBOrderItem.setAdjustAmt(ocBReturnOrderExchange.getAmtRefund().subtract(multiply)); //调整金额
            ProductSku productSku = map.get(ocBReturnOrderExchange.getPsCSkuEcode());
            ocBOrderItem.setPriceList(productSku.getPricelist()); //标准价
            ocBOrderItem.setTitle(productSku.getName());
            ocBOrderItem.setPsCClrId(productSku.getColorId());
            ocBOrderItem.setPsCClrEcode(productSku.getColorCode());
            ocBOrderItem.setPsCClrEname(productSku.getColorName());
            ocBOrderItem.setPsCSizeId(productSku.getSizeId());
            ocBOrderItem.setPsCSizeEcode(productSku.getSkuEcode());
            ocBOrderItem.setPsCSizeEname(productSku.getSizeName());

            // 增加品类信息 20220923
            ocBOrderItem.setMDim4Id(productSku.getMDim4Id());
            ocBOrderItem.setMDim6Id(productSku.getMDim6Id());
            if ("Y".equals(productSku.getIsEnableExpiry())) {
                ocBOrderItem.setIsEnableExpiry(1);
            } else {
                ocBOrderItem.setIsEnableExpiry(0);
            }
            Long disputeId = taobaoExchange.getDisputeId();
            ocBOrderItem.setOoid(disputeId + ""); //明细的ooid
            this.setOperateUserInfo(ocBOrderItem, user);
            ocBOrderItem.setSex(productSku.getSex());
            ocBOrderItem.setPriceTag(productSku.getPricelist()); //吊牌价

            // @20200810 bug#20869 订单明细加换货标识--发货后更新平台使用
            ocBOrderItem.setIsExchangeItem(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());

            ocBOrderItem.setExchangeBillNo(disputeId);
            orderItems.add(ocBOrderItem);
        }
        return orderItems;
    }


    /**
     * 淘宝换货接口关系转换MOS退换货关系
     *
     * @param taobaoExchangeRelation
     * @return
     */
    public OcBReturnOrderRelation taobaoExchangeOrderToReturnOrder(
            IpTaobaoExchangeRelation taobaoExchangeRelation) {
        OcBReturnOrderRelation returnOrderRelation = new OcBReturnOrderRelation();
        //TODO 需完善功能
        OcBOrder ocBOrder = taobaoExchangeRelation.getOriginalValidOrderInfo(); //原始单号
        IpBTaobaoExchange taobaoExchange = taobaoExchangeRelation.getTaobaoExchange(); //中间表数据
        List<OcBOrderItem> ocBOrderItems = taobaoExchangeRelation.getOriginalOrderItemList();
        List<ProductSku> psCProSkuResult = taobaoExchangeRelation.getExchangeProductDetailList();

        OcBReturnOrder returnOrder =
                this.buildOcBReturnOrderFromTaobaoExchange(taobaoExchange, ocBOrder);

        Boolean isGroupGoods = taobaoExchangeRelation.getIsGroupGoods();
        //退货的数据
        List<OcBReturnOrderRefund> returnOrderRefunds = buildReturnOrderItemFromExchange(ocBOrderItems, taobaoExchange, isGroupGoods);

        // @20200811 task#20832 换货单加可退数量校验
        boolean qtyFlag = OmsOrderQtyCalculateService.getInstance().checkQtyCanReturn(returnOrderRefunds, ocBOrderItems);

        if (!qtyFlag) {
            throw new NDSException("申请数量大于可退换数量");
        }

        this.getAllSku(returnOrderRefunds, returnOrder);
        //换货的数据
        List<OcBReturnOrderExchange> ocBReturnOrderExchanges =
                buildExchangeOrderItemFromExchange(psCProSkuResult, ocBOrderItems,
                        taobaoExchange, isGroupGoods);
        //计算换货明细金额相加
        BigDecimal reduce = ocBReturnOrderExchanges.stream().
                map(OcBReturnOrderExchange::getAmtRefund).reduce(BigDecimal.ZERO, BigDecimal::add);
        //商品应退金额
        returnOrder.setReturnAmtList(reduce);
        //换货金额
        returnOrder.setExchangeAmt(reduce);
        returnOrderRelation.setReturnOrderInfo(returnOrder);
        returnOrderRelation.setOrderRefundList(returnOrderRefunds);
        returnOrderRelation.setOrderExchangeList(ocBReturnOrderExchanges);
        return returnOrderRelation;
    }

    /**
     * 淘宝退货接口关系转换OMS退换货关系
     *
     * @param taobaoRefundRelation
     * @return
     */
    public List<OcBReturnOrderRelation> taobaoRefundOrderToReturnOrder(
            IpTaobaoRefundRelation taobaoRefundRelation) {
        List<OcBReturnOrderRelation> returnOrderRelations = new ArrayList<>();
        OcBReturnOrderRelation returnOrderRelation = new OcBReturnOrderRelation();

        List<OcBOrderItem> ocbOrderItems = taobaoRefundRelation.getOcBOrderItems();
        OcBOrder ocBOrder = taobaoRefundRelation.getOcBOrder().get(0);

        IpBTaobaoRefund taobaoRefund = taobaoRefundRelation.getTaobaoRefund();
        List<OcBOrderItem> ocBOrderGifts = taobaoRefundRelation.getOcBOrderGifts();
        if (CollectionUtils.isNotEmpty(ocBOrderGifts)) {
            boolean gift = this.isGift(taobaoRefund);
            if (!gift) {
                ocbOrderItems.addAll(ocBOrderGifts);
            }
        }
        OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromTaobaoRefund(taobaoRefundRelation);
        returnOrderRelation.setReturnOrderInfo(ocBReturnOrder);
        List<OcBReturnOrderRefund> returnOrderItems =
                this.buildReturnOrderItemFromRefund(taobaoRefund, ocbOrderItems, false);
        if (Objects.nonNull(ocBReturnOrder)) {
            this.getAllSku(returnOrderItems, ocBReturnOrder);
        }
        returnOrderRelation.setOrderRefundList(returnOrderItems);
        returnOrderRelations.add(returnOrderRelation);

        return returnOrderRelations;
    }

    /**
     * 判断赠品是否存在退换货订单 根据平台单号判断是否存在退换货订单
     * (按道理此逻辑不应在这里  前期没考虑的问题,目前先加这里)
     */
    private boolean isGift(IpBTaobaoRefund taoBaoRefund) {
        //通过平台单号查询ES
        Set<Long> ids = ES4ReturnOrder.findIdByTid(taoBaoRefund.getTid());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(ids)) {
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(ids));
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
                return true;
            }
        }
        //是否存在退换货订单
        return false;
    }


    /**
     * 封装主表的all_sku以及 商品数量
     *
     * @param returnOrderItems
     * @return
     */
    private void getAllSku(List<OcBReturnOrderRefund> returnOrderItems, OcBReturnOrder returnOrder) {
        //拼接退货sku加数量
        String skuQyt = "";
        BigDecimal qtyInstore = BigDecimal.ZERO;
        for (OcBReturnOrderRefund returnOrderItem : returnOrderItems) {
            String str = returnOrderItem.getPsCSkuEcode() + "(" + returnOrderItem.getQtyRefund().intValue() + "),";
            skuQyt = skuQyt + str;
            qtyInstore = qtyInstore.add(returnOrderItem.getQtyRefund());
        }
        if (StringUtils.isNotEmpty(skuQyt)) {
            //去掉最后一个,号
            skuQyt = skuQyt.substring(0, skuQyt.length() - 1);
        }
        returnOrder.setAllSku(skuQyt);
        returnOrder.setQtyInstore(qtyInstore);
    }

    /**
     * 退款金额是否一致
     *
     * @param taobaoRefundRelation
     * @return
     */
    public boolean isAmtBalance(IpTaobaoRefundRelation taobaoRefundRelation) {

        List<OcBOrderItem> orderItems = taobaoRefundRelation.getOcBOrderItems();
        IpBTaobaoRefund taobaoRefund = taobaoRefundRelation.getTaobaoRefund();
        if (CollectionUtils.isNotEmpty(orderItems)) {
            //总数数量
            BigDecimal allQty = orderItems.stream().map(OcBOrderItem::getQty).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            //实际实收汇总
            BigDecimal allAealAmt = orderItems.stream().map(OcBOrderItem::getRealAmt).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal refundFee = taobaoRefund.getRefundFee();
            //总额比较
            if (allAealAmt.equals(refundFee)) {
                return true;
            }
            //a.单件实际成交价=单行实际成交金额（real_amt）合计/数量合计
            BigDecimal realPrice = allAealAmt.divide(allQty, 4, BigDecimal.ROUND_HALF_DOWN);
            if (refundFee.compareTo(realPrice) >= 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * i.先将退单申请金额与原单明细的单行实际成交价real_amt做比较，相等则下一步转换；
     * ii.不相等，再将退单申请金额与原单明细的单件实际成交价（单行实际成交价/数量）做比较，
     * 相等则下一步转换，
     *
     * @param taobaoRefundRelation
     * @return
     */
    public boolean isAmtBalanceNew(IpTaobaoRefundRelation taobaoRefundRelation) {
        IpBTaobaoRefund taobaoRefund = taobaoRefundRelation.getTaobaoRefund();
        BigDecimal refundFee = taobaoRefund.getRefundFee(); //退还金额
        //liqb 更改ooid类型从Long类型改成String类型
        String oid = String.valueOf(taobaoRefund.getOid()); //申请退款的oid
        List<OcBOrderItem> ocBOrderItems = taobaoRefundRelation.getOcBOrderItems();
        List<OcBOrderItem> collect = new ArrayList<>();
        BigDecimal realAmtCount = BigDecimal.ZERO;
        boolean exchangeGoods = taobaoRefundRelation.isExchangeGoods(); //是否为退货后又换货
        //退货后又换货,换货单的ooid为换货单号  所以导致校验ooid时不等  成交金额就为0了
        if (exchangeGoods) {
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                realAmtCount = realAmtCount.add(ocBOrderItem.getRealAmt()); //单行实际成交价
                collect.add(ocBOrderItem);
            }
        } else {
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                String ooid = ocBOrderItem.getOoid();
                if (oid.equals(ooid)) {
                    realAmtCount = realAmtCount.add(ocBOrderItem.getRealAmt()); //单行实际成交价
                    collect.add(ocBOrderItem);
                }
            }
        }
        if (refundFee.compareTo(realAmtCount) == 0 || realAmtCount.compareTo(refundFee) < 0) {
            return true;
        }
        BigDecimal amtCount = BigDecimal.ZERO;
        for (OcBOrderItem orderItem : collect) {
            BigDecimal qty = orderItem.getQty(); //数量
            BigDecimal realAmt = orderItem.getRealAmt(); //单行实际成交价
            amtCount = amtCount.add(realAmt.divide(qty, 4, BigDecimal.ROUND_HALF_DOWN));
        }
        //单件实际成交价（单行实际成交价/数量）
        if (refundFee.compareTo(amtCount) == 0) {
            return true;
        }
        return false;
    }


    /**
     * 封装日志参数
     *
     * @param ocBReturnOrderLog 日志实体
     */
    public void saveSysLog(OcBReturnOrderLog ocBReturnOrderLog, User operateUser) {
        Long logId = ModelUtil.getSequence(TaobaoReturnOrderExt.TABLENAME_OCBRETURNORDERLOG);
        ocBReturnOrderLog.setId(logId);
        ocBReturnOrderLog.setIpAddress(getIp(operateUser));
        ocBReturnOrderLog.setOwnerename(operateUser.getName());
        this.setOperateUserInfo(ocBReturnOrderLog, operateUser);
    }

    public String getIp(User operateUser) {
        String ip = "";
        if (operateUser == null) {
            ip = webUtil.getWebRequestIpAddress();
        } else {
            ip = operateUser.getLastloginip();
            if (StringUtils.isEmpty(ip)) {
                ip = webUtil.getWebRequestIpAddress();
            }
        }
        return ip;
    }


    public void setOperateUserInfo(BaseModel model, User operateUser) {
        User tempUser = operateUser;
        if (tempUser == null) {
            tempUser = SystemUserResource.getRootUser();
        }
        model.setAdOrgId((long) tempUser.getOrgId());
        model.setOwnername(tempUser.getEname());
        model.setAdClientId((long) tempUser.getClientId());
        model.setOwnerid(Long.valueOf(tempUser.getId()));
        model.setCreationdate(new Date());
        model.setModifierid(Long.valueOf(tempUser.getId()));
        model.setModifieddate(new Date());
        model.setModifiername(tempUser.getName());
        model.setIsactive("Y");
    }


    public void updateOperator(BaseModel model, User operateUser) {
        User tempUser = operateUser;
        if (tempUser == null) {
            tempUser = SystemUserResource.getRootUser();
        }
        model.setModifierid(Long.valueOf(tempUser.getId()));
        model.setModifieddate(new Date());
        model.setModifiername(tempUser.getName());
    }

    /**
     * 封装退换货单的省市区及id
     *
     * @param ocBReturnOrder
     * @param address
     */
    public void returnOrderAddress(OcBReturnOrder ocBReturnOrder, String address) {
        if (StringUtils.isNotEmpty(address)) {
            int i1 = StringUtils.ordinalIndexOf(address, "， ", 2);
            if (i1 != -1) {
                address = address.substring(i1 + 1);
            }
        }
        Address address1 = this.addressResolutionNew(address);
        if (address1 != null) {
            String province = address1.getProvince(); //省
            String city = address1.getCity(); //市
            String county = address1.getCounty(); //区
            ocBReturnOrder.setReceiverProvinceName(province);
            ocBReturnOrder.setReceiverCityName(city);
            ocBReturnOrder.setReceiverAreaName(county);
            ProvinceCityAreaInfo provinceCityAreaInfo =
                    regionService.selectProvinceCityAreaInfo(province, city, county);
            if (provinceCityAreaInfo != null && provinceCityAreaInfo.getProvinceInfo() != null) {
                ocBReturnOrder.setReceiverProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
            }
            if (provinceCityAreaInfo != null && provinceCityAreaInfo.getCityInfo() != null) {
                ocBReturnOrder.setReceiverCityId(provinceCityAreaInfo.getCityInfo().getId());
            }
            if (provinceCityAreaInfo != null && provinceCityAreaInfo.getAreaInfo() != null) {
                ocBReturnOrder.setReceiverAreaId(provinceCityAreaInfo.getAreaInfo().getId());
            }
        }

    }

    /**
     * 订单主表封装省市区名称及id
     *
     * @param ocBOrder
     * @param address
     */
    public void orderAddress(OcBOrder ocBOrder, String address) {
        Address address1 = this.addressResolutionNew(address);
        if (address1 != null) {
            String province = address1.getProvince(); //省
            String city = address1.getCity(); //市
            String county = address1.getCounty(); //区
            ProvinceCityAreaInfo provinceCityAreaInfo =
                    regionService.selectProvinceCityAreaInfo(province, city, county);
            if (provinceCityAreaInfo != null) {
                RegionInfo provinceInfo = provinceCityAreaInfo.getProvinceInfo();
                RegionInfo cityInfo = provinceCityAreaInfo.getCityInfo();
                RegionInfo areaInfo = provinceCityAreaInfo.getAreaInfo();
                if (provinceInfo != null) {
                    ocBOrder.setCpCRegionProvinceEcode(provinceInfo.getCode());
                    ocBOrder.setCpCRegionProvinceId(provinceInfo.getId());
                    ocBOrder.setCpCRegionProvinceEname(province);
                }
                if (cityInfo != null) {
                    ocBOrder.setCpCRegionCityId(cityInfo.getId());
                    ocBOrder.setCpCRegionCityEname(city);
                    ocBOrder.setCpCRegionCityEcode(cityInfo.getCode());
                }
                if (areaInfo != null) {
                    ocBOrder.setCpCRegionAreaId(areaInfo.getId());
                    ocBOrder.setCpCRegionAreaEname(county);
                    ocBOrder.setCpCRegionAreaEcode(areaInfo.getCode());
                }
            }
        }
    }


    /**
     * 传入地址获取省市区的方法(去掉特殊字符及空格)
     *
     * @param addr 地址
     * @return
     */

    public Address addressResolution(String addr) {
        Pattern p = Pattern.compile("[`~☆★!@#$%^&*()+=|{}':;,\\\\[\\\\]》" +
                "·.<>/?~！@#￥%……——+|{}【】‘；：”“’。，、？]");
        Matcher t = p.matcher(addr);
        String repl = t.replaceAll("").trim().replace(" ", "").replace("\\", "");//将匹配的特殊字符转变为空
        System.out.println(repl);
        String regex = "((?<province>[^省]+省|.+自治区|上海|北京|天津|重庆))" +
                "(?<city>[^市]+市|.+自治州)(?<county>[^县]+县|.+?区|.+镇|" +
                ".+局)?(?<town>[^区]+区|.+镇)?(?<village>.*)";
        Matcher m = Pattern.compile(regex).matcher(repl);
        Address address = null;
        if (m.find()) {
            address = new Address();
            String province = m.group("province");
            address.setProvince(province == null ? "" : province.trim());
            String city = m.group("city");
            address.setCity(city == null ? "" : city.trim());
            String county = m.group("county");
            address.setCounty(county == null ? "" : county.trim());
        }
        return address;
    }

    /**
     * 拆分换货单地址的省市区
     * 市为空时  拿区
     *
     * @param addr
     * @return
     */
    public Address addressResolutionNew(String addr) {
        int indexOf = addr.lastIndexOf("^^^");
        String substring = addr.substring(0, indexOf);
        Address address = new Address();
        String[] split = substring.split("\\^\\^\\^");
        address.setProvince(split[0]);
        String str = split[1];
        System.out.println(str);
        if (StringUtils.isEmpty(str.trim())) {
            address.setCity(split[2]);
        } else {
            address.setCity(str);
        }
        address.setCounty(split[2]);
        return address;
    }


    /**
     * 设置O2O退单上的门店信息
     *
     * @param order       原零售发货单
     * @param returnOrder 现退单
     */
    public static void setO2OReturnOrderInfo(OcBOrder order, OcBReturnOrder returnOrder) {
        if (order == null || returnOrder == null || Tools.getInt(order.getIsO2oOrder(), 0) != 1) {
            return;
        }
        // 退货门店编码
//        returnOrder.setStoreCode(order.getDeliveryStoreCode());
        // 退货门店名称
//        returnOrder.setStoreName(order.getDeliveryStoreName());
        // 结算组织编码
//        returnOrder.setSettleOrgCode(order.getSettleOrganizationCode());
        // 结算组织名称
//        returnOrder.setSettleOrgName(order.getSettleOrganizationName());
        // 结算供应商编码
//        returnOrder.setSettleSupplierCode(order.getSettleSupplierCode());
        // 结算供应商名称
//        returnOrder.setSettleSupplierName(order.getSettleSupplierName());
    }

//    public static void main(String[] args) {
//        String addr = "广东省^^^东莞市^^^ ^^^虎门镇永安路东方国际双子座28D";
//        int indexOf = addr.lastIndexOf("^^^");
//        String substring = addr.substring(0, indexOf);
//        Address address = new Address();
//        String[] split = substring.split("\\^\\^\\^");
//        address.setProvince(split[0]);
//        String str = split[1];
//        System.out.println(str);
//        if (StringUtils.isEmpty(str.trim())) {
//            address.setCity(split[2]);
//        } else {
//            address.setCity(str);
//        }
//        address.setCounty(split[2]);
//        System.out.println(address);


//
//        ReturnOrderTransferUtil util = new ReturnOrderTransferUtil();
//        Address address = util.addressResolution(addr);
//        System.out.println(address);
//        BigDecimal bg = new BigDecimal("1.2000000000000001");
//        String string = JSON.toJSONString(bg);
//        System.out.println(string);


//        List<ProductSku> psCProSku = new ArrayList<>();
//        ProductSku productSku = new ProductSku();
//        productSku.setPricelist(new BigDecimal("22"));
//        productSku.setNum(new BigDecimal("2"));
//        psCProSku.add(productSku);
//
//        ProductSku productSku1 = new ProductSku();
//        productSku1.setPricelist(new BigDecimal("31"));
//        productSku1.setNum(new BigDecimal("2"));
//        psCProSku.add(productSku1);
//        BigDecimal reduce1 = psCProSku.stream().map(x -> x.getPricelist().multiply(x.getNum())).
//                reduce(BigDecimal.ZERO, BigDecimal::add);
//        System.out.println(reduce1);


//    }
}
