package com.jackrain.nea.oc.oms.matcher.live;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 策略类型枚举
 * Author: RESET
 * Date: Created in 2020/6/15 21:39
 * Modified By:
 */
public enum LiveStrategyTypeEnum {

    // 匹配策略类型
    TITLE(1, "LIVE_STRATEGY_RULE_TITLE", "根据商品标题识别"),
    ID(2, "LIVE_STRATEGY_RULE_ID", "根据平台商品ID识别"),
    REMARK(3, "LIVE_STRATEGY_RULE_REMARK", "根据订单备注识别"),
    SPU(4, "LIVE_STRATEGY_RULE_SPU", "根据spu识别");

    @Getter
    private Integer value;
    @Getter
    private String code;
    @Getter
    private String description;

    LiveStrategyTypeEnum(Integer value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static LiveStrategyTypeEnum fromValue(Integer v) {
        for (LiveStrategyTypeEnum c : LiveStrategyTypeEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

}
