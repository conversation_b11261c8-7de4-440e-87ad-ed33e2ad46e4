package com.jackrain.nea.oc.oms.matcher.live;

import lombok.Getter;

import java.util.Objects;

/**
 * Description： 直播平台枚举
 * Author: RESET
 * Date: Created in 2020/6/15 21:39
 * Modified By:
 */
public enum LivePlatformEnum {

    // 匹配策略类型
    DOUYIN("1", "douyin", "抖音"),
    KUAISHOU("2", "kuaishou", "快手"),
    MOMO("3", "momo", "陌陌"),
    MOGUJIE("4", "mogujie", "蘑菇街"),
    TAOBAO("5", "taobao", "淘宝"),
    ;

    @Getter
    private String value;
    @Getter
    private String code;
    @Getter
    private String description;

    LivePlatformEnum(String value, String code, String description) {
        this.value = value;
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static LivePlatformEnum fromValue(String v) {
        for (LivePlatformEnum c : LivePlatformEnum.values()) {
            if (Objects.equals(v, c.value)) {
                return c;
            }
        }
        throw new IllegalArgumentException(String.valueOf(v));
    }

    /**
     * 依据值取描述
     *
     * @param value
     * @return
     * @20200803 by wu.lb
     */
    public static String getDescription(String value) {
        try {
            return LivePlatformEnum.fromValue(value).getDescription();
        } catch (Exception e) {

        }

        return null;
    }

}
