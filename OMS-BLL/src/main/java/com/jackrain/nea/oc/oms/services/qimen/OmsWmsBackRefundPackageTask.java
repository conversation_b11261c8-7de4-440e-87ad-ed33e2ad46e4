package com.jackrain.nea.oc.oms.services.qimen;

import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.common.SgConstants;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.Resources;
import com.jackrain.nea.cpext.api.CpcPhyWareHouseQueryCmd;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.cpext.model.RedisKeyConstans;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.task.OcBRefundPackageTaskMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundPackageTask;
import com.jackrain.nea.redis.config.CusRedisTemplate;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.constants.ResultCode;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.OmsStorageUtils;
import com.jackrain.nea.util.RedisOmsMasterUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Description: 退货包裹中间表
 *
 * @Author: guo.kw
 * @Since: 2022/7/20
 * create at: 2022/7/20 11:42
 */
@Slf4j
@Component
public class OmsWmsBackRefundPackageTask {

    @Autowired
    private OcBRefundPackageTaskMapper ocBRefundPackageTaskMapper;

    @Reference(version = "1.0", group = "cp-ext")
    private CpcPhyWareHouseQueryCmd cpcPhyWareHouseQueryCmd;

    public ValueHolderV14<String> apiProcess(String msg) {
        log.info(LogUtil.format("退货包裹中间表报文:{}",
                "OmsWmsBackRefundPackageTask apiProcess"), msg);
        CusRedisTemplate<Object, Object> redisMasterTemplate = RedisOmsMasterUtils.getStrRedisTemplate();
        String lockKsy = SgConstants.SG_BILL_LOCK_WMSRETURN;

        try {
            JSONObject request = JSONObject.parseObject(msg);
            JSONObject returnOrder = request.getJSONObject("order");
            // 仓库编码
            String warehouseCode = returnOrder.getString("warehouseCode");
            //外部业务编码  用此字段判断唯一
            String outBizCode = returnOrder.getString("outBizCode");
            if (StringUtils.isEmpty(outBizCode)) {
                throw new NDSException("外部业务编码不能为空");
            }
            //wms单号
            String orderCode = returnOrder.getString("orderCode");

            lockKsy += outBizCode;

            Boolean ifAbsent = redisMasterTemplate.opsForValue().setIfAbsent(lockKsy, "OK");

            OcBRefundPackageTask ocBRefundPackageTask = ocBRefundPackageTaskMapper.selectOne(new LambdaQueryWrapper<OcBRefundPackageTask>()
                    .eq(OcBRefundPackageTask::getOutBizCode, outBizCode)
                    .eq(OcBRefundPackageTask::getIsactive, "Y"));

            if (Objects.nonNull(ocBRefundPackageTask) || ifAbsent == null || !ifAbsent) {
                log.error(LogUtil.format("退货包裹WMS回传重复.messageBody:{}",
                        "OmsWmsBackRefundPackageTask apiProcess"), msg);
            } else {
                redisMasterTemplate.expire(lockKsy, 30, TimeUnit.SECONDS);
                OcBRefundPackageTask ocBRefundPackageTask1 = new OcBRefundPackageTask();
                ocBRefundPackageTask1.setId(ModelUtil.getSequence("oc_b_refund_package_task"));
                ocBRefundPackageTask1.setWarehouseCode(warehouseCode);
                ocBRefundPackageTask1.setOutBizCode(outBizCode);
                ocBRefundPackageTask1.setWmsBillCode(orderCode);
                ocBRefundPackageTask1.setMessage(msg);
                ocBRefundPackageTask1.setTransformStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_WAIT);
                ocBRefundPackageTask1.setFailedCount(NumberUtils.INTEGER_ZERO);
                ocBRefundPackageTask1.setIsactive(SgConstants.IS_ACTIVE_Y);
                if (StringUtils.isNotEmpty(warehouseCode)) {
                    CpCPhyWarehouse warehouse = queryWarehouseByWmsWarehouseCode(warehouseCode);
                    if (warehouse != null) {
                        ocBRefundPackageTask1.setWmsWarehouseType(warehouse.getWmsType());
                        /*富勒仓库编码取库存地点*/
                        if (ThirdWmsTypeEnum.FLWMS.getCode().equals(warehouse.getWmsType())) {
                            JSONArray packages = request.getJSONArray("packages");
                            if (packages != null && packages.size() > 0) {
                                String storageLocation = ((JSONObject) packages.get(0)).getString("remarks");
                                if (StringUtils.isNotEmpty(storageLocation)) {
                                    ocBRefundPackageTask1.setWarehouseCode(storageLocation);
                                }
                            }
                        }
                    }
                }
                OmsStorageUtils.setBModelDefalutData(ocBRefundPackageTask1, R3SystemUserResource.getSystemRootUser());
                ocBRefundPackageTaskMapper.insert(ocBRefundPackageTask1);
            }
        } catch (Exception e) {
            redisMasterTemplate.delete(lockKsy);
            log.error(LogUtil.format("退货包裹WMS回传异常:{}"
                    , "OmsWmsBackRefundPackageTask apiProcess.error")
                    , Throwables.getStackTraceAsString(e));
            return new ValueHolderV14<>(ResultCode.FAIL, e.getMessage());
        }
        return new ValueHolderV14(ResultCode.SUCCESS, Resources.getMessage("success"));
    }

    private CpCPhyWarehouse queryWarehouseByWmsWarehouseCode(String wmsCode) {
        CusRedisTemplate<String, String> redisTemplate = RedisOpsUtil.getStrRedisTemplate();
        String redisKey = RedisKeyConstans.CP_WAREHOUSE_BY_WMS_CODE_JSON + wmsCode;
        String warehouseStr = redisTemplate.opsForValue().get(redisKey);

        CpCPhyWarehouse warehouse = null;
        if (!org.springframework.util.StringUtils.isEmpty(warehouseStr)) {
            warehouse = JSONObject.parseObject(warehouseStr, CpCPhyWarehouse.class);
            log.info(LogUtil.format("根据wmsCode查询redis仓库信息，warehouse:{}"
                    , "根据wmsCode查询redis仓库信息"), JSON.toJSONString(warehouse));
        }
        if (warehouse == null) {
            ValueHolderV14<CpCPhyWarehouse> v14 = cpcPhyWareHouseQueryCmd.queryWarehouseByWmsWarehouseCode(wmsCode);
            if (v14.isOK() && v14.getData() != null) {
                warehouse = v14.getData();
                redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(warehouse));
                redisTemplate.expire(redisKey, 30L, TimeUnit.SECONDS);
            }
            log.info(LogUtil.format("根据wmsCode查询mysql仓库信息，warehouse:{}"
                    , "根据wmsCode查询mysql仓库信息"), JSON.toJSONString(warehouse));
        }
        return warehouse;

    }

}
