package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.TypeUtils;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.es.util.ESHolder;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.request.OrderQueryRequest;
import com.jackrain.nea.util.ApplicationContextHandle;
import io.searchbox.client.JestClient;
import io.searchbox.core.Search;
import io.searchbox.core.SearchResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.common.Strings;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.join.query.JoinQueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/5/24
 */
@Slf4j
public class ElasticSearchUtil4Csm {

    private static final List<String> childTypeKeys = Lists.newArrayList("oc_b_order_item", "oc_b_order_item_ext");

    /**
     * deal valid process
     */
    private static final Function<String, JSONObject> invalidFun = s -> {
        ESHolder valueHolder = new ESHolder();
        valueHolder.put("code", -1);
        valueHolder.put("message", s);
        return valueHolder.toJSONObject();
    };

    public static JSONObject orderListSearch(String index, String type, OrderQueryRequest var) {
        int range = var.getSize();
        int startIndex = var.getStart();
        JSONArray orderKeys = var.getOrder();
        String[] returnFiled = var.getField();
        JSONObject filterKeys = var.getFilter();
        JSONArray searchKeys = var.getWhereKeys();
        JSONObject existKeys = var.getExistKeys();
        JSONObject notExistKeys = var.getNotExistKeys();
        boolean ocBOrderItemExt = var.getChild().getJSONObject("oc_b_order_item_ext").size() != 0;
        index = transferIndex(index);

        if (null == returnFiled) {
            return invalidFun.apply("Missing Return Value");
        }

        ESHolder valueHolder = new ESHolder();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();

        // 调用两次
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();

        boolean cellResult = choiceWay2Search("oc_b_order_item", searchKeys, filterKeys, mainQueryBuilder, true, true, queryBuilder);
        if (cellResult) {
            return invalidFun.apply("Mission Cell Search Parameter");
        }
        if (ocBOrderItemExt) {
            cellResult = choiceWay2Search("oc_b_order_item_ext", searchKeys, filterKeys, mainQueryBuilder, true, false, queryBuilder);
            if (cellResult) {
                return invalidFun.apply("Mission Cell Search Parameter");
            }
        }
        BoolQueryBuilder builder = QueryBuilders.boolQuery();
        BoolQueryBuilder shouldBuilder = (BoolQueryBuilder) mainQueryBuilder.should().get(0);
//        shouldBuilder.must(QueryBuilders.existsQuery("CARPOOL_NO"));
        if (existKeys != null && existKeys.size() > 0) {
            if (existKeys.containsKey("IS_CARPOOL")) {
                shouldBuilder.must(QueryBuilders.existsQuery("CARPOOL_NO"));
            }
        }
        if (notExistKeys != null && notExistKeys.size() > 0) {
            if (notExistKeys.containsKey("IS_CARPOOL")) {
                shouldBuilder.mustNot(QueryBuilders.existsQuery("CARPOOL_NO"));
            }
        }

        /**
         * order search
         */
        sortQuery(orderKeys, searchSourceBuilder);

        /**
         * range
         */
        range = range == 0 ? 20 : range;
        searchSourceBuilder.from(startIndex).size(range);

        // add
        searchSourceBuilder.query(mainQueryBuilder);
        JestClient jestClient = ApplicationContextHandle.getBean(JestClient.class);
        return doQuery(index, type, returnFiled, jestClient, valueHolder, searchSourceBuilder);
    }

    /**
     * es search
     *
     * @param index
     * @param type
     * @param childType
     * @return
     */
    public static JSONObject search(String index, String type, String childType, OrderQueryRequest var) {

        int range = var.getSize();
        int startIndex = var.getStart();
        JSONArray orderKeys = var.getOrder();
        String[] returnFiled = var.getField();
        JSONObject filterKeys = var.getFilter();
        JSONArray searchKeys = var.getWhereKeys();

        index = transferIndex(index);

        if (null == returnFiled) {
            return invalidFun.apply("Missing Return Value");
        }

        ESHolder valueHolder = new ESHolder();
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();

        boolean cellResult = choiceWay2Search(childType, searchKeys, filterKeys, mainQueryBuilder, false, true, null);
        if (cellResult) {
            return invalidFun.apply("Mission Cell Search Parameter");
        }
        /**
         * filter search
         */
        //   filterQuery(filterKeys, mainQueryBuilder);

        /**
         * order search
         */
        sortQuery(orderKeys, searchSourceBuilder);

        /**
         * range
         */
        range = range == 0 ? 20 : range;
        searchSourceBuilder.from(startIndex).size(range);

        // add
        searchSourceBuilder.query(mainQueryBuilder);

        JestClient jestClient = ApplicationContextHandle.getBean(JestClient.class);
        return doQuery(index, type, returnFiled, jestClient, valueHolder, searchSourceBuilder);

    }

    /**
     * primary
     *
     * @param whereKeys
     * @param queryBuilder
     * @param filterKeys
     */
    private static void primaryQuery(JSONObject whereKeys, BoolQueryBuilder queryBuilder, JSONObject filterKeys, boolean isRunFilter) {
        if (whereKeys != null) {
            Set<String> keySet = whereKeys.keySet();
            for (String key : keySet) {
                String val = whereKeys.getString(key);
                JSONArray jsnAry = new JSONArray();
                boolean sign = true;
                if (val != null) {
                    try {
                        jsnAry = JSON.parseArray(val);
                    } catch (Exception ex1) {
                        sign = false;
                    }
                } else {
                    sign = false;
                }

                if (sign) {
                    if (jsnAry != null && jsnAry.size() > 2000) {
                        BoolQueryBuilder batchQueryBuilder = getBoolQueryBuilder(key, jsnAry);
                        queryBuilder.must(batchQueryBuilder);
                    } else if (jsnAry != null && jsnAry.size() > 0) {
                        String secVal = jsnAry.getString(0);
                        if (secVal.contains(",")) {
                            BoolQueryBuilder secBoolQueryBuilder = QueryBuilders.boolQuery();
                            for (int i = 0; i < jsnAry.size(); i++) {
                                secBoolQueryBuilder.should(QueryBuilders.wildcardQuery(key, "*," + jsnAry.getString(i) + "*"));
                            }
                            queryBuilder.must(secBoolQueryBuilder);
                        } else if (secVal.contains("!=")) {
                            for (int i = 0; i < jsnAry.size(); i++) {
                                String eachStr = jsnAry.getString(i);
                                if (eachStr != null && eachStr.contains("!=")) {
                                    eachStr = eachStr.replace("!=", "");
                                    queryBuilder.mustNot(QueryBuilders.matchQuery(key, eachStr));
                                    continue;
                                }
                            }
                        } else if (!secVal.contains("*")) {
                            BoolQueryBuilder secBoolQueryBuilder = QueryBuilders.boolQuery();
                            BoolQueryBuilder secBoolQuery = QueryBuilders.boolQuery();
                            secBoolQuery.must(QueryBuilders.termsQuery(key, jsnAry));
                            secBoolQueryBuilder.should(secBoolQuery);
                            for (int i = 0; i < jsnAry.size(); i++) {
                                String str = jsnAry.getString(i);
                                if (StringUtils.isNotEmpty(str) && OcBOrderConst.IS_NULL.equals(str)) {
                                    BoolQueryBuilder secExistBoolQuery = QueryBuilders.boolQuery();
                                    secExistBoolQuery.mustNot(QueryBuilders.existsQuery(key));
                                    secBoolQueryBuilder.should(secExistBoolQuery);
                                }
                            }
                            queryBuilder.must(secBoolQueryBuilder);
                        } else {
                            BoolQueryBuilder secBoolQueryBuilder = QueryBuilders.boolQuery();
                            for (int i = 0; i < jsnAry.size(); i++) {
                                secBoolQueryBuilder.should(QueryBuilders.wildcardQuery(key, jsnAry.getString(i)));
                            }
                            queryBuilder.must(secBoolQueryBuilder);
                        }
                    }

                } else if (val == null) {
                    queryBuilder.mustNot(QueryBuilders.existsQuery(key));
                } else if (val.contains("!*")) {
                    queryBuilder.mustNot(QueryBuilders.wildcardQuery(key, (val.replaceAll("!*", ""))));
                } else if (val.contains("*")) {
                    queryBuilder.must(QueryBuilders.wildcardQuery(key, val));
                } else if (val.contains("!=")) {
                    queryBuilder.mustNot(QueryBuilders.matchQuery(key, val.replaceAll("!=", "")));
                } else {
                    queryBuilder.must(QueryBuilders.matchQuery(key, val));
                }
            }
        }
        if (isRunFilter) {
            filterQuery(filterKeys, queryBuilder);
        }
    }

    /**
     * multi search
     *
     * @param key
     * @param jsnAry
     * @return
     */
    private static BoolQueryBuilder getBoolQueryBuilder(String key, JSONArray jsnAry) {
        BoolQueryBuilder batchQueryBuilder = QueryBuilders.boolQuery();
        int n = jsnAry.size() / 2000;
        for (int i = 0; i < n; i++) {
            jsnAry.add((i + 1) * 2000, "p");
        }
        String batchString = jsnAry.toJSONString().replace("[", "").replaceAll("]", "");
        String[] batchAry = batchString.split("p");

        for (int i = 0, l = batchAry.length; i < l; i++) {
            String eachStr = batchAry[i];
            StringBuilder sb = new StringBuilder();
            sb.append("[");
            if (i == 0) {
                eachStr = eachStr.substring(0, eachStr.length() - 1);
            } else if (i > 0 && i < batchAry.length - 1) {
                eachStr = eachStr.substring(0, eachStr.length() - 1).substring(1);
            } else {
                eachStr = eachStr.substring(1);
            }

            sb.append(eachStr).append("]");
            JSONArray eachAry = JSONArray.parseArray(sb.toString());
            batchQueryBuilder.should(QueryBuilders.termsQuery(key, eachAry));
        }
        return batchQueryBuilder;
    }

    /**
     * child
     *
     * @param childType
     * @param childKeys
     * @param queryBuilder
     * @return
     */
    private static boolean childQuery(String childType, JSONObject childKeys, BoolQueryBuilder queryBuilder,
                                      JSONObject filterKeys, boolean isRunFilter) {
        if (childKeys == null) {
            return false;
        }
        if (StringUtils.isBlank(childType)) {

        }
        JSONArray jsnAry;
        Set<String> keySet = childKeys.keySet();
        for (String key : keySet) {
            String val = childKeys.getString(key);
            jsnAry = new JSONArray();
            boolean sign = true;
            if (val != null) {
                try {
                    jsnAry = JSON.parseArray(val);
                } catch (Exception ex2) {
                    sign = false;
                }
            } else {
                sign = false;
            }

            if (sign) {
                if (jsnAry != null && jsnAry.size() > 2000) {
                    BoolQueryBuilder batchQueryBuilder = getBoolQueryBuilder(key, jsnAry);
                    queryBuilder.must(JoinQueryBuilders.hasChildQuery(childType, batchQueryBuilder, ScoreMode.None));
                } else if (jsnAry != null && jsnAry.size() > 0) {
                    queryBuilder.must(JoinQueryBuilders.hasChildQuery(childType, QueryBuilders.termsQuery(key, jsnAry), ScoreMode.None));
                }

            } else if (val == null) {
                queryBuilder.mustNot(JoinQueryBuilders.hasChildQuery(childType, QueryBuilders.existsQuery(key), ScoreMode.None));
            } else if (val.contains("!*")) {
                val = val.replace("!*", "");
                queryBuilder.mustNot(JoinQueryBuilders.hasChildQuery(childType, QueryBuilders.wildcardQuery(key, "*" + val), ScoreMode.None));
            } else if (val.contains("*")) {
                queryBuilder.must(JoinQueryBuilders.hasChildQuery(childType, QueryBuilders.wildcardQuery(key, val), ScoreMode.None));
            } else if (val.contains("!=")) {
                val = val.replace("!=", "");
                queryBuilder.mustNot(JoinQueryBuilders.hasChildQuery(childType, QueryBuilders.matchQuery(key, val), ScoreMode.None));
            } else if (val.contains("~")) {
                String[] sAry = val.split("~");
                // 严格格式
                if (sAry[0] == null || sAry[1] == null) {
                    continue;
                }
                String startDt = sAry[0];
                String endDt = sAry[1];
                QueryBuilder rangeQuery = QueryBuilders.rangeQuery(key).from(startDt).to(endDt);
                queryBuilder.filter(JoinQueryBuilders.hasChildQuery(childType, rangeQuery, ScoreMode.None));
            } else {
                queryBuilder.must(JoinQueryBuilders.hasChildQuery(childType, QueryBuilders.matchQuery(key, val), ScoreMode.None));
            }
        }
        if (isRunFilter) {
            filterQuery(filterKeys, queryBuilder);
        }

        return true;
    }

    /**
     * filter
     *
     * @param filterKeys
     * @param mainQueryBuilder
     */
    private static void filterQuery(JSONObject filterKeys, BoolQueryBuilder mainQueryBuilder) {
        if (filterKeys == null || filterKeys.size() < 1) {
            return;
        }
        String v1, v2;
        Set<String> keySet = filterKeys.keySet();
        for (String key : keySet) {
            v2 = null;
            String origin = filterKeys.getString(key);
            String[] split = origin.split("~");
            if (split.length == 0) {
                continue;
            } else if (split.length < 2) {
                v1 = split[0];
            } else {
                v1 = split[0];
                v2 = split[1];
            }
            RangeQueryBuilder rangeQueryBuilder = new RangeQueryBuilder(key);
            try {
                if (StringUtils.isNotBlank(v1)) {
                    rangeQueryBuilder.gte(v1);
                }
                if (StringUtils.isNotBlank(v2)) {
                    rangeQueryBuilder.lte(v2);
                }
            } catch (Exception e) {

            }
            mainQueryBuilder.filter(rangeQueryBuilder);
        }

    }

    /**
     * sorter
     *
     * @param orderKeys
     * @param searchSourceBuilder
     */
    private static void sortQuery(JSONArray orderKeys, SearchSourceBuilder searchSourceBuilder) {
        if (orderKeys != null && orderKeys.size() > 0) {
            for (int i = 0; i < orderKeys.size(); i++) {
                JSONObject sortJsn = JSON.parseObject(TypeUtils.castToString(orderKeys.get(i)));
                if (sortJsn != null && sortJsn.size() > 0) {
                    boolean order = sortJsn.getBooleanValue("asc");
                    String val = sortJsn.getString("name");
                    if (StringUtils.isNotBlank(val) && val.indexOf(".") != -1) {
                        val = val.split(".")[1];
                    }
                    SortBuilder sortBuilder;
                    if (order) {
                        sortBuilder = SortBuilders.fieldSort(val).order(SortOrder.ASC);
                        searchSourceBuilder.sort(sortBuilder);
                        continue;
                    }
                    sortBuilder = SortBuilders.fieldSort(val).order(SortOrder.DESC);
                    searchSourceBuilder.sort(sortBuilder);
                }
            }
        }
    }

    private static boolean parentKey2Search(JSONArray searchKeys, JSONObject filterKeys, BoolQueryBuilder mainQueryBuilder) {
        boolean hasWhereSearch = false;

        if (searchKeys != null) {
            List<Object> collect = searchKeys.stream().filter(x -> ((JSONObject) x).size() > 0).collect(Collectors.toList());
            if (collect.size() > 0) {
                hasWhereSearch = true;
            }
        }
        int loopCell = searchKeys.size();
        if (hasWhereSearch) {
            for (int i = 0; i < loopCell; i++) {
                boolean isRunFilter = true;
                JSONObject cellJsn = searchKeys.getJSONObject(i);
                JSONArray whereKeys = cellJsn.getJSONArray("parentKey");
                if (CollectionUtils.isEmpty(whereKeys)) {
                    continue;
                }
                BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
                for (int k = 0; k < whereKeys.size(); k++) {
                    JSONObject whereKey = whereKeys.getJSONObject(k);
                    primaryQuery(whereKey, queryBuilder, filterKeys, isRunFilter);
                    isRunFilter = false;
                }
                mainQueryBuilder.should(queryBuilder);
            }
        } else {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            filterQuery(filterKeys, queryBuilder);
            mainQueryBuilder.should(queryBuilder);
        }
        return false;
    }

    private static boolean childKey2Search(JSONArray searchKeys, JSONObject filterKeys, BoolQueryBuilder mainQueryBuilder) {

        boolean hasWhereSearch = false;

        if (searchKeys != null) {
            List<Object> collect = searchKeys.stream().filter(x -> ((JSONObject) x).size() > 0).collect(Collectors.toList());
            if (collect.size() > 0) {
                hasWhereSearch = true;
            }
        }
        int loopCell = searchKeys.size();
        if (hasWhereSearch) {
            for (int i = 0; i < loopCell; i++) {
                boolean isRunFilter = true;
                for (int k = 0; k < childTypeKeys.size(); k++) {
                    String childType = childTypeKeys.get(k);
                    JSONObject cellJsn = searchKeys.getJSONObject(i);
                    JSONArray childKeys = new JSONArray();
                    JSONArray childArray = cellJsn.getJSONArray("childKey");
                    if (CollectionUtils.isNotEmpty(childArray)) {
                        JSONObject jsonObject = childArray.getJSONObject(0);
                        JSONObject childObject = jsonObject.getJSONObject(childType);
                        childKeys.add(childObject);
                    }

                    if (CollectionUtils.isEmpty(childKeys)) {
                        continue;
                    }

                    BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
                    for (int m = 0; m < childKeys.size(); m++) {
                        JSONObject childKey = childKeys.getJSONObject(m);
                        childQuery(childType, childKey, queryBuilder, filterKeys, isRunFilter);
                        isRunFilter = false;
                    }
                    mainQueryBuilder.should(queryBuilder);
                }
            }
        } else {
            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            filterQuery(filterKeys, queryBuilder);
            mainQueryBuilder.should(queryBuilder);
        }
        return false;
    }

    /**
     * primary search process that : the way to filter when it has no where key  that with special query
     *
     * @param childType
     * @param searchKeys
     * @param filterKeys
     * @param mainQueryBuilder
     * @return
     */
    private static boolean choiceWay2Search(String childType, JSONArray searchKeys, JSONObject filterKeys, BoolQueryBuilder mainQueryBuilder, boolean newQuery, boolean dealParentKey, BoolQueryBuilder queryBuilder) {

        boolean hasWhereSearch = false;

        if (searchKeys != null) {
            List<Object> collect = searchKeys.stream().filter(x -> ((JSONObject) x).size() > 0).collect(Collectors.toList());
            if (collect.size() > 0) {
                hasWhereSearch = true;
            }
        }
        int loopCell = searchKeys.size();
        if (hasWhereSearch) {
            for (int i = 0; i < loopCell; i++) {
                boolean isRunFilter = true;
                boolean newBoolQuery = true;
                JSONObject cellJsn = searchKeys.getJSONObject(i);
                JSONArray whereKeys = new JSONArray();
                if (dealParentKey) {
                    whereKeys = cellJsn.getJSONArray("parentKey");
                }
                JSONArray childKeys = new JSONArray();
                if (newQuery) {
                    JSONArray childArray = cellJsn.getJSONArray("childKey");
                    if (CollectionUtils.isNotEmpty(childArray)) {
                        JSONObject jsonObject = childArray.getJSONObject(0);
                        JSONObject childObject = jsonObject.getJSONObject(childType);
                        if (childObject.size() != 0) {
                            childKeys.add(childObject);
                        }
                        if (jsonObject.getJSONObject("oc_b_order_item_ext").size() != 0) {
                            newBoolQuery = false;
                        }
                    }
                } else {
                    childKeys = cellJsn.getJSONArray("childKey");
                }
                if (newBoolQuery) {
                    queryBuilder = QueryBuilders.boolQuery();
                }

                if (CollectionUtils.isEmpty(whereKeys) && CollectionUtils.isEmpty(childKeys)) {
                    continue;
                }
                if (whereKeys != null) {
                    for (int k = 0; k < whereKeys.size(); k++) {
                        JSONObject whereKey = whereKeys.getJSONObject(k);
                        primaryQuery(whereKey, queryBuilder, filterKeys, isRunFilter);
                        isRunFilter = false;
                    }
                }

                if (childKeys != null) {
                    for (int m = 0; m < childKeys.size(); m++) {
                        JSONObject childKey = childKeys.getJSONObject(m);
                        childQuery(childType, childKey, queryBuilder, filterKeys, isRunFilter);
                        isRunFilter = false;
                    }
                }
                mainQueryBuilder.should(queryBuilder);
            }
        } else {
            if (queryBuilder == null) {
                queryBuilder = QueryBuilders.boolQuery();
            }
            filterQuery(filterKeys, queryBuilder);
            mainQueryBuilder.should(queryBuilder);
        }
        return false;
    }

    /**
     * execute search
     *
     * @param index
     * @param type
     * @param returnFiled
     * @param jestClient
     * @param valueHolder
     * @param searchSourceBuilder
     * @return
     */
    private static JSONObject doQuery(String index, String type, String[] returnFiled, JestClient jestClient,
                                      ESHolder valueHolder, SearchSourceBuilder searchSourceBuilder) {
        FetchSourceContext sourceContext = new FetchSourceContext(true, returnFiled, Strings.EMPTY_ARRAY);
        searchSourceBuilder.fetchSource(sourceContext);
        Search search = (new Search.Builder(searchSourceBuilder.toString())).addIndex(index).addType(type).build();
        JSONArray returnArray;
        String jsonObject;
        try {
            SearchResult result = jestClient.execute(search);
            log.info("=======ElasticSearchUtil4Csm.doQuery:{}", searchSourceBuilder);
            if (result.isSucceeded()) {
                JsonArray jsonArray = result.getJsonObject().getAsJsonObject("hits").getAsJsonArray("hits");
                returnArray = new JSONArray();
                if (null != jsonArray && jsonArray.size() > 0) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        jsonObject = jsonArray.get(i).getAsJsonObject().getAsJsonObject("_source").toString();
                        JSONObject returnJSON = JSONObject.parseObject(jsonObject);
                        returnArray.add(returnJSON);
                    }
                    valueHolder.put("rowcount", jsonArray.size());
                } else {
                    valueHolder.put("rowcount", 0);
                }
                valueHolder.put("data", returnArray);
                valueHolder.put("code", 0);
                valueHolder.put("total", result.getJsonObject().getAsJsonObject("hits").get("total").getAsBigInteger());
            } else {
                valueHolder.put("message", result.getErrorMessage());
                valueHolder.put("code", -1);
                valueHolder.put("total", 0);
            }
        } catch (IOException ex) {
            ex.printStackTrace();
        }
        return valueHolder.toJSONObject();
    }

    /**
     * config
     *
     * @param index
     * @return
     */
    private static String transferIndex(String index) {

        PropertiesConf propConf = ApplicationContextHandle.getBean(PropertiesConf.class);
        return null != propConf.getProperty("r3.es.index.prefix") ? propConf.getProperty("r3.es.index.prefix")
                .toLowerCase() + "_" + index : index;
    }

}
