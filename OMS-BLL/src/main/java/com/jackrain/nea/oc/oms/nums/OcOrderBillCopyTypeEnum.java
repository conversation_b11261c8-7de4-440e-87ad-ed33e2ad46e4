package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * 开票通知发票类型枚举
 *
 * @author: huang.zaizai
 * create at: 2019/7/23 19:20
 */
public enum OcOrderBillCopyTypeEnum {

    ORIGINAL_INVALID_COPY(1, "原单无效复制"),
    NORMAL_COPY(2, "正常复制"),
    NEW_RETURN(3,"新增退单"),
    UNKNOW(-1, "未知");

    OcOrderBillCopyTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    @Getter
    private Integer type;

    @Getter
    private String name;

    public static OcOrderBillCopyTypeEnum getCopyEnumByType(Integer type){
        for (OcOrderBillCopyTypeEnum value : OcOrderBillCopyTypeEnum.values()) {
            if (value.getType().equals(type)){
                return value;
            }
        }
        return UNKNOW;
    }
}


