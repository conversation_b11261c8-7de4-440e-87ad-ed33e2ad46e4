package com.jackrain.nea.oc.oms.services.task;


import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.enums.YesNoEnum;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.burgeon.r3.sg.inf.api.wms.in.SgBRefundInTaskCmd;
import com.burgeon.r3.sg.inf.common.SgInfConstants;
import com.burgeon.r3.sg.inf.model.request.wms.in.SgBRefundInTaskRequest;
import com.burgeon.r3.sg.inf.model.result.oms.SgBRefundInTaskResult;
import com.burgeon.r3.sg.store.common.SgStoreConstantsIF;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsDeliveryOrderConfig;
import com.jackrain.nea.oc.oms.mapper.task.OcBRefundInTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.ReturnInType;
import com.jackrain.nea.oc.oms.model.request.OmsRefundInSaveRequest;
import com.jackrain.nea.oc.oms.model.table.task.OcBRefundInTask;
import com.jackrain.nea.oc.oms.services.returnin.OcRefundInService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.impl.R3SystemUserResource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Auther: chenhao
 * @Date: 2022-07-16 10:43
 * @Description: 退换入库确认中间表定时任务
 */

@Slf4j
@Component
public class OmsRefundInTaskService {

    @Autowired
    private OcBRefundInTaskMapper inTaskMapper;

    @Reference(group = "sg", version = "1.0")
    SgBRefundInTaskCmd inTaskCmd;
    @Autowired
    private OcRefundInService inService;

    public ValueHolderV14 execute() {
        log.info(LogUtil.format("退换入库确认中间表定时转化任务-开始", "OmsRefundInTaskService.execute"));
        ValueHolderV14 v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "执行成功！");

        List<String> wmsTypeList = new ArrayList<>();
        wmsTypeList.add(ThirdWmsTypeEnum.QMWMS.getCode());
        wmsTypeList.add(ThirdWmsTypeEnum.JDWMS.getCode());
        wmsTypeList.add(ThirdWmsTypeEnum.DBWMS.getCode());
        wmsTypeList.add(ThirdWmsTypeEnum.FLWMS.getCode());
        wmsTypeList.add(ThirdWmsTypeEnum.JYWMS.getCode());
        //查数据
        List<OcBRefundInTask> inTaskList = inTaskMapper.selectList(new LambdaQueryWrapper<OcBRefundInTask>()
                .lt(OcBRefundInTask::getFailedCount, SgConstantsIF.FAIL_COUNT)
                .ne(OcBRefundInTask::getBillStatus, SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS)
                .in(OcBRefundInTask::getWmsWarehouseType, wmsTypeList)
                .eq(OcBRefundInTask::getIsactive, YesNoEnum.Y.getKey()));
        if (CollectionUtils.isEmpty(inTaskList)) {
            v14.setMessage("无处理数据！");
            return v14;
        }

        try {
            List<OcBRefundInTask> normalList = inTaskList.stream().filter(s -> !(ThirdWmsTypeEnum.FLWMS.getCode().equals(s.getWmsWarehouseType()))).collect(Collectors.toList());
            List<OcBRefundInTask> flList = inTaskList.stream().filter(s -> ThirdWmsTypeEnum.FLWMS.getCode().equals(s.getWmsWarehouseType())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(normalList)) {
                executeForNormal(normalList);
            }
            if (CollectionUtils.isNotEmpty(flList)) {
                executeForFl(flList);
            }
        } catch (NDSException e) {
            v14.setMessage(e.getMessage());
            v14.setCode(ResultCode.FAIL);
            return v14;
        }

        log.info(LogUtil.format("退换入库确认中间表定时转化任务-结束:{}", "OmsRefundInTaskService.execute"), JSON.toJSONString(v14));
        return v14;
    }

    /**
     * 处理业务（非富勒）
     *
     * @param inTaskList
     */
    private void executeForNormal(List<OcBRefundInTask> inTaskList) {
        List<SgBRefundInTaskRequest> requestLit = new ArrayList<>();
        HashMap<Long, OcBRefundInTask> inTaskHashMap = new HashMap<>(16);
        //调用sg check
        for (OcBRefundInTask inTask : inTaskList) {
            requestLit.add(buildRequest(inTask));
            inTaskHashMap.put(inTask.getId(), inTask);
        }
        log.info(LogUtil.format("OmsRefundInTaskService.check.size:{} ",
                "OmsRefundInTaskService.execute"), requestLit.size());
        ValueHolderV14<SgBRefundInTaskResult> execute = inTaskCmd.execute(requestLit);
        log.info(LogUtil.format("OmsRefundInTaskService.check.ValueHolderV14:{} ",
                "OmsRefundInTaskService.execute"), JSONObject.toJSONString(execute));

        SgBRefundInTaskResult data = execute.getData();
        if (data != null) {
            //check 有问题的数据更新
            List<SgBRefundInTaskRequest> failList = data.getFailList();
            if (CollectionUtils.isNotEmpty(failList)) {
                for (SgBRefundInTaskRequest inTaskRequest : failList) {
                    //id 封装参数d时候就传过去了
                    updateFail(inTaskHashMap.get(inTaskRequest.getId()), inTaskRequest.getFailedReason());
                }
            }

            //成功的走下面逻辑
            List<SgBRefundInTaskRequest> successList = data.getSuccessList();
            if (CollectionUtils.isNotEmpty(successList)) {
                for (SgBRefundInTaskRequest inTaskRequest : successList) {
                    OcBRefundInTask inTask = inTaskHashMap.get(inTaskRequest.getId());
                    try {
                        String orderType = inTask.getOrderType();
                        if (OmsDeliveryOrderConfig.OREDER_TYPE_B2BRK.equals(orderType) ||
                                OmsDeliveryOrderConfig.OREDER_TYPE_THRK.equals(orderType)) {

                            // 新增更新退货入库结果单
                            OmsRefundInSaveRequest omsRefundInSaveRequest = buildRequest(inTask.getMsg());
                            if (OmsDeliveryOrderConfig.OREDER_TYPE_B2BRK.equals(orderType)) {
                                omsRefundInSaveRequest.setReturnInType(ReturnInType.NORM2B.val());
                            } else {
                                omsRefundInSaveRequest.setReturnInType(ReturnInType.NORM2C.val());
                            }

                            log.info(LogUtil.format("新增退货入库结果单，入参:{}",
                                    "OmsRefundInTaskService.execute"), JSONObject.toJSONString(omsRefundInSaveRequest));
                            ValueHolderV14<String> save = inService.save(omsRefundInSaveRequest);
                            log.info(LogUtil.format("新增退货入库结果单，结果:{}",
                                    "OmsRefundInTaskService.execute"), JSONObject.toJSONString(save));

                            if (!save.isOK()) {
                                updateFail(inTask, save.getMessage());
                                continue;
                            }
                        } else if (OmsDeliveryOrderConfig.OREDER_TYPE_GCXT.equals(orderType) || SgInfConstants.OREDER_TYPE_WTJRK.equals(orderType)) {
                            //库存调整单
                            log.info(LogUtil.format("新增并审核库存调整单，入参:{}",
                                    "OmsRefundInTaskService.execute"), JSONObject.toJSONString(inTask));
                            ValueHolderV14<SgR3BaseResult> v141 = inTaskCmd.adjustSaveAndSubmit(buildRequest(inTask));
                            log.info(LogUtil.format("新增并审核库存调整单，结果:{}",
                                    "OmsRefundInTaskService.execute"), JSONObject.toJSONString(v141));
                            if (!v141.isOK()) {
                                updateFail(inTask, v141.getMessage());
                                continue;
                            }
                            //更新退换货单
                            SgR3BaseResult v141Data = v141.getData();
                            inService.updateVirtualStatus(v141Data.getBillId(), R3SystemUserResource.getSystemRootUser());
                        }

                        //都成功记录成功
                        updateSuccess(inTask);
                    } catch (Exception e) {
                        log.error(LogUtil.format("退换入库确认中间表定时转化任务,中间表ID:{},异常：{}",
                                "OmsRefundInTaskService.execute"), inTask.getId(), Throwables.getStackTraceAsString(e));
                        updateFail(inTask, e.getMessage());
                    }
                }
            }
        } else {
            throw new NDSException("检查有误，无返回值");
        }
    }

    /**
     * 处理业务（富勒）
     *
     * @param inTaskList
     */
    private void executeForFl(List<OcBRefundInTask> inTaskList) {
        List<SgBRefundInTaskRequest> requestLit = new ArrayList<>();
        HashMap<Long, OcBRefundInTask> inTaskHashMap = new HashMap<>(16);
        //调用sg check
        for (OcBRefundInTask inTask : inTaskList) {
            requestLit.add(buildRequest(inTask));
            inTaskHashMap.put(inTask.getId(), inTask);
        }
        log.info(LogUtil.format("OmsRefundInTaskService.check.size:{} ",
                "OmsRefundInTaskService.execute"), requestLit.size());
        ValueHolderV14<SgBRefundInTaskResult> execute = inTaskCmd.executeForFl(requestLit);
        log.info(LogUtil.format("OmsRefundInTaskService.check.ValueHolderV14:{} ",
                "OmsRefundInTaskService.execute"), JSONObject.toJSONString(execute));

        SgBRefundInTaskResult data = execute.getData();
        if (data != null) {
            //check 有问题的数据更新
            List<SgBRefundInTaskRequest> failList = data.getFailList();
            if (CollectionUtils.isNotEmpty(failList)) {
                for (SgBRefundInTaskRequest inTaskRequest : failList) {
                    //id 封装参数d时候就传过去了
                    updateFail(inTaskHashMap.get(inTaskRequest.getId()), inTaskRequest.getFailedReason());
                }
            }

            //成功的走下面逻辑
            List<SgBRefundInTaskRequest> successList = data.getSuccessList();
            if (CollectionUtils.isNotEmpty(successList)) {
                for (SgBRefundInTaskRequest inTaskRequest : successList) {
                    OcBRefundInTask inTask = inTaskHashMap.get(inTaskRequest.getId());
                    try {
                        String orderType = inTask.getOrderType();
                        if (OmsDeliveryOrderConfig.OREDER_TYPE_B2BRK.equals(orderType) ||
                                OmsDeliveryOrderConfig.OREDER_TYPE_THRK.equals(orderType)) {

                            // 新增更新退货入库结果单
                            OmsRefundInSaveRequest omsRefundInSaveRequest = buildRequest(inTask.getMsg());
                            if (OmsDeliveryOrderConfig.OREDER_TYPE_B2BRK.equals(orderType)) {
                                omsRefundInSaveRequest.setReturnInType(ReturnInType.NORM2B.val());
                            } else {
                                omsRefundInSaveRequest.setReturnInType(ReturnInType.NORM2C.val());
                            }

                            log.info(LogUtil.format("新增退货入库结果单，入参:{}",
                                    "OmsRefundInTaskService.execute"), JSONObject.toJSONString(omsRefundInSaveRequest));
                            ValueHolderV14<String> save = inService.save(omsRefundInSaveRequest);
                            log.info(LogUtil.format("新增退货入库结果单，结果:{}",
                                    "OmsRefundInTaskService.execute"), JSONObject.toJSONString(save));

                            if (!save.isOK()) {
                                updateFail(inTask, save.getMessage());
                                continue;
                            }
                        } else if (OmsDeliveryOrderConfig.OREDER_TYPE_GCXT.equals(orderType) || SgInfConstants.OREDER_TYPE_WTJRK.equals(orderType)) {
                            //库存调整单
                            log.info(LogUtil.format("新增并审核库存调整单，入参:{}",
                                    "OmsRefundInTaskService.execute"), JSONObject.toJSONString(inTask));
                            ValueHolderV14<SgR3BaseResult> v141 = inTaskCmd.adjustSaveAndSubmit(buildRequest(inTask));
                            log.info(LogUtil.format("新增并审核库存调整单，结果:{}",
                                    "OmsRefundInTaskService.execute"), JSONObject.toJSONString(v141));
                            if (!v141.isOK()) {
                                updateFail(inTask, v141.getMessage());
                                continue;
                            }
                            //更新退换货单
                            SgR3BaseResult v141Data = v141.getData();
                            inService.updateVirtualStatus(v141Data.getBillId(), R3SystemUserResource.getSystemRootUser());
                        }

                        //都成功记录成功
                        updateSuccess(inTask);
                    } catch (Exception e) {
                        log.error(LogUtil.format("退换入库确认中间表定时转化任务,中间表ID:{},异常：{}",
                                "OmsRefundInTaskService.execute"), inTask.getId(), Throwables.getStackTraceAsString(e));
                        updateFail(inTask, e.getMessage());
                    }
                }
            }
        } else {
            throw new NDSException("检查有误，无返回值");
        }
    }

    /**
     * 新增退货入库结果单参数
     *
     * @param msg 报文
     * @return OmsRefundInSaveRequest
     */
    private OmsRefundInSaveRequest buildRequest(String msg) {
        JSONObject request = JSONObject.parseObject(msg);
        JSONObject returnOrder = request.getJSONObject("returnOrder");


        OmsRefundInSaveRequest.ReturnOrder order = new OmsRefundInSaveRequest.ReturnOrder();
        String orderConfirmTime = returnOrder.getString("orderConfirmTime");
        if (StringUtils.isEmpty(orderConfirmTime)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            orderConfirmTime = format.format(new Date());
        }
        //入库日期
        order.setOrderConfirmTime(orderConfirmTime);
        //入库通知单（原退换单号）
        order.setReturnOrderCode(returnOrder.getString("returnOrderCode"));
        //入库仓库
        order.setWarehouseCode(returnOrder.getString("warehouseCode"));
        /*库位编码*/
        JSONObject location = request.getJSONObject("extendProps");
        if (Objects.nonNull(location)) {
            order.setStorageLocation(location.getString("storageLocation"));
        }

        //物流公司
        order.setLogisticsCode(returnOrder.getString("logisticsCode"));
        JSONObject senderInfo = returnOrder.getJSONObject("senderInfo");
        if (Objects.isNull(senderInfo)) {
            senderInfo = new JSONObject();
        }

        OmsRefundInSaveRequest.SenderInfo info = new OmsRefundInSaveRequest.SenderInfo();
        //物流单号
        order.setExpressCode(returnOrder.getString("expressCode"));
        //手机号
        info.setMobile(senderInfo.getString("mobile"));
        //姓名
        info.setName(senderInfo.getString("name"));
        //发件地址
        info.setDetailAddress(senderInfo.getString("detailAddress"));
        order.setSenderInfo(info);

        //备注
        order.setRemark(returnOrder.getString("remark"));
        //WMS单据编号
        order.setReturnOrderId(returnOrder.getString("returnOrderId"));
        // 单据类型
        order.setOrderType(returnOrder.getString("orderType"));

        JSONArray items = request.getJSONArray("orderLines");
        List<OmsRefundInSaveRequest.OrderLine> orderLineList = new ArrayList<>();
        items.forEach(item -> {
            JSONObject orderLine = (JSONObject) item;
            OmsRefundInSaveRequest.OrderLine line = new OmsRefundInSaveRequest.OrderLine();
            //条码
            line.setItemCode(orderLine.getString("itemCode"));
            //数量
            line.setActualQty(orderLine.getBigDecimal("actualQty"));
            //生产日期
            line.setBatchCode(orderLine.getString("batchCode"));
            //商品标记
            line.setInventoryType(orderLine.getString("inventoryType"));
            //类型
            JSONObject extendProps = orderLine.getJSONObject("extendProps");
            if (!Objects.isNull(extendProps)) {
                line.setAbnormalFlag(extendProps.getInteger("abnormalFlag"));
            }
            orderLineList.add(line);
        });

        OmsRefundInSaveRequest omsRefundInSaveRequest = new OmsRefundInSaveRequest();
        omsRefundInSaveRequest.setOrderLines(orderLineList);
        omsRefundInSaveRequest.setReturnOrder(order);

        return omsRefundInSaveRequest;
    }

    /**
     * 封装请求参数
     *
     * @param inTask inTask
     * @return SgBRefundInTaskRequest
     */
    private SgBRefundInTaskRequest buildRequest(OcBRefundInTask inTask) {
        SgBRefundInTaskRequest refundInTaskRequest = new SgBRefundInTaskRequest();
        refundInTaskRequest.setId(inTask.getId());
        refundInTaskRequest.setMessage(inTask.getMsg());
        refundInTaskRequest.setWarehouseCode(inTask.getWarehouseCode());
        refundInTaskRequest.setNoticesBillNo(inTask.getReturnBillNo());
        return refundInTaskRequest;
    }

    /**
     * 更新错误信息
     *
     * @param inTask       中间表数据
     * @param failedReason 失败原因
     */
    private void updateFail(OcBRefundInTask inTask, String failedReason) {

        OcBRefundInTask update = new OcBRefundInTask();
        update.setId(inTask.getId());
        int failedConut = Optional.ofNullable(inTask.getFailedCount()).orElse(0) + 1;
        update.setFailedCount(failedConut);
        update.setFailedReason(failedReason.length() > 500 ? failedReason.substring(500) : failedReason);
        update.setBillStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_FAILED);
        inTaskMapper.updateById(update);
    }

    /**
     * 更新成功信息
     *
     * @param inTask 中间表数据
     */
    private void updateSuccess(OcBRefundInTask inTask) {

        log.info(LogUtil.format("OmsRefundInTaskService.updateSuccess :{} ",
                "OmsRefundInTaskService.updateSuccess "), JSONObject.toJSONString(inTask));

        OcBRefundInTask update = new OcBRefundInTask();
        update.setId(inTask.getId());
        update.setBillStatus(SgStoreConstantsIF.WMS_TO_RESULT_STATUS_SUCCESS);
        update.setTransformationData(new Date());
        inTaskMapper.update(update, new LambdaUpdateWrapper<OcBRefundInTask>()
                .set(OcBRefundInTask::getFailedReason, null)
                .eq(OcBRefundInTask::getId, inTask.getId()));
    }
}
