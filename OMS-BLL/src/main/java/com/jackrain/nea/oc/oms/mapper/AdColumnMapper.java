package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.ad.model.AdColumn;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@Mapper
public interface AdColumnMapper extends ExtentionMapper<AdColumn> {
    @Select("select dbname,description,props from  ad_column where ad_table_id=#{id} and mask like \"____1_____\" and isactive ='Y' ORDER BY orderno asc")
    List<AdColumn> getForList(@PathVariable Long id);
}