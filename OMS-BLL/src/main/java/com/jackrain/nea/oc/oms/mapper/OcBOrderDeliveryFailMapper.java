package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBOrderDeliveryFailTaskSql;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDeliveryFail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderDeliveryFailMapper
 * @Description 平台发货失败记录
 * <AUTHOR>
 * @Date 2023/3/14 15:28
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderDeliveryFailMapper extends ExtentionMapper<OcBOrderDeliveryFail> {

    @Select("SELECT * FROM oc_b_order_delivery_fail WHERE OC_B_ORDER_ID = #{orderId} AND ISACTIVE = 'Y'")
    List<OcBOrderDeliveryFail> selectOrderDeliveryFailByOrderId(@Param("orderId") Long orderId);


    @SelectProvider(type = OcBOrderDeliveryFailTaskSql.class, method = "selectDeliveryFail4Retry")
    List<OcBOrderDeliveryFail> selectDeliveryFail4Retry(@Param(value = "limit") int limit,
                                                        @Param(value = "taskTableName") String taskTableName);

    @Update("<script> "
            + "UPDATE oc_b_order_delivery_fail SET STATUS = 1,modifieddate = now() where OC_B_ORDER_ID in "
            + "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void updateStatus(@Param("ids") List<Long> orderIds);
}
