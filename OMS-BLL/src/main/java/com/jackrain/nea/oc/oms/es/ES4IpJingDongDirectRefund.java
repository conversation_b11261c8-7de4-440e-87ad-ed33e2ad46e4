package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jingdongdirect.JingdongDirectOrderExt;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ES4IpJingDongDirectRefund
 * @Description 京东厂直
 * <AUTHOR>
 * @Date 2024/4/1 16:33
 * @Version 1.0
 */
public class ES4IpJingDongDirectRefund {

    public static JSONArray selectJingdongDirectRefundKey(int pageIndex, int pageSize) {

        List<String> list = new ArrayList<>();
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }

        JSONObject whereKeys = new JSONObject();
        whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());
        String[] returnFields = {"REFUND_ID", "ID"};
        JSONObject search = ElasticSearchUtil.search(JingdongDirectOrderExt.TABLENAM_IPBJINGDONGDIRECTREFUND,
                JingdongDirectOrderExt.TABLENAM_IPBJINGDONGDIRECTREFUND, whereKeys, null,
                null, pageSize, startIndex, returnFields);
        return search.getJSONArray("data");
    }

}
