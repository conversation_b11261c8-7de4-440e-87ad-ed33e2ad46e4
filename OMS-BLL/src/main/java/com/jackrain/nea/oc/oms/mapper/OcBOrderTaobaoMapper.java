package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderTaobao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

@Mapper
@Component
public interface OcBOrderTaobaoMapper extends ExtentionMapper<OcBOrderTaobao> {

    /**
     * @param tid tid
     * @return OcBOrderTaobao
     */
    @Select("SELECT * FROM oc_b_order_taobao WHERE tid=#{tid}")
    OcBOrderTaobao selectByTid(String tid);

    /**
     * @param id        订单ID
     * @param sysRemark 系统备注
     * @return int
     */
    @Update("UPDATE oc_b_order_taobao SET SYS_REMARK=#{sysRemark} WHERE id=#{id}")
    int updateMarkDesc(@Param("id") long id, @Param("sysRemark") String sysRemark);
}