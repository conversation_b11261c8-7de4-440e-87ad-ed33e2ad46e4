package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongReturnOrderExt;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 5:03 下午
 */

@Slf4j
public class ES4IpJingDongSaRefund {

    private ES4IpJingDongSaRefund() {
    }

    /**
     * 京东取消订单补偿服务 查询未转换的数据
     * ES获取京东取消订单中间表数据分库键
     * <p>
     * 根据转换状态获取退款单号id
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     *                  <p>
     *                  POPAFSREFUNDAPPLYID 退款单号id
     *                  TRANS 转换状态
     */
    public static List<Long> selectJdCancelOrder(int pageIndex, int pageSize) {
        List<Long> list = new ArrayList<>();
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "TRANSDATE");
        orderKes.put("asc", true);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);

        JSONObject whereKeys = new JSONObject();
        String[] returnFileds = {"POPAFSREFUNDAPPLYID"};
        whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());
        JSONObject search = ElasticSearchUtil.search(JingdongReturnOrderExt.TABLENAM_IPBJINGDONGSAREFUND,
                JingdongReturnOrderExt.TABLENAM_IPBJINGDONGSAREFUND, whereKeys, null,
                orderKeys, pageSize, startIndex, returnFileds);
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getLong("POPAFSREFUNDAPPLYID"));
            }
        }
        return list;
    }

}
