package com.jackrain.nea.oc.oms.services.invoice;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.core.model.result.SgR3BaseResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.hub.api.HubInvoicingCmd;
import com.jackrain.nea.oc.oms.dto.invoice.mcp.CheckOrderParamMcpDTO;
import com.jackrain.nea.oc.oms.gsi.GSI4OrderItem;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCInvoiceStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceSystemItemMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.request.AcFOrderInvoiceSaveRequest;
import com.jackrain.nea.oc.oms.model.result.InvoiceSaveCheckReceiverResult;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.BaseModel;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/09/12 13:20
 */
@Slf4j
@Component
public class AcFOrderInvoiceSaveService {

    @Autowired
    private AcFOrderInvoiceMapper acOrderInvoiceMapper;
    @Autowired
    private AcFInvoiceApplyItemMapper acInvoiceApplyItemMapper;
    @Autowired
    private AcFInvoiceApplyMapper acInvoiceApplyMapper;
    @Autowired
    private OcBOrderMapper ocOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocOrderItemMapper;
    @Autowired
    private StCInvoiceStrategyMapper stInvoiceStrategyMapper;
    @Autowired
    private AcFOrderInvoiceSystemItemMapper orderInvoiceSystemItemMapper;
    @Autowired
    private CpRpcService cpRpcService;

    @Reference(group = "hub", version = "1.0")
    private HubInvoicingCmd hubInvoicingCmd;

    /**
     * 申请开票模糊查询
     *
     * @param param tid
     * @return ValueHolderV14
     */
    public ValueHolderV14<PageInfo<OcBOrder>> queryTidFuzzy(JSONObject param) {
        log.info(LogUtil.format("AcFOrderInvoiceSaveService.queryTidFuzzy.start={}", "queryTidFuzzy"),
                JSONObject.toJSONString(param));
        ValueHolderV14<PageInfo<OcBOrder>> vh = new ValueHolderV14<>(ResultCode.FAIL, "查询成功!");
        if (param == null) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("参数不合法,查询失败!");
            return vh;
        }
        Integer pageSize = Optional.ofNullable(param.getInteger("pageSize")).orElse(20);
        Integer pageNum = Optional.ofNullable(param.getInteger("pageNum")).orElse(1);
        String tid = param.getString("tid");
        PageHelper.startPage(pageNum, pageSize);
        List<OcBOrder> orders = ocOrderMapper.selectList(new LambdaQueryWrapper<OcBOrder>()
                .select(OcBOrder::getId, OcBOrder::getTid, OcBOrder::getCpCShopId, OcBOrder::getReceiverName,
                        OcBOrder::getReceiverAddress, OcBOrder::getReceiverMobile, OcBOrder::getReceiverEmail,
                        BaseModel::getCreationdate)
                .likeRight(StringUtils.isNotBlank(tid), OcBOrder::getTid, tid)
                .ne(OcBOrder::getOrderStatus, OmsOrderStatus.SYS_VOID.toInteger())
                .orderByDesc(OcBOrder::getId));
        if (CollectionUtils.isEmpty(orders)) {
            vh.setMessage("没有符合条件的数据");
            return vh;
        }
        PageInfo<OcBOrder> resultPage = new PageInfo<>(orders);
        vh.setCode(ResultCode.SUCCESS);
        vh.setData(resultPage);
        log.info(LogUtil.format("AcFOrderInvoiceSaveService.queryTidFuzzy.return={}", "queryTidFuzzy"),
                JSONObject.toJSONString(vh));
        return vh;
    }

    /**
     * 勾选单号校验能否开票
     *
     * @param param tids
     * @return ValueHolderV14
     */
    public ValueHolderV14<InvoiceSaveCheckReceiverResult> checkTid(JSONObject param) {
        log.info(LogUtil.format("AcFOrderInvoiceSaveService.checkTid.start={}", "checkTid"),
                JSONObject.toJSONString(param));
        ValueHolderV14<InvoiceSaveCheckReceiverResult> vh = new ValueHolderV14<>(ResultCode.FAIL, "Fail");
        if (param == null || !param.containsKey("tid")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不合法!");
            return vh;
        }
        String tid = param.getString("tid");

        List<Long> orderIdList = GSI4OrderItem.selectOcBOrderItemByTid(tid);
        if (CollectionUtils.isEmpty(orderIdList)) {
            vh.setMessage("平台单号没有对应的订单!");
            return vh;
        }

        List<OcBOrder> ocOrders = ocOrderMapper.selectByIdsList(orderIdList.stream().distinct().collect(Collectors.toList()));
        List<OcBOrder> canInvoiceOrders =
                ocOrders.stream().filter(o -> !OmsOrderStatus.CANCELLED.toInteger().equals(o.getOrderStatus()) &&
                        !OmsOrderStatus.SYS_VOID.toInteger().equals(o.getOrderStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(canInvoiceOrders)) {
            vh.setMessage("平台单号没有符合开票条件的订单!");
            return vh;
        }
        List<Long> canInvoiceOrderIds = canInvoiceOrders.stream().map(OcBOrder::getId).distinct().collect(Collectors.toList());

        List<OcBOrderItem> orderItems = ocOrderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>()
                .in(OcBOrderItem::getOcBOrderId, canInvoiceOrderIds)
                .ne(OcBOrderItem::getRefundStatus, 6));

        canInvoiceOrders.sort(Comparator.comparing(BaseModel::getCreationdate).reversed());
        OcBOrder order = canInvoiceOrders.get(0);
        InvoiceSaveCheckReceiverResult result = new InvoiceSaveCheckReceiverResult();
        //收票人信息
        InvoiceSaveCheckReceiverResult.ReceiverInfo receiverResult = new InvoiceSaveCheckReceiverResult.ReceiverInfo();
        receiverResult.setReceiver(order.getReceiverName());
        receiverResult.setEmail(order.getReceiverEmail());
        receiverResult.setReceiverAddress(order.getReceiverAddress());
        receiverResult.setPhone(order.getReceiverMobile());
        //开票策略数据
        List<StCInvoiceStrategy> invoiceStrategies =
                stInvoiceStrategyMapper.selectList(new LambdaQueryWrapper<StCInvoiceStrategy>()
                        .select(StCInvoiceStrategy::getId, StCInvoiceStrategy::getCpCShopId,
                                StCInvoiceStrategy::getIsInvoice)
                        .eq(BaseModel::getIsactive, YesNoEnum.Y.getKey()));
        if (CollectionUtils.isEmpty(invoiceStrategies)) {
            vh.setMessage("没有符合条件的开票策略，不能申请开票！");
            return vh;
        }
        long publicStrategy =
                invoiceStrategies.stream().filter(i -> i.getCpCShopId() == null && "1".equals(i.getIsInvoice())).count();

        //发票申请明细表是否存在数据
        Integer invoiceApplyItemCount =
                acInvoiceApplyItemMapper.selectCount(new LambdaQueryWrapper<AcFInvoiceApplyItem>()
                        .eq(AcFInvoiceApplyItem::getTid, tid)
                        .eq(AcFInvoiceApplyItem::getCpCShopId, order.getCpCShopId()));
        if (invoiceApplyItemCount > 0) {
            //            // 根据平台单号查询未取消,未红冲/红冲中的蓝票,如果存在则不允许转换
            //            List<Long> orderInvoiceIdList = orderInvoiceSystemItemMapper.selectByTids(Lists.newArrayList(tid));
            //            if(CollectionUtils.isNotEmpty(orderInvoiceIdList)){
            //                // 查询发票单管理表,查看是否有蓝票
            //                long blueCount = acOrderInvoiceMapper.countBlueByIds(orderInvoiceIdList);
            //                if(blueCount > 0){
            //                    vh.setMessage("平台单号:" + tid + ",发票已经申请，不能重复申请开票!");
            //                    return vh;
            //                }
            //            }
            vh.setMessage("平台单号:" + tid + ",发票已经申请，不能重复申请开票!");
            return vh;
        }

        long shopStrategy = invoiceStrategies.stream().filter(i -> i.getCpCShopId() != null &&
                i.getCpCShopId().contains(String.valueOf(order.getCpCShopId()))&&"1".equals(i.getIsInvoice())).count();
        if (shopStrategy < 1 && publicStrategy < 1) {
            vh.setMessage(order.getCpCShopTitle() + "该店铺不允许OMS系统开票，请联系财务人员！");
            return vh;
        }
        BigDecimal invoiceAmt = orderItems.stream().map(OcBOrderItem::getRealAmt).reduce(BigDecimal.ZERO,
                BigDecimal::add);
        result.setTid(tid);
        result.setCpCShopId(order.getCpCShopId());
        result.setAmt(invoiceAmt);
        //        result.setIds(orderItems.stream().map(OcBOrderItem::getId).collect(Collectors.toList()));
        //        result.setOcBOrderIds(canInvoiceOrderIds);
        result.setReceiverInfo(receiverResult);

        vh.setData(result);
        vh.setCode(ResultCode.SUCCESS);
        vh.setMessage("success");
        log.info(LogUtil.format("AcFOrderInvoiceSaveService.checkTid.return={}", "checkTid"),
                JSONObject.toJSONString(vh));
        return vh;
    }

    /**
     * 申请开票保存
     *
     * @param param 开票信息
     * @param user  用户
     * @return ValueHolderV14
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<SgR3BaseResult> saveApplyInvoice(JSONObject param, User user) {
        log.info(LogUtil.format("AcFOrderInvoiceSaveService.saveApplyInvoice.start={}", "saveApplyInvoice"),
                JSONObject.toJSONString(param));
        AcFOrderInvoiceSaveRequest request =
                JSONObject.parseObject(param.toJSONString(), AcFOrderInvoiceSaveRequest.class);
        AcFInvoiceApply acInvoiceApply = JSONObject.parseObject(param.toJSONString(), AcFInvoiceApply.class);

        List<AcFOrderInvoiceSaveRequest.SaveData> saveDataList = request.getTids();
        if (CollectionUtils.isEmpty(saveDataList)) {
            return new ValueHolderV14<>(ResultCode.FAIL, "平台单号开票信息不能为空!");
        }
        // 判断订单是否开过票,根据平台单号获取所有明细,根据明细查询所有订单
        List<String> tidAll = saveDataList.stream().map(AcFOrderInvoiceSaveRequest.SaveData::getTid).collect(Collectors.toList());
        List<OcBOrder> invoicedOrderList = ocOrderMapper.queryInvoicedTid(tidAll);
        List<String> invoicedTids = invoicedOrderList.stream().filter(e -> Objects.nonNull(e.getInvoiceStatus()) && e.getInvoiceStatus().compareTo(3) == 0).map(OcBOrder::getTid).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(invoicedTids)){
            return new ValueHolderV14<>(ResultCode.FAIL, "平台单号"+invoicedTids+"已开票,不能重复申请开票!");
        }
        List<Long> shopIds =
                saveDataList.stream().map(AcFOrderInvoiceSaveRequest.SaveData::getCpCShopId).distinct().collect(Collectors.toList());
        if (shopIds.size() > 1) {
            return new ValueHolderV14<>(ResultCode.FAIL, "申请开票失败,不同店铺不允许申请开票!");
        }

        Long mainId = null;
        try {
            CpShop cpShop = cpRpcService.selectShopById(shopIds.get(0));
            if (cpShop == null) {
                return new ValueHolderV14<>(ResultCode.FAIL, "当前开票店铺不存在或不可用!");
            }
            //来源店铺和平台信息
            acInvoiceApply.setCpCShopId(cpShop.getId());
            acInvoiceApply.setCpCShopEcode(cpShop.getEcode());
            acInvoiceApply.setCpCShopTitle(cpShop.getCpCShopTitle());
            acInvoiceApply.setCpCPlatformId(cpShop.getCpCPlatformId());
            acInvoiceApply.setCpCPlatformEcode(cpShop.getCpCPlatformEcode());
            acInvoiceApply.setCpCPlatformEname(cpShop.getCpCPlatformEname());
            mainId = Tools.getSequence(InvoiceConst.AC_F_INVOICE_APPLY);
            //多个平台单号拼接
            List<String> tidList = new ArrayList<>();
            List<AcFInvoiceApplyItem> insertItems = new ArrayList<>();
            BigDecimal totInvoiceAmt = BigDecimal.ZERO;
            for (AcFOrderInvoiceSaveRequest.SaveData saveData : saveDataList) {
                AcFInvoiceApplyItem applyItem = new AcFInvoiceApplyItem();
                applyItem.setId(Tools.getSequence(InvoiceConst.AC_F_INVOICE_APPLY_ITEM));
                applyItem.setAmt(saveData.getAmt());
                applyItem.setTid(saveData.getTid());
                tidList.add(saveData.getTid());
                applyItem.setAcFInvoiceApplyId(mainId);
                applyItem.setCpCShopId(saveData.getCpCShopId());
                BaseModelUtil.initialBaseModelSystemField(applyItem, user);
                insertItems.add(applyItem);
                totInvoiceAmt = totInvoiceAmt.add(saveData.getAmt());
            }
            acInvoiceApply.setTid(String.join(",", tidList));
            acInvoiceApply.setInvoiceAmt(totInvoiceAmt);
            acInvoiceApply.setId(mainId);
            //单据编号生成更新
            JSONObject sequence = new JSONObject();
            String billNo = SequenceGenUtil.generateSquence("SEQ_AC_F_INVOICE_APPLY",
                    sequence, user.getLocale(), false);
            acInvoiceApply.setApplyBillNo(billNo);
            acInvoiceApply.setApplyDate(new Date());
            BaseModelUtil.initialBaseModelSystemField(acInvoiceApply, user);
            acInvoiceApplyMapper.insert(acInvoiceApply);
            acInvoiceApplyItemMapper.batchInsert(insertItems);
        } catch (Exception e) {
            log.error(LogUtil.format("AcFOrderInvoiceSaveService.saveApplyInvoice.error={}",
                    "AcFOrderInvoiceSaveService.saveApplyInvoice"),
                    Throwables.getStackTraceAsString(e));

            AssertUtils.logAndThrowException(e, user.getLocale());
        }
        ValueHolderV14<SgR3BaseResult> objectValueHolderV14 = new ValueHolderV14<>(ResultCode.SUCCESS, "发票申请成功！请到订单发票查看开票进度！");
        SgR3BaseResult result = new SgR3BaseResult();
        result.setBillId(mainId);
        objectValueHolderV14.setData(result);
        return objectValueHolderV14;
    }

    public ValueHolderV14<List<InvoiceSaveCheckReceiverResult>> checkOrderParam(JSONObject param) {
        log.info(LogUtil.format("AcFOrderInvoiceSaveService.checkOrderParam.start={}", "checkOrderParam"),
                JSONObject.toJSONString(param));
        ValueHolderV14<List<InvoiceSaveCheckReceiverResult>> vh = new ValueHolderV14<>(ResultCode.FAIL, "Fail");
        if (param == null || !param.containsKey("tids")) {
            vh.setCode(ResultCode.FAIL);
            vh.setMessage("请求参数不合法!");
            return vh;
        }
        List<InvoiceSaveCheckReceiverResult> results = new ArrayList<>();
        String tids = param.getString("tids");
        String[] split = tids.split(",");
        List<String> tidList = Arrays.asList(split);
        List<String> tidDistinct = tidList.stream().distinct().collect(Collectors.toList());
        JSONObject jo = new JSONObject();
        for (String tid : tidDistinct) {
            jo.put("tid", tid);
            ValueHolderV14<InvoiceSaveCheckReceiverResult> holderV14 = checkTid(jo);
            if (!holderV14.isOK()) {
                vh.setMessage(holderV14.getMessage() + "申请开票失败,平台单号:" + tid);
                return vh;
            } else {
                InvoiceSaveCheckReceiverResult data = holderV14.getData();
                results.add(data);
            }
        }
        if (CollectionUtils.isNotEmpty(results)){
            vh.setCode(ResultCode.SUCCESS);
            vh.setData(results);
            vh.setMessage("success!");
        }
        log.info(LogUtil.format("AcFOrderInvoiceSaveService.checkOrderParam.return={}", "checkOrderParam"),
                JSONObject.toJSONString(vh));
        return vh;
    }

    /**
     * 批量查询订单开票参数
     *
     * @param param
     * @return
     */
    public ValueHolderV14<List<InvoiceSaveCheckReceiverResult>> checkOrderParamMcp(CheckOrderParamMcpDTO param) {
        JSONObject jo = JSONObject.parseObject(JSONObject.toJSONString(param));
        return checkOrderParam(jo);
    }

    /**
     * 批量保存订单开票参数
     *
     * @param param
     * @param user
     * @return
     */
    public ValueHolderV14<SgR3BaseResult> saveApplyInvoiceMcp(JSONObject param, User user) {
        return this.saveApplyInvoice(param, user);
    }

}
