package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderItemExt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/9/3 下午4:38
 * @description 猫超直发mapper
 **/
@Mapper
public interface IpBAlibabaAscpOrderItemMapper extends ExtentionMapper<IpBAlibabaAscpOrderItem> {

    @Select("SELECT * FROM ip_b_alibaba_ascp_order_item WHERE ip_b_alibaba_ascp_order_id=#{orderId}")
    List<IpBAlibabaAscpOrderItemExt> selectOrderItemList(@Param("orderId") long orderId);

    /**
     * @param orderId
     * @param orderCode
     * @return
     */
    @Select(" SELECT * FROM ip_b_alibaba_ascp_order a ,ip_b_alibaba_ascp_order_item b where a.id = b.ip_b_alibaba_ascp_order_id and b.ip_b_taobao_order_id = #{orderId} AND a.biz_order_code = #{orderCode}")
    List<IpBAlibabaAscpOrderItem> selectTaobaoOrderItemByBizOrderCode(@Param("orderId") long orderId, @Param("orderCode") long orderCode);

    /**
     * @param orderId
     * @param subOrderCode
     * @return
     */
    @Select("SELECT * FROM ip_b_alibaba_ascp_order_item WHERE ip_b_taobao_order_id = #{orderId} AND sub_order_code = #{subOrderCode}")
    IpBAlibabaAscpOrderItem selectTaobaoOrderItemBySubOrderCode(@Param("orderId") long orderId, @Param("subOrderCode") String subOrderCode);

}