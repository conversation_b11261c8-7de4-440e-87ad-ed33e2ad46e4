package com.jackrain.nea.oc.oms.services.directreport;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.sourcing.api.SgDirectOrderCmd;
import com.burgeon.r3.sg.sourcing.model.request.SgDirectOrderStorageTransItemRequest;
import com.burgeon.r3.sg.sourcing.model.request.SgDirectOrderStorageTransRequest;
import com.burgeon.r3.sg.sourcing.model.result.SgDirectOrderStorageTransResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cp.services.CpSaleOrganizationService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.log.service.LogCommonService;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBDirectReportOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemExtMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBDirectReportOrderStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderSaleProductAttrEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.request.OcBDirectReportTransRequest;
import com.jackrain.nea.oc.oms.model.table.OcBDirectReportOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOperationLog;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.nums.StConstant;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.util.BuildSequenceUtil;
import com.jackrain.nea.oc.oms.util.OmsModelUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sg.service.SgOccupiedInventoryService;
import com.jackrain.nea.st.model.enums.OperationTypeEnum;
import com.jackrain.nea.st.service.StCTraceabilityStrategyMarkingService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AssertUtils;
import com.jackrain.nea.util.MD5Util;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/12/3 09:21
 * @Description
 */
@Slf4j
@Service
public class OcBDirectReportOrderTransStorageService {

    @Resource
    private OcBDirectReportOrderMapper ocBDirectReportOrderMapper;
    @Resource
    private OcBOrderMapper ocBOrderMapper;
    @Resource
    private OcBOrderItemMapper orderItemMapper;
    @DubboReference(version = SgConstantsIF.VERSION, group = SgConstantsIF.GROUP)
    private SgDirectOrderCmd sgDirectOrderCmd;
    @Resource
    private OmsOrderLogService omsOrderLogService;
    @Resource
    private BuildSequenceUtil sequenceUtil;
    @Resource
    private OcBOrderItemExtMapper ocBOrderItemExtMapper;
    @Resource
    private LogCommonService logCommonService;
    @Resource
    private SgOccupiedInventoryService sgOccupiedInventoryService;
    @Resource
    private CpSaleOrganizationService cpSaleOrganizationService;
    @Resource
    private StCTraceabilityStrategyMarkingService stCTraceabilityStrategyMarkingService;


    /**
     * 库存平移（预占单->零售发货单）
     *
     * @param request
     * @param user
     */
    public ValueHolderV14<Void> storageTrans(OcBDirectReportTransRequest request, User user) {
        ValueHolderV14<Void> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "库存转移成功");
        RedisReentrantLock directOrderLock = RedisMasterUtils.getReentrantLock(BllRedisKeyResources.buildDirectReportOptLockKey(request.getId()));
        try {
            if (directOrderLock.tryLock(1, TimeUnit.MINUTES)) {
                OcBDirectReportOrder ocBDirectReportOrder = ocBDirectReportOrderMapper.selectById(request.getId());
                if (ocBDirectReportOrder == null) {
                    throw new NDSException("直发预占单不存在!");
                }
                if (!(OcBDirectReportOrderStatusEnum.AUDITED.getValue().equals(ocBDirectReportOrder.getStatus()) ||
                        OcBDirectReportOrderStatusEnum.PARTIALLY_FULFILLED.getValue().equals(ocBDirectReportOrder.getStatus()))) {
                    throw new NDSException("直发预占单状态不允许转移!");
                }
                //库存转移
                transByOrder(request, user, ocBDirectReportOrder);
            } else {
                throw new NDSException("请勿同时操作，建议稍后再试!");
            }
        } catch (Exception e) {
            log.warn(LogUtil.format("OcBDirectReportOrderTransStorageService.storageTrans error:{}",
                    "OcBDirectReportOrderTransStorageService.storageTrans"), Throwables.getStackTraceAsString(e));
            v14.setCode(ResultCode.FAIL);
            v14.setMessage(e.getMessage());
        } finally {
            directOrderLock.unlock();
        }
        return v14;
    }

    /**
     * 库存转移
     *
     * @param request
     * @param user
     * @param ocBDirectReportOrder
     */
    private void transByOrder(OcBDirectReportTransRequest request, User user,
                              OcBDirectReportOrder ocBDirectReportOrder) {
        //查询订单
        Long orderId = ES4Order.getIdByBillNo(request.getOrderNo());
        if (orderId == null) {
            throw new NDSException("ES查询订单编号不存在!");
        }
        RedisReentrantLock orderLock = RedisMasterUtils.getReentrantLock(BllRedisKeyResources.buildLockOrderKey(orderId));
        try {
            if (orderLock.tryLock(1, TimeUnit.MINUTES)) {
                OcBOrder order = ocBOrderMapper.selectByID(orderId);
                //订单校验并查询订单明细
                List<OcBOrderItem> orderItemList = checkOrder(ocBDirectReportOrder, order);
                //构建
                SgDirectOrderStorageTransRequest transRequest = builfTransParam(user, ocBDirectReportOrder, order, orderItemList);
                //调用库存平移
                ValueHolderV14<SgDirectOrderStorageTransResult> holderV14 =
                        sgDirectOrderCmd.storageTrans(transRequest);
                log.info(LogUtil.format("OcBDirectReportOrderTransStorageService.transByOrder holderV14:{}",
                        "OcBDirectReportOrderTransStorageService.transByOrder"), JSONObject.toJSONString(holderV14));
                //后续处理
                if (!holderV14.isOK()) {
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                            OrderLogTypeEnum.TOB_OCCUPY.getKey(), "OrderId=" + order.getId() +
                                    "库存转移失败,原因：" + holderV14.getMessage() + ",直发单号[" + ocBDirectReportOrder.getBillNo() + "]",
                            "", "", user);
                    //预占单记录失败日志
                    OcBOperationLog operationLog = logCommonService.getOperationLog(StConstant.OC_B_DIRECT_REPORT_ORDER,
                            OperationTypeEnum.AUDIT.getOperationValue(), ocBDirectReportOrder.getId(), "直发预占单", "",
                            "", "单据编号=" + order.getBillNo() + "库存转移失败，原因：" + holderV14.getMessage(), user);
                    logCommonService.insertLog(operationLog);
                    throw new NDSException("库存转移失败，原因：" + holderV14.getMessage());
                } else {
                    SgDirectOrderStorageTransResult transResult = holderV14.getData();
                    if (transResult == null) {
                        throw new NDSException("库存转移成功，但是返回实体仓信息为空，请联系管理员");
                    }
                    //新增订单明细扩展表
                    addOrderItemExt(user, ocBDirectReportOrder, order, orderItemList);
                    //更新订单信息
                    updateOrder(user, order, transResult);
                    //更新预占单信息
                    BigDecimal totQty = updateDirectOrder(user, ocBDirectReportOrder, orderItemList, transResult);
                    //记录订单日志
                    omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                            OrderLogTypeEnum.TOB_OCCUPY.getKey(), "OrderId=" + order.getId() +
                                    "库存转移成功,仓库编码：" + transResult.getWarehouseCode() + ",仓库名称：" +
                                    transResult.getWarehouseName() + ",直发单号[" + ocBDirectReportOrder.getBillNo() + "]",
                            "", "", user);
                    //记录预占单日志
                    OcBOperationLog operationLog = logCommonService.getOperationLog(StConstant.OC_B_DIRECT_REPORT_ORDER,
                            OperationTypeEnum.AUDIT.getOperationValue(), ocBDirectReportOrder.getId(), "直发预占单", "",
                            "", "单据编号=" + order.getBillNo() + "库存转移成功，转移数量："
                                    + totQty.stripTrailingZeros().toPlainString(), user);
                    logCommonService.insertLog(operationLog);
                    //溯源打标
                    OcBOrderParam orderParam = new OcBOrderParam();
                    order.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
                    orderParam.setOcBOrder(order);
                    orderParam.setOrderItemList(orderItemList);
                    stCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService(orderParam, SystemUserResource.getRootUser());
                }
            } else {
                throw new NDSException("当前订单正在被其他人操作,请稍后再试!");
            }
        } catch (Exception e) {
            throw new NDSException(e.getMessage());
        } finally {
            orderLock.unlock();
        }
    }

    /**
     * 更新预占单信息
     *
     * @param user
     * @param ocBDirectReportOrder
     * @param orderItemList
     * @param transResult
     * @return
     */
    private BigDecimal updateDirectOrder(User user, OcBDirectReportOrder ocBDirectReportOrder,
                                         List<OcBOrderItem> orderItemList, SgDirectOrderStorageTransResult transResult) {
        OcBDirectReportOrder updateDirectOrder = new OcBDirectReportOrder();
        updateDirectOrder.setId(ocBDirectReportOrder.getId());
        updateDirectOrder.setSgBShareOutBillNo(transResult.getShareOutBillNo());
        updateDirectOrder.setSgBStoOutBillNo(transResult.getStoOutBillNo());
        BigDecimal totQty = orderItemList.stream().map(OcBOrderItem::getQty).reduce(BigDecimal.ZERO, BigDecimal::add);
        updateDirectOrder.setTotalFulfillQty(ocBDirectReportOrder.getTotalFulfillQty().add(totQty));
        if (ocBDirectReportOrder.getTotalQty().compareTo(updateDirectOrder.getTotalFulfillQty()) > 0) {
            updateDirectOrder.setStatus(OcBDirectReportOrderStatusEnum.PARTIALLY_FULFILLED.getValue());
        } else {
            updateDirectOrder.setStatus(OcBDirectReportOrderStatusEnum.FULFILLED.getValue());
        }
        OmsModelUtil.setDefault4Upd(updateDirectOrder, user);
        ocBDirectReportOrderMapper.updateById(updateDirectOrder);
        return totQty;
    }

    /**
     * 更新订单信息
     *
     * @param user
     * @param order
     * @param transResult
     */
    private void updateOrder(User user, OcBOrder order, SgDirectOrderStorageTransResult transResult) {
        OcBOrder updateModel = new OcBOrder();
        updateModel.setId(order.getId());
        updateModel.setCpCShopId(order.getCpCShopId());
        updateModel.setBillNo(order.getBillNo());
        updateModel.setPlatform(order.getPlatform());
        updateModel.setIsExpress(order.getIsExpress());
        //赋值合单需要的字段
        setOrderEncryptionCodeDate(updateModel, order);
        setOrderColumn(updateModel, transResult);
        updateModel.setSysremark("");
        if (sgOccupiedInventoryService.isO2OOrder(updateModel)) {
            updateModel.setIsO2oOrder(OcBOrderConst.IS_STATUS_IY);
        } else {
            updateModel.setIsO2oOrder(OcBOrderConst.IS_STATUS_IN);
        }
        //埋点占用成功
        updateModel.setOccupySuccessDate(new Date());
        updateModel.setIsOutStock(0);
        //寻源失败标识
        updateModel.setIsOccupyStockFail(OcBOrderConst.IS_STATUS_IN);
        //寻源失败标识
        updateModel.setIsException(OcBOrderConst.IS_STATUS_IN);
        updateModel.setExceptionType("");
        updateModel.setExceptionExplain("");
        OmsModelUtil.setDefault4Upd(updateModel, user);
        ocBOrderMapper.updateById(updateModel);
    }

    /**
     * 赋值
     *
     * @param newOcBOrder
     * @param oldOcBOrder
     */
    private void setOrderEncryptionCodeDate(OcBOrder newOcBOrder, OcBOrder oldOcBOrder) {
        newOcBOrder.setUserNick(oldOcBOrder.getUserNick());
        newOcBOrder.setCpCShopId(oldOcBOrder.getCpCShopId());
        newOcBOrder.setPlatform(oldOcBOrder.getPlatform());
        newOcBOrder.setCpCPhyWarehouseId(oldOcBOrder.getCpCPhyWarehouseId());
        newOcBOrder.setOrderType(oldOcBOrder.getOrderType());
        newOcBOrder.setReceiverName(oldOcBOrder.getReceiverName());
        newOcBOrder.setReceiverMobile(oldOcBOrder.getReceiverMobile());
        newOcBOrder.setReceiverPhone(oldOcBOrder.getReceiverPhone());
        newOcBOrder.setCpCRegionProvinceId(oldOcBOrder.getCpCRegionProvinceId());
        newOcBOrder.setCpCRegionCityId(oldOcBOrder.getCpCRegionCityId());
        newOcBOrder.setCpCRegionAreaId(oldOcBOrder.getCpCRegionAreaId());
        newOcBOrder.setReceiverAddress(oldOcBOrder.getReceiverAddress());
        newOcBOrder.setMergedCode(oldOcBOrder.getMergedCode());
        newOcBOrder.setBusinessTypeId(oldOcBOrder.getBusinessTypeId());
        newOcBOrder.setGwSourceGroup(oldOcBOrder.getGwSourceGroup());
        newOcBOrder.setSellerMemo(oldOcBOrder.getSellerMemo());
    }

    /**
     * <AUTHOR>
     * @Date 18:05 2021/7/12
     * @Description 占单赋值
     */
    private void setOrderColumn(OcBOrder ocBOrder, SgDirectOrderStorageTransResult transResult) {
        ocBOrder.setSysremark("订单信息整理中");
        ocBOrder.setCpCPhyWarehouseId(transResult.getWarehouseId());
        ocBOrder.setOrderStatus(OmsOrderStatus.UNCONFIRMED.toInteger());
        ocBOrder.setCpCPhyWarehouseEcode(transResult.getWarehouseCode());
        ocBOrder.setCpCPhyWarehouseEname(transResult.getWarehouseName());
        ocBOrder.setIsOutStock(0);
        ocBOrder.setStoOutBillNo(transResult.getOrderStoOutBillNo());
        // 需要传有地址信息的订单参数
        MD5Util.encryptOrderInfo4Merge(ocBOrder);
    }

    /**
     * 新增订单明细拓展表信息
     *
     * @param user
     * @param ocBDirectReportOrder
     * @param order
     * @param orderItemList
     */
    private void addOrderItemExt(User user, OcBDirectReportOrder ocBDirectReportOrder,
                                 OcBOrder order, List<OcBOrderItem> orderItemList) {
        List<OcBOrderItemExt> ocBOrderItemExtList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : orderItemList) {
            OcBOrderItemExt ocBOrderItemExt = new OcBOrderItemExt();
            ocBOrderItemExtList.add(ocBOrderItemExt);
            ocBOrderItemExt.setId(sequenceUtil.buildOrderItemExtSequenceId());
            ocBOrderItemExt.setOcBOrderId(order.getId());
            ocBOrderItemExt.setOrderItemId(ocBOrderItem.getId());
            ocBOrderItemExt.setDistCodeLevelTwo(ocBDirectReportOrder.getCpCDisOrgLv2Code());
            ocBOrderItemExt.setDistNameLevelTwo(ocBDirectReportOrder.getCpCDisOrgLv2Name());
            ocBOrderItemExt.setTid(order.getTid());
            OmsModelUtil.setDefault4Add(ocBOrderItemExt, user);
        }
        ocBOrderItemExtMapper.delete(new LambdaQueryWrapper<OcBOrderItemExt>()
                .eq(OcBOrderItemExt::getOcBOrderId, order.getId()));
        ocBOrderItemExtMapper.batchInsert(ocBOrderItemExtList);
    }

    private SgDirectOrderStorageTransRequest builfTransParam(User user, OcBDirectReportOrder ocBDirectReportOrder, OcBOrder order, List<OcBOrderItem> orderItemList) {
        //构建库存平移参数
        SgDirectOrderStorageTransRequest transRequest = new SgDirectOrderStorageTransRequest();
        transRequest.setSourceBillId(ocBDirectReportOrder.getId());
        transRequest.setSourceBillNo(ocBDirectReportOrder.getBillNo());
        transRequest.setSourceBillType(SgConstantsIF.BILL_TYPE_DIRECT_ORDER);
        transRequest.setOrderId(order.getId());
        transRequest.setOrderNo(order.getBillNo());
        transRequest.setOrderTid(order.getSourceCode());
        transRequest.setOrderBillDate(order.getOrderDate());
        transRequest.setShopId(order.getCpCShopId());
        transRequest.setUser(user);
        //解析效期范围
        Map<Long, String> itemIdAndExpiryDateRangeMap = new HashMap<>();
        analysisExpiryDateRange(itemIdAndExpiryDateRangeMap, orderItemList);
        List<SgDirectOrderStorageTransItemRequest> itemRequestList = new ArrayList<>();
        transRequest.setItemRequestList(itemRequestList);
        for (OcBOrderItem orderItem : orderItemList) {
            SgDirectOrderStorageTransItemRequest itemRequest = new SgDirectOrderStorageTransItemRequest();
            itemRequestList.add(itemRequest);
            itemRequest.setOrderItemId(orderItem.getId());
            itemRequest.setPsCSkuId(orderItem.getPsCSkuId());
            itemRequest.setPsCSkuCode(orderItem.getPsCSkuEcode());
            itemRequest.setQty(orderItem.getQty());
            String expiryDateRange = itemIdAndExpiryDateRangeMap.get(orderItem.getId());
            if (!"UNLIMITED".equals(expiryDateRange)) {
                String[] split = expiryDateRange.split("-");
                itemRequest.setBeginProduceDate(split[0]);
                itemRequest.setEndProduceDate(split[1]);
            }
        }
        return transRequest;
    }

    private List<OcBOrderItem> checkOrder(OcBDirectReportOrder ocBDirectReportOrder, OcBOrder order) {
        if (order == null) {
            throw new NDSException("订单不存在!");
        }
        if (!OmsBusinessTypeUtil.isToBOrder(order)) {
            throw new NDSException("仅TOB订单允许库存转移!");
        }
        if (!OmsOrderStatus.BE_OUT_OF_STOCK.toInteger().equals(order.getOrderStatus())) {
            throw new NDSException("仅待寻源订单允许库存转移!");
        }
        if (OmsBusinessTypeUtil.isToBOrder(order) && OrderSaleProductAttrEnum.isToBCC(order.getSaleProductAttr())) {
            throw new NDSException("仅非残次订单允许库存转移!");
        }
//        if (!order.getCpCShopId().equals(ocBDirectReportOrder.getCpCShopId())) {
//            throw new NDSException("仅同店铺订单允许库存转移!");
//        }

        /*获取并检查明细*/
        List<OcBOrderItem> orderItemList = orderItemMapper.selectUnSuccessRefund(order.getId());
        if (CollectionUtils.isEmpty(orderItemList)) {
            throw new NDSException("当前订单明细已经不存在");
        }

        /*明细行的零级、销售组织一二三级、分货组织一二三级：只做校验，不变更数据*/
        List<OcBOrderItemExt> itemExtList = cpSaleOrganizationService.queryExtByItem(orderItemList, order);
        if (CollectionUtils.isEmpty(itemExtList)) {
            throw new NDSException("未找到任一明细行分货信息");
        }

        List<String> distCodeLevel2 = itemExtList.stream()
                .map(OcBOrderItemExt::getDistCodeLevelTwo).distinct().collect(Collectors.toList());
        if (distCodeLevel2.size() != 1) {
            log.warn(LogUtil.format("订单明细的分货二级部门不全为同一个分货二级部门，订单ID:{}，分货部门编码:{}",
                    "OcBDirectReportOrderTransStorageService.checkOrder"), order.getId(), distCodeLevel2);
            throw new NDSException("订单【" + order.getBillNo() + "】存在多个二级分货部门，无法进行直发库存平移");
        }

        if (!distCodeLevel2.get(0).equals(ocBDirectReportOrder.getCpCDisOrgLv2Code())) {
            log.warn(LogUtil.format("直发预占单与零售发货单分货部门不一致，订单ID:{}，直发单ID:{},订单部门:{}，直发单部门:{},",
                            "OcBDirectReportOrderTransStorageService.checkOrder"),
                    order.getId(), ocBDirectReportOrder.getId(), distCodeLevel2.get(0), ocBDirectReportOrder.getCpCDisOrgLv2Code());
            throw new NDSException("直发预占单与零售发货单所属分货部门不一致，直发预占单【"
                    + ocBDirectReportOrder.getCpCDisOrgLv2Code() + "】,零售发货单【" + distCodeLevel2.get(0) + "】");
        }

        return orderItemList;
    }


    private void analysisExpiryDateRange(Map<Long, String> itemIdAndExpiryDateRangeMap,
                                         List<OcBOrderItem> orderItemList) {
        try {
            for (OcBOrderItem ocBOrderItem : orderItemList) {
                Integer expiryDateType = ocBOrderItem.getExpiryDateType();

                String newExpiryDateRange = "UNLIMITED";

                if (expiryDateType != null && StringUtils.isNotEmpty(ocBOrderItem.getExpiryDateRange())) {
                    if (expiryDateType == 2) {
                        String expiryDateRange = ocBOrderItem.getExpiryDateRange();

                        String[] split = expiryDateRange.split("-");

                        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

                        Calendar calendar1 = Calendar.getInstance();
                        System.out.println(calendar1.getTime());
                        calendar1.add(Calendar.DATE, -Integer.parseInt(split[1]));
                        String stringStartDate = sdf.format(calendar1.getTime());

                        Calendar calendar2 = Calendar.getInstance();
                        System.out.println(calendar2.getTime());
                        calendar2.add(Calendar.DATE, -Integer.parseInt(split[0]));
                        String stringEndDate = sdf.format(calendar2.getTime());

                        newExpiryDateRange = stringStartDate + "-" + stringEndDate;

                    } else if (expiryDateType == 1) {
                        newExpiryDateRange = ocBOrderItem.getExpiryDateRange();
                    }
                }
                itemIdAndExpiryDateRangeMap.put(ocBOrderItem.getId(), newExpiryDateRange);
            }
        } catch (Exception e) {
            AssertUtils.logAndThrow("效期解析异常");
        }

    }
}
