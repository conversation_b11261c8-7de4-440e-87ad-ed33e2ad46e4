package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OmsOrderDistributeWarehouseService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCExchangeStrategyOrderDO;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 根据条件获取实体仓ID
 * @author: 江家雷
 * @since: 2020/8/29
 * create at : 2020/8/29 2:49
 */
@Component
@Slf4j
public class OmsOrderDistributeWarehouseUtils {

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private StRpcService stRpcService;

    @Autowired
    private OmsOrderDistributeWarehouseService distributeWarehouseService;

    @Autowired
    private OmsStCShopStrategyService shopStrategyService;

    /**
     * 获取符合条件的实体仓ID
     *
     * @param orderRelation
     * @param logisticStoreIdList
     * @param physicalWarehouseIdList
     * @return
     */
    public List<Long> getUnO2oWareHouseIdList(OcBOrderRelation orderRelation, List<Long> logisticStoreIdList, List<Long> physicalWarehouseIdList) {

        //  天猫换货订单,若未配置走O2O仓，分仓时排除O2O仓
        if (orderRelation.getOrderInfo().getPlatform() == null || orderRelation.getOrderInfo().getOrderType() == null) {
            return physicalWarehouseIdList;
        }
        if (PlatFormEnum.TAOBAO.getCode() != orderRelation.getOrderInfo().getPlatform().intValue()
                || !orderRelation.getOrderInfo().getOrderType().equals(OrderTypeEnum.EXCHANGE.getVal())) {
            return physicalWarehouseIdList;
        }
        if (CollectionUtils.isEmpty(logisticStoreIdList) || CollectionUtils.isEmpty(physicalWarehouseIdList)) {
            return physicalWarehouseIdList;
        }

        Long shopId = orderRelation.getOrderInfo().getCpCShopId();
        // 1.根据店铺ID查询天猫换货策略
        StCExchangeStrategyOrderDO stCExchangeStrategy = stRpcService.queryExchangeStrategyByShopId(shopId);
        if (stCExchangeStrategy == null
                || !Integer.valueOf(1).equals(stCExchangeStrategy.getIsO2oDelivery())) {
            // 订单明细含有【平台换货单号】（exchange_bill_no）值（非空）的换货订单
            List<OcBOrderItem> items = orderRelation.getOrderItemList().stream().filter(item -> Objects.nonNull(item.getExchangeBillNo())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(items)) {
                // 2.查询出O2O仓对应的实体仓ID
                List<Long> o2oList = cpRpcService.rpcQueryO2OCpCPhyWareHouses(logisticStoreIdList);
                if (CollectionUtils.isEmpty(o2oList)) {
                    return physicalWarehouseIdList;
                }
                // 3.排除O2O对应的实体仓
                List<Long> resultList = new ArrayList<>();
                HashSet<Long> physicalWarehouseSet = new HashSet(physicalWarehouseIdList);
                HashSet<Long> o2oSet = new HashSet(o2oList);
                physicalWarehouseSet.removeAll(o2oSet);
                resultList.addAll(physicalWarehouseSet);
                if (CollectionUtils.isNotEmpty(resultList)) {
                    return resultList;
                } else {
                    resultList.add(queryDefaultWarehouse(shopId));
                    return resultList;
                }
            }
        }
        return physicalWarehouseIdList;
    }

    /**
     * 查询店铺的默认发货仓库
     *
     * @param shopId 店铺Id
     * @return Long
     */
    public Long queryDefaultWarehouse(Long shopId) {
        StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(shopId);
        if (shopStrategy == null) {
            return null;
        }
        return shopStrategy.getDefaultStoreId();
    }
}