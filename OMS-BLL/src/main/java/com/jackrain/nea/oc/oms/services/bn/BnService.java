package com.jackrain.nea.oc.oms.services.bn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.jackrain.nea.ip.model.bn.BnTaskListQueryRequest;
import com.jackrain.nea.ip.model.bn.BnTaskListQueryResponse;
import com.jackrain.nea.oc.oms.model.BnConditionColumn;
import com.jackrain.nea.oc.oms.model.enums.BnReturnOrderColumnEnum;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.assertj.core.util.Sets;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 班牛 工单服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BnService {

    /**
     * 班牛工单关闭原因-超时已入库自动关闭
     */
    private static final String CLOSE_REASON = "OMS 超时已入库自动关闭";

    @Resource
    private IpRpcService ipRpcService;

    /**
     * 修改工单信息
     *
     * @param expressCode   快递单号
     * @param logisticsName 物流公司名称
     */
    public Boolean batchUpdateBnTask(String expressCode, String logisticsName) {
        log.info("批量修改工单信息 expressCode:{},logisticsName:{}", expressCode, logisticsName);
        if (StringUtils.isBlank(expressCode) || StringUtils.isBlank(logisticsName)) {
            log.info("批量修改工单信息 expressCode:{},logisticsName:{}", expressCode, logisticsName);
            return null;
        }

        //查询工单信息
        BnTaskListQueryRequest pageRequest = new BnTaskListQueryRequest();
        pageRequest.setProjectId("21939");
        pageRequest.setPage(1);
        pageRequest.setPageSize(20);
        pageRequest.setConditionColumn(getBnConditionColumns(expressCode, logisticsName));

        ValueHolderV14<BnTaskListQueryResponse> queryValueHolder = ipRpcService.queryTaskList(pageRequest);
        log.info("批量修改工单信息 查询工单queryTaskList expressCode:{},logisticsName:{},res:{}", expressCode, logisticsName, JSON.toJSONString(queryValueHolder));

        if (!queryValueHolder.isOK()) {
            log.info("批量修改工单信息 查询工单失败 expressCode:{},logisticsName:{},res:{}", expressCode, logisticsName, queryValueHolder.getMessage());
            return false;
        }

        BnTaskListQueryResponse data = queryValueHolder.getData();
        if (data == null || data.getTotal() == 0) {
            log.info("批量修改工单信息 查询工单失败 expressCode:{},logisticsName:{}", expressCode, logisticsName);
            return false;
        }

        Set<String> taskIds = Sets.newHashSet();
        JSONArray queryResult = data.getResult();
        for (Object o : queryResult) {
            JSONObject task = (JSONObject) o;
            String taskId = task.getString("-1");
            //判责结果
            String judgmentResult = task.getString(BnReturnOrderColumnEnum.JUDGMENT_RESULT.getColumnId());
            if (StringUtils.isBlank(judgmentResult)) {
                taskIds.add(taskId);
            }
        }

        if (CollectionUtils.isEmpty(taskIds)) {
            log.info("批量修改工单信息 无符合条件的工单 expressCode:{},logisticsName:{}", expressCode, logisticsName);
            return null;
        }

        //修改工单
        List<Map<String, String>> contents = Lists.newArrayList();
        for (String taskId : taskIds) {
            Map<String, String> content = Maps.newHashMap();
            content.put("task_id", taskId);
            content.put(BnReturnOrderColumnEnum.CLOSE_REASON.getColumnId(), CLOSE_REASON);
            contents.add(content);
        }

        log.info("批量修改工单信息 批量修改工单batchUpdateTask expressCode:{},logisticsName:{},res:{}", expressCode, logisticsName, JSON.toJSONString(contents));
        ValueHolderV14 valueHolderV14 = ipRpcService.batchUpdateTask(contents, "21939", "36039", null);
        if (!valueHolderV14.isOK()) {
            log.warn("批量修改工单信息 失败 expressCode:{},logisticsName:{},res:{}", expressCode, logisticsName, valueHolderV14.getMessage());
            return false;
        }

        return true;
    }

    private static String getBnConditionColumns(String expressCode, String logisticsName) {
        List<BnConditionColumn> conditionColumns = new ArrayList<>();
        conditionColumns.add(BnConditionColumn.builder()
                .id(Long.valueOf(BnReturnOrderColumnEnum.LOGISTICS_CODE.getColumnId()))
                .behaviorType(1)
                .searchType("1")
                .value(expressCode)
                .build());
        conditionColumns.add(BnConditionColumn.builder()
                .id(Long.valueOf(BnReturnOrderColumnEnum.LOGISTICS_COMPANY.getColumnId()))
                .behaviorType(1)
                .searchType("1")
                .value(logisticsName)
                .build());
        return JSON.toJSONString(conditionColumns);
    }
}
