package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBOrderToAgTask;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;

@Mapper
public interface OcBOrderToAgTaskMapper extends ExtentionMapper<OcBOrderToAgTask> {

    @Select("SELECT * FROM oc_b_order_to_ag_task WHERE refund_id = #{refundId}")
    OcBOrderToAgTask selectToAgTaskByRefundId(@Param("refundId") String refundId);

    @SelectProvider(type = OcBOrderToAgTaskMapper.SqlProvider.class, method = "selectDynamicTaskOrderToAg")
    List<OcBOrderToAgTask> selectDynamicTaskOrderToAg(@Param("node") String node, @Param("name") String name,
                                                      @Param("num") int num, @Param("status") int status,
                                                      @Param("retriesTimes") int retriesTimes);

    @UpdateProvider(type = OcBOrderToAgTaskMapper.SqlProvider.class, method = "updateOrderToAgRetriesTimes")
    int updateOrderToAgRetriesTimes(@Param("id") long id, @Param("status") int status,
                                    @Param("remark") String remark,
                                    @Param("isUpdateRetries") boolean isUpdateRetries,
                                    @Param("refundId") String refundId);

    class SqlProvider {

        public String selectDynamicTaskOrderToAg(@Param("node") String node, @Param("name") String name,
                                                 @Param("num") int num, @Param("status") int status,
                                                 @Param("retriesTimes") int retriesTimes) {
            StringBuilder sb = new StringBuilder();
            sb.append("/*!TDDL:NODE=");
            sb.append(node);
            sb.append("*/ ");
            sb.append("SELECT * FROM ");
            sb.append(name);
            sb.append(" WHERE STATUS = ");
            sb.append(status);
            sb.append(" AND RETRIES_TIMES < ");
            sb.append(retriesTimes);
            sb.append(" ORDER BY modifieddate ASC ");
            sb.append(" LIMIT ");
            sb.append(num);
            return sb.toString();
        }

        public String updateOrderToAgRetriesTimes(@Param("id") long id, @Param("status") int status,
                                                  @Param("remark") String remark,
                                                  @Param("isUpdateRetries") boolean isUpdateRetries,
                                                  @Param("refundId") String refundId) {
            return new SQL() {
                {
                    UPDATE("oc_b_order_to_ag_task");
                    SET("status=#{status}");
                    SET("remark=#{remark}");
                    SET("modifieddate=NOW()");
                    if (isUpdateRetries) {
                        SET("retries_times = IFNULL(retries_times, 0) + 1");
                    }
                    WHERE("ID=#{id} and refund_id = #{refundId}");
                }
            }.toString();

        }
    }
}