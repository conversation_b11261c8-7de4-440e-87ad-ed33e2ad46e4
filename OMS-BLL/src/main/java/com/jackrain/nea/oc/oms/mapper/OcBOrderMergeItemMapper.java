package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderMergeItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Mapper
@Component
public interface OcBOrderMergeItemMapper extends ExtentionMapper<OcBOrderMergeItem> {

    @Select("SELECT * FROM oc_b_order_merge_item WHERE oc_b_order_id=#{orderId} and isactive='Y'")
    List<OcBOrderMergeItem> selectOrderMergeItemList(long orderId);
}
