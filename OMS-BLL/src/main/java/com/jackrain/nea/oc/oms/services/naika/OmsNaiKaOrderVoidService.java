package com.jackrain.nea.oc.oms.services.naika;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.request.naika.NaiKaReissueRequest;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderHoldService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName OmsNaiKaOrderVoidService
 * @Description 奶卡订单作废
 * <AUTHOR>
 * @Date 2022/7/11 11:23
 * @Version 1.0
 */
@Component
@Slf4j
public class OmsNaiKaOrderVoidService {

    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderHoldService ocBOrderHoldService;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private OcBReturnAfSendMapper ocBReturnAfSendMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolder naiKaOrderVoid(List<Long> naiKaIdList, Long id, User user) {
        log.info("作废奶卡,传入奶卡信息:{}", id);
        List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.selectNaiKaByIdList(naiKaIdList);
        ValueHolder vh = new ValueHolder();
        if (CollectionUtil.isEmpty(ocBOrderNaiKaList)) {
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "奶卡信息为空");
            return vh;
        }

        // 过滤出来作废成功的
        ocBOrderNaiKaList = ocBOrderNaiKaList.stream().filter(s -> !OmsOrderNaiKaStatusEnum.VOID_SUCCESS.getStatus().equals(s.getNaikaStatus())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(ocBOrderNaiKaList)) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "奶卡全部为已作废,无需继续作废");
            return vh;
        }
        List<String> cardCodeList = ocBOrderNaiKaList.stream().map(OcBOrderNaiKa::getCardCode).collect(Collectors.toList());
        List<Long> cardIdList = ocBOrderNaiKaList.stream().map(OcBOrderNaiKa::getId).collect(Collectors.toList());
        Long ocBOrderId = ocBOrderNaiKaList.get(0).getOcBOrderId();
        OcBOrder ocBOrder = ocBOrderMapper.get4NaiKaOrder(ocBOrderId);
        CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(ocBOrder.getPlatform()));
        NaiKaReissueRequest request = new NaiKaReissueRequest();
        request.setBillNo(ocBOrder.getBillNo());
        request.setSourceCode(ocBOrder.getSourceCode());
        request.setPlatformCode(cpCPlatform.getEcode());
        // 目前此字段无任何含义
        request.setType(1);
        request.setRemark("奶卡作废");
        request.setCardList(cardCodeList);
        request.setShopCode(ocBOrder.getCpCShopEcode());
        ValueHolderV14 valueHolderV14;
        try {
            valueHolderV14 = naiKaOrderCmd.orderReissue(request);
            if (valueHolderV14.isOK()) {
                ocBOrderNaiKaMapper.updateNaiKaStatusByIdList(cardIdList, OmsOrderNaiKaStatusEnum.VOID_SUCCESS.getStatus());
                // 奶卡作废后 判断新的订单是否还处于hold单状态 如果是的 需要解hold
                if (ObjectUtil.isNotNull(id)) {
                    // fixme id不是分库分表建 现在的使用方式有点问题
                    List<OcBReturnAfSend> ocBReturnAfSendList = ocBReturnAfSendMapper.selectOcBReturnAfSendListById(Collections.singletonList(id));
                    OcBReturnAfSend ocBReturnAfSend = ocBReturnAfSendList.get(0);
                    if (ObjectUtil.isNotNull(ocBReturnAfSend.getNewOcBOrderId())) {
                        OcBOrder order = ocBOrderMapper.get4NaiKaOrder(ocBReturnAfSend.getNewOcBOrderId());
                        OcBOrder updateOrder = new OcBOrder();
                        updateOrder.setId(order.getId());
                        updateOrder.setIsInterecept(0);
                        updateOrder.setModifieddate(new Date());
                        ocBOrderMapper.updateById(updateOrder);
                        omsOrderLogService.addUserOrderLog(order.getId(), order.getBillNo(),
                                OrderLogTypeEnum.ORDER_HOLD_CANCEL.getKey(), "奶卡手动作废后自动取消hold", null, null, user);
                    }
                }
            } else {
                log.error(LogUtil.format("奶卡作废失败 ,卡号信息{}. 失败原因{}"), JSONUtil.toJsonStr(cardCodeList), valueHolderV14.getMessage());
                ocBOrderNaiKaMapper.updateNaiKaStatusByIdList(cardIdList, OmsOrderNaiKaStatusEnum.VOID_FAILED.getStatus());
            }
            vh.put("code", valueHolderV14.getCode());
            vh.put("message", valueHolderV14.getMessage());
        } catch (Exception e) {
            log.error(LogUtil.format("奶卡作废异常 ,卡号信息{}. 异常原因{}"), JSONUtil.toJsonStr(cardCodeList), e.getMessage());
            vh.put("code", ResultCode.FAIL);
            vh.put("message", e.getMessage());
        }
        return vh;
    }
}
