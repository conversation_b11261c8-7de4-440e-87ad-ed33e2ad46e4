package com.jackrain.nea.oc.oms.nums;


/**
 * 开票通知状态枚举
 *
 * @author: huang.zai<PERSON>
 * create at: 2019/7/23 19:20
 */
public enum OcInvoiceStatusEnum {

    NOT_AUDITED("未审核", 0),
    WAIT_INVOICE("待开票", 1),
    REPRIEVE_INVOICE("暂缓开票", 2),
    ALREADY_INVOICE("已开票", 3),
    VOID("作废", 4);


    String key;
    int val;

    OcInvoiceStatusEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    /**
     * 根据状态值,获取状态名
     *
     * @param val
     * @return String
     */
    public static String enumToStringByVal(Integer val) {
        String s = "";
        if (val == null) {
            return s;
        }
        for (OcInvoiceStatusEnum e : OcInvoiceStatusEnum.values()) {
            if (e.getVal().equals(val)) {
                s = e.getKey();
                return s;
            }
        }
        return null;
    }
}


