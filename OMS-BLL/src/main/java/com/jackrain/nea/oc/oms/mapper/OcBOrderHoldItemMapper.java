package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderHoldItem;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Update;

import java.util.List;


@Mapper
public interface OcBOrderHoldItemMapper extends ExtentionMapper<OcBOrderHoldItem> {

    @Select("select * from oc_b_order_hold_item where oc_b_order_id = #{orderId} and hold_order_reason = #{holdOrderReason} limit 1")
    OcBOrderHoldItem selectOrderHoldItemByOrderIdAndHoldReason(@Param("orderId") Long orderId, @Param("holdOrderReason") Integer holdOrderReason);

    @Select("select * from oc_b_order_hold_item where oc_b_order_id = #{orderId} and hold_order_reason = #{holdOrderReason}")
    List<OcBOrderHoldItem> selectOrderHoldItemByOrderIdAndHoldReasonList(@Param("orderId") Long orderId, @Param("holdOrderReason") Integer holdOrderReason);


    @Select("select distinct oc_b_order_id from oc_b_order_hold_item where hold_status = 'Y' limit #{num}")
    List<Long> selectOrderIdList(@Param("num") int num);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_hold_item WHERE hold_status = 'Y' and oc_b_order_id "
            + "in <foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderHoldItem> selectOrderHoldItemList(@Param("ids") List<Long> ids);

    @Select("<script> "
            + "SELECT * FROM oc_b_order_hold_item WHERE hold_status = 'Y' and oc_b_order_id = #{orderId} "
            + "and hold_order_reason in <foreach item='item' index='index' collection='orderReasons' "
            + "open='(' separator=',' close=')'> #{item} </foreach> "
            + "</script>")
    List<OcBOrderHoldItem> selectOrderHoldItemsByHoldReasons(@Param("orderId") Long orderId, @Param("orderReasons") List<Integer> orderReasons);

    @Update("<script>"
            + "update oc_b_order_hold_item set hold_status = 'N' where oc_b_order_id = #{orderId} and id in "
            + "<foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    void updateOrderHoldItemByOrderId(@Param("orderId") Long orderId, @Param("ids") List<Long> ids);

    @Select("SELECT * FROM oc_b_order_hold_item WHERE hold_status = 'Y' and oc_b_order_id = #{orderId}")
    List<OcBOrderHoldItem> selectOrderHoldItemListByOrderId(@Param("orderId") Long orderId);

    @SelectProvider(type = SqlProvider.class, method = "selectDynamicTaskOrderHoldItem")
    List<Long> selectDynamicTaskOrderHoldItem(@Param("name") String name, @Param("num") int num,
                                              @Param("status") String status);

    @Update("<script>"
            + "update oc_b_order_hold_item set modifieddate = now() where oc_b_order_id in "
            + "<foreach item='item' index='index' collection='ids' "
            + "open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    void batchUpdateModifieddateByIds(@Param("ids") List<Long> ids);

    @Update("update oc_b_order_hold_item set hold_status = #{item.holdStatus} ,release_time = #{item.releaseTime},modifierid = #{item.modifierid}," +
            " modifiername = #{item.modifiername} , modifierename = #{item.modifierename}, modifieddate = #{item.modifieddate} " +
            " where oc_b_order_id = #{item.ocBOrderId} and hold_order_reason = #{item.holdOrderReason} ")
    void updateOcBOrderHoldItem(@Param("item") OcBOrderHoldItem item);

    /**
     * 根据订单id删除
     *
     * @param ocBOrderId
     * @return
     */
    @Delete("delete from oc_b_order_hold_item where oc_b_order_id = #{ocBOrderId}")
    int delByOcBOrderId(@Param("ocBOrderId") Long ocBOrderId);

    /**
     * 根据订单id+holdItemId删除
     *
     * @param ocBOrderId
     * @param id
     * @return
     */
    @Delete("delete from oc_b_order_hold_item where oc_b_order_id = #{ocBOrderId} and id = #{id} ")
    int delByOcBOrderIdAndId(@Param("ocBOrderId") Long ocBOrderId, @Param("id") Long id);

    class SqlProvider {

        public String selectDynamicTaskOrderHoldItem(@Param("name") String name,
                                                     @Param("num") int num, @Param("status") String status) {

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT DISTINCT oc_b_order_id FROM ");
            sb.append(name);
            sb.append(" WHERE `hold_status`='");
            sb.append(status);
            sb.append("' order by modifieddate asc");
            sb.append(" LIMIT ");
            sb.append(num);
            return sb.toString();
        }

    }

}