package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderAppointLogistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName OcBOrderAppointLogisticsMapper
 * @Description 指定快递
 * <AUTHOR>
 * @Date 2024/4/16 15:59
 * @Version 1.0
 */
@Mapper
@Component
public interface OcBOrderAppointLogisticsMapper extends ExtentionMapper<OcBOrderAppointLogistics> {

    @SelectProvider(type = OcBOrderAppointLogisticsMapper.SqlProvider.class, method = "select4CancelAppointLogistics")
    List<OcBOrderAppointLogistics> select4CancelAppointLogistics(@Param("now") String now, @Param("tableName") String tableName);

    class SqlProvider {
        public String select4CancelAppointLogistics(@Param("now") String now, @Param("tableName") String tableName) {
            StringBuilder sb = new StringBuilder();
            sb.append("SELECT ")
                    .append("*")
                    .append(" FROM ")
                    .append(tableName)
                    .append(" where cancel_appoint_time <= '")
                    .append(now)
                    .append("' and isactive = 'Y' order by id asc limit 100");
            return sb.toString();
        }
    }
}
