package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.model.constant.PresaleStatus;
import com.jackrain.nea.oc.oms.model.enums.*;
import com.jackrain.nea.oc.oms.model.relation.IpTaobaoOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.rpc.VpRpcService;
import com.jackrain.nea.st.model.table.StCOrderUrgentStrategyDO;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OrderUrgentStrategyService;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 淘宝订单中间表转换成全渠道订单
 *
 * @author: 易邵峰
 * @since: 2019-01-24
 * create at : 2019-01-24 13:15
 */
@Component
@Slf4j
public class TaobaoOrderTransferUtil {

    private RegionNewService regionService;

    private CpRpcService cpRpcService;

    private BuildSequenceUtil sequenceUtil;

    private PropertiesConf propertiesConf;

    private OrderUrgentStrategyService orderUrgentStrategyService;

    private VpRpcService vpRpcService;

    private OmsSystemConfig omsSystemConfig;
    @Autowired
    private StRpcService stRpcService;

    @Autowired
    OmsOrderAdvanceParseService omsOrderAdvanceParseService;

    public TaobaoOrderTransferUtil() {
    }

    @Autowired
    public TaobaoOrderTransferUtil(RegionNewService regionService, CpRpcService cpRpcService, BuildSequenceUtil sequenceUtil,
                                   OrderUrgentStrategyService orderUrgentStrategyService, VpRpcService vpRpcService,
                                   PropertiesConf propertiesConf, OmsSystemConfig omsSystemConfig) {
        this.regionService = regionService;
        this.cpRpcService = cpRpcService;
        this.sequenceUtil = sequenceUtil;
        this.orderUrgentStrategyService = orderUrgentStrategyService;
        this.vpRpcService = vpRpcService;
        this.propertiesConf = propertiesConf;
        this.omsSystemConfig = omsSystemConfig;
    }

    /**
     * 订单单行商品调整金额 （调整金额+税费）暂时将税费计算到调整金额中
     *
     * @param taobaoOrderItem 淘宝中间表订单明细信息
     * @return 订单单行商品调整金额
     */
    private BigDecimal buildAdjustFee(IpBTaobaoOrderItemEx taobaoOrderItem) {
        if (taobaoOrderItem.getPrice() == null
                || taobaoOrderItem.getNum() == null
                || StringUtils.equalsIgnoreCase("TRADE_CLOSED_BY_TAOBAO", taobaoOrderItem.getStatus())
                || StringUtils.equalsIgnoreCase("TRADE_CLOSED" , taobaoOrderItem.getStatus())) {
            return BigDecimal.ZERO;
        }

        if (taobaoOrderItem.getDivideOrderFee() == null || BigDecimal.ZERO.compareTo(taobaoOrderItem.getDivideOrderFee()) == 0) {
            return taobaoOrderItem.getAdjustFee();
        } else {
            // 支付金额 = 单行商品金额 - 商品优惠金额 - 平摊金额 + 调整金额
            BigDecimal adjustFee = Optional.ofNullable(taobaoOrderItem.getAdjustFee()).orElse(BigDecimal.ZERO);
            BigDecimal payment = taobaoOrderItem.getPrice().multiply(new BigDecimal(taobaoOrderItem.getNum()))
                    .subtract(Optional.ofNullable(taobaoOrderItem.getDiscountFee()).orElse(BigDecimal.ZERO))
                    .add(adjustFee);
            // divide_order_fee（分摊之后的实付金额） = payment  - part_mjz_discount（优惠分摊） + 税费（***）
            // 计算完成税费后将此金额添加到【调整金额】
            BigDecimal partMjzDiscount = Optional.ofNullable(taobaoOrderItem.getPartMjzDiscount()).orElse(BigDecimal.ZERO);
            BigDecimal taxFee = taobaoOrderItem.getDivideOrderFee().subtract(payment).add(partMjzDiscount);
            return taxFee.add(adjustFee)
                    .setScale(4, BigDecimal.ROUND_HALF_UP);
        }
    }

    /**
     * 订单调整金额 （调整金额+税费）暂时将税费计算到调整金额中
     *
     * @param taobaoOrderItems 淘宝中间表订单明细信息
     * @return 订单调整金额
     */
    private BigDecimal buildTotalAdjustFee(List<IpBTaobaoOrderItemEx> taobaoOrderItems) {
        if (CollectionUtils.isEmpty(taobaoOrderItems)) {
            return BigDecimal.ZERO;
        }


        return taobaoOrderItems.stream().filter(item -> !StringUtils.equalsIgnoreCase("TRADE_CLOSED_BY_TAOBAO" , item.getStatus())
        && !StringUtils.equalsIgnoreCase("TRADE_CLOSED" , item.getStatus()))// 过滤掉已退款的明细行
                .map(this::buildAdjustFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 排除已退款明细，明细状态为 ('TRADE_CLOSED_BY_TAOBAO')
     * 明细行单行商品金额 = 平台售价*数量
     * 订单优惠金额 = SUM(单行平摊金额) = total
     *
     * @param taobaoOrderItemList 淘宝中间表订单明细信息
     * @return 订单总优惠 可以去订单中间表
     */
    private BigDecimal buildTotalDiscountAmt(List<IpBTaobaoOrderItemEx> taobaoOrderItemList) {

        if (CollectionUtils.isEmpty(taobaoOrderItemList)) {
            return BigDecimal.ZERO;
        }
        return taobaoOrderItemList.stream()
                .filter(item -> (!StringUtils.equalsIgnoreCase("TRADE_CLOSED_BY_TAOBAO", item.getStatus())
                        && !StringUtils.equalsIgnoreCase("TRADE_CLOSED" , item.getStatus()))// 过滤掉已退款的明细行,
                        && item.getPartMjzDiscount() != null) // 平摊金额不为null的
                .map(IpBTaobaoOrderItemEx::getPartMjzDiscount) // 单行平摊金额
                .reduce(BigDecimal.ZERO, BigDecimal::add)// SUM(单行平摊金额)
                .setScale(4, BigDecimal.ROUND_HALF_UP); // 四舍五入保留4位小数

    }

    /**
     * 排除已退款明细，明细状态为 ('TRADE_CLOSED_BY_TAOBAO')
     * 明细行单行商品金额 = 平台售价*数量
     * 订单商品金额 = SUM(单行商品金额)
     *
     * @param taobaoOrderItemList 淘宝订单中间表，订单明细信息
     * @return 订单商品总金额
     */
    private BigDecimal buildTotalProductAmount(List<IpBTaobaoOrderItemEx> taobaoOrderItemList) {

        if (CollectionUtils.isEmpty(taobaoOrderItemList)) {
            return BigDecimal.ZERO;
        }

        return taobaoOrderItemList.stream()
                .filter(item -> (!StringUtils.equalsIgnoreCase("TRADE_CLOSED_BY_TAOBAO", item.getStatus())
                        && !StringUtils.equalsIgnoreCase("TRADE_CLOSED" , item.getStatus()))// 过滤掉已退款的明细行,
                        && item.getPrice() != null // 过滤掉 平台售价为空的
                        && item.getNum() != null) // 过滤掉 商品数量为空的
                .map(item -> item.getPrice().multiply(new BigDecimal(item.getNum()))) // 单行售价 = 平台售价*数量
                .reduce(BigDecimal.ZERO, BigDecimal::add)// SUM(单行售价)
                .setScale(4, BigDecimal.ROUND_HALF_UP); // 四舍五入保留4位小数

    }

    /**
     * 订单商品优惠总金额
     *
     * @param taobaoOrderItemList 淘宝中间表订单明细信息表
     * @return 订单商品优惠金额
     */
    private BigDecimal buildTotalProductDiscount(List<IpBTaobaoOrderItemEx> taobaoOrderItemList) {

        if (CollectionUtils.isEmpty(taobaoOrderItemList)) {
            return BigDecimal.ZERO;
        }
        return taobaoOrderItemList.stream()
                .filter(item -> (!StringUtils.equalsIgnoreCase("TRADE_CLOSED_BY_TAOBAO", item.getStatus())
                        && !StringUtils.equalsIgnoreCase("TRADE_CLOSED" , item.getStatus()))// 过滤掉已退款的明细行,
                        && item.getDiscountFee() != null) // 商品优惠金额不为null的
                .map(IpBTaobaoOrderItemEx::getDiscountFee) // 单行商品优惠金额
                .reduce(BigDecimal.ZERO, BigDecimal::add)// SUM(单行商品优惠金额)
                .setScale(4, BigDecimal.ROUND_HALF_UP); // 四舍五入保留4位小数
    }

    /**
     * 计算订单金额
     * “商品总额”+“物流费用”+“调整金额（包含税费）”-“订单优惠金额”-“商品优惠金额”
     *
     * @param tbOrderRelation 淘宝中间表订单信息
     * @return 计算订单金额
     */
    private BigDecimal buildOrderAmount(IpTaobaoOrderRelation tbOrderRelation) {

        IpBTaobaoOrder tbOrder = tbOrderRelation.getTaobaoOrder();
        return Optional.ofNullable(this.buildTotalProductAmount(tbOrderRelation.getTaobaoOrderItemList())).orElse(BigDecimal.ZERO) // 订单商品总金额
                .add(Optional.ofNullable(tbOrder.getPostFee()).orElse(BigDecimal.ZERO)) // 订单物流费用
                .add(this.buildTotalAdjustFee(tbOrderRelation.getTaobaoOrderItemList())) // 订单调整金额(税费+调整金额)
                .subtract(Optional.ofNullable(this.buildTotalDiscountAmt(tbOrderRelation.getTaobaoOrderItemList())).orElse(BigDecimal.ZERO)) //订单优惠金额
                .subtract(Optional.ofNullable(this.buildTotalProductDiscount(tbOrderRelation.getTaobaoOrderItemList())).orElse(BigDecimal.ZERO)) // 订单商品优惠金额
                .setScale(4, BigDecimal.ROUND_HALF_UP);
    }
    /**
     * <AUTHOR>
     * @Date 14:14 2021/4/16
     * @Description 订单折扣 = （总金额-配送费用 -服务费）/ 商品金额。
     */
    private  BigDecimal buildOrderDiscount(IpTaobaoOrderRelation tbOrderRelation){
        IpBTaobaoOrder tbOrder = tbOrderRelation.getTaobaoOrder();
        return (Optional.ofNullable(this.buildOrderAmount(tbOrderRelation)).orElse(BigDecimal.ZERO)
                .subtract(Optional.ofNullable(tbOrder.getPostFee()).orElse(BigDecimal.ZERO))
                .subtract(Optional.ofNullable(tbOrder.getCodFee()).orElse(BigDecimal.ZERO)))
                .divide(Optional.ofNullable(this.buildTotalProductAmount(tbOrderRelation.getTaobaoOrderItemList())).orElse(BigDecimal.ZERO) , 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 订单唯一码，通过明细tid、oid、sku+头表订单补充信息字段生成
     *
     * @param taobaoOrderRelation 淘宝订单关联表对象
     * @return 订单唯一码
     */
    private String buildUniqueKey(IpTaobaoOrderRelation taobaoOrderRelation) {

        StringBuilder sb = new StringBuilder();
        if (taobaoOrderRelation.getTaobaoOrder() != null) {
            sb.append(taobaoOrderRelation.getTaobaoOrder().getTid());
        }

        if (taobaoOrderRelation.getTaobaoOrderItemList() != null) {
            for (IpBTaobaoOrderItem taobaoItem : taobaoOrderRelation.getTaobaoOrderItemList()) {
                sb.append(taobaoItem.getOid());
                sb.append(taobaoItem.getSkuId());
            }
        }
        String sbLength = sb.toString();
        if (sbLength.length() >= 190) {
            sbLength = sbLength.substring(0, 190);
        }
        return sbLength;
    }

    /**
     * 解析是否为预售状态
     *
     * @param tbOrder 淘宝订单中间表对象
     * @return 预售状态值
     */
    private int parsePreSaleStatus(IpBTaobaoOrder tbOrder) {
        if (StringUtils.equalsIgnoreCase(tbOrder.getStepTradeStatus(), TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID)) {
            return PresaleStatus.PRE_SALE;
        } else {
            return PresaleStatus.NORMAL;
        }
    }

    /**
     * 解析是否有预售商品
     *
     * @param tbOrderRelation 淘宝订单中间表关联对象
     * @return 是否有预售商品。True=包含
     */
    private boolean hasPreSaleProduct(IpTaobaoOrderRelation tbOrderRelation) {
        for (IpBTaobaoOrderItemEx orderItem : tbOrderRelation.getTaobaoOrderItemList()) {
            if (this.checkProductIsPreSale(orderItem)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 解析是否有组合商品
     *
     * @param tbOrderRelation 淘宝订单中间表关联对象
     * @return 是否有组合商品。True=包含
     */
    private boolean hasCombineProduct(IpTaobaoOrderRelation tbOrderRelation) {
        for (IpBTaobaoOrderItemEx orderItem : tbOrderRelation.getTaobaoOrderItemList()) {
            if (orderItem.getProdSku() != null &&
                    (orderItem.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT
                            || orderItem.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 解析下单店铺的Title内容
     *
     * @param shopId 下单店铺ID
     * @return 下单店铺的Title内容
     */
    private String parseShopTitle(Long shopId) {
        if (shopId != null) {
            CpShop shopInfo = cpRpcService.selectShopById(shopId);
            if (shopInfo != null) {
                return shopInfo.getCpCShopTitle();
            }
        }
        return null;
    }

//    private VpBVipMemberDO selectVipMemberInfo(String buyerNick, long shopId) {
//        return this.vpRpcService.selectVipMember(buyerNick, shopId);
//    }

    private int parseOrderUrgency(IpTaobaoOrderRelation taobaoOrderRelation) {
        try {
            // 如果未开启查询会员紧急标功能，则直接返回0
            if (!this.omsSystemConfig.isTransferSelectVipUrgencyEnabled()) {
                return 0;
            }

            String selectVipEnabled = propertiesConf.getProperty("r3.oc.oms.transfer.select.vip.urgency.enabled");
            if (StringUtils.equalsIgnoreCase("false", selectVipEnabled)) {
                return 0;
            }
            Long shopId = taobaoOrderRelation.getTaobaoOrder().getCpCShopId();
            String buyerNick = taobaoOrderRelation.getTaobaoOrder().getBuyerNick();
//            VpBVipMemberDO vipMemberDO = this.selectVipMemberInfo(buyerNick, shopId);
//            if (vipMemberDO == null) {
//                return 0;
//            }
//            if (log.isDebugEnabled()) {
//                log.debug("TaobaoOrderTransfer.SelectVipMember.BuyerNick={},ShopId={},VipMember={}", buyerNick, shopId,
//                        vipMemberDO.toString());
//            }
//            Integer vipLevel = vipMemberDO.getMemberRank();
//            if (vipLevel == null) {
//                return 0;
//            }
//            StCOrderUrgentStrategyDO orderUrgentStrategyDO = this.orderUrgentStrategyService.selectOrderUrgentStrategy(shopId,
//                    vipLevel);
//            if (orderUrgentStrategyDO == null) {
//                return 0;
//            }
//            if (log.isDebugEnabled()) {
//                log.debug("TaobaoOrderTransfer.SelectStrategy.ShopId={},VipLevel={},Strategy={}", shopId, vipLevel,
//                        orderUrgentStrategyDO.toString());
//            }
//            if (StringUtils.equalsIgnoreCase("Y", orderUrgentStrategyDO.getIsUrgent())) {
//                return 1;
//            } else {
//                return 0;
//            }
            return 0;
        } catch (Exception ex) {
            log.error(LogUtil.format("parseOrderUrgencyError:{}"), Throwables.getStackTraceAsString(ex));
            return 0;
        }
    }

    /**
     * 转换成零售发货单对象
     *
     * @param taobaoOrderRelation 淘宝订单中间表关联对象
     * @param isHistoryOrder      是否为历史订单
     * @return 零售发货单对象
     */
    private OcBOrder buildOcBOrderFromIpTaobaoOrder(IpTaobaoOrderRelation taobaoOrderRelation,
                                                    boolean isHistoryOrder) {
        IpBTaobaoOrder tbOrder = taobaoOrderRelation.getTaobaoOrder();
        OcBOrder order = new OcBOrder();
        //id自增长
        order.setId(sequenceUtil.buildOrderSequenceId());
        order.setModifierename(SystemUserResource.ROOT_USER_NAME);
        order.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        order.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(order);

        //调整金额
        order.setAdjustAmt(this.buildTotalAdjustFee(taobaoOrderRelation.getTaobaoOrderItemList()));
        //所有SKU（最多五个超过，显示数量）。转单赋值空
        order.setAllSku(null);
        //应收金额. =订单金额
        order.setReceivedAmt(null);
        //审核时间
        order.setAuditTime(null);
        //自动审核状态
        order.setAutoAuditStatus(0);
        //单据编号
        order.setBillNo(sequenceUtil.buildBillNo());
        //买家邮箱
        order.setBuyerEmail(tbOrder.getBuyerEmail());
        //买家留言
        order.setBuyerMessage(tbOrder.getBuyerMessage());
        //到付代收金额. 如果是支付方式=到付，则赋值订单金额
        order.setCodAmt(BigDecimal.ZERO);
        //代销结算金额. 默认为0
        order.setConsignAmt(BigDecimal.ZERO);
        //代销运费. 默认为0
        order.setConsignShipAmt(BigDecimal.ZERO);
        //配送费用。如果为空，则赋值0.
        order.setShipAmt(tbOrder.getPostFee() == null ? BigDecimal.ZERO : tbOrder.getPostFee());
        // 处理解析物流公司信息
        this.parseLogisticsInfo(tbOrder, order);

        //发货实体仓. 赋值null，后续在分配物流中使用
        order.setCpCPhyWarehouseId(null);
        //下单店铺id.
        // TODO: 需要按照云店类型进行赋值。现在还没有云店类型。暂时不进行判断赋值
        order.setCpCShopId(tbOrder.getCpCShopId());
        //下单店铺标题。需要查个表获取Title（平台店铺信息表）
        //平台店铺标题
        CpShop shopInfo = null;
        if (tbOrder.getCpCShopId() != null) {
            shopInfo = cpRpcService.selectShopById(tbOrder.getCpCShopId());
        } else {
            throw new NDSException("平台店铺id为空!");
        }
        if (shopInfo != null) {
            order.setCpCShopTitle(shopInfo.getCpCShopTitle());
            //下单店仓编码. 到平台店铺信息表中获取下单店仓字段ID值；
//            order.setCpCStoreEcode(shopInfo.getCpCStoreEcode());
            //下单店仓名称. 到平台店铺信息表中获取下单店仓字段ID值；
//            order.setCpCStoreEname(shopInfo.getCpCStoreEname());
            //下单店仓id. 到平台店铺信息表中获取下单店仓字段ID值；
//            order.setCpCStoreId(shopInfo.getCpCStoreId());
            //下单店仓id. 到平台店铺信息表中获取下单卖家店铺名称
            order.setCpCShopSellerNick(shopInfo.getSellerNick());
            order.setCpCShopEcode(shopInfo.getEcode());
        } else {
            // 20190727修改：如果 平台店铺不存在，则不再继续保持。而是抛出异常，不允许转单操作
            throw new NDSException("平台店铺id=" + tbOrder.getCpCShopId() + "不存在");
        }
        //配货时间. 配货阶段进行赋值
        order.setDistributionTime(null);
        //交易结束时间
        order.setEndTime(tbOrder.getEndTime());
        //物流编码. 分配物流后进行赋值
        order.setExpresscode(null);
        //内部备注。后续手动填写
        order.setInsideRemark(null);
        //开票内容
        order.setInvoiceContent(null);
        //开票抬头
        order.setInvoiceHeader(tbOrder.getInvoiceName());
        //商品计算重量。是否需要进行称重。根据系统配置来进行赋值。可能没用。产品也不知道
        order.setIsCalcweight(0);
        //是否组合订单
        boolean hasCombine = this.hasCombineProduct(taobaoOrderRelation);
        order.setIsCombination(hasCombine ? 1 : 0);
        //是否生成开票通知。现在赋值为N。占用订单后再进行赋值
        order.setIsGeninvoiceNotice(0);
        // 是否已给物流。占单后再进行赋值
        // order.setIsGiveLogistic(0);
        // 是否有赠品.0.否。计算完赠品策略赋值
        // TODO: 后期需要根据逻辑判断进行赋值
        order.setIsHasgift(0);
        //包含预售商品
        /*boolean hasPreSale = this.hasPreSaleProduct(taobaoOrderRelation);
        order.setIsHaspresalesku(hasPreSale ? 1 : 0);*/
        //是否退款中
        order.setIsInreturning(0);
        //是否已经挂起
        order.setIsInterecept(0);
        //是否开票.若【开票抬头】为空，则为否（0），若有值，则为是（1）
        if (StringUtils.isNotEmpty(tbOrder.getInvoiceName())) {
            order.setIsInvoice(1);
        } else {
            order.setIsInvoice(0);
        }
        //是否虚拟订单。现在赋值为N
        order.setIsInvented(0);
        //京仓订单
        order.setIsJcorder(0);
        //实缺标记
//        order.setIsLackstock(0);
        //是否合并订单 默认0不合并
        order.setIsMerge(0);
        //是否拆分订单
        order.setIsSplit(0);
        //缺货重占次数
        order.setQtySplit(0L);
        //是否生成调拨零售
        // order.setIsTodrp(0);
        //应收平台金额（京东）
        order.setJdReceiveAmt(BigDecimal.ZERO);
        //京东结算金额
        order.setJdSettleAmt(BigDecimal.ZERO);
        //物流成本.需要计算成本。默认为0
        order.setLogisticsCost(BigDecimal.ZERO);
        //合并单据后生成的订单，对原始数据进行修改
        order.setMergeOrderId(null);
        //订单占单状态
        order.setOccupyStatus(0);
        //操作费.默认为0
        // order.setOperateAmt(BigDecimal.ZERO);
        //下单时间
        order.setOrderDate(tbOrder.getCreated());
        //订单优惠金额。
        order.setOrderDiscountAmt(this.buildTotalDiscountAmt(taobaoOrderRelation.getTaobaoOrderItemList()));
        //订单旗帜
        order.setOrderFlag(tbOrder.getSellerFlag());
        //订单来源
        String tradeFrom = tbOrder.getTradeFrom();
        if (tradeFrom.contains("WAP,")) {
            List<String> collect = Arrays.stream(tradeFrom.split(",")).distinct().collect(Collectors.toList());
            if (collect.size() == 1) {
                tradeFrom = collect.get(0);
            } else {
                collect.remove("WAP");
                tradeFrom = collect.get(0);
            }
        }
        order.setOrderSource(tradeFrom);
        //扫描出库时间.WMS回传值
        order.setScanTime(null);
        if (isHistoryOrder) {
            //为历史订单时直接修改状态为平台发货
            order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            order.setIsHistory("Y");
            // @历史订单出库时间赋值 任务ID 29976
            order.setScanTime(new Date());
        } else {
            //订单状态. 默认状态为50
            order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
            order.setIsHistory("N");
        }
        //订单标签
        order.setOrderTag(null);
        //订单类型
        order.setOrderType(OrderTypeEnum.NORMAL.getVal());
        //原始订单号。默认空
        order.setOrigOrderId(null);
        //原始退货单号
        order.setOrigReturnOrderId(null);
        //出库状态. WMS后调用,已出库未出库,现在没有用
        order.setOutStatus(null);
        //付款时间
        order.setPayTime(tbOrder.getPayTime());
        //支付方式（淘宝，天猫没有货到付款类型。PRD中写到COD=货到付款的判断可用忽视）
        order.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        //平台
        order.setPlatform(PlatFormEnum.TAOBAO.getCode());
        //预售状态
        // order.setSysPresaleStatus(this.parsePreSaleStatus(tbOrder));
        // 双11的预售状态。现在暂时赋值0
        String statusPayStep = tbOrder.getStepTradeStatus();
        order.setStatusPayStep(statusPayStep);
        if (StringUtils.isNotEmpty(statusPayStep)) {
            order.setDouble11PresaleStatus(1);
            order.setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
        } else {
            order.setDouble11PresaleStatus(0);
        }
        //商品总额 2019-10-09中间表商品总价为0时取商品价格的值
        order.setProductAmt(this.buildTotalProductAmount(taobaoOrderRelation.getTaobaoOrderItemList()));
        //商品优惠金额.
        BigDecimal prodDiscountAmt = this.buildTotalProductDiscount(taobaoOrderRelation.getTaobaoOrderItemList());
        order.setProductDiscountAmt(prodDiscountAmt);
        //商品数量
        if (tbOrder.getNum() == null || tbOrder.getNum() == 0) {
            List<IpBTaobaoOrderItemEx> taobaoOrderItemList = taobaoOrderRelation.getTaobaoOrderItemList();
            long count = taobaoOrderItemList.stream().mapToLong(IpBTaobaoOrderItemEx::getNum).sum();
            tbOrder.setNum(count);
        }
        order.setQtyAll(new BigDecimal(tbOrder.getNum()));
        order.setSkuKindQty(new BigDecimal(taobaoOrderRelation.getTaobaoOrderItemList().size()));
        // 已收金额。如果为空，则赋值0.
        order.setReceivedAmt(tbOrder.getPayment() == null ? BigDecimal.ZERO : tbOrder.getPayment());
        // 买家收货详细地址
        order.setReceiverAddress(tbOrder.getReceiverAddress().replaceAll(",", "::::"));
        //买家所在省
        String provinceName = tbOrder.getReceiverState();
        //买家所在市
        String cityName = tbOrder.getReceiverCity();
        //买家所在区ID。
        String areaName = tbOrder.getReceiverDistrict();
        ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(provinceName,
                cityName, areaName);
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getProvinceInfo() != null) {
            order.setCpCRegionProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
            order.setCpCRegionProvinceEcode(provinceCityAreaInfo.getProvinceInfo().getCode());
            order.setCpCRegionProvinceEname(provinceName);
        } else {
            order.setCpCRegionProvinceId(null);
            order.setCpCRegionProvinceEcode(null);
        }

        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getCityInfo() != null) {
            order.setCpCRegionCityId(provinceCityAreaInfo.getCityInfo().getId());
            order.setCpCRegionCityEcode(provinceCityAreaInfo.getCityInfo().getCode());
            order.setCpCRegionCityEname(provinceCityAreaInfo.getCityInfo().getName());
        } else {
            order.setCpCRegionCityId(null);
            order.setCpCRegionCityEcode(null);
        }
        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getAreaInfo() != null) {
            order.setCpCRegionAreaId(provinceCityAreaInfo.getAreaInfo().getId());
            order.setCpCRegionAreaEcode(provinceCityAreaInfo.getAreaInfo().getCode());
            order.setCpCRegionAreaEname(provinceCityAreaInfo.getAreaInfo().getName());
        } else {
            order.setCpCRegionAreaId(null);
            order.setCpCRegionAreaEcode(null);
        }
        order.setReceiverEmail(tbOrder.getBuyerEmail());
        order.setReceiverMobile(tbOrder.getReceiverMobile());
        order.setReceiverName(tbOrder.getReceiverName());
        order.setReceiverPhone(tbOrder.getReceiverPhone());

        //四级街道/乡镇
        if (StringUtils.isNotBlank(tbOrder.getReceiverTown())) {
            order.setCpCRegionTownEname(tbOrder.getReceiverTown());
        }

        order.setReceiverZip(tbOrder.getReceiverZip());
        //退款审核状态（AG使用）
        order.setRefundConfirmStatus(null);
        //退货状态
        order.setReturnStatus(null);
        //销售员编号
        order.setSalesmanId(null);
        //销售员名称
        order.setSalesmanName(null);

        if(StringUtils.isNotEmpty(tbOrder.getSellerMemo()) && tbOrder.getSellerMemo().length()>1000){
            //卖家备注
            order.setSellerMemo(tbOrder.getSellerMemo().substring(tbOrder.getSellerMemo().length()-1000,tbOrder.getSellerMemo().length()-1));
        }else {
            //卖家备注
            order.setSellerMemo(tbOrder.getSellerMemo());
        }
        //平台预售活动。暂时用不到。有可能是平台传输过来
        // order.setSendTime(null);
        //货到付款服务费。如果为空，则赋值0.
        order.setServiceAmt(tbOrder.getCodFee() == null ? BigDecimal.ZERO : tbOrder.getCodFee());
        //配送费用。如果为空，则赋值0.
        order.setShipAmt(tbOrder.getPostFee() == null ? BigDecimal.ZERO : tbOrder.getPostFee());
        //平台单号信息
        order.setSourceCode(tbOrder.getTid());
        //拆分原单单号
        order.setSplitOrderId(null);
        //订单补充信息
        order.setSuffixInfo(null);
        //系统备注
        order.setSysremark(null);
        //淘宝店铺编号（星盘使用）
        order.setTbStorecode(null);
        //初始平台单号（确定唯一）
        order.setTid(tbOrder.getTid());
        order.setMergeSourceCode(tbOrder.getTid());
        //订单唯一码，通过明细tid、oid、sku+头表订单补充信息字段生成
        //order.setUniqueKey(this.buildUniqueKey(taobaoOrderRelation));
        //下单用户
        order.setUserId(null);

        order.setUserNick(tbOrder.getBuyerNick());
        //版本信息
        order.setVersion(0L);
        //商品重量
        //TODO: 后期需要根据商品计算重量（乔丹暂时不需要）
        order.setWeight(BigDecimal.ZERO);
        //wms撤回状态调用WMS撤回是否成功。1=成功；2=失败
        order.setWmsCancelStatus(0);
        //仓储状态（拣货中，已打印，已装箱）
        //order.setWmsStatus(null);
        //是否插入核销流水
        // order.setIsWriteoff(0);
        //出库状态
        order.setOutStatus(1);
        //支付宝交易账号
        //order.setAlipayNo(tbOrder.getAlipayNo());
        //买家支付账号
        order.setBuyerAlipayNo(tbOrder.getBuyerAlipayNo());
        //订单总额.“商品总额”+“物流费用”+“调整金额”-“订单优惠金额”-“商品优惠金额”
        order.setOrderAmt(this.buildOrderAmount(taobaoOrderRelation));
        //订单折扣 = （总金额-配送费用 -服务费）/ 商品金额。
        order.setOrderDiscount(this.buildOrderDiscount(taobaoOrderRelation));
        order.setPresaleDepositTime(tbOrder.getPresaleDepositTime()); //预售付定金时间
        // 平台状态
        order.setPlatformStatus(tbOrder.getStatus());
        //购物金相关
        order.setBasicPriceUsed(tbOrder.getBasicPriceUsed());
        order.setExpandPriceUsed(tbOrder.getExpandPriceUsed());

        int isUrgency = this.parseOrderUrgency(taobaoOrderRelation);
        order.setIsOutUrgency(isUrgency);
//        for (IpBTaobaoOrderItemEx item : taobaoOrderRelation.getTaobaoOrderItemList()) {
//            ProductSku productSku = item.getProdSku();
//            if (productSku == null) {
//                continue;
//            }
//            if ("Y".equals(productSku.getIsVirtual())) {
//                order.setIsInvented(1);
//                order.setPriceLabel("Y");
//                order.setOrderType(OrderTypeEnum.DIFFPRICE.getVal());
//                break;
//            }
//        }
        //oaid
        order.setOaid(tbOrder.getOaid());

        //天猫打标-送货上门
        if (StringUtils.isNotBlank(tbOrder.getAsdpAds())) {
            order.setIsDeliveryToDoor(OcBOrderConst.IS_STATUS_IY);
        }
        return order;
    }

    /**
     * 解析物流公司赋值
     *
     * @param tbOrder 淘宝订单表
     * @param order   全渠道订单表
     */
    private void parseLogisticsInfo(IpBTaobaoOrder tbOrder, OcBOrder order) {
        // 若【淘宝订单中间表】中“物流类型”（shipping_type）为“ems”，则为“EMS”
        if (StringUtils.equalsIgnoreCase(CpRpcService.EMS_LOGISTICS_CODE, tbOrder.getShippingType())) {
            LogisticsInfo logisticsInfo = this.cpRpcService.selectLogisticsInfo(
                    CpRpcService.EMS_LOGISTICS_CODE);
            if (logisticsInfo != null) {
                order.setCpCLogisticsEcode(logisticsInfo.getCode());
                order.setCpCLogisticsEname(logisticsInfo.getName());
                order.setCpCLogisticsId(logisticsInfo.getId());
            } else {
                //物流公司编码. 赋值null，后续在分配物流中使用。如果是EMS的则进行赋值
                order.setCpCLogisticsEcode(null);
                //物流公司名称. 赋值null，后续在分配物流中使用
                order.setCpCLogisticsEname(null);
                //配送物流公司. 赋值null，后续在分配物流中使用
                order.setCpCLogisticsId(null);
            }
        } else {
            //物流公司编码. 赋值null，后续在分配物流中使用。如果是EMS的则进行赋值
            order.setCpCLogisticsEcode(null);
            //物流公司名称. 赋值null，后续在分配物流中使用
            order.setCpCLogisticsEname(null);
            //配送物流公司. 赋值null，后续在分配物流中使用
            order.setCpCLogisticsId(null);
        }
    }

    /**
     * 根据退款状态转换成退款Integer值
     *
     * @param refundStatus 退款状态
     * @return 退款Integer值
     */
    private int convertRefundStatusToInteger(String refundStatus) {
        if (StringUtils.equalsIgnoreCase(refundStatus, "WAIT_SELLER_AGREE")) {
            return 1;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "WAIT_BUYER_RETURN_GOODS")) {
            return 2;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "WAIT_SELLER_CONFIRM_GOODS")) {
            return 3;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "SELLER_REFUSE_BUYER")) {
            return 4;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "CLOSED")) {
            return 5;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "SUCCESS")) {
            return 6;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "NO_REFUND")) {
            return 0;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "8")) {
            // 微购,退款完成
            return 6;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "7")) {
            // 微购,退款中
            return 1;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "17")) {
            //微购,待商家同意退货
            return 1;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "18")) {
            // 微购,待买家发货
            return 2;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "19")) {
            // 微购,待卖家审核发货
            return 3;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "20")) {
            // 微购,取消退货
            return 5;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "21")) {
            // 微购,退货完成
            return 6;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "22")) {
            // 微购,商家审核退货通过
            return 1;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "23")) {
            // 微购,商家审核退货不通过
            return 4;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "24")) {
            // 微购,退货中
            return 2;
        } else {
            return -1;
        }
    }

    /**
     * 成交单价 = 成交金额 / 商品数量
     *
     * @param taobaoOrderItem 淘宝订单中间表明细数据
     * @return 成交价格
     */
    private BigDecimal calcOrderItemPrice(IpBTaobaoOrderItemEx taobaoOrderItem) {
        return calcOrderItemRealAmount(taobaoOrderItem).divide(new BigDecimal(taobaoOrderItem.getNum()), 4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
     *
     * @param taobaoOrderItem 淘宝订单中间表明细数据
     * @return 单行实际成交金额
     */
    private BigDecimal calcOrderItemRealAmount(IpBTaobaoOrderItemEx taobaoOrderItem) {
        if (taobaoOrderItem.getPrice() == null || taobaoOrderItem.getNum() == null) {
            return BigDecimal.ZERO;
        }
        return taobaoOrderItem.getPrice().multiply(new BigDecimal(taobaoOrderItem.getNum())) // 单行商品金额
                .add(buildAdjustFee(taobaoOrderItem)) // 调整金额
                .subtract(Optional.ofNullable(taobaoOrderItem.getPartMjzDiscount()).orElse(BigDecimal.ZERO)) //减去平摊金额
                .subtract(Optional.ofNullable(taobaoOrderItem.getDiscountFee()).orElse(BigDecimal.ZERO));//减去商品优惠金额
    }

    /**
     * 判断商品是否为预售商品
     *
     * @param taobaoOrderItem 淘宝订单中间表明细数据
     * @return 是否为预售商品。True=是
     */
    private boolean checkProductIsPreSale(IpBTaobaoOrderItemEx taobaoOrderItem) {
        if (taobaoOrderItem.getProdSku() != null
                && taobaoOrderItem.getProdSku().getSkuType() == SkuType.PRE_SALE_PRODUCT) {
            return true;
        }

        return false;
    }


    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            log.error(LogUtil.format("checkIsNeedTransferSkuUpperCase:{}"), Throwables.getStackTraceAsString(ex));
            return true;
        }
    }


//    /**
//     * 转换淘宝订单明细表
//     *
//     * @param ocBOrder        全渠道订单
//     * @param taobaoOrderItem 淘宝中间表明细
//     * @return 淘宝订单明细表
//     */
//    private OcBOrderItem buildOrderItemFromTaobaoOrderItem(OcBOrder ocBOrder, IpBTaobaoOrderItemEx taobaoOrderItem) {
//        OcBOrderItem item = new OcBOrderItem();
//        item.setId(sequenceUtil.buildOrderItemSequenceId());
//        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
//        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
//        item.setVersion(0L);
//
//        BaseModelUtil.initialBaseModelSystemField(item);
//
//        //活动编号. 默认赋值为null
//        item.setActiveId(null);
//        item.setAddIntegral(null);
//        //调整金额.若为组合商品的值，则【淘宝订单中间表】明细表的“调整金额”*【组合商品】数量*【组合商品】价格比例，
//        // 若不为组合商品，则【淘宝订单中间表】明细表的“调整金额”
//        item.setAdjustAmt(this.calcProdAdjustAmount(taobaoOrderItem));
//        //优惠金额.若为组合商品的值，则【淘宝订单中间表】明细表的“优惠金额”*【组合商品】数量*【组合商品】价格比例，
//        // 若不为组合商品，则【淘宝订单中间表】明细表的“优惠金额”
//        item.setAmtDiscount(this.calcProdDiscountAmount(taobaoOrderItem));
//        //退货金额.默认为0
//        item.setAmtRefund(BigDecimal.ZERO);
//        //国标码。SKU 69码。从条码档案中有一个69码字段
//        if (taobaoOrderItem.getProdSku() != null) {
//            item.setBarcode(taobaoOrderItem.getProdSku().getBarcode69());
//        } else {
//            item.setBarcode(null);
//        }
//        //使用积分
//        item.setBuyerUsedIntegral(0L);
//        //分销价格。默认为0
//        item.setDistributionPrice(BigDecimal.ZERO);
//        // 扩展内容. 在转单过程中用不到
//        item.setExtendText(null);
//        //福袋条码
//        // TODO: 如果为福袋条码则进行赋值原始福袋条码
//        item.setGiftbagSku(null);
//        //组合名称
//        item.setGroupName(null);
//        //是否已经占用库存
//        item.setIsAllocatestock(0);
//        //买家是否已评价
//        item.setIsBuyerRate(0);
//        //是否是赠品
//        item.setIsGift(0);
//        //实缺标记
//        item.setIsLackstock(0);
//        //预售状态
//        boolean isPreSaleProd = this.checkProductIsPreSale(taobaoOrderItem);
//        item.setIsPresalesku(isPreSaleProd ? 1 : 0);
//        //商品数字编号
//        item.setNumIid("0");
//        //订单编号
//        item.setOcBOrderId(ocBOrder.getId());
//        //子订单编号(明细编号)
//        //liqb 更改ooid类型从Long类型改成String类型
//        if (null != taobaoOrderItem.getOid()) {
//            item.setOoid(String.valueOf(taobaoOrderItem.getOid()));
//        }
//        //整单平摊金额
//        item.setOrderSplitAmt(
//                taobaoOrderItem.getPartMjzDiscount() == null ? BigDecimal.ZERO : taobaoOrderItem.getPartMjzDiscount()
//        );
//        //商品路径
//        item.setPicPath(taobaoOrderItem.getPicPath());
//        //成交价格.若为组合商品的值，则【则【淘宝订单中间表】明细表的（“成本价”*“数量”-“优惠费用”+“调整费用”）/“数量”*【组合商品】的“价格比例”，
//        // 若不为组合商品，则【淘宝订单中间表】明细表的（“成本价”*“数量”-“优惠费用”+“调整费用”）/“数量”
//        item.setPrice(this.calcOrderItemPrice(taobaoOrderItem));
//        //标准价。【淘宝订单中间表】明细表的“标准价”
//        item.setPriceList(taobaoOrderItem.getPrice() == null ? BigDecimal.ZERO : taobaoOrderItem.getPrice());
//        //数量
//        item.setQty(BigDecimal.valueOf(taobaoOrderItem.getNum()));
//        //已退数量。默认为0
//        item.setQtyRefund(BigDecimal.ZERO);
//        //单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
//        item.setRealAmt(this.calcOrderItemRealAmount(taobaoOrderItem));
//        // 平台退款编号
//        item.setRefundId(taobaoOrderItem.getRefundId());
//        //退款状态
//        // 如果是退款完成，或者是交易关闭 状态=6
//        item.setRefundStatus(this.convertRefundStatusToInteger(taobaoOrderItem.getRefundStatus()));
//
//        //规格。商品条码. normsdetailnames
//        //条码id
//        //标准重量。商品条码. weight
//        //条码编码。
//        //若为组合商品的值，则【淘宝订单中间表】明细表的在【组合商品】中对应的实际商品编码（商品档案中存在，且状态为已启用），则【淘宝订单中间表】明细表的“商品编码”
//        this.initialTaobaoOrderItem(taobaoOrderItem, item);
//
//        //库位。不用赋值
//        item.setStoreSite(null);
//        //标题
//        item.setTitle(taobaoOrderItem.getTitle());
//
//        item.setTid(ocBOrder.getTid());
//        return item;
//    }


    /**
     * 2019-07-30 新增福袋组合商品
     * 订单明细解析时若为福袋或者组合商品是明细为中间表数据
     */
    private OcBOrderItem buildOrderItemFromTaobaoOrderItemNew(OcBOrder ocBOrder, IpBTaobaoOrderItemEx taobaoOrderItem, boolean isHistoryOrder) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(item);

        //活动编号. 默认赋值为null
        item.setActiveId(null);
        // 若不为组合商品，则【淘宝订单中间表】明细表的“调整金额”  调整金额 = 订单实际调整金额 + 税费
        item.setAdjustAmt(this.buildAdjustFee(taobaoOrderItem));
        //优惠金额.若为组合商品的值，则【淘宝订单中间表】明细表的“优惠金额”*【组合商品】数量*【组合商品】价格比例，
        // 若不为组合商品，则【淘宝订单中间表】明细表的“优惠金额”
        item.setAmtDiscount(taobaoOrderItem.getDiscountFee());
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //国标码。SKU 69码。从条码档案中有一个69码字段
        if (taobaoOrderItem.getProdSku() != null) {
            item.setBarcode(taobaoOrderItem.getProdSku().getBarcode69());
        } else {
            item.setBarcode(null);
        }
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //组合名称
        item.setGroupName(null);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //是否是赠品
        item.setIsGift(0);
        //实缺标记
        item.setIsLackstock(0);
        //预售状态
        boolean isPreSaleProd = this.checkProductIsPreSale(taobaoOrderItem);
        item.setIsPresalesku(isPreSaleProd ? 1 : 0);
        //商品数字编号
        item.setNumIid(taobaoOrderItem.getNumIid());
        //平台条码
        item.setSkuNumiid(taobaoOrderItem.getSkuId() + "");

        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        //子订单编号(明细编号)
        //liqb 更改ooid类型从Long类型改成String类型
        if (null != taobaoOrderItem.getOid()) {
            item.setOoid(String.valueOf(taobaoOrderItem.getOid()));
        }
        //整单平摊金额
        item.setOrderSplitAmt(
                taobaoOrderItem.getPartMjzDiscount() == null ? BigDecimal.ZERO : taobaoOrderItem.getPartMjzDiscount()
        );
        //商品路径
        item.setPicPath(taobaoOrderItem.getPicPath());
        //成交价格.若为组合商品的值，则【则【淘宝订单中间表】明细表的（“成本价”*“数量”-“优惠费用”+“调整费用”）/“数量”*【组合商品】的“价格比例”，
        // 若不为组合商品，则【淘宝订单中间表】明细表的（“成本价”*“数量”-“优惠费用”+“调整费用”）/“数量”
        //todo 2020-04-11 经过军哥批准  改掉
        //this.calcOrderItemPrice(taobaoOrderItem)
        item.setPrice(taobaoOrderItem.getPrice());
        // 成交单价
        item.setPriceActual(calcOrderItemPrice(taobaoOrderItem));
        //标准价。【淘宝订单中间表】明细表的“标准价”
        // item.setPriceList(taobaoOrderItem.getPrice() == null ? BigDecimal.ZERO : taobaoOrderItem.getPrice());
        //taobaoOrderItem.getPrice() == null ? BigDecimal.ZERO : taobaoOrderItem.getPrice()
        item.setPriceList(taobaoOrderItem.getProdSku().getPricelist());
        //数量
        item.setQty(BigDecimal.valueOf(taobaoOrderItem.getNum()));
        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //单行实际成交金额. s.price * s.num - s.discount_fee + s.adjust_fee- part_mjz_discount
        item.setRealAmt(this.calcOrderItemRealAmount(taobaoOrderItem));
        // 平台退款编号
        item.setRefundId(taobaoOrderItem.getRefundId());
        //退款状态
        // 如果是退款完成，或者是交易关闭 状态=6
        if (isHistoryOrder) {
            item.setRefundStatus(0);
        } else {
            item.setRefundStatus(this.convertRefundStatusToInteger(taobaoOrderItem.getRefundStatus()));
        }
        //规格。商品条码. normsdetailnames
        //条码id
        //标准重量。商品条码. weight
        //条码编码。
        //若为组合商品的值，则【淘宝订单中间表】明细表的在【组合商品】中对应的实际商品编码（商品档案中存在，且状态为已启用），则【淘宝订单中间表】明细表的“商品编码”
        initialTaobaoOrderItem(taobaoOrderItem, item);

        //库位。不用赋值
//        item.setStoreSite(null);
        //标题
        item.setTitle(taobaoOrderItem.getTitle());

        item.setTid(ocBOrder.getTid());

        //购物金相关
        //购物金核销子订单本金分摊金额 平台下来单位为‘分’ 转为‘元’
        if (StringUtils.isNotEmpty(taobaoOrderItem.getExpandCardBasicPriceUsedSuborder())) {
            BigDecimal value = new BigDecimal(taobaoOrderItem.getExpandCardBasicPriceUsedSuborder());
            item.setExpandCardBasicPriceUsedSuborder(value);
        }
        //购物金核销子订单权益金分摊金额  平台下来单位为‘分’ 转为‘元’
        if (StringUtils.isNotEmpty(taobaoOrderItem.getExpandCardExpandPriceUsedSuborder())) {
            BigDecimal value = new BigDecimal(taobaoOrderItem.getExpandCardExpandPriceUsedSuborder());
            item.setExpandCardExpandPriceUsedSuborder(value);
        }

        if (item.getRealAmt().compareTo(BigDecimal.ZERO) == 0) {
            item.setGiftType("2"); //平台赠品
            item.setIsGift(1);
            ocBOrder.setIsHasgift(1);
            StCShopStrategyDO strategyByCpCshopId =
                    stRpcService.selectOcStCShopStrategyByCpCshopId(ocBOrder.getCpCShopId());
            if (strategyByCpCshopId != null){
                String canSplit = strategyByCpCshopId.getCanSplit();
                if ("Y".equals(canSplit)) {
                    item.setIsGiftSplit(2);
                } else {
                    item.setIsGiftSplit(1);
                }
            }
        }
        //给预计发货时间赋值
        if (StringUtils.isNotEmpty(taobaoOrderItem.getEstimateConTime())){
            item.setEstimateConTime(omsOrderAdvanceParseService.parseColumn(taobaoOrderItem.getEstimateConTime(),ocBOrder.getPayTime()));
            item.setIsExistConTime(AdvanceConstant.IS_EXIST_CON_TIME_0);
            ocBOrder.setDouble11PresaleStatus(1);
            ocBOrder.setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
        }
        item.setPsCBrandId(taobaoOrderItem.getProdSku().getPsCBrandId());
        return item;
    }

    /**
     * 初始化TaobaoOrderItem内容
     * 2019-07-30 组合福袋商品修改
     *
     * @param taobaoOrderItem 淘宝中间表数据
     * @param item            需要赋值的taobaoorderItem
     */

    private void initialTaobaoOrderItem(IpBTaobaoOrderItemEx taobaoOrderItem, OcBOrderItem item) {
        if (taobaoOrderItem.getProdSku() != null) {
            item.setPsCProId(taobaoOrderItem.getProdSku().getProdId());
            // ProECode
            item.setPsCProEcode(taobaoOrderItem.getProdSku().getProdCode());
            item.setPsCSkuId(taobaoOrderItem.getProdSku().getId());
            item.setSex(taobaoOrderItem.getProdSku().getSex());
            //2019-08-30吊牌价改为取商品表数据
            item.setPriceTag(taobaoOrderItem.getProdSku().getPricelist()); //吊牌价
            item.setPsCClrEcode(taobaoOrderItem.getProdSku().getColorCode());
            item.setPsCClrEname(taobaoOrderItem.getProdSku().getColorName());
            item.setPsCClrId(taobaoOrderItem.getProdSku().getColorId());
            item.setPsCSizeEcode(taobaoOrderItem.getProdSku().getSizeCode());
            item.setPsCSizeEname(taobaoOrderItem.getProdSku().getSizeName());
            item.setPsCSizeId(taobaoOrderItem.getProdSku().getSizeId());
            item.setPsCProMaterieltype(taobaoOrderItem.getProdSku().getMaterialType());
            item.setStandardWeight(taobaoOrderItem.getProdSku().getWeight());
            item.setSkuSpec(taobaoOrderItem.getProdSku().getSkuSpec());
            item.setProType(NumberUtils.toLong(taobaoOrderItem.getProdSku().getSkuType() + ""));
            item.setPsCSkuPtEcode(taobaoOrderItem.getProdSku().getSkuEcode());
            //平台商品名称
            item.setPtProName(taobaoOrderItem.getTitle());
            //商品名称
            item.setPsCProEname(taobaoOrderItem.getProdSku().getName());
            item.setPsCSkuEname(taobaoOrderItem.getProdSku().getSkuName());
            // 供应类型 0 普通 1.代销轻供 2.寄售轻供
            item.setPsCProSupplyType(taobaoOrderItem.getProdSku().getPsCProSupplyType());
            // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
            String psSkuEcode = taobaoOrderItem.getOuterSkuId();
            item.setMDim4Id(taobaoOrderItem.getProdSku().getMDim4Id());
            item.setMDim6Id(taobaoOrderItem.getProdSku().getMDim6Id());
            if ("Y".equals(taobaoOrderItem.getProdSku().getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            if (StringUtils.isBlank(psSkuEcode)) {
                psSkuEcode = taobaoOrderItem.getOuterIid();
            }
            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            if (taobaoOrderItem.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT
                    || taobaoOrderItem.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT) {
                //为福袋或者组合商品
                item.setPsCSkuEcode(taobaoOrderItem.getProdSku().getSkuEcode()); //虚拟条码
                //item.setPsCProEname(taobaoOrderItem.getTitle()); //虚拟条码商品名称取中间表的名称
                //由于数据库做了对尺寸code和商品code做了非空限制
                item.setPsCSizeEcode(psSkuEcode);
                item.setPsCProEcode(psSkuEcode);
                item.setQtyGroup(BigDecimal.valueOf(taobaoOrderItem.getNum())); //组合商品数量
                item.setProType(NumberUtils.toLong(SkuType.NO_SPLIT_COMBINE + ""));

            } else {
                item.setPsCSkuEcode(taobaoOrderItem.getProdSku().getSkuEcode());
            }
            if ("Y".equals(taobaoOrderItem.getProdSku().getIsEnableExpiry())){
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            item.setMDim4Id(taobaoOrderItem.getProdSku().getMDim4Id());
            item.setMDim6Id(taobaoOrderItem.getProdSku().getMDim6Id());
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }

    /**
     * 将淘宝中间表转换成付款信息表
     *
     * @param ocOrderInfo      已生成的全渠道订单信息
     * @param tbOrderPromotion 淘宝优惠中间表信息
     * @return 订单优惠信息
     */
    private OcBOrderPromotion buildOrderPromotionFromTaobaoOrderPromotion(OcBOrder ocOrderInfo,
                                                                          IpBTaobaoOrderPromotion tbOrderPromotion) {
        OcBOrderPromotion orderPromotion = new OcBOrderPromotion();

        orderPromotion.setId(sequenceUtil.buildOrderPromotionSequenceId());
        orderPromotion.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderPromotion.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        orderPromotion.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(orderPromotion);
        //活动编号。默认为null
        orderPromotion.setActiveId(null);
        //优惠活动描述
        orderPromotion.setPromotionDesc(tbOrderPromotion.getPromotionDesc());
        //赠送商品id。默认为空
        orderPromotion.setGiftItemId(null);
        //赠送商品编码。默认为空
        orderPromotion.setGiftItemCode(null);
        //赠送商品
        orderPromotion.setGiftItemName(tbOrderPromotion.getGiftItemName());
        //优惠名称
        orderPromotion.setPromotionName(tbOrderPromotion.getPromotionName());
        //优惠金额
        orderPromotion.setAmtDiscount(tbOrderPromotion.getDiscountFee());
        //赠送数量
        orderPromotion.setGiftItemQty(null);

        orderPromotion.setOcBOrderId(ocOrderInfo.getId());
        return orderPromotion;
    }

    /**
     * 转换成天猫订单信息表
     *
     * @param ocOrderInfo   全渠道订单信息
     * @param ipTaobaoOrder 淘宝订单中间表信息
     * @return 天猫订单信息表
     */
    private OcBOrderTaobao buildOrderTaobaoFromTaobaoOrder(OcBOrder ocOrderInfo, IpBTaobaoOrder ipTaobaoOrder) {
        OcBOrderTaobao orderTb = new OcBOrderTaobao();

        //id自增长
        orderTb.setId(sequenceUtil.buildOrderTaobaoSequenceId());

        orderTb.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderTb.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        orderTb.setVersion(0L);
        orderTb.setOcBOrderId(ocOrderInfo.getId());

        BaseModelUtil.initialBaseModelSystemField(orderTb);
        //是否同步接单. 默认为否
        orderTb.setIsAllocatexp(0);
        //是否同步接单确认. 默认为否
        orderTb.setIsAcceptxp(0);
        //3pl标记。默认为空
        orderTb.setThreePlFlag(null);
        //云店店铺名称
        orderTb.setCloudStore(ipTaobaoOrder.getCloudStore());
        //O2O店铺编号
        orderTb.setO2oShopid(ipTaobaoOrder.getO2oShopid());
        //O2O店铺名称
        orderTb.setO2oShopName(ipTaobaoOrder.getO2oShopName());
        //云店订单标记
        if (ipTaobaoOrder.getOrdertaking() != null) {
            orderTb.setOrderTaking(ipTaobaoOrder.getOrdertaking().intValue());
        } else {
            orderTb.setOrderTaking(0);
        }

        //是否门店自提。默认为否
        orderTb.setIsStoreoneself(0);
        //平台店铺id
        orderTb.setCpCShopId(ipTaobaoOrder.getCpCShopId());
        orderTb.setCpCShopTitle(this.parseShopTitle(ipTaobaoOrder.getCpCShopId()));
        //是否天猫直送
        orderTb.setIsTmallDelivery(ipTaobaoOrder.getTmalldelivery() == null ? 0 : 1);
        //3PL有时效订单标
        orderTb.setThreePlTimingFlag(null);
        //80：当日达、81,：次日达；84:多日达
        orderTb.setCnservice(ipTaobaoOrder.getCnservice());
        //预约时间 格式2018-03-29
        orderTb.setOsDate(ipTaobaoOrder.getOsdate());
        //预计送达时间 格式同上
        orderTb.setEsDate(ipTaobaoOrder.getEsdate());
        //预约送达时间段 格式00:00 ~23:59
        orderTb.setOsRange(ipTaobaoOrder.getOsrange());
        //预计送达时间段 格式同上
        orderTb.setEsRange(ipTaobaoOrder.getEsrange());
        // 截单时间到分钟
        // TODO：字段类型不一样
//        orderTb.setCutoffMinutes(ipTaobaoOrder.getCutoffminutes());
        //相对时间，单位为天 0当日达，1次日达，2隔日达，3三日达，4四日达
        orderTb.setEsTime(ipTaobaoOrder.getEstime());
        //最晚发货时间 格式2018-03-29 19:00:00
        orderTb.setDeliveryTime(ipTaobaoOrder.getDeliverytime());
        //最晚揽收时间
        orderTb.setCollectTime(ipTaobaoOrder.getCollecttime());
        //最晚派送时间
        orderTb.setSendTime(ipTaobaoOrder.getSendTime());
        //最晚签收时间
        orderTb.setSignTime(ipTaobaoOrder.getSigntime());
        //应派送cp，多个用逗号分隔，例如YTO,YUNDA
        orderTb.setDeliveryCps(ipTaobaoOrder.getDeliverycps());
        //发货仓，是商家在商家中心后台维护的仓code
        orderTb.setStoreCode(ipTaobaoOrder.getStorecode());

        //聚单目的中心
        orderTb.setGatherDestCenter(ipTaobaoOrder.getGatherlastcenter());
        //聚单目的网点
        orderTb.setGatherDestStation(ipTaobaoOrder.getGatherstation());
        return orderTb;
    }

    /**
     * 将淘宝中间表转换成付款信息表
     *
     * @param order   已生成的全渠道订单信息
     * @param tbOrder 淘宝中间表订单信息
     * @return 付款信息表
     */
    private OcBOrderPayment buildOrderPaymentFromTaobaoOrder(OcBOrder order, IpBTaobaoOrder tbOrder) {
        OcBOrderPayment orderPayment = new OcBOrderPayment();
        orderPayment.setId(sequenceUtil.buildOrderPaymentSequenceId());
        orderPayment.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderPayment.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        orderPayment.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(orderPayment);
        //orderPayment.setPaymentNo(order.getAlipayNo());
        orderPayment.setOcBOrderId(order.getId());
        //付款时间
        orderPayment.setPayTime(tbOrder.getPayTime());
        //完成时间
        orderPayment.setEndTime(tbOrder.getEndTime());
        BigDecimal receivedAmt = tbOrder.getStepPaidFee() ==null ? tbOrder.getPayment(): tbOrder.getStepPaidFee();
        //支付金额  去实付金额 不取收货人金额
        orderPayment.setPaymentAmt(receivedAmt);
        //订单金额
        orderPayment.setAmtOrder(tbOrder.getPayment());
        //付款方式
        orderPayment.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        //备注
        orderPayment.setRemark(null);
        //付款状态（1已付款，0未付款）
        orderPayment.setPayStatus(OmsPayStatus.PAID.toInteger());

        return orderPayment;
    }

    /**
     * 淘宝订单
     *
     * @param taobaoOrder    淘宝订单中间表关联关系
     * @param isHistoryOrder 是否为历史订单信息
     * @return 零售发货单关联对象
     */
    public OcBOrderRelation taobaoOrderToOrder(IpTaobaoOrderRelation taobaoOrder,
                                               boolean isHistoryOrder) {
        OcBOrderRelation orderRelation = new OcBOrderRelation();
        if (log.isDebugEnabled()) {
            log.debug("Start BuildOcOrder OrderNo=" + taobaoOrder.getOrderNo());
        }
        OcBOrder orderInfo = this.buildOcBOrderFromIpTaobaoOrder(taobaoOrder, isHistoryOrder);
        if (log.isDebugEnabled()) {
            log.debug("Finish BuildOcOrder OrderNo=" + taobaoOrder.getOrderNo());
        }
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        for (IpBTaobaoOrderItemEx taobaoItem : taobaoOrder.getTaobaoOrderItemList()) {
            OcBOrderItem item = this.buildOrderItemFromTaobaoOrderItemNew(orderInfo, taobaoItem,isHistoryOrder);
            orderItemList.add(item);
        }
        if (log.isDebugEnabled()) {
            log.debug("Finish BuildOcOrderItem OrderNo=" + taobaoOrder.getOrderNo());
        }

        if (this.omsSystemConfig.isTransferTaobaoOrderOtherItemEnabled()) {
            if (log.isDebugEnabled()) {
                log.debug("Start BuildOcOrderPromotion OrderNo=" + taobaoOrder.getOrderNo());
            }
            List<OcBOrderPromotion> promotionList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(taobaoOrder.getTaobaoOrderPromotionList())) {
                for (IpBTaobaoOrderPromotion taobaoPromotion : taobaoOrder.getTaobaoOrderPromotionList()) {
                    OcBOrderPromotion item = this.buildOrderPromotionFromTaobaoOrderPromotion(orderInfo,
                            taobaoPromotion);
                    promotionList.add(item);
                }
            }
            if (log.isDebugEnabled()) {
                log.debug("Finish BuildOcOrderPromotion OrderNo=" + taobaoOrder.getOrderNo());
            }

            if (log.isDebugEnabled()) {
                log.debug("Start BuildOcOrderPayment OrderNo=" + taobaoOrder.getOrderNo());
            }
            OcBOrderPayment orderPayment = this.buildOrderPaymentFromTaobaoOrder(orderInfo, taobaoOrder.getTaobaoOrder());
            List<OcBOrderPayment> orderPaymentList = new ArrayList<>();
            orderPaymentList.add(orderPayment);
            if (log.isDebugEnabled()) {
                log.debug("Finish BuildOcOrderPayment OrderNo=" + taobaoOrder.getOrderNo());
            }

            if (log.isDebugEnabled()) {
                log.debug("Start BuildOcOrderTaobaoFromTaobaoOrder OrderNo=" + taobaoOrder.getOrderNo());
            }
            OcBOrderTaobao orderTaobaoInfo = this.buildOrderTaobaoFromTaobaoOrder(orderInfo, taobaoOrder.getTaobaoOrder());
            if (log.isDebugEnabled()) {
                log.debug("Finish BuildOcOrderTaobaoFromTaobaoOrder OrderNo=" + taobaoOrder.getOrderNo());
            }

            orderRelation.setOrderPromotionList(promotionList);
            orderRelation.setOrderTaobao(orderTaobaoInfo);
            orderRelation.setOrderPaymentList(orderPaymentList);
        }
//        if (log.isDebugEnabled()) {
//            log.debug("Start buildOrderLink OrderNo=" + taobaoOrder.getOrderNo());
//        }
//        OcBOrderLink orderLink = this.buildOrderLink(orderInfo, taobaoOrder.getTaobaoOrder());
//        if (log.isDebugEnabled()) {
//            log.debug("Finish buildOrderLink OrderNo=" + taobaoOrder.getOrderNo());
//        }
//        orderRelation.setOrderLink(orderLink);

        orderRelation.setOrderInfo(orderInfo);
        orderRelation.setOrderItemList(orderItemList);

        return orderRelation;
    }
}
