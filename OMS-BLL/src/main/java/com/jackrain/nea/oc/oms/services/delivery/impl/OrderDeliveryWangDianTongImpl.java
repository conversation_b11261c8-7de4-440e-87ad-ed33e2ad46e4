package com.jackrain.nea.oc.oms.services.delivery.impl;


import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.common.ReferenceUtil;
import com.jackrain.nea.cpext.model.CpCLogisticsItem;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.api.qimen.StandplatOrderDeliveryCmd;
import com.jackrain.nea.ip.model.qimen.StandplatLogisticsSendItemModel;
import com.jackrain.nea.ip.model.qimen.StandplatSendModel;
import com.jackrain.nea.oc.oms.mapper.task.OcBTobedeliveryorderConfirmTaskMapper;
import com.jackrain.nea.oc.oms.model.StandplatSendCallUpdateOrderModel;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.StandplatSendCallUpdateOrderServcice;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Auther: chenhao
 * @Date: 2022-07-07 10:57
 * @Description: 旺店通 平台发货调用  发货确认接口
 */

@Slf4j
@Component
public class OrderDeliveryWangDianTongImpl implements OrderDeliveryCmd {

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private OcBTobedeliveryorderConfirmTaskMapper taskMapper;
    @Autowired
    private StandplatSendCallUpdateOrderServcice callUpdateOrderServcice;

    // 中间表
    private final static String OC_B_TOBEDELIVERYORDER_CONFIRM_TASK = "oc_b_tobedeliveryorder_confirm_task";
    //奇门发货确认接口
    private final static String TAOBAO = "taobao.qimen.deliveryorder.confirm";

    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {

        log.info(LogUtil.format("OrderDeliveryWangDianTongImpl.deliveryDeal ocBOrderRelation:{}",
                "OrderDeliveryWangDianTongImpl.start"), JSONObject.toJSONString(ocBOrderRelation));

        OcBOrder ocOrder = ocBOrderRelation.getOrderInfo();
        List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();

        StandplatOrderDeliveryCmd deliveryCmd = (StandplatOrderDeliveryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
                StandplatOrderDeliveryCmd.class.getName(), "ip", "1.4.0");

        StandplatSendModel standplatSendModel = new StandplatSendModel();
        standplatSendModel.setMethod(TAOBAO);
//        standplatSendModel.setPlatform(ocOrder.getGwSourceGroup() != null ? Integer.parseInt(ocOrder.getGwSourceGroup()) : ocOrder.getPlatform());
        standplatSendModel.setPlatform(ocOrder.getPlatform());
        //出库单号
        standplatSendModel.setOutBillNo(ocOrder.getTid());
        //查店铺
        CpShop cpShop = cpRpcService.selectShopById(ocOrder.getCpCShopId());
        standplatSendModel.setWarehouseCode(cpShop.getEcode());
        standplatSendModel.setOrderType("JYCK");
        standplatSendModel.setCustomerId(cpShop.getCustomerId());

        //查物流公司档案 然后取明细 拿平台对应的物流
//        CpCLogistics cpLogistics = cpRpcService.queryLogistics(ocOrder.getCpCLogisticsEcode());
        CpCLogisticsItem cpLogisticsItem = cpRpcService.selectCpCLogisticsEcode(Long.valueOf(standplatSendModel.getPlatform()), ocOrder.getCpCLogisticsId());
        if (cpLogisticsItem == null) {
            throw new NDSException("查询物流档案信息为空！");
        }
        standplatSendModel.setLogisticsCode(cpLogisticsItem.getCpCLogisticsEcode());
        standplatSendModel.setExpressCode(ocOrder.getExpresscode());


        if (CollectionUtils.isNotEmpty(orderItemList)) {
            List<StandplatLogisticsSendItemModel> itemModelList = new ArrayList<>();
//            //查平台商品
//            SgChannelProductQueryCmd o = (SgChannelProductQueryCmd) ReferenceUtil.refer(ApplicationContextHandle.getApplicationContext(),
//                    SgChannelProductQueryCmd.class.getName(), "sg", "1.0");
//            SgChannelProductQueryRequest sgChannelProductQueryRequest = new SgChannelProductQueryRequest();
//            sgChannelProductQueryRequest.setCpCShopId(ocOrder.getCpCShopId());
//            List<Long> skuIds = orderItemList.stream().map(OcBOrderItem::getPsCSkuId).collect(Collectors.toList());
//            sgChannelProductQueryRequest.setPsCSkuIdList(skuIds);
//
//            ValueHolderV14<List<SgBChannelProduct>> valueHolderV14 = o.queryChannelProduct(sgChannelProductQueryRequest);
//            if (!valueHolderV14.isOK()) {
//                log.info(LogUtil.format("OrderDeliveryWangDianTongImpl.deliveryDeal valueHolderV14:{}",
//                        "OrderDeliveryWangDianTongImpl.valueHolderV14"), JSONObject.toJSONString(valueHolderV14));
//                return false;
//            }
//            List<SgBChannelProduct> data = valueHolderV14.getData();
//            Map<String, String> skuMap = data.stream().collect(Collectors.toMap(SgBChannelProduct::getPsCSkuEcode, SgBChannelProduct::getSkuId, (v1, v2) -> v1));

            for (OcBOrderItem item : orderItemList) {
                StandplatLogisticsSendItemModel orderLine = new StandplatLogisticsSendItemModel();
                orderLine.setActualPrice(item.getPriceActual());
//                orderLine.setItemCode(skuMap.get(item.getPsCSkuEcode()));
                orderLine.setItemCode(item.getSkuNumiid());
                if (Objects.nonNull(item.getOriginSkuQty())) {
                    orderLine.setQuantity(item.getOriginSkuQty());
                } else {
                    orderLine.setQuantity(new BigDecimal(item.getQty().stripTrailingZeros().toPlainString()));
                }
                itemModelList.add(orderLine);
            }
            standplatSendModel.setItems(itemModelList);

        }
        //请求ip发给奇门
        log.info(LogUtil.format("OrderDeliveryWangDianTongImpl.deliveryDeal orderDelivery:{}",
                "OrderDeliveryWangDianTongImpl.orderDelivery"), JSONObject.toJSONString(standplatSendModel));
        ValueHolderV14 v14 = deliveryCmd.orderDelivery(standplatSendModel);
        log.info(LogUtil.format("OrderDeliveryWangDianTongImpl.deliveryDeal v14:{}",
                "OrderDeliveryWangDianTongImpl.v14"), JSONObject.toJSONString(v14));

        //请求成功插入中间表
        if (v14.isOK()) {
            StandplatSendCallUpdateOrderModel model = new StandplatSendCallUpdateOrderModel();
            model.setId(ocOrder.getId());
            model.setTid(ocOrder.getTid());
            model.setIsSuccess(v14.isOK());
            model.setReturnMessage(JSONObject.toJSONString(v14));
            model.setErrorMessage(v14.getMessage());
            callUpdateOrderServcice.cosume(model);

            //线上奶卡销售
            //线上免费奶卡
            //任务重复，这里不需要
//            OrderBusinessTypeCodeEnum orderBusinessTypeEnumByCode = OrderBusinessTypeCodeEnum.getOrderBusinessTypeEnumByCode(ocOrder.getBusinessTypeCode());
//            if (orderBusinessTypeEnumByCode == OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE ||
//                    orderBusinessTypeEnumByCode == OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD) {
//
//                Integer selectCount = taskMapper.selectCount(new LambdaQueryWrapper<OcBTobedeliveryorderConfirmTask>()
//                        .eq(OcBTobedeliveryorderConfirmTask::getOrderId, ocOrder.getId()));
//
//                if (selectCount <= 0) {
//                    OcBTobedeliveryorderConfirmTask insertTask = new OcBTobedeliveryorderConfirmTask();
//                    insertTask.setId(ModelUtil.getSequence(OC_B_TOBEDELIVERYORDER_CONFIRM_TASK));
//                    insertTask.setFailCount(0);
//                    insertTask.setOrderId(ocOrder.getId());
//                    insertTask.setStatus(OmsDeliveryOrderConfig.TOBEDELIVERYORDER_CONFIRM_TASK_ZENO);
//                    Date date = new Date();
//                    insertTask.setCreationdate(date);
//                    insertTask.setModifieddate(date);
//                    taskMapper.insert(insertTask);
//                }
//
//            }


        } else {
            String message = v14.getMessage().length() > 200 ?
                    "旺店通平台发货失败:" + StringUtils.substring(v14.getMessage(), 0, 200) :
                    "旺店通平台发货失败:" + v14.getMessage();

            log.info(LogUtil.format("OrderDeliveryWangDianTongImpl.deliveryDeal message:{}",
                    "OrderDeliveryWangDianTongImpl.message"), message);
            //记录日志
            ApplicationContextHandle.getBean(OmsOrderLogService.class).addUserOrderLog(ocBOrderRelation.getOrderId(), ocBOrderRelation.getOrderInfo().getBillNo()
                    , OrderLogTypeEnum.PLATFORM_SEND.getKey(), message
                    , null, null, SystemUserResource.getRootUser());
        }

        return v14.isOK();
    }
}
