package com.jackrain.nea.oc.oms.nums.excel;

import com.jackrain.nea.util.excel.XlsAno;
import com.jackrain.nea.util.excel.XlsDBAno;
import com.jackrain.nea.util.excel.XlsSt;
import com.jackrain.nea.util.excel.XlsTyp;

import java.math.BigDecimal;

/**
 * @author: xiWen.z
 * create at: 2019/8/15 0015
 */
@XlsDBAno(name = "oc_b_refund_in_product_item", desc = "入库单明细", index = 1, sort = "id:asc", st = {XlsSt.DB})
public class OcBRefundInItemModel {

    @XlsAno(name = "oc_b_refund_in_id", value = {XlsSt.NOTNULL}, type = XlsTyp.STRING, index = 10, desc = "入库单编号")
    private Long ocBRefundInId;

    @XlsAno(name = "id", value = {XlsSt.NOTNULL}, type = XlsTyp.STRING, index = 20, desc = "明细编号")
    private Long id;

    @XlsAno(name = "gbcode", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 30, desc = "国标码")
    private String gbcode;

    @XlsAno(name = "ps_c_sku_ecode", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 40, desc = "条码编码")
    private String psCSkuEcode;

    @XlsAno(name = "real_sku_ecode", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 50, desc = "实收条码编码")
    private String realSkuEcode;

    @XlsAno(name = "ps_c_pro_ecode", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 60, desc = "商品编号")
    private String psCProEcode;

    @XlsAno(name = "ps_c_pro_ename", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 70, desc = "商品名称")
    private String psCProEname;

    /*@XlsAno(name = "sc_b_in_id", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 80, desc = "入库通知单编号")
    private Long scBInId;*/

    @XlsAno(name = "product_mark", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 90, desc = "商品标记")
    private String productMark;

    @XlsAno(name = "is_without_orig", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 100, desc = "是否无原单条码")
    private Integer isWithoutOrig;

    @XlsAno(name = "qty", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 110, desc = "数量")
    private BigDecimal qty;

    @XlsAno(name = "oc_b_return_order_id", value = {XlsSt.NOTNULL}, type = XlsTyp.STRING, index = 120, desc = "退换货单编号")
    private Long ocBReturnOrderId;

    @XlsAno(name = "is_match", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 130, desc = "是否匹配")
    private Integer isMatch;

    @XlsAno(name = "is_gen_adjust", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 140, desc = "是否生成调整单")
    private Integer isGenAdjust;

    @XlsAno(name = "ps_c_clr_ename", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 150, desc = "颜色")
    private String ps_c_clr_ename;


    @XlsAno(name = "ps_c_size_ename", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 160, desc = "尺寸")
    private String ps_c_size_ename;

}
