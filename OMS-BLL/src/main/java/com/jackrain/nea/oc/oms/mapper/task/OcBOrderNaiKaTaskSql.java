package com.jackrain.nea.oc.oms.mapper.task;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @ClassName OcBOrderNaiKaTaskSql
 * @Description 奶卡定时任务
 * <AUTHOR>
 * @Date 2022/9/8 11:14
 * @Version 1.0
 */
@Slf4j
public class OcBOrderNaiKaTaskSql {

    public String selectToNaiKaAccount(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int accountToNaikaTimes = para.get("accountToNaikaTimes") != null ? (int) para.get("accountToNaikaTimes") : 6;
        sql.append("select * from ")
                .append(taskTableName)
                .append("  where account_to_naika in (1, 4) ")
                .append(" and account_to_naika_times <= ")
                .append(accountToNaikaTimes)
                .append(" order by modifieddate asc")
                .append(limitStr);
        return sql.toString();
    }
}
