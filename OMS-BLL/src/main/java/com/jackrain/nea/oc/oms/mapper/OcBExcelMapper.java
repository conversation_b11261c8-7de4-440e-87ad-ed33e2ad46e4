package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.util.excel.XlsSqlModel;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @author: xiWen.z
 * create at: 2019/8/12 0012
 */
@Mapper
@Component
public interface OcBExcelMapper extends ExtentionMapper {

    /**
     * search.data
     *
     * @param model XlsSqlModel
     * @return list
     */
    @SelectProvider(type = OcBExcelMapper.SqlProvider.class, method = "selectOcBExcelInfo")
    List<Map<String, Object>> selectOcBExcelInfo(XlsSqlModel model);

    /**
     * constructor.sql
     */
    class SqlProvider {

        /**
         * search
         *
         * @param model XlsSqlModel
         * @return string
         */
        public String selectOcBExcelInfo(XlsSqlModel model) {

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT ");
            sb.append(model.getFields());
            sb.append(" FROM ");
            sb.append(model.getTableName());
            sb.append(" WHERE `");
            sb.append(model.getHashKey());
            sb.append("` IN (");
            sb.append(model.getEs());
            sb.append(")");
            String isActive = model.getIsActive();
            if (isActive != null && isActive.length() > 1) {
                sb.append(" AND ISACTIVE='Y' ");
            }
            if (StringUtils.equalsIgnoreCase(model.getTableName(), "OC_B_ORDER_ITEM")) {
                sb.append(" AND PRO_TYPE !=4 ");
            }
            String sort = model.getSort();
            if (sort != null && sort.length() > 1) {
                sb.append(" ORDER BY ");
                sb.append(sort);
            }
            return sb.toString();
        }


        /**
         * search
         *
         * @param model XlsSqlModel
         * @return string
         */
        public String selectOcBAllExcelInfo(XlsSqlModel model,String table,String column) {

            StringBuilder sb = new StringBuilder();
            sb.append("SELECT ");
            sb.append(column);
            sb.append(" FROM ");
            sb.append(table);
            sb.append(" WHERE ");
            sb.append("O."+model.getHashKey());
            sb.append(" IN (");
            sb.append(model.getEs());
            sb.append(")");
            String isActive = model.getIsActive();
            if (isActive != null && isActive.length() > 1) {
                sb.append(" AND I.ISACTIVE='Y' ");
                sb.append(" AND O.ISACTIVE='Y' ");
            }
            sb.append(" AND I.PRO_TYPE !=4 ");
            String sort = model.getSort();
            if (sort != null && sort.length() > 1) {
                sb.append(" ORDER BY ");
                sb.append("O."+sort);
            }
            return sb.toString();
        }
    }

    /**
     * search.data
     *
     * @param model XlsSqlModel
     * @return list
     */
    @SelectProvider(type = OcBExcelMapper.SqlProvider.class, method = "selectOcBAllExcelInfo")
    List<Map<String, Object>> selectOcBAllExcelInfo(XlsSqlModel model,String table,String column);
}
