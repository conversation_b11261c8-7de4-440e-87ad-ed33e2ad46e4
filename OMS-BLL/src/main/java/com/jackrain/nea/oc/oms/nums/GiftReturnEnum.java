package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

/**
 * @Author: 黄世新
 * @Date: 2019/11/6 3:05 下午
 * @Version 1.0
 */
public enum GiftReturnEnum {

    SUCCESS(0, "有可用赠品"),

    FAIL(-1, "赠品服务调用失败"),

    NO_GIFT(1, "无可用赠品");

    GiftReturnEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Getter
    private Integer code;
    @Getter
    private String message;


}
