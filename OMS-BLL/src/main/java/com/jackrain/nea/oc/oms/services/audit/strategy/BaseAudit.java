package com.jackrain.nea.oc.oms.services.audit.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.burgeon.r3.sg.core.model.table.store.out.SgBStoOutItem;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutItemRequest;
import com.burgeon.r3.sg.inf.model.request.oms.SgOmsShareOutRequest;
import com.burgeon.r3.sg.store.model.result.out.SgBStoOutQueryResult;
import com.google.common.base.Throwables;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.AppointTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.AutoAuditStatus;
import com.jackrain.nea.oc.oms.model.enums.IsForbiddenDeliveryEnum;
import com.jackrain.nea.oc.oms.model.enums.OcOrderLockStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.enums.OmsMethod;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderIsInterceptEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBShopSkuBatchInfo;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.OcBShopSkuBatchInfoService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.oc.oms.services.audit.AuditEnum;
import com.jackrain.nea.oc.oms.services.audit.AuditStrategyHandler;
import com.jackrain.nea.oc.oms.services.audit.OmsOrderAutoAuditService;
import com.jackrain.nea.oc.oms.services.task.OmsAuditTaskService;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 基础数据校验
 *
 * @Auther: 黄志优
 * @Date: 2020/11/3 10:31
 * @Description:
 */
@Service
@Slf4j
public class BaseAudit implements AuditStrategyHandler {

    @Autowired
    private OmsOrderAutoAuditService omsOrderAutoAuditService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsAuditTaskService auditTaskService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private OcBShopSkuBatchInfoService ocBShopSkuBatchInfoService;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService omsOrderLogService;

    private static final String RECEIVER_ADDRESS_LIMIT = "business_system:receiver_address_limit";

    @Override
    public boolean doHandle(OcBOrderRelation orderInfo, User operateUser) {
        OmsMethod omsMethod = orderInfo.getOmsMethod();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("开始校验基础数据", orderInfo.getOrderId()));
        }

        //查询订单是否为待审核状态
        OcBOrder ocBOrder = orderInfo.getOrderInfo();
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("audit.status={}", orderInfo.getOrderId()), ocBOrder.getOrderStatus());
        }
        if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
            String message = "当前订单非待审核状态,审核失败";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            OcBOrder ocBOrderDto = new OcBOrder();
            ocBOrderDto.setId(orderInfo.getOrderId());
            ocBOrderDto.setAutoAuditStatus(AutoAuditStatus.Audit_INIT.toInteger());
            ocBOrderDto.setSysremark(message);
            omsOrderService.updateOrderInfo(ocBOrderDto);
            // 用来给手工审核时页面提示用的
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_01);
            return false;
        }

        // HOLD单的订单不能自动审核
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("audit.isInterecept={}", orderInfo.getOrderId()), ocBOrder.getIsInterecept());
        }
        if (OmsOrderIsInterceptEnum.YES.getVal().equals(ocBOrder.getIsInterecept())) {
            String message = "订单处于HOLD单状态，不允许自动审核";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            OcBOrder ocBOrderDto = new OcBOrder();
            ocBOrderDto.setId(orderInfo.getOrderId());
            ocBOrderDto.setAutoAuditStatus(AutoAuditStatus.Audit_INIT.toInteger());
            ocBOrderDto.setSysremark("订单处于HOLD单状态，不允许自动审核");
            omsOrderService.updateOrderInfo(ocBOrderDto);
            auditTaskService.updateAuditTaskByOrderId(Arrays.asList(orderInfo.getOrderId()));
            // 用来给手工审核时页面提示用的
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_01);
            return false;
        }
        // 锁单的订单不能自动审核
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("audit.lockStatus={}", orderInfo.getOrderId()),  ocBOrder.getLockStatus());
        }
        if (ocBOrder.getLockStatus() != null && OcOrderLockStatusEnum.LOCKED.getKey() == ocBOrder.getLockStatus()) {
            String message = "订单处于锁单状态,不允许自动审核";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_49);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_49);
            return false;
        }

        // 卡单单状态不允许审核
        if (ocBOrder.getIsDetention() != null && AdvanceConstant.DETENTION_STATUS_1.equals(ocBOrder.getIsDetention())) {
            String message = "订单卡单状态,不允许自动审核！";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_59);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_59);
            return false;
        }

        if (!omsOrderAutoAuditService.checkDuplicateOrder(orderInfo)) {
            String message = "订单存在重单,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_24);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_24);

            return false;
        }

        if (OmsMethod.AUTO.equals(omsMethod) && OmsBusinessTypeUtil.isToBOrder(ocBOrder)) {
            String message = "该业务类型订单无法自动审核";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_24);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_58);
            return false;
        }
        //判断非退款明细是否存在
        List<OcBOrderItem> orderItemList = orderInfo.getOrderItemList();
        //判断明细数据是否存在
        if (CollectionUtils.isEmpty(orderItemList)) {
            String message = "订单不存在明细数据或者全部明细已退款,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_02);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_02);

            return false;
        }

        if (orderInfo.getOrderInfo().getCpCShopId() == null) {
            String message = "订单不存在下单店铺,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_03);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_03);

            return false;
        }

        Long cpCShopId = orderInfo.getOrderInfo().getCpCShopId();
        CpShop cpShop = cpRpcService.selectShopById(cpCShopId);
        if (ObjectUtil.isNull(cpShop)) {
            String message = "订单不存在下单店铺,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_03);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_03);
            return false;
        }

        // toC 订单审核时检查店铺分类
        if (!checkShopCategoryForToCOrder(orderInfo, cpShop, omsMethod)) {
            return false;
        }



//        若【订单来源系统！=sap false】 & 订单非手工单 false  &店铺可用=否，则不允许审核
//        1)记录审核失败日志内容及系统备注：平台订单所属店铺冻结不可用，审核失败！
//        2)审核失败类型=基础校验错误，审核失败原因=平台订单所属店铺已冻结不可用
//        boolean isSapOrder = StringUtils.isNotEmpty(ocBOrder.getGwSourceGroup()) && PlatFormEnum.SAP.getCode().equals(Integer.valueOf(ocBOrder.getGwSourceGroup()));
//        boolean isHand = StringUtils.isNotEmpty(ocBOrder.getOrderSource()) && StringUtils.equals("手工新增", ocBOrder.getOrderSource());
        if (ObjectUtil.notEqual("Y", cpShop.getIsactive()) && !orderInfo.getMandatoryAudit()) {
            String message = "平台订单所属店铺冻结不可用，审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_61);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_61);
            return false;
        }

        // toB 订单审核时检查效期倒挂（仅限普通审核，强制审核不检查）
        if (!orderInfo.getMandatoryAudit() && !checkExpiryDateInversion(orderInfo, cpShop, omsMethod)) {
            return false;
        }

        if (Objects.isNull(ocBOrder.getOrderDate())) {
            String message = "订单下单日期为空,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_04);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_04);

            return false;
        }

        //订单中省、市未维护，不允许审核！
        if (StringUtils.isBlank(ocBOrder.getCpCRegionProvinceEname())
                || StringUtils.isBlank(ocBOrder.getCpCRegionCityEname())) {
            String message = "订单中省或者市未维护,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_08);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_08);

            return false;
        }

        //付款类型为空直接结束
        if (Objects.isNull(ocBOrder.getPayType())) {
            String message = "订单付款类型为空,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_10);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_10);

            return false;
        }

        //判断发货仓库是否为空
        if (Objects.isNull(ocBOrder.getCpCPhyWarehouseId()) || ocBOrder.getCpCPhyWarehouseId() == 0L) {
            String message = "订单发货仓库为空,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_09);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_09);

            return false;
        }

        //判断物流公司是否为空
        if (Objects.isNull(ocBOrder.getCpCLogisticsId()) || ocBOrder.getCpCLogisticsId() == 0L || ocBOrder.getCpCLogisticsId() == -1L) {
            String message = "订单发货物流为空,审核失败!";
            log.info("{}.audit.message={}", orderInfo.getOrderId(), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_11);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_11);

            return false;
        }

        //判断发货仓库和物流是否在系统中存在
        if (!omsOrderAutoAuditService.checkWarehouseAndLogistics(orderInfo)) {
            String message = "订单未分配到发货仓库或者未分配到发货物流,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_20);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_20);
            return false;
        }

        //判断订单总额是否为空
        if (Objects.isNull(ocBOrder.getOrderAmt())) {
            String message = "订单总额为空,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_12);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_12);

            return false;
        }

        //检查平台类型是否为空
        if (Objects.isNull(ocBOrder.getPlatform())) {
            String message = "订单平台类型为空,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_13);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_13);

            return false;
        }

        //@20210109 黄志优注释 唯品会JITX平台需要判断物流单号是否存在，存在可审核；不存在，则不能审核
        if (PlatFormEnum.VIP_JITX.getCode().equals(ocBOrder.getPlatform())) {
            String message = "";
            OmsAuditFailedReason failedReason = OmsAuditFailedReason.ERROR_99;
            if (StringUtils.isBlank(ocBOrder.getExpresscode())) {
                message = OmsAuditFailedReason.ERROR_38.getKey();
                failedReason=OmsAuditFailedReason.ERROR_38;
            } else if (IsForbiddenDeliveryEnum.FORBIDDEN.getCode().equals(ocBOrder.getIsForbiddenDelivery())) {
                message = OmsAuditFailedReason.ERROR_45.getKey();
                failedReason=OmsAuditFailedReason.ERROR_45;
            }
            if (StringUtils.isNotEmpty(message)) {
                log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
                omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_20);
                orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_38);
                return false;
            }
        }

        //检查订单类型是否为空
        if (Objects.isNull(ocBOrder.getOrderType())) {
            String message = "订单类型为空,审核失败!";
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_14);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_14);

            return false;
        }
        //校验业务系统参数中设置的收货人地址特殊字符限制
        String limitStr = this.receiverAddressLimit(orderInfo);
        if (StringUtils.isNotEmpty(limitStr)) {
            String message = String.format("订单收货地址包含[业务系统参数]限制的关键字:%s,审核失败!", limitStr);
            log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_22);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_22);
            return false;
        }

        //来源平台=38&39 审核校验
        if ("38".equals(ocBOrder.getGwSourceGroup()) || "39".equals(ocBOrder.getGwSourceGroup())) {
            String factory = omsOrderAutoAuditService.checkFactory(orderInfo);
            if (factory == null) {
                String message = "发货工厂与指定工厂不符,审核失败!";
                log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
                omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_62);
                orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_62);
                return false;
            }

            if (StringUtils.isNotBlank(factory)) {
                String message = "发货工厂与指定工厂不符，请更换" + factory + "工厂的仓库发货！";
                log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message);
                omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_62);
                orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_62);
                return false;
            }
        }

        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("audit.校验基础数据完成",orderInfo.getOrderId()));
        }

        return true;
    }


    @Override
    public Integer getSort(String name) {
        return AuditEnum.getValueFromValueTag(name);
    }

    /**
     * 收货地址限制：如% @
     *
     * @param orderInfo 订单对象
     * @return boolean
     */
    public String receiverAddressLimit(OcBOrderRelation orderInfo) {

        try {
            // 获取业务系统参数里的收货地址限制
            String splitString = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(RECEIVER_ADDRESS_LIMIT);
            List<String> strList = new ArrayList<>();
            if (StringUtils.isNotEmpty(splitString)) {
                splitString = splitString.replace(",", "，");
                strList = Arrays.asList(splitString.split("，"));
            }
            String receiverAddress = orderInfo.getOrderInfo().getReceiverAddress().trim();
            if (StringUtils.isNotEmpty(receiverAddress)) {
                for (String str : strList) {
                    if (receiverAddress.contains(str.trim())) {
                        return str;
                    }
                }
            }
            return "";
        } catch (Exception ex) {
            String errorMsg = StringUtils.isEmpty(ex.getMessage()) ? "" : ex.getMessage();
            String message = "订单判断收货人地址存在【业务系统参数】中的“限制字符”信息服务异常,审核失败!" + errorMsg;
            log.error(LogUtil.format("{}.audit.message={}", orderInfo.getOrderId()),orderInfo.getOrderId(), Throwables.getStackTraceAsString(ex));
            omsOrderAutoAuditService.updateOrderInfo(orderInfo.getOmsMethod(), orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_22);
            return "";
        }
    }

    /**
     * 检查 toC 订单的店铺分类
     * toC 订单定义：业务类型编码为 RYCK16、RYCK17、RYCK18、RYCK19 的订单
     * 若店铺档案中的店铺分类≠旺店通店铺，则提示：该业务类型的客户不允许发 toC类型订单！并禁止审核
     *
     * @param orderInfo 订单信息
     * @param cpShop 店铺信息
     * @param omsMethod 审核方法
     * @return true-通过检查，false-检查失败
     */
    private boolean checkShopCategoryForToCOrder(OcBOrderRelation orderInfo, CpShop cpShop, OmsMethod omsMethod) {
        try {
            OcBOrder ocBOrder = orderInfo.getOrderInfo();

            String businessTypeCode = ocBOrder.getBusinessTypeCode();
            boolean isToBOrder = false;

            if (StringUtils.isNotBlank(businessTypeCode)) {
                // 此处校验是否tob订单与之前的逻辑不太一致。左老师强烈要求如此判断
                isToBOrder = OrderBusinessTypeCodeEnum.SAP_CONSIGN_SALE.getCode().equals(businessTypeCode)      // RYCK16
                        || OrderBusinessTypeCodeEnum.SAP_STANDARD_SALE.getCode().equals(businessTypeCode)       // RYCK17
                        || OrderBusinessTypeCodeEnum.SAP_RAW_MATERIAL_SALE.getCode().equals(businessTypeCode)   // RYCK18
                        || OrderBusinessTypeCodeEnum.SAP_FREE.getCode().equals(businessTypeCode)               // RYCK19
                        || OrderBusinessTypeCodeEnum.SAP_INSIDE.getCode().equals(businessTypeCode)          // RYCK20
                        || OrderBusinessTypeCodeEnum.SAP_GIVE.getCode().equals(businessTypeCode)               // RYCK03
                        || OrderBusinessTypeCodeEnum.SAP_MILK_CARD.getCode().equals(businessTypeCode)               // RYCK23
                        || OrderBusinessTypeCodeEnum.SAP_UNLINE_MILK_CARD.getCode().equals(businessTypeCode) ;               // RYCK24

            }

            if (!isToBOrder) {
                log.debug(LogUtil.format("检查 toC 订单店铺分类，订单ID={}，业务类型编码={}，店铺分类={}", orderInfo.getOrderId()),
                        businessTypeCode, cpShop.getStoreCategary());

                // 检查店铺分类是否为"旺店通店铺"
                String storeCategory = cpShop.getStoreCategary();
                if (StringUtils.isBlank(storeCategory) || (!"Z009".equals(storeCategory) && !"Z001".equals(storeCategory) )) {
                    String message = "该业务类型的客户不允许发 toC类型订单！";
                    log.info(LogUtil.format("audit.message={}", orderInfo.getOrderId()),
                            "toC订单店铺分类检查失败，店铺分类={}，{}", storeCategory, message);

                    omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_64);
                    orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_64);
                    return false;
                }

                log.debug(LogUtil.format("toC订单店铺分类检查通过，订单ID={}，业务类型编码={}，店铺分类={}", orderInfo.getOrderId()),
                        businessTypeCode, storeCategory);
            }

            return true;
        } catch (Exception e) {
            String message = "toC订单店铺分类检查异常，审核失败！";
            log.error(LogUtil.format("audit.message={}", orderInfo.getOrderId()), message, e);
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, orderInfo.getOrderInfo(), message, OmsAuditFailedReason.ERROR_99);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_99);
            return false;
        }
    }

    /**
     * 检查效期倒挂
     * 待审核状态的toB订单（RYCK16、RYCK17）点击"审核"按钮时（注意，强制审核按钮不算！），
     * 判断该店铺是否配置了限制效期倒挂，如果是，判断每个品相实际寻源占单的效期
     * （要查逻辑占用单该SKU的所有占用行，取最早的日期）是否都晚于或等于表中记录的最早效期，
     * 如果否，则审核时alert提示：订单中存在效期倒挂商品，请重新寻源！倒挂的商品效期已重新修改！
     * 并将订单卡单，然后解除卡单恢复成待寻源状态，修改对应商品的效期范围。
     *
     * @param orderInfo 订单信息
     * @param cpShop 店铺信息
     * @param omsMethod 审核方法
     * @return true-通过检查，false-检查失败
     */
    private boolean checkExpiryDateInversion(OcBOrderRelation orderInfo, CpShop cpShop, OmsMethod omsMethod) {
        try {
            OcBOrder ocBOrder = orderInfo.getOrderInfo();

            // 检查业务类型是否为 RYCK16 或 RYCK17
            String businessTypeCode = ocBOrder.getBusinessTypeCode();
            if (!OrderBusinessTypeCodeEnum.SAP_CONSIGN_SALE.getCode().equals(businessTypeCode) &&
                !OrderBusinessTypeCodeEnum.SAP_STANDARD_SALE.getCode().equals(businessTypeCode)) {
                log.debug("订单业务类型不是RYCK16或RYCK17，跳过效期倒挂检查，订单ID={}，业务类型={}",
                        orderInfo.getOrderId(), businessTypeCode);
                return true;
            }


            if (StringUtils.isNotEmpty(ocBOrder.getSaleProductAttr())){
                return true;
            }

            // 检查店铺是否配置了限制效期倒挂
            String shopCode = cpShop.getEcode();
            if (!businessSystemParamService.isShopCodeInBatchInversion(shopCode)) {
                log.debug("店铺未配置限制效期倒挂，跳过检查，订单ID={}，店铺编码={}",
                        orderInfo.getOrderId(), shopCode);
                return true;
            }

            log.info("开始检查效期倒挂，订单ID={}，店铺编码={}，业务类型={}",
                    orderInfo.getOrderId(), shopCode, businessTypeCode);

            // 获取订单明细
            List<OcBOrderItem> orderItems = orderInfo.getOrderItemList();
            if (CollectionUtils.isEmpty(orderItems)) {
                log.warn("订单明细为空，跳过效期倒挂检查，订单ID={}", orderInfo.getOrderId());
                return true;
            }

            // 检查每个商品的效期倒挂情况
            List<String> inversionProducts = new ArrayList<>();
            Map<String, String> productEarliestDates = new HashMap<>();
            String receiverAddress = ocBOrder.getReceiverAddress();

            for (OcBOrderItem orderItem : orderItems) {
                String skuCode = orderItem.getPsCSkuEcode();
                if (StringUtils.isBlank(skuCode)) {
                    continue;
                }

                // 查询该SKU在店铺SKU批次信息表中记录的最早效期（增加收货地址维度）
                OcBShopSkuBatchInfo shopSkuBatchInfo = getShopSkuBatchInfoByAddress(cpShop.getId(), shopCode, skuCode, receiverAddress);
                if (shopSkuBatchInfo == null || shopSkuBatchInfo.getProduceDate() == null) {
                    log.debug("SKU在店铺批次信息表中无记录或无生产日期，跳过检查，订单ID={}，SKU={}，收货地址={}",
                            orderInfo.getOrderId(), skuCode, receiverAddress);
                    continue;
                }

                String recordedEarliestDate = shopSkuBatchInfo.getProduceDate();
                log.debug("recordedEarliestDate:{}", recordedEarliestDate);
                // 查询逻辑占用单该SKU的所有占用行，取最早的日期
                String actualEarliestDate = getEarliestOccupationDate(orderInfo.getOrderId(), skuCode, ocBOrder.getBillNo());
                if (StringUtils.isBlank(actualEarliestDate)) {
                    log.debug("SKU在逻辑占用单中无记录，跳过检查，订单ID={}，SKU={}",
                            orderInfo.getOrderId(), skuCode);
                    continue;
                }

                // 比较实际最早日期是否晚于或等于记录的最早效期
                // 需要统一日期格式进行比较：recordedEarliestDate格式为"2025-07-18 00:00:00"，actualEarliestDate格式为"20250710"
                String recordedDateFormatted = convertToYyyyMMdd(recordedEarliestDate);
                if (StringUtils.isNotBlank(recordedDateFormatted) && actualEarliestDate.compareTo(recordedDateFormatted) < 0) {
                    // 存在效期倒挂
                    String productCode = orderItem.getPsCProEcode();
                    if (!inversionProducts.contains(productCode)) {
                        inversionProducts.add(productCode);
                        productEarliestDates.put(productCode, recordedEarliestDate);
                    }

                    log.warn("发现效期倒挂，订单ID={}，商品编码={}，SKU={}，实际最早日期={}，记录最早日期={}（原格式：{}）",
                            orderInfo.getOrderId(), productCode, skuCode, actualEarliestDate, recordedDateFormatted, recordedEarliestDate);
                }
            }

            // 如果存在效期倒挂，执行相应处理
            if (!inversionProducts.isEmpty()) {
                return handleExpiryDateInversion(orderInfo, inversionProducts, productEarliestDates, omsMethod);
            }

            log.info("效期倒挂检查通过，订单ID={}", orderInfo.getOrderId());
            return true;

        } catch (Exception e) {
            log.error("检查效期倒挂异常，订单ID={}", orderInfo.getOrderId(), e);
            // 异常情况下不阻止审核流程
            return true;
        }
    }

    /**
     * 处理效期倒挂情况
     * 将订单卡单，然后解除卡单恢复成待寻源状态，修改对应商品的效期范围
     *
     * @param orderInfo 订单信息
     * @param inversionProducts 倒挂的商品列表
     * @param productEarliestDates 商品最早日期映射
     * @param omsMethod 审核方法
     * @return false-阻止审核
     */
    private boolean handleExpiryDateInversion(OcBOrderRelation orderInfo, List<String> inversionProducts,
                                            Map<String, String> productEarliestDates, OmsMethod omsMethod) {
        try {
            OcBOrder ocBOrder = orderInfo.getOrderInfo();

            // 记录审核失败信息
            String message = "订单中存在效期倒挂商品，请重新寻源！倒挂的商品效期已重新修改！倒挂商品：" +
                    String.join("、", inversionProducts);

            log.info("处理效期倒挂，订单ID={}，倒挂商品数量={}，商品列表={}",
                    orderInfo.getOrderId(), inversionProducts.size(), inversionProducts);

            // 1. 将订单卡单
            holdOrder(orderInfo, message);

            // 2. 修改对应商品的效期范围
            updateProductExpiryRange(orderInfo, productEarliestDates);

            // 3. 记录审核失败
            omsOrderAutoAuditService.updateOrderInfo(omsMethod, ocBOrder, message, OmsAuditFailedReason.ERROR_65);
            orderInfo.setOmsAuditFailedReason(OmsAuditFailedReason.ERROR_65);

            return false;

        } catch (Exception e) {
            log.error("处理效期倒挂异常，订单ID={}", orderInfo.getOrderId(), e);
            return false;
        }
    }

    /**
     * 获取SKU在逻辑占用单中的最早日期
     *
     * @param orderId 订单ID
     * @param skuCode SKU编码
     * @return 最早的占用日期
     */
    private String getEarliestOccupationDate(Long orderId, String skuCode, String billNo) {
        try {
            log.debug("查询SKU最早占用日期，订单ID={}，SKU={}，单据号={}", orderId, skuCode, billNo);

            // 调用SG服务查询逻辑占用单信息
            List<Long> orderIds = Arrays.asList(orderId);
            List<SgBStoOutQueryResult> sgBStoOutQueryResults = sgRpcService.sgQuerySgBStoOutByIds(orderIds);

            if (CollectionUtils.isEmpty(sgBStoOutQueryResults)) {
                log.debug("订单在逻辑占用单中无记录，订单ID={}", orderId);
                return null;
            }

            String earliestDate = null;

            for (SgBStoOutQueryResult queryResult : sgBStoOutQueryResults) {
                if (queryResult == null || queryResult.getMain() == null) {
                    continue;
                }

                // 检查是否是当前订单的占用单
                if (!orderId.equals(queryResult.getMain().getSourceBillId())) {
                    continue;
                }

                List<SgBStoOutItem> items = queryResult.getItems();
                if (CollectionUtils.isEmpty(items)) {
                    continue;
                }

                // 查找指定SKU的所有占用行
                for (SgBStoOutItem item : items) {
                    if (item == null || !skuCode.equals(item.getPsCSkuEcode())) {
                        continue;
                    }

                    // 获取生产日期（yyyyMMdd格式字符串）
                    String produceDate = item.getProduceDate();
                    if (StringUtils.isNotBlank(produceDate)) {
                        if (StringUtils.isBlank(earliestDate) || produceDate.compareTo(earliestDate) < 0) {
                            earliestDate = produceDate;
                        }
                    }
                }
            }

            if (StringUtils.isNotBlank(earliestDate)) {
                log.debug("找到SKU最早占用日期，订单ID={}，SKU={}，最早日期={}", orderId, skuCode, earliestDate);
            } else {
                log.debug("未找到SKU的有效占用日期，订单ID={}，SKU={}", orderId, skuCode);
            }

            return earliestDate;

        } catch (Exception e) {
            log.error("查询SKU最早占用日期异常，订单ID={}，SKU={}", orderId, skuCode, e);
            return null;
        }
    }

    /**
     * 将订单卡单
     *
     * @param orderInfo 订单信息
     * @param reason 卡单原因
     */
    private void holdOrder(OcBOrderRelation orderInfo, String reason) {
        try {
            OcBOrder ocBOrder = orderInfo.getOrderInfo();
            List<OcBOrderItem> ocBOrderItemList = orderInfo.getOrderItemList();
            User operateUser = SystemUserResource.getRootUser();
            SgOmsShareOutRequest request = buildSgOmsShareOutRequest(ocBOrder, ocBOrderItemList, operateUser);
            sgRpcService.voidSgOmsShareOut(request, ocBOrder, ocBOrderItemList);
            clearWarehouse(orderInfo.getOrderInfo());
            // 添加订单操作日志
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), "710",
                    "订单卡单，原因：" + reason, "", "", operateUser);
        } catch (Exception e) {
            log.error("卡单处理异常，订单ID={}", orderInfo.getOrderId(), e);
        }
    }

    /**
     * 修改对应商品的效期范围
     *
     * 逻辑说明：
     * 1. 若商品效期范围的类型为：生产天数范围，则直接将类型改为生产日期范围，且开始时间是SKU的最新效期，结束时间是最新效期+30；
     * 2. 若商品效期范围类型为：生产日期范围，判断生产日期范围的结束时间是否早于最新效期；
     *    2.1.如果不早于，则将SKU的生产日期范围改成：开始时间：最新效期；结束时间：原始效期结束时间
     *    2.2.如果早于：则将SKU的生产日期范围改成：开始时间：原始效期开始时间；结束时间：原始效期开始时间+30
     * 3. 若该品没命中效期倒挂则不变更该品效期范围。
     *
     * 注意：假设同一个SKU两行，一行命中了倒挂另一行未命中，则两行的商品效期都要改成一样的！
     * 即只要有一个SKU命中了倒挂，那该订单的所有SKU都要更改效期范围，且改后的结果需要完全一致！
     *
     * @param orderInfo 订单信息
     * @param productEarliestDates 商品最早日期映射（命中倒挂的商品）
     */
    private void updateProductExpiryRange(OcBOrderRelation orderInfo, Map<String, String> productEarliestDates) {
        try {
            log.info("修改商品效期范围，订单ID={}，倒挂商品数量={}", orderInfo.getOrderId(), productEarliestDates.size());

            // 获取订单所有明细
            List<OcBOrderItem> orderItems = orderInfo.getOrderItemList();
            if (CollectionUtils.isEmpty(orderItems)) {
                log.warn("订单明细为空，无法修改效期范围，订单ID={}", orderInfo.getOrderId());
                return;
            }

            // 按SKU分组，收集所有需要更新的明细
            Map<String, List<OcBOrderItem>> skuItemsMap = new HashMap<>();
            Map<String, ExpiryRangeUpdateInfo> skuUpdateInfoMap = new HashMap<>();

            for (OcBOrderItem orderItem : orderItems) {
                String skuCode = orderItem.getPsCSkuEcode();
                if (StringUtils.isBlank(skuCode)) {
                    continue;
                }

                // 按SKU分组
                skuItemsMap.computeIfAbsent(skuCode, k -> new ArrayList<>()).add(orderItem);

                // 检查该SKU是否命中倒挂
                String productCode = orderItem.getPsCProEcode();
                boolean isInverted = productEarliestDates.containsKey(productCode);

                if (isInverted) {
                    // 计算该SKU的新效期范围
                    String latestDate = productEarliestDates.get(productCode);
                    // latestDate转成Date类型。格式为yyyyMMdd
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                    Date latestDateObj = sdf.parse(latestDate);
                    ExpiryRangeUpdateInfo updateInfo = calculateNewExpiryRange(orderItem, latestDateObj);
                    skuUpdateInfoMap.put(skuCode, updateInfo);

                    log.info("SKU命中倒挂，订单ID={}，SKU={}，商品编码={}，最新效期={}，新效期范围={}~{}",
                            orderInfo.getOrderId(), skuCode, productCode, latestDate,
                            updateInfo.getNewStartDate(), updateInfo.getNewEndDate());
                }
            }

            // 批量更新效期范围
            int updateCount = 0;
            for (Map.Entry<String, ExpiryRangeUpdateInfo> entry : skuUpdateInfoMap.entrySet()) {
                String skuCode = entry.getKey();
                ExpiryRangeUpdateInfo updateInfo = entry.getValue();
                List<OcBOrderItem> skuItems = skuItemsMap.get(skuCode);

                if (CollectionUtils.isNotEmpty(skuItems)) {
                    for (OcBOrderItem item : skuItems) {
                        // 更新该SKU的所有明细行
                        boolean updated = updateOrderItemExpiryRange(item, updateInfo);
                        if (updated) {
                            updateCount++;
                        }
                    }
                }
            }

            log.info("商品效期范围修改完成，订单ID={}，更新明细数量={}", orderInfo.getOrderId(), updateCount);

        } catch (Exception e) {
            log.error("修改商品效期范围异常，订单ID={}", orderInfo.getOrderId(), e);
        }
    }

    /**
     * 计算新的效期范围
     *
     * 逻辑说明：
     * 0. 若商品效期策略类型或效期范围为空，使用默认逻辑：开始时间是SKU的最新效期，结束时间是最新效期+30；
     * 1. 若商品效期范围的类型为：生产天数范围，则直接将类型改为生产日期范围，且开始时间是SKU的最新效期，结束时间是最新效期+30；
     * 2. 若商品效期范围类型为：生产日期范围，判断生产日期范围的结束时间是否早于最新效期；
     *    2.1.如果不早于，则将SKU的生产日期范围改成：开始时间：最新效期；结束时间：原始效期结束时间
     *    2.2.如果早于：则将SKU的生产日期范围改成：开始时间：原始效期开始时间；结束时间：原始效期开始时间+30
     * 3. 其他未知类型，使用默认逻辑。
     *
     * @param orderItem 订单明细
     * @param latestDate 最新效期（SKU的最新效期）
     * @return 效期范围更新信息
     */
    private ExpiryRangeUpdateInfo calculateNewExpiryRange(OcBOrderItem orderItem, Date latestDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String latestDateStr = sdf.format(latestDate);

            Integer currentType = orderItem.getExpiryDateType();
            String currentRange = orderItem.getExpiryDateRange();

            ExpiryRangeUpdateInfo updateInfo = new ExpiryRangeUpdateInfo();
            updateInfo.setNewType(AppointTypeEnum.DATE_SCOPE.getKey()); // 统一改为生产日期范围

            // 检查效期策略类型和范围是否为空
            if (currentType == null || StringUtils.isBlank(currentRange)) {
                // 效期策略类型或范围为空，使用默认逻辑：开始时间是SKU的最新效期，结束时间是最新效期+30
                String endDateStr = addDaysToDate(latestDate, 30);
                updateInfo.setNewStartDate(latestDateStr);
                updateInfo.setNewEndDate(endDateStr);
                updateInfo.setNewRange(latestDateStr + "-" + endDateStr);

                log.debug("效期策略类型或范围为空，使用默认逻辑，SKU={}，原类型={}，原范围={}，新范围={}-{}",
                        orderItem.getPsCSkuEcode(), currentType, currentRange, latestDateStr, endDateStr);

            } else if (AppointTypeEnum.DAYS_SCOPE.getKey().equals(currentType)) {
                // 1. 若商品效期范围的类型为：生产天数范围，则直接将类型改为生产日期范围，
                //    且开始时间是SKU的最新效期，结束时间是最新效期+30
                String endDateStr = addDaysToDate(latestDate, 30);
                updateInfo.setNewStartDate(latestDateStr);
                updateInfo.setNewEndDate(endDateStr);
                updateInfo.setNewRange(latestDateStr + "-" + endDateStr);

                log.debug("生产天数范围转换为生产日期范围，SKU={}，新范围={}-{}",
                        orderItem.getPsCSkuEcode(), latestDateStr, endDateStr);

            } else if (AppointTypeEnum.DATE_SCOPE.getKey().equals(currentType)) {
                // 2. 若商品效期范围类型为：生产日期范围，判断生产日期范围的结束时间是否早于最新效期
                String[] rangeParts = parseExpiryDateRange(currentRange);
                String originalStartDate = rangeParts[0];
                String originalEndDate = rangeParts[1];

                // 检查解析出的日期是否有效
                if (StringUtils.isBlank(originalStartDate) || StringUtils.isBlank(originalEndDate)) {
                    log.warn("效期范围解析结果为空，SKU={}，原始范围={}，使用默认逻辑",
                            orderItem.getPsCSkuEcode(), currentRange);
                    // 使用默认逻辑
                    String endDateStr = addDaysToDate(latestDate, 30);
                    updateInfo.setNewStartDate(latestDateStr);
                    updateInfo.setNewEndDate(endDateStr);
                    updateInfo.setNewRange(latestDateStr + "-" + endDateStr);
                } else {
                    try {
                        Date originalEndDateObj = sdf.parse(originalEndDate);
                        Date originalStartDateObj = sdf.parse(originalStartDate);

                        if (originalEndDateObj.before(latestDate)) {
                            // 2.2. 如果早于：则将SKU的生产日期范围改成：开始时间：原始效期开始时间；结束时间：原始效期开始时间+30
                            String newEndDate = addDaysToDate(latestDate, 30);
                            updateInfo.setNewStartDate(latestDateStr);
                            updateInfo.setNewEndDate(newEndDate);
                            updateInfo.setNewRange(latestDateStr + "-" + newEndDate);

                            log.debug("原结束时间早于最新效期，SKU={}，原范围={}-{}，新范围={}-{}",
                                    orderItem.getPsCSkuEcode(), originalStartDate, originalEndDate,
                                    originalStartDate, newEndDate);
                        } else {
                            // 2.1. 如果不早于，则将SKU的生产日期范围改成：开始时间：最新效期；结束时间：原始效期结束时间
                            updateInfo.setNewStartDate(latestDateStr);
                            updateInfo.setNewEndDate(originalEndDate);
                            updateInfo.setNewRange(latestDateStr + "-" + originalEndDate);

                            log.debug("原结束时间不早于最新效期，SKU={}，原范围={}-{}，新范围={}-{}",
                                    orderItem.getPsCSkuEcode(), originalStartDate, originalEndDate,
                                    latestDateStr, originalEndDate);
                        }
                    } catch (ParseException e) {
                        log.error("解析原始效期日期失败，SKU={}，原始范围={}，开始日期={}，结束日期={}",
                                orderItem.getPsCSkuEcode(), currentRange, originalStartDate, originalEndDate, e);
                        // 解析失败时使用默认逻辑
                        String endDateStr = addDaysToDate(latestDate, 30);
                        updateInfo.setNewStartDate(latestDateStr);
                        updateInfo.setNewEndDate(endDateStr);
                        updateInfo.setNewRange(latestDateStr + "-" + endDateStr);
                    }
                }
            } else {
                // 其他未知类型，使用默认逻辑
                String endDateStr = addDaysToDate(latestDate, 30);
                updateInfo.setNewStartDate(latestDateStr);
                updateInfo.setNewEndDate(endDateStr);
                updateInfo.setNewRange(latestDateStr + "-" + endDateStr);

                log.debug("未知效期策略类型，使用默认逻辑，SKU={}，原类型={}，新范围={}-{}",
                        orderItem.getPsCSkuEcode(), currentType, latestDateStr, endDateStr);
            }

            return updateInfo;

        } catch (Exception e) {
            log.error("计算新效期范围异常，SKU={}", orderItem.getPsCSkuEcode(), e);
            // 异常时返回默认值
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String latestDateStr = sdf.format(latestDate);
            String endDateStr = addDaysToDate(latestDate, 30);

            ExpiryRangeUpdateInfo updateInfo = new ExpiryRangeUpdateInfo();
            updateInfo.setNewType(AppointTypeEnum.DATE_SCOPE.getKey());
            updateInfo.setNewStartDate(latestDateStr);
            updateInfo.setNewEndDate(endDateStr);
            updateInfo.setNewRange(latestDateStr + "-" + endDateStr);
            return updateInfo;
        }
    }

    /**
     * 解除卡单恢复成待寻源状态
     *
     * @param orderInfo 订单信息
     */
    private void releaseOrderToSourcing(OcBOrderRelation orderInfo) {
        try {
            log.info("解除卡单恢复待寻源状态，订单ID={}", orderInfo.getOrderId());
            // TODO: 实现解除卡单并恢复成待寻源状态的逻辑
        } catch (Exception e) {
            log.error("解除卡单恢复待寻源状态异常，订单ID={}", orderInfo.getOrderId(), e);
        }
    }

    /**
     * 更新订单明细的效期范围
     *
     * @param orderItem 订单明细
     * @param updateInfo 更新信息
     * @return 是否更新成功
     */
    private boolean updateOrderItemExpiryRange(OcBOrderItem orderItem, ExpiryRangeUpdateInfo updateInfo) {
        try {
            // 构建更新对象
            OcBOrderItem updateItem = new OcBOrderItem();
            updateItem.setId(orderItem.getId());
            updateItem.setExpiryDateType(updateInfo.getNewType());
            updateItem.setExpiryDateRange(updateInfo.getNewRange());

            // 执行更新
            int updateCount = ocBOrderItemMapper.updateById(updateItem);

            if (updateCount > 0) {
                log.info("更新订单明细效期范围成功，明细ID={}，SKU={}，新类型={}，新范围={}",
                        orderItem.getId(), orderItem.getPsCSkuEcode(),
                        updateInfo.getNewType(), updateInfo.getNewRange());
                return true;
            } else {
                log.warn("更新订单明细效期范围失败，明细ID={}，SKU={}",
                        orderItem.getId(), orderItem.getPsCSkuEcode());
                return false;
            }

        } catch (Exception e) {
            log.error("更新订单明细效期范围异常，明细ID={}，SKU={}",
                    orderItem.getId(), orderItem.getPsCSkuEcode(), e);
            return false;
        }
    }

    /**
     * 解析效期日期范围字符串
     *
     * @param expiryDateRange 效期日期范围字符串，格式如 "20240101-20240131"
     * @return 数组，[0]为开始日期，[1]为结束日期，如果解析失败返回空字符串
     */
    private String[] parseExpiryDateRange(String expiryDateRange) {
        if (StringUtils.isBlank(expiryDateRange)) {
            log.debug("效期范围字符串为空");
            return new String[]{"", ""};
        }

        try {
            String[] parts = null;

            // 尝试不同的分隔符，优先使用 - 分隔符
            if (expiryDateRange.contains("-")) {
                parts = expiryDateRange.split("-");
            } else if (expiryDateRange.contains("~")) {
                parts = expiryDateRange.split("~");
            } else {
                log.warn("效期范围格式不支持，无法识别分隔符，范围={}", expiryDateRange);
                return new String[]{"", ""};
            }

            if (parts.length >= 2) {
                String startDate = parts[0].trim();
                String endDate = parts[1].trim();

                // 验证日期格式是否正确（8位数字）
                if (isValidDateFormat(startDate) && isValidDateFormat(endDate)) {
                    return new String[]{startDate, endDate};
                } else {
                    log.warn("效期范围日期格式不正确，范围={}，开始日期={}，结束日期={}",
                            expiryDateRange, startDate, endDate);
                    return new String[]{"", ""};
                }
            } else if (parts.length == 1) {
                String dateStr = parts[0].trim();
                if (isValidDateFormat(dateStr)) {
                    return new String[]{dateStr, dateStr};
                } else {
                    log.warn("效期范围单一日期格式不正确，范围={}，日期={}", expiryDateRange, dateStr);
                    return new String[]{"", ""};
                }
            } else {
                log.warn("效期范围格式不正确，无法分割，范围={}", expiryDateRange);
                return new String[]{"", ""};
            }
        } catch (Exception e) {
            log.error("解析效期范围异常，范围={}", expiryDateRange, e);
            return new String[]{"", ""};
        }
    }

    /**
     * 验证日期格式是否正确（yyyyMMdd格式的8位数字）
     *
     * @param dateStr 日期字符串
     * @return true-格式正确，false-格式错误
     */
    private boolean isValidDateFormat(String dateStr) {
        if (StringUtils.isBlank(dateStr) || dateStr.length() != 8) {
            return false;
        }

        // 检查是否为8位数字
        if (!dateStr.matches("\\d{8}")) {
            return false;
        }

        // 尝试解析日期以验证有效性
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            sdf.setLenient(false); // 严格模式，不允许无效日期
            sdf.parse(dateStr);
            return true;
        } catch (ParseException e) {
            log.debug("日期格式验证失败，日期={}", dateStr);
            return false;
        }
    }

    /**
     * 从批次号中解析生产日期字符串
     *
     * @param batchNo 批次号
     * @return 生产日期字符串（yyyyMMdd格式）
     */
    private String parseDateStringFromBatchNo(String batchNo) {
        try {
            // 这里需要根据实际的批次号格式来实现
            // 常见的格式可能包括：
            // 1. YYYYMMDD格式的日期
            // 2. 包含日期的复合格式

            // 示例实现：假设批次号包含8位日期格式
            if (batchNo.length() >= 8) {
                String dateStr = null;

                // 尝试提取8位连续数字作为日期
                for (int i = 0; i <= batchNo.length() - 8; i++) {
                    String substr = batchNo.substring(i, i + 8);
                    if (substr.matches("\\d{8}")) {
                        // 验证日期有效性
                        if (isValidDateFormat(substr)) {
                            dateStr = substr;
                            break;
                        }
                    }
                }

                if (dateStr != null) {
                    return dateStr;
                }
            }

            log.debug("无法从批次号中解析生产日期，批次号={}", batchNo);
            return null;

        } catch (Exception e) {
            log.error("从批次号中解析生产日期异常，批次号={}", batchNo, e);
            return null;
        }
    }

    /**
     * 给日期增加指定天数
     *
     * @param date 原始日期
     * @param days 要增加的天数
     * @return 格式化后的日期字符串 (yyyyMMdd)
     */
    private String addDaysToDate(Date date, int days) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, days);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            return sdf.format(calendar.getTime());
        } catch (Exception e) {
            log.error("日期计算异常，原始日期={}，增加天数={}", date, days, e);
            return "";
        }
    }

    /**
     * 情况仓库信息
     */
    private void clearWarehouse(OcBOrder ocBOrder) {
        ocBOrderMapper.updateWarehouse(ocBOrder.getId());
    }

    public SgOmsShareOutRequest buildSgOmsShareOutRequest(OcBOrder orderInfo, List<OcBOrderItem> ocBOrderItemList, User user) {
        SgOmsShareOutRequest request = new SgOmsShareOutRequest();
        request.setSourceBillId(orderInfo.getId());
        request.setSourceBillNo(orderInfo.getBillNo());
        request.setSourceBillType(SgConstantsIF.BILL_TYPE_RETAIL);
        request.setLoginUser(user);
        List<SgOmsShareOutItemRequest> itemRequestList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            SgOmsShareOutItemRequest sgOmsShareOutItemRequest = new SgOmsShareOutItemRequest();
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            sgOmsShareOutItemRequest.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            sgOmsShareOutItemRequest.setQtyPreout(ocBOrderItem.getQty());
            sgOmsShareOutItemRequest.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            itemRequestList.add(sgOmsShareOutItemRequest);
        }
        request.setItemRequestList(itemRequestList);

        return request;
    }

    /**
     * 效期范围更新信息内部类
     */
    private static class ExpiryRangeUpdateInfo {
        private Integer newType;
        private String newStartDate;
        private String newEndDate;
        private String newRange;

        public Integer getNewType() {
            return newType;
        }

        public void setNewType(Integer newType) {
            this.newType = newType;
        }

        public String getNewStartDate() {
            return newStartDate;
        }

        public void setNewStartDate(String newStartDate) {
            this.newStartDate = newStartDate;
        }

        public String getNewEndDate() {
            return newEndDate;
        }

        public void setNewEndDate(String newEndDate) {
            this.newEndDate = newEndDate;
        }

        public String getNewRange() {
            return newRange;
        }

        public void setNewRange(String newRange) {
            this.newRange = newRange;
        }
    }

    /**
     * 根据店铺、SKU和收货地址查询批次信息
     * 优先使用店铺ID查询，如果查不到则使用店铺编码查询
     *
     * @param shopId 店铺ID
     * @param shopCode 店铺编码
     * @param skuCode SKU编码
     * @param receiverAddress 收货地址
     * @return 批次信息，如果不存在则返回null
     */
    private OcBShopSkuBatchInfo getShopSkuBatchInfoByAddress(Long shopId, String shopCode, String skuCode, String receiverAddress) {
        try {
            // 优先使用店铺ID和收货地址查询
            if (shopId != null) {
                if (ocBShopSkuBatchInfoService.existsByShopAndSkuAndAddress(shopId, skuCode, receiverAddress)) {
                    // 如果存在记录，则通过店铺ID查询详细信息
                    List<OcBShopSkuBatchInfo> batchInfos = ocBShopSkuBatchInfoService.getByShopIdAndSkuList(shopId, Arrays.asList(skuCode));
                    // 打印出来batchInfos与receiverAddress
                    log.info("getShopSkuBatchInfoByAddress方法中batchInfos查询结果：数量={}，receiverAddress={}，batchInfos详情={}",
                            batchInfos != null ? batchInfos.size() : 0, receiverAddress, JSONUtil.toJsonStr(batchInfos));
                    if (CollectionUtils.isNotEmpty(batchInfos)) {
                        // 从结果中筛选出匹配收货地址的记录
                        return batchInfos.stream()
                                .filter(info -> isAddressMatch(info.getReceiverAddress(), receiverAddress))
                                .findFirst()
                                .orElse(null);
                    }
                }
            }
            return null;

        } catch (Exception e) {
            log.error("查询店铺SKU批次信息异常，shopId={}，shopCode={}，skuCode={}，receiverAddress={}",
                     shopId, shopCode, skuCode, receiverAddress, e);
            return null;
        }
    }

    /**
     * 判断地址是否匹配
     *
     * @param storedAddress 存储的地址
     * @param targetAddress 目标地址
     * @return true-匹配，false-不匹配
     */
    private boolean isAddressMatch(String storedAddress, String targetAddress) {
        // 如果两个地址都为空，则匹配
        if (StringUtils.isBlank(storedAddress) && StringUtils.isBlank(targetAddress)) {
            return true;
        }

        // 如果其中一个为空，另一个不为空，则不匹配
        if (StringUtils.isBlank(storedAddress) || StringUtils.isBlank(targetAddress)) {
            return false;
        }

        // 精确匹配
        return storedAddress.equals(targetAddress);
    }

    /**
     * 将日期字符串转换为yyyyMMdd格式
     * 支持多种输入格式：
     * - "2025-07-18 00:00:00" -> "20250718"
     * - "2025-07-18" -> "20250718"
     * - "20250718" -> "20250718"（已经是目标格式）
     *
     * @param dateStr 输入的日期字符串
     * @return yyyyMMdd格式的日期字符串，转换失败返回null
     */
    private String convertToYyyyMMdd(String dateStr) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }

        try {
            // 如果已经是yyyyMMdd格式（8位数字），直接返回
            if (dateStr.matches("\\d{8}")) {
                return dateStr;
            }

            // 处理"2025-07-18 00:00:00"或"2025-07-18"格式
            if (dateStr.contains("-")) {
                // 提取日期部分（去掉时间部分）
                String datePart = dateStr.split(" ")[0];

                // 解析yyyy-MM-dd格式
                SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
                SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");

                Date date = inputFormat.parse(datePart);
                return outputFormat.format(date);
            }

            // 其他格式暂不支持
            log.warn("不支持的日期格式：{}", dateStr);
            return null;

        } catch (Exception e) {
            log.error("日期格式转换异常，输入：{}，异常信息：{}", dateStr, e.getMessage(), e);
            return null;
        }
    }
}
