package com.jackrain.nea.oc.oms.services.invoice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.jackrain.nea.ac.service.OrderInvoiceChangeTaskService;
import com.jackrain.nea.cpext.utils.ValueHolderUtils;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import com.jackrain.nea.oc.oms.model.table.StCInvoiceStrategy;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 发票申请传唤
 * @author: caomalai
 * @create: 2022-09-17 14:34
 **/
@Slf4j
@Component
public class AcFInvoiceApplyTransService {
    @Autowired
    private OrderInvoiceChangeTaskService orderInvoiceChangeTaskService;
    @Autowired
    private AcFInvoiceApplyMapper acFInvoiceApplyMapper;

    /**
     * 发票申请手动转换
     * @param param
     * @param user
     * @return
     */
    public ValueHolder transfer(JSONObject param, User user) {
        if(Objects.isNull(param)){
            throw new NDSException("参数不能为空！");
        }
        JSONArray idsArray = param.getJSONArray("ids");
        List<Long> ids = JSON.parseArray(idsArray.toJSONString(), Long.class);
        List<AcFInvoiceApply> acFInvoiceApplies = acFInvoiceApplyMapper.selectBatchIds(ids);
        if(CollectionUtils.isEmpty(acFInvoiceApplies)){
            throw new NDSException("所选记录不存在！");
        }
        int successNum = 0;
        for(AcFInvoiceApply acFInvoiceApply:acFInvoiceApplies){
            try{
                if (InvoiceConst.TransStatus.TRANS_SUCCESS.equals(acFInvoiceApply.getTransStatus())) {
                    throw new NDSException("状态已转换不允许单据转换！");
                }
                ValueHolderV14<StCInvoiceStrategy> stCInvoiceStrategyValueHolderV14 =
                        orderInvoiceChangeTaskService.changeApplyInvoice(null, null, null, null, null, acFInvoiceApply,user);
                if(!stCInvoiceStrategyValueHolderV14.isOK()){
                    throw new NDSException(stCInvoiceStrategyValueHolderV14.getMessage());
                }
                successNum++;
                //修改发票申请表转换状态
                AcFInvoiceApply successPo = new AcFInvoiceApply();
                successPo.setId(acFInvoiceApply.getId());
                successPo.setTransStatus(InvoiceConst.TransStatus.TRANS_SUCCESS);
                BaseModelUtil.makeBaseModifyField(successPo,user);
                acFInvoiceApplyMapper.updateById(successPo);
            }catch(Exception e){
                log.info(LogUtil.format("手工转换失败，单号：" + acFInvoiceApply.getTid(), "发票申请"), e);
                //单据原始状态为成功，不需要修改转换状态
                if (!InvoiceConst.TransStatus.TRANS_SUCCESS.equals(acFInvoiceApply.getTransStatus())) {
                    Integer failCount = acFInvoiceApply.getFailCount()==null?0:acFInvoiceApply.getFailCount();
                    AcFInvoiceApply failPo = new AcFInvoiceApply();
                    failPo.setId(acFInvoiceApply.getId());
                    failPo.setTransStatus(InvoiceConst.TransStatus.TRANS_FAIL);
                    failPo.setFailCount(++failCount);
                    String failMsg = e.getMessage();
                    if(StringUtils.isNotBlank(failMsg) && failMsg.length()>500){
                        failMsg = failMsg.substring(0, 450);
                    }
                    failPo.setFailReason(failMsg);
                    BaseModelUtil.makeBaseModifyField(failPo,user);
                    acFInvoiceApplyMapper.updateById(failPo);
                    if(acFInvoiceApplies.size()==1){
                        throw new NDSException(e.getMessage());
                    }
                }
            }
        }
        if(successNum == 0){
            return ValueHolderUtils.fail("手动转换失败！失败条数："+acFInvoiceApplies.size());
        }else{
            if(successNum<acFInvoiceApplies.size()){
                return ValueHolderUtils.success(String.format("手动转换部分成功！成功条数：%s，失败条数：%s",successNum,acFInvoiceApplies.size()-successNum));
            }else{
                return ValueHolderUtils.success(String.format("手动转换成功！成功条数：%s",successNum));
            }
        }
    }

    /**
     * 标记转换成功
     * @param param
     * @param user
     * @return
     */
    public ValueHolder markSuccess(JSONObject param, User user) {
        if(Objects.isNull(param)){
            throw new NDSException("参数不能为空！");
        }
        JSONArray idsArray = param.getJSONArray("ids");
        List<Long> ids = JSON.parseArray(idsArray.toJSONString(), Long.class);
        List<AcFInvoiceApply> acFInvoiceApplies = acFInvoiceApplyMapper.selectBatchIds(ids);
        if(CollectionUtils.isEmpty(acFInvoiceApplies)){
            throw new NDSException("所选记录不存在！");
        }
        int successCount = 0;
        for(AcFInvoiceApply acFInvoiceApply:acFInvoiceApplies){
            try {
                if (InvoiceConst.TransStatus.TRANS_SUCCESS.equals(acFInvoiceApply.getTransStatus())) {
                    throw new NDSException("状态已转换不允许标记成功！");
                }
                AcFInvoiceApply updateOne = new AcFInvoiceApply();
                updateOne.setId(acFInvoiceApply.getId());
                updateOne.setTransStatus(InvoiceConst.TransStatus.TRANS_SUCCESS);
                updateOne.setTransId(new Long(user.getId()));
                updateOne.setFailReason("");
                BaseModelUtil.setupUpdateParam(updateOne,user);
                acFInvoiceApplyMapper.updateById(updateOne);
                successCount++;
            }catch (Exception e){
                log.error(LogUtil.format("标记转换失败！平台单号："+acFInvoiceApply.getTid(),"发票申请"),e);
                //如果只选中了一条数据，直接把错误抛出去
                if (ids.size()==1) {
                    throw new NDSException(e.getMessage());
                }
            }
        }
        if(successCount == 0){
            return ValueHolderUtils.fail("标记转换失败！失败条数："+acFInvoiceApplies.size());
        }else{
            if(successCount<acFInvoiceApplies.size()){
                return ValueHolderUtils.success(String.format("标记转换部分成功！成功条数：%s，失败条数：%s",successCount,acFInvoiceApplies.size()));
            }else{
                return ValueHolderUtils.success(String.format("标记转换成功！成功条数：%s",successCount));
            }
        }
    }
}
