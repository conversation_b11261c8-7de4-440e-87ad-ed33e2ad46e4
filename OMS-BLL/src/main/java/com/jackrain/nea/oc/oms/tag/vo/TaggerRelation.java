package com.jackrain.nea.oc.oms.tag.vo;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import lombok.*;

import java.util.List;

/**
 * Description： 订单打标需要的入参定义
 * Author: RESET
 * Date: Created in 2020/7/8 22:01
 * Modified By:
 */
@Getter
@Setter
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.MODULE)
@Builder(toBuilder = true)
public class TaggerRelation {

    // 公共的参数
    OcBOrder ocBOrder;  // 零售发货单
    List<OcBOrderItem> ocBOrderItemList; // 零售发货单明细

    List<OcBOrder> mergeOrders;//合并零售发货单

}
