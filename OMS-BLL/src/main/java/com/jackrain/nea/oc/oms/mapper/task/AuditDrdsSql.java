package com.jackrain.nea.oc.oms.mapper.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @author: 易邵峰
 * @since: 2020-02-17
 * create at : 2020-02-17 22:48
 */
@Slf4j
public class AuditDrdsSql {

    public String selectByNodeSql(Map<String, Object> para) {
        Long executeTime = (Long)para.get("executeTime");
        StringBuilder sql = new StringBuilder();
        StringBuilder limitStr = new StringBuilder(" LIMIT ");
        String shopIds = (String) para.get("shopIds");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);
        String taskTableName = (String) para.get("taskTableName");

        sql.append("select order_id,shop_id from ")
                .append(taskTableName)
                .append(" FORCE INDEX (status_index) where status = 0 and shop_id in ")
                .append(shopIds)
                .append(" and next_time <= ")
                .append(executeTime)
                .append(" ORDER BY modifieddate asc ")
                .append(limitStr);

        return sql.toString();
    }

    public String insertAuditTaskList(Map<String, Object> para) {
        JSONArray jsonArray = (JSONArray) para.get("jsonArray");
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO oc_b_audit_task (id, order_id, status, creationdate) VALUES ");
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            sql.append("(");
            sql.append(jsonObject.get("id") + ",");
            sql.append(jsonObject.get("order_id") + ",");
            sql.append(jsonObject.get("status") + ",");
            sql.append("now()");
            sql.append(")" + ",");
        }
        return sql.substring(0, sql.length() - 1);
    }


}
