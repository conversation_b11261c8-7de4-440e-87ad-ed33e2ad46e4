package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;

/**
 * 时效订单释放需要备注释放方式，
 * 区分手工释放 or 取消时效订单
 *
 * <AUTHOR>
 * @date 2020-07-22
 */
public enum TimeOrderOutEnum {

    /**
     * 手动释放
     */
    MAUUAL(1, "手动释放"),

    TRANSFORMATION(2, "单据转换");

    TimeOrderOutEnum(Integer key, String name) {
        this.key = key;
        this.name = name;
    }

    @Getter
    private Integer key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String TimeOrderOutEnum(Integer key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (OcInvoiceLogTypeEnum e : OcInvoiceLogTypeEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return null;
    }

}
