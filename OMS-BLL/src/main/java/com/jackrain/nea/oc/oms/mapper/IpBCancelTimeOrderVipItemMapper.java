package com.jackrain.nea.oc.oms.mapper;


import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBCancelTimeOrderVipItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Mapper
public interface IpBCancelTimeOrderVipItemMapper extends ExtentionMapper<IpBCancelTimeOrderVipItem> {
    @Select("SELECT * FROM ip_b_cancel_time_order_vip_item WHERE ip_b_cancel_time_order_vip_id=#{orderId}")
    List<IpBCancelTimeOrderVipItem> selectOrderItemList(@Param("orderId") long orderId);
}