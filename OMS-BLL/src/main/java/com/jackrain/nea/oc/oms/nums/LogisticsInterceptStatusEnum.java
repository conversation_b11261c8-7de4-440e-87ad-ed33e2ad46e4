package com.jackrain.nea.oc.oms.nums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/3/3 21:53
 * @Description 拦截退改类型
 */
public enum LogisticsInterceptStatusEnum {

    NOT_INTERCEPT(0, "NOT_INTERCEPT", "未拦截"),

    SEND_INTERCEPT_SUCCESS(1, "SEND_INTERCEPT_SUCCESS", "发起拦截成功"),

    SEND_INTERCEPT_FAIL(2, "SEND_INTERCEPT_FAIL", "发起拦截失败"),

    INTERCEPTING(3, "INTERCEPTING", "拦截中"),

    INTERCEPT_SUCCESS(4, "INTERCEPT_SUCCESS", "拦截成功"),

    INTERCEPT_FAIL(5, "INTERCEPT_FAIL", "拦截失败"),

    SEND_CANCEL_SUCCESS(6, "INTERCEPT_FAIL", "发起取消拦截成功"),

    SEND_CANCEL_FAIL(7, "INTERCEPT_FAIL", "发起取消拦截失败"),

    INTERCEPT_CANCEL(8, "INTERCEPT_CANCEL", "取消拦截成功"),

    PACKAGE_DELIVERYING(9, "PACKAGE_DELIVERYING", "退改中"),

    INTERCEPT_DISCONTINUE(10, "INTERCEPT_DISCONTINUE", "拦截完结"),

    ERROR(-1, "", "平台推送了未知状态");

    LogisticsInterceptStatusEnum(Integer key, String value, String desc) {
        this.key = key;
        this.value = value;
        this.desc = desc;
    }

    @Getter
    private Integer key;

    @Getter
    private String value;

    @Getter
    private String desc;

    public static LogisticsInterceptStatusEnum getEnumByValue(String value) {

        if (StringUtils.isEmpty(value)) {
            return ERROR;
        }
        for (LogisticsInterceptStatusEnum e : LogisticsInterceptStatusEnum.values()) {
            if (StringUtils.equals(value, e.getValue())) {
                return e;
            }
        }
        return ERROR;
    }

    public static LogisticsInterceptStatusEnum getEnumByKey(Integer key) {
        if (Objects.isNull(key)) {
            return ERROR;
        }
        for (LogisticsInterceptStatusEnum e : LogisticsInterceptStatusEnum.values()) {
            if (e.getKey().equals(key)) {
                return e;
            }
        }
        return ERROR;
    }
}
