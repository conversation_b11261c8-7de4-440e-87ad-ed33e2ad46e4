package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCStickerItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StCStickerItemMapper extends ExtentionMapper<StCStickerItem> {

    @Select("SELECT COUNT(1) FROM st_c_sticker_item WHERE st_c_sticker_id = #{mainId}")
    int selectStCStickerItemByMainId(@Param("mainId")Long mainId);


    @Select("SELECT * FROM st_c_sticker_item WHERE st_c_sticker_id = #{mainId} and isactive = 'Y'")
    List<StCStickerItem> selectStCStickerItemListByMainId(@Param("mainId")Long mainId);
}