package com.jackrain.nea.oc.oms.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.org.apache.commons.lang3.math.NumberUtils;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cp.result.CpCSupplier;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.RegionType;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.constant.NaiKaTypeConstant;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.enums.BackflowStatus;
import com.jackrain.nea.oc.oms.model.enums.ExpiryDateOrderLabelEnum;
import com.jackrain.nea.oc.oms.model.enums.IsActiveEnum;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OmsOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderLink;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.oc.oms.services.OmsBusinessTypeDistinguishService;
import com.jackrain.nea.oc.oms.services.advance.AdvanceConstant;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.psext.api.PsCCollShopProMappingQueryCmd;
import com.jackrain.nea.psext.model.table.PsCCollShopProMapping;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.OmsBusinessTypeUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

import static com.jackrain.nea.resource.CpRedisKeyResources.getRegionKey;

/**
 * 通用订单表订单中间表转换成全渠道订单
 *
 * <AUTHOR>
 * @since: 2019-07-09
 * create at : 2019-07-09
 */
@Component
@Slf4j
public class StandplatOrderTransferUtil {

    private RegionNewService regionService;

    private CpRpcService cpRpcService;

    private BuildSequenceUtil sequenceUtil;

    private PropertiesConf propertiesConf;

    /**
     * oms 系统参数配置
     */
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private OcBOrderNaiKaMapper orderNaiKaMapper;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private PsRpcService psRpcService;

    @DubboReference(group = "ps-ext", version = "1.0")
    private PsCCollShopProMappingQueryCmd psCCollShopProMappingQueryCmd;

    @Autowired
    private OmsBusinessTypeDistinguishService omsBusinessTypeDistinguishService;

    @Autowired
    private BusinessSystemParamService businessSystemParamService;
    @Autowired
    private StRpcService stRpcService;

    private static final String PVLE_ORDER = "4";
    private static final String FREE_PVLE_ORDER = "10";
    private static final long FORTY_NINE_HOUR = 49 * 60 * 60 * 1000;

//    private static List<Long> platforms = Lists.newArrayList(103L, 20L);

    public StandplatOrderTransferUtil() {
    }

    @Autowired
    public StandplatOrderTransferUtil(RegionNewService regionService, CpRpcService cpRpcService,
                                      BuildSequenceUtil sequenceUtil, PropertiesConf propertiesConf, OmsSystemConfig omsSystemConfig) {
        this.regionService = regionService;
        this.cpRpcService = cpRpcService;
        this.sequenceUtil = sequenceUtil;
        this.propertiesConf = propertiesConf;
        this.omsSystemConfig = omsSystemConfig;
    }

    /**
     * 通过子表的   平台售价（price）*商品数量之和（排除status NOT IN ('TRADE_CLOSED_BY_standplat')）
     *
     * @param itemList 通用中间表订单明细
     * @return BigDecimal
     */
    private BigDecimal buildTotalProductAmount(List<IpBStandplatOrderItemEx> itemList) {

        if (CollectionUtils.isEmpty(itemList)) {
            return BigDecimal.ZERO;
        }
        return itemList.stream()
                .filter(item -> !StringUtils.equalsIgnoreCase("6", item.getRefundStatus()))
                .map(item -> Optional.ofNullable(item.getPrice()).orElse(BigDecimal.ZERO).multiply(new BigDecimal(item.getNum()))) // 单行商品金额 = 平台售价*数量
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO)
                .setScale(4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算商品总折扣金额
     *
     * @param itemList 通用中间表订单信息
     * @return 商品总折扣金额
     */
    private BigDecimal buildTotalProductDiscount(List<IpBStandplatOrderItemEx> itemList) {

        if (CollectionUtils.isEmpty(itemList)) {
            return BigDecimal.ZERO;
        }
        // 退款完成的商品不计算到商品总折扣金额中
        return itemList.stream()
                .filter(item -> !StringUtils.equalsIgnoreCase("6", item.getRefundStatus())) // 去除已退款商品
                .map(item -> Optional.ofNullable(item.getDiscountFee()).orElse(BigDecimal.ZERO)) // 每个商品行的折扣金额
                .reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO)
                .setScale(4, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算订单金额(金额四舍五入保留4位)
     * “商品总额”+“物流费用”+“调整金额”-“订单优惠金额”-“商品优惠金额”
     *
     * @param standplatOrderRelation 通用中间表订单信息
     * @return 计算订单金额
     */
    private BigDecimal buildOrderAmount(IpStandplatOrderRelation standplatOrderRelation) {
        IpBStandplatOrder standplatOrder = standplatOrderRelation.getStandplatOrder();
        // 商品金额
        BigDecimal orderAmount = this.buildTotalProductAmount(standplatOrderRelation.getStandPlatOrderItemList());
        // 注意点：需要判断值是否为空。如果为空则默认按照0处理。
        // 邮费
        orderAmount = orderAmount.add(Optional.ofNullable(standplatOrder.getPostFee()).orElse(BigDecimal.ZERO));
        // 调整金额
        orderAmount = orderAmount.add(Optional.ofNullable(standplatOrder.getAdjustFee()).orElse(BigDecimal.ZERO));
        // 订单优惠金额
        orderAmount = orderAmount.subtract(Optional.ofNullable(standplatOrder.getDiscountFee()).orElse(BigDecimal.ZERO));
        // 商品优惠金额
        orderAmount = orderAmount.subtract(this.buildTotalProductDiscount(standplatOrderRelation.getStandPlatOrderItemList()));

        // 平台优惠金额：这里是异常的代码，平台金额不需要在这里去添加，根据商品价格计算，平台分摊的部分已是包含了
//        BigDecimal plantforCount = Optional.ofNullable(standplatOrder.getPlantforCount()).orElse(BigDecimal.ZERO);
//        orderAmount = orderAmount.add(plantforCount);
        return orderAmount.setScale(4, BigDecimal.ROUND_HALF_UP);
    }


    /**
     * 转换成零售发货单对象
     *
     * @param standplatOrderRelation 通用订单中间表关联对象
     * @param isHistoryOrder         是否为历史订单
     * @return 零售发货单对象
     */
    private OcBOrder buildOcBOrderFromIpstandplatOrder(IpStandplatOrderRelation standplatOrderRelation,
                                                       boolean isHistoryOrder) {
        IpBStandplatOrder standplatOrder = standplatOrderRelation.getStandplatOrder();
        OcBOrder order = new OcBOrder();
        //id自增长
        order.setId(sequenceUtil.buildOrderSequenceId());
        order.setModifierename(SystemUserResource.ROOT_USER_NAME);
        order.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        order.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(order);

        //单据编号
        order.setBillNo(sequenceUtil.buildBillNo());
        //平台单号信息
        order.setSourceCode(standplatOrder.getTid());
        //初始平台单号（确定唯一）
        order.setTid(standplatOrder.getTid());
        //下单店铺id.
        order.setCpCShopId(standplatOrder.getCpCShopId());
        //下单店铺标题。需要查个表获取Title（平台店铺信息表）
        //平台店铺标题
        CpShop shopInfo = null;
        if (standplatOrder.getCpCShopId() != null) {
            shopInfo = cpRpcService.selectShopById(standplatOrder.getCpCShopId());
        }
        if (shopInfo != null) {
            //下单店仓id. 到平台店铺信息表中获取下单卖家店铺名称
            order.setCpCShopSellerNick(shopInfo.getSellerNick());
            order.setCpCShopTitle(shopInfo.getCpCShopTitle());
            // @20200723 增加店铺编码的设置
            order.setCpCShopEcode(shopInfo.getEcode());
        } else {
            // 20190730修改：如果 平台店铺不存在，则不再继续保持。而是抛出异常，不允许转单操作
            throw new NDSException("平台店铺id=" + standplatOrder.getCpCShopId() + "不存在");
        }

        //订单类型
        order.setOrderType(OrderTypeEnum.NORMAL.getVal());
        if (ObjectUtil.isNotEmpty(standplatOrder.getOrderType())) {
            order.setBusinessType(standplatOrder.getOrderType());
//            if ("1".equals(standplatOrder.getOrderType())) {
//                omsBusinessTypeDistinguishService.handleTYPE1(order);
//            }

        }
        if (isHistoryOrder) {
            //为历史订单时直接修改状态为平台发货
            order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            order.setIsHistory("Y");
            // @20201218 历史订单出库时间赋值 任务ID 29976
            order.setScanTime(new Date());
        } else {
            //订单状态. 默认状态为50,如果平台类型为POS，则赋值为平台发货
            order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
            order.setIsHistory("N");
        }
        //订单旗帜
        order.setOrderFlag(null);
        // 商品总额 = SUM(平台售价*数量）
        order.setProductAmt(this.buildTotalProductAmount(standplatOrderRelation.getStandPlatOrderItemList()));
        //订单总额.“商品总额”+“物流费用”+“调整金额”-“订单优惠金额”-“商品优惠金额”
        order.setOrderAmt(this.buildOrderAmount(standplatOrderRelation));
        //商品优惠金额
        BigDecimal prodDiscountAmt = this.buildTotalProductDiscount(standplatOrderRelation.getStandPlatOrderItemList());
        order.setProductDiscountAmt(prodDiscountAmt);
        //订单优惠金额。就是 中间表整单优惠金额
        order.setOrderDiscountAmt(standplatOrder.getDiscountFee() == null ? BigDecimal.ZERO : standplatOrder.getDiscountFee());

        //配送费用。如果为空，则赋值0.
        order.setShipAmt(standplatOrder.getPostFee() == null ? BigDecimal.ZERO : standplatOrder.getPostFee());
        //货到付款服务费。如果为空，则赋值0.
        order.setServiceAmt(standplatOrder.getCodFee() == null ? BigDecimal.ZERO : standplatOrder.getCodFee());
        // 已收金额。如果为空，则赋值0.
        order.setReceivedAmt(standplatOrder.getPayment() == null ? BigDecimal.ZERO : standplatOrder.getPayment());
        //是否开票.若【开票抬头】为空，则为否（0），若有值，则为是（1）
        if (StringUtils.isNotEmpty(standplatOrder.getInvoiceName())) {
            order.setIsInvoice(1);
        } else {
            order.setIsInvoice(0);
        }
        //开票抬头
        order.setInvoiceHeader(standplatOrder.getInvoiceName());
        //开票内容
        order.setInvoiceContent(null);
        //商品重量
        order.setWeight(BigDecimal.ZERO);
        //下单时间
        order.setOrderDate(standplatOrder.getTradeCreateTime());
        if (order.getOrderDate() == null) {
            //【支付时间】：【通用订单中间表】中“支付时间”
            order.setOrderDate(standplatOrder.getPayTime());
        }
        //交易结束时间
        order.setEndTime(standplatOrder.getTradeEndTime());

        // @20200723 设置区域地址信息：由于速卖通的设置有区别，所以独立拆分成两套逻辑
        setReceiverInfo(standplatOrderRelation, order);
        // @20200723 设置物流信息：速卖通有区分，所以独立
        setLogisticsInfo(standplatOrderRelation, order);

        //是否合并订单 默认0不合并
        order.setIsMerge(0);
        //是否拆分订单
        order.setIsSplit(0);
        //缺货重新占单/拆单 次数
        order.setQtySplit(0L);
        //是否已经拦截
        order.setIsInterecept(0);
        //是否退款中
        order.setIsInreturning(0);
        //销售员编号
        order.setSalesmanId(0L);
        //销售员名称
        order.setSalesmanName(null);
        //物流成本.需要计算成本。默认为0
        order.setLogisticsCost(BigDecimal.ZERO);
        //所有SKU（最多五个超过，显示数量）。转单赋值空
        order.setAllSku(null);
        //支付方式若
        order.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        //买家留言
        order.setBuyerMessage(standplatOrder.getBuyerMessage());
        //订单来源
        order.setOrderSource("通用订单新增");
        //销售商品属性
        order.setSaleProductAttr(standplatOrder.getReserveVarchar05());
        Long reserveBigint05 = standplatOrder.getReserveBigint05();
        if (reserveBigint05 != null && reserveBigint05 == 1) {
            order.setStatusPayStep(TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID);
            order.setDouble11PresaleStatus(1);
            order.setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
        }
        if (reserveBigint05 != null && reserveBigint05 == 2) {
            order.setStatusPayStep(TaoBaoOrderStatus.FRONT_PAID_FINAL_PAID);
            order.setDouble11PresaleStatus(1);
            order.setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
        }
        //原始订单号。默认空
        order.setOrigOrderId(null);
        //原始退货单号
        order.setOrigReturnOrderId(null);
        //是否启用y启用
        //是否启用
        order.setIsactive(IsActiveEnum.Y.getKey());

        // 是否有赠品.0.
        order.setIsHasgift(0);
        //商品数量
        if (standplatOrder.getNum() == null || standplatOrder.getNum() == 0) {
            List<IpBStandplatOrderItemEx> standplatOrderItemList = standplatOrderRelation.getStandPlatOrderItemList();
            long count = standplatOrderItemList.stream().mapToLong(IpBStandplatOrderItemEx::getNum).sum();
            standplatOrder.setNum(count);
        }
        order.setQtyAll(new BigDecimal(standplatOrder.getNum()));
        order.setSkuKindQty(new BigDecimal(standplatOrderRelation.getStandPlatOrderItemList().size()));
        //卖家备注 若【通用订单中间表】中“卖家备注”为空，则不填写，若不为空，则叠加备注，并附带时间（原卖家备注，现卖家备注，当前时间）
        if (standplatOrder.getSellerMemo() == null) {
            order.setSellerMemo(null);
        } else {
            order.setSellerMemo(standplatOrder.getSellerMemo());
        }
        //买家邮箱地址
        order.setReceiverEmail(standplatOrder.getBuyerEmail());
        order.setPresaleDepositTime(standplatOrder.getPresaleDepositTime());
        //平台  通过平台店铺名称获取平台  shop ename
        // @20200723 和上面设置重复
        order.setOrderSourcePlatformEcode(standplatOrder.getOrderSourcePlatformEcode());
        if (PlatFormEnum.SAP.getCode().equals(standplatOrder.getCpCPlatformId().intValue()) || PlatFormEnum.DMS.getCode().equals(standplatOrder.getCpCPlatformId().intValue())) {
            order.setPlatform(standplatOrder.getTradeSource() == null ? null : Integer.parseInt(standplatOrder.getTradeSource()));
            order.setGwSourceGroup(standplatOrder.getCpCPlatformEcode());
        } else {
            order.setPlatform(standplatOrder.getCpCPlatformId() == null ? -1 : standplatOrder.getCpCPlatformId().intValue());
            String tradeSource = standplatOrder.getTradeSource();
            if (StringUtils.isNotBlank(tradeSource)) {
                CpCPlatform platform = cpRpcService.queryCpCPlatformBySourcePlatformCode(tradeSource);
                if (platform == null) {
                    throw new NDSException("来源系统编码:" + tradeSource + "未查询到平台信息");
                }
                order.setGwSourceGroup(platform.getId().toString());
            }
        }

        //付款时间
        // @20200803 bug-prd-282-无支付时间会导致订单无法审核，如无支付时间，则赋值为默认值
        order.setPayTime(Objects.isNull(standplatOrder.getPayTime()) ? new Date() : standplatOrder.getPayTime());

        //wms撤回状态调用WMS撤回是否成功。1=成功；2=失败
        order.setWmsCancelStatus(0);
        //买家昵称
        order.setUserNick(standplatOrder.getBuyerNick());
        //调整金额
        order.setAdjustAmt(standplatOrder.getAdjustFee() == null ? BigDecimal.ZERO : standplatOrder.getAdjustFee());
        //自动审核状态
        order.setAutoAuditStatus(0);
        //买家邮箱
        order.setBuyerEmail(standplatOrder.getBuyerEmail());
        //到付代收金额. 如果是支付方式=到付，则赋值订单金额
        order.setCodAmt(BigDecimal.ZERO);
        //代销结算金额. 默认为0
        order.setConsignAmt(BigDecimal.ZERO);
        //代销运费. 默认为0
        order.setConsignShipAmt(BigDecimal.ZERO);
        //发货实体仓. 赋值null，后续在分配物流中使用
        order.setCpCPhyWarehouseId(null);
        //配货时间. 配货阶段进行赋值
        order.setDistributionTime(null);
        //内部备注。后续手动填写
        order.setInsideRemark(null);
        //商品计算重量。是否需要进行称重。根据系统配置来进行赋值。可能没用。产品也不知道
        order.setIsCalcweight(0);
        //是否组合订单
        boolean hasCombine = this.hasCombineProduct(standplatOrderRelation);
        order.setIsCombination(hasCombine ? 1 : 0);
        //是否生成开票通知。现在赋值为N。占用订单后再进行赋值
        order.setIsGeninvoiceNotice(0);
        // 是否已给物流。占单后再进行赋值
        // order.setIsGiveLogistic(0);
        // 是否有赠品.0.否。计算完赠品策略赋值
        // 后期需要根据逻辑判断进行赋值
        order.setIsHasgift(0);
        //包含预售商品
       /* boolean hasPreSale = this.hasPreSaleProduct(standplatOrderRelation);
        order.setIsHaspresalesku(hasPreSale ? 1 : 0);*/
        //是否虚拟订单。现在赋值为N
        order.setIsInvented(0);
        //京仓订单
        order.setIsJcorder(0);
        //实缺标记
//        order.setIsLackstock(0);
        //是否生成调拨零售
        // order.setIsTodrp(0);
        //应收平台金额
        order.setJdReceiveAmt(BigDecimal.ZERO);
        //京东结算金额
        order.setJdSettleAmt(BigDecimal.ZERO);
        //物流成本.需要计算成本。默认为0
        order.setMergeOrderId(null);
        // 合并单号，初始值与平台单号一致
        order.setMergeSourceCode(standplatOrder.getTid());
        //订单占单状态
        order.setOccupyStatus(0);
        //操作费.默认为0
        // order.setOperateAmt(BigDecimal.ZERO);
        //订单标签
        order.setOrderTag(null);
        //出库状态. WMS后调用,已出库未出库,现在没有用
        order.setOutStatus(null);
        // 平台状态
        order.setPlatformStatus(standplatOrder.getStatus());
        //官网渠道代码
        order.setGwSourceCode(standplatOrder.getGwVipMobile());

        //译氪优惠券
        order.setOrderDiscount(this.buildOrderDiscount(standplatOrderRelation));

        if (StringUtils.isEmpty(standplatOrder.getWhetherNeedReceipt()) || "N".equalsIgnoreCase(standplatOrder.getWhetherNeedReceipt())) {
            order.setWhetherNeedReceipt("N");
        } else if ("Y".equalsIgnoreCase(standplatOrder.getWhetherNeedReceipt())) {
            order.setWhetherNeedReceipt("Y");
        }
        order.setEstimateConTime(standplatOrder.getEstimatedDeliveryTime());
        /**销售组织*/
        if (StringUtils.isNotBlank(standplatOrder.getSalesOrganization())) {
            CpCSupplier cpCSupplier = cpRpcService.queryCompanyByEcode(standplatOrder.getSalesOrganization());
            if (cpCSupplier != null) {
                order.setSalesOrganizationId(cpCSupplier.getId());
            }
        }
        /**查询销售部门、成本中心id*/
        List<String[]> arrList = new ArrayList<>();
        if (StringUtils.isNotBlank(standplatOrder.getSalesDepartment())) {
            String[] salesDepartment = {"C_STOREATTRIB2_ID", standplatOrder.getSalesDepartment()};
            arrList.add(salesDepartment);
        }
        if (StringUtils.isNotBlank(standplatOrder.getCostCenter())) {
            String[] costCenter = {"C_STOREATTRIB4_ID", standplatOrder.getCostCenter()};
            arrList.add(costCenter);
        }
        if (StringUtils.isNotBlank(standplatOrder.getSalesGroupCode())) {
            String[] costCenter = {"C_STOREATTRIB3_ID", standplatOrder.getSalesGroupCode()};
            arrList.add(costCenter);
        }
        order.setOaid(standplatOrder.getOaid());
        if (CollectionUtils.isNotEmpty(arrList)) {
            Map<String, JSONObject> resultMap = cpRpcService.findStoredimItemIdByeCodeList(arrList);
            if (resultMap != null) {
                order.setSalesDepartmentId(resultMap.get("C_STOREATTRIB2_ID") != null ? resultMap.get("C_STOREATTRIB2_ID").getLong("id") : null);
                order.setSalesDepartmentName(resultMap.get("C_STOREATTRIB2_ID") != null ? resultMap.get("C_STOREATTRIB2_ID").getString("ename") : null);
                order.setCostCenterId(resultMap.get("C_STOREATTRIB4_ID") != null ? resultMap.get("C_STOREATTRIB4_ID").getLong("id") : null);
                order.setSalesGroupId(resultMap.get("C_STOREATTRIB3_ID") != null ? resultMap.get("C_STOREATTRIB3_ID").getLong("id") : null);
                order.setSalesGroupCode(resultMap.get("C_STOREATTRIB3_ID") != null ? resultMap.get("C_STOREATTRIB3_ID").getString("ecode") : null);
                order.setSalesGroupName(resultMap.get("C_STOREATTRIB3_ID") != null ? resultMap.get("C_STOREATTRIB3_ID").getString("ename") : null);
            }
        }

        List<IpBStandplatOrderItemEx> standPlatOrderItemList = standplatOrderRelation.getStandPlatOrderItemList();
        if (CollectionUtils.isNotEmpty(standPlatOrderItemList)) {
            List<IpBStandplatOrderItemEx> collect = standPlatOrderItemList.stream().filter(o -> StringUtils.isNotEmpty(o.getFactory())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                order.setFactory(collect.get(0).getFactory());
            }
            //判断是不是奶卡提货,如果CARD_CODE 存在就是奶卡提货
            List<IpBStandplatOrderItemEx> itemExList = standPlatOrderItemList.stream().filter(o -> StringUtils.isNotEmpty(o.getCardCode())).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(itemExList)){
//                String cardCode = itemExList.get(0).getCardCode();
//                //奶卡原销售店铺赋值逻辑
//                naiKaiParesGwSourceCode(cardCode,order);
//            }
        }
        //京东代销逻辑
        if (PlatFormEnum.JINGDONG_DX.getCode().equals(standplatOrder.getCpCPlatformId().intValue())) {
            //分销商id字段
            if (StringUtils.isNotEmpty(standplatOrder.getSellerId())) {
                order.setSellerId(standplatOrder.getSellerId());
            }
            //如果通用订单中间表的交易类型type=1 货到付款 则创建零售发货单时，物流公司赋值为京东快递
            if (StringUtils.isNotEmpty(standplatOrder.getType())
                    && standplatOrder.getType().equals("1")) {
                order.setPayType(OmsPayType.CASH_ON_DELIVERY.toInteger());
            }
        }
        order.setDouble11PresaleStatus(1);
        order.setOrderType(OrderTypeEnum.TBA_PRE_SALE.getVal());
        return order;
    }

    /**
     * description:赋值奶卡原销售店铺
     *
     * @Author: liuwenjin
     * @Date 2022/9/26 19:29
     */
    private void naiKaiParesGwSourceCode(String cardCode, OcBOrder newOcBOrder) {
        try {
            // 根据奶卡卡号查询非提货订单ID，理论上只有一条
            List<OcBOrderNaiKa> naiKaList = orderNaiKaMapper.selectBySourceCodebyGsi(cardCode);
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("通用订单转换单奶卡提货逻辑开始:{}"), JSONObject.toJSONString(naiKaList));
            }
            if (CollectionUtils.isNotEmpty(naiKaList)) {
                naiKaList = naiKaList.stream().filter(o -> !NaiKaTypeConstant.PICK_UP.equals(o.getBusinessType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(naiKaList)) {

                    OcBOrder ocBOrder = orderMapper.selectById(naiKaList.get(0).getOcBOrderId());

                    //判断是否查询到原单，查不到就报错
                    if (Objects.nonNull(ocBOrder)) {
                        String oldGwSourceCode = ocBOrder.getCpCShopId() + "";
                        //是云集店铺查询映射把子店赋值给奶卡原销售店铺 不是就直接把原单店铺赋值
                        if (log.isDebugEnabled()) {
                            log.debug(LogUtil.format("通用订单转换单奶卡提货是否云集，Platform:{},ShopEcode{}"), ocBOrder.getPlatform(), ocBOrder.getCpCShopEcode());
                        }
                        if (checkIsYunJi(ocBOrder.getPlatform(), ocBOrder.getCpCShopEcode())) {
                            List<OcBOrderItem> orderItems = orderItemMapper.selectOrderItems(ocBOrder.getId());
                            //获取所有的sku集合
                            List<Long> skuList = orderItems.stream().map(OcBOrderItem::getPsCSkuId).distinct().collect(Collectors.toList());
                            //查询映射
                            PsCCollShopProMapping shopProMapping = psRpcService.queryProMappingByShopIdAndSku(ocBOrder.getCpCShopId(), skuList);
                            if (log.isDebugEnabled()) {
                                log.debug(LogUtil.format("通用订单转换单奶卡提货逻辑，查询映射:{}"), JSONObject.toJSONString(shopProMapping));
                            }
                            if (shopProMapping != null) {
                                oldGwSourceCode = shopProMapping.getCpCShopChildId() + "";
                            }
                        }
                        newOcBOrder.setGwSourceCode(oldGwSourceCode);
                    } else {
                        throw new NDSException("奶卡编码:" + cardCode + "未查到原零售发货单");
                    }
                }
            }
        } catch (Exception e) {
            log.error(LogUtil.format("通用订单转换单奶卡提货逻辑异常:{}"), Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * description:订单判断是不是云集店铺的 200066为指定的云集店铺写死
     *
     * @Author: liuwenjin
     * @Date 2022/9/26 19:24
     */
    private boolean checkIsYunJi(Integer platform, String cpCShopEcode) {
        return PlatFormEnum.PLATFORM62.getCode().equals(platform) && "200066".equals(cpCShopEcode);
    }

    /**
     * <AUTHOR>
     * @Date 14:14 2021/4/16
     * @Description 订单折扣 = （总金额-配送费用 -服务费）/ 商品金额。
     */
    private BigDecimal buildOrderDiscount(IpStandplatOrderRelation standplatOrderRelation) {
        try {
            IpBStandplatOrder standplatOrder = standplatOrderRelation.getStandplatOrder();
            return (Optional.ofNullable(this.buildOrderAmount(standplatOrderRelation)).orElse(BigDecimal.ZERO)
                    .subtract(Optional.ofNullable(standplatOrder.getPostFee()).orElse(BigDecimal.ZERO))
                    .subtract(Optional.ofNullable(standplatOrder.getCodFee()).orElse(BigDecimal.ZERO)))
                    .divide(Optional.ofNullable(this.buildTotalProductAmount(standplatOrderRelation.getStandPlatOrderItemList())).orElse(BigDecimal.ZERO), 4, BigDecimal.ROUND_HALF_UP);
        } catch (Exception e) {
            log.info("buildOrderDiscount", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 设置物流信息：因为速卖通的需要特殊处理，所以独立逻辑
     *
     * @param standplatOrderRelation
     * @param order
     */
    private void setLogisticsInfo(IpStandplatOrderRelation standplatOrderRelation, OcBOrder order) {
        IpBStandplatOrder standplatOrder = standplatOrderRelation.getStandplatOrder();

        if (Objects.nonNull(standplatOrder)) {
            if (standplatOrderRelation.isAliExpress()) {
                // 速卖通的逻辑
                // 物流公司编码
                String logisticsCompany = standplatOrder.getLogisticscompany();

                if (StringUtils.isNotEmpty(logisticsCompany)) {
                    LogisticsInfo logisticsInfo = this.cpRpcService.selectLogisticsInfo(logisticsCompany);

                    if (Objects.nonNull(logisticsInfo)) {
                        // 设置物流信息
                        order.setCpCLogisticsEcode(logisticsInfo.getCode());
                        order.setCpCLogisticsEname(logisticsInfo.getName());
                        order.setCpCLogisticsId(logisticsInfo.getId());
                    } else {
                        // 理论上这里不会为空，如果为空则30步骤能校验到
                        throw new NDSException("速卖通平台，物流公司不存在。");
                    }
                } else {
                    throw new NDSException("速卖通平台，物流公司为空。");
                }
            } else {
                // 原逻辑
                // 处理解析物流公司信息
                this.parseLogisticsInfo(standplatOrder, order);
                //物流编码. 分配物流后进行赋值
                order.setExpresscode(null);
            }
        }
    }

    /**
     * 设置地址区域信息
     *
     * @param standplatOrderRelation
     * @param order
     */
    private void setReceiverInfo(IpStandplatOrderRelation standplatOrderRelation, OcBOrder order) {
        IpBStandplatOrder standplatOrder = standplatOrderRelation.getStandplatOrder();

        if (Objects.nonNull(standplatOrder)) {
            if (standplatOrderRelation.isAliExpress()) {
                setRegionInfo4AliExpress(standplatOrder, order);
            } else {
                setRegionInfo4Normal(standplatOrder, order);
            }

            //收货人姓名
            order.setReceiverName(standplatOrder.getReceiverName());
            //收货人的手机号码
            // 如果是得物的话 需要解析下。 原文是"13245067612转4776（生效中）"
            if (ObjectUtil.equal(PlatFormEnum.DE_WU.getCode(), standplatOrder.getCpCPlatformId().intValue())) {
                String receiverMobile = standplatOrder.getReceiverMobile();
                if (StringUtils.isNotEmpty(receiverMobile) && receiverMobile.contains("转")) {
                    String[] receiverMobileArr = receiverMobile.split("转");
                    if (StringUtils.isNotEmpty(receiverMobileArr[0])) {
                        order.setReceiverMobile(receiverMobileArr[0]);
                    }
                    if (StringUtils.isNotEmpty(receiverMobileArr[1])) {
                        order.setReceiverName(order.getReceiverName() + receiverMobileArr[1]);
                    }
                }
                if (StringUtils.isEmpty(order.getReceiverMobile())) {
                    order.setReceiverMobile(standplatOrder.getReceiverMobile());
                }
            } else {
                order.setReceiverMobile(standplatOrder.getReceiverMobile());
            }
            //收货人的电话号码
            order.setReceiverPhone(standplatOrder.getReceiverPhone());
            //买家邮件地址
            order.setReceiverEmail(null);
        }
    }

    /**
     * 速卖通的区域地址信息赋值逻辑
     *
     * @param standplatOrder
     * @param order
     */
    private void setRegionInfo4AliExpress(IpBStandplatOrder standplatOrder, OcBOrder order) {
        /*
        速卖通信息
           id/ename/ecode
        国：1/中国/1
        省：990000/海外/990000
        市：990106/其他/990106
         */
        Long provinceId = 990000L;
        String provinceCode = provinceId.toString();
        String provinceName = "海外";

        Long cityId = 990106L;
        String cityCode = cityId.toString();
        String cityName = "其它";

        // 赋值
        // 省
        order.setCpCRegionProvinceId(provinceId);
        order.setCpCRegionProvinceEcode(provinceCode);
        order.setCpCRegionProvinceEname(provinceName);
        // 市
        order.setCpCRegionCityId(cityId);
        order.setCpCRegionCityEcode(cityCode);
        order.setCpCRegionCityEname(cityName);
        // 区
        order.setCpCRegionAreaId(null);
        order.setCpCRegionAreaEcode(null);
        order.setCpCRegionAreaEname(null);

        // 地址信息，拼接规则：国家 省市区 详细地址
        StringBuffer address = new StringBuffer();
        // 国家
        if (Objects.nonNull(standplatOrder.getReserveVarchar04())) {
            address.append(standplatOrder.getReserveVarchar04()).append(StringUtils.SPACE);
        }
        // 省
        if (Objects.nonNull(standplatOrder.getReceiverProvince())) {
            address.append(standplatOrder.getReceiverProvince()).append(StringUtils.SPACE);
        }
        // 市
        if (Objects.nonNull(standplatOrder.getReceiverCity())) {
            address.append(standplatOrder.getReceiverCity()).append(StringUtils.SPACE);
        }
        // 区
        if (Objects.nonNull(standplatOrder.getReceiverDistrict())) {
            address.append(standplatOrder.getReceiverDistrict()).append(StringUtils.SPACE);
        }
        // 地址
        address.append(standplatOrder.getReceiverAddress());
        order.setReceiverAddress(address.toString());

        // zipCode
        order.setReceiverZip(standplatOrder.getReceiverZip());
    }

    /**
     * 通用设置地址信息
     *
     * @param standplatOrder
     * @param order
     */
    private void setRegionInfo4Normal(IpBStandplatOrder standplatOrder, OcBOrder order) {
        //买家所在省
        String provinceName = standplatOrder.getReceiverProvince();
        //买家所在市
        String cityName = standplatOrder.getReceiverCity();
        //买家所在区ID。
        String areaName = standplatOrder.getReceiverDistrict();

        ProvinceCityAreaInfo provinceCityAreaInfo = this.regionService.selectProvinceCityAreaInfo(provinceName, cityName, areaName);
        if (provinceCityAreaInfo == null) {
            throw new NDSException("省市区匹配异常");
        }
        if (provinceCityAreaInfo.getProvinceInfo() == null) {
            throw new NDSException("省匹配异常");
        }
        if (provinceCityAreaInfo.getCityInfo() == null) {
            throw new NDSException("市区匹配异常");
        }
        order.setCpCRegionProvinceId(provinceCityAreaInfo.getProvinceInfo().getId());
        order.setCpCRegionProvinceEcode(provinceCityAreaInfo.getProvinceInfo().getCode());
        order.setCpCRegionProvinceEname(provinceCityAreaInfo.getProvinceInfo().getName());
        order.setCpCRegionCityId(provinceCityAreaInfo.getCityInfo().getId());
        order.setCpCRegionCityEcode(provinceCityAreaInfo.getCityInfo().getCode());
        order.setCpCRegionCityEname(provinceCityAreaInfo.getCityInfo().getName());

        if (provinceCityAreaInfo != null && provinceCityAreaInfo.getAreaInfo() != null) {
            order.setCpCRegionAreaId(provinceCityAreaInfo.getAreaInfo().getId());
            order.setCpCRegionAreaEcode(provinceCityAreaInfo.getAreaInfo().getCode());
            order.setCpCRegionAreaEname(provinceCityAreaInfo.getAreaInfo().getName());
        } else {
            //如果区匹配不到那就用 "其他区" 来匹配
            String areaNameOther = "其它区";
            if (log.isDebugEnabled()) {
                log.debug(" 通用转单调 其他区逻辑,areaNameOther:{},cpCRegionCityId:{}", areaNameOther, order.getCpCRegionCityId());
            }
            String areaRedisKey = getRegionKey(RegionType.AREA, areaName, order.getCpCRegionCityId());
            RegionInfo areaInfo = regionService.getObjRedisTemplate().opsForValue().get(areaRedisKey);
            if (areaInfo == null || areaInfo.getId() == null) {
                areaInfo = regionService.selectRegionInfo(areaNameOther, 3, order.getCpCRegionCityId(), RegionType.AREA).getData();
            }
            if (areaInfo != null) {
                if (log.isDebugEnabled()) {
                    log.debug(" 通用转单调 其他区逻辑,areaNameOther:{}", JSON.toJSONString(areaInfo));
                }
                order.setCpCRegionAreaId(areaInfo.getId());
                order.setCpCRegionAreaEcode(areaInfo.getCode());
                order.setCpCRegionAreaEname(areaName);
            }
        }
        // 买家收货详细地址
        order.setReceiverAddress(standplatOrder.getReceiverAddress());
        //收件人邮编
        order.setReceiverZip(standplatOrder.getReceiverZip());
        // 收件人街道/乡镇
        order.setCpCRegionTownEname(standplatOrder.getReceiverTown());
    }

    /**
     * 解析是否有组合商品
     *
     * @param ipStandplatOrderRelation 淘宝订单中间表关联对象
     * @return 是否有组合商品。True=包含
     */
    private boolean hasCombineProduct(IpStandplatOrderRelation ipStandplatOrderRelation) {
        for (IpBStandplatOrderItemEx orderItem : ipStandplatOrderRelation.getStandPlatOrderItemList()) {
            if (orderItem.getProdSku() != null &&
                    (orderItem.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT
                            || orderItem.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 解析是否有预售商品
     *
     * @param orderRelation 淘宝订单中间表关联对象
     * @return 是否有预售商品。True=包含
     */
    private boolean hasPreSaleProduct(IpStandplatOrderRelation orderRelation) {
        for (IpBStandplatOrderItemEx orderItem : orderRelation.getStandPlatOrderItemList()) {
            if (this.checkProductIsPreSale(orderItem)) {
                return true;
            }
        }

        return false;
    }


    /**
     * 判断商品是否为预售商品
     *
     * @param orderItem 淘宝订单中间表明细数据
     * @return 是否为预售商品。True=是
     */
    private boolean checkProductIsPreSale(IpBStandplatOrderItemEx orderItem) {
        return orderItem.getProdSku() != null
                && orderItem.getProdSku().getSkuType() == SkuType.PRE_SALE_PRODUCT;

    }


    /**
     * 解析物流公司赋值
     *
     * @param standplatOrder 通用订单表
     * @param order          全渠道订单表
     */
    private void parseLogisticsInfo(IpBStandplatOrder standplatOrder, OcBOrder order) {
        // 若【通用订单中间表】中“物流类型”（shipping_type）为“ems”，则为“EMS”
        if (StringUtils.equalsIgnoreCase(CpRpcService.EMS_LOGISTICS_CODE, standplatOrder.getShippingType())) {
            LogisticsInfo logisticsInfo = this.cpRpcService.selectLogisticsInfo(
                    CpRpcService.EMS_LOGISTICS_CODE);
            if (logisticsInfo != null) {
                order.setCpCLogisticsEcode(logisticsInfo.getCode());
                order.setCpCLogisticsEname(logisticsInfo.getName());
                order.setCpCLogisticsId(logisticsInfo.getId());
            } else {
                //物流公司编码. 赋值null，后续在分配物流中使用。如果是EMS的则进行赋值
                order.setCpCLogisticsEcode(null);
                //物流公司名称. 赋值null，后续在分配物流中使用
                order.setCpCLogisticsEname(null);
                //配送物流公司. 赋值null，后续在分配物流中使用
                order.setCpCLogisticsId(null);
            }
        } else {
            //物流公司编码. 赋值null，后续在分配物流中使用。如果是EMS的则进行赋值
            order.setCpCLogisticsEcode(null);
            //物流公司名称. 赋值null，后续在分配物流中使用
            order.setCpCLogisticsEname(null);
            //配送物流公司. 赋值null，后续在分配物流中使用
            order.setCpCLogisticsId(null);
        }
    }

    /**
     * 根据退款状态转换成退款Integer值
     *
     * @param refundStatus 退款状态
     * @return 退款Integer值
     */
    private int convertRefundStatusToInteger(String refundStatus) {
        if (StringUtils.equalsIgnoreCase(refundStatus, "WAIT_SELLER_AGREE")) {
            return 1;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "WAIT_BUYER_RETURN_GOODS")) {
            return 2;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "WAIT_SELLER_CONFIRM_GOODS")) {
            return 3;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "SELLER_REFUSE_BUYER")) {
            return 4;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "CLOSED")) {
            return 5;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "SUCCESS")) {
            return 6;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "NO_REFUND")) {
            return 0;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "8")) {
            // 微购,退款完成
            return 6;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "7")) {
            // 微购,退款中
            return 1;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "17")) {
            //微购,待商家同意退货
            return 1;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "18")) {
            // 微购,待买家发货
            return 2;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "19")) {
            // 微购,待卖家审核发货
            return 3;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "20")) {
            // 微购,取消退货
            return 5;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "21")) {
            // 微购,退货完成
            return 6;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "22")) {
            // 微购,商家审核退货通过
            return 1;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "23")) {
            // 微购,商家审核退货不通过
            return 4;
        } else if (StringUtils.equalsIgnoreCase(refundStatus, "24")) {
            // 微购,退货中
            return 2;
        } else {
            return -1;
        }
    }

    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            return true;
        }
    }

    /**
     * 转换通用订单明细表
     *
     * @param ocBOrder           全渠道订单
     * @param standplatOrderItem 通用中间表明细
     * @return 通用订单明细表
     */
    private OcBOrderItem buildOrderItemFromstandplatOrderItem(OcBOrder ocBOrder, IpBStandplatOrderItemEx standplatOrderItem, boolean isHistoryOrder) {
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setVersion(0L);
        item.setPtProName(StringUtils.defaultString(standplatOrderItem.getTitle()));

        BaseModelUtil.initialBaseModelSystemField(item);

        // 活动编号. 默认赋值为null
        item.setActiveId(null);
        // 组合商品类型 断平台订单明细对应的商品是否为组合商品或者福袋商品？ 记录对应的状态
        // 正常商品
        int productType = SkuType.NORMAL_PRODUCT;
        if (standplatOrderItem.getProdSku() != null) {
            int skuType = standplatOrderItem.getProdSku().getSkuType();
            productType = combinationProductType(skuType);
        }
        item.setProType(new Long(productType));

        //
        //  退款状态
        //  如果是退款完成，或者是交易关闭 状态=6
        //  item.setRefundStatus(this.convertRefundStatusToInteger(standplatOrderItem.getRefundStatus()));
        String refundStatus = standplatOrderItem.getRefundStatus();
        if (refundStatus == null || isHistoryOrder) {
            String status = standplatOrderItem.getStatus();
            if (TaoBaoOrderStatus.TRADE_CANCELED.equals(status)) {
                item.setRefundStatus(6);
            } else {
                item.setRefundStatus(0);
            }
        } else {
            item.setRefundStatus(Integer.parseInt(standplatOrderItem.getRefundStatus()));
        }

        //条码id
        item.setPsCSkuId(standplatOrderItem.getProdSku().getId());

        //实缺标记
        item.setIsLackstock(0);
        //子订单编号(明细编号)
        String oid = standplatOrderItem.getOid();
        item.setOoid(oid);


        //商品路径就是图片路径
        item.setPicPath(standplatOrderItem.getPicPath());
        //【标题】：若为组合商品或福袋，则字段值为：组合+【通用订单中间表】明细表的“标题”+“条码”，否则【通用订单中间表】明细表的“标题”
        if (productType == SkuType.COMBINE_PRODUCT || productType == SkuType.GIFT_PRODUCT) {
            StringBuilder title = new StringBuilder();
            title.append("[组合]");
            title.append(standplatOrderItem.getTitle());
            title.append("[");
            String psSkuEcode = standplatOrderItem.getOuterSkuId();
            if (StringUtils.isBlank(psSkuEcode)) {
                psSkuEcode = standplatOrderItem.getOuterIid();
            }
            title.append(psSkuEcode);
            title.append("]");
            item.setTitle(title.toString());
        } else {
            item.setTitle(standplatOrderItem.getTitle());
        }
        //数量
        item.setQty(BigDecimal.valueOf(standplatOrderItem.getNum()));
        // 最近退款编号
        item.setRefundId(null);
        //已退数量。默认为0
        item.setQtyRefund(BigDecimal.ZERO);
        //调整金额
        item.setAdjustAmt(Optional.ofNullable(standplatOrderItem.getAdjustFee()).orElse(BigDecimal.ZERO));
        //优惠金额
        item.setAmtDiscount(Optional.ofNullable(standplatOrderItem.getDiscountFee()).orElse(BigDecimal.ZERO));
        //平台价格 【通用订单中间表】明细表的（“成本价”*“数量”-“优惠费用”+“调整费用”）/“数量”
        item.setPrice(standplatOrderItem.getPrice() == null ? BigDecimal.ZERO : standplatOrderItem.getPrice());
        //吊牌价格。【通用订单中间表】明细表的“标准价”
        item.setPriceList(standplatOrderItem.getProdSku().getPricelist());
        //规格  ----->  商品条码. normsdetailnames
        //商品条码
        //条码id
        //标准重量  ----> 商品条码. weight
        //条码编码。
        initialstandplatOrderItem(standplatOrderItem, item);
        //买家是否已评价
        item.setIsBuyerRate(0);
        //使用积分
        item.setBuyerUsedIntegral(0L);
        //是否已经占用库存
        item.setIsAllocatestock(0);
        //是否启用
        item.setIsactive(standplatOrderItem.getIsactive());
        //国标码。SKU 69码。从条码档案中有一个69码字段
        if (standplatOrderItem.getProdSku() != null) {
            item.setBarcode(standplatOrderItem.getProdSku().getBarcode69());
        } else {
            item.setBarcode(null);
        }
        //tid
        item.setTid(ocBOrder.getTid());
        //退货金额.默认为0
        item.setAmtRefund(BigDecimal.ZERO);
        //分销价格。默认为0
        item.setDistributionPrice(BigDecimal.ZERO);
        //组合名称
        item.setGroupName(null);
        //订单编号
        item.setOcBOrderId(ocBOrder.getId());
        initialTaobaoOrderItem(standplatOrderItem, item);
        //商品数字编码
        item.setNumIid(standplatOrderItem.getNumIid());
        //平台商品id
        item.setSkuNumiid(standplatOrderItem.getSkuId());

        //增加吊牌价和sex传值 getPricelist

        item.setSex(standplatOrderItem.getProdSku().getSex());
        item.setPriceTag(standplatOrderItem.getProdSku().getPricelist());
        item.setPsCBrandId(standplatOrderItem.getProdSku().getPsCBrandId());
        //平台SKUID   SKU_ID
        item.setPsCSkuPtEcode(standplatOrderItem.getSkuId());
        item.setGwCouponCode(standplatOrderItem.getGwCouponCode());
        if ("Y".equals(standplatOrderItem.getProdSku().getIsEnableExpiry())) {
            item.setIsEnableExpiry(1);
        } else {
            item.setIsEnableExpiry(0);
        }
        item.setMDim4Id(standplatOrderItem.getProdSku().getMDim4Id());
        item.setMDim6Id(standplatOrderItem.getProdSku().getMDim6Id());
        item.setExpiryDateType(standplatOrderItem.getExpiryDateType());
        item.setExpiryDateRange(standplatOrderItem.getExpiryDateRange());

        if (StringUtils.isNotEmpty(standplatOrderItem.getLabelingRequirements())) {
            String[] labelingRequirementsArr = standplatOrderItem.getLabelingRequirements().split(";");
            // 使用treemap 既可以排序 又能去重
            TreeMap<String, Integer> treeMap = new TreeMap<>();
            for (String labelingRequirement : labelingRequirementsArr) {
                if (StringUtils.isNotEmpty(labelingRequirement)) {
                    treeMap.put(labelingRequirement, 1);
                }
            }
            if (!treeMap.isEmpty()) {
                List<String> labelingRequirementList = new ArrayList<>();
                for (String labelingRequirement : treeMap.keySet()) {
                    labelingRequirementList.add(labelingRequirement);
                }
                if (CollectionUtils.isNotEmpty(labelingRequirementList)) {
                    String labelingRequirements = String.join(";", labelingRequirementList);
                    item.setLabelingRequirements(labelingRequirements);
                }
            }
        }


        // 一米有品
        item.setReserveVarchar04(standplatOrderItem.getReserveVarchar04());
        item.setPlanLineCategory(standplatOrderItem.getPlanLineCategory());
        String reserveVarchar10 = standplatOrderItem.getReserveVarchar10();

        if (StringUtils.isNotEmpty(reserveVarchar10)) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                item.setEstimateConTime(sdf.parse(reserveVarchar10));
            } catch (Exception e) {
                log.error(LogUtil.format("预计发货时间解析失败:{}", "预计发货时间解析失败"), Throwables.getStackTraceAsString(e));
            }
            // 如果预计发货时间跟支付时间如果差距<49小时 不标记"预";
            if (ocBOrder.getPayTime() != null && item.getEstimateConTime() != null) {
                long time = item.getEstimateConTime().getTime() - ocBOrder.getPayTime().getTime();
                if (time < FORTY_NINE_HOUR) {
                    ocBOrder.setDouble11PresaleStatus(0);
                    ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
                }
            }
        } else {
            ocBOrder.setDouble11PresaleStatus(0);
            ocBOrder.setOrderType(OrderTypeEnum.NORMAL.getVal());
            item.setIsExistConTime(AdvanceConstant.IS_EXIST_CON_TIME_0);
        }
        String isPresent = standplatOrderItem.getIsPresent();
        if ("true".equals(isPresent)) {
            // 平台赠品
            item.setGiftType("2");
            item.setIsGift(1);
            ocBOrder.setIsHasgift(1);
            String shopEcode = ocBOrder.getCpCShopEcode();
            List<String> omsStandplatOrderGiftSplit = businessSystemParamService.getOmsStandplatOrderGiftSplit();
            if (CollectionUtils.isNotEmpty(omsStandplatOrderGiftSplit) && omsStandplatOrderGiftSplit.contains(shopEcode)) {

                // 判断店铺策略
                StCShopStrategyDO strategyByCpCshopId =
                        stRpcService.selectOcStCShopStrategyByCpCshopId(ocBOrder.getCpCShopId());
                if (strategyByCpCshopId != null){
                    String canSplit = strategyByCpCshopId.getCanSplit();
                    if ("Y".equals(canSplit)) {
                        item.setIsGiftSplit(2);
                    }
                }
            }
        }
        item.setReserveVarchar03(standplatOrderItem.getReserveVarchar02());

        //周期购提数
        if (StringUtils.isNotEmpty(standplatOrderItem.getReserveVarchar02()) &&
                (PVLE_ORDER.equals(standplatOrderItem.getReserveVarchar02()) || FREE_PVLE_ORDER.equals(standplatOrderItem.getReserveVarchar02()))) {
            item.setCycleQty(standplatOrderItem.getCycleQty());
        }

        // 赠品属性
        item.setReserveBigint01(standplatOrderItem.getReserveBigint01());

        return item;
    }


    private List<OcBOrderNaiKa> buildOrderNaiKaFromstandplatOrderItem(OcBOrder ocBOrder, OcBOrderItem orderItem, IpBStandplatOrderItemEx standplatOrderItem, String naiKaType) {
        // 如果需要执行 则根据商品数量 遍历生成数据
        // 虚拟奶卡。 如果购买了两张奶卡 则会给两条明细。  实体奶卡如果购买了两张奶卡 会给我们一条明细。 两种情况我们都是在奶卡表中生成两条数据
        List<OcBOrderNaiKa> ocBOrderNaiKas = new ArrayList<>();
        String[] cardCodeArr = standplatOrderItem.getCardCode().split(",");
        if (ObjectUtil.equals(NaiKaTypeConstant.PICK_UP, naiKaType)) {
            OcBOrderNaiKa ocBOrderNaiKa = new OcBOrderNaiKa();
            BaseModelUtil.initialBaseModelSystemField(ocBOrderNaiKa);
            ocBOrderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
            ocBOrderNaiKa.setOcBOrderId(orderItem.getOcBOrderId());
            ocBOrderNaiKa.setOcBOrderItemId(orderItem.getId());
            ocBOrderNaiKa.setTid(orderItem.getTid());
            ocBOrderNaiKa.setBusinessType(naiKaType);
//            ocBOrderNaiKa.setBusinessTypeId(ocBOrder.getBusinessTypeId());
//            ocBOrderNaiKa.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
//            ocBOrderNaiKa.setBusinessTypeName(ocBOrder.getBusinessTypeName());
            ocBOrderNaiKa.setNaikaStatus(OmsOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus());
            ocBOrderNaiKa.setPsCProEcode(orderItem.getPsCProEcode());
            ocBOrderNaiKa.setPsCProId(orderItem.getPsCProId());
            ocBOrderNaiKa.setPsCProEname(orderItem.getPsCProEname());
            ocBOrderNaiKa.setPsCSkuId(orderItem.getPsCSkuId());
            ocBOrderNaiKa.setPsCSkuEcode(orderItem.getPsCSkuEcode());
            ocBOrderNaiKa.setCardCode(cardCodeArr[0]);
            ocBOrderNaiKa.setPsCSkuEname(orderItem.getPsCSkuEname());
            ocBOrderNaiKa.setSkuSpec(orderItem.getSkuSpec());
            ocBOrderNaiKas.add(ocBOrderNaiKa);
        } else {
            for (int i = 0; i < orderItem.getQty().intValue(); i++) {
                String cardCode = null;
                cardCode = cardCodeArr[i];
                OcBOrderNaiKa ocBOrderNaiKa = new OcBOrderNaiKa();
                BaseModelUtil.initialBaseModelSystemField(ocBOrderNaiKa);
                ocBOrderNaiKa.setId(sequenceUtil.buildOrderNaiKaSequenceId());
                ocBOrderNaiKa.setOcBOrderId(orderItem.getOcBOrderId());
                ocBOrderNaiKa.setOcBOrderItemId(orderItem.getId());
                ocBOrderNaiKa.setTid(orderItem.getTid());
                ocBOrderNaiKa.setBusinessType(naiKaType);
//                ocBOrderNaiKa.setBusinessTypeId(ocBOrder.getBusinessTypeId());
//                ocBOrderNaiKa.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
//                ocBOrderNaiKa.setBusinessTypeName(ocBOrder.getBusinessTypeName());
                ocBOrderNaiKa.setNaikaStatus(OmsOrderNaiKaStatusEnum.FREEZE_SUCCESS.getStatus());
                ocBOrderNaiKa.setPsCProEcode(orderItem.getPsCProEcode());
                ocBOrderNaiKa.setPsCProId(orderItem.getPsCProId());
                ocBOrderNaiKa.setPsCProEname(orderItem.getPsCProEname());
                ocBOrderNaiKa.setPsCSkuId(orderItem.getPsCSkuId());
                ocBOrderNaiKa.setPsCSkuEcode(orderItem.getPsCSkuEcode());
                ocBOrderNaiKa.setCardCode(cardCode);
                ocBOrderNaiKa.setPsCSkuEname(orderItem.getPsCSkuEname());
                ocBOrderNaiKa.setSkuSpec(orderItem.getSkuSpec());
                ocBOrderNaiKas.add(ocBOrderNaiKa);
            }
        }
        return ocBOrderNaiKas;
    }

    /**
     * 初始化orderItem内容
     *
     * @param standplatOrderItem 淘宝中间表数据
     * @param item               需要赋值的StandplatorderItem
     */
    private void initialTaobaoOrderItem(IpBStandplatOrderItemEx standplatOrderItem, OcBOrderItem item) {
        if (standplatOrderItem.getProdSku() != null) {
            item.setPsCProId(standplatOrderItem.getProdSku().getProdId());
            // ProECode
            item.setPsCProEcode(standplatOrderItem.getProdSku().getProdCode());
            item.setPsCSkuId(standplatOrderItem.getProdSku().getId());

            item.setPsCClrEcode(standplatOrderItem.getProdSku().getColorCode());
            item.setPsCClrEname(standplatOrderItem.getProdSku().getColorName());
            item.setPsCClrId(standplatOrderItem.getProdSku().getColorId());
            item.setPsCSizeEcode(standplatOrderItem.getProdSku().getSizeCode());
            item.setPsCSizeEname(standplatOrderItem.getProdSku().getSizeName());
            item.setPsCSizeId(standplatOrderItem.getProdSku().getSizeId());
            item.setPsCProMaterieltype(standplatOrderItem.getProdSku().getMaterialType());
            item.setStandardWeight(standplatOrderItem.getProdSku().getWeight());
            item.setSkuSpec(standplatOrderItem.getProdSku().getSkuSpec());
            item.setProType(NumberUtils.toLong(standplatOrderItem.getProdSku().getSkuType() + ""));
            item.setMDim4Id(standplatOrderItem.getProdSku().getMDim4Id());
            item.setMDim6Id(standplatOrderItem.getProdSku().getMDim6Id());
            if ("Y".equals(standplatOrderItem.getProdSku().getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
            String psSkuEcode = standplatOrderItem.getOuterSkuId();
            if (StringUtils.isBlank(psSkuEcode)) {
                psSkuEcode = standplatOrderItem.getOuterIid();
            }
            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            if (standplatOrderItem.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT
                    || standplatOrderItem.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT) {
                //为福袋或者组合商品
                item.setPsCSkuEcode(standplatOrderItem.getProdSku().getSkuEcode());
                //虚拟条码商品名称取中间表的名称
                item.setPsCProEname(standplatOrderItem.getTitle());
                //由于数据库做了对尺寸code和商品code做了非空限制
                item.setPsCSizeEcode(psSkuEcode);
                item.setPsCProEcode(standplatOrderItem.getProdSku().getSkuEcode());
            } else {
                item.setPsCSkuEcode(standplatOrderItem.getProdSku().getSkuEcode());
                item.setPsCProEname(standplatOrderItem.getProdSku().getName()); //商品名称
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }

    /**
     * 获取商品类型
     *
     * @param skuType 通用item
     * @return 是否是组合商品
     */
    private int combinationProductType(int skuType) {
        if (skuType == SkuType.COMBINE_PRODUCT) {
            return SkuType.COMBINE_PRODUCT;
        } else if (skuType == SkuType.GIFT_PRODUCT) {
            return SkuType.GIFT_PRODUCT;
        } else if (skuType == SkuType.PRE_SALE_PRODUCT) {
            return SkuType.PRE_SALE_PRODUCT;
        } else {
            return SkuType.NORMAL_PRODUCT;
        }
    }

    /**
     * 初始化standplatOrderItem内容
     *
     * @param standplatOrderItem 通用中间表数据
     * @param item               需要赋值的standplatorderItem
     */
    private void initialstandplatOrderItem(IpBStandplatOrderItemEx standplatOrderItem, OcBOrderItem item) {
        if (standplatOrderItem.getProdSku() != null) {
            item.setPsCProId(standplatOrderItem.getProdSku().getProdId());
            // ProECode
            item.setPsCProEname(standplatOrderItem.getProdSku().getName());
            item.setPsCProEcode(standplatOrderItem.getProdSku().getProdCode());
            item.setPsCSkuId(standplatOrderItem.getProdSku().getId());
            // 增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
            String psSkuEcode = standplatOrderItem.getOuterSkuId();
            if (StringUtils.isBlank(psSkuEcode)) {
                psSkuEcode = standplatOrderItem.getOuterIid();
            }
            if (checkIsNeedTransferSkuUpperCase()) {
                psSkuEcode = StringUtils.upperCase(psSkuEcode);
            }
            item.setPsCSizeEcode(standplatOrderItem.getProdSku().getSizeCode());
            item.setPsCSizeEname(standplatOrderItem.getProdSku().getSizeName());
            item.setPsCSizeId(standplatOrderItem.getProdSku().getSizeId());
            item.setPsCClrEcode(standplatOrderItem.getProdSku().getColorCode());
            item.setPsCClrEname(standplatOrderItem.getProdSku().getColorName());
            item.setPsCClrId(standplatOrderItem.getProdSku().getColorId());
            item.setPsCProMaterieltype(standplatOrderItem.getProdSku().getMaterialType());
            item.setPsCSkuEcode(standplatOrderItem.getProdSku().getSkuEcode());
            item.setStandardWeight(standplatOrderItem.getProdSku().getWeight());
            item.setSkuSpec(standplatOrderItem.getProdSku().getSkuSpec());
            // 供应类型 0 普通 1.代销轻供 2.寄售轻供
            item.setPsCProSupplyType(standplatOrderItem.getProdSku().getPsCProSupplyType());
            // 针对一米有品的特殊字段处理存储
            item.setReserveVarchar04(standplatOrderItem.getReserveVarchar04());

            // 增加品类信息 20220923
            item.setMDim4Id(standplatOrderItem.getProdSku().getMDim4Id());
            item.setMDim6Id(standplatOrderItem.getProdSku().getMDim6Id());
            if ("Y".equals(standplatOrderItem.getProdSku().getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }

    /**
     * 将通用中间表转换成付款信息表
     *
     * @param order          已生成的全渠道订单信息
     * @param standplatOrder 通用中间表订单信息
     * @return 付款信息表
     */
    private OcBOrderPayment buildOrderPaymentFromstandplatOrder(OcBOrder order, IpBStandplatOrder standplatOrder) {
        OcBOrderPayment orderPayment = new OcBOrderPayment();
        orderPayment.setId(sequenceUtil.buildOrderPaymentSequenceId());
        orderPayment.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderPayment.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        orderPayment.setVersion(0L);

        BaseModelUtil.initialBaseModelSystemField(orderPayment);

        orderPayment.setOcBOrderId(order.getId());


        //付款时间
        orderPayment.setPayTime(standplatOrder.getPayTime());
        //完成时间
        orderPayment.setEndTime(order.getEndTime());
        //支付金额
        orderPayment.setPaymentAmt(order.getReceivedAmt());
        //订单金额
        orderPayment.setAmtOrder(order.getOrderAmt());
        //付款方式
        orderPayment.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        //备注
        orderPayment.setRemark(null);
        //付款状态（1已付款，0未付款）
        orderPayment.setPayStatus(OmsPayStatus.PAID.toInteger());

        return orderPayment;
    }

    /**
     * 创建Order全链路日志信息
     *
     * @param orderInfo      订单信息表
     * @param standplatOrder 通用订单信息表
     * @return Order全链路日志信息
     */
    public OcBOrderLink buildOrderLink(OcBOrder orderInfo, IpBStandplatOrder standplatOrder) {
        OcBOrderLink orderLink = new OcBOrderLink();
        orderLink.setId(sequenceUtil.buildOrderSequenceId());
        orderLink.setOcBOrderId(orderInfo.getId());
        orderLink.setTid(orderInfo.getTid());
        orderLink.setBackflowStatus(BackflowStatus.QIMEN_ERP_TRANSFER.parseValue());
        orderLink.setExtAttribute(null);
        orderLink.setSellerNick(standplatOrder.getSellerNick());
        orderLink.setSyncStatus(0);
        orderLink.setErrorInfo(null);
        orderLink.setSyncTime(null);
        orderLink.setCreationdate(new Date());
        orderLink.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        orderLink.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderLink.setModifierename(SystemUserResource.ROOT_USER_NAME);
        orderLink.setModifieddate(new Date());
        orderLink.setModifierid(SystemUserResource.ROOT_USER_ID);
        orderLink.setOwnerid(SystemUserResource.ROOT_USER_ID);
        orderLink.setPlatform(PlatFormEnum.JINGDONG.getCode().toString());

        return orderLink;
    }

    /**
     * 通用订单
     *
     * @param standplatOrder 通用订单中间表关联关系
     * @param isHistoryOrder 是否为历史订单信息
     * @return 零售发货单关联对象
     */
    public OcBOrderRelation standplatOrderToOrder(IpStandplatOrderRelation standplatOrder,
                                                  boolean isHistoryOrder) {
        this.handleOrderAmt(standplatOrder);
        OcBOrderRelation orderRelation = new OcBOrderRelation();
        // 中间表优惠特殊处理 1027
        discountSpecialDealForPlatform(standplatOrder);
        OcBOrder orderInfo = this.buildOcBOrderFromIpstandplatOrder(standplatOrder, isHistoryOrder);
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        List<OcBOrderNaiKa> allOcBOrderNaiKaList = new ArrayList<>();
        Integer toNaiKaStatus = 0;
        for (IpBStandplatOrderItemEx standplatItem : standplatOrder.getStandPlatOrderItemList()) {
            OcBOrderItem item = this.buildOrderItemFromstandplatOrderItem(orderInfo, standplatItem, isHistoryOrder);
            // Sap传过来业务类型为：sap免费订单-toc。若sap有传指定效期则订单标签赋值为“手工指定”
            if (StringUtils.isNotBlank(standplatItem.getExpiryDateRange())
                    && (String.valueOf(PlatFormEnum.SAP.getCode()).equals(standplatOrder.getStandplatOrder().getCpCPlatformEcode()) ||
                    String.valueOf(PlatFormEnum.DMS.getCode()).equals(standplatOrder.getStandplatOrder().getCpCPlatformEcode()))
                    && OmsBusinessTypeUtil.PtBusinessType.ZS03.getCode().equals(standplatOrder.getStandplatOrder().getOrderType())) {
                item.setOrderLabel(ExpiryDateOrderLabelEnum.特殊订单需单独处理.getDesc());
            }

            if (ObjectUtil.isNotEmpty(standplatItem.getCardCode())) {
                // 如果是虚拟
                String naiKaType = "";
                if (standplatItem.getReserveVarchar02() != null && (standplatItem.getReserveVarchar02().equals("5") || standplatItem.getReserveVarchar02().equals("6"))) {
                    naiKaType = NaiKaTypeConstant.PICK_UP;
                }
                List<OcBOrderNaiKa> ocBOrderNaiKaList = buildOrderNaiKaFromstandplatOrderItem(orderInfo, item, standplatItem, naiKaType);
                allOcBOrderNaiKaList.addAll(ocBOrderNaiKaList);
            }
            orderItemList.add(item);
        }

        orderInfo.setToNaikaStatus(toNaiKaStatus);
        // 计算每行平摊金额、成交单价、成交金额
        setItemOrderSplitMat(orderInfo, orderItemList, standplatOrder.getStandPlatOrderItemList());

        // 如果有平台优惠时，这个订单计算有问题，故沟通后调整，针对商品金额为 0的重新计算 20221010
        // 因为奶卡、SAP、好食期 等这些渠道过来等订单 明细等price为0，在setItemOrderSplitMat中重新赋值了price，故需要更新总金额
        if (orderInfo.getProductAmt() == null || orderInfo.getProductAmt().compareTo(BigDecimal.ZERO) == 0) {
            BigDecimal amtCount = BigDecimal.ZERO;
            for (OcBOrderItem item : orderItemList) {
                BigDecimal amt = item.getPrice().multiply(item.getQty()).setScale(2, BigDecimal.ROUND_HALF_UP);
                amtCount = amtCount.add(amt);
            }
            orderInfo.setProductAmt(amtCount);
            orderInfo.setOrderAmt(orderInfo.getReceivedAmt());
        }

        //订单付款信息对象
        OcBOrderPayment orderPayment = this.buildOrderPaymentFromstandplatOrder(orderInfo, standplatOrder.getStandplatOrder());
        List<OcBOrderPayment> orderPaymentList = new ArrayList<>();
        orderPaymentList.add(orderPayment);

        OcBOrderLink orderLink = this.buildOrderLink(orderInfo, standplatOrder.getStandplatOrder());

        orderRelation.setOrderPaymentList(orderPaymentList);
        orderRelation.setOrderInfo(orderInfo);
        orderRelation.setOrderItemList(orderItemList);
        orderRelation.setOrderLink(orderLink);
        orderRelation.setOrderNaiKaList(allOcBOrderNaiKaList);
        return orderRelation;
    }

    private void handleOrderAmt(IpStandplatOrderRelation standplatOrder) {
        Long platformId = standplatOrder.getPlatformId();
        if (PlatFormEnum.DOU_YIN.getCode().equals(platformId.intValue())) {
            //下单前优惠
            List<IpBStandplatOrderItemEx> standPlatOrderItemList = standplatOrder.getStandPlatOrderItemList();
            for (IpBStandplatOrderItemEx standplatOrderItem : standPlatOrderItemList) {
                BigDecimal reserveDecimal01 = Optional.ofNullable(standplatOrderItem.getReserveDecimal01()).orElse(BigDecimal.ZERO);
                // 优惠金额
                BigDecimal discountFee = standplatOrderItem.getDiscountFee() == null ? BigDecimal.ZERO : standplatOrderItem.getDiscountFee();
                // 应付金额
                BigDecimal totalFee = standplatOrderItem.getTotalFee() == null ? BigDecimal.ZERO : standplatOrderItem.getTotalFee();
                // 实付金额
                BigDecimal payment = standplatOrderItem.getPayment() == null ? BigDecimal.ZERO : standplatOrderItem.getPayment();
                BigDecimal add = payment.add(discountFee).add(reserveDecimal01);
                // 平台优惠加上去（如果实付金额+商品优惠+平台优惠 不等于 应付金额）
                if (add.compareTo(totalFee) != 0) {
                    // 计算优惠前，如果平台优惠+实付金额已等于实付金额，直接将商品优惠设置为 0：场景一：
                    if (payment.add(reserveDecimal01).compareTo(standplatOrderItem.getTotalFee()) == 0) {
                        standplatOrderItem.setDiscountFee(BigDecimal.ZERO);
                    }else {
                        if (reserveDecimal01.compareTo(BigDecimal.ZERO) != 0) {
                            standplatOrderItem.setPrice(totalFee.add(reserveDecimal01).divide(new BigDecimal(standplatOrderItem.getNum().toString()), 4, BigDecimal.ROUND_HALF_UP));
                            standplatOrderItem.setTotalFee(totalFee.add(reserveDecimal01));
                        }
                        // 如果实付金额+平台分摊金额 = 应付金额 ，将明细优惠重置为0 2023-03-10 崔艳吉，解决抖音平台转单后金额为负数的问题
                        if (payment.add(reserveDecimal01).compareTo(standplatOrderItem.getTotalFee()) == 0) {
                            standplatOrderItem.setDiscountFee(BigDecimal.ZERO);
                        }
                    }
                }
            }
        }
    }

    /**
     * 针对配置的平台将优惠金额进行特殊处理，主要针对代销的模式，临时处理方案 1027
     *
     * @param standplatOrder 中间表单据信息
     */
    private void discountSpecialDealForPlatform(IpStandplatOrderRelation standplatOrder) {
        try {
            //  针对部分代销模式的平台，商品优惠金额和订单优惠金额特殊处理（过滤掉，将优惠金额设置为 0) 1027
            if (log.isDebugEnabled()) {
                log.debug(" StandplatOrderTransferUtil discountSpecialDealForPlatform omsSystemConfig,isOpen:{},orderPlatformIds:{},productPlatformIds:{} ",
                        omsSystemConfig.getTransferDiscountSpecialIsOpen(),
                        omsSystemConfig.getTransferDiscountSpecialOrderPlatformIds(),
                        omsSystemConfig.getTransferDiscountSpecialProductPlatformIds());
            }
            if (omsSystemConfig != null && omsSystemConfig.getTransferDiscountSpecialIsOpen()) {
                Long platform = standplatOrder.getStandplatOrder().getCpCPlatformId();
                if (platform != null) {
                    String specialOrderPlatformIds = omsSystemConfig.getTransferDiscountSpecialOrderPlatformIds();
                    String specialProductPlatformIds = omsSystemConfig.getTransferDiscountSpecialProductPlatformIds();
                    if (StringUtils.isNotBlank(specialOrderPlatformIds)) {
                        List<Long> orderDiscountPlatformList = Arrays.stream(specialOrderPlatformIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                        if (orderDiscountPlatformList.contains(platform)) {
                            // 重置中间表订单的优惠金额设置为 0
                            standplatOrder.getStandplatOrder().setDiscountFee(BigDecimal.ZERO);
                        }
                    }
                    if (StringUtils.isNotBlank(specialProductPlatformIds)) {
                        List<Long> productDiscountPlatformList = Arrays.stream(specialProductPlatformIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
                        if (productDiscountPlatformList.contains(platform)) {
                            for (IpBStandplatOrderItemEx standplatItem : standplatOrder.getStandPlatOrderItemList()) {
                                // 重置中间表的明细优惠金额为 0
                                standplatItem.setDiscountFee(BigDecimal.ZERO);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error(" StandplatOrderTransferUtil discountSpecialDealForPlatform error:" + e.getMessage(), e);
        }
    }

    /**
     * 计算和赋值订单明细整单平摊金额 并赋值单行实际成交金额
     *
     * @param orderInfo
     * @param orderItemList
     */
    private void setItemOrderSplitMat(OcBOrder orderInfo, List<OcBOrderItem> orderItemList,
                                      List<IpBStandplatOrderItemEx> standplatOrderItemList) {
        //去除最后一条明细所有明细优惠金额总和
        BigDecimal countPrice = BigDecimal.ZERO;
        //所有明细实际成交金额总和
        BigDecimal discountCountPrice = BigDecimal.ZERO;
        //主表的订单优惠金额
        BigDecimal orderDiscountAmt = orderInfo.getOrderDiscountAmt() == null ? BigDecimal.ZERO : orderInfo.getOrderDiscountAmt();
        for (int i = 0, length = orderItemList.size(); i < length; i++) {
            OcBOrderItem ocBOrderItem = orderItemList.get(i);
            //匹配对应中间表明细
            IpBStandplatOrderItemEx standplatItem = getOcBOrderItem(orderInfo, ocBOrderItem, standplatOrderItemList);
            if (log.isDebugEnabled()) {
                log.debug(" setItemOrderSplitMat ocBOrderItem:{},standplatItem:{}", JSONObject.toJSONString(ocBOrderItem), JSONObject.toJSONString(standplatItem));
            }
            if (standplatItem == null) {
                //没有匹配到对应的明细 默认赋值为0
                ocBOrderItem.setOrderSplitAmt(BigDecimal.ZERO);
            } else {
                if (length - 1 == i) {
                    //最后一条商品的【优惠平摊】= 【主表的订单优惠金额 - 其余所有优惠平摊之和】
                    //赋值明细整单平摊金额
                    ocBOrderItem.setOrderSplitAmt(orderDiscountAmt.subtract(countPrice));
                } else {
                    //【优惠平摊】 = 主表的订单优惠金额 * （成交金额 / 所有商品明细的成交金额之和）
                    //【优惠平摊】 = 主表的订单优惠金额 * （标准价*数量 / 所有标准价*数量之和）
                    if (i == 0) {
                        for (IpBStandplatOrderItemEx standItem : standplatOrderItemList) {
                            BigDecimal price = standItem.getPrice() == null ? BigDecimal.ZERO : standItem.getPrice();
                            BigDecimal num = standItem.getNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(standItem.getNum());
                            discountCountPrice = discountCountPrice.add(price.multiply(num))
                                    .subtract(Optional.ofNullable(standItem.getDiscountFee()).orElse(BigDecimal.ZERO));
                        }
                    }

                    BigDecimal price = standplatItem.getPrice() == null ? BigDecimal.ZERO : standplatItem.getPrice();
                    BigDecimal num = standplatItem.getNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(standplatItem.getNum());
                    //标准价乘以数量
                    // 如果是奶卡 则特殊处理
                    //ObjectUtil.isNotNull(cpCPlatform) && (ObjectUtil.equals("1010", cpCPlatform.getEcode()))) {
                    BigDecimal realAmt;
                    if (BigDecimal.ZERO.compareTo(price) == 0) {
                        realAmt = standplatItem.getPayment() == null ? BigDecimal.ZERO : standplatItem.getPayment();
                    } else {
                        realAmt = price.multiply(num).subtract(Optional.ofNullable(standplatItem.getDiscountFee()).orElse(BigDecimal.ZERO));
                    }
                    log.info("realAmt==>{},discountCountPrice==>{}", realAmt, discountCountPrice);
                    //计算当前明细所占总优惠金额的比例金额保留4位小数
                    BigDecimal ratio = discountCountPrice.compareTo(BigDecimal.ZERO) > 0 ? realAmt.divide(discountCountPrice, 2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;
                    //计算当前明细优惠整单平摊金额
                    BigDecimal multiply = orderDiscountAmt.multiply(ratio).setScale(2, BigDecimal.ROUND_HALF_UP);
                    //赋值明细整单平摊金额
                    ocBOrderItem.setOrderSplitAmt(multiply);

                    //计算除集中最后一条明细优惠金额之和
                    countPrice = countPrice.add(multiply);
                }
            }

            //单行实际成交金额.
            BigDecimal realAmt = this.calcOrderItemRealAmount(ocBOrderItem, orderInfo.getPlatform(), standplatItem);
            ocBOrderItem.setRealAmt(realAmt);
            ocBOrderItem.setPriceActual(realAmt.divide(ocBOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP)); // 成交单价 = 成交金额 / 数量
            BigDecimal price = ocBOrderItem.getPrice();
            price = price == null ? BigDecimal.ZERO : price;
            if (BigDecimal.ZERO.compareTo(price) == 0) {
                ocBOrderItem.setPrice(realAmt.divide(ocBOrderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP));
            }
        }
    }


    /**
     * 匹配对应中间表明细 匹配到了返回对应的明细
     *
     * @param ocBOrderItem
     * @param standplatOrderItemList
     * @return 返回对应的明细 没有匹配到返回null
     */
    private IpBStandplatOrderItemEx getOcBOrderItem(OcBOrder orderInfo, OcBOrderItem ocBOrderItem,
                                                    List<IpBStandplatOrderItemEx> standplatOrderItemList) {

        String ooid = ocBOrderItem.getOoid();
        if (StringUtils.isNotEmpty(ooid)) {
            for (IpBStandplatOrderItemEx standplatOrderItemEx : standplatOrderItemList) {
                String oid = standplatOrderItemEx.getOid();
                if (ooid.equals(oid)) {
                    return standplatOrderItemEx;
                }
            }
        }
        String numIid1 = ocBOrderItem.getNumIid();
        String skuNumiid = ocBOrderItem.getSkuNumiid();
        String psCSkuEcode = numIid1 + skuNumiid;
        for (IpBStandplatOrderItemEx standplatOrderItemEx : standplatOrderItemList) {
            String numIid = standplatOrderItemEx.getNumIid();
            String skuId = standplatOrderItemEx.getSkuId();
            String outerSkuId = "" + numIid + skuId;
            if (psCSkuEcode.equals(outerSkuId)) {
                return standplatOrderItemEx;
            }
        }
        return null;
    }

    /**
     * 单行实际成交金额. 平台售价*数量-平摊金额-商品优惠+调增金额
     *
     * @param
     * @return
     */
    private BigDecimal calcOrderItemRealAmount(OcBOrderItem orderItem, Integer platform, IpBStandplatOrderItemEx standplatItem) {
        if (orderItem.getQty() == null || BigDecimal.ZERO.compareTo(orderItem.getQty()) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal amount = BigDecimal.ZERO;
        BigDecimal price = orderItem.getPrice();
        price = price == null ? BigDecimal.ZERO : price;
        if (BigDecimal.ZERO.compareTo(price) == 0) {
            amount = standplatItem.getPayment() == null ? BigDecimal.ZERO : standplatItem.getPayment();
        } else {
            amount = Optional.ofNullable(orderItem.getPrice()).orElse(BigDecimal.ZERO).multiply(orderItem.getQty());
        }
        return amount.subtract(Optional.ofNullable(orderItem.getAmtDiscount()).orElse(BigDecimal.ZERO))  // 减去商品优惠金额
                .subtract(Optional.ofNullable(orderItem.getOrderSplitAmt()).orElse(BigDecimal.ZERO)) //减去平摊金额
                .add(Optional.ofNullable(orderItem.getAdjustAmt()).orElse(BigDecimal.ZERO)) // 加调整金额
                .setScale(4, BigDecimal.ROUND_HALF_UP);

    }


    /**
     * 获取退单所属平台
     *
     * @param returnOrder
     * @return
     */
    public Integer getReturnOrderPlatmform(OcBReturnOrder returnOrder) {
        Integer platmform;
        String reserveVarchar05 = returnOrder.getReserveVarchar05();
        if (StringUtils.isNotEmpty(reserveVarchar05)) {
            platmform = Integer.parseInt(reserveVarchar05);
        } else {
            platmform = returnOrder.getPlatform();
        }
        return platmform;
    }
}
