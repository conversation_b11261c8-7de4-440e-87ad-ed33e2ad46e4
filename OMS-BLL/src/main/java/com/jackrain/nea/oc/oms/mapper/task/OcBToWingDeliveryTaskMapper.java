package com.jackrain.nea.oc.oms.mapper.task;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.task.OcBToWingDeliveryTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

@Mapper
@Component
public interface OcBToWingDeliveryTaskMapper extends ExtentionMapper<OcBToWingDeliveryTask> {
    @Select("SELECT OUT_BILL_NO from oc_b_towing_delivery_task where status in (0,3) and OUT_BILL_NO is not null LIMIT #{limit} ORDER BY modifieddate ASC")
    List<String> queryList(@Param("limit") Integer totalCount);

    @Update("<script> "
            + "UPDATE oc_b_towing_delivery_task set  modifieddate = NOW() where OUT_BILL_NO IN "
            + "<foreach item='item' index='index' collection='outBillNos' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void batchUpdateDate(@Param("outBillNos") List<String> tids);

    @Update("<script> "
            + "UPDATE oc_b_towing_delivery_task set status =1, modifieddate = NOW() where OUT_BILL_NO IN "
            + "<foreach item='item' index='index' collection='outBillNos' open='(' separator=',' close=')'> #{item} </foreach>"
            + "</script>")
    void batchUpdateStatus(@Param("outBillNos") List<String> tids);

    @Update("UPDATE oc_b_towing_delivery_task set status =#{status}, modifieddate = NOW(), remake =#{failMsg} where OUT_BILL_NO = #{outBillNo}")
    void updateByOutBillNo(@Param("outBillNo") String outBillNo, @Param("failMsg") String failMsg, @Param("status") Integer status);

    @Update("UPDATE oc_b_towing_delivery_task set OUT_BILL_NO =#{noticesBillNo}, modifieddate = NOW() where tid = #{sourceCode}")
    void updateByTid(@Param("sourceCode") String sourceCode,@Param("noticesBillNo") String noticesBillNo);

    @Select("<script> " +
            "SELECT * FROM oc_b_towing_delivery_task WHERE OUT_BILL_NO in "
            + "<foreach item='item' index='index' collection='outBillNoList' open='(' separator=',' close=')'>"
            + " #{item}"
            + " </foreach> "
            + "</script>")
    List<OcBToWingDeliveryTask> selectListByoutBill(@Param("outBillNoList") List<String> outBillNoList);
}
