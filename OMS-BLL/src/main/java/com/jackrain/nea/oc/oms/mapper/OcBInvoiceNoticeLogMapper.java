package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBInvoiceNoticeLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface OcBInvoiceNoticeLogMapper extends ExtentionMapper<OcBInvoiceNoticeLog> {

    @Select("select * from oc_b_invoice_notice_log where oc_b_invoice_notice_id = #{mainid}")
    List<OcBInvoiceNoticeLog> listByMainid(@Param("mainid") Long mainid);
}