package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBRefundInActualItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/7/18
 */
@Mapper
public interface OcBRefundInActualItemMapper extends ExtentionMapper<OcBRefundInActualItem> {

    /**
     * 入库单匹配
     *
     * @param refundId 入库单id
     * @return 入库单明细
     */
    @Select("SELECT * FROM OC_B_REFUND_IN_ACTUAL_ITEM WHERE OC_B_REFUND_IN_ID=#{refundId} AND ISACTIVE='Y'")
    List<OcBRefundInActualItem> selectActualInItemsByRefundInId(@Param("refundId") Long refundId);

}
