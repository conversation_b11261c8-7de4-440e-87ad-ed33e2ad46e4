package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/10 10:41 下午
 * @description
 * @since version -1.0
 */

@Slf4j
public class ES4IpTaoBaoFxOrder {

    /**
     * 从ES中查询未转换成功的单据信息
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * TRANS 转换状态
     * FENXIAOID 单据信息
     * @return 单据编号列表
     */
    public static List<String> selectUnTransferredOrderFromEs(int pageIndex, int pageSize) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("ISTRANS", "0");

            String[] returnFieldNames = new String[]{"FENXIAO_ID"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_TAOBAO_FX_ORDER_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_TAOBAO_FX_ORDER_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("FENXIAO_ID");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            log.error(LogUtil.format("selectUnTransferredOrderFromEs.异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return orderNoList;
    }
}
