package com.jackrain.nea.oc.oms.services.audit;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsAuditFailedReason;
import com.jackrain.nea.oc.oms.model.enums.OmsMethod;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.request.OrderICheckRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaoBaoOrderStatus;
import com.jackrain.nea.oc.oms.nums.OcBOrderNodeEnum;
import com.jackrain.nea.oc.oms.services.OcBOrderNodeRecordService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.advance.OmsOrderAdvanceParseService;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单审核直调
 *
 * @date 2019/3/8
 * @author: ming.fz
 */
@Slf4j
@Component
public class OmsOrderAuditService {

    @Autowired
    private OmsOrderManualAuditService omsOrderManualAuditService;

    @Autowired
    private OmsOrderAdvanceParseService orderAdvanceParseService;

    @Autowired
    private OmsOrderService omsOrderService;

    @Autowired
    AuditStrategyHandlerFactory auditStrategyHandlerFactory;

    @Autowired
    private RedisOpsUtil<String, String> redisUtil;

    @Autowired
    private BllRedisLockOrderUtil redisLockOrderUtil;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderNodeRecordService ocBOrderNodeRecordService;


    public ValueHolderV14 orderCheck(OrderICheckRequest param, User user) throws NDSException {
        //        String usedNewAuditMethod = redisUtil.strRedisTemplate.opsForValue().get("business_system:used_new_audit_method");
        //        //暂时关闭 单独开关
        //        if (StringUtils.isBlank(usedNewAuditMethod) || "是".equals(usedNewAuditMethod)) {
        //
        //
        //        }
        //
        //        return omsOrderManualAuditService.doManualAuditOrder(param, user);
        OmsOrderAuditService bean = ApplicationContextHandle.getBean(OmsOrderAuditService.class);
        return bean.auditOrder(param, user);
    }


    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 auditOrder(OrderICheckRequest auditOrderRequest, User user) {

        ValueHolderV14 resultValueHolderV14 = new ValueHolderV14<>();

        try {
            boolean hasError = false;
            int failedNumber = 0;
            int successNumber = 0;
            List<String> errors = new ArrayList<>();
            for (Long orderId : auditOrderRequest.getIds()) {
                try {
                    OcBOrderRelation orderRelation = omsOrderService.selectOmsOrderInfoOccupy(orderId);
                    if (orderRelation == null) {
                        hasError = false;
                        failedNumber++;
                        continue;
                    }
                    //未退款的只要protype(0,4)
                    orderRelation.setNoRefundOrderItems(omsOrderService.getUnSuccessRefundAudit(orderRelation.getOrderItemList()));
                    orderRelation.setOmsMethod(OmsMethod.Manual);

                    /**是否强制审核*/
                    orderRelation.setMandatoryAudit(auditOrderRequest.getMandatoryAudit());

                    boolean auditResult = auditStrategyHandlerFactory.doHandle(orderRelation, user);
                    if (auditResult) {
                        //审核时间埋点  类型手动
                        OcBOrder order = new OcBOrder();
                        order.setId(orderRelation.getOrderInfo().getId());
                        order.setAuditType("manual");
                        order.setAuditSuccessDate(new Date());
                        //手动审核新增审核人信息
                        order.setAuditId(user.getId().longValue());
                        order.setAuditName(user.getEname());
                        ocBOrderMapper.updateById(order);
                        ocBOrderNodeRecordService.insertByNode(OcBOrderNodeEnum.AUDIT_SUCCESS_DATE,new Date(),order.getId(),user);
                        if (omsOrderManualAuditService.updateChecked(orderRelation.getOrderInfo(), user,auditOrderRequest.getMandatoryAudit())) {
                            boolean b = orderAdvanceParseService.checkIsDepositPreSale(orderRelation.getOrderInfo());
                            if (b){
                                String reserveVarchar03 = orderRelation.getOrderInfo().getStatusPayStep();
                                if (TaoBaoOrderStatus.FRONT_PAID_FINAL_NOPAID.equals(reserveVarchar03) && TaoBaoOrderStatus.SUGGEST_PRESINK_STATUS_Y.equals(orderRelation.getOrderInfo().getSuggestPresinkStatus())) {
                                    OcBOrder updateOcBOrder = new OcBOrder();
                                    updateOcBOrder.setId(orderRelation.getOrderInfo().getId());
                                    updateOcBOrder.setActualPresinkStatus(TaoBaoOrderStatus.ACTUAL_PRESINK_STATUS_NOTIFIED);
                                    ocBOrderMapper.updateById(updateOcBOrder);
                                }
                            }
                            successNumber++;
                        }
                    } else {
                        hasError = true;
                        failedNumber++;
                        OmsAuditFailedReason omsAuditFailedReason = orderRelation.getOmsAuditFailedReason();
                        omsAuditFailedReason = omsAuditFailedReason == null ? OmsAuditFailedReason.ERROR_99 : omsAuditFailedReason;
                        errors.add("OrderId=" + orderRelation.getOrderId() + "," + omsAuditFailedReason.getKey());
                    }

                } catch (Exception ex) {
                    hasError = true;
                    failedNumber++;
                    errors.add(String.format("%s", ex.getMessage()));
                }
            }

            if (hasError) {
                resultValueHolderV14.setCode(ResultCode.FAIL);
                StringBuilder sbMessage = new StringBuilder();
                sbMessage.append(String.format("审单成功%s条；审单失败%s条；失败原因：\r\n", successNumber, failedNumber));
                for (String errorMsg : errors) {
                    if (errorMsg != null) {
                        sbMessage.append(errorMsg);
                        sbMessage.append("\r\n");
                    }
                }

                resultValueHolderV14.setMessage(sbMessage.toString());
            } else {
                resultValueHolderV14.setCode(ResultCode.SUCCESS);
                resultValueHolderV14.setMessage("审单全部成功。");
            }
        } catch (Exception e) {
            resultValueHolderV14.setCode(ResultCode.FAIL);
            resultValueHolderV14.setMessage("审单异常：" + e.getMessage());
        }

        return resultValueHolderV14;
    }

    /**
     * 判断订单是否为 一件代发店铺渠道订单，是 则不允许拆单
     * @param orderId
     * @param shopId
     * @return
     */
    public ValueHolderV14 checkIssuingOrder(Long orderId,Long shopId) {
        boolean isCheck = omsOrderService.checkOrderIssuing(orderId,shopId);
        if(isCheck){
            return ValueHolderV14Utils.getFailValueHolder("一件代发店铺渠道订单,不允许拆单！");
        }else {
            return ValueHolderV14Utils.getSuccessValueHolder("校验通过！");
        }
    }

    /**
     * 强制审核
     * @param orderCheckRequest
     * @param user
     * @return
     * @throws NDSException
     */
    public ValueHolderV14 mandatoryAuditOrder(OrderICheckRequest orderCheckRequest, User user) throws NDSException {
        OmsOrderAuditService bean = ApplicationContextHandle.getBean(OmsOrderAuditService.class);
        orderCheckRequest.setMandatoryAudit(true);
        return bean.auditOrder(orderCheckRequest, user);
    }
}