package com.jackrain.nea.oc.oms.mapper.task;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * @ClassName CardAutoVoidTaskSql
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/3/2 16:45
 * @Version 1.0
 */
@Slf4j
public class CardAutoVoidTaskSql {

    public String selectCardAutoVoid(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 100;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        sql.append("select * from ")
                .append(taskTableName)
                .append("  where card_auto_void_mark=")
                .append(1)
                .append(" and card_auto_void not in (2,3,4) and t_return_status = 'SUCCESS' and void_mark = 0 ")
                .append(" order by id asc")
                .append(limitStr);
        return sql.toString();
    }

    public String select4Void(Map<String, Object> para) {
        StringBuffer sql = new StringBuffer();
        StringBuffer limitStr = new StringBuffer(" LIMIT ");
        int limit = para.get("limit") != null ? (int) para.get("limit") : 3000;
        limitStr.append(limit);

        String taskTableName = (String) para.get("taskTableName");
        int voidTimes = 6;
        sql.append("select * from ")
                .append(taskTableName)
                .append("  where void_status in (0,2)")
                .append(" and void_times < ")
                .append(voidTimes)
                .append(" order by creationdate desc")
                .append(limitStr);
        return sql.toString();
    }
}
