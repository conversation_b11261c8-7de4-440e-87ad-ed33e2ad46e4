package com.jackrain.nea.oc.oms.matcher.parser;

import com.jackrain.nea.oc.oms.matcher.live.LiveFlagEnum;
import com.jackrain.nea.oc.oms.matcher.live.LiveMatchManager;
import com.jackrain.nea.oc.oms.matcher.vo.ParamOutputVO;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * Description： AbstractLiveMatchInfoParser
 * Author: RESET
 * Date: Created in 2020/7/9 13:06
 * Modified By:
 */
public abstract class AbstractLiveMatchInfoParser<T> implements ILiveMatchInfoParser<T> {

    /**
     * 校验
     *
     * @param channelOrigOrder
     * @param order
     * @param items
     * @return
     */
    protected boolean check(T channelOrigOrder, OcBOrder order, List<OcBOrderItem> items) {
        return Objects.nonNull(channelOrigOrder)
                && Objects.nonNull(order)
                && CollectionUtils.isNotEmpty(items);
    }

    /**
     * 复制
     *
     * @param item
     * @param outputVO
     */
    protected void setResult(OcBOrderItem item, ParamOutputVO outputVO) {
        item.setLivePlatform(outputVO.getLivePlatform());
        item.setAnchorName(outputVO.getAnchorName());
        item.setAnchorId(outputVO.getAnchorId());
        item.setLiveFlag(LiveFlagEnum.Y.getValue());
        //直播主体，配合主体，直播场次赋值
        item.setAcFManageId(outputVO.getAcFManageId());
        item.setAcFManageEcode(outputVO.getAcFManageEcode());
        item.setAcFManageEname(outputVO.getAcFManageEname());
        item.setCooperateId(outputVO.getCooperateId());
        item.setCooperateEcode(outputVO.getAcFManageEcode());
        item.setCooperateEname(outputVO.getCooperateEname());
        item.setLiveEvents(outputVO.getLiveEvents());
    }

    /**
     * 赋值主单
     *
     * @param ocBOrder
     * @param items
     */
    protected void setOrderByItem(OcBOrder ocBOrder, List<OcBOrderItem> items) {
        LiveMatchManager.getInstance().cleanUp(ocBOrder, items);
    }

}
