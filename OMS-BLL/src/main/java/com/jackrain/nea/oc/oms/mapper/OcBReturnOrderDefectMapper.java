package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderDefect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface OcBReturnOrderDefectMapper extends ExtentionMapper<OcBReturnOrderDefect> {

    @Select("select count(1) from oc_b_return_order_defect where oc_b_return_order_id =#{orderRefuntId} and ps_c_sku_id=#{skuId} ")
    int selectRecord(@Param("orderRefuntId") Long orderRefuntId, @Param("skuId") Long skuId);
}