package com.jackrain.nea.oc.oms.tag;

import com.jackrain.nea.oc.oms.tag.vo.TaggerRelation;

/**
 * Description： 订单打标签标准接口
 * Author: RESET
 * Date: Created in 2020/7/8 22:00
 * Modified By:
 */
public interface ITagger {

    /**
     * 打标逻辑
     *
     * @param relation
     */
    public void doTag(TaggerRelation relation);

    /**
     * 打标开关
     *
     * @return
     */
    public boolean shouldTag();

    /**
     * 打标顺序
     *
     * @return
     */
    public int taggerOrder();

    /**
     * 打标类型
     *
     * @return
     */
    public int taggerType();

}
