package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.IpOrderLockStatusEnum;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/10 3:46 下午
 */
@Slf4j
public class ES4IpOrderLock {

    /**
     * 根据单据状态获取id
     *
     * @param pageIndex
     * @param pageSize  BILL_STATUS 单据状态 0-待锁单  1-已锁单 2-锁单失败 3-已解锁 4-部分锁单 5-部分锁单失败  6-已作废',
     * @return java.util.List<java.lang.String>
     * @Description 获取淘宝中间表分库键
     */
    public static List<Long> selectLockKeyBy(int pageIndex, int pageSize) {
        List<Long> list = new ArrayList<>();
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONObject whereKeys = new JSONObject();
        String[] returnFileds = {"ID"};

        JSONArray ja = new JSONArray();
        ja.add(IpOrderLockStatusEnum.WAIT_LOCK.getKey());
        ja.add(IpOrderLockStatusEnum.PART_LOCKED.getKey());
        whereKeys.put("BILL_STATUS", ja);//单据状态

        JSONArray orderKeys = new JSONArray();
        JSONObject orderKey = new JSONObject();
        // 按照倒序进行查询
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        orderKeys.add(orderKey);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME, OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME, whereKeys, null,
                orderKeys, pageSize, startIndex, returnFileds);
        if (search != null && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getLong("ID"));
            }
        }
        return list;
    }

    /**
     * 根据单据状态是否自动解锁查询id
     *
     * @return java.util.List<java.lang.String>
     * @Description 查询ES分库键
     * @Param [pageIndex, pageSize]
     **/
    public static List<Long> selectOrderLockEsForUnlock(int pageIndex, int pageSize) {
        List<Long> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            JSONArray ja = new JSONArray();
            ja.add(IpOrderLockStatusEnum.LOCKED.getKey());
            ja.add(IpOrderLockStatusEnum.PART_LOCK_FAIL.getKey());
            // 单据状态
            whereKeys.put("BILL_STATUS", ja);
            // 是否自动解锁
            whereKeys.put("IS_AUTO_UNLOCK", 1);

            JSONObject filterKeys = new JSONObject();
            Long endTime = System.currentTimeMillis();
            filterKeys.put("EXCEPT_UNLOCK_TIME", "~" + endTime);

            String[] returnFieldNames = new String[]{"ID"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "EXCEPT_UNLOCK_TIME");//预计解锁时间
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
                    OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
                    whereKeys, filterKeys, orderKeys,
                    pageSize, startIndex, returnFieldNames);
            // 优化代码报错问题：目前锁单没有用到，这个查询返回的是 null 1121
            if (search != null && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");
                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    Long id = jsonObject.getLong("ID");
                    orderNoList.add(id);
                }
            }

        } catch (Exception ex) {
            log.error(LogUtil.format("查询自动解锁的订单失败.异常: {}"), Throwables.getStackTraceAsString(ex));
        }
        return orderNoList;
    }

    /**
     * 根据是否能够自动解锁和是否可用查询id
     *
     * @param pageIndex 页码
     * @param pageSize  页面大小
     * @return
     * @Description 获取退货入库ES
     * <AUTHOR>
     * @date 2019-09-25 2019-09-25
     */
    public static List<Long> selectLockOrderIdListFromES(int pageIndex, int pageSize) {
        List<Long> idList = Lists.newArrayList();
        String[] filed = {"ID"};
        //1.查询 所有状态为：等待退货入库状态 并且 是原退的退货单
        JSONObject whereKeys = new JSONObject();
        //退货入库:等待退货入库
        whereKeys.put("IS_AUTO_UNLOCK", 0);
        whereKeys.put("ISACTIVE", "Y");

        JSONObject orderKey = new JSONObject();
        //CREATIONDATE降序
        orderKey.put("asc", false);
        orderKey.put("name", "CREATIONDATE");
        JSONArray order = new JSONArray();
        order.add(orderKey);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
                OcElasticSearchIndexResources.IP_B_ORDER_LOCK_TYPE_NAME,
                whereKeys, null, order,
                pageSize, pageIndex, filed);
        if (search != null) {
            JSONArray data = search.getJSONArray("data");
            for (int i = 0; i < data.size(); i++) {
                String id = data.getJSONObject(i).getString("ID");
                if (StringUtils.isNotEmpty(id)) {
                    idList.add(Long.valueOf(id));
                }
            }
        }
        return idList;
    }


}
