package com.jackrain.nea.oc.oms.nums;


import lombok.Getter;

/**
 * <AUTHOR> wang<PERSON>yu
 * @since : 2022/9/19
 * description : 订单动作定义枚举
 */
public enum OcOrderActionEnum {

    NEW_RETURN("New refund receipt", "新增退单"),
    NEW_RETURN_BATCH("batchReturnOrder", "批量退单"),
    REFUND_PRICE("refund_price","额外退款"),
    UN_KNOW("", "未知");

    OcOrderActionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    @Getter
    private String code;

    @Getter
    private String name;

    public static OcOrderActionEnum getOrderActionEnumByCode(String code){
        for (OcOrderActionEnum value : OcOrderActionEnum.values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        return UN_KNOW;
    }
}


