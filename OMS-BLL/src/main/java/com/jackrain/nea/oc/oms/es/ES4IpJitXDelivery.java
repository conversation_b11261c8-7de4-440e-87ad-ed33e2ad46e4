package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.SyncStatus;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/11 2:57 下午
 */
public class ES4IpJitXDelivery {

    private ES4IpJitXDelivery() {
    }

    /**
     * 根据唯品会订单号从ES中查询未转换成功的单据信息
     *
     * @param pageIndex 页码
     * @param pageSize  每页大小
     * @return List orderNo
     */
    public static List<String> findOrderSnBySynStatus(int pageIndex, int pageSize, boolean isFailed) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            if (isFailed) {
                whereKeys.put("SYNSTATUS", SyncStatus.EXCEPTION.toInteger());
            } else {
                whereKeys.put("SYNSTATUS", SyncStatus.UNSYNC.toInteger());
            }
            // task-28351
            JSONObject filterKeys = new JSONObject();
            Long endTime = DateUtils.addMinutes(new Date(), -15).getTime();
            filterKeys.put("UPDATE_TIME", "~" + endTime);

            String[] returnFieldNames = new String[]{"ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }
            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_TYPE_NAME,
                    whereKeys, filterKeys, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");
                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return orderNoList;
    }

    /**
     * 业务：唯品会寻仓反馈补偿任务
     * <p>
     * 根据同步状态和订单状态查询唯品会订单
     *
     * @param pageIndex  页码
     * @param pageSize   每页大小
     * @param status     订单状态
     * @return List orderNo
     */
    public static List<String> findOrderSnBySynStatusOrStatus(int pageIndex, int pageSize, String status) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            JSONArray syncStatusArray = new JSONArray();
            //未处理
            syncStatusArray.add(SyncStatus.UNSYNC.toInteger());
            //反馈异常
            syncStatusArray.add(SyncStatus.EXCEPTION.toInteger());
            whereKeys.put("SYNSTATUS", syncStatusArray);
            if (StringUtils.isNotBlank(status)) {
                whereKeys.put("STATUS", status);
            }
            String[] returnFieldNames = new String[]{"ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_TYPE_NAME,
                    whereKeys, null, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return orderNoList;
    }

    /**
     * 根据FeedbackState 查询寻仓单号
     * @param pageIndex
     * @param pageSize
     * @param feedbackStateArray
     * @param isStoreDelivery
     * @return
     */
    public static List<String> findOrderSnByFeedbackState(int pageIndex, int pageSize, JSONArray feedbackStateArray,int isStoreDelivery,int datys) {
        List<String> orderNoList = new ArrayList<>();
        try {
            JSONObject whereKeys = new JSONObject();
            whereKeys.put("FEEDBACK_STATE", feedbackStateArray);
            whereKeys.put("IS_STORE_DELIVERY",isStoreDelivery);
            JSONObject filterKeys = new JSONObject();
            Long beginTime = DateUtils.addDays(new Date(), -datys).getTime();
            filterKeys.put("CREATIONDATE",beginTime + "~" + System.currentTimeMillis());

            String[] returnFieldNames = new String[]{"ORDER_SN"};
            JSONArray orderKeys = new JSONArray();
            JSONObject orderKey = new JSONObject();
            // 按照倒序进行查询
            orderKey.put("asc", false);
            orderKey.put("name", "CREATIONDATE");
            orderKeys.add(orderKey);
            int startIndex = pageIndex * pageSize;
            if (startIndex < 0) {
                startIndex = 0;
            }

            JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_INDEX_NAME,
                    OcElasticSearchIndexResources.IP_B_JITX_DELIVERY_TYPE_NAME,
                    whereKeys, filterKeys, orderKeys,
                    pageSize, startIndex, returnFieldNames);

            if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
                JSONArray arrayObj = search.getJSONArray("data");

                for (Object obj : arrayObj) {
                    JSONObject jsonObject = (JSONObject) obj;
                    String orderNo = jsonObject.getString("ORDER_SN");
                    orderNoList.add(orderNo);
                }
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return orderNoList;
    }
}
