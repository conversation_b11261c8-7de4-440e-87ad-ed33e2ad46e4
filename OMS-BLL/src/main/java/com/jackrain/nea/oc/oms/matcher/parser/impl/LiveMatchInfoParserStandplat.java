package com.jackrain.nea.oc.oms.matcher.parser.impl;

import com.jackrain.nea.cpext.model.table.CpCAnchorArchives;
import com.jackrain.nea.oc.oms.matcher.live.LiveFlagEnum;
import com.jackrain.nea.oc.oms.matcher.live.LiveMatchManager;
import com.jackrain.nea.oc.oms.matcher.live.LivePlatformEnum;
import com.jackrain.nea.oc.oms.matcher.parser.AbstractLiveMatchInfoParser;
import com.jackrain.nea.oc.oms.matcher.vo.OcMatcherInfoVO;
import com.jackrain.nea.oc.oms.matcher.vo.ParamInputVO;
import com.jackrain.nea.oc.oms.matcher.vo.ParamOutputVO;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.IpStandplatOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrderItemEx;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.rpc.CpRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/06/15
 * 京东渠道单据信息提取
 */
@Component
@Slf4j
public class LiveMatchInfoParserStandplat extends AbstractLiveMatchInfoParser<IpStandplatOrderRelation> {

    // 通用平台OID可能是空值，空值时匹配所有明细
    private final String STANDPLAT_DEFAULT_OID = "-999999";

    @Autowired
    private CpRpcService cpRpcService;

    @Override
    protected boolean check(IpStandplatOrderRelation channelOrigOrder, OcBOrder order, List<OcBOrderItem> items) {
        return super.check(channelOrigOrder, order, items)
                && Objects.nonNull(channelOrigOrder.getStandplatOrder());
    }

    /**
     * 明细匹配
     *
     * @param matcherInfoVO
     * @param item
     * @param stOrder
     * @param originalItem
     */
    private void matchItems(OcMatcherInfoVO<IpStandplatOrderRelation> matcherInfoVO, OcBOrderItem item, IpBStandplatOrder stOrder,
                            IpBStandplatOrderItemEx originalItem, OcBOrder order) {
        ParamInputVO inputVO = new ParamInputVO();
        inputVO.setOriginalRemark(stOrder.getBuyerMessage());

        if (Objects.nonNull(originalItem)) {
            inputVO.setOriginalId(String.valueOf(originalItem.getNumIid()));
            inputVO.setOriginalTitle(originalItem.getTitle());
            ProductSku productSku = originalItem.getProdSku();
            if (productSku != null) {
                //赋值spu
                inputVO.setOriginalSpu(productSku.getProdCode());
            }
            if (Objects.equals(PlatFormEnum.SHIPH.getCode().longValue(),
                    stOrder.getCpCPlatformId())) {
                setAnchorInfo(item, originalItem, PlatFormEnum.SHIPH.getCode().toString(), order);
                return;
            }
            // 根据平台给出的主播信息去赋值,没有的话,在走下面的策略匹配 todo 是否只考虑抖音?
            if (originalItem.getReserveDecimal02() != null && StringUtils.isNotBlank(originalItem.getReserveVarchar03())) {
                this.setAnchorInfo(item, originalItem, stOrder);
                return;
            }
        }
        // 每次覆盖这个对象
        matcherInfoVO.setInputVO(inputVO);
        ParamOutputVO outputVO = LiveMatchManager.getInstance().doMatch(matcherInfoVO);

        // 赋值
        if (Objects.nonNull(outputVO) && outputVO.isResult()) {
            setResult(item, outputVO);
        }
    }

    /**
     * 赋值主播信息
     *
     * @param item         订单明细
     * @param originalItem 通用订单明细
     */
    private void setAnchorInfo(OcBOrderItem item, IpBStandplatOrderItemEx originalItem, IpBStandplatOrder matcherInfoVO) {
        CpCAnchorArchives anchorArchives = cpRpcService.queryCpCAnchorArchivesByName(originalItem.getReserveVarchar03());

        if (anchorArchives.getId() != null) {
            item.setAnchorName(anchorArchives.getAnchorNickName());
            item.setAnchorId(String.valueOf(anchorArchives.getAnchorId()));
        } else {
            item.setAnchorName(originalItem.getReserveVarchar03());
            item.setAnchorId(String.valueOf(originalItem.getReserveDecimal02()));
        }
        item.setAcFManageId(NumberUtils.LONG_ZERO);
        item.setLiveFlag(LiveFlagEnum.Y.getValue());
        item.setLivePlatform(String.valueOf(matcherInfoVO.getCpCPlatformId()));
    }

    private void setAnchorInfo(OcBOrderItem item, IpBStandplatOrderItemEx originalItem, String platform, OcBOrder order) {
        item.setAnchorName(originalItem.getReserveVarchar09());
        item.setAnchorId(originalItem.getReserveVarchar03());
        item.setAcFManageId(NumberUtils.LONG_ZERO);
        item.setLiveFlag(LiveFlagEnum.Y.getValue());
        item.setLivePlatform(platform);
        order.setAnchorName(originalItem.getReserveVarchar09());
        order.setAnchorId(originalItem.getReserveVarchar03());
    }

    /**
     * 渠道原单解析，得出规则匹配锁需要的信息
     *
     * @param channelOrigOrder
     * @return
     */
    @Override
    public void doParser(IpStandplatOrderRelation channelOrigOrder, OcBOrder order, List<OcBOrderItem> items) {
        if (check(channelOrigOrder, order, items)) {
            // 优先级-2 通用策略识别
            doParser4Normal(channelOrigOrder, order, items);
        }

        // 设置主单
        setOrderByItem(order, items);
    }

    /**
     * 通用匹配：按策略
     *
     * @param channelOrigOrder
     * @param order
     * @param items
     */
    private void doParser4Normal(IpStandplatOrderRelation channelOrigOrder, OcBOrder order, List<OcBOrderItem> items) {
        IpBStandplatOrder stOrder = channelOrigOrder.getStandplatOrder();
        List<IpBStandplatOrderItemEx> stOrderItems = channelOrigOrder.getStandPlatOrderItemList();

        if (stOrderItems == null) {
            stOrderItems = new ArrayList<>();
        }

        // to map
        // @20200713 加oid为空的条件过滤
        Map<String, List<IpBStandplatOrderItemEx>> stItemMap = stOrderItems.stream().filter(i -> StringUtils.isNotEmpty(i.getOid())).collect(Collectors.groupingBy(IpBStandplatOrderItem::getOid));

        OcMatcherInfoVO<IpStandplatOrderRelation> matcherInfoVO = new OcMatcherInfoVO<>();
        // 原单对象
        matcherInfoVO.setOriginalOrderRelation(channelOrigOrder);
        // 店铺ID
        matcherInfoVO.setCpCShopId(stOrder.getCpCShopId());
        // 下单时间：交易创建时间
        matcherInfoVO.setOrderDate(stOrder.getTradeCreateTime());
        // 支付时间
        matcherInfoVO.setPayTime(stOrder.getPayTime());

        // 循环
        items.forEach(item -> {
            // 取原单明细
            List<IpBStandplatOrderItemEx> oItems = stItemMap.get(item.getOoid());
            IpBStandplatOrderItemEx oItem = null;

            if (CollectionUtils.isNotEmpty(oItems)) {
                oItem = oItems.get(0);
            }

            matchItems(matcherInfoVO, item, stOrder, oItem, order);
        });
    }

    /**
     * 抖音识别
     * 是否按抖音识别：判断预留字段是否为空
     * reserve_bigint05  预留字段15（主播ID）
     * reserve_varchar05 预留字段25（主播账号）
     *
     * @param channelOrigOrder
     * @param items
     */
    private void doParser4DouYin(IpStandplatOrderRelation channelOrigOrder, List<OcBOrderItem> items) {
        // 按明细
        if (CollectionUtils.isNotEmpty(channelOrigOrder.getStandPlatOrderItemList())) {
            List<IpBStandplatOrderItemEx> standplatOrderItemExList = channelOrigOrder.getStandPlatOrderItemList();

            // 过滤主播ID为空记录
            standplatOrderItemExList = standplatOrderItemExList.stream().filter(f -> Objects.nonNull(f.getReserveDecimal02())).collect(Collectors.toList());
            // 赋默认值
            standplatOrderItemExList.forEach(si -> {
                if (Objects.isNull(si.getOid())) {
                    si.setOid(STANDPLAT_DEFAULT_OID);
                }
            });

            if (CollectionUtils.isNotEmpty(standplatOrderItemExList) && CollectionUtils.isNotEmpty(items)) {
                // 归组，过滤主播ID为空的记录
                Map<String, List<IpBStandplatOrderItemEx>> standPlatItemMap = standplatOrderItemExList.stream()
                        .collect(Collectors.groupingBy(IpBStandplatOrderItemEx::getOid));

                // 遍历items
                items.forEach(i -> {
                    // 如果为空，则取默认，这样就能和原单匹配上，匹配多个，随机取一个
                    String oid = Objects.isNull(i.getOoid()) ? STANDPLAT_DEFAULT_OID : i.getOoid();
                    List<IpBStandplatOrderItemEx> standplatOrderItemExes = standPlatItemMap.get(oid);

                    if (CollectionUtils.isNotEmpty(standplatOrderItemExes)) {
                        copyLiveInfo2Item(standplatOrderItemExes.get(0), i);
                    }
                });
            }
        }
    }

    /**
     * 赋值直播属性
     *
     * @param standplatOrderItemEx
     * @param item
     */
    private void copyLiveInfo2Item(IpBStandplatOrderItemEx standplatOrderItemEx, OcBOrderItem item) {
        if (Objects.nonNull(standplatOrderItemEx) && Objects.nonNull(item)) {
            // 只有在直播ID不为空的情况下，才赋值，否则不处理
            // if (Objects.nonNull(standplatOrderItemEx.getReserveBigint05())) {
            if (Objects.nonNull(standplatOrderItemEx.getReserveDecimal02())) {
                // 直播字段
                // 主播ID：reserve_bigint05  -> reserve_decimal02
                String anchorId = String.valueOf(standplatOrderItemEx.getReserveDecimal02());
                // 主播昵称：reserve_varchar05/主播账号  ->  reserve_varchar03
                String anchorName = standplatOrderItemEx.getReserveVarchar03();
                // 直播平台：抖音-1
                item.setLiveFlag(LiveFlagEnum.Y.getValue());
                item.setLivePlatform(LivePlatformEnum.DOUYIN.getValue());
                item.setAnchorId(anchorId);
                item.setAnchorName(anchorName);
            }
        }
    }

    /**
     * 渠道类型
     *
     * @return
     */
    @Override
    public ChannelType getCurrentChannelType() {
        return ChannelType.STANDPLAT;
    }

}
