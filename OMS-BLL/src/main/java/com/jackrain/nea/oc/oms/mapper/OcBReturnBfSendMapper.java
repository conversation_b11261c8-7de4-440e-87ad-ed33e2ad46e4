package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBReturnBfSend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface OcBReturnBfSendMapper extends ExtentionMapper<OcBReturnBfSend> {


    @Select("SELECT * FROM oc_b_return_bf_send WHERE t_return_id = #{refundId}")
    OcBReturnBfSend selectOcBReturnBfSendByRefundId(@Param("refundId") String refundId);

    @Select("SELECT * FROM oc_b_return_bf_send WHERE t_return_id = #{refundId}")
    List<OcBReturnBfSend> selectOcBReturnBfSendsByRefundId(@Param("refundId") String refundId);

    @Update("UPDATE oc_b_return_bf_send SET t_return_status = #{refundStatus} WHERE t_return_id = #{refundId}")
    int updateOcBReturnBfSend(@Param("refundStatus") String refundStatus, @Param("refundId") String refundId);

    @Select("<script>" +
            "select * from oc_b_return_bf_send where isactive = 'Y' and tid in " +
            "<foreach item='item' index='index' collection='tids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<OcBReturnBfSend> selectInTid(@Param("tids") List<String> tidList);
}