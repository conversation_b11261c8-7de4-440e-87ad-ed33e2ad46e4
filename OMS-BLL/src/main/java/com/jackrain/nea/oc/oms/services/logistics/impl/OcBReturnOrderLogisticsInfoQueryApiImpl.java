package com.jackrain.nea.oc.oms.services.logistics.impl;

import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.LogisticsTableEnum;
import com.jackrain.nea.oc.oms.model.request.LogisticsInfoQueryRequest;
import com.jackrain.nea.oc.oms.model.result.LogisticsInfoQueryResult;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.logistics.AbstractLogisticsInfoQueryApi;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/6/16
 * description :
 */
@Slf4j
@Component
public class OcBReturnOrderLogisticsInfoQueryApiImpl extends AbstractLogisticsInfoQueryApi {

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    @Override
    protected ValueHolderV14<LogisticsInfoQueryResult> invoke(LogisticsInfoQueryRequest request) {
        ValueHolderV14<LogisticsInfoQueryResult> v14 = new ValueHolderV14<>(ResultCode.SUCCESS, "查询成功");
        OcBReturnOrder returnOrder = returnOrderMapper.selectById(request.getId());
        if (returnOrder == null) {
            return new ValueHolderV14<>(ResultCode.FAIL, "当前记录已不存在");
        }
        LogisticsInfoQueryResult queryResult = new LogisticsInfoQueryResult();
        queryResult.setLogisticsId(returnOrder.getCpCLogisticsId());

        if (StringUtils.isBlank(returnOrder.getReturnId())) {
            queryResult.setSourceCode(returnOrder.getBillNo());
        } else {
            queryResult.setSourceCode(returnOrder.getReturnId());
        }
        if(returnOrder.getReturnCreateTime() == null){
            queryResult.setPaytime(DateUtil.format(returnOrder.getCreationdate(), "yyyy-MM-dd HH:mm:ss"));
        }else{
            queryResult.setPaytime(DateUtil.format(returnOrder.getReturnCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        queryResult.setExpresscode(returnOrder.getLogisticsCode());
        if (StringUtils.isNotEmpty(returnOrder.getOaid())) {
            //todo 暂时使用OAID判断加密
            queryResult.setReceiverName("***");
            queryResult.setReceiverMobile("***********");
            queryResult.setReceiverAddress("********************");
        } else {
            queryResult.setReceiverName(returnOrder.getReceiveName());
            queryResult.setReceiverMobile(returnOrder.getReceiveMobile());
            queryResult.setReceiverAddress(returnOrder.getReceiveAddress());
        }
        v14.setData(queryResult);
        return v14;
    }

    @Override
    public LogisticsTableEnum table() {
        return LogisticsTableEnum.OC_B_RETURN_ORDER;
    }
}
