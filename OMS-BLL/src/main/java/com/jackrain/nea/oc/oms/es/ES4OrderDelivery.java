package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;

/**
 * 发货信息表
 *
 * <AUTHOR>
 * @date 2020/11/11 2:27 下午
 */
public class ES4OrderDelivery {

    private ES4OrderDelivery() {
    }

    /**
     * 根据 物流单号 查询订单id
     *
     * @param logisticNO 物流号
     * @return JSONArray
     */
    public static JSONArray findDataByLogisticNumber(JSONArray logisticNO) {
        JSONObject whereKey = new JSONObject();
        whereKey.put("LOGISTIC_NUMBER", logisticNO);

        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_DELIVERY,
                OcElasticSearchIndexResources.OC_B_ORDER_DELIVERY, whereKey, null, null,
                logisticNO.size() * 3, 0, new String[]{"ID", "OC_B_ORDER_ID"});

        return search.getJSONArray("data");
    }
}
