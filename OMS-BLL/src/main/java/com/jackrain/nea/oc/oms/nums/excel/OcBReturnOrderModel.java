package com.jackrain.nea.oc.oms.nums.excel;

import com.jackrain.nea.util.excel.XlsAno;
import com.jackrain.nea.util.excel.XlsDBAno;
import com.jackrain.nea.util.excel.XlsSt;
import com.jackrain.nea.util.excel.XlsTyp;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: xiWen.z
 * create at: 2019/8/14 0014
 */
@XlsDBAno(name = "oc_b_return_order", desc = "退换货订单", index = 0, sort = "id:asc", st = {XlsSt.DB, XlsSt.ES})
public class OcBReturnOrderModel {

    @XlsAno(name = "id", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 0, desc = "退单编号")
    private Long id;

    @XlsAno(name = "bill_no", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 0, desc = "单据编号")
    private Long billNo;

    @XlsAno(name = "orig_order_id", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 10, desc = "原始订单编号")
    private Long origOrderId;

    @XlsAno(name = "bill_type", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 20, desc = "单据类型")
    private Integer billType;

    @XlsAno(name = "buyer_nick", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 30, desc = "买家昵称")
    private String buyerNick;

    @XlsAno(name = "orig_source_code", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 40, desc = "原始平台单号")
    private String origSourceCode;

    @XlsAno(name = "cp_c_shop_title", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 50, desc = "店铺名称")
    private String cpCShopTitle;

    @XlsAno(name = "return_id", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 60, desc = "平台退款单号")
    private String returnId;

    @XlsAno(name = "cp_c_logistics_ename", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 70, desc = "退回物流公司")
    private String cpCLogisticsEname;

    @XlsAno(name = "return_reason", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 80, desc = "退款原因")
    private String returnReason;

    @XlsAno(name = "logistics_code", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 90, desc = "退回物流单号")
    private String logisticsCode;

    @XlsAno(name = "is_reserved", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 100, desc = "换货预留库存")
    private Integer isReserved;

    @XlsAno(name = "is_back", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 110, desc = "是否原退")
    private Integer isBack;

    @XlsAno(name = "remark", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 120, desc = "备注")
    private String remark;

    @XlsAno(name = "receive_name", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 130, desc = "收货人")
    private String receiveName;

    @XlsAno(name = "receive_mobile", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 140, desc = "收货人手机")
    private String receiveMobile;

    @XlsAno(name = "creationdate", value = {XlsSt.NORMAL}, type = XlsTyp.DATE, index = 140, desc = "创建时间")
    private Date creationdate;

    @XlsAno(name = "in_time", value = {XlsSt.NORMAL}, type = XlsTyp.DATE, index = 140, desc = "入库时间")
    private Date inTime;

    @XlsAno(name = "business_type_name", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 150, desc = "业务类型")
    private String businessTypeName;

    @XlsAno(name = "receive_phone", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 150, desc = "收货人电话")
    private String receivePhone;

    @XlsAno(name = "pro_return_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 155, desc = "退货状态")
    private String proReturnStatus;

    @XlsAno(name = "receive_zip", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 160, desc = "收货人邮编")
    private String receiveZip;

    @XlsAno(name = "receiver_province_name", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 170, desc = "收货人省份")
    private String receiverProvinceName;

    @XlsAno(name = "receiver_city_name", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 180, desc = "收货人市")
    private String receiverCityName;

    @XlsAno(name = "receiver_area_name", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 190, desc = "收货人区")
    private String receiverAreaName;

    @XlsAno(name = "receive_address", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 200, desc = "收货人地址")
    private String receiveAddress;

    @XlsAno(name = "ship_amt", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 210, desc = "换货邮费")
    private BigDecimal shipAmt;

    @XlsAno(name = "return_amt_list", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 220, desc = "商品应退金额")
    private BigDecimal returnAmtList;

    @XlsAno(name = "return_amt_ship", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 230, desc = "应退邮费")
    private BigDecimal returnAmtShip;

    @XlsAno(name = "return_amt_other", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 240, desc = "其他金额")
    private BigDecimal returnAmtOther;

    @XlsAno(name = "exchange_amt", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 250, desc = "换货金额")
    private BigDecimal exchangeAmt;

    @XlsAno(name = "return_amt_actual", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 260, desc = "退货单总金额")
    private BigDecimal returnAmtActual;

    @XlsAno(name = "consign_amt_settle", value = {XlsSt.NORMAL}, type = XlsTyp.DOUBLE, index = 270, desc = "代销结算金额")
    private BigDecimal consignAmtSettle;

    @XlsAno(name = "tb_dispute_id", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 280, desc = "淘宝换货平台单号")
    private String tbDisputeId;

    @XlsAno(name = "return_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 290, desc = "单据状态")
    private Integer returnStatus;

    @XlsAno(name = "cp_c_phy_warehouse_in_id", value = {XlsSt.FOREIGN}, type = XlsTyp.STRING, index = 290, desc = "入库实体仓")
    private String cpCPhyWarehouseInId;

    @XlsAno(name = "is_confirm_pre", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 300, desc = "财务是否预处理")
    private String isConfirmPre;

    @XlsAno(name = "generic_mark", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 310, desc = "通用标记")
    private String genericMark;

    @XlsAno(name = "difference_ratio", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 320, desc = "应退和实退差异比例")
    private String differenceRatio;

    @XlsAno(name = "difference_amount", value = {XlsSt.NORMAL}, type = XlsTyp.STRING, index = 330, desc = "应退和实退差异金额")
    private BigDecimal differenceAmount;

    @XlsAno(name = "difference_mark", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 340, desc = "差异标记")
    private Integer differenceMark;
}
