package com.jackrain.nea.oc.oms.services.delivery.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.burgeon.r3.sg.channel.model.request.product.SgChannelProductQueryRequest;
import com.burgeon.r3.sg.core.model.table.channel.product.SgBChannelProduct;
import com.google.common.collect.Lists;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.data.basic.model.request.ProInfoQueryRequest;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendDataModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendModel;
import com.jackrain.nea.ip.model.others.StandplatLogisticsSendResult;
import com.jackrain.nea.oc.oms.mapper.OcBOrderDeliveryMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBSapSalesDataRecordAddTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.GiftTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.task.OcBSapSalesDataRecordAddTask;
import com.jackrain.nea.oc.oms.nums.LogisticsTelEnum;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.sap.OcBSapSalesDataRecordAddTaskService;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.IpRpcService;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName OrderDeliveryOfCyclePickUpImpl
 * @Description 中台周期购提货发货
 * <AUTHOR>
 * @Date 2024/8/23 09:17
 * @Version 1.0
 */
@Slf4j
@Component
public class OrderDeliveryOfCyclePickUpImpl implements OrderDeliveryCmd {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private IpRpcService ipRpcService;
    @Autowired
    private OmsOrderItemService omsOrderItemServie;
    @Autowired
    private OcBOrderDeliveryMapper ocBOrderDeliveryMapper;
    @Autowired
    private SgRpcService sgRpcService;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;
    @Autowired
    private OcBSapSalesDataRecordAddTaskService sapSalesDataRecordAddTaskService;
    @Autowired
    private OcBSapSalesDataRecordAddTaskMapper taskMapper;

    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
        Long orderId = ocBOrder.getId();
        // 判断期数。 如果是第一期 并且之前没有已发货的第一期 则构建新的数据 否则 直接结束
        if (ocBOrder.getCurrentCycleNumber() == null || ocBOrder.getCurrentCycleNumber() != 1) {
            updateOrderItem(ocBOrder, orderItemList);
            return true;
        }
        // 如果是第一期 判断之前是否有发货的订单
        List<OcBOrder> ocBOrders = ocBOrderMapper.selectFirstCycleDeliveryOrder(ocBOrder.getTid());
        if (CollectionUtils.isNotEmpty(ocBOrders)) {
            updateOrderItem(ocBOrder, orderItemList);
            return true;
        }
        // 一期 并且之前没有发货成功的订单 则包装订单信息 因为需要用母单的信息去发货
        List<OcBOrder> cycleOrderList = ocBOrderMapper.selectCycleOrderByTid(ocBOrder.getTid());
        if (CollectionUtils.isEmpty(cycleOrderList)) {
            updateOrderItem(ocBOrder, orderItemList);
            return true;
        }
        OcBOrder cycleOrder = cycleOrderList.get(0);
        OcBOrderRelation origOrderRelation = omsOrderService.selectOmsOrderInfo(cycleOrder.getId());
        origOrderRelation.getOrderInfo().setOrderType(ocBOrder.getOrderType());
        origOrderRelation.getOrderInfo().setOrderStatus(ocBOrder.getOrderStatus());
        origOrderRelation.getOrderInfo().setExpresscode(ocBOrder.getExpresscode());
        origOrderRelation.getOrderInfo().setCpCLogisticsId(ocBOrder.getCpCLogisticsId());
        origOrderRelation.getOrderInfo().setCpCLogisticsEcode(ocBOrder.getCpCLogisticsEcode());
        origOrderRelation.getOrderInfo().setCpCLogisticsEname(ocBOrder.getCpCLogisticsEname());
        origOrderRelation.setDeliveryOrderId(ocBOrder.getId());

        // 修改周期购订单状态为仓库发货状态
        // 修改状态
        OcBOrder updateOrder = new OcBOrder();
        updateOrder.setOrderStatus(OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger());
        updateOrder.setId(cycleOrder.getId());
        updateOrder.setModifieddate(new Date());
        updateOrder.setScanTime(new Date());
        ocBOrderMapper.updateById(updateOrder);
        OcBSapSalesDataRecordAddTask task = taskMapper.selectByOrderIdAndBillType(cycleOrder.getId(), 0);
        if (Objects.isNull(task)) {
            // 中台周期购订单 往销售数据中间表写数据
            sapSalesDataRecordAddTaskService.addTask(0, cycleOrder.getId(), SystemUserResource.getRootUser());
        }
        List<StandplatLogisticsSendDataModel> standplatLogisticsSendDataModels = this.addCommonIpParam(origOrderRelation, null, null, null);
        try {
            for (StandplatLogisticsSendDataModel model : standplatLogisticsSendDataModels) {
                ValueHolderV14<List<StandplatLogisticsSendResult>> vh = ipRpcService.sendStandPlatLogistics(model);
                if (vh == null || vh.getCode() == ResultCode.FAIL) {
                    ApplicationContextHandle.getBean(OmsOrderLogService.class).addUserOrderLog(ocBOrderRelation.getOrderId(), ocBOrderRelation.getOrderInfo().getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), "平台发货失败," + (vh == null ? "" : vh.getMessage()), null, null, SystemUserResource.getRootUser());
                    return false;
                } else {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("调用通用发货rpc异常", e);
            OcBOrder ocBOrderflag = new OcBOrder();
            ocBOrderflag.setId(ocBOrderRelation.getOrderInfo().getId());
            ocBOrderflag.setIsForce(0L);
            ocBOrderflag.setForceSendFailReason(e.getMessage());
            omsOrderService.updateOrderInfo(ocBOrderflag);
            return false;
        }
        return true;
    }

    private void updateOrderItem(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItems) {
        ocBOrderItemMapper.updateItemsWhenDeliverySuccess(ocBOrder.getId(), ocBOrder.getTid());
        String logMsg = "OrderId=" + ocBOrder.getId() + ",平台单号=" + ocBOrder.getTid() + "发货通知平台成功";
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "",
                null, null);
        OcBOrder update = new OcBOrder();
        update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        update.setId(ocBOrder.getId());
        update.setModifieddate(new Date());
        ocBOrderMapper.updateById(update);
    }

    private List<StandplatLogisticsSendDataModel> addCommonIpParam(OcBOrderRelation ocBOrderRelation, String subTid, Integer isSplit, String expressCode) {
        OcBOrder orderInfo = ocBOrderRelation.getOrderInfo();
        List<StandplatLogisticsSendDataModel> result = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = null;
        if (StringUtils.isEmpty(expressCode)) {
            ocBOrderItems = omsOrderItemServie.selectUnSuccessRefund(orderInfo.getId());
        } else {
            ocBOrderItems = ocBOrderRelation.getOrderItemList();
        }

        if (CollectionUtils.isEmpty(ocBOrderItems)) {
            return result;
        }
        ocBOrderItems = this.removeNoDeliveries(orderInfo, ocBOrderItems);
        if (ocBOrderItems == null) {
            return null;
        }
        Map<String, List<OcBOrderItem>> lists = ocBOrderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));
        for (Map.Entry<String, List<OcBOrderItem>> map : lists.entrySet()) {
            List<OcBOrderItem> orderItems = map.getValue();
            if (CollectionUtils.isNotEmpty(orderItems)) {
                //如果存在福袋、组合商品将真实商品明细转换为平台的虚拟明细
                Long id = orderInfo.getId();
                orderItems = transformCommonIpItemParam(orderItems, id, orderInfo.getPlatform());
                StandplatLogisticsSendDataModel model = getStandplatLogisticsSendDataModel(ocBOrderRelation, subTid, isSplit, expressCode, orderItems);
                if (model != null) {
                    result.add(model);
                }
            }
        }
        return result;
    }

    private List<OcBOrderItem> removeNoDeliveries(OcBOrder orderInfo, List<OcBOrderItem> ocBOrderItems) {
        List<OcBOrderItem> items = Lists.newArrayList();
        for (OcBOrderItem item : ocBOrderItems) {
            if (OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(item.getIsGift())
                    && StringUtils.isNotBlank(item.getReserveVarchar01())
                    && GiftTypeEnum.SYSTEM.getVal().equals(item.getGiftType())) {
                log.info(LogUtil.format("发货移除促销系统赠品 billNo:{},itemId:{}", "deliveryRemove"), orderInfo.getBillNo(), item.getId());
            } else if (StringUtils.isBlank(item.getOoid())) {
                log.info(LogUtil.format("发货移除无子单号商品 billNo:{},itemId:{}", "deliveryRemove"), orderInfo.getBillNo(), item.getId());
            } else {
                items.add(item);
            }
        }
        if (CollectionUtils.isEmpty(items)) {
            //排除后无可发货明细，返回null
            return null;
        }
        ocBOrderItems = items;
        return ocBOrderItems;
    }

    private List<OcBOrderItem> transformCommonIpItemParam(List<OcBOrderItem> orderItems, Long id, Integer platform) {
        List<OcBOrderItem> newOrderItems = new ArrayList<>();
        List<OcBOrderItem> transformOrderItems = new ArrayList<>();
        for (OcBOrderItem item : orderItems) {
            Long proType = item.getProType();
            if (proType != null && (proType.intValue() == SkuType.COMBINE_PRODUCT || proType.intValue() == SkuType.GIFT_PRODUCT)) {
                transformOrderItems.add(item);
            } else {
                newOrderItems.add(item);
            }
        }
        if (CollectionUtils.isEmpty(transformOrderItems)) {
            //需要转换的明细为空时，直接返回原明细
            return orderItems;
        }
        List<String> ooidList = transformOrderItems.stream().filter(obj -> StringUtils.isNotBlank(obj.getOoid())).map(obj -> obj.getOoid()).distinct().collect(Collectors.toList());
        //好衣库、拼多多没有子订单编号
        if (CollectionUtils.isEmpty(ooidList) && !(PlatFormEnum.HAO_YI_KU.getCode().equals(platform)
                || PlatFormEnum.PINDUODUO.getCode().equals(platform))) {
            //ooid为空时返回过滤后的明细列表
            return newOrderItems;
        }
        List<OcBOrderItem> specialItems = ocBOrderItemMapper.selectList(new LambdaQueryWrapper<OcBOrderItem>().eq(OcBOrderItem::getOcBOrderId, id).eq(OcBOrderItem::getProType, Long.valueOf(SkuType.NO_SPLIT_COMBINE)).in(CollectionUtils.isNotEmpty(ooidList), OcBOrderItem::getOoid, ooidList));
        if (CollectionUtils.isNotEmpty(specialItems)) {
            newOrderItems.addAll(specialItems);
        }
        return newOrderItems;
    }

    private StandplatLogisticsSendDataModel getStandplatLogisticsSendDataModel(OcBOrderRelation ocBOrderRelation, String subTid, Integer isSplit, String expressCode, List<OcBOrderItem> orderItems) {
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        BigDecimal skuNum = BigDecimal.ZERO;
        // 爱库存拆单发货 子单号获取
        StringBuilder subIds = new StringBuilder();

        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem ocBOrderItem : orderItems) {
                if (ocBOrderItem.getQty() != null) {
                    skuNum = skuNum.add(ocBOrderItem.getQty());
                }
                if (StringUtils.isNotBlank(ocBOrderItem.getOoid())) {
                    subIds.append(ocBOrderItem.getOoid()).append(",");
                }
            }
        }
        StandplatLogisticsSendDataModel standplatLogisticsSendDataModel = new StandplatLogisticsSendDataModel();
        StandplatLogisticsSendModel sendModel = new StandplatLogisticsSendModel();
        String company_code = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
        Map<Long, String> companyCodeMap = new HashMap<>();
        companyCodeMap.put(ocBOrder.getCpCLogisticsId(), company_code);
        sendModel.setErpOrderId(ocBOrder.getId());
        if (null != isSplit) {
            sendModel.setIsSplit(isSplit.longValue());
        } else {
            sendModel.setIsSplit(ocBOrder.getIsSplit() == null ? null : ocBOrder.getIsSplit().longValue());
        }
        // 不拆单 传null
        sendModel.setSubTid(subTid);
        if (StringUtils.isEmpty(expressCode)) {
            sendModel.setOutSid(ocBOrder.getExpresscode());
        } else {
            sendModel.setOutSid(expressCode);
        }
        sendModel.setOutSid(ocBOrder.getExpresscode());
        // 平台物流公司编码
        sendModel.setCompanyCode(company_code);
        sendModel.setPayType(ocBOrder.getPayType() == null ? "" : ocBOrder.getPayType().toString());
        sendModel.setPlatform(ocBOrder.getPlatform() == null ? null : ocBOrder.getPlatform().longValue());
        sendModel.setSellerNick(ocBOrder.getCpCShopSellerNick());
        // 换货单号 暂时没有
        sendModel.setDisputeId(null);
        sendModel.setOrderType(ocBOrder.getOrderType() == null ? "" : ocBOrder.getOrderType().toString());
        sendModel.setLogisticsCompanyName(ocBOrder.getCpCLogisticsEname());
        // 订单来源 先传空字符串，后面设计为可配置
        sendModel.setRetailSource("");
        // sku数量
        sendModel.setSize(String.valueOf(skuNum.intValue()));
        // 运单包裹状态(1发货完成，2部分发货， 取固定值)
        sendModel.setStatus("1");
        // 物流电话
        sendModel.setLogisticsTel(LogisticsTelEnum.enumToTel(sendModel.getCompanyCode()));
        List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> sendDetails = new ArrayList<>();
        List<OcBOrderDelivery> ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrder.getId());
        // 中台周期购订单 第一单发货时 需要使用母单的信息去发货
        if (ocBOrderRelation.getDeliveryOrderId() != null) {
            ocBOrderDeliveries = ocBOrderDeliveryMapper.selectOrderDeliveryByOrderId(ocBOrderRelation.getDeliveryOrderId());
            OcBOrderDelivery ocBOrderDelivery = ocBOrderDeliveries.get(0);
            ocBOrder.setCpCLogisticsId(ocBOrderDelivery.getCpCLogisticsId());
            ocBOrder.setExpresscode(ocBOrderDelivery.getLogisticNumber());
            // 重新生成delivery信息
            List<OcBOrderDelivery> cycleOrderDeliveryList = new ArrayList<>();
            // 根据订单明细来生成delivery信息
            for (OcBOrderItem ocBOrderItem : orderItems) {
                OcBOrderDelivery delivery = new OcBOrderDelivery();
                delivery.setPsCProEcode(ocBOrderItem.getPsCProEcode());
                delivery.setPsCProEname(ocBOrderItem.getPsCProEname());
                delivery.setPsCProId(ocBOrderItem.getPsCProId());
                delivery.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
                delivery.setPsCSkuId(ocBOrderItem.getPsCSkuId());
                delivery.setLogisticNumber(ocBOrderDelivery.getLogisticNumber());
                delivery.setCpCLogisticsId(ocBOrderDelivery.getCpCLogisticsId());
                delivery.setCpCLogisticsEcode(ocBOrderDelivery.getCpCLogisticsEcode());
                delivery.setCpCLogisticsEname(ocBOrderDelivery.getCpCLogisticsEname());
                cycleOrderDeliveryList.add(delivery);
            }
            ocBOrderDeliveries = cycleOrderDeliveryList;
        }
        log.debug("组装包裹信息：ocBOrderDeliveries={}", JSON.toJSONString(ocBOrderDeliveries));
        Map<String, OcBOrderItem> skuOoidMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderItems)) {
            StandplatLogisticsSendDataModel sendDataModel = douYinSend(ocBOrderDeliveries, orderItems, sendModel, companyCodeMap, ocBOrder, false);
            return sendDataModel;
        }
        log.debug("组装包裹信息：ocBOrderDeliveries={}", JSON.toJSONString(ocBOrderDeliveries));
        log.debug("组装包裹信息：skuOoidMap={}", JSON.toJSONString(skuOoidMap));
        //组装包裹信息
        if (CollectionUtils.isNotEmpty(ocBOrderDeliveries)) {
            List<String> proCodeList = ocBOrderDeliveries.stream().filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCProEcode())).map(obj -> obj.getPsCProEcode()).collect(Collectors.toList());
            ProInfoQueryRequest proInfoQueryRequest = new ProInfoQueryRequest();
            proInfoQueryRequest.setProEcodeList(proCodeList);
            for (OcBOrderDelivery item : ocBOrderDeliveries) {
                StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                OcBOrderItem ocBOrderItem = skuOoidMap.get(item.getPsCSkuEcode());
                if (ocBOrderItem == null) {
                    continue;
                }
                detail.setItemId(ocBOrderItem.getOoid());
                detail.setDeliveryNo(item.getLogisticNumber());
                //物流公司查询物流档案中是否存在
                String logisticsCompany = companyCodeMap.get(item.getCpCLogisticsId());
                if (StringUtils.isEmpty(logisticsCompany)) {
                    logisticsCompany = cpRpcService.getPlatformLogisticEcode(ocBOrder.getCpCLogisticsId(), Long.valueOf(ocBOrder.getPlatform()));
                    companyCodeMap.put(item.getCpCLogisticsId(), logisticsCompany);
                    detail.setLogisticsCompany(logisticsCompany);
                } else {
                    detail.setLogisticsCompany(logisticsCompany);
                }
                detail.setSku(ocBOrderItem.getPsCSkuEcode());
                detail.setSkuId(formatSkuId(ocBOrderItem.getPsCSkuPtEcode()));
                detail.setProductCode(item.getPsCProEcode());

                Long sendNum = getSendNum(ocBOrder, ocBOrderItem);
                detail.setNum(sendNum);
                detail.setRealNum(sendNum);
                detail.setLackNum(0L);
                detail.setExpressType("0");
                sendModel.setTid(ocBOrderItem.getTid());
                sendDetails.add(detail);
            }
        }
        sendModel.setStandplatLogisticsSendDetails(sendDetails);
        List<StandplatLogisticsSendModel> list = new ArrayList<>();
        list.add(sendModel);

        standplatLogisticsSendDataModel.setLogisticsSendModels(list);
        standplatLogisticsSendDataModel.setPlatform(sendModel.getPlatform());
        standplatLogisticsSendDataModel.setSellerNick(sendModel.getSellerNick());
        return standplatLogisticsSendDataModel;
    }

    private StandplatLogisticsSendDataModel douYinSend(List<OcBOrderDelivery> ocBOrderDeliveries, List<OcBOrderItem> orderItems, StandplatLogisticsSendModel sendModel, Map<Long, String> companyCodeMap, OcBOrder order, Boolean isAsc) {
        List<StandplatLogisticsSendModel.StandplatLogisticsSendDetails> sendDetails = new ArrayList<>();
        StandplatLogisticsSendDataModel standplatLogisticsSendDataModel = new StandplatLogisticsSendDataModel();

        List<OcBOrderItem> ocBOrderItemList = orderItems.stream().filter(obj -> obj != null && StringUtils.isNotEmpty(obj.getPsCSkuEcode()) && StringUtils.isNotEmpty(obj.getOoid())).collect(Collectors.toList());
        //组装包裹信息
        if (CollectionUtils.isNotEmpty(ocBOrderDeliveries)) {
            Map<String, List<OcBOrderDelivery>> deliveryNap = ocBOrderDeliveries.stream().collect(Collectors.groupingBy(OcBOrderDelivery::getPsCSkuEcode));

            /**
             * 组合商品的发货直接取订单信息，不获取发货信息列表的，
             */
            if (NumberUtils.INTEGER_ONE.equals(order.getIsCombination())) {
                for (int i = 0; i < orderItems.size(); i++) {
                    OcBOrderItem s = orderItems.get(i);
                    StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                    detail.setItemId(s.getOoid());
                    detail.setDeliveryNo(order.getExpresscode());
                    //物流公司查询物流档案中是否存在
                    String logisticsCompany = companyCodeMap.get(order.getCpCLogisticsId());
                    if (StringUtils.isEmpty(logisticsCompany)) {
                        logisticsCompany = cpRpcService.getPlatformLogisticEcode(order.getCpCLogisticsId(), Long.valueOf(order.getPlatform()));
                        companyCodeMap.put(order.getCpCLogisticsId(), logisticsCompany);
                        detail.setLogisticsCompany(logisticsCompany);
                    } else {
                        detail.setLogisticsCompany(logisticsCompany);
                    }
                    //不取条码ID，取平台条码编码
                    detail.setSkuId(s.getPsCSkuPtEcode());
                    detail.setSku(s.getPsCSkuEcode());
                    detail.setProductCode(s.getPsCProEcode());

                    Long sendNum = getSendNum(order, s);
                    detail.setNum(sendNum);
                    detail.setRealNum(sendNum);
                    detail.setLackNum(0L);
                    sendModel.setTid(s.getTid());
                    sendDetails.add(detail);
                }
            } else {
                List<OcBOrderEqualExchangeItem> ocBOrderEqualExchangeItemList = null;
                if (OcBOrderConst.IS_STATUS_IY.equals(order.getIsEqualExchange())) {
                    ocBOrderEqualExchangeItemList = ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemListByOrderId(order.getId());
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("douYinSend.selectOcBOrderEqualExchangeItemListByOrderId={}",
                                "douYinSend.selectOcBOrderEqualExchangeItemListByOrderId"), JSON.toJSONString(ocBOrderEqualExchangeItemList));
                    }
                }
                for (OcBOrderItem orderItem : ocBOrderItemList) {
                    if (log.isDebugEnabled()) {
                        log.debug(LogUtil.format("douYinSend.orderItem={}", "douYinSend.orderItem"), JSON.toJSONString(orderItem));
                    }
                    List<StandplatLogisticsSendModel.Bundle> bundleList = Lists.newArrayList();
                    StandplatLogisticsSendModel.StandplatLogisticsSendDetails detail = new StandplatLogisticsSendModel.StandplatLogisticsSendDetails();
                    sendModel.setTid(orderItem.getTid());
                    sendDetails.add(detail);
                    List<OcBOrderDelivery> orderDeliveryList = deliveryNap.get(orderItem.getPsCSkuEcode());
                    if (CollectionUtils.isEmpty(orderDeliveryList)) {
                        continue;
                    }
                    OcBOrderDelivery delivery = orderDeliveryList.get(0);
                    detail.setItemId(orderItem.getOoid());
                    detail.setDeliveryNo(delivery.getLogisticNumber());
                    //物流公司查询物流档案中是否存在
                    String logisticsCompany = companyCodeMap.get(delivery.getCpCLogisticsId());
                    if (StringUtils.isEmpty(logisticsCompany)) {
                        logisticsCompany = cpRpcService.getPlatformLogisticEcode(order.getCpCLogisticsId(), Long.valueOf(order.getPlatform()));
                        companyCodeMap.put(delivery.getCpCLogisticsId(), logisticsCompany);
                        detail.setLogisticsCompany(logisticsCompany);
                    } else {
                        detail.setLogisticsCompany(logisticsCompany);
                    }

                    detail.setSku(orderItem.getPsCSkuEcode());
                    detail.setSkuId(formatSkuId(orderItem.getPsCSkuPtEcode()));

                    Long sendNum = getSendNum(order, orderItem);
                    detail.setNum(sendNum);
                    detail.setRealNum(sendNum);
                    detail.setLackNum(0L);
                    detail.setProductCode(orderItem.getPsCProEcode());
                    detail.setOid(orderItem.getOoid());
                    // 为组合商品拆单明细
                    if (StringUtils.isNotBlank(orderItem.getGroupGoodsMark())) {
                        if (!orderItem.getGroupGoodsMark().contains("CG")) {
                            throw new NDSException("查询原组合商品订单明细组合商品标识为空");
                        }
                        OcBOrderItem item = ocBOrderItemMapper.selectItemByTid(orderItem.getTid(), Long.valueOf(orderItem.getGroupGoodsMark().split("CG")[1]));
                        if (Objects.isNull(item) || StringUtils.isBlank(item.getNumIid())) {
                            throw new NDSException("查询原组合商品订单明细平台商品ID为空");
                        }
                        detail.setSku(item.getPsCSkuEcode());
                        detail.setSkuId(formatSkuId(item.getPsCSkuPtEcode()));
                        sendNum = getSendNum(order, item);
                        detail.setNum(sendNum);
                        detail.setRealNum(sendNum);

                        SgChannelProductQueryRequest sgChannelProductQueryRequest = new SgChannelProductQueryRequest();
                        sgChannelProductQueryRequest.setNumiidList(Collections.singletonList(item.getNumIid()));
                        // 根据平台商品id获取平台店铺商品表信息
                        // 后续没有维护组合商品条码 直接continue 整单发货，
                        ValueHolderV14<List<SgBChannelProduct>> v14 = sgRpcService.queryChannelProduct(sgChannelProductQueryRequest);
                        if (!v14.isOK() || CollectionUtils.isEmpty(v14.getData())) {
                            continue;
                        }

                        //按照时间排序，取最近一条
                        List<SgBChannelProduct> products = v14.getData();
                        List<SgBChannelProduct> sortProducts = products.stream().sorted(Comparator.comparing(SgBChannelProduct::getCreationdate).reversed()).collect(Collectors.toList());

                        SgBChannelProduct sgBChannelProduct = sortProducts.get(0);
                        String combinationSkuIds = sgBChannelProduct.getCombinationSkuIds();
                        if (StringUtils.isBlank(combinationSkuIds)) {
                            continue;
                        }
                        String[] splitCombinationSkuIds = combinationSkuIds.replaceAll("\\[", "").replaceAll("\\]", "").split(",");
                        if (ArrayUtils.isEmpty(splitCombinationSkuIds)) {
                            continue;
                        }
                        SgChannelProductQueryRequest queryRequest = new SgChannelProductQueryRequest();
                        List<String> list = Arrays.asList(splitCombinationSkuIds);
                        queryRequest.setNumiidList(list);
                        // 根据匹配关系的平台商品id获取平台店铺商品表信息
                        v14 = sgRpcService.queryChannelProduct(queryRequest);
                        if (!v14.isOK() || CollectionUtils.isEmpty(v14.getData())) {
                            continue;
                        }
                        List<SgBChannelProduct> sgBChannelProductList = v14.getData();
                        Map<String, SgBChannelProduct> sgBChannelProductMap = new HashMap<>();
                        if (isAsc) {
                            sgBChannelProductMap = sgBChannelProductList.stream().sorted(Comparator.comparing(SgBChannelProduct::getCreationdate).thenComparing(SgBChannelProduct::getId))
                                    .collect(Collectors.toMap(SgBChannelProduct::getPsCSkuEcode, Function.identity(), (k1, k2) -> k1));
                        } else {
                            sgBChannelProductMap = sgBChannelProductList.stream().sorted(Comparator.comparing(SgBChannelProduct::getCreationdate).thenComparing(SgBChannelProduct::getId).reversed())
                                    .collect(Collectors.toMap(SgBChannelProduct::getPsCSkuEcode, Function.identity(), (k1, k2) -> k1));
                        }

                        String skuEcode = StringUtils.isNotBlank(orderItem.getOriginSkuEcode()) ? orderItem.getOriginSkuEcode() : orderItem.getPsCSkuEcode();
                        // 对等换货
                        if (OcBOrderConst.IS_STATUS_IY.equals(order.getIsEqualExchange())
                                && OcBOrderConst.IS_STATUS_IY.equals(orderItem.getIsEqualExchange())) {
                            if (CollectionUtils.isEmpty(ocBOrderEqualExchangeItemList)) {
                                continue;
                            }
                            Map<String, OcBOrderEqualExchangeItem> ooidMap = ocBOrderEqualExchangeItemList.stream()
                                    .filter(obj -> StringUtils.isNotEmpty(obj.getOoid()))
                                    .collect(Collectors.toMap(OcBOrderEqualExchangeItem::getOoid, a -> a, (k1, k2) -> k2));

                            OcBOrderEqualExchangeItem equalExchangeItem = ooidMap.get(item.getOoid());
                            if (Objects.isNull(equalExchangeItem)) {
                                continue;
                            }
                            skuEcode = equalExchangeItem.getPsCSkuEcode();
                        }
                        if (!sgBChannelProductMap.containsKey(skuEcode)) {
                            continue;
                        }
                        StandplatLogisticsSendModel.Bundle bundle = new StandplatLogisticsSendModel.Bundle();
                        bundle.setSubSkuId(formatSkuId(sgBChannelProductMap.get(skuEcode).getSkuId()));
                        bundle.setSubProductId(sgBChannelProductMap.get(skuEcode).getNumiid());
                        // 获取数量
                        sendNum = getSendNum(order, orderItem);
                        bundle.setComboNum(sendNum);
                        bundleList.add(bundle);
                        detail.setBundleList(bundleList);
                    }
                }
            }
        }
        sendModel.setStandplatLogisticsSendDetails(sendDetails);
        List<StandplatLogisticsSendModel> list = new ArrayList<>();
        list.add(sendModel);
        standplatLogisticsSendDataModel.setLogisticsSendModels(list);
        standplatLogisticsSendDataModel.setPlatform(sendModel.getPlatform());
        standplatLogisticsSendDataModel.setSellerNick(sendModel.getSellerNick());
        return standplatLogisticsSendDataModel;
    }


    private Long getSendNum(OcBOrder ocBOrder, OcBOrderItem ocBOrderItem) {
        // 存在替换商品数量
        if (Objects.nonNull(ocBOrderItem.getOriginSkuQty())) {
            return ocBOrderItem.getOriginSkuQty().longValue();
        }
        // 对等换货
        else if (OcBOrderConst.IS_STATUS_IY.equals(ocBOrder.getIsEqualExchange()) && OcBOrderConst.IS_STATUS_IY.equals(ocBOrderItem.getIsEqualExchange())) {
            //对等比例
            String ratio = ocBOrderItem.getEqualExchangeRatio();
            //计算对等换货原数量
            return paresEqualExchangeCount(ratio, ocBOrderItem.getQty());
        } else {
            return ocBOrderItem.getQty() == null ? 0L : ocBOrderItem.getQty().longValue();
        }
    }

    private String formatSkuId(String psCSkuPtEcode) {
        if (StringUtils.isNotEmpty(psCSkuPtEcode) && psCSkuPtEcode.contains("-")) {
            String[] strings = psCSkuPtEcode.split("-");
            return strings[1];
        }
        return psCSkuPtEcode;
    }

    private Long paresEqualExchangeCount(String ratioStr, BigDecimal qty) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format(" paresEqualExchange,ratioStr{},qty{}"), ratioStr, qty);
        }
        String[] ratio = ratioStr.split(":");
        if (ratio.length > 0) {
            List<String> list = Arrays.asList(ratio);
            //分母
            Long num1 = Long.valueOf(list.get(0));
            //分子
            Long num2 = Long.valueOf(list.get(1));
            //获取原数量 分母 * 数量 / 分子
            return num1 * qty.intValue() / num2;
        }
        return qty.longValue();
    }


}
