package com.jackrain.nea.oc.oms.services.invoice;

import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceItem;
import com.jackrain.nea.oc.oms.util.BigDecimalUtil;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.psext.result.ProExtResult;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.utils.ValueHolderUtils;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 发票重新计算金额
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AcFOrderInvoiceRefreshAmtService {

    @Resource
    private AcFOrderInvoiceMapper orderInvoiceMapper;

    @Resource
    private AcFOrderInvoiceItemMapper invoiceItemMapper;

    @Resource
    private PsRpcService psRpcService;

    public ValueHolder execute(QuerySession querySession) {
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        List<Long> ids = request.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            return ValueHolderUtils.getFailValueHolder("请选择数据");
        }

        if (ids.size() > 1) {
            return ValueHolderUtils.getFailValueHolder("请选择单条数据，不支持多条修改");
        }

        Long invoiceId = ids.get(0);

        AcFOrderInvoice orderInvoice = orderInvoiceMapper.selectById(invoiceId);
        if (Objects.isNull(orderInvoice)) {
            return ValueHolderUtils.getFailValueHolder("未找到发票单数据");
        }

        String invoiceStatus = orderInvoice.getInvoiceStatus();
        //仅针对开票状态为“待开票”和“开票失败”的发票有用
        if (!InvoiceConst.InvoiceStatus.NOT_INVOICE.equals(invoiceStatus) && !InvoiceConst.InvoiceStatus.INVOICE_FAIL.equals(invoiceStatus)) {
            return ValueHolderUtils.getFailValueHolder("只能修改待开票或开票失败的数据");
        }

        //查找开票明细
        List<AcFOrderInvoiceItem> invoiceItems = invoiceItemMapper.queryByOrderInvoiceId(invoiceId);
        if (CollectionUtils.isEmpty(invoiceItems)) {
            return ValueHolderUtils.getFailValueHolder("未找到明细数据");
        }

        List<String> skuCodes = invoiceItems.stream().map(AcFOrderInvoiceItem::getPsCSkuEcode).collect(Collectors.toList());
        List<ProExtResult> proExtList = psRpcService.queryProExtByEcodes(skuCodes);
        if (CollectionUtils.isEmpty(proExtList)) {
            return ValueHolderUtils.getFailValueHolder("未找到商品数据信息");
        }

        Map<String, List<ProExtResult>> proMap = proExtList.stream().collect(Collectors.groupingBy(ProExtResult::getEcode));

        //处理开票明细
        //主-合计税额
        BigDecimal totalTaxPrice = BigDecimal.ZERO;
        for (AcFOrderInvoiceItem invoiceItem : invoiceItems) {
            try {
                String skuCode = invoiceItem.getPsCSkuEcode();
                List<ProExtResult> proExtResults = proMap.get(skuCode);
                if (CollectionUtils.isEmpty(proExtResults)) {
                    log.error(LogUtil.format("查商品数据空,skuCode:{}", "发票金额重新计算"), skuCode);
                    continue;
                }
                ProExtResult proExtResult = proExtResults.get(0);
                //新查询的税率
                BigDecimal newTaxRate = proExtResult.getTaxRate();
                //未税单价
                BigDecimal noTaxPrice = invoiceItem.getInclusiveTaxPrice().divide(newTaxRate.add(new BigDecimal("1")), 2, RoundingMode.HALF_UP);
                //单价税额
                BigDecimal hadTaxPrice = invoiceItem.getInclusiveTaxPrice().subtract(noTaxPrice);
                //税额
                BigDecimal taxPrice = BigDecimalUtil.multiply(hadTaxPrice, invoiceItem.getQty());
                //未税总价
                BigDecimal noTaxAllPrice = BigDecimalUtil.multiply(noTaxPrice, invoiceItem.getQty());

                //更新明细发票金额等信息
                AcFOrderInvoiceItem updateItem = new AcFOrderInvoiceItem();
                updateItem.setId(invoiceItem.getId());
                updateItem.setTaxRate(newTaxRate.toString());
                updateItem.setInvoiceTaxAmt(taxPrice);
                updateItem.setNoTaxPrice(noTaxPrice);
                updateItem.setNoTaxAmt(noTaxAllPrice);
                updateItem.setModifieddate(new Date());
                invoiceItemMapper.updateById(updateItem);

                totalTaxPrice = totalTaxPrice.add(taxPrice);
            } catch (Exception e) {
                log.error(LogUtil.format("重新计算明细发票失败,invoiceId:{},skuCode:{}", "发票金额重新计算"), invoiceId, invoiceItem.getPsCSkuEcode(), e);
                DingTalkUtil.dingInvoice("invoiceId:" + invoiceId + ",sku:" + invoiceItem.getPsCSkuEcode() + ",重新计算发票失败");
            }
        }

        //主-未税总价
        BigDecimal totalNoTaxPrice = BigDecimalUtil.subtract(orderInvoice.getInvoiceInclusiveTaxAmt(), totalTaxPrice);

        //更新主单发票价格信息
        AcFOrderInvoice updateInvoice = new AcFOrderInvoice();
        updateInvoice.setId(orderInvoice.getId());
        updateInvoice.setInvoiceTaxAmt(totalTaxPrice);
        updateInvoice.setInvoiceNoTaxAmt(totalNoTaxPrice);
        updateInvoice.setModifieddate(new Date());

        orderInvoiceMapper.updateById(updateInvoice);

        return ValueHolderUtils.getSuccessValueHolder("success");
    }


}
