package com.jackrain.nea.oc.oms.nums.excel;

import com.jackrain.nea.util.excel.XlsAno;
import com.jackrain.nea.util.excel.XlsDBAno;
import com.jackrain.nea.util.excel.XlsSt;
import com.jackrain.nea.util.excel.XlsTyp;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: xiWen.z
 * create at: 2019/8/12 0012
 */
@XlsDBAno(name = "oc_b_order", desc = "全渠道订单", index = 0, sort = "id:asc", st = {XlsSt.DB, XlsSt.ES, XlsSt.R3})
public class OcBOrderModel {


    @XlsAno(name = "id", index = 0, desc = "订单ID")
    private Long id;

    @XlsAno(name = "bill_no", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 1, desc = "单据编号")
    private String billNo;

    @XlsAno(name = "order_tag", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 2, desc = "标签")
    private String orderTag;

    @XlsAno(name = "order_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 3, desc = "订单状态")
    private Integer orderStatus;


    @XlsAno(name = "cp_c_shop_title", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 4, desc = "店铺名称")
    private String cpCShopTitle;

    @XlsAno(name = "source_code", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 5, desc = "平台单号")
    private String sourceCode;


   /* @XlsAno(name = "platform", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 4, desc = "平台")
    private Integer platform;*/


    @XlsAno(name = "qty_all", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 6, desc = "商品总数")
    private BigDecimal qtyAll;

    @XlsAno(name = "user_nick", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 7, desc = "买家昵称")
    private String userNick;

    /* @XlsAno(name = "totqtylost", type = XlsTyp.DOUBLE, index = 8, desc = "缺货数量", ignore = 3)
     private BigDecimal totQtyLost;
 */
    @XlsAno(name = "receiver_name", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 8, desc = "收货人")
    private String receiverName;

    @XlsAno(name = "receiver_mobile", value = XlsSt.NOTNULL, type = XlsTyp.STRING, index = 9, desc = "收货人手机")
    private String receiverMobile;

   /* @XlsAno(name = "pay_type", value = {XlsSt.GROUP, XlsSt.NOTNULL}, type = XlsTyp.STRING, index = 8, desc = "付款方式")
    private Integer payType;
*/

    @XlsAno(name = "cp_c_region_province_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 10, desc = "省")
    private String cpCRegionProvinceEname;

    @XlsAno(name = "cp_c_region_city_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 11, desc = "市")
    private String cpCRegionCityEname;

    @XlsAno(name = "cp_c_region_area_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 12, desc = "区")
    private String cpCRegionAreaEname;

    @XlsAno(name = "receiver_address", value = {XlsSt.JOIN, XlsSt.NOTNULL}, type = XlsTyp.STRING, index = 13, desc = "收货人地址")
    private String receiverAddress;

    @XlsAno(name = "cp_c_phy_warehouse_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 14, desc = "发货仓库")
    private String cpCPhyWarehouseEname;

//    @XlsAno(name = "cainiao_wh_status", value = XlsSt.GROUP, type = XlsTyp.STRING, index = 15, desc = "菜鸟仓库作业状态")
//    private String cainiaoWhStatus;

    @XlsAno(name = "cp_c_logistics_ename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 16, desc = "快递公司")
    private String cpCLogisticsEname;

    @XlsAno(name = "expresscode", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 17, desc = "快递单号")
    private String expresscode;


    @XlsAno(name = "creationdate", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 18, desc = "创建时间")
    private Date creationdate;

    @XlsAno(name = "presale_deposit_time", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 19, desc = "定金时间")
    private Date presaleDepositTime;

    @XlsAno(name = "pay_time", value = {XlsSt.NORMAL}, type = XlsTyp.DATE, index = 20, desc = "付款时间")
    private Date payTime;

    @XlsAno(name = "audit_time", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 21, desc = "审核时间")
    private Date auditTime;

    @XlsAno(name = "scan_time", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 22, desc = "出库时间")
    private Date scanTime;

    @XlsAno(name = "seller_memo", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 23, desc = "卖家备注")
    private String sellerMemo;

    @XlsAno(name = "buyer_message", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 24, desc = "买家留言")
    private String buyerMessage;

    @XlsAno(name = "order_type", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 25, desc = "订单类型")
    private Integer orderType;

    @XlsAno(name = "double11_presale_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 26, desc = "预售状态")
    private Integer double11PresaleStatus;


    @XlsAno(name = "product_amt", type = XlsTyp.DOUBLE, index = 27, desc = "商品总额")
    private BigDecimal productAmt;

   /* @XlsAno(name = "order_discount_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 14, desc = "订单优惠金额")
    private BigDecimal orderDiscountAmt;*/

/*
    @XlsAno(name = "ownerename", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 21, desc = "创建人")
    private String ownerename;*/


    @XlsAno(name = "distribution_time", value = XlsSt.NORMAL, type = XlsTyp.DATE, index = 28, desc = "配货时间")
    private Date distributionTime;


    @XlsAno(name = "wms_cancel_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 29, desc = "wms撤回状态")
    private Integer wmsCancelStatus;

    @XlsAno(name = "return_status", value = {XlsSt.GROUP}, type = XlsTyp.STRING, index = 30, desc = "退货状态")
    private Integer returnStatus;

    @XlsAno(name = "product_discount_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 31, desc = "商品优惠金额")
    private BigDecimal productDiscountAmt;

    @XlsAno(name = "adjust_amt", type = XlsTyp.DOUBLE, index = 32, desc = "调整金额")
    private BigDecimal adjustAmt;

    @XlsAno(name = "ship_amt", type = XlsTyp.DOUBLE, index = 33, desc = "配送费用")
    private BigDecimal shipAmt;

    @XlsAno(name = "service_amt", type = XlsTyp.DOUBLE, index = 34, desc = "服务费")
    private BigDecimal serviceAmt;

    @XlsAno(name = "order_amt", type = XlsTyp.DOUBLE, index = 35, desc = "订单总额")
    private BigDecimal orderAmt;

    @XlsAno(name = "received_amt", type = XlsTyp.DOUBLE, index = 36, desc = "已收金额")
    private BigDecimal receivedAmt;

//    @XlsAno(name = "consign_amt", type = XlsTyp.DOUBLE, index = 37, desc = "结算金额")
//    private BigDecimal consignAmt;

//    @XlsAno(name = "consign_ship_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 38, desc = "代销运费")
//    private BigDecimal consignShipAmt;

    @XlsAno(name = "amt_receive", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 37, desc = "应收金额")
    private BigDecimal amtReceive;

    @XlsAno(name = "jd_receive_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 48, desc = "应收平台金额(京东)")
    private BigDecimal jdReceiveAmt;

    @XlsAno(name = "jd_settle_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 39, desc = "京东结算金额")
    private BigDecimal jdSettleAmt;

    @XlsAno(name = "cod_amt", value = XlsSt.NORMAL, type = XlsTyp.DOUBLE, index = 40, desc = "到付代收金额")
    private BigDecimal codAmt;


    @XlsAno(name = "invoice_header", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 41, desc = "开票抬头")
    private String invoiceHeader;

    @XlsAno(name = "invoice_content", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 42, desc = "开票内容")
    private String invoiceContent;

    @XlsAno(name = "suffix_info", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 43, desc = "订单补充信息")
    private String suffixInfo;

    @XlsAno(name = "sysremark", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 44, desc = "系统备注")
    private String sysremark;

    @XlsAno(name = "sg_b_out_bill_no", value = XlsSt.NORMAL, type = XlsTyp.STRING, index = 49, desc = "出库通知单")
    private String sgBOutBillNo;

}

