package com.jackrain.nea.oc.oms.nums;

import com.jackrain.nea.exception.NDSException;
import lombok.Getter;

import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 业务单据类型
 * @author: caomalai
 * @create: 2022-07-14 15:19
 **/
public enum StCBusinessStrategyStatusEnum {
    NON_AUDIT(1,"未审核"),
    AUDITED(2,"已审核"),
    VOID(3,"已作废"),
    END(4,"已结案");

    @Getter
    private Integer value;

    @Getter
    private String desc;

    StCBusinessStrategyStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value) {
        StCBusinessStrategyStatusEnum[] values = StCBusinessStrategyStatusEnum.values();
        for (StCBusinessStrategyStatusEnum nodeEnum : values) {
            if (Objects.equals(nodeEnum.value, value)) {
                return nodeEnum.desc;
            }
        }
        throw new NDSException("错误的节点配置:" + value);
    }
}
