package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;


@Mapper
@Component
public interface OcBOrderPromItemMapper extends ExtentionMapper<OcBOrderPromItem> {

    /**
     * 订单促销活动明细
     *
     * @param orderId 订单ID
     * @return List<OcBOrderPromItem>
     */
    @Select("SELECT * FROM oc_b_order_prom_item WHERE oc_b_order_id=#{orderId} and ISACTIVE = 'Y'")
    List<OcBOrderPromItem> selectOcBOrderPromItemList(long orderId);

    /**
     * @param isExcute 是否已执行
     * @param orderId  订单id
     * @param promId   促销id
     * @return
     */
    @Update("UPDATE oc_b_order_prom_item SET is_excute=#{isExcute},ISACTIVE=#{isActive},modifieddate = now() WHERE oc_b_order_id=#{orderId} and prom_id=#{promId}")
    Integer updateOcBOrderPromItem(@Param("isExcute") String isExcute,
                                   @Param("isActive") String isActive,
                                   @Param("orderId") long orderId,
                                   @Param("promId") long promId);

    /**
     * @param isExcute 是否已执行
     * @param orderId  订单id
     * @param promId   促销id
     * @return
     */
    @Update("UPDATE oc_b_order_prom_item SET is_excute=#{isExcute},ISACTIVE=#{isActive},pro_list=#{giftInfo},modifieddate = now() WHERE oc_b_order_id=#{orderId} and prom_id=#{promId}")
    Integer updateOcBOrderPromItemAndGiftInfo(@Param("isExcute") String isExcute,
                                              @Param("isActive") String isActive,
                                              @Param("orderId") long orderId,
                                              @Param("promId") long promId, @Param("giftInfo") String giftInfo);

    /**
     * @param isExcute 是否已执行
     * @param orderId  订单id
     * @param groupNo  促销活动分组编码
     * @return
     */
    @Update("UPDATE oc_b_order_prom_item SET is_excute=#{isExcute},ISACTIVE=#{isActive},pro_list=#{giftInfo},modifieddate = now() WHERE oc_b_order_id=#{orderId} and group_code=#{groupNo}")
    Integer updateOcBOrderPromItemAndGiftInfoByGroupNo(@Param("isExcute") String isExcute,
                                                       @Param("isActive") String isActive,
                                                       @Param("orderId") long orderId,
                                                       @Param("groupNo") String groupNo, @Param("giftInfo") String giftInfo);

    /**
     * @param isExcute 是否已执行
     * @param orderId  订单id
     * @param groupNo  促销活动分组编码
     * @return
     */
    @Update("UPDATE oc_b_order_prom_item SET is_excute=#{isExcute},ISACTIVE=#{isActive},modifieddate = now() WHERE oc_b_order_id=#{orderId} and group_code=#{groupNo}")
    Integer updateOcBOrderPromItemByGroupNo(@Param("isExcute") String isExcute,
                                            @Param("isActive") String isActive,
                                            @Param("orderId") long orderId,
                                            @Param("groupNo") String groupNo);

    /**
     * 是否执行过促销
     * @param orderId
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order_prom_item WHERE oc_b_order_id=#{orderId} and group_code=#{groupNo} and IS_EXCUTE = 'Y'")
    int isExecutedPromotionByGroupNo(@Param("orderId")long orderId, @Param("groupNo") String groupNo);

    /**
     * 是否执行过促销
     * @param orderId
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order_prom_item WHERE oc_b_order_id=#{orderId} and prom_id=#{promId} and IS_EXCUTE = 'Y'")
    int isExecutedPromotionByPromId(@Param("orderId")long orderId, @Param("promId") long promId);

    /**
     * 统计未释放的hold单个数
     * @param orderId
     * @return
     */
    @Select("SELECT count(*) FROM oc_b_order_prom_item WHERE oc_b_order_id=#{orderId} and ISACTIVE = 'Y' and (IS_EXCUTE = 'N' OR IS_EXCUTE IS NULL )")
    int countNotReleaseHoldOrder(long orderId);

}