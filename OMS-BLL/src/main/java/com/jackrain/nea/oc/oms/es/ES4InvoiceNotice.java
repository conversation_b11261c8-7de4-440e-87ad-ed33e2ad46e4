package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.result.OcBInvoiceNoticeQueryResult;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/10 4:22 下午
 */

@Slf4j
public class ES4InvoiceNotice {

    /**
     * @param billNo 单据编号
     * @return JSONArray 查询结果
     * @Description 根据单据编号获取开票通知信息
     * <AUTHOR>
     * @date 2020/11/10 4:22 下午
     */
    public static JSONArray findJSONArrayByBillNo(String billNo) {
        String[] returnFileds = new String[]{"ID"};
        //开票状态 1-待开票,2-暂缓开票允许导入
        List<String> estatusArr = new ArrayList<>();
        estatusArr.add("1");
        estatusArr.add("2");
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("BILL_NO", billNo);
        whereKeys.put("ESTATUS", estatusArr);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_TYPE_NAME, whereKeys, null,
                null, 50, 0, returnFileds);
        JSONArray data = search.getJSONArray("data");
        return data;
    }


    /**
     * @param result
     * @return JSONArray 查询结果
     * @Description 查询开票通知信息
     * <AUTHOR>
     * @date 2020/11/10 4:22 下午
     */
    public static JSONObject findJSONObjectByEs(OcBInvoiceNoticeQueryResult result, JSONObject whereKeys, JSONObject filterKeys, int pageStartIndex) {
        JSONArray orderKeys = new JSONArray();
        JSONObject order = new JSONObject();
        order.put("asc", false);
        order.put("name", "CREATIONDATE");
        orderKeys.add(order);
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME,
                OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_TYPE_NAME, whereKeys, filterKeys, orderKeys,
                result.getPage().getPageSize(), pageStartIndex, new String[]{"ID"});
        return search;

    }


    /**
     * @param indexName
     * @param type
     * @param pageIndex
     * @param pageSize
     * @param whereKeys
     * @param filterKeys
     * @param returnFileds
     * @return JSONArray 查询结果
     * @Description ES查询，返回分库键
     * <AUTHOR>
     * @date 2020/11/10 4:22 下午
     */
    public static JSONObject findJSONObjectByIndexName(String indexName, String type, JSONObject whereKeys, JSONObject filterKeys, int pageIndex, int pageSize, String[] returnFileds) {
        int startIndex = pageIndex * pageSize;
        JSONObject search = ElasticSearchUtil.search(indexName, type, whereKeys, filterKeys,
                null, pageSize, startIndex, returnFileds);
        return search;
    }

    /**
     * @return JSONArray 查询结果
     * @Description 开票通知 ES递归查询
     * <AUTHOR>
     * @date 2020/11/10 4:22 下午
     */
    public static JSONObject findInvoiceNoticeByEs(JSONObject whereKeys, JSONObject filterKeys, JSONArray orderKeys
            , Integer range, Integer startIndex, String[] returnFileds) {
        JSONObject search = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_INDEX_NAME, OcElasticSearchIndexResources.OC_B_INVOICE_NOTICE_TYPE_NAME,
                whereKeys, filterKeys, orderKeys, range, startIndex, returnFileds);
        return search;
    }

}
