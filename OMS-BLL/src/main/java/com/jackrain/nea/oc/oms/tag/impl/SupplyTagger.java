package com.jackrain.nea.oc.oms.tag.impl;

import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.tag.AbstractTagger;
import com.jackrain.nea.oc.oms.tag.consts.SupplyTypeEnum;
import com.jackrain.nea.oc.oms.tag.vo.TaggerRelation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Description： 轻供打标
 * Author: RESET
 * Date: Created in 2020/7/9 10:01
 * Modified By:
 */
@Component
public class SupplyTagger extends AbstractTagger {

    /**
     * 打标逻辑
     *
     * @param relation
     */
    @Override
    public void doTag(TaggerRelation relation) {
        if (Objects.nonNull(relation)) {
            doTag(relation.getOcBOrder(), relation.getOcBOrderItemList());
        }
    }

    /**
     * 打标逻辑
     *
     * @param ocBOrder
     * @param ocBOrderItemList
     */
    private void doTag(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList) {
        if (Objects.nonNull(ocBOrder) && CollectionUtils.isNotEmpty(ocBOrderItemList)) {
            // 只要商品明细有带轻供的商品，则主表打上轻供标
            // 过滤已退款明细
            List<OcBOrderItem> filterList = ocBOrderItemList.stream()
                    .filter(f -> Objects.isNull(f.getRefundStatus()) || f.getRefundStatus() != 6).collect(Collectors.toList());

            boolean isSupply = false;
            if (CollectionUtils.isNotEmpty(filterList)) {
                // 遍历，只要存在轻供商品，则返回，主表即可以打标
                for (int i = 0; i < filterList.size(); i++) {
                    OcBOrderItem item = filterList.get(i);
                    int itemSupplyType = item.getPsCProSupplyType() == null ? 0 : item.getPsCProSupplyType().intValue();
                    if (Objects.nonNull(item) && (SupplyTypeEnum.PROXY.getValue().equals(itemSupplyType)
                            || SupplyTypeEnum.CONSIGN.getValue().equals(itemSupplyType))) {
                        isSupply = true;
                        break;
                    }
                }
            }

            /*if (isSupply) {
                // 打标
                ocBOrder.setHasLightSupplyProd(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
            } else {
                // 取消标记
                ocBOrder.setHasLightSupplyProd(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
            }*/
        }
    }

}
