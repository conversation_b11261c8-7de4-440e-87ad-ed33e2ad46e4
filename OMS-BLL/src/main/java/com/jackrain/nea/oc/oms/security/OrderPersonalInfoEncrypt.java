package com.jackrain.nea.oc.oms.security;

import com.burgeon.r3.security.PersonalInfoSecurityUtil;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.oc.oms.model.result.GetOrderResult;
import com.jackrain.nea.oc.oms.model.result.QueryOrderResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: 易邵峰
 * @since: 2020-07-23
 * create at : 2020-07-23 14:35
 */
@Component
public class OrderPersonalInfoEncrypt {
    @Autowired
    private PropertiesConf propertiesConf;

    private boolean isOpenSecurityPersonal() {
        String isOpenParam = propertiesConf.getProperty("r3.security.personal", "true");
        return StringUtils.equalsIgnoreCase(isOpenParam, "true");
    }

    public void securityOrderPersonalInfo(QueryOrderResult orderResult) {
        if (isOpenSecurityPersonal()) {
            orderResult.setReceiverAddress(PersonalInfoSecurityUtil.securityAddress(orderResult.getReceiverAddress().replaceAll(",", "::::")));
            orderResult.setRegionReceiverAddress(PersonalInfoSecurityUtil.securityAddress(orderResult.getRegionReceiverAddress()));
            orderResult.setUserNick(PersonalInfoSecurityUtil.securityUserInfo(orderResult.getUserNick()));
            orderResult.setReceiverEmail(PersonalInfoSecurityUtil.securityEmail(orderResult.getReceiverEmail()));
            orderResult.setReceiverMobile(PersonalInfoSecurityUtil.securityMobile(orderResult.getReceiverMobile()));
            orderResult.setReceiverPhone(PersonalInfoSecurityUtil.securityPhone(orderResult.getReceiverPhone()));
            orderResult.setReceiverName(PersonalInfoSecurityUtil.securityUserInfo(orderResult.getReceiverName()));

        }
    }

    public void securityOrderPersonalInfo(GetOrderResult orderResult) {
        orderResult.setReceiverAddress(PersonalInfoSecurityUtil.securityAddress(orderResult.getReceiverAddress().replaceAll(",", "::::")));
        orderResult.setOrderAddress(PersonalInfoSecurityUtil.securityAddress(orderResult.getOrderAddress()));
        orderResult.setUserNick(PersonalInfoSecurityUtil.securityUserInfo(orderResult.getUserNick()));
        orderResult.setReceiverEmail(PersonalInfoSecurityUtil.securityEmail(orderResult.getReceiverEmail()));
        orderResult.setReceiverMobile(PersonalInfoSecurityUtil.securityMobile(orderResult.getReceiverMobile()));
        orderResult.setReceiverPhone(PersonalInfoSecurityUtil.securityPhone(orderResult.getReceiverPhone()));
        orderResult.setReceiverName(PersonalInfoSecurityUtil.securityUserInfo(orderResult.getReceiverName()));
    }

}
