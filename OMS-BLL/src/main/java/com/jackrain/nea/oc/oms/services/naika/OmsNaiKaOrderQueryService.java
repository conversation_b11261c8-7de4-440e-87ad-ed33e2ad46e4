package com.jackrain.nea.oc.oms.services.naika;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderNaiKaMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.enums.naika.OcBOrderNaiKaStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.NaiKaOrderQueryModel;
import com.jackrain.nea.oc.oms.model.request.naika.NaiKaOrderQueryRequest;
import com.jackrain.nea.oc.oms.model.table.OcBOrderNaiKa;
import com.jackrain.nea.resource.OcElasticSearchIndexResources;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import com.jackrain.nea.web.query.QueryUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName OmsNaiKaOrderQueryService
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/7/20 20:50
 * @Version 1.0
 */
@Component
@Slf4j
public class OmsNaiKaOrderQueryService {

    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderNaiKaMapper ocBOrderNaiKaMapper;
    @Autowired
    private CpRpcService cpRpcService;

    public ValueHolder naiKaOrderQuery(QuerySession querySession) {
        ValueHolder vh = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject resultData = new JSONObject();
        JSONObject param = JSON.parseObject(event.getParameterValue("param").toString());
        JSONObject fixColumn = param.getJSONObject("fixedcolumns");
        Integer range = param.getInteger("range") == null ? QueryUtils.getdefalutrange() : param.getInteger("range");
        Integer startIndex = param.getInteger("startindex") == null ? 0 : param.getInteger("startindex");
        NaiKaOrderQueryRequest naiKaOrderQueryRequest = fixColumn.toJavaObject(NaiKaOrderQueryRequest.class);
        log.info(LogUtil.format("naiKaOrderQueryRequest param:{}", "naiKaOrderQueryRequest"), JSONUtil.toJsonStr(naiKaOrderQueryRequest));

        return getValueHolder(querySession, vh, resultData, range, startIndex, naiKaOrderQueryRequest);
    }

    public ValueHolder getValueHolder(QuerySession querySession, ValueHolder vh, JSONObject resultData, Integer range, Integer startIndex, NaiKaOrderQueryRequest naiKaOrderQueryRequest) {
        // 根据参数 查询es数据
        JSONObject whereKeys = new JSONObject();
        JSONObject filterKey = new JSONObject();
        JSONArray orderByKey = this.getOrderByKey();
        try {
            Integer totalCount = 0;
            List<Long> ids = new ArrayList<>();
            if (StringUtils.isEmpty(naiKaOrderQueryRequest.getCardCode())) {
                buildWhereKeys(whereKeys, naiKaOrderQueryRequest);
                buildFilterKey(filterKey, naiKaOrderQueryRequest);
                JSONObject esResult = ElasticSearchUtil.search(OcElasticSearchIndexResources.OC_B_ORDER_INDEX_NAME, OcElasticSearchIndexResources.OC_B_ORDER_TYPE_NAME,
                        whereKeys, filterKey, orderByKey, range, startIndex, new String[]{"ID"});

                if (null == esResult) {
                    resultData.put("start", startIndex);
                    resultData.put("row", "");
                    resultData.put("totalRowCount", 0);
                    vh.put("data", resultData);
                    vh.put("code", 0);
                    vh.put("message", "success");
                    return vh;
                }
                JSONArray aryIds = esResult.getJSONArray("data");
                totalCount = esResult.getInteger("total");
                if (CollectionUtils.isEmpty(aryIds)) {
                    resultData.put("start", startIndex);
                    resultData.put("row", "");
                    resultData.put("totalRowCount", totalCount);
                    vh.put("data", resultData);
                    vh.put("code", 0);
                    vh.put("message", "success");
                    return vh;
                }
                ids = Lists.newArrayList();
                for (int i = 0; i < aryIds.size(); i++) {
                    Map<String, Long> map = (Map<String, Long>) aryIds.get(i);
                    ids.add(map.get("ID"));
                }
            } else {
                List<Long> ocBOrderIdList = ocBOrderNaiKaMapper.selectByCode(naiKaOrderQueryRequest.getCardCode());
                if (CollectionUtil.isEmpty(ocBOrderIdList)) {
                    totalCount = 0;
                } else {
                    List<OcBOrderNaiKa> ocBOrderNaiKaList = ocBOrderNaiKaMapper.getOrderNaiKaListByOrderId(ocBOrderIdList);
                    List<OcBOrderNaiKa> unPickUp = ocBOrderNaiKaList.stream().filter(s -> !"pickup".equals(s.getBusinessType())).collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(unPickUp)) {
                        totalCount = 0;
                    } else {
                        totalCount = 1;
                        ids = Collections.singletonList(unPickUp.get(0).getOcBOrderId());
                    }
                }
            }
            extracted(vh, resultData, range, startIndex, totalCount, ids);
            return vh;
        } catch (Exception e) {
            log.error(LogUtil.format("查询奶卡管理页面异常,异常信息为:{}"), Throwables.getStackTraceAsString(e));
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return vh;
        }
    }

    private void extracted(ValueHolder vh, JSONObject resultData, Integer range, Integer startIndex, Integer totalCount, List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            resultData.put("start", startIndex);
            resultData.put("row", "");
            resultData.put("totalRowCount", 0);
            vh.put("data", resultData);
            vh.put("code", 0);
            vh.put("message", "success");
            return;
        }
        List<NaiKaOrderQueryModel> naiKaOrderQueryModels = ocBOrderMapper.selectNaiKaQueryByIds(ids);
        for (NaiKaOrderQueryModel naiKaOrderQueryModel : naiKaOrderQueryModels) {
            buildNaiKaOrderQueryModel(naiKaOrderQueryModel);
        }

        JSONArray jsonArray = (JSONArray) JSONArray.toJSON(naiKaOrderQueryModels);
        List<JSONObject> jsonObjectList = JSONObject.parseArray(
                JSONObject.toJSONString(jsonArray, SerializerFeature.WriteMapNullValue), JSONObject.class);
        JSONArray getFrameDataFormat = getFrameDataFormat(jsonObjectList);
        resultData.put("start", startIndex);
        resultData.put("rowCount", range);
        resultData.put("row", getFrameDataFormat);
        resultData.put("totalRowCount", totalCount);
        vh.put("data", resultData);
        vh.put("code", 0);
        vh.put("message", "success");
    }

    private void buildNaiKaOrderQueryModel(NaiKaOrderQueryModel naiKaOrderQueryModel) {
        // orderStatusName 转换
        String orderStatusName = OmsOrderStatus.toDesc(naiKaOrderQueryModel.getOrderStatus());
        // toNaikaStatusName 转换
        OcBOrderNaiKaStatusEnum toNaikaStatus = OcBOrderNaiKaStatusEnum.getOrderStatusByStatus(naiKaOrderQueryModel.getToNaikaStatus());
        naiKaOrderQueryModel.setOrderStatusName(orderStatusName);
        naiKaOrderQueryModel.setToNaikaStatusName(toNaikaStatus == null ? "" : toNaikaStatus.getDesc());
        if (ObjectUtil.isNotNull(naiKaOrderQueryModel.getPlatform())) {
            CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(naiKaOrderQueryModel.getPlatform());
            if (ObjectUtil.isNotNull(cpCPlatform)) {
                naiKaOrderQueryModel.setPlatformName(cpCPlatform.getEname());
            }
        }

    }

    private void buildFilterKey(JSONObject filterKey, NaiKaOrderQueryRequest request) {
        if (ObjectUtil.isNotEmpty(request.getCreationDate())) {
            String orderDate = request.getCreationDate();
            String[] orderSplitDate = orderDate.split("~");
            String orderDateResult = convertDate(orderSplitDate[0], orderSplitDate[1]);
            filterKey.put("CREATIONDATE", orderDateResult);
        }
    }

    private void buildWhereKeys(JSONObject whereKeys, NaiKaOrderQueryRequest request) {

        if (ObjectUtil.isNotEmpty(request.getSourceCode())) {
            String sourceCode = request.getSourceCode();
            String sourceCodeReplace = sourceCode.replaceAll("\\s*", "");
            String[] splitSourceCode = sourceCodeReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitSourceCode));
            whereKeys.put("SOURCE_CODE", jsonArray);
        }

        if (CollectionUtil.isNotEmpty(request.getToNaiKaStatus())) {
            List<String> toNaiKaStatus = request.getToNaiKaStatus();
            JSONArray jsonArray = new JSONArray();
            for (String to : toNaiKaStatus) {
                jsonArray.add(to.replaceAll("=", ""));
            }
            whereKeys.put("TO_NAIKA_STATUS", jsonArray);
        }

        if (CollectionUtil.isNotEmpty(request.getOrderStatus())) {
            List<String> orderStatus = request.getOrderStatus();
            JSONArray jsonArray = new JSONArray();
            for (String to : orderStatus) {
                jsonArray.add(to.replaceAll("=", ""));
            }
            whereKeys.put("ORDER_STATUS", jsonArray);
        }

        if (CollectionUtil.isNotEmpty(request.getCpcShopId())) {
            whereKeys.put("CP_C_SHOP_ID", request.getCpcShopId());
        }

        if (ObjectUtil.isNotEmpty(request.getBillNo())) {
            String billNo = request.getBillNo();
            String billNoReplace = billNo.replaceAll("\\s*", "");
            String[] splitBillNoCode = billNoReplace.split(",|，");
            JSONArray jsonArray = new JSONArray(Arrays.asList(splitBillNoCode));
            whereKeys.put("BILL_NO", jsonArray);
        }

        if (CollectionUtil.isEmpty(request.getBusinessTypeCode())) {
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(OrderBusinessTypeCodeEnum.ON_LINE_MILK_CARD_SALE.getCode());
            jsonArray.add(OrderBusinessTypeCodeEnum.ON_LINE_FREE_MILK_CARD.getCode());
            jsonArray.add(OrderBusinessTypeCodeEnum.CYCLE_ORDER.getCode());
            jsonArray.add(OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER.getCode());
            jsonArray.add(OrderBusinessTypeCodeEnum.VIRTUAL_MILK_CARD.getCode());
            jsonArray.add(OrderBusinessTypeCodeEnum.MILK_CARD_RESET.getCode());
            jsonArray.add(OrderBusinessTypeCodeEnum.FREE_MILK_CARD_RESET.getCode());
            jsonArray.add(OrderBusinessTypeCodeEnum.SAP_GIVE.getCode());
            jsonArray.add(OrderBusinessTypeCodeEnum.SAP_MILK_CARD.getCode());
            jsonArray.add(OrderBusinessTypeCodeEnum.SAP_UNLINE_MILK_CARD.getCode());
            whereKeys.put("BUSINESS_TYPE_CODE", jsonArray);
        } else {
            List<String> businessTypeCode = request.getBusinessTypeCode();
            JSONArray jsonArray = new JSONArray();
            for (String to : businessTypeCode) {
                jsonArray.add(to.replaceAll("=", ""));
            }
            whereKeys.put("BUSINESS_TYPE_CODE", jsonArray);
        }
    }

    /**
     * 框架格式返回
     *
     * @param dataList
     * @return
     */
    private static JSONArray getFrameDataFormat(List<JSONObject> dataList) {
        JSONArray array = new JSONArray();
        if (dataList != null && dataList.size() > 0) {
            for (JSONObject emp : dataList) {
                Set<String> keySet = emp.keySet();
                JSONObject json = new JSONObject();
                for (String key : keySet) {
                    JSONObject val = new JSONObject();
                    val.put("val", emp.get(key));
                    json.put(key.toUpperCase(), val);
                }
                array.add(json);
            }
        }
        return array;
    }

    /**
     * 日期转成ES需要的格式
     *
     * @return
     */
    public String convertDate(String begindate, String endDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (StringUtils.isEmpty(begindate) || StringUtils.isEmpty(endDate)) {
            return "";
        }
        try {
            String result = sdf.parse(begindate).getTime() + "~" + sdf.parse(endDate).getTime();
            return result;

        } catch (ParseException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * ES 查询orderby条件
     *
     * @return
     */
    public JSONArray getOrderByKey() {
        JSONArray orderKeys = new JSONArray();
        JSONObject orderByKey = new JSONObject();
        orderByKey.put("asc", false);
        orderByKey.put("name", "ID");
        orderKeys.add(orderByKey);
        return orderKeys;
    }
}
