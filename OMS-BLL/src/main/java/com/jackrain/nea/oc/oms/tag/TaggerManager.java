package com.jackrain.nea.oc.oms.tag;

import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.tag.vo.TaggerRelation;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * Description： 标签总入口
 * Author: RESET
 * Date: Created in 2020/7/8 22:45
 * Modified By:
 */
@Component
public class TaggerManager {

    /**
     * 打标签 - 所有标签遍历打一轮
     *
     * @param relation
     */
    public void doTag(TaggerRelation relation) {
        if (Objects.nonNull(relation)) {
            // 取实例列表
            List<ITagger> taggerList = TaggerFactory.get().getTaggers();

            // 循环遍历所有打标服务，依次执行
            if (CollectionUtils.isNotEmpty(taggerList)) {
                taggerList.forEach(t -> t.doTag(relation));
            }
        }
    }

    /**
     * 打标签 - 所有标签遍历打一轮
     *
     * @param ocBOrder
     * @param ocBOrderItemList
     */
    public void doTag(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList) {
        doTag(TaggerRelation.builder().ocBOrder(ocBOrder).ocBOrderItemList(ocBOrderItemList).build());
    }

    /**
     * 打标签 - 所有标签遍历打一轮
     *
     * @param ocBOrder
     * @param ocBOrderItemList
     */
    public void doTag(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList,List<OcBOrder> ocBOrders) {
        doTag(TaggerRelation.builder().ocBOrder(ocBOrder).ocBOrderItemList(ocBOrderItemList).mergeOrders(ocBOrders).build());
    }



    /**
     * 打指定标签
     *
     * @param relation
     * @param taggerType - TaggerTypeEnum
     */
    public void doTagBySpecify(TaggerRelation relation, Integer taggerType) {
        TaggerFactory.get().getTaggerByType(taggerType).doTag(relation);
    }

    /**
     * 打指定标签
     *
     * @param ocBOrder
     * @param ocBOrderItemList
     * @param taggerType       - TaggerTypeEnum
     */
    public void doTagBySpecify(OcBOrder ocBOrder, List<OcBOrderItem> ocBOrderItemList, Integer taggerType) {
        doTagBySpecify(TaggerRelation.builder().ocBOrder(ocBOrder).ocBOrderItemList(ocBOrderItemList).build(), taggerType);
    }

    /**
     * 获取实例
     *
     * @return
     */
    public static TaggerManager get() {
        return ApplicationContextHandle.getBean(TaggerManager.class);
    }

}
