package com.jackrain.nea.oc.oms.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.config.PropertiesConf;
import com.jackrain.nea.cp.services.RegionNewService;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.ProvinceCityAreaInfo;
import com.jackrain.nea.cpext.model.RegionInfo;
import com.jackrain.nea.cpext.model.RegionType;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.IpBJingdongCoupondtaiMapper;
import com.jackrain.nea.oc.oms.mapper.IpBJingdongRefundMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OmsPayType;
import com.jackrain.nea.oc.oms.model.enums.OrderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongCouponType;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongInvoice;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongOrderStatus;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongPayType;
import com.jackrain.nea.oc.oms.model.relation.IpJingdongOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderConst;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongCoupondtai;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrder;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrderItem;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongOrderItemExt;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongRefund;
import com.jackrain.nea.oc.oms.model.table.IpBJingdongUser;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPayment;
import com.jackrain.nea.oc.oms.model.table.OcBOrderPromotion;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.services.OmsReturnOrderService;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.jackrain.nea.resource.CpRedisKeyResources.getRegionKey;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-04-26
 * create at : 2019-04-26 11:13 AM
 * 京东订单中间表转换成全渠道订单
 */
@Component
@Slf4j
public class JingdongOrderTransferUtils {

    @Autowired
    private PropertiesConf propertiesConf;

    @Autowired
    private BuildSequenceUtil sequenceUtil;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private RegionNewService regionService;

    @Autowired
    private OmsReturnOrderService omsReturnOrderService;

    @Autowired
    private IpBJingdongRefundMapper ipBJingdongRefundMapper;

    @Autowired
    private IpBJingdongCoupondtaiMapper ipBJingdongCoupondtaiMapper;

    @Autowired
    private JingdongAmountCalculationUtil jingdongAmountCalculationUtil;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;


    /**
     * @param jdCouponList 京东优惠表List
     * @param ocBOrder     订单主表实体
     * @return 转好的优惠信息表
     */
    public List<OcBOrderPromotion> convertJingdongOrderCoupondListToOmsOrderPromotionList(List<IpBJingdongCoupondtai> jdCouponList, OcBOrder ocBOrder) {
        if (jdCouponList == null || jdCouponList.size() == 0) {
            return new ArrayList<>();
        }
        List<OcBOrderPromotion> promotionList = new ArrayList<>();
        for (IpBJingdongCoupondtai coupondtai : jdCouponList) {
            promotionList.add(convertJingdongOrderCoupondToOmsOrderPromotion(coupondtai, ocBOrder.getId()));
        }
        return promotionList;
    }

    /**
     * 京东优惠表转全渠道优惠表
     *
     * @param coupondtai 京东优惠表信息
     * @param id
     * @return 全渠道优惠表信息
     */
    private OcBOrderPromotion convertJingdongOrderCoupondToOmsOrderPromotion(IpBJingdongCoupondtai coupondtai, Long id) {
        OcBOrderPromotion item = new OcBOrderPromotion();
        item.setId(sequenceUtil.buildOrderPromotionSequenceId());
        //设置分库键
        item.setOcBOrderId(id);
        item.setCreationdate(new Date());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setModifiername(SystemUserResource.ROOT_USER_NAME);
        item.setModifieddate(new Date());
        item.setModifierid(SystemUserResource.ROOT_USER_ID);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerid(SystemUserResource.ROOT_USER_ID);
        item.setOwnername(SystemUserResource.ROOT_USER_NAME);
        item.setAdClientId(SystemUserResource.AD_CLIENT_ID);
        item.setAdOrgId(SystemUserResource.AD_ORG_ID);
        item.setVersion(0L);
        //优惠名称
        item.setPromotionName(coupondtai.getCouponType());
        //优惠金额
        item.setAmtDiscount(coupondtai.getCouponPrice());
        //赠送数量
        item.setGiftItemQty(new BigDecimal("0"));
        //赠送商品
        item.setGiftItemName(null);
        //优惠活动描述
        item.setPromotionDesc(null);
        //活动编号T
        item.setActiveId("-1");
        //是否启用
        item.setIsactive("Y");
        return item;
    }

    /**
     * @param ocBOrder 订单主表实体
     * @param jdOrder  京东主表
     * @return 转好的支付信息表
     */
    public OcBOrderPayment convertJingdongOrderPayToOmsOrderPay(OcBOrder ocBOrder, IpBJingdongOrder jdOrder) {
        OcBOrderPayment item = new OcBOrderPayment();
        item.setId(sequenceUtil.buildOrderPaymentSequenceId());
        //设置分库键
        item.setOcBOrderId(ocBOrder.getId());
        item.setCreationdate(new Date());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setModifiername(SystemUserResource.ROOT_USER_NAME);
        item.setModifieddate(new Date());
        item.setModifierid(SystemUserResource.ROOT_USER_ID);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerid(SystemUserResource.ROOT_USER_ID);
        item.setOwnername(SystemUserResource.ROOT_USER_NAME);
        item.setAdClientId(SystemUserResource.AD_CLIENT_ID);
        item.setAdOrgId(SystemUserResource.AD_ORG_ID);
        item.setVersion(0L);
        //支付流水号
        item.setPaymentNo(null);
        //交易创建时间
        item.setCreationdate(ocBOrder.getOrderDate());
        //支付时间
        item.setPayTime(ocBOrder.getPayTime());
        //交易完成时间
        item.setEndTime(ocBOrder.getEndTime());
        //支付金额
        item.setPaymentAmt(ocBOrder.getReceivedAmt());
        //订单金额
        item.setAmtOrder(ocBOrder.getOrderAmt());
        //付款方式
        item.setPayType(ocBOrder.getPayType());
        //付款状态
        item.setPayStatus(getPayStatus(jdOrder));
        //是否启用
        item.setIsactive("Y");
        return item;
    }

    /**
     * @param jdOrder 京东主表
     * @return 除了京东货到付款，其他的都是已付款
     */
    private Integer getPayStatus(IpBJingdongOrder jdOrder) {
        if (JingdongPayType.ONE_PAY_ARRIVAL.equals(jdOrder.getPayType())) {
            return 0;
        }
        return 1;
    }

    /**
     * @return 转换好的订单明细List
     */
    public List<OcBOrderItem> convertJdOrderItemToOmsOrderItem(IpJingdongOrderRelation orderInfo) throws Exception {

        List<IpBJingdongOrderItemExt> jingdongOrderItems = orderInfo.getJingdongOrderItems();
        if (CollectionUtils.isEmpty(jingdongOrderItems)) {
            return new ArrayList<>();
        }

        List<OcBOrderItem> orderItems = new ArrayList<>();
        for (IpBJingdongOrderItemExt jingdongOrderItem : jingdongOrderItems) {
            orderItems.add(covertJdItemToOmsOrderItem(jingdongOrderItem, orderInfo));
        }
        return orderItems;
    }

    /**
     * 京东明细转为全渠道明细
     *
     * @param jdItem 京东明细实体
     * @return 转换好全渠道明细实体
     */
    private OcBOrderItem covertJdItemToOmsOrderItem(IpBJingdongOrderItemExt jdItem, IpJingdongOrderRelation orderInfo) throws Exception {
        OcBOrder order = orderInfo.getOcBOrder();
        OcBOrderItem item = new OcBOrderItem();
        item.setId(sequenceUtil.buildOrderItemSequenceId());
        item.setOcBOrderId(order.getId());
        item.setCreationdate(new Date());
        item.setModifierename(SystemUserResource.ROOT_USER_NAME);
        item.setModifiername(SystemUserResource.ROOT_USER_NAME);
        item.setModifieddate(new Date());
        item.setModifierid(SystemUserResource.ROOT_USER_ID);
        item.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        item.setOwnerid(SystemUserResource.ROOT_USER_ID);
        item.setOwnername(SystemUserResource.ROOT_USER_NAME);
        item.setAdClientId(SystemUserResource.AD_CLIENT_ID);
        item.setAdOrgId(SystemUserResource.AD_ORG_ID);
        item.setVersion(0L);

        //转换数据
        //liqb 更改ooid类型从Long类型改成String类型
        item.setOoid(String.valueOf(jdItem.getSkuId()));
        item.setRefundStatus(0);
        //商品路径
        item.setPicPath(null);
        //组合商品 在商品名称前面加上【组合】
        item.setTitle(calcProductName(jdItem));
        //组合商品时这个数量为拆解后数量
        item.setQty(BigDecimal.valueOf(jdItem.getItemTotal()));

        item.setRefundId(null);
        item.setQtyRefund(BigDecimal.ZERO);
        //调整金额 2019-11-27 默认为0
        item.setAdjustAmt(BigDecimal.ZERO);
        //商品优惠金额 2019-11-27
        item.setAmtDiscount(discountAmtNew(jdItem));
        //成交价格
        item.setPrice(calcFinishPrice(jdItem));

        //标准价
        item.setPriceList(jdItem.getJdPrice());
        //成交金额 2019-11-27 成交价格 * 数量 – 平摊金额
        item.setRealAmt(calcRealAmt(jdItem));
        //标准重量 商品条码. weight
        item.setStandardWeight(getStandardWeight(jdItem));
        item.setIsBuyerRate(null);
        item.setBuyerUsedIntegral(0L);
        item.setIsAllocatestock(0);
        //京东num_iid 无
        item.setNumIid(jdItem.getWareId());
        item.setSkuNumiid(jdItem.getSkuId() + "");

        //国标码 条码档案对应的国标码 (数据库无此字段)
        //item.setBarcode(getGbCode(jdItem));
        //订单表.tid
        item.setTid(order.getSourceCode());
//        //条码信息
//        item.setPsCSkuId(getSkuId(jdItem));
//        // 2019-06-17 增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
//        String psSkuEcode = jdItem.getOuterSkuId();
//        if (checkIsNeedTransferSkuUpperCase()) {
//            psSkuEcode = StringUtils.upperCase(psSkuEcode);
//        }
//        item.setPsCSkuEcode(psSkuEcode);
//        //商品条码
//        item.setPsCProId(getProId(jdItem));
//        item.setPsCProEcode(getProEcode(jdItem));
//        item.setPsCProEname(getProEname(jdItem));
//        //颜色信息
//        item.setPsCClrId(getColorId(jdItem));
//        item.setPsCClrEcode(getColorEcode(jdItem));
//        item.setPsCClrEname(getColorEname(jdItem));
//        //尺寸信息
//        item.setPsCSizeId(getSizeId(jdItem));
//        item.setPsCSizeEcode(getSizeEcode(jdItem));
//        item.setPsCSizeEname(getSizeEname(jdItem));
        //是否赠品
        item.setIsGift(0);
        //组合商品条码
        //item.setGroupName(getGroupName(jdItem));
        //优惠平摊
        item.setOrderSplitAmt(getAmtDiscount(jdItem));
        //是否预售条码，需要和预售活动做判断
        item.setIsPresalesku(getIsPresaleSku(jdItem));
        //isactive
        item.setIsactive("Y");
        //是否实缺
        item.setIsLackstock(0);
        //退货金额
        item.setAmtRefund(BigDecimal.ZERO);
        //退款状态
        item.setRefundStatus(0);
        //是否发货
        item.setIsSendout(0);
        //是否买家使用积分
        item.setIsBuyerRate(0);
        //国标码。SKU 69码。从条码档案中有一个69码字段
        if (jdItem.getProdSku() != null) {
            item.setBarcode(jdItem.getProdSku().getBarcode69());
        } else {
            item.setBarcode(null);
        }
        initialTaobaoOrderItem(jdItem, item);
        //赋值平台商品名称
        item.setPtProName(StringUtils.defaultString(jdItem.getSkuName()));
        //增加吊牌价和sex传值
        item.setSex(jdItem.getProdSku().getSex());
        item.setPriceTag(jdItem.getProdSku().getPricelist());
        //2019-10-09  新增京东转单京东平台优惠券应收金额
        item.setAmtJingdongCoupon(jdItem.getReserveDecimal01());
        item.setPsCBrandId(jdItem.getProdSku().getPsCBrandId());

        //京东换货单
        if (orderInfo.getIsExchange()) {
            //订单类型
            order.setOrderType(OrderTypeEnum.EXCHANGE.getVal());
            String orderRemark = orderInfo.getJingdongOrder().getOrderRemark();
            //平台退款单号（服务单号）   售后返修换新！服务单号：822460006#返修发货#原订单号:120712624090
            String returnId = StringUtils.substringBetween(orderRemark, "：", "#");
            //原订单号
            String sourceTid = StringUtils.substringAfterLast(orderRemark, ":");
            /**
             * ☆获取退单，如果有，则设置关联关系，如果没有，则可能人工新增了退单
             */
            OcBReturnOrder ocBReturnOrder = omsReturnOrderService.selectRefundOrderByRefundId(returnId);
            if (Optional.ofNullable(ocBReturnOrder).isPresent()) {
                order.setOrigReturnOrderId(ocBReturnOrder.getId());
            }
            /**
             * ☆ 根据服务单号获取退单中间表信息
             */
            IpBJingdongRefund ipBJingdongRefund = ipBJingdongRefundMapper.selectJingdongRefundByAfsserviceId4WareId(NumberUtils.createLong(returnId));

            //排除系统作废和已取消的单据，原因：主表作废后，明细不作废
            List<OcBOrderItem> collect = ocBOrderItemMapper.selectOrderJdReplacementOrderItem(sourceTid, ipBJingdongRefund.getWareid());
            List<OcBOrderItem> ocBOrderItems = collect.stream().filter(o -> {
                Integer orderStatus = ocBOrderMapper.selectById(o.getOcBOrderId()).getOrderStatus();
                return !(OmsOrderStatus.SYS_VOID.toInteger().equals(orderStatus) || OmsOrderStatus.CANCELLED.toInteger().equals(orderStatus));
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(ocBOrderItems)) {
                OcBOrderItem sourceItem = ocBOrderItems.get(0);
                //退货金额
                item.setAmtRefund(sourceItem.getAmtRefund());
                //整单平摊金额
                item.setOrderSplitAmt(sourceItem.getOrderSplitAmt().multiply(item.getQty()).divide(sourceItem.getQty(), 4, BigDecimal.ROUND_CEILING));
                //标准价
                item.setPriceList(sourceItem.getPriceList());
                //成交价格
                item.setPrice(sourceItem.getPrice());
                //优惠金额
                item.setAmtDiscount(sourceItem.getAmtDiscount().multiply(item.getQty()).divide(sourceItem.getQty(), 4, BigDecimal.ROUND_CEILING));
                //调整金额
                item.setAdjustAmt(sourceItem.getAdjustAmt().multiply(item.getQty()).divide(sourceItem.getQty(), 4, BigDecimal.ROUND_CEILING));
                //行成交金额
                item.setRealAmt(sourceItem.getRealAmt().multiply(item.getQty()).divide(sourceItem.getQty(), 4, BigDecimal.ROUND_CEILING));
                // 分销价格
                item.setDistributionPrice(sourceItem.getDistributionPrice());
                //吊牌价
                item.setReserveDecimal02(sourceItem.getReserveDecimal02());
                // 京东平台优惠券
                item.setReserveDecimal03(sourceItem.getReserveDecimal03());
                // 京东平台优惠金额
                item.setReserveDecimal04(sourceItem.getReserveDecimal04());
                // 结算单价
                item.setPriceSettle(sourceItem.getPriceSettle());
                //结算总额
                item.setTotPriceSettle(sourceItem.getTotPriceSettle());
                //单件实际成交价
                item.setPriceActual(sourceItem.getPriceActual());
                //吊牌价
                item.setReserveDecimal02(sourceItem.getReserveDecimal02());


                order.setOrderAmt(item.getRealAmt());
                order.setReceivedAmt(item.getRealAmt());

                //主表调整优惠
                order.setAdjustAmt(item.getAdjustAmt());
                //主表商品优惠金额
                order.setProductDiscountAmt(item.getAmtDiscount());
                //主表订单优惠金额
                order.setOrderDiscountAmt(item.getOrderSplitAmt());
                //主表商品总额
                order.setProductAmt(order.getOrderAmt().add(order.getProductDiscountAmt()).add(order.getOrderDiscountAmt()).subtract(order.getAdjustAmt()));
            } else {
                throw new Exception("京东换新单据关联退单的原始订单明细失败,找不到原单");
            }
        }
        return item;
    }


    /**
     * 京东换货
     *
     * @param item
     * @param orderInfo
     */
    private void jinDongExchangeOrderItem(OcBOrderItem item, IpJingdongOrderRelation orderInfo) {

        String orderRemark = orderInfo.getJingdongOrder().getOrderRemark();
        OcBOrder ocBOrder = orderInfo.getOcBOrder();

        // 服务单号
        String returnId = StringUtils.substringBetween(orderRemark, "：", "#");
        judgeDataIsIllegal(returnId, "服务单号->{}为空", returnId, "换货单服务单号为空");

        String sourceTid = StringUtils.substringAfterLast(orderInfo.getOcBOrder().getBuyerMessage(), ":");
        judgeDataIsIllegal(sourceTid, "原订单号[原平台单号]->{}为空", sourceTid, "原订单号为空");

        // 查询京东退单中间表
        IpBJingdongRefund jdRefund = ipBJingdongRefundMapper.selectJingdongRefundByAfsserviceId4WareId(Long.valueOf(returnId));
        judgeDataIsIllegal(jdRefund, "查询京东退单中间表结果为空, 服务单号->{}", returnId, "未查询到京东退单");

        Long wareId = jdRefund.getWareid();
        judgeDataIsIllegal(jdRefund.getWareid(), "退单中间表商品编号为空,中间退单Id->{}", jdRefund.getId(), "退单中间表商品编号为空");

        // 调用金额服务
        OcBOrderItem sourceItem = null;
        try {
            List<OcBOrderItem> orderItems = jingdongAmountCalculationUtil.amountCalculation(sourceTid);
            judgeDataIsIllegal(orderItems, "未找到原订单信息,原订单单号{}", sourceTid, "未找到原订单信息");
            if (orderItems.size() < 1) {
                throw new NDSException("京东换货, 未找到原单商品明细信息");
            }
            String wareIdStr = String.valueOf(wareId);
            for (OcBOrderItem orderItem : orderItems) {
                if (orderItem == null) {
                    continue;
                }
                String skuNumIid = orderItem.getSkuNumiid();
                if (StringUtils.isBlank(skuNumIid)) {
                    continue;
                }
                if (!StringUtils.equalsIgnoreCase(wareIdStr, skuNumIid)) {
                    continue;
                }
                sourceItem = orderItem;
                break;
            }

            if (log.isDebugEnabled()) {
                log.debug("JinDongOrderTransferUtils.京东换货.退单中间表商品编号->{}, 数量->{}", wareId, item.getQty());
            }
            judgeDataIsIllegal(sourceItem, "未找到对应的原单商品{}", wareIdStr, "未找到对应的原单商品");

        } catch (Exception ex) {
            throw new NDSException("京东换货, 调用金额计算异常");
        }

        BigDecimal qty = item.getQty();
        if (qty == null || qty.compareTo(BigDecimal.ZERO) < 1) {
            throw new NDSException("京东换货, 换货商品数量异常");
        }

        // 明细金额
        item.setPriceActual(sourceItem.getPriceActual());
        item.setPrice(sourceItem.getPrice());
        BigDecimal tmpRealAmt = sourceItem.getPriceActual() == null ? BigDecimal.ZERO : sourceItem.getPriceActual();
        BigDecimal realTotAml = tmpRealAmt.multiply(qty);
        item.setRealAmt(realTotAml);

        BigDecimal tmpAmt = sourceItem.getPrice() == null ? BigDecimal.ZERO : sourceItem.getPrice();
        BigDecimal priceAmtTot = qty.multiply(tmpAmt);
        item.setAdjustAmt(realTotAml.subtract(priceAmtTot));

        ocBOrder.setProductAmt(item.getPrice().multiply(item.getQty()));
        ocBOrder.setAdjustAmt(item.getAdjustAmt());
        ocBOrder.setOrderAmt(item.getRealAmt());
        ocBOrder.setReceivedAmt(item.getRealAmt());

        if (log.isDebugEnabled()) {
            log.debug("JinDongOrderTransferUtils.京东换货.处理商品明细结束.Finished...");
        }
        //退货金额
       /* item.setAmtRefund(sourceItem.getAmtRefund());
        BigDecimal divide = item.getQty().divide(sourceItem.getQty(), 4, RoundingMode.FLOOR);
        //整单平摊金额
        item.setOrderSplitAmt(sourceItem.getOrderSplitAmt().multiply(divide));
        //标准价
        item.setPriceList(sourceItem.getPriceList());
        //成交价格
        item.setPrice(sourceItem.getPrice());
        //优惠金额
        item.setAmtDiscount(sourceItem.getAmtDiscount().multiply(divide));
        //调整金额
        item.setAdjustAmt(sourceItem.getAdjustAmt().multiply(divide));
        //行成交金额
        item.setRealAmt(sourceItem.getRealAmt().multiply(divide));
        // 分销价格
        item.setDistributionPrice(sourceItem.getDistributionPrice());
        //吊牌价
        item.setReserveDecimal02(sourceItem.getReserveDecimal02());
        // 京东平台优惠券
        item.setReserveDecimal03(sourceItem.getReserveDecimal03());
        // 京东平台优惠金额
        item.setReserveDecimal04(sourceItem.getReserveDecimal04());
        // 结算单价
        item.setPriceSettle(sourceItem.getPriceSettle());
        //结算总额
        item.setTotPriceSettle(sourceItem.getTotPriceSettle());
        //单件实际成交价
        item.setPriceActual(sourceItem.getPriceActual());
        //吊牌价
        item.setPriceTag(sourceItem.getPriceTag());

        //主表商品优惠金额
        ocBOrder.setProductDiscountAmt(ocBOrder.getProductDiscountAmt().add(item.getAmtDiscount()));
        //主表订单优惠金额
        ocBOrder.setOrderDiscountAmt(ocBOrder.getOrderDiscountAmt().add(item.getOrderSplitAmt()));
        //主表商品总额
        ocBOrder.setProductAmt(ocBOrder.getOrderAmt().add(ocBOrder.getProductDiscountAmt()).add(ocBOrder.getOrderDiscountAmt()));*/


    }


    /**
     * 京东换货.日志记录
     *
     * @param obj
     * @param errorMsg
     * @param flag
     * @param expMsg
     */
    private void judgeDataIsIllegal(Object obj, String errorMsg, Object flag, String expMsg) {
        if (obj == null) {
            throw new NDSException("京东换货," + expMsg);
        }
    }

    /**
     * 初始化TaobaoOrderItem内容
     * 2019-07-30 组合福袋商品修改
     *
     * @param orderItemExt 淘宝中间表数据
     * @param item         需要赋值的taobaoorderItem
     */

    private void initialTaobaoOrderItem(IpBJingdongOrderItemExt orderItemExt, OcBOrderItem item) {
        if (orderItemExt.getProdSku() != null) {
            item.setPsCProId(orderItemExt.getProdSku().getProdId());
            // ProECode
            item.setPsCProEcode(orderItemExt.getProdSku().getProdCode());
            item.setPsCSkuId(orderItemExt.getProdSku().getId());

            item.setPsCClrEcode(orderItemExt.getProdSku().getColorCode());
            item.setPsCClrEname(orderItemExt.getProdSku().getColorName());
            item.setPsCClrId(orderItemExt.getProdSku().getColorId());
            item.setPsCSizeEcode(orderItemExt.getProdSku().getSizeCode());
            item.setPsCSizeEname(orderItemExt.getProdSku().getSizeName());
            item.setPsCSizeId(orderItemExt.getProdSku().getSizeId());
            item.setPsCProMaterieltype(orderItemExt.getProdSku().getMaterialType());
            item.setStandardWeight(orderItemExt.getProdSku().getWeight());
            item.setSkuSpec(orderItemExt.getProdSku().getSkuSpec());
            item.setProType(NumberUtils.toLong(orderItemExt.getProdSku().getSkuType() + ""));
            item.setMDim4Id(orderItemExt.getProdSku().getMDim4Id());
            item.setMDim6Id(orderItemExt.getProdSku().getMDim6Id());
            if ("Y".equals(orderItemExt.getProdSku().getIsEnableExpiry())) {
                item.setIsEnableExpiry(1);
            } else {
                item.setIsEnableExpiry(0);
            }
            if (orderItemExt.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT
                    || orderItemExt.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT) {
                //为福袋或者组合商品
                item.setPsCSkuEcode(orderItemExt.getProdSku().getSkuEcode()); //虚拟条码
                item.setPsCProEname(orderItemExt.getSkuName()); //虚拟条码商品名称取中间表的名称
                //由于数据库做了对尺寸code和商品code做了非空限制
                item.setPsCSizeEcode(orderItemExt.getOuterSkuId());
                item.setPsCProEcode(orderItemExt.getOuterSkuId());
            } else {
                // 2019-06-16 易邵峰修改：增加参数判断是否需要进行对SKU进行大写转换。目的是为了统一SKU
                String psSkuEcode = orderItemExt.getOuterSkuId();
                if (checkIsNeedTransferSkuUpperCase()) {
                    psSkuEcode = StringUtils.upperCase(psSkuEcode);
                }
                item.setPsCSkuEcode(psSkuEcode);
                item.setPsCProEname(orderItemExt.getProdSku().getName()); //商品名称
            }
            // 供应类型 0 普通 1.代销轻供 2.寄售轻供
            item.setPsCProSupplyType(orderItemExt.getProdSku().getPsCProSupplyType());
        } else {
            item.setSkuSpec(null);
            item.setStandardWeight(BigDecimal.ZERO);
        }
    }

    private Integer getIsPresaleSku(IpBJingdongOrderItemExt jdItem) {
        if (jdItem.getProdSku() != null && jdItem.getProdSku().getSkuType() == SkuType.PRE_SALE_PRODUCT) {
            return 1;
        }
        return 0;
    }

    /**
     * 调用金额平摊服务
     * todo 组合和福袋时特殊处理
     *
     * @param jdItem 京东明细
     * @return 平摊金额
     */
    private BigDecimal getAmtDiscount(IpBJingdongOrderItemExt jdItem) {
//        if (jdItem.getProdSku() != null) {
//            if (jdItem.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT || jdItem.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT) {
//                return new BigDecimal("0.0000");
//            } else {
//                return null;
//            }
//        }
//        return null;
        return BigDecimal.ZERO;
    }

    /**
     * @param jdItem 京东明细
     * @return 组合商品条码
     */
    private String getGroupName(IpBJingdongOrderItemExt jdItem) {
        if (jdItem.getProdSku() != null) {
            return jdItem.getProdSku().getSkuEcode();
        }
        return null;
    }

    /**
     * @param jdItem 京东明细
     * @return 标准重量
     */
    private BigDecimal getStandardWeight(IpBJingdongOrderItemExt jdItem) {
        if (jdItem.getProdSku() != null) {
            return jdItem.getProdSku().getWeight();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 标准价*数量-优惠金额-优惠平摊金额
     *
     * @param jdItem 京东明细
     * @return 实际成交金额
     */
    private BigDecimal calcRealAmt(IpBJingdongOrderItemExt jdItem) {
        if (null == jdItem.getJdPrice() || null == jdItem.getItemTotal()) {
            jdItem.setJdPrice(BigDecimal.ZERO);
            jdItem.setItemTotal(0L);
            return BigDecimal.ZERO;
        }
        if (null == jdItem.getAveragediscount()) {
            jdItem.setAveragediscount(BigDecimal.ZERO);
        }
        BigDecimal decimal = jdItem.getJdPrice().multiply(BigDecimal.valueOf(jdItem.getItemTotal()))
                .subtract(discountAmtNew(jdItem)).subtract(jdItem.getAveragediscount());
        //四位小数
        return decimal.setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * todo 组合和福袋时特殊处理
     * （标准价*数量-优惠金额-平摊金额+调整金额）/数量，保留4位小数
     *
     * @param jdItem 京东明细
     * @return 成交价格
     */
    private BigDecimal calcPrice(IpBJingdongOrderItemExt jdItem) {
        if (jdItem.getProdSku() != null) {
            if (jdItem.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT || jdItem.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT) {
                return new BigDecimal("0.0000");
            } else {
                return calcFinishPrice(jdItem);
            }
        }
        return calcFinishPrice(jdItem);
    }

    /**
     * 计算成交价
     *
     * @param jdItem 京东明细
     */
    private BigDecimal calcFinishPrice(IpBJingdongOrderItemExt jdItem) {
        if (null == jdItem.getJdPrice() || null == jdItem.getItemTotal()) {
            jdItem.setJdPrice(BigDecimal.ZERO);
            jdItem.setItemTotal(0L);
            return BigDecimal.ZERO;
        }
        if (null == jdItem.getAveragediscount()) {
            jdItem.setAveragediscount(BigDecimal.ZERO);
        }
        if (null == jdItem.getAveragediscount()) {
            jdItem.setAveragediscount(BigDecimal.ZERO);
        }

        return jdItem.getJdPrice();

        //四位小数
//        return (jdItem.getJdPrice().multiply(BigDecimal.valueOf(jdItem.getItemTotal()))
//                .subtract(discountAmtNew(jdItem)).subtract(jdItem.getAveragediscount()).add(BigDecimal.ZERO))
//                .divide(BigDecimal.valueOf(jdItem.getItemTotal()), 4, RoundingMode.HALF_UP);
    }

    /**
     * todo 组合和福袋时特殊处理
     * jd_price为0返回0.否则取值根据skuid匹配条码优惠信息表中会类型为【30-单品促销优惠】中sum（coupon_price）
     *
     * @param jdItem 京东明细
     * @return 优惠金额
     */
    //加入组合和福袋之后  此处不再判断是否为组合商品  故注释掉先关逻辑
    private BigDecimal calcDiscountAmtNew(IpBJingdongOrderItemExt jdItem) {

        if (jdItem.getJdPrice().equals(BigDecimal.ZERO)) {
            return new BigDecimal("0");
        } else {
            QueryWrapper<IpBJingdongCoupondtai> wrapper = new QueryWrapper<>();
            wrapper.eq("sku_id", jdItem.getSkuId()).eq("ip_b_jingdong_order_id", jdItem.getIpBJingdongOrderId());
            List<IpBJingdongCoupondtai> coupondtaiList = ipBJingdongCoupondtaiMapper.selectList(wrapper);
            BigDecimal discountAmt = BigDecimal.ZERO;
            if (coupondtaiList != null && coupondtaiList.size() != 0) {
                for (IpBJingdongCoupondtai item : coupondtaiList) {
                    if (JingdongCouponType.THIRTY_COUPON.equals(item.getCouponType())) {
                        if (null != item.getCouponPrice()) {
                            discountAmt = discountAmt.add(item.getCouponPrice());
                        }
                    }
                }
            }
            return discountAmt;
        }
    }

    /**
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @Description 优惠信息列表中优惠劵店铺承担且SKU编码非空的优惠金额
     * @Date 2019-11-27
     * @Param [jdItem]
     **/
    private BigDecimal discountAmtNew(IpBJingdongOrderItemExt jdItem) {
        BigDecimal discountAmt = BigDecimal.ZERO;
        QueryWrapper<IpBJingdongCoupondtai> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(IpBJingdongCoupondtai::getSkuId, jdItem.getSkuId())
                .eq(IpBJingdongCoupondtai::getIpBJingdongOrderId, jdItem.getIpBJingdongOrderId());

        List<IpBJingdongCoupondtai> coupondtaiList = ipBJingdongCoupondtaiMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(coupondtaiList)) {
            for (IpBJingdongCoupondtai ipBJingdongCoupondtai : coupondtaiList) {
                if (JingdongCouponType.TWENTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_NINE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FOUR_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FIVE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_HUNDRED_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SEVEN_ONE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THREE_SIX_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_TWO_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())) {

                    if (null != ipBJingdongCoupondtai.getCouponPrice()) {
                        discountAmt = discountAmt.add(ipBJingdongCoupondtai.getCouponPrice());
                    }
                }
            }
        }
        return discountAmt;
    }

    /**
     * 组合商品 在商品名称前面加上【组合】
     *
     * @param jdItem 京东Item
     * @return 商品名
     */
    private String calcProductName(IpBJingdongOrderItemExt jdItem) {
        if (jdItem.getProdSku() != null && jdItem.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT) {
            return "[组合]" + jdItem.getSkuName();
        }
        return jdItem.getSkuName();
    }

    /**
     * @param orderInfo 包含所有京东中间表的实体
     * @return 转换好的订单实体
     */
    public OcBOrder convertJdOrderToOmsOrder(IpJingdongOrderRelation orderInfo, boolean isHistoryOrder) {

        IpBJingdongOrder jdOrder = orderInfo.getJingdongOrder();
        String orderState = jdOrder.getOrderState();

        List<IpBJingdongCoupondtai> coupondtaiList = orderInfo.getJingdongCoupondtaiList();
        IpBJingdongUser jdUser = orderInfo.getJingdongUser();
        OcBOrder order = new OcBOrder();
        if(orderInfo.getIsExchange()) {
            order.setIsDeliveryUrgent(OcBOrderConst.IS_DELIVERY_URGENT);
        }
        //基础信息
        order.setId(sequenceUtil.buildOrderSequenceId());
        order.setModifierename(SystemUserResource.ROOT_USER_NAME);
        order.setOwnerename(SystemUserResource.ROOT_USER_NAME);
        order.setVersion(0L);
        BaseModelUtil.initialBaseModelSystemField(order);
        //平台单号
        order.setSourceCode(jdOrder.getOrderId().toString());
        //oaid
        order.setOaid(jdOrder.getOaid());
        // 等于平台单号
        order.setTid(jdOrder.getOrderId().toString());
        // 合并单号，默认与平台单号一致
        order.setMergeSourceCode(jdOrder.getOrderId().toString());
        //单据编号
        order.setBillNo(sequenceUtil.buildBillNo());
        //下单店铺
        order.setCpCShopId(jdOrder.getCpCShopId());
        //下单店铺id.
        // TODO: 需要按照云店类型进行赋值。现在还没有云店类型。暂时不进行判断赋值
        //下单店铺标题。需要查个表获取Title（平台店铺信息表）
        //平台店铺标题
        CpShop shopInfo = null;
        if (jdOrder.getCpCShopId() != null) {
            shopInfo = cpRpcService.selectShopById(jdOrder.getCpCShopId());
        } else {
            throw new NDSException("平台店铺id为空!");
        }
        if (shopInfo != null) {
            order.setCpCShopTitle(shopInfo.getCpCShopTitle());
            //下单店仓编码. 到平台店铺信息表中获取下单店仓字段ID值；
            /*order.setCpCStoreEcode(shopInfo.getCpCStoreEcode());
            //下单店仓名称. 到平台店铺信息表中获取下单店仓字段ID值；
            order.setCpCStoreEname(shopInfo.getCpCStoreEname());
            //下单店仓id. 到平台店铺信息表中获取下单店仓字段ID值；
            order.setCpCStoreId(shopInfo.getCpCStoreId());*/
            //下单店仓id. 到平台店铺信息表中获取下单卖家店铺名称
            order.setCpCShopEcode(shopInfo.getEcode());
            order.setCpCShopSellerNick(shopInfo.getSellerNick());
        } else {
            // 20190727修改：如果 平台店铺不存在，则不再继续保持。而是抛出异常，不允许转单操作
            throw new NDSException("平台店铺id=" + jdOrder.getCpCShopId() + "不存在");
        }

        String fullname = orderInfo.getJingdongUser().getFullname();
        if (StringUtils.isNotBlank(fullname) && fullname.length() > 10) {
            fullname = fullname.substring(0, 10);
        }

        order.setUserNick(fullname);
        order.setIsInreturning(0);
        order.setIsInterecept(0);
        if (isHistoryOrder) {
            order.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            order.setIsHistory("Y");
            // @20201218 历史订单出库时间赋值 任务ID 29976
            order.setScanTime(new Date());
        } else if (StringUtils.equalsIgnoreCase(JingdongOrderStatus.TRADE_CANCELED, orderState)) {
            order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
            order.setIsInreturning(1);
        } else if (StringUtils.equalsIgnoreCase(JingdongOrderStatus.LOCKED, orderState)) {
            order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
            order.setIsInreturning(1);
            order.setIsInterecept(1);
        } else {
            order.setOrderStatus(OmsOrderStatus.ORDER_DEFAULT.toInteger());
            order.setIsHistory("N");
        }
        order.setOrderFlag("0");
        //商品总额
        order.setProductAmt(buildTotalProductAmt(orderInfo.getJingdongOrderItems()));
        //商品优惠金额 2019-11-27
        BigDecimal productDiscountAmount = buildTotalProductDiscountNew(coupondtaiList);
        order.setProductDiscountAmt(productDiscountAmount);
        //订单优惠金额 2019-11-27
        order.setOrderDiscountAmt(getOrderDiscountAmountNew(coupondtaiList, orderInfo.getJingdongOrderItems()));
        order.setAdjustAmt(new BigDecimal("0"));
        order.setShipAmt(jdOrder.getFreightPrice());
        order.setServiceAmt(new BigDecimal("0"));
        order.setReceivedAmt(buildReceivedAmt(jdOrder));
        //订单总额 商品总额+配送费用-商品优惠金额-订单优惠金额
        BigDecimal orderAmt = order.getProductAmt().add(jdOrder.getFreightPrice()).subtract(order.getProductDiscountAmt()).subtract(order.getOrderDiscountAmt());
        order.setOrderAmt(orderAmt);
        //京东结算金额
        order.setJdSettleAmt(buildJdSettleAmt(jdOrder, orderInfo.getJingdongCoupondtaiList()));
        //客户实付金额
        order.setReceivedAmt(buildReceivedAmt(jdOrder));
        //发票相关
        setIsInvoiceInfo(order, jdOrder);
        order.setWeight(BigDecimal.ZERO);
        /*
         * 配送物流公司 若货到付款则物流公司为京东快递，通过名称查询京东相关信息塞进去（物流公司表）
         * 京东支付方式1-货到付款,订单表支付方式赋值2(货到付款)
         * 京东支付方式其他，订单表支付方式为1(在线支付)
         */
        String payType = jdOrder.getPayType();
        if (!StringUtils.isBlank(payType) && payType.equals(JingdongPayType.ONE_PAY_ARRIVAL)) {
            try {
                LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName("京东");
                order.setCpCLogisticsId(logisticsInfo.getId());
                order.setCpCLogisticsEcode(logisticsInfo.getCode());
                order.setCpCLogisticsEname(logisticsInfo.getName());
            } catch (Exception ex) {
                log.error(LogUtil.format("通过看物流名称查询物流信息异常:{}"), Throwables.getStackTraceAsString(ex));
            }
            order.setCodAmt(jdOrder.getOrderPayment() != null ? jdOrder.getOrderPayment() : BigDecimal.ZERO);
            order.setPayType(OmsPayType.CASH_ON_DELIVERY.toInteger());
        } else {
            order.setCodAmt(BigDecimal.ZERO);
            order.setPayType(OmsPayType.ON_LINE_PAY.toInteger());
        }

        order.setOrderDate(jdOrder.getOrderStartTime());
        order.setEndTime(jdOrder.getOrderEndTime());
        order.setReceiverName(jdUser.getFullname());
        order.setReceiverMobile(jdUser.getMobile());
        //省市区匹配
        ProvinceCityAreaInfo regionInfo = regionService.selectProvinceCityAreaInfo(jdUser.getProvince(), jdUser.getCity(), jdUser.getCounty());
        if (regionInfo.getProvinceInfo() != null) {
            order.setCpCRegionProvinceId(regionInfo.getProvinceInfo().getId());
            order.setCpCRegionProvinceEcode(regionInfo.getProvinceInfo().getCode());
            order.setCpCRegionProvinceEname(regionInfo.getProvinceInfo().getName());
        } else {
            throw new NDSException("调用<省>市区服务异常");
        }
        if (regionInfo.getCityInfo() != null) {
            order.setCpCRegionCityId(regionInfo.getCityInfo().getId());
            order.setCpCRegionCityEcode(regionInfo.getCityInfo().getCode());
            order.setCpCRegionCityEname(regionInfo.getCityInfo().getName());
        } else {
            throw new NDSException("调用省<市>区服务异常");
        }
        if (regionInfo.getAreaInfo() != null) {
            order.setCpCRegionAreaId(regionInfo.getAreaInfo().getId());
            order.setCpCRegionAreaEcode(regionInfo.getAreaInfo().getCode());
            order.setCpCRegionAreaEname(regionInfo.getAreaInfo().getName());
        }else{
            //如果区匹配不到那就用 "其他区" 来匹配
            String areaName = "其它区";
            if (log.isDebugEnabled()) {
                log.debug(" 京东转单调 其他区逻辑,areaName:{},cpCRegionCityId:{}",areaName,order.getCpCRegionCityId());
            }
            String areaRedisKey = getRegionKey(RegionType.AREA, areaName, order.getCpCRegionCityId());
            RegionInfo areaInfo = regionService.getObjRedisTemplate().opsForValue().get(areaRedisKey);
            if (areaInfo == null || areaInfo.getId() == null) {
                areaInfo = regionService.selectRegionInfo(areaName, 3, order.getCpCRegionCityId(), RegionType.AREA).getData();
            }
            if (areaInfo != null){
                if (log.isDebugEnabled()) {
                    log.debug(" 京东转单调 其他区逻辑,areaInfo:{}",JSON.toJSONString(areaInfo));
                }
                order.setCpCRegionAreaId(areaInfo.getId());
                order.setCpCRegionAreaEcode(areaInfo.getCode());
                order.setCpCRegionAreaEname(jdUser.getCounty());
            }
        }
        //平台省
        order.setPlatformProvince(jdUser.getProvince());
        //平台市
        order.setPlatformCity(jdUser.getCity());
        //平台区
        order.setPlatformArea(jdUser.getCounty());
        //省市区匹配
        order.setIsMerge(0);
        order.setIsSplit(0);
        order.setQtySplit(0L);
        //是否已打印出库
        //order.setWmsStatus(null);
        order.setIsInterecept(0);
        //是否退款中
        order.setIsInreturning(0);
        order.setSalesmanId(null);
        order.setSalesmanName(null);
        order.setLogisticsCost(new BigDecimal("0"));
        order.setAllSku(null);
        order.setBuyerMessage(jdOrder.getOrderRemark());
        //订单来源
        order.setOrderSource(PlatFormEnum.JINGDONG.toString());
        order.setOrigOrderId(null);
        order.setOrigReturnOrderId(null);
        //是否有赠品 默认0 数据库无字段
        order.setIsHasgift(0);
        //商品数量 明细数量之和
        order.setQtyAll(buildTotalProductNum(orderInfo.getJingdongOrderItems()));
        order.setSkuKindQty(orderInfo.getJingdongOrderItems() == null ?
                BigDecimal.ZERO : new BigDecimal(orderInfo.getJingdongOrderItems().size()));
        order.setSellerMemo(jdOrder.getVenderremark());
        // order.setSendTime(null);
        //预售状态
        // order.setSysPresaleStatus(0);
        //地址
        order.setReceiverAddress(jdUser.getFullAddress());
        //wms撤回状态
        order.setWmsCancelStatus(0);
        //自动审核状态
        order.setAutoAuditStatus(0);
        //实缺标记
//        order.setIsLackstock(0);
        //下单店铺是否代销
       // order.setIsShopCommission(0);
        //是否有工单
        order.setIsHasTicket(0);
        //平台
        order.setPlatform(4);
        if ("京仓订单".equals(jdOrder.getStoreOrder())||"云仓订单".equals(jdOrder.getStoreOrder())) {
            order.setIsJcorder(1);
        }
        //订单类型
        order.setOrderType(1);

        order.setPayTime(jdOrder.getPaymentConfirmTime());
        //先用后付，取创建时间
        if (StringUtils.isNotBlank(payType) && JingdongPayType.PAY_AFTER_USE.equals(payType)) {
            order.setPayTime(jdOrder.getOrderStartTime());
        }

        // 平台状态
        order.setPlatformStatus(orderState);

        //是否插入核销流水
        // order.setIsWriteoff(0);
        order.setOccupyStatus(0);
        order.setOrderDiscount(this.buildOrderDiscount(orderInfo,order));
        //是否组合订单
        boolean hasCombine = this.hasCombineProduct(orderInfo);
        order.setIsCombination(hasCombine ? 1 : 0);
        //2019-11-26 平台优惠金额  优惠劵为平台承担（包含100-店铺优惠中平台承担的金额）
        order.setAmtPlatDiscount(this.buildPlatformDiscountAmt(orderInfo.getJingdongCoupondtaiList(), orderInfo.getJingdongOrderItems()));

        //抖音&快手改造
        String sendPayMap = jdOrder.getSendPayMap();
        if (StringUtils.isNotBlank(sendPayMap)) {
            jingdongPopExtra(jdOrder, order, sendPayMap);
        }

        orderInfo.setOcBOrder(order);
        return order;
    }

    /**
     * 京东pop 抖音&快手改造
     *
     * @param jdOrder
     * @param order
     * @param sendPayMap
     */
    private void jingdongPopExtra(IpBJingdongOrder jdOrder, OcBOrder order, String sendPayMap) {
        String ctpOrderInfo = jdOrder.getCtpOrderInfo();
        Integer sourcePlatform = null;

        //sendpayMap 429=1是抖音 ，429=2 是快手
        JSONObject sendPayObject = JSONObject.parseObject(sendPayMap);
        if (StringUtils.equals("1", sendPayObject.getString("429"))) {
            sourcePlatform = PlatFormEnum.DOU_YIN.getCode();
        } else if (StringUtils.equals("2", sendPayObject.getString("429"))) {
            sourcePlatform = PlatFormEnum.KUAISHOU.getCode();
        }

        if (sourcePlatform != null && StringUtils.isNotBlank(ctpOrderInfo)) {
            JSONObject jsonObject = JSONObject.parseObject(ctpOrderInfo);

            String ctpParentOrderId = jsonObject.getString("ctpParentOrderId");
            String ctpWhere = jsonObject.getString("ctpWhere");
            String ctpName = jsonObject.getString("ctpName");
            String ctpMobile = jsonObject.getString("ctpMobile");

            order.setGwSourceGroup(String.valueOf(sourcePlatform));
            order.setOrderSourcePlatformEcode(StringUtils.isBlank(ctpParentOrderId) ? "" : ctpParentOrderId);
            order.setReceiverAddress(StringUtils.isBlank(ctpWhere) ? "" : ctpWhere);
            order.setReceiverName(StringUtils.isBlank(ctpName) ? "" : ctpName);
            order.setReceiverMobile(StringUtils.isBlank(ctpMobile) ? "" : ctpMobile);
        }
    }

    /**
     * <AUTHOR>
     * @Date 14:14 2021/4/16
     * @Description 订单折扣 = （总金额-配送费用 -服务费）/ 商品金额。
     */
    private  BigDecimal buildOrderDiscount(IpJingdongOrderRelation ipJingdongOrderRelation,OcBOrder order){
        try {
            IpBJingdongOrder jingdongOrder = ipJingdongOrderRelation.getJingdongOrder();
            return (Optional.ofNullable(order.getOrderAmt()).orElse(BigDecimal.ZERO)
                    .subtract(Optional.ofNullable(jingdongOrder.getFreightPrice()).orElse(BigDecimal.ZERO))
                    .divide(Optional.ofNullable(order.getProductAmt()).orElse(BigDecimal.ZERO) , 4, BigDecimal.ROUND_HALF_UP));
        }catch (Exception e){
            log.error("buildOrderDiscount", e);
            return BigDecimal.ZERO;
        }
    }
    /**
     * 解析是否有组合商品
     *
     * @param orderInfo 订单中间表关联对象
     * @return 是否有组合商品。True=包含
     */
    private boolean hasCombineProduct(IpJingdongOrderRelation orderInfo) {
        List<IpBJingdongOrderItemExt> orderItem = orderInfo.getJingdongOrderItems();
        for (IpBJingdongOrderItemExt jingdongOrderItem : orderItem) {
            if (jingdongOrderItem.getProdSku() != null &&
                    (jingdongOrderItem.getProdSku().getSkuType() == SkuType.COMBINE_PRODUCT
                            || jingdongOrderItem.getProdSku().getSkuType() == SkuType.GIFT_PRODUCT)) {
                return true;
            }
        }

        return false;
    }


    /**
     * 设置发票相关信息
     *
     * @param order   订单
     * @param jdOrder 京东订单
     */
    private void setIsInvoiceInfo(OcBOrder order, IpBJingdongOrder jdOrder) {
        String isInvoice = JingdongInvoice.NOTNEEDINVOICE;
        if (StringUtils.isBlank(jdOrder.getInvoiceInfo()) || isInvoice.equals(jdOrder.getInvoiceInfo())) {
            order.setIsInvoice(0);
            order.setInvoiceHeader(null);
            order.setInvoiceContent(isInvoice);
        } else {
            order.setIsInvoice(1);
            //拆解string 发票类型:普通发票;发票抬头:个人;发票内容:明细
            String[] split = jdOrder.getInvoiceInfo().split(";");
            for (String string : split) {
                if (string.contains(JingdongInvoice.INVOICE_HEADER)) {
                    order.setInvoiceHeader(string.substring(string.indexOf(":") + 1));
                }
                if (string.contains(JingdongInvoice.INVOICE_CONTENT)) {
                    order.setInvoiceContent(string.substring(string.indexOf(":") + 1));
                }
            }
        }
    }

    /**
     * BALANCEUSED+ORDER_PAYMENT）+sum（优惠信息表优惠类型为【39-京豆优惠】）+sum(优惠信息表优惠类型为【41-京券优惠】)
     *
     * @param jdOrder        京东主表
     * @param coupondtaiList 优惠表
     * @return 京东结算金额
     */
    private BigDecimal buildJdSettleAmt(IpBJingdongOrder jdOrder, List<IpBJingdongCoupondtai> coupondtaiList) {
        BigDecimal jdSettleAmt;
        if (null == jdOrder.getOrderPayment()) {
            jdOrder.setOrderPayment(new BigDecimal("0"));
        }
        if (jdOrder.getBalanceused() == null) {
            jdOrder.setBalanceused(BigDecimal.ZERO);
        }
        jdSettleAmt = jdOrder.getOrderPayment().add(jdOrder.getBalanceused());
        if (coupondtaiList == null || coupondtaiList.size() == 0) {
            return jdSettleAmt;
        }
        for (IpBJingdongCoupondtai item : coupondtaiList) {
            if (!StringUtils.isBlank(item.getCouponType())) {
                if (JingdongCouponType.THIRTY_NINE_COUPON.equals(item.getCouponType()) || JingdongCouponType.FORTY_ONE_COUPON.equals(item.getCouponType())) {
                    jdSettleAmt = jdSettleAmt.add(item.getCouponPrice());
                }
            }
        }

        return jdSettleAmt;
    }

    private BigDecimal getOrderDiscountAmount(List<IpBJingdongCoupondtai> coupondtaiList) {
        BigDecimal bigTotalDiscount = BigDecimal.ZERO;
        if (coupondtaiList != null && coupondtaiList.size() != 0) {
            for (IpBJingdongCoupondtai item : coupondtaiList) {
                //Sum(优惠信息表中优惠类型为非【30_单品优惠】的“优惠金额”)
                if (!JingdongCouponType.THIRTY_COUPON.equals(item.getCouponType())) {
                    if (null == item.getCouponPrice()) {
                        item.setCouponPrice(new BigDecimal("0"));
                    }
                    bigTotalDiscount = bigTotalDiscount.add(item.getCouponPrice());
                }
            }
        }
        return bigTotalDiscount;
    }

    /**
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @Description 订单优惠金额 优惠信息列表中优惠劵为店铺承担且SKU编码为空的优惠金额
     * 20-套装优惠
     * 28-闪团优惠
     * 29-团购优惠
     * 30-单品促销优惠
     * 34-手机红包
     * 35-满返满送(返现)
     * 100-店铺优惠中店铺承担
     * @Date 2019-11-27
     * @Param [coupondtaiList]
     **/
    private BigDecimal getOrderDiscountAmountNew(List<IpBJingdongCoupondtai> coupondtaiList, List<IpBJingdongOrderItemExt> ipBJingdongOrderItemExtList) {
        BigDecimal orderDiscountAmt = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(coupondtaiList)) {
            for (IpBJingdongCoupondtai ipBJingdongCoupondtai : coupondtaiList) {
                if (JingdongCouponType.TWENTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_NINE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FOUR_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FIVE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.FORTY_ONE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.FIFTY_TWO_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_HUNDRED_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SEVEN_ONE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THREE_SIX_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_TWO_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())) {
                    if (null == ipBJingdongCoupondtai.getSkuId()) {
                        if (null != ipBJingdongCoupondtai.getCouponPrice()) {
                            orderDiscountAmt = orderDiscountAmt.add(ipBJingdongCoupondtai.getCouponPrice());
                        }
                    }
                }
            }
        }

        //100-店铺优惠 扣减平台优惠
        BigDecimal platformDiscountAmt = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(ipBJingdongOrderItemExtList)) {
            for (IpBJingdongOrderItemExt ipBJingdongOrderItemExt : ipBJingdongOrderItemExtList) {
                if (null != ipBJingdongOrderItemExt.getReserveDecimal01()) {
                    platformDiscountAmt = platformDiscountAmt.add(ipBJingdongOrderItemExt.getReserveDecimal01());
                }
            }
        }
        orderDiscountAmt = orderDiscountAmt.subtract(platformDiscountAmt);
        return orderDiscountAmt;
    }


    /**
     * 明细数量之和
     *
     * @param list 京东明细列表
     * @return 商品数量
     */
    private BigDecimal buildTotalProductNum(List<IpBJingdongOrderItemExt> list) {
        Long num = 0L;
        if (list != null && list.size() != 0) {
            for (IpBJingdongOrderItem item : list) {
                Long itemTotal = item.getItemTotal();
                if (itemTotal == null) {
                    item.setItemTotal(0L);
                    itemTotal = 0L;
                }
                num = num + itemTotal;
            }
        }
        return BigDecimal.valueOf(num);
    }

    /**
     * 已收金额
     * 客户实付金额
     * BALANCEUSED+ ORDER_PAYMENT
     *
     * @param jdOrder 京东信息
     * @return 已收金额
     */
    private BigDecimal buildReceivedAmt(IpBJingdongOrder jdOrder) {
        if (null == jdOrder.getOrderPayment()) {
            jdOrder.setOrderPayment(new BigDecimal("0"));
        }
        if (jdOrder.getBalanceused() == null) {
            jdOrder.setBalanceused(BigDecimal.ZERO);
        }
        return jdOrder.getOrderPayment().add(jdOrder.getBalanceused());
    }

    /**
     * Sum
     * (京东订单明细中的商品价格
     * JD_PRICE*数量ITEM_TOTAL)
     *
     * @param list 京东订单明细
     * @return 商品总额
     */
    private BigDecimal buildTotalProductAmt(List<IpBJingdongOrderItemExt> list) {
        BigDecimal total = BigDecimal.ZERO;
        if (list != null && list.size() != 0) {
            for (IpBJingdongOrderItemExt item : list) {
                if (item.getJdPrice() == null) {
                    item.setJdPrice(BigDecimal.ZERO);
                }
                if (item.getItemTotal() == null) {
                    item.setItemTotal(0L);
                }
                total = total.add(item.getJdPrice().multiply(BigDecimal.valueOf(item.getItemTotal())));
            }

        }
        return total;
    }

    /**
     * Sum(优惠信息表中优惠类型为【30_单品优惠】的“优惠金额”)
     *
     * @param coupondtaiList 京东优惠表信息
     * @return 商品优惠金额
     */
    private BigDecimal buildTotalProductDiscount(List<IpBJingdongCoupondtai> coupondtaiList) {
        BigDecimal bigTotalDiscount = BigDecimal.ZERO;
        if (coupondtaiList != null && coupondtaiList.size() != 0) {
            for (IpBJingdongCoupondtai item : coupondtaiList) {
                //Sum(优惠信息表中优惠类型为【30_单品优惠】的“优惠金额”)
                if (JingdongCouponType.THIRTY_COUPON.equals(item.getCouponType())) {
                    if (null == item.getCouponPrice()) {
                        item.setCouponPrice(new BigDecimal("0"));
                    }
                    bigTotalDiscount = bigTotalDiscount.add(item.getCouponPrice());
                }
            }
        }
        return bigTotalDiscount;
    }

    /**
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @Description 主表商品优惠金额  = 明细商品优惠金额之和 = 优惠表中skuid不为空的
     * @Date 2019-11-27
     * @Param [coupondtaiList]
     **/
    private BigDecimal buildTotalProductDiscountNew(List<IpBJingdongCoupondtai> coupondtaiList) {
        BigDecimal totalProductDiscountAmt = BigDecimal.ZERO;

        if (CollectionUtils.isNotEmpty(coupondtaiList)) {
            for (IpBJingdongCoupondtai ipBJingdongCoupondtai : coupondtaiList) {
                if (JingdongCouponType.TWENTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.TWENTY_NINE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FOUR_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THIRTY_FIVE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_HUNDRED_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SEVEN_ONE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.THREE_SIX_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_TWO_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.ONE_SIX_EIGHT_COUPON.equals(ipBJingdongCoupondtai.getCouponType())) {
                    if (null != ipBJingdongCoupondtai.getSkuId()) {
                        if (null != ipBJingdongCoupondtai.getCouponPrice()) {
                            totalProductDiscountAmt = totalProductDiscountAmt.add(ipBJingdongCoupondtai.getCouponPrice());
                        }
                    }
                }
            }
        }
        return totalProductDiscountAmt;
    }

    /**
     * 是否需要转换成大写
     * 乔丹项目中：SAP系统存储的SKU部分有小写。为了统一，库里存储的全部为大写。因此在转单的时候强制转换成大写。
     *
     * @return true
     */
    private boolean checkIsNeedTransferSkuUpperCase() {
        try {
            String value = propertiesConf.getProperty("r3.oc.oms.transfer.sku.toupper", "true");
            return StringUtils.equalsIgnoreCase(value, "true");
        } catch (Exception ex) {
            log.error("checkIsNeedTransferSkuUpperCase", ex);
            return true;
        }
    }

    /**
     * @return java.math.BigDecimal
     * <AUTHOR>
     * @Description 平台优惠金额 39-京豆优惠  41-京东券优惠 52-礼品卡优惠  100-店铺优惠中平台承担的在分摊后再加回去
     * @Date 2019-11-27
     * @Param [jdOrder, ipBJingdongOrderItemExt]
     **/
    private BigDecimal buildPlatformDiscountAmt(List<IpBJingdongCoupondtai> coupondtaiList, List<IpBJingdongOrderItemExt> ipBJingdongOrderItemExt) {
        BigDecimal discountAmt = BigDecimal.ZERO;
        //39-京豆优惠  41-京东券优惠 52-礼品卡优惠
        if (CollectionUtils.isNotEmpty(coupondtaiList)) {
            for (IpBJingdongCoupondtai ipBJingdongCoupondtai : coupondtaiList) {
                if (JingdongCouponType.THIRTY_NINE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.FORTY_ONE_COUPON.equals(ipBJingdongCoupondtai.getCouponType())
                        || JingdongCouponType.FIFTY_TWO_COUPON.equals(ipBJingdongCoupondtai.getCouponType())) {
                    if (null != ipBJingdongCoupondtai.getCouponPrice()) {
                        discountAmt = discountAmt.add(ipBJingdongCoupondtai.getCouponPrice());
                    }
                }
            }
        }
        return discountAmt;
    }


}

