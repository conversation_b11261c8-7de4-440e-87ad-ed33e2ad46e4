package com.jackrain.nea.oc.oms.sap;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.task.OcBSapSalesDataRecordAddTaskMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.task.OcBSapSalesDataRecordAddTask;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.oc.oms.util.RedisMasterUtils;
import com.jackrain.nea.redis.tlock.RedisReentrantLock;
import com.jackrain.nea.resource.BllRedisKeyResources;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.BllRedisLockOrderUtil;
import com.jackrain.nea.utils.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> wangbinyu
 * @since : 2022/9/20
 * description :
 */
@Slf4j
@Component
public class OcBSapSalesDataRecordAddTaskService {
    @Autowired
    private OcBSapSalesDataRecordAddTaskMapper taskMapper;
    @Autowired
    private OcBSapSalesDataRecordService sapSalesDataRecordService;
    @Autowired
    private BllRedisLockOrderUtil redisUtil;
    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;
    @Autowired
    private OcBOrderMapper orderMapper;

    public List<Long> selectOcBSapSalesDataRecordAddTaskSqlList(int limit, String taskTableName) {
        return taskMapper.selectOcBSapSalesDataRecordAddTaskSqlList(limit, taskTableName);
    }

    public void addByTask(List<Long> ids) {
        User sysUser = SystemUserResource.getRootUser();
        for (Long id : ids) {
            doAdd(id, sysUser);
        }
    }

    public ValueHolderV14 doAdd(Long id, User user) {
        if (log.isDebugEnabled()) {
            log.debug("Start OcBSapSalesDataRecordAddTaskService.doAdd id={}", id);
        }
        OcBSapSalesDataRecordAddTask task = taskMapper.selectById(id);
        if (task == null) {
            return ValueHolderV14Utils.getFailValueHolder("当前记录已不存在");
        }
        String lockRedisKey = BllRedisKeyResources.buildSapSalesDataRecordAddTaskRedisLockKey(task.getOrderId(), task.getBillType());
        RedisReentrantLock redisLock = RedisMasterUtils.getReentrantLock(lockRedisKey);
        try {
            AssertUtil.assertException(!redisLock.tryLock(redisUtil.getLockOrderTimeOut(), TimeUnit.MILLISECONDS), "当前订单正在被操作，请稍后再试");
        } catch (Exception e) {
            log.error("redis try lock error", e);
            return ValueHolderV14Utils.getFailValueHolder(e.getMessage());
        }
        try {
            task = taskMapper.selectById(id);
            if(task.getStatus() == null || task.getStatus() != 0){
                return ValueHolderV14Utils.getFailValueHolder("当前状态不允许处理！");
            }
            // 更新为处理中
            taskMapper.updateStatus(id, 1, Long.valueOf(user.getId()), user.getName(), user.getEname());
            boolean result = sapSalesDataRecordService.saveOrder(task.getOrderId(), task.getBillType(), user);
            if (result) {
                // 更新为处理成功
                taskMapper.updateStatus(id, 2, Long.valueOf(user.getId()), user.getName(), user.getEname());
            } else {
                // 重置处理状态,延迟10分钟
                taskMapper.initStatus(id, Date.from(LocalDateTime.now()
                        .plusMinutes(10L)
                        .atZone(ZoneId.systemDefault()).toInstant()), Long.valueOf(user.getId()), user.getName(), user.getEname());
                return ValueHolderV14Utils.getFailValueHolder("处理失败");
            }
        }catch (Exception e){
            return ValueHolderV14Utils.getFailValueHolder("处理失败" + e.getMessage());
        }finally {
            redisLock.unlock();
        }
        return ValueHolderV14Utils.getSuccessValueHolder("处理成功");
    }

    public void addTask(int billType, Long orderId, User user) {
        try {
            OcBSapSalesDataRecordAddTask task = new OcBSapSalesDataRecordAddTask();
            Date now = new Date();
            task.setId(ModelUtil.getSequence("oc_b_sap_sales_data_record_add_task"));
            task.setBillType(billType);
            task.setOrderId(orderId);
            task.setStatus(0);
            task.setNextTime(now);
            task.setRetryNumber(0);
            task.setOwnerid(Long.valueOf(user.getId()));
            task.setOwnername(user.getName());
            task.setOwnerename(user.getEname());
            task.setCreationdate(now);
            task.setModifierid(Long.valueOf(user.getId()));
            task.setModifiername(user.getName());
            task.setModifierename(user.getEname());
            task.setModifieddate(now);
            task.setAdClientId(37L);
            task.setAdOrgId(27L);
            taskMapper.insert(task);
        }catch (Exception e){
            log.error("OcBSapSalesDataRecordAddTaskService.addTask.error", e);
        }
    }

    /**
     * 前端弹框新增任务
     * @param obj 参数
     * @param user 用户
     * @return return
     */
    public ValueHolderV14 save(JSONObject obj, User user){
        int billType = obj.getIntValue("billType");
        String billNo = obj.getString("billNo");
        Long orderId = obj.getLong("orderId");
        if(billType != 4 && StringUtils.isBlank(billNo)){
            return ValueHolderV14Utils.getFailValueHolder("单据编号不能为空！");
        }
        if(billType == 0){
            OcBOrder order = null;
            orderId = ES4Order.getIdByBillNo(billNo);
            if(orderId != null){
                order = orderMapper.selectById(orderId);
            }
            if(order == null){
                return ValueHolderV14Utils.getFailValueHolder("订单不存在！");
            }
            // 校验订单状态
            if(!OmsOrderStatus.WAREHOUSE_DELIVERY.toInteger().equals(order.getOrderStatus())
                    && !OmsOrderStatus.PLATFORM_DELIVERY.toInteger().equals(order.getOrderStatus())){
                return ValueHolderV14Utils.getFailValueHolder("订单状态不是仓库发货或平台发货，不允许操作！");
            }
        } else if(billType == 1){
            OcBReturnOrder returnOrder = returnOrderMapper.selectByBillNo(billNo);
            if(returnOrder == null){
                return ValueHolderV14Utils.getFailValueHolder("退换货单不存在！");
            }
            // 校验退单状态
            if (!ReturnStatusEnum.WAIT_AFTERSALE_ENSURE.getVal().equals(returnOrder.getReturnStatus())) {
                return ValueHolderV14Utils.getFailValueHolder("退单状态不是等待售后确认，不允许操作！");
            }
            orderId = returnOrder.getId();
        } else if(billType == 4){
            // 库存调整单
            if(orderId == null){
                return ValueHolderV14Utils.getFailValueHolder("单据ID不能为空！");
            }
        }else {
            return ValueHolderV14Utils.getFailValueHolder("单据类型暂不支持！");
        }
        addTask(billType, orderId, user);
        return ValueHolderV14Utils.getSuccessValueHolder("订单编号[" + orderId + "]已插入任务表！");
    }
}
