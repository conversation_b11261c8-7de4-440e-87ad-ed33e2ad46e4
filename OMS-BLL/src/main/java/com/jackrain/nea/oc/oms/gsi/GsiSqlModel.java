package com.jackrain.nea.oc.oms.gsi;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * @Desc : GSI DDM
 * <AUTHOR> xiWen
 * @Date : 2020/11/25
 */
@Data
@Accessors(chain = true)
public class GsiSqlModel {

    private String columns;

    private String table;

    private Node<String, Object> where;

    private Node<Integer, Integer> limit;

    private Node<String, String> order;

    private String statement;


    /**
     * @return sql
     */
    public String gsiSqlStatement() {

        StringBuilder sb = new StringBuilder();
        sb.append("SELECT ").append(columns == null ? "*" : columns).append(" FROM ").append(table).append(" WHERE ").append(where.getKey())
                .append(" = ").append(where.getValue());
        if (order != null) {
            sb.append(" ORDER BY ").append(order.getKey()).append(" ").append(order.getValue());
        }
        if (limit != null) {
            sb.append(" LIMIT ").append(limit.getKey()).append(",").append(limit.getValue());
        }
        statement = sb.toString();
        return statement;
    }

    public GsiSqlModel limit(Integer... var) {

        if (var.length > 1) {
            limit = new Node<>(var[0], var[1]);
        } else {
            limit = new Node<>(0, var[0]);
        }
        return this;
    }

    public GsiSqlModel where(String k, Object v) {
        where = new Node<>(k, v);
        return this;
    }

    public GsiSqlModel order(String k, boolean v) {
        order = new Node<>(k, v ? "ASC " : "DESC ");
        return this;
    }

    /**
     * 内部.节点
     *
     * @param <K>
     * @param <V>
     */
    static class Node<K, V> implements Map.Entry<K, V> {

        K key;
        V value;

        Node(K key, V value) {
            this.key = key;
            this.value = value;
        }

        @Override
        public K getKey() {
            return key;
        }

        @Override
        public V getValue() {
            return value;
        }

        @Override
        public V setValue(V val) {
            value = val;
            return val;
        }

        public void setNode(K k, V v) {
            key = k;
            value = v;
        }


    }

}
