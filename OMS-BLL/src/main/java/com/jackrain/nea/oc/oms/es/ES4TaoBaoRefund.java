package com.jackrain.nea.oc.oms.es;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.es.util.ElasticSearchUtil;
import com.jackrain.nea.oc.oms.model.enums.TransferOrderStatus;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/5 9:35 上午
 */
public class ES4TaoBaoRefund {

    /**
     * 退单补偿服务 根据ip_b_taobao_refund的未转化状态查询分库键refund_id（平台退单id）
     *
     * @param pageIndex 起始页
     * @param pageSize  每页显示条数
     * @return 平台退单id
     */
    public static List<String> selectRefundOrderByRefundId(int pageIndex, int pageSize) {
        List<String> list = new ArrayList<>();
        int startIndex = pageIndex * pageSize;
        if (startIndex < 0) {
            startIndex = 0;
        }
        JSONObject orderKes = new JSONObject();
        orderKes.put("name", "ID");
        orderKes.put("asc", false);
        JSONArray orderKeys = new JSONArray();
        orderKeys.add(orderKes);

        JSONObject whereKeys = new JSONObject();
        String[] returnFields = {"REFUND_ID"};
        whereKeys.put("ISTRANS", TransferOrderStatus.NOT_TRANSFER.toInteger());
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOREFUND,
                TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOREFUND, whereKeys, null,
                orderKeys, pageSize, startIndex, returnFields);
        if (search.containsKey("rowcount") && search.getInteger("rowcount") > 0) {
            JSONArray arrayObj = search.getJSONArray("data");
            for (Object obj : arrayObj) {
                JSONObject jsonObject = (JSONObject) obj;
                list.add(jsonObject.getString("REFUND_ID"));
            }
        }
        return list;
    }

    /**
     * 根据子订单编号(明细编号oid)查询平台退单id(refund_id)
     *
     * @param oid 子订单编号
     * @return 平台退单id
     */
    public static String findRefundIdByOid(Object oid) {
        String[] returnFields = {"REFUND_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("OID", oid);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOREFUND,
                TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOREFUND, whereKeys, null, null, 10, 0, returnFields);
        JSONArray returnData = search.getJSONArray("data");
        return returnData.getJSONObject(0).getString("REFUND_ID");
    }

    /**
     * 根据传AG失败状态查询 refundId
     *
     * @param pageSize 每页显示条数
     * @return List refundId
     */
    public static List<String> findRefundIdByToAgStatus(Integer pageSize) {
        List<String> returnId = new ArrayList<>();
        String[] returnFields = {"REFUND_ID"};
        JSONObject whereKeys = new JSONObject();
        whereKeys.put("RESERVE_BIGINT10", 1);
        JSONObject search = ElasticSearchUtil.search(TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOREFUND,
                TaobaoReturnOrderExt.TABLENAME_IPBTAOBAOREFUND, whereKeys, null, null, pageSize, 0, returnFields);

        JSONArray returnData = search.getJSONArray("data");
        if (CollectionUtils.isNotEmpty(returnData)){
            List<HashMap> orderIdHmp = JSONArray.parseArray(returnData.toJSONString(), HashMap.class);
            for (HashMap hashMap : orderIdHmp) {
                returnId.add((String) hashMap.get("REFUND_ID"));
            }
        }
        return returnId;
    }

}
