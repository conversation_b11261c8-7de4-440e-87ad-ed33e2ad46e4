package com.jackrain.nea.oc.oms.matcher.parser;

import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.enums.ChannelType;
import com.jackrain.nea.util.ApplicationContextHandle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Description： 单据信息提取器获取工厂类
 * Author: RESET
 * Date: Created in 2020/6/15 20:03
 * Modified By:
 */
@Component
public class LiveMatchInfoParserFactory {

    // 暂存
    private final Map<ChannelType, ILiveMatchInfoParser> matcherMap = new ConcurrentHashMap<>();

    /**
     * 注册
     *
     * @param matchers
     */
    @Autowired
    public void init(List<ILiveMatchInfoParser> matchers) {
        if (!CollectionUtils.isEmpty(matchers)) {
            matchers.forEach(m -> {
                matcherMap.put(m.getCurrentChannelType(), m);
            });
        }
    }

    /**
     * 按类型获取解析器
     *
     * @param channel
     * @return
     */
    public ILiveMatchInfoParser getParser(ChannelType channel) {
        ILiveMatchInfoParser matcher = matcherMap.get(channel);

        if (Objects.isNull(matcher)) {
            throw new NDSException("获取解析器出错，该渠道无对应的解析器[" + (Objects.isNull(channel) ? null : channel.toInteger()) + "]");
        }

        return matcher;
    }

    public static LiveMatchInfoParserFactory getInstance() {
        return ApplicationContextHandle.getBean(LiveMatchInfoParserFactory.class);
    }

}
