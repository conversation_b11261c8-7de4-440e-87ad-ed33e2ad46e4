package com.jackrain.nea.oc.oms.mapper.ac;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.List;

@Mapper
public interface AcFOrderInvoiceMapper extends ExtentionMapper<AcFOrderInvoice> {
    /**
     * 根据tid查询未红冲的蓝票
     * @param tid
     * @return
     */
    @Select("select * from ac_f_order_invoice where tid = #{tid} and red_rush_status = '0' and ticket_type = '0' and cancel_status = '0'")
    List<AcFOrderInvoice> queryByTid(@Param("tid") String tid);

    /**
     * 查询 已审核,未冻结,未开票,未取消的订单发票
     * @return
     */
    @Select("select * from ac_f_order_invoice where audit_status = '1' and freeze_status = '0' and invoice_status = '0' and cancel_status = '0' limit #{pageSize}")
    List<AcFOrderInvoice> queryByInvoice(@Param("pageSize") int pageSize);

    /**
     * 根据tid模糊查询 未取消,未红冲的蓝票
     * @param tid
     * @return
     */
    @Select("select * from ac_f_order_invoice where ticket_type = '0' and cancel_status = '0' and red_rush_status = '0' and tid like CONCAT('%',#{0},'%')")
    List<AcFOrderInvoice> selectLikeTid(String tid);

    /**
     * 根据id查询 蓝票,未取消,未红冲,红冲中
     * @param orderInvoiceIdList
     * @return
     */
    @Select("<script>" +
            "select count(*) from ac_f_order_invoice where cancel_status = '0' and ticket_type = '0' and red_rush_status in ('0','2') and isactive = 'Y' and id in " +
            "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    long countBlueByIds(@Param("ids") List<Long> orderInvoiceIdList);

    /**
     * 查询未取消,未红冲的蓝票
     * @param orderInvoiceIdList
     * @return
     */
    @Select("<script>" +
            "select * from ac_f_order_invoice where cancel_status = '0' and ticket_type = '0' and red_rush_status = '0' and id in " +
            "<foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'> #{item} </foreach>" +
            "</script>")
    List<AcFOrderInvoice> selectBlueByIds(@Param("ids") List<Long> orderInvoiceIdList);

    @SelectProvider(type = AcFOrderInvoiceMapper.AcFOrderInvoiceSql.class, method = "selectByNodeSql")
    List<AcFOrderInvoice> selectTaskObjectList(@Param("limit") int limit, @Param("taskTableName") String taskTableName);

    @SelectProvider(type = AcFOrderInvoiceMapper.AcFOrderInvoiceSql.class, method = "selectInInvoiceSql")
    List<AcFOrderInvoice> selectInInvoiceList(@Param("limit") int limit, @Param("taskTableName") String taskTableName);

    @Slf4j
    class AcFOrderInvoiceSql {
        public String selectByNodeSql(@Param("taskTableName") String taskTableName,
                                      @Param("limit") int limit) {
            StringBuffer sql = new StringBuffer();
            StringBuffer limitStr = new StringBuffer(" LIMIT ");
            limitStr.append(limit);

            sql.append("select * from ")
                    .append(taskTableName)
                    .append(" where audit_status = '1' and freeze_status = '0' and invoice_status = '0' and cancel_status = '0' ")
                    .append(limitStr);
            return sql.toString();
        }

        public String selectInInvoiceSql(@Param("taskTableName") String taskTableName,
                                         @Param("limit") int limit) {
            StringBuffer sql = new StringBuffer();
            StringBuffer limitStr = new StringBuffer(" LIMIT ");
            limitStr.append(limit);
            sql.append("select * from ")
                    .append(taskTableName)
                    .append(" where invoice_status = '1' ")
                    .append(limitStr);
            return sql.toString();
        }
    }

    /**
     * 根据INVOICE_APPLY_ID查询发票单
     * @param invoiceApplyId
     * @return
     */
    @Select("select * from ac_f_order_invoice where invoice_apply_id = #{invoiceApplyId}")
    List<AcFOrderInvoice> selectByInvoiceApplyId(@Param("invoiceApplyId") Long invoiceApplyId);
}