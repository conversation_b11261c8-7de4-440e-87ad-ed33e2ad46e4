package com.jackrain.nea.oc.oms.services.patrol;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.es.ES4Order;
import com.jackrain.nea.oc.oms.gsi.GSI4Order;
import com.jackrain.nea.oc.oms.mapper.*;
import com.jackrain.nea.oc.oms.model.jingdong.JingdongCouponType;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.services.OmsOrderItemService;
import com.jackrain.nea.oc.oms.services.OmsOrderSplitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 黄世新
 * @Date: 2019/9/18 1:54 下午
 * @Version 1.0
 * 京东优惠金额数据修护
 */
@Slf4j
@Component
public class JDDiscountMoneyService {

    @Autowired
    private IpBJingdongCoupondtaiTmbMapper ipBJingdongCoupondtaiTmbMapper;
    @Autowired
    private IpBJingdongOrderMapper ipBJingdongOrderMapper;
    @Autowired
    private IpBJingdongOrderItemMapper ipBJingdongOrderItemMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private OmsOrderItemService omsOrderItemService;
    @Autowired
    private OmsOrderSplitService omsOrderSplitService;

    /**
     * @param platformNum 平台单号
     */
    public List<Long> discountMoney(Long platformNum) {
        List<JSONObject> jsonObjects = null;
        if (platformNum != null) {
            jsonObjects = ipBJingdongCoupondtaiTmbMapper.selectIpBJingdongCoupondtaiTmbList(platformNum);
        } else {
            jsonObjects = ipBJingdongCoupondtaiTmbMapper.selectIpBJingdongCoupondtai();
        }
        List<Long> longs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jsonObjects)) {
            for (JSONObject jsonObject : jsonObjects) {
                Long aLong = this.updateOrder(jsonObject);
                if (aLong != null) {
                    longs.add(aLong);
                }
            }
        }
        return longs;
    }

    /**
     * 临时数据的详细信息
     *
     * @param jsonObject
     */
    private Long updateOrder(JSONObject jsonObject) {
        Long sku_id = jsonObject.getLong("sku_id");
        String coupon_type = jsonObject.getString("coupon_type");
        if (sku_id != null) {
            Long order_id = jsonObject.getLong("order_id");

            //通过平台单号在es上获取订单id 过滤作废的订单
        //    List<Long> esIdList = ES4Order.getIdsBySourceCode(order_id);
            if (order_id == null){
                return null;
            }
            List<Long> esIdList = GSI4Order.getIdListBySourceCode(String.valueOf(order_id));

            if (CollectionUtils.isEmpty(esIdList)) {
                return null;
            }
            //查询订单过滤掉取消作废的订单信息
            List<OcBOrder> ocBOrders = ocBOrderMapper.selectListByIdsToVoid(esIdList);
            if (CollectionUtils.isEmpty(ocBOrders)) {
                return null;
            }
            //ooid赋值为skuid 通过ooid加订单单号确定唯一的明细信息
            OcBOrderItem ocBOrderItem = null;
            for (OcBOrder ocBOrder : ocBOrders) {
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectByOrderIdAndOOId(ocBOrder.getId(), sku_id + "");
                if (CollectionUtils.isEmpty(orderItems)) {
                    continue;
                }
                ocBOrderItem = orderItems.get(0);
                break;
            }
            if (ocBOrderItem == null) {
                return null;
            }
            if (StringUtils.isNotEmpty(coupon_type) && JingdongCouponType.THIRTY_COUPON.equals(coupon_type)) {
                BigDecimal coupon_price = jsonObject.getBigDecimal("coupon_price");
                Long ocBOrderId = ocBOrderItem.getOcBOrderId();
                OcBOrderItem item = new OcBOrderItem();
                item.setId(ocBOrderItem.getId());
                item.setOcBOrderId(ocBOrderId);
                item.setAmtDiscount(coupon_price);
                //重新计算成交价格和成交金额
                BigDecimal qty = ocBOrderItem.getQty(); //数量
                BigDecimal priceList = ocBOrderItem.getPriceList(); //吊牌价
                BigDecimal orderSplitAmt = ocBOrderItem.getOrderSplitAmt();
                BigDecimal price = (priceList.multiply(qty).subtract(coupon_price)).divide(qty, 2, BigDecimal.ROUND_HALF_UP);
                item.setPrice(price);
                BigDecimal realAmt = price.multiply(qty).subtract(orderSplitAmt);
                item.setRealAmt(realAmt);
                omsOrderItemService.updateOcBOrderItem(item, ocBOrderId);
                OcBOrder ocBOrder = ocBOrderMapper.selectById(ocBOrderId);
                List<OcBOrderItem> orderItems = ocBOrderItemMapper.selectOrderItemList(ocBOrderId);
                omsOrderSplitService.reAmount(ocBOrder, orderItems, false);
                ipBJingdongCoupondtaiTmbMapper.updateIpBJingdongCoupondtaiTmb(ocBOrderId, order_id, sku_id);
                return ocBOrderId;
            }
        }
        return null;
    }
}



