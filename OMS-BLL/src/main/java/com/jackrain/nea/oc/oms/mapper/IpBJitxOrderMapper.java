package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBJitxOrder;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.jdbc.SQL;

import java.util.List;

@Mapper
public interface IpBJitxOrderMapper extends ExtentionMapper<IpBJitxOrder> {

    /**
     * REMARK最长数量。默认200字符
     */
    int MAX_REMARK_LENGTH = 200;

    /**
     * 依据OrderSn进行查询订单数据
     *
     * @param orderNo 订单数据。平台订单数据
     * @return 订单数据
     */
    @Select("SELECT * FROM ip_b_jitx_order WHERE order_sn=#{orderNo} LIMIT 1")
    IpBJitxOrder selectJitxOrderByOrderSn(@Param("orderNo") String orderNo);

    /**
     * 批量更新中间表转换状态
     *
     * @param orderNoList 单号集合
     * @param isTrans     转换状态
     * @param name        操作用户
     * @param ename
     * @param id
     * @param remark      备注
     * @return
     */
    @Update("<script> "
            + "UPDATE ip_b_jitx_order SET istrans = #{isTrans} ,sysremark = #{remark} ,modifierid =#{id}"
            + ",modifierename = #{ename} ,modifiername =#{name} ,modifieddate = NOW() where order_sn in "
            + "<foreach item='orderNo' index='index' collection='orderNoList' open='(' separator=',' "
            + "close=')'> #{orderNo} </foreach>"
            + "</script>")
    int batchUpdateOrderIsTrans(@Param("orderNoList") List<String> orderNoList, @Param("isTrans") int isTrans,
                                @Param("name") String name, @Param("ename") String ename, @Param("id") long id,
                                @Param("remark") String remark);

    /**
     * 批量查询中间表信息
     *
     * @param orderNoList 订单号集合
     * @param isTrans     转换状态
     * @return 订单信息
     */
    @Select("<script>"
            + "SELECT * FROM ip_b_jitx_order WHERE istrans = #{isTrans} and order_sn in"
            + "<foreach item='orderNo' index='index' collection='orderNoList' open='(' separator=',' "
            + "close=')'> #{orderNo} </foreach>"
            + "</script>")
    List<IpBJitxOrder> batchSelectJitxOrder(@Param("orderNoList") List<String> orderNoList,
                                            @Param("isTrans") int isTrans);


    /**
     * 订单SQL创建器
     */
    class JitxOrderSqlBuilder {
        /**
         * 创建更新订单转换状态SQL
         *
         * @param orderNo          订单编号
         * @param isTrans          转换状态
         * @param isUpdateTransNum 是否更新转换数量
         * @param remarks          转换备注信息
         * @return 更新SQL语句
         */
        public String buildUpdateOrderTransSQL(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                                               @Param("isUpdateTransNum") boolean isUpdateTransNum,
                                               @Param("remarks") String remarks) {

            return new SQL() {
                {
                    UPDATE("ip_b_jitx_order");
                    SET("istrans=#{isTrans}");
                    if (isUpdateTransNum) {
                        SET("trans_count = IFNULL(trans_count, 0) + 1");
                        SET("transdate = SYSDATE()");
                    }
                    SET("sysremark=#{remarks}");
                    WHERE("order_sn=#{orderNo}");

                }
            }.toString();
        }
    }

    /**
     * 更新订单转换状态
     *
     * @param orderNo          订单编号
     * @param isTrans          转换状态
     * @param isUpdateTransNum 是否更新转换数量
     * @param remarks          转换备注信息
     * @return 更新结果
     */
    @UpdateProvider(type = JitxOrderSqlBuilder.class, method = "buildUpdateOrderTransSQL")
    int updateOrderIsTrans(@Param("orderNo") String orderNo, @Param("isTrans") int isTrans,
                           @Param("isUpdateTransNum") boolean isUpdateTransNum, @Param("remarks") String remarks);


    /**
     * @param orderNo          原始单号
     * @param changeAddrStatus 地址更新状态
     * @return
     */
    @Update("update ip_b_jitx_order set CHANGE_ADDR_STATUS = #{changeAddrStatus} WHERE order_sn=#{orderNo}")
    int updateChangeAddrStatus(@Param("orderNo") String orderNo, @Param("changeAddrStatus") int changeAddrStatus);

    @Select("SELECT count(*) FROM ip_b_jitx_order WHERE order_sn=#{orderNo}")
    int countJitxOrderByOrderSn(@Param("orderNo") String orderNo);
}