package com.jackrain.nea.oc.oms.services.delivery.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.config.annotation.Reference;
import com.alibaba.fastjson.JSON;
import com.jackrain.nea.cpext.model.table.CpCPlatform;
import com.jackrain.nea.hub.api.naika.NaiKaOrderCmd;
import com.jackrain.nea.hub.request.naika.NaiKaOrderDeliveryItemRequest;
import com.jackrain.nea.hub.request.naika.NaiKaOrderDeliveryRequest;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.oc.oms.services.OmsOrderService;
import com.jackrain.nea.oc.oms.services.OrderPlatformDeliveryService;
import com.jackrain.nea.oc.oms.services.delivery.OrderDeliveryCmd;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName OrderDeliveryNaiKaImpl
 * @Description 奶卡发货
 * <AUTHOR>
 * @Date 2022/6/28 21:09
 * @Version 1.0
 */
@Slf4j
@Component
public class OrderDeliveryNaiKaImpl implements OrderDeliveryCmd {

    @Reference(group = "hub", version = "1.0")
    private NaiKaOrderCmd naiKaOrderCmd;

    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private OmsOrderLogService orderLogService;
    @Autowired
    private OmsOrderService omsOrderService;
    @Autowired
    private OmsOrderLogService omsOrderLogService;
    @Autowired
    private OrderPlatformDeliveryService orderPlatformDeliveryService;
    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;
    @Autowired
    private CpRpcService cpRpcService;


    private static List<String> getOoidsBySkuId(Map<String, Long> map, Long value) {
        List<String> ooids = new ArrayList<>();
        for (String getKey : map.keySet()) {
            if (map.get(getKey).equals(value)) {
                ooids.add(getKey);
            }
        }
        return ooids;
    }

    private static String getOoidBySkuId(Map<String, Long> map, Long value) {
        String key = null;
        for (String getKey : map.keySet()) {
            if (map.get(getKey).equals(value)) {
                key = getKey;
                break;
            }
        }
        map.remove(key);
        return key;
    }

    @Override
    public boolean deliveryDeal(OcBOrderRelation ocBOrderRelation, List<String> tips) {
        OcBOrder ocBOrder = ocBOrderRelation.getOrderInfo();
        log.info(LogUtil.format("Start 奶卡订单发货 ocBOrderRelation:{}", "奶卡订单发货"), JSON.toJSONString(ocBOrderRelation));

        User user = SystemUserResource.getRootUser();
        // 判断订单类型 周期购订单无需回传
        String businessTypeCode = ocBOrder.getBusinessTypeCode();
        if (ObjectUtil.isNotEmpty(businessTypeCode)
                && (ObjectUtil.equals(businessTypeCode, OrderBusinessTypeCodeEnum.CYCLE_ORDER.getCode())
                || ObjectUtil.equals(businessTypeCode, OrderBusinessTypeCodeEnum.FREE_CYCLE_ORDER.getCode())
                || ObjectUtil.equals(businessTypeCode, OrderBusinessTypeCodeEnum.VIRTUAL_MILK_CARD.getCode()))) {
            String logMsg = "虚拟订单" + ocBOrder.getId() + "(平台单号=" + ocBOrder.getTid() + ")无需调用平台发货";
            omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, null, ocBOrder.getForceSendFailReason(), user);
            OcBOrder ocBOrderFlag = new OcBOrder();
            ocBOrderFlag.setId(ocBOrderRelation.getOrderInfo().getId());
            ocBOrderFlag.setIsForce(1L);
            ocBOrderFlag.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            omsOrderService.updateOrderInfo(ocBOrderFlag);
            List<OcBOrderItem> ocBOrderItemList = ocBOrderItemMapper.selectOrderItems(ocBOrder.getId());
            // 调整明细状态
            for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                OcBOrderItem updateOcBOrderItem = new OcBOrderItem();
                updateOcBOrderItem.setId(ocBOrderItem.getId());
                updateOcBOrderItem.setOcBOrderId(ocBOrderItem.getOcBOrderId());
                updateOcBOrderItem.setIsSendout(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
                updateOcBOrderItem.setModifieddate(new Date());
                ocBOrderItemMapper.updateById(updateOcBOrderItem);
            }
            return true;
        }
        CpCPlatform cpCPlatform = cpRpcService.selectCpcPlatformById(Long.valueOf(ocBOrder.getPlatform()));
        Long orderId = ocBOrderRelation.getOrderId();
        String billNo = ocBOrder.getBillNo();

        //判断是否为手工单，如果为手工单直接标记平台发货
        if ("手工新增".equals(ocBOrderRelation.getOrderInfo().getOrderSource())) {
            //更新发货状态，插入日志
            String logMsg = "OrderId=" + orderId + "为手工新增单，直接标记平台发货";
            orderLogService.addUserOrderLog(orderId, billNo, OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, "", null, null);
            OcBOrder update = new OcBOrder();
            update.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
            update.setId(orderId);
            ocBOrderMapper.updateById(update);
            return true;
        }

        // 需要考虑是否是合单的发货
        List<OcBOrderItem> orderItemList = ocBOrderRelation.getOrderItemList();
        //ooid集合
        orderItemList = orderItemList.stream().filter(s -> StringUtils.isNotEmpty(s.getOoid()) && !OcBorderListEnums.YesOrNoEnum.IS_YES.getVal().equals(s.getIsSendout())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderItemList)) {
            return orderPlatformDeliveryService.updateOrderAfterPlatDeliverySuccess(ocBOrder);
        }
        // 根据tid拆分
        Map<String, List<OcBOrderItem>> collect = orderItemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));

        for (String tid : collect.keySet()) {
            List<OcBOrderItem> ocBOrderItems = collect.get(tid);
            Map<String, Long> ooidAndSkuIdMap = new HashMap<>();
            Map<String, OcBOrderItem> ocBOrderItemMap = new HashMap<>();
            for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                ooidAndSkuIdMap.put(ocBOrderItem.getOoid(), ocBOrderItem.getPsCSkuId());
                ocBOrderItemMap.put(ocBOrderItem.getOcBOrderId() + ocBOrderItem.getPsCSkuId() + ocBOrderItem.getOoid(), ocBOrderItem);
            }
            // 获取物流信息
            Map<String, List<OcBOrderDelivery>> orderDeliveryGroup = orderPlatformDeliveryService.getExpressCodeFromOrderDelivery(ocBOrder, ooidAndSkuIdMap.values());
            for (String logisticsNumber : orderDeliveryGroup.keySet()) {
                List<OcBOrderItem> ocBOrderItemList = new ArrayList<>();
                List<OcBOrderDelivery> ocBOrderDeliveryList = orderDeliveryGroup.get(logisticsNumber);
                OcBOrderDelivery delivery = ocBOrderDeliveryList.get(0);
                NaiKaOrderDeliveryRequest naiKaOrderDeliveryRequest = new NaiKaOrderDeliveryRequest();
                naiKaOrderDeliveryRequest.setBillNo(ocBOrder.getBillNo());
                naiKaOrderDeliveryRequest.setSourceCode(tid);
                naiKaOrderDeliveryRequest.setExpressCode(logisticsNumber);
                naiKaOrderDeliveryRequest.setLogisticsName(delivery.getCpCLogisticsEname());
                naiKaOrderDeliveryRequest.setScanTime(ocBOrder.getScanTime());
                naiKaOrderDeliveryRequest.setPlatformCode(cpCPlatform.getEcode());
                List<NaiKaOrderDeliveryItemRequest> orderDeliveryItemRequestList = new ArrayList<>();
                for (OcBOrderDelivery ocBOrderDelivery : ocBOrderDeliveryList) {
                    List<String> ooids = getOoidsBySkuId(ooidAndSkuIdMap, ocBOrderDelivery.getPsCSkuId());
                    for (String ooid : ooids) {
                        NaiKaOrderDeliveryItemRequest request = new NaiKaOrderDeliveryItemRequest();
                        request.setSkuCode(ocBOrderDelivery.getPsCSkuEcode());
                        OcBOrderItem item = ocBOrderItemMap.get(ocBOrderDelivery.getOcBOrderId() + ocBOrderDelivery.getPsCSkuId() + ooid);
                        request.setOoid(ooid);
                        if (Objects.nonNull(item) && Objects.nonNull(item.getQty())) {
                            request.setQty(item.getQty());
                        } else {
                            request.setQty(ocBOrderDelivery.getQty());
                        }
                        ocBOrderItemList.add(item);
                        orderDeliveryItemRequestList.add(request);
                    }
                }
                naiKaOrderDeliveryRequest.setOrderDeliveryItemRequestList(orderDeliveryItemRequestList);
                ValueHolderV14 valueHolderV14 = naiKaOrderCmd.orderDelivery(naiKaOrderDeliveryRequest);
                if (!valueHolderV14.isOK()) {
                    log.error("调用小程序平台发货rpc异常:{}", valueHolderV14.getMessage());
                    throw new RuntimeException(StringUtils.isEmpty(valueHolderV14.getMessage()) ? "调用奶卡小程序发货服务返回接口为空" : valueHolderV14.getMessage());
                }
                if (CollectionUtils.isNotEmpty(ocBOrderItemList)) {
                    for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                        OcBOrderItem updateOcBOrderItem = new OcBOrderItem();
                        updateOcBOrderItem.setId(ocBOrderItem.getId());
                        updateOcBOrderItem.setOcBOrderId(ocBOrderItem.getOcBOrderId());
                        updateOcBOrderItem.setIsSendout(OcBorderListEnums.YesOrNoEnum.IS_YES.getVal());
                        updateOcBOrderItem.setModifieddate(new Date());
                        ocBOrderItemMapper.updateById(updateOcBOrderItem);
                    }
                }
            }
        }
        String logMsg = "订单" + ocBOrder.getId() + "(平台单号=" + ocBOrder.getTid() + ")平台发货成功";
        omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(), OrderLogTypeEnum.PLATFORM_SEND.getKey(), logMsg, null, ocBOrder.getForceSendFailReason(), user);
        OcBOrder ocBOrderFlag = new OcBOrder();
        ocBOrderFlag.setId(ocBOrderRelation.getOrderInfo().getId());
        ocBOrderFlag.setIsForce(1L);
        ocBOrderFlag.setOrderStatus(OmsOrderStatus.PLATFORM_DELIVERY.toInteger());
        omsOrderService.updateOrderInfo(ocBOrderFlag);
        return true;
    }

    private List<NaiKaOrderDeliveryItemRequest> build(List<OcBOrderItem> items) {
        List<NaiKaOrderDeliveryItemRequest> itemRequests = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem : items) {
            NaiKaOrderDeliveryItemRequest request = new NaiKaOrderDeliveryItemRequest();
            request.setSkuCode(ocBOrderItem.getPsCSkuEcode());
            request.setQty(ocBOrderItem.getQty());
            request.setOoid(ocBOrderItem.getOoid());
            itemRequests.add(request);
        }
        return itemRequests;
    }
}
