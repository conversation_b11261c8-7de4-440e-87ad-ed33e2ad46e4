package com.jackrain.nea.oc.oms.mapper;

import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategyItem;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略明细
 * @author: lijin
 * @create: 2024-12-19
 **/
@Mapper
public interface StCTraceabilityStrategyItemMapper extends ExtentionMapper<StCTraceabilityStrategyItem> {

    /**
     * 批量插入
     * @param items
     * @return
     */
    int batchInsert(List<StCTraceabilityStrategyItem> items);
}
