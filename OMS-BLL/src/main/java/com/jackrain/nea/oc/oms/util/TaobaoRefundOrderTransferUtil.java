package com.jackrain.nea.oc.oms.util;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.cpext.model.LogisticsInfo;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.data.basic.services.BasicCpQueryService;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.config.OmsSystemConfig;
import com.jackrain.nea.oc.oms.es.ES4ReturnOrder;
import com.jackrain.nea.oc.oms.mapper.IpBStandplatOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.model.constant.OcCommonConstant;
import com.jackrain.nea.oc.oms.model.enums.AGStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.IsWrongReceive;
import com.jackrain.nea.oc.oms.model.enums.OcBReturnAfSendListEnums;
import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.enums.PlatFormEnum;
import com.jackrain.nea.oc.oms.model.enums.ProReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnAfSendReturnBillTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ReturnStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.ToACStatusEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendRelation;
import com.jackrain.nea.oc.oms.model.relation.OcBReturnOrderRelation;
import com.jackrain.nea.oc.oms.model.relation.OmsOrderRelation;
import com.jackrain.nea.oc.oms.model.table.IpBTaobaoRefund;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderDelivery;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCBusinessType;
import com.jackrain.nea.oc.oms.model.table.IpBStandplatOrder;
import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;
import com.jackrain.nea.oc.oms.nums.RefundOrderSourceTypeEnum;
import com.jackrain.nea.oc.oms.services.OcSaveChangingOrRefundingService;
import com.jackrain.nea.oc.oms.services.OmsRefundOrderService;
import com.jackrain.nea.oc.oms.services.refund.OmsReturnAfterUtil;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.st.model.table.StCShopStrategyDO;
import com.jackrain.nea.st.service.OmsStCShopStrategyService;
import com.jackrain.nea.util.OperateUserUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: 黄世新
 * @Date: 2020/3/2 3:02 下午
 * @Version 1.0
 * <p>
 * 退单转换的处理类
 */
@Slf4j
@Component
public class TaobaoRefundOrderTransferUtil {

    @Autowired
    private OmsSystemConfig omsSystemConfig;

    @Autowired
    private BasicCpQueryService basicCpQueryService;

    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BuildSequenceUtil sequenceUtil;
    @Autowired
    private OmsStCShopStrategyService shopStrategyService;
    @Autowired
    private OcBReturnOrderMapper ocBReturnOrderMapper;
    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;

    @Autowired
    private OcSaveChangingOrRefundingService ocSaveChangingOrRefundingService;
    @Autowired
    private OmsRefundOrderService omsRefundOrderService;
    @Autowired
    private OcBOrderMapper ocBOrderMapper;
    @Autowired
    private IpBStandplatOrderMapper ipBStandplatOrderMapper;

    /**
     * 退单主表数据创建
     *
     * @return
     */
    public OcBReturnOrder buildOcBReturnOrderFromTaobaoRefund(OmsOrderRelation orderRelation,
                                                              IpBTaobaoRefund ipBTaobaoRefund,
                                                              OcBOrderDelivery ocBOrderDelivery,
                                                              Integer proType, User user) {
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
        OcBReturnOrder returnOrder = new OcBReturnOrder();
        if (ipBTaobaoRefund != null) {
            //平台退款单号
            returnOrder.setReturnId(ipBTaobaoRefund.getRefundId());
            //卖家昵称
            returnOrder.setBuyerNick(ipBTaobaoRefund.getBuyerNick());
            //申请退款时间
            returnOrder.setReturnCreateTime(ipBTaobaoRefund.getCreated());
            //最后修改时间
            returnOrder.setLastUpdateTime(ipBTaobaoRefund.getModified());

            //货物退回时间
            returnOrder.setReturnTime(ipBTaobaoRefund.getGoodReturnTime());
            //退款说明
            returnOrder.setReturnDesc(ipBTaobaoRefund.getReason());
            //商品应退金额(
            returnOrder.setReturnAmtList(ipBTaobaoRefund.getRefundFee());
            //售后/售中
            returnOrder.setReturnPhase(ipBTaobaoRefund.getRefundPhase());
            //退款金额(计算 商品应退金额+退还运费+退还其他费用-换货金额) = 商品应退金额
            returnOrder.setReturnAmtActual(ipBTaobaoRefund.getRefundFee());
            //卖家呢城
            returnOrder.setSellerNick(ipBTaobaoRefund.getSellerNick());
            //物流公司名称
            String companyName = ipBTaobaoRefund.getCompanyName();
            //退回物流单号
            returnOrder.setLogisticsCode(ipBTaobaoRefund.getSid());

            returnOrder.setCpCLogisticsEname(companyName);
            //退回说明
            returnOrder.setBackMessage(ipBTaobaoRefund.getRefunddesc());
            this.setLogisticInfo(returnOrder, companyName);
            returnOrder.setTid(ipBTaobaoRefund.getTid() + "");

        }
        //下载时间
        returnOrder.setCreationdate(new Date());
        returnOrder.setBillNo(sequenceUtil.buildReturnBillNo());
        //等待退货入库(PRD数据对象)
        returnOrder.setReturnStatus(TaobaoReturnOrderExt.ReturnOrderStatus.WAITIN.getCode());
        //原始订单编号
        returnOrder.setOrigOrderId(ocBOrder.getId());
        //退还运费，默认0
        returnOrder.setReturnAmtShip(BigDecimal.ZERO);
        //退还其他费用，默认0
        returnOrder.setReturnAmtOther(BigDecimal.ZERO);
        //换货人姓名
        returnOrder.setReceiveName(ocBOrder.getReceiverName());
        //换货人手机
        returnOrder.setReceiveMobile(ocBOrder.getReceiverMobile());
        //订单来源
        returnOrder.setOrdeSource(ocBOrder.getOrderSource());
        //邮编
        returnOrder.setReceiveZip(ocBOrder.getReceiverZip());
        //发货仓库
        returnOrder.setCpCPhyWarehouseId(ocBOrder.getCpCPhyWarehouseId());
        //平台类型
        returnOrder.setPlatform(ocBOrder.getPlatform());
        // returnOrder.setThirdWarehouseType(ocBOrder.getThirdWarehouseType());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());

        //换货金额
        returnOrder.setExchangeAmt(BigDecimal.ZERO);

        //是否传AG默认否
        returnOrder.setIsToag(AGStatusEnum.INIT.getVal());
        //是否生成调拨单，默认0
        returnOrder.setIsTransfer(0);
        //是否生成零售，默认0
        returnOrder.setIsTodrp(0);
        //退单状态，默认20
        //TaobaoReturnOrderExt.ReturnStatus.WAIT_RETURN_LIBRARY.getCode()
        returnOrder.setReturnStatus(ReturnStatusEnum.WAIT_RETURN_STORAGE.getVal());
        //是否手工新增，默认0
        returnOrder.setIsAdd(0);
        //虚拟入库状态，默认0
        returnOrder.setInventedStatus(0);
        //是否原退，默认0
        returnOrder.setIsRefund(0);
        //是否确认收货，默认0
        returnOrder.setIsReceiveConfirm(0);
        //WMS撤回状态，默认0
        returnOrder.setWmsCancelStatus(0);
        //强制入库，默认0
        returnOrder.setIsForce(0);
        //是否手工审核，默认0
        returnOrder.setIsManualAudit(0);
        //是否传WMS
        returnOrder.setIsTowms(0);
        //是否入仓成功
        returnOrder.setIsInstorage(0);
        returnOrder.setOrigSourceCode(ocBOrder.getSourceCode());
        returnOrder.setOrigOrderNo(ocBOrder.getBillNo());
        //退款原因
        //returnOrder.setRemark(ipBTaobaoRefund.getReason());
        //returnOrder.setReturnReason(ipBTaobaoRefund.getReason());
        returnOrder.setReceiveAddress(ocBOrder.getReceiverAddress());

        //店铺id
        returnOrder.setCpCShopId(ocBOrder.getCpCShopId());
        returnOrder.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        returnOrder.setCpCShopEcode(ocBOrder.getCpCShopEcode());

        returnOrder.setReceiverProvinceName(ocBOrder.getCpCRegionProvinceEname());
        returnOrder.setReceiverCityName(ocBOrder.getCpCRegionCityEname());
        returnOrder.setReceiverAreaName(ocBOrder.getCpCRegionAreaEname());
        returnOrder.setReceiverProvinceId(ocBOrder.getCpCRegionProvinceId());
        returnOrder.setReceiverCityId(ocBOrder.getCpCRegionCityId());
        returnOrder.setReceiverAreaId(ocBOrder.getCpCRegionAreaId());
        returnOrder.setProReturnStatus(ProReturnStatusEnum.WAIT.getVal());
        //this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder, true);

        // @20200721 设置默认值
        returnOrder.setIsNeedToWms(Long.valueOf(OcBorderListEnums.YesOrNoEnum.IS_NO.getVal()));

        returnOrder.setReturnProType(proType);
        if (ocBOrderDelivery != null) {
            returnOrder.setLogisticsCode(ocBOrderDelivery.getLogisticNumber());
            returnOrder.setCpCLogisticsId(ocBOrderDelivery.getCpCLogisticsId());
            returnOrder.setCpCLogisticsEcode(ocBOrderDelivery.getCpCLogisticsEcode());
            returnOrder.setCpCLogisticsEname(ocBOrderDelivery.getCpCLogisticsEname());
        }
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        returnOrder.setInventedStatus(orderRelation.getInterceptMark()); //未发起拦截
        if (proType.equals(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode())
                || TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_REJECTION.getCode().equals(proType)) {
            returnOrder.setCpCPhyWarehouseInId(ocBOrder.getCpCPhyWarehouseId());
            this.selectReturnCPhyWarehouse(ocBOrder.getCpCPhyWarehouseId(), returnOrder, true);
            //拦截或者拒收，走原退
            returnOrder.setIsBack(1);
        } else {
            StCShopStrategyDO shopStrategy = shopStrategyService.selectOcStCShopStrategy(ocBOrder.getCpCShopId());

            // @2020713 修改查询策略为空问题
            if (Objects.nonNull(shopStrategy)) {
                Integer isMultiReturnWarehouse = shopStrategy.getIsMultiReturnWarehouse();

                // @******** 如果为空，则认为没指定，则为否（可能策略维护没维护默认值，但是如果为是，则需要界面控件打钩，即一定会有值）
                if (Objects.isNull(isMultiReturnWarehouse) || isMultiReturnWarehouse == 0) {
                    returnOrder.setCpCPhyWarehouseInId(shopStrategy.getCpCWarehouseDefId());
                    this.selectReturnCPhyWarehouse(shopStrategy.getCpCWarehouseDefId(), returnOrder, false);
                }
            }
            //申请金额
            BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
            //商品价格
            BigDecimal price1 = ipBTaobaoRefund.getPrice();
            if (refundFee.compareTo(price1) > 0) {
                BigDecimal amtShip = refundFee.subtract(price1);
                returnOrder.setReturnAmtShip(amtShip);
            }
        }

        if (returnOrder.getIsBack() != null
                && returnOrder.getIsBack() == 1
                && StringUtils.isNotEmpty(returnOrder.getCpCLogisticsEcode())
                && returnOrder.getCpCLogisticsEcode().equals(OcCommonConstant.DNKD)
                && StringUtils.isNotEmpty(returnOrder.getLogisticsCode())
                && !returnOrder.getLogisticsCode().contains("T")) {
            returnOrder.setLogisticsCode("T" + returnOrder.getLogisticsCode());
        }

        // @20200708 退回转换就是退货单，不需要逻辑
        returnOrder.setBillType(TaobaoReturnOrderExt.BillType.REFUND.getCode());
        //加入“空运单号延迟推单有效时间”字段 在保存的方法里统一处理 2021-11-10
//        returnOrder.setPushDelayTime(ocSaveChangingOrRefundingService.PushDelayTime(returnOrder));
        // ocSaveChangingOrRefundingService.checkBillType(returnOrder); // 这个是一商的逻辑，带预退货，这次不需要
        OperateUserUtils.saveOperator(returnOrder, user);
        //设置业务类型
        StCBusinessType resultType = omsRefundOrderService.queryReturnOrderType(ocBOrder);
        returnOrder.setBusinessTypeId(resultType.getId());
        returnOrder.setBusinessTypeCode(resultType.getEcode());
        returnOrder.setBusinessTypeName(resultType.getEname());
        returnOrder.setIsWrongReceive(IsWrongReceive.NO.val());
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("构建退货单主表数据:{}", "构建退货单主表数据"), JSONObject.toJSONString(returnOrder));
        }
        return returnOrder;
    }


    /**
     * 退单明细表数量
     *
     * @param ipBTaobaoRefund
     * @return
     */
    private List<OcBReturnOrderRefund> buildReturnOrderItemFromRefund(OmsOrderRelation orderRelation,
                                                                      IpBTaobaoRefund ipBTaobaoRefund,
                                                                      Map<String, BigDecimal> skuCount,
                                                                      User user) {
        List<OcBReturnOrderRefund> result = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        for (OcBOrderItem orderItem : ocBOrderItems) {
            BigDecimal packQty = skuCount.get(orderItem.getPsCSkuEcode());
            if (packQty == null) {
                continue;
            }
            String psCSkuEcode = orderItem.getPsCSkuEcode();
            if (packQty.compareTo(orderItem.getQty()) > 0) {
                packQty = orderItem.getQty();
            }
            //当前明细的申请数量
            BigDecimal qtyReturnApply = orderItem.getQtyReturnApply() == null ?
                    BigDecimal.ZERO : orderItem.getQtyReturnApply();
            //当前明细的可退数量
            BigDecimal subQty = orderItem.getQty().subtract(qtyReturnApply);
            if (subQty.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (packQty.compareTo(subQty) > 0) {
                packQty = subQty;
            }
            BigDecimal qty = skuCount.get(orderItem.getPsCSkuEcode());
            if (packQty.compareTo(qty) == 0) {
                skuCount.remove(psCSkuEcode);
            } else {
                skuCount.put(orderItem.getPsCSkuEcode(), qty.subtract(packQty));
            }
            orderItem.setQtyReturnApply(packQty.add(qtyReturnApply));
            OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
            BigDecimal refundAmt = orderItem.getRealAmt().divide(orderItem.getQty(),
                    4, BigDecimal.ROUND_HALF_DOWN).multiply(packQty);
            returnOrderRefund.setAmtRefund(refundAmt);
            //申请数量
            returnOrderRefund.setQtyRefund(packQty);
            //商品名称
            returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());

            returnOrderRefund.setPrice(orderItem.getPrice());
            returnOrderRefund.setPriceList(orderItem.getPriceList());
            //1 qty_can_refund 购买数量 合计所有明细qty
            returnOrderRefund.setQtyCanRefund(orderItem.getQty());
            //商品单价
            returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
            //国标码
            returnOrderRefund.setBarcode(orderItem.getBarcode());
            //修改人用户名
            returnOrderRefund.setModifierename(orderItem.getModifierename());
            //商品规格
            returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
            returnOrderRefund.setOid(orderItem.getOoid());
            //条码id
            returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
            returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            returnOrderRefund.setPsCSkuEcode(psCSkuEcode);
            returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
            returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
            returnOrderRefund.setPsCProId(orderItem.getPsCProId());
            //颜色尺寸
            returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
            returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
            returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

            returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
            returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
            returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
            returnOrderRefund.setSex(orderItem.getSex());
            returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
            returnOrderRefund.setOcBOrderItemId(orderItem.getId());
            returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
            returnOrderRefund.setTid(orderItem.getTid());
            //returnOrderRefund.setAmtPtRefund(ipBTaobaoRefund.getRefundFee());
            returnOrderRefund.setGiftType(orderItem.getGiftType());
            returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
            //returnOrderRefund.setRefundStatus(ipBTaobaoRefund.getStatus());
            //returnOrderRefund.setRefundBillNo(ocBOrder.getBillNo());
            returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                    4, BigDecimal.ROUND_HALF_UP));
            returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");
            if (ipBTaobaoRefund != null && orderItem.getOoid() != null &&
                    orderItem.getOoid().equals(ipBTaobaoRefund.getOid() + "")) {
                returnOrderRefund.setRefundStatus(ipBTaobaoRefund.getStatus());
                returnOrderRefund.setRefundBillNo(ipBTaobaoRefund.getRefundId());
                returnOrderRefund.setAmtPtRefund(ipBTaobaoRefund.getRefundFee());
            }
            OperateUserUtils.saveOperator(returnOrderRefund, user);
            result.add(returnOrderRefund);
        }
        return result;
    }


    /**
     * 按包裹生成退换货订单的关系处理
     *
     * @param orderRelation
     * @param ipBTaobaoRefund
     * @param skuCount
     * @param ocBOrderDelivery
     * @param billsStatus      单据类型(拒收 , 拦截)
     * @return
     */
    public OcBReturnOrderRelation taobaoRefundOrderToReturnOrder(OmsOrderRelation orderRelation,
                                                                 IpBTaobaoRefund ipBTaobaoRefund,
                                                                 Map<String, BigDecimal> skuCount,
                                                                 OcBOrderDelivery ocBOrderDelivery,
                                                                 TaobaoReturnOrderExt.ReturnBillsStatus billsStatus,
                                                                 User user) {
        OcBReturnOrderRelation returnOrderRelation = new OcBReturnOrderRelation();
        OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromTaobaoRefund(orderRelation,
                ipBTaobaoRefund, ocBOrderDelivery, billsStatus.getCode(), user);
        List<OcBReturnOrderRefund> orderRefunds = buildReturnOrderItemFromRefund(orderRelation,
                ipBTaobaoRefund, skuCount, user);
        String jointTid = OmsReturnAfterUtil.getJointTid(orderRefunds);
        ocBReturnOrder.setTid(jointTid);
        this.getAllSku(orderRefunds, ocBReturnOrder);
        returnOrderRelation.setReturnOrderInfo(ocBReturnOrder);
        returnOrderRelation.setOrderRefundList(orderRefunds);
        return returnOrderRelation;
    }

    /**
     * 封装主表的all_sku以及 商品数量
     *
     * @param returnOrderItems
     * @return
     */
    private void getAllSku(List<OcBReturnOrderRefund> returnOrderItems, OcBReturnOrder returnOrder) {
        //拼接退货sku加数量
        String skuQyt = "";
        BigDecimal qtyInstore = BigDecimal.ZERO;
        int i = 0;
        for (OcBReturnOrderRefund returnOrderItem : returnOrderItems) {
            qtyInstore = qtyInstore.add(returnOrderItem.getQtyRefund());
            if (i == 5) {
                continue;
            }
            String str = returnOrderItem.getPsCSkuEcode() + "(" + returnOrderItem.getQtyRefund().intValue() + "),";
            skuQyt = skuQyt + str;
            i++;

        }
        if (StringUtils.isNotEmpty(skuQyt)) {
            //去掉最后一个,号
            skuQyt = skuQyt.substring(0, skuQyt.length() - 1);
        }
        returnOrder.setAllSku(skuQyt);
        returnOrder.setQtyInstore(qtyInstore);
    }

    /**
     * 按子订单生成退单明细数据
     *
     * @param orderRelation
     * @param alreadyApplyQty 已经生成的数量
     * @return
     */
    public List<OcBReturnOrderRefund> buildReturnOrderItemFromOid(OmsOrderRelation orderRelation,
                                                                  IpBTaobaoRefund ipBTaobaoRefund,
                                                                  User user, BigDecimal alreadyApplyQty) {
        List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
        List<OcBOrderItem> ocBOrderItems = orderRelation.getOcBOrderItems();
        OcBOrder ocBOrder = orderRelation.getOcBOrder();
        //申请金额
        BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
        //总金额
        BigDecimal totalFee = ipBTaobaoRefund.getTotalFee();
        //申请金额大于总金额的时候  已总金额为准计算申请数量 其他的为运费
        if (refundFee.compareTo(totalFee) > 0) {
            refundFee = totalFee;
        }
        //退或金额
        BigDecimal realAmt = ocBOrderItems.stream().map(OcBOrderItem::getRealAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal qtySum = ocBOrderItems.stream().map(OcBOrderItem::getQty).
                reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal qtyApply = null;
        //成交金额与申请的退款金额不相等的时候
        if (realAmt.compareTo(refundFee) != 0) {
            //判断是否为组合商品
            Long proType = ocBOrderItems.get(0).getProType();
            if (SkuType.COMBINE_PRODUCT == proType) {
                List<OcBReturnOrderRefund> orderRefundList =
                        this.combinationGoodsHandle(ocBOrderItems, realAmt, ipBTaobaoRefund, user, ocBOrder);
                return orderRefundList;
            }
            //根据退货金额算出来的退货数量
            //单价
            BigDecimal price = realAmt.divide(qtySum, 4, BigDecimal.ROUND_HALF_UP);
            if (price != null && BigDecimal.ZERO.compareTo(price) != 0) {
                //根据退款金额计算的数量
                qtyApply = refundFee.divide(price, 0, BigDecimal.ROUND_UP);
                //已经申请的再次申请的数一样
                if (alreadyApplyQty.compareTo(qtyApply) >= 0) {
                    return null;
                }
                qtyApply = qtyApply.subtract(alreadyApplyQty);
            }
        }
        BigDecimal qtyCount = BigDecimal.ZERO;
        for (OcBOrderItem orderItem : ocBOrderItems) {
            BigDecimal qty = BigDecimal.ZERO;
            if (orderItem.getIsGift() != null && orderItem.getIsGift() == 0) {
                //申请退货数量
                BigDecimal qtyReturnApply = orderItem.getQtyReturnApply();

                if (qtyReturnApply == null) {
                    qtyReturnApply = BigDecimal.ZERO;
                }
                if (qtyReturnApply.compareTo(orderItem.getQty()) == 0) {
                    continue;
                }
                //可以退的数量
                qty = orderItem.getQty().subtract(qtyReturnApply);
                if (qtyApply != null) {
                    if (qtyApply.compareTo(qtyCount) == 0) {
                        continue;
                    }
                    if (qty.compareTo(qtyApply) >= 0) {
                        qty = qtyApply;
                    } else {
                        qtyApply = qtyApply.subtract(qty);

                    }
                    qtyCount = qtyCount.add(qty);

                }
            } else {
                //赠品直接带过阿里
                qty = orderItem.getQty();
            }
            OcBReturnOrderRefund returnOrderRefund = getOcBReturnOrderRefund(ipBTaobaoRefund, user, ocBOrder, orderItem, qty);

            orderRefunds.add(returnOrderRefund);
        }
        return orderRefunds;
    }

    /**
     * 判断是否需要进行还原对等换货
     *
     * @param omsRelation
     * @param refundFee   退还金额
     * @param shipAmt     退还运费
     * @return
     */
    public boolean checkIsPeerExchange(OmsOrderRelation omsRelation, BigDecimal refundFee, BigDecimal shipAmt, boolean isBeforAndAfter) {
        BigDecimal returnQty = BigDecimal.ZERO;
        List<OcBOrderItem> ocBOrderItems = omsRelation.getOcBOrderItems();
        boolean isPeerExchange = false;
        //对等换货明细
        List<OcBOrderItem> collect = ocBOrderItems.stream().filter(x -> x.getIsEqualExchange() != null && x.getIsEqualExchange().equals(1)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            //没有对等换货明细，走原逻辑
            return isPeerExchange;
        }
        OcBOrderItem orderItem = collect.get(0);
        /*if(shipAmt != null && shipAmt.compareTo(BigDecimal.ZERO) > 0){
            refundFee = refundFee.subtract(shipAmt);
        }*/
        //成交单价
        BigDecimal priceActual = orderItem.getPriceActual();
        //零售发货单明细成交金额
        BigDecimal realAmt = ocBOrderItems.stream().map(OcBOrderItem::getRealAmt).
                reduce(BigDecimal.ZERO, BigDecimal::add);

        //拼多多平台：对等换货后退换货单“申请数量”计算公式=退款金额+通用订单“平台补偿金额”，若大于等于，则不走对应换货数量
        if (PlatFormEnum.PINDUODUO.getCode().equals(omsRelation.getOcBOrder().getPlatform())) {
            IpBStandplatOrder ipBStandplatOrder = ipBStandplatOrderMapper.selectStandplatOrderByTid(omsRelation.getOcBOrder().getTid());
            if (ipBStandplatOrder != null) {
                BigDecimal plantforCount = ipBStandplatOrder.getPlantforCount() == null ? BigDecimal.ZERO : ipBStandplatOrder.getPlantforCount();
                BigDecimal refundTotalAmt = refundFee.add(plantforCount);
                if (refundTotalAmt.compareTo(realAmt) >= 0) {
                    return isPeerExchange;
                }
            }
        }

        //成交金额与申请的退款金额相等的时候
        if (refundFee.compareTo(realAmt) >= 0) {
            //全退时，走原有逻辑
            return isPeerExchange;
        } else if (refundFee.compareTo(realAmt) < 0) {
            //还原对等换货,例如：对等换货比例 2:1
            String equalExchangeRatio = orderItem.getEqualExchangeRatio();
            if (StringUtils.isBlank(equalExchangeRatio)) {
                throw new NDSException("还原对等换货，查询对等换货比例为空！");
            }
            String[] equalExchangeRatioAr = equalExchangeRatio.split(":");
            BigDecimal beforeQty = new BigDecimal(equalExchangeRatioAr[0]);
            BigDecimal afterQty = new BigDecimal(equalExchangeRatioAr[1]);
            if (beforeQty.compareTo(afterQty) == 0) {
                //比例是1:1时，如果申请退款金额除以明细成交单价，如果是整数，则不用还原对等换货
                returnQty = refundFee.divide(priceActual, 4, BigDecimal.ROUND_HALF_UP);
                boolean isInteger = returnQty.setScale(0, RoundingMode.HALF_UP).compareTo(returnQty) == 0;
                if (!isInteger) {
                    isPeerExchange = true;
                }
            } else if (beforeQty.compareTo(afterQty) > 0) {
                //比例是N:1
                isPeerExchange = true;
            }
        }
        if (isPeerExchange) {
            //如果是拆单，并且同时存在 发货前和发货后的订单，则不转换，卡在中间表，此时转换不确定找那个订单
            Integer isSplit = omsRelation.getOcBOrder().getIsSplit();
            if (isSplit != null && isSplit.equals(1) && isBeforAndAfter) {
                throw new NDSException("还原对等换货失败，存在已发货和未发货订单！");
            }
        }
        return isPeerExchange;
    }

    /**
     * 查询对等换货原明细
     *
     * @param items
     * @return
     */
    public List<OcBOrderEqualExchangeItem> queryEqualExchangeItems(List<OcBOrderItem> items) {
        //查询订单原明细
        List<Long> orderIds = items.stream().map(OcBOrderItem::getOcBOrderId).collect(Collectors.toList());
        List<String> equalExchangeMarkList = items.stream().map(OcBOrderItem::getEqualExchangeMark).collect(Collectors.toList());
        List<OcBOrderEqualExchangeItem> equalExchangeItemList = ocBOrderEqualExchangeItemMapper.selectItemListByOoid(orderIds, equalExchangeMarkList);
        if (CollectionUtils.isEmpty(equalExchangeItemList)) {
            throw new NDSException("还原对等换货，查询订单原对等换货明细为空！");
        }
        return equalExchangeItemList;
    }

    /**
     * 还原对等换货明细
     *
     * @return
     */
    private List<OcBReturnOrderRefund> reductionOrderItems(OmsOrderRelation omsRelation,
                                                           IpBTaobaoRefund ipBTaobaoRefund,
                                                           User user) {
        List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
        OcBOrder ocBOrder = omsRelation.getOcBOrder();
        List<OcBOrderItem> ocBOrderItems = omsRelation.getOcBOrderItems();

        //对等换货明细
        List<OcBOrderItem> collect = ocBOrderItems.stream().filter(x -> x.getIsEqualExchange() != null && x.getIsEqualExchange().equals(1)).collect(Collectors.toList());
        OcBOrderItem orderItem = collect.get(0);
        List<OcBOrderEqualExchangeItem> equalExchangeItemList = queryEqualExchangeItems(collect);
        Map<String, List<OcBOrderEqualExchangeItem>> exchangeItemMap = equalExchangeItemList.stream().
                collect(Collectors.groupingBy(x -> x.getOcBOrderId() + "-" +
                        x.getOoid()));
        //原明细成交单价
        BigDecimal priceActual = equalExchangeItemList.get(0).getPriceActual();
        //根据申请退款金额除以原明细成交单价计算退货数量
        //申请金额
        BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
        //交易总额
        BigDecimal totalFee = ipBTaobaoRefund.getTotalFee();
        if (refundFee.compareTo(totalFee) > 0) {
            refundFee = totalFee;
        }
        //计算对等还原后的退货商品数量
        BigDecimal returnQty = refundFee.divide(priceActual, 0, BigDecimal.ROUND_DOWN);

        //还原对等换货,例如：对等换货比例 2:1
        String equalExchangeRatio = orderItem.getEqualExchangeRatio();
        String[] equalExchangeRatioAr = equalExchangeRatio.split(":");
        BigDecimal beforeQty = new BigDecimal(equalExchangeRatioAr[0]);
        BigDecimal afterQty = new BigDecimal(equalExchangeRatioAr[1]);

        //如果有明细直接满足数量，则不用还原对等换货明细
        List<OcBOrderItem> filterList = new ArrayList<>();
        for (OcBOrderItem ocBOrderItem1 : ocBOrderItems) {
            if (ocBOrderItem1.getIsGift() != null && ocBOrderItem1.getIsGift() == 1) {
                continue;
            }
            //当前明细数量
            BigDecimal qty = ocBOrderItem1.getQty();
            //对应原明细数量
            BigDecimal bQty = beforeQty.multiply(qty).divide(afterQty, 4, BigDecimal.ROUND_HALF_UP);
            if (returnQty.compareTo(bQty) == 0) {
                //取当前这条明细生成退换货单，不用还原对等换货明细
                filterList.add(ocBOrderItem1);
                break;
            }
        }
        if (CollectionUtils.isNotEmpty(filterList)) {
            OcBOrderItem ocBOrderItem = filterList.get(0);
            OcBReturnOrderRefund returnOrderRefund = getOcBReturnOrderRefund(ipBTaobaoRefund, user, ocBOrder, ocBOrderItem, returnQty);
            orderRefunds.add(returnOrderRefund);
            returnQty = BigDecimal.ZERO;
        }

        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            BigDecimal qty = BigDecimal.ZERO;
            OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
            if (ocBOrderItem.getIsGift() != null && ocBOrderItem.getIsGift() == 0) {
                if (returnQty.compareTo(BigDecimal.ZERO) == 0) {
                    break;
                }
                List<OcBOrderEqualExchangeItem> exchangeItems = exchangeItemMap.get(ocBOrderItem.getOcBOrderId() + "-" + ocBOrderItem.getOoid());
                if (CollectionUtils.isEmpty(exchangeItems)) {
                    continue;
                }
                OcBOrderEqualExchangeItem exchangeItem = exchangeItems.get(0);
                //非赠品，计算当前明细可退数量
                /**换货后商品数量*/
                BigDecimal aQty = ocBOrderItem.getQty();
                /**换货前商品数量*/
                BigDecimal bQty = aQty.multiply(beforeQty).divide(afterQty, 4, BigDecimal.ROUND_HALF_UP);
                /**可退数量*/
                BigDecimal qtyApply = bQty.subtract(exchangeItem.getQtyRefund() == null ? BigDecimal.ZERO : exchangeItem.getQtyRefund());
                if (log.isDebugEnabled()) {
                    log.debug(LogUtil.format("计算当前明细还原对等换货明细数量。REFUND_ID={},换货后明细数量={}，换货前明细数量={}，可退数量={};",
                            "TaobaoRefundOrderTransferUtil"), ipBTaobaoRefund.getRefundId(), aQty, bQty, qtyApply);
                }
                OcBOrderItem newOrderItem = new OcBOrderItem();
                BeanUtils.copyProperties(exchangeItem, newOrderItem);
                newOrderItem.setId(ocBOrderItem.getId());

                if (qtyApply.compareTo(returnQty) >= 0) {
                    qty = returnQty;
                    returnQty = BigDecimal.ZERO;
                } else {
                    qty = qtyApply;
                    returnQty = returnQty.subtract(qtyApply);
                }
                returnOrderRefund = getOcBReturnOrderRefund(ipBTaobaoRefund, user, ocBOrder, newOrderItem, qty);
            } else {
                //赠品
                qty = ocBOrderItem.getQty();
                returnOrderRefund = getOcBReturnOrderRefund(ipBTaobaoRefund, user, ocBOrder, orderItem, qty);
            }
            orderRefunds.add(returnOrderRefund);
        }
        if (returnQty.compareTo(BigDecimal.ZERO) > 0) {
            throw new NDSException("还原对等换货，可退数量小于申请数量！");
        }
        return orderRefunds;
    }

    /**
     * 组合商品根据退款金额算数量的处理
     *
     * @param ocBOrderItems
     */
    private List<OcBReturnOrderRefund> combinationGoodsHandle(List<OcBOrderItem> ocBOrderItems, BigDecimal realAmtCount,
                                                              IpBTaobaoRefund ipBTaobaoRefund, User user, OcBOrder order) {
        List<OcBReturnOrderRefund> refundList = new ArrayList<>();
        //组合商品的数量
        BigDecimal qtyGroup = ocBOrderItems.get(0).getQtyGroup();
        BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
        //单价
        BigDecimal price = realAmtCount.divide(qtyGroup, 4, BigDecimal.ROUND_HALF_UP);
        //根据退款金额计算的数量
        BigDecimal qtyApply = refundFee.divide(price, 0, BigDecimal.ROUND_UP);
        for (OcBOrderItem orderItem : ocBOrderItems) {
            //明细的购买数量
            BigDecimal qty = orderItem.getQty();
            //当前组合商品的对应真实条码的数量
            BigDecimal groupNum = qty.divide(qtyGroup, 0, BigDecimal.ROUND_HALF_UP);
            qtyApply = groupNum.multiply(qtyApply);

            //申请退货数量
            BigDecimal qtyReturnApply = orderItem.getQtyReturnApply();

            if (qtyReturnApply == null) {
                qtyReturnApply = BigDecimal.ZERO;
            }
            if (qtyReturnApply.compareTo(orderItem.getQty()) == 0) {
                continue;
            }
            OcBReturnOrderRefund ocBReturnOrderRefund =
                    this.getOcBReturnOrderRefund(ipBTaobaoRefund, user, order, orderItem, qtyApply);
            refundList.add(ocBReturnOrderRefund);
        }
        return refundList;
    }


    /**
     * 封装客退的明细数据
     *
     * @param ipBTaobaoRefund
     * @param user
     * @param ocBOrder
     * @param orderItem
     * @param qty
     * @return
     */
    private OcBReturnOrderRefund getOcBReturnOrderRefund(IpBTaobaoRefund ipBTaobaoRefund, User user,
                                                         OcBOrder ocBOrder, OcBOrderItem orderItem, BigDecimal qty) {
        //发货信息
        OcBReturnOrderRefund returnOrderRefund = new OcBReturnOrderRefund();
        // @20200722 调整精度问题
        BigDecimal refundAmt = orderItem.getRealAmt().divide(orderItem.getQty(),
                10, BigDecimal.ROUND_HALF_UP).multiply(qty).setScale(4, BigDecimal.ROUND_HALF_UP);
        returnOrderRefund.setAmtRefund(refundAmt);
        returnOrderRefund.setOcBOrderItemId(orderItem.getId());
        //申请数量
        returnOrderRefund.setQtyRefund(qty);
        returnOrderRefund.setRefundStatus(ipBTaobaoRefund.getStatus());
        returnOrderRefund.setAmtPtRefund(ipBTaobaoRefund.getRefundFee());
        //商品名称
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPrice(orderItem.getPrice());
        returnOrderRefund.setPriceList(orderItem.getPriceList());
        //1 qty_can_refund 购买数量 合计所有明细qty
        returnOrderRefund.setQtyCanRefund(orderItem.getQty());
        //商品单价
        returnOrderRefund.setAmtAdjust(orderItem.getAdjustAmt());
        //国标码
        returnOrderRefund.setBarcode(orderItem.getBarcode());
        //修改人用户名
        returnOrderRefund.setModifierename(orderItem.getModifierename());
        //商品规格
        returnOrderRefund.setSkuSpec(orderItem.getSkuSpec());
        returnOrderRefund.setOid(orderItem.getOoid());
        //条码id
        returnOrderRefund.setPsCSkuId(orderItem.getPsCSkuId());
        returnOrderRefund.setPsCSkuEcode(orderItem.getPsCSkuEcode());
        returnOrderRefund.setPsCSkuEname(orderItem.getPsCSkuEname());
        returnOrderRefund.setGiftRelation(orderItem.getGiftRelation());
        returnOrderRefund.setGiftType(orderItem.getGiftType());
        returnOrderRefund.setPsCProEname(orderItem.getPsCProEname());
        returnOrderRefund.setPsCProEcode(orderItem.getPsCProEcode());
        returnOrderRefund.setPsCProId(orderItem.getPsCProId());
        //颜色尺寸
        returnOrderRefund.setPsCSizeEcode(orderItem.getPsCSizeEcode());
        returnOrderRefund.setPsCSizeEname(orderItem.getPsCSizeEname());
        returnOrderRefund.setPsCSizeId(orderItem.getPsCSizeId());

        returnOrderRefund.setPsCClrEcode(orderItem.getPsCClrEcode());
        returnOrderRefund.setPsCClrEname(orderItem.getPsCClrEname());
        returnOrderRefund.setPsCClrId(orderItem.getPsCClrId());
        returnOrderRefund.setSex(orderItem.getSex());
        returnOrderRefund.setRefundBillNo(ipBTaobaoRefund.getRefundId());
        returnOrderRefund.setOcBOrderId(orderItem.getOcBOrderId());
        returnOrderRefund.setTid(orderItem.getTid());
        returnOrderRefund.setAmtRefundSingle(orderItem.getRealAmt().divide(orderItem.getQty(),
                4, BigDecimal.ROUND_HALF_UP));
        //returnOrderRefund.setReserveDecimal01(orderItem.getReserveDecimal02());
        returnOrderRefund.setToAgStatus(AGStatusEnum.INIT.getVal() + "");
        returnOrderRefund.setIsEqualExchange(orderItem.getIsEqualExchange());
        OperateUserUtils.saveOperator(returnOrderRefund, user);
        return returnOrderRefund;
    }


    /**
     * 按子订单生成退换货订单的关系处理
     *
     * @param orderRelation
     * @param ipBTaobaoRefund
     * @return
     */
    public List<OcBReturnOrderRelation> taobaoRefundOrderToReturnOid(List<OmsOrderRelation> orderRelation,
                                                                     IpBTaobaoRefund ipBTaobaoRefund,
                                                                     User user, BigDecimal applyQty, boolean useIntercept) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("taoBaoRefundTrans.{},taobaoRefundOrderToReturnOid"), ipBTaobaoRefund.getRefundId());
        }
        //生成主表数据
        List<OcBReturnOrderRelation> orderRelations = new ArrayList<>();
        OmsOrderRelation omsOrderRelation = orderRelation.get(0);
        OcBOrderDelivery delivery = null;
        Integer code = TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode();
        if (useIntercept) {
            code = TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode();
            List<OcBOrderDelivery> orderDeliveries = omsOrderRelation.getOrderDeliveries();
            if (CollectionUtils.isNotEmpty(orderDeliveries)) {
                delivery = orderDeliveries.get(0);
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("taoBaoRefundTrans.{},Intercept"), ipBTaobaoRefund.getRefundId());
            }
        }
        OcBReturnOrder ocBReturnOrder = this.buildOcBReturnOrderFromTaobaoRefund(omsOrderRelation, ipBTaobaoRefund,
                delivery, code, user);
        List<OcBOrderItem> orderItemList = new ArrayList<>();
        for (OmsOrderRelation relation : orderRelation) {
            orderItemList.addAll(relation.getOcBOrderItems());
            //根据系统参数判断是否要带入赠品
            if (omsSystemConfig.isReturnOrderAddBringGift()) {
                //判断赠品(是否有挂靠赠品)
                List<OmsOrderRelation.OcOrderGifts> ocOrderGifts = relation.getOcOrderGifts();
                if (CollectionUtils.isNotEmpty(ocOrderGifts)) {
                    for (OmsOrderRelation.OcOrderGifts ocOrderGift : ocOrderGifts) {
                        if (ocOrderGift.getGiftMark() == 2) {
                            List<OcBOrderItem> ocBOrderGifts = ocOrderGift.getOcBOrderGifts();
                            //将挂靠赠品加入明细
                            orderItemList.addAll(ocBOrderGifts);
                        } else {
                            if (!isGift(ipBTaobaoRefund)) {
                                List<OcBOrderItem> ocBOrderGifts = ocOrderGift.getOcBOrderGifts();
                                //将挂靠赠品加入明细
                                orderItemList.addAll(ocBOrderGifts);
                            }
                        }
                    }
                }
            }
        }
        OmsOrderRelation omsRelation = new OmsOrderRelation();
        omsRelation.setOcBOrder(omsOrderRelation.getOcBOrder());
        omsRelation.setOcBOrderItems(orderItemList);
        applyQty = applyQty == null ? BigDecimal.ZERO : applyQty;
        List<OcBReturnOrderRefund> orderRefunds = new ArrayList<>();
        //判断退货明细是否需要还原对等换货明细
        boolean isBeforAndAfter = false;
        List<OmsOrderRelation> bfOrderRelation = orderRelation.stream().filter(x -> TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_FRONT.getCode().equals(x.getOrderMark())).collect(Collectors.toList());
        List<Integer> orderMarkList = new ArrayList<>();
        orderMarkList.add(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_AFTER.getCode());
        orderMarkList.add(TaobaoReturnOrderExt.ReturnBillsStatus.GOODS_INTERCEPT.getCode());
        List<OmsOrderRelation> afOrderRelation = orderRelation.stream().filter(x -> orderMarkList.contains(x.getOrderMark())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(bfOrderRelation) && CollectionUtils.isNotEmpty(afOrderRelation)) {
            isBeforAndAfter = true;
        }
        boolean isPeerExchange = checkIsPeerExchange(omsRelation, ipBTaobaoRefund.getRefundFee(), omsOrderRelation.getOcBOrder().getShipAmt(), isBeforAndAfter);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("查询当前退单是否需要还原对等换货明细！REFUND_ID={}，isPeerExchange={};",
                    "TaobaoRefundOrderTransferUtil"), ipBTaobaoRefund.getRefundId(), isPeerExchange);
        }
        if (isPeerExchange) {
            orderRefunds = this.reductionOrderItems(omsRelation, ipBTaobaoRefund, user);
        } else {
            orderRefunds = this.buildReturnOrderItemFromOid(omsRelation, ipBTaobaoRefund, user, applyQty);
        }

        if (CollectionUtils.isEmpty(orderRefunds)) {
            return null;
        }
        String jointTid = OmsReturnAfterUtil.getJointTid(orderRefunds);
        ocBReturnOrder.setTid(jointTid);
        this.getAllSku(orderRefunds, ocBReturnOrder);
        OcBReturnOrderRelation relation = new OcBReturnOrderRelation();
        relation.setReturnOrderInfo(ocBReturnOrder);
        relation.setOrderRefundList(orderRefunds);
        orderRelations.add(relation);
        return orderRelations;
    }


    /**
     * 通过平台单号查询是否生成过退换货单
     *
     * @param taobaoRefund
     * @return
     */
    private boolean isGift(IpBTaobaoRefund taobaoRefund) {
        //通过平台单号查询ES
        Set<Long> esIdList = ES4ReturnOrder.findIdByTid(taobaoRefund.getTid());
        //是否存在退换货订单
        if (CollectionUtils.isNotEmpty(esIdList)) {
            //判断是否是取消
            List<OcBReturnOrder> list = ocBReturnOrderMapper.selectOcBReturnOrderByOrder(new ArrayList<>(esIdList));
            return (CollectionUtils.isNotEmpty(list));
        }
        //是否存在退换货订单
        return false;
    }

    /**
     * 生成发货后退款单
     *
     * @return
     */
    private OcBReturnAfSend buildOcBReturnAfSend(OcBOrder ocBOrder,
                                                 IpBTaobaoRefund taobaoRefund,
                                                 Integer billType, User user) {
        OcBReturnAfSend ocBReturnAfSend = new OcBReturnAfSend();
        ocBReturnAfSend.setCpCShopId(ocBOrder.getCpCShopId());
        ocBReturnAfSend.setCpCShopEcode(ocBOrder.getCpCShopEcode());
        ocBReturnAfSend.setCpCShopTitle(ocBOrder.getCpCShopTitle());
        ocBReturnAfSend.setTid(taobaoRefund.getTid() + "");
        ocBReturnAfSend.setBillNo(sequenceUtil.aFbuildBillNo());
        // 退款状态 0 待审核
        ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDING.getVal());
        // 待审核待传对账,赋默认值
        ocBReturnAfSend.setToSettleStatus(ToACStatusEnum.INIT.val());
        ocBReturnAfSend.setTReturnId(taobaoRefund.getRefundId());
        ocBReturnAfSend.setTReturnStatus(taobaoRefund.getStatus());
        //单据类型 0 退货退款 1仅退款',
        ocBReturnAfSend.setBillType(billType);
        ocBReturnAfSend.setVipNick(taobaoRefund.getBuyerNick());
        // @******** 增加手机号码
        ocBReturnAfSend.setVipPhone(ocBOrder.getReceiverPhone());
        // @******** 增加阿里支付账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        ocBReturnAfSend.setReason(taobaoRefund.getReason());
        //单据来源设置默认值为2 自动
        ocBReturnAfSend.setRefundOrderSourceType(RefundOrderSourceTypeEnum.AUTO.getValue());
        //支付方式
        ocBReturnAfSend.setPayMode(ocBOrder.getPayType() + "");
        //支付宝账号
        ocBReturnAfSend.setPayAccount(ocBOrder.getBuyerAlipayNo());
        //申请退款金额
        ocBReturnAfSend.setAmtReturnApply(taobaoRefund.getRefundFee());
        ocBReturnAfSend.setTReturnStatus(taobaoRefund.getStatus());
        ocBReturnAfSend.setSourceBillNo(ocBOrder.getId() + "");
        ocBReturnAfSend.setCpCPlatformId(Long.valueOf(ocBOrder.getPlatform()));
        ocBReturnAfSend.setPayMode(OcBReturnAfSendListEnums.PayTypeEnum.Alipay.getVal());
        //todo 实际退款金额
        ocBReturnAfSend.setAmtReturnActual(taobaoRefund.getRefundFee());
        ocBReturnAfSend.setPtGoodStatus(taobaoRefund.getGoodStatus());
        //申请退款时间
        ocBReturnAfSend.setReturnApplyTime(new Date());
        ocBReturnAfSend.setAgStatus(AGStatusEnum.INIT.getVal() + "");
        ocBReturnAfSend.setReturnExplain(taobaoRefund.getRefunddesc());
        ocBReturnAfSend.setHasGoodReturn(taobaoRefund.getHasGoodReturn());
        String status = taobaoRefund.getStatus();
        if (TaobaoReturnOrderExt.RefundStatus.SUCCESS.getCode().equals(status)) {
            ocBReturnAfSend.setReturnStatus(ReturnAfSendReturnBillTypeEnum.REFUNDCOMPLETED.getVal());
            ocBReturnAfSend.setReturnPaymentTime(new Date());
            // 退款成功待传对账
            ocBReturnAfSend.setToSettleStatus(ToACStatusEnum.PENDING.val());
        }
        //设置业务类型
        StCBusinessType stCBusinessType = omsRefundOrderService.queryRefundOrderType(ocBOrder);
        ocBReturnAfSend.setBusinessTypeId(stCBusinessType.getId());
        ocBReturnAfSend.setBusinessTypeCode(stCBusinessType.getEcode());
        ocBReturnAfSend.setBusinessTypeName(stCBusinessType.getEname());
        OperateUserUtils.saveOperator(ocBReturnAfSend, user);

        return ocBReturnAfSend;
    }


    /**
     * 生成发货后退款单明细(关联退换货单)
     *
     * @param ocBReturnOrderRefunds
     * @return
     */
    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItemRelation(List<OcBReturnOrderRefund> ocBReturnOrderRefunds,
                                                                       IpBTaobaoRefund ipBTaobaoRefund,
                                                                       User user, List<OcBOrderItem> orderItems,
                                                                       List<OcBReturnOrder> ocBReturnOrders) {
        Long oid = ipBTaobaoRefund.getOid();
        //List<OcBOrderItem> orderItemList = orderItems.stream().filter(p -> StringUtils.isNotEmpty(p.getOoid()) && p.getOoid().equals(oid + "")).collect(Collectors.toList());
        Map<Long, OcBOrderItem> map = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(orderItems)) {
            for (OcBOrderItem item : orderItems) {
                map.put(item.getId(), item);
            }
        }

        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();
        for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefunds) {
            if ((StringUtils.isNotEmpty(ocBReturnOrderRefund.getOid())
                    && !ocBReturnOrderRefund.getOid().equals(oid + ""))
                    || StringUtils.isEmpty(ocBReturnOrderRefund.getOid())) {
                continue;
            }
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(0L);
            ocBReturnAfSendItem.setRelationBillId(ocBReturnOrderRefund.getOcBReturnOrderId());
            List<OcBReturnOrder> list = ocBReturnOrders.stream().filter(p -> p.getId().equals(ocBReturnOrderRefund.getOcBReturnOrderId())).collect(Collectors.toList());
            ocBReturnAfSendItem.setRelationBillNo(list.get(0).getBillNo());
            //'单据类型  客退 0，拦截 1，拒收 2 ',
            ocBReturnAfSendItem.setBillType(1);
            //todo 拦截状态
            //ocBReturnAfSendItem.setInterceptStatus();
            //赠品
            ocBReturnAfSendItem.setGift(ocBReturnOrderRefund.getGiftType());
            ocBReturnAfSendItem.setPsCSkuId(ocBReturnOrderRefund.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBReturnOrderRefund.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBReturnOrderRefund.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBReturnOrderRefund.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBReturnOrderRefund.getPsCProId());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);

            //todo 规格id
            //ocBReturnAfSendItem.setPsCSpecId(ocBReturnOrderRefund.);
            //todo 规格名称
            //ocBReturnAfSendItem.setPsCSpecEname();
            //申请退货数量
            ocBReturnAfSendItem.setQtyReturnApply(ocBReturnOrderRefund.getQtyRefund());
            ocBReturnAfSendItem.setAmtReturn(ocBReturnOrderRefund.getAmtRefund());
            ocBReturnAfSendItem.setPurchaseQty(ocBReturnOrderRefund.getQtyRefund());
            OcBOrderItem orderItem = map.get(ocBReturnOrderRefund.getOcBOrderItemId());
            if (orderItem != null) {
                ocBReturnAfSendItem.setPurchaseQty(orderItem.getQty());
                ocBReturnAfSendItem.setPtProName(orderItem.getPtProName());
                ocBReturnAfSendItem.setPsCSkuEname(orderItem.getPsCSkuEname());
                ocBReturnAfSendItem.setPsCSkuPtEcode(orderItem.getPsCSkuPtEcode());
                // BigDecimal realAmt = orderItem.getRealAmt().divide(orderItem.getQty(), 4, BigDecimal.ROUND_HALF_UP).multiply(ocBReturnOrderRefund.getQtyRefund());
                ocBReturnAfSendItem.setAmtActual(orderItem.getRealAmt());

                ocBReturnAfSendItem.setOcBOrderItemId(orderItem.getId());
                if (ObjectUtil.isNotNull(orderItem.getOcBOrderId())) {
                    OcBOrder ocBOrder = ocBOrderMapper.get4AfReturn(orderItem.getOcBOrderId());
                    if (ObjectUtil.isNotNull(ocBOrder)) {
                        ocBReturnAfSendItem.setOcBOrderId(orderItem.getOcBOrderId());
                        ocBReturnAfSendItem.setBusinessTypeId(ocBOrder.getBusinessTypeId());
                        ocBReturnAfSendItem.setBusinessTypeCode(ocBOrder.getBusinessTypeCode());
                        ocBReturnAfSendItem.setBusinessTypeName(ocBOrder.getBusinessTypeName());
                    }
                }
            }

            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        return ocBReturnAfSendItems;
    }


    private List<OcBReturnAfSendItem> buildOcBReturnAfSendItem(List<OcBOrderItem> ocBOrderItems,
                                                               IpBTaobaoRefund ipBTaobaoRefund,
                                                               User user, OcBOrder ocBOrder) {
        //退款金额
        BigDecimal refundFee = ipBTaobaoRefund.getRefundFee();
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = new ArrayList<>();

        // 检查是否同时包含主品和赠品
        boolean hasMainProduct = ocBOrderItems.stream().anyMatch(item -> item.getIsGift() == null || item.getIsGift() != 1);
        boolean hasGiftProduct = ocBOrderItems.stream().anyMatch(item -> item.getIsGift() != null && item.getIsGift() == 1);

        // 如果同时包含主品和赠品，优先将金额分配给主品
        boolean amountAssigned = false;

        for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
            OcBReturnAfSendItem ocBReturnAfSendItem = new OcBReturnAfSendItem();
            //关联类型
            ocBReturnAfSendItem.setRelationBillType(1L);
            ocBReturnAfSendItem.setRelationBillId(ocBOrderItem.getOcBOrderId());
            //赠品
            ocBReturnAfSendItem.setRelationBillNo(ocBOrder.getBillNo());
            ocBReturnAfSendItem.setGift(ocBOrderItem.getIsGift() + "");
            ocBReturnAfSendItem.setPsCSkuId(ocBOrderItem.getPsCSkuId());
            ocBReturnAfSendItem.setPsCSkuEcode(ocBOrderItem.getPsCSkuEcode());
            ocBReturnAfSendItem.setPsCProEcode(ocBOrderItem.getPsCProEcode());
            ocBReturnAfSendItem.setPsCProEname(ocBOrderItem.getPsCProEname());
            ocBReturnAfSendItem.setPsCProId(ocBOrderItem.getPsCProId());
            ocBReturnAfSendItem.setPtProName(ocBOrderItem.getPtProName());
            ocBReturnAfSendItem.setPurchaseQty(ocBOrderItem.getQty());
            ocBReturnAfSendItem.setAmtActual(ocBOrderItem.getRealAmt());

            // 金额分配逻辑：如果同时包含主品和赠品，优先将金额分配给主品
            boolean isMainProduct = ocBOrderItem.getIsGift() == null || ocBOrderItem.getIsGift() != 1;
            boolean shouldAssignAmount = false;

            if (hasMainProduct && hasGiftProduct) {
                // 同时包含主品和赠品时，优先将金额分配给主品
                if (isMainProduct && !amountAssigned) {
                    shouldAssignAmount = true;
                    amountAssigned = true;
                }
            } else {
                // 只有主品或只有赠品时，分配给第一个商品
                if (!amountAssigned) {
                    shouldAssignAmount = true;
                    amountAssigned = true;
                }
            }

            if (shouldAssignAmount) {
                ocBReturnAfSendItem.setAmtHasReturn(refundFee);
                ocBReturnAfSendItem.setAmtReturn(refundFee);
            } else {
                ocBReturnAfSendItem.setAmtHasReturn(BigDecimal.ZERO);
                ocBReturnAfSendItem.setAmtReturn(BigDecimal.ZERO);
            }
            ocBReturnAfSendItem.setPsCSkuEname(ocBOrderItem.getPsCSkuEname());
            ocBReturnAfSendItem.setFreight(BigDecimal.ZERO);
            //todo 规格id
            //ocBReturnAfSendItem.setPsCSpecId(ocBReturnOrderRefund.);
            //todo 规格名称
            //ocBReturnAfSendItem.setPsCSpecEname();
            //申请退货数量
            ocBReturnAfSendItem.setRelationBillItemId(ocBOrderItem.getId());
            ocBReturnAfSendItem.setPsCSkuPtEcode(ocBOrderItem.getPsCSkuPtEcode());
            ocBReturnAfSendItem.setGift(ocBOrderItem.getGiftType());
            ocBReturnAfSendItem.setQtyReturnApply(ocBOrderItem.getQty());

            ocBReturnAfSendItem.setOcBOrderItemId(ocBOrderItem.getId());
            if (ObjectUtil.isNotNull(ocBOrderItem.getOcBOrderId())) {
                OcBOrder order = ocBOrderMapper.get4AfReturn(ocBOrderItem.getOcBOrderId());
                if (ObjectUtil.isNotNull(order)) {
                    ocBReturnAfSendItem.setOcBOrderId(order.getId());
                    ocBReturnAfSendItem.setBusinessTypeCode(order.getBusinessTypeCode());
                    ocBReturnAfSendItem.setBusinessTypeId(order.getBusinessTypeId());
                    ocBReturnAfSendItem.setBusinessTypeName(order.getBusinessTypeName());
                }
            }
            OperateUserUtils.saveOperator(ocBReturnAfSendItem, user);
            ocBReturnAfSendItems.add(ocBReturnAfSendItem);
        }
        return ocBReturnAfSendItems;
    }

    /**
     * 处理发货后退款单的数据(退货退款)
     *
     * @param ocBReturnOrderRefunds
     * @param ocBOrder
     * @param ipBTaobaoRefund
     * @return
     */
    public OcBReturnAfSendRelation taobaoRefundAfSendToReturn(List<OcBReturnOrderRefund> ocBReturnOrderRefunds,
                                                              OcBOrder ocBOrder,
                                                              IpBTaobaoRefund ipBTaobaoRefund,
                                                              User user, List<OcBOrderItem> orderItems,
                                                              List<OcBReturnOrder> ocBReturnOrders) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, ipBTaobaoRefund,
                TaobaoReturnOrderExt.SendBillType.RETURN_REFUND.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItemRelation(ocBReturnOrderRefunds,
                ipBTaobaoRefund, user, orderItems, ocBReturnOrders);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }


    /**
     * 处理发货后退款单的数据(仅退款)
     *
     * @param ocBOrderItems
     * @param ocBOrder
     * @param ipBTaobaoRefund
     * @return
     */
    public OcBReturnAfSendRelation taobaoRefundAfSendToRefundOnly(List<OcBOrderItem> ocBOrderItems,
                                                                  OcBOrder ocBOrder,
                                                                  IpBTaobaoRefund ipBTaobaoRefund,
                                                                  User user) {
        OcBReturnAfSendRelation ocBReturnAfSendRelation = new OcBReturnAfSendRelation();
        OcBReturnAfSend ocBReturnAfSend = this.buildOcBReturnAfSend(ocBOrder, ipBTaobaoRefund,
                TaobaoReturnOrderExt.SendBillType.REFUND_ONLY.getCode(), user);
        List<OcBReturnAfSendItem> ocBReturnAfSendItems = buildOcBReturnAfSendItem(ocBOrderItems, ipBTaobaoRefund, user, ocBOrder);
        ocBReturnAfSendRelation.setOcBReturnAfSend(ocBReturnAfSend);
        ocBReturnAfSendRelation.setOcBReturnAfSendItems(ocBReturnAfSendItems);
        return ocBReturnAfSendRelation;
    }


    /**
     * 通过实体仓id查询该实体仓的退货仓id
     */
    private void selectReturnCPhyWarehouse(Long cPhyWarehouseId, OcBReturnOrder returnOrder, boolean isNeedOverSetWarehouse) {
        if (cPhyWarehouseId != null) {
            CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.queryByWarehouseId(cPhyWarehouseId);
            if (cpCPhyWarehouse != null) {
                // 是否需要重设退仓：拿店仓档案中的"原退入库实体仓"
                if (isNeedOverSetWarehouse && Objects.nonNull(cpCPhyWarehouse.getOriginalReturnPhyWarehouseId())) {
                    returnOrder.setCpCPhyWarehouseInId(cpCPhyWarehouse.getOriginalReturnPhyWarehouseId());
                }
                //查询退货逻辑仓 存在修改实体仓  逻辑仓未变更以及门店必须查询逻辑仓信息判断  此处的转换会影响后续流程 -- 20220104 by caomeng
//                Long cpCPhyWarehouseId = returnOrder.getCpCPhyWarehouseInId();
//                StoreInfoQueryRequest storeInfoQueryRequest = new StoreInfoQueryRequest();
//                storeInfoQueryRequest.setPhyId(cpCPhyWarehouseId);
//                HashMap<Long, List<CpCStore>> storeInfoByPhyId = basicCpQueryService.getStoreInfoByPhyId(storeInfoQueryRequest);
//                if (MapUtils.isNotEmpty(storeInfoByPhyId)) {
//                    List<CpCStore> cpCStores = storeInfoByPhyId.get(cpCPhyWarehouseId);
//                    CpCStore cpCStore = null;
//                    if (CollectionUtils.isNotEmpty(cpCStores)) {
//                        Optional<CpCStore> returnStore = cpCStores.stream().filter(x -> DrpStoreTypeEnum.TYPE_27.getValue().equals(x.getStoretype())).findFirst();
//                        if (returnStore.isPresent()) {
//                            cpCStore = returnStore.get();
//                            returnOrder.setCpCStoreId(cpCStore.getId());
//                            returnOrder.setCpCStoreEcode(cpCStore.getEcode());
//                            returnOrder.setCpCStoreEname(cpCStore.getEname());
//                        } else {
//                            if (log.isDebugEnabled()) {
//                                log.debug("{}, 未查询到退货类型的逻辑仓信息,wareId:{}", this.getClass().getName(), cpCPhyWarehouseId);
//                            }
//                        }
//                    } else {
//                        if (log.isDebugEnabled()) {
//                            log.debug("{}, 通过实体仓ID未查询到逻辑仓信息,wareId:{}", this.getClass().getName(), cpCPhyWarehouseId);
//                        }
//                    }
//                }
                Integer wmsControlWarehouse = cpCPhyWarehouse.getWmsControlWarehouse();
                if (wmsControlWarehouse != null && wmsControlWarehouse == 1) {
                    returnOrder.setIsNeedToWms(1L);
                }
            }
        }
    }


    public void setLogisticInfo(OcBReturnOrder returnOrder, String buyerLogisticName) {
        if (StringUtils.isNotEmpty(buyerLogisticName)) {
            LogisticsInfo logisticsInfo = cpRpcService.selectLogisticsInfoByName(buyerLogisticName);
            if (logisticsInfo != null) {
                returnOrder.setCpCLogisticsEcode(logisticsInfo.getCode());
                returnOrder.setCpCLogisticsId(logisticsInfo.getId());
            }
        }
    }

    /**
     * 是否天猫周期购售后单
     * 周期购三个参数都大于0，则判断为天猫周期购售后单
     *
     * @param ipBTaobaoRefund
     * @return
     */
    public boolean isTaobaoRefundCycleBuy(IpBTaobaoRefund ipBTaobaoRefund) {
        if (Objects.isNull(ipBTaobaoRefund)) {
            return false;
        }
        if (ipBTaobaoRefund.getCyclePeriod() == null || ipBTaobaoRefund.getCyclePeriod() <= 0) {
            return false;
        }
        if (ipBTaobaoRefund.getCycleTpc() == null || ipBTaobaoRefund.getCycleTpc() <= 0) {
            return false;
        }
        return ipBTaobaoRefund.getCyclePeriodCount() != null && ipBTaobaoRefund.getCyclePeriodCount() > 0;
    }
}
