package com.jackrain.nea.oc.oms.mapper;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.jdbc.mybatis.plus.ExtentionMapper;
import com.jackrain.nea.oc.oms.model.table.IpBAlibabaAscpOrderRefund;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.jdbc.SQL;

@Mapper
public interface IpBAlibabaAscpOrderRefundMapper extends ExtentionMapper<IpBAlibabaAscpOrderRefund> {

    class AlibabaAscpOrderRefundSqlBuilder {
        public String updateRefund(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_alibaba_ascp_order_refund");
                    for (String key : map.keySet()) {
                        if (!"biz_order_code".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("biz_order_code   = #{biz_order_code}");
                }
            }.toString();
        }

        public String updateRefundAndTransCount(JSONObject map) {
            return new SQL() {
                {
                    UPDATE("ip_b_alibaba_ascp_order_refund");
                    SET("trans_count = IFNULL(trans_count, 0) + 1");
                    for (String key : map.keySet()) {
                        if (!"biz_order_code".equals(key)) {
                            SET(key + "= #{" + key + "}");
                        }
                    }
                    WHERE("biz_order_code = #{biz_order_code}");
                }
            }.toString();
        }
    }

    @Select("SELECT * FROM ip_b_alibaba_ascp_order_refund WHERE biz_order_code=#{bizOrderCode}")
    IpBAlibabaAscpOrderRefund selectAlibabaAscpOrderRefundByBizOrderCode(@Param("bizOrderCode") String bizOrderCode);

    @UpdateProvider(type = AlibabaAscpOrderRefundSqlBuilder.class, method = "updateRefundAndTransCount")
    int updateAlibabaAscpOrderRefundAndTransCount(JSONObject jsonObject);

    @UpdateProvider(type = AlibabaAscpOrderRefundSqlBuilder.class, method = "updateRefund")
    int updateAlibabaAscpOrderRefund(JSONObject jsonObject);
}