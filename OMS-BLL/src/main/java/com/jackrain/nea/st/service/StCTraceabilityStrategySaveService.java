package com.jackrain.nea.st.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityConfigurationMapper;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.TraceabilityStrategySubmitStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityConfiguration;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategy;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategyItem;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略服务类
 * @author: lijin
 * @create: 2024-12-19
 **/
@Slf4j
@Component
public class StCTraceabilityStrategySaveService {

    private static final int FIVE_HUNDRED = 500;

    @Autowired
    private StCTraceabilityStrategyMapper stCTraceabilityStrategyMapper;

    @Autowired
    private StCTraceabilityStrategyItemMapper stCTraceabilityStrategyItemMapper;

    @Autowired
    private StCTraceabilityConfigurationMapper stCTraceabilityConfigurationMapper;

    public ValueHolder save(QuerySession querySession) {
        ValueHolder holder;
        try {
            DefaultWebEvent event = querySession.getEvent();
            List<String> nullKeyList = new ArrayList<>();
            JSONObject param = JsonUtils.filterNull((JSONObject) event.getParameterValue("param"), nullKeyList);
            log.info(LogUtil.format("StCTraceabilityStrategyService.save param:{}",
                    "StCTraceabilityStrategyService.save"), param.toJSONString());
            Long objid = param.getLong("objid");
            User user = querySession.getUser();
            JSONObject fixColumn = param.getJSONObject("fixcolumn");
            JSONObject stCTraceabilityStrategy = fixColumn.getJSONObject("ST_C_TRACEABILITY_STRATEGY");
            JSONArray item = fixColumn.getJSONArray("ST_C_TRACEABILITY_STRATEGY_ITEM");
            StCTraceabilityStrategy traceabilityStrategy = JsonUtils.jsonParseClass(stCTraceabilityStrategy, StCTraceabilityStrategy.class);
            List<StCTraceabilityStrategyItem> traceabilityStrategyItems = null;
            if (CollectionUtils.isNotEmpty(item)) {
                traceabilityStrategyItems = JSON.parseArray(item.toJSONString(), StCTraceabilityStrategyItem.class);
            }

            // 校验remark字段是否超过500
            if (ObjectUtil.isNotNull(traceabilityStrategy) && StringUtils.isNotEmpty(traceabilityStrategy.getRemarks())
                    && traceabilityStrategy.getRemarks().length() > FIVE_HUNDRED) {
                String remark = traceabilityStrategy.getRemarks();
                remark = remark.substring(0, 499);
                traceabilityStrategy.setRemarks(remark);
            }

            if (objid < 0) {
                //新增
                holder = addTraceabilityStrategy(traceabilityStrategy, traceabilityStrategyItems, user);
            } else {
                //更新
                holder = updateTraceabilityStrategy(traceabilityStrategy, traceabilityStrategyItems, objid, user, stCTraceabilityStrategy);
            }
        } catch (Exception e) {
            log.error("保存异常", e);
            throw new NDSException("保存异常:" + e.getMessage());
        }
        return holder;
    }

    private ValueHolder updateTraceabilityStrategy(StCTraceabilityStrategy traceabilityStrategy,
                                                   List<StCTraceabilityStrategyItem> traceabilityStrategyItems,
                                                   Long objid, User user, JSONObject originalJson) {
        if (traceabilityStrategy != null) {
            traceabilityStrategy.setId(objid);
        } else {
            traceabilityStrategy = new StCTraceabilityStrategy();
            traceabilityStrategy.setId(objid);
        }
        checkMain(traceabilityStrategy, objid, originalJson);
        if (CollectionUtils.isNotEmpty(traceabilityStrategyItems)) {
            checkItem(traceabilityStrategyItems, objid);
            for (StCTraceabilityStrategyItem strategyItem : traceabilityStrategyItems) {
                strategyItem.setId(ModelUtil.getSequence("ST_C_TRACEABILITY_STRATEGY_ITEM"));
                strategyItem.setStCTraceabilityStrategyId(objid);
                strategyItem.setIsactive("Y");
                BaseModelUtil.initialBaseModelSystemField(strategyItem, user);
            }
            stCTraceabilityStrategyItemMapper.batchInsert(traceabilityStrategyItems);
        }
        BaseModelUtil.setupUpdateParam(traceabilityStrategy, user);
        stCTraceabilityStrategyMapper.updateById(traceabilityStrategy);
        return ValueHolderUtils.getSuccessValueHolder(objid, "ST_C_TRACEABILITY_STRATEGY", "保存成功");
    }

    private ValueHolder addTraceabilityStrategy(StCTraceabilityStrategy traceabilityStrategy,
                                                List<StCTraceabilityStrategyItem> traceabilityStrategyItems,
                                                User user) {
        checkMain(traceabilityStrategy, null, null);
        checkItem(traceabilityStrategyItems, null);

        // 设置默认提交状态为未提交
        traceabilityStrategy.setSubmitStatus(TraceabilityStrategySubmitStatusEnum.NOT_SUBMITTED.getCode());

        Long mainId = ModelUtil.getSequence("ST_C_TRACEABILITY_STRATEGY");
        traceabilityStrategy.setId(mainId);
        BaseModelUtil.initialBaseModelSystemField(traceabilityStrategy, user);

        if (CollectionUtils.isNotEmpty(traceabilityStrategyItems)) {
            for (StCTraceabilityStrategyItem strategyItem : traceabilityStrategyItems) {
                strategyItem.setId(ModelUtil.getSequence("ST_C_TRACEABILITY_STRATEGY_ITEM"));
                strategyItem.setStCTraceabilityStrategyId(mainId);
                strategyItem.setIsactive("Y");
                BaseModelUtil.initialBaseModelSystemField(strategyItem, user);
            }
        }

        saveData(traceabilityStrategy, traceabilityStrategyItems);
        return ValueHolderUtils.getSuccessValueHolder(mainId, "ST_C_TRACEABILITY_STRATEGY", "保存成功");
    }

    public void saveData(StCTraceabilityStrategy traceabilityStrategy, List<StCTraceabilityStrategyItem> traceabilityStrategyItems) {
        stCTraceabilityStrategyMapper.insert(traceabilityStrategy);
        if (CollectionUtils.isNotEmpty(traceabilityStrategyItems)) {
            stCTraceabilityStrategyItemMapper.batchInsert(traceabilityStrategyItems);
        }
    }

    private void checkMain(StCTraceabilityStrategy traceabilityStrategy, Long objid, JSONObject originalJson) {
        // 新增时的校验（objid < 0）
        if (objid == null || objid < 0) {
            // 新增时主表信息不能为空
            if (traceabilityStrategy == null) {
                throw new NDSException("主表信息不能为空");
            }
            if (traceabilityStrategy.getShopId() == null) {
                throw new NDSException("店铺不能为空");
            }
            if (traceabilityStrategy.getStartTime() == null) {
                throw new NDSException("开始时间不能为空");
            }
            if (traceabilityStrategy.getEndTime() == null) {
                throw new NDSException("结束时间不能为空");
            }
            if (traceabilityStrategy.getStartTime().after(traceabilityStrategy.getEndTime())) {
                throw new NDSException("开始时间不能大于结束时间");
            }
        } else {
            // 修改时的校验（objid > 0）
            // 从数据库获取现有数据
            StCTraceabilityStrategy existingStrategy = stCTraceabilityStrategyMapper.selectById(objid);
            if (existingStrategy == null) {
                throw new NDSException("修改的数据已不存在！");
            }
            if (!TraceabilityStrategySubmitStatusEnum.NOT_SUBMITTED.getCode().equals(existingStrategy.getSubmitStatus())
                    || !YesNoEnum.Y.getKey().equals(existingStrategy.getIsactive())) {
                throw new NDSException("当前状态不允许修改！");
            }
            if (originalJson == null || !originalJson.containsKey("REMARKS")) {
                traceabilityStrategy.setRemarks(existingStrategy.getRemarks());
            }
            if (traceabilityStrategy != null && originalJson != null) {
                // 如果JSON中存在SHOP_ID字段，则校验其值
                if (originalJson.containsKey("SHOP_ID")) {
                    if (traceabilityStrategy.getShopId() == null) {
                        throw new NDSException("店铺不能为空");
                    }
                }
                // 如果JSON中存在START_TIME字段，则校验其值
                if (originalJson.containsKey("START_TIME")) {
                    if (traceabilityStrategy.getStartTime() == null) {
                        throw new NDSException("开始时间不能为空");
                    }
                }
                // 如果JSON中存在END_TIME字段，则校验其值
                if (originalJson.containsKey("END_TIME")) {
                    if (traceabilityStrategy.getEndTime() == null) {
                        throw new NDSException("结束时间不能为空");
                    }
                }
                // 时间逻辑校验：需要获取完整的时间信息进行校验
                boolean hasStartTime = originalJson.containsKey("START_TIME");
                boolean hasEndTime = originalJson.containsKey("END_TIME");
                if (hasStartTime || hasEndTime) {
                    Date startTime = hasStartTime ? traceabilityStrategy.getStartTime() : existingStrategy.getStartTime();
                    Date endTime = hasEndTime ? traceabilityStrategy.getEndTime() : existingStrategy.getEndTime();
                    if (startTime != null && endTime != null && startTime.after(endTime)) {
                        throw new NDSException("开始时间不能大于结束时间");
                    }
                }
            }

        }
    }

    private void checkItem(List<StCTraceabilityStrategyItem> traceabilityStrategyItems, Long objid) {
        // 明细行校验：明细行不支持修改，只支持新增和删除
        if (CollectionUtils.isNotEmpty(traceabilityStrategyItems)) {
            // 收集所有SKU ID
            List<Long> skuIds = new ArrayList<>();
            Set<Long> skuIdSet = new HashSet<>();

            for (StCTraceabilityStrategyItem strategyItem : traceabilityStrategyItems) {
                if (strategyItem.getPsCSkuId() == null) {
                    throw new NDSException("商品SKU不能为空");
                }

                Long skuId = strategyItem.getPsCSkuId();

                // 检查当前明细列表中是否有重复的SKU
                if (skuIdSet.contains(skuId)) {
                    throw new NDSException("明细中存在重复的商品SKU：" + skuId);
                }

                skuIdSet.add(skuId);
                skuIds.add(skuId);
            }

            // 批量校验SKU在溯源配置表中是否存在有效数据
            checkSkuInConfigurationBatch(skuIds);

            // 校验SKU是否已在当前策略的现有明细中存在
            checkSkuDuplicateInCurrentStrategy(skuIds, objid);
        }
    }

    /**
     * 批量校验SKU在溯源配置表中是否存在有效数据
     *
     * @param skuIds 商品SKU ID列表
     */
    private void checkSkuInConfigurationBatch(List<Long> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        // 批量查询溯源配置表中的有效SKU
        QueryWrapper<StCTraceabilityConfiguration> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(StCTraceabilityConfiguration::getPsCSkuId, skuIds)
                .eq(StCTraceabilityConfiguration::getIsactive, YesNoEnum.Y.getKey());

        List<StCTraceabilityConfiguration> configurations = stCTraceabilityConfigurationMapper.selectList(queryWrapper);

        // 提取已配置的SKU ID
        Set<Long> configuredSkuIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(configurations)) {
            configuredSkuIds = configurations.stream()
                    .map(StCTraceabilityConfiguration::getPsCSkuId)
                    .collect(Collectors.toSet());
        }

        // 检查是否有未配置的SKU
        List<Long> unconfiguredSkuIds = new ArrayList<>();
        for (Long skuId : skuIds) {
            if (!configuredSkuIds.contains(skuId)) {
                unconfiguredSkuIds.add(skuId);
            }
        }

        if (CollectionUtils.isNotEmpty(unconfiguredSkuIds)) {
            throw new NDSException("以下SKU在溯源配置表不存在，请联系管理员：" + unconfiguredSkuIds);
        }
    }

    /**
     * 校验SKU是否已在当前策略的现有明细中存在
     *
     * @param skuIds 商品SKU ID列表
     * @param objid  当前策略ID（新增时为null，修改时检查现有明细）
     */
    private void checkSkuDuplicateInCurrentStrategy(List<Long> skuIds, Long objid) {
        if (CollectionUtils.isEmpty(skuIds) || objid == null || objid <= 0) {
            // 新增策略时不需要检查，因为还没有现有明细
            return;
        }

        // 查询当前策略下是否已经存在这些SKU
        QueryWrapper<StCTraceabilityStrategyItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(StCTraceabilityStrategyItem::getStCTraceabilityStrategyId, objid)
                .in(StCTraceabilityStrategyItem::getPsCSkuId, skuIds)
                .eq(StCTraceabilityStrategyItem::getIsactive, YesNoEnum.Y.getKey());

        List<StCTraceabilityStrategyItem> existingItems = stCTraceabilityStrategyItemMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(existingItems)) {
            throw new NDSException("商品SKU已在当前策略中存在，不允许重复添加！");
        }
    }
}
