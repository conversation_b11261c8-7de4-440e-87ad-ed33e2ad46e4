package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityConfigurationMapper;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityConfiguration;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 溯源配置表服务类
 * @author: lijin
 * @create: 2024-12-19
 **/
@Component
@Slf4j
public class StCTraceabilityConfigurationService {

    @Autowired
    private StCTraceabilityConfigurationMapper stCTraceabilityConfigurationMapper;

    /**
     * 溯源配置表保存
     *
     * @param querySession
     * @return
     */
    public ValueHolder save(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCTraceabilityConfigurationSaveCmdImpl.save param：{}",
                    "StCTraceabilityConfigurationSaveCmdImpl.save"), param);
        }
        User user = querySession.getUser();
        try {
            Long id = save(param, user);
            String tableName = param.getString("table");
            return ValueHolderUtils.getSuccessValueHolder(id, tableName, "保存成功");
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LogUtil.format("新增异常"), e);
            }
            return ValueHolderUtils.getFailValueHolder(e.getMessage());
        }
    }

    /**
     * 保存逻辑（仅支持新增）
     *
     * @param param
     * @param user
     * @return 新增记录的ID
     */
    private Long save(JSONObject param, User user) {
        JSONObject fixColumn = param.getJSONObject("fixcolumn");

        Long objid = param.getLong("objid");
        String tableName = param.getString("table");
        JSONObject jsonObject = fixColumn.getJSONObject(tableName);
        if (Objects.isNull(jsonObject)) {
            throw new NDSException("表数据不能为空");
        }

        // 检查是否为修改操作，如果是则抛出异常
        if (objid != null && objid > 0) {
            throw new NDSException("该接口仅支持新增操作，不支持修改！");
        }

        StCTraceabilityConfiguration stCTraceabilityConfiguration
                = JSONObject.parseObject(jsonObject.toJSONString(), StCTraceabilityConfiguration.class);

        // 检查商品SKU是否已存在配置
        if (stCTraceabilityConfiguration.getPsCSkuId() != null) {
            QueryWrapper<StCTraceabilityConfiguration> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(StCTraceabilityConfiguration::getPsCSkuId, stCTraceabilityConfiguration.getPsCSkuId());
            queryWrapper.lambda().eq(StCTraceabilityConfiguration::getIsactive, YesNoEnum.Y.getKey());
            List<StCTraceabilityConfiguration> existingConfigs = stCTraceabilityConfigurationMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(existingConfigs)) {
                throw new NDSException("该商品SKU已存在溯源配置，无法重复配置！");
            }
        }

        // 新增操作
        Long id = ModelUtil.getSequence(tableName.toLowerCase());
        stCTraceabilityConfiguration.setId(id);
        StBeanUtils.makeCreateField(stCTraceabilityConfiguration, user);
        stCTraceabilityConfigurationMapper.insert(stCTraceabilityConfiguration);
        return id;
    }

    /**
     * 溯源配置表删除
     *
     * @param querySession
     * @return
     */
    public ValueHolder delete(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("StCTraceabilityConfigurationDeleteCmdImpl.delete param：{}",
                    "StCTraceabilityConfigurationDeleteCmdImpl.delete"), param);
        }
        try {
            Long objid = param.getLong("objid");
            stCTraceabilityConfigurationMapper.delete(new QueryWrapper<StCTraceabilityConfiguration>()
                    .lambda().eq(StCTraceabilityConfiguration::getId, objid));
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LogUtil.format("删除异常"), e);
            }
            return ValueHolderUtils.getFailValueHolder("删除失败！");
        }
        return ValueHolderUtils.getSuccessValueHolder("删除成功！");
    }

}
