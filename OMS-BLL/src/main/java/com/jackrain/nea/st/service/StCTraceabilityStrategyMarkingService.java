package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.OmsOrderStatus;
import com.jackrain.nea.oc.oms.model.enums.TraceabilityStrategySubmitStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderParam;
import com.jackrain.nea.oc.oms.model.relation.StCTraceabilityStrategyRelations;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategy;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategyItem;
import com.jackrain.nea.oc.oms.nums.OrderLogTypeEnum;
import com.jackrain.nea.oc.oms.services.OmsOrderLogService;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.StRedisKeyResources;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略打标服务
 * @author: lijin
 * @create: 2024-12-19
 **/
@Slf4j
@Component
public class StCTraceabilityStrategyMarkingService {

    @Autowired
    private StCTraceabilityStrategyMapper stCTraceabilityStrategyMapper;

    @Autowired
    private StCTraceabilityStrategyItemMapper stCTraceabilityStrategyItemMapper;

    @Autowired
    private OcBOrderMapper ocBOrderMapper;

    @Autowired
    private OcBOrderItemMapper ocBOrderItemMapper;

    @Autowired
    private OmsOrderLogService omsOrderLogService;

    /**
     * 溯源标记策略缓存过期时间（分钟）
     */
    private static final int TRACEABILITY_CACHE_EXPIRE_MINUTES = 30;

    /**
     * 处理溯源标记策略
     *
     * @param orderParam 订单参数
     * @param user       用户
     */
    public void handleTraceabilityMarkingService(OcBOrderParam orderParam, User user) {
        OcBOrder ocBOrder = orderParam.getOcBOrder();
        log.info(LogUtil.format("StCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService start orderId:{}",
                "StCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService"), ocBOrder.getId());

        try {
            //判断订单状态
            if (!OmsOrderStatus.UNCONFIRMED.toInteger().equals(ocBOrder.getOrderStatus())) {
                log.info(LogUtil.format("OmsStickerService.handelStickerService 订单非待审核不走溯源标记策略  orderId:{}",
                        "OmsStickerService.handelStickerService"), ocBOrder.getId());
                return;
            }
            // 1. 根据店铺信息查询溯源标记策略（走缓存）
            List<StCTraceabilityStrategyRelations> strategyRelations = getTraceabilityStrategyRelationsByShopId(ocBOrder.getCpCShopId());
            if (CollectionUtils.isEmpty(strategyRelations)) {
                log.info(LogUtil.format("StCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService 不存在溯源标记策略 orderId:{}",
                        "StCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService"), ocBOrder.getId());
                // 没有策略时，将订单和明细的溯源标记设为null
                updateAllTraceabilityStatus(ocBOrder, orderParam.getOrderItemList(), null);
                return;
            }

            // 2. 根据订单的支付时间获取符合条件的StCTraceabilityStrategyRelations
            Date payTime = ocBOrder.getPayTime();
            if (payTime == null) {
                log.info(LogUtil.format("StCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService 支付时间为空 orderId:{}",
                        "StCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService"), ocBOrder.getId());
                // 支付时间为空时，将订单和明细的溯源标记设为null
                updateAllTraceabilityStatus(ocBOrder, orderParam.getOrderItemList(), null);
                return;
            }

            // 3. 按照支付时间过滤策略
            List<StCTraceabilityStrategyRelations> filteredRelations = strategyRelations.stream()
                    .filter(relation -> {
                        StCTraceabilityStrategy strategy = relation.getStCTraceabilityStrategy();
                        return payTime.compareTo(strategy.getStartTime()) >= 0 &&
                                payTime.compareTo(strategy.getEndTime()) <= 0;
                    })
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filteredRelations)) {
                log.info(LogUtil.format("StCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService 没有符合支付时间的溯源标记策略 orderId:{} payTime:{}",
                        "StCTraceabilityStrategyMarkingService.handleTraceabilityMarkingService"), ocBOrder.getId(), payTime);
                // 没有符合条件的策略时，将订单和明细的溯源标记设为null
                updateAllTraceabilityStatus(ocBOrder, orderParam.getOrderItemList(), null);
                return;
            }
            // 4. 如果存在多个，按更新时间倒序排列，更新时间为空的放在最后，取第一个（最新的）
            if (filteredRelations.size() > 1) {
                filteredRelations.sort(Comparator.comparing(
                        (StCTraceabilityStrategyRelations relation) -> relation.getStCTraceabilityStrategy().getModifieddate(),
                        Comparator.nullsLast(Comparator.reverseOrder())
                ));
            }
            StCTraceabilityStrategyRelations selectedRelation = filteredRelations.get(0);
            // 5. 收集策略明细中的psCSkuId
            Set<Long> strategySkuIds = new HashSet<>();
            List<StCTraceabilityStrategyItem> strategyItems = selectedRelation.getStCTraceabilityStrategyItems();
            if (CollectionUtils.isNotEmpty(strategyItems)) {
                strategySkuIds = strategyItems.stream()
                        .map(StCTraceabilityStrategyItem::getPsCSkuId)
                        .collect(Collectors.toSet());
            }
            // 6. 判断订单明细是否命中策略中的SKU，收集匹配和未匹配的明细ID
            List<OcBOrderItem> orderItems = orderParam.getOrderItemList();
            List<Long> matchedItemIds = new ArrayList<>();
            List<Long> unmatchedItemIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(orderItems)) {
                for (OcBOrderItem orderItem : orderItems) {
                    if (strategySkuIds.contains(orderItem.getPsCSkuId())) {
                        matchedItemIds.add(orderItem.getId());
                    } else {
                        unmatchedItemIds.add(orderItem.getId());
                    }
                }
            }
            // 7. 批量更新明细状态
            if (CollectionUtils.isNotEmpty(matchedItemIds)) {
                batchUpdateOrderItemTraceabilityStatus(matchedItemIds, YesNoEnum.ONE.getVal());
            }
            if (CollectionUtils.isNotEmpty(unmatchedItemIds)) {
                batchUpdateOrderItemTraceabilityStatus(unmatchedItemIds, null);
            }
            // 8. 根据是否有明细命中来更新主表状态
            Integer orderTraceabilityStatus = CollectionUtils.isNotEmpty(matchedItemIds) ? YesNoEnum.ONE.getVal() : null;
            updateOrderTraceabilityStatus(ocBOrder.getId(), orderTraceabilityStatus);

            // 9. 如果打标成功，添加操作日志
            if (CollectionUtils.isNotEmpty(matchedItemIds)) {
                String logContent = "命中溯源标记策略成功，策略ID[" + selectedRelation.getStCTraceabilityStrategy().getId() + "]";
                omsOrderLogService.addUserOrderLog(ocBOrder.getId(), ocBOrder.getBillNo(),
                        OrderLogTypeEnum.TRACEABILITY_MARKING.getKey(), logContent, "", "", user);
            }
        } catch (Exception e) {
            log.error(LogUtil.format("溯源标记策略执行异常,orderId:{}", "溯源标记策略执行异常"), ocBOrder.getId(), e);
        }
    }

    /**
     * 根据店铺获取溯源标记策略（使用缓存）
     *
     * @param shopId 店铺ID
     * @return 溯源标记策略列表
     */
    private List<StCTraceabilityStrategyRelations> getTraceabilityStrategyRelationsByShopId(Long shopId) {
        if (shopId == null) {
            return Collections.emptyList();
        }

        String cacheKey = StRedisKeyResources.buildTraceabilityStrategyCacheKey(shopId);
        String cacheValue = (String) RedisOpsUtil.getStrRedisTemplate().opsForValue().get(cacheKey);

        List<StCTraceabilityStrategyRelations> result;
        if (StringUtils.isNotEmpty(cacheValue)) {
            // 从缓存中获取
            result = JSON.parseArray(cacheValue, StCTraceabilityStrategyRelations.class);
        } else {
            // 从数据库中获取
            result = new ArrayList<>();
            // 查询已审核且可用的溯源标记策略
            List<StCTraceabilityStrategy> strategies = stCTraceabilityStrategyMapper.selectList(
                    new LambdaQueryWrapper<StCTraceabilityStrategy>()
                            .eq(StCTraceabilityStrategy::getShopId, shopId)
                            .eq(StCTraceabilityStrategy::getSubmitStatus, TraceabilityStrategySubmitStatusEnum.SUBMITTED.getCode())
                            .eq(StCTraceabilityStrategy::getIsactive, YesNoEnum.Y.getKey())
                            .ge(StCTraceabilityStrategy::getEndTime, getHalfYearAgoDate())
            );

            if (CollectionUtils.isNotEmpty(strategies)) {
                for (StCTraceabilityStrategy strategy : strategies) {
                    // 查询策略明细
                    List<StCTraceabilityStrategyItem> items = stCTraceabilityStrategyItemMapper.selectList(
                            new LambdaQueryWrapper<StCTraceabilityStrategyItem>()
                                    .eq(StCTraceabilityStrategyItem::getStCTraceabilityStrategyId, strategy.getId())
                                    .eq(StCTraceabilityStrategyItem::getIsactive, YesNoEnum.Y.getKey())
                    );
                    if (CollectionUtils.isNotEmpty(items)) {
                        result.add(new StCTraceabilityStrategyRelations(strategy, items));
                    }
                }
            }

            // 写入缓存，即使没有数据也写入缓存，避免空查询
            RedisOpsUtil.getStrRedisTemplate().opsForValue().set(
                    cacheKey,
                    JSON.toJSONString(result),
                    TRACEABILITY_CACHE_EXPIRE_MINUTES,
                    TimeUnit.MINUTES
            );
        }
        return result;
    }

    /**
     * 更新订单和订单明细的溯源标记状态（统一状态）
     *
     * @param ocBOrder           订单
     * @param orderItems         订单明细列表
     * @param traceabilityStatus 溯源标记状态（1-命中，null-未命中）
     */
    private void updateAllTraceabilityStatus(OcBOrder ocBOrder, List<OcBOrderItem> orderItems, Integer traceabilityStatus) {
        Date currentTime = new Date();

        // 更新订单的溯源标记状态
        updateOrderTraceabilityStatus(ocBOrder.getId(), traceabilityStatus);

        // 批量更新订单明细的溯源标记状态（使用LambdaUpdateWrapper确保null值也能更新）
        if (CollectionUtils.isNotEmpty(orderItems)) {
            List<Long> orderItemIds = orderItems.stream()
                    .map(OcBOrderItem::getId)
                    .collect(Collectors.toList());

            LambdaUpdateWrapper<OcBOrderItem> itemUpdateWrapper = new LambdaUpdateWrapper<>();
            itemUpdateWrapper.in(OcBOrderItem::getId, orderItemIds)
                    .set(OcBOrderItem::getModifieddate, currentTime);
            if (traceabilityStatus != null) {
                itemUpdateWrapper.set(OcBOrderItem::getIsTraceability, traceabilityStatus);
            } else {
                itemUpdateWrapper.setSql("IS_TRACEABILITY = NULL");
            }
            ocBOrderItemMapper.update(null, itemUpdateWrapper);
        }

        log.info(LogUtil.format("批量更新溯源标记状态完成,orderId:{}, 状态:{}", "批量更新溯源标记状态完成"),
                ocBOrder.getId(), traceabilityStatus);
    }

    /**
     * 更新订单的溯源标记状态
     *
     * @param orderId            订单ID
     * @param traceabilityStatus 溯源标记状态
     */
    private void updateOrderTraceabilityStatus(Long orderId, Integer traceabilityStatus) {
        Date currentTime = new Date();
        LambdaUpdateWrapper<OcBOrder> orderUpdateWrapper = new LambdaUpdateWrapper<>();
        orderUpdateWrapper.eq(OcBOrder::getId, orderId)
                .set(OcBOrder::getModifieddate, currentTime);
        if (traceabilityStatus != null) {
            orderUpdateWrapper.set(OcBOrder::getIsTraceability, traceabilityStatus);
        } else {
            orderUpdateWrapper.setSql("IS_TRACEABILITY = NULL");
        }
        ocBOrderMapper.update(null, orderUpdateWrapper);
    }

    /**
     * 批量更新订单明细的溯源标记状态
     *
     * @param orderItemIds       订单明细ID列表
     * @param traceabilityStatus 溯源标记状态
     */
    private void batchUpdateOrderItemTraceabilityStatus(List<Long> orderItemIds, Integer traceabilityStatus) {
        if (CollectionUtils.isEmpty(orderItemIds)) {
            return;
        }

        Date currentTime = new Date();
        LambdaUpdateWrapper<OcBOrderItem> itemUpdateWrapper = new LambdaUpdateWrapper<>();
        itemUpdateWrapper.in(OcBOrderItem::getId, orderItemIds)
                .set(OcBOrderItem::getModifieddate, currentTime);
        if (traceabilityStatus != null) {
            itemUpdateWrapper.set(OcBOrderItem::getIsTraceability, traceabilityStatus);
        } else {
            itemUpdateWrapper.setSql("IS_TRACEABILITY = NULL");
        }
        ocBOrderItemMapper.update(null, itemUpdateWrapper);

        log.info(LogUtil.format("批量更新订单明细溯源标记状态完成,明细数量:{}, 状态:{}", "批量更新订单明细溯源标记状态完成"),
                orderItemIds.size(), traceabilityStatus);
    }

    /**
     * 获取半年前的时间
     *
     * @return 半年前的时间
     */
    private Date getHalfYearAgoDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -6);
        return calendar.getTime();
    }

    /**
     * 检查并更新订单溯源标记状态
     * 如果订单状态为未确认且订单有溯源标记，但所有明细都没有溯源标记，则清除订单的溯源标记
     *
     * @param ocBOrder 订单对象
     */
    public void checkAndUpdateOrderTraceabilityStatus(OcBOrder ocBOrder) {
        List<OcBOrderItem> itemList = ocBOrderItemMapper.selectOrderItemListOccupy(ocBOrder.getId());
        if (CollectionUtils.isNotEmpty(itemList)) {
            long count = itemList.stream().filter(s -> s.getIsTraceability() != null && s.getIsTraceability() == 1).count();
            if (count == 0) {
                // 更新订单的溯源标记状态为null
                Date currentTime = new Date();
                LambdaUpdateWrapper<OcBOrder> orderUpdateWrapper = new LambdaUpdateWrapper<>();
                orderUpdateWrapper.eq(OcBOrder::getId, ocBOrder.getId())
                        .setSql("IS_TRACEABILITY = NULL")
                        .set(OcBOrder::getModifieddate, currentTime);
                ocBOrderMapper.update(null, orderUpdateWrapper);

                log.info(LogUtil.format("清除订单溯源标记状态,orderId:{}", "清除订单溯源标记状态"), ocBOrder.getId());
            }
        }

    }

    /**
     * 清除订单及其所有明细的溯源标记
     * 用于手工寻源等场景，需要完全清除订单的溯源标记状态
     *
     * @param ocBOrder 订单对象
     */
    public void clearOrderTraceabilityStatus(OcBOrder ocBOrder) {
        Date currentTime = new Date();
        // 更新订单的溯源标记状态为null
        LambdaUpdateWrapper<OcBOrder> orderUpdateWrapper = new LambdaUpdateWrapper<>();
        orderUpdateWrapper.eq(OcBOrder::getId, ocBOrder.getId())
                .setSql("IS_TRACEABILITY = NULL")
                .set(OcBOrder::getModifieddate, currentTime);
        ocBOrderMapper.update(null, orderUpdateWrapper);

        // 批量更新该订单所有明细的溯源标记状态为null
        LambdaUpdateWrapper<OcBOrderItem> itemUpdateWrapper = new LambdaUpdateWrapper<>();
        itemUpdateWrapper.eq(OcBOrderItem::getOcBOrderId, ocBOrder.getId())
                .setSql("IS_TRACEABILITY = NULL")
                .set(OcBOrderItem::getModifieddate, currentTime);
        ocBOrderItemMapper.update(null, itemUpdateWrapper);
        log.info(LogUtil.format("清除订单及明细溯源标记状态,orderId:{}", "清除订单及明细溯源标记状态"), ocBOrder.getId());
    }
}
