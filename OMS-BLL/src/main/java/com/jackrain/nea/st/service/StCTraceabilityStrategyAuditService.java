package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.TraceabilityStrategySubmitStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategy;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategyItem;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.StRedisKeyResources;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略审核服务
 * @author: lijin
 * @create: 2024-12-19
 **/
@Slf4j
@Component
public class StCTraceabilityStrategyAuditService {

    @Autowired
    private StCTraceabilityStrategyMapper stCTraceabilityStrategyMapper;

    @Autowired
    private StCTraceabilityStrategyItemMapper stCTraceabilityStrategyItemMapper;

    public ValueHolder auditTraceabilityStrategy(QuerySession querySession) {
        // 1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        User user = querySession.getUser();

        // 生成Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        List<Long> ids = JSON.parseArray(auditArray.toJSONString(), Long.class);
        HashMap<Long, Object> errMap = new HashMap<>();

        for (Long id : ids) {
            try {
                auditStrategy(id, user);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        return StBeanUtils.getExcuteValueHolder(ids.size(), errMap);
    }

    private void auditStrategy(Long id, User user) {
        StCTraceabilityStrategy strategy = stCTraceabilityStrategyMapper.selectById(id);
        if (strategy == null) {
            throw new NDSException("当前记录已不存在！");
        }

        // 检查状态：仅未审核(0)的数据可以审核
        if (strategy.getSubmitStatus() == null ||
                !TraceabilityStrategySubmitStatusEnum.NOT_SUBMITTED.getCode().equals(strategy.getSubmitStatus())) {
            throw new NDSException("当前策略的状态不是未审核，不允许审核！");
        }

        // 检查可用状态：仅可用的数据可以审核
        if (!YesNoEnum.Y.getKey().equals(strategy.getIsactive())) {
            throw new NDSException("当前策略已不可用，不允许审核！");
        }

        // 检查策略明细：策略必须有明细才能审核
        checkStrategyItems(id);

        // 检查时间交集：不能存在时间有交集且已审核的数据
        checkTimeConflict(strategy);

        // 执行审核操作
        StCTraceabilityStrategy updateStrategy = new StCTraceabilityStrategy();
        updateStrategy.setId(id);
        updateStrategy.setSubmitStatus(TraceabilityStrategySubmitStatusEnum.SUBMITTED.getCode());
        updateStrategy.setRemarks(strategy.getRemarks());
        updateStrategy.setModifiername(user.getName());
        updateStrategy.setModifierid(Long.valueOf(user.getId()));
        updateStrategy.setModifieddate(new Date());

        int result = stCTraceabilityStrategyMapper.updateById(updateStrategy);
        if (result <= 0) {
            throw new NDSException("审核失败！");
        }
        // 清除该店铺的溯源策略缓存
        clearTraceabilityStrategyCache(strategy.getShopId());
    }

    /**
     * 检查策略明细
     *
     * @param strategyId 策略ID
     */
    private void checkStrategyItems(Long strategyId) {
        // 查询策略下的有效明细
        QueryWrapper<StCTraceabilityStrategyItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(StCTraceabilityStrategyItem::getStCTraceabilityStrategyId, strategyId)
                .eq(StCTraceabilityStrategyItem::getIsactive, YesNoEnum.Y.getKey());

        List<StCTraceabilityStrategyItem> items = stCTraceabilityStrategyItemMapper.selectList(queryWrapper);

        if (CollectionUtils.isEmpty(items)) {
            throw new NDSException("策略没有明细商品编码，不允许审核！");
        }
    }

    /**
     * 检查时间交集冲突
     *
     * @param strategy 待审核的策略
     */
    private void checkTimeConflict(StCTraceabilityStrategy strategy) {
        // 查询同店铺下已审核的策略
        QueryWrapper<StCTraceabilityStrategy> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(StCTraceabilityStrategy::getShopId, strategy.getShopId())
                .eq(StCTraceabilityStrategy::getSubmitStatus, TraceabilityStrategySubmitStatusEnum.SUBMITTED.getCode())
                .eq(StCTraceabilityStrategy::getIsactive, YesNoEnum.Y.getKey())
                .ne(StCTraceabilityStrategy::getId, strategy.getId());

        List<StCTraceabilityStrategy> existingStrategies = stCTraceabilityStrategyMapper.selectList(queryWrapper);

        if (existingStrategies != null && !existingStrategies.isEmpty()) {
            for (StCTraceabilityStrategy existingStrategy : existingStrategies) {
                if (hasTimeOverlap(strategy.getStartTime(), strategy.getEndTime(),
                        existingStrategy.getStartTime(), existingStrategy.getEndTime())) {
                    throw new NDSException("时间范围与已审核的策略存在交集！");
                }
            }
        }
    }

    /**
     * 判断两个时间段是否有重叠
     *
     * @param start1 时间段1开始时间
     * @param end1   时间段1结束时间
     * @param start2 时间段2开始时间
     * @param end2   时间段2结束时间
     * @return 是否有重叠
     */
    private boolean hasTimeOverlap(Date start1, Date end1, Date start2, Date end2) {
        // 两个时间段有重叠的条件（包括边界相等）：
        // 1. start1 <= end2 且 end1 >= start2
        // 如果 start1 == end2 或 end1 == start2，也认为是有交集的
        return start1.compareTo(end2) <= 0 && end1.compareTo(start2) >= 0;
    }

    /**
     * 清除溯源策略缓存
     *
     * @param shopId 店铺ID
     */
    private void clearTraceabilityStrategyCache(Long shopId) {
        try {
            String cacheKey = StRedisKeyResources.buildTraceabilityStrategyCacheKey(shopId);
            RedisOpsUtil.getStrRedisTemplate().delete(cacheKey);
            log.info("清除溯源策略缓存成功，店铺ID：{}，缓存key：{}", shopId, cacheKey);
        } catch (Exception e) {
            log.error("清除溯源策略缓存失败，店铺ID：{}", shopId, e);
        }
    }
}
