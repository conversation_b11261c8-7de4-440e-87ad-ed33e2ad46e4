package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityStrategyItemMapper;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.TraceabilityStrategySubmitStatusEnum;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategy;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategyItem;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略删除明细服务类
 * @author: lijin
 * @create: 2024-12-19
 **/
@Slf4j
@Component
public class StCTraceabilityStrategyDelItemService {

    @Autowired
    private StCTraceabilityStrategyMapper stCTraceabilityStrategyMapper;

    @Autowired
    private StCTraceabilityStrategyItemMapper stCTraceabilityStrategyItemMapper;

    /**
     * 溯源标记策略删除明细
     *
     * @param querySession 入参
     * @return
     */
    public ValueHolder traceabilityStrategyDelItemService(QuerySession querySession) {
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(
                JSON.toJSONStringWithDateFormat(event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        boolean delMainFlag = param.getBoolean("isdelmtable"); // 是否删除主表
        Long id = param.getLong("objid");
        User user = querySession.getUser();

        if (delMainFlag) {
            // 删除主表
            if (Objects.nonNull(id) && id > 0) {
                // 主表删除逻辑可以在这里实现
                throw new NDSException("暂不支持删除主表操作！");
            }
        } else {
            // 删除明细
            JSONObject tabitem = param.getJSONObject("tabitem");
            if (!CollectionUtils.isEmpty(tabitem)) {
                JSONArray itemArray = tabitem.getJSONArray("ST_C_TRACEABILITY_STRATEGY_ITEM");
                JSONArray errorArray = new JSONArray();
                if (!CollectionUtils.isEmpty(itemArray)) {
                    deleteItemByID(itemArray, errorArray, querySession);
                }
                // 修改主表信息
                updateTraceabilityStrategy(id, user);
                return StBeanUtils.getExcuteValueHolder(errorArray);
            }
        }
        throw new NDSException("当前记录已不存在！");
    }

    /**
     * 更新溯源标记策略主表
     *
     * @param id   主表ID
     * @param user 用户
     */
    private void updateTraceabilityStrategy(Long id, User user) {
        StCTraceabilityStrategy existingStrategy = stCTraceabilityStrategyMapper.selectById(id);
        if (existingStrategy == null) {
            throw new NDSException("当前记录已不存在！");
        }

        // 只有未审核状态才允许删除明细
        if (existingStrategy.getSubmitStatus() == null ||
                !TraceabilityStrategySubmitStatusEnum.NOT_SUBMITTED.getCode().equals(existingStrategy.getSubmitStatus())) {
            throw new NDSException("当前策略的状态不是未审核，不允许删除明细！");
        }

        StCTraceabilityStrategy updateStrategy = new StCTraceabilityStrategy();
        updateStrategy.setId(id);
        updateStrategy.setModifierid(Long.valueOf(user.getId())); // 修改人id
        updateStrategy.setModifiername(user.getName()); // 修改人用户名
        updateStrategy.setModifieddate(new Date()); // 修改时间

        int result = stCTraceabilityStrategyMapper.updateById(updateStrategy);
        if (result <= 0) {
            throw new NDSException("修改主表的信息失败！");
        }
    }

    /**
     * 根据ID删除明细
     *
     * @param itemArray    明细ID数组
     * @param errorArray   错误信息数组
     * @param querySession 查询会话
     */
    private void deleteItemByID(JSONArray itemArray, JSONArray errorArray, QuerySession querySession) {
        Map<Long, String> beforeDelObjMap = new HashMap<>();

        for (int i = 0; i < itemArray.size(); i++) {
            Long itemId = itemArray.getLong(i);
            StCTraceabilityStrategyItem strategyItem = stCTraceabilityStrategyItemMapper.selectById(itemId);
//            if (strategyItem != null) {
//                beforeDelObjMap.put(itemId, JSON.toJSONString(strategyItem));
//            }
            if (stCTraceabilityStrategyItemMapper.deleteById(itemId) <= 0) {
                errorArray.add(StBeanUtils.getJsonObjectInfo(itemId, "删除明细失败"));
            }
        }
//        querySession.setAttribute("beforeDelObjMap", beforeDelObjMap);
    }
}
