package com.jackrain.nea.st.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.StCTraceabilityStrategyMapper;
import com.jackrain.nea.oc.oms.model.enums.TraceabilityStrategySubmitStatusEnum;
import com.jackrain.nea.oc.oms.model.enums.YesNoEnum;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategy;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.StRedisKeyResources;
import com.jackrain.nea.util.StBeanUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略结案服务
 * @author: lijin
 * @create: 2024-12-19
 **/
@Slf4j
@Component
public class StCTraceabilityStrategyCloseService {

    @Autowired
    private StCTraceabilityStrategyMapper stCTraceabilityStrategyMapper;

    public ValueHolder closeTraceabilityStrategy(QuerySession querySession) {
        // 1.获取传入参数
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(event.getParameterValue("param"),
                "yyyy-MM-dd HH:mm:ss", SerializerFeature.WriteMapNullValue), Feature.OrderedField);
        if (param == null) {
            throw new NDSException("参数为空！");
        }
        User user = querySession.getUser();

        // 生成Json数组
        JSONArray auditArray = StBeanUtils.makeAuditJsonArray(param);
        List<Long> ids = JSON.parseArray(auditArray.toJSONString(), Long.class);
        HashMap<Long, Object> errMap = new HashMap<>();

        for (Long id : ids) {
            try {
                closeStrategy(id, user);
            } catch (Exception e) {
                errMap.put(id, e.getMessage());
            }
        }
        return StBeanUtils.getExcuteValueHolder(ids.size(), errMap);
    }

    private void closeStrategy(Long id, User user) {
        StCTraceabilityStrategy strategy = stCTraceabilityStrategyMapper.selectById(id);
        if (strategy == null) {
            throw new NDSException("当前记录已不存在！");
        }

        // 检查状态：仅已审核(1)的数据可以结案
        if (strategy.getSubmitStatus() == null ||
                !TraceabilityStrategySubmitStatusEnum.SUBMITTED.getCode().equals(strategy.getSubmitStatus())) {
            throw new NDSException("当前策略的状态不是已审核，不允许结案！");
        }

        // 检查可用状态：仅可用的数据可以结案
        if (!YesNoEnum.Y.getKey().equals(strategy.getIsactive())) {
            throw new NDSException("当前策略已不可用，不允许结案！");
        }

        // 执行结案操作
        StCTraceabilityStrategy updateStrategy = new StCTraceabilityStrategy();
        updateStrategy.setId(id);
        updateStrategy.setSubmitStatus(TraceabilityStrategySubmitStatusEnum.CLOSED.getCode());
        updateStrategy.setIsactive(YesNoEnum.N.getKey());
        updateStrategy.setRemarks(strategy.getRemarks());
        updateStrategy.setModifiername(user.getName());
        updateStrategy.setModifierid(Long.valueOf(user.getId()));
        updateStrategy.setModifieddate(new Date());

        int result = stCTraceabilityStrategyMapper.updateById(updateStrategy);
        if (result <= 0) {
            throw new NDSException("结案失败！");
        }
        // 清除该店铺的溯源策略缓存
        clearTraceabilityStrategyCache(strategy.getShopId());
    }

    /**
     * 清除溯源策略缓存
     *
     * @param shopId 店铺ID
     */
    private void clearTraceabilityStrategyCache(Long shopId) {
        try {
            String cacheKey = StRedisKeyResources.buildTraceabilityStrategyCacheKey(shopId);
            RedisOpsUtil.getStrRedisTemplate().delete(cacheKey);
            log.info("清除溯源策略缓存成功，店铺ID：{}，缓存key：{}", shopId, cacheKey);
        } catch (Exception e) {
            log.error("清除溯源策略缓存失败，店铺ID：{}", shopId, e);
        }
    }
}
