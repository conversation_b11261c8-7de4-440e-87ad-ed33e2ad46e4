package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentLogDO;
import com.jackrain.nea.ac.model.QueryPayableAdjustmentResult;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentLogMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: 陈俊明
 * @since: 2019-04-08
 * @create at : 2019-04-08 14:27
 */
@Component
@Slf4j
@Transactional
public class PayableAdjustmentSelectService extends CommandAdapter {
    @Resource
    private AcFPayableAdjustmentMapper mainMapper;

    @Resource
    private AcFPayableAdjustmentItemMapper itemMapper;

    @Resource
    private AcFPayableAdjustmentLogMapper logMapper;

    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        QueryPayableAdjustmentResult queryResult = new QueryPayableAdjustmentResult();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        Long objid = param.getLong("objid");

        if (objid != null && objid > 0) {
            AcFPayableAdjustmentDO mainDO = mainMapper.selectById(objid);
            List<AcFPayableAdjustmentItemDO> itemDOList  = itemMapper.listByItemId(objid);
            List<AcFPayableAdjustmentLogDO> logDOList = logMapper.listByLogId(objid);
            queryResult.setAcFPayableAdjustment(mainDO);
            queryResult.setAcFPayableAdjustmentItemList(itemDOList);
            queryResult.setAcFPayableAdjustmentLogList(logDOList);

            valueHolder.put("code", 0);
            valueHolder.put("message", "查询成功");
            valueHolder.put("data", queryResult);
        } else {
            valueHolder.put("code", -1);
            valueHolder.put("message", "查询失败");
        }

        return valueHolder;
    }
}
