package com.jackrain.nea.ac.service.converter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.ac.service.InvoiceStrategyService;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.cp.result.CpCSupplier;
import com.jackrain.nea.oc.oms.mapper.ac.AcFTaxMachineManageMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.*;
import com.jackrain.nea.oc.oms.nums.OmsParamConstant;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.result.ProExtResult;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @ClassName ApplyInvoiceConvert
 * @Description 订单申请开票数据转换成订单开票表数据
 * @Date 2022/9/5 下午7:11
 * @Created by wuhang
 */
@Component
@Slf4j
public class ApplyInvoiceConvert {

    @Autowired
    private InvoiceStrategyService invoiceStrategyService;

    @Reference(group = "ps", version = "1.0")
    private SkuLikeQueryCmd skuLikeQueryCmd;

    private Random random = new Random();

    private static final int INVOICE_REMARK_CUT = 150;

    /**
     * 开蓝票
     * @param apply 申请开票数据
     * @param strategy 开票策略
     * @param orders 零售发货单
     * @param id 主键id
     * @param freezeStatus 冻结状态
     * @return
     */
    public AcFOrderInvoice applyChange(AcFInvoiceApply apply, StCInvoiceStrategy strategy, List<OcBOrder> orders, Long id, String freezeStatus, CpCSupplier company,String taxMachineNo, User user){
        AcFOrderInvoice orderInvoice = new AcFOrderInvoice();
        orderInvoice.setId(id);
        orderInvoice.setCpCShopId(apply.getCpCShopId());
        orderInvoice.setCpCShopEcode(apply.getCpCShopEcode());
        orderInvoice.setCpCShopTitle(apply.getCpCShopTitle());
        orderInvoice.setCpCPlatformId(apply.getCpCPlatformId());
        orderInvoice.setCpCPlatformEcode(apply.getCpCPlatformEcode());
        orderInvoice.setCpCPlatformEname(apply.getCpCPlatformEname());
        orderInvoice.setTid(apply.getTid());
        orderInvoice.setInvoiceNode(strategy.getInvoiceNode());
        orderInvoice.setIsSyncPlatform(strategy.getIsSyncPlatform());
        orderInvoice.setApplyInvoiceDate(apply.getApplyDate());
        orderInvoice.setCpCSupplierId(company.getId());
        orderInvoice.setCpCSupplierEcode(company.getEcode());
        orderInvoice.setCpCSupplierEname(company.getEname());
        orderInvoice.setOrderStatus(orders.get(0).getOrderStatus());
        orderInvoice.setPayee(company.getPayee());
        orderInvoice.setDrawer(company.getDrawer());
        orderInvoice.setReviewer(company.getReviewer());
        orderInvoice.setSyncPlatformStatus(InvoiceConst.SyncPlatformStatus.NOT_SYNC);
        orderInvoice.setTicketType(InvoiceConst.TicketType.BLUE);
        orderInvoice.setAuditStatus(InvoiceConst.AuditStatus.NOT_AUDIT);
        orderInvoice.setHeaderType(apply.getHeaderType());
        orderInvoice.setTaxMachineNo(taxMachineNo);
        // 购方开票信息
        orderInvoice.setInvoiceHeader(apply.getInvoiceHeader());
        orderInvoice.setTaxpayerNo(StringUtils.isNotBlank(apply.getTaxpayerNo()) ? apply.getTaxpayerNo().toUpperCase() : apply.getTaxpayerNo());
        String openingBank = StringUtils.isNotBlank(apply.getOpeningBank()) ? apply.getOpeningBank() : "";
        String bankAccount = StringUtils.isNotBlank(apply.getBankAccount()) ? apply.getBankAccount() : "";
        if(openingBank.length() + bankAccount.length() > 110){
            openingBank = openingBank.substring(0,110 - bankAccount.length());
        }
        orderInvoice.setOpeningBank(openingBank);
        orderInvoice.setBankAccount(bankAccount);
        String invoiceAddress = StringUtils.isNotBlank(apply.getInvoiceAddress()) ? apply.getInvoiceAddress() : "";
        String mobile = StringUtils.isNotBlank(apply.getMobile()) ? apply.getMobile() : "";
        if(invoiceAddress.length() + mobile.length() > 110){
            invoiceAddress = invoiceAddress.substring(0,110 - mobile.length());
        }
        orderInvoice.setUnitAddress(invoiceAddress);
        orderInvoice.setUnitMobile(mobile);
        // 销方信息
        orderInvoice.setSupplierAddress(company.getAddress());
        orderInvoice.setSupplierTaxpayerNo(company.getSocialCreditCode());
        orderInvoice.setSupplierMobile(company.getPhone());
        orderInvoice.setSupplierOpeningBank(company.getBillbank());
        orderInvoice.setSupplierBankAccount(company.getBillaccount());
        orderInvoice.setCpCSupplierEname(company.getAllname());

        orderInvoice.setBillNo(getBillNo());
        orderInvoice.setInvoiceKind(StringUtils.isNotBlank(apply.getInvoiceKind()) ? apply.getInvoiceKind() : strategy.getInvoiceKind());
        orderInvoice.setInvoiceType(StringUtils.isNotBlank(apply.getInvoiceType()) ? apply.getInvoiceType() : strategy.getInvoiceType());
        // 订单备注不超过200个字符，到150个加 ...
        String invoiceRemark = "订单号：" + apply.getTid();
        if(invoiceRemark.length() > INVOICE_REMARK_CUT){
            invoiceRemark = invoiceRemark.substring(0,INVOICE_REMARK_CUT) + "...";
        }
        orderInvoice.setInvoiceRemark(invoiceRemark);
        if(InvoiceConst.InvoiceRemark.INVOICE_REMARK.equals(strategy.getInvoiceRemark())){
            orderInvoice.setInvoiceRemark(apply.getInvoiceRemark());
        }
        orderInvoice.setRemark(apply.getRemark());
        Optional<OcBOrder> o = orders.stream().filter(e -> StringUtils.isNotBlank(e.getInvoiceContent())).findFirst();
        if(o.isPresent()) {
            orderInvoice.setInvoiceContent(o.get().getInvoiceContent());// 发票内容
        }
        orderInvoice.setInvoiceStatus(InvoiceConst.InvoiceStatus.NOT_INVOICE);
        orderInvoice.setChangeInvoiceStatus(InvoiceConst.ChangeInvoiceStatus.NOT_CHANGE_INVOICE);
        orderInvoice.setFreezeStatus(freezeStatus);
        orderInvoice.setRedRushStatus(InvoiceConst.RedRushStatus.NOT_RED_RUSH);
        orderInvoice.setCancelStatus(InvoiceConst.CancelStatus.NOT_CANCEL);
        orderInvoice.setEmail(apply.getEmail());
        // 获取收票人手机号姓名地址
        if(StringUtils.isNotBlank(apply.getReceiverPhone()) && StringUtils.isNotBlank(apply.getReceiver()) && StringUtils.isNotBlank(apply.getReceiverAddress())){
            String receiverPhone = apply.getReceiverPhone().length() > 400 ? apply.getReceiverPhone().substring(0,390) : apply.getReceiverPhone();
            orderInvoice.setPhone(receiverPhone);// 收件人手机号
            String receiver = apply.getReceiver().length() > 700 ? apply.getReceiver().substring(0,690) : apply.getReceiver();
            orderInvoice.setReceiver(receiver);// 收票人姓名
            orderInvoice.setReceiverAddress(apply.getReceiverAddress());// 收件人地址
        }else{
            // 取零售发货单的收件人信息
            List<OcBOrder> orderList = orders.stream().filter(e -> StringUtils.isNotBlank(e.getReceiverMobile()) &&
                    StringUtils.isNotBlank(e.getReceiverName()) &&
                    StringUtils.isNotBlank(e.getCpCRegionProvinceEname()) &&
                    StringUtils.isNotBlank(e.getCpCRegionCityEname()) &&
                    StringUtils.isNotBlank(e.getCpCRegionAreaEname()) &&
                    StringUtils.isNotBlank(e.getReceiverAddress())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(orderList)) {
                OcBOrder order = orderList.get(0);
                String receiverMobil = order.getReceiverMobile().length() > 400 ? order.getReceiverMobile().substring(0,390) : order.getReceiverMobile();
                orderInvoice.setPhone(receiverMobil);
                String receiverName = order.getReceiverName().length() > 700 ? order.getReceiverName().substring(0,690) : order.getReceiverName();
                orderInvoice.setReceiver(receiverName);
                orderInvoice.setReceiverAddress(order.getCpCRegionProvinceEname() + order.getCpCRegionCityEname() + order.getCpCRegionAreaEname() + " " + order.getReceiverAddress());
            }
        }
        if(InvoiceConst.MergeInvoiceFlag.MERGE.equals(apply.getMergeInvoiceFlag())){
            orderInvoice.setMergeInvoiceFlag(InvoiceConst.MergeInvoiceFlag.MERGE);
        }
        // 判断是否是自动审核
        if(Objects.nonNull(strategy.getInvoiceControl()) && InvoiceConst.InvoiceControl.AUTO.equals(strategy.getInvoiceControl())){
            orderInvoice.setAuditStatus(OmsParamConstant.ONE);
        }
        orderInvoice.setInvoiceApplyId(apply.getId());
        orderInvoice.setCreationdate(new Date());
        orderInvoice.setOwnerid(Long.valueOf(user.getId()));
        orderInvoice.setOwnerename(user.getEname());
        orderInvoice.setOwnername(user.getName());

        return orderInvoice;
    }

    /**
     * 开票明细转换
     * @param orderItems 开票订单明细
     * @param orderInvoiceId 主表id
     * @param strategy 对应开票策略
     * @param proMap 商品信息,取税率和税收分类编码
     * @param subsidy 平台补贴
     * @return 开票订单明细
     */
    public List<AcFOrderInvoiceItem> applyItemChange(List<AcFOrderInvoiceSystemItem> orderItems, Long orderInvoiceId, StCInvoiceStrategy strategy, Map<String, List<ProExtResult>> proMap,BigDecimal subsidy) {
        List<AcFOrderInvoiceItem> list = new ArrayList<>();
        if(CollectionUtils.isEmpty(orderItems)){
            return list;
        }
        String isGiftInvoice = strategy.getIsGiftInvoice();
        // 过滤赠品以及全部退款的商品，此时的金额已经是扣减完之后的了
        List<AcFOrderInvoiceSystemItem> invoiceTempList = orderItems.stream().filter(e -> (InvoiceConst.InvoiceGIft.NOT_INVOICE.equals(isGiftInvoice) && InvoiceConst.GIFT.compareTo(e.getIsGift()) != 0) &&
                (Objects.nonNull(e.getPriceAmt()) &&
                        (e.getPriceAmt().compareTo(BigDecimal.ZERO) > 0 ||
                                e.getQty().compareTo(BigDecimal.ZERO) > 0))).collect(Collectors.toList());
        // 0元商品分摊，分摊逻辑：整单金额按照数量比例分摊
        List<AcFOrderInvoiceSystemItem> invoiceItemList = amtAllocationByQty(invoiceTempList);
        log.info("invoiceTempList: {}", JSON.toJSONString(invoiceTempList));
        // 平台补贴分摊
        invoiceItemList = platformSubsidyAllocate(invoiceItemList,subsidy);
        log.info("invoiceItemList2: {}", JSON.toJSONString(invoiceItemList));
        // 同商品合并
        List<AcFOrderInvoiceSystemItem> invoiceMergeList = invoiceSystemItemListMerge(invoiceItemList);
        log.info("invoiceMergeList: {}", JSON.toJSONString(invoiceMergeList));
        for(AcFOrderInvoiceSystemItem item : invoiceMergeList){
            // 合并后的实付金额，已扣出退款金额
            BigDecimal amt = item.getPriceAmt();
            // 合并后的数量
            BigDecimal qty = item.getQty();

            AcFOrderInvoiceItem invoiceItem = new AcFOrderInvoiceItem();
            Long id = Tools.getSequence(InvoiceConst.AC_F_ORDER_INVOICE_ITEM);
            invoiceItem.setAcFOrderInvoiceId(orderInvoiceId);
            invoiceItem.setPsCSkuId(item.getPsCSkuId());
            invoiceItem.setPsCSkuEcode(item.getPsCSkuEcode());
            invoiceItem.setPsCProId(item.getPsCProId());
            invoiceItem.setPsCProEcode(item.getPsCProEcode());
            invoiceItem.setPsCProEname(item.getPsCProEname());
            invoiceItem.setAdClientId(37L);
            invoiceItem.setAdOrgId(27L);
            // 商品名称
            if (InvoiceConst.InvoiceProNameType.ORDER_PRO_NAME.equals(strategy.getInvoiceProName())) {
                // 取订单商品名称
                invoiceItem.setInvoiceProName(item.getPsCProEname());
            } else {
                // 取商品资料的商品名称
                invoiceItem.setInvoiceProName(item.getPsCProEname());
            }

            // 规格名称
            if(InvoiceConst.InvoiceSkuNameType.ORDER_SKU_NAME.equals(strategy.getInvoiceSpecName())){
                // 取订单sku名称
                invoiceItem.setInvoiceSpecName(item.getPsCSkuEcode());
            } else {
                // 取商品资料sku
                invoiceItem.setInvoiceSpecName(item.getPsCSkuEcode());
            }

            ProExtResult proExt = proMap.get(item.getPsCProEcode()).get(0);
            // 税收分类编码
            String taxClassification = proExt.getTaxClassification();
            invoiceItem.setTaxClassification(taxClassification);
            // 税率
            BigDecimal taxRate = proExt.getTaxRate();
            invoiceItem.setTaxRate(taxRate.toString());
            if(StringUtils.isNotBlank(proExt.getUnit())){
                invoiceItem.setUnit(proExt.getUnit());// 单位
            } else {
                // 如果单位为空,则取策略中的单位
                invoiceItem.setUnit(strategy.getInvoiceUnit());
            }

            invoiceItem.setInclusiveTaxAmt(amt);
            log.info("601tax.amt: {}", amt);
            if(amt.multiply(new BigDecimal("100")).intValue() % qty.intValue() == 0){
                invoiceItem.setInclusiveTaxPrice(amt.divide(qty,6,BigDecimal.ROUND_HALF_UP));
                invoiceItem.setQty(qty);
                taxCal(amt,qty,invoiceItem,taxRate);
            }else{
                log.info("601taxNoDivideTotal");
                //不能整除 需要多加一条明细 金额等需要重新计算
                // 含税单价
                BigDecimal taxPrice = amt.divide(qty, 2, BigDecimal.ROUND_DOWN);
                invoiceItem.setInclusiveTaxPrice(taxPrice);
                invoiceItem.setQty(qty.subtract(BigDecimal.ONE));
                // 含税总价
                invoiceItem.setInclusiveTaxAmt(taxPrice.multiply(invoiceItem.getQty()));
                // 重新生成一条明细
                AcFOrderInvoiceItem invoiceItem1 = JSONObject.parseObject(JSON.toJSONString(invoiceItem), AcFOrderInvoiceItem.class);
                invoiceItem1.setQty(BigDecimal.ONE);// 1
                // 新生成的明细,含税总额
                BigDecimal taxPriceOne = amt.subtract(invoiceItem.getInclusiveTaxAmt());
                // 以为数量是1,所以扣减完的既是单价,也是总额
                invoiceItem1.setInclusiveTaxPrice(taxPriceOne);
                // 新生成明细的含税金额
                invoiceItem1.setInclusiveTaxAmt(taxPriceOne);
                // 重新计算主明细的未税金额,未税单价,税额
                taxCal(invoiceItem.getInclusiveTaxAmt(), invoiceItem.getQty(),invoiceItem,taxRate);
                // 计算新生成明细的的未税金额,未税单价,税额
                taxCal(invoiceItem1.getInclusiveTaxAmt(), invoiceItem1.getQty(), invoiceItem1, taxRate);

                Long idOne = Tools.getSequence(InvoiceConst.AC_F_ORDER_INVOICE_ITEM);
                invoiceItem1.setId(idOne);
                list.add(invoiceItem1);
            }
            invoiceItem.setId(id);
            list.add(invoiceItem);
        }
        return list;
    }

    private List<AcFOrderInvoiceSystemItem> invoiceSystemItemListMerge(List<AcFOrderInvoiceSystemItem> invoiceTempList) {
        Map<String, List<AcFOrderInvoiceSystemItem>> itemBySkuId = invoiceTempList.stream().collect(Collectors.groupingBy(AcFOrderInvoiceSystemItem::getPsCSkuEcode));
        List<AcFOrderInvoiceSystemItem> itemMergeList = new ArrayList<>();
        for(Map.Entry<String, List<AcFOrderInvoiceSystemItem>> entry : itemBySkuId.entrySet()){
            List<AcFOrderInvoiceSystemItem> value = entry.getValue();
            AcFOrderInvoiceSystemItem systemItem = value.get(0);
            // 合并后的实付金额
            BigDecimal allPriceAmt = sumOfBigdecimalAmount(value, null, AcFOrderInvoiceSystemItem::getPriceAmt);
            // 合并后的退款金额
            BigDecimal allReturnAmt = sumOfBigdecimalAmount(value, null, AcFOrderInvoiceSystemItem::getPriceReturnAmt);
            // 合并后的数量
            BigDecimal allQty = sumOfBigdecimalAmount(value, null, AcFOrderInvoiceSystemItem::getQty);
            // 合并后的退货数量
            BigDecimal allQtyReturn = sumOfBigdecimalAmount(value, null, AcFOrderInvoiceSystemItem::getQtyReturn);
            AcFOrderInvoiceSystemItem itemMerge = JSONObject.parseObject(JSON.toJSONString(systemItem), AcFOrderInvoiceSystemItem.class);
            itemMerge.setPriceAmt(allPriceAmt);
            itemMerge.setPriceReturnAmt(allReturnAmt);
            itemMerge.setQty(allQty);
            itemMerge.setQtyReturn(allQtyReturn);
            itemMergeList.add(itemMerge);
        }
        return itemMergeList;
    }

    /**
     * 计算未税总价, 未税单件,税额
     * @param realAmt 成交金额
     * @param qty 数量
     * @param invoiceItem 开票明细
     * @param taxRate 税率
     */
    public void taxCal(BigDecimal realAmt, BigDecimal qty, AcFOrderInvoiceItem invoiceItem, BigDecimal taxRate) {
        // 未税总价 含税金额 / （1+税率）
        BigDecimal noTaxAmt = realAmt.divide(taxRate.add(BigDecimal.ONE), 6, BigDecimal.ROUND_HALF_UP);
        if(Objects.isNull(taxRate) || taxRate.compareTo(BigDecimal.ZERO) == 0){
            noTaxAmt = realAmt;
        }

        // 未税单价 未税总价/数量
        invoiceItem.setNoTaxPrice(noTaxAmt.divide(qty, 8, BigDecimal.ROUND_HALF_UP));
        noTaxAmt = noTaxAmt.setScale(2,BigDecimal.ROUND_HALF_UP);
        invoiceItem.setNoTaxAmt(noTaxAmt);
        // 税额 不含税价 * 税率
//        BigDecimal tax = noTaxAmt.multiply(taxRate).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal tax = realAmt.subtract(noTaxAmt);
        invoiceItem.setInvoiceTaxAmt(tax);
        log.info("---| order invoice id:"+invoiceItem.getAcFOrderInvoiceId()+"未税总价"+invoiceItem.getNoTaxAmt()+",未税单价:"+invoiceItem.getNoTaxPrice()+",税额:"+invoiceItem.getInvoiceTaxAmt());
    }

    public String getBillNo(){
        return InvoiceConst.INVOICE_PREFIX + System.currentTimeMillis() + random.nextInt(99999);
    }

    /**
     * 指定过滤条件，指定字段求和
     * @param invoiceItemList
     * @param predicate
     * @param func
     * @return
     */
    public static BigDecimal sumOfBigdecimalAmount(List<AcFOrderInvoiceSystemItem> invoiceItemList, Predicate<AcFOrderInvoiceSystemItem> predicate, Function<AcFOrderInvoiceSystemItem, BigDecimal> func) {
        BigDecimal amount = new BigDecimal("0.0");
        if (null == predicate) {
            amount = invoiceItemList.stream().map(func).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
        } else {
            amount = invoiceItemList.stream().filter(predicate).map(func).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
        }
        return amount;
    }

    /**
     * 0金额按照数量比例分摊
     * @param invoiceItemList
     * @return
     */
    private List<AcFOrderInvoiceSystemItem> amtAllocationByQty(List<AcFOrderInvoiceSystemItem> invoiceItemList) {
        if(invoiceItemList.size() <= 1){
            return invoiceItemList;
        }
        List<String> tids = invoiceItemList.stream().filter(e -> Objects.nonNull(e.getPriceAmt()) && e.getPriceAmt().compareTo(BigDecimal.ZERO) == 0 &&
                e.getPriceReturnAmt().compareTo(BigDecimal.ZERO) == 0 && e.getQtyReturn().compareTo(BigDecimal.ZERO) == 0).map(AcFOrderInvoiceSystemItem::getTid).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(tids)){
            log.info("---| tid:" + tids + ",开始0元商品分摊");
            // 0元商品分摊
            // 获取所有金额
            BigDecimal allAmt = sumOfBigdecimalAmount(invoiceItemList, null, AcFOrderInvoiceSystemItem::getPriceAmt);
            // 获取所有数量
            BigDecimal allQty = sumOfBigdecimalAmount(invoiceItemList, null, AcFOrderInvoiceSystemItem::getQty);
            List<AcFOrderInvoiceSystemItem> list = new ArrayList<>();
            if(allAmt.multiply(new BigDecimal("100")).intValue() % allQty.intValue() == 0){
                // 可以整除
                BigDecimal partAmt = allAmt.divide(allQty, 2, BigDecimal.ROUND_HALF_UP);
                for(AcFOrderInvoiceSystemItem systemItem : invoiceItemList){
                    BigDecimal amt = systemItem.getQty().multiply(partAmt);
                    systemItem.setPriceAmt(amt);
                    systemItem.setPriceActual(amt.divide(systemItem.getQty(),6,BigDecimal.ROUND_HALF_UP));
                    list.add(systemItem);
                }
            }else{
                // 不可以整除
                BigDecimal partAmt = allAmt.divide(allQty, 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal addAmt = new BigDecimal("0.0");
                for(int i = 0; i < invoiceItemList.size(); i++){
                    AcFOrderInvoiceSystemItem systemItem = invoiceItemList.get(i);
                    BigDecimal amt = systemItem.getQty().multiply(partAmt);
                    if(i == invoiceItemList.size() - 1){
                        BigDecimal rest = allAmt.subtract(addAmt);
                        systemItem.setPriceAmt(rest);
                        systemItem.setPriceActual(rest.divide(systemItem.getQty(),6,BigDecimal.ROUND_HALF_UP));
                    } else {
                        systemItem.setPriceAmt(amt);
                        systemItem.setPriceActual(amt.divide(systemItem.getQty(),6,BigDecimal.ROUND_HALF_UP));
                        addAmt = addAmt.add(amt);
                    }
                    list.add(systemItem);
                }
            }
            log.info("---| tids:" + tids + ",分摊后的商品列表:" + JSON.toJSONString(list));
            return list;
        } else {
            return invoiceItemList;
        }
    }


    /**
     * 按照金额比例分摊平台补贴
     * @param invoiceItemList 开票明细
     * @param subsidy 补贴金额
     * @return
     */
    private List<AcFOrderInvoiceSystemItem> platformSubsidyAllocate(List<AcFOrderInvoiceSystemItem> invoiceItemList, BigDecimal subsidy) {
        if(Objects.isNull(subsidy) || subsidy.compareTo(BigDecimal.ZERO)==0){
            return invoiceItemList;
        }
        List<String> tids = invoiceItemList.stream().map(AcFOrderInvoiceSystemItem::getTid).collect(Collectors.toList());
        // 计算总金额
        BigDecimal allAmt = sumOfBigdecimalAmount(invoiceItemList, null, AcFOrderInvoiceSystemItem::getPriceAmt);
        if(allAmt.compareTo(subsidy) <= 0){
            log.info("---| tid" + tids + "平台退款金额大于付款金额,不进行分摊");
            return invoiceItemList;
        }
        BigDecimal addAmt = BigDecimal.ZERO;// 已经计算的金额
        for(int i = 0; i < invoiceItemList.size(); i++){
            AcFOrderInvoiceSystemItem item = invoiceItemList.get(i);
            if(i == (invoiceItemList.size() - 1)){
                item.setPriceAmt(item.getPriceAmt().subtract(subsidy.subtract(addAmt)));
            } else {
                // 计算比例
                BigDecimal point = item.getPriceAmt().divide(allAmt, 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal subAmt = subsidy.multiply(point);
                item.setPriceAmt(item.getPriceAmt().subtract(subAmt));
                addAmt = addAmt.add(subAmt);
            }
        }
        return invoiceItemList;
    }
}
