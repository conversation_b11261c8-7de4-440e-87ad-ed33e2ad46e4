package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AcBeanUtils;
import com.jackrain.nea.util.AcPayableAdjustmentPushESUtil;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class PayableAdjustmentBizAuditService extends CommandAdapter {
    @Resource
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Resource
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    @Resource
    private PayableAdjustmentSaveService payableAdjustmentSaveService;


    private void checkStatus(AcFPayableAdjustmentDO acFPayableAdjustmentDO, Long objId) {
        int iStatus = acFPayableAdjustmentDO.getBillStatus();
        AssertUtil.notNull(acFPayableAdjustmentDO.getResponsibleParty(), "当前单据责任方为空，不允许业审!");
        AssertUtil.assertException(iStatus == AcConstant.CON_BILL_STATUS_01, "当前记录未客审，不允许业审!");
        AssertUtil.assertException(iStatus == AcConstant.CON_BILL_STATUS_03, "当前记录已业务审核，不允许重复审核!");
        AssertUtil.assertException(iStatus == AcConstant.CON_BILL_STATUS_04, "当前记录已业审，不允许业审!");
        AssertUtil.assertException(iStatus == AcConstant.CON_BILL_STATUS_05, "当前记录已作废，不允许业审!");
        List<AcFPayableAdjustmentItemDO> acFPayableAdjustmentItemDOList =
                acFPayableAdjustmentItemMapper.listByItemId(objId);
        if (acFPayableAdjustmentItemDOList == null || acFPayableAdjustmentItemDOList.size() <= 0) {
            throw new NDSException("明细没有记录，不允许业审！");
        }
    }

    /**
     * 业审
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            JSONArray itemArray = AcBeanUtils.makeAuditJsonArray(param);
            JSONArray errorArray = new JSONArray();
            if (itemArray.size() > 0) {
                for (int i = 0; i < itemArray.size(); i++) {
                    Long id = itemArray.getLong(i);
                    try {
                        auditFunction(id, querySession);
                    } catch (Exception e) {
                        JSONObject errJo = new JSONObject();
                        errJo.put("objid", id);
                        errJo.put("message", e.getMessage());
                        errorArray.add(errJo);
                    }
                }
            } else {
                throw new NDSException("未找到需要业审的记录！");
            }
            valueHolder = AcBeanUtils.getProcessValueHolder(itemArray, errorArray, "业审");
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    @Transactional(rollbackFor = {Exception.class})
    public void auditFunction(Long objId, QuerySession querySession) {
        AcFPayableAdjustmentDO acFPayableAdjustmentDO = acFPayableAdjustmentMapper.selectById(objId);
        if (acFPayableAdjustmentDO != null) {
            checkStatus(acFPayableAdjustmentDO, objId);
            AcFPayableAdjustmentDO payableAdjustmentDO = new AcFPayableAdjustmentDO();
            payableAdjustmentDO.setId(objId);
            //BILL_STATUS    1:未审核 2:已客审 3:已业审 4:财审 5:已作废
            payableAdjustmentDO.setBillStatus(AcConstant.CON_BILL_STATUS_03);
            BaseModelUtil.makeBaseModifyField(payableAdjustmentDO, querySession.getUser());
            int iResult = acFPayableAdjustmentMapper.updateById(payableAdjustmentDO);
            if (iResult < 0) {
                throw new NDSException("单据编码:" + acFPayableAdjustmentDO.getBillNo() + ",业审失败！");
            }

            //应付款调整单-操作类型=业审
            ValueHolderV14 v14 = payableAdjustmentSaveService.insertLogFun(querySession.getUser(), objId,
                    OperatorLogTypeEnum.BUSINESS_AUDIT.getVal(),
                    OperatorLogTypeEnum.BUSINESS_AUDIT.getText(), new Date());
            //推送ES
            AcPayableAdjustmentPushESUtil.pushOrder(objId);
            if (v14.getCode() == ResultCode.FAIL) {
                throw new NDSException("应付款调整单=>新增操作日志-业审失败!");
            }
        } else {
            throw new NDSException("当前记录不存在！");
        }
    }

}
