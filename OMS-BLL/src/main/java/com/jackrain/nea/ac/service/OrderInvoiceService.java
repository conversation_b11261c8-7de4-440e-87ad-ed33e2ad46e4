package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.burgeon.r3.sg.core.model.request.SgR3BaseRequest;
import com.jackrain.nea.ac.service.converter.ApplyInvoiceConvert;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceSystemItemMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceItem;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceSystemItem;
import com.jackrain.nea.oc.oms.model.table.StCInvoiceStrategy;
import com.jackrain.nea.oc.oms.services.invoice.InvoiceLogService;
import com.jackrain.nea.oc.request.InvoiceInfoModifyRequest;
import com.jackrain.nea.oc.request.OrderInvoiceChangeRequest;
import com.jackrain.nea.oc.request.OrderInvoiceModifyAmountRequest;
import com.jackrain.nea.redis.util.RedisOpsUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.R3ParamUtils;
import com.jackrain.nea.util.TransactionUtils;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.web.face.User;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName OrderInvoiceService
 * @Description 订单开票页面相关接口
 * @Date 2022/9/8 上午11:20
 * @Created by wuhang
 */
@Service
@Slf4j
public class OrderInvoiceService {

    @Autowired
    private AcFOrderInvoiceMapper orderInvoiceMapper;

    @Autowired
    private AcFInvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    private AcFOrderInvoiceItemMapper invoiceItemMapper;

    @Autowired
    private AcFOrderInvoiceSystemItemMapper invoiceSystemItemMapper;

    @Autowired
    private AcFInvoiceApplyItemMapper invoiceApplyItemMapper;

    @Autowired
    private OrderInvoiceChangeTaskService changeTaskService;

    @Autowired
    private OrderInvoiceTaskService invoiceTaskService;

    @Autowired
    private AcFInvoiceApplyItemService applyItemService;

    @Autowired
    private InvoiceStrategyService invoiceStrategyService;

    @Autowired
    private AcFInvoiceReturnRedOffsetService redOffsetService;

    @Autowired
    private OrderInvoiceGetResultTaskService getResultTaskService;

    @Autowired
    private AcFInvoiceReturnRedOffsetService returnRedOffsetService;

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Autowired
    private InvoiceLogService logService;

    @Autowired
    private ApplyInvoiceConvert applyInvoiceConvert;

    @Autowired
    private RedisOpsUtil redisOpsUtil;

    @Autowired
    private ThreadPoolTaskExecutor acFOrderInvoiceGetThreadPoolExecutor;

    @Autowired
    private AcFInvoiceReturnRedOffsetService acFInvoiceReturnRedOffsetService;


    /**
     * 红冲
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder redRush(JSONObject param, User user) {
        log.info("---| invoice | red rush param : " + param + ", user : " + JSON.toJSONString(user));
        ValueHolder vh = new ValueHolder();
        JSONArray ids = param.getJSONArray("ids");
        JSONArray errorMessage = new JSONArray(); // 错误信息
        int fail = 0;
        for(int i = 0; i < ids.size(); i++){
            Long id = ids.getLong(i);
            JSONObject jsonObject = new JSONObject();
            AcFOrderInvoice orderInvoice = orderInvoiceMapper.selectById(id);
            String msg = checkRedRush(orderInvoice);
            if (StringUtils.isNotBlank(msg)) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", msg);
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", msg);
                jsonObject.put("objid", id);
                errorMessage.add(jsonObject);
                fail++;
                continue;
            }
            // 创建红冲发票
            ValueHolder result = changeTaskService.createRedByBlue(orderInvoice, user,InvoiceConst.AuditStatus.NOT_AUDIT);
            if(!result.isOK()){
                return result;
            }
            Long originId = (Long) result.get("originId");
            String isDelete = result.get("isDelete").toString();
            // 将原蓝票红冲状态修改为红冲中
            AcFOrderInvoice update = new AcFOrderInvoice();
            update.setId(originId);
            if(StringUtils.isNotBlank(isDelete) && "1".equals(isDelete)){
                update.setIsactive("N");// 作废
            } else {
                update.setRedRushStatus(InvoiceConst.RedRushStatus.IN_RED_RUSH);// 红冲中
            }
            update.setModifierename(user.getEname());
            update.setModifiername(user.getEname());
            update.setModifierid(Long.valueOf(user.getId().toString()));
            update.setModifieddate(new Date());
            orderInvoiceMapper.updateById(update);
            Long applyId = orderInvoice.getInvoiceApplyId();
            if (StringUtils.isNotBlank(isDelete) && "1".equals(isDelete) && Objects.nonNull(applyId) && applyId.compareTo(0L) > 0) {
                invoiceApplyService.updateInvoiceApplyItemTidSuffix(applyId);
            }
            logService.addUserOrderLog(originId,"红冲","发票红冲中",user);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "红冲申请成功");
        }
        if (ids.size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "红冲申请成功");
        } else {
            vh.put("data", errorMessage);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "红冲申请成功" + (ids.size() - fail) + "条，失败" + fail + "条数据");
        }
        return vh;
    }

    /**
     * 撤销开票
     * @param param
     * @return
     */
    public ValueHolderV14 revoke(JSONObject param) {
        ValueHolderV14 resp = new ValueHolderV14();
        if(Objects.isNull(param) || param.isEmpty()){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("参数不能为空");
            return resp;
        }
        Long id = param.getLong("id");
        if(Objects.isNull(id) || id < 0){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("参数错误");
            return resp;
        }
        AcFOrderInvoice invoice = orderInvoiceMapper.selectById(id);
        if(Objects.isNull(invoice)){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("记录不存在");
            return resp;
        }
        if(InvoiceConst.CancelStatus.CANCELED.equals(invoice.getCancelStatus()) || !"Y".equals(invoice.getIsactive()) || InvoiceConst.FreezeStatus.FREEZED.equals(invoice.getFreezeStatus())){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("状态异常,不可撤销");
            return resp;
        }
        if(!InvoiceConst.InvoiceStatus.IN_INVOICE.equals(invoice.getInvoiceStatus())){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("当前发票开票状态不为开票中,不可撤销");
            return resp;
        }
        User user = SystemUserResource.getRootUser();
        return returnRedOffsetService.invoiceRevoke(invoice,user,"手动撤销");
    }


    /**
     * 重新申请开票
     * @param param
     * @return
     */
    public ValueHolder reInvoice(JSONObject param, User user) {
        log.info("---| invoice | reinvoice param : " + param + ", user : " + JSON.toJSONString(user));
        ValueHolder vh = new ValueHolder();
        JSONArray ids = param.getJSONArray("ids");
        JSONArray errorMessage = new JSONArray(); // 错误信息
        int fail = 0;
        for(int i = 0; i < ids.size(); i++) {
            Long id = ids.getLong(i);
            JSONObject jsonObject = new JSONObject();
            AcFOrderInvoice orderInvoice = orderInvoiceMapper.selectById(id);
            String msg = reInvoiceCheck(orderInvoice);
            if (StringUtils.isNotBlank(msg)) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", msg);
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", msg);
                jsonObject.put("objid", id);
                errorMessage.add(jsonObject);
                fail++;
                continue;
            }
            // 重新申请开票,将开票状态修改为未开票,等待定时任务重新拉取开票
            AcFOrderInvoice update = new AcFOrderInvoice();
            update.setId(id);
            update.setInvoiceStatus(InvoiceConst.InvoiceStatus.NOT_INVOICE);
            update.setFailReason("");// 重新申请开票后将开票失败信息置空
            update.setModifierename(user.getEname());
            update.setModifiername(user.getEname());
            update.setModifierid(Long.valueOf(user.getId().toString()));
            update.setModifieddate(new Date());
            orderInvoiceMapper.updateById(update);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "重新开票申请成功");
        }
        if (ids.size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "重新开票申请成功");
        } else {
            vh.put("data", errorMessage);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "成功" + (ids.size() - fail) + "条，失败" + fail + "条数据");
        }
        return vh;
    }


    /**
     * 合并开票
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 mergeInvoice(JSONObject param, User user) {
        log.info("---| invoice | merge invoice param : " + param + ", user : " + JSON.toJSONString(user));
        ValueHolderV14 resp = new ValueHolderV14();
        JSONArray ids = param.getJSONArray("ids");
        LambdaQueryWrapper<AcFOrderInvoice> query = new LambdaQueryWrapper<>();
        query.in(AcFOrderInvoice::getId,ids);
        List<AcFOrderInvoice> orderInvoiceList = orderInvoiceMapper.selectList(query);
        String msg = mergeInvoiceCheck(orderInvoiceList);
        if(StringUtils.isNotEmpty(msg)){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage(msg);
            return resp;
        }
        createApplyByOrderInvoice(orderInvoiceList,user,null,true,null);
        resp.setMessage("合并开票申请成功！");
        resp.setCode(ResultCode.SUCCESS);
        return resp;
    }

    /**
     * 合并开票
     * @param orderInvoiceList 需要合并的发票
     */
    public void createApplyByOrderInvoice(List<AcFOrderInvoice> orderInvoiceList,User user,Long redTicketId, boolean isMerge,BigDecimal otherAmt) {
        AcFInvoiceApply apply = new AcFInvoiceApply();
        Long applyId = Tools.getSequence(InvoiceConst.AC_F_INVOICE_APPLY);
        apply.setId(applyId);
        AcFOrderInvoice orderInvoice = orderInvoiceList.get(0);
        String tids = orderInvoiceList.stream().map(AcFOrderInvoice::getTid).collect(Collectors.joining(","));
        apply.setTid(tids);
        apply.setRedTicketId(redTicketId);
        if(Objects.nonNull(redTicketId)){

        }
        apply.setCpCShopId(orderInvoice.getCpCShopId());
        apply.setCpCShopEcode(orderInvoice.getCpCShopEcode());
        apply.setCpCShopTitle(orderInvoice.getCpCShopTitle());
        apply.setCpCPlatformId(orderInvoice.getCpCPlatformId());
        apply.setCpCPlatformEcode(orderInvoice.getCpCPlatformEcode());
        apply.setCpCPlatformEname(orderInvoice.getCpCPlatformEname());
        apply.setInvoiceKind(orderInvoice.getInvoiceKind());
        BigDecimal invoiceAmt = orderInvoiceList.stream().map(AcFOrderInvoice::getInvoiceInclusiveTaxAmt).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
        apply.setInvoiceAmt(invoiceAmt);
        // 如果是退货待红冲来的重新申请会有剩余金额,赋值到开票金额上
        if(Objects.nonNull(otherAmt)){
            apply.setInvoiceAmt(otherAmt);
        }
        apply.setInvoiceRemark(orderInvoice.getInvoiceRemark());
        apply.setInvoiceType(orderInvoice.getInvoiceType());
        apply.setHeaderType(orderInvoice.getHeaderType());
        apply.setInvoiceHeader(orderInvoice.getInvoiceHeader());
        apply.setTaxpayerNo(orderInvoice.getTaxpayerNo()==null?"":orderInvoice.getTaxpayerNo());
        apply.setInvoiceAddress(orderInvoice.getUnitAddress());
        apply.setMobile(orderInvoice.getUnitMobile());
        apply.setOpeningBank(orderInvoice.getOpeningBank());
        apply.setBankAccount(orderInvoice.getBankAccount());
        apply.setReceiver(orderInvoice.getReceiver());
        apply.setReceiverAddress(orderInvoice.getReceiverAddress());
        apply.setReceiverPhone(orderInvoice.getPhone());
        apply.setEmail(orderInvoice.getEmail());
        apply.setTransStatus(InvoiceConst.TransStatus.NOT_TRANS);
        apply.setFailCount(0);
        apply.setFailReason("");
        apply.setApplyBillNo(applyInvoiceConvert.getBillNo());
        apply.setApplyDate(new Date());
        apply.setOwnerid(Long.valueOf(user.getId()));
        apply.setOwnername(user.getName());
        apply.setOwnerename(user.getEname());
        apply.setCreationdate(new Date());
        List<Long> ids = orderInvoiceList.stream().map(AcFOrderInvoice::getId).collect(Collectors.toList());
        if(isMerge) {
            apply.setMergeOrderInvoiceId(StringUtils.join(ids, ","));
            apply.setMergeInvoiceFlag(InvoiceConst.MergeInvoiceFlag.MERGE);
        }
        // 将原开票表状态修改为已取消
        if(isMerge){
            UpdateWrapper<AcFOrderInvoice> update = new UpdateWrapper<>();
            update.lambda().set(AcFOrderInvoice::getCancelStatus, InvoiceConst.CancelStatus.CANCELED)
                    .in(AcFOrderInvoice::getId, ids);
            orderInvoiceMapper.update(null, update);
        }
        for(Long updateId : ids){
            if(isMerge) {
                logService.addUserOrderLog(updateId, "取消", "合并开票", user);
            } else {
                logService.addUserOrderLog(updateId, "取消", "红冲退单", user);
            }
        }
        // 插入申请数据
        // 获取所有开票订单明细
        List<AcFOrderInvoiceSystemItem> systemItemList = invoiceSystemItemMapper.selectbyOrderInvoiceIds(ids);
        applyItemService.insertBySystemItemList(systemItemList,applyId,user,apply.getCpCShopId());
        invoiceApplyMapper.insert(apply);

    }


    /**
     * 换票
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 changeInvoice(OrderInvoiceChangeRequest param) {
        User user = SystemUserResource.getRootUser();
        log.info("---| invoice | change invoice param : " + param + ", user : " + JSON.toJSONString(user));
        ValueHolderV14 resp = new ValueHolderV14();
        Long id = param.getId();
        AcFOrderInvoice orderInvoice = orderInvoiceMapper.selectById(id);
        String msg = changeInvoiceCheck(orderInvoice,param);
        if(StringUtils.isNotBlank(msg)){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage(msg);
            return resp;
        }
        ValueHolder result = changeInvoiceByChangeType(param, user, orderInvoice);
        if(result.isOK()) {
            resp.setMessage("换票申请成功！");
            resp.setCode(ResultCode.SUCCESS);
        } else {
            resp.setCode(ResultCode.FAIL);
            resp.setMessage(result.get("message").toString());
        }
        return resp;
    }



    /**
     * 修改金额
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14 modAmount(OrderInvoiceModifyAmountRequest req) {
        User user = SystemUserResource.getRootUser();
        log.info("---| invoice | modify amount param : " + req + ", user : " + JSON.toJSONString(user));
        ValueHolderV14 resp = new ValueHolderV14();
        // 修改金额校验校验
        AcFOrderInvoice orderInvoice = orderInvoiceMapper.selectById(req.getId());
        List<AcFOrderInvoiceItem> orderInvoiceItems = invoiceItemMapper.queryByOrderInvoiceId(req.getId());
        ValueHolderV14<Map<Long, BigDecimal>> checkResult = modAmountCheck(orderInvoice,req,orderInvoiceItems);
        if(!checkResult.isOK()){
            return checkResult;
        }
        // TODO 修改金额
        Map<Long, BigDecimal> amtMap = checkResult.getData();
        for(AcFOrderInvoiceItem item : orderInvoiceItems){
            if(amtMap.containsKey(item.getId())){
                BigDecimal newAmount = amtMap.get(item.getId());
                item.setInclusiveTaxAmt(newAmount);
                item.setInclusiveTaxPrice(newAmount.divide(item.getQty(),6,BigDecimal.ROUND_HALF_UP));
                applyInvoiceConvert.taxCal(newAmount,item.getQty(),item,new BigDecimal(item.getTaxRate()));
                invoiceItemMapper.updateById(item);
            }
        }
        UpdateWrapper<AcFOrderInvoice> update = new UpdateWrapper<>();
        // 开票未税金额
        BigDecimal noTaxAmount = OrderInvoiceChangeTaskService.sumOfBigdecimalAmount(orderInvoiceItems, null, e -> e.getNoTaxAmt());
        // 开票含税金额
        BigDecimal inclusiveTaxAmount = OrderInvoiceChangeTaskService.sumOfBigdecimalAmount(orderInvoiceItems, null, e -> e.getInclusiveTaxAmt());
        // 税额
        BigDecimal invoiceTaxAmount = OrderInvoiceChangeTaskService.sumOfBigdecimalAmount(orderInvoiceItems, null, e -> e.getInvoiceTaxAmt());
        update.lambda().eq(AcFOrderInvoice::getId,req.getId()).set(AcFOrderInvoice::getInvoiceNoTaxAmt,noTaxAmount)
                .set(AcFOrderInvoice::getInvoiceInclusiveTaxAmt,inclusiveTaxAmount)
                .set(AcFOrderInvoice::getInvoiceTaxAmt,invoiceTaxAmount)
                .set(AcFOrderInvoice::getAmountMoreThanBeforeFlag,req.getAmountMoreThanBeforeFlag());
        orderInvoiceMapper.update(null,update);
        resp.setMessage("成功");
        resp.setCode(ResultCode.SUCCESS);
        return resp;
    }

    /**
     * 红冲校验
     * @param orderInvoice
     * @return
     */
    private String checkRedRush(AcFOrderInvoice orderInvoice) {
        if(Objects.isNull(orderInvoice)){
            return "发票不存在";
        }
        if(InvoiceConst.CancelStatus.CANCELED.equals(orderInvoice.getCancelStatus()) ||
                !InvoiceConst.RedRushStatus.NOT_RED_RUSH.equals(orderInvoice.getRedRushStatus()) ||
                InvoiceConst.TicketType.RED.equals(orderInvoice.getTicketType()) ||
                !InvoiceConst.InvoiceStatus.INVOICE_SUCCESS.equals(orderInvoice.getInvoiceStatus())){
            return "选择的订单发票状态不能红冲！";
        }
        // 判断当月不能红冲
        if(InvoiceConst.InvoiceKind.PAPER.equals(orderInvoice.getInvoiceKind()) && changeTaskService.checkTwoDateInOneMonth(orderInvoice.getInvoiceDate(),new Date())){
            return "当月的纸质发票不能红冲, 只能作废! ";
        }

        return "";
    }

    /**
     * 重新申请开票校验
     * @param orderInvoice
     * @return
     */
    private String reInvoiceCheck(AcFOrderInvoice orderInvoice) {
        if(Objects.isNull(orderInvoice)){
            return "发票不存在";
        }
        // 如果【审核状态】=非已审核或【开票状态】=非开票失败或未开票状态，则提示：“选择的订单发票状态不能重新申请开票！”
        if(InvoiceConst.AuditStatus.NOT_AUDIT.equals(orderInvoice.getAuditStatus()) ||
                InvoiceConst.InvoiceStatus.IN_INVOICE.equals(orderInvoice.getInvoiceStatus()) ||
                InvoiceConst.InvoiceStatus.INVOICE_SUCCESS.equals(orderInvoice.getInvoiceStatus())  ||
                InvoiceConst.InvoiceStatus.NOT_INVOICE.equals(orderInvoice.getInvoiceStatus()) ||
                InvoiceConst.CancelStatus.CANCELED.equals(orderInvoice.getCancelStatus()) ||
                InvoiceConst.FreezeStatus.FREEZED.equals(orderInvoice.getFreezeStatus())){
            return "选择的订单发票状态不能重新申请开票！";
        }

        return "";
    }

    /**
     * 合并开票校验
     * @param orderInvoiceList
     * @return
     */
    private String mergeInvoiceCheck(List<AcFOrderInvoice> orderInvoiceList) {
        /*
        若选择单条数据点击【合并开票】按钮，则提示：“合并开票至少需要选择两单！”
        3）、若批量选择多条，则所有单据，，
        如果有【取消状态】=已取消或【审核状态】=已审核，则提示：“选择的订单发票部分状态不一致不满足合并开票，不能合并！”；
        如果选择的单据开票类型或发票类型不一致，则提示“选择的订单发票发票类型不一致不满足合并开票，不能合并！”；
        如果选择的单据抬头、社会信用代码、开户行、地址、电话任何一个不一致，则提示“选择的订单发票开票信息不一致不满足合并开票，不能合并！”
        如果选择的开票人信息不一致，则提示“选择的订单发票开票人不一致不满足合并开票，不能合并！”
        4）、选择单据，单击【合并开票】按钮时，满足条件的订单发票，先取消原发票订单，将按照随机取一张订单发票开票信息和所有需要合并订单的平台单号，
        产生一张申请开票插入到发票申请表，主表字段是随机取一张订单发票开票信息，明细平台单号是所有需要合并的平台单号，成功后返回信息提示：“合并开票申请成功！”；
         */
        if(CollectionUtils.isEmpty(orderInvoiceList) || orderInvoiceList.size() < 2){
            return "合并开票至少需要选择两单！";
        }
        AcFOrderInvoice orderInvoice = orderInvoiceList.get(0);
        String invoiceKind = orderInvoice.getInvoiceKind();// 发票种类,纸质,电子
        String invoiceType = orderInvoice.getInvoiceType();// 发票类型,专票,普票
        String headerType = orderInvoice.getHeaderType();// 抬头类型
        String invoiceInfo = getInvoiceInfoString(orderInvoice);// 开票信息,发票抬头+社会信用编码+开户行+地址+电话
        Long cpCShopId = orderInvoice.getCpCShopId();
        for(AcFOrderInvoice item : orderInvoiceList){
            if(InvoiceConst.CancelStatus.CANCELED.equals(item.getCancelStatus()) ||
                    InvoiceConst.AuditStatus.AUDITED.equals(item.getAuditStatus()) ||
                    InvoiceConst.FreezeStatus.FREEZED.equals(orderInvoice.getFreezeStatus()) ||
                    InvoiceConst.TicketType.RED.equals(orderInvoice.getTicketType()) ){
                return "选择的订单发票部分状态不一致不满足合并开票，不能合并！";
            }
            if(!invoiceKind.equals(item.getInvoiceKind()) || !invoiceType.equals(item.getInvoiceType())){
                return "选择的订单发票发票类型不一致不满足合并开票，不能合并！";
            }
            String itemInvoiceInfo = getInvoiceInfoString(item);
            if(!invoiceInfo.equals(itemInvoiceInfo)){
                return "选择的订单发票开票信息不一致不满足合并开票，不能合并！";
            }
            if(!headerType.equals(item.getHeaderType())){
                return "选择的订单发票抬头类型不一致不满足合并开票，不能合并！";
            }
            if(cpCShopId.compareTo(item.getCpCShopId()) != 0){
                return "选择的订单店铺不一致, 不能合并! ";
            }
        }
        return "";
    }

    /**
     * 换票校验
     * @param orderInvoice 原发票
     * @param param 修改内容
     * @return
     */
    private String changeInvoiceCheck(AcFOrderInvoice orderInvoice, OrderInvoiceChangeRequest param) {
        if(Objects.isNull(orderInvoice) || Objects.isNull(param)){
            return "发票不存在或参数错误";
        }
        if(InvoiceConst.CancelStatus.CANCELED.equals(orderInvoice.getCancelStatus())){
            return "选择的订单发票已经取消，不能申请换票！";
        }
        if(InvoiceConst.FreezeStatus.FREEZED.equals(orderInvoice.getFreezeStatus())){
            return "选择的订单发票已经冻结，不能申请换票！";
        }
        if(InvoiceConst.InvoiceStatus.IN_INVOICE.equals(orderInvoice.getInvoiceStatus())){
            return "当前发票开票中,不能申请换票! ";
        }
        if(InvoiceConst.ChangeInvoiceStatus.IN_CHANGE_INVOICE.equals(orderInvoice.getChangeInvoiceStatus())){
            return "当前发票换票中,不能申请换票! ";
        }
        if(InvoiceConst.TicketType.RED.equals(orderInvoice.getTicketType())){
            return "红字发票无法申请换票! ";
        }
        if(StringUtils.isBlank(param.getInvoiceHeader())){
            return "发票抬头不能为空";
        }
        // 校验字段
        String changeType = param.getChangeType();
        switch (changeType) {
            case InvoiceConst.InvoiceChangeType.CHANGE_INVOICE_KIND:
                // 换纸质,只能修改发票种类
                break;
            case InvoiceConst.InvoiceChangeType.CHANGE_INVOICE_TYPE:
                // 换专票
                if(StringUtils.isBlank(param.getTaxpayerNo())){
                    return "纳税人识别号不能为空";
                }
                if(StringUtils.isBlank(param.getOpeningBank())){
                    return "开户行不能为空";
                }
                if(StringUtils.isBlank(param.getBankAccount())){
                    return "银行账户不能为空";
                }
                if(StringUtils.isBlank(param.getUnitAddress())){
                    return "地址不能为空";
                }
                if(StringUtils.isBlank(param.getUnitMobile())){
                    return "电话不能为空";
                }
                break;
            case InvoiceConst.InvoiceChangeType.CHANGE_INVOICE_HEADER:
                // 换抬头
                if(InvoiceConst.HeaderType.ENTERPRISE.equals(orderInvoice.getHeaderType())) {
                    if (StringUtils.isBlank(param.getTaxpayerNo())) {
                        return "纳税人识别号不能为空";
                    }
                    if (InvoiceConst.InvoiceType.SPECIAL.equals(orderInvoice.getInvoiceType())) {
                        if (StringUtils.isBlank(param.getOpeningBank())) {
                            return "开户行不能为空";
                        }
                        if (StringUtils.isBlank(param.getBankAccount())) {
                            return "银行账户不能为空";
                        }
                        if (StringUtils.isBlank(param.getUnitAddress())) {
                            return "地址不能为空";
                        }
                        if (StringUtils.isBlank(param.getUnitMobile())) {
                            return "电话不能为空";
                        }
                    }
                }
        }
        return "";
    }

    /**
     * 修改换票信息
     * @param param
     */
    private ValueHolder changeInvoiceByChangeType(OrderInvoiceChangeRequest param,User user,AcFOrderInvoice orderInvoice) {
        ValueHolder vh = new ValueHolder();
        Long redTicketId = null;
        Long originId = orderInvoice.getId();
        if(InvoiceConst.InvoiceStatus.NOT_INVOICE.equals(orderInvoice.getInvoiceStatus()) || InvoiceConst.InvoiceStatus.INVOICE_FAIL.equals(orderInvoice.getInvoiceStatus())){
            // 未开票,将原发票取消
            cancelOrderInvoice(originId,user,InvoiceConst.ChangeInvoiceStatus.IN_CHANGE_INVOICE);
            logService.addUserOrderLog(originId,"换票","换票-取消发票",user);
        } else if (InvoiceConst.InvoiceStatus.IN_INVOICE.equals(orderInvoice.getInvoiceStatus())) {
            ValueHolderV14 result = redOffsetService.invoiceRevoke(orderInvoice, user,"换票");
            if(!result.isOK()){
                vh.put("code",ResultCode.FAIL);
                vh.put("message",result.getMessage());
                return vh;
            }
        } else if (InvoiceConst.InvoiceStatus.INVOICE_SUCCESS.equals(orderInvoice.getInvoiceStatus())){
            // 开票成功,生成一张红票
            ValueHolder result = changeTaskService.createRedByBlue(orderInvoice, user,InvoiceConst.AuditStatus.NOT_AUDIT);
            if(!result.isOK()){
                return result;
            }
            originId = (Long) result.get("originId");
            redTicketId = (Long) result.get("redTicketId");// 红票id
            String isDelete = result.get("isDelete").toString();
            // 将原蓝票红冲状态修改为红冲中
            AcFOrderInvoice update = new AcFOrderInvoice();
            update.setId(originId);
            if(StringUtils.isNotBlank(isDelete) && "1".equals(isDelete)){
                update.setIsactive("N");// 作废
            } else {
                update.setRedRushStatus(InvoiceConst.RedRushStatus.IN_RED_RUSH);// 红冲中
            }
            update.setChangeInvoiceStatus(InvoiceConst.ChangeInvoiceStatus.IN_CHANGE_INVOICE);// 换票中
            update.setModifierename(user.getEname());
            update.setModifiername(user.getEname());
            update.setModifierid(Long.valueOf(user.getId().toString()));
            update.setModifieddate(new Date());
            orderInvoiceMapper.updateById(update);
//            Long applyId = orderInvoice.getInvoiceApplyId();
//            if(StringUtils.isNotBlank(isDelete) && "1".equals(isDelete) && Objects.nonNull(applyId) && applyId.compareTo(0l) > 0){
//                invoiceApplyService.updateInvoiceApplyItemTidSuffix(applyId);
//            }
            logService.addUserOrderLog(originId,"换票","换票-发票红冲中",user);
        }
        // 重新生成新的开票申请
        AcFInvoiceApply apply = new AcFInvoiceApply();
        String changeType = param.getChangeType();
        apply.setInvoiceKind(orderInvoice.getInvoiceKind());
        apply.setRedTicketId(redTicketId);// 原蓝票id
        apply.setChangeInvoiceType(changeType);
        Date now = new Date();
        switch (changeType) {
            case InvoiceConst.InvoiceChangeType.CHANGE_INVOICE_KIND:
                // 换纸质,只能修改发票种类
                apply.setInvoiceKind(StringUtils.isBlank(param.getInvoiceKind()) ? InvoiceConst.InvoiceKind.PAPER : param.getInvoiceKind());//发票种类
                apply.setInvoiceType(orderInvoice.getInvoiceType());
                apply.setHeaderType(orderInvoice.getHeaderType());
                apply.setInvoiceHeader(orderInvoice.getInvoiceHeader());
                apply.setOpeningBank(orderInvoice.getOpeningBank());
                apply.setBankAccount(orderInvoice.getBankAccount());
                apply.setInvoiceAddress(orderInvoice.getUnitAddress());
                apply.setMobile(orderInvoice.getUnitMobile());
                break;
            case InvoiceConst.InvoiceChangeType.CHANGE_INVOICE_TYPE:
                // 换专票
                apply.setInvoiceType(InvoiceConst.InvoiceType.SPECIAL);
                apply.setHeaderType(param.getHeaderType());
                apply.setInvoiceHeader(param.getInvoiceHeader());
                apply.setOpeningBank(param.getOpeningBank());// 开户行
                apply.setBankAccount(param.getBankAccount());// 银行账号
                apply.setInvoiceAddress(param.getUnitAddress());// 地址
                apply.setMobile(param.getUnitMobile());// 电话
                break;
            case InvoiceConst.InvoiceChangeType.CHANGE_INVOICE_HEADER:
                // 换抬头
                apply.setHeaderType(param.getHeaderType());
                apply.setInvoiceHeader(param.getInvoiceHeader());// 抬头
                apply.setTaxpayerNo(param.getTaxpayerNo()==null?"":param.getTaxpayerNo());// 纳税人识别号
                apply.setOpeningBank(param.getOpeningBank());// 开户行
                apply.setBankAccount(param.getBankAccount());// 银行账号
                apply.setInvoiceAddress(param.getUnitAddress());// 地址
                apply.setMobile(param.getUnitMobile());// 电话
        }
        if(StringUtils.isNotBlank(param.getInvoiceRemark())){
            apply.setInvoiceRemark(param.getInvoiceRemark());
        }
        apply.setAdClientId(orderInvoice.getAdClientId());
        apply.setTid(orderInvoice.getTid());
        apply.setInvoiceAmt(orderInvoice.getInvoiceInclusiveTaxAmt());
        apply.setTaxpayerNo(orderInvoice.getTaxpayerNo()==null?"":orderInvoice.getTaxpayerNo());
        apply.setRemark(orderInvoice.getRemark());
        apply.setApplyDate(now);
        apply.setInvoiceRemark(orderInvoice.getInvoiceRemark());
        apply.setAdOrgId(orderInvoice.getAdOrgId());
        apply.setCpCShopId(orderInvoice.getCpCShopId());
        apply.setCpCShopEcode(orderInvoice.getCpCShopEcode());
        apply.setCpCShopTitle(orderInvoice.getCpCShopTitle());
        apply.setCpCPlatformId(orderInvoice.getCpCPlatformId());
        apply.setCpCPlatformEname(orderInvoice.getCpCPlatformEname());
        apply.setCpCPlatformEcode(orderInvoice.getCpCPlatformEcode());
        apply.setReceiver(param.getReceiver());
        apply.setReceiverAddress(param.getReceiverAddress());
        apply.setReceiverPhone(param.getPhone());
        apply.setEmail(param.getEmail());
        apply.setCreationdate(now);
        apply.setOwnerename(user.getEname());
        apply.setOwnerid(Long.valueOf(user.getId()));
        apply.setOwnername(user.getEname());
        apply.setFailCount(0);
        apply.setFailReason("");
        apply.setTransStatus(InvoiceConst.TransStatus.NOT_TRANS);
        List<AcFOrderInvoiceSystemItem> systemItemList = invoiceSystemItemMapper.selectByOrderInvoiceId(orderInvoice.getId());
        // 根据开票订单明细将tid写入申请表明细
        Long id = Tools.getSequence(InvoiceConst.AC_F_INVOICE_APPLY);
        apply.setId(id);
        applyItemService.insertBySystemItemList(systemItemList,id, user,apply.getCpCShopId());
        invoiceApplyMapper.insert(apply);
        vh.put("code",ResultCode.SUCCESS);
        return vh;
    }

    /**
     * 修改开票金额校验
     * @param orderInvoice
     * @return
     */
    private ValueHolderV14<Map<Long, BigDecimal>> modAmountCheck(AcFOrderInvoice orderInvoice,OrderInvoiceModifyAmountRequest req,List<AcFOrderInvoiceItem> orderInvoiceItems) {
        ValueHolderV14 resp = new ValueHolderV14();
        if(Objects.isNull(orderInvoice)){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("发票不存在");
            return resp;
        }
        if(Objects.isNull(req) || Objects.isNull(req.getId()) || CollectionUtils.isEmpty(req.getItems())){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("数据缺失");
            return resp;
        }
        if(!InvoiceConst.AuditStatus.NOT_AUDIT.equals(orderInvoice.getAuditStatus())){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("选择的订单发票不能修改金额！");
            return resp;
        }
        if(InvoiceConst.TicketType.RED.equals(orderInvoice.getTicketType())){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("红字发票不能修改金额！");
            return resp;
        }
        // 获取对应店铺策略
        StCInvoiceStrategy strategy = invoiceStrategyService.getByShopId(orderInvoice.getCpCShopId());
        if(InvoiceConst.ModifyAMt.NOT_MODIFY.equals(strategy.getIsModifyAmt())){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("开票策略不允许修改金额! ");
            return resp;
        }
        Map<Long, BigDecimal> qtytMap = new HashMap<>();
        for(AcFOrderInvoiceItem item : orderInvoiceItems){
            qtytMap.put(item.getId(),item.getQty());
        }
        StringBuffer sb = new StringBuffer();
        Map<Long, BigDecimal> amtMap = new HashMap<>();
        for(int i = 1; i <= req.getItems().size(); i++){
            List<OrderInvoiceModifyAmountRequest.OrderInvoiceModifyAmountItem> items = req.getItems();
            OrderInvoiceModifyAmountRequest.OrderInvoiceModifyAmountItem modItem = items.get(i-1);
            if(qtytMap.containsKey(modItem.getItemId())){
                BigDecimal qty = qtytMap.get(modItem.getItemId());
                if(modItem.getAmount().multiply(new BigDecimal("100000")).intValue() % qty.intValue() != 0){
                    sb.append("第" + i + "行金额不能被数量整除，请重新修改" + "\n");
                } else {
                    amtMap.put(modItem.getItemId(),modItem.getAmount());
                }
            }
        }
        if(sb.length() > 0){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage(sb.toString());
            return resp;
        }
        resp.setCode(ResultCode.SUCCESS);
        resp.setData(amtMap);
        return resp;
    }

    public ValueHolderV14 change() {
        ValueHolderV14 change = changeTaskService.change();
        System.out.println(JSON.toJSONString(change));
        return change;
    }

    public ValueHolderV14 invoice() {
        invoiceTaskService.invoice();
        ValueHolderV14 result = new ValueHolderV14();
        result.setCode(ResultCode.SUCCESS);
        result.setMessage("开票成功");
        return result;
    }

    public ValueHolder resultConvert(ValueHolderV14 result){
        ValueHolder vh = new ValueHolder();
        vh.put("code", result.getCode());
        vh.put("message", result.getMessage());
        vh.put("data",result.getData());
        return vh;
    }

    public ValueHolderV14 getByid(Long id) {
        ValueHolderV14 resp = new ValueHolderV14();
        if(Objects.isNull(id) || id < 0){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("参数错误");
            return resp;
        }
        AcFOrderInvoice orderInvoice = orderInvoiceMapper.selectById(id);
        if(Objects.isNull(orderInvoice)){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("记录不存在");
            return resp;
        }
        resp.setCode(ResultCode.SUCCESS);
        resp.setMessage("查询成功");
        resp.setData(orderInvoice);
        return resp;
    }

    public void cancelOrderInvoice(Long id, User user,String changeInvoiceStatus){
        UpdateWrapper<AcFOrderInvoice> update = new UpdateWrapper<>();
        update.lambda().eq(AcFOrderInvoice::getId,id)
                .set(AcFOrderInvoice::getCancelStatus,InvoiceConst.CancelStatus.CANCELED)
                .set(AcFOrderInvoice::getModifieddate,new Date())
                .set(AcFOrderInvoice::getModifierename,user.getEname())
                .set(AcFOrderInvoice::getModifierid,Long.valueOf(user.getId()))
                .set(AcFOrderInvoice::getModifiername,user.getName());
        if(StringUtils.isNotBlank(changeInvoiceStatus)){
            update.lambda().set(AcFOrderInvoice::getChangeInvoiceStatus,changeInvoiceStatus);
        }
        orderInvoiceMapper.update(null,update);
    }

    private String getInvoiceInfoString(AcFOrderInvoice orderInvoice){
        return orderInvoice.getInvoiceHeader() + orderInvoice.getTaxpayerNo() +
                orderInvoice.getOpeningBank() + orderInvoice.getUnitName() +
                orderInvoice.getBankAccount() + orderInvoice.getUnitAddress() + orderInvoice.getUnitMobile() +
                orderInvoice.getCpCSupplierEname() + orderInvoice.getSupplierTaxpayerNo() +
                orderInvoice.getSupplierOpeningBank() + orderInvoice.getSupplierBankAccount() +
                orderInvoice.getSupplierAddress() + orderInvoice.getSupplierMobile()+
                orderInvoice.getDrawer() + orderInvoice.getPayee() + orderInvoice.getReviewer();
    }

    /**
     * 根据id查询开票列表
     * @param id
     * @return
     */
    public ValueHolderV14 getItemListByInvoiceId(Long id) {
        ValueHolderV14 resp = new ValueHolderV14();
        if(Objects.isNull(id)){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("参数不能为空");
            return resp;
        }
        List<AcFOrderInvoiceItem> orderInvoiceItems = invoiceItemMapper.queryByOrderInvoiceId(id);
        resp.setCode(ResultCode.SUCCESS);
        resp.setMessage("查询成功");
        resp.setData(orderInvoiceItems);
        return resp;
    }

    public ValueHolder modifyAmount(JSONObject param, User user) {
        OrderInvoiceModifyAmountRequest req = new OrderInvoiceModifyAmountRequest();
        JSONObject fixColumn = param.getJSONObject("fixcolumn");
        Long objid = param.getLong("objid");
        String subTableName = "AC_F_ORDER_INVOICE_ITEM";
        JSONArray jsonObjectItems = fixColumn.getJSONArray(subTableName);
        req.setId(objid);
        List<OrderInvoiceModifyAmountRequest.OrderInvoiceModifyAmountItem> items = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jsonObjectItems)) {
            List<AcFOrderInvoiceItem> orderInvoiceItems = JSONArray.parseArray(jsonObjectItems.toString(), AcFOrderInvoiceItem.class);
            // TODO 判断修改后的明细金额是否高于原金额
            // 获取所有开票明细
            List<AcFOrderInvoiceItem> orderInvoiceItemsBeforeList = invoiceItemMapper.queryByOrderInvoiceId(objid);
            List<Long> ids = orderInvoiceItems.stream().map(AcFOrderInvoiceItem::getId).collect(Collectors.toList());
            List<AcFOrderInvoiceItem> filteList = orderInvoiceItemsBeforeList.stream().filter(e -> !ids.contains(e.getId())).collect(Collectors.toList());
            filteList.addAll(orderInvoiceItems);
            BigDecimal allAmount = OrderInvoiceChangeTaskService.sumOfBigdecimalAmount(filteList, null, e -> e.getInclusiveTaxAmt());
            // 获取原始金额
            List<AcFOrderInvoiceSystemItem> systemItemList = invoiceSystemItemMapper.selectByOrderInvoiceId(objid);
            BigDecimal originAmt = systemItemList.stream().map(AcFOrderInvoiceSystemItem::getPriceAmt).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
            if(allAmount.compareTo(originAmt) > 0){
                req.setAmountMoreThanBeforeFlag("1");
            } else {
                req.setAmountMoreThanBeforeFlag("0");
            }
            for(AcFOrderInvoiceItem invoiceItem : orderInvoiceItems){
                OrderInvoiceModifyAmountRequest.OrderInvoiceModifyAmountItem item = new OrderInvoiceModifyAmountRequest.OrderInvoiceModifyAmountItem();
                item.setItemId(invoiceItem.getId());
                item.setAmount(invoiceItem.getInclusiveTaxAmt());
                items.add(item);
            }
            req.setItems(items);
        }
        ValueHolder result = resultConvert(modAmount(req));
        return result;
    }

    public ValueHolderV14 getResult() {
        getResultTaskService.getResult();
        ValueHolderV14 result = new ValueHolderV14();
        result.setCode(ResultCode.SUCCESS);
        result.setMessage("查询发票成功");
        return result;
    }

    /**
     * 反审核
     * 将未开票,未取消,未冻结,已审核的蓝票回退到未审核状态
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ValueHolder auditFallback(QuerySession querySession) {
        User user = querySession.getUser();
        SgR3BaseRequest request = R3ParamUtils.parseSaveObject(querySession, SgR3BaseRequest.class);
        request.setR3(true);

        ValueHolder vh = new ValueHolder();
        List<Long> ids = R3ParamUtils.getBatchObjIds(request);
        if(CollectionUtils.isEmpty(ids)){
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "请勾选一条数据来操作");
            return vh;
        }
        JSONArray errorMessage = new JSONArray(); // 错误信息
        int fail = 0;
        for(int i = 0; i < ids.size(); i++){
            Long id = ids.get(i);
            JSONObject jsonObject = new JSONObject();
            AcFOrderInvoice orderInvoice = orderInvoiceMapper.selectById(id);
            String msg = checkAuditFallback(orderInvoice);
            if (StringUtils.isNotBlank(msg)) {
                vh.put("code", ResultCode.FAIL);
                vh.put("message", msg);
                jsonObject.put("code", ResultCode.FAIL);
                jsonObject.put("message", msg);
                jsonObject.put("objid", id);
                errorMessage.add(jsonObject);
                fail++;
                continue;
            }
            // 校验通过, 将当前发票的审核状态改为未审核
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            UpdateWrapper<AcFOrderInvoice> update = new UpdateWrapper<>();
            update.lambda().eq(AcFOrderInvoice::getId,id).set(AcFOrderInvoice::getAuditStatus,InvoiceConst.AuditStatus.NOT_AUDIT)
                            .set(AcFOrderInvoice::getModifieddate,dateFormat.format(new Date())).set(AcFOrderInvoice::getModifierid,user.getId())
                            .set(AcFOrderInvoice::getModifierename,user.getEname()).set(AcFOrderInvoice::getModifiername,user.getName());
            if(InvoiceConst.InvoiceStatus.INVOICE_FAIL.equals(orderInvoice.getInvoiceStatus())){
                update.lambda().set(AcFOrderInvoice::getInvoiceStatus,InvoiceConst.InvoiceStatus.NOT_INVOICE);
            }
            orderInvoiceMapper.update(null,update);
            logService.addUserOrderLog(id,"反审核","反审核成功",user);
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "反审核成功");
        }
        if (ids.size() == 1) {
            return vh;
        }
        if (fail == 0) {
            vh.put("code", ResultCode.SUCCESS);
            vh.put("message", "反审核成功");
        } else {
            vh.put("data", errorMessage);
            vh.put("code", ResultCode.FAIL);
            vh.put("message", "反审核成功" + (ids.size() - fail) + "条，失败" + fail + "条数据");
        }
        return vh;
    }

    private String checkAuditFallback(AcFOrderInvoice invoice) {
        if(Objects.isNull(invoice)){
            return "记录不存在";
        }
        if(InvoiceConst.AuditStatus.NOT_AUDIT.equals(invoice.getAuditStatus())){
            return "当前发票未审核, 操作失败! ";
        }
        if(InvoiceConst.InvoiceStatus.IN_INVOICE.equals(invoice.getInvoiceStatus())){
            return "当前发票开票中, 操作失败! ";
        }
        if(InvoiceConst.InvoiceStatus.INVOICE_SUCCESS.equals(invoice.getInvoiceStatus())){
            return "当前发票开票成功, 操作失败! ";
        }
        if(InvoiceConst.CancelStatus.CANCELED.equals(invoice.getCancelStatus())){
            return "当前发票已取消, 操作失败! ";
        }
        if(InvoiceConst.FreezeStatus.FREEZED.equals(invoice.getFreezeStatus())){
            return "当前发票已冻结, 操作失败! ";
        }
        if(InvoiceConst.TicketType.RED.equals(invoice.getTicketType())){
            return "红字发票不允许反审核, 操作失败! ";
        }
        return null;
    }

    public ValueHolderV14 invoiceInfoModify(InvoiceInfoModifyRequest param) {
        AcFOrderInvoice orderInvoice = orderInvoiceMapper.selectById(param.getId());
        if (Objects.isNull(orderInvoice)) {
            return ValueHolderV14Utils.getFailValueHolder("未找到发票单数据");
        }
        //仅针对开票状态为“待开票”和“开票失败”的发票有用
        String invoiceStatus = orderInvoice.getInvoiceStatus();
        if (!InvoiceConst.InvoiceStatus.NOT_INVOICE.equals(invoiceStatus) && !InvoiceConst.InvoiceStatus.INVOICE_FAIL.equals(invoiceStatus)) {
            return ValueHolderV14Utils.getFailValueHolder("只能修改待开票或开票失败的数据");
        }

        if (StringUtils.isBlank(param.getInvoiceHeader()) || StringUtils.isBlank(param.getTaxpayerNo())) {
            return ValueHolderV14Utils.getFailValueHolder("抬头或者税号不能为空");
        }

        String invoiceHeader = param.getInvoiceHeader().trim();
        String taxpayerNo = param.getTaxpayerNo().trim();

        AcFOrderInvoice invoice = new AcFOrderInvoice();
        invoice.setId(orderInvoice.getId());
        invoice.setModifieddate(new Date());
        invoice.setInvoiceHeader(invoiceHeader);
        invoice.setTaxpayerNo(taxpayerNo);

        orderInvoiceMapper.updateById(invoice);

        return ValueHolderV14Utils.getSuccessValueHolder("修改成功!");
    }

    public void autoChange(Long id, User user) {
        log.info("生成退单待红冲发票后自动转换，id:{}", id);
        TransactionUtils.afterCommitAsyncExecute(acFOrderInvoiceGetThreadPoolExecutor, () -> {
            log.info("生成退单待红冲发票后自动转换，redOffsetId:{}", id);
            JSONArray ids = new JSONArray();
            ids.add(id);
            ValueHolder valueHolder = acFInvoiceReturnRedOffsetService.manualChange(ids, user);
            log.info("生成退单待红冲发票后自动转换结果：{}", valueHolder.toJSONObject().toJSONString());
        });
        log.info("生成退单待红冲发票后自动转换结束，id:{}", id);
    }
}
