package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.burgeon.r3.sg.basic.common.SgConstantsIF;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.rpc.SgRpcService;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AcBeanUtils;
import com.jackrain.nea.util.AcPayableAdjustmentPushESUtil;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Component
@Slf4j
public class PayableAdjustmentAuditService extends CommandAdapter {
    @Resource
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Resource
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    @Resource
    private PayableAdjustmentSaveService payableAdjustmentSaveService;

    /**
     * 客审
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);

        if (param != null) {
            JSONArray itemArray = AcBeanUtils.makeAuditJsonArray(param);
            JSONArray errorArray = new JSONArray();
            if (itemArray.size() > 0) {
                for (int i = 0; i < itemArray.size(); i++) {
                    Long id = itemArray.getLong(i);
                    try {
                        auditFunction(id, querySession);
                    } catch (Exception e) {
                        JSONObject errJo = new JSONObject();
                        errJo.put("objid", id);
                        errJo.put("message", e.getMessage());
                        errorArray.add(errJo);
                    }
                }
            } else {
                throw new NDSException("未找到需要客审的记录！");
            }
            valueHolder = AcBeanUtils.getProcessValueHolder(itemArray, errorArray, "客审");
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    private void checkStatus(Long objId) {
        AcFPayableAdjustmentDO acFPayableAdjustmentDO = acFPayableAdjustmentMapper.selectById(objId);
        int iStatus = acFPayableAdjustmentDO.getBillStatus();
        if (iStatus != AcConstant.CON_BILL_STATUS_01) {
            throw new NDSException("单据处于未审核状态才能进行客审！");
        }

        Date sourceOutsourceDate = acFPayableAdjustmentDO.getSourceOutsourceDate();
        if (sourceOutsourceDate == null) {
            throw new NDSException("原始出库日期不能为空！");
        }

        List<AcFPayableAdjustmentItemDO> acFPayableAdjustmentItemDOList =
                acFPayableAdjustmentItemMapper.listByItemId(objId);
        if (acFPayableAdjustmentItemDOList == null || acFPayableAdjustmentItemDOList.size() <= 0) {
            throw new NDSException("明细没有记录，不允许客审！");
        }

        //判断明细的发货逻辑仓是否为空
//        for (AcFPayableAdjustmentItemDO itemDO : acFPayableAdjustmentItemDOList) {
//            Long logicalStoreId = itemDO.getLogicalStoreId() == null ? 0L : itemDO.getLogicalStoreId();
//            if (logicalStoreId <= 0L) {
//                errJo.put("objid", objId);
//                errJo.put("message", "明细发货逻辑不能为空！");
//                return errJo;
//            }
//        }
    }

    @Transactional(rollbackFor = {Exception.class})
    public void auditFunction(Long objId, QuerySession querySession) {
        AcFPayableAdjustmentDO acFPayableAdjustmentDO = acFPayableAdjustmentMapper.selectById(objId);

        Date date = new Date();

        if (acFPayableAdjustmentDO != null) {
            //更新明细的发货逻辑仓,如果存在则判断是否是有效的
//                    updateStoreItem(acFPayableAdjustmentDO);

            //校验单据
            checkStatus(objId);

            //BILL_STATUS    1:未审核 2:已客审 3:已业审 4:财审 5:已作废
            acFPayableAdjustmentDO.setBillStatus(AcConstant.CON_BILL_STATUS_02);

            acFPayableAdjustmentDO.setGuestTrialId(Long.valueOf(querySession.getUser().getId()));

            acFPayableAdjustmentDO.setGuestTrialTime(date);

            acFPayableAdjustmentDO.setGuestTrialName(querySession.getUser().getName());
            acFPayableAdjustmentDO.setGuestTrialEname(querySession.getUser().getEname());

            int iResult = acFPayableAdjustmentMapper.updateById(acFPayableAdjustmentDO);
            if (iResult < 0) {
                throw new NDSException("单据编码:" + acFPayableAdjustmentDO.getBillNo() + ",客审失败！");
            }

            //应付款调整单-操作类型=审核
            ValueHolderV14 valueHolderV14 = payableAdjustmentSaveService.insertLogFun(querySession.getUser(), objId,
                    OperatorLogTypeEnum.OPERATOR_KS.getVal(),
                    OperatorLogTypeEnum.OPERATOR_KS.getText(), date);
            //推送ES
            AcPayableAdjustmentPushESUtil.pushOrder(objId);
            if (valueHolderV14.getCode() == ResultCode.FAIL) {
                throw new NDSException("应付款调整单=>新增操作日志-客审失败!");
            }
        } else {
            throw new NDSException("当前记录不存在！");
        }
    }

    /**
     * 更新应付款调整单明细的发货逻辑仓
     *
     * @param acFPayableAdjustmentDO
     */
    private void updateStoreItem(AcFPayableAdjustmentDO acFPayableAdjustmentDO) {
        //objId
        Long objId = acFPayableAdjustmentDO.getId();

        //来源单据ID=全渠道订单id
        Long sourceBillId = acFPayableAdjustmentDO.getSourceTid();

        //来源单据编号=全渠道单据编号
        String sourceBillNo = acFPayableAdjustmentDO.getOrderNo();

        //单据类型=零售发货单
        Integer sourceBillType = SgConstantsIF.BILL_TYPE_RETAIL;

        //查询sg RPC 查询出逻辑发货单
        SgRpcService sgRpcService = ApplicationContextHandle.getBean(SgRpcService.class);
//        ValueHolderV14<SgSendBillQueryResult> valueHolderV14 = sgRpcService.getQuerySgBSend(sourceBillId, sourceBillNo, sourceBillType);

//        if (valueHolderV14 != null && valueHolderV14.getData() != null) {
//            String key = sourceBillId + "," + sourceBillType;
//            HashMap<SgBSend, List<SgBSendItem>> sgBSend = valueHolderV14.getData().getResults().get(key);
//
//            if (sgBSend == null) {
//                return;
//            }
//
//            HashMap<Long, List<SgBSendItem>> listHashMap = new HashMap<Long, List<SgBSendItem>>();
//
//            for (HashMap.Entry<SgBSend, List<SgBSendItem>> entry : sgBSend.entrySet()) {
//                List<SgBSendItem> itemList = entry.getValue();
//
//                itemList.forEach(item -> {
//                    SgBSendItem sgBSendItem = item;
//                    Long sourceBillItemId = sgBSendItem.getSourceBillItemId();
//                    List<SgBSendItem> sgBSendList = new ArrayList<>();
//                    if (listHashMap.containsKey(sourceBillItemId)) {
//                        sgBSendList = listHashMap.get(sourceBillItemId);
//                        sgBSendList.add(sgBSendItem);
//                        listHashMap.put(sourceBillItemId, sgBSendList);
//                    } else {
//                        sgBSendList.add(sgBSendItem);
//                        listHashMap.put(sourceBillItemId, sgBSendList);
//                    }
//                });
//            }
//
//            if (listHashMap != null && listHashMap.size() > 0) {
//                for (HashMap.Entry<Long, List<SgBSendItem>> entry : listHashMap.entrySet()) {
//                    //如果一行SKU存在多个发货逻辑仓，则不赋值
//                    List<SgBSendItem> itemList = entry.getValue();
//                    Long id = entry.getKey();
//
//                    if (itemList.size() == 1) {
//                        //获取明细的发货逻辑仓
//                        Long cpCStoreId = itemList.get(0).getCpCStoreId();
//                        String cpcStoreEcode = itemList.get(0).getCpCStoreEcode();
//                        String cpCStoreEname = itemList.get(0).getCpCStoreEname();
//                        acFPayableAdjustmentItemMapper.updateStorePayableItem(objId, id, cpCStoreId, cpcStoreEcode, cpCStoreEname);
//                    }
//                }
//            }
//        }
    }
}
