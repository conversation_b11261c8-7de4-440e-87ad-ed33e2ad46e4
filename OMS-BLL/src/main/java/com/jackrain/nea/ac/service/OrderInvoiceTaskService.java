package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.base.Throwables;
import com.jackrain.nea.cp.result.CpCSupplier;
import com.jackrain.nea.hub.api.HubInvoicingCmd;
import com.jackrain.nea.hub.model.HXInvoicingModel.InvoiceSplitLines;
import com.jackrain.nea.hub.model.HXInvoicingModel.JsonRootBean;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultType;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceSystemItemMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceItem;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceSystemItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.StCInvoiceStrategy;
import com.jackrain.nea.oc.oms.services.invoice.InvoiceLogService;
import com.jackrain.nea.oc.oms.util.DingTalkUtil;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.util.ValueHolderV14Utils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * @ClassName OrderInvoiceService
 * @Description 订单开票定时任务实现类
 * @Date 2022/9/8 上午9:50
 * @Created by wuhang
 */
@Slf4j
@Component
public class OrderInvoiceTaskService {

    /**
     * 纸质专票-全电纸专
     */
    private final static String ALL_ELECTRIC_PAPER_SPECIALIZATION = "I";
    /**
     * 纸质普票-全电纸普
     */
    private final static String ALL_ELECTRIC_PAPER = "J";
    /**
     * 电子专票-全电专票
     */
    private final static String FULL_ELECTRIC_SPECIAL_TICKET = "K";
    /**
     * 电子普票-全电普票
     */
    private final static String ALL_ELECTRIC_GENERAL_TICKET = "L";

    @Autowired
    private AcFOrderInvoiceMapper orderInvoiceMapper;

    @Autowired
    private AcFOrderInvoiceItemMapper orderInvoiceItemMapper;

    @Autowired
    private AcFOrderInvoiceSystemItemMapper systemItemMapper;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private InvoiceStrategyService invoiceStrategyService;

    @Autowired
    private OrderInvoiceService orderInvoiceService;

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Autowired
    private InvoiceLogService logService;

    @Autowired
    private ThreadPoolTaskExecutor acFOrderInvoiceThreadPoolExecutor;

    @Reference(group = "hub", version = "1.0")
    private HubInvoicingCmd hubInvoicingCmd;

    private final Map<String, CpCSupplier> supplierMap = new HashMap<>();

    @NacosValue(value = "${lts.OrderInvoiceTaskService.range:100}", autoRefreshed = true)
    public Integer range;

    public void invoice() {
        executeThread();
    }

    //使用线程
    public void executeThread() {
        String threadPoolName = "R3_OMS_AC_F_ORDER_INVOICE_%d";
        log.info(LogUtil.format("OrderInvoiceTaskService---->发票申请表汇总条数:{}", "OrderInvoiceTaskService"), range);
        try {
            long start = System.currentTimeMillis();
            final String taskTableName = "ac_f_order_invoice";
            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
            List<AcFOrderInvoice> acFOrderInvoices = orderInvoiceMapper.selectTaskObjectList(range * 24, taskTableName);
            if (CollectionUtils.isEmpty(acFOrderInvoices)) {
                return;
            }
            List<List<AcFOrderInvoice>> lists = ListSplitUtil.averageAssign(acFOrderInvoices, 24);
            for (List<AcFOrderInvoice> data : lists) {
                results.add(acFOrderInvoiceThreadPoolExecutor.submit(new OrderInvoiceTaskService.CallableTobeConfirmedTaskWithResult(data)));
            }

            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    log.debug(LogUtil.format("OrderInvoiceTaskService------>线程结果:{}", "OrderInvoiceTaskService"), futureResult.get().toString());
                } catch (Exception e) {
                    log.error(LogUtil.format("OrderInvoiceTaskService：{}", threadPoolName, "OrderInvoiceTaskService"), Throwables.getStackTraceAsString(e));
                }
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OrderInvoiceTaskService 开票定时任务完成 useTime:{}", "OrderInvoiceTaskService", (System.currentTimeMillis() - start)));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OrderInvoiceTaskService.Execute Error：{}", threadPoolName, "OrderInvoiceTaskService"), Throwables.getStackTraceAsString(ex));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } finally {
            //executor.shutdown();
        }
    }

    /**
     * 开启线程类
     */
    class CallableTobeConfirmedTaskWithResult implements Callable<Boolean> {

        private final List<AcFOrderInvoice> data;

        public CallableTobeConfirmedTaskWithResult(List<AcFOrderInvoice> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            if (CollectionUtils.isNotEmpty(data)) {
                exccuteInvoice(data);
            }
            return true;
        }
    }

    public void exccuteInvoice(List<AcFOrderInvoice> invoiceList){
        // 定时任务查询订单发票表“审核状态”=已审核、“冻结状态”=未冻结、
        // “开票状态”=未开票、发票种类=电子且订单状态和发票开票节点一致，且订单没有申请退款的记录。
        // 默认只有交易成功,取oc_b_order order_status的5(仓库发货),6(平台发货) 平台状态为 platform_status=TRADE_FINISHED(交易完成)
        User user = SystemUserResource.getRootUser();
        List<AcFOrderInvoice> canInvoiceList = new ArrayList<>();
        for(AcFOrderInvoice invoice : invoiceList){
            // 获取零售发货单,判断零售发货单当前状态是否满足开票条件
            if(InvoiceConst.TicketType.BLUE.equals(invoice.getTicketType())) {
                List<Long> orderIds = systemItemMapper.selectOrderidsByOrderInvoiceId(invoice.getId());
                // 查询零售发货单时过滤掉补发/复制的订单
                List<OcBOrder> orders = orderMapper.queryListByTidsWithOutCopyAndResetShip(orderIds);
                // 校验订单是否全部取消或作废,如果全部取消或作废则将该发票取消,跳过此次开票
                long cancelCount = orders.stream().filter(e -> !InvoiceConst.cancelOrderStatusList.contains(e.getOrderStatus())).count();
                if(cancelCount <= 0){
                    log.info("---| tid:["+invoice.getTid()+"],订单已全部取消或作废,取消开票");
                    // 取消发票
                    orderInvoiceService.cancelOrderInvoice(invoice.getId(),user,null);
                    if(Objects.nonNull(invoice.getInvoiceApplyId()) && invoice.getInvoiceApplyId() > 0){
                        // 变更申请明细的id
                        invoiceApplyService.updateInvoiceApplyItemTidSuffix(invoice.getInvoiceApplyId());
                    }
                    continue;
                }
                // 获取开票策略
                List<String> invalidOrderTids = new ArrayList<>();
                StCInvoiceStrategy strategy = invoiceStrategyService.getByShopId(invoice.getCpCShopId());
                log.info("---| tid:["+invoice.getTid()+"],获取到的开票策略:"+JSON.toJSONString(strategy));
                if(Objects.isNull(strategy)){
                    log.info("---| tid:["+invoice.getTid()+"],shop id:["+invoice.getCpCShopId()+"],开票策略为空！");
                    continue;
                }
                if(InvoiceConst.InvoiceNode.TRADE_SUCCESS_ALL.equals(strategy.getInvoiceNode())){
                    // 交易完成-全部订单 订单状态是5(仓库发货)/6(平台发货) 并且 平台状态是 TRADE_FINISHED交易完成
                    invalidOrderTids = orders.stream().filter(e -> (!"5".equals(e.getOrderStatus().toString())
                            && !"6".equals(e.getOrderStatus().toString())) || !"TRADE_FINISHED".equals(e.getPlatformStatus())).map(OcBOrder::getTid).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(invalidOrderTids)) {
                        log.info("---| 开票校验-tids:" + invalidOrderTids + "状态不满足开票要求");
                        continue;
                    }
                } else if (InvoiceConst.InvoiceNode.TRADE_SUCCESS_PART.equals(strategy.getInvoiceNode())){
                    // 交易完成-部分订单，订单只要有一个满足即可开票
                    long count = orders.stream().filter(e -> ("5".equals(e.getOrderStatus().toString()) || "6".equals(e.getOrderStatus().toString())) && "TRADE_FINISHED".equals(e.getPlatformStatus())).count();
                    if(count <= 0){
                        log.info("---| 开票校验-tids:" + invoice.getTid() + "状态不满足开票要求");
                        continue;
                    }
                } else if (InvoiceConst.InvoiceNode.AFTER_SHIPMENT_ALL.equals(strategy.getInvoiceNode())){
                    // 发货后-全部订单 订单状态是5(仓库发货)/6(平台发货) 不校验平台状态
                    invalidOrderTids = orders.stream().filter(e -> (!"5".equals(e.getOrderStatus().toString()) && !"6".equals(e.getOrderStatus().toString()))).map(OcBOrder::getTid).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(invalidOrderTids)) {
                        log.info("---| 开票校验-tids:" + invalidOrderTids + "状态不满足开票要求");
                        continue;
                    }
                } else if (InvoiceConst.InvoiceNode.AFTER_SHIPMENT_PART.equals(strategy.getInvoiceNode())) {
                    // 发货后-部分订单，订单状态只要有一个满足即可开票
                    long count = orders.stream().filter(e -> ("5".equals(e.getOrderStatus().toString()) || "6".equals(e.getOrderStatus().toString()))).count();
                    if (count <= 0) {
                        log.info("---| 开票校验-tids:" + invoice.getTid() + "状态不满足开票要求");
                        continue;
                    }
                } else if (InvoiceConst.InvoiceNode.AFTER_VERIFY.equals(strategy.getInvoiceNode())){
                    // 审核后
                    invalidOrderTids = orders.stream().filter(e -> (!"3".equals(e.getOrderStatus().toString()) && !"4".equals(e.getOrderStatus().toString()) && !"5".equals(e.getOrderStatus().toString()) && !"6".equals(e.getOrderStatus().toString()))).map(OcBOrder::getTid).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(invalidOrderTids)) {
                        log.info("---| 开票校验-tids:" + invalidOrderTids + "状态不满足开票要求");
                        continue;
                    }
                }
            }
            canInvoiceList.add(invoice);
        }

        if(CollectionUtils.isNotEmpty(canInvoiceList)) {
            for(AcFOrderInvoice invoice : canInvoiceList) {
                // 组装开票数据
                JsonRootBean invoiceParam = invoiceConvert(invoice);
                if (Objects.isNull(invoiceParam)) {
                    continue;
                }
                log.info("---| 开票信息-tid:" + invoice.getTid() + ", 开票提交入参:" + JSON.toJSONString(invoiceParam));
                // 调用开票接口
                ResultType result = hubInvoicingCmd.asynchronousJsonInvoicing(invoiceParam);
                log.info("---| 发票开票接口-tid:" + invoice.getTid() + ", 开票提交出参:" + JSON.toJSONString(result));
                UpdateWrapper<AcFOrderInvoice> updateWrapper = new UpdateWrapper<>();
                LambdaUpdateWrapper<AcFOrderInvoice> update = updateWrapper.lambda();
                update.eq(AcFOrderInvoice::getId, invoice.getId());
                if ("1".equals(result.getCode())
                        || "200".equals(result.getCode())) {
                    //  改为异步接口后提交成功更新为开票中
                    update.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.IN_INVOICE);
                    update.set(AcFOrderInvoice::getFailReason, "");// 将失败原因置空
                    logService.addUserOrderLog(invoice.getId(), "开票", "开票提交成功成功", user);
                } else {
                    String msg = "1".equals(result.getCode()) ? result.getMsg() : result.getMessage();
                    log.info("---| 开票失败, tid:" + invoice.getTid() + ", 失败信息:" + msg);
                    // 更新开票失败状态
                    String errMsg = StringUtils.isNotBlank(msg) ? msg : "开票提交失败";
                    if (errMsg.length() > 200) {
                        errMsg = errMsg.substring(0, 200);
                    }
                    update.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.INVOICE_FAIL);
                    update.set(AcFOrderInvoice::getFailReason, errMsg);
                    logService.addUserOrderLog(invoice.getId(), "开票", "开票提交失败：" + errMsg, user);
                }

                // 处理开票失败
//                if(!"1".equals(result.getCode())){
//                    log.info("---| 开票失败, tid:" + invoice.getTid() + ", 失败信息:" + result.getMsg());
//                    // 更新开票失败状态
//                    String errMsg = StringUtils.isNotBlank(result.getMsg()) ? result.getMsg() : "开票提交失败";
//                    if(errMsg.length() > 200){
//                        errMsg = errMsg.substring(0,200);
//                    }
//                    update.set(AcFOrderInvoice::getInvoiceStatus,InvoiceConst.InvoiceStatus.INVOICE_FAIL);
//                    update.set(AcFOrderInvoice::getFailReason,errMsg);
//                    logService.addUserOrderLog(invoice.getId(),"开票","开票提交失败失败：" + errMsg,user);
//                } else {
//                    //  改为异步接口后提交成功更新为开票中
//                    update.set(AcFOrderInvoice::getInvoiceStatus,InvoiceConst.InvoiceStatus.IN_INVOICE);
//                    update.set(AcFOrderInvoice::getFailReason,"");// 将失败原因置空
//                    logService.addUserOrderLog(invoice.getId(), "开票", "开票提交成功成功", user);
//                }
                update.set(AcFOrderInvoice::getModifierid, Long.valueOf(user.getId()));
                update.set(AcFOrderInvoice::getModifierename, user.getEname());
                update.set(AcFOrderInvoice::getModifiername, user.getName());
                update.set(AcFOrderInvoice::getModifieddate, new Date());
                orderInvoiceMapper.update(null, updateWrapper);
            }
        }
        log.info("---| 开票定时任务执行完毕");
    }

    /**
     * 开票成功将零售发货单的开票状态改成已发货
     * @param invoice
     */
    private void updateOrderInvoiceStatus(AcFOrderInvoice invoice) {
        // 根据发票单管理查询出所有的订单明细,根据订单明细找出所有的平台单号,将所有的平台单对应的零售发货单更新成
        List<AcFOrderInvoiceSystemItem> items = systemItemMapper.selectByOrderInvoiceId(invoice.getId());
        if(CollectionUtils.isEmpty(items)){
            return;
        }
        List<Long> ids = items.stream().map(AcFOrderInvoiceSystemItem::getOcBOrderId).collect(Collectors.toList());
        UpdateWrapper<OcBOrder> update = new UpdateWrapper<>();
        update.lambda().in(OcBOrder::getId,ids).set(OcBOrder::getInvoiceStatus,3);// 0未登记,1部分登记,2已开票,3部分开票
        orderMapper.update(null,update);
    }

    /**
     * 开票表数据转换为开票接口数据
     * @param invoice
     * @return
     */
    private JsonRootBean invoiceConvert(AcFOrderInvoice invoice) {
        JsonRootBean invoiceParam = new JsonRootBean();
        // 根据供应商id查询
        invoiceParam.setOrgId(invoice.getCpCSupplierEcode());// 公司代码
        invoiceParam.setOrgName(invoice.getCpCSupplierEname());// 公司全称
        invoiceParam.setOrgTaxcode(invoice.getSupplierTaxpayerNo());// 社会信用代码
        // 税控机号,如果发票表存在税控机号,则取发票表的税控机号,如果为空则取默认
        invoiceParam.setOrgMachine(StringUtils.isBlank(invoice.getTaxMachineNo()) ? "03" : invoice.getTaxMachineNo());
        invoiceParam.setOrgAddress(invoice.getSupplierAddress());// 销方组织地址
        invoiceParam.setOrgTelephone(invoice.getSupplierMobile());// 销方组织电话
        invoiceParam.setOrgBankname(invoice.getSupplierOpeningBank());// 销方组织开户行
        invoiceParam.setOrgBankaccount(invoice.getSupplierBankAccount());// 销方组织银行账号
        invoiceParam.setBillRemark(invoice.getInvoiceRemark());// 开票备注
        invoiceParam.setCustAddress(invoice.getUnitAddress());// 开票客户地址,专票非空,普票/外销可空
        invoiceParam.setCustBankaccount(invoice.getBankAccount());// 开票客户银行账号
        invoiceParam.setCustBankname(invoice.getOpeningBank());// 开票客户开户行
        invoiceParam.setCustName(invoice.getInvoiceHeader());// 开票客户全称(发票抬头)
        if(InvoiceConst.HeaderType.ENTERPRISE.equals(invoice.getHeaderType())) {
            invoiceParam.setCustTaxcode(invoice.getTaxpayerNo());// 开票客户税号(专票非空,普票/外销可空)
            //企业
            invoiceParam.setGfzrrbs("N");
        }else {
            //个人
            invoiceParam.setGfzrrbs("Y");
        }
        invoiceParam.setCustTelephone(invoice.getUnitMobile());// 开票客户电话
        invoiceParam.setDocNum(invoice.getBillNo());// 单据编号
        invoiceParam.setInvoiceBase("1");// 基准方式 1:含税金额固定 2:未税金额固定 3:含税单价固定 4:未税单价固定
        invoiceParam.setInvoiceWay("1");// 开票方式1:净值开票 2:折扣开票
        invoiceParam.setInvoiceList("0");// 是否清单开票 (0:否 1：是)
        if(InvoiceConst.TicketType.RED.equals(invoice.getTicketType())){
            invoiceParam.setInvoiceRed("X");// X红票,Y蓝票
            // 根据关联蓝票id查询对应蓝票,获取发票
            if(Objects.isNull(invoice.getBlueTicketId())){
                return null;
            }
            AcFOrderInvoice blueTicket = orderInvoiceMapper.selectById(invoice.getBlueTicketId());
            if(Objects.isNull(blueTicket)){
                return null;
            }
            invoiceParam.setInvoiceRedFpdm(blueTicket.getInvoiceCode());// 原蓝票发票代码
            invoiceParam.setInvoiceRedFphm(blueTicket.getInvoiceNumber());// 原蓝票发票号码
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
            invoiceParam.setInvoiceRedKprq(dateFormat.format(blueTicket.getInvoiceDate()));// 原蓝票开票日期
            invoiceParam.setInvoiceRedReqm("21");// 红票申请原因 11购方申请,21销方申请,固定销方申请
        } else {
            invoiceParam.setInvoiceRed("Y");
        }
        invoiceParam.setInvoiceType(invoiceTypeConvert(invoice));
        invoiceParam.setPayeeName(invoice.getPayee());// 收款人
        invoiceParam.setUserName(invoice.getDrawer());// 开票人
        invoiceParam.setCheckName(invoice.getReviewer());// 复核人
        invoiceParam.setIsOil("0");
        invoiceParam.setReopen(0);
        invoiceParam.setCustEmail(invoice.getEmail());// 接受发票邮箱
        // 组装开票明细
        List<AcFOrderInvoiceItem> orderInvoiceItems = orderInvoiceItemMapper.queryByOrderInvoiceId(invoice.getId());
        List<InvoiceSplitLines> lines = new ArrayList<>();
        for(int i = 0; i < orderInvoiceItems.size(); i++){
            AcFOrderInvoiceItem item = orderInvoiceItems.get(i);
            InvoiceSplitLines line = new InvoiceSplitLines();
            line.setDocLine(String.valueOf(i+1));// 行号
            line.setItemName(item.getInvoiceProName());// 商品名称
            line.setItemSpec(item.getInvoiceSpecName());// 规格
            line.setQuantity(item.getQty().toString());// 数量,保留8位小数
            line.setTaxCatecode(item.getTaxClassification());// 品类分类编码
            line.setTaxRate(Double.valueOf(item.getTaxRate()));// 税率,保留两位小数
            line.setUnitName(item.getUnit());// 单位
            line.setZamountHsj(item.getInclusiveTaxAmt().doubleValue());// 含税金额,保留两位小数
            line.setZamountSej(item.getInvoiceTaxAmt().doubleValue());// 税额,保留两位小数
            line.setZamountWsj(item.getNoTaxAmt().doubleValue());// 未税金额,保留两位小数
            line.setZpriceHsj(item.getInclusiveTaxPrice().doubleValue());// 含税单价,保留8位小数
            line.setZpriceWsj(item.getNoTaxPrice().doubleValue());// 未税单价,保留8位小数
            lines.add(line);
        }
        invoiceParam.setInvoiceSplitLines(lines);
        return invoiceParam;
    }

    /**
     * 将发票类型转换为对应的字符串常量。
     *
     * @param invoice 输入的发票对象，包含发票种类和发票类型信息。
     * @return 根据发票种类和发票类型返回相应的字符串常量。若未找到匹配的类型，则返回空字符串。
     */
    private String invoiceTypeConvert(AcFOrderInvoice invoice) {
        String invoiceKind = invoice.getInvoiceKind();
        String invoiceType = invoice.getInvoiceType();

        // 处理纸质发票
        if (InvoiceConst.InvoiceKind.PAPER.equals(invoiceKind)) {
            // 特殊发票
            if (InvoiceConst.InvoiceType.SPECIAL.equals(invoiceType)) {
                return ALL_ELECTRIC_PAPER_SPECIALIZATION;
            }
            // 普通发票
            if (InvoiceConst.InvoiceType.NORMAL.equals(invoiceType)) {
                return ALL_ELECTRIC_PAPER;
            }
        }

        // 处理电子发票
        if (InvoiceConst.InvoiceKind.ELECTRONIC.equals(invoiceKind)) {
            // 特殊发票
            if (InvoiceConst.InvoiceType.SPECIAL.equals(invoiceType)) {
                return FULL_ELECTRIC_SPECIAL_TICKET;
            }
            // 普通发票
            if (InvoiceConst.InvoiceType.NORMAL.equals(invoiceType)) {
                return ALL_ELECTRIC_GENERAL_TICKET;
            }
        }

        // 当发票种类或类型未匹配到时，通过钉钉工具记录信息
        DingTalkUtil.dingInvoice("未找到开票类型,billNo:" + invoice.getBillNo());

        return "";
    }

    public ValueHolderV14 exccuteInvoiceMcp(List<AcFOrderInvoice> invoiceList) {
        StringBuilder sb = new StringBuilder();
        User user = SystemUserResource.getRootUser();
        List<AcFOrderInvoice> canInvoiceList = new ArrayList<>();
        for (AcFOrderInvoice invoice : invoiceList) {
            // 获取零售发货单,判断零售发货单当前状态是否满足开票条件
            if (InvoiceConst.TicketType.BLUE.equals(invoice.getTicketType())) {
                List<Long> orderIds = systemItemMapper.selectOrderidsByOrderInvoiceId(invoice.getId());
                // 查询零售发货单时过滤掉补发/复制的订单
                List<OcBOrder> orders = orderMapper.queryListByTidsWithOutCopyAndResetShip(orderIds);
                // 校验订单是否全部取消或作废,如果全部取消或作废则将该发票取消,跳过此次开票
                long cancelCount = orders.stream().filter(e -> !InvoiceConst.cancelOrderStatusList.contains(e.getOrderStatus())).count();
                if (cancelCount <= 0) {
                    log.info("invoice mcp ---| tid:[" + invoice.getTid() + "],订单已全部取消或作废,取消开票");
                    // 取消发票
                    orderInvoiceService.cancelOrderInvoice(invoice.getId(), user, null);
                    if (Objects.nonNull(invoice.getInvoiceApplyId()) && invoice.getInvoiceApplyId() > 0) {
                        // 变更申请明细的id
                        invoiceApplyService.updateInvoiceApplyItemTidSuffix(invoice.getInvoiceApplyId());
                    }
                    sb.append(invoice.getTid()).append("订单已全部取消或作废,取消开票").append(",");
                    continue;
                }
                // 获取开票策略
                List<String> invalidOrderTids = new ArrayList<>();
                StCInvoiceStrategy strategy = invoiceStrategyService.getByShopId(invoice.getCpCShopId());
                log.info("---| tid:[" + invoice.getTid() + "],获取到的开票策略:" + JSON.toJSONString(strategy));
                if (Objects.isNull(strategy)) {
                    log.info("---| tid:[" + invoice.getTid() + "],shop id:[" + invoice.getCpCShopId() + "],开票策略为空！");
                    sb.append(invoice.getTid()).append("shop id:[" + invoice.getCpCShopId() + "],开票策略为空！").append(",");
                    continue;
                }
                if (InvoiceConst.InvoiceNode.TRADE_SUCCESS_ALL.equals(strategy.getInvoiceNode())) {
                    // 交易完成-全部订单 订单状态是5(仓库发货)/6(平台发货) 并且 平台状态是 TRADE_FINISHED交易完成
                    invalidOrderTids = orders.stream().filter(e -> (!"5".equals(e.getOrderStatus().toString())
                            && !"6".equals(e.getOrderStatus().toString())) || !"TRADE_FINISHED".equals(e.getPlatformStatus())).map(OcBOrder::getTid).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(invalidOrderTids)) {
                        log.info("---| 开票校验-tids:" + invalidOrderTids + "状态不满足开票要求");
                        sb.append(invoice.getTid()).append("订单状态不满足开票要求").append(",");
                        continue;
                    }
                } else if (InvoiceConst.InvoiceNode.TRADE_SUCCESS_PART.equals(strategy.getInvoiceNode())) {
                    // 交易完成-部分订单，订单只要有一个满足即可开票
                    long count =
                            orders.stream().filter(e -> ("5".equals(e.getOrderStatus().toString()) || "6".equals(e.getOrderStatus().toString())) && "TRADE_FINISHED".equals(e.getPlatformStatus())).count();
                    if (count <= 0) {
                        log.info("---| 开票校验-tids:" + invoice.getTid() + "状态不满足开票要求");
                        continue;
                    }
                } else if (InvoiceConst.InvoiceNode.AFTER_SHIPMENT_ALL.equals(strategy.getInvoiceNode())) {
                    // 发货后-全部订单 订单状态是5(仓库发货)/6(平台发货) 不校验平台状态
                    invalidOrderTids =
                            orders.stream().filter(e -> (!"5".equals(e.getOrderStatus().toString()) && !"6".equals(e.getOrderStatus().toString()))).map(OcBOrder::getTid).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(invalidOrderTids)) {
                        log.info("---| 开票校验-tids:" + invalidOrderTids + "状态不满足开票要求");
                        sb.append(invoice.getTid()).append("订单状态不满足开票要求").append(",");
                        continue;
                    }
                } else if (InvoiceConst.InvoiceNode.AFTER_SHIPMENT_PART.equals(strategy.getInvoiceNode())) {
                    // 发货后-部分订单，订单状态只要有一个满足即可开票
                    long count =
                            orders.stream().filter(e -> ("5".equals(e.getOrderStatus().toString()) || "6".equals(e.getOrderStatus().toString()))).count();
                    if (count <= 0) {
                        log.info("---| 开票校验-tids:" + invoice.getTid() + "状态不满足开票要求");
                        sb.append(invoice.getTid()).append("订单状态不满足开票要求").append(",");
                        continue;
                    }
                } else if (InvoiceConst.InvoiceNode.AFTER_VERIFY.equals(strategy.getInvoiceNode())) {
                    // 审核后
                    invalidOrderTids =
                            orders.stream().filter(e -> (!"3".equals(e.getOrderStatus().toString()) && !"4".equals(e.getOrderStatus().toString()) && !"5".equals(e.getOrderStatus().toString()) && !"6".equals(e.getOrderStatus().toString()))).map(OcBOrder::getTid).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(invalidOrderTids)) {
                        log.info("---| 开票校验-tids:" + invalidOrderTids + "状态不满足开票要求");
                        sb.append(invoice.getTid()).append("订单状态不满足开票要求").append(",");
                        continue;
                    }
                }
            }
            canInvoiceList.add(invoice);
        }

        if (CollectionUtils.isEmpty(canInvoiceList)) {
            ValueHolderV14 holder = ValueHolderV14Utils.getFailValueHolder("无符合开票条件订单");
            holder.setData(sb.toString());
            return holder;
        }

        for (AcFOrderInvoice invoice : canInvoiceList) {
            // 组装开票数据
            JsonRootBean invoiceParam = invoiceConvert(invoice);
            if (Objects.isNull(invoiceParam)) {
                continue;
            }
            log.info("---| 开票信息-tid:" + invoice.getTid() + ", 开票提交入参:" + JSON.toJSONString(invoiceParam));
            // 调用开票接口
            ResultType result = hubInvoicingCmd.asynchronousJsonInvoicing(invoiceParam);
            log.info("---| 发票开票接口-tid:" + invoice.getTid() + ", 开票提交出参:" + JSON.toJSONString(result));
            UpdateWrapper<AcFOrderInvoice> updateWrapper = new UpdateWrapper<>();
            LambdaUpdateWrapper<AcFOrderInvoice> update = updateWrapper.lambda();
            update.eq(AcFOrderInvoice::getId, invoice.getId());
            if ("1".equals(result.getCode())
                    || "200".equals(result.getCode())) {
                //  改为异步接口后提交成功更新为开票中
                update.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.IN_INVOICE);
                update.set(AcFOrderInvoice::getFailReason, "");// 将失败原因置空
                logService.addUserOrderLog(invoice.getId(), "开票", "开票提交成功成功", user);
            } else {
                String msg = "1".equals(result.getCode()) ? result.getMsg() : result.getMessage();
                log.info("---| 开票失败, tid:" + invoice.getTid() + ", 失败信息:" + msg);
                // 更新开票失败状态
                String errMsg = StringUtils.isNotBlank(msg) ? msg : "开票提交失败";
                if (errMsg.length() > 200) {
                    errMsg = errMsg.substring(0, 200);
                }
                update.set(AcFOrderInvoice::getInvoiceStatus, InvoiceConst.InvoiceStatus.INVOICE_FAIL);
                update.set(AcFOrderInvoice::getFailReason, errMsg);
                logService.addUserOrderLog(invoice.getId(), "开票", "开票提交失败：" + errMsg, user);
            }

            update.set(AcFOrderInvoice::getModifierid, Long.valueOf(user.getId()));
            update.set(AcFOrderInvoice::getModifierename, user.getEname());
            update.set(AcFOrderInvoice::getModifiername, user.getName());
            update.set(AcFOrderInvoice::getModifieddate, new Date());
            orderInvoiceMapper.update(null, updateWrapper);
        }
        log.info("---| 开票定时任务执行完毕");
        return ValueHolderV14Utils.getSuccessValueHolder("开票成功");
    }

}
