package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.jackrain.nea.ac.service.converter.ApplyInvoiceConvert;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.core.db.Tools;
import com.jackrain.nea.cp.api.CcompanyQueryCmd;
import com.jackrain.nea.cp.result.CpCSupplier;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.hub.api.HubInvoicingCmd;
import com.jackrain.nea.hub.model.HXInvoicingModel.DataType;
import com.jackrain.nea.hub.model.HXInvoicingModel.JsonRootObject;
import com.jackrain.nea.hub.model.HXInvoicingModel.ResultType;
import com.jackrain.nea.oc.oms.mapper.OcBOrderEqualExchangeItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendItemMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnAfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnBfSendMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderMapper;
import com.jackrain.nea.oc.oms.mapper.OcBReturnOrderRefundMapper;
import com.jackrain.nea.oc.oms.mapper.StCInvoiceStrategyMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFInvoiceApplyMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFOrderInvoiceSystemItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFTaxMachineManageMapper;
import com.jackrain.nea.oc.oms.model.constant.InvoiceConst;
import com.jackrain.nea.oc.oms.model.enums.ac.InvoiceHeaderTypeEnum;
import com.jackrain.nea.oc.oms.model.enums.ac.InvoiceTypeEnum;
import com.jackrain.nea.oc.oms.model.table.AcFInvoiceApply;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoice;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceItem;
import com.jackrain.nea.oc.oms.model.table.AcFOrderInvoiceSystemItem;
import com.jackrain.nea.oc.oms.model.table.AcFTaxMachineManage;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderEqualExchangeItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnAfSendItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnBfSend;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.oc.oms.model.table.StCInvoiceStrategy;
import com.jackrain.nea.oc.oms.services.invoice.InvoiceLogService;
import com.jackrain.nea.psext.api.SkuLikeQueryCmd;
import com.jackrain.nea.psext.result.ProExtResult;
import com.jackrain.nea.resource.SystemUserResource;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ComputeEqualExchangeQtyUtil;
import com.jackrain.nea.util.ListSplitUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * @ClassName OrderInvoiceChangeService
 * @Description 订单开票中间表拉取数据转换到订单发票表
 * @Date 2022/8/31 下午4:37
 * @Created by wuhang
 */
@Slf4j
@Component
public class OrderInvoiceChangeTaskService {

    @Autowired
    private AcFInvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    private StCInvoiceStrategyMapper invoiceStrategyMapper;

    @Autowired
    private OcBReturnOrderMapper returnOrderMapper;

    @Autowired
    private OcBOrderMapper orderMapper;

    @Autowired
    private OcBOrderItemMapper orderItemMapper;

    @Autowired
    private AcFOrderInvoiceMapper orderInvoiceMapper;

    @Autowired
    private AcFOrderInvoiceItemMapper orderInvoiceItemMapper;

    @Autowired
    private OcBReturnAfSendMapper returnAfSendMapper;

    @Autowired
    private OcBReturnAfSendItemMapper returnAfSendItemMapper;

    @Autowired
    private OcBReturnOrderRefundMapper returnOrderRefundMapper;

    @Autowired
    private AcFOrderInvoiceSystemItemMapper orderInvoiceSystemItemMapper;

    @Autowired
    private AcFInvoiceApplyItemMapper applyItemMapper;

    @Autowired
    private OcBOrderEqualExchangeItemMapper ocBOrderEqualExchangeItemMapper;

    @Autowired
    private OcBReturnBfSendMapper returnBfSendMapper;

    @Autowired
    private AcFTaxMachineManageMapper acTaxMachineManageMapper;

    @Autowired
    private PsRpcService psRpcService;

    @Autowired
    private CpRpcService cpRpcService;

    @Autowired
    private InvoiceLogService logService;

    @Autowired
    private ApplyInvoiceConvert invoiceConvert;

    @Autowired
    private ApplyInvoiceConvert applyInvoiceConvert;
    @Autowired
    private ThreadPoolTaskExecutor acFInvoiceApplyThreadPoolExecutor;

    @Reference(group = "cp", version = "1.0")
    private CcompanyQueryCmd ccompanyQueryCmd;

    @Reference(group = "ps-ext", version = "1.0")
    private SkuLikeQueryCmd skuLikeQueryCmd;

    @Reference(group = "hub", version = "1.0")
    private HubInvoicingCmd hubInvoicingCmd;

    @NacosValue(value = "${oms.invoice.changeTask.fail.interval.min:60}", autoRefreshed = true)
    public Integer failInterval;

    private static final String QUERY_COMPANY_FAIL = "-1";
    private static final String SUCCESS = "1";
    private static final String FAIL = "2";

//    private static final String NUMBER_LETTER_MATCH = "^[A-Za-z0-9 ]+$";

    /**
     * 基本线程池常量定义
     */
    int corePoolSize = 16;
    int maxPoolSize = 20;
    long keepAliveThreadTime = 60000;
    @NacosValue(value = "${lts.OrderInvoiceChangeTaskService.range:100}", autoRefreshed = true)
    public Integer range;

    private final List<Integer> cancelOrderStatusList = Lists.newArrayList(7, 8);
    private final List<Integer> returnAfSendReturnStatusSuccess = Lists.newArrayList(2, 3);

    public ValueHolderV14 change() {
        ValueHolderV14 resp = new ValueHolderV14();
        executeThread();
        return resp;
    }

    //使用线程
    public void executeThread() {
        String threadPoolName = "R3_OMS_AC_F_INVOICE_APPLY_%d";
        try {
            long start = System.currentTimeMillis();
            final String taskTableName = "ac_f_invoice_apply";

            List<Future<Boolean>> results = new ArrayList<Future<Boolean>>();
            List<AcFInvoiceApply> acFInvoiceApplies = invoiceApplyMapper.selectTaskObjectList(range * 24, taskTableName);
            if (CollectionUtils.isEmpty(acFInvoiceApplies)) {
                return;
            }
            List<List<AcFInvoiceApply>> lists = ListSplitUtil.averageAssign(acFInvoiceApplies, 24);
            for (List<AcFInvoiceApply> data : lists) {
                results.add(acFInvoiceApplyThreadPoolExecutor.submit(new CallableTobeConfirmedTaskWithResult(data)));
            }

            //线程执行结果获取
            for (Future<Boolean> futureResult : results) {
                try {
                    log.debug(LogUtil.format("OrderInvoiceChangeTaskService------>线程结果:{}", "OrderInvoiceChangeTaskService"), futureResult.get().toString());
                } catch (Exception e) {
                    log.error(LogUtil.format("OrderInvoiceChangeTaskService：{}", threadPoolName, "OrderInvoiceChangeTaskService"), Throwables.getStackTraceAsString(e));
                }
            }
            if (log.isDebugEnabled()) {
                log.debug(LogUtil.format("OrderInvoiceChangeTaskService 发票申请表任务完成 useTime:{}", "OrderInvoiceChangeTaskService", (System.currentTimeMillis() - start)));
            }
        } catch (Exception ex) {
            log.error(LogUtil.format("OrderInvoiceChangeTaskService.Execute Error：{}", threadPoolName, "OrderInvoiceChangeTaskService"), Throwables.getStackTraceAsString(ex));
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
    }

    /**
     * 执行
     * @param invoiceList
     * @return
     */
    public ValueHolderV14 executeChange(List<AcFInvoiceApply> invoiceList) {
        ValueHolderV14 resp = new ValueHolderV14();
        // 根据店铺编码查询店铺开票策略
        StCInvoiceStrategy defaultStrategy = null;
        Map<Long, StCInvoiceStrategy> invoiceStrategyMap = new HashMap<>();
        Map<Long, String> shopCompanyIdRelation = new HashMap<>();
        Map<String, CpCSupplier> companyMap = new HashMap<>();
        Map<String, CpShop> shopMap = new HashMap<>();
        User user = SystemUserResource.getRootUser();
        for (AcFInvoiceApply apply : invoiceList) {
            try {
                ValueHolderV14<StCInvoiceStrategy> result = changeApplyInvoice(defaultStrategy, invoiceStrategyMap, shopCompanyIdRelation, companyMap, shopMap, apply, user);
            } catch (Exception e) {
                log.error("change invoice apply error! " + apply.getTid(), e);
            }
        }
        return resp;
    }

    /**
     * 开启线程类
     */
    class CallableTobeConfirmedTaskWithResult implements Callable<Boolean> {
        private final List<AcFInvoiceApply> data;

        public CallableTobeConfirmedTaskWithResult(List<AcFInvoiceApply> data) {
            this.data = data;
        }

        @Override
        public Boolean call() throws Exception {
            long time1 = System.currentTimeMillis();

            if (CollectionUtils.isNotEmpty(data)) {
                executeChange(data);
            }
            return true;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public ValueHolderV14<StCInvoiceStrategy> changeApplyInvoice(StCInvoiceStrategy defaultStrategy, Map<Long, StCInvoiceStrategy> invoiceStrategyMap, Map<Long, String> shopCompanyIdRelation, Map<String, CpCSupplier> companyMap, Map<String, CpShop> shopMap, AcFInvoiceApply apply, User user) {
        ValueHolderV14<StCInvoiceStrategy> resp = new ValueHolderV14<>();
        if (Objects.isNull(apply)) {
            resp.setCode(ResultCode.FAIL);
            resp.setMessage("开票申请记录不存在");
        }
        // 获取店铺所属公司
        CpShop shop = null;
        if (Objects.nonNull(shopMap) && shopMap.containsKey(apply.getCpCShopEcode())) {
            shop = shopMap.get(apply.getCpCShopEcode());
        } else {
            shop = cpRpcService.queryShop(apply.getCpCShopEcode());
            if (!Objects.isNull(shop)){
                if(Objects.isNull(shopMap)){
                    shopMap = new HashMap<>();
                }
                shopMap.put(shop.getEcode(),shop);
            } else {
                OrderMessage message = new OrderMessage();
                message.setMsg("获取店铺失败").setFlag(FAIL).setApplyId(apply.getId());
                message.setTid(apply.getTid());
                recordInvoice(message, apply, user);
                resp.setCode(ResultCode.FAIL);
                resp.setMessage(message.getMessage());
                resp.setData(defaultStrategy);
                return resp;
            }
        }
        // 获取店铺对应公司档案
        CpCSupplier company = null;
        if(Objects.nonNull(shopCompanyIdRelation) && shopCompanyIdRelation.containsKey(apply.getCpCShopId())){
            if(QUERY_COMPANY_FAIL.equals(shopCompanyIdRelation.get(apply.getCpCShopId()))){
                OrderMessage message = new OrderMessage();
                message.setMsg("获取店铺关联公司档案失败").setFlag(FAIL).setApplyId(apply.getId());
                message.setTid(apply.getTid());
                recordInvoice(message,apply, user);
                resp.setCode(ResultCode.FAIL);
                resp.setMessage(message.getMessage());
                resp.setData(defaultStrategy);
                return resp;
            }
            company = companyMap.get(shopCompanyIdRelation.get(apply.getCpCShopId()));
        }else {
            if(StringUtils.isBlank(shop.getCompany())){
                OrderMessage message = new OrderMessage();
                message.setMsg("店铺未维护所属公司").setFlag(FAIL).setApplyId(apply.getId());
                message.setTid(apply.getTid());
                recordInvoice(message,apply, user);
                resp.setCode(ResultCode.FAIL);
                resp.setMessage(message.getMessage());
                resp.setData(defaultStrategy);
                return resp;
            }
            ValueHolderV14<CpCSupplier> companyResp = ccompanyQueryCmd.queryCompany(Long.valueOf(shop.getCompany()));
            log.info("---| shop info:" + JSON.toJSONString(shop) + ", cpmpanyResp:" + JSON.toJSONString(companyResp));
            if(Objects.isNull(shopCompanyIdRelation)){
                shopCompanyIdRelation = new HashMap<>();
            }
            if(Objects.isNull(companyMap)){
                companyMap = new HashMap<>();
            }
            if (companyResp.isOK()) {
                company = companyResp.getData();
                shopCompanyIdRelation.put(apply.getCpCShopId(),company.getEcode());
                companyMap.put(company.getEcode(),company);
            } else {
                shopCompanyIdRelation.put(apply.getCpCShopId(),QUERY_COMPANY_FAIL);
                OrderMessage message = new OrderMessage();
                message.setMsg("获取店铺关联公司档案失败").setFlag(FAIL).setApplyId(apply.getId());
                message.setTid(apply.getTid());
                recordInvoice(message,apply, user);
                resp.setCode(ResultCode.FAIL);
                resp.setMessage(message.getMessage());
                resp.setData(defaultStrategy);
                return resp;
            }
        }
        // 获取公司对应税盘号
        String taxMachineNo = "";
        List<AcFTaxMachineManage> acFTaxMachineManages = acTaxMachineManageMapper.selectList(new LambdaQueryWrapper<AcFTaxMachineManage>()
                .eq(AcFTaxMachineManage::getUserId, company.getId()));
        if(CollectionUtils.isNotEmpty(acFTaxMachineManages)){
            taxMachineNo = acFTaxMachineManages.get(0).getTaxMachine();
        }
        if(StringUtils.isBlank(taxMachineNo)){
            OrderMessage message = new OrderMessage();
            message.setMsg("订单店铺所属公司没有配置税盘号").setFlag(FAIL).setApplyId(apply.getId());
            message.setTid(apply.getTid());
            recordInvoice(message,apply, user);
            resp.setCode(ResultCode.FAIL);
            resp.setMessage(message.getMessage());
            resp.setData(defaultStrategy);
            return resp;
        }
        // 获取对应店铺的开票策略，如果为空则取默认策略
        StCInvoiceStrategy strategy = null;
        if (Objects.nonNull(invoiceStrategyMap) && invoiceStrategyMap.containsKey(apply.getCpCShopId())) {
            strategy = invoiceStrategyMap.get(apply.getCpCShopId());
        } else {
            strategy = invoiceStrategyMapper.queryByShopId(apply.getCpCShopId());
            if(Objects.isNull(strategy)){
                // 获取默认策略
                if(Objects.isNull(defaultStrategy)){
                    strategy = invoiceStrategyMapper.queryByShopNull();
                    defaultStrategy = strategy;
                } else {
                    strategy = defaultStrategy;
                }
            }
        }
        if (Objects.isNull(strategy)) {
            strategy = defaultStrategy;
        } else {
            if(Objects.isNull(invoiceStrategyMap)){
                invoiceStrategyMap = new HashMap<>();
            }
            invoiceStrategyMap.put(apply.getCpCShopId(),strategy);
        }
        // 基础校验
        List<String> tidList = applyItemMapper.selectTidsByApplyId(apply.getId());
        OrderMessage orderMessage = invoiceCheck(apply, strategy, company,tidList);
        if(Objects.nonNull(orderMessage)){
            // 更新订单开票
            recordInvoice(orderMessage,apply, user);
            if(orderMessage.getSuccessFlag().equals(SUCCESS)){
                resp.setCode(ResultCode.SUCCESS);
            } else {
                resp.setCode(ResultCode.FAIL);
            }
            resp.setMessage(orderMessage.getMessage());
            resp.setData(defaultStrategy);
            return resp;
        }

        // 根据平台单号判断是否开过票
        // 根据申请表id查询申请子表tid,拿到tid集合查询发票单管理的订单明细表,根据订单明细表关联的发票单管理表id查询发票单,判断是否有蓝票
        // 根据apply.getId查询tids
        if(CollectionUtils.isEmpty(tidList)){
            OrderMessage message = new OrderMessage();
            message.setMsg("发票申请明细不存在，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
            message.setTid(apply.getTid());
            recordInvoice(message,apply, user);
            resp.setCode(ResultCode.FAIL);
            resp.setMessage(message.getMessage());
            resp.setData(defaultStrategy);
            return resp;
        }
        List<Long> orderInvoiceIdList = orderInvoiceSystemItemMapper.selectByTids(tidList);
        if(CollectionUtils.isNotEmpty(orderInvoiceIdList)){
            // 查询发票单管理表,查看是否有蓝票
            long blueCount = orderInvoiceMapper.countBlueByIds(orderInvoiceIdList);
            if(blueCount > 0){
                OrderMessage message = new OrderMessage();
                message.setMsg("已存在发票,请勿重新申请").setFlag(FAIL).setApplyId(apply.getId());
                message.setTid(apply.getTid());
                recordInvoice(message,apply, user);
                resp.setCode(ResultCode.FAIL);
                resp.setMessage(message.getMessage());
                resp.setData(defaultStrategy);
                return resp;
            }
        }
        log.info("---| tid:[" + apply.getTid() + "]校验完成");
        // 发票申请表转换为订单发票表数据
        OrderMessage orderMessage1 = openBlueInvoice(apply, strategy, InvoiceConst.FreezeStatus.NOT_FREEZE, company, tidList,taxMachineNo,user);
        // 转换完成
        recordInvoice(orderMessage1,apply, user);
        if(orderMessage1.getSuccessFlag().equals(FAIL)){
            resp.setCode(ResultCode.FAIL);
            resp.setMessage(orderMessage1.getMessage());
            resp.setData(defaultStrategy);
            return resp;
        }
        resp.setCode(ResultCode.SUCCESS);
        resp.setMessage("转换成功");
        resp.setData(defaultStrategy);
        return resp;
    }

    /**
     * 记录转换状态
     * @param msg
     */
    private void recordInvoice(OrderMessage msg,AcFInvoiceApply apply, User user){
        AcFInvoiceApply update = new AcFInvoiceApply();
        update.setId(msg.getId());
        update.setTransStatus(msg.getSuccessFlag());
        Date transDate = new Date();
        if(msg.getSuccessFlag().equals(FAIL)){
            update.setFailCount(Objects.isNull(apply.getFailCount()) ? 1 : apply.getFailCount() + 1);
            // 将next_time设置为一小时之后
            Calendar now = Calendar.getInstance();
            now.add(Calendar.MINUTE,failInterval);
            update.setNextTime(now.getTime());
        } else if (msg.getSuccessFlag().equals(SUCCESS)) {
            update.setTransDate(transDate);
        }
        update.setFailReason(msg.getMessage());
        update.setTransId(Long.valueOf(user.getId()));
        update.setModifieddate(transDate);
        update.setModifierename(user.getEname());
        update.setModifierid(Long.valueOf(user.getId()));
        update.setModifiername(user.getName());
        invoiceApplyMapper.updateById(update);
        log.info("---| tid : " + apply.getTid() + ", " + JSON.toJSONString(msg));
    }

    /**
     * 根据蓝票生成一张红票,除了红蓝票不一样，其他都一样
     * @param orderInvoice
     * @return 原蓝票id
     */
    public ValueHolder createRedByBlue(AcFOrderInvoice orderInvoice,User user, String auditStatus) {
        ValueHolder vh = new ValueHolder();
        Long originId = orderInvoice.getId();
        vh.put("originId",originId);
        if(InvoiceConst.InvoiceKind.PAPER.equals(orderInvoice.getInvoiceKind()) && checkTwoDateInOneMonth(orderInvoice.getInvoiceDate(),new Date())){
            // 如果是纸质发票,并且没有跨月, 调用作废接口
            JsonRootObject invalidParam = new JsonRootObject();
            invalidParam.setDocNum(orderInvoice.getBillNo());
            invalidParam.setGroupNum("101");
            ResultType resultType = hubInvoicingCmd.invalidInvoicing(invalidParam);
            if(Objects.isNull(resultType) || !"1".equals(resultType.getCode())){
                log.info("---| tid:["+orderInvoice.getTid()+"]纸质发票作废失败,返回值:"+JSON.toJSONString(resultType));
                vh.put("code",ResultCode.FAIL);
                vh.put("message",Objects.isNull(resultType) ? "发票作废失败" : resultType.getMsg());
                return vh;
            }
            DataType data = resultType.getData();
            if(Objects.isNull(data) || !"1".equals(data.getCode())){
                log.info("---| tid:["+orderInvoice.getTid()+"]纸质发票作废失败,返回值:"+JSON.toJSONString(resultType));
                vh.put("code",ResultCode.FAIL);
                vh.put("message",Objects.isNull(data) ? "发票作废失败" : data.getMsg());
                return vh;
            }
            vh.put("code",ResultCode.SUCCESS);
            vh.put("isDelete","1");// 作废
        } else {
            Long id = Tools.getSequence(InvoiceConst.AC_F_ORDER_INVOICE);
            orderInvoice.setId(id);
            orderInvoice.setTicketType(InvoiceConst.TicketType.RED);// 红票
            orderInvoice.setInvoiceStatus(InvoiceConst.InvoiceStatus.NOT_INVOICE);// 未开票
            orderInvoice.setAuditStatus(auditStatus);// 未审核
            orderInvoice.setBlueTicketId(originId);// 记录蓝票id
            orderInvoice.setInvoiceLinkAddress("");
            orderInvoice.setInvoiceDate(null);
            orderInvoice.setInvoiceNumber("");
            orderInvoice.setInvoiceCode("");
            Date now = new Date();
            orderInvoice.setCreationdate(now);
            orderInvoice.setOwnerename(user.getEname());
            orderInvoice.setOwnerid(Long.valueOf(user.getId()));
            orderInvoice.setOwnername(user.getName());
            orderInvoice.setBillNo(applyInvoiceConvert.getBillNo());// 重新生成单据编号
            orderInvoiceMapper.insert(orderInvoice);
            // 根据原蓝票id查询所有明细
            List<AcFOrderInvoiceItem> itemList = orderInvoiceItemMapper.queryByOrderInvoiceId(originId);
            List<AcFOrderInvoiceItem> updateList = new ArrayList<>();
            for (AcFOrderInvoiceItem item : itemList) {
                Long itemId = Tools.getSequence(InvoiceConst.AC_F_ORDER_INVOICE_ITEM);
                item.setId(itemId);
                item.setAcFOrderInvoiceId(id);
                // 开红票将未税金额,含税金额,数量取相反数
                BigDecimal noTaxAmt = item.getNoTaxAmt();
                item.setNoTaxAmt(noTaxAmt.multiply(new BigDecimal(-1)));
                BigDecimal inclusiveTaxAmt = item.getInclusiveTaxAmt();
                item.setInclusiveTaxAmt(inclusiveTaxAmt.multiply(new BigDecimal(-1)));
                BigDecimal qty = item.getQty();
                item.setQty(qty.multiply(new BigDecimal(-1)));
                BigDecimal invoiceTaxAmt = item.getInvoiceTaxAmt();
                item.setInvoiceTaxAmt(invoiceTaxAmt.multiply(new BigDecimal(-1)));
                updateList.add(item);
            }
            orderInvoiceItemMapper.batchInsert(updateList);
            List<AcFOrderInvoiceSystemItem> systemItemList = orderInvoiceSystemItemMapper.selectByOrderInvoiceId(originId);
            for(AcFOrderInvoiceSystemItem systemItem : systemItemList){
                Long itemId = Tools.getSequence(InvoiceConst.AC_F_ORDER_INVOICE_SYSTEM_ITEM);
                systemItem.setId(itemId);
                systemItem.setAcFOrderInvoiceId(id);
                systemItem.setCreationdate(new Date());
                systemItem.setOwnerid(Long.valueOf(user.getId()));
                systemItem.setOwnerename(user.getEname());
                systemItem.setOwnername(user.getName());
            }
            orderInvoiceSystemItemMapper.batchInsert(systemItemList);
            vh.put("code",ResultCode.SUCCESS);
            vh.put("message","成功");
            vh.put("redTicketId",id);
            vh.put("isDelete","0");// 作废
            logService.addUserOrderLog(id,"创建","创建红冲发票成功",user);
        }
        return vh;
    }

    /**
     * 开票失败记录
     * @param invoiceApply 开票信息
     * @param message 失败信息
     */
    private void changeFailRecord(AcFInvoiceApply invoiceApply,String message) {
        AcFInvoiceApply apply = new AcFInvoiceApply();
        apply.setId(invoiceApply.getId());
        apply.setTransStatus(InvoiceConst.TransStatus.TRANS_FAIL);
        apply.setFailReason(message);
        apply.setFailCount(Objects.isNull(invoiceApply.getFailCount()) ? 0 : invoiceApply.getFailCount() + 1);
        invoiceApplyMapper.updateById(apply);
    }

    private String cancelInvoice(Long id) {
        AcFOrderInvoice update = new AcFOrderInvoice();
        update.setId(id);
        update.setCancelStatus(InvoiceConst.CancelStatus.CANCELED);
        orderInvoiceMapper.updateById(update);
        return "";
    }

    /**
     * 开蓝票
     * @param apply 发票申请数据
     * @param strategy 开票策略
     */
    private OrderMessage openBlueInvoice(AcFInvoiceApply apply, StCInvoiceStrategy strategy,String freezeStatus,CpCSupplier company, List<String> tids,String taxMachineNo, User user) {
        OrderMessage msg = new OrderMessage();
        // 查询订单明细 可用,取消状态=未取消
        LambdaQueryWrapper<OcBOrderItem> query = new LambdaQueryWrapper<>();
        query.in(OcBOrderItem::getTid, tids).eq(OcBOrderItem::getIsactive,"Y");
        query.and(e -> e.isNull(OcBOrderItem::getRefundStatus).or().ne(OcBOrderItem::getRefundStatus,"6"));
//        query.and(e -> e.isNull(OcBOrderItem::getPtReturnStatus).or().ne(OcBOrderItem::getPtReturnStatus,"SUCCESS"));
        List<OcBOrderItem> orderItemAll = orderItemMapper.selectList(query);
        if(CollectionUtils.isEmpty(orderItemAll)){
            msg.setMsg("未找到零售发货单或单据取消&作废，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
            msg.setTid(apply.getTid());
            return msg;
        }
        // 根据订单明细关联零售发货单id查询零售发货单
        List<Long> orderIds = orderItemAll.stream().map(OcBOrderItem::getOcBOrderId).distinct().collect(Collectors.toList());
        // 查询零售发货单时不过滤掉补发/复制的订单
        List<OcBOrder> ocBOrders = orderMapper.queryListByTidsWithOutCopyAndResetShip(orderIds);
        // 过滤掉已取消和已作废的订单
        List<OcBOrder> orders = ocBOrders.stream().filter(e -> !InvoiceConst.cancelOrderStatusList.contains(e.getOrderStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(orders)){
            msg.setMsg("订单状态为作废或取消，转换异常！").setFlag(SUCCESS).setApplyId(apply.getId());
            msg.setTid(apply.getTid());
            return msg;
        }
        BigDecimal allSubsidy = getOrderPtSubsidy(orders);
        // 过滤订单明细
        List<Long> canInvoicesOrderIds = orders.stream().map(OcBOrder::getId).collect(Collectors.toList());
        List<OcBOrderItem> orderItems = orderItemAll.stream().filter(e -> canInvoicesOrderIds.contains(e.getOcBOrderId())).collect(Collectors.toList());
        long noReturnSuccessOrCloseCount = orderItems.stream().filter(e -> StringUtils.isNotBlank(e.getPtReturnStatus()) && (!"SUCCESS".equals(e.getPtReturnStatus()) && !"CLOSED".equals(e.getPtReturnStatus()))).count();
        if(noReturnSuccessOrCloseCount > 0){
            msg.setMsg("存在未完成的退款申请，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
            msg.setTid(apply.getTid());
            return msg;
        }
        // 开票主表
        Long id = Tools.getSequence(InvoiceConst.AC_F_ORDER_INVOICE);
        AcFOrderInvoice orderInvoice = invoiceConvert.applyChange(apply, strategy, orders,id,freezeStatus,company,taxMachineNo,user);
        log.info("---| tid:" + tids + "发票主数据转换完成");
        // 开票子表
        // 根据零售发货单平台单号查询退换货单OC_B_RETURN_ORDER
//        List<Long> returnOrderIds = returnOrderMapper.queryIdsByTids(tids);
        // 根据退换货单查询退换货明细OC_B_RETURN_ORDER_REFUND OC_B_RETURN_ORDER_REFUND.OC_B_RETURN_ORDER_ID
//        List<OcBReturnOrderRefund> returnOrderRefunds = new ArrayList<>();
//        if(CollectionUtils.isNotEmpty(returnOrderIds)) {
//            returnOrderRefunds = returnOrderRefundMapper.selectOcBReturnOrderRefundByOrderIds(returnOrderIds);
//        }
        // 查询退货明细
        List<OcBReturnOrderRefund> returnOrderRefunds = returnOrderRefundMapper.selectByTids(tids);
        if(CollectionUtils.isNotEmpty(returnOrderRefunds)) {
            // 先将退款金额为0的过滤掉，防止在未完成那边校验住
            returnOrderRefunds = returnOrderRefunds.stream().filter(e -> Objects.nonNull(e.getAmtRefund()) && e.getAmtRefund().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(returnOrderRefunds)){
                // 根据明细查询主表数据
                List<Long> returnOrderIds = returnOrderRefunds.stream().map(OcBReturnOrderRefund::getOcBReturnOrderId).collect(Collectors.toList());
                LambdaQueryWrapper<OcBReturnOrder> returnOrderQuery = new LambdaQueryWrapper<>();
                returnOrderQuery.in(OcBReturnOrder::getId,returnOrderIds);
                List<OcBReturnOrder> ocBReturnOrders = returnOrderMapper.selectList(returnOrderQuery);
                // 判断是否存在未完成的退换货单
                long count = ocBReturnOrders.stream().filter(e -> Objects.nonNull(e.getReturnStatus()) && (e.getReturnStatus().compareTo(50) != 0 && e.getReturnStatus().compareTo(60) != 0)).count();
                if(count > 0){
                    msg.setMsg("存在未完成的退换货单，转换失败！").setFlag(FAIL).setApplyId(apply.getId());
                    msg.setTid(apply.getTid());
                    return msg;
                }
                // 将状态=取消(60),或,单据类型=退换货(2)的单子过滤掉,不参与后续的运算
                List<Long> needCompareIds = ocBReturnOrders.stream().filter(e -> (Objects.nonNull(e.getBillType()) && e.getBillType().compareTo(2) == 0) ||
                        (Objects.nonNull(e.getReturnStatus()) && e.getReturnStatus().compareTo(60)==0)).map(OcBReturnOrder::getId).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(needCompareIds)){
                    log.info("---| 过滤的退换货单id:"+needCompareIds);
                    returnOrderRefunds = returnOrderRefunds.stream().filter(e -> !needCompareIds.contains(e.getOcBReturnOrderId())).collect(Collectors.toList());
                }
            }
        }

        // 根据零售发货单查询已发货退货单 仅退款 未取消 OC_B_RETURN_AF_SEND BILL_TYPE=1
        List<OcBReturnAfSend> returnAfSendList = returnAfSendMapper.queryIdsByBillTypeAndTid(tids,1);

        List<OcBReturnAfSendItem> returnafsenditems = new ArrayList<>();
        // 根据已发货退款单查询到明细 oc_b_return_af_send_item   oc_b_return_af_send_id
        if(CollectionUtils.isNotEmpty(returnAfSendList)) {
            // 判断是否存在未打款的，未完成
            long count = returnAfSendList.stream().filter(e -> Objects.nonNull(e.getReturnStatus()) &&
                    !returnAfSendReturnStatusSuccess.contains(e.getReturnStatus())).count();
            if(count > 0){
                msg.setMsg("存在未退款完成的退款单，转换失败！").setFlag(FAIL).setApplyId(apply.getId());
                msg.setTid(apply.getTid());
                return msg;
            }
            List<Long> returnAfSendIds = returnAfSendList.stream().map(OcBReturnAfSend::getId).collect(Collectors.toList());
            returnafsenditems = returnAfSendItemMapper.selectByOcBReturnAfSendIdList(returnAfSendIds);
        }
        // 判断退换货单和已发货退款单是否有数据,如果没有则过滤补发单和复制单
        if(CollectionUtils.isEmpty(returnOrderRefunds) && CollectionUtils.isEmpty(returnAfSendList)){
            // 过滤补发单和复制单
            List<Long> ids = orders.stream().filter(e -> (Objects.isNull(e.getIsCopyOrder()) || e.getIsCopyOrder().compareTo(0) == 0) && (Objects.isNull(e.getIsResetShip()) || e.getIsResetShip().compareTo(0) == 0)).map(OcBOrder::getId).collect(Collectors.toList());
            orderItems = orderItems.stream().filter(e -> ids.contains(e.getOcBOrderId())).collect(Collectors.toList());
        }
        // 先处理对等换货以及退换货单和已发货退款单中的对等换货商品
        // 只要订单明细中存在对等换货的商品,就记录下来,对退换货单和已发货仅退款单都进行还原
        List<OcBOrderItem> ocBOrderItems = equalExchangeReduction(orderItems,returnOrderRefunds,returnafsenditems);
        log.info("---| tid:"+tids+"对等换货后的明细:"+JSON.toJSONString(ocBOrderItems) + ",      退换货单:"+JSON.toJSONString(returnOrderRefunds)+",      发货后仅退款单:"+JSON.toJSONString(returnafsenditems));
        // 将退货退款的和已发货仅退款的过滤掉
        List<OcBOrderItem> ocBOrderItemsFiltered = filterRefund(ocBOrderItems,returnOrderRefunds,returnAfSendList,returnafsenditems);
        if(CollectionUtils.isEmpty(ocBOrderItemsFiltered)){
            msg.setMsg("零售发货单已全部退款，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
            msg.setTid(apply.getTid());
            return msg;
        }
        // 校验发货数量小于等于0
        List<String> qtyZeroProEcode = ocBOrderItemsFiltered.stream().filter(e -> Objects.isNull(e.getQty()) || e.getQty().compareTo(BigDecimal.ZERO) <= 0).map(OcBOrderItem::getPsCProEcode).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(qtyZeroProEcode)){
            msg.setMsg(qtyZeroProEcode + "的开票数量为0，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
            msg.setTid(apply.getTid());
            return msg;
        }
        log.info("---| 过滤后的订单明细:"+JSON.toJSONString(ocBOrderItemsFiltered) + ",      退换货单:"+JSON.toJSONString(returnOrderRefunds)+",      发货后仅退款单:"+JSON.toJSONString(returnafsenditems));

        // 聚合零售发货单明细,退换货明细,已发货仅退款明细 取出需要开票的数据
        List<String> proEcodes = ocBOrderItemsFiltered.stream().filter(e -> e.getProType().compareTo(InvoiceConst.ProType.GROUP_NOT_SPLIT)!=0).map(OcBOrderItem::getPsCProEcode).distinct().collect(Collectors.toList());
        List<ProExtResult> proExtList = skuLikeQueryCmd.queryProExtByEcodes(proEcodes);
        if(CollectionUtils.isEmpty(proExtList)){
            msg.setMsg("商品"+proEcodes+"获取失败，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
            msg.setTid(apply.getTid());
            return msg;
        }
        String isGiftInvoice = strategy.getIsGiftInvoice();
        // 策略是赠品不开票且商品是赠品
        List<String> giftNotInvoice = ocBOrderItemsFiltered.stream().filter(e -> (InvoiceConst.InvoiceGIft.NOT_INVOICE.equals(isGiftInvoice) && InvoiceConst.GIFT.compareTo(e.getIsGift() == null ? 0 : e.getIsGift()) == 0)).map(OcBOrderItem::getPsCProEcode).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(giftNotInvoice)){
            giftNotInvoice = new ArrayList<>();
        }
        List<String> giftNotInvoiceTemp = giftNotInvoice;
        // 过滤掉不开票的赠品,税率,税收分类编码未为空,税收分类编码不足19位的商品,提示错误
        List<String> proEcode = proExtList.stream().filter(e -> !giftNotInvoiceTemp.contains(e.getEcode()) && (!proEcodes.contains(e.getEcode()) || Objects.isNull(e.getTaxRate()) || StringUtils.isBlank(e.getTaxClassification()))).map(ProExtResult::getEcode).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(proEcode)){
            msg.setMsg("商品"+proEcode+"未找到商品编码或税率/税收分类编码异常，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
            msg.setTid(apply.getTid());
            return msg;
        }

        // 将零售发货单的明细,退换货单的明细,已发货仅退款的明细存入system_item中
        Map<Long, String> billNoMap = orders.stream().collect(Collectors.toMap(OcBOrder::getId, OcBOrder::getBillNo));
        Map<String, List<ProExtResult>> proMap = proExtList.stream().collect(Collectors.groupingBy(ProExtResult::getEcode));


        // 判断明细中的税收分类是否都来源601，如果都是601，则正常走下面流程；如果既有601和非601的，则将601的单独拿出来开票
        List<String> classification601Ecodes = proExtList.stream().filter(e -> Objects.nonNull(e.getTaxClassification()) && e.getTaxClassification().equals("601")).map(ProExtResult::getEcode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(classification601Ecodes) && classification601Ecodes.size() < proExtList.size()) {
            Long cpId = Tools.getSequence(InvoiceConst.AC_F_ORDER_INVOICE);
            AcFOrderInvoice cpOrderInvoice = invoiceConvert.applyChange(apply, strategy, orders, cpId, freezeStatus,company, taxMachineNo, user);
            List<OcBOrderItem> cpOrderItems = ocBOrderItemsFiltered.stream().filter(e -> classification601Ecodes.contains(e.getPsCProEcode())).collect(Collectors.toList());
            List<AcFOrderInvoiceSystemItem> cpSystemItemList = saveOrderInvoiceSystemItem(cpId, cpOrderItems, returnOrderRefunds, returnafsenditems, apply.getTid(), billNoMap);
            log.info("---| 601tid:" + tids + "获取明细条数:" + cpSystemItemList.size());

            // 订单明细转换为开票明细
            List<AcFOrderInvoiceItem> cpOrderInvoiceItems = invoiceConvert.applyItemChange(cpSystemItemList, cpId, strategy, proMap, allSubsidy);
            log.info("---| 601tid:" + tids + "发票明细转换完成,开票明细:" + JSON.toJSONString(cpOrderInvoiceItems));
            // 开票含税金额
            BigDecimal inclusiveTaxAmount = sumOfBigdecimalAmount(cpOrderInvoiceItems, null, e -> e.getInclusiveTaxAmt());
            if(inclusiveTaxAmount.compareTo(BigDecimal.ZERO) <= 0){
                msg.setMsg("开票金额为0，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
                msg.setTid(apply.getTid());
                return msg;
            }
            // 开票未税金额
            BigDecimal noTaxAmount = sumOfBigdecimalAmount(cpOrderInvoiceItems, null, e -> e.getNoTaxAmt());
            cpOrderInvoice.setInvoiceNoTaxAmt(noTaxAmount);
            cpOrderInvoice.setInvoiceInclusiveTaxAmt(inclusiveTaxAmount);
            // 税额
            BigDecimal invoiceTaxAmount = sumOfBigdecimalAmount(cpOrderInvoiceItems, null, e -> e.getInvoiceTaxAmt());
            cpOrderInvoice.setInvoiceTaxAmt(invoiceTaxAmount);
            orderInvoiceSystemItemMapper.batchInsert(cpSystemItemList);
            orderInvoiceItemMapper.batchInsert(cpOrderInvoiceItems);
            orderInvoiceMapper.insert(cpOrderInvoice);
            logService.addUserOrderLog(cpId,"创建","创建发票单",user);
            log.info("---| 601tid:" + tids + "蓝票开票完成");
            ocBOrderItemsFiltered.removeAll(cpOrderItems);
        }

        List<AcFOrderInvoiceSystemItem> systemItemList = saveOrderInvoiceSystemItem(id, ocBOrderItemsFiltered, returnOrderRefunds, returnafsenditems, apply.getTid(),billNoMap);
        log.info("---| tid:" + tids + "获取明细条数:" + systemItemList.size());

        // 订单明细转换为开票明细
        List<AcFOrderInvoiceItem> orderInvoiceItems = invoiceConvert.applyItemChange(systemItemList, id, strategy,proMap,allSubsidy);
        log.info("---| tid:" + tids + "发票明细转换完成,开票明细:" + JSON.toJSONString(orderInvoiceItems));
        if (CollectionUtils.isEmpty(orderInvoiceItems)) {
            log.info("开票明细为空，转换成功，tid: {}", tids);
            msg.setMsg("").setFlag(SUCCESS).setApplyId(apply.getId());
            msg.setTid(apply.getTid());
            return msg;
        }
        // 开票含税金额
        BigDecimal inclusiveTaxAmount = sumOfBigdecimalAmount(orderInvoiceItems, null, e -> e.getInclusiveTaxAmt());
        if(inclusiveTaxAmount.compareTo(BigDecimal.ZERO) <= 0){
            msg.setMsg("开票金额为0，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
            msg.setTid(apply.getTid());
            return msg;
        }
        // 开票未税金额
        BigDecimal noTaxAmount = sumOfBigdecimalAmount(orderInvoiceItems, null, e -> e.getNoTaxAmt());
        orderInvoice.setInvoiceNoTaxAmt(noTaxAmount);
        orderInvoice.setInvoiceInclusiveTaxAmt(inclusiveTaxAmount);
        // 税额
        BigDecimal invoiceTaxAmount = sumOfBigdecimalAmount(orderInvoiceItems, null, e -> e.getInvoiceTaxAmt());
        orderInvoice.setInvoiceTaxAmt(invoiceTaxAmount);
        orderInvoiceSystemItemMapper.batchInsert(systemItemList);
        orderInvoiceItemMapper.batchInsert(orderInvoiceItems);
        orderInvoiceMapper.insert(orderInvoice);
        logService.addUserOrderLog(id,"创建","创建发票单",user);
        log.info("---| tid:" + tids + "蓝票开票完成");
        msg.setMsg("").setFlag(SUCCESS).setApplyId(apply.getId());
        msg.setTid(apply.getTid());
        return msg;
    }

    private BigDecimal getOrderPtSubsidy(List<OcBOrder> orders) {
        BigDecimal subsidy = BigDecimal.ZERO;
        if(CollectionUtils.isEmpty(orders)){
            return subsidy;
        }
        for(OcBOrder order : orders){
            if(Objects.isNull(order.getReceivedAmt()) || order.getReceivedAmt().compareTo(BigDecimal.ZERO)==0){
                continue;
            }
            BigDecimal orderAmt = order.getOrderAmt();// 订单金额
            BigDecimal receivedAmt = order.getReceivedAmt();// 实收金额
            try{
                subsidy = subsidy.add(orderAmt.subtract(receivedAmt));
            } catch (Exception e){
                log.error("---| order id:" + order.getId() + "计算平台补贴失败");
                continue;
            }
        }
        return subsidy;
    }

    private List<OcBOrderItem> filterRefund(List<OcBOrderItem> orderItems, List<OcBReturnOrderRefund> returnOrderRefunds, List<OcBReturnAfSend> returnAfSendList,List<OcBReturnAfSendItem> returnafsenditems) {
        if(CollectionUtils.isEmpty(returnOrderRefunds) && CollectionUtils.isEmpty(returnafsenditems) && CollectionUtils.isEmpty(returnAfSendList)){
            return orderItems;
        }
        // 过滤逆向订单，从tid维度来过滤
        Map<String, List<OcBReturnOrderRefund>> returnOrderRefundTidMap = returnOrderRefunds.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getTid));
        Map<String, List<OcBReturnAfSend>> returnAfSendTidMap = returnAfSendList.stream().collect(Collectors.groupingBy(OcBReturnAfSend::getTid));
        Map<String, List<OcBReturnAfSendItem>> returnAfSendItemTidMap = new HashMap<>();
        if(Objects.nonNull(returnAfSendTidMap) && !returnAfSendTidMap.isEmpty()){
            for(String key : returnAfSendTidMap.keySet()){
                List<OcBReturnAfSend> ocBReturnAfSends = returnAfSendTidMap.get(key);
                List<Long> returnAfSendIds = ocBReturnAfSends.stream().map(OcBReturnAfSend::getId).collect(Collectors.toList());
                List<OcBReturnAfSendItem> returnAfsendItemPart = returnafsenditems.stream().filter(e -> returnAfSendIds.contains(e.getOcBReturnAfSendId())).collect(Collectors.toList());
                returnAfSendItemTidMap.put(key,returnAfsendItemPart);
            }
        }
        List<Long> returnOrderAfSendIds = returnafsenditems.stream().map(OcBReturnAfSendItem::getOcBReturnAfSendId).collect(Collectors.toList());
        // 没有明细的仅退款单
        Map<String, List<OcBReturnAfSend>> returnAfSendNotItemMap = returnAfSendList.stream().filter(e -> !returnOrderAfSendIds.contains(e.getId())).collect(Collectors.groupingBy(OcBReturnAfSend::getTid));
        Iterator<OcBOrderItem> orderItemIterator = orderItems.iterator();
        if((Objects.nonNull(returnOrderRefundTidMap) && !returnOrderRefundTidMap.isEmpty()) || (Objects.nonNull(returnAfSendItemTidMap) && !returnAfSendItemTidMap.isEmpty())) {
            List<Long> removedItemIds = new ArrayList<>();
            List<Long> removedReturnRefundIds = new ArrayList<>();
            List<Long> removedReturnAfSendIDs = new ArrayList<>();
            while (orderItemIterator.hasNext()) {
                OcBOrderItem item = orderItemIterator.next();
                // 退换货单
                if (Objects.nonNull(returnOrderRefundTidMap) && returnOrderRefundTidMap.containsKey(item.getTid())) {
                    List<OcBReturnOrderRefund> returnOrderRefundPart = returnOrderRefundTidMap.get(item.getTid());
                    Iterator<OcBReturnOrderRefund> partIterator = returnOrderRefundPart.iterator();
                    while (partIterator.hasNext()){
                        OcBReturnOrderRefund refundItem = partIterator.next();
                        if(removedItemIds.contains(item.getId()) || removedReturnRefundIds.contains(refundItem.getId())){
                            log.info("---| item id :[" + item.getId() + "] is removed! return order refund");
                            continue;
                        }
                        log.info("---| tid:"+item.getTid()+"退换货单信息:"+JSON.toJSONString(refundItem));
                        if (item.getPsCSkuId().equals(refundItem.getPsCSkuId())) {
                            if (item.getRealAmt().subtract(refundItem.getAmtRefund()).compareTo(BigDecimal.ZERO) > 0) {
                                item.setRealAmt(item.getRealAmt().subtract(refundItem.getAmtRefund()));
//                                item.setQty(item.getQty().subtract(refundItem.getQtyIn()));
                                removedReturnRefundIds.add(refundItem.getId());
                                partIterator.remove();
                            } else if (item.getRealAmt().subtract(refundItem.getAmtRefund()).compareTo(BigDecimal.ZERO) == 0) {
                                removedItemIds.add(item.getId());
                                orderItemIterator.remove();
                                removedReturnRefundIds.add(refundItem.getId());
                                partIterator.remove();
                                continue;
                            } else {
                                refundItem.setAmtRefund(refundItem.getAmtRefund().subtract(item.getRealAmt()));
                                refundItem.setQtyRefund(refundItem.getQtyIn().subtract(item.getQty()));
                                removedItemIds.add(item.getId());
                                orderItemIterator.remove();
                                continue;
                            }
                        }
                    }
                    returnOrderRefundTidMap.put(item.getTid(),returnOrderRefundPart);
                }
                if (Objects.nonNull(returnAfSendItemTidMap) && returnAfSendItemTidMap.containsKey(item.getTid())) {
                    List<OcBReturnAfSendItem> returnAfSendItemPart = returnAfSendItemTidMap.get(item.getTid());
                    Iterator<OcBReturnAfSendItem> partIterator = returnAfSendItemPart.iterator();
                    while (partIterator.hasNext()){
                        OcBReturnAfSendItem returnItem = partIterator.next();
                        if(removedItemIds.contains(item.getId()) || removedReturnAfSendIDs.contains(returnItem.getId())){
                            log.info("---| item id :[" + item.getId() + "] is removed! return after send");
                            continue;
                        }
                        // 如果已发货仅退款明细有qty_in则取qty_in,如果为空则取qty_return_apply, 若依然为空则赋默认值0
                        BigDecimal returnQty = Objects.nonNull(returnItem.getQtyIn()) ? returnItem.getQtyIn() : returnItem.getQtyReturnApply();
                        if(Objects.isNull(returnQty)){
                            returnQty = new BigDecimal("0.0");
                        }
                        log.info("---| tid:"+item.getTid()+"已发货仅退款单信息:"+JSON.toJSONString(returnItem));
                        if (item.getPsCSkuId().equals(returnItem.getPsCSkuId())) {
                            if (item.getRealAmt().subtract(returnItem.getAmtReturn()).compareTo(BigDecimal.ZERO) > 0) {
                                item.setRealAmt(item.getRealAmt().subtract(returnItem.getAmtReturn()));
//                                item.setQty(item.getQty().subtract(returnQty));
                                removedReturnAfSendIDs.add(returnItem.getId());
                                partIterator.remove();
                            } else if (item.getRealAmt().subtract(returnItem.getAmtReturn()).compareTo(BigDecimal.ZERO) == 0) {
                                removedItemIds.add(item.getId());
                                orderItemIterator.remove();
                                removedReturnAfSendIDs.add(returnItem.getId());
                                partIterator.remove();
                                continue;
                            } else {
                                returnItem.setAmtReturn(returnItem.getAmtReturn().subtract(item.getRealAmt()));
                                returnItem.setQtyReturnApply(returnQty.subtract(item.getQty()));
                                removedItemIds.add(item.getId());
                                orderItemIterator.remove();
                                continue;
                            }
                        }
                    }
                    returnAfSendItemTidMap.put(item.getTid(),returnAfSendItemPart);
                }
            }
        }
        if(Objects.isNull(returnAfSendNotItemMap) || returnAfSendNotItemMap.isEmpty()){
            return orderItems;
        }
        // 分摊,没有明细的仅退款单
        List<OcBOrderItem> newList = new ArrayList<>();
        List<OcBOrderItem> orderItemsOthers = orderItems.stream().filter(e -> !returnAfSendNotItemMap.containsKey(e.getTid())).collect(Collectors.toList());
        Map<String, List<OcBOrderItem>> orderItemByTidMap = orderItems.stream().collect(Collectors.groupingBy(OcBOrderItem::getTid));
        for(String tid : returnAfSendNotItemMap.keySet()){
            List<OcBReturnAfSend> ocBReturnAfSends = returnAfSendNotItemMap.get(tid);
            BigDecimal refundAmtAll = ocBReturnAfSends.stream().map(OcBReturnAfSend::getAmtReturnActual).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
            // 分摊
            List<OcBOrderItem> orderItemPart = orderItemByTidMap.get(tid);
            if(refundAmtAll.compareTo(BigDecimal.ZERO) <= 0){
                // 退0元
                newList.addAll(orderItemPart);
                continue;
            }
            BigDecimal realAmtAll = orderItemPart.stream().map(OcBOrderItem::getRealAmt).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
            BigDecimal subtractAmt = BigDecimal.ZERO; // 已扣减的金额
            for(int i = 0; i < orderItemPart.size(); i++){
                OcBOrderItem item = orderItemPart.get(i);
                BigDecimal proportion = item.getRealAmt().divide(realAmtAll, 6, BigDecimal.ROUND_HALF_UP);// 比例
                if(i == orderItemPart.size() - 1){
                    item.setRealAmt(item.getRealAmt().subtract(subtractAmt));
                } else {
                    BigDecimal refund = refundAmtAll.multiply(proportion);
                    item.setRealAmt(item.getRealAmt().subtract(refund));
                    subtractAmt = subtractAmt.add(refund);
                }
                newList.add(item);
            }
        }
        newList.addAll(orderItemsOthers);
        return newList;
    }

    /**
     * 开票校验
     * @param apply 开票申请
     * @param strategy 开票策略
     * @param company 开票公司
     * @return
     */
    private OrderMessage invoiceCheck(AcFInvoiceApply apply, StCInvoiceStrategy strategy, CpCSupplier company,List<String> tidList) {
        OrderMessage msg = new OrderMessage();
        msg.setTid(apply.getTid());
        // 不允许开具电子专票
        if(InvoiceConst.InvoiceKind.ELECTRONIC.equals(apply.getInvoiceKind()) && InvoiceConst.InvoiceType.SPECIAL.equals(apply.getInvoiceType())){
            return msg.setMsg("无法开具电子专票，转换失败！").setFlag(FAIL).setApplyId(apply.getId());
        }
        if(InvoiceConst.InvoiceKind.PAPER.equals(apply.getInvoiceKind()) && InvoiceConst.InvoiceType.NORMAL.equals(apply.getInvoiceType())){
            return msg.setMsg("无法开具纸质普票，转换失败！").setFlag(FAIL).setApplyId(apply.getId());
        }
        if(StringUtils.isBlank(apply.getInvoiceHeader())){
            if(InvoiceConst.EmptyHeaderPersonInvoice.INVOICE.equals(strategy.getIsPersonInvoice())){
                apply.setInvoiceHeader("个人");
                apply.setHeaderType(InvoiceConst.HeaderType.INDIVIDUAL);
                apply.setInvoiceType(InvoiceConst.InvoiceType.NORMAL);
            } else {
                return msg.setMsg("发票抬头不能为空，转换失败！").setFlag(FAIL).setApplyId(apply.getId());
            }
        }
        if(InvoiceHeaderTypeEnum.COMPANY.getCode().equals(apply.getHeaderType())){
            // 开票抬头为企业,校验字段
//            if(StringUtils.isBlank(apply.getTaxpayerNo())){
//                return msg.setMsg("纳税人识别号异常，转换失败！").setFlag(FAIL).setApplyId(apply.getId());
//            }
//            if(!apply.getTaxpayerNo().matches(NUMBER_LETTER_MATCH)){
//                return msg.setMsg("纳税人识别号不符合要求, 转换失败! ").setFlag(FAIL).setApplyId(apply.getId());
//            }
            if(InvoiceTypeEnum.SPECIAL.getCode().equals(apply.getInvoiceType())
                    && (StringUtils.isBlank(apply.getOpeningBank())
                    || StringUtils.isBlank(apply.getBankAccount())
                    || StringUtils.isBlank(apply.getInvoiceAddress()))){
                // 开专票,校验必填字段
                return msg.setMsg("专票开票信息不完整，转换失败！").setFlag(FAIL).setApplyId(apply.getId());
            }
        }
        if(InvoiceConst.NOT_ALLOW.equals(strategy.getIsInvoice())){
            return msg.setMsg("该店铺不允许OMS系统开票，请联系财务人员！").setFlag(FAIL).setApplyId(apply.getId());
        }
        // 判断平台单号在oms中是否存在非作废/非取消订单,存在继续,不存在返回
//        long orderCount = orderMapper.countByTidAndStatus(tidList);
//        if(orderCount == 0){
//            return msg.setMsg("订单不存在或不可用，转换完成！").setFlag(SUCCESS).setApplyId(apply.getId());
//        }
        // 判断平台单号在oms中是否存在未入库的退换货单
        long returnCount = returnOrderMapper.countByReturnStatus(20,tidList);
        if(returnCount > 0){
            return msg.setMsg("存在未完成的退换货单，转换失败！").setFlag(FAIL).setApplyId(apply.getId());
        }
        // 查询未发货退款单是否存在未完成的申请
        List<OcBReturnBfSend> returnBfSendList = returnBfSendMapper.selectInTid(tidList);
        long count = returnBfSendList.stream().filter(e -> !"CLOSED".equals(e.getTReturnStatus()) && !"SUCCESS".equals(e.getTReturnStatus())).count();
        if(count > 0){
            return msg.setMsg("订单发货前退款单存在未完成申请，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
        }
        // 判断店铺关联公司开票信息是否完成
        if(Objects.isNull(company) || StringUtils.isBlank(company.getPayee()) ||
                StringUtils.isBlank(company.getDrawer()) || StringUtils.isBlank(company.getReviewer()) ||
                StringUtils.isBlank(company.getAllname()) || StringUtils.isBlank(company.getBillaccount()) ||
                StringUtils.isBlank(company.getBillbank()) || StringUtils.isBlank(company.getSocialCreditCode()) ||
                StringUtils.isBlank(company.getAddress()) || StringUtils.isBlank(company.getPhone())){
            return msg.setMsg("店铺关联的开票公司信息不完整，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
        }
        if(Objects.nonNull(apply.getRedTicketId())){
            // 查询红票是否开票完成
            LambdaQueryWrapper<AcFOrderInvoice> query = new LambdaQueryWrapper<>();
            query.eq(AcFOrderInvoice::getId,apply.getRedTicketId()).eq(AcFOrderInvoice::getIsactive,"Y");
            AcFOrderInvoice orderInvoice = orderInvoiceMapper.selectOne(query);
            if(Objects.nonNull(orderInvoice) && !InvoiceConst.InvoiceStatus.INVOICE_SUCCESS.equals(orderInvoice.getInvoiceStatus())){
                return msg.setMsg("关联红冲发票未开票成功，转换异常！").setFlag(FAIL).setApplyId(apply.getId());
            }
        }
        return null;
    }

    public static BigDecimal sumOfBigdecimalAmount(List<AcFOrderInvoiceItem> invoiceItemList, Predicate<AcFOrderInvoiceItem> predicate, Function<AcFOrderInvoiceItem, BigDecimal> func) {
        BigDecimal amount = new BigDecimal("0.0");
        if (null == predicate) {
            amount = invoiceItemList.stream().map(func).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
        } else {
            amount = invoiceItemList.stream().filter(predicate).map(func).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
        }
        return amount;
    }

    @Data
    class OrderMessage{

        Long id;
        // 平台单号
        String tid;
        // 1成功,2失败
        String successFlag;
        // 成功/失败消息
        String message;

        public OrderMessage setMsg(String msg){
            this.message = msg;
            return this;
        }

        public OrderMessage setFlag(String flag){
            this.successFlag = flag;
            return this;
        }

        public OrderMessage setApplyId(Long id){
            this.id = id;
            return this;
        }
    }

    private List<String> getTids(AcFInvoiceApply apply){
        List<String> tids = new ArrayList<>();
        if(InvoiceConst.MergeInvoiceFlag.MERGE.equals(apply.getMergeInvoiceFlag())){
            tids = Arrays.asList(apply.getTid().split(","));
        } else {
            tids.add(apply.getTid());
        }
        return tids;
    }

    /**
     * 记录ac_f_order_invoice_system_item
     * @param id 订单开票表id
     * @param orderItems 零售发货单明细
     * @param returnOrderRefunds 退换货单明细
     * @param returnafsenditems 已发货退款单明细
     */
    private List<AcFOrderInvoiceSystemItem> saveOrderInvoiceSystemItem(Long id, List<OcBOrderItem> orderItems, List<OcBReturnOrderRefund> returnOrderRefunds,
                                                                       List<OcBReturnAfSendItem> returnafsenditems,String tid,Map<Long, String> billNoMap) {
        Map<Long, List<OcBReturnOrderRefund>> returnOrderRefundMap = returnOrderRefunds.stream().collect(Collectors.groupingBy(OcBReturnOrderRefund::getPsCSkuId));
        Map<Long, List<OcBReturnAfSendItem>> returnafsenditemMap = returnafsenditems.stream().collect(Collectors.groupingBy(OcBReturnAfSendItem::getPsCSkuId));
        List<AcFOrderInvoiceSystemItem> systemItemList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(orderItems)){
            for(OcBOrderItem item : orderItems){
                // 过滤组合商品
                if(item.getProType().compareTo(InvoiceConst.ProType.GROUP_NOT_SPLIT)==0){
                    continue;
                }
                AcFOrderInvoiceSystemItem systemItem = new AcFOrderInvoiceSystemItem();
                Long systemItemId = Tools.getSequence(InvoiceConst.AC_F_ORDER_INVOICE_SYSTEM_ITEM);
                systemItem.setOcBOrderId(item.getOcBOrderId());
                systemItem.setOrderBillNo(billNoMap.get(item.getOcBOrderId()));
                systemItem.setId(systemItemId);
                systemItem.setAcFOrderInvoiceId(id);
                systemItem.setTid(item.getTid());
                systemItem.setPsCSkuId(item.getPsCSkuId());
                systemItem.setPsCSkuEcode(item.getPsCSkuEcode());
                systemItem.setPsCProId(item.getPsCProId());
                systemItem.setPsCProEcode(item.getPsCProEcode());
                systemItem.setPsCProEname(item.getPsCProEname());
                systemItem.setIsReturnGoods(InvoiceConst.ReturnGoodsFlag.NO);
                systemItem.setPriceActual(item.getPriceActual());
                systemItem.setPriceAmt(item.getRealAmt());
                systemItem.setQty(item.getQty());
                systemItem.setIsGift(item.getIsGift());
                systemItem.setPriceReturnAmt(BigDecimal.ZERO);
                systemItem.setQtyReturn(BigDecimal.ZERO);
                systemItem.setShipPrice(BigDecimal.ZERO);
                systemItem.setAdClientId(37L);
                systemItem.setAdOrgId(27L);
                if (Objects.nonNull(returnOrderRefundMap) && returnOrderRefundMap.containsKey(item.getPsCSkuId())) {
                    systemItem.setIsReturnGoods(InvoiceConst.ReturnGoodsFlag.YES);
                    List<OcBReturnOrderRefund> returnOrderRefundSubList = returnOrderRefundMap.get(item.getPsCSkuId());
                    // 退货数量
                    BigDecimal returnQty = returnOrderRefundSubList.stream().map(OcBReturnOrderRefund::getQtyRefund).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
                    systemItem.setQtyReturn(returnQty);
                    // 退款金额
                    BigDecimal amtRefund = returnOrderRefundSubList.stream().map(OcBReturnOrderRefund::getAmtRefund).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
                    systemItem.setPriceReturnAmt(amtRefund);
                }
                if(Objects.nonNull(returnafsenditemMap) && returnafsenditemMap.containsKey(item.getPsCSkuId())){
                    systemItem.setIsReturnGoods(InvoiceConst.ReturnGoodsFlag.YES);
                    List<OcBReturnAfSendItem> ocBReturnAfSendItemSubList = returnafsenditemMap.get(item.getPsCSkuId());
                    // 退货数量
                    BigDecimal returnQty = ocBReturnAfSendItemSubList.stream().filter(p -> Objects.nonNull(p.getQtyReturnApply()))
                            .map(OcBReturnAfSendItem::getQtyReturnApply).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
                    systemItem.setQtyReturn(systemItem.getQtyReturn().add(returnQty));
                    // 退款金额
                    BigDecimal amtReturn = ocBReturnAfSendItemSubList.stream().map(OcBReturnAfSendItem::getAmtReturn).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
                    systemItem.setPriceReturnAmt(systemItem.getPriceReturnAmt().add(amtReturn));
                    // 运费
                    BigDecimal freight = ocBReturnAfSendItemSubList.stream().map(OcBReturnAfSendItem::getFreight).reduce(new BigDecimal("0.0"), (k, j) -> k.add(j));
                    systemItem.setShipPrice(freight);
                }
                systemItemList.add(systemItem);
            }
            if(CollectionUtils.isEmpty(systemItemList)){
                return systemItemList;
            }
        }
        return systemItemList;
    }

    public boolean checkTwoDateInOneMonth(Date date1, Date date2){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        return format.format(date1).equals(format.format(date2));
    }

    /**
     * 处理对对等换货,还原
     * @param ocBOrderItemList
     * @return
     */
    private List<OcBOrderItem> equalExchangeReduction(List<OcBOrderItem> ocBOrderItemList,List<OcBReturnOrderRefund> returnOrderRefunds,List<OcBReturnAfSendItem> returnafsenditems){
        List<OcBOrderItem> itemList = ocBOrderItemList.stream().filter(p -> StringUtils.isNotBlank(p.getEqualExchangeMark()) && Objects.nonNull(p.getIsEqualExchange()) && p.getIsEqualExchange().compareTo(1) == 0).collect(Collectors.toList());
        List<OcBOrderItem> orderItems = new ArrayList<>();
        Map<String, OcBOrderEqualExchangeItem> equalExchangeRelation = new HashMap<>();// 对等换货前后的商品sku
        Map<String,BigDecimal> equalExchangeProportion = new HashMap<>();// 换货商品比例
        if(CollectionUtils.isEmpty(itemList)){
            return ocBOrderItemList;
        } else {
            Map<Long, List<OcBOrderItem>> itemByIdMap = itemList.stream().collect(Collectors.groupingBy(OcBOrderItem::getOcBOrderId));
            for(Long orderId : itemByIdMap.keySet()) {
                List<OcBOrderItem> orderPartList = itemByIdMap.get(orderId);
                //查询对等换货的数据
                List<OcBOrderEqualExchangeItem> exchangeItems = ocBOrderEqualExchangeItemMapper.selectOcBOrderEqualExchangeItemListByOrderId(orderId);
                Map<String, OcBOrderEqualExchangeItem> orderItemMap = exchangeItems.stream().collect(Collectors.toMap(OcBOrderEqualExchangeItem::getEqualExchangeMark, Function.identity(), (key1, key2) -> key2));
                Map<String, List<OcBOrderItem>> itemMap = orderPartList.stream().collect(Collectors.groupingBy(OcBOrderItem::getEqualExchangeMark));
                Set<String> skuSet = new HashSet<>();
                for (String mark : itemMap.keySet()) {
                    List<OcBOrderItem> ocBOrderItems = itemMap.get(mark);
                    //计算原数量是多少
                    BigDecimal qtyCount = BigDecimal.ZERO;
                    for (OcBOrderItem ocBOrderItem : ocBOrderItems) {
                        BigDecimal qty = ComputeEqualExchangeQtyUtil.computeQty(ocBOrderItem);
                        qtyCount = qtyCount.add(qty);
                        skuSet.add(ocBOrderItem.getPsCSkuEcode());
                    }
                    OcBOrderEqualExchangeItem equalExchangeItem = orderItemMap.get(mark);
                    if (equalExchangeItem == null) {
                        return ocBOrderItemList;
                    }
                    OcBOrderItem afterExchangeItem = ocBOrderItems.get(0);// 对等换货后的明细
                    equalExchangeRelation.put(afterExchangeItem.getPsCSkuEcode(),equalExchangeItem);
                    String equalExchangeRatio = afterExchangeItem.getEqualExchangeRatio();
                    if (StringUtils.isNotEmpty(equalExchangeRatio)){
                        String[] qtyArray = equalExchangeRatio.split(":");
                        BigDecimal a = new BigDecimal(qtyArray[0]);
                        BigDecimal b = new BigDecimal(qtyArray[1]);
                        //对等换货只能换1  所以肯定能除尽
                        BigDecimal divide = a.divide(b, 4, BigDecimal.ROUND_HALF_UP);
                        equalExchangeProportion.put(afterExchangeItem.getPsCSkuEcode(),divide);
                    }

                    OcBOrderItem item = new OcBOrderItem();
                    BeanUtils.copyProperties(equalExchangeItem, item);
                    item.setQty(qtyCount);
                    // 对等换货明细的成交单价如果为空，需要手动计算成交单价，用对等换货明细里的 成交金额/数量
                    BigDecimal priceActual = equalExchangeItem.getPriceActual();
                    if(Objects.isNull(priceActual)){
                        priceActual = equalExchangeItem.getRealAmt().divide(equalExchangeItem.getQty(),2,BigDecimal.ROUND_HALF_UP);
                    }
                    item.setRealAmt(qtyCount.multiply(priceActual));
                    item.setOcBOrderId(orderId);
                    item.setIsExchangeItem(0);
                    item.setEqualExchangeRatio("");
                    item.setEqualExchangeMark("");
                    item.setProType(ocBOrderItems.get(0).getProType());
                    orderItems.add(item);
                }
            }
            List<OcBOrderItem> others = ocBOrderItemList.stream().filter(p -> (Objects.isNull(p.getIsEqualExchange()) || p.getIsEqualExchange().compareTo(1)!=0)).collect(Collectors.toList());
            orderItems.addAll(others);
        }
        if(Objects.nonNull(equalExchangeRelation) && !equalExchangeRelation.isEmpty() &&
                Objects.nonNull(equalExchangeProportion) && !equalExchangeProportion.isEmpty()){
            if(CollectionUtils.isNotEmpty(returnOrderRefunds)){
                // 处理退换货单明细的对等换货
                List<OcBReturnOrderRefund> orderRefundList = new ArrayList<>();
                for(OcBReturnOrderRefund r : returnOrderRefunds){
                    if(equalExchangeRelation.containsKey(r.getPsCSkuEcode()) && equalExchangeProportion.containsKey(r.getPsCSkuEcode())){
                        OcBOrderEqualExchangeItem ocBOrderEqualExchangeItem = equalExchangeRelation.get(r.getPsCSkuEcode());
                        BigDecimal proportion = equalExchangeProportion.get(r.getPsCSkuEcode());
                        r.setPsCSkuId(ocBOrderEqualExchangeItem.getPsCSkuId());
                        r.setPsCSkuEcode(ocBOrderEqualExchangeItem.getPsCSkuEcode());
                        r.setPsCSkuEname(ocBOrderEqualExchangeItem.getPsCSkuEname());
                        r.setPsCProId(ocBOrderEqualExchangeItem.getPsCProId());
                        r.setPsCProEcode(ocBOrderEqualExchangeItem.getPsCProEcode());
                        r.setPsCProEname(ocBOrderEqualExchangeItem.getPsCProEname());
                        r.setQtyIn(r.getQtyIn().multiply(proportion));
                        r.setPrice(ocBOrderEqualExchangeItem.getPriceActual());
                        orderRefundList.add(r);
                    } else {
                        orderRefundList.add(r);
                    }
                }
                returnOrderRefunds = orderRefundList;
            }
            if(CollectionUtils.isNotEmpty(returnafsenditems)){
                // 处理发货后退款单明细的对等换货
                List<OcBReturnAfSendItem> afSendItemList = new ArrayList<>();
                for(OcBReturnAfSendItem s : returnafsenditems){
                    if(equalExchangeRelation.containsKey(s.getPsCSkuEcode())){
                        OcBOrderEqualExchangeItem ocBOrderEqualExchangeItem = equalExchangeRelation.get(s.getPsCSkuEcode());
                        BigDecimal proportion = equalExchangeProportion.get(s.getPsCSkuEcode());
                        s.setPsCSkuId(ocBOrderEqualExchangeItem.getPsCSkuId());
                        s.setPsCSkuEcode(ocBOrderEqualExchangeItem.getPsCSkuEcode());
                        s.setPsCSkuEname(ocBOrderEqualExchangeItem.getPsCSkuEname());
                        s.setPsCProId(ocBOrderEqualExchangeItem.getPsCProId());
                        s.setPsCProEcode(ocBOrderEqualExchangeItem.getPsCProEcode());
                        s.setPsCProEname(ocBOrderEqualExchangeItem.getPsCProEname());
                        if (Objects.isNull(s.getQtyIn())) {
                            s.setQtyIn(BigDecimal.valueOf(1).multiply(proportion));
                        } else {
                            s.setQtyIn(s.getQtyIn().multiply(proportion));
                        }
                        afSendItemList.add(s);
                    } else {
                        afSendItemList.add(s);
                    }
                }
                returnafsenditems = afSendItemList;
            }
        }
        return orderItems;
    }
}

