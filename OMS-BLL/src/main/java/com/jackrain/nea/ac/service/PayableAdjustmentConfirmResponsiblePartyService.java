package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.refund.util.AssertUtil;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.AcBeanUtils;
import com.jackrain.nea.util.AcPayableAdjustmentPushESUtil;
import com.jackrain.nea.util.BaseModelUtil;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Component
@Slf4j
public class PayableAdjustmentConfirmResponsiblePartyService extends CommandAdapter {
    @Resource
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;


    @Resource
    private PayableAdjustmentSaveService payableAdjustmentSaveService;


    private void checkStatus(AcFPayableAdjustmentDO acFPayableAdjustmentDO) {
        Integer iStatus = acFPayableAdjustmentDO.getBillStatus();

        AssertUtil.assertException(iStatus == AcConstant.CON_BILL_STATUS_01,"当前记录未审核，不允许修改责任方!");
        AssertUtil.assertException(iStatus == AcConstant.CON_BILL_STATUS_04,"当前记录已财审，不允许修改责任方!");
        AssertUtil.assertException(iStatus == AcConstant.CON_BILL_STATUS_05,"当前记录已作废，不允许修改责任方!");
        //业务未审核且赔付金额大于原单金额
        boolean flag = !(iStatus == AcConstant.CON_BILL_STATUS_03) && acFPayableAdjustmentDO.getPayablePrice().compareTo(acFPayableAdjustmentDO.getOriginOrderAmt()) > 0;
        AssertUtil.assertException(flag,"当前记录赔偿款大于原订单金额，需要业务审核，不允许修改责任方!");
    }

    /**
     * 确认责任方
     *
     * @param querySession 参数封装
     * @return 返回状态
     * @throws NDSException 异常信息
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        Integer responsibleParty = param.getInteger("RESPONSIBLE_PARTY");

        if (param != null) {
            JSONArray itemArray = AcBeanUtils.makeAuditJsonArray(param);
            JSONArray errorArray = new JSONArray();
            if (itemArray.size() > 0) {
                for (int i = 0; i < itemArray.size(); i++) {
                    Long id = itemArray.getLong(i);
                    try {
                        updateResponsibleParty(id,responsibleParty, querySession);
                    } catch (Exception e) {
                        JSONObject errJo = new JSONObject();
                        errJo.put("objid", id);
                        errJo.put("message", e.getMessage());
                        errorArray.add(errJo);
                    }
                }
            } else {
                throw new NDSException("未找到需要确认责任方的记录！");
            }
            valueHolder = AcBeanUtils.getProcessValueHolder(itemArray, errorArray, "确认责任方");
        } else {
            throw new NDSException("参数为空！");
        }
        return valueHolder;
    }

    @Transactional(rollbackFor = {Exception.class})
    public void updateResponsibleParty(Long objId, int responsibleParty, QuerySession querySession) {
        AcFPayableAdjustmentDO acFPayableAdjustmentDO = acFPayableAdjustmentMapper.selectById(objId);
        if (acFPayableAdjustmentDO != null) {
            checkStatus(acFPayableAdjustmentDO);
            AcFPayableAdjustmentDO update =new AcFPayableAdjustmentDO();
            update.setId(objId);
            update.setResponsibleParty(responsibleParty);
            BaseModelUtil.makeBaseModifyField(update,querySession.getUser());

            int iResult = acFPayableAdjustmentMapper.updateById(update);
            if (iResult < 0) {
                throw new NDSException("单据编码:" + acFPayableAdjustmentDO.getBillNo() + ",确认责任方失败！");
            }
            //记录日志
            payableAdjustmentSaveService.insertLogFun(querySession.getUser(), objId,
                    OperatorLogTypeEnum.CONFIRM_RESPONSIBLE_PARTY.getVal(),
                    OperatorLogTypeEnum.CONFIRM_RESPONSIBLE_PARTY.getText(), new Date());
            //推送ES
            AcPayableAdjustmentPushESUtil.pushOrder(objId);
        } else {
            throw new NDSException("当前记录已不存在！");
        }
    }

}
