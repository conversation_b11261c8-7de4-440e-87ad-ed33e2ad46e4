package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.oc.oms.model.enums.ac.OperatorLogTypeEnum;
import com.jackrain.nea.constants.ResultCode;
import com.jackrain.nea.cpext.model.table.CpCPhyWarehouse;
import com.jackrain.nea.cpext.model.table.CpLogistics;
import com.jackrain.nea.cpext.model.table.CpShop;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.ext.sequence.util.SequenceGenUtil;
import com.jackrain.nea.model.util.ModelUtil;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.model.enums.ac.PayBillTypeEnum;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrder;
import com.jackrain.nea.oc.oms.model.table.OcBReturnOrderRefund;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.CompensateQueryRequest;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.AcBeanUtils;
import com.jackrain.nea.util.AcPayableAdjustmentPushESUtil;
import com.jackrain.nea.util.AmountCalcUtils;
import com.jackrain.nea.util.ApplicationContextHandle;
import com.jackrain.nea.util.JsonUtils;
import com.jackrain.nea.utility.LogUtil;
import com.jackrain.nea.web.face.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: 陈俊明
 * @since: 2019-04-30
 * @create at : 2019-04-30 9:17
 */

@Component
@Slf4j
public class PayableAdjustDropCopyService {
    @Resource
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Resource
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    @Resource
    private PayableAdjustmentSaveService payableAdjustmentSaveService;

    @Resource
    private StRpcService payableRpcService;
    @Resource
    private CpRpcService cpRpcService;

    public ValueHolderV14 insertPayableAdjustDropCopyFun(JSONObject jsonObject, User user) {
        if (log.isDebugEnabled()) {
            log.debug(LogUtil.format("Start 全渠道订单丢单赔付自动生成应付款调整单 Receive Params{}","生成应付款调整单"), jsonObject.toString());
        }

        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        String returnMsg = "保存成功";
        int resultCode = ResultCode.SUCCESS;

        if (jsonObject != null) {
            JSONObject mainMap = jsonObject.getJSONObject("ocBorderDto");
            JSONArray listMap = jsonObject.getJSONArray("ocBorderItemDto");
            //来源类型 1=全渠道订单丢单；2=丢单对应的退换货单
            String sourceType = jsonObject.getString("sourceType");
            if (null==mainMap){
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("主表数据为空!");
                return valueHolderV14;
            }
            if (null==listMap){
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("明细数据为空!");
                return valueHolderV14;
            }

            if (StringUtils.isEmpty(sourceType)){
                valueHolderV14.setCode(ResultCode.FAIL);
                valueHolderV14.setMessage("来源类型为空!");
                return valueHolderV14;
            }

            if ("1".equals(sourceType)) {
                try {
                    valueHolderV14 = payableAdjustDropCopy(mainMap, listMap, user);
                } catch (Exception e) {
                    resultCode = ResultCode.FAIL;
                    returnMsg = e.getMessage();
                }
            }else  if ("2".equals(sourceType)){
                try {
                    valueHolderV14 = payableAdjustDropCopyReutrnOrder(mainMap, listMap, user);
                } catch (Exception e) {
                    resultCode = ResultCode.FAIL;
                    returnMsg = e.getMessage();
                }
            }
            valueHolderV14.setCode(resultCode);
            valueHolderV14.setMessage(returnMsg);
        } else {
            throw new NDSException("参数为空！");
        }

        return valueHolderV14;
    }

    @Transactional(rollbackFor = {Exception.class})
    public ValueHolderV14 payableAdjustDropCopy(JSONObject mainMap, JSONArray listMap, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();

        AcFPayableAdjustmentDO acFPayableAdjustmentDO = new AcFPayableAdjustmentDO();

        OcBOrder ocBOrder = JsonUtils.jsonParseClass(mainMap, OcBOrder.class);

        if (ocBOrder != null) {
            //@20200106 截回或拒收的订单赔付类型为wos-截回/拒收
            /*if (ocBOrder.getIsWosCut() != null && ocBOrder.getIsWosCut().intValue() == 1) {
                //保存前 判断全渠道订单是否已经生成了单据
                int existsSourceCode = acFPayableAdjustmentMapper.selectBillNo(ocBOrder.getBillNo());
                if (existsSourceCode > 0) {
                    throw new NDSException("订单单号【" + ocBOrder.getBillNo() + "】对应的应付款调整单已存在！不允许重复生成！");
                }
            }*/
            acFPayableAdjustmentDO = this.setDo(ocBOrder, user);
            //单据编号生成更新
            JSONObject sequence = new JSONObject();
            String billNo = SequenceGenUtil.generateSquence("SEQ_AC_F_PAYABLE_ADJUSTMENT",
                    sequence, user.getLocale(),false);
            acFPayableAdjustmentDO.setBillNo(billNo);

            if (acFPayableAdjustmentMapper.insert(acFPayableAdjustmentDO) < 0) {
                throw new NDSException("应付款调整单新增保存失败！");
            }
        } else {
            throw new NDSException("主表传参为空保存失败！");
        }
        /**
         * 子表新增
         */
        if (listMap != null) {
            List<OcBOrderItem> ocBOrderItemList = JSON.parseObject(listMap.toJSONString(),
                    new TypeReference<ArrayList<OcBOrderItem>>() {
                    });
            if (ocBOrderItemList != null) {
                for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
                    AcFPayableAdjustmentItemDO acFPayableAdjustmentItemDO =
                            this.setListDo(mainMap, ocBOrderItem, acFPayableAdjustmentDO.getId(), user);
                    if (acFPayableAdjustmentItemMapper.insert(acFPayableAdjustmentItemDO) < 0) {
                        throw new NDSException("应付款调整单新增保存失败！");
                    }
                }
            } else {
                throw new NDSException("明细传参为空保存失败！");
            }
        }

        //应付款调整单: 汇总行的应付款金额(payable_price)  更新到主表的总应付金额(payable_price)
        if (acFPayableAdjustmentMapper.updatePayablePrice(acFPayableAdjustmentDO.getId()) <= 0) {
            throw new NDSException("更新主表总应付金额失败！");
        }

        AcFPayableAdjustmentDO mainDo = acFPayableAdjustmentMapper.selectById(acFPayableAdjustmentDO.getId());
        String billNo = mainDo.getBillNo();

        //应付款调整单-操作类型=新增(丢单复制)
        ValueHolderV14 v14 = payableAdjustmentSaveService.insertLogFun(user, mainDo.getId(), OperatorLogTypeEnum.OPERATOR_DROP.getVal(),
                OperatorLogTypeEnum.OPERATOR_DROP.getText(), new Date());
        if (v14.getCode() == ResultCode.FAIL) {
            throw new NDSException("丢单复制新增操作日志失败！");
        }


        valueHolderV14.setCode(0);
        valueHolderV14.setMessage("应付款调整单新增保存成功！单据号:" + billNo.toString());
        return valueHolderV14;
    }

    /**
     * 明细保存
     *
     * @param ocBOrderItem
     * @param objId
     * @return
     */
    private AcFPayableAdjustmentItemDO setListDo(JSONObject mainMap, OcBOrderItem ocBOrderItem, Long objId, User user) {
        //主表id
        Long acFPayableAdjustmentId = objId;
        //商品id
        Long psCProId = ocBOrderItem.getPsCProId();
        //商品编码
        String psCProEcode = ocBOrderItem.getPsCProEcode();
        //商品名称
        String psCProEname = ocBOrderItem.getPsCProEname();
        //原厂货号
        String factoryNo = ocBOrderItem.getPsCProEcode();
        //条码 ID
        Long psCSkuId = ocBOrderItem.getPsCSkuId();
        //条码编码
        String psCSkuEcode = ocBOrderItem.getPsCSkuEcode();
        //颜色id
        Long psCClrId = ocBOrderItem.getPsCClrId();
        //颜色编码
        String psCClrEcode = ocBOrderItem.getPsCClrEcode();
        //颜色名称
        String psCClrEname = ocBOrderItem.getPsCClrEname();
        //尺码id
        Long psCSizeId = ocBOrderItem.getPsCSizeId();
        //尺码编码
        String psCSizeEcode = ocBOrderItem.getPsCSizeEcode();
        //尺码名称
        String psCSizeEname = ocBOrderItem.getPsCSizeEname();
        //品牌id

        //国标码
        String gbcode = ocBOrderItem.getBarcode();
        //数量
        BigDecimal qty = ocBOrderItem.getQty();
        //标准价
        BigDecimal standardPrice = ocBOrderItem.getPriceList();

        //全渠道订单成交单价 = 成交金额(OC_B_ORDER_ITEM.real_amt) / 订单数量(OC_B_ORDER_ITEM.qty)
        BigDecimal price = AmountCalcUtils.divideBigDecimal(ocBOrderItem.getRealAmt(), qty, 2);

        //实际成交价 = 成交金额(OC_B_ORDER_ITEM.real_amt)
        BigDecimal truePrice = ocBOrderItem.getRealAmt();

        //应付款金额
        Integer billType = mainMap.getInteger("BILL_TYPE"); //单据类型
        Long logisticsId = mainMap.getLong("CP_C_LOGISTICS_ID"); //快递公司
        Long phyWarehouseId = mainMap.getLong("CP_C_PHY_WAREHOUSE_ID"); //赔付仓库
        Date currentDate = new Date(); //日期
        String sourceCode = mainMap.getString("TID"); //平台单号

        CompensateQueryRequest compensateQueryRequest = new CompensateQueryRequest();
        compensateQueryRequest.setPhyWarehouseId(phyWarehouseId);
        compensateQueryRequest.setLogisticsId(logisticsId);
        compensateQueryRequest.setCurrentDate(currentDate);
        compensateQueryRequest.setSourceCode(sourceCode);
        compensateQueryRequest.setPriceList(standardPrice);
        compensateQueryRequest.setPrice(price);

        // ValueHolderV14<BigDecimal> valueHolderV14 = payableRpcService.getCompensate(compensateQueryRequest);
        // BigDecimal payablePrice = valueHolderV14.getData() == null ? BigDecimal.ZERO : valueHolderV14.getData();
        // payablePrice = AmountCalcUtils.multiplyBigDecimal(payablePrice, qty, 2);

        AcFPayableAdjustmentItemDO acFPayableAdjustmentItemDO = new AcFPayableAdjustmentItemDO();
        acFPayableAdjustmentItemDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT_ITEM));
        acFPayableAdjustmentItemDO.setAcFPayableAdjustmentId(acFPayableAdjustmentId);
        acFPayableAdjustmentItemDO.setPsCProId(psCProId);
        acFPayableAdjustmentItemDO.setPsCProEcode(psCProEcode);
        acFPayableAdjustmentItemDO.setPsCProEname(psCProEname);
        acFPayableAdjustmentItemDO.setFactoryNo(factoryNo);
        acFPayableAdjustmentItemDO.setPsCSkuId(psCSkuId);
        acFPayableAdjustmentItemDO.setPsCSkuEcode(psCSkuEcode);
        acFPayableAdjustmentItemDO.setPsCClrId(psCClrId);
        acFPayableAdjustmentItemDO.setPsCClrEcode(psCClrEcode);
        acFPayableAdjustmentItemDO.setPsCClrEname(psCClrEname);
        acFPayableAdjustmentItemDO.setPsCSizeId(psCSizeId);
        acFPayableAdjustmentItemDO.setPsCSizeEcode(psCSizeEcode);
        acFPayableAdjustmentItemDO.setPsCSizeEname(psCSizeEname);
        acFPayableAdjustmentItemDO.setGbcode(gbcode);
        acFPayableAdjustmentItemDO.setQty(qty);
        acFPayableAdjustmentItemDO.setStandardPrice(standardPrice);
        acFPayableAdjustmentItemDO.setTruePrice(truePrice);
        acFPayableAdjustmentItemDO.setPayablePrice(price);
        acFPayableAdjustmentItemDO.setDealAmt(price);


        //组织中心、所属公司、可用状态
        AcBeanUtils.makeCreateAd(acFPayableAdjustmentItemDO, user);

        Date date = new Date();

        //创建人id
        acFPayableAdjustmentItemDO.setOwnerid(Long.valueOf(user.getId()));
        //创建时间
        acFPayableAdjustmentItemDO.setCreationdate(date);
        //创建人用户名
        acFPayableAdjustmentItemDO.setOwnername(user.getName());
        //创建人姓名
        acFPayableAdjustmentItemDO.setOwnerename(user.getEname());
        //修改人id
        acFPayableAdjustmentItemDO.setModifierid(Long.valueOf(user.getId()));
        //修改时间
        acFPayableAdjustmentItemDO.setModifieddate(date);
        //修改人用户名
        acFPayableAdjustmentItemDO.setModifiername(user.getName());
        //修改人姓名
        acFPayableAdjustmentItemDO.setModifierename(user.getEname());

        return acFPayableAdjustmentItemDO;
    }

    /**
     * 主表保存
     *
     * @param ocBorder
     * @return
     */
    private AcFPayableAdjustmentDO setDo(OcBOrder ocBorder, User user) {
        //店铺id
        Long cpCShopId = ocBorder.getCpCShopId();
        //店铺名称
        String cpCShopTitle = ocBorder.getCpCShopTitle();
        //付款类型： 银行
        Integer payType = AcConstant.CON_PAY_TYPE_BANK;
        //付款时间
        Date payTime = ocBorder.getPayTime();
        String remark = "";
        //单据类型：丢单赔付
        Integer billType = PayBillTypeEnum.PAY_BF.getVal();
        //截回或拒收的订单赔付类型为wos-截回/拒收
        /*if (ocBorder.getIsWosCut() != null && ocBorder.getIsWosCut().intValue() == 1) {
            billType = AcConstant.CON_PAY_BILL_TYPE_05;
        }*/

        //系统订单号 = 全渠道订单的单据编号
        String orderNo = ocBorder.getBillNo();
        //顾客姓名 = 收货人姓名
        String customerName = ocBorder.getReceiverName();
        //顾客电话 = 收货人手机号码
        String customerTel = ocBorder.getReceiverMobile();
        //调整类型：线下
        Integer adjustType = AcConstant.CON_ADJUST_TYPE_OFFLINE;
        String alipayAccount = "";
        //会员昵称 = 用户昵称
        String customerNick = ocBorder.getUserNick();
        //平台单号 = 全渠道订单的平台单号信息
        String tid = ocBorder.getSourceCode();
        //实体仓id
        Long cpCPhyWarehouseId = ocBorder.getCpCPhyWarehouseId();
        //实体仓编码
        String cpCPhyWarehouseEcode = ocBorder.getCpCPhyWarehouseEcode();
        //实体仓名称
        String cpCPhyWarehouseEname = ocBorder.getCpCPhyWarehouseEname();
        //快递公司id
        Long cpCLogisticsId = ocBorder.getCpCLogisticsId();
        //快递公司编码
        String cpCLogisticsEcode = ocBorder.getCpCLogisticsEcode();
        //快递公司名称
        String cpCLogisticsEname = ocBorder.getCpCLogisticsEname();
        //快递单号 = 全渠道订单物流编码
        String logisticsNo = ocBorder.getExpresscode();
        //总应付金额 = 订单总额
        BigDecimal payablePrice = ocBorder.getOrderAmt();


        AcFPayableAdjustmentDO acFPayableAdjustmentDO = new AcFPayableAdjustmentDO();
        acFPayableAdjustmentDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT));
        //新增单据状态默认为: 待审核(未审核)
        acFPayableAdjustmentDO.setBillStatus(AcConstant.CON_BILL_STATUS_01);
        acFPayableAdjustmentDO.setCpCShopId(cpCShopId);

        CpRpcService cpRpcService = ApplicationContextHandle.getBean(CpRpcService.class);

        CpShop cpShop = cpRpcService.selectCpCShopById(cpCShopId);
        if (cpShop != null) {
            //渠道类型
            Long channelType = Long.parseLong(cpShop.getChannelType());
            acFPayableAdjustmentDO.setReserveBigint01(channelType);
        }

        acFPayableAdjustmentDO.setCpCShopTitle(cpCShopTitle);
        acFPayableAdjustmentDO.setPayTime(payTime);
        acFPayableAdjustmentDO.setRemark(remark);
        acFPayableAdjustmentDO.setBillType(billType);
        acFPayableAdjustmentDO.setOrderNo(orderNo);
        acFPayableAdjustmentDO.setCustomerName(customerName);
        acFPayableAdjustmentDO.setCustomerTel(customerTel);
        acFPayableAdjustmentDO.setAdjustType(adjustType);
        acFPayableAdjustmentDO.setAlipayAccount(alipayAccount);
        acFPayableAdjustmentDO.setCustomerNick(customerNick);
        acFPayableAdjustmentDO.setTid(tid);
        acFPayableAdjustmentDO.setCpCPhyWarehouseId(cpCPhyWarehouseId);
        acFPayableAdjustmentDO.setCpCPhyWarehouseEcode(cpCPhyWarehouseEcode);
        acFPayableAdjustmentDO.setCpCPhyWarehouseEname(cpCPhyWarehouseEname);
        acFPayableAdjustmentDO.setCpCLogisticsId(cpCLogisticsId);
        acFPayableAdjustmentDO.setCpCLogisticsEcode(cpCLogisticsEcode);
        acFPayableAdjustmentDO.setCpCLogisticsEname(cpCLogisticsEname);
        acFPayableAdjustmentDO.setLogisticsNo(logisticsNo);
        acFPayableAdjustmentDO.setPayablePrice(payablePrice);
        //零售发货单
        acFPayableAdjustmentDO.setReserveVarchar01(AcConstant.PAYABLE_SOURCE_TYPE_1);
        //组织中心、所属公司、可用状态
        AcBeanUtils.makeCreateAd(acFPayableAdjustmentDO, user);

        //创建人id
        acFPayableAdjustmentDO.setOwnerid(Long.valueOf(user.getId()));
        //创建时间
        acFPayableAdjustmentDO.setCreationdate(new Date());
        //创建人用户名
        acFPayableAdjustmentDO.setOwnername(user.getName());
        //创建人姓名
        acFPayableAdjustmentDO.setOwnerename(user.getEname());

        return acFPayableAdjustmentDO;
    }

    /**
     * 退单生成应付款调整单
     * @param mainMap
     * @param listMap
     * @param user
     * @return
     */
    @Transactional(rollbackFor = {Exception.class})
    public ValueHolderV14 payableAdjustDropCopyReutrnOrder(JSONObject mainMap, JSONArray listMap, User user) {
        ValueHolderV14 valueHolderV14 = new ValueHolderV14();
        AcFPayableAdjustmentDO mainDo = new AcFPayableAdjustmentDO();
        OcBReturnOrder returnOrder = JsonUtils.jsonParseClass(mainMap, OcBReturnOrder.class);
        if (null != returnOrder) {
            mainDo = this.setDoByReturnOrder(returnOrder, user);
            if (acFPayableAdjustmentMapper.insert(mainDo) < 0) {
                throw new NDSException("退单新增应付款调整单保存失败！");
            }
        } else {
            throw new NDSException("主表传参为空保存失败！");
        }
        //明细新增
        List<OcBReturnOrderRefund> ocBReturnOrderRefundList = JSON.parseObject(listMap.toJSONString(),
                new TypeReference<ArrayList<OcBReturnOrderRefund>>() {});
        if (null != ocBReturnOrderRefundList) {
            for (OcBReturnOrderRefund ocBReturnOrderRefund : ocBReturnOrderRefundList) {
                AcFPayableAdjustmentItemDO ItemDO = this.setListDoByReturnOrder(ocBReturnOrderRefund, mainDo.getId(), user);
                if (acFPayableAdjustmentItemMapper.insert(ItemDO) < 0) {
                    throw new NDSException("退单新增应付款调整单明细保存失败！");
                }
            }
        } else {
            throw new NDSException("明细传参为空保存失败！");
        }

        //应付款调整单: 汇总行的应付款金额(payable_price)  更新到主表的总应付金额(payable_price)
        if (acFPayableAdjustmentMapper.updatePayablePrice(mainDo.getId()) <= 0) {
            throw new NDSException("更新主表总应付金额失败！");
        }

        String billNo = mainDo.getBillNo();
        //应付款调整单-操作类型=新增退单(丢单复制)
        ValueHolderV14 v14 = payableAdjustmentSaveService.insertLogFun(user, mainDo.getId(), OperatorLogTypeEnum.OPERATOR_DROP_RETURN.getVal(),
                OperatorLogTypeEnum.OPERATOR_DROP_RETURN.getText(), new Date());

        if (v14.getCode() == ResultCode.FAIL) {
            throw new NDSException("丢单复制新增操作日志失败！");
        }
        //推送ES
        AcPayableAdjustmentPushESUtil.pushOrderAndItem(mainDo.getId());
        valueHolderV14.setCode(0);
        valueHolderV14.setMessage("退单新增应付款调整单保存成功！单据号:" + billNo);
        return valueHolderV14;
    }

    /**
     * 主表保存
     *
     * @param returnOrder
     * @return
     */
    private AcFPayableAdjustmentDO setDoByReturnOrder(OcBReturnOrder returnOrder, User user) {
        //店铺id
        Long cpCShopId = returnOrder.getCpCShopId();
        //店铺名称
        String cpCShopTitle = returnOrder.getCpCShopTitle();
        //付款类型： 默认：支付宝
        Integer payType = AcConstant.CON_PAY_TYPE_ZFB;
        //付款时间
        //Date payTime = ocBorder.getPayTime();
        String remark = "";
        //单据类型：丢单赔付
        Integer billType = PayBillTypeEnum.PAY_BF.getVal();
        //系统订单号 = 全渠道订单的单据编号
        String orderNo = returnOrder.getId().toString();
        //顾客姓名 = 收货人姓名
        String customerName = returnOrder.getReceiveName();
        //顾客电话 = 收货人手机号码
        String customerTel = returnOrder.getReceivePhone();
        //调整类型：线下
        Integer adjustType = AcConstant.CON_ADJUST_TYPE_OFFLINE;
        String alipayAccount = "";
        //会员昵称 = 用户昵称
        String customerNick = returnOrder.getBuyerNick();
        //平台单号 = 全渠道订单的平台单号信息
        String tid = returnOrder.getTid();
        //实体仓id
        Long cpCPhyWarehouseId = returnOrder.getCpCPhyWarehouseId();
        CpCPhyWarehouse cpCPhyWarehouse = cpRpcService.selectPhyWarehouseById(cpCPhyWarehouseId);
        //实体仓编码
        String cpCPhyWarehouseEcode = cpCPhyWarehouse==null?"":cpCPhyWarehouse.getEcode();
        //实体仓名称
        String cpCPhyWarehouseEname = cpCPhyWarehouse==null?"":cpCPhyWarehouse.getEname();
        //快递公司id
        Long cpCLogisticsId = returnOrder.getCpCLogisticsId();
        CpLogistics cpLogistics = cpRpcService.selectLogisticsById(cpCLogisticsId);
        //快递公司编码
        String cpCLogisticsEcode = StringUtils.isEmpty(returnOrder.getCpCLogisticsEcode())?cpLogistics==null?"":
                cpLogistics.getEcode():returnOrder.getCpCLogisticsEcode();
        //快递公司名称
        String cpCLogisticsEname = StringUtils.isEmpty(returnOrder.getCpCLogisticsEname())?cpLogistics==null?"":
                cpLogistics.getEname():returnOrder.getCpCLogisticsEname();
        //快递单号 = 全渠道订单物流编码
        String logisticsNo = returnOrder.getLogisticsCode();
        //总应付金额 = 订单总额
        BigDecimal payablePrice = BigDecimal.ONE;//returnOrder.get();

        AcFPayableAdjustmentDO acFPayableAdjustmentDO = new AcFPayableAdjustmentDO();
        acFPayableAdjustmentDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT));
        //新增单据状态默认为: 待审核(未审核)
        acFPayableAdjustmentDO.setBillStatus(AcConstant.CON_BILL_STATUS_01);
        acFPayableAdjustmentDO.setCpCShopId(cpCShopId);

        CpRpcService cpRpcService = ApplicationContextHandle.getBean(CpRpcService.class);

        CpShop cpShop = cpRpcService.selectCpCShopById(cpCShopId);
        if (cpShop != null) {
            //渠道类型
            Long channelType = Long.parseLong(cpShop.getChannelType());
            acFPayableAdjustmentDO.setReserveBigint01(channelType);
        }

        //单据编号生成更新
        JSONObject sequence = new JSONObject();
        String billNo = SequenceGenUtil.generateSquence("SEQ_AC_F_PAYABLE_ADJUSTMENT",
                sequence, user.getLocale(),false);
        acFPayableAdjustmentDO.setBillNo(billNo);

        acFPayableAdjustmentDO.setCpCShopTitle(cpCShopTitle);
        //acFPayableAdjustmentDO.setPayTime(payTime);
        acFPayableAdjustmentDO.setRemark(remark);
        acFPayableAdjustmentDO.setBillType(billType);
        acFPayableAdjustmentDO.setOrderNo(orderNo);
        acFPayableAdjustmentDO.setCustomerName(customerName);
        acFPayableAdjustmentDO.setCustomerTel(customerTel);
        acFPayableAdjustmentDO.setAdjustType(adjustType);
        acFPayableAdjustmentDO.setAlipayAccount(alipayAccount);
        acFPayableAdjustmentDO.setCustomerNick(customerNick);
        acFPayableAdjustmentDO.setTid(tid);
        acFPayableAdjustmentDO.setCpCPhyWarehouseId(cpCPhyWarehouseId);
        acFPayableAdjustmentDO.setCpCPhyWarehouseEcode(cpCPhyWarehouseEcode);
        acFPayableAdjustmentDO.setCpCPhyWarehouseEname(cpCPhyWarehouseEname);
        acFPayableAdjustmentDO.setCpCLogisticsId(cpCLogisticsId);
        acFPayableAdjustmentDO.setCpCLogisticsEcode(cpCLogisticsEcode);
        acFPayableAdjustmentDO.setCpCLogisticsEname(cpCLogisticsEname);
        acFPayableAdjustmentDO.setLogisticsNo(logisticsNo);
        acFPayableAdjustmentDO.setPayablePrice(payablePrice);
        //零售退货单
        acFPayableAdjustmentDO.setReserveVarchar01(AcConstant.PAYABLE_SOURCE_TYPE_2);
        //原单出库日期=入库时间
        acFPayableAdjustmentDO.setSourceOutsourceDate(returnOrder.getInTime());
        //组织中心、所属公司、可用状态
        AcBeanUtils.makeCreateAd(acFPayableAdjustmentDO, user);
        //创建人id
        acFPayableAdjustmentDO.setOwnerid(Long.valueOf(user.getId()));
        //创建时间
        acFPayableAdjustmentDO.setCreationdate(new Date());
        //创建人用户名
        acFPayableAdjustmentDO.setOwnername(user.getName());
        //创建人姓名
        acFPayableAdjustmentDO.setOwnerename(user.getEname());

        return acFPayableAdjustmentDO;
    }


    /**
     * 明细保存
     *
     * @param objId
     * @return
     */
    private AcFPayableAdjustmentItemDO setListDoByReturnOrder(OcBReturnOrderRefund ocBReturnOrderRefund, Long objId, User user) {

        AcFPayableAdjustmentItemDO acFPayableAdjustmentItemDO = new AcFPayableAdjustmentItemDO();
        acFPayableAdjustmentItemDO.setId(ModelUtil.getSequence(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT_ITEM));
        acFPayableAdjustmentItemDO.setAcFPayableAdjustmentId(objId);
        //商品id
        acFPayableAdjustmentItemDO.setPsCProId(ocBReturnOrderRefund.getPsCProId());
        //商品编码
        acFPayableAdjustmentItemDO.setPsCProEcode(ocBReturnOrderRefund.getPsCProEcode());
        //商品名称
        acFPayableAdjustmentItemDO.setPsCProEname(ocBReturnOrderRefund.getPsCProEname());
        //原厂货号
        acFPayableAdjustmentItemDO.setFactoryNo(ocBReturnOrderRefund.getBarcode());
        //条码 ID
        acFPayableAdjustmentItemDO.setPsCSkuId(ocBReturnOrderRefund.getPsCSkuId());
        //条码编码
        acFPayableAdjustmentItemDO.setPsCSkuEcode(ocBReturnOrderRefund.getPsCSkuEcode());
        //颜色id
        acFPayableAdjustmentItemDO.setPsCClrId(ocBReturnOrderRefund.getPsCClrId());
        //颜色编码
        acFPayableAdjustmentItemDO.setPsCClrEcode(ocBReturnOrderRefund.getPsCClrEcode());
        //颜色名称
        acFPayableAdjustmentItemDO.setPsCClrEname(ocBReturnOrderRefund.getPsCClrEname());
        //尺码id
        acFPayableAdjustmentItemDO.setPsCSizeId(ocBReturnOrderRefund.getPsCSizeId());
        //尺码编码
        acFPayableAdjustmentItemDO.setPsCSizeEcode(ocBReturnOrderRefund.getPsCSizeEcode());
        //尺码名称
        acFPayableAdjustmentItemDO.setPsCSizeEname(ocBReturnOrderRefund.getPsCSizeEname());
        //国标码
        acFPayableAdjustmentItemDO.setGbcode(ocBReturnOrderRefund.getBarcode());
        //数量
        acFPayableAdjustmentItemDO.setQty(ocBReturnOrderRefund.getQtyRefund());
        //标准价
        acFPayableAdjustmentItemDO.setStandardPrice(ocBReturnOrderRefund.getPrice());
        //实际成交价 = 退单金额
        acFPayableAdjustmentItemDO.setTruePrice(ocBReturnOrderRefund.getAmtRefund());
        //应付金额 默认 0
        acFPayableAdjustmentItemDO.setPayablePrice(BigDecimal.ZERO);
        //成交单价 = 单件退货金额
        acFPayableAdjustmentItemDO.setDealAmt(ocBReturnOrderRefund.getAmtRefundSingle());
        //原始单据明细id
        acFPayableAdjustmentItemDO.setOrderItemId(ocBReturnOrderRefund.getId());
        //订单数量
        acFPayableAdjustmentItemDO.setOrderQty(ocBReturnOrderRefund.getQtyRefund());

        //组织中心、所属公司、可用状态
        AcBeanUtils.makeCreateAd(acFPayableAdjustmentItemDO, user);

        Date date = new Date();
        //创建人id
        acFPayableAdjustmentItemDO.setOwnerid(Long.valueOf(user.getId()));
        //创建时间
        acFPayableAdjustmentItemDO.setCreationdate(date);
        //创建人用户名
        acFPayableAdjustmentItemDO.setOwnername(user.getName());
        //创建人姓名
        acFPayableAdjustmentItemDO.setOwnerename(user.getEname());
        //修改人id
        acFPayableAdjustmentItemDO.setModifierid(Long.valueOf(user.getId()));
        //修改时间
        acFPayableAdjustmentItemDO.setModifieddate(date);
        //修改人用户名
        acFPayableAdjustmentItemDO.setModifiername(user.getName());
        //修改人姓名
        acFPayableAdjustmentItemDO.setModifierename(user.getEname());

        return acFPayableAdjustmentItemDO;
    }

}
