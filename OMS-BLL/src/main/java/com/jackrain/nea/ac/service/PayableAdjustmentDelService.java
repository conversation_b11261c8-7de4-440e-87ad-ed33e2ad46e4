package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentDO;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.st.utils.ValueHolderUtils;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * Bll层-子明细删除逻辑
 *
 * <AUTHOR> 陈俊明
 * @since : 2019-03-25
 * create at : 2019-03-25 11:01
 */
@Component
@Slf4j
@Transactional
public class PayableAdjustmentDelService extends CommandAdapter {
    @Autowired
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Autowired
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    /**
     * 主子表删除
     *
     * @param querySession
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder holder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"), Feature.OrderedField);

        if (param == null || param.size() == 0) {
            throw new NDSException("参数为空！");
        }

        String isDel = param.getString("isdelmtable");
        Long objId = param.getLong("objid");

        //判断主表是否存在
        AcFPayableAdjustmentDO acFPayableAdjustmentDO = acFPayableAdjustmentMapper.selectById(objId);
        if (acFPayableAdjustmentDO == null) {
            holder = ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
            return holder;
        }

        //状态数据检查
        if (!checkStatus(acFPayableAdjustmentDO, holder)) {
            return holder;
        }

        JSONObject tabItem = param.getJSONObject("tabitem");
        Boolean isDelete = false;

        //判断是删除主表还是明细表单独删除
        if (isDelete.equals(isDel)) {
            JSONArray itemArray = tabItem.getJSONArray(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT_ITEM);
            //单独删除明细
            holder = delItem(itemArray, holder, querySession, acFPayableAdjustmentDO);
        } else {
            //删除主表
            holder = delMain(objId, holder, querySession);
        }
        return holder;
    }

    /**
     * 删除明细表
     *
     * @param itemArray   子表数据
     * @param valueHolder 封装数据
     * @return 返回状态
     */
    private ValueHolder delItem(JSONArray itemArray, ValueHolder valueHolder, QuerySession querySession,
                                AcFPayableAdjustmentDO acFPayableAdjustmentDO) {
        for (int i = 0; i < itemArray.size(); i++) {
            Long itemId = itemArray.getLong(i);
            AcFPayableAdjustmentItemDO acFPayableAdjustmentItemDO = acFPayableAdjustmentItemMapper.selectById(itemId);
            if (acFPayableAdjustmentItemDO == null) {
                valueHolder = ValueHolderUtils.getFailValueHolder("当前记录已不存在！");
                return valueHolder;
            } else {
                int deleteCount = acFPayableAdjustmentItemMapper.deleteById(itemId);
                if (deleteCount < 0) {
                    valueHolder = ValueHolderUtils.getFailValueHolder("删除失败！");
                    return valueHolder;
                } else {
                    //修改人id
                    acFPayableAdjustmentDO.setModifierid(Long.valueOf(querySession.getUser().getId()));
                    //修改人用户名
                    acFPayableAdjustmentDO.setModifiername(querySession.getUser().getName());
                    //修改人姓名
                    acFPayableAdjustmentDO.setModifierename(querySession.getUser().getEname());
                    //修改时间
                    acFPayableAdjustmentDO.setModifieddate(new Date());

                    if (acFPayableAdjustmentMapper.updateById(acFPayableAdjustmentDO) < 0) {
                        valueHolder = ValueHolderUtils.getFailValueHolder("删除失败！");
                        return valueHolder;
                    }
                }
            }
        }
        valueHolder = ValueHolderUtils.getDeleteSuccessValueHolder();
        return valueHolder;
    }

    /**
     * 删除主表
     *
     * @param mainId 主表id
     * @param holder 封装数据
     * @return 返回状态
     */
    private ValueHolder delMain(Long mainId, ValueHolder holder, QuerySession querySession) {
        return holder;
    }

    private boolean checkStatus(AcFPayableAdjustmentDO acFPayableAdjustmentDO, ValueHolder valueHolder) {
        int iStatus = acFPayableAdjustmentDO.getBillStatus();
        if (iStatus != AcConstant.CON_BILL_STATUS_01) {
            valueHolder.put("code", -1);
            valueHolder.put("message", "单据处于未审核状态，才允许删除！");
            return false;
        }
        return true;
    }
}

