package com.jackrain.nea.ac.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.jackrain.nea.ac.model.AcFPayableAdjustmentItemDO;
import com.jackrain.nea.oc.oms.model.constant.AcConstant;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentItemMapper;
import com.jackrain.nea.oc.oms.mapper.ac.AcFPayableAdjustmentMapper;
import com.jackrain.nea.oc.oms.model.enums.ac.PayBillTypeEnum;
import com.jackrain.nea.rpc.StRpcService;
import com.jackrain.nea.st.model.request.CompensateQueryRequest;
import com.jackrain.nea.sys.CommandAdapter;
import com.jackrain.nea.sys.domain.ValueHolderV14;
import com.jackrain.nea.util.ValueHolder;
import com.jackrain.nea.web.query.DefaultWebEvent;
import com.jackrain.nea.web.query.QuerySession;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Bll层-获取丢单赔付策略，并重新计算明细金额
 *
 * <AUTHOR> 陈俊明
 * @since : 2019-03-28
 * create at : 2019-03-28 09:45
 */
@Component
@Slf4j
@Transactional
public class PayableGetCompensateService extends CommandAdapter {
    @Autowired
    private AcFPayableAdjustmentMapper acFPayableAdjustmentMapper;

    @Autowired
    private AcFPayableAdjustmentItemMapper acFPayableAdjustmentItemMapper;

    @Autowired
    private StRpcService payableRpcService;

    /**
     * 根据获取的丢单赔偿策略，计算明细金额。如果未获取到，行明细的应付金额默认带0
     * @param querySession
     * @return
     * @throws NDSException
     */
    @Override
    public ValueHolder execute(QuerySession querySession) throws NDSException {
        ValueHolder valueHolder = new ValueHolder();
        DefaultWebEvent event = querySession.getEvent();
        JSONObject param = JSON.parseObject(JSON.toJSONStringWithDateFormat(
                event.getParameterValue("param"), "yyyy-MM-dd HH:mm:ss"),
                Feature.OrderedField);
        JSONArray itemMap = null;
        JSONObject mainMap = null;

        BigDecimal totalPayablePrice = BigDecimal.ZERO;
        List<AcFPayableAdjustmentItemDO> payableAdjustmentItemDOList = new ArrayList<>();

        if (param != null) {
            Long id = param.getLong("objid");
            JSONObject fixColumn = param.getJSONObject("fixcolumn");

            if (fixColumn != null) {
                mainMap = fixColumn.getJSONObject(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT);
                itemMap = fixColumn.getJSONArray(AcConstant.TAB_AC_F_PAYABLE_ADJUSTMENT_ITEM);
                if(itemMap!=null) {
                    payableAdjustmentItemDOList = JSON.parseObject(itemMap.toJSONString(),
                            new TypeReference<ArrayList<AcFPayableAdjustmentItemDO>>() {
                            });
                }
            }

            Integer billType = mainMap.getInteger("BILL_TYPE"); //单据类型
            Long logisticsId = mainMap.getLong("CP_C_LOGISTICS_ID"); //快递公司
            Long phyWarehouseId = mainMap.getLong("CP_C_PHY_WAREHOUSE_ID"); //赔付仓库
            Date currentDate = id >= 0 ? mainMap.getDate("CREATIONDATE") : new Date(); //日期
            String sourceCode = mainMap.getString("TID"); //平台单号

            //先判断传过来的单据类型为：丢单赔付的时候才进行 明细金额的重新计算
            if ((PayBillTypeEnum.PAY_BF.getVal() == billType || PayBillTypeEnum.PAY_TK.getVal() == billType || PayBillTypeEnum.PAY_STORE.getVal() == billType)
                    && itemMap != null) {
                try {
                    for (AcFPayableAdjustmentItemDO itemDO : payableAdjustmentItemDOList) {
                        if (itemDO != null) {
                            BigDecimal priceList = itemDO.getStandardPrice();
                            BigDecimal price = itemDO.getTruePrice();
                            CompensateQueryRequest compensateQueryRequest = new CompensateQueryRequest();
                            compensateQueryRequest.setPhyWarehouseId(phyWarehouseId);
                            compensateQueryRequest.setLogisticsId(logisticsId);
                            compensateQueryRequest.setCurrentDate(currentDate);
                            compensateQueryRequest.setSourceCode(sourceCode);
                            compensateQueryRequest.setPriceList(priceList);
                            compensateQueryRequest.setPrice(price);
                            ValueHolderV14<BigDecimal> valueHolderV14 = payableRpcService.getCompensate(compensateQueryRequest);
                            BigDecimal payablePrice = valueHolderV14.getData() == null ? BigDecimal.ZERO : valueHolderV14.getData();
                            totalPayablePrice = totalPayablePrice.add(payablePrice);
                            BigDecimal qty = itemDO.getQty() == null ? BigDecimal.ZERO : itemDO.getQty();
                            BigDecimal payAmt = itemDO.getPayAmt();

                            if (qty.compareTo(BigDecimal.ZERO) == 0) {
                                itemDO.setPayAmt(BigDecimal.ZERO);
                            } else {
                                payAmt = payablePrice.divide(qty, 2);
                                itemDO.setPayAmt(payAmt);
                            }

                            itemDO.setPayablePrice(payablePrice);
                        }
                    }
                } catch (Exception e) {
                    valueHolder.put("data", itemMap);
                }
            }

            valueHolder.put("code", 0);
            valueHolder.put("message", "返回明细信息");

            return valueHolder;
        } else {
            valueHolder.put("code", -1);
            valueHolder.put("message", "参数问题");
        }

        return valueHolder;
    }
}
