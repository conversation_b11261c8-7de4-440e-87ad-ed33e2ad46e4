package com.jackrain.nea.cp.services;

import com.jackrain.nea.cp.dto.CpCShopProfileExt;
import com.jackrain.nea.cpext.model.result.CpCSaleOrganizationQueryResult;
import com.jackrain.nea.cpext.model.table.CpCShopProfile;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItemExt;
import com.jackrain.nea.oc.oms.services.BusinessSystemParamService;
import com.jackrain.nea.ps.model.OmsProAttributeInfo;
import com.jackrain.nea.ps.model.ProductSku;
import com.jackrain.nea.ps.model.SkuType;
import com.jackrain.nea.rpc.CpRpcService;
import com.jackrain.nea.rpc.PsRpcService;
import com.jackrain.nea.utility.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName CpSaleOrganizationService
 * @Description 销售组织查询
 * <AUTHOR>
 * @Date 2024/7/26 13:56
 * @Version 1.0
 */
@Slf4j
@Component
public class CpSaleOrganizationService {

    @Autowired
    private PsRpcService psRpcService;
    @Autowired
    private CpRpcService cpRpcService;
    @Autowired
    private BusinessSystemParamService businessSystemParamService;

    public Map<Long, CpCShopProfileExt> querySaleOrganization(List<OcBOrderItem> ocBOrderItemList, Long shopId, Map<Long, String> errorMap) {
        Map<Long, CpCShopProfileExt> cpCShopProfileMap = new HashMap<>();
        // 构建销售123级
        for (OcBOrderItem ocBOrderItem : ocBOrderItemList) {
            if (SkuType.NO_SPLIT_COMBINE == ocBOrderItem.getProType()) {
                continue;
            }
            cpCShopProfileMap.put(ocBOrderItem.getId(), null);
            String skuEcode = ocBOrderItem.getPsCSkuEcode();
            // 根据商品编码 查询商品款号信息
            ProductSku productSku = psRpcService.selectProductSku(skuEcode);
            if (productSku != null && productSku.getProAttributeMap() != null) {
                if (productSku.getProAttributeMap().get("M_DIM12_ID") == null) {
                    errorMap.put(ocBOrderItem.getId(), "商品零级查询不到");
                    continue;
                }
                // 零级商品编码
                String categoryCode = productSku.getProAttributeMap().get("M_DIM12_ID").getEcode();
                String categoryName = productSku.getProAttributeMap().get("M_DIM12_ID").getEname();
                // 如果找到了零级。则走指定的销售组织 否则就查询销售组织
                Map<String, String> specifiedMDim4Map = businessSystemParamService.getSpecifiedMDim4();
                if (specifiedMDim4Map != null) {
                    String appointCode = specifiedMDim4Map.get(categoryCode);
                    // 根据英文逗号隔开 分出来三个数值
                    if (StringUtils.isNotEmpty(appointCode)) {
                        String[] split = appointCode.split(",");
                        if (split.length == 3) {
                            CpCShopProfile cpCShopProfile = new CpCShopProfile();
                            CpCShopProfileExt cpCShopProfileExt = new CpCShopProfileExt();
                            String salesCenterCode = split[0];
                            String salesDepartmentCode = split[1];
                            String salesGroupCode = split[2];
                            cpCShopProfile.setCategoryCode(categoryCode);
                            cpCShopProfile.setCategoryName(categoryName);
                            cpCShopProfile.setSalesCenterCode(salesCenterCode);
                            cpCShopProfile.setSalesDepartmentCode(salesDepartmentCode);
                            cpCShopProfile.setSalesGroupCode(salesGroupCode);
                            cpCShopProfileExt.setCpCShopProfile(cpCShopProfile);
                            cpCShopProfileExt.setProductSku(productSku);
                            cpCShopProfileExt.setCategoryCode(categoryCode);
                            cpCShopProfileExt.setCategoryName(categoryName);
                            cpCShopProfileMap.put(ocBOrderItem.getId(), cpCShopProfileExt);
                            continue;
                        }
                    }
                }

                List<CpCShopProfile> cpCShopProfileList = cpRpcService.queryShopProfileByShopId(shopId);
                if (CollectionUtils.isEmpty(cpCShopProfileList)) {
                    errorMap.put(ocBOrderItem.getId(), "平台店铺档案品项明细找不到");
                    continue;
                }
                // 根据cpCShopProfileList生成map， 里面的categoryCode来作为key
                Map<String, List<CpCShopProfile>> cpCShopProfileCategoryMap = cpCShopProfileList.stream().collect(Collectors.groupingBy(CpCShopProfile::getCategoryCode));
                List<CpCShopProfile> cpCShopProfiles = cpCShopProfileCategoryMap.get(categoryCode);
                if (CollectionUtils.isEmpty(cpCShopProfiles)) {
                    errorMap.put(ocBOrderItem.getId(), "平台店铺档案品项明细找不到");
                    continue;
                }
                CpCShopProfile cpCShopProfile = cpCShopProfiles.get(0);
                CpCShopProfileExt cpCShopProfileExt = new CpCShopProfileExt();
                cpCShopProfileExt.setCpCShopProfile(cpCShopProfile);
                cpCShopProfileExt.setProductSku(productSku);
                cpCShopProfileExt.setCategoryCode(categoryCode);
                cpCShopProfileExt.setCategoryName(categoryName);
                if (StringUtils.isNotEmpty(cpCShopProfile.getSalesCenterCode()) && StringUtils.isNotEmpty(cpCShopProfile.getSalesGroupCode())) {
                    cpCShopProfileMap.put(ocBOrderItem.getId(), cpCShopProfileExt);
                }
            } else {
                errorMap.put(ocBOrderItem.getId(), "商品或商品属性不存在");
            }
        }
        return cpCShopProfileMap;
    }

    /**
     * 构建商品明细附加信息
     *
     * @param orderItemList 商品明细
     * @param order         订单信息
     * @return 商品明细附加信息列表
     */
    public List<OcBOrderItemExt> queryExtByItem(List<OcBOrderItem> orderItemList, OcBOrder order) {
        if (CollectionUtils.isEmpty(orderItemList)
                || Objects.isNull(order) || Objects.isNull(order.getCpCShopId())) {
            log.warn(LogUtil.format("查找明细附加信息出错,订单ID:{},店铺ID:{}",
                    "CpSaleOrganizationService.queryExtByItem"), order.getId(), order.getCpCShopId());
            throw new NDSException("查找明细附加信息出错");
        }

        List<OcBOrderItemExt> itemExtList = new ArrayList<>();

        for (OcBOrderItem ocBOrderItem : orderItemList) {
            /*未拆的组合商品直接跳过*/
            if (SkuType.NO_SPLIT_COMBINE == ocBOrderItem.getProType()) {
                continue;
            }

            // 根据商品编码 查询商品款号信息
            ProductSku productSku = psRpcService.selectProductSku(ocBOrderItem.getPsCSkuEcode());
            if (Objects.isNull(productSku) || Objects.isNull(productSku.getProAttributeMap())) {
                log.warn(LogUtil.format("商品或商品属性不存在，商品明细ID:{}，SKU编码:{}",
                        "CpSaleOrganizationService.queryExtByItem"), ocBOrderItem.getId(), ocBOrderItem.getPsCSkuEcode());
                throw new NDSException(ocBOrderItem.getPsCSkuEcode() + "商品或商品属性不存在");
            }

            // 零级商品编码
            OmsProAttributeInfo attributeInfo = productSku.getProAttributeMap().get("M_DIM12_ID");
            if (Objects.isNull(attributeInfo) || StringUtils.isEmpty(attributeInfo.getEcode())) {
                log.warn(LogUtil.format("找不到商品零级，商品明细ID:{}，SKU编码:{}",
                        "CpSaleOrganizationService.queryExtByItem"), ocBOrderItem.getId(), ocBOrderItem.getPsCSkuEcode());
                throw new NDSException(ocBOrderItem.getPsCSkuEcode() + "找不到商品零级");
            }
            String categoryCode = attributeInfo.getEcode();

            /*走缓存*/
            List<CpCShopProfile> cpCShopProfileList = cpRpcService.queryShopProfileByShopId(order.getCpCShopId());
            CpCShopProfile cpCShopProfile = ListUtils.emptyIfNull(cpCShopProfileList).stream()
                    .filter(cShopProfile -> categoryCode.equals(cShopProfile.getCategoryCode()))
                    .findAny().orElse(null);
            if (Objects.isNull(cpCShopProfile)) {
                log.warn(LogUtil.format("找不到商品零级，商品明细ID:{}，SKU编码:{}",
                        "CpSaleOrganizationService.queryExtByItem"), ocBOrderItem.getId(), ocBOrderItem.getPsCSkuEcode());
                throw new NDSException("平台店铺档案品项明细找不到【" + ocBOrderItem.getPsCSkuEcode() + "】");
            }

            OcBOrderItemExt ext = new OcBOrderItemExt();
            ext.setOcBOrderId(order.getId());
            ext.setOrderItemId(ocBOrderItem.getId());

            /*零级编码*/
            ext.setMDim12Code(categoryCode);
            ext.setMDim12Name(cpCShopProfile.getCategoryName());
            /*销售一级：销售中心编码*/
            ext.setSalesCenterCode(cpCShopProfile.getSalesCenterCode());
            ext.setSalesCenterName(cpCShopProfile.getSalesCenterName());
            /*销售二级：销售部门编码*/
            ext.setSalesDepartmentCode(cpCShopProfile.getSalesDepartmentCode());
            ext.setSalesDepartmentName(cpCShopProfile.getSalesDepartmentName());
            /*销售三级：销售组编码*/
            ext.setSalesGroupCode(cpCShopProfile.getSalesGroupCode());
            ext.setSalesGroupName(cpCShopProfile.getSalesGroupName());
            if (Objects.isNull(ext.getSalesCenterCode())
                    || Objects.isNull(ext.getSalesDepartmentCode())
                    || Objects.isNull(ext.getSalesGroupCode())) {
                log.warn(LogUtil.format("找不到销售组织，商品明细ID:{}，零级编码:{}，店铺ID:{}",
                                "CpSaleOrganizationService.queryExtByItem"),
                        ocBOrderItem.getId(), categoryCode, categoryCode);
                throw new NDSException("店铺【" + order.getCpCShopTitle() + "】无【" + categoryCode + "】零级的销售组织");
            }

            assignSaleOrg(ext);
            itemExtList.add(ext);
        }

        return itemExtList;
    }


    /**
     * 查询行对应的销售组织架构
     *
     * @param itemExt
     * @return
     */
    private void assignSaleOrg(OcBOrderItemExt itemExt) {
        /*通过二级组织查询*/
        List<CpCSaleOrganizationQueryResult> organizationQueryResults =
                cpRpcService.querySaleOrganizationByDepartmentCode(itemExt.getMDim12Code(), itemExt.getSalesDepartmentCode());
        if (CollectionUtils.isEmpty(organizationQueryResults)) {
            throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售部门【"
                    + itemExt.getSalesDepartmentCode() + "】未查询到销售组织架构！");
        }

        /*过滤三级*/
        List<CpCSaleOrganizationQueryResult> resultByGroupCodeList = organizationQueryResults.stream()
                .filter(s -> itemExt.getSalesGroupCode().equals(s.getSalesGroupCode()))
                .collect(Collectors.toList());
        CpCSaleOrganizationQueryResult result;

        if (CollectionUtils.isNotEmpty(resultByGroupCodeList)) {
            /*一二三全部一致的只能有一个*/
            if (resultByGroupCodeList.size() != 1) {
                throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售组【"
                        + itemExt.getSalesGroupCode() + "】对应的销售组织架构不唯一！");
            }

            result = resultByGroupCodeList.get(0);
            if (StringUtils.isEmpty(result.getDistCodeLevelOne())) {
                throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售组【"
                        + itemExt.getSalesGroupCode() + "】对应的销售组织架构的一级分货组织编码不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistNameLevelOne())) {
                throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售组【"
                        + itemExt.getSalesGroupCode() + "】对应的销售组织架构的一级分货组织名称不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistCodeLevelTwo())) {
                throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售组【"
                        + itemExt.getSalesGroupCode() + "】对应的销售组织架构的二级分货组织编码不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistNameLevelTwo())) {
                throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售组【"
                        + itemExt.getSalesGroupCode() + "】对应的销售组织架构的二级分货组织名称不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistCodeLevelThree())) {
                throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售组【"
                        + itemExt.getSalesGroupCode() + "】对应的销售组织架构的三级分货组织编码不存在或已作废！");
            }
            if (StringUtils.isEmpty(result.getDistNameLevelThree())) {
                throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售组【"
                        + itemExt.getSalesGroupCode() + "】对应的销售组织架构的三级分货组织名称不存在或已作废！");
            }
        } else {
            /*过滤二级*/
            List<CpCSaleOrganizationQueryResult> resultByGroupIsNullList = organizationQueryResults.stream()
                    .filter(s -> StringUtils.isEmpty(s.getSalesGroupCode())).collect(Collectors.toList());
            /*如果二级没有，说明数据异常*/
            if (CollectionUtils.isEmpty(resultByGroupIsNullList)) {
                throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售组【"
                        + itemExt.getSalesGroupCode() + "】未查询到销售组织架构！");
            } else {
                /*三级不存在，一二级却有多条记录，说明数据异常*/
                if (resultByGroupIsNullList.size() != 1) {
                    throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售部门【"
                            + itemExt.getSalesDepartmentCode() + "】且销售组为空对应的销售组织架构不唯一！");
                } else {
                    result = resultByGroupIsNullList.get(0);
                    if (StringUtils.isEmpty(result.getDistCodeLevelOne())) {
                        throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售部门【"
                                + itemExt.getSalesDepartmentCode() + "】对应的销售组织架构的一级分货组织编码不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistNameLevelOne())) {
                        throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售部门【"
                                + itemExt.getSalesDepartmentCode() + "】对应的销售组织架构的一级分货组织名称不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistCodeLevelTwo())) {
                        throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售部门【"
                                + itemExt.getSalesDepartmentCode() + "】对应的销售组织架构的二级分货组织编码不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistNameLevelTwo())) {
                        throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售部门【"
                                + itemExt.getSalesDepartmentCode() + "】对应的销售组织架构的二级分货组织名称不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistCodeLevelThree())) {
                        throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售部门【"
                                + itemExt.getSalesDepartmentCode() + "】对应的销售组织架构的三级分货组织编码不存在或已作废！");
                    }
                    if (StringUtils.isEmpty(result.getDistNameLevelThree())) {
                        throw new NDSException("商品零级【" + itemExt.getMDim12Code() + "】和销售部门【"
                                + itemExt.getSalesDepartmentCode() + "】对应的销售组织架构的三级分货组织名称不存在或已作废！");
                    }
                }
            }
        }

        /*分组组织编码*/
        itemExt.setDistCodeLevelOne(result.getDistCodeLevelOne());
        itemExt.setDistCodeLevelTwo(result.getDistCodeLevelTwo());
        itemExt.setDistCodeLevelThree(result.getDistCodeLevelThree());

        /*分货组织名称*/
        itemExt.setDistNameLevelOne(result.getDistNameLevelOne());
        itemExt.setDistNameLevelTwo(result.getDistNameLevelTwo());
        itemExt.setDistNameLevelThree(result.getDistNameLevelThree());
    }


}
