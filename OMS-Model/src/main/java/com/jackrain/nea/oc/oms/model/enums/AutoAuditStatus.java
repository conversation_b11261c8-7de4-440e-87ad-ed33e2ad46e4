package com.jackrain.nea.oc.oms.model.enums;

/**
 * 自动审核状态
 *
 * @author: heliu
 * @since: 2019-05-21
 * create at : 2019-05-21 12:09  AutoAuditStatus
 */
public enum AutoAuditStatus {

    /**
     * 审核状态初始化
     */
    Audit_INIT,

    /**
     * 审核成功
     */
    Audit_SUCCESS,

    /**
     * 审核失败
     */
    Audit_FAIL,

    /**
     * 审核中
     */
    Audit_TRAN,

    /**
     * 反审核状态
     */
    Audit_ANTI;

    public int toInteger() {
        if (this == Audit_SUCCESS) {
            return 2;
        } else if (this == Audit_FAIL) {
            return 1;
        } else if (this == Audit_TRAN) {
            return 3;
        } else if (this == Audit_INIT) {
            return 0;
        } else if (this == Audit_ANTI) {
            return 5;
        } else {
            return -1;
        }
    }
}
