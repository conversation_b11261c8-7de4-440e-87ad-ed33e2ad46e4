package com.jackrain.nea.oc.oms.model.enums;


import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 入库单明细-是否匹配
 *
 * @author: 周琳胜
 * create at: 2019/3/25 13:20
 */
public enum IsMatchEnum {

    UN_MATCH("未匹配", 0),
    MATCHED("已匹配", 1);


    String key;
    Integer val;

    IsMatchEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    /**
     * 转化成QueryOrderCheckBoxResult
     *
     * @return list<QueryOrderCheckBoxResult>
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (IsMatchEnum e : IsMatchEnum.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getKey());
            o.setValue(String.valueOf(e.getVal()));
            list.add(o);
        }
        return list;
    }

}


