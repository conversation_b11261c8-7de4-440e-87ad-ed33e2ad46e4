package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ip_b_jitx_delivery_record")
@Data
@Document(index = "ip_b_jitx_delivery_record", type = "ip_b_jitx_delivery_record")
public class IpBJitxDeliveryRecord extends BaseModel {

    private static final long serialVersionUID = -5115260701775687715L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    private String tid;

    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    private String billNo;

    @JSONField(name = "APPLICATION_TIME")
    @Field(type = FieldType.Date)
    private Date applicationTime;

    @JSONField(name = "STORE_CODE")
    @Field(type = FieldType.Keyword)
    private String storeCode;

    @JSONField(name = "STORE_CODE_NAME")
    @Field(type = FieldType.Keyword)
    private String storeCodeName;

    @JSONField(name = "YY_STORE_CODE")
    @Field(type = FieldType.Keyword)
    private String yyStoreCode;

    @JSONField(name = "YY_STORE_NAME")
    @Field(type = FieldType.Long)
    private String yyStoreName;

    @JSONField(name = "VIP_STORE_ID")
    @Field(type = FieldType.Long)
    private Long vipStoreId;

    @JSONField(name = "VIP_STORE_CODE")
    @Field(type = FieldType.Keyword)
    private String vipStoreCode;

    @JSONField(name = "VIP_STORE_NAME")
    @Field(type = FieldType.Keyword)
    private String vipStoreName;

    @JSONField(name = "LOGISTIC_NUMBER")
    @Field(type = FieldType.Keyword)
    private String logisticNumber;

    @JSONField(name = "EWAYBILL_CONTENT")
    @Field(type = FieldType.Keyword)
    private String ewaybillContent;

    @JSONField(name = "CARRIER_CODE")
    @Field(type = FieldType.Keyword)
    private String carrierCode;

    @JSONField(name = "TRANS_TYPE")
    @Field(type = FieldType.Integer)
    private Integer transType;

    @JSONField(name = "TRANS_STATUS")
    @Field(type = FieldType.Integer)
    private Integer transStatus;

    @JSONField(name = "OCCUPY_STATUS")
    @Field(type = FieldType.Integer)
    private Integer occupyStatus;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}