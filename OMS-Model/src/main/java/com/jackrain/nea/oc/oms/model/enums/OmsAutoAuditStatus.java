package com.jackrain.nea.oc.oms.model.enums;

/**
 * 订单审核状态
 *
 * @author: heliu
 * @since: 2019/3/22
 * create at : 2019/3/22 9:18
 */
public enum OmsAutoAuditStatus {

    /**
     * 已审核
     */
    AUDITED,

    /**
     * 未审核
     */
    UNAUDITED;

    public int toInteger() {
        if (this == AUDITED) {
            return 1;
        } else if (this == UNAUDITED) {
            return 0;
        } else {
            return -1;
        }
    }
}
