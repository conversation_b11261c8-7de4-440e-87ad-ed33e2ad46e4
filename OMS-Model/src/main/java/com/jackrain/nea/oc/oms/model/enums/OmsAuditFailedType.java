package com.jackrain.nea.oc.oms.model.enums;

/**
 * 审核失败类型
 *
 * @Auther: 黄志优
 * @Date: 2020/11/2 09:57
 * @Description:
 */
public enum OmsAuditFailedType {

    DEFAULT_00("", 0),
    BASE_01("基础校验错误", 1),
    STRATEGY_02("策略校验错误", 2),
    BUSINESS_03("业务校验错误", 3),
    OTHER_04("其他校验错误", 4);

    /**
     * 描述
     */
    String key;

    /**
     * 策略页面值
     */
    int val;


    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }

    OmsAuditFailedType(String k, int v) {
        this.key = k;
        this.val = v;
    }


    /**
     * 根据状态值,获取状态名
     *
     * @param integer integer
     * @return String
     */
    public static String enumToStringByValue(Integer integer) {
        String s = "";
        if (integer == null) {
            return s;
        }
        for (OmsAuditFailedType e : OmsAuditFailedType.values()) {
            if (e.getVal() == integer) {
                s = e.getKey();
                break;
            }
        }
        return s;
    }


}
