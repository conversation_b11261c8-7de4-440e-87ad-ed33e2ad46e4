package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 店铺价格管控
 * @date 2022/1/14 18:33
 */
@TableName("oc_b_store_price_control")
@Data
public class OcBStorePriceControl extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    // 商品款号id,从视图获取
    @JSONField(name = "pro")
    private String  pro;

    // sku
    @JSONField(name = "sku")
    private String  sku;

/*
    // 价格类型
    @JSONField(name = "price_type")
    private String  priceType;
*/

    // 开始时间
    @JSONField(name = "start_time")
    private Date startTime;

    // 结束时间
    @JSONField(name = "end_time")
    private Date  endTime;

    // 吊牌价
    @JSONField(name = "price_list")
    private BigDecimal priceList;

    // 活动价
    @JSONField(name = "promotion_price")
    private BigDecimal promotionPrice;

    // 单件到手价
    @JSONField(name = "active_order_price")
    private BigDecimal activeOrderPrice;

    // 单间到手价折扣
    @JSONField(name = "discount")
    private String discount;

    // 前N单到手价
    @JSONField(name = "front_price")
    private BigDecimal frontPrice;

    // 前N单到手价折扣
    @JSONField(name = "front_discount")
    private String frontDiscount;

    // 上传用户
    @JSONField(name = "ename")
    private String ename;

    // 上传时间
    @JSONField(name = "upload_time")
    private Date uploadTime;

    // 店铺
    @JSONField(name = "cp_c_shop_id")
    private Integer cpCShopId;

    // 创建人用户名
    @JSONField(name = "ownerename")
    private String ownereName;

}
