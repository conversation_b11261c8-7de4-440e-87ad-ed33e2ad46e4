package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ip_b_jingdong_user")
@Data
@Document(index = "ip_b_jingdong_user", type = "ip_b_jingdong_user")
public class IpBJingdongUser extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "FULLNAME")
    @Field(type = FieldType.Keyword)
    private String fullname;

    @JSONField(name = "FULL_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String fullAddress;

    @JSONField(name = "TELEPHONE")
    @Field(type = FieldType.Keyword)
    private String telephone;

    @JSONField(name = "MOBILE")
    @Field(type = FieldType.Keyword)
    private String mobile;

    @JSONField(name = "PROVINCE")
    @Field(type = FieldType.Keyword)
    private String province;

    @JSONField(name = "CITY")
    @Field(type = FieldType.Keyword)
    private String city;

    @JSONField(name = "COUNTY")
    @Field(type = FieldType.Keyword)
    private String county;

    @JSONField(name = "IP_B_JINGDONG_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBJingdongOrderId;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;
}