package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_order_jingdong_split_task")
@Data
public class OcBOrderJingdongSplitTask extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "oc_b_order_id")
    private Long ocBOrderId;

    @JSONField(name = "STATUS")
    private Integer status;

    @JSONField(name = "times")
    private Integer times;

    @JSONField(name = "next_time")
    private Date nextTime;

    @JSONField(name = "jd_split_params")
    private String jdSplitParams;

    @JSONField(name = "jd_result")
    private String jdResult;

    @JSONField(name = "remark")
    private String remark;

    @J<PERSON><PERSON>ield(name = "ownerename")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "modifierename")
    private String modifierename;
}