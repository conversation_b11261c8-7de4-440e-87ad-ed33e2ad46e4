package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "oc_b_direct_report_order")
@Data
public class OcBDirectReportOrder extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "BILL_DATE")
    private Date billDate;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_DIS_ORG_LV2_ID")
    private Long cpCDisOrgLv2Id;

    @JSONField(name = "CP_C_DIS_ORG_LV2_CODE")
    private String cpCDisOrgLv2Code;

    @JSONField(name = "CP_C_DIS_ORG_LV2_NAME")
    private String cpCDisOrgLv2Name;

    @JSONField(name = "SG_C_SA_STORE_ID")
    private Long sgCSaStoreId;

    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @JSONField(name = "ESTIMATE_CON_TIME")
    private Date estimateConTime;

    @JSONField(name = "AUTO_RELEASE_TIME")
    private Date autoReleaseTime;

    @JSONField(name = "STATUS")
    private Integer status;

    @JSONField(name = "SUBMIT_USER_ID")
    private Long submitUserId;

    @JSONField(name = "SUBMIT_TIME")
    private Date submitTime;

    @JSONField(name = "CLOSE_USER_ID")
    private Long closeUserId;

    @JSONField(name = "close_TIME")
    private Date closeTime;

    @JSONField(name = "SG_B_SHARE_OUT_BILL_NO")
    private String sgBShareOutBillNo;

    @JSONField(name = "SG_B_STO_OUT_BILL_NO")
    private String sgBStoOutBillNo;

    @JSONField(name = "TOTAL_FULFILL_QTY")
    private BigDecimal totalFulfillQty;

    @JSONField(name = "TOTAL_QTY")
    private BigDecimal totalQty;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "TOT_WEIGHT")
    private BigDecimal totWeight;

    @JSONField(name = "REMARK")
    private String remark;
}