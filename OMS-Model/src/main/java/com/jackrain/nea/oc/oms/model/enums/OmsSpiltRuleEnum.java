package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

/**
 * @Author: 黄世新
 * @Date: 2022/8/1 下午3:09
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum  OmsSpiltRuleEnum {

    PRE_SALE(1, "预售拆单"),

    GIFT_AFTER(2, "赠品后发"),

    BUSINESS_TYPE(3, "业务类型"),

    FICTITIOUS(4, "虚拟订单拆单"),

    CARD(5, "卡单拆");

    private Integer code;

    private String message;


}
