package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-04-02
 * create at:  2019-04-02 14:00
 * 是否开启AG
 */
public enum IsOpenAGEnum {
    /**
     * 是否开启Ag
     */
    OPEN("开启AG", "Y"),
    CLOSE("未开启", "N");

    String key;
    String val;

    IsOpenAGEnum(String k, String v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }

    /**
     * 转化成QueryOrderCheckBoxResult
     *
     * @return list<QueryOrderCheckBoxResult>
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (AGStatusEnum e : AGStatusEnum.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getKey());
            o.setValue(String.valueOf(e.getVal()));
            list.add(o);
        }
        return list;
    }

}
