package com.jackrain.nea.oc.oms.model.enums;

/**
 * 渠道类型
 *
 * @author: ming.fz
 * @since: 2019-08-14
 */
public enum CpCShopChannelType {

    /**
     * 直营
     */
    DIRECT_SALES("直营", "1"),

    /**
     * 分销
     */
    DISTRIBUTION("分销", "2");

    String key;
    String val;

    CpCShopChannelType(String k, String v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }


}
