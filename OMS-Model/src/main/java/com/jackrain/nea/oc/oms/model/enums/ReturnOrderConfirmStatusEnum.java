package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: 秦雄飞
 * @time: 2022/5/22 13:22
 * @description: 退单确认状态
 */

@AllArgsConstructor
@Getter
public enum ReturnOrderConfirmStatusEnum {

    NOT_CONFIRM("0", "未确认"),

    CONFIRM("1", "已确认");

    String key;
    String value;

    public static String getValueName(String key) {
        if (key == null) {
            return NOT_CONFIRM.getValue();
        }
        for (ReturnOrderConfirmStatusEnum e : ReturnOrderConfirmStatusEnum.values()) {
            if (e.getKey().equals(key)) {
                return e.getValue();
            }
        }
        return NOT_CONFIRM.getValue();
    }
}
