package com.jackrain.nea.oc.oms.model.enums.kdzs;

import java.util.Objects;

/**
 * 物流子状态枚举类
 * <AUTHOR>
 * @date 2022/4/2 11:05
 */
public enum SubLogisticsStatusEnum {
    RECEIVE	(	"RECEIVE"	,	"接单中"	)	,
    WAIT_ACCEPT	(	"WAIT_ACCEPT"	,	"待揽收"	)	,
    ACCEPT	(	"ACCEPT"	,	"已揽收"	)	,
    TRANSPORT	(	"TRANSPORT"	,	"运输中"	)	,
    ON_THE_WAY	(	"ON_THE_WAY"	,	"在途中"	)	,
    SEND_ON	(	"SEND_ON"	,	"转单或修改地址转寄"	)	,
    ARRIVE_CITY	(	"ARRIVE_CITY"	,	"到达目的城市"	)	,
    DELIVERING	(	"DELIVERING"	,	"派件中"	)	,
    STA_INBOUND	(	"STA_INBOUND"	,	"已放入快递柜或驿站"	)	,
    AGENT_SIGN	(	"AGENT_SIGN"	,	"已代签收"	)	,
    SIGN	(	"SIGN"	,	"已签收"	)	,
    STA_SIGN	(	"STA_SIGN"	,	"从快递柜或者驿站取出"	)	,
    RETURN_SIGN	(	"RETURN_SIGN"	,	"退回签收"	)	,
    FAILED	(	"FAILED"	,	"包裹异常"	)	,
    TIMEOUT_UNSIGEN	(	"TIMEOUT_UNSIGEN"	,	"超时未签收"	)	,
    TIMEOUT_NO_UPDATE	(	"TIMEOUT_NO_UPDATE"	,	"超时未更新"	)	,
    REFUSE_SIGN	(	"REFUSE_SIGN"	,	"拒收"	)	,
    DELIVER_ABNORMAL	(	"DELIVER_ABNORMAL"	,	"派件异常"	)	,
    STA_TIMEOUT_UNSIGEN	(	"STA_TIMEOUT_UNSIGEN"	,	"快递柜或者驿站超时未取"	)	,
    CONTACT_FAIL	(	"CONTACT_FAIL"	,	"无法联系"	)	,
    OVER_AREA	(	"OVER_AREA"	,	"超区"	)	,
    RETENTION	(	"RETENTION"	,	"滞留件"	)	,
    ISSUE	(	"ISSUE"	,	"问题件"	)	,
    RETURN	(	"RETURN"	,	"退回件"	)	,
    SEND_NO_MESSAGE	(	"SEND_NO_MESSAGE"	,	"发货无信息"	)	,
    DAMAGE	(	"DAMAGE"	,	"破损"	)
    ;
    private String key;

    private String val;

    SubLogisticsStatusEnum(String key, String val) {
        this.key = key;
        this.val = val;
    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }

    public static String getValByKey(String k) {
        for (SubLogisticsStatusEnum c : SubLogisticsStatusEnum.values()) {
            if (Objects.equals(k, c.getKey())) {
                return c.getVal();
            }
        }
        return "";
    }



}
