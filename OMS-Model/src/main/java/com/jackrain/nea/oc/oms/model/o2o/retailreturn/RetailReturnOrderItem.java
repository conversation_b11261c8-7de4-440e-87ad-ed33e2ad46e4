package com.jackrain.nea.oc.oms.model.o2o.retailreturn;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Description： o2o消退单明细 -- MQ -> 云枢纽
 * Author: RESET
 * Date: Created in 2020/8/16 12:37
 * Modified By:
 */
@Data
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@Builder(toBuilder = true)
public class RetailReturnOrderItem implements Serializable {

    /**
     * 商品代码
     * 根据退货商品SKU从商品中心获取，商品中心提供获取服务
     */
    @JSONField(name = "goodsCode")
    private String goodsCode;

    /**
     * 颜色代码
     * 根据退货商品SKU从商品中心获取，商品中心提供获取服务
     */
    @JSONField(name = "colorCode")
    private String colorCode;

    /**
     * 尺码代码
     * 根据退货商品SKU从商品中心获取，商品中心提供获取服务
     */
    @JSONField(name = "sizeCode")
    private String sizeCode;

    /**
     * 数量
     * 退换货单退货明细相应商品的订单数量
     */
    @JSONField(name = "qty")
    private Integer qty;

    /**
     * 单件退货金额
     * 退换货单退货明细相应商品的单件退货金额
     */
    @JSONField(name = "amt_refund_single")
    private BigDecimal amtRefundSingle;

    /**
     * 退货金额
     * 退换货单退货明细相应商品的退货金额
     */
    @JSONField(name = "amt_refund")
    private BigDecimal amtRefund;

    /**
     * 赠品标识
     * 退换货单退货明细相应商品的赠品状态
     * 0、非赠品，1、赠品
     */
    @JSONField(name = "giftSign")
    private Integer giftSign;

}
