package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 零售发货单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-02
 */
@TableName(value = "oc_b_order")
@Data
@Document(index = "oc_b_order", type = "oc_b_order")
//@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_order", description = "零售发货单主表")
//@Builder(toBuilder = true)
public class OcBOrder extends BaseModel {

    private static final long serialVersionUID = 7447010592253398073L;
    @ApiModelProperty(value = "订单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "单据编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BILL_NO")
    private String billNo;

    @ApiModelProperty(value = "平台单号信息")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SOURCE_CODE")
    private String sourceCode;

    @ApiModelProperty(value = "下单店铺id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @ApiModelProperty(value = "店铺编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @ApiModelProperty(value = "下单店铺标题")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @ApiModelProperty(value = "发货实体仓")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @ApiModelProperty(value = "实体仓编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @ApiModelProperty(value = "实体仓名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @ApiModelProperty(value = "JITX要求发货仓ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "JITX_REQUIRES_DELIVERY_WAREHOUSE_ID")
    private Long jitxRequiresDeliveryWarehouseId;

    @ApiModelProperty(value = "JITX要求发货仓ID编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "JITX_REQUIRES_DELIVERY_WAREHOUSE_CODE")
    private String jitxRequiresDeliveryWarehouseCode;

    @ApiModelProperty(value = "JITX要求发货仓名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "JITX_REQUIRES_DELIVERY_WAREHOUSE_NAME")
    private String jitxRequiresDeliveryWarehouseName;

    @ApiModelProperty(value = "经销商id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_CUSTOMER_ID")
    private Long cpCCustomerId;

    @ApiModelProperty(value = "经销商编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_CUSTOMER_ECODE")
    private String cpCCustomerEcode;

    @ApiModelProperty(value = "经销商名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_CUSTOMER_ENAME")
    private String cpCCustomerEname;

    @ApiModelProperty(value = "下单用户")
    @Field(type = FieldType.Long)
    @JSONField(name = "USER_ID")
    private Long userId;

    @ApiModelProperty(value = "用户昵称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "USER_NICK")
    private String userNick;

    @Field(type = FieldType.Integer)
    @JSONField(name = "ORDER_TYPE")
    private Integer orderType;

    @ApiModelProperty(value = "订单状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "ORDER_STATUS")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单占单状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "OCCUPY_STATUS")
    private Integer occupyStatus;

    @ApiModelProperty(value = "订单补充信息")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SUFFIX_INFO")
    private String suffixInfo;

    @ApiModelProperty(value = "订单旗帜")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORDER_FLAG")
    private String orderFlag;

    @ApiModelProperty(value = "商品总额")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRODUCT_AMT")
    private BigDecimal productAmt;

    @ApiModelProperty(value = "商品优惠金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRODUCT_DISCOUNT_AMT")
    private BigDecimal productDiscountAmt;

    @ApiModelProperty(value = "订单优惠金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "ORDER_DISCOUNT_AMT")
    private BigDecimal orderDiscountAmt;

    @ApiModelProperty(value = "调整金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "ADJUST_AMT")
    private BigDecimal adjustAmt;

    @ApiModelProperty(value = "配送费用")
    @Field(type = FieldType.Double)
    @JSONField(name = "SHIP_AMT")
    private BigDecimal shipAmt;

    @ApiModelProperty(value = "服务费")
    @Field(type = FieldType.Double)
    @JSONField(name = "SERVICE_AMT")
    private BigDecimal serviceAmt;

    @ApiModelProperty(value = "订单总额")
    @Field(type = FieldType.Double)
    @JSONField(name = "ORDER_AMT")
    private BigDecimal orderAmt;

    @ApiModelProperty(value = "已收金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "RECEIVED_AMT")
    private BigDecimal receivedAmt;

    @ApiModelProperty(value = "代销结算金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "CONSIGN_AMT")
    private BigDecimal consignAmt;

    @ApiModelProperty(value = "代销运费")
    @Field(type = FieldType.Double)
    @JSONField(name = "CONSIGN_SHIP_AMT")
    private BigDecimal consignShipAmt;

    @ApiModelProperty(value = "应收金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_RECEIVE")
    private BigDecimal amtReceive;

    @ApiModelProperty(value = "到付代收金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "COD_AMT")
    private BigDecimal codAmt;

   /* @ApiModelProperty(value = "操作费")
    @Field(type = FieldType.Double)
    @JSONField(name = "OPERATE_AMT")
    private BigDecimal operateAmt;*/

    @ApiModelProperty(value = "应收平台金额（京东）")
    @Field(type = FieldType.Double)
    @JSONField(name = "JD_RECEIVE_AMT")
    private BigDecimal jdReceiveAmt;

    @ApiModelProperty(value = "京东结算金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "JD_SETTLE_AMT")
    private BigDecimal jdSettleAmt;

    @ApiModelProperty(value = "物流成本")
    @Field(type = FieldType.Double)
    @JSONField(name = "LOGISTICS_COST")
    private BigDecimal logisticsCost;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_INVOICE")
    private Integer isInvoice;

    @ApiModelProperty(value = "开票抬头")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "INVOICE_HEADER")
    private String invoiceHeader;

    @ApiModelProperty(value = "开票内容")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "INVOICE_CONTENT")
    private String invoiceContent;

    @ApiModelProperty(value = "是否生成开票通知")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_GENINVOICE_NOTICE")
    private Integer isGeninvoiceNotice;

    @ApiModelProperty(value = "商品重量")
    @Field(type = FieldType.Double)
    @JSONField(name = "WEIGHT")
    private BigDecimal weight;

    @ApiModelProperty(value = "商品计算重量")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_CALCWEIGHT")
    private Integer isCalcweight;

    @ApiModelProperty(value = "物流公司id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @ApiModelProperty(value = "物流公司编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @ApiModelProperty(value = "物流公司名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @ApiModelProperty(value = "物流编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "EXPRESSCODE")
    private String expresscode;

    @ApiModelProperty(value = "下单时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "ORDER_DATE")
    private Date orderDate;

    @ApiModelProperty(value = "交易结束时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "END_TIME")
    private Date endTime;

  /*  @ApiModelProperty(value = "预计发货时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "SEND_TIME")
    private Date sendTime;*/

    @ApiModelProperty(value = "付款时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "PAY_TIME")
    private Date payTime;

    @ApiModelProperty(value = "审核时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "AUDIT_TIME")
    private Date auditTime;

    @ApiModelProperty(value = "买家邮箱")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUYER_EMAIL")
    private String buyerEmail;

    @ApiModelProperty(value = "收货人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_NAME")
    private String receiverName;

    @ApiModelProperty(value = "收货人的手机号码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_MOBILE")
    private String receiverMobile;

    @ApiModelProperty(value = "收货人的电话号码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_PHONE")
    private String receiverPhone;

    @ApiModelProperty(value = "买家省id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    private Long cpCRegionProvinceId;

    @ApiModelProperty(value = "买家省编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    private String cpCRegionProvinceEcode;

    @ApiModelProperty(value = "买家省名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @ApiModelProperty(value = "买家市id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_REGION_CITY_ID")
    private Long cpCRegionCityId;

    @ApiModelProperty(value = "买家市编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_CITY_ECODE")
    private String cpCRegionCityEcode;

    @ApiModelProperty(value = "买家市名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_CITY_ENAME")
    private String cpCRegionCityEname;

    @ApiModelProperty(value = "买家区id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_REGION_AREA_ID")
    private Long cpCRegionAreaId;

    @ApiModelProperty(value = "买家区编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_AREA_ECODE")
    private String cpCRegionAreaEcode;

    @ApiModelProperty(value = "买家区名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    private String cpCRegionAreaEname;

    @ApiModelProperty(value = "买家街道/乡镇名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_TOWN_ENAME")
    private String cpCRegionTownEname;

    @ApiModelProperty(value = "买家收货详细地址")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;

    @ApiModelProperty(value = "平台省名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PLATFORM_PROVINCE")
    private String platformProvince;

    @ApiModelProperty(value = "平台市名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PLATFORM_CITY")
    private String platformCity;

    @ApiModelProperty(value = "平台区名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PLATFORM_AREA")
    private String platformArea;

    @ApiModelProperty(value = "收货人的邮编")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_ZIP")
    private String receiverZip;

    @ApiModelProperty(value = "买家邮件地址")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_EMAIL")
    private String receiverEmail;

    @ApiModelProperty(value = "是否取消合并")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_CANCEL_MERGE")
    private Integer isCancelMerge;

    @ApiModelProperty(value = "是否合并订单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_MERGE")
    private Integer isMerge;

    @ApiModelProperty(value = "是否拆分订单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_SPLIT")
    private Integer isSplit;

//    @ApiModelProperty(value = "仓储状态（拣货中，已打印，已装箱）")
//    @Field(type = FieldType.Integer)
//    @JSONField(name = "WMS_STATUS")
//    private Integer wmsStatus;

    @ApiModelProperty(value = "是否已经拦截")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_INTERECEPT")
    private Integer isInterecept;

    @ApiModelProperty(value = "是否退款中")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_INRETURNING")
    private Integer isInreturning;

    @ApiModelProperty(value = "销售员编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "SALESMAN_ID")
    private Long salesmanId;

    @ApiModelProperty(value = "销售员名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALESMAN_NAME")
    private String salesmanName;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "ALL_SKU")
    private String allSku;

    @Field(type = FieldType.Integer)
    @JSONField(name = "PAY_TYPE")
    private Integer payType;

    @ApiModelProperty(value = "买家留言")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUYER_MESSAGE")
    private String buyerMessage;

    @ApiModelProperty(value = "订单来源")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORDER_SOURCE")
    private String orderSource;

    @ApiModelProperty(value = "原始订单号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ORIG_ORDER_ID")
    private Long origOrderId;

    @ApiModelProperty(value = "原始退货单号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ORIG_RETURN_ORDER_ID")
    private Long origReturnOrderId;

    @ApiModelProperty(value = "是否有赠品")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_HASGIFT")
    private Integer isHasgift;

    @ApiModelProperty(value = "商品数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_ALL")
    private BigDecimal qtyAll;

    @ApiModelProperty(value = "sku条数")
    @Field(type = FieldType.Double)
    @JSONField(name = "SKU_KIND_QTY")
    private BigDecimal skuKindQty;

    @ApiModelProperty(value = "系统备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SYSREMARK")
    private String sysremark;

    @ApiModelProperty(value = "内部备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "INSIDE_REMARK")
    private String insideRemark;

    @ApiModelProperty(value = "卖家备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SELLER_MEMO")
    private String sellerMemo;

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    @ApiModelProperty(value = "合并后发货的订单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MERGE_SOURCE_CODE")
    private String mergeSourceCode;

    @ApiModelProperty(value = "平台ID")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PLATFORM")
    private Integer platform;

    @ApiModelProperty(value = "合并新单号")
    @Field(type = FieldType.Long)
    @JSONField(name = "MERGE_ORDER_ID")
    private Long mergeOrderId;

    @ApiModelProperty(value = "拆分原单单号")
    @Field(type = FieldType.Long)
    @JSONField(name = "SPLIT_ORDER_ID")
    private Long splitOrderId;

   /* @ApiModelProperty(value = "是否生成调拨零售")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_TODRP")
    private Integer isTodrp;*/

    @ApiModelProperty(value = "扫描出库时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "SCAN_TIME")
    private Date scanTime;

    /*   @ApiModelProperty(value = "是否已给物流")
       @Field(type = FieldType.Integer)
       @JSONField(name = "IS_GIVE_LOGISTIC")
       private Integer isGiveLogistic;
   */
    @ApiModelProperty(value = "出库状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "OUT_STATUS")
    private Integer outStatus;

    @ApiModelProperty(value = "初始平台单号（确定唯一）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "订单标签")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORDER_TAG")
    private String orderTag;

    @Field(type = FieldType.Integer)
    @JSONField(name = "WMS_CANCEL_STATUS")
    private Integer wmsCancelStatus;

    @Field(type = FieldType.Integer)
    @JSONField(name = "RETURN_STATUS")
    private Integer returnStatus;

    @ApiModelProperty(value = "淘宝店铺编号（星盘使用）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TB_STORECODE")
    private String tbStorecode;

    @ApiModelProperty(value = "退款审核状态（AG使用）")
    @Field(type = FieldType.Integer)
    @JSONField(name = "REFUND_CONFIRM_STATUS")
    private Integer refundConfirmStatus;

    @ApiModelProperty(value = "自动审核状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "AUTO_AUDIT_STATUS")
    private Integer autoAuditStatus;

    /*@ApiModelProperty(value = "包含预售商品")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_HASPRESALESKU")
    private Integer isHaspresalesku;*/

    @ApiModelProperty(value = "京仓订单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_JCORDER")
    private Integer isJcorder;

   /* @ApiModelProperty(value = "实缺标记")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_LACKSTOCK")
    private Integer isLackstock;*/

    @ApiModelProperty(value = "双11预售状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "DOUBLE11_PRESALE_STATUS")
    private Integer double11PresaleStatus;

    @ApiModelProperty(value = "配货时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "DISTRIBUTION_TIME")
    private Date distributionTime;

    @ApiModelProperty(value = "是否虚拟订单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_INVENTED")
    private Integer isInvented;

    @ApiModelProperty(value = "是否组合订单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_COMBINATION")
    private Integer isCombination;

   /* @ApiModelProperty(value = "系统预售状态，非双11")
    @Field(type = FieldType.Integer)
    @JSONField(name = "SYS_PRESALE_STATUS")
    private Integer sysPresaleStatus;*/

    @ApiModelProperty(value = "是否催发货")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_OUT_URGENCY")
    private Integer isOutUrgency;

//    @ApiModelProperty(value = "下单店铺是否代销")
//    @Field(type = FieldType.Integer)
//    @JSONField(name = "IS_SHOP_COMMISSION")
//    private Integer isShopCommission;

    @ApiModelProperty(value = "是否有工单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_HAS_TICKET")
    private Integer isHasTicket;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

  /*  @ApiModelProperty(value = "是否插入核销流水")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_WRITEOFF")
    private Integer isWriteoff;*/

//    @ApiModelProperty(value = "支付宝交易账号")
//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "ALIPAY_NO")
//    private String alipayNo;

    @ApiModelProperty(value = "买家支付账号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUYER_ALIPAY_NO")
    private String buyerAlipayNo;

    @ApiModelProperty(value = "卖家昵称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_SELLER_NICK")
    private String cpCShopSellerNick;

  /*  @ApiModelProperty(value = "仓库类型(0:本地仓,1:菜鸟仓,2:京仓)")
    @Field(type = FieldType.Integer)
    @JSONField(name = "WAREHOUSE_TYPE")
    private Integer warehouseType;*/

    @ApiModelProperty(value = "强制平台发货标记(0,失败，1成功，2初始化)")
    @Field(type = FieldType.Long)
    @JSONField(name = "IS_FORCE")
    private Long isForce;

    @ApiModelProperty(value = "传wms是否超过5次 1:是,0:否")
    @Field(type = FieldType.Long)
    @JSONField(name = "IS_OVERFIVE")
    private Long isOverfive;

    @ApiModelProperty(value = "是否换货未入库 1:是,0:否")
    @Field(type = FieldType.Long)
    @JSONField(name = "IS_EXCHANGE_NO_IN")
    private Long isExchangeNoIn;

    /*@ApiModelProperty(value = "待传结算标志（0:默认，1:待传，2：已传，3:失败）")
    @Field(type = FieldType.Long)
    @JSONField(name = "TO_SETTLE_STATUS")
    private Long toSettleStatus;*/

    @ApiModelProperty(value = "多包裹标识【0：非多包裹；1：多包裹】")
    @Field(type = FieldType.Long)
    @JSONField(name = "IS_MULTI_PACK")
    private Long isMultiPack;

   /* @ApiModelProperty(value = "代销资金处理重试标识")
    @Field(type = FieldType.Long)
    @JSONField(name = "CONSIGN_AMT_RETRY_FLAG")
    private Long consignAmtRetryFlag;

    @ApiModelProperty(value = "代销资金处理状态（0 已处理  1 未处理)")
    @Field(type = FieldType.Long)
    @JSONField(name = "CONSIGN_AMT_STATUS")
    private Long consignAmtStatus;*/

    @ApiModelProperty(value = "平台发货补偿失败次数")
    @Field(type = FieldType.Long)
    @JSONField(name = "MAKEUP_FAIL_NUM")
    private Long makeupFailNum;

    @ApiModelProperty(value = "锁单状态 0-待锁单 1-已锁定 2-锁定成功")
    @Field(type = FieldType.Long)
    @JSONField(name = "LOCK_STATUS")
    private Long lockStatus;

    @ApiModelProperty(value = "POS订单的ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "POS_BILL_ID")
    private Long posBillId;

    @ApiModelProperty(value = "平台优惠金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_PLAT_DISCOUNT")
    private BigDecimal amtPlatDiscount;

/*    @ApiModelProperty(value = "传sap次数")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_SAP_FAIL")
    private BigDecimal qtySapFail;*/

/*    @ApiModelProperty(value = "传对账中心，交易失败，2表示成功，3标识失败。")
    @Field(type = FieldType.Double)
    @JSONField(name = "STATUS_TO_TRAD_FAIL")
    private BigDecimal statusToTradFail;

    @ApiModelProperty(value = "传对账中心，交易完成，2表示成功，3标识失败。")
    @Field(type = FieldType.Double)
    @JSONField(name = "STATUS_TO_TRAD_SUC")
    private BigDecimal statusToTradSuc;*/

    @ApiModelProperty(value = "强制平台发货失败原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "FORCE_SEND_FAIL_REASON")
    private String forceSendFailReason;

    @ApiModelProperty(value = "标签 ’价‘")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PRICE_LABEL")
    private String priceLabel;

    @ApiModelProperty(value = "阶段付款状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STATUS_PAY_STEP")
    private String statusPayStep;

    @ApiModelProperty(value = "标签名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LABEL_ENAME")
    private String cpCLabelEname;

    @ApiModelProperty(value = "标签内容")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LABEL_CONTENT")
    private String cpCLabelContent;

    @Field(type = FieldType.Integer)
    @JSONField(name = "INVOICE_STATUS")
    private Integer invoiceStatus;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "OC_B_INVOICE_NOTICE_ID")
    private String ocBInvoiceNoticeId;

 /*   @ApiModelProperty(value = "刷单类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "SCALPING_TYPE")
    private Integer scalpingType;*/

    @ApiModelProperty(value = "预售付定金时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "PRESALE_DEPOSIT_TIME")
    private Date presaleDepositTime;

    @ApiModelProperty(value = "标签id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_LABEL_ID")
    private Long cpCLabelId;

    @ApiModelProperty(value = "出库单ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "SG_B_OUT_BILL_ID")
    private Long sgBOutBillId;

    @ApiModelProperty(value = "出库单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SG_B_OUT_BILL_NO")
    private String sgBOutBillNo;

    @ApiModelProperty(value = "出库类型 0 电商出库  1 大货出库 默认0")
    @Field(type = FieldType.Integer)
    @JSONField(name = "OUT_TYPE")
    private Integer outType;

    /*@ApiModelProperty(value = "是否爆品下沉 0 否 ，1是  默认0")
    @Field(type = FieldType.Long)
    @JSONField(name = "IS_HOT_SINK")
    private Long isHotSink;*/
//
//    @ApiModelProperty(value = "菜鸟仓库作业状态")
//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "CAINIAO_WH_STATUS")
//    private String cainiaoWhStatus;

   /* @ApiModelProperty(value = "第三方仓库类型 0 菜鸟仓库 1 京东仓 2 其它")
    @Field(type = FieldType.Integer)
    @JSONField(name = "THIRD_WAREHOUSE_TYPE")
    private Integer thirdWarehouseType;
    @ApiModelProperty(value = "材料类型")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MATERIAL_TYPE")
    private String materialType;*/

    @ApiModelProperty(value = "付款状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PAY_STATUS")
    private Integer payStatus;

    @ApiModelProperty(value = "平台订单状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PLATFORM_STATUS")
    private String platformStatus;

    @ApiModelProperty(value = "最晚发货时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "DELIVERYTIME")
    private Date deliverytime;

    @ApiModelProperty(value = "预计送达时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "EXPECTED_DELIVERYTIME")
    private Date expectedDeliverytime;

    @ApiModelProperty(value = "配送方式")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DELIVERY_METHOD")
    private String deliveryMethod;

    @ApiModelProperty(value = "“时”标签")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "LABEL_TIME")
    private String labelTime;

    @ApiModelProperty(value = "退款状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "REFUND_STATUS")
    private Integer refundStatus;

    @ApiModelProperty(value = "取消状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CANCEL_STATUS")
    private String cancelStatus;

    @ApiModelProperty(value = "红包")
    @Field(type = FieldType.Double)
    @JSONField(name = "RED_ENVELOPER")
    private BigDecimal redEnveloper;

    @ApiModelProperty(value = "内部便签")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "INTERNAL_MEMO")
    private String internalMemo;

    @ApiModelProperty(value = "退款金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "REFUND_FEE")
    private BigDecimal refundFee;

//    @ApiModelProperty(value = "订单净重")
//    @Field(type = FieldType.Double)
//    @JSONField(name = "ORDER_WEIGHT")
//    private BigDecimal orderWeight;

//    @ApiModelProperty(value = "订单毛重")
//    @Field(type = FieldType.Double)
//    @JSONField(name = "ORDER_GROSS")
//    private BigDecimal orderGross;

    @ApiModelProperty(value = "单品数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "SINGLE_QUANTITY")
    private BigDecimal singleQuantity;

//    @ApiModelProperty(value = "单品条数")
//    @Field(type = FieldType.Double)
//    @JSONField(name = "SINGLE_NUMBER")
//    private BigDecimal singleNumber;

    @ApiModelProperty(value = "WMS单据编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "WMS_BILL_NO")
    private String wmsBillNo;

    @ApiModelProperty(value = "平台补发单唯一标识")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REISSUE_NOTE")
    private String reissueNote;

    @ApiModelProperty(value = "门店id(门店自提时赋值)")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TARGET_CODE")
    private String targetCode;

    @ApiModelProperty(value = "审核失败类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "AUDIT_FAILED_TYPE")
    private Integer auditFailedType;

    @ApiModelProperty(value = "审核失败原因")
    @JSONField(name = "AUDIT_FAILED_REASON")
    @Field(type = FieldType.Keyword)
    private String auditFailedReason;

    @ApiModelProperty(value = "是否o2o订单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_O2O_ORDER")
    private Integer isO2oOrder;

    @ApiModelProperty(value = "预售类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PRESELL_TYPE")
    private Integer presellType;

    @ApiModelProperty(value = "预售方式")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PRESELL_WAY")
    private Integer presellWay;

    @ApiModelProperty(value = "复制原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "COPY_REASON")
    private String copyReason;

//    @ApiModelProperty(value = "直播平台：1-快手,2-抖音,3-蘑菇街,4-陌陌,5-淘宝")
//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "LIVE_PLATFORM")
//    private String livePlatform;

    @ApiModelProperty(value = "直播标识：1-直播单，2-非直播单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "LIVE_FLAG")
    private Integer liveFlag;



    @ApiModelProperty(value = "主播ID")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ANCHOR_ID")
    private String anchorId;

    @ApiModelProperty(value = "主播昵称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ANCHOR_NAME")
    private String anchorName;

    @ApiModelProperty(value = "是否缺货拆单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_OUT_STOCK_SPLIT")
    private Integer isOutStockSplit;

    @ApiModelProperty(value = "是否紧急发货：1是，0否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_DELIVERY_URGENT")
    private Integer isDeliveryUrgent;

    @ApiModelProperty(value = "复制次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "COPY_NUM")
    private Integer copyNum;

    @ApiModelProperty(value = "是否是丢单复制：1是，0否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_LOSE_COPY_ORDER")
    private Integer isLoseCopyOrder;

    @ApiModelProperty(value = "是否复制订单：1是，0否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_COPY_ORDER")
    private Integer isCopyOrder;

    @ApiModelProperty(value = "是否补发订单：1是，0否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_RESET_SHIP")
    private Integer isResetShip;

    @ApiModelProperty(value = "是否改单：1是，0否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_MODIFIED_ORDER")
    private Integer isModifiedOrder;

    @ApiModelProperty(value = "唯品会改仓工单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "VIP_WORKFLOW_SN")
    private String vipWorkflowSn;

    @ApiModelProperty(value = "是否唯品会改仓")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_VIP_UPDATE_WAREHOUSE")
    private Integer isVipUpdateWarehouse;

    @ApiModelProperty(value = "平台换货单号")
    @Field(type = FieldType.Long)
    @JSONField(name = "DISPUTE_ID")
    private Long disputeId;

    @ApiModelProperty(value = "HOLD单释放时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "HOLD_RELEASE_TIME")
    private Date holdReleaseTime;

    @ApiModelProperty(value = "是否历史订单 Y:是  N:否")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_HISTORY")
    private String isHistory;

   /* @ApiModelProperty(value = "是否包含带有轻供属性的商品明细 1=是；0=否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "HAS_LIGHT_SUPPLY_PROD")
    private Integer hasLightSupplyProd;*/

    @ApiModelProperty(value = "拆单次数")
    @Field(type = FieldType.Long)
    @JSONField(name = "QTY_SPLIT")
    private Long qtySplit;

    @ApiModelProperty(value = "是否促销订单：1是，0否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_PROM_ORDER")
    private Integer isPromOrder;

//    @ApiModelProperty(value = "是否实缺订单：1是，0否")
//    @Field(type = FieldType.Integer)
//    @JSONField(name = "IS_REAL_LACKSTOCK")
//    private Integer isRealLackstock;

    @ApiModelProperty(value = "是否包含额外退款 0:不包含 1:包含")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_EXTRA")
    private Integer isExtra;

    @ApiModelProperty(value = "是否同城购：0:否 1:是")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_SAME_CITY_PURCHASE")
    private Integer isSameCityPurchase;

/*    @ApiModelProperty(value = "门店接单状态: 1:已路由 2:已接单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "STORE_DELIVERY_STATUS")
    private Integer storeDeliveryStatus;*/

    /* @ApiModelProperty(value = "发货门店ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "DELIVERY_STORE_ID")
    private Long deliveryStoreId;

    @ApiModelProperty(value = "发货门店编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DELIVERY_STORE_CODE")
    private String deliveryStoreCode;

    @ApiModelProperty(value = "发货门店名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DELIVERY_STORE_NAME")
    private String deliveryStoreName;

    @ApiModelProperty(value = "结算供应商编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_SUPPLIER_CODE")
    private String settleSupplierCode;

    @ApiModelProperty(value = "结算供应商名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_SUPPLIER_NAME")
    private String settleSupplierName;

    @ApiModelProperty(value = "结算组织编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_ORGANIZATION_CODE")
    private String settleOrganizationCode;

    @ApiModelProperty(value = "结算组织名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_ORGANIZATION_NAME")
    private String settleOrganizationName;

    @ApiModelProperty(value = "线下中台订单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OFFLINE_ORDER_CODE")
    private String offlineOrderCode;
    */



    /*@ApiModelProperty(value = "是否WOS催：1是，0否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_WOS_URGE")
    private Integer isWosUrge;

    @ApiModelProperty(value = "是否WOS截：1是，0否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_WOS_CUT")
    private Integer isWosCut;*/

    @ApiModelProperty(value = "POS系统订单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "POS_ORDERNO")
    private String posOrderno;

    @ApiModelProperty(value = "是否是根据款号或SKU进行拆单的订单，为1时此订单不参与合并：0否 1是")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_SPILT_SKU_STYLE")
    private Integer isSpiltSkuStyle;

    @ApiModelProperty(value = "缺货错误合单次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "MERGE_ERROR_NUM")
    private Integer mergeErrorNum;

    /*@ApiModelProperty(value = "会员等级")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MEMBER_LEVEL")
    private String memberLevel;*/

    @ApiModelProperty(value = "传SAP状态：0未传，1传中，2传成功，3传失败")
    @Field(type = FieldType.Integer)
    @JSONField(name = "TO_SAP_STATUS")
    private Integer toSapStatus;

//    @ApiModelProperty(value = "是否传SAP 0否 1是")
//    @Field(type = FieldType.Integer)
//    @JSONField(name = "IS_TO_SAP")
//    private Integer isToSap;

    @ApiModelProperty(value = "拆单状态：0:未处理 1:已处理")
    @Field(type = FieldType.Integer)
    @JSONField(name = "SPLIT_STATUS")
    private Integer splitStatus;

    @ApiModelProperty(value = "拆单原因：0.其他 1.部分发货拆单 2.虚拟拆单 3.缺货拆单 4.按SKU拆单" +
            " 5.按SPU拆单 6.按品牌组拆 7.按性别拆 8.手工拆单 9.O2O拆单 10.按SKU、SPU拆单后的另一个字段")
    @Field(type = FieldType.Integer)
    @JSONField(name = "SPLIT_REASON")
    private Integer splitReason;

    @ApiModelProperty(value = "周期购商品策略ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @ApiModelProperty(value = "数字类型备用字段2")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @ApiModelProperty(value = "数字类型备用字段3")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @ApiModelProperty(value = "数字类型备用字段4")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @ApiModelProperty(value = "数字类型备用字段5")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @ApiModelProperty(value = "价格备用字段1")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "价格备用字段3")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @ApiModelProperty(value = "价格备用字段4")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @ApiModelProperty(value = "价格备用字段5")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @ApiModelProperty(value = "文本备用字段1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @ApiModelProperty(value = "周期购原商品编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @ApiModelProperty(value = "文本备用字段3")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @ApiModelProperty(value = "文本备用字段4")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @ApiModelProperty(value = "文本备用字段5")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @ApiModelProperty(value = "wms撤回次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "WMS_CANCEL_NUMBER")
    private Integer wmsCancelNumber;

    @ApiModelProperty(value = "反审核标识")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_AUDIT_TAG")
    private String reserveAuditTag;

    //数据库 没用到的字段 用来做反审核中标记
    @ApiModelProperty(value = "反审核中标记")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REVERSE_AUDIT_TYPE")
    private String reverseAuditType;

    @ApiModelProperty(value = "'订单合单加密信息")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORDER_ENCRYPTION_CODE")
    private String orderEncryptionCode;

    // 直播主体
    @ApiModelProperty(value = "直播主体")
    @Field(type = FieldType.Long)
    @JSONField(name = "AC_F_MANAGE_ID")
    private Long acFManageId;

    // 直播主体 经营主体ecode
//    @ApiModelProperty(value = "直播主体 经营主体ecode")
//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "AC_F_MANAGE_ECODE")
//    private String acFManageEcode;

    // 直播主体 经营主体ename
//    @ApiModelProperty(value = "直播主体 经营主体ename")
//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "AC_F_MANAGE_ENAME")
//    private String acFManageEname;

    // 配合主体
//    @ApiModelProperty(value = "配合主体")
//    @Field(type = FieldType.Long)
//    @JSONField(name = "COOPERATE_ID")
//    private Long cooperateId;

//    // 配合主体 经营主体ecode
//    @ApiModelProperty(value = "配合主体 经营主体ename")
//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "COOPERATE_ECODE")
//    private String cooperateEcode;

    // 配合主体 经营主体ename
//    @ApiModelProperty(value = "直播主体")
//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "COOPERATE_ENAME")
//    private String cooperateEname;

    // 直播场次
    @ApiModelProperty(value = "直播场次")
    @Field(type = FieldType.Integer)
    @JSONField(name = "LIVE_EVENTS")
    private Integer liveEvents;

    @ApiModelProperty(value = "订单折扣")
    @Field(type = FieldType.Double)
    @JSONField(name = "ORDER_DISCOUNT")
    private BigDecimal orderDiscount;

    @ApiModelProperty(value = "建议预打包状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SUGGEST_PREPACKAGE_STATUS")
    private String suggestPrepackageStatus;
    @ApiModelProperty(value = "实际预打包状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ACTUAL_PREPACKAGE_STATUS")
    private String actualPrepackageStatus;
    @ApiModelProperty(value = "建议预下沉状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SUGGEST_PRESINK_STATUS")
    private String suggestPresinkStatus;
    @ApiModelProperty(value = "实际预下沉状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ACTUAL_PRESINK_STATUS")
    private String actualPresinkStatus;

    @ApiModelProperty(value = "合包码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MERGED_CODE")
    private String mergedCode;

    @ApiModelProperty(value = "合包单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MERGED_SN")
    private String mergedSn;

    @ApiModelProperty(value = "jitx要求合包")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "JITX_REQUIRES_MERGE")
    private String jitxRequiresMerge;

    @ApiModelProperty(value = "JITX合包发货平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "JITX_MERGED_DELIVERY_SN")
    private String jitxMergedDeliverySn;

    @ApiModelProperty(value = "JITX是否可发货，0=可发货 1=不可发货")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_FORBIDDEN_DELIVERY")
    private Integer isForbiddenDelivery;

    // 自定义拆单原因id
    @ApiModelProperty(value = "自定义拆单原因id")
    @Field(type = FieldType.Long)
    @JSONField(name = "SPLIT_REASON_ID")
    private Long splitReasonId;

    // 自定义拆单原因
    @ApiModelProperty(value = "自定义拆单原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CUSTOM_REASON")
    private String customReason;


    // 预售类型 新
    @ApiModelProperty(value = "预售类型新")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ADVANCE_TYPE")
    private String advanceType;


    @ApiModelProperty(value = "卡单状态 1 是卡单 0是默认不卡")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_DETENTION")
    private Integer isDetention;

    @ApiModelProperty(value = "用卡订单使用的本金")
    @JSONField(name = "BASIC_PRICE_USED")
    @Field(type = FieldType.Double)
    private BigDecimal basicPriceUsed;

    @ApiModelProperty(value = "用卡订单使用的权益金")
    @JSONField(name = "EXPAND_PRICE_USED")
    @Field(type = FieldType.Double)
    private BigDecimal expandPriceUsed;

//    /**
//     * 0-未传DRP；1-传DRP中；2-传DRP成功；3-传DRP失败
//     */
//    @ApiModelProperty(value = "传DRP状态")
//    @JSONField(name = "TO_DRP_STATUS")
//    @Field(type = FieldType.Keyword)
//    private String toDrpStatus;

//    @ApiModelProperty(value = "传DRP次数")
//    @JSONField(name = "TO_DRP_COUNT")
//    @Field(type = FieldType.Integer)
//    private Integer toDrpCount;

//    @ApiModelProperty(value = "传DRP失败原因")
//    @JSONField(name = "TO_DRP_FAILED_REASON")
//    @Field(type = FieldType.Keyword)
//    private String toDrpFailedReason;

    @ApiModelProperty(value = "逻辑占用单编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STO_OUT_BILL_NO")
    private String stoOutBillNo;
    @ApiModelProperty(value = "淘宝收货人信息加密串")
    @JSONField(name = "OAID")
    @Field(type = FieldType.Keyword)
    private String oaid;

    @ApiModelProperty(value = "平台发货时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "PLATFORM_DELIVERY_TIME")
    private Date platformDeliveryTime;

    @ApiModelProperty(value = "仓库发货时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "WAREHOUSE_DELIVERY_TIME")
    private Date warehouseDeliveryTime;

//    @ApiModelProperty(value = "传第三方错误类型")
//    @JSONField(name = "THIRD_PARTY_FAIL_STATUS")
//    @Field(type = FieldType.Keyword)
//    private String thirdPartyFailStatus;


    // HOLD单原因
    @ApiModelProperty(value = "hold单原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "HOLD_REASON")
    private String holdReason;

    // 卡单原因
    @ApiModelProperty(value = "卡单原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DETENTION_REASON")
    private String detentionReason;

    // 自定义标签档案
    @ApiModelProperty(value = "自定义标签档案")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ST_C_CUSTOM_LABEL_ID")
    private String stCCustomLabelId;

    // 自定义标签档案
    @ApiModelProperty(value = "自定义标签档案")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ST_C_CUSTOM_LABEL_ENAME")
    private String stCCustomLabelEname;
    @JSONField(name = "IS_STORE_DELIVERY")
    @Field(type = FieldType.Integer)
    private Integer isStoreDelivery;

    @ApiModelProperty(value = "是否已通知发货方入库：0-否，1-是")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_NOTICE_DELIVERY")
    private String isNoticeDelivery;

    @ApiModelProperty(value = "发货方入库状态：0-未入库，1-已入库")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DELIVERY_IN_STATUS")
    private String deliveryInStatus;

//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "GW_VIP_CODE")
//    private String gwVipCode;

//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "GW_VIP_MOBILE")
//    private String gwVipMobile;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "GW_SOURCE_CODE")
    private String gwSourceCode;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "GW_SOURCE_GROUP")
    private String gwSourceGroup;


//    @ApiModelProperty(value = "驿客券码")
//    @Field(type = FieldType.Keyword)
//    @JSONField(name = "USE_COUPON_NO")
//    private String useCouponNo;


    @ApiModelProperty(value = "预计发货日期")
    @Field(type = FieldType.Long)
    @JSONField(name = "ESTIMATE_CON_TIME")
    private Date estimateConTime;

    @ApiModelProperty(value = "HOLD单释放原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "HOLD_RELEASE_REASON")
    private String holdReleaseReason;

    @ApiModelProperty(value = "HOLD单释放人")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "HOLD_RELEASE_NAME")
    private String holdReleaseName;


    @ApiModelProperty(value = "HOLD单释放时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "HOLD_RELEASE_DATE")
    private Date holdReleaseDate;

    /*@ApiModelProperty(value = "wing将出库信息推送wms的时间", required = true)
    @Field(type = FieldType.Long)
    @JSONField(name = "out_wing_to_wms_time")
    private Date outWingToWmsTime;*/

    @ApiModelProperty(value = "wms接收出库信息的时间", required = true)
    @Field(type = FieldType.Long)
    @JSONField(name = "OUT_WMS_RECEIVE_TIME")
    private Date outWmsReceiveTime;

    /*@ApiModelProperty(value = "wms实际出库后告知wing的时间", required = true)
    @Field(type = FieldType.Long)
    @JSONField(name = "OUT_WMS_OUT_TIME")
    private Date outWmsOutTime;

    @ApiModelProperty(value = "wing接收实际出库信息并通知中台的时间", required = true)
    @JSONField(name = "OUT_WING_TO_SG_TIME")
    @Field(type = FieldType.Long)
    private Date outWingToSgTime;*/

    @ApiModelProperty(value = "物流状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "LOGISTICS_STATUS")
    private String logisticsStatus;


    @ApiModelProperty(value = "卡单时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "DETENTION_DATE")
    private Date detentionDate;


    @ApiModelProperty(value = "卡单释放时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "DETENTION_RELEASE_DATE")
    private Date detentionReleaseDate;


    @ApiModelProperty(value = "首次缺货时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "STOCK_OCCUPY_DATE")
    private Date stockOccupyDate;


    @ApiModelProperty(value = "占库存成功时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "OCCUPY_SUCCESS_DATE")
    private Date occupySuccessDate;


    @ApiModelProperty(value = "Hold单时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "HOLD_DATE")
    private Date holdDate;

    @ApiModelProperty(value = "审核类型 auto:自动 ， manual ： 手动")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "AUDIT_TYPE")
    private String auditType;


    @ApiModelProperty(value = "审核成功时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "AUDIT_SUCCESS_DATE")
    private Date auditSuccessDate;


    @ApiModelProperty(value = "取消时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "CANCEL_DATE")
    private Date cancelDate;


    @ApiModelProperty(value = "反审核时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "EXAMINE_ORDER_DATE")
    private Date examineOrderDate;


//    @ApiModelProperty(value = "物流-揽收时间")
//    @Field(type = FieldType.Long)
//    @JSONField(name = "ONROAD_DATE")
//    private Date onroadDate;


//    @ApiModelProperty(value = "物流-第一次中转时间")
//    @Field(type = FieldType.Long)
//    @JSONField(name = "ONROAD_TRANSFER_DATE")
//    private Date onroadTransferDate;


//    @ApiModelProperty(value = "物流-签收时间")
//    @Field(type = FieldType.Long)
//    @JSONField(name = "ARRIVED_DATE")
//    private Date arrivedDate;


    @ApiModelProperty(value = "物流-签收时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "SAP_ARRIVED_DATE")
    private Date sapArrivedDate;

    @ApiModelProperty(value = "订单来源平台编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORDER_SOURCE_PLATFORM_ECODE")
    private String orderSourcePlatformEcode;

    @ApiModelProperty(value = "来源单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SOURCE_BILL_NO")
    private String sourceBillNo;

    @ApiModelProperty(value = "平台业务类型")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUSINESS_TYPE")
    private String businessType;

    @ApiModelProperty(value = "订单业务类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "BUSINESS_TYPE_ID")
    private Long businessTypeId;

    @ApiModelProperty(value = "订单业务类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "BUSINESS_TYPE_NAME")
    private String businessTypeName;


    @ApiModelProperty(value = "订单业务类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "BUSINESS_TYPE_CODE")
    private String businessTypeCode;


    @ApiModelProperty(value = "传奶卡系统状态: 0 待解冻，1已解冻，2解冻失败")
    @Field(type = FieldType.Integer)
    @JSONField(name = "TO_NAIKA_STATUS")
    private Integer toNaikaStatus;

    @ApiModelProperty(value = "是否需要签收")
    @JSONField(name = "WHETHER_NEED_RECEIPT")
    @Field(type = FieldType.Keyword)
    private String whetherNeedReceipt;

    @ApiModelProperty(value = "签收日期")
    @JSONField(name = "RECEIPT_DATE")
    @Field(type = FieldType.Long)
    private Date receiptDate;

    @ApiModelProperty(value = "销售组织ID")
    @JSONField(name = "SALES_ORGANIZATION_ID")
    @Field(type = FieldType.Long)
    private Long salesOrganizationId;

    @ApiModelProperty(value = "销售部门ID")
    @JSONField(name = "SALES_DEPARTMENT_ID")
    @Field(type = FieldType.Long)
    private Long salesDepartmentId;

    @ApiModelProperty(value = "销售部门名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_DEPARTMENT_NAME")
    private String salesDepartmentName;

    @ApiModelProperty(value = "销售组ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "SALES_GROUP_ID")
    private Long salesGroupId;

    @ApiModelProperty(value = "销售组编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_GROUP_CODE")
    private String salesGroupCode;

    @ApiModelProperty(value = "销售组名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_GROUP_NAME")
    private String salesGroupName;

    @ApiModelProperty(value = "成本中心ID")
    @JSONField(name = "COST_CENTER_ID")
    @Field(type = FieldType.Long)
    private Long costCenterId;

    @ApiModelProperty(value = "工厂")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "FACTORY")
    private String factory;

    @ApiModelProperty(value = "对等换货标识")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_EQUAL_EXCHANGE")
    private Integer isEqualExchange;

    @ApiModelProperty(value = "缺货标识")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_OUT_STOCK")
    private Integer isOutStock;

    @ApiModelProperty(value = "是否快运")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_EXPRESS")
    private String isExpress;

    @ApiModelProperty(value = "当前期数（周期购）")
    @Field(type = FieldType.Integer)
    @JSONField(name = "CURRENT_CYCLE_NUMBER")
    private Integer currentCycleNumber;

    @ApiModelProperty(value = "是否加密")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_ENCRYPTED")
    private Integer isEncrypted;

    @ApiModelProperty(value = "订单审核人id")
    @Field(type = FieldType.Integer)
    @JSONField(name = "AUDIT_ID")
    private Long auditId;

    @ApiModelProperty(value = "订单审核人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "AUDIT_NAME")
    private String auditName;


    @ApiModelProperty(value = "是否寻源失败")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_OCCUPY_STOCK_FAIL")
    private Integer isOccupyStockFail;

    @ApiModelProperty(value = "是否手动修改地址")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_MANUAL_ADDR")
    private Integer isManualAddr;


    @ApiModelProperty(value = "异常标签")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_EXCEPTION")
    private Integer isException;


    @ApiModelProperty(value = "异常类型")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "EXCEPTION_TYPE")
    private String exceptionType;


    @ApiModelProperty(value = "异常说明")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "EXCEPTION_EXPLAIN")
    private String exceptionExplain ;

    /**
     * 分销商id
     */
    @ApiModelProperty(value = "分销商id")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SELLER_ID")
    private String sellerId;

    /**
     * 周期购订单。0:否；1:天猫周期购
     */
    @ApiModelProperty(value = "周期购标识")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_CYCLE")
    private Integer isCycle;

    /**
     * 是否会员。0:否；1:是
     */
    @ApiModelProperty(value = "是否会员")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_MEMBER")
    private Integer isMember;

    /**
     * 卡单原因ID 关联HOLD单原因定义表
     */
    @ApiModelProperty(value = "卡单原因ID")
    @Field(type = FieldType.Integer)
    @JSONField(name = "DETENTION_REASON_ID")
    private Integer detentionReasonId;

    /**
     * HOLD单原因ID 关联HOLD单原因定义表
     */
    @ApiModelProperty(value = "hold单原因ID")
    @Field(type = FieldType.Integer)
    @JSONField(name = "HOLD_REASON_ID")
    private Integer holdReasonId;

    /**
     * 是否送货上门。0:否；1:是
     */
    @ApiModelProperty(value = "是否送货上门")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_DELIVERY_TO_DOOR")
    private Integer isDeliveryToDoor;

    /**
     * 物流服务类型  非数据库字段
     *
     * @see com.jackrain.nea.oc.oms.model.enums.LogisticsServiceTypeEnum
     */
    @TableField(exist = false)
    private Integer logisticsServiceType;

    @ApiModelProperty(value = "指定物流公司id")
    @Field(type = FieldType.Long)
    @JSONField(name = "APPOINT_LOGISTICS_ID")
    private Long appointLogisticsId;

    @ApiModelProperty(value = "指定物流公司编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "APPOINT_LOGISTICS_ECODE")
    private String appointLogisticsEcode;

    @ApiModelProperty(value = "指定物流公司名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "APPOINT_LOGISTICS_ENAME")
    private String appointLogisticsEname;

    @ApiModelProperty(value = "销售商品属性")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALE_PRODUCT_ATTR")
    private String saleProductAttr;

    @ApiModelProperty(value = "是否逾期 1是 0否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_OVERDUE")
    private Integer isOverdue;

    /**
     * 是否店铺冻结订单。0:否；1:是
     */
    @ApiModelProperty(value = "是否店铺冻结订单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_UNAVAILABLE_SHOP")
    private Integer isUnavailableShop;

    /**
     * 地址是否明文 0:否；1:是
     */
    @ApiModelProperty(value = "是否明文")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_PLAIN_ADDR")
    private Integer isPlainAddr;

    /**
     * 通用标记 1:拦截
     */
    @ApiModelProperty(value = "通用标记")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GENERIC_MARK")
    private String genericMark;

    @ApiModelProperty(value = "拼车单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CARPOOL_NO")
    private String carpoolNo;

    /**
     * 无范围。0:否；1:是
     */
    @ApiModelProperty(value = "无范围标识")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_NO_RANGE")
    private Integer isNoRange;

    /**
     * 备赠。0:否；1:是
     */
    @ApiModelProperty(value = "备赠")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_REMARK_GIFT")
    private Integer isRemarkGift;

    /**
     * 最晚发货时间
     */
    @ApiModelProperty(value = "最晚发货时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "LATEST_DELIVERY_TIME")
    private Date latestDeliveryTime;
}