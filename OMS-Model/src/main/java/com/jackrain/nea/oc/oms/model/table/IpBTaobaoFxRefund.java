package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_taobao_fx_refund")
@Data
@Document(index = "ip_b_taobao_fx_refund", type = "ip_b_taobao_fx_refund")
@ApiModel
public class IpBTaobaoFxRefund extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ID")
    private Long id;

    @JSONField(name = "SUB_ORDER_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "SUB_ORDER_ID")
    private Long subOrderId;

    @JSONField(name = "IS_RETURN_GOODS")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "IS_RETURN_GOODS")
    private Integer isReturnGoods;

    @JSONField(name = "REFUND_CREATE_TIME")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "REFUND_CREATE_TIME")
    private Date refundCreateTime;

    @JSONField(name = "REFUND_STATUS")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "REFUND_STATUS")
    private Long refundStatus;

    @JSONField(name = "REFUND_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "REFUND_FEE")
    private BigDecimal refundFee;

    @JSONField(name = "PAY_SUP_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "PAY_SUP_FEE")
    private BigDecimal paySupFee;

    @JSONField(name = "REFUND_REASON")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "REFUND_REASON")
    private String refundReason;

    @JSONField(name = "REFUND_DESC")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "REFUND_DESC")
    private String refundDesc;

    @JSONField(name = "SUPPLIER_NICK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SUPPLIER_NICK")
    private String supplierNick;

    @JSONField(name = "DISTRIBUTOR_NICK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "DISTRIBUTOR_NICK")
    private String distributorNick;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIED")
    private Date modified;

    @JSONField(name = "PURCHASE_ORDER_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "PURCHASE_ORDER_ID")
    private Long purchaseOrderId;

    @JSONField(name = "REFUND_FLOW_TYPE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "REFUND_FLOW_TYPE")
    private Long refundFlowType;

    @JSONField(name = "TIMEOUT")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "TIMEOUT")
    private Date timeout;

    @JSONField(name = "TO_TYPE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "TO_TYPE")
    private Long toType;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "TRANSDATE")
    private Date transdate;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SYSREMARK")
    private String sysremark;

    @JSONField(name = "IP_B_TAOBAO_FX_ORDER_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "IP_B_TAOBAO_FX_ORDER_ID")
    private Long ipBTaobaoFxOrderId;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "INSERTDATE")
    private Date insertdate;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "ISTRANS")
    private Integer istrans;

    @JSONField(name = "ISRELATED")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "ISRELATED")
    private Integer isrelated;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "XREFUND_CREATE_TIME")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "XREFUND_CREATE_TIME")
    private Date xrefundCreateTime;

    @JSONField(name = "XREFUND_STATUS")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "XREFUND_STATUS")
    private Long xrefundStatus;

    @JSONField(name = "XGOODS_STATUS_DESC")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "XGOODS_STATUS_DESC")
    private String xgoodsStatusDesc;

    @JSONField(name = "XNEED_RETURN_GOOD")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "XNEED_RETURN_GOOD")
    private Long xneedReturnGood;

    @JSONField(name = "XRETURN_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "XRETURN_FEE")
    private BigDecimal xreturnFee;

    @JSONField(name = "XTO_SELLER_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "XTO_SELLER_FEE")
    private BigDecimal xtoSellerFee;

    @JSONField(name = "XREFUND_REASON")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "XREFUND_REASON")
    private String xrefundReason;

    @JSONField(name = "XREFUND_DESC")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "XREFUND_DESC")
    private String xrefundDesc;

    @JSONField(name = "XREFUND_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "XREFUND_ID")
    private String xrefundId;

    @JSONField(name = "XSUB_ORDER_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "XSUB_ORDER_ID")
    private String xsubOrderId;

    @JSONField(name = "XBIZ_ORDER_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "XBIZ_ORDER_ID")
    private String xbizOrderId;

    @JSONField(name = "XBUYER_NICK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "XBUYER_NICK")
    private String xbuyerNick;

    @JSONField(name = "XMODIFIED")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "XMODIFIED")
    private Date xmodified;

    @JSONField(name = "SKU")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SKU")
    private String sku;

    @JSONField(name = "NUM")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "NUM")
    private Long num;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "TRANS_COUNT")
    private Long transCount;

    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT06")
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT07")
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT08")
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT09")
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT10")
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL01")
    private Long reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL02")
    private Long reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL03")
    private Long reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL04")
    private Long reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL05")
    private Long reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL06")
    private Long reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL07")
    private Long reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL08")
    private Long reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL09")
    private Long reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL10")
    private Long reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR06")
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR07")
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR08")
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR09")
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR10")
    private String reserveVarchar10;
}