package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * 残次策略
 *
 * <AUTHOR>
 */
@Data
@TableName("st_c_imperfect_strategy")
public class StCImperfectStrategy extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 策略编码
     */
    @JSONField(name = "STRATEGY_CODE")
    private String strategyCode;

    /**
     * 策略名称
     */
    @JSONField(name = "STRATEGY_NAME")
    private String strategyName;

    /**
     * 开始时间
     */
    @JSONField(name = "START_TIME")
    private Date startTime;

    /**
     * 结束时间
     */
    @JSONField(name = "END_TIME")
    private Date endTime;

    /**
     * 店铺ID
     */
    @JSONField(name = "SHOP_ID")
    private Long shopId;

    /**
     * 商品销售属性
     */
    @JSONField(name = "SALE_PRODUCT_ATTR")
    private String saleProductAttr;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    /**
     * 状态
     */
    @JSONField(name = "STATUS")
    private Integer status;

}
