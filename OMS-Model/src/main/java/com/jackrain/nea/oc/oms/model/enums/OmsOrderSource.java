package com.jackrain.nea.oc.oms.model.enums;

/**
 * 订单来源
 *
 * @author: hulinyang
 * @since: 2019-4-02
 */
public enum OmsOrderSource {

    /**
     * 手动新增
     */
    MANUAL_ADD,

    /**
     * 京东
     */
    JINGDONG,

    /**
     * 淘宝
     */
    TAOBAO;

    public String getEcode() {
        if (this == OmsOrderSource.MANUAL_ADD) {
            return "手工新增";
        } else if (this == OmsOrderSource.JINGDONG) {
            return "京东";
        } else if (this == OmsOrderSource.TAOBAO) {
            return "淘宝";
        }
        return null;
    }

}
