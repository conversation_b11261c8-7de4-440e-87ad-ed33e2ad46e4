package com.jackrain.nea.oc.oms.vo;

import cn.hutool.core.util.ObjectUtil;
import com.jackrain.nea.exception.NDSException;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName OcBOrderPreImpVO
 * @Description 订单预导入
 * <AUTHOR>
 * @Date 2022/10/10 11:15
 * @Version 1.0
 */
@Data
public class OcBOrderPreImpVO extends OcBOrder implements Serializable {

    public static final String cellStr = "cell_";

    public static final String rowStr = "row_";

    public static final String reg = "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]";

    /**
     * 订单类型
     */
    private String orderTypeName;

    /**
     * 支付方式
     */
    private String payTypeName;
    /**
     * 是否开票
     */
    private String isInvoiceName;
    /**
     * 数量
     */
    private BigDecimal qty;

    /**
     * sku编码
     */
    private String psCSkuEcode;

    /**
     * 商品名称
     */
    private String psCSkuEname;

    /**
     * 行号
     */
    private int rowNum;

    /**
     * 成交单价
     */
    private BigDecimal priceActual;

    /**
     * 错误信息
     */
    private String desc;

    /**
     * 是否赠品
     */
    private Integer isGift;

    private String isGiftStr;

    /**
     * 平台售价
     */
    private BigDecimal platformPrice;

    /**
     * 异常信息
     */
    private String errorMsg;


    private String sourceCode;

    private String importContent;

    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBOrderPreImpVO importCreate(int index, OcBOrderPreImpVO ocBOrderImpVo, Map<String, String> columnMap,
                                                Map<String, StCPreorderItemStrategyVO> itemStrategyDOMap,
                                                Map<String, StCPreorderPriceSkuVO> skuPriceMap, String cpCShopTitle) {
        FastDateFormat dateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

        String importContent = columnMap.get("导入内容");
        StCPreorderPriceSkuVO stCPreorderPriceSkuVO = skuPriceMap.get(importContent);
        if (ObjectUtil.isNull(stCPreorderPriceSkuVO)){
            throw new NDSException("导入内容【"+importContent+"】匹配失败，请检查相关策略");
        }

        try {
            ocBOrderImpVo.setCpCShopTitle(cpCShopTitle);
        } catch (Exception e) {

        }
        try {
            //配送费用
            BigDecimal shipAmt = new BigDecimal(columnMap.get("配送费用"));
            if (ObjectUtil.isNull(shipAmt)) {
                shipAmt = BigDecimal.ZERO;
            }
            ocBOrderImpVo.setShipAmt(shipAmt);
        } catch (Exception e) {

        }
        try {
            //买家昵称
            ocBOrderImpVo.setUserNick(columnMap.get("买家昵称"));
        } catch (Exception e) {

        }

        try {
            //平台单号
            ocBOrderImpVo.setSourceCode(columnMap.get("平台单号"));
        } catch (Exception e) {

        }
        try {
            //付款方式:(一头牛调整为空)
            ocBOrderImpVo.setPayTypeName(columnMap.get("付款方式"));
        } catch (Exception e) {

        }
        try {
            //收货人
            String receiverName = columnMap.get("收货人");
            //过滤特殊字符和表情
            if (StringUtils.isNotBlank(receiverName)) {
                receiverName = filterSpecialStr(receiverName);
                receiverName = filterEmoji(receiverName);
            }
            ocBOrderImpVo.setReceiverName(receiverName);
        } catch (Exception e) {

        }
        try {
            //收货人手机
            ocBOrderImpVo.setReceiverMobile(columnMap.get("收货人手机"));
        } catch (Exception e) {

        }
        try {
            //收货人电话
            ocBOrderImpVo.setReceiverPhone(columnMap.get("收货人电话"));
        } catch (Exception e) {

        }
        try {
            //收货人邮编
            ocBOrderImpVo.setReceiverZip(columnMap.get("收货人邮编"));
        } catch (Exception e) {

        }
        try {
            //收货人省份
            ocBOrderImpVo.setCpCRegionProvinceEname(columnMap.get("收货人省份") == null ? "" : columnMap.get("收货人省份"));
        } catch (Exception e) {

        }
        try {
            //收货人市
            ocBOrderImpVo.setCpCRegionCityEname(columnMap.get("收货人市") == null ? "" : columnMap.get("收货人市"));
        } catch (Exception e) {

        }
        try {
            //收货人区
            ocBOrderImpVo.setCpCRegionAreaEname(columnMap.get("收货人区") == null ? "" : columnMap.get("收货人区"));
        } catch (Exception e) {

        }
        try {
            //收货人地址
            String receiverAddress = columnMap.get("收货人地址");
            //过滤特殊字符和表情
            if (StringUtils.isNotBlank(receiverAddress)) {
                receiverAddress = filterSpecialStr(receiverAddress);
                receiverAddress = filterEmoji(receiverAddress);
            }
            ocBOrderImpVo.setReceiverAddress(receiverAddress);
        } catch (Exception e) {

        }

        try {
            // 商品名称
            ocBOrderImpVo.setPsCSkuEname(columnMap.get("商品名称"));
        } catch (Exception e) {

        }

        try {
            //商品编码
            ocBOrderImpVo.setPsCSkuEcode(stCPreorderPriceSkuVO.getSkuEcode());
//            ocBOrderImpVo.setPsCSkuEcode(columnMap.get("商品SKU编码"));
//            if (StringUtils.isEmpty(ocBOrderImpVo.getPsCSkuEcode()) && StringUtils.isNotEmpty(ocBOrderImpVo.getPsCSkuEname())) {
//                StCPreorderItemStrategyVO preorderItemStrategyVO = itemStrategyDOMap.get(ocBOrderImpVo.getPsCSkuEname());
//                if (ObjectUtil.isNotNull(preorderItemStrategyVO)) {
//                    ocBOrderImpVo.setPsCSkuEcode(preorderItemStrategyVO.getSkuEcode());
//                }
//            }
        } catch (Exception e) {

        }

        try {
            //导入内容
            ocBOrderImpVo.setImportContent(importContent);
        } catch (Exception e) {

        }

        try {
            //商品数量
            ocBOrderImpVo.setQty(new BigDecimal(columnMap.get("数量")));
        } catch (Exception e) {

        }
        try {
            //商品成交金额
            ocBOrderImpVo.setPriceActual(stCPreorderPriceSkuVO.getAmt());

//            Object o = columnMap.get("成交单价");
//            if (StringUtils.isNotEmpty(String.valueOf(o))) {
//                ocBOrderImpVo.setPriceActual(new BigDecimal(String.valueOf(o)));
//            }
//            if (ObjectUtil.isNull(ocBOrderImpVo.getPriceActual()) && StringUtils.isNotEmpty(ocBOrderImpVo.getPsCSkuEname())) {
//                StCPreorderItemStrategyVO preorderItemStrategyVO = itemStrategyDOMap.get(ocBOrderImpVo.getPsCSkuEname());
//                if (ObjectUtil.isNotNull(preorderItemStrategyVO)) {
//                    ocBOrderImpVo.setPriceActual(preorderItemStrategyVO.getPriceActual());
//                }
//            }
        } catch (Exception e) {

        }
        try {
            // 下单时间
            String orderDate = columnMap.get("下单时间");
            ocBOrderImpVo.setOrderDate(StringUtils.isNotBlank(orderDate) ? dateFormat.parse(orderDate) : new Date());
        } catch (Exception e) {

        }
        try {
            // 支付时间
            String payDate = columnMap.get("支付时间");
            ocBOrderImpVo.setPayTime(StringUtils.isNotBlank(payDate) ? dateFormat.parse(payDate) : new Date());
        } catch (Exception e) {

        }
        try {
            // 平台售价
            ocBOrderImpVo.setPlatformPrice(new BigDecimal(columnMap.get("平台售价")));
        } catch (Exception e) {

        }
        try {
            //买家备注
            ocBOrderImpVo.setBuyerMessage(columnMap.get("买家备注"));
        } catch (Exception e) {

        }
        try {
            //卖家备注
            ocBOrderImpVo.setSellerMemo(columnMap.get("卖家备注"));
            //OAID
            ocBOrderImpVo.setOaid(columnMap.get("OAID"));
            //是否赠品 优化解析bug
            String isGift = columnMap.get("是否赠品");
            if (StringUtils.isNotBlank(isGift) && "是".equals(isGift)) {
                ocBOrderImpVo.setIsGift(1);
            } else {
                ocBOrderImpVo.setIsGift(0);
            }
        } catch (Exception e) {

        }
        try {
            ocBOrderImpVo.setSalesmanName(columnMap.get("业务员"));
        } catch (Exception e) {

        }
        ocBOrderImpVo.setRowNum(index + 1);
        return ocBOrderImpVo;
    }


    /**
     * 过滤特殊字符
     *
     * @param str
     * @return
     */
    private static String filterSpecialStr(String str) {
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("-").trim();
    }

    /**
     * 过滤表情
     *
     * @param source
     * @return
     */
    private static String filterEmoji(String source) {
        if (source != null) {
            Pattern emoji = Pattern.compile(reg, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(source);
            if (emojiMatcher.find()) {
                source = emojiMatcher.replaceAll("");
                return source;
            }
            return source;
        }
        return source;
    }
}
