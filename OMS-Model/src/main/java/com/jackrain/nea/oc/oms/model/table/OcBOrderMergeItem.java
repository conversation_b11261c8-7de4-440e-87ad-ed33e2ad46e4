package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_order_merge_item", type = "oc_b_order_merge_item")
@ApiModel(value="oc_b_order_merge_item", description="")
public class OcBOrderMergeItem extends BaseModel {

    @ApiModelProperty(value = "合单编号")
    @Field(type = FieldType.Long)
    @JSONField(name= "ID")
    private Long id;

    @ApiModelProperty(value = "订单编号")
    @Field(type = FieldType.Long)
    @JSONField(name= "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "原始订单号")
    @Field(type = FieldType.Long)
    @JSONField(name= "ORIG_ORDER_ID")
    private Long origOrderId;

    @ApiModelProperty(value = "平台单号信息")
    @Field(type = FieldType.Keyword)
    @JSONField(name= "SOURCE_CODE")
    private String sourceCode;

    @ApiModelProperty(value = "初始平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name= "TID")
    private String tid;

    @ApiModelProperty(value = "订单补充信息")
    @Field(type = FieldType.Keyword)
    @JSONField(name= "SUFFIX_INFO")
    private String suffixInfo;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name= "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name= "MODIFIERENAME")
    private String modifierename;
}
