package com.jackrain.nea.oc.oms.model.enums;

/**
 * 全渠道订单表 -> 订单拦截状态
 *
 * @author: ming.fz
 * create at: 2019/3/28
 */
public enum OmsOrderIsInterceptEnum {

    YES("订单已挂起", 1),
    NO("订单未挂起", 0);

    Integer val; // 值
    String key; // 数据库字段

    OmsOrderIsInterceptEnum(String k, Integer v) {
        this.val = v;
        this.key = k;
    }

    public Integer getVal() {
        return val;
    }

    public String getKey() {
        return key;
    }
}
