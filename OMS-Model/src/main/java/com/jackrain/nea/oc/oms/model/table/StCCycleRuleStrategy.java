package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @ClassName StCCycleRuleStrategy
 * @Description 周期购促销
 * <AUTHOR>
 * @Date 2024/8/19 14:16
 * @Version 1.0
 */
@Data
@TableName("st_c_cycle_rule_strategy")
public class StCCycleRuleStrategy extends BaseModel {
    private static final long serialVersionUID = -3849708191760427368L;

    /**
     * id
     */
    private Long id;

    /**
     * 策略ID
     */
    @JSONField(name = "STRATEGY_ID")
    private Long strategyId;

    /**
     * 识别规则 1平台商品ID 2SKU
     */
    @JSONField(name = "RULE_TYPE")
    private Integer ruleType;

    /**
     * 识别内容
     */
    @J<PERSON>NField(name = "RULE_CONTENT")
    private String ruleContent;

    @JSONField(name = "STATUS")
    private Integer status;

}
