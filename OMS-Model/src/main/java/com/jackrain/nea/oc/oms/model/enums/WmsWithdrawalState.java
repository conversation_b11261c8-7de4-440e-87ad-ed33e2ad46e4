package com.jackrain.nea.oc.oms.model.enums;

/**
 * 传wms状态
 *
 * @author: 夏继超
 * @since: 2019/3/29
 * create at : 2019/3/29 12:28
 */
public enum WmsWithdrawalState {
    // 0未传WMS，1传WMS中，2传WMS成功，3传WMS失败
    /**
     * 未传WMS
     */
    NO,
    /**
     * 传WMS成功
     */
    YES,
    /**
     * 传WMS失败
     */
    FAIL,
    /**
     * 传WMS中
     */
    PASS;


    public Integer toInteger() {
        if (this == WmsWithdrawalState.NO) {
            return 0;
        } else if (this == WmsWithdrawalState.YES) {
            return 2;
        } else if (this == WmsWithdrawalState.FAIL) {
            return 3;
        } else {
            return 1;
        }
    }
}
