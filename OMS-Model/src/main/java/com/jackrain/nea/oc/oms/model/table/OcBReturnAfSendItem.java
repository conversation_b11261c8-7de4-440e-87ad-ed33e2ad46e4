package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@TableName(value = "oc_b_return_af_send_item")
@Data
@Document(index = "oc_b_return_af_send", type = "oc_b_return_af_send_item")
public class OcBReturnAfSendItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "OC_B_RETURN_AF_SEND_ID")
    @Field(type = FieldType.Long)
    private Long ocBReturnAfSendId;

    @JSONField(name = "RELATION_BILL_TYPE")
    @Field(type = FieldType.Long)
    private Long relationBillType;

    @JSONField(name = "RELATION_BILL_ID")
    @Field(type = FieldType.Long)
    private Long relationBillId;

    @JSONField(name = "RELATION_BILL_NO")
    @Field(type = FieldType.Keyword)
    private String relationBillNo;

    @JSONField(name = "BILL_TYPE")
    @Field(type = FieldType.Integer)
    private Integer billType;

    @JSONField(name = "INTERCEPT_STATUS")
    @Field(type = FieldType.Long)
    private Long interceptStatus;

    @JSONField(name = "GIFT")
    @Field(type = FieldType.Keyword)
    private String gift;

    @JSONField(name = "PS_C_SKU_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSkuEcode;

    @JSONField(name = "PS_C_PRO_ID")
    @Field(type = FieldType.Long)
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCProEname;

    @JSONField(name = "PS_C_SPEC_ID")
    @Field(type = FieldType.Long)
    private Long psCSpecId;

    @JSONField(name = "PS_C_SPEC_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCSpecEname;

    @JSONField(name = "QTY_RETURN_APPLY")
    @Field(type = FieldType.Double)
    private BigDecimal qtyReturnApply;

    @JSONField(name = "QTY_IN")
    @Field(type = FieldType.Double)
    private BigDecimal qtyIn;

    @JSONField(name = "AMT_RETURN")
    @Field(type = FieldType.Double)
    private BigDecimal amtReturn;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    private Long version;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    private Date modifieddate;

    @JSONField(name = "AMT_ACTUAL")
    @Field(type = FieldType.Double)
    private BigDecimal amtActual;

    @JSONField(name = "AMT_HAS_RETURN")
    @Field(type = FieldType.Double)
    private BigDecimal amtHasReturn;

    @JSONField(name = "PURCHASE_QTY")
    @Field(type = FieldType.Double)
    private BigDecimal purchaseQty;

    @JSONField(name = "RELATION_BILL_ITEM_ID")
    @Field(type = FieldType.Long)
    private Long relationBillItemId;

    @JSONField(name = "BARCODE")
    @Field(type = FieldType.Keyword)
    private String barcode;

    @JSONField(name = "FREIGHT")
    @Field(type = FieldType.Double)
    private BigDecimal freight;

    @JSONField(name = "PS_C_SKU_PT_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSkuPtEcode;

    @JSONField(name = "PT_PRO_NAME")
    @Field(type = FieldType.Keyword)
    private String ptProName;

    @JSONField(name = "PS_C_SKU_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCSkuEname;

    @JSONField(name = "OC_B_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ocBOrderId;

    @JSONField(name = "OC_B_ORDER_ITEM_ID")
    @Field(type = FieldType.Long)
    private Long ocBOrderItemId;

    @ApiModelProperty(value = "订单业务类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "BUSINESS_TYPE_ID")
    private Long businessTypeId;

    @ApiModelProperty(value = "订单业务类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "BUSINESS_TYPE_NAME")
    private String businessTypeName;

    @ApiModelProperty(value = "订单业务类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "BUSINESS_TYPE_CODE")
    private String businessTypeCode;

}