package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "OC_B_INVOICE_NOTICE")
@Data
@Document(index = "oc_b_invoice_notice", type = "oc_b_invoice_notice")
public class OcBInvoiceNotice extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    private String billNo;

    @JSONField(name = "ESTATUS")
    @Field(type = FieldType.Integer)
    private Integer estatus;

    @JSONField(name = "TAX_NO")
    @Field(type = FieldType.Keyword)
    private String taxNo;

    @JSONField(name = "INVOICE_NO")
    @Field(type = FieldType.Keyword)
    private String invoiceNo;

    @JSONField(name = "AMT")
    @Field(type = FieldType.Double)
    private BigDecimal amt;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "COMPANY")
    @Field(type = FieldType.Long)
    private Long company;

    @JSONField(name = "SOURCE_CODE")
    @Field(type = FieldType.Keyword)
    private String sourceCode;

    @JSONField(name = "OC_B_ORDER_ID")
    @Field(type = FieldType.Keyword)
    private String ocBOrderId;

    @JSONField(name = "BUYER_RAMARK")
    @Field(type = FieldType.Keyword)
    private String buyerRamark;

    @JSONField(name = "SELLER_REMARK")
    @Field(type = FieldType.Keyword)
    private String sellerRemark;

    @JSONField(name = "INVOICE_TYPE")
    @Field(type = FieldType.Integer)
    private Integer invoiceType;

    @JSONField(name = "HEADER_NAME")
    @Field(type = FieldType.Keyword)
    private String headerName;

    @JSONField(name = "HEADER_TYPE")
    @Field(type = FieldType.Integer)
    private Integer headerType;

    @JSONField(name = "TAXPAYER_NO")
    @Field(type = FieldType.Keyword)
    private String taxpayerNo;

    @JSONField(name = "EMAIL")
    @Field(type = FieldType.Keyword)
    private String email;

    @JSONField(name = "COMPANY_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String companyAddress;

    @JSONField(name = "PHONE_NO")
    @Field(type = FieldType.Keyword)
    private String phoneNo;

    @JSONField(name = "OPENING_BANK")
    @Field(type = FieldType.Keyword)
    private String openingBank;

    @JSONField(name = "OPENING_BANK_ACCOUNT")
    @Field(type = FieldType.Keyword)
    private String openingBankAccount;

    @JSONField(name = "INVOICE_REMARK")
    @Field(type = FieldType.Keyword)
    private String invoiceRemark;

    @JSONField(name = "RECEIVE_NAME")
    @Field(type = FieldType.Keyword)
    private String receiveName;

    @JSONField(name = "RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String receiverMobile;

    @JSONField(name = "RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;

    @JSONField(name = "CHECK_ID")
    @Field(type = FieldType.Long)
    private Long checkId;

    @JSONField(name = "CHECK_ENAME")
    @Field(type = FieldType.Keyword)
    private String checkEname;

    @JSONField(name = "CHECK_NAME")
    @Field(type = FieldType.Keyword)
    private String checkName;

    @JSONField(name = "CHECKTIME")
    @Field(type = FieldType.Long)
    private Date checktime;

    @JSONField(name = "INVOICE_ID")
    @Field(type = FieldType.Long)
    private Long invoiceId;

    @JSONField(name = "INVOICE_ENAME")
    @Field(type = FieldType.Keyword)
    private String invoiceEname;

    @JSONField(name = "INVOICE_NAME")
    @Field(type = FieldType.Keyword)
    private String invoiceName;

    @JSONField(name = "INVOICE_TIME")
    @Field(type = FieldType.Long)
    private Date invoiceTime;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    @Field(type = FieldType.Long)
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEname;

    @JSONField(name = "LOGISTICS_NO")
    @Field(type = FieldType.Keyword)
    private String logisticsNo;

    @JSONField(name = "DELER_ID")
    @Field(type = FieldType.Long)
    private Long delerId;

    @JSONField(name = "DELER_ENAME")
    @Field(type = FieldType.Keyword)
    private String delerEname;

    @JSONField(name = "DELER_NAME")
    @Field(type = FieldType.Keyword)
    private String delerName;

    @JSONField(name = "DEL_TIME")
    @Field(type = FieldType.Long)
    private Date delTime;

    @JSONField(name = "INVOICE_COMPANY")
    @Field(type = FieldType.Keyword)
    private String invoiceCompany;
}