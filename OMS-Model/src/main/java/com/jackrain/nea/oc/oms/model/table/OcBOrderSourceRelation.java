package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "oc_b_order_source_relation")
@Data
@Document(index = "oc_b_order_source_relation", type = "oc_b_order_source_relation")
public class OcBOrderSourceRelation extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 订单id
     */
    @JSONField(name = "ORDER_ID")
    @Field(type = FieldType.Keyword)
    private Long orderId;

    /**
     * 来源id
     */
    @JSONField(name = "SOURCE_ORDER_ID")
    @Field(type = FieldType.Keyword)
    private Long sourceOrderId;

    /**
     * 类型
     * {@link com.jackrain.nea.oc.oms.model.enums.OcBOrderSourceRelationTypeEnum}
     */
    @JSONField(name = "TYPE")
    @Field(type = FieldType.Keyword)
    private String type;

}