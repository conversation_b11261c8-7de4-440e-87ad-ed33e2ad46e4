package com.jackrain.nea.oc.oms.model.enums;


import java.util.Objects;

/**
 * <AUTHOR> ruan.gz
 * @Description : 换货占单状态
 * @Date : 2020/7/3
 **/
public enum ExchangeOccupancyOrderStatus {


    NOTOCCUPANCY("未处理", 1),
    OCCUPANCY("处理中", 2),
    OCCUPANCYEND("已处理", 3),
    OCCUPANCYFAIL("处理失败", 4);

    String key;
    Integer val;

    ExchangeOccupancyOrderStatus(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    public static ExchangeOccupancyOrderStatus getFromValue(Integer value) {
        ExchangeOccupancyOrderStatus[] values = ExchangeOccupancyOrderStatus.values();
        for (ExchangeOccupancyOrderStatus statusEnum : values) {
            if (Objects.equals(statusEnum.val, value)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException(String.valueOf(value));
    }
}
