package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName StCCycleStrategy
 * @Description 周期购促销策略
 * <AUTHOR>
 * @Date 2024/8/19 13:50
 * @Version 1.0
 */
@Data
@TableName("st_c_cycle_strategy")
public class StCCycleStrategy extends BaseModel {

    private static final long serialVersionUID = -989918925342967822L;

    /**
     * id
     */
    private Long id;

    /**
     * 策略编码
     */
    @JSONField(name = "STRATEGY_CODE")
    private String strategyCode;

    /**
     * 策略名称
     */
    @JSONField(name = "STRATEGY_NAME")
    private String strategyName;

    /**
     * 开始时间
     */
    @JSONField(name = "START_TIME")
    private Date startTime;

    /**
     * 结束时间
     */
    @JSONField(name = "END_TIME")
    private Date endTime;

    /**
     * 店铺ID
     */
    @JSONField(name = "SHOP_ID")
    private Long shopId;

    /**
     * 业务类型 1,"天猫周期购",2,"中台周期购"
     */
    @JSONField(name = "TYPE")
    private Integer type;

    /**
     * 备注
     */
    @JSONField(name = "REMARK")
    private String remark;

    /**
     * 状态
     */
    @JSONField(name = "STATUS")
    private Integer status;
}
