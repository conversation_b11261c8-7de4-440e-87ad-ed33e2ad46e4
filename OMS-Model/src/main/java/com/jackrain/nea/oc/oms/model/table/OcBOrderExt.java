package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName OcBOrderExtend
 * @Description 订单拓展表
 * <AUTHOR>
 * @Date 2022/11/10 21:51
 * @Version 1.0
 */
@TableName(value = "oc_b_order_ext")
@Data
@Document(index = "oc_b_order_ext", type = "oc_b_order_ext")
//@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_order_ext", description = "零售发货单拓展表")
public class OcBOrderExt extends BaseModel {

    @ApiModelProperty(value = "ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "单据编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BILL_NO")
    private String billNo;

    @ApiModelProperty(value = "订单ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "ALL_ITEM_EXPIRE_DATE")
    private String allItemExpireDate;

    @ApiModelProperty(value = "数字类型备用字段1")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @ApiModelProperty(value = "数字类型备用字段2")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @ApiModelProperty(value = "数字类型备用字段3")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @ApiModelProperty(value = "数字类型备用字段4")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @ApiModelProperty(value = "数字类型备用字段5")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @ApiModelProperty(value = "价格备用字段1")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "价格备用字段3")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @ApiModelProperty(value = "价格备用字段4")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @ApiModelProperty(value = "价格备用字段5")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @ApiModelProperty(value = "文本备用字段1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @ApiModelProperty(value = "文本备用字段2")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @ApiModelProperty(value = "文本备用字段3")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @ApiModelProperty(value = "文本备用字段4")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @ApiModelProperty(value = "文本备用字段5")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

}
