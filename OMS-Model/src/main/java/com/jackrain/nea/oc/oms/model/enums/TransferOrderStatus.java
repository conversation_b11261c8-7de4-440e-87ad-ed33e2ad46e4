package com.jackrain.nea.oc.oms.model.enums;

/**
 * @author: 易邵峰
 * @since: 2019-01-21
 * create at : 2019-01-21 14:46
 */
public enum TransferOrderStatus {
    /**
     * 未转换0
     */
    NOT_TRANSFER,

    /**
     * 转换中1
     */
    TRANSFERRING,

    /**
     * 已转换2
     */
    TRANSFERRED,

    /**
     * 退款转换中3
     */
    REFUNDTRANSFERRING,
    /**
     * 转换失败4
     */
    TRANSFERFAIL,
    /**
     * 转换异常5
     */
    TRANSFEREXCEPTION;

    public static TransferOrderStatus parse(String value) {
        if ("2".equals(value)) {
            return TransferOrderStatus.TRANSFERRED;
        } else if ("1".equals(value)) {
            return TransferOrderStatus.TRANSFERRING;
        } else if ("3".equals(value)) {
            return TransferOrderStatus.REFUNDTRANSFERRING;
        } else if ("4".equals(value)) {
            return TransferOrderStatus.TRANSFERFAIL;
        } else if ("5".equals(value)) {
            return TransferOrderStatus.TRANSFEREXCEPTION;
        } else {
            return TransferOrderStatus.NOT_TRANSFER;
        }
    }

    public int toInteger() {
        if (this == TransferOrderStatus.REFUNDTRANSFERRING) {
            return 3;
        } else if (this == TransferOrderStatus.TRANSFERRED) {
            return 2;
        } else if (this == TransferOrderStatus.TRANSFERRING) {
            return 1;
        } else if (this == TransferOrderStatus.TRANSFERFAIL) {
            return 4;
        } else if (this == TransferOrderStatus.TRANSFEREXCEPTION) {
            return 5;
        } else {
            return 0;
        }
    }

    public String toKeyword() {
        if (this == TransferOrderStatus.REFUNDTRANSFERRING) {
            return "3";
        } else if (this == TransferOrderStatus.TRANSFERRED) {
            return "2";
        } else if (this == TransferOrderStatus.TRANSFERRING) {
            return "1";
        } else if (this == TransferOrderStatus.TRANSFERFAIL) {
            return "4";
        } else if (this == TransferOrderStatus.TRANSFEREXCEPTION) {
            return "5";
        } else {
            return "0";
        }
    }

}
