package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_time_order_vip")
@Data
@Document(index = "ip_b_time_order_vip", type = "ip_b_time_order_vip")
public class IpBTimeOrderVip extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    private String billNo;

    @JSONField(name = "ORDER_SN")
    @Field(type = FieldType.Keyword)
    private String orderSn;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_CODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopCode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "TRANS_DATE")
    @Field(type = FieldType.Long)
    private Date transDate;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    @JSONField(name = "CREATE_DATE")
    @Field(type = FieldType.Long)
    private Date createDate;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    @JSONField(name = "OCCUPIED_ORDER_SN")
    @Field(type = FieldType.Keyword)
    private String occupiedOrderSn;

    @JSONField(name = "SALE_WAREHOUSE")
    @Field(type = FieldType.Keyword)
    private String saleWarehouse;

    @JSONField(name = "ADDRESS_CODE")
    @Field(type = FieldType.Keyword)
    private String addressCode;

    @JSONField(name = "PICK_NO")
    @Field(type = FieldType.Keyword)
    private String pickNo;

    @JSONField(name = "PO_NO")
    @Field(type = FieldType.Keyword)
    private String poNo;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    /**
     * 下次补偿时间
     */
    @JSONField(name = "NEXT_COMPENSATION_DATE")
    @Field(type = FieldType.Long)
    private Date nextCompensationDate;

    /**
     * 补偿次数
     */
    @JSONField(name = "COMPENSATION_TIME")
    @Field(type = FieldType.Integer)
    private Integer compensationTime;

    /**
     * 缺货数量
     */
    @JSONField(name = "OUT_STOCK_QUANTITY")
    @Field(type = FieldType.Double)
    private BigDecimal outStockQuantity;

    /**
     * 异常类型
     */
    @JSONField(name = "EXCEPTION_TYPE")
    @Field(type = FieldType.Keyword)
    private String exceptionType;

    /**
     * 根订单号
     */
    @JSONField(name = "ROOT_ORDER_SN")
    @Field(type = FieldType.Long)
    private String rootOrderSn;

    /**
     * 标记虚拟寻仓中  1:虚拟寻仓中 2:虚拟寻仓完成
     */
    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Integer)
    private Integer reserveBigint01;

}