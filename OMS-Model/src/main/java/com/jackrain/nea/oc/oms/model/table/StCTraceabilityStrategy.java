package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * st_c_traceability_strategy
 *
 * <AUTHOR>
 * @create 2024-12-19
 */
@TableName
@Data
public class StCTraceabilityStrategy extends BaseModel {
    @JSONField(name = "ID")
    private Long id;

    /**
     * 店铺id
     */
    @JSONField(name = "SHOP_ID", alternateNames = "shopId")
    private Long shopId;

    /**
     * 开始时间
     */
    @JSONField(name = "START_TIME", alternateNames = "startTime")
    private Date startTime;

    /**
     * 结束时间
     */
    @JSONField(name = "END_TIME", alternateNames = "endTime")
    private Date endTime;

    /**
     * 备注
     */
    @JSONField(name = "REMARKS")
    @TableField(strategy = FieldStrategy.IGNORED)
    private String remarks;

    /**
     * 提交状态(0,未提交,1,已提交,2已作废,3已结案)
     */
    @JSONField(name = "SUBMIT_STATUS", alternateNames = "submitStatus")
    private Integer submitStatus;

    private static final long serialVersionUID = 1L;
}
