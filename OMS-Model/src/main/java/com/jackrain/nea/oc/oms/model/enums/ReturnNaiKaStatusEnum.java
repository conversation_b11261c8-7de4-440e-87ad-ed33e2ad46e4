package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName ReturnNaiKaStatusEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/8/10 14:46
 * @Version 1.0
 */
@Getter
public enum ReturnNaiKaStatusEnum {

    IGNORE(1, "无需传"),
    UN_PUSH(2, "待传"),
    PUSHED(3, "传成功"),
    PUSH_FAIL(4, "传失败"),
    OFFLINE_FINISH(5, "线下处理完成");

    /**
     * 状态
     */
    private Integer status;

    /**
     * 描述
     */
    private String desc;

    ReturnNaiKaStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static ReturnNaiKaStatusEnum getByStatus(Integer status) {
        if (status == null) {
            return null;
        }
        ReturnNaiKaStatusEnum[] enums = values();
        for (ReturnNaiKaStatusEnum returnNaiKaStatusEnum : enums) {
            if (ObjectUtils.equals(returnNaiKaStatusEnum.getStatus(), status)) {
                return returnNaiKaStatusEnum;
            }
        }
        return null;
    }

    public static ReturnNaiKaStatusEnum getByDesc(String desc) {
        if (StringUtils.isEmpty(desc)) {
            return null;
        }
        ReturnNaiKaStatusEnum[] enums = values();
        for (ReturnNaiKaStatusEnum returnNaiKaStatusEnum : enums) {
            if (ObjectUtils.equals(returnNaiKaStatusEnum.getDesc(), desc)) {
                return returnNaiKaStatusEnum;
            }
        }
        return null;
    }
}
