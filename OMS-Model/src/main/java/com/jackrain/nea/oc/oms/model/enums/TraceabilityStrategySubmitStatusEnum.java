package com.jackrain.nea.oc.oms.model.enums;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略提交状态枚举
 * @author: lijin
 * @create: 2024-12-19
 **/
public enum TraceabilityStrategySubmitStatusEnum {

    /**
     * 未审核
     */
    NOT_SUBMITTED(1, "未审核"),

    /**
     * 已审核
     */
    SUBMITTED(2, "已审核"),

    /**
     * 已作废
     */
    CANCELLED(3, "已作废"),

    /**
     * 已结案
     */
    CLOSED(4, "已结案");

    private final Integer code;
    private final String desc;

    TraceabilityStrategySubmitStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static TraceabilityStrategySubmitStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TraceabilityStrategySubmitStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断code是否有效
     *
     * @param code
     * @return
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
