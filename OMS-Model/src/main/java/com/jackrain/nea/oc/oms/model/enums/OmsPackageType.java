package com.jackrain.nea.oc.oms.model.enums;

/**
 * 包裹属性
 *
 * @author: 胡林洋
 * @since: 2019-03-14
 * create at : 2019-03-14 20:52
 */
public enum OmsPackageType {
    /**
     * 包裹重量
     */
    WEIGHT,
    /**
     * 订单金额
     */
    AMOUNT,
    /**
     * 包裹数量
     */
    QUANTITY;

    public String paseValue() {
        if (this == WEIGHT) {
            return "Weight";
        } else if (this == AMOUNT) {
            return "Amount";
        } else if (this == QUANTITY) {
            return "Quantity";
        } else {
            return " ";
        }
    }

}
