package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_refund_in", type = "oc_b_refund_in_product_item")
@ApiModel(value = "oc_b_refund_in_product_item", description = "退货入库单商品明细")
public class OcBRefundInProductItem extends BaseModel {


    @ApiModelProperty(value = "明细编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "退货入库单id")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_REFUND_IN_ID")
    private Long ocBRefundInId;

    @ApiModelProperty(value = "国标码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GBCODE")
    private String gbcode;

    @ApiModelProperty(value = "条码id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @ApiModelProperty(value = "条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @ApiModelProperty(value = "实收条码id")
    @Field(type = FieldType.Long)
    @JSONField(name = "REAL_SKU_ID")
    private Long realSkuId;

    @ApiModelProperty(value = "实收条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REAL_SKU_ECODE")
    private String realSkuEcode;

    @ApiModelProperty(value = "商品id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @ApiModelProperty(value = "商品编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @ApiModelProperty(value = "商品名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @ApiModelProperty(value = "入库单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "SC_B_IN_ID")
    private Long scBInId;

    @ApiModelProperty(value = "商品标记")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PRODUCT_MARK")
    private String productMark;

    @ApiModelProperty(value = "是否无原单条码")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_WITHOUT_ORIG")
    private Integer isWithoutOrig;

    @ApiModelProperty(value = "数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY")
    private BigDecimal qty;

    @ApiModelProperty(value = "退换货单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_RETURN_ORDER_ID")
    private Long ocBReturnOrderId;

    @ApiModelProperty(value = "是否匹配")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_MATCH")
    private Integer isMatch;

    @ApiModelProperty(value = "是否生成调整单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_GEN_ADJUST")
    private Integer isGenAdjust;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @ApiModelProperty(value = "是否生成入库单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_GEN_IN_ORDER")
    private Integer isGenInOrder;

    @ApiModelProperty(value = "是否生成错发调整单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_GEN_WRO_ADJUST")
    private Integer isGenWroAdjust;

    @ApiModelProperty(value = "是否生成冲无头件调整单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_GEN_MINUS_ADJUST")
    private Integer isGenMinusAdjust;

    @ApiModelProperty(value = "吊牌价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_LIST")
    private BigDecimal priceList;

    @ApiModelProperty(value = "实收条码国标码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GBCODE_ACTUAL")
    private String gbcodeActual;

    @ApiModelProperty(value = "实际发出条码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE_ACTUAL")
    private String psCSkuEcodeActual;

    @ApiModelProperty(value = "颜色id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_CLR_ID")
    private Long psCClrId;

    @ApiModelProperty(value = "颜色编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_CLR_ECODE")
    private String psCClrEcode;

    @ApiModelProperty(value = "颜色名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_CLR_ENAME")
    private String psCClrEname;

    @ApiModelProperty(value = "尺寸id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SIZE_ID")
    private Long psCSizeId;

    @ApiModelProperty(value = "尺寸编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SIZE_ECODE")
    private String psCSizeEcode;

    @ApiModelProperty(value = "尺寸名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SIZE_ENAME")
    private String psCSizeEname;

    @ApiModelProperty(value = "订单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "批次")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PRODUCT_DATE")
    private String productDate;


    @ApiModelProperty(value = "数字类型备用字段1")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @ApiModelProperty(value = "数字类型备用字段2")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @ApiModelProperty(value = "数字类型备用字段3")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @ApiModelProperty(value = "数字类型备用字段4")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @ApiModelProperty(value = "数字类型备用字段5")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @ApiModelProperty(value = "价格备用字段1")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "价格备用字段3")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @ApiModelProperty(value = "价格备用字段4")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @ApiModelProperty(value = "价格备用字段5")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @ApiModelProperty(value = "文本备用字段1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @ApiModelProperty(value = "文本备用字段2")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @ApiModelProperty(value = "文本备用字段3")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @ApiModelProperty(value = "文本备用字段4")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @ApiModelProperty(value = "文本备用字段5")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @ApiModelProperty(value = "品异（0.否 1.是）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PRODUCT_DIFF")
    private String productDiff;
}
