package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName OcBOrderOutStockRecord
 * @Description 订单缺货记录表
 * <AUTHOR>
 * @Date 2023/3/29 19:28
 * @Version 1.0
 */
@TableName(value = "oc_b_order_outstock_record")
@Data
@Document(index = "oc_b_order_outstock_record", type = "oc_b_order_outstock_record")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_order_outstock_record", description = "订单缺货记录表")
public class OcBOrderOutStockRecord extends BaseModel {

    private static final long serialVersionUID = 6030979257057570076L;

    @ApiModelProperty(value = "编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "订单ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "初始平台单号（确定唯一）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "最新缺货时间")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OUTSTOCK_DATE")
    private Date outstockDate;

    @ApiModelProperty(value = "异常说明")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "error_msg")
    private String errorMsg;
}
