package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * @author:洪艺安
 * @since: 2019/6/21
 * @create at : 2019/6/21 10:16
 */
@Getter
public enum PayTypeEnum {
    /**
     * 支付宝
     */
    ALIPAY(1 ,"支付宝"),
    /**
     * 微信
     */
    TXPAY(2 ,"微信"),
    /**
     * 现金
     */
    CASHPAY(3, "现金"),
    /**
     * 备用金
     */
    BACKUPAMTPAY(4, "备用金"),
    /**
     * 财付通
     */
    CFTPAY(5, "财付通"),
    /**
     * 银行
     */
    BANKPAY(6, "银行");

    String text;
    int val;

    PayTypeEnum(int val,String text) {
        this.text = text;
        this.val = val;
    }

    public String getText() {
        return text;
    }

    public int getVal() {
        return val;
    }

    public static String getTextByVal(Integer val) {
        for (PayTypeEnum payTypeEnum : values()) {
            if (payTypeEnum.getVal() == val) {
                return payTypeEnum.getText();
            }
        }
        return null;
    }
}

