package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_order_to_ag_task")
@Data
public class OcBOrderToAgTask extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "REFUND_ID")
    private String refundId;

    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @JSONField(name = "OC_B_ORDER_IDS")
    private String ocBOrderIds;

    @JSONField(name = "STATUS")
    private Integer status;

    @J<PERSON>NField(name = "RETRIES_TIMES")
    private Integer retriesTimes;

    @J<PERSON>NField(name = "NEXT_TIME")
    private Date nextTime;

    @J<PERSON><PERSON>ield(name = "REMARK")
    private String remark;

    @JSO<PERSON>ield(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}