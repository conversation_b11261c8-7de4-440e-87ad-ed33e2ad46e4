package com.jackrain.nea.oc.oms.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @author: lijin
 * @create: 2024-06-04
 * @description: 对等换货策略导入实体
 **/
@Data
public class StCEquityBarterStrategyImpVo {
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    /**
     * 类型名称
     */
    private String typeName;
    /**
     * 类型
     */
    private String type;
    /**
     * 平台店铺名称
     */
    private String platformShopName;
    /**
     * 平台店铺ID
     */
    private Long shopId;
    /**
     * 换货商品编码
     */
    private String skuCode;
    /**
     * 换货商品ID
     */
    private Long skuId;
    /**
     * 换货商品名称
     */
    private String skuName;
    /**
     * 换货数量字符串
     */
    private String skuQtyStr;
    /**
     * 换货数量
     */
    private BigDecimal skuQty;
    /**
     * 对等商品编码
     */
    private String equitySkuCode;
    /**
     * 对等商品ID
     */
    private Long equitySkuId;
    /**
     * 对等商品名称
     */
    private String equitySkuName;
    /**
     * 对等数量字符串
     */
    private String equitySkuQtyStr;
    /**
     * 对等数量
     */
    private BigDecimal equitySkuQty;
    /**
     * 缺货不还原
     */
    private String outStockNoRestore;
    /**
     * 行号
     */
    private int rowNum;
    /**
     * 错误信息
     */
    private String desc;

    public static StCEquityBarterStrategyImpVo importCreate(int index, Map<String, String> columnMap) {
        StCEquityBarterStrategyImpVo impVo = new StCEquityBarterStrategyImpVo();
        impVo.setTypeName(columnMap.get(rowStr + index + cellStr + 0));
        impVo.setPlatformShopName(columnMap.get(rowStr + index + cellStr + 1));
        impVo.setSkuCode(columnMap.get(rowStr + index + cellStr + 2));
        impVo.setSkuQtyStr(columnMap.get(rowStr + index + cellStr + 4));
        impVo.setEquitySkuCode(columnMap.get(rowStr + index + cellStr + 5));
        impVo.setEquitySkuQtyStr(columnMap.get(rowStr + index + cellStr + 7));
        impVo.setOutStockNoRestore(columnMap.get(rowStr + index + cellStr + 8));
        impVo.setRowNum(index+1);
        return impVo;
    }
}
