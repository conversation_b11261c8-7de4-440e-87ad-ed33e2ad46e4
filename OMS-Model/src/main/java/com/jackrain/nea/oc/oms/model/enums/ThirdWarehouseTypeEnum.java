package com.jackrain.nea.oc.oms.model.enums;

/**
 * @author: 夏继超
 * {@link com.jackrain.nea.cpext.model.Enum.ThirdWmsTypeEnum}
 * @since: 2020/4/10
 * create at : 2020/4/10 14:28
 */
@Deprecated
public enum ThirdWarehouseTypeEnum {
    //    0 菜鸟仓库  1  京东仓 2 其它仓库',
    ROOKIEWAREHOUSE("菜鸟仓", 0),

    JDWAREHOUSE("京仓", 1),

    OTHERWAREHOUSE("其他仓库", 2);


    String key;
    Integer val;

    ThirdWarehouseTypeEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

}
