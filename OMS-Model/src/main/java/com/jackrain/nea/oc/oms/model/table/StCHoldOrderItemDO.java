package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ST_C_HOLD_ORDER_ITEM")
@Data
public class StCHoldOrderItemDO extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "HOLD_ORDER_ID")
    private Long holdOrderId;

    @JSONField(name = "RULES_RECOGNITION")
    private String rulesRecognition;

    @JSONField(name = "CATEGORIES_ID")
    private Long categoriesId;

    @JSONField(name = "IN_THE_CLASS_ID")
    private Long inTheClassId;

    @J<PERSON>NField(name = "SMALL_CLASS_ID")
    private Long smallClassId;

    @JSONField(name = "GOODS_CODE_ID")
    private Long goodsCodeId;

    @JSONField(name = "GOODS_SKU_ID")
    private Long goodsSkuId;

    @JSONField(name = "PT_SKU_ID")
    private String ptSkuId;

    @JSONField(name = "PT_SPU_ID")
    private String ptSpuId;

    @JSONField(name = "MINIMUM_AMOUNT")
    private BigDecimal minimumAmount;

    @JSONField(name = "CONTENT")
    private String content;

    @JSONField(name = "CONTENT_ID")
    private Long contentId;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

}