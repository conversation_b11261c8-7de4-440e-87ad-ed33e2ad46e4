package com.jackrain.nea.oc.oms.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName DmsOrderManualSplitTempDTO
 * @Description 待拆的临时表
 * <AUTHOR>
 * @Date 2024/6/12 18:31
 * @Version 1.0
 */
@Data
public class DmsOrderManualSplitTempDTO implements Serializable {
    private static final long serialVersionUID = -8826934359755447902L;
    /**
     * 订单明细ID
     */
    private Long orderItemId;

    /**
     * 均摊数量
     */
    private Integer splitQty;

    /**
     * 剩余可拆的数量
     */
    private Integer remainingQty;
}
