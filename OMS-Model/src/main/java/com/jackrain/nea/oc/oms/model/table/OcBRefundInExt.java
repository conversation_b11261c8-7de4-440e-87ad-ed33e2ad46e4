package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-03-26
 * create at:  2019-03-26 09:52
 * 入库单扩展类
 */
@Data
public class OcBRefundInExt extends OcBRefundIn {
    private List<OcBRefundInProductItem> itemList;
    //********** begin 夏继超
    //作废状态翻译
    @JSONField(name = "INVALIDSTATE")
    @Field(type = FieldType.Keyword)
    private String invalidState;
    //入库状态翻译
    @JSONField(name = "WAREHOUSINGSTATUS")
    @Field(type = FieldType.Keyword)
    private String warehousingStatus;
    //创建时间
    @JSONField(name = "CREATETIME")
    @Field(type = FieldType.Keyword)
    private String createTime;
    //修改时间
    @JSONField(name = "UPDATETIME")
    @Field(type = FieldType.Keyword)
    private String updateTime;
    //匹配状态名
    @JSONField(name = "MATCHSTATUSNAME")
    @Field(type = FieldType.Keyword)
    private String matchstatusname;
    //*********  end   夏继超
    //退货入库单id 的集合
    private List<Long> idList;

    //特殊处理类型
    @JSONField(name = "SPECIALTYPENAME")
    @Field(type = FieldType.Keyword)
    private String specialtypeName;

    //入库数量
    @JSONField(name = "QTY_ALL")
    @Field(type = FieldType.Keyword)
    private BigDecimal qtyAll;


    //匹配是否关闭匹配名称
    @JSONField(name = "ISOFFMATCHNAME")
    @Field(type = FieldType.Keyword)
    private String isoffmatchname;

    @ApiModelProperty(value = "数异少（0.否 1.是）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "NUMLESSNAME")
    private String numLessName;

    @ApiModelProperty(value = "数异多（0.否 1.是）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "NUMMORENAME")
    private String numMoreName;

    @ApiModelProperty(value = "品异（0.否 1.是）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PRODUCTDIFFNAME")
    private String productDiffName;
}
