package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.oc.oms.model.result.QueryOrderTagResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 枚举-订单标签
 *
 * @author: xiwen.z
 * create at: 2019/3/7 0007
 */
public enum OcOrderTagEum {

    TAG_JOIN("1", "IS_MERGE", "#ff7676", "合", 1),
    TAG_BLOCK("1", "IS_INTERECEPT", "#84c9e2", "Hold", 2),
    TAG_RETREAT("1", "IS_INRETURNING", "#9be09b", "退款中", 3),
    TAG_GIFT("1", "IS_HASGIFT", "#f3c305", "赠", 4),
    TAG_DISMANTLE("1", "IS_SPLIT", "#e36aee", "拆", 5),
    TAG_CHANGE("2", "ORDER_TYPE", "#6660d2", "换", 6),
    TAG_HAND("手工新增", "ORDER_SOURCE", "#b935f0", "手", 7),
    TAG_TICKET("1", "IS_INVOICE", "#0489d6", "票", 8),
    TAG_ELE_PRE("1", "DOUBLE11_PRESALE_STATUS", "#eb3333", "预", 9),
    TAG_LACK("1", "IS_OUT_STOCK", "#0789a3", "缺", 13),
    TAG_TO_PAY("2", "PAY_TYPE", "#5fe9e1", "到", 16),
    TAG_COMBINATION("1", "IS_COMBINATION", "#996f25", "组", 19),
    TAG_DIFFPRICE("Y", "PRICE_LABEL", "#996f25", "虚", 20),
    TAG_LOCK("1", "LOCK_STATUS", "#FFB6C1", "锁", 21),
    TAG_LIVE("1", "LIVE_FLAG", "#FFB6C1", "播", 23),
    TAG_DELIVERY_URGENT("1", "IS_DELIVERY_URGENT", "#84c9e2", "急", 25),
    TAG_O2O("1", "IS_O2O_ORDER", "#6660d2", "O2O", 26),
    TAG_COPY("1", "IS_COPY_ORDER", "#0489d6", "复", 27),
    TAG_PROM("1", "IS_PROM_ORDER", "#e6e047", "促", 28),
    TAG_MODIFIED("1", "IS_MODIFIED_ORDER", "#e6e047", "改", 29),
    TAG_REAL_LACK("1", "IS_REAL_LACKSTOCK", "#0789a3", "实缺", 30),
    TAG_EXTRA("1", "IS_EXTRA", "#0489d6", "额", 31),
    TAG_WOS_CUT_LACK("1", "IS_WOS_CUT", "#969596", "截", 33),
    TAG_DETENTION("1", "IS_DETENTION", "#0489d6", "卡", 34),
    TAG_FORBIDDEN_DELIVERY("1", "IS_FORBIDDEN_DELIVERY", "#DC143C", "禁发", 35),
    TAG_CHANGE_WAREHOUSE("1", "IS_VIP_UPDATE_WAREHOUSE", "#FFA500", "改仓", 36),
    TAG_BACK_AUDIT_ING("1", "REVERSE_AUDIT_TYPE", "#FF00FF", "反审核中", 37),
    TAG_RESET_SHIP("1", "IS_RESET_SHIP", "#FF00FF", "补", 38),
    TAG_EQUAL_EXCHANGE("1", "IS_EQUAL_EXCHANGE", "#0489d6", "对等", 39),
    TAG_TMALL_CYCLE("1", "IS_CYCLE", "#fc4c4c", "周期购", 49),
    TAG_EXCEPTION("1", "IS_EXCEPTION", "#FF0000", "异常", 50),
    TAG_MEMBER("1", "IS_MEMBER", "#CFB53B", "会员", 51),
    TAG_TMALL_TO_DOOR("1", "IS_DELIVERY_TO_DOOR", "#CD6600", "上门", 52),
    TAG_IS_OVERDUE("1", "IS_OVERDUE", "#F4D04F", "逾期", 53),
    TAG_IS_UNAVAILABLE_SHOP("1", "IS_UNAVAILABLE_SHOP", "#1E90FF", "冻", 54),
    TAG_IS_CARPOOL("1", "IS_CARPOOL", "#2B78EE", "拼", 55),
    TAG_NO_RANGE("1", "IS_NO_RANGE", "#0000CD", "无范围", 56),
    TAG_IS_REMARK_GIFT("1", "IS_REMARK_GIFT", "#EE00EE", "备赠", 57),

    ;
    // 值
    String val;
    // 数据库字段
    String key;
    // 颜色
    String clr;
    // 文本
    String text;
    // 序号
    int sort;

    OcOrderTagEum(String v, String qn, String c, String s, int r) {
        this.val = v;
        this.key = qn;
        this.clr = c;
        this.text = s;
        this.sort = r;
    }

    /**
     * <AUTHOR>
     * @Description 根据sort值获取
     * @Date 3:12 下午 2021/6/17
     * @Param [sort]
     * @return com.jackrain.nea.oc.oms.model.result.QueryOrderTagResult
     **/
    public static QueryOrderTagResult getQueryOrderTagResultBySort(int sort) {
        QueryOrderTagResult o = new QueryOrderTagResult();
        for (OcOrderTagEum e : OcOrderTagEum.values()) {
            if (sort == e.getSort()) {
                o.setVal(e.getVal());
                o.setKey(e.getKey());
                o.setClr(e.getClr());
                o.setText(e.getText());
                o.setSort(e.getSort());
                break;
            }
        }
        return o;
    }

    public String getVal() {
        return val;
    }

    public String getKey() {
        return key;
    }

    public String getClr() {
        return clr;
    }

    public String getText() {
        return text;
    }

    public int getSort() {
        return sort;
    }

    /**
     * 转化所有枚举值为list<QueryOrderTagResult>
     *
     * @return list<QueryOrderTagResult>
     */
    public static List<QueryOrderTagResult> toQueryOrderTagResult() {
        List<QueryOrderTagResult> list = new ArrayList<>();
        for (OcOrderTagEum e : OcOrderTagEum.values()) {
            QueryOrderTagResult o = new QueryOrderTagResult();
            o.setVal(e.getVal());
            o.setKey(e.getKey());
            o.setClr(e.getClr());
            o.setText(e.getText());
            o.setSort(e.getSort());
            list.add(o);
        }
        return list;
    }

    /**
     * 根据枚举key集合,转化为list<QueryOrderTagResult>
     *
     * @param nlist list<Integer>
     * @return list<QueryOrderTagResult>
     */
    public static List<QueryOrderTagResult> toListQueryOrderTagResult(List<String> nlist) {
        List<QueryOrderTagResult> list = new ArrayList<QueryOrderTagResult>();
        if (nlist == null) {
            return list;
        }
        int nSize = nlist.size();
        for (int i = 0; i < nSize; i++) {
            for (OcOrderTagEum e : OcOrderTagEum.values()) {
                if (e.getKey().equals(nlist.get(i))) {
                    QueryOrderTagResult o = new QueryOrderTagResult();
                    o.setVal(e.getVal());
                    o.setKey(e.getKey());
                    o.setClr(e.getClr());
                    o.setText(e.getText());
                    o.setSort(e.getSort());
                    list.add(o);
                    break;
                }
            }
        }
        return list;
    }

    /**
     * 根据枚举文本转化对应枚举为QueryOrderTagResult
     *
     * @param s string
     * @return QueryOrderTagResult
     */
    public static QueryOrderTagResult getQueryOrderTagResult(String s) {
        QueryOrderTagResult o = new QueryOrderTagResult();
        if (s == null || s.trim().length() == 0) {
            return o;
        }
        for (OcOrderTagEum e : OcOrderTagEum.values()) {
            if (s.equals(e.getText())) {
                o.setVal(e.getVal());
                o.setKey(e.getKey());
                o.setClr(e.getClr());
                o.setText(e.getText());
                o.setSort(e.getSort());
                break;
            }
        }
        return o;
    }

    public static QueryOrderTagResult getQueryOrderTagResult(OcOrderTagEum e) {
        QueryOrderTagResult o = new QueryOrderTagResult();
        o.setVal(e.getVal());
        o.setKey(e.getKey());
        o.setClr(e.getClr());
        o.setText(e.getText());
        o.setSort(e.getSort());
        return o;
    }


    /**
     * 根据key获取
     *
     * @param key
     * @return
     */
    public static String getTextByKey(String key) {
        if (key == null || key.trim().length() == 0) {
            return null;
        }
        for (OcOrderTagEum e : OcOrderTagEum.values()) {
            if (key.equals(e.getKey())) {
                return e.getText();
            }
        }
        return null;
    }

}
