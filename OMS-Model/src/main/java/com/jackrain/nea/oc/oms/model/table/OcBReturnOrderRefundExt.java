package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-05-16
 * create at : 2019-05-16 1:50 PM
 */
@TableName(value = "oc_b_return_order_refund")
@Data
public class OcBReturnOrderRefundExt extends OcBReturnOrderRefund {

    @TableField(exist = false)
    @JSONField(name = "BILL_NO")
    private Long billNo;

    @TableField(exist = false)
    @JSONField(name = "QTY")
    private BigDecimal qty;

    @TableField(exist = false)
    @JSONField(name = "REAL_AMT")
    private BigDecimal realMoney;

    @JSONField(name = "SEX_ENAME")
    private String sexEname;

    @TableField(exist = false)
    @JSONField(name = "TO_AG_STATUS_NAME")
    private String toAgStatusName;

    @TableField(exist = false)
    @JSONField(name = "REFUND_STATUS_NAME")
    private String refundStatusName;

    @TableField(exist = false)
    @JSONField(name = "GIFT_TYPE_NAME")
    private String giftTypeName;

    @TableField(exist = false)
    private JSONObject selected;
}
