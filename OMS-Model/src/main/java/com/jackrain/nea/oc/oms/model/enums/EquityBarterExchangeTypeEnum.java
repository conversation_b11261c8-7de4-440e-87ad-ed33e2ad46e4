package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @author: lijin
 * @create: 2024-06-12
 * @description: 对等换货对等类型
 **/
public enum EquityBarterExchangeTypeEnum {
    MORE_FOR_LESS("1", "多换少"),
    EQUITY("2", "对等置换"),
    UNKNOWN("-1", "未知");
    @Getter
    private String key;
    @Getter
    private String desc;

    EquityBarterExchangeTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static EquityBarterExchangeTypeEnum getByKey(String key) {
        for (EquityBarterExchangeTypeEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return UNKNOWN;
    }

    public static EquityBarterExchangeTypeEnum getByDesc(String desc) {
        for (EquityBarterExchangeTypeEnum current : values()) {
            if (Objects.equals(current.getDesc(), desc)) {
                return current;
            }
        }
        return UNKNOWN;
    }
}
