package com.jackrain.nea.oc.oms.model.enums;

/**
 * 自动拆单状态
 *
 * @author: hulinyang
 * @since: 2019-07-31
 * create at : 2019-07-31 11:09  AutoASplitStatus
 */
public enum AutoSplitStatus {

    /**
     * 未拆分
     */
    UN_SPLIT,

    /**
     * 拆分中
     */
    TRAN_SPLIT,

    /**
     * 已拆分
     */
    ALREADY_SPLIT,

    /**
     * 再拆分
     */
    REPEAT_SPLIT;

    public int toInteger() {
        if (this == UN_SPLIT) {
            return 0;
        } else if (this == ALREADY_SPLIT) {
            return 1;
        } else if (this == TRAN_SPLIT) {
            return 2;
        } else if (this == REPEAT_SPLIT) {
            return 3;
        } else {
            return -1;
        }
    }
}
