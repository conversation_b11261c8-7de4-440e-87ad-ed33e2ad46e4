package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * 订单来源信息关系type
 *
 * <AUTHOR>
 */
public enum OcBOrderSourceRelationTypeEnum {

    UN_KNOWN(" ", "未知"),
    REISSUE("REISSUE", "补发");

    OcBOrderSourceRelationTypeEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    @Getter
    private String key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String enumToStringBykey(String key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (OcBOrderSourceRelationTypeEnum e : OcBOrderSourceRelationTypeEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return key;
    }
}
