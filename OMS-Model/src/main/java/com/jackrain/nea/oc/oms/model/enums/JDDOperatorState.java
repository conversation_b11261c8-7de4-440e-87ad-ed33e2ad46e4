package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Desc : 京东厂直.操作状态
 * <AUTHOR> xiWen
 * @Date : 2022/3/30
 */
public enum JDDOperatorState {

    /**
     * 初始
     */
    INIT(0),

    /**
     * 新订单
     */
    NEW(5),

    /**
     * 出库中
     */
    DISTRIBUTION(9),

    /**
     * 等待发货
     */
    WAIT_DELIVERY(10),

    /**
     * 发货中
     */
    SHIPPING(15),

    /**
     * 发货完成
     */
    DELIVERY_COMPLETED(16);

    Integer val;

    public Integer val() {
        return this.val;
    }

    JDDOperatorState(Integer v) {
        this.val = v;
    }


}
