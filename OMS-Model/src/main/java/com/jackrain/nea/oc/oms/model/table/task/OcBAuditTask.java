package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.sql.Timestamp;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_audit_task", type = "oc_b_audit_task")
@ApiModel(value="oc_b_audit_task", description="")
public class OcBAuditTask extends BaseModel {

    @Field(type = FieldType.Long)
    @JSONField(name= "ID")
    private Long id;

    @Field(type = FieldType.Long)
    @JSONField(name= "ORDER_ID")
    private Long orderId;

    @Field(type = FieldType.Integer)
    @JSONField(name= "STATUS")
    private Integer status;

    @ApiModelProperty(value = "传WOS状态")
    @Field(type = FieldType.Integer)
    @JSONField(name= "WOS_STATUS")
    private Integer wosStatus;

    @ApiModelProperty(value = "店铺id")
    @Field(type = FieldType.Long)
    @JSONField(name= "SHOP_ID")
    private Long shopId;

    @ApiModelProperty(value = "重试次数")
    @Field(type = FieldType.Integer)
    @JSONField(name= "RETRY_NUMBER")
    private Integer retryNumber;

    @ApiModelProperty(value = "下次执行时间")
    @Field(type = FieldType.Long)
    @JSONField(name= "NEXT_TIME")
    private Long nextTime;

    @ApiModelProperty(value = "hold单时间")
    @Field(type = FieldType.Long)
    @JSONField(name= "HOLD_TIME")
    private Timestamp holdTime;
}
