package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 猫超订单主表
 */
@TableName(value = "ip_b_alibaba_ascp_order")
@Data
@Document(index = "ip_b_alibaba_ascp_order", type = "ip_b_alibaba_ascp_order")
public class IpBAlibabaAscpOrder extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 供应商id
     */
    @JSONField(name = "SUPPLIER_ID")
    @Field(type = FieldType.Keyword)
    private String supplierId;
    /**
     * 供应商名称
     */
    @JSONField(name = "SUPPLIER_NAME")
    @Field(type = FieldType.Keyword)
    private String supplierName;
    /**
     * 平台来源
     */
    @JSONField(name = "ORDER_SOURCE")
    @Field(type = FieldType.Keyword)
    private String orderSource;
    /**
     * 履约单号
     */
    @JSONField(name = "BIZ_ORDER_CODE")
    @Field(type = FieldType.Keyword)
    private String bizOrderCode;
    /**
     * 发货仓编码
     */
    @JSONField(name = "STORE_CODE")
    @Field(type = FieldType.Keyword)
    private String storeCode;
    /**
     * 发货仓名称
     */
    @JSONField(name = "STORE_NAME")
    @Field(type = FieldType.Keyword)
    private String storeName;
    /**
     * 收件方邮编
     */
    @JSONField(name = "RECEIVER_ZIP_CODE")
    @Field(type = FieldType.Keyword)
    private String receiverZipcode;
    /**
     * 收件方国家
     */
    @JSONField(name = "RECEIVER_COUNTRY")
    @Field(type = FieldType.Keyword)
    private String receiverCountry;
    /**
     * 收件方省份
     */
    @JSONField(name = "RECEIVER_PROVINCE")
    @Field(type = FieldType.Keyword)
    private String receiverProvince;
    /**
     * 收件方城市
     */
    @JSONField(name = "RECEIVER_CITY")
    @Field(type = FieldType.Keyword)
    private String receiverCity;
    /**
     * 收件方区县
     */
    @JSONField(name = "RECEIVER_AREA")
    @Field(type = FieldType.Keyword)
    private String receiverArea;
    /**
     * 收件方镇
     */
    @JSONField(name = "RECEIVE_TOWN")
    @Field(type = FieldType.Keyword)
    private String receiveTown;
    /**
     * 收件方地址
     */
    @JSONField(name = "RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;
    /**
     * 收件人名称
     */
    @JSONField(name = "RECEIVER_NAME")
    @Field(type = FieldType.Keyword)
    private String receiverName;
    /**
     * 收件人手机
     */
    @JSONField(name = "RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String receiverMobile;
    /**
     * 收件人电话
     */
    @JSONField(name = "RECEIVER_PHONE")
    @Field(type = FieldType.Keyword)
    private String receiverPhone;
    /**
     * 发件方邮编
     */
    @JSONField(name = "SENDER_ZIP_CODE")
    @Field(type = FieldType.Keyword)
    private String senderZipCode;
    /**
     * 发件方国家
     */
    @JSONField(name = "SENDER_COUNTRY")
    @Field(type = FieldType.Keyword)
    private String senderCountry;
    /**
     * 发件方省份
     */
    @JSONField(name = "SENDER_PROVINCE")
    @Field(type = FieldType.Keyword)
    private String senderProvince;
    /**
     * 发件方城市
     */
    @JSONField(name = "SENDER_CITY")
    @Field(type = FieldType.Keyword)
    private String senderCity;
    /**
     * 发件方区县
     */
    @JSONField(name = "SENDER_AREA")
    @Field(type = FieldType.Keyword)
    private String senderArea;
    /**
     * 发件方镇村
     */
    @JSONField(name = "SENDER_TOWN")
    @Field(type = FieldType.Keyword)
    private String senderTown;
    /**
     * 发件方地址
     */
    @JSONField(name = "SENDER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String senderAddress;
    /**
     * 发件方名称
     */
    @JSONField(name = "SENDER_NAME")
    @Field(type = FieldType.Keyword)
    private String senderName;
    /**
     * 发件方手机
     */
    @JSONField(name = "SENDER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String senderMobile;
    /**
     * 发件方电话
     */
    @JSONField(name = "SENDER_PHONE")
    @Field(type = FieldType.Keyword)
    private String senderPhone;

    /**
     * 预约配送时间
     */
    @JSONField(name = "APPOINT_DELIVERY_TIME")
    @Field(type = FieldType.Long)
    private Date appointDeliveryTime;
    /**
     * 预计送达时间
     */
    @JSONField(name = "APPOINT_ARRIVED_TIME")
    @Field(type = FieldType.Long)
    private Date appointArrivedTime;

    /**
     * 配送公司名称
     */
    @JSONField(name = "TMS_SERVICE_NAME")
    @Field(type = FieldType.Keyword)
    private String tmsServiceName;
    /**
     * 运费模式
     */
    @JSONField(name = "POST_MODE")
    @Field(type = FieldType.Keyword)
    private String postMode;
    /**
     * 运费金额
     */
    @JSONField(name = "POST_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal postFee;

    /**
     * 履约创单时间
     */
    @JSONField(name = "ORDER_CREATE_TIME")
    @Field(type = FieldType.Keyword)
    private String orderCreateTime;
    /**
     * 扩展属性
     */
    @JSONField(name = "EXTRA_CONTENT")
    @Field(type = FieldType.Keyword)
    private String extraContent;

    //region 数据库扩展字段
    /**
     * 插入时间
     */
    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertDate;

    /**
     * 交易结束时间
     */
    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Long)
    private Date endTime;

    /**
     * 交易修改时间
     */
    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    /**
     * 付款时间
     */
    @JSONField(name = "PAY_TIME")
    @Field(type = FieldType.Long)
    private Date payTime;

    /**
     * 交易创建时间
     */
    @JSONField(name = "CREATED")
    @Field(type = FieldType.Long)
    private Date created;

    /**
     * 店铺Id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer isTrans;

    /**
     * 转化时间
     */
    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transDate;

    /**
     * 转换备注
     */
    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysRemark;

    /**
     * 转换次数
     */
    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;


    /**
     * 创建人ID
     */
    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerId;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownereName;

    /**
     * 创建人用户名
     */
    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownerName;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    private Date creationDate;

    /**
     * 修改人id
     */
    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierId;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifiereName;

    /**
     * 修改人用户名
     */
    @JSONField(name = "modifiername")
    @Field(type = FieldType.Keyword)
    private String modifierName;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    private Date modifiedDate;

    /**
     * 是否可用 默认 "y"
     */
    @JSONField(name = "isactive")
    @Field(type = FieldType.Keyword)
    private String isActive;
    //endregion

    //region 预留扩展字段
    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;
    //endregion
}
