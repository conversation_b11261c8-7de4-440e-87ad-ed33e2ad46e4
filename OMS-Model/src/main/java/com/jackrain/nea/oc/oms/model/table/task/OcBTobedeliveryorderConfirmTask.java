package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "oc_b_tobedeliveryorder_confirm_task")
@Data
public class OcBTobedeliveryorderConfirmTask extends BaseModel {
    private static final long serialVersionUID = -3777545402820122807L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ORDER_ID")
    private Long orderId;

    @JSO<PERSON>ield(name = "STATUS")
    private Integer status;

    @JSONField(name = "FAIL_COUNT")
    private Integer failCount;

    @JSONField(name = "FAIL_REASON")
    private String failReason;
}