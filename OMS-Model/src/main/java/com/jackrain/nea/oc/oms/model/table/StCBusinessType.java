package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @program: r3-oc-oms
 * @description: 单据业务类型
 * @author: caomalai
 * @create: 2022-07-09 13:29
 **/
@TableName(value = "st_c_business_type")
@Data
public class StCBusinessType extends BaseModel {
    private static final long serialVersionUID = -3718889469445984120L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 单据类型 1=订单、2=退单、3=退款单
     */
    @JSONField(name = "BILL_TYPE")
    private Integer billType;

    /**
     * 业务类型名称
     */
    @JSONField(name = "ENAME")
    private String ename;

    /**
     * 业务类型编码
     */
    @JSONField(name = "ECODE")
    private String ecode;

    /**
     * 主业务类型
     */
    @JSONField(name = "PARENT_CODE")
    private String parentCode;

    /**
     * 退款业务类型(退货退款)
     */
    @JSONField(name = "RETURN_REFUND_TYPE_ID")
    private Long returnRefundTypeId;

    /**
     * 退款业务类型(仅退款)
     */
    @JSONField(name = "REFUND_TYPE_ID")
    private Long refundTypeId;

    /**
     * 退货业务类型
     */
    @JSONField(name = "RETURN_TYPE_ID")
    private Long returnTypeId;

    /**
     * 订单业务类型(补发)
     */
    @JSONField(name = "RESEND_TYPE_ID")
    private Long resendTypeId;

    /**
     * 提货
     */
    @JSONField(name = "PICK_GOODS_TYPE_ID")
    private Long pickGoodsTypeId;

    /**
     * 第三方编码
     */
    @JSONField(name = "THIRD_CODE")
    private String thirdCode;

    /**
     * 出库通知单类型编码
     */
    @JSONField(name = "OUT_TYPE_CODE")
    private String outTypeCode;

    /**
     * 入库通知单类型编码
     */
    @JSONField(name = "IN_TYPE_CODE")
    private String inTypeCode;

    /**
     * 是否寻源占单
     */
    @JSONField(name = "IS_SOURCE_OCCUPY")
    private Integer isSourceOccupy;

    /**
     * 是否允许复制 0=否、1=是
     */
    @JSONField(name = "IS_ALLOW_COPY")
    private Integer isAllowCopy;

    /**
     * 是否允许补发 0=否、1=是
     */
    @JSONField(name = "IS_ALLOW_RESEND")
    private Integer isAllowResend;

    /**
     * 是否允许手工建退单
     */
    @JSONField(name = "IS_ALLOW_HAND_RETURN")
    private Integer isAllowHandReturn;

    /**
     * 退货是否校验原单 0=否、1=是
     */
    @JSONField(name = "IS_CHECK_SOURCE")
    private Integer isCheckSource;

    /**
     * 是否允许创建额外退款单 0=否、1=是
     */
    @JSONField(name = "IS_ALLOW_EXTRA_REFUND")
    private Integer isAllowExtraRefund;

    @JSONField(name = "SAP_TYPE")
    private Integer sapType;

    /**
     * 是否可以取消订单,Y是 N否
     */
    @JSONField(name = "IS_CANCEL_ORDER")
    private String isCancelOrder;

    /**
     * 是否可以取消合并,Y是 N否
     */
    @JSONField(name = "IS_MERGE_ORDER")
    private String isMergeOrder;


}
