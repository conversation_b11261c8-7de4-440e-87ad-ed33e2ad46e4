package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Auther: 黄志优
 * @Date: 2020/8/28 13:41
 * @Description:
 */
public enum QiMenMqMethodEnum {

    RECEIVE_ORDER_STATUS_CALLBACK("3e9nl9rhrg.burgeon.taobao.pos.salesorder.update", "奇门POS接单状态回传"),
    INSERT_REFUND_IN("3e9nl9rhrg.burgeon.taobao.pos.order.return.add", "奇门退单新增");

    String method;
    String txt;

    QiMenMqMethodEnum(String method, String txt) {
        this.method = method;
        this.txt = txt;
    }

    public String getMethod() {
        return method;
    }

    public String getTxt() {
        return txt;
    }
}
