package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * @ClassName InvoiceType
 * @Description
 * @Date 2022/8/31 下午8:07
 * @Created by wuhang
 */
@Getter
public enum InvoiceTypeEnum {
    NORMAL("0","普票"),
    SPECIAL("1","专票");

    private String code;
    private String value;

    InvoiceTypeEnum(String code,String value){
        this.code = code;
        this.value = value;
    }
}
