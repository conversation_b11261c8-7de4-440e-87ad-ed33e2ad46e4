package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * 应付款调整单-单据类型
 *
 * @author: 陈俊明
 * @since: 2019-08-09
 * @create at : 2019-08-09 上午 10:38
 */
@Getter
public enum PayBillTypeEnum {
    /**
     * 发货快递丢件
     */
    PAY_BF(1 ,"发货快递丢件"),

    /**
     * 仓库已发未揽
     */
    PAY_STORE(2 ,"仓库已发未揽"),

    /**
     * 其他
     */
    PAY_OTHER(3, "其他"),

    /**
     * 丢单赔付-仅退款
     */
    PAY_TK(4, "退货快递丢件");

    String text;
    int val;

    PayBillTypeEnum(int val,String text) {
        this.text = text;
        this.val = val;
    }

    public String getText() {
        return text;
    }

    public int getVal() {
        return val;
    }

}
