package com.jackrain.nea.oc.oms.model.table;


import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_jingdong_direct")
@Data
public class IpBJingdongDirect extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CUSTOM_ORDER_ID")
    private String customOrderId;

    @J<PERSON><PERSON><PERSON>(name = "PAY")
    private BigDecimal pay;

    @JSONField(name = "ORDER_STATE")
    private Integer orderState;

    @J<PERSON>NField(name = "OPERATOR_STATE")
    private Integer operatorState;

    @J<PERSON>NField(name = "CONSIGNEE_NAME")
    private String consigneeName;

    @JSONField(name = "POSTCODE")
    private String postcode;

    @J<PERSON><PERSON>ield(name = "EXPECTED_DELIVERY_TIME")
    private Date expectedDeliveryTime;

    @JSONField(name = "TELEPHONE")
    private String telephone;

    @JSONField(name = "PHONE")
    private String phone;

    @JSONField(name = "EMAIL")
    private String email;

    @JSONField(name = "ADDRESS")
    private String address;

    @JSONField(name = "ORDER_TIME")
    private Date orderTime;

    @JSONField(name = "ORDER_REMARK")
    private String orderRemark;

    @JSONField(name = "ORDER_CREATE_DATE")
    private Date orderCreateDate;

    @JSONField(name = "IS_NOTICE")
    private Integer isNotice;

    @JSONField(name = "SEND_PAY")
    private String sendPay;

    @JSONField(name = "ORDER_SOURCE")
    private String orderSource;

    @JSONField(name = "PAYMENT_CATEGORY")
    private String paymentCategory;

    @JSONField(name = "PAYMENT_CATEGORY_DISP_NAME")
    private String paymentCategoryDispName;

    @JSONField(name = "CREATE_DATE")
    private Date createDate;

    @JSONField(name = "PIN")
    private String pin;

    @JSONField(name = "MEMO_BY_VENDOR")
    private String memoByVendor;

    @JSONField(name = "PARENT_ORDER_ID")
    private Long parentOrderId;

    @JSONField(name = "REFUND_SOURCE_FLAG")
    private Integer refundSourceFlag;

    @JSONField(name = "PROVINCE_NAME")
    private String provinceName;

    @JSONField(name = "CITY_NAME")
    private String cityName;

    @JSONField(name = "COUNTY_NAME")
    private String countyName;

    @JSONField(name = "TOWN_NAME")
    private String townName;

    @JSONField(name = "REDUCE_COUNT")
    private BigDecimal reduceCount;

    @JSONField(name = "TOTAL_CARRIAGE")
    private BigDecimal totalCarriage;

    @JSONField(name = "SELLER_NICK")
    private String sellerNick;

    @JSONField(name = "MODIFIED")
    private Date modified;

    @JSONField(name = "VENDOR_STORE_ID")
    private Long vendorStoreId;

    @JSONField(name = "VENDOR_STORE_NAME")
    private String vendorStoreName;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "TRANS_COUNT")
    private Integer transCount;

    @JSONField(name = "ISTRANS")
    private Integer istrans;

    @JSONField(name = "TRANS_REMARK")
    private String transRemark;

    @JSONField(name = "SYSREMARK")
    private String sysremark;

    @JSONField(name = "CHANNEL_TYPE")
    private Integer channelType;

    @JSONField(name = "PRO_TRANS_REMARK")
    private String proTransRemark;

    @JSONField(name = "TRANSDATE")
    private Date transdate;

    @JSONField(name = "OAID")
    private String oaid;

    @JSONField(name = "EXT_INFO")
    private String extInfo;

    @JSONField(name = "OUT_PLATFORM_ORDER_INFO")
    private String outPlatformOrderInfo;
}
