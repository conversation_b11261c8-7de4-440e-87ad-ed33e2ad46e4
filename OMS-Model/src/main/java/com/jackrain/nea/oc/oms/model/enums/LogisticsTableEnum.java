package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.exception.NDSException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> wang<PERSON>yu
 * @since : 2022/6/16
 * description : 物流关联表名枚举
 */
@AllArgsConstructor
@Getter
public enum LogisticsTableEnum {
    /**
     * table name list
     */
    OC_B_ORDER("OC_B_ORDER","零售发货单"),
    OC_B_RETURN_ORDER("OC_B_RETURN_ORDER","退换货单");

    private final String tableName;
    private final String desc;

    /**
     * 根据表名获取对应枚举
     * @param tableName 表名
     * @return LogisticsTableEnum
     */
    public static LogisticsTableEnum getEnum(String tableName) {
        for (LogisticsTableEnum value : values()) {
            if (value.getTableName().equals(tableName)) {
                return value;
            }
        }
        throw new NDSException("匹配表名异常! tableName :" + tableName);
    }
}
