package com.jackrain.nea.oc.oms.model.enums;

/**
 * 预售状态（1已付款，0未付款）
 *
 * @author: 易邵峰
 * @since: 2019-03-08
 * create at : 2019-03-08 14:52
 */
public enum OmsPreSaleStatus {
    /**
     * 非预售
     */
    NO_PRE_SALE,

    /**
     * 预售
     */
    PRE_SALE;

    /**
     * 0:非预售  非0：预售
     *
     * @return
     */
    public int toInteger() {
        if (this == NO_PRE_SALE) {
            return 0;
        } else {
            return 1;
        }
    }

}
