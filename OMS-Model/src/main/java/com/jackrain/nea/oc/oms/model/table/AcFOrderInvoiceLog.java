package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ac_f_order_invoice_log")
@Data
public class AcFOrderInvoiceLog extends BaseModel {
    private static final long serialVersionUID = 7970640339337548610L;
    @J<PERSON><PERSON>ield(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "AC_F_ORDER_INVOICE_ID")
    private Long acFOrderInvoiceId;

    @JSONField(name = "LOG_TYPE")
    private String logType;

    @J<PERSON>NField(name = "LOG_CONTENT")
    private String logContent;

    @J<PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @J<PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;
}