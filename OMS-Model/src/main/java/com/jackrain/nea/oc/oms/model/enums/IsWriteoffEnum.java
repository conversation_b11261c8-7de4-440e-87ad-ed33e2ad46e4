package com.jackrain.nea.oc.oms.model.enums;


/**
 * 是否插入核销流水
 *
 * @author: 周琳胜
 * create at: 2019/3/25 13:20
 */
public enum IsWriteoffEnum {


    /**
     * 是否插入核销流水
     */
    NOT_WRITEOFF("未插入核销流水", 0),

    ALREADY_WRITEOFF("已插入核销流水", 1);

    String key;
    Integer val;

    IsWriteoffEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }


}


