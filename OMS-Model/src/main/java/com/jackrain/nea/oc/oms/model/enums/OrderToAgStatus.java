package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020/11/4 3:39 下午
 */
@Getter
public enum OrderToAgStatus {

    UNTREATED(0, "未处理"),
    PROCESSED(1, "已处理");

    Integer val;
    String description;

    OrderToAgStatus(int v, String s) {
        this.val = v;
        this.description = s;
    }

    public static String getTextByVal(Integer integer) {
        if (integer == null) {
            return null;
        }
        for (OrderToAgStatus o : OrderToAgStatus.values()) {
            if (o.getVal().equals(integer)) {
                return o.getDescription();
            }
        }
        return "";
    }
}
