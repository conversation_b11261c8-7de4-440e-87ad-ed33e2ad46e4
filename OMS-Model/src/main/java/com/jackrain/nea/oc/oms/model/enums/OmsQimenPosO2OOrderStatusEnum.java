package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * @Auther: 黄志优
 * @Date: 2020/9/14 15:31
 * @Description:
 */
public enum OmsQimenPosO2OOrderStatusEnum {

    STANDPLAT_SUBSTITUTION("待发货", 1),
    WAREHOUSE_DELIVERY("仓库发货", 2);

    OmsQimenPosO2OOrderStatusEnum(String key, int val) {
        this.key = key;
        this.val = val;
    }

    @Getter
    String key;

    @Getter
    int val;
}
