package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ac_f_order_invoice")
@Data
@Document(index = "ac_f_order_invoice",type = "ac_f_order_invoice")
public class AcFOrderInvoice extends BaseModel {
    private static final long serialVersionUID = 3604068419637835169L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    private String tid;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SUPPLIER_ID")
    @Field(type = FieldType.Long)
    private Long cpCSupplierId;

    @JSONField(name = "CP_C_SUPPLIER_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCSupplierEname;

    @JSONField(name = "CP_C_SUPPLIER_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCSupplierEcode;

    @JSONField(name = "CP_C_PLATFORM_ID")
    @Field(type = FieldType.Long)
    private Long cpCPlatformId;

    @JSONField(name = "CP_C_PLATFORM_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEname;

    @JSONField(name = "CP_C_PLATFORM_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEcode;

    @JSONField(name = "ORDER_STATUS")
    @Field(type = FieldType.Integer)
    private Integer orderStatus;

    @JSONField(name = "APPLY_INVOICE_DATE")
    @Field(type = FieldType.Long)
    private Date applyInvoiceDate;

    @JSONField(name = "SYNC_PLATFORM_STATUS")
    @Field(type = FieldType.Integer)
    private Integer syncPlatformStatus;

    @JSONField(name = "INVOICE_HEADER")
    @Field(type = FieldType.Keyword)
    private String invoiceHeader;

    @JSONField(name = "BLUE_TICKET_ID")
    @Field(type = FieldType.Long)
    private Long blueTicketId;

    @JSONField(name = "INVOICE_CODE")
    @Field(type = FieldType.Keyword)
    private String invoiceCode;

    @JSONField(name = "INVOICE_NUMBER")
    @Field(type = FieldType.Keyword)
    private String invoiceNumber;

    @JSONField(name = "INVOICE_DATE")
    @Field(type = FieldType.Long)
    private Date invoiceDate;

    @JSONField(name = "INVOICE_LINK_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String invoiceLinkAddress;

    @JSONField(name = "PAYEE")
    @Field(type = FieldType.Keyword)
    private String payee;

    @JSONField(name = "DRAWER")
    @Field(type = FieldType.Keyword)
    private String drawer;

    @JSONField(name = "REVIEWER")
    @Field(type = FieldType.Keyword)
    private String reviewer;

    @JSONField(name = "TICKET_TYPE")
    @Field(type = FieldType.Keyword)
    private String ticketType;

    @JSONField(name = "INVOICE_KIND")
    @Field(type = FieldType.Keyword)
    private String invoiceKind;

    @JSONField(name = "INVOICE_TYPE")
    @Field(type = FieldType.Keyword)
    private String invoiceType;

    @JSONField(name = "HEADER_TYPE")
    @Field(type = FieldType.Keyword)
    private String headerType;

    @JSONField(name = "INVOICE_CHANNEL")
    @Field(type = FieldType.Keyword)
    private String invoiceChannel;

    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    private String billNo;

    @JSONField(name = "TAXPAYER_NO")
    @Field(type = FieldType.Keyword)
    private String taxpayerNo;

    @JSONField(name = "UNIT_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String unitAddress;

    @JSONField(name = "UNIT_NAME")
    @Field(type = FieldType.Keyword)
    private String unitName;

    @JSONField(name = "UNIT_MOBILE")
    @Field(type = FieldType.Keyword)
    private String unitMobile;

    @JSONField(name = "OPENING_BANK")
    @Field(type = FieldType.Keyword)
    private String openingBank;

    @JSONField(name = "BANK_ACCOUNT")
    @Field(type = FieldType.Keyword)
    private String bankAccount;

    @JSONField(name = "INVOICE_NO_TAX_AMT")
    @Field(type = FieldType.Double)
    private BigDecimal invoiceNoTaxAmt;

    @JSONField(name = "INVOICE_INCLUSIVE_TAX_AMT")
    @Field(type = FieldType.Double)
    private BigDecimal invoiceInclusiveTaxAmt;

    @JSONField(name = "INVOICE_TAX_AMT")
    @Field(type = FieldType.Double)
    private BigDecimal invoiceTaxAmt;

    @JSONField(name = "INVOICE_REMARK")
    @Field(type = FieldType.Keyword)
    private String invoiceRemark;

    @JSONField(name = "INVOICE_CONTENT")
    @Field(type = FieldType.Keyword)
    private String invoiceContent;

    @JSONField(name = "EMAIL")
    @Field(type = FieldType.Keyword)
    private String email;

    @JSONField(name = "PHONE")
    @Field(type = FieldType.Keyword)
    private String phone;

    @JSONField(name = "RECEIVER")
    @Field(type = FieldType.Keyword)
    private String receiver;

    @JSONField(name = "RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;

    @JSONField(name = "INVOICE_STATUS")
    @Field(type = FieldType.Keyword)
    private String invoiceStatus;

    @JSONField(name = "CHANGE_INVOICE_STATUS")
    @Field(type = FieldType.Keyword)
    private String changeInvoiceStatus;

    @JSONField(name = "FREEZE_STATUS")
    @Field(type = FieldType.Keyword)
    private String freezeStatus;

    @JSONField(name = "RED_RUSH_STATUS")
    @Field(type = FieldType.Keyword)
    private String redRushStatus;

    @JSONField(name = "AUDIT_STATUS")
    @Field(type = FieldType.Keyword)
    private String auditStatus;

    @JSONField(name = "CANCEL_STATUS")
    @Field(type = FieldType.Keyword)
    private String cancelStatus;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "IS_SYNC_PLATFORM")
    @Field(type = FieldType.Keyword)
    private String isSyncPlatform;

    @JSONField(name = "INVOICE_NODE")
    @Field(type = FieldType.Keyword)
    private String invoiceNode;

    @JSONField(name = "TAX_MACHINE_NO")
    @Field(type = FieldType.Keyword)
    private String taxMachineNo;

    @JSONField(name = "SUPPLIER_TAXPAYER_NO")
    @Field(type = FieldType.Keyword)
    private String supplierTaxpayerNo;

    @JSONField(name = "SUPPLIER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String supplierAddress;

    @JSONField(name = "SUPPLIER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String supplierMobile;

    @JSONField(name = "SUPPLIER_OPENING_BANK")
    @Field(type = FieldType.Keyword)
    private String supplierOpeningBank;

    @JSONField(name = "SUPPLIER_BANK_ACCOUNT")
    @Field(type = FieldType.Keyword)
    private String supplierBankAccount;

    @JSONField(name = "INVOICE_APPLY_ID")
    @Field(type = FieldType.Long)
    private Long invoiceApplyId;

    @JSONField(name = "MERGE_INVOICE_FLAG")
    @Field(type = FieldType.Keyword)
    private String mergeInvoiceFlag;

    /** 开票失败原因 */
    @JSONField(name = "FAIL_REASON")
    @Field(type = FieldType.Keyword)
    private String failReason;

    @JSONField(name = "AMOUNT_MORE_THAN_BEFORE_FLAG")
    @Field(type = FieldType.Keyword)
    private String amountMoreThanBeforeFlag;

    @JSONField(name = "E_XML_URL")
    @Field(type = FieldType.Keyword)
    private String eXmlUrl;
}