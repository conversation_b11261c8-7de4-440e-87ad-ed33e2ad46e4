package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName OcBPreOrder
 * @Description 订单预导入主表
 * <AUTHOR>
 * @Date 2022/10/11 09:06
 * @Version 1.0
 */
@TableName(value = "oc_b_pre_order")
@Data
@Document(index = "oc_b_pre_order", type = "oc_b_pre_order")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_pre_order", description = "订单预导入主表")
public class OcBPreOrder extends BaseModel {

    @ApiModelProperty(value = "预导入订单ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "下单店铺标题")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @ApiModelProperty(value = "下单店铺ID")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @ApiModelProperty(value = "下单店铺编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @ApiModelProperty(value = "配送费用")
    @Field(type = FieldType.Double)
    @JSONField(name = "SHIP_AMT")
    private BigDecimal shipAmt;

    @ApiModelProperty(value = "用户昵称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "USER_NICK")
    private String userNick;

    @ApiModelProperty(value = "平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "付款时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "PAY_TIME")
    private Date payTime;

    @ApiModelProperty(value = "支付方式")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PAY_TYPE_NAME")
    private String payTypeName;

    @ApiModelProperty(value = "收货人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_NAME")
    private String receiverName;

    @ApiModelProperty(value = "收货人的手机号码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_MOBILE")
    private String receiverMobile;

    @ApiModelProperty(value = "收货人的电话号码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_PHONE")
    private String receiverPhone;

    @ApiModelProperty(value = "买家省名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    private String cpCRegionProvinceEname;

    @ApiModelProperty(value = "买家市名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_CITY_ENAME")
    private String cpCRegionCityEname;

    @ApiModelProperty(value = "买家区名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    private String cpCRegionAreaEname;

    @ApiModelProperty(value = "买家收货详细地址")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;

    @ApiModelProperty(value = "收货人邮编")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_ZIP")
    private String receiverZip;

    @ApiModelProperty(value = "商品数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_ALL")
    private BigDecimal qtyAll;

    @ApiModelProperty(value = "下单时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "ORDER_DATE")
    private Date orderDate;

    @ApiModelProperty(value = "买家留言")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUYER_MESSAGE")
    private String buyerMessage;

    @ApiModelProperty(value = "卖家备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SELLER_MEMO")
    private String sellerMemo;

    @ApiModelProperty(value = "淘宝收货人信息加密串")
    @JSONField(name = "OAID")
    @Field(type = FieldType.Keyword)
    private String oaid;

    @ApiModelProperty(value = "销售员名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALESMAN_NAME")
    private String salesmanName;

    @ApiModelProperty(value = "行号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ROW_NUM")
    private Integer rowNum;

    @ApiModelProperty(value = "流水号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SERIAL_NUMBER")
    private String serialNumber;

    @ApiModelProperty(value = "转换状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "TRANSFER_STATUS")
    private Integer transferStatus;

    @ApiModelProperty(value = "成交单价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_ACTUAL")
    private BigDecimal priceActual;

    @ApiModelProperty(value = "系统备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SYSREMARK")
    private String sysremark;

    @Field(type = FieldType.Integer)
    @JSONField(name = "PAY_TYPE")
    private Integer payType;

    @ApiModelProperty(value = "平台售价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PLATFORM_PRICE")
    private BigDecimal platformPrice;
}
