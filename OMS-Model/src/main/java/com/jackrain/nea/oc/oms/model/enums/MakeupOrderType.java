package com.jackrain.nea.oc.oms.model.enums;

/**
 * 补偿订单类型
 *
 * @author: 易邵峰
 * @since: 2019-03-06
 * create at : 2019-03-06 16:42
 */
public enum MakeupOrderType {

    /**
     * 转单补偿
     */
    TRANSFER_ORDER,

    /**
     * 不需要补偿
     */
    DO_NOT_MAKEUP;


    public int toInteger() {
        if (this == MakeupOrderType.TRANSFER_ORDER) {
            return 1;
        } else if (this == MakeupOrderType.DO_NOT_MAKEUP) {
            return -1;
        } else {
            return 0;
        }
    }

}
