package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@TableName(value = "ip_b_standplat_refund")
@Data
@Document(index = "ip_b_standplat_refund", type = "ip_b_standplat_refund")
public class IpBStandplatRefund extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "COMPANY_NAME")
    @Field(type = FieldType.Keyword)
    private String companyName;

    @JSONField(name = "LOGISTICS_NO")
    @Field(type = FieldType.Keyword)
    private String logisticsNo;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "ORDER_NO")
    @Field(type = FieldType.Keyword)
    private String orderNo;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    @JSONField(name = "RETURN_CREDIT")
    @Field(type = FieldType.Double)
    private BigDecimal returnCredit;

    @JSONField(name = "ORDER_CREDIT")
    @Field(type = FieldType.Double)
    private BigDecimal orderCredit;

    @JSONField(name = "IS_RETURN_COUPON")
    @Field(type = FieldType.Integer)
    private Integer isReturnCoupon;

    @JSONField(name = "RETURN_SHIPAMOUNT")
    @Field(type = FieldType.Double)
    private BigDecimal returnShipamount;

    @JSONField(name = "REFUND_AMOUNT")
    @Field(type = FieldType.Double)
    private BigDecimal refundAmount;

    @JSONField(name = "ORDER_STATUS")
    @Field(type = FieldType.Keyword)
    private String orderStatus;

    @JSONField(name = "RETURN_REASON")
    @Field(type = FieldType.Keyword)
    private String returnReason;

    @JSONField(name = "HAS_GOOD_RETURN")
    @Field(type = FieldType.Integer)
    private Integer hasGoodReturn;

    @JSONField(name = "RETURN_STATUS")
    @Field(type = FieldType.Integer)
    private Integer returnStatus;

    @JSONField(name = "BUYER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String buyerMobile;

    @JSONField(name = "BUYER_REMARK")
    @Field(type = FieldType.Keyword)
    private String buyerRemark;

    @JSONField(name = "BUYER_NICK")
    @Field(type = FieldType.Keyword)
    private String buyerNick;

    @JSONField(name = "RETURN_NO")
    @Field(type = FieldType.Keyword)
    private String returnNo;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    @JSONField(name = "CREATED")
    @Field(type = FieldType.Long)
    private Date created;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    private Date modifieddate;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Long)
    private Long reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Long)
    private Long reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Long)
    private Long reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Long)
    private Long reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Long)
    private Long reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Long)
    private Long reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Long)
    private Long reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Long)
    private Long reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Long)
    private Long reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Long)
    private Long reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;

    @JSONField(name = "CP_C_PLATFORM_ID")
    @Field(type = FieldType.Long)
    private Long cpCPlatformId;

    @JSONField(name = "CP_C_PLATFORM_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEcode;

    @JSONField(name = "CP_C_PLATFORM_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEname;

    @JSONField(name = "REFUND_TYPE")
    @Field(type = FieldType.Integer)
    private Integer refundType;

    @JSONField(name = "SPEED_REFUND_FLAG")
    @Field(type = FieldType.Integer)
    private Integer speedRefundFlag;

    @JSONField(name = "SUB_ORDER_ID")
    @Field(type = FieldType.Keyword)
    private String subOrderId;

    /**
     * 转换失败原因
     */
    @JSONField(name = "TRANS_FAIL_REASON")
    @Field(type = FieldType.Integer)
    private Integer transFailReason;

    @JSONField(name = "EXCHANGE_RECEIVER_NAME")
    @Field(type = FieldType.Keyword)
    private String exchangeReceiverName;

    @JSONField(name = "EXCHANGE_RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String exchangeReceiverMobile;

    @JSONField(name = "EXCHANGE_RECEIVER_PHONE")
    @Field(type = FieldType.Keyword)
    private String exchangeReceiverPhone;

    @JSONField(name = "EXCHANGE_RECEIVER_PROVINCE")
    @Field(type = FieldType.Keyword)
    private String exchangeReceiverProvince;

    @JSONField(name = "EXCHANGE_RECEIVER_CITY")
    @Field(type = FieldType.Keyword)
    private String exchangeReceiverCity;

    @JSONField(name = "EXCHANGE_RECEIVER_DISTRICT")
    @Field(type = FieldType.Keyword)
    private String exchangeReceiverDistrict;

    @JSONField(name = "EXCHANGE_RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String exchangeReceiverAddress;

    @JSONField(name = "EXCHANGE_RECEIVER_STREET")
    @Field(type = FieldType.Keyword)
    private String exchangeReceiverStreet;

    @JSONField(name = "EXCHANGE_RECEIVER_ZIP")
    @Field(type = FieldType.Keyword)
    private String exchangeReceiverZip;

    @JSONField(name = "IS_ENCRYPTION")
    @Field(type = FieldType.Integer)
    private Integer isEncryption;

    @JSONField(name = "OAID")
    @Field(type = FieldType.Keyword)
    private String oaid;

    @JSONField(name = "ORDER_PUSH_TYPE")
    @ApiModelProperty(name = "销售推送接口类型")
    private String orderPushType;


    @Field(type = FieldType.Keyword)
    @JSONField(name = "SEND_BACK_STORE_CODE")
    private String sendBackStoreCode;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "SEND_BACK_STORE_NAME")
    private String sendBackStoreName;


    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORIGINAL_BILL_NO")
    private String originalBillNo;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "COST_CENTER")
    private String costCenter;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_ORGANIZE")
    private String salesOrganize;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUSINESS_TYPE_CODE")
    private String businessTypeCode;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "DELIVERY_ORDER_NO")
    private String deliveryOrderNo;

}