package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * @ClassName DmsGiftAttrEnum
 * @Description 赠品属性
 * <AUTHOR>
 * @Date 2024/6/8 14:42
 * @Version 1.0
 */
@Getter
public enum DmsGiftAttrEnum {
    // 对下面的值生成枚举：
    CONTRACT_GIVE(1L, "合同搭赠"),
    ACTIVITY_GIVE(2L, "活动搭赠"),
    REPLENISHMENT(3L, "货补"),
    TAIL_DIFFERENCE(4L, "尾差"),
    MATERIAL(5L, "物料"),
    OUR_PRODUCT(0L, "本品");

    Long val;
    String desc;

    DmsGiftAttrEnum(Long val, String desc) {
        this.val = val;
        this.desc = desc;
    }

    public Long getVal() {
        return val;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByVal(Long val) {
        DmsGiftAttrEnum[] enums = values();
        for (DmsGiftAttrEnum dmsGiftAttrEnum : enums) {
            if (val.equals(dmsGiftAttrEnum.getVal())) {
                return dmsGiftAttrEnum.getDesc();
            }
        }
        return null;
    }
}
