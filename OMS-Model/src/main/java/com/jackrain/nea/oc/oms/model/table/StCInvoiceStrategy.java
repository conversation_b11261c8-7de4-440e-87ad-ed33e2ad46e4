package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "st_c_invoice_strategy")
@Data
public class StCInvoiceStrategy extends BaseModel {

    private static final long serialVersionUID = 961652610034240158L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON><PERSON>ield(name = "ENAME")
    private String ename;

    @JSONField(name = "CP_C_SHOP_ID")
    private String cpCShopId;

    @JSONField(name = "INVOICE_OBJECT")
    private String invoiceObject;

    @J<PERSON><PERSON>ield(name = "INVOICE_PLATFORM")
    private String invoicePlatform;

    @J<PERSON>NField(name = "INVOICE_KIND")
    private String invoiceKind;

    @JSONField(name = "INVOICE_TYPE")
    private String invoiceType;

    @J<PERSON><PERSON>ield(name = "INVOICE_NODE")
    private String invoiceNode;

    @JSONField(name = "INVOICE_CONTROL")
    private String invoiceControl;

    @JSONField(name = "RED_RUSH_CONTROL")
    private String redRushControl;

    @JSONField(name = "HEADER_TYPE")
    private String headerType;

    @JSONField(name = "IS_AUTO_SYNC")
    private String isAutoSync;

    @JSONField(name = "IS_SYNC_PLATFORM")
    private String isSyncPlatform;

    @JSONField(name = "IS_PERSON_INVOICE")
    private String isPersonInvoice;

    @JSONField(name = "IS_INVOICE")
    private String isInvoice;

    @JSONField(name = "IS_MODIFY_AMT")
    private String isModifyAmt;

    @JSONField(name = "GROUP_INVOICE")
    private String groupInvoice;

    @JSONField(name = "INVOICE_PRO_NAME")
    private String invoiceProName;

    @JSONField(name = "INVOICE_SPEC_NAME")
    private String invoiceSpecName;

    @JSONField(name = "IS_GIFT_INVOICE")
    private String isGiftInvoice;

    @JSONField(name = "IS_ZERO_AMT_INVOICE")
    private String isZeroAmtInvoice;

    @JSONField(name = "IS_FREIGHT_INVOICE")
    private String isFreightInvoice;

    @JSONField(name = "FREIGHT_ECODE")
    private String freightEcode;

    @JSONField(name = "INVOICE_UNIT")
    private String invoiceUnit;

    @JSONField(name = "INVOICE_EXCLUDE_PRO")
    private String invoiceExcludePro;

    @JSONField(name = "INVOICE_REMARK")
    private String invoiceRemark;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}