package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 周期购金额变动记录表
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "ac_cycle_buy_amt")
@Document(index = "ac_cycle_buy_amt", type = "ac_cycle_buy_amt")
public class AcCycleBuyAmt extends BaseModel {
    /**
     * id
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 订单平台单号
     */
    @JSONField(name = "ORDER_TID")
    private String orderTid;

    /**
     * 订单id
     */
    @JSONField(name = "ORDER_ID")
    private Long orderId;

    /**
     * 订单OM单号
     */
    @JSONField(name = "ORDER_BILL_NO")
    private String orderBillNo;

    /**
     * 店铺ID
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    /**
     * 变动金额
     */
    @JSONField(name = "CHANGE_AMT")
    private BigDecimal changeAmt;

    /**
     * 对账后金额
     */
    @JSONField(name = "AFTER_CONFIRM_AMT")
    private BigDecimal afterConfirmAmt;

    /**
     * 变动类型。1:对账（传SAP）；2:取消对账（取消结算）
     * {@link com.jackrain.nea.ac.model.enums.confirm.CycleSalesReportAmtChangeTypeEnum}
     */
    @JSONField(name = "CHANGE_TYPE")
    private Integer changeType;

    /**
     * 冲抵金额
     */
    @JSONField(name = "WRITE_OFF_AMT")
    private BigDecimal writeOffAmt;

    /**
     * 奶卡提奶金额冲抵id
     */
    @JSONField(name = "MILK_CARD_AMOUNT_OFFSET_ORDER_ID")
    private Long milkCardAmountOffsetOrderId;

}
