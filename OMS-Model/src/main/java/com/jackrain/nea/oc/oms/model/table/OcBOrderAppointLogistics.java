package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName OcBOrderApointLogistics
 * @Description 指定快递
 * <AUTHOR>
 * @Date 2024/4/16 15:48
 * @Version 1.0
 */
@TableName(value = "oc_b_order_appoint_logistics")
@Data
@Document(index = "oc_b_order_appoint_logistics", type = "oc_b_order_appoint_logistics")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_order_appoint_logistics", description = "指定快递")
public class OcBOrderAppointLogistics {
    private static final long serialVersionUID = -7427218491859655923L;

    @ApiModelProperty(value = "唯一ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "ORDER_ID")
    private Long orderId;

    /**
     * 可拆单的时间
     */
    @JSONField(name = "CANCEL_APPOINT_TIME")
    private Date cancelAppointTime;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "ISACTIVE")
    private String isactive;
}
