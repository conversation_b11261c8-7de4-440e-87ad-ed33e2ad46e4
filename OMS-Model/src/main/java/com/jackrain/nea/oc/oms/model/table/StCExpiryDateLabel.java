package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "st_c_expiry_date_label")
@Data
public class StCExpiryDateLabel extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "BILL_NO")
    private String billNo;

    @JSONField(name = "APPOINT_TYPE")
    private Integer appointType;

    @JSONField(name = "START_DATE_DAY")
    private String startDateDay;

    @JSONField(name = "END_DATE_DAY")
    private String endDateDay;

    @JSONField(name = "ORDER_LABEL")
    private String orderLabel;

    @JSONField(name = "NEW_START_DATE_DAY")
    private String newStartDateDay;

    @JSONField(name = "NEW_END_DATE_DAY")
    private String newEndDateDay;

    @JSONField(name = "ITEM_ID")
    private Long itemId;

    @JSONField(name = "MAIN_ID")
    private Long mainId;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}