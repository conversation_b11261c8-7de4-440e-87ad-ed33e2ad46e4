package com.jackrain.nea.oc.oms.dto.invoice.mcp;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 创建开票参数MCP
 *
 * <AUTHOR>
 */
@Data
public class CheckOrderParamMcpDTO implements Serializable {

    private static final long serialVersionUID = -7415203556554619589L;

    @ApiModelProperty(value = "平台单号")
    @JSONField(name = "TIDS")
    private String tids;

}
