package com.jackrain.nea.oc.oms.model.order.address;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @author: DXF
 * @since: 2020/11/26
 * create at : 2020/11/26 14:02
 */
@Data
@Accessors(chain = true)
public class ReceiverAddressDto implements Serializable {


    // oc_b_order的id
    @JSONField(name = "ID")
    private Long id;


    @JSONField(name = "CP_C_REGION_PROVINCE_ID")
    @JsonProperty(value="cp_c_region_province_id")
    private Long cpCRegionProvinceId;
    @JSONField(name = "CP_C_REGION_PROVINCE_ECODE")
    @JsonProperty(value="cp_c_region_province_ecode")
    private String cpCRegionProvinceEcode;
    @JSONField(name = "CP_C_REGION_PROVINCE_ENAME")
    @JsonProperty(value="cp_c_region_province_ename")
    private String cpCRegionProvinceEname;


    @JSONField(name = "CP_C_REGION_CITY_ID")
    @JsonProperty(value="cp_c_region_city_id")
    private Long cpCRegionCityId;
    @JSONField(name = "CP_C_REGION_CITY_ECODE")
    @JsonProperty(value="cp_c_region_city_ecode")
    private String cpCRegionCityEcode;
    @JSONField(name = "CP_C_REGION_CITY_ENAME")
    @JsonProperty(value="cp_c_region_city_ename")
    private String cpCRegionCityEname;

    @JSONField(name = "CP_C_REGION_AREA_ID")
    @JsonProperty(value="cp_c_region_area_id")
    private Long cpCRegionAreaId;
    @JSONField(name = "CP_C_REGION_AREA_ECODE")
    @JsonProperty(value="cp_c_region_area_ecode")
    private String cpCRegionAreaEcode;
    @JSONField(name = "CP_C_REGION_AREA_ENAME")
    @JsonProperty(value="cp_c_region_area_ename")
    private String cpCRegionAreaEname;

    @JSONField(name = "CP_C_REGION_TOWN_ENAME")
    @JsonProperty(value="cp_c_region_town_ename")
    private String cpCRegionTownEname;
    @JSONField(name = "RECEIVER_ADDRESS")
    @JsonProperty(value="receiver_address")
    private String receiverAddress;

    @JSONField(name = "RECEIVER_NAME")
    @JsonProperty(value="receiver_name")
    private String receiverName;
    @JSONField(name = "RECEIVER_MOBILE")
    @JsonProperty(value="receiver_mobile")
    private String receiverMobile;
    @JSONField(name = "RECEIVER_PHONE")
    @JsonProperty(value="receiver_phone")
    private String receiverPhone;
    @JSONField(name = "RECEIVER_ZIP")
    @JsonProperty(value="receiver_zip")
    private String receiverZip;

    @JSONField(name = "SHIP_AMT")
    @JsonProperty(value="ship_amt")
    private String shipAmt;

    @JSONField(name = "OAID")
    @JsonProperty(value="oaid")
    private String oaid;

    @JSONField(name = "PLATFORM_PROVINCE")
    @JsonProperty(value="platform_province")
    private String platformProvince;

    @JSONField(name = "PLATFORM_CITY")
    @JsonProperty(value = "platform_city")
    private String platformCity;

    @JSONField(name = "PLATFORM_AREA")
    @JsonProperty(value = "platform_area")
    private String platformArea;

    @JSONField(name = "PLATFORM_TOWN")
    @JsonProperty(value = "platform_town")
    private String platformTown;

    /**
     * 地址是否明文。0:否；1:是
     */
    @JSONField(name = "IS_PLAIN_ADDR")
    @JsonProperty(value = "is_plain_addr")
    private Integer isPlainAddr;
}
