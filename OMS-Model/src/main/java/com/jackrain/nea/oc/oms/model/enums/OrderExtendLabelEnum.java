package com.jackrain.nea.oc.oms.model.enums;

/**
 * description：订单标签扩展
 *
 * <AUTHOR>
 * @date 2021/7/5
 */
public enum OrderExtendLabelEnum {

    LABEL101("101", "平台发货失败"),
    LABEL102("102", "待确认-待换货"),
    LABEL103("103", "待确认-换货完成");

    /**
     * 类型值
     */
    String val;

    /**
     * 类型描述
     */
    String text;

    OrderExtendLabelEnum(String v, String k) {
        this.val = v;
        this.text = k;
    }

    public String getText() {
        return text;
    }

    public String getVal() {
        return val;
    }


}


