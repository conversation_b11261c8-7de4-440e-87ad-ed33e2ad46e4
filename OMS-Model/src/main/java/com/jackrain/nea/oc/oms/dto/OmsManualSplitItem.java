package com.jackrain.nea.oc.oms.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: 黄世新
 * @Date: 2022/9/18 下午2:08
 * @Version 1.0
 */
@Data
public class OmsManualSplitItem implements Serializable {

    private static final long serialVersionUID = 2415197601922538110L;
    private Long orderId;

    private Long itemId;

    private BigDecimal qty;

    private String tid;

    private String isSplit = "是";

    /**
     * 实体仓id
     */
    private Long warehouseId;

    private String warehouseName;

    private String skuCode;

    private String proName;

    private Integer isGift;

    /**
     * 批次/效期范围
     */
    private String expiryDateRange;

    /**
     * 业务类型编码
     */
    private String businessTypeCode;

    /**
     * 平台
     */
    private Integer platform;

    /**
     * 销售商品属性
     */
    private String saleProductAttr;

}
