package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * className: AcFPriceSetting
 * description:
 *
 * <AUTHOR>
 * create: 2021-10-25
 * @since JDK 1.8
 */
@Data
@TableName("ac_f_price_setting")
@Document(index = "ac_f_price_setting",type = "ac_f_price_setting")
@ApiModel(value = "ac_f_price_setting",description = "全渠道价格配置表")
public class AcFPriceSetting extends BaseModel {

    private static final long serialVersionUID = 8973827344462870724L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "ID")
    private Long id;

    @JSONField(name = "PRO_LEVEL")
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "商品等级",allowEmptyValue = true)
    private Integer proLevel;

    @JSONField(name = "STANDARD_DISCOUNT")
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "标准折扣",allowEmptyValue = true)
    private BigDecimal standardDiscount;

    @JSONField(name = "OFFLINE_PRO_LEVEL")
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "线下商品等级",allowEmptyValue = true)
    private Integer offlineProLevel;

    @JSONField(name = "OFFLINE_STANDARD_DISCOUNT")
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "线下标准折扣",allowEmptyValue = true)
    private BigDecimal offlineStandardDiscount;

    @JSONField(name = "YEAR")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "年份",allowEmptyValue = true)
    private String year;

    @JSONField(name = "QUARTER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "季度",allowEmptyValue = true)
    private String quarter;

    @JSONField(name = "MAIN_CHANNEL")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "主控渠道",allowEmptyValue = true)
    private String mainChannel;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "备注",allowEmptyValue = true)
    private String remark;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "创建人姓名")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "修改人姓名")
    private String modifierename;

    @JSONField(name = "CANCEL_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "作废人",allowEmptyValue = true)
    private Long cancelId;

    @JSONField(name = "CANCEL_DATE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "作废时间",allowEmptyValue = true)
    private Date cancelDate;

    @JSONField(name = "PS_C_PRO_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "商品id")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "商品编码",allowEmptyValue = true)
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "商品名称",allowEmptyValue = true)
    private String psCProEname;
}
