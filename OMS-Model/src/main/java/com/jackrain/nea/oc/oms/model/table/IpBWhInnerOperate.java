package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_wh_inner_operate")
@Data
@Document(index = "ip_b_wh_inner_operate", type = "ip_b_wh_inner_operate")
public class IpBWhInnerOperate extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "SG_B_PHY_OUT_NOTICES_NO")
    private String sgBPhyOutNoticesNo;

    @JSONField(name = "WMS_BILL_NO")
    private String wmsBillNo;

    @JSONField(name = "BILL_TYPE")
    private String billType;

    @JSONField(name = "BILL_STATUS")
    private String billStatus;

    @JSONField(name = "OPERATOR_CODE")
    private String operatorCode;

    @JSONField(name = "OPERATOR_NAME")
    private String operatorName;

    @JSONField(name = "OPERATE_TIME")
    private Date operateTime;

    @JSONField(name = "OPERATE_INFO")
    private String operateInfo;

    @JSONField(name = "EXPRESS_CODE")
    private String expressCode;

    @JSONField(name = "TRANS_STATUS")
    private Integer transStatus;

    @JSONField(name = "TRANS_TIME")
    private Date transTime;

    @JSONField(name = "SYSREMARK")
    private String sysremark;

    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;
}