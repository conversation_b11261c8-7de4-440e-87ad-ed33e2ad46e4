package com.jackrain.nea.oc.oms.model.o2o.retailreturn;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description： o2o消退单 -- MQ -> 云枢纽
 * Author: RESET
 * Date: Created in 2020/8/16 12:46
 * Modified By:
 */
@Data
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@Builder(toBuilder = true)
public class RetailReturnOrder implements Serializable {

    /**
     * 伯俊中台退单编号
     * 退换货单的单据编号
     */
    @JSONField(name = "refund_id")
    private String refundId;

    /**
     * 伯俊中台订单编号
     * 退换货单原始订单编号对应的出库通知单
     */
    @JSONField(name = "orig_order_id")
    private Long origOrderId;

    /**
     *
     */
    @JSONField(name = "goodsCode")
    private String goodsCode;

    /**
     * 平台单号，多个逗号分隔
     * 退换货单的原始平台单号
     */
    @JSONField(name = "sourcecode")
    private String sourceCode;

    /**
     * 入库仓库
     * 退换货单的入库实体仓编码
     */
    @JSONField(name = "refund_warehouse")
    private String refundWarehouse;

    /**
     * 创建时间
     * 退换货单的创建时间
     */
    @JSONField(name = "creationdate")
    private Date creationdate;

    /**
     * 入库时间
     * 退换货单的入库时间
     */
    @JSONField(name = "in_time")
    private Date inTime;

    /**
     * 来源店铺代码
     * 退换货单对应零售发货单下单店铺的店铺代码
     */
    @JSONField(name = "lyzd_dm")
    private String lyzdDm;

    /**
     * 来源店铺名称
     * 退换货单对应零售发货单的下单店铺的店铺名称
     */
    @JSONField(name = "lyzd_mc")
    private String lyzdMc;

    /**
     * 来源渠道代码
     * 退换货单对应零售发货单的下单店铺的来源平台编码
     */
    @JSONField(name = "lyorg_dm")
    private String lyorgDm;

    /**
     * 来源渠道名称
     * 退换货单对应零售发货单的下单店铺的来源平台名称
     */
    @JSONField(name = "lyorg_mc")
    private String lyorgMc;

    /**
     * 下单门店代码
     * 退换货单的原发货门店编码
     */
    @JSONField(name = "xdzd_dm")
    private String xdzdDm;

    /**
     * 明细
     */
    @JSONField(name = "item")
    private List<RetailReturnOrderItem> item;

}
