package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 孙俊磊
 * @since :  2019-03-25
 * create at:  2019-03-25 11:04
 */
@Data
public class OcBReturnOrderExt extends OcBReturnOrder {

    @JSONField(name = "ITEM_LIST")
    private List<OcBRefundInProductItemExt> itemList;

    @J<PERSON>NField(name = "SCAN_STORE_ID")
    private Long scanStoreId;

    @J<PERSON><PERSON>ield(name = "SCAN_STORE_CODE")
    private String scanStoreCode;

    @J<PERSON><PERSON>ield(name = "SCAN_STORE_NAME")
    private String scanStoreName;

    @J<PERSON><PERSON>ield(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;


}
