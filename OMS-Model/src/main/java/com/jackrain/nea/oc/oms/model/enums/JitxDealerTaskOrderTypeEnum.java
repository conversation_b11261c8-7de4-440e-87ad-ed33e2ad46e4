package com.jackrain.nea.oc.oms.model.enums;


import java.util.Objects;

/**
 * description：jitx经销商单据类型
 *
 * <AUTHOR>
 * @date 2021/12/21
 */
public enum JitxDealerTaskOrderTypeEnum {

    VIP_TIME_ORDER("时效单", 1),
    OC_B_ORDER("发货单", 2);

    int code;
    String text;

    JitxDealerTaskOrderTypeEnum(String text, int code) {
        this.text = text;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    public static String getTextByCode(String v) {
        for (JitxDealerTaskOrderTypeEnum c : JitxDealerTaskOrderTypeEnum.values()) {
            if (Objects.equals(v, c.getCode())) {
                return c.getText();
            }
        }
        return "";
    }

}


