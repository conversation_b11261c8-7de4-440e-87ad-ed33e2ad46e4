package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Author: 黄世新
 * @Date: 2020/2/12 2:48 下午
 * @Version 1.0
 */
public enum AbnormalTypeEnum {

    /**
     * SKU异常
     */
    SKU_ABNORMAL(1, "SKU异常"),

    /**
     * 省市区匹配异常
     */
    MATE_ABNORMAL(2, "省市区匹配异常"),

    /**
     * 数据存储异常
     */
    DATA_STORAGE(3, "数据存储异常"),

    /**
     * 其他异常
     */
    OTHERS(4, "其他异常");

    Integer key;
    String description;

    AbnormalTypeEnum(Integer key, String description) {
        this.key = key;
        this.description = description;
    }

    public Integer getKey() {
        return key;
    }

    public String getDescription() {
        return description;
    }
}
