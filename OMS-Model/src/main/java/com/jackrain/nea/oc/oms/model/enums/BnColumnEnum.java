package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * @ClassName BnColumnEnum
 * @Description 班牛字段枚举
 * <AUTHOR>
 * @Date 2024/11/18 11:36
 * @Version 1.0
 */
@Getter
public enum BnColumnEnum {

    /**
     * 平台单号
     */
    TID("tid", "订单号", "20941"),
    SHOP_TITLE("shopTitle", "店铺", "20942"),
    USER_NICK("userNick", "买家昵称", "20943"),
    WAREHOUSE_NAME("warehouseName", "发货仓库", "20944"),
    LOGISTICS_COMPANY_NAME("logisticsCompanyName", "快递公司", "20945"),
    LOGISTICS_CODE("logisticsCode", "快递单号", "20946"),
    LOGISTICS("logistics", "快递", "20947"),
    PROBLEM_TEXT("problemText", "问题类型", "20948"),
    CUSTOMER_REQUEST("customerRequest", "客户要求", "20949"),
    PAY_AMT("payAmt", "实付金额", "20951"),
    PAY_FOR("payFor", "赔付金额", "20952"),
    URL("url", "附件", "20953"),
    REMARK("remark", "问题描述", "20954"),
    RECEIVER_NAME("receiverName", "收货姓名", "20955"),
    RECEIVER_PHONE("receiverPhone", "收货手机", "20956"),
    PROVINCE("province", "省", "20957"),
    CITY("city", "市", "20958"),
    AREA("area", "区", "20959"),
    STREET("street", "街道", "20960"),
    ADDRESS("address", "详细地址", "20961"),
    OMS_CREATER("omsCreater", "oms创建人", "20962"),
    ITEM("item", "商品编码", "20950"),
    EXECUTOR("2", "执行人", "2"),
    TASK_STATUS("5", "任务状态", "5"),
    END_DATE("7", "截止时间", "7"),
    LOGISTICS_CODE_NEW("logisticsCodeNew", "快递单号新", "21421"),
    LOGISTICS_CODE_SUB("logisticsCodeNewSub", "快递单号新", "21422"),

    ;


    String columnName;
    String columnDesc;
    String columnId;

    BnColumnEnum(String columnName, String columnDesc, String columnId) {
        this.columnName = columnName;
        this.columnDesc = columnDesc;
        this.columnId = columnId;
    }
}
