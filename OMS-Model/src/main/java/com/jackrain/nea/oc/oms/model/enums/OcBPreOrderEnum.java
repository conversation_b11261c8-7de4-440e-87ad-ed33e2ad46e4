package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @ClassName OcBPreOrderEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2022/10/17 10:09
 * @Version 1.0
 */
public class OcBPreOrderEnum {

    @AllArgsConstructor
    public enum OcBPreTransferEnum{

        UN_TRANSFER(0, "未转换"),
        TRANSFERING(1, "转换中"),
        TRANSFERED(2,"转换成功"),
        TRANSFER_FAILED(3,"转换失败");

        private int val;
        private String desc;

        public int getVal() {
            return val;
        }

        public String getDesc() {
            return desc;
        }

        public static String getDescByVal(int val){
            OcBPreTransferEnum[] transferEnums = OcBPreTransferEnum.values();
            for (OcBPreTransferEnum transferEnum : transferEnums){
                if (val == transferEnum.getVal()){
                    return transferEnum.getDesc();
                }
            }
            return "";
        }
    }
}
