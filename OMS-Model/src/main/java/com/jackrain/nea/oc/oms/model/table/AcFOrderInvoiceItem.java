package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ac_f_order_invoice_item")
@Data
public class AcFOrderInvoiceItem extends BaseModel {
    private static final long serialVersionUID = 405628719707592877L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "AC_F_ORDER_INVOICE_ID")
    private Long acFOrderInvoiceId;

    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @JSONField(name = "INVOICE_PRO_NAME")
    private String invoiceProName;

    @JSONField(name = "INVOICE_SPEC_NAME")
    private String invoiceSpecName;

    @JSONField(name = "TAX_CLASSIFICATION")
    private String taxClassification;

    @JSONField(name = "LARGESERIES")
    private String largeseries;

    @JSONField(name = "NO_TAX_AMT")
    private BigDecimal noTaxAmt;

    @JSONField(name = "NO_TAX_PRICE")
    private BigDecimal noTaxPrice;

    @JSONField(name = "INCLUSIVE_TAX_AMT")
    private BigDecimal inclusiveTaxAmt;

    @JSONField(name = "INCLUSIVE_TAX_PRICE")
    private BigDecimal inclusiveTaxPrice;

    @JSONField(name = "TAX_RATE")
    private String taxRate;

    @JSONField(name = "INVOICE_TAX_AMT")
    private BigDecimal invoiceTaxAmt;

    @JSONField(name = "UNIT")
    private String unit;

    @JSONField(name = "QTY")
    private BigDecimal qty;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}