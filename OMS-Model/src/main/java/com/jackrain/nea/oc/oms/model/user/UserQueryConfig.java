package com.jackrain.nea.oc.oms.model.user;


import com.jackrain.nea.oc.oms.model.relation.UserConfig;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

public class UserQueryConfig implements Serializable {

    private static final long serialVersionUID = -1704642472081195949L;

    //表隐藏字段配置
    private HashMap<String, List<UserConfig>> param;


    public List<UserConfig> getParam(String tableName) {
        if (param != null) {
            return param.get(tableName);
        }
        return null;
    }

    public void setParam(String tableName, List<UserConfig> userConfig) {
        param = new HashMap<>();
        param.put(tableName, userConfig);
    }

    public void addParam(String tableName, List<UserConfig> userConfig) {
        if (param == null) {
            param = new HashMap<>();
        }
        param.put(tableName, userConfig);
    }

}
