package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName(value = "ac_f_customer_package_fee")
@Data
@Document(index = "ac_f_customer_package_fee",type = "ac_f_customer_package_fee")
@ApiModel(value = "ac_f_customer_package_fee", description = "经销商打包费用配置")
public class AcFCustomerPackageFee extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "ID")
    private Long id;

    @JSONField(name = "CP_C_CUSTOMER_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "经销商")
    private Long cpCCustomerId;

    @JSONField(name = "DISCOUNT_RESOURCE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "折扣依据:1-吊牌金额，2-成交金额")
    private Integer discountResource;

    @JSONField(name = "DISCOUNT_RATIO")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "系数")
    private String discountRatio;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "备注")
    private String remark;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "创建人姓名")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "修改人姓名")
    private String modifierename;

}