package com.jackrain.nea.oc.oms.model.table;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;

@TableName(value = "ip_b_taobao_jx_order_item")
@Data
public class IpBTaobaoJxOrderItemEx extends IpBTaobaoJxOrderItem {
    @TableField(exist = false)
    private ProductSku prodSku;
}
