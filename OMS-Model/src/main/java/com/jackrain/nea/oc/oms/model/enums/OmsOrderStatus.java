package com.jackrain.nea.oc.oms.model.enums;

import java.util.Objects;

/**
 * @author: 易邵峰
 * @since: 2019-03-07
 * create at : 2019-03-07 21:24
 */
public enum OmsOrderStatus {

    /**
     * 订单默认状态50
     */
    ORDER_DEFAULT,

    /**
     * 订单“待分配”状态50
     */
    TO_BE_ASSIGNED,

    /**
     * 订单“待审核”状态1
     */
    UNCONFIRMED,

    /**
     * 订单“缺货”状态2
     * todo 注意:此状态已改为 待寻源
     */
    BE_OUT_OF_STOCK,

    /**
     * 已审核3
     */
    CHECKED,
    /**
     * 订单“配货中”状态4
     */
    IN_DISTRIBUTION,
    /**
     * 订单“仓库发货”状态5
     */
    WAREHOUSE_DELIVERY,
    /**
     * 订单“平台发货”状态6
     */
    PLATFORM_DELIVERY,
    /**
     * 订单“已取消”状态7
     */
    CANCELLED,
    /**
     * 订单“系统作废”状态8
     */
    SYS_VOID,
    /**
     * 订单“预售”状态9
     */
    PRE_SALE,
    /**
     * 订单“代发”状态10
     */
    SUBSTITUTE,
    /**
     * 订单“物流已送达”状态11
     */
    DELIVERED,
    /**
     * 订单“交易完成”状态12
     */
    DEAL_DONE,
    /**
     * 订单“未付款”状态13
     */
    UNPAID,
    /**
     * 订单“传WMS中”状态21
     */
    PENDING_WMS,

    /**
     * 寻源中
     */
    OCCUPY_IN;

    /**
     * 1,待审核 2,缺货 3,已审核 4,配货中 5,仓库发货 6,平台发货 7,已取消 8,系统作废
     * 9,预售 10,代发 11,物流已送达 12,交易完成 13,未付款 21,传wms中 50,待分配,0,新增订单暂存草稿状态
     */
    public Integer toInteger() {
        if (this == ORDER_DEFAULT) {
            return 50;
        } else if (this == TO_BE_ASSIGNED) {
            return 50;
        } else if (this == UNCONFIRMED) {
            return 1;
        } else if (this == BE_OUT_OF_STOCK) {
            return 2;
        } else if (this == CHECKED) {
            return 3;
        } else if (this == IN_DISTRIBUTION) {
            return 4;
        } else if (this == WAREHOUSE_DELIVERY) {
            return 5;
        } else if (this == PLATFORM_DELIVERY) {
            return 6;
        } else if (this == CANCELLED) {
            return 7;
        } else if (this == SYS_VOID) {
            return 8;
        } else if (this == PRE_SALE) {
            return 9;
        } else if (this == SUBSTITUTE) {
            return 10;
        } else if (this == DELIVERED) {
            return 11;
        } else if (this == DEAL_DONE) {
            return 12;
        } else if (this == UNPAID) {
            return 13;
        } else if (this == PENDING_WMS) {
            return 21;
        } else if (this == OCCUPY_IN) {
            return 22;
        } else {
            return 0;
        }
    }

    /**
     * 1,待审核 2,缺货 3,已审核 4,配货中 5,仓库发货 6,平台发货 7,已取消 8,系统作废
     * 9,预售 10,代发 11,物流已送达 12,交易完成 13,未付款 21,传wms中 50,待分配,0,新增订单暂存草稿状态
     */
    public static String toDesc(Integer status) {
        if (Objects.equals(status, ORDER_DEFAULT.toInteger())) {
            return "待分配";
        } else if (Objects.equals(status, TO_BE_ASSIGNED.toInteger())) {
            return "待分配";
        } else if (Objects.equals(status, UNCONFIRMED.toInteger())) {
            return "待审核";
        } else if (Objects.equals(status, BE_OUT_OF_STOCK.toInteger())) {
            return "缺货";
        } else if (Objects.equals(status, CHECKED.toInteger())) {
            return "已审核";
        } else if (Objects.equals(status, IN_DISTRIBUTION.toInteger())) {
            return "配货中";
        } else if (Objects.equals(status, WAREHOUSE_DELIVERY.toInteger())) {
            return "仓库发货";
        } else if (Objects.equals(status, PLATFORM_DELIVERY.toInteger())) {
            return "平台发货";
        } else if (Objects.equals(status, CANCELLED.toInteger())) {
            return "已取消";
        } else if (Objects.equals(status, SYS_VOID.toInteger())) {
            return "系统作废";
        } else if (Objects.equals(status, OCCUPY_IN.toInteger())) {
            return "寻源中";
        } else {
            // 其他都暂时无意义
            return "";
        }
    }

    public static OmsOrderStatus convert2Enum(Integer val) {
        switch (val) {
            case 1:
                return UNCONFIRMED;
            case 2:
                return BE_OUT_OF_STOCK;
            case 3:
                return CHECKED;
            case 4:
                return IN_DISTRIBUTION;
            case 5:
                return WAREHOUSE_DELIVERY;
            case 6:
                return PLATFORM_DELIVERY;
            case 7:
                return CANCELLED;
            case 8:
                return SYS_VOID;
            case 21:
                return PENDING_WMS;
            case 50:
                return TO_BE_ASSIGNED;
            default:
                return ORDER_DEFAULT;
        }
    }


    /**
     * 是否是无效的订单状态
     *
     * @param orderStatus orderStatus
     * @return boolean
     */
    public static boolean isInvalid(Integer orderStatus) {
        return CANCELLED.toInteger().equals(orderStatus) || SYS_VOID.toInteger().equals(orderStatus);
    }

}
