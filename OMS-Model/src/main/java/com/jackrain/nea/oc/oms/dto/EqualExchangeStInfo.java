package com.jackrain.nea.oc.oms.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

/**
 * @Author: 黄世新
 * @Date: 2022/7/11 下午3:22
 * @Version 1.0
 */
@Data
public class EqualExchangeStInfo implements Serializable {

    @JSO<PERSON>ield(name = "exchange_sku_code")
    private String exchangeSkuCode;

    @JSONField(name = "exchange_sku_title")
    private String exchangeSkuTitle;

    @JSONField(name = "exchange_qty")
    private BigDecimal exchangeQty;

    @JSONField(name = "equal_sku_code")
    private String equalSkuCode;

    @J<PERSON>NField(name = "equal_sku_title")
    private String equalSkuTitle;

    @JSONField(name = "equal_qty")
    private BigDecimal equalQty;

    @JSONField(name = "relation_ids")
    private Set<Long> relationIds;

    @JSONField(name = "type")
    private String type;

    @JSONField(name = "shop_name")
    private String shopName;

    @JSONField(name = "out_stock_no_restore")
    private String outStockNoRestore;
}
