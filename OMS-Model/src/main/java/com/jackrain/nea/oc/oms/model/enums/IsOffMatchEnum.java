package com.jackrain.nea.oc.oms.model.enums;


/**
 * 是否关闭匹配
 *
 * @author: 汪聿森
 * create at: 2019/8/22 17:26
 */
public enum IsOffMatchEnum {


    //0 关闭匹配，1 开启匹配
    OFF_MATCH,
    ON_MATCH;

    public int toInteger() {
        if (this == IsOffMatchEnum.OFF_MATCH) {
            return 0;
        } else {
            return 1;
        }
    }

    public String toDescription() {
        if (this == IsOffMatchEnum.OFF_MATCH) {
            return "OFF_MATCH";
        } else {
            return "ON_MATCH";
        }
    }
}


