package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Desc : 退货入库,入库类型
 * <AUTHOR> xiWen
 * @Date : 2022/7/19
 */
public enum ReturnInType {

    /**
     * 无头件登记单
     */
    NAMELESS(0, "WTJRK", "无头件登记单"),

    /**
     * B2C退货入库
     */
    NORM2C(1, "THRK", "2C退货入库"),

    /**
     * B2B退货入库
     */
    NORM2B(2, "B2BRK", "2B退货入库"),

    /**
     * 工厂销退入库
     */
    GCXT(3, "GCXT", "工厂销退入库");

    private Integer val;

    private String code;

    private String desc;

    public Integer val() {
        return this.val;
    }

    /**
     * 值转枚举
     *
     * @param val
     * @return
     */
    public static ReturnInType convert2Enum(Integer val) {
        if (val == null) {
            return NAMELESS;
        }
        switch (val) {
            case 1:
                return NORM2C;
            case 2:
                return NORM2B;
            case 3:
                return GCXT;
            case 0:
            default:
                return NAMELESS;
        }
    }

    /**
     * 编码转枚举
     *
     * @param val
     * @return
     */
    public static ReturnInType convert2Enum(String val) {
        if (val == null) {
            return NAMELESS;
        }
        for (ReturnInType value : ReturnInType.values()) {
            if (value.code.equals(val)) {
                return value;
            }
        }
        return NAMELESS;
    }

    ReturnInType(Integer val, String code, String desc) {
        this.val = val;
        this.code = code;
        this.desc = desc;
    }

}
