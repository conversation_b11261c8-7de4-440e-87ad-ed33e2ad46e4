package com.jackrain.nea.oc.oms.dto.invoice;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ReturnResOffsetDTO
 * @Description
 * @Date 2022/9/20 下午9:27
 * @Created by wuhang
 */
@Data
public class ReturnRedOffsetDTO implements Serializable {
    private static final long serialVersionUID = -7472195113730394283L;

    /** 平台单号 */
    @JSONField(name = "TID")
    private String tid;

    /** 单据编号 零售发货单、退换货或发货后仅退款单据编号*/
    @JSONField(name = "BILL_NO")
    private String billNo;

    /** 单据类型,1退换货单,2零售发货单,3已发货仅退款单*/
    @JSONField(name = "BILL_TYPE")
    private String billType;

    /** 店铺id */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    /** 店铺标题 */
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    /** 店铺编码 */
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    /** 平台id */
    @JSONField(name = "CP_C_PLATFORM_ID")
    private Long cpCPlatformId;

    /** 平台名称 */
    @JSONField(name = "CP_C_PLATFORM_ENAME")
    private String cpCPlatformEname;

    /** 平台编码 */
    @JSONField(name = "CP_C_PLATFORM_ECODE")
    private String cpCPlatformEcode;

    /** 退货类型 0退货,1仅退款 */
    @JSONField(name = "RETURN_TYPE")
    private String returnType;

    /** 转换状态 0未转换,1已转换 */
    @JSONField(name = "CHANGE_STATUS")
    private String changeStatus;

    /** 转换人 */
    @JSONField(name = "CHANGE_USER")
    private Long changeUser;

    /** 转换时间 */
    @JSONField(name = "CHANGE_DATE")
    private Date changeDate;

    /** 退货数量 */
    @JSONField(name = "RETURN_COUNT")
    private String returnCount;

    /** 退款金额 */
    @JSONField(name = "REFUND_MONEY")
    private BigDecimal refundMoney;

    /** 退货时间 */
    @JSONField(name = "RETURN_DATE")
    private Date returnDate;

    /** 运费 */
    @JSONField(name = "FREIGHT_AMOUNT")
    private BigDecimal freightAmount;

    /** 系统备注 */
    @JSONField(name = "SYSTEM_REMARK")
    private String systemRemark;

    /** 整单退货标识 */
    @JSONField(name = "ALL_RETURN_FLAG")
    private String allReturnFlag;

    @JSONField(name = "ITEM_LIST")
    private List<ReturnOffsetItemDTO> itemList;

    @Data
    @NoArgsConstructor
    public static class ReturnOffsetItemDTO implements Serializable{

        private static final long serialVersionUID = -3699047798145195512L;
        /** 商品编码 */
        @JSONField(name = "PRODUCT_CODE")
        private String productCode;

        /** 商品名称 */
        @JSONField(name = "PRODUCT_NAME")
        private String productName;

        /** sku编码 */
        @JSONField(name = "SKU_CODE")
        private String skuCode;

        /** sku名称 */
        @JSONField(name = "SKU_NAME")
        private String skuName;

        /** 单位 */
        @JSONField(name = "UNIT")
        private String unit;

        /** 退货数量 */
        @JSONField(name = "RETURN_COUNT")
        private Integer returnCount;

        /** 退款金额 */
        @JSONField(name = "REFUND_AMOUNT")
        private BigDecimal refundAmount;

        /** 赠品标识,0否1是 */
        @JSONField(name = "GIFT_FLAG")
        private String giftFlag;

    }
}
