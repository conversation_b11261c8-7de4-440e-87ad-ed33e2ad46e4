package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


@TableName(value = "ip_b_jingdong_direct_refund")
@Data
public class IpBJingdongDirectRefund extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "REFUND_ID")
    private String refundId;

    @JSONField(name = "CUSTOM_ORDER_ID")
    private String customOrderId;

    @JSONField(name = "RO_APPLY_FEE")
    private BigDecimal roApplyFee;

    @JSONField(name = "RO_APPLY_DATE")
    private Date roApplyDate;

    @JSONField(name = "ORDER_CREATE_DATE")
    private Date orderCreateDate;

    @JSONField(name = "REFUND_MODIFIEDDATE")
    private Date refundModifieddate;

    @JSONField(name = "APPROVAL_STATE")
    private Integer approvalState;

    @JSONField(name = "ORDER_STATE")
    private Integer orderState;

    @JSONField(name = "OPERATOR_STATE")
    private Integer operatorState;

    @JSONField(name = "RO_PRE_NO")
    private Long roPreNo;

    @JSONField(name = "RO_ACCOUNT")
    private String roAccount;

    @JSONField(name = "RO_REASON")
    private String roReason;

    @JSONField(name = "APPROVAL_SUGGESTION")
    private String approvalSuggestion;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "SELLER_NICK")
    private String sellerNick;

    @JSONField(name = "TRANSDATE")
    private Date transdate;

    @JSONField(name = "SYSREMARK")
    private String sysremark;

    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "TRANS_COUNT")
    private Integer transCount;

    @JSONField(name = "ISTRANS")
    private Integer istrans;

    @JSONField(name = "VENDORSTOREID")
    private Long vendorstoreid;

    @JSONField(name = "VENDORSTORENAME")
    private String vendorstorename;
}
