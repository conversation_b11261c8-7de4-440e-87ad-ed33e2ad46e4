//package com.jackrain.nea.oc.oms.model.enums;
//
//import lombok.AllArgsConstructor;
//import lombok.Getter;
//
///**
// * @Author: 黄世新
// * @Date: 2022/6/6 下午3:19
// * @Version 1.0
// * <p>
// * 订单业务类型枚举
// */
//@Getter
//@AllArgsConstructor
//public enum OrderBusinessTypeEnum {
//    ON_LINE_MILK_CARD_SALE(1, "线上奶卡销售"),
//    ON_LINE_FREE_MILK_CARD(2, "线上免费奶卡"),
//    MILK_CARD_GIVE_OUT_WAREHOUSE(3, "奶卡赠送出库"),
//    MILK_CARD_PICK_UP_GOODS(4, "奶卡提货"),
//    FREE_MILK_CARD_PICK_UP_GOODS(5, "免费奶卡提货"),
//    CYCLE_PURCHASE_PICK_UP_GOODS(6, "周期购提货"),
//    CYCLE_PURCHASE_ORDER(7, "周期购订单"),
//    E_COMMERCE_SALE_ORDER(8, "电商销售订单"),
//    OFFLINE_SALES_ORDER_TO_C(9, "线下销售订单-toC"),
//    OFFLINE_SALES_ORDER_TO_B(10, "线下销售订单-toB"),
//    ELECTRONICS_MILK_CARD_SALE(11, "电子奶卡销售"),
//    OFFLINE_MILK_CARD_SALE(12, "线下奶卡销售"),
//    MILK_CARD_PICK_UP_GOODS_REISSUE(13, "奶卡提奶补发"),
//    MILK_CARD_REISSUE(14, "奶卡补发"),
//    AFTER_SALES_REISSUE(15, "售后补发"),
//    CYCLE_PURCHASE_PICK_UP_GOODS_REISSUE(16, "周期购提货"),
//    UNKNOW(-1, "未知");
//
//    private Integer code;
//
//    private String massage;
//
//    public static OrderBusinessTypeEnum getOrderBusinessTypeEnumByCode(Integer code){
//        for (OrderBusinessTypeEnum value : OrderBusinessTypeEnum.values()) {
//            if (value.getCode().equals(code)){
//                return value;
//            }
//        }
//        return UNKNOW;
//    }
//
//    /**
//     * 分仓分物流的检验
//     *
//     * @param status
//     * @return false继续流程 true不走
//     */
//    public static boolean checkSubWarehouseOccupy(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return OFFLINE_SALES_ORDER_TO_B.getCode().equals(status);
//    }
//
//    /**
//     * 促销检验
//     *
//     * @param status
//     * @return
//     */
//    public static boolean checkPromotion(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return MILK_CARD_GIVE_OUT_WAREHOUSE.getCode().equals(status)
//                || AFTER_SALES_REISSUE.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_B.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_C.getCode().equals(status)
//                || MILK_CARD_PICK_UP_GOODS_REISSUE.getCode().equals(status)
//                || MILK_CARD_REISSUE.getCode().equals(status)
//                || OFFLINE_MILK_CARD_SALE.getCode().equals(status);
//    }
//
//    /**
//     * 检验直播
//     *
//     * @param status
//     * @return
//     */
//    public static boolean checkLive(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return OFFLINE_SALES_ORDER_TO_B.getCode().equals(status);
//    }
//
//    /**
//     * 检验卡单
//     *
//     * @param status
//     * @return
//     */
//    public static boolean checkDetention(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return OFFLINE_SALES_ORDER_TO_B.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_C.getCode().equals(status)
//                || MILK_CARD_PICK_UP_GOODS_REISSUE.getCode().equals(status)
//                || MILK_CARD_REISSUE.getCode().equals(status)
//                || OFFLINE_MILK_CARD_SALE.getCode().equals(status);
//    }
//
//    /**
//     * 检验hold单
//     * @param status
//     * @return
//     */
//    public static boolean checkHoldOrder(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return MILK_CARD_GIVE_OUT_WAREHOUSE.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_B.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_C.getCode().equals(status)
//                || MILK_CARD_REISSUE.getCode().equals(status)
//                || OFFLINE_MILK_CARD_SALE.getCode().equals(status);
//    }
//
//}
