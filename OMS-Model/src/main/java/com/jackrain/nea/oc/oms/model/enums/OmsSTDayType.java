package com.jackrain.nea.oc.oms.model.enums;

/**
 * @author: 胡林洋
 * @since: 2019-03-13
 * create at : 2019-03-13 17:46
 */
public enum OmsSTDayType {

    /**
     * 日期类型（物流方案） 1:下单日期
     */
    ORDER_DATE,
    /**
     * 日期类型（物流方案） 2: 分配日期
     */
    DISTRIBUTION_DATE;

    /**
     * 日期类型（物流方案） 1:下单日期 2: 分配日期
     */
    public int toInteger() {
        if (this == ORDER_DATE) {
            return 1;
        } else if (this == DISTRIBUTION_DATE) {
            return 2;
        } else {
            return 0;
        }
    }

}
