package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "oc_b_order_delivery")
@Data
@Document(index = "oc_b_order_delivery", type = "oc_b_order_delivery")
public class OcBOrderDelivery extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    @Field(type = FieldType.Long)
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEname;

    @JSONField(name = "LOGISTIC_NUMBER")
    @Field(type = FieldType.Keyword)
    private String logisticNumber;

    @JSONField(name = "PKG_PRODUCTS")
    @Field(type = FieldType.Keyword)
    private String pkgProducts;

    @JSONField(name = "WEIGHT")
    @Field(type = FieldType.Double)
    private BigDecimal weight;

    @JSONField(name = "SIZE")
    @Field(type = FieldType.Double)
    private BigDecimal size;

    @JSONField(name = "OC_B_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ocBOrderId;

    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    private Long version;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    private Long reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    private Long reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    private Long reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    private Long reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    private Long reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    private Long reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    private Long reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    private Long reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    private Long reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    private Long reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;

    @JSONField(name = "PS_C_PRO_ID")
    @Field(type = FieldType.Long)
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCProEname;

    @JSONField(name = "PS_C_SKU_ID")
    @Field(type = FieldType.Long)
    private Long psCSkuId;

    @JSONField(name = "PS_C_SKU_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSkuEcode;

    @JSONField(name = "PS_C_CLR_ID")
    @Field(type = FieldType.Long)
    private Long psCClrId;

    @JSONField(name = "PS_C_CLR_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCClrEcode;

    @JSONField(name = "PS_C_CLR_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCClrEname;

    @JSONField(name = "PS_C_SIZE_ID")
    @Field(type = FieldType.Long)
    private Long psCSizeId;

    @JSONField(name = "PS_C_SIZE_ECODE")
    @Field(type = FieldType.Keyword)
    private String psCSizeEcode;

    @JSONField(name = "PS_C_SIZE_ENAME")
    @Field(type = FieldType.Keyword)
    private String psCSizeEname;

    @JSONField(name = "QTY")
    @Field(type = FieldType.Double)
    private BigDecimal qty;

    @JSONField(name = "GBCODE")
    @Field(type = FieldType.Keyword)
    private String gbcode;

    @JSONField(name = "CAINIAO_WH_STATUS")
    @Field(type = FieldType.Keyword)
    private String cainiaoWhStatus;
}