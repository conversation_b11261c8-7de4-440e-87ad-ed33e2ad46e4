package com.jackrain.nea.oc.oms.model.enums;


/**
 * 同步状态
 *
 * @author: heliu
 * @since: 2019-03-21
 * create at : 2019-03-21 12:09
 */
public enum SyncStatus {

    /**
     * 未处理
     */
    UNSYNC,
    /**
     * JITX
     */
    SYNCSUCCESS,

    /**
     * JIT
     */
    SYNCFAILD,

    /**
     * 寻仓中
     */
    SYNCIN,

    /**
     * 虚拟寻仓中
     */
    IN_VIRTUAL_OCCUPY,

    /**
     * 失败
     */
    EXCEPTION;

    public int toInteger() {
        if (this == UNSYNC) {
            return 0;
        } else if (this == SYNCSUCCESS) {
            return 1;
        } else if (this == SYNCFAILD) {
            return 2;
        } else if (this == EXCEPTION) {
            return 3;
        }else if (this == SYNCIN) {
            return 4;
        } else if (this == IN_VIRTUAL_OCCUPY) {
            return 5;
        }else {
            return 111;
        }
    }
}
