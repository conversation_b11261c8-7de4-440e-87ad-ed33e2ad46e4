package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * @Des:订单中间表锁单状态
 * @author: 洪艺安
 * @since: 2019-10-9
 * create at : 2019-01-21 14:46
 */
public enum IpOrderLockStatusEnum {

    WAIT_LOCK("0", "待锁单"),
    LOCKED("1", "已锁单"),
    LOCK_FAIL("2", "锁单失败"),
    UNLOCK("3", "已解锁"),
    PART_LOCKED("4", "部分锁单"),
    PART_LOCK_FAIL("5", "部分锁单失败"),
    VOID("6", "已作废");


    IpOrderLockStatusEnum(String key, String name) {
        this.key = key;
        this.name = name;
    }

    @Getter
    private String key;

    @Getter
    private String name;


    /**
     * 根据状态值,获取状态名
     *
     * @param key
     * @return String
     */
    public static String enumToStringBykey(String key) {
        String s = "";
        if (key == null) {
            return s;
        }
        for (IpOrderLockStatusEnum e : IpOrderLockStatusEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getName();
                return s;
            }
        }
        return key;
    }
}
