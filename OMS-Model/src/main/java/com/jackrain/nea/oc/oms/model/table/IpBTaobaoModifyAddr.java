package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@TableName(value = "ip_b_taobao_modify_addr")
@Data
@Document(index = "ip_b_taobao_modify_addr", type = "ip_b_taobao_modify_addr")
public class IpBTaobaoModifyAddr extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "REMAR<PERSON>")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "IS_UPDATE")
    @Field(type = FieldType.Integer)
    private Integer isUpdate;

    @JSONField(name = "ADDRESS")
    @Field(type = FieldType.Keyword)
    private String address;

    @JSONField(name = "TOWNCODE")
    @Field(type = FieldType.Keyword)
    private String towncode;

    @JSONField(name = "DISTRICTCODE")
    @Field(type = FieldType.Keyword)
    private String districtcode;

    @JSONField(name = "CITYCODE")
    @Field(type = FieldType.Keyword)
    private String citycode;

    @JSONField(name = "PROVINCECODE")
    @Field(type = FieldType.Keyword)
    private String provincecode;

    @JSONField(name = "ZIP")
    @Field(type = FieldType.Keyword)
    private String zip;

    @JSONField(name = "RECEIVE_NAME")
    @Field(type = FieldType.Keyword)
    private String receiveName;

    @JSONField(name = "PHONE_NUM")
    @Field(type = FieldType.Keyword)
    private String phoneNum;

    @JSONField(name = "SOURCECODE")
    @Field(type = FieldType.Keyword)
    private String sourcecode;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    @JSONField(name = "OAID")
    @Field(type = FieldType.Keyword)
    private String oaid;




    /**
     * Start 通用平台修改地址
     */
    @ApiModelProperty(name = "同步方式")
    @JSONField(name = "SYNC_MODE")
    private String syncMode;

    @ApiModelProperty(name = "卖家昵称")
    @JSONField(name = "SELLER_NICK")
    private String sellerNick;

    @ApiModelProperty(name = "平台编码:id")
    @JSONField(name = "PLATFORM_CODE")
    private Long platformCode;

    @ApiModelProperty(name = "其他信息")
    @JSONField(name = "OTHER_DATA")
    private String otherData;

    @ApiModelProperty(name = "全部地址:上海 上海市 闵行区 AFC大虹桥国际5L")
    @JSONField(name = "ALL_ADDRESS")
    private String allAddress;

    @ApiModelProperty(name = "城镇")
    @JSONField(name = "receiver_town")
    private String receiverTown;
}