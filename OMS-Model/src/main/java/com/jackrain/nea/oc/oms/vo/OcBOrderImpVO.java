package com.jackrain.nea.oc.oms.vo;

import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Data
public class OcBOrderImpVO extends OcBOrder implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1L;
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    private static final String REG = "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]";
    // 订单类型
    private String orderTypeName;
    // 支付方式
    private String payTypeName;
    // 退款审核状态（AG使用）
    private String refundConfirmStatusName;
    // 双11预售状态
    private String double11PresaleStatusName;
    // 系统预售状态，非双11
    private String sysPresaleStatusName;
    // 是否开票
    private String isInvoiceName;
    //数量
    private BigDecimal qty;
    //sku编码
    private String psCSkuEcode;
    //行号
    private int rowNum;
    //成交单价
    private BigDecimal priceActual;
    //错误信息
    private String desc;
    //是否赠品
    private Integer isGift;

    /**
     * 平台售价
     */
    private BigDecimal platformPrice;

    //设置默认值
    public void checkDefault(OcBOrderImpVO ocBOrderImpVo) {
        if (ocBOrderImpVo.getOrderStatus() == null) {
            ocBOrderImpVo.setOrderStatus(1);
        }
        if (ocBOrderImpVo.getIsInvoice() == null) {
            ocBOrderImpVo.setIsInvoice(0);
        }
        if (ocBOrderImpVo.getIsGeninvoiceNotice() == null) {
            ocBOrderImpVo.setIsGeninvoiceNotice(0);
        }
        if (ocBOrderImpVo.getIsCalcweight() == null) {
            ocBOrderImpVo.setIsCalcweight(0);
        }
        if (ocBOrderImpVo.getIsMerge() == null) {
            ocBOrderImpVo.setIsMerge(0);
        }
        if (ocBOrderImpVo.getIsSplit() == null) {
            ocBOrderImpVo.setIsSplit(0);
        }
        if (ocBOrderImpVo.getIsInterecept() == null) {
            ocBOrderImpVo.setIsInterecept(0);
        }
        if (ocBOrderImpVo.getIsInreturning() == null) {
            ocBOrderImpVo.setIsInreturning(0);
        }
        if (ocBOrderImpVo.getIsHasgift() == null) {
            ocBOrderImpVo.setIsHasgift(0);
        }
        if (ocBOrderImpVo.getIsJcorder() == null) {
            ocBOrderImpVo.setIsJcorder(0);
        }
        if (ocBOrderImpVo.getIsCombination() == null) {
            ocBOrderImpVo.setIsCombination(0);
        }
        if (ocBOrderImpVo.getIsOutUrgency() == null) {
            ocBOrderImpVo.setIsOutUrgency(0);
        }
        if (ocBOrderImpVo.getIsHasTicket() == null) {
            ocBOrderImpVo.setIsHasTicket(0);
        }
    }

    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBOrderImpVO importCreate(int index, OcBOrderImpVO ocBOrderImpVo, Map<String, String> columnMap) {
        FastDateFormat dateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
        try {
            //  下单店铺  现在默认传title   之前是 CpCShopEcode
            ocBOrderImpVo.setCpCShopTitle(columnMap.get(rowStr + index + cellStr + 0));
        } catch (Exception e) {

        }
        try {
            //配送费用
            ocBOrderImpVo.setShipAmt(new BigDecimal(columnMap.get(rowStr + index + cellStr + 1)));
        } catch (Exception e) {

        }
        try {
            //买家昵称
            ocBOrderImpVo.setUserNick(columnMap.get(rowStr + index + cellStr + 2));
        } catch (Exception e) {

        }

        try {
            //平台单号
            ocBOrderImpVo.setSourceCode(columnMap.get(rowStr + index + cellStr + 3));
        } catch (Exception e) {

        }
        try {
            //付款方式:(一头牛调整为空)
            ocBOrderImpVo.setPayTypeName(columnMap.get(rowStr + index + cellStr + 4));
        } catch (Exception e) {

        }
        try {
            //收货人
            String receiverName = columnMap.get(rowStr + index + cellStr + 5);
            //过滤特殊字符和表情
            if(StringUtils.isNotBlank(receiverName)){
                receiverName = filterSpecialStr(receiverName);
                receiverName = filterEmoji(receiverName);
            }
            ocBOrderImpVo.setReceiverName(receiverName);
        } catch (Exception e) {

        }
        try {
            //收货人手机
            ocBOrderImpVo.setReceiverMobile(columnMap.get(rowStr + index + cellStr + 6));
        } catch (Exception e) {

        }
        try {
            //收货人电话
            ocBOrderImpVo.setReceiverPhone(columnMap.get(rowStr + index + cellStr + 7));
        } catch (Exception e) {

        }
        try {
            //收货人邮编
            ocBOrderImpVo.setReceiverZip(columnMap.get(rowStr + index + cellStr + 8));
        } catch (Exception e) {

        }
        try {
            //收货人省份
            ocBOrderImpVo.setCpCRegionProvinceEname(columnMap.get(rowStr + index + cellStr + 9));
        } catch (Exception e) {

        }
        try {
            //收货人市
            ocBOrderImpVo.setCpCRegionCityEname(columnMap.get(rowStr + index + cellStr + 10));
        } catch (Exception e) {

        }
        try {
            //收货人区
            ocBOrderImpVo.setCpCRegionAreaEname(columnMap.get(rowStr + index + cellStr + 11));
        } catch (Exception e) {

        }
        try {
            //收货人地址
            String receiverAddress = columnMap.get(rowStr + index + cellStr + 12);
            //过滤特殊字符和表情
            if(StringUtils.isNotBlank(receiverAddress)){
                receiverAddress = filterSpecialStr(receiverAddress);
                receiverAddress = filterEmoji(receiverAddress);
            }
            ocBOrderImpVo.setReceiverAddress(receiverAddress);
//            // 如果省市区为空则使用智能匹配地址进行匹配  一头牛需求变更 0911
//            if (StringUtils.isNotBlank(ocBOrderImpVo.getReceiverAddress())) {
//                Map<String, String> map = AddressResolutionUtils.addressResolution(ocBOrderImpVo.getReceiverAddress());
//                if (StringUtils.isBlank(ocBOrderImpVo.getCpCRegionProvinceEname())) {
//                    ocBOrderImpVo.setCpCRegionProvinceEname(map.get("province"));
//                }
//                if (StringUtils.isBlank(ocBOrderImpVo.getCpCRegionCityEname())) {
//                    ocBOrderImpVo.setCpCRegionCityEname(map.get("city"));
//                }
//                if (StringUtils.isBlank(ocBOrderImpVo.getCpCRegionAreaEname())) {
//                    ocBOrderImpVo.setCpCRegionAreaEname(map.get("area"));
//                }
//            }
        } catch (Exception e) {

        }

        // 商品名称（因只是excel对照用，不入库，这里不做处理）
        try {
            //商品编码
            ocBOrderImpVo.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 14));
        } catch (Exception e) {

        }
        try {
            //商品数量
            ocBOrderImpVo.setQty(new BigDecimal(columnMap.get(rowStr + index + cellStr + 15)));
        } catch (Exception e) {

        }
        try {
            //商品成交金额
            ocBOrderImpVo.setPriceActual(new BigDecimal(columnMap.get(rowStr + index + cellStr + 16)));
        } catch (Exception e) {

        }
        try {
            // 下单时间
            String orderDate = columnMap.get(rowStr + index + cellStr + 17);
            // 一头牛需求调整，当为空时默认当前操作时间 0912 浩南
            ocBOrderImpVo.setOrderDate(StringUtils.isNotBlank(orderDate) ? dateFormat.parse(orderDate) : new Date());
        } catch (Exception e) {

        }
        try {
            // 支付时间
            String payDate = columnMap.get(rowStr + index + cellStr + 18);
            // 一头牛需求调整，当为空时默认当前操作时间 0912 浩南
            ocBOrderImpVo.setPayTime(StringUtils.isNotBlank(payDate) ? dateFormat.parse(payDate) : new Date());
        } catch (Exception e) {

        }
        try {
            // 平台售价
            ocBOrderImpVo.setPlatformPrice(new BigDecimal(columnMap.get(rowStr + index + cellStr + 19)));
        } catch (Exception e) {

        }
        try {
            //买家备注
            ocBOrderImpVo.setBuyerMessage(columnMap.get(rowStr + index + cellStr + 20));
        } catch (Exception e) {

        }
        try {
            //卖家备注
            ocBOrderImpVo.setSellerMemo(columnMap.get(rowStr + index + cellStr + 21));
            //OAID
            ocBOrderImpVo.setOaid(columnMap.get(rowStr + index + cellStr + 22));
            //是否赠品 优化解析bug
            String isGift = columnMap.get(rowStr + index + cellStr + 23);
            if (StringUtils.isNotBlank(isGift) && "是".equals(isGift)) {
                ocBOrderImpVo.setIsGift(1);
            } else {
                ocBOrderImpVo.setIsGift(0);
            }
        } catch (Exception e) {

        }

        try {
            // 一头牛需求 新增业务 0912
            ocBOrderImpVo.setSalesmanName(columnMap.get(rowStr + index + cellStr + 24));
        } catch (Exception e) {

        }

        try {
            //是否明文
            String content = columnMap.get(rowStr + index + cellStr + 25);
            ocBOrderImpVo.setIsPlainAddr(OcBorderListEnums.YesOrNoEnum.IS_YES.getText().equals(content)
                    ? OcBorderListEnums.YesOrNoEnum.IS_YES.getVal() : OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
        } catch (Exception e) {

        }

        ocBOrderImpVo.setRowNum(index + 1);
        ocBOrderImpVo.checkDefault(ocBOrderImpVo);
        return ocBOrderImpVo;
    }

    /**
     * 过滤特殊字符
     * @param str
     * @return
     */
    private static String filterSpecialStr(String str){
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("-").trim();
    }

    /**
     * 过滤表情
     * @param source
     * @return
     */
    private static  String filterEmoji(String source) {
        if(source != null)
        {
            Pattern emoji = Pattern.compile(REG, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(source);
            if ( emojiMatcher.find())
            {
                source = emojiMatcher.replaceAll("");
                return source ;
            }
            return source;
        }
        return source;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!super.equals(obj)) {
            return false;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        OcBOrderImpVO other = (OcBOrderImpVO) obj;
        if (double11PresaleStatusName == null) {
            if (other.double11PresaleStatusName != null) {
                return false;
            }
        } else if (!double11PresaleStatusName.equals(other.double11PresaleStatusName)) {
            return false;
        }
        if (isInvoiceName == null) {
            if (other.isInvoiceName != null) {
                return false;
            }
        } else if (!isInvoiceName.equals(other.isInvoiceName)) {
            return false;
        }
        if (orderTypeName == null) {
            if (other.orderTypeName != null) {
                return false;
            }
        } else if (!orderTypeName.equals(other.orderTypeName)) {
            return false;
        }
        if (payTypeName == null) {
            if (other.payTypeName != null) {
                return false;
            }
        } else if (!payTypeName.equals(other.payTypeName)) {
            return false;
        }
        if (refundConfirmStatusName == null) {
            if (other.refundConfirmStatusName != null) {
                return false;
            }
        } else if (!refundConfirmStatusName.equals(other.refundConfirmStatusName)) {
            return false;
        }
        if (sysPresaleStatusName == null) {
            return other.sysPresaleStatusName == null;
        } else {
            return sysPresaleStatusName.equals(other.sysPresaleStatusName);
        }
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((double11PresaleStatusName == null) ? 0 : double11PresaleStatusName.hashCode());
        result = prime * result + ((isInvoiceName == null) ? 0 : isInvoiceName.hashCode());
        result = prime * result + ((orderTypeName == null) ? 0 : orderTypeName.hashCode());
        result = prime * result + ((payTypeName == null) ? 0 : payTypeName.hashCode());
        result = prime * result + ((refundConfirmStatusName == null) ? 0 : refundConfirmStatusName.hashCode());
        result = prime * result + ((sysPresaleStatusName == null) ? 0 : sysPresaleStatusName.hashCode());
        return result;
    }




}
