package com.jackrain.nea.oc.oms.model.enums;


import java.util.Objects;

/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/7/4
 **/
public enum OmsSendMsgTaskNodeEnum {

    NO_PLATFORM_SPLIT_ORDER_DELIVERY("非平台拆单完成仓库发货", 1),
    NO_TIANMAO_SWAP_ORDER_DELIVERY("非天猫换货订单完成仓库发货", 2),
    REFUND_SWAP_IN_STORAGE("退换货完成入库", 3),
    NO_NAME_IN_STORAGE("无名件完成入库", 4);

    String key;
    Integer val;

    OmsSendMsgTaskNodeEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    public static OmsSendMsgTaskNodeEnum getFromValue(Integer value) {
        OmsSendMsgTaskNodeEnum[] values = OmsSendMsgTaskNodeEnum.values();
        for (OmsSendMsgTaskNodeEnum statusEnum : values) {
            if (Objects.equals(statusEnum.val, value)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException(String.valueOf(value));
    }
}


