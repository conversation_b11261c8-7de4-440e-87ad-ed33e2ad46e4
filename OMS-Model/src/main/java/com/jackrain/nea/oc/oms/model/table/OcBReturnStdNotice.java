

package com.jackrain.nea.oc.oms.model.table;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 退货单入库私域通知
 *
 * <AUTHOR>
 * @date 2021-12-30 11:55:13
 */
@Data
@TableName("oc_b_return_std_notice")
@ApiModel("退货单入库私域通知")
public class OcBReturnStdNotice implements Serializable{
    private static final long serialVersionUID = 1L;

    @TableId
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("平台退款单号")
    private String returnId;
    @ApiModelProperty("退货单id")
    private Long ocBReturnOrderId;
    @ApiModelProperty("退款金额")
    private BigDecimal returnAmtActual;
    @ApiModelProperty("退回物流公司编码")
    private String cpCLogisticsEcode;
    @ApiModelProperty("退回物流公司名称")
    private String cpCLogisticsEname;
    @ApiModelProperty("退回物流单号")
    private String logisticsCode;
    @ApiModelProperty("店铺编码")
    private String cpCShopEcode;
    @ApiModelProperty("入库数量")
    private BigDecimal qtyInstore;
    @ApiModelProperty("入库时间")
    private Date inTime;
    @ApiModelProperty("通知状态（0：待通知，1：已通知，-1：通知失败）")
    private Integer noticeStatus;
    @ApiModelProperty("通知次数")
    private Integer noticeTimes;
    @ApiModelProperty("通知错误信息")
    private String errorMsg;
    @ApiModelProperty("平台编号")
    private Integer platform;
    @ApiModelProperty("平台订单号")
    private String tid;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("修改时间")
    private Date updateTime;

}
