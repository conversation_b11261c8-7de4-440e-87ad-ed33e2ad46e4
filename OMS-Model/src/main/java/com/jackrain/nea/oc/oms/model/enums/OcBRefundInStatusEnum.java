package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * @author: 秦雄飞
 * @time: 2021/5/22 6:12 下午
 * @description: 退货入库表 状态 枚举
 */

public class OcBRefundInStatusEnum {

    /**
     * 中间表解析状态
     */
    @ToString
    @AllArgsConstructor
    @Getter
    public enum RefundInTaskStatusEnum {

        INIT(0, "未转化"),

        SUCCESS(1, "转换成功"),

        FAIL(2, "转换失败");

        private Integer code;
        private String name;
    }

    /**
     * 退货入库单 特殊处理类型
     */
    @ToString
    @AllArgsConstructor
    @Getter
    public enum RefundInSpecialTypeEnum {

        NORMAL(0, "正常"),

        WRONG_DEAL(1, "错发扫描处理");

        private Integer code;
        private String name;
    }
}
