package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;

import java.io.Serializable;

public class GsiIpBTimeOrderVipOrderSn implements Serializable {
    private static final long serialVersionUID = 5362431120563549684L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    private String billNo;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;

    @JSONField(name = "OCCUPIED_ORDER_SN")
    @Field(type = FieldType.Keyword)
    private String occupiedOrderSn;

    @JSONField(name = "ORDER_SN")
    @Field(type = FieldType.Keyword)
    private String orderSn;
}