package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @Descroption 短信策略-短信发送记录
 * <AUTHOR>
 * @Date 2020/8/29
 */
@TableName(value = "oc_b_msg_send_record")
@Data
@Document(index = "oc_b_msg_send_record", type = "oc_b_msg_send_record")
public class OcBMsgSendRecord extends BaseModel {

    @JSONField(name = "ID")
    @Field(type = FieldType.Long)
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    //短信策略ID
    @JSONField(name = "ST_C_MSG_ID")
    @Field(type = FieldType.Long)
    private Long stCMsgId;

    //发送状态 0：初始化 1：发送成功 2：发送失败
    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    private String status;

    //收件人手机号
    @JSONField(name = "RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String receiverMobile;

    //重试次数
    @JSONField(name = "COUNT")
    @Field(type = FieldType.Long)
    private Long count;

    //短信内容
    @JSONField(name = "CONTENT")
    @Field(type = FieldType.Keyword)
    private String content;

    //备注
    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;
}
