package com.jackrain.nea.oc.oms.model.table;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;

/**
 * @author: 周琳胜
 * @since: 2019/7/11
 * create at : 2019/7/11 20:01
 */
@TableName(value = "ip_b_taobao_fx_order_item")
@Data
public class IpBTaobaoFxOrderItemExt extends IpBTaobaoFxOrderItem {

    @TableField(exist = false)
    private ProductSku prodSku;

}
