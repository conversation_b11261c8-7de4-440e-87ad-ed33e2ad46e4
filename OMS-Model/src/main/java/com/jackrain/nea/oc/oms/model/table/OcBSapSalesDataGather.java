package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import java.util.Date;
import lombok.Data;

@TableName(value = "OC_B_SAP_SALES_DATA_GATHER")
@Data
public class OcBSapSalesDataGather extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "COST_CENTER")
    private String costCenter;

    @JSONField(name = "SAP_FAIL_REASON")
    private String sapFailReason;

    @JSONField(name = "SUM_TYPE")
    private String sumType;

    @JSONField(name = "TO_SAP_STATUS")
    private String toSapStatus;

    @JSONField(name = "GATHER_DATE")
    private Date gatherDate;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "SALES_ORGANIZATION")
    private String salesOrganization;

    @JSONField(name = "SAP_BILL_TYPE")
    private String sapBillType;

    @JSONField(name = "IN_TIME")
    private Date inTime;

    @JSONField(name = "GATHER_NO")
    private String gatherNo;

    @JSONField(name = "TO_SAP_FAIL_NUM")
    private Integer toSapFailNum;

    @JSONField(name = "GATHER_MIDDLE_STATUS")
    private String gatherMiddleStatus;

    @JSONField(name = "MERGE_CODE")
    private String mergeCode;


}