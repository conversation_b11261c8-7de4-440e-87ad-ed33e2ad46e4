package com.jackrain.nea.oc.oms.vo;

import com.jackrain.nea.oc.oms.model.enums.OcBorderListEnums;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * @ClassName : OcBOrderRemarkImpVO  
 * @Description : 
 * <AUTHOR>  YCH
 * @Date: 2021-09-06 11:26  
 */

@Data
public class OcBOrderRemarkImpVO extends OcBOrder implements Serializable  {

    private static final long serialVersionUID = 1L;
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    //行号
    private int rowNum;

    //错误信息
    private String desc;
    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBOrderRemarkImpVO importCreate(int index, OcBOrderRemarkImpVO ocBOrderImpVo, Map<String, String> columnMap) {
        try {
            //平台单号
            ocBOrderImpVo.setSourceCode(columnMap.get(rowStr + index + cellStr + 0));
        } catch (Exception e) {

        }
        try {
            //省份
            ocBOrderImpVo.setCpCRegionProvinceEname(columnMap.get(rowStr + index + cellStr + 1));
        } catch (Exception e) {

        }
        try {
            //市
            ocBOrderImpVo.setCpCRegionCityEname(columnMap.get(rowStr + index + cellStr + 2));
        } catch (Exception e) {

        }

        try {
            //区
            ocBOrderImpVo.setCpCRegionAreaEname(columnMap.get(rowStr + index + cellStr + 3));
        } catch (Exception e) {

        }
        try {
            //详细地址
            ocBOrderImpVo.setReceiverAddress(columnMap.get(rowStr + index + cellStr + 4));
        } catch (Exception e) {

        }
        try {
            //收货人
            ocBOrderImpVo.setReceiverName(columnMap.get(rowStr + index + cellStr + 5));
        } catch (Exception e) {

        }
        try {
            //收货人手机
            ocBOrderImpVo.setReceiverMobile(columnMap.get(rowStr + index + cellStr + 6));
        } catch (Exception e) {

        }
        try {
            //收货人电话
            ocBOrderImpVo.setReceiverPhone(columnMap.get(rowStr + index + cellStr + 7));
        } catch (Exception e) {

        }
        try {
            //收货人邮编
            ocBOrderImpVo.setReceiverZip(columnMap.get(rowStr + index + cellStr + 8));
        } catch (Exception e) {

        }
        try {
            //是否明文
            String content = columnMap.get(rowStr + index + cellStr + 9);
            ocBOrderImpVo.setIsPlainAddr(OcBorderListEnums.YesOrNoEnum.IS_YES.getText().equals(content)
                    ? OcBorderListEnums.YesOrNoEnum.IS_YES.getVal() : OcBorderListEnums.YesOrNoEnum.IS_NO.getVal());
        } catch (Exception e) {

        }
        ocBOrderImpVo.setRowNum(index + 1);
        return ocBOrderImpVo;
    }

}
