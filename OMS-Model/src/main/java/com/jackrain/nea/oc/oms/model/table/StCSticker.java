package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * st_c_sticker
 *
 * <AUTHOR>
@TableName
@Data
public class StCSticker extends BaseModel {
    @JSONField(name = "ID")
    private Long id;

    /**
     * 策略名称
     */
    @JSONField(name = "NAME")
    private String name;

    /**
     * 店铺id
     */
    @JSONField(name = "SHOP_ID")
    private Long shopId;

    /**
     * 状态(1.未审核,2已审核,3已结案)
     */
    @JSONField(name = "STATUS")
    private Integer status;

    /**
     * 开始时间
     */
    @JSONField(name = "START_TIME")
    private Date startTime;

    /**
     * 结束时间
     */
    @JSONField(name = "END_TIME")
    private Date endTime;

    /**
     * 备注
     */
    @JSONField(name = "REMARKS")
    private String remarks;

    /**
     * 审核人
     */
    @JSONField(name = "SUBMIT_USER_ID")
    private Long submitUserId;
    /**
     * 审核时间
     */
    @JSONField(name = "SUBMIT_TIME")
    private Date submitTime;
    /**
     * 结案人
     */
    @JSONField(name = "CLOSE_USER_ID")
    private Long closeUserId;
    /**
     * 结案时间
     */
    @JSONField(name = "CLOSE_TIME")
    private Date closeTime;

    private static final long serialVersionUID = 1L;
}