package com.jackrain.nea.oc.oms.model.enums;

import cn.hutool.core.collection.CollUtil;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/3/22 13:49
 * @Description
 */
public enum LogisticsServiceTypeEnum {

    ZTO(1, "中通"),

    Y<PERSON><PERSON>(2, "韵达"),

    <PERSON><PERSON>(3, "极兔"),

    <PERSON><PERSON>(4, "京东"),

    <PERSON><PERSON>(5, "丹鸟"),

    EMS(6, "EMS"),

    SF(7, "顺丰"),

    YTO(8, "圆通"),

    ;

    Integer val;
    String description;

    LogisticsServiceTypeEnum(int v, String s) {
        this.val = v;
        this.description = s;
    }

    public Integer getVal() {
        return val;
    }

    public String getDescription() {
        return description;
    }


    private static final List<Integer> cancelTypeList =
            CollUtil.newArrayList(ZTO.getVal(), YTO.getVal());

    public static boolean cancelType(Integer type) {
        return cancelTypeList.contains(type);
    }

    public static LogisticsServiceTypeEnum of(Integer val) {
        if (Objects.isNull(val)) {
            return null;
        }
        for (LogisticsServiceTypeEnum e : LogisticsServiceTypeEnum.values()) {
            if (Objects.equals(val, e.getVal())) {
                return e;
            }
        }
        return null;
    }
}
