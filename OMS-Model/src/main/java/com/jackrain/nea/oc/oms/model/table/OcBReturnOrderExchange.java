package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_return_order", type = "oc_b_return_order_exchange")
@ApiModel(value = "oc_b_return_order_exchange", description = "换货商品")
public class OcBReturnOrderExchange extends BaseModel {


    @ApiModelProperty(value = "编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "国标码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BARCODE")
    private String barcode;

    @ApiModelProperty(value = "子订单ID")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OID")
    private String oid;

    @ApiModelProperty(value = "规格")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SKU_SPEC")
    private String skuSpec;

    @ApiModelProperty(value = "条码id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @ApiModelProperty(value = "条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @ApiModelProperty(value = "商品id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @ApiModelProperty(value = "商品编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @ApiModelProperty(value = "商品名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @ApiModelProperty(value = "平台换货条码ID")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "EXCHANGE_SKU")
    private String exchangeSku;

    @ApiModelProperty(value = "换货金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_REFUND")
    private BigDecimal amtRefund;

    @ApiModelProperty(value = "调整金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_ADJUST")
    private BigDecimal amtAdjust;

    @ApiModelProperty(value = "商品单价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE")
    private BigDecimal price;

    @ApiModelProperty(value = "退、换货标识")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_RETURN")
    private Integer isReturn;

    @ApiModelProperty(value = "退换单ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_RETURN_ORDER_ID")
    private Long ocBReturnOrderId;

    @ApiModelProperty(value = "入库数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_IN")
    private BigDecimal qtyIn;

    @ApiModelProperty(value = "换货数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_EXCHANGE")
    private BigDecimal qtyExchange;

    @ApiModelProperty(value = "购买数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_BUY")
    private BigDecimal qtyBuy;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @ApiModelProperty(value = "吊牌价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_LIST")
    private BigDecimal priceList;

    @ApiModelProperty(value = "结算单价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_SETTLE")
    private BigDecimal priceSettle;

    @ApiModelProperty(value = "结算总额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_SETTLE_TOT")
    private BigDecimal amtSettleTot;

    @ApiModelProperty(value = "颜色id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_CLR_ID")
    private Long psCClrId;

    @ApiModelProperty(value = "颜色编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_CLR_ECODE")
    private String psCClrEcode;

    @ApiModelProperty(value = "颜色名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_CLR_ENAME")
    private String psCClrEname;

    @ApiModelProperty(value = "尺寸id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SIZE_ID")
    private Long psCSizeId;

    @ApiModelProperty(value = "尺寸编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SIZE_ECODE")
    private String psCSizeEcode;

    @ApiModelProperty(value = "尺寸名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SIZE_ENAME")
    private String psCSizeEname;

    @ApiModelProperty(value = "性别")
    @Field(type = FieldType.Long)
    @JSONField(name = "SEX")
    private Long sex;

    @ApiModelProperty(value = "条码名称 ")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ENAME")
    private String psCSkuEname;

    @ApiModelProperty(value = "数字类型备用字段1")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @ApiModelProperty(value = "数字类型备用字段2")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @ApiModelProperty(value = "数字类型备用字段3")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @ApiModelProperty(value = "数字类型备用字段4")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @ApiModelProperty(value = "数字类型备用字段5")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @ApiModelProperty(value = "价格备用字段1")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "价格备用字段3")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @ApiModelProperty(value = "价格备用字段4")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @ApiModelProperty(value = "价格备用字段5")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @ApiModelProperty(value = "文本备用字段1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @ApiModelProperty(value = "文本备用字段2")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @ApiModelProperty(value = "文本备用字段3")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @ApiModelProperty(value = "文本备用字段4")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @ApiModelProperty(value = "文本备用字段5")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @ApiModelProperty(value = "平台换货状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "EXCHANGE_STATUS")
    private String exchangeStatus;

    @ApiModelProperty(value = "平台换货单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DISPUTE_ID")
    private String disputeId;

    @TableField(exist = false)
    @JSONField(name = "OC_B_ORDER_ITEM_ID")
    private Long ocBOrderItemId;
}