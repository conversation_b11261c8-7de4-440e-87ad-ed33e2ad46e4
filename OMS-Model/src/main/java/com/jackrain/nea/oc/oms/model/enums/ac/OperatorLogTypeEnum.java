package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * 应收/应付款调整单 操作类型枚举
 *
 * @author: 陈俊明
 * @since: 2019-12-26
 * @create at : 2019-12-26 下午 08:02
 */
@Getter
public enum OperatorLogTypeEnum {
    /**
     * 新增
     */
    OPERATOR_ADD(1,"新增"),
    /**
     * 编辑保存
     */
    OPERATOR_UPD(2,"编辑保存"),
    /**
     * 客审
     */
    OPERATOR_KS(3,"客审"),

    /**
     * 反客审
     */
    OPERATOR_FKS(3,"反客审"),

    /**
     * 财审
     */
    OPERATOR_CS(4,"财审"),
    /**
     * 作废
     */
    OPERATOR_VOID(5,"作废"),
    /**
     * 新增(丢单复制)
     */
    OPERATOR_DROP(6, "新增(丢单复制)"),
    /**
     * 新增(平台)
     */
    OPERATOR_PLAT(7, "新增(平台)"),
    /**
     * 单据导入
     */
    OPERATOR_IMPORT(8, "单据导入"),

    /**
     * 新增退单(丢单复制)
     */
    OPERATOR_DROP_RETURN(9, "新增退单(丢单复制)"),

    /**
     * 入库已完成 取消赔付
     */
    OPERATOR_RETURN_VOID(10,"入库已完成，作废丢件单"),
    /**
     * 确认责任方
     */

    CONFIRM_RESPONSIBLE_PARTY(11,"确认责任方"),
    BUSINESS_AUDIT(13,"业务审核"),
    ORIGIN_ORDER_ADD(12,"原单生成丢件单"),
    PUSH_TO_WING(15,"丢件赔付传wing");


    int val;
    String text;

    OperatorLogTypeEnum(int val, String text) {
        this.text = text;
        this.val = val;
    }

    public String getText() {
        return text;
    }

    public int getVal() {
        return val;
    }
}
