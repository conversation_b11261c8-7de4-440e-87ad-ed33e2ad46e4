package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2022/8/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_return_order", type = "oc_b_return_order_actual")
@ApiModel(value = "oc_b_return_order_actual", description = "退货单实物商品明细")
public class OcBReturnOrderActual extends BaseModel {

    @ApiModelProperty(value = "明细编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "退换货单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_RETURN_ORDER_ID")
    private Long ocBReturnOrderId;

    @ApiModelProperty(value = "退货入库单id")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_REFUND_IN_ID")
    private Long ocBRefundInId;

    @ApiModelProperty(value = "条码id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @ApiModelProperty(value = "条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @ApiModelProperty(value = "商品id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @ApiModelProperty(value = "商品编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @ApiModelProperty(value = "商品名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @ApiModelProperty(value = "国标码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GBCODE")
    private String gbcode;

    @ApiModelProperty(value = "数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY")
    private BigDecimal qty;

    @ApiModelProperty(value = "吊牌价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_LIST")
    private BigDecimal priceList;

    @ApiModelProperty(value = "颜色id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_CLR_ID")
    private Long psCClrId;

    @ApiModelProperty(value = "颜色编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_CLR_ECODE")
    private String psCClrEcode;

    @ApiModelProperty(value = "颜色名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_CLR_ENAME")
    private String psCClrEname;

    @ApiModelProperty(value = "尺寸id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SIZE_ID")
    private Long psCSizeId;

    @ApiModelProperty(value = "尺寸编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SIZE_ECODE")
    private String psCSizeEcode;

    @ApiModelProperty(value = "尺寸名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SIZE_ENAME")
    private String psCSizeEname;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

}
