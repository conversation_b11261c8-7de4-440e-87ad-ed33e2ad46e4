package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_jingdong_refund")
@Data
@Document(index = "ip_b_jingdong_refund", type = "ip_b_jingdong_refund")
public class IpBJingdongRefund extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertdate;

    @JSONField(name = "MODIFYDATE")
    @Field(type = FieldType.Long)
    private Date modifydate;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "SHOP_NAME")
    @Field(type = FieldType.Keyword)
    private String shopName;

    @JSONField(name = "AFSSERVICEID")
    @Field(type = FieldType.Long)
    private Long afsserviceid;

    @JSONField(name = "IP_B_JINGDONG_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBJingdongOrderId;

    @JSONField(name = "ORDERTYPE")
    @Field(type = FieldType.Long)
    private Long ordertype;

    @JSONField(name = "ORDERTYPENAME")
    @Field(type = FieldType.Keyword)
    private String ordertypename;

    @JSONField(name = "WARENAME")
    @Field(type = FieldType.Keyword)
    private String warename;

    @JSONField(name = "WAREID")
    @Field(type = FieldType.Long)
    private Long wareid;

    @JSONField(name = "CUSTOMERNAME")
    @Field(type = FieldType.Keyword)
    private String customername;

    @JSONField(name = "CUSTOMEREXPECT")
    @Field(type = FieldType.Long)
    private Long customerexpect;

    @JSONField(name = "CUSTOMEREXPECTNAME")
    @Field(type = FieldType.Keyword)
    private String customerexpectname;

    @JSONField(name = "AFSSERVICESTATUS")
    @Field(type = FieldType.Long)
    private Long afsservicestatus;

    @JSONField(name = "AFSSERVICESTATUSNAME")
    @Field(type = FieldType.Keyword)
    private String afsservicestatusname;

    @JSONField(name = "AFSAPPLYTIME")
    @Field(type = FieldType.Long)
    private Date afsapplytime;

    @JSONField(name = "AFSAPPROVEDTIME")
    @Field(type = FieldType.Long)
    private Date afsapprovedtime;

    @JSONField(name = "CUSTOMERPIN")
    @Field(type = FieldType.Keyword)
    private String customerpin;

    @JSONField(name = "AFSCATEGORYID")
    @Field(type = FieldType.Long)
    private Long afscategoryid;

    @JSONField(name = "AFSDETAILTYPE")
    @Field(type = FieldType.Double)
    private BigDecimal afsdetailtype;

    @JSONField(name = "APPROVERESONCID2")
    @Field(type = FieldType.Long)
    private Long approveresoncid2;

    @JSONField(name = "CUSTOMERMOBILEPHONE")
    @Field(type = FieldType.Keyword)
    private String customermobilephone;

    @JSONField(name = "CUSTOMERGRADE")
    @Field(type = FieldType.Long)
    private Long customergrade;

    @JSONField(name = "PICKWAREADDRESS")
    @Field(type = FieldType.Keyword)
    private String pickwareaddress;

    @JSONField(name = "REASON")
    @Field(type = FieldType.Keyword)
    private String reason;

    @JSONField(name = "EXPRESSCOMPANY")
    @Field(type = FieldType.Keyword)
    private String expresscompany;

    @JSONField(name = "EXPRESSCODE")
    @Field(type = FieldType.Keyword)
    private String expresscode;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal price;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "NICK")
    @Field(type = FieldType.Keyword)
    private String nick;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    @JSONField(name = "ORDERID")
    @Field(type = FieldType.Keyword)
    private String orderid;

    /**
     * 转换失败原因
     */
    @JSONField(name = "TRANS_FAIL_REASON")
    @Field(type = FieldType.Integer)
    private Integer transFailReason;

    @JSONField(name = "SERVICECOUNT")
    @Field(type = FieldType.Double)
    private BigDecimal servicecount;

}