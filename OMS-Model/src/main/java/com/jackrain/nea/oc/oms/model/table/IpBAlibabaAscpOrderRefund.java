package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 猫超直发退单中间表 -头表
 */
@TableName(value = "ip_b_alibaba_ascp_order_refund")
@Data
@Document(index = "ip_b_alibaba_ascp_order_refund", type = "ip_b_alibaba_ascp_order_refund")
public class IpBAlibabaAscpOrderRefund extends BaseModel {
    /**
     * 退单ID
     */
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 供应商ID
     */
    @JSONField(name = "SUPPLIERID")
    @Field(type = FieldType.Keyword)
    private String supplierId;

    /**
     * 供应商名称
     */
    @JSONField(name = "SUPPLIERNAME")
    @Field(type = FieldType.Keyword)
    private String supplierName;


    /**
     * 逆向退货单号
     */
    @JSONField(name = "BIZORDERCODE")
    @Field(type = FieldType.Keyword)
    private String bizOrderCode;

    /**
     * 退回仓库编码
     */
    @JSONField(name = "STORECODE")
    @Field(type = FieldType.Keyword)
    private String storeCode;

    /**
     * 退回快递编码
     */
    @JSONField(name = "TMSSERVICECODE")
    @Field(type = FieldType.Keyword)
    private String tmsServiceCode;

    /**
     * 退回快递单号
     */
    @JSONField(name = "TMSORDERCODE")
    @Field(type = FieldType.Keyword)
    private String tmsOrderCode;


    /**
     * 履约单号（开放平台：正向发货物流单号）
     */
    @JSONField(name = "FORWARDORDERCODE")
    @Field(type = FieldType.Keyword)
    private String forwardOrderCode;

    /**
     * 外部业务编号
     */
    @JSONField(name = "OUT_BIZ_ID")
    @Field(type = FieldType.Keyword)
    private String outBizId;


    /**
     * 收件人邮编
     */
    @JSONField(name = "RECEIVER_ZIP_CODE")
    @Field(type = FieldType.Keyword)
    private String receiverZipcode;
    /**
     * 收件人国家
     */
    @JSONField(name = "RECEIVER_COUNTRY")
    @Field(type = FieldType.Keyword)
    private String receiverCountry;
    /**
     * 收件人省份
     */
    @JSONField(name = "RECEIVER_PROVINCE")
    @Field(type = FieldType.Keyword)
    private String receiverProvince;
    /**
     * 收件人城市
     */
    @JSONField(name = "RECEIVER_CITY")
    @Field(type = FieldType.Keyword)
    private String receiverCity;
    /**
     * 收件人区县
     */
    @JSONField(name = "RECEIVER_AREA")
    @Field(type = FieldType.Keyword)
    private String receiverArea;
    /**
     * 收件人镇
     */
    @JSONField(name = "RECEIVE_TOWN")
    @Field(type = FieldType.Keyword)
    private String receiveTown;
    /**
     * 收件人地址
     */
    @JSONField(name = "RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;
    /**
     * 收件人名称
     */
    @JSONField(name = "RECEIVER_NAME")
    @Field(type = FieldType.Keyword)
    private String receiverName;
    /**
     * 收件人手机
     */
    @JSONField(name = "RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String receiverMobile;
    /**
     * 收件人电话
     */
    @JSONField(name = "RECEIVER_PHONE")
    @Field(type = FieldType.Keyword)
    private String receiverPhone;
    /**
     * 退货人邮编
     */
    @JSONField(name = "SENDER_ZIPCODE")
    @Field(type = FieldType.Keyword)
    private String senderZipcode;
    /**
     * 退货人国家
     */
    @JSONField(name = "SENDER_COUNTRY")
    @Field(type = FieldType.Keyword)
    private String senderCountry;
    /**
     * 退货人省份
     */
    @JSONField(name = "SENDER_PROVINCE")
    @Field(type = FieldType.Keyword)
    private String senderProvince;
    /**
     * 退货人城市
     */
    @JSONField(name = "SENDER_CITY")
    @Field(type = FieldType.Keyword)
    private String senderCity;
    /**
     * 退货人区县
     */
    @JSONField(name = "SENDER_AREA")
    @Field(type = FieldType.Keyword)
    private String senderArea;
    /**
     * 退货人镇村
     */
    @JSONField(name = "SENDER_TOWN")
    @Field(type = FieldType.Keyword)
    private String senderTown;
    /**
     * 退货人地址
     */
    @JSONField(name = "SENDER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String senderAddress;
    /**
     * 退货人名称
     */
    @JSONField(name = "SENDER_NAME")
    @Field(type = FieldType.Keyword)
    private String senderName;
    /**
     * 退货人手机
     */
    @JSONField(name = "SENDER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String senderMobile;
    /**
     * 退货人电话
     */
    @JSONField(name = "SENDER_PHONE")
    @Field(type = FieldType.Keyword)
    private String senderPhone;

    //region 数据库扩展字段
    /**
     * 插入时间
     */
    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertDate;

    /**
     * 交易结束时间
     */
    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Long)
    private Date endTime;

    /**
     * 交易修改时间
     */
    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    /**
     * 付款时间
     */
    @JSONField(name = "PAY_TIME")
    @Field(type = FieldType.Long)
    private Date payTime;

    /**
     * 交易创建时间
     */
    @JSONField(name = "CREATED")
    @Field(type = FieldType.Long)
    private Date created;

    /**
     * 店铺Id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    /**
     * 店铺编码
     */
    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    /**
     * 转化时间
     */
    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transDate;

    /**
     * 转换备注
     */
    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    /**
     * 转换次数
     */
    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;


    /**
     * 创建人ID
     */
    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerId;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownereName;

    /**
     * 创建人用户名
     */
    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownerName;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    private Date creationDate;

    /**
     * 修改人id
     */
    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierId;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifiereName;

    /**
     * 修改人用户名
     */
    @JSONField(name = "modifiername")
    @Field(type = FieldType.Keyword)
    private String modifierName;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    private Date modifiedDate;

    /**
     * 是否可用 默认 "y"
     */
    @JSONField(name = "isactive")
    @Field(type = FieldType.Keyword)
    private String isActive;
    //endregion

    //region 预留扩展字段
    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;
    //endregion

    /**
     * 卖家昵称
     */
    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    /**
     * 退款状态
     */
    @JSONField(name = "RETURN_STATUS")
    @Field(type = FieldType.Integer)
    private Integer returnStatus;

    /**
     * 是否退货
     */
    @JSONField(name = "HAS_GOOD_RETURN")
    @Field(type = FieldType.Integer)
    private Integer hasGoodReturn;

    /**
     * 退单退货类型
     */
    @JSONField(name = "REFUND_TYPE")
    @Field(type = FieldType.Integer)
    private Integer refundType;
    //endregion
}