package com.jackrain.nea.oc.oms.model.yike;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import java.math.BigDecimal;
import lombok.Data;

@TableName(value = "oc_b_yike_update_price")
@Data
public class OcBYikeUpdatePrice extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "STATUS")
    private Integer status;

    @JSONField(name = "SHOP_CODE")
    private String shopCode;

    @JSONField(name = "ITEM_NO")
    private String itemNo;

    @JSONField(name = "IS_ON_SALE")
    private String isOnSale;

    @JSONField(name = "SALE_PRICE")
    private BigDecimal salePrice;

    @JSONField(name = "DELIV_TYPE")
    private Integer delivType;

    @JSONField(name = "BAR_CODE")
    private String barCode;

    @JSONField(name = "ITEM_SALE_PRICE")
    private BigDecimal itemSalePrice;

    @JSONField(name = "FAIL_REASON")
    private String failReason;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION_NO")
    private Long versionNo;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

}