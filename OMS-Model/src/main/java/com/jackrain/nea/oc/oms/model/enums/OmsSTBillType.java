package com.jackrain.nea.oc.oms.model.enums;

/**
 * 订单类型（物流方案）
 *
 * @author: 胡林洋
 * @since: 2019-03-13
 * create at : 2019-03-07 15:21
 */
public enum OmsSTBillType {

    /**
     * 普通订单
     */
    NORMAL_ORDER,
    /**
     * 预售订单
     */
    PRESALE_ORDER,
    /**
     * 货到付款订单
     */
    CASH_ON_DELIVERY_ORDER;

    /**
     * 1.普通订单 2, 预售订单 3, 货到付款订单
     */
    public int toInteger() {
        if (this == OmsSTBillType.NORMAL_ORDER) {
            return 1;
        } else if (this == OmsSTBillType.PRESALE_ORDER) {
            return 2;
        } else if (this == OmsSTBillType.CASH_ON_DELIVERY_ORDER) {
            return 3;
        } else {
            return 0;
        }
    }

}
