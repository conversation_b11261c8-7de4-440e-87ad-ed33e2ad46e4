package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * description：预下沉状态
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
public enum PresinkStatusEnum {

    NOT_NOTIFIED("1", "未通知"),

    NOTIFIED("2", "已通知"),

    COMPLETED("3", "已完成"),

    CANCELLED("4", "已取消");

    String val;
    String text;

    PresinkStatusEnum(String v, String s) {
        this.val = v;
        this.text = s;
    }

    public String getVal() {
        return val;
    }

    public String getText() {
        return text;
    }

    /**
     * 根据值,转换成文本
     */
    public static String getTextByVal(String val) {
        for (PresinkStatusEnum o : PresinkStatusEnum.values()) {
            if (Objects.equals(o.getVal(), val)) {
                return o.getText();
            }
        }
        return "";
    }

    /**
     * 转化为hashMap
     *
     * @return map
     */
    public static Map convertAllToHashVal() {
        Map<String, String> m = new HashMap<>();
        for (PresinkStatusEnum o : PresinkStatusEnum.values()) {
            m.put(o.getVal(), o.getText());
        }
        return m;
    }
}