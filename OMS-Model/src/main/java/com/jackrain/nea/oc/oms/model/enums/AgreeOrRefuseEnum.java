package com.jackrain.nea.oc.oms.model.enums;


import java.util.Objects;


/**
 * <AUTHOR> ruan.gz
 * @Description : 同意或者拒绝
 * @Date : 2020/7/4
 **/
public enum AgreeOrRefuseEnum {

    AGREE("同意", 1),
    REFUSE("拒绝", 2);


    String key;
    Integer val;

    AgreeOrRefuseEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    public static AgreeOrRefuseEnum getFromValue(Integer value) {
        AgreeOrRefuseEnum[] values = AgreeOrRefuseEnum.values();
        for (AgreeOrRefuseEnum statusEnum : values) {
            if (Objects.equals(statusEnum.val, value)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException(String.valueOf(value));
    }
}


