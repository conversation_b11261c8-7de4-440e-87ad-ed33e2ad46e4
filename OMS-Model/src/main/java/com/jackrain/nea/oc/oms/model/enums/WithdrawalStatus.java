//package com.jackrain.nea.oc.oms.model.enums;
//
///**
// * @author: 夏继超
// * @since: 2019/3/26
// * create at : 2019/3/26 8:59
// */
//public enum WithdrawalStatus {
//    //退换货状态,20等待退货入库，30等待售后确认，50完成，60取消
//    /**
//     * 20等待退货入库
//     */
//    WAITING_FOR_RETURN_TO_WAREHOUSE,
//    /**
//     * 30等待售后确认
//     */
//    WAITING_FOR_AFTER_SALE_CONFIRMATION,
//    /**
//     * 50完成
//     */
//    COMPLETE,
//    /**
//     * 60取消
//     */
//    CANCEL;
////    public int toInteger() {
////        if (this == WithdrawalStatus.WAITING_FOR_RETURN_TO_WAREHOUSE) {
////            return 20;
////        } else if (this == WithdrawalStatus.WAITING_FOR_AFTER_SALE_CONFIRMATION) {
////            return 30;
////        } else if (this == WithdrawalStatus.COMPLETE) {
////            return 50;
////        } else {
////            return 60;
////        }
////    }
////
////    public String toDescription() {
////        if (this == WithdrawalStatus.WAITING_FOR_RETURN_TO_WAREHOUSE) {
////            return "WAITING_FOR_RETURN_TO_WAREHOUSE";
////        } else if (this == WithdrawalStatus.WAITING_FOR_AFTER_SALE_CONFIRMATION) {
////            return "WAITING_FOR_AFTER_SALE_CONFIRMATION";
////        } else if (this == WithdrawalStatus.COMPLETE) {
////            return "COMPLETE";
////        } else {
////            return "CANCEL";
////        }
////    }
//}
