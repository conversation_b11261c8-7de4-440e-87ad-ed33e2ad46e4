package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "oc_b_tob_find_source_diff_log")
@Data
public class OcBTobFindSourceDiffLog extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "CHOOSE_TYPE")
    private Integer chooseType;

    @JSONField(name = "LINE_NO")
    private Integer lineNo;

    @JSONField(name = "SOURCE_BILL_ID")
    private Long sourceBillId;

    @JSONField(name = "SOURCE_BILL_NO")
    private String sourceBillNo;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @JSONField(name = "TOTAL_FEE")
    private BigDecimal totalFee;

    @JSONField(name = "IS_RADIATION_WAREHOUSE")
    private String isRadiationWarehouse;

    @JSONField(name = "ARRIVAL_DAYS")
    private Integer arrivalDays;

    @JSONField(name = "UNFULLCAR_COST")
    private BigDecimal unfullcarCost;

    @JSONField(name = "ALLOCATION_COST")
    private BigDecimal allocationCost;

    @JSONField(name = "STORAGE_COST")
    private BigDecimal storageCost;

    @JSONField(name = "FACTORY")
    private Integer factory;

    @JSONField(name = "FIND_SOURCE_REASON")
    private String findSourceReason;

    @JSONField(name = "REMARK")
    private String remark;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}