package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_standplat_order")
@Data
@Document(index = "ip_b_standplat_order", type = "ip_b_standplat_order")
public class IpBStandplatOrder extends BaseModel {
    private static final long serialVersionUID = 8174577141078258110L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "IP_B_STAPORDPRO")
    @Field(type = FieldType.Long)
    private Long ipBStapordpro;

    @JSONField(name = "CP_C_PLATFORM_ID")
    @Field(type = FieldType.Long)
    private Long cpCPlatformId;

    @JSONField(name = "CP_C_PLATFORM_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEcode;

    @JSONField(name = "CP_C_PLATFORM_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPlatformEname;

    @JSONField(name = "SHIPPING_TYPE")
    @Field(type = FieldType.Keyword)
    private String shippingType;

    @JSONField(name = "BUYER_EMAIL")
    @Field(type = FieldType.Keyword)
    private String buyerEmail;

    @JSONField(name = "ISREMIND")
    @Field(type = FieldType.Integer)
    private Integer isremind;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "SELLER_MEMO")
    @Field(type = FieldType.Keyword)
    private String sellerMemo;

    @JSONField(name = "INVOICE_NAME")
    @Field(type = FieldType.Keyword)
    private String invoiceName;

    @JSONField(name = "INVOICETYPE")
    @Field(type = FieldType.Keyword)
    private String invoicetype;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertdate;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    @JSONField(name = "RECEIVER_PHONE")
    @Field(type = FieldType.Keyword)
    private String receiverPhone;

    @JSONField(name = "RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String receiverMobile;

    @JSONField(name = "RECEIVER_ZIP")
    @Field(type = FieldType.Keyword)
    private String receiverZip;

    @JSONField(name = "RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;

    @JSONField(name = "RECEIVER_TOWN")
    @Field(type = FieldType.Keyword)
    private String receiverTown;

    @JSONField(name = "RECEIVER_DISTRICT")
    @Field(type = FieldType.Keyword)
    private String receiverDistrict;

    @JSONField(name = "RECEIVER_CITY")
    @Field(type = FieldType.Keyword)
    private String receiverCity;

    @JSONField(name = "RECEIVER_PROVINCE")
    @Field(type = FieldType.Keyword)
    private String receiverProvince;

    @JSONField(name = "RECEIVER_NAME")
    @Field(type = FieldType.Keyword)
    private String receiverName;

    @JSONField(name = "RECEIVER_STATE")
    @Field(type = FieldType.Keyword)
    private String receiverState;

    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    @JSONField(name = "TRADE_SOURCE")
    @Field(type = FieldType.Keyword)
    private String tradeSource;

    @JSONField(name = "SEND_TIME")
    @Field(type = FieldType.Keyword)
    private String sendTime;

    @JSONField(name = "MARK_DESC")
    @Field(type = FieldType.Keyword)
    private String markDesc;

    @JSONField(name = "BUYER_MESSAGE")
    @Field(type = FieldType.Keyword)
    private String buyerMessage;

    @JSONField(name = "BUYER_AREA")
    @Field(type = FieldType.Keyword)
    private String buyerArea;

    @JSONField(name = "BUYER_NICK")
    @Field(type = FieldType.Keyword)
    private String buyerNick;

    @JSONField(name = "TRADE_END_TIME")
    @Field(type = FieldType.Long)
    private Date tradeEndTime;

    @JSONField(name = "TRADE_UPDATE_TIME")
    @Field(type = FieldType.Long)
    private Date tradeUpdateTime;

    @JSONField(name = "PAY_TIME")
    @Field(type = FieldType.Long)
    private Date payTime;

    @JSONField(name = "TRADE_CREATE_TIME")
    @Field(type = FieldType.Long)
    private Date tradeCreateTime;

    @JSONField(name = "COMMISSION_FEE")
    @Field(type = FieldType.Keyword)
    private String commissionFee;

    @JSONField(name = "COD_STATUS")
    @Field(type = FieldType.Keyword)
    private String codStatus;

    @JSONField(name = "COD_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal codFee;

    @JSONField(name = "PAYMENT")
    @Field(type = FieldType.Double)
    private BigDecimal payment;

    @JSONField(name = "POST_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal postFee;

    @JSONField(name = "ADJUST_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal adjustFee;

    @JSONField(name = "TOTAL_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal totalFee;

    @JSONField(name = "DISCOUNT_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal discountFee;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal price;

    @JSONField(name = "TYPE")
    @Field(type = FieldType.Keyword)
    private String type;

    @JSONField(name = "ORDER_TYPE")
    @Field(type = FieldType.Keyword)
    private String orderType;

    @JSONField(name = "TITLE")
    @Field(type = FieldType.Keyword)
    private String title;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    private String status;

    @JSONField(name = "NUM")
    @Field(type = FieldType.Long)
    private Long num;

    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    private String tid;

    @JSONField(name = "MASTER_ORDER_NO")
    @Field(type = FieldType.Keyword)
    private String masterOrderNo;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    private Date modifieddate;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal05;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "SELLER_FLAG")
    @Field(type = FieldType.Keyword)
    private String sellerFlag;

    @JSONField(name = "LOGISTICSCOMPANY")
    @Field(type = FieldType.Keyword)
    private String logisticscompany;

    @JSONField(name = "DELIVERNO")
    @Field(type = FieldType.Keyword)
    private String deliverno;

    @JSONField(name = "ABNORMAL_TYPE")
    @Field(type = FieldType.Keyword)
    private Integer abnormalType;

    @Field(type = FieldType.Keyword)
    @JSONField(name="GW_VIP_CODE")
    private String gwVipCode;

    @Field(type = FieldType.Keyword)
    @JSONField(name="GW_VIP_MOBILE")
    private String gwVipMobile;

    @Field(type = FieldType.Keyword)
    @JSONField(name="GW_SOURCE_CODE")
    private String gwSourceCode;

    @Field(type = FieldType.Keyword)
    @JSONField(name="GW_SOURCE_GROUP")
    private String gwSourceGroup;

    //驿客券码
    @JSONField(name = "USE_COUPON_NO")
    @Field(type = FieldType.Keyword)
    private String useCouponNo;

    @JSONField(name = "ORDER_SOURCE_PLATFORM_ECODE")
    @Field(type = FieldType.Keyword)
    private String orderSourcePlatformEcode;

    @JSONField(name = "SOURCE_BILL_NO")
    @Field(type = FieldType.Keyword)
    private String sourceBillNo;

    /**
     * 是否需要签收
     */
    @JSONField(name = "WHETHER_NEED_RECEIPT")
    @Field(type = FieldType.Keyword)
    private String whetherNeedReceipt;

    /**
     * 预计交货时间
     */
    @JSONField(name = "ESTIMATED_DELIVERY_TIME")
    @Field(type = FieldType.Long)
    private Date estimatedDeliveryTime;

    /**
     * 销售组织ID
     */
    @JSONField(name = "SALES_ORGANIZATION_ID")
    @Field(type = FieldType.Long)
    private Long salesOrganizationId;

    /**
     * 销售组织
     */
    @JSONField(name = "SALES_ORGANIZATION")
    @Field(type = FieldType.Keyword)
    private String salesOrganization;

    /**
     * 销售部门ID
     */
    @JSONField(name = "SALES_DEPARTMENT_ID")
    @Field(type = FieldType.Long)
    private Long salesDepartmentId;

    /**
     * 销售部门
     */
    @JSONField(name = "SALES_DEPARTMENT")
    @Field(type = FieldType.Keyword)
    private String salesDepartment;

    /**
     * 销售组编码
     */
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_GROUP_CODE")
    private String salesGroupCode;

    /**
     * 成本中心ID
     */
    @JSONField(name = "COST_CENTER_ID")
    @Field(type = FieldType.Long)
    private Long costCenterId;

    /**
     * 成本中心
     */
    @JSONField(name = "COST_CENTER")
    @Field(type = FieldType.Keyword)
    private String costCenter;

    @JSONField(name = "ORDER_PUSH_TYPE")
    @ApiModelProperty(name = "销售推送接口类型")
    private String orderPushType;

    @JSONField(name = "PLANTFOR_COUNT")
    @Field(type = FieldType.Double)
    private BigDecimal plantforCount;

    @JSONField(name = "OAID")
    private String oaid;

    @JSONField(name = "PRESALE_DEPOSIT_TIME")
    private Date presaleDepositTime;

    /**
     * 分销商id
     */
    @JSONField(name = "SELLER_ID")
    private String sellerId;

    /**
     * 分销商名称
     */
    @JSONField(name = "SELLER_VENDER_NAME")
    private String sellerVenderName;


    /**
     * 分销商店铺ID
     */
    @JSONField(name = "SELLER_SHOP_ID")
    private String sellerShopId;


    /**
     * 分销商店铺名称
     */
    @JSONField(name = "SELLER_SHOP_NAME")
    private String sellerShopName;


}
