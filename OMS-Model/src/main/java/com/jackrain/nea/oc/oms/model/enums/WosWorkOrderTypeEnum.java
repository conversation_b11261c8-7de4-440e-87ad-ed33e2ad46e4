package com.jackrain.nea.oc.oms.model.enums;

/**
 * <AUTHOR>
 * @create 2020-08-15 15:51
 * @desc WOS工单类型
 **/
public enum WosWorkOrderTypeEnum {
    /**
     * 修改信息
     */
    MODIFY_INFORMATION("modifymsg", "修改信息"),
    /**
     * 催派
     */
    URGE("urge", "催派"),
    /**
     * 截回/拒收
     */
    INTERCEPTION_REJECTION("cutreject", "截回/拒收"),
    /**
     * 其他
     */
    WORK_OTHER("workother", "其他");

    /**
     * code
     */
    private String code;
    /**
     * 名称
     */
    private String name;

    WosWorkOrderTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (WosWorkOrderTypeEnum select : WosWorkOrderTypeEnum.values()) {
            if (select.code.equals(code)) {
                return select.name;
            }
        }
        return "";
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
