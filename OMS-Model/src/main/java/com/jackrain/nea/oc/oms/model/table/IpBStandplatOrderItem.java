package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_standplat_order_item")
@Data
@Document(index = "ip_b_standplat_order_item", type = "ip_b_standplat_order_item")
public class IpBStandplatOrderItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "IP_B_STANDPLAT_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBStandplatOrderId;

    @JSONField(name = "CID")
    @Field(type = FieldType.Long)
    private Long cid;

    @JSONField(name = "REFUND_STATUS")
    @Field(type = FieldType.Keyword)
    private String refundStatus;

    @JSONField(name = "BUYER_MESSAGE")
    @Field(type = FieldType.Keyword)
    private String buyerMessage;

    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    @JSONField(name = "PIC_PATH")
    @Field(type = FieldType.Keyword)
    private String picPath;

    @JSONField(name = "SKU_PROPERTIES_NAME")
    @Field(type = FieldType.Keyword)
    private String skuPropertiesName;

    @JSONField(name = "ORDER_UPDATE_TIME")
    @Field(type = FieldType.Long)
    private Date orderUpdateTime;

    @JSONField(name = "ADJUST_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal adjustFee;

    @JSONField(name = "DISCOUNT_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal discountFee;

    @JSONField(name = "PAYMENT")
    @Field(type = FieldType.Double)
    private BigDecimal payment;

    @JSONField(name = "TOTAL_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal totalFee;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal price;

    @JSONField(name = "SKU_ID")
    @Field(type = FieldType.Keyword)
    private String skuId;

    @JSONField(name = "OUTER_IID")
    @Field(type = FieldType.Keyword)
    private String outerIid;

    @JSONField(name = "NUM")
    @Field(type = FieldType.Long)
    private Long num;

    @JSONField(name = "OUTER_SKU_ID")
    @Field(type = FieldType.Keyword)
    private String outerSkuId;

    @JSONField(name = "NUM_IID")
    @Field(type = FieldType.Keyword)
    private String numIid;

    @JSONField(name = "TITLE")
    @Field(type = FieldType.Keyword)
    private String title;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    private String status;

    @JSONField(name = "OID")
    @Field(type = FieldType.Keyword)
    private String oid;

    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    private String tid;

    @JSONField(name = "TEMPORARYID")
    @Field(type = FieldType.Keyword)
    private String temporaryid;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    private Date modifieddate;

    /**
     * 赠品属性
     */
    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Long)
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Long)
    private Long reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Long)
    private Long reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Long)
    private Long reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Long)
    private Long reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Long)
    private Long reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Long)
    private Long reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Long)
    private Long reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Long)
    private Long reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Long)
    private Long reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "GW_COUPON_CODE")
    private String gwCouponCode;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "CARD_CODE")
    private String cardCode;

    /**
     * 效期类型
     */
    @Field(type = FieldType.Integer)
    @JSONField(name = "EXPIRY_DATE_TYPE")
    private Integer expiryDateType;

    /**
     * 效期范围
     */
    @Field(type = FieldType.Keyword)
    @JSONField(name = "EXPIRY_DATE_RANGE")
    private String expiryDateRange;

    /**
     * 计划行类别
     */
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PLAN_LINE_CATEGORY")
    private String planLineCategory;

    /**
     * 贴标要求
     */
    @Field(type = FieldType.Keyword)
    @JSONField(name = "LABELING_REQUIREMENTS")
    private String labelingRequirements;

    /**
     * 工厂
     */
    @Field(type = FieldType.Keyword)
    @JSONField(name = "FACTORY")
    private String factory;


    @JSONField(name = "LGORT")
    @ApiModelProperty(name = "库存地点")
    private String warehouse;

    @JSONField(name = "IS_PRESENT")
    @ApiModelProperty(name = "是否平台赠品")
    private String isPresent;

    /**
     * 周期购提数
     */
    @JSONField(name = "CYCLE_QTY")
    @Field(type = FieldType.Long)
    private Long cycleQty;

}