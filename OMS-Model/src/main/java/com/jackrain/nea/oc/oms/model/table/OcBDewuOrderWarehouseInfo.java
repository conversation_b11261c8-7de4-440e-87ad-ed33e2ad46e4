package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @ClassName OcBDewuOrderWarehouseInfo
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/9 14:26
 * @Version 1.0
 */
@Data
@TableName(value = "oc_b_dewu_order_warehouse_info")
public class OcBDewuOrderWarehouseInfo extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "TID")
    private String tid;

    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @JSONField(name = "ADDRESS_ID")
    private String addressId;

    @JSONField(name = "WAREHOUSE_CODE")
    private String warehouseCode;
}
