package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName OcBPreOrderItem
 * @Description 订单预导入子表
 * <AUTHOR>
 * @Date 2022/10/11 09:31
 * @Version 1.0
 */
@TableName(value = "oc_b_pre_order_item")
@Data
@Document(index = "oc_b_pre_order_item", type = "oc_b_pre_order_item")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_pre_order_item", description = "订单预导入子表")
public class OcBPreOrderItem extends BaseModel {

    @ApiModelProperty(value = "ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @ApiModelProperty(value = "预导入订单id")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_PRE_ORDER_ID")
    private Long ocBPreOrderId;

    @ApiModelProperty(value = "平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "成交单价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_ACTUAL")
    private BigDecimal priceActual;

    @ApiModelProperty(value = "流水号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SERIAL_NUMBER")
    private String serialNumber;

    @ApiModelProperty(value = "商品数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY")
    private BigDecimal qty;

    @ApiModelProperty(value = "是否是赠品")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_GIFT")
    private Integer isGift;

    @ApiModelProperty(value = "平台售价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PLATFORM_PRICE")
    private BigDecimal platformPrice;


}
