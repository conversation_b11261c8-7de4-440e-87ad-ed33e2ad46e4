package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ip_b_vip_return_order_item")
@Data
@Document(index = "ip_b_vip_return_order_item", type = "ip_b_vip_return_order_item")
public class IpBVipReturnOrderItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "IP_B_VIP_RETURN_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBVipReturnOrderId;

    @JSONField(name = "RETURN_SN")
    @Field(type = FieldType.Keyword)
    private String returnSn;

    @JSONField(name = "BARCODE")
    @Field(type = FieldType.Keyword)
    private String barcode;

    @JSONField(name = "PRODUCT_NAME")
    @Field(type = FieldType.Keyword)
    private String productName;

    @JSONField(name = "GRADE")
    @Field(type = FieldType.Keyword)
    private String grade;

    @JSONField(name = "ORDER_SN")
    @Field(type = FieldType.Keyword)
    private String orderSn;

    @JSONField(name = "PO_NO")
    @Field(type = FieldType.Keyword)
    private String poNo;

    @JSONField(name = "QTY")
    @Field(type = FieldType.Double)
    private BigDecimal qty;

    @JSONField(name = "BOX_NO")
    @Field(type = FieldType.Keyword)
    private String boxNo;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    private Long version;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}