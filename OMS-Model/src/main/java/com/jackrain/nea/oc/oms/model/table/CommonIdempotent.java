package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "common_idempotent")
@Data
@Document(index = "common_idempotent", type = "common_idempotent")
public class CommonIdempotent extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    /**
     * 业务编码
     */
    @JSONField(name = "BUSINESS_CODE")
    @Field(type = FieldType.Keyword)
    private String businessCode;

    /**
     * 批次，默认0
     */
    @JSONField(name = "BATCH_NO")
    @Field(type = FieldType.Keyword)
    private String batchNo;

    /**
     * 类型
     * {@link com.jackrain.nea.oc.oms.model.enums.CommonIdempotentTypeEnum}
     */
    @JSONField(name = "TYPE")
    @Field(type = FieldType.Keyword)
    private String type;

}