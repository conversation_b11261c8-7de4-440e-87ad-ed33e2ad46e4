package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_taobao_jx_order")
@Data
@Document(index = "ip_b_taobao_jx_order", type = "ip_b_taobao_jx_order")
public class IpBTaobaoJxOrder extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ALIPAY_NO")
    @Field(type = FieldType.Keyword)
    private String alipayNo;

    @JSONField(name = "APPLIED_TIME")
    @Field(type = FieldType.Long)
    private Date appliedTime;

    @JSONField(name = "APPLIER_NICK")
    @Field(type = FieldType.Keyword)
    private String applierNick;

    @JSONField(name = "AUDIT_TIME_SUPPLIER")
    @Field(type = FieldType.Long)
    private Date auditTimeSupplier;

    @JSONField(name = "DEALER_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long dealerOrderId;

    @JSONField(name = "DELIVERED_QUANTITY_COUNT")
    @Field(type = FieldType.Long)
    private Long deliveredQuantityCount;

    @JSONField(name = "LOGISTICS_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal logisticsFee;

    @JSONField(name = "LOGISTICS_TYPE")
    @Field(type = FieldType.Keyword)
    private String logisticsType;

    @JSONField(name = "MODIFIED_TIME")
    @Field(type = FieldType.Long)
    private Date modifiedTime;

    @JSONField(name = "ORDER_STATUS")
    @Field(type = FieldType.Keyword)
    private String orderStatus;

    @JSONField(name = "PAY_TIME")
    @Field(type = FieldType.Long)
    private Date payTime;

    @JSONField(name = "PAY_TYPE")
    @Field(type = FieldType.Keyword)
    private String payType;

    @JSONField(name = "QUANTITY_COUNT")
    @Field(type = FieldType.Long)
    private Long quantityCount;

    @JSONField(name = "ADDRESS")
    @Field(type = FieldType.Keyword)
    private String address;

    @JSONField(name = "CITY")
    @Field(type = FieldType.Keyword)
    private String city;

    @JSONField(name = "DISTRICT")
    @Field(type = FieldType.Keyword)
    private String district;

    @JSONField(name = "MOBILE_PHONE")
    @Field(type = FieldType.Keyword)
    private String mobilePhone;

    @JSONField(name = "NAME")
    @Field(type = FieldType.Keyword)
    private String name;

    @JSONField(name = "STATE")
    @Field(type = FieldType.Keyword)
    private String state;

    @JSONField(name = "ZIP")
    @Field(type = FieldType.Keyword)
    private String zip;

    @JSONField(name = "SUPPLIER_NICK")
    @Field(type = FieldType.Keyword)
    private String supplierNick;

    @JSONField(name = "TOTAL_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal totalPrice;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertdate;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "SUPPLIER_MEMO")
    @Field(type = FieldType.Keyword)
    private String supplierMemo;

    @JSONField(name = "JDP_MODIFIED")
    @Field(type = FieldType.Long)
    private Date jdpModified;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Long)
    private Long reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Long)
    private Long reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Long)
    private Long reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Long)
    private Long reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Long)
    private Long reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Long)
    private Long reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Long)
    private Long reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Long)
    private Long reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Long)
    private Long reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Long)
    private Long reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;

    @JSONField(name = "SUPPLIER_MEMO_FLAG")
    @Field(type = FieldType.Keyword)
    private String supplierMemoFlag;

    @JSONField(name = "REBATE_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal rebateFee;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;
}