package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_jingdong_sa_refund")
@Data
@Document(index = "ip_b_jingdong_sa_refund", type = "ip_b_jingdong_sa_refund")
public class IpBJingdongSaRefund extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "POPAFSREFUNDAPPLYID")
    @Field(type = FieldType.Long)
    private Long popafsrefundapplyid;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    @JSONField(name = "ORDERID")
    @Field(type = FieldType.Keyword)
    private String orderid;

    @JSONField(name = "BUYERID")
    @Field(type = FieldType.Keyword)
    private String buyerid;

    @JSONField(name = "BUYERNAME")
    @Field(type = FieldType.Keyword)
    private String buyername;

    @JSONField(name = "CHECKTIME")
    @Field(type = FieldType.Long)
    private Date checktime;

    @JSONField(name = "APPLYTIME")
    @Field(type = FieldType.Long)
    private Date applytime;

    @JSONField(name = "APPLYREFUNDSUM")
    @Field(type = FieldType.Double)
    private BigDecimal applyrefundsum;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    private Integer status;

    @JSONField(name = "CHECKUSERNAME")
    @Field(type = FieldType.Keyword)
    private String checkusername;

    @JSONField(name = "CHECKREMARK")
    @Field(type = FieldType.Keyword)
    private String checkremark;

    @JSONField(name = "REASON")
    @Field(type = FieldType.Keyword)
    private String reason;

    @JSONField(name = "SYSTEMID")
    @Field(type = FieldType.Integer)
    private Integer systemid;

    @JSONField(name = "STOREID")
    @Field(type = FieldType.Integer)
    private Integer storeid;

    @JSONField(name = "OPEN_ID_BUYER")
    @Field(type = FieldType.Keyword)
    private String openIdBuyer;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertdate;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "MARK_REFUND_COUNT")
    @Field(type = FieldType.Integer)
    private Integer markRefundCount;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "RETURNSTATUS")
    @Field(type = FieldType.Integer)
    private Integer returnstatus;

    @JSONField(name = "OUTBOUNDVALUE")
    @Field(type = FieldType.Integer)
    private Integer outboundvalue;


    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;


    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal05;


    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;
}