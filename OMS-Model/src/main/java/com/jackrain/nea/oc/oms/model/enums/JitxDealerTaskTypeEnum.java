package com.jackrain.nea.oc.oms.model.enums;


import java.util.Objects;

/**
 * description：jitx经销商任务类型
 *
 * <AUTHOR>
 * @date 2021/12/21
 */
public enum JitxDealerTaskTypeEnum {


    YY_OCCUPY("YY占用", 1),
    YY_CANCEL_OCCUPY("YY取消占用", 2),
    YY_CHANGE_WAREHOUSE("YY换仓结果反馈", 3),
    YY_BILLS_UPDATE("YY单据更新", 4);

    int code;
    String text;

    JitxDealerTaskTypeEnum(String text, int code) {
        this.text = text;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    public static String getTextByCode(String v) {
        for (JitxDealerTaskTypeEnum c : JitxDealerTaskTypeEnum.values()) {
            if (Objects.equals(v, c.getCode())) {
                return c.getText();
            }
        }
        return "";
    }

}


