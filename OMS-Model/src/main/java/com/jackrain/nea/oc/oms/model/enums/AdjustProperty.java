package com.jackrain.nea.oc.oms.model.enums;

/**
 * <AUTHOR> 孙俊磊
 * @since : 2019-06-20
 * create at : 2019-06-20 10:02 AM
 * 调整性质表  生成调整单时候用
 */
public enum AdjustProperty {

    /*
     * id：1  encode：1 enname：无头件入库
     * id：2  encode：2 enname：冲无头件、
     * id：3  encode：3 enname：错发调整
     * id：4  encode：4 enname：错发匹配
     * id：5  encode：5 enname：销退错录
     * id：6  encode：6 enname：分销错录
     * id：7  encode：7 enname：唯品会退货
     * id：8  encode：8 enname：损益调整
     */
    /**
     * 无头件入库
     */
    HEADLESS_STORAGE,
    /**
     * 冲无头件
     */
    HEADLESS_TWO,
    /**
     * 错发调整
     */
    WRONG_ADJUST;

    public Long toLong() {
        if (this == HEADLESS_STORAGE) {
            return 1L;
        } else if (this == HEADLESS_TWO) {
            return 2L;
        } else {
            return 3L;
        }
    }
}

