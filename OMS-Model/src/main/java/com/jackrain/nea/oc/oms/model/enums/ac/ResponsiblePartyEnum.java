package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * description：丢件单责任方
 *
 * <AUTHOR>
 * @date 2021/5/24
 */
@Getter
public enum ResponsiblePartyEnum {
    /**
     * 1TP，2 快递仓储，3客户，4斯凯奇，5其他
     */
    TP(1 ,"TP"),

    EXPRESS_WAREHOU(2 ,"快递仓储"),

    CUSTOMER(3, "客户"),

    SKECHERS(4, "斯凯奇"),

    OTHER(5, "其他");

    String text;
    int val;

    ResponsiblePartyEnum(int val, String text) {
        this.text = text;
        this.val = val;
    }

    public String getText() {
        return text;
    }

    public int getVal() {
        return val;
    }

    public static String getTextByVal(int v) {
        for (ResponsiblePartyEnum e : ResponsiblePartyEnum.values()) {
            if (e.getVal()==v) {
                return e.getText();
            }
        }
        return "";
    }
}

