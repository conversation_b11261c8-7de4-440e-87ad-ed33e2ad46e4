package com.jackrain.nea.oc.oms.model.enums;


/**
 * 是否启用
 *
 * @author: ming.fz
 * create at: 2019/7/23
 */
public enum IsActiveEnum {


    /**
     * 是否可用
     */
    Y("Y", 1),
    N("N", 0);

    String key;
    Integer val;

    IsActiveEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }


}


