package com.jackrain.nea.oc.oms.model.dto;

import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategy;
import com.jackrain.nea.oc.oms.model.table.StCDropshipBasePriceStrategyDetail;
import lombok.Data;

import java.util.List;

/**
 * 一件代发客户基价策略DTO
 *
 * <AUTHOR>
 */
@Data
public class StCDropshipBasePriceStrategyDTO extends StCDropshipBasePriceStrategy {

    /**
     * 策略明细列表
     */
    private List<StCDropshipBasePriceStrategyDetail> detailList;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺编码
     */
    private String shopCode;

}
