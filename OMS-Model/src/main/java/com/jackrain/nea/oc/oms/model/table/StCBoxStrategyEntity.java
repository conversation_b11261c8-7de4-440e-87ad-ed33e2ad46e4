package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 箱型策略(StCBoxStrategy)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-31
 */
@Data
@TableName("st_c_box_strategy")
public class StCBoxStrategyEntity implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 策略编码
     */
    @JSONField(name = "STRATEGY_CODE")
    private String strategyCode;

    /**
     * 订单商品
     */
    @JSONField(name = "ORDER_GOODS")
    private String orderGoods;

    /**
     * 拆单规则
     */
    @JSONField(name = "SPLIT_ORDER_RULES")
    private String splitOrderRules;

    /**
     * 箱型
     */
    @JSONField(name = "BOX_NAME")
    @TableField(value = "box_name", strategy = FieldStrategy.NOT_NULL)
    private String boxName;

    /**
     * 所属公司
     */
    private Long adClientId;

    /**
     * 所属组织
     */
    private Long adOrgId;

    /**
     * 是否启用（Y:启用，N：未启用）
     */
    @JSONField(name = "ISACTIVE")
    private String isactive;

    /**
     * 创建人
     */
    @JSONField(name = "OWNERID")
    private Long ownerid;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    /**
     * 创建人名称
     */
    @JSONField(name = "OWNERNAME")
    private String ownername;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    /**
     * 修改人
     */
    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    /**
     * 修改人名称
     */
    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;
}

