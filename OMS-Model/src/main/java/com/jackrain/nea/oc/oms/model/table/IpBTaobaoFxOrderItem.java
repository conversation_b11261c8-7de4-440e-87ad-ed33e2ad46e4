package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_taobao_fx_order_item")
@Data
@Document(index = "ip_b_taobao_fx_order_item", type = "ip_b_taobao_fx_order_item")
@ApiModel
public class IpBTaobaoFxOrderItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ID")
    private Long id;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "STATUS")
    private String status;

    @JSONField(name = "REFUND_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "REFUND_FEE")
    private BigDecimal refundFee;

    @JSONField(name = "SID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "SID")
    private Long sid;

    @JSONField(name = "FENXIAO_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "FENXIAO_ID")
    private Long fenxiaoId;

    @JSONField(name = "SKU_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "SKU_ID")
    private Long skuId;

    @JSONField(name = "TC_ORDER_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "TC_ORDER_ID")
    private Long tcOrderId;

    @JSONField(name = "ITEM_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ITEM_ID")
    private Long itemId;

    @JSONField(name = "ORDER_200_STATUS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ORDER_200_STATUS")
    private String order200Status;

    @JSONField(name = "AUCTION_PRICE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "AUCTION_PRICE")
    private BigDecimal auctionPrice;

    @JSONField(name = "NUM")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "NUM")
    private Long num;

    @JSONField(name = "TITLE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TITLE")
    private String title;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "PRICE")
    private BigDecimal price;

    @JSONField(name = "TOTAL_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "TOTAL_FEE")
    private BigDecimal totalFee;

    @JSONField(name = "DISTRIBUTOR_PAYMENT")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "DISTRIBUTOR_PAYMENT")
    private BigDecimal distributorPayment;

    @JSONField(name = "BUYER_PAYMENT")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "BUYER_PAYMENT")
    private BigDecimal buyerPayment;

    @JSONField(name = "BILL_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "BILL_FEE")
    private BigDecimal billFee;

    @JSONField(name = "SC_ITEM_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "SC_ITEM_ID")
    private Long scItemId;

    @JSONField(name = "OLD_SKU_PROPERTIES")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OLD_SKU_PROPERTIES")
    private String oldSkuProperties;

    @JSONField(name = "ITEM_OUTER_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ITEM_OUTER_ID")
    private String itemOuterId;

    @JSONField(name = "SKU_OUTER_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SKU_OUTER_ID")
    private String skuOuterId;

    @JSONField(name = "SKU_PROPERTIES")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SKU_PROPERTIES")
    private String skuProperties;

    @JSONField(name = "SNAPSHOT_URL")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SNAPSHOT_URL")
    private String snapshotUrl;

    @JSONField(name = "CREATED")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CREATED")
    private Date created;

    @JSONField(name = "IP_B_TAOBAO_FX_ORDER_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "IP_B_TAOBAO_FX_ORDER_ID")
    private Long ipBTaobaoFxOrderId;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "ISTRANS")
    private Integer istrans;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "TRANSDATE")
    private Date transdate;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "INSERTDATE")
    private Date insertdate;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SYSREMARK")
    private String sysremark;

    @JSONField(name = "QUANTITY")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "QUANTITY")
    private Long quantity;

    @JSONField(name = "ISNEW")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "ISNEW")
    private Integer isnew;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT06")
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT07")
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT08")
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT09")
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_BIGINT10")
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL01")
    private Long reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL02")
    private Long reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL03")
    private Long reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL04")
    private Long reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL05")
    private Long reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL06")
    private Long reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL07")
    private Long reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL08")
    private Long reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL09")
    private Long reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "RESERVE_DECIMAL10")
    private Long reserveDecimal10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR06")
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR07")
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR08")
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR09")
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RESERVE_VARCHAR10")
    private String reserveVarchar10;

    @JSONField(name = "TC_DISCOUNT_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "TC_DISCOUNT_FEE")
    private BigDecimal tcDiscountFee;

    @JSONField(name = "TC_ADJUST_FEE")
    @Field(type = FieldType.Double)
    @ApiModelProperty(name = "TC_ADJUST_FEE")
    private BigDecimal tcAdjustFee;
}