package com.jackrain.nea.oc.oms.model.enums;


import java.util.Objects;

/**
 * description：jitx经销商任务状态
 *
 * <AUTHOR>
 * @date 2021/12/21
 */
public enum JitxDealerTaskStatusEnum {

    NOT("未处理", 0),
    SUCCESS("成功", 1),
    FAIL("失败", 2);

    int code;
    String text;

    JitxDealerTaskStatusEnum(String text, int code) {
        this.text = text;
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

    public String getText() {
        return text;
    }

    public static String getTextByCode(String v) {
        for (JitxDealerTaskStatusEnum c : JitxDealerTaskStatusEnum.values()) {
            if (Objects.equals(v, c.getCode())) {
                return c.getText();
            }
        }
        return "";
    }

}


