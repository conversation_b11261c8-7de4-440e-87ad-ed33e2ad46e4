package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName UnFreezeEnum
 * @Description 解冻状态
 * <AUTHOR>
 * @Date 2022/6/30 14:24
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum UnFreezeEnum {

    UN_FREEZE(0, "待解冻"),
    UN_FREEZE_SUCCESS(1, "解冻成功"),
    UN_FREEZE_FAIL(2, "解冻失败"),
    ;

    private Integer status;

    private String desc;


}
