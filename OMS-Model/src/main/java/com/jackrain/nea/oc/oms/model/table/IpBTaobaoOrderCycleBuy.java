package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "ip_b_taobao_order_cycle_buy")
@Data
@Document(index = "ip_b_taobao_order", type = "ip_b_taobao_order_cycle_buy")
public class IpBTaobaoOrderCycleBuy extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "IP_B_TAOBAO_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBTaobaoOrderId;

    @JSONField(name = "PLAN_ID")
    @Field(type = FieldType.Keyword)
    private String planId;

    @JSONField(name = "ORDER_ID")
    @Field(type = FieldType.Keyword)
    private String orderId;

    @JSONField(name = "CURR_PHASE")
    @Field(type = FieldType.Integer)
    private Long currPhase;

    @JSONField(name = "OUT_LOGISTICS_ID")
    @Field(type = FieldType.Keyword)
    private String outLogisticsId;

    @JSONField(name = "PREPARE_TIME_BEGIN")
    @Field(type = FieldType.Long)
    private Date prepareTimeBegin;

    @JSONField(name = "SHIP_TIME_BEGIN")
    @Field(type = FieldType.Long)
    private Date shipTimeBegin;

    @JSONField(name = "ACTUAL_SHIP_TIME")
    @Field(type = FieldType.Long)
    private Date actualShipTime;

    @JSONField(name = "GOODS_NUM")
    @Field(type = FieldType.Integer)
    private Long goodsNum;

    @JSONField(name = "PLAN_STATUS")
    @Field(type = FieldType.Integer)
    private Long planStatus;

    @JSONField(name = "PLAN_REFUND_STATUS")
    @Field(type = FieldType.Integer)
    private Long planRefundStatus;

    @JSONField(name = "RECEIVER_NAME")
    @Field(type = FieldType.Keyword)
    private String receiverName;

    @JSONField(name = "RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String receiverMobile;

    @JSONField(name = "RECEIVER_PHONE")
    @Field(type = FieldType.Keyword)
    private String receiverPhone;

    @JSONField(name = "RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;

    @JSONField(name = "RECEIVER_TOWN")
    @Field(type = FieldType.Keyword)
    private String receiverTown;

    @JSONField(name = "RECEIVER_DISTRICT")
    @Field(type = FieldType.Keyword)
    private String receiverDistrict;

    @JSONField(name = "RECEIVER_CITY")
    @Field(type = FieldType.Keyword)
    private String receiverCity;

    @JSONField(name = "RECEIVER_STATE")
    @Field(type = FieldType.Keyword)
    private String receiverState;

    @JSONField(name = "RECEIVER_COUNTRY")
    @Field(type = FieldType.Keyword)
    private String receiverCountry;

    @JSONField(name = "OAID")
    @Field(type = FieldType.Keyword)
    private String oaid;

    @JSONField(name = "PRIVACY_NUM")
    @Field(type = FieldType.Keyword)
    private String privacyNum;

    @JSONField(name = "PRIVACY_EXPIRE_TIME")
    @Field(type = FieldType.Long)
    private String privacyExpireTime;
}