package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

/**
 * B2C退货回传中间表
 */
@TableName(value = "oc_b_refund_in_task")
@Data
public class OcBRefundInTask extends BaseModel {

    private static final long serialVersionUID = -3302642224118055879L;
    @JSONField(name = "ID")
    private Long id;

    /**
     * 报文
     */
    @JSONField(name = "MSG")
    private String msg;

    /**
     * 退单单据编号
     */
    @JSONField(name = "RETURN_BILL_NO")
    private String returnBillNo;

    /**
     * wmsBillNo
     */
    @JSONField(name = "WMS_BILL_NO")
    private String wmsBillNo;

    /**
     * 退货入库编号
     */
    @JSONField(name = "REFUND_IN_Id")
    private Long refundInId;

    /**
     * 转化状态
     */
    @JSONField(name = "BILL_STATUS")
    private Integer billStatus;


    /**
     * 转化失败次数
     */
    @JSONField(name = "FAILED_COUNT")
    private Integer failedCount;

    /**
     * 失败原因
     */
    @JSONField(name = "FAILED_REASON")
    private String failedReason;

    /**
     * 单据类型
     */
    @JSONField(name = "ORDER_TYPE")
    private String orderType;

    /**
     * 仓库编码
     */
    @JSONField(name = "WAREHOUSE_CODE")
    private String warehouseCode;

    /**
     * 仓库类型
     */
    @JSONField(name = "WMS_WAREHOUSE_TYPE")
    private String wmsWarehouseType;

    /**
     * 转换时间时间
     */
    @JSONField(name = "TRANSFORMATION_DATA")
    private Date transformationData;

    /**
     * 外部业务编码
     */
    @JSONField(name = "OUT_BIZ_CODE")
    private String outBizCode;

}