package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Des:订单中间表锁单状态
 * @author: 洪艺安
 * @since: 2019-10-9
 * create at : 2019-01-21 14:46
 */
public enum OcOrderLockStatusEnum {

    WAIT_LOCK(0, "待锁单"),
    LOCKED(1, "已锁单"),
    LOCK_FAIL(2, "锁单失败"),
    UNLOCK(3, "已解锁");


    OcOrderLockStatusEnum(long key, String name) {
        this.key = key;
        this.name = name;
    }

    public static String toName(Long key) {
        if (key == 0L) {
            return "待锁单";
        } else if (key == 1L) {
            return "已锁单";
        } else if (key == 2L) {
            return "锁单失败";
        } else if (key == 3L) {
            return "已解锁";
        } else {
            return "待锁单";
        }
    }

    /**
     * 转化
     *
     * @return map
     */
    public static Map<Long, String> convertAllToHashVal() {
        Map<Long, String> m = new HashMap<>();
        for (OcOrderLockStatusEnum o : OcOrderLockStatusEnum.values()) {
            m.put(o.getKey(), o.getName());
        }
        m.put(null, "");
        return m;
    }

    @Getter
    private long key;

    @Getter
    private String name;
}
