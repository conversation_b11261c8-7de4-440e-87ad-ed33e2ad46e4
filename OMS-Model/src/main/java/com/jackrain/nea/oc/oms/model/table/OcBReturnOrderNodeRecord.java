package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * className: OcBReturnOrderNodeRecord
 * description: 退换货单节点触发时间记录表
 *
 * <AUTHOR>
 * create: 2021-12-08
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_return_order_node_record", type = "oc_b_return_order_node_record")
@ApiModel(value = "oc_b_return_order_node_record", description = "退换货单节点触发时间记录")
public class OcBReturnOrderNodeRecord extends BaseModel {

    private static final long serialVersionUID = -8758966211937477423L;

    @ApiModelProperty(value = "主键编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REMARK")
    private String remark;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @ApiModelProperty(value = "节点")
    @Field(type = FieldType.Integer)
    @JSONField(name = "NODE")
    private Integer node;

    @ApiModelProperty(value = "节点触发时间")
    @Field(type = FieldType.Date)
    @JSONField(name = "NODE_OPERATE_TIME")
    private Date nodeOperateTime;

    @ApiModelProperty(value = "退换货单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_RETURN_ORDER_ID")
    private Long ocBReturnOrderId;

}
