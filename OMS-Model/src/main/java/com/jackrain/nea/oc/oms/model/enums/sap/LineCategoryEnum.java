package com.jackrain.nea.oc.oms.model.enums.sap;

import com.jackrain.nea.oc.oms.model.enums.OrderBusinessTypeCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR> wang<PERSON><PERSON>
 * @since : 2022/9/11
 * description : sap 计划行类别枚举
 */
@Getter
@AllArgsConstructor
public enum LineCategoryEnum {
    /**
     * 捐赠
     */
    DONATION("Z1","捐赠","Y1","捐赠退货"),
    /**
     * 福利
     */
    BENEFITS("Z2","福利","Y2","福利退货"),
    /**
     * 样品
     */
    SAMPLE("Z3","样品","Y3","样品退货"),

    /**
     * 宣传
     */
    PUBLICITY("Z4","宣传","Y4","宣传退货"),

    /**
     * 赠品
     */
    GIFT("Z5","赠品","Y5","赠品退货"),

    /**
     * 招待
     */
    ENTERTAINMENT("Z6","招待","Y6","招待退货"),

    /**
     * 物耗
     */
    MATERIAL_CONSUMPTION("Z7","物耗","DN","物耗退货"),

    /**
     * 免费补货
     */
    FREE_REPLENISHMENT("CN","免费补货","DN","物耗退货"),

    /**
     * 默认值
     */
    NONE(null,"默认值",null,"默认值");

    private final String saleCode;

    private final String saleDesc;

    private final String returnCode;

    private final String returnDesc;

    public static LineCategoryEnum getLineCategoryEnumBySaleCode(String saleCode) {
        for (LineCategoryEnum value : LineCategoryEnum.values()) {
            if(Objects.equals(value.getSaleCode(), saleCode)){
                return value;
            }
        }
        return NONE;
    }
}
