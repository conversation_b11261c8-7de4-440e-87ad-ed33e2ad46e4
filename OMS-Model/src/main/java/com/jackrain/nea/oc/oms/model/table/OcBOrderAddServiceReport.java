package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName OcBOrderAddService
 * @Description TODO
 * <AUTHOR>
 * @Date 2023/10/25 15:42
 * @Version 1.0
 */
@TableName(value = "oc_b_order_addservice_report")
@Data
@Document(index = "oc_b_order_addservice_report", type = "oc_b_order_addservice_report")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "oc_b_order_addservice_report", description = "零售发货单增值服务")
public class OcBOrderAddServiceReport extends BaseModel {
    private static final long serialVersionUID = 8839148236872396335L;

    @ApiModelProperty(value = "编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "单据编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORDER_BILL_NO")
    private String orderBillNo;

    @ApiModelProperty(value = "零售发货单ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "初始平台单号（确定唯一）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "是否匹配")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_MATCH")
    private Integer isMatch;

    @ApiModelProperty(value = "出库通知单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STO_OUT_BILL_NO")
    private String stoOutBillNo;

    @ApiModelProperty(value = "条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @ApiModelProperty(value = "商品货号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @ApiModelProperty(value = "商品名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @ApiModelProperty(value = "下单店铺id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @ApiModelProperty(value = "店铺编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @ApiModelProperty(value = "下单店铺标题")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @ApiModelProperty(value = "发货实体仓")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @ApiModelProperty(value = "实体仓编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    private String cpCPhyWarehouseEcode;

    @ApiModelProperty(value = "实体仓名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    private String cpCPhyWarehouseEname;

    @ApiModelProperty(value = "成本中心")
    @Field(type = FieldType.Long)
    @JSONField(name = "COST_CENTER_ID")
    private Long costCenterId;

    @ApiModelProperty(value = "成本中心")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "COST_CENTER_CODE")
    private String costCenterCode;


    @ApiModelProperty(value = "成本中心")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "COST_CENTER")
    private String costCenterName;

    @ApiModelProperty(value = "增值服务单价")
    @Field(type = FieldType.Double)
    @JSONField(name = "ADDSERVICE_STRATEGY_UNIT_PRICE")
    private BigDecimal addserviceStrategyUnitPrice;

    @ApiModelProperty(value = "增值服务总费用")
    @Field(type = FieldType.Double)
    @JSONField(name = "ADDSERVICE_STRATEGY_PRICE")
    private BigDecimal addserviceStrategyPrice;

    @ApiModelProperty(value = "发货时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "DELIVERY_TIME")
    private Date deliveryTime;

    /**
     * 增值服务
     */
    @ApiModelProperty(value = "增值服务")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "LABELING_REQUIREMENTS")
    private String labelingRequirements;

    @ApiModelProperty(value = "实发数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "REAL_OUT_NUM")
    private BigDecimal realOutNum;

    @ApiModelProperty(value = "贴标数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "LABEL_NUM")
    private BigDecimal labelNum;

    @ApiModelProperty(value = "数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY")
    private BigDecimal qty;

    @ApiModelProperty(value = "是否归档")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_ARCHIVED")
    private Integer isArchived;

    @ApiModelProperty(value = "备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REMARK")
    private String remark;
}
