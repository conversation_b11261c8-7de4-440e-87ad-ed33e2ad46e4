package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * toc残次策略明细识别规则
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TocCcAppointRuleEnum {
    PLATFORM_SKU_ID(1, "平台商品ID"),
    SKU_CODE_AND_TITLE(2, "SKU编码+商品标题"),
    SKU_TITLE(3, "商品标题");

    Integer val;
    String description;

    public static String getDescriptionByVal(Integer val) {
        if (val == null) {
            return null;
        }
        for (TocCcAppointRuleEnum o : TocCcAppointRuleEnum.values()) {
            if (o.getVal().equals(val)) {
                return o.getDescription();
            }
        }
        return "";
    }

}
