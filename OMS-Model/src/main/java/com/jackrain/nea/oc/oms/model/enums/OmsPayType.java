package com.jackrain.nea.oc.oms.model.enums;

/**
 * 支付方式
 *
 * @author: 易邵峰
 * @since: 2019-03-07
 * create at : 2019-03-07 22:00
 */
public enum OmsPayType {

    /**
     * 在线支付
     */
    ON_LINE_PAY,

    /**
     * 货到付款
     */
    CASH_ON_DELIVERY;

    /**
     * 转换成数字内容
     *
     * @return 数字内容
     */
    public int toInteger() {
        if (this == ON_LINE_PAY) {
            return 1;
        } else if (this == CASH_ON_DELIVERY) {
            return 2;
        } else {
            return 0;
        }
    }

}
