package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description oc_b_refund_package_task 退货包裹状态中间表
 * <AUTHOR>
 * @date 2022-07-20
 */
@TableName(value = "oc_b_refund_package_task")
@Data
public class OcBRefundPackageTask extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;
    /**
    * wms单据编号
    */
    @JSONField(name = "WMS_BILL_CODE")
    private String wmsBillCode;

    /**
    * wms仓库编码
    */
    @JSONField(name = "WAREHOUSE_CODE")
    private String warehouseCode;

    /**
    * 回传报文
    */
    @JSONField(name = "MESSAGE")
    private String message;

    /**
    * 转化状态
    */
    @JSONField(name = "TRANSFORM_STATUS")
    private Integer transformStatus;

    /**
    * 转换时间
    */
    @JSONField(name = "TRANSFORMATION_DATA")
    private Date transformationData;

    /**
    * 外部业务编码
    */
    @JSONField(name = "OUT_BIZ_CODE")
    private String outBizCode;

    /**
    * 失败次数
    */
    @JSONField(name = "FAILED_COUNT")
    private Integer failedCount;

    /**
    * 失败原因
    */
    @JSONField(name = "FAILED_REASON")
    private String failedReason;

    /**
    * qmwms = 奇门，jdwms = 京东
    */
    @JSONField(name = "WMS_WAREHOUSE_TYPE")
    private String wmsWarehouseType;
}