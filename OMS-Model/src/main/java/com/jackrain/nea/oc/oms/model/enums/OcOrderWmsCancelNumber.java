package com.jackrain.nea.oc.oms.model.enums;


/**
 * 订单撤回失败次数
 *
 * @author: <PERSON>
 * @since: 2020/11/9
 * create at : 2020/11/9 11:36
 */
public enum OcOrderWmsCancelNumber {

    /**
     * 订单的次数超过五次
     */
    OVER,
    /**
     * 订单的次数小于五次
     */
    UNOVER;



    public int toInteger() {
        if (this == OVER) {
            return 6;
        } else {
            return 5;
        }

    }

    }
