package com.jackrain.nea.oc.oms.model.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 可合并拆单分类(1.部分发货拆单 2.虚拟拆单 3.缺货拆单 4.按SKU拆单
 * 5.按SPU拆单 6.按品牌组拆 7.按性别拆 8.手工拆单 9.O2O拆单,10.非指定商品拆)
 *
 * @Auther: 黄志优
 * @Date: 2020/11/10 10:56
 * @Description:
 */
public enum OmsMergeSplitOrderType {

    DEFAULT_00("默认类型", 0),
    DELIVERY_OF_PART("部分发货拆单", 1),
    VIRTUAL("虚拟拆单", 2),
    STOCK_OUT("缺货拆单", 3),
    SKU("按SKU拆单", 4),
    SPU("按SPU拆单", 5),
    BRAND("按品牌组拆", 6),
    GENDER("按性别拆", 7),
    MANUAL("手工拆单", 8),
    O2O("O2O拆单", 9),
    GOODS_NOT_SPECIFIED("非指定商品拆", 10);

    /**
     * 描述
     */
    String key;

    /**
     * 策略页面值
     */
    int val;

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }

    OmsMergeSplitOrderType(String k, int v) {
        this.key = k;
        this.val = v;
    }

    ///**
    // * 拆单类型可以合并(0,1,2,3,8,10)
    // * @return
    // */
    //public static List<Integer> getUsedType(){
    //
    //    List<Integer> list = new ArrayList<>();
    //    list.add(OmsMergeSplitOrderType.DEFAULT_00.val);
    //    list.add(OmsMergeSplitOrderType.DELIVERY_OF_PART.val);
    //    list.add(OmsMergeSplitOrderType.VIRTUAL.val);
    //    list.add(OmsMergeSplitOrderType.STOCK_OUT.val);
    //    list.add(OmsMergeSplitOrderType.MANUAL.val);
    //    list.add(OmsMergeSplitOrderType.GOODS_NOT_SPECIFIED.val);
    //
    //    return list;
    //}
}
