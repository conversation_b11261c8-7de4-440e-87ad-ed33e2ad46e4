package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ip_b_jingdong_coupondtai")
@Data
@Document(index = "ip_b_jingdong_coupondtai", type = "ip_b_jingdong_coupondtai")
public class IpBJingdongCoupondtai extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "COUPON_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal couponPrice;

    @JSONField(name = "COUPON_TYPE")
    @Field(type = FieldType.Keyword)
    private String couponType;

    @JSONField(name = "ORDER_ID")
    @Field(type = FieldType.Keyword)
    private Long orderId;

    @JSONField(name = "SKU_ID")
    @Field(type = FieldType.Long)
    private Long skuId;

    @JSONField(name = "IP_B_JINGDONG_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBJingdongOrderId;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;
}