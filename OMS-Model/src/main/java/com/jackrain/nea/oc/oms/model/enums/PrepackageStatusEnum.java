package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * description：预打包状态
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
public enum PrepackageStatusEnum {

    NOT_NOTIFIED("1", "未通知"),

    NOTIFIED("2", "已通知"),

    COMPLETED("3", "已完成"),

    CANCELING("4", "取消中"),

    CANCEL_SUCCESS("5", "取消成功"),

    CANCEL_FAIL("6", "取消失败");

    String val;
    String text;

    PrepackageStatusEnum(String v, String s) {
        this.val = v;
        this.text = s;
    }

    public String getVal() {
        return val;
    }

    public String getText() {
        return text;
    }

    /**
     * 根据值,转换成文本
     */
    public static String getTextByVal(String val) {
        for (PrepackageStatusEnum o : PrepackageStatusEnum.values()) {
            if (Objects.equals(o.getVal(), val)) {
                return o.getText();
            }
        }
        return "";
    }

    /**
     * 转化为hashMap
     *
     * @return map
     */
    public static Map convertAllToHashVal() {
        Map<String, String> m = new HashMap<>();
        for (PrepackageStatusEnum o : PrepackageStatusEnum.values()) {
            m.put(o.getVal(), o.getText());
        }
        return m;
    }
}