package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: 黄世新
 * @Date: 2022/6/6 下午3:19
 * @Version 1.0
 * <p>
 * 订单业务类型枚举
 */
@Getter
@AllArgsConstructor
public enum OrderBusinessTypeCodeEnum {


    ON_LINE_MILK_CARD_SALE("RYCK01", "线上奶卡销售", "entity", "1"),
    ON_LINE_FREE_MILK_CARD("RYCK02", "线上免费奶卡", "entity", "1"),
    SAP_GIVE("RYCK03", "SAP奶卡赠送出库", "entity", "-1"),
    MILK_CARD_PICK_UP_GOODS("RYCK04", "奶卡提货", "pickup", "0"),
    FREE_MILK_CARD_PICK_UP_GOODS("RYCK05", "免费奶卡提货", "pickup", "0"),

    /**
     * 中台周期购订单 属于中台这边配置的周期购策略，与奶卡业务无关
     */
    CYCLE_PURCHASE_ORDER("RYCK06", "中台周期购订单", "ignore", "1"),
    FREE_CYCLE_PURCHASE_ORDER("RYCK07", "免费中台周期购订单", "ignore", "1"),
    CYCLE_PURCHASE_ORDER_PICK_UP("RYCK08", "中台周期购提货", "-1", "0"),
    FREE_CYCLE_PURCHASE_ORDER_PICK_UP("RYCK09", "免费中台周期购提货", "", "0"),
    CYCLE_ORDER("RYCK10", "奶卡周期购订单", "virtual", "1"),
    FREE_CYCLE_ORDER("RYCK11", "免费奶卡周期购订单", "virtual", "1"),
    CYCLE_PICK_UP("RYCK12", "奶卡周期购提货（目前奶卡系统下发）", "pickup", "0"),
    FREE_CYCLE_ORDER_PICK_UP("RYCK13", "免费奶卡周期购提货", "pickup", "0"),
    E_COMMERCE_SALE_ORDER("RYCK14", "电商销售订单", "ignore", "0"),
    AFTER_SALES_REISSUE("RYCK15", "售后补发", "ignore", "0"),
    SAP_CONSIGN_SALE("RYCK16", "SAP寄售补货订单-TOB", "ignore", "-1"),
    SAP_STANDARD_SALE("RYCK17", "SAP标准销售订单-TOB", "ignore", "-1"),
    SAP_RAW_MATERIAL_SALE("RYCK18", "SAP原材料销售订单-TOB", "ignore", "-1"),
    SAP_FREE("RYCK19", "SAP免费订单-TOC（不允许合单）", "ignore", "-1"),
    SAP_INSIDE("RYCK20", "SAP员工内购订单-TOC（不允许合单）", "ignore", "-1"),
    VIRTUAL_MILK_CARD("RYCK21", "电子奶卡销售", "virtual", "1"),
    MILK_CARD_RESET("RYCK22", "奶卡补发订单", "entity", "1"),
    SAP_MILK_CARD("RYCK23", "SAP员工奶卡内购（不允许合单）", "entity", "-1"),
    SAP_UNLINE_MILK_CARD("RYCK24", "SAP奶卡线下销售", "entity", "-1"),
    MILK_CARD_PICK_UP_GOODS_REISSUE("RYCK25", "奶卡提货补发", "ignore", "0"),
    CYCLE_PURCHASE_ORDER_PICK_UP_REISSUE("RYCK26", "中台周期购提货补发", "", "0"),
    CYCLE_PICK_UP_REISSUE("RYCK27", "奶卡周期购提货补发", "", "0"),
    FREE_MILK_CARD_RESET("RYCK31", "免费奶卡补发订单", "entity", "1"),
    MILK_CARD_PICK_UP_GOODS_RETURN("RYTH01", "奶卡提货退货", "", "0"),
    FREE_MILK_CARD_PICK_UP_GOODS_RETURN("RYTH02", "免费奶卡提货（补发）退货", "", "0"),
    CYCLE_PICK_UP_RETURN("RYTH03", "奶卡周期购提货退货", "", "0"),
    E_COMMERCE_SALE_ORDER_RETURN("RYTH04", "电商销售退货", "", "0"),
    AFTER_SALES_REISSUE_RETURN("RYTH05", "售后补发退货", "", "0"),
    MILK_CARD_PICK_UP_GOODS_REISSUE_RETURN("RYTH06", "奶卡提货补发退货", "", "0"),
    //            ("RYTH07","沧海2B退货","", "-1"),
//            ("RYTH08","沧海2B免费退货","", "-1"),
//            ("RYTH09","京云仓2B退货","", "-1"),
//            ("RYTH10","京云仓2B免费退货","", "-1"),
//            ("RYTH11","SAP销售退货","", "-1"),
//            ("RYTH12","SAP拒收退货","", "-1"),
    CYCLE_PURCHASE_ORDER_PICK_UP_RETURN("RYTH13", "中台周期购提货退货", "", "0"),
    CYCLE_PURCHASE_ORDER_PICK_UP_REISSUE_RETURN("RYTH14", "中台周期购提货补发退货", "", "0"),
    CYCLE_PICK_UP_REISSUE_RETURN("RYTH15", "奶卡周期购提货补发退货", "", "0"),
    MILK_RETURN_ONLY("RYTK01", "奶卡仅退款订单", "ignore", "1"),
    CYCLE_RETURN_ONLY("RYTK02", "奶卡周期购仅退款订单", "ignore", "1"),
    //            ("RYTK03","电商销售仅退款订单","", "-1"),
    MIDDLE_CYCLE_RETURN_ONLY("RYTK04","中台周期购仅退款订单","", "1"),
    FREE_CYCLE_PURCHASE_ORDER_PICK_UP_REISSUE_RETURN("RYCK99", "免费中台周期购提货（补发）退货", "", "0"),
    FREE_CYCLE_PICK_UP_REISSUE_RETURN("RYCK98", "免费奶卡周期购提货（补发）退货", "", "0"),
    FREE_CYCLE_PURCHASE_ORDER_PICK_UP_REISSUE("RYCK28", "免费中台周期购提货补发", "", "0"),
    FREE_CYCLE_PICK_UP_REISSUE("RYCK29", "免费奶卡周期购提货补发", "", "0"),
    FREE_MILK_CARD_PICK_UP_GOODS_REISSUE("RYCK30", "免费奶卡提货补发", "", "0"),

    UNKNOW("UNKNOW", "未知", "ignore", "-1");

    private String code;

    private String massage;

    private String naiKaType;

    /**
     * sap汇总类型
     * -1 不传
     * 0 销售汇总
     * 1 奶卡汇总
     */
    private String saleSumType;

    public static OrderBusinessTypeCodeEnum getOrderBusinessTypeEnumByCode(String code) {
        for (OrderBusinessTypeCodeEnum value : OrderBusinessTypeCodeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UNKNOW;
    }
//
//    /**
//     * 分仓分物流的检验
//     *
//     * @param status
//     * @return false继续流程 true不走
//     */
//    public static boolean checkSubWarehouseOccupy(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return OFFLINE_SALES_ORDER_TO_B.getCode().equals(status);
//    }
//
//    /**
//     * 促销检验
//     *
//     * @param status
//     * @return
//     */
//    public static boolean checkPromotion(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return MILK_CARD_GIVE_OUT_WAREHOUSE.getCode().equals(status)
//                || AFTER_SALES_REISSUE.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_B.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_C.getCode().equals(status)
//                || MILK_CARD_PICK_UP_GOODS_REISSUE.getCode().equals(status)
//                || MILK_CARD_REISSUE.getCode().equals(status)
//                || OFFLINE_MILK_CARD_SALE.getCode().equals(status);
//    }
//
//    /**
//     * 检验直播
//     *
//     * @param status
//     * @return
//     */
//    public static boolean checkLive(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return OFFLINE_SALES_ORDER_TO_B.getCode().equals(status);
//    }
//
//    /**
//     * 检验卡单
//     *
//     * @param status
//     * @return
//     */
//    public static boolean checkDetention(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return OFFLINE_SALES_ORDER_TO_B.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_C.getCode().equals(status)
//                || MILK_CARD_PICK_UP_GOODS_REISSUE.getCode().equals(status)
//                || MILK_CARD_REISSUE.getCode().equals(status)
//                || OFFLINE_MILK_CARD_SALE.getCode().equals(status);
//    }
//
//    /**
//     * 检验hold单
//     * @param status
//     * @return
//     */
//    public static boolean checkHoldOrder(Integer status) {
//        if (status == null) {
//            return false;
//        }
//        return MILK_CARD_GIVE_OUT_WAREHOUSE.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_B.getCode().equals(status)
//                || OFFLINE_SALES_ORDER_TO_C.getCode().equals(status)
//                || MILK_CARD_REISSUE.getCode().equals(status)
//                || OFFLINE_MILK_CARD_SALE.getCode().equals(status);
//    }

}
