package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * @ClassName BnReturnOrderColumnEnum
 * @Description 班牛退换货单字段枚举
 * <AUTHOR>
 * @Date 2025/5/15 16:30
 * @Version 1.0
 */
@Getter
public enum BnReturnOrderColumnEnum {

    /**
     * 发货仓库
     */
    WAREHOUSE_OUT("warehouseOut", "发货仓库", "21964"),

    /**
     * 退货仓库
     */
    WAREHOUSE_IN("warehouseIn", "退货仓库", "21941"),

    /**
     * 快递公司
     */
    LOGISTICS_COMPANY("logisticsCompany", "快递公司", "21942"),

    /**
     * 快递单号
     */
    LOGISTICS_CODE("logisticsCode", "快递单号", "21940"),

    /**
     * 签收状态
     */
    SIGNING_STATUS("signingStatus", "签收状态", "21962"),

    /**
     * 退回类型
     */
    RETURN_TYPE("returnType", "退回类型", "21961"),

    /**
     * 签收时间
     */
    SIGNING_TIME("signingTime", "签收时间", "21951"),

    /**
     * 仓库举证
     */
    WAREHOUSE_EVIDENCE("warehouseEvidence", "仓库举证", "21949"),

    /**
     * 仓库补充说明
     */
    WAREHOUSE_REMARK("warehouseRemark", "仓库补充说明", "21946"),

    /**
     * 快递举证
     */
    LOGISTICS_EVIDENCE("logisticsEvidence", "快递举证", "21958"),

    /**
     * 快递补充说明
     */
    LOGISTICS_REMARK("logisticsRemark", "快递补充说明", "21948"),

    /**
     * 判责结果
     */
    JUDGMENT_RESULT("judgmentResult", "判责结果", "21953"),

    /**
     * 任务状态
     */
    TASK_STATUS("taskStatus", "任务状态", "5"),

    /**
     * 关闭原因
     */
    CLOSE_REASON("closeReason", "关闭原因", "21960"),

    /**
     * OMS创建人
     */
    OMS_CREATOR("omsCreator", "OMS创建人", "21959"),

    /**
     * 创建人
     */
    CREATOR("creator", "创建人", "1"),

    /**
     * 执行人
     */
    EXECUTOR("executor", "执行人", "2"),

    /**
     * 创建时间
     */
    CREATE_TIME("createTime", "创建时间", "3"),

    /**
     * 截止时间
     */
    DEADLINE("deadline", "截止时间", "7"),

    /**
     * 修改时间
     */
    MODIFY_TIME("modifyTime", "修改时间", "4"),

    /**
     * 标题
     */
    TITLE("title", "标题", "6"),

    /**
     * 任务完结人
     */
    TASK_COMPLETER("taskCompleter", "任务完结人", "8"),

    /**
     * 任务完结时间
     */
    TASK_COMPLETE_TIME("taskCompleteTime", "任务完结时间", "9");

    /**
     * 字段名称
     */
    private final String columnName;

    /**
     * 字段描述
     */
    private final String columnDesc;

    /**
     * 字段ID
     */
    private final String columnId;

    BnReturnOrderColumnEnum(String columnName, String columnDesc, String columnId) {
        this.columnName = columnName;
        this.columnDesc = columnDesc;
        this.columnId = columnId;
    }
}
