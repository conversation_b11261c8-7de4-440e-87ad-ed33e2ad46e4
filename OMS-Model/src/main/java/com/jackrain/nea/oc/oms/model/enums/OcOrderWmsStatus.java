package com.jackrain.nea.oc.oms.model.enums;

/**
 * 全渠道订单表 -> WMS撤回状态
 *
 * @author: ming.fz
 * create at: 2019/3/28
 */
public enum OcOrderWmsStatus {

    WMS_NOTHING("未撤回", 0),
    WMS_SUCCESS("撤回成功", 1),
    WMS_FALSE("撤回失败", 2),
    WMS_NONE("无法撤回", 3);

    int val;
    String key;

    OcOrderWmsStatus(String k, int v) {
        this.val = v;
        this.key = k;
    }

    public int getVal() {
        return val;
    }

    public String getKey() {
        return key;
    }
}
