package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName OcBReturnOrderBnTask
 * @Description 退换货单班牛工单关联表
 * <AUTHOR>
 * @Date 2025/5/15 14:30
 * @Version 1.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "oc_b_return_order_bn_task")
public class OcBReturnOrderBnTask extends BaseModel {

    @ApiModelProperty(value = "ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;


    /**
     * 退换货单ID
     */
    @ApiModelProperty(value = "退换货单ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_RETURN_ORDER_ID")
    private Long ocBReturnOrderId;

    /**
     * 退换货单号
     */
    @ApiModelProperty(value = "退换货单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BILL_NO")
    private String billNo;

    /**
     * 班牛工单ID
     */
    @ApiModelProperty(value = "班牛工单ID")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BN_TASK_ID")
    private String bnTaskId;

    /**
     * 工单参数
     */
    @ApiModelProperty(value = "工单参数")
    @Field(type = FieldType.Text)
    @JSONField(name = "TASK_PARAM")
    private String taskParam;

    /**
     * 工单状态：0-已创建，1-已完成，2-已取消
     */
    @ApiModelProperty(value = "工单状态：0-已创建，1-已完成，2-已取消")
    @Field(type = FieldType.Integer)
    @JSONField(name = "TASK_STATUS")
    private Integer taskStatus;
}
