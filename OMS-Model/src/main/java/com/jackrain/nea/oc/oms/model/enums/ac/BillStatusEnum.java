package com.jackrain.nea.oc.oms.model.enums.ac;

import lombok.Getter;

/**
 * @author:洪艺安
 * @since: 2019/7/12
 * @create at : 2019/7/12 17:26
 */
@Getter
public enum BillStatusEnum {
    /**
     * 未审核
     */
    STATUS_UNAUDIT(1,"未审核"),
    /**
     * 已审核
     */
    STATUS_AUDIT(2,"已审核"),
    /**
     * 已作废
     */
    STATUS_VOID(3,"已作废"),
    /**
     * 已结案
     */
    STATUS_FINISH(4,"已结案");

    int val;
    String text;

    BillStatusEnum(int val,String text) {
        this.text = text;
        this.val = val;
    }

    public String getText() {
        return text;
    }

    public int getVal() {
        return val;
    }
}
