package com.jackrain.nea.oc.oms.model.enums;

/**
 * @author: lijin
 * @create: 2024-12-19
 * @description: 店铺类型枚举
 **/
public enum EquityBarterBatchDelItemShopTypeEnum {

    /**
     * 指定店铺
     */
    SPECIFIED_SHOP(1, "指定店铺"),

    /**
     * 所有指定店铺
     */
    ALL_SPECIFIED_SHOP(2, "所有指定店铺");

    private final Integer code;
    private final String desc;

    EquityBarterBatchDelItemShopTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code获取枚举
     */
    public static EquityBarterBatchDelItemShopTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (EquityBarterBatchDelItemShopTypeEnum shopType : values()) {
            if (shopType.getCode().equals(code)) {
                return shopType;
            }
        }
        return null;
    }

    /**
     * 校验code是否有效
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }
}
