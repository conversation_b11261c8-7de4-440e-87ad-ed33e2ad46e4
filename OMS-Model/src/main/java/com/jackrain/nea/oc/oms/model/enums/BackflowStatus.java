package com.jackrain.nea.oc.oms.model.enums;

/**
 * 回流状态
 *
 * @author: heliu
 * @since: 2019-03-21
 * create at : 2019-03-21 12:09  BACKFLOWSTATUS
 */
public enum BackflowStatus {

    //，可选值 ：qimen_erp_transfer(erp转单),sqimen_erp_check(erp审单),qimen_cp_notify（erp通知配单）,
    // qimen_cp_out（仓库通知erp出库）
    /**
     * erp转单
     */
    QIMEN_ERP_TRANSFER,

    /**
     * erp审单
     */
    QIMEN_ERP_CHECK,
    /**
     * erp通知配单
     */
    QIMEN_CP_NOTIFY,
    /**
     * 仓库通知erp出库
     */
    QIMEN_CP_OUT;

    public String parseValue() {
        if (this == QIMEN_ERP_TRANSFER) {
            return "QIMEN_ERP_TRANSFER";
        } else if (this == QIMEN_ERP_CHECK) {
            return "QIMEN_ERP_CHECK";
        } else if (this == QIMEN_CP_NOTIFY) {
            return "QIMEN_CP_NOTIFY";
        } else if (this == QIMEN_CP_OUT) {
            return "QIMEN_CP_OUT";
        } else {
            return "其他";
        }
    }

}
