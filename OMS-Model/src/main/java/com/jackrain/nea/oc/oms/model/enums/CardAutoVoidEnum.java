package com.jackrain.nea.oc.oms.model.enums;

/**
 * @ClassName CardAutoVoidEnum
 * @Description 奶卡自动作废状态枚举
 * <AUTHOR>
 * @Date 2023/3/1 14:26
 * @Version 1.0
 */
public enum CardAutoVoidEnum {

    UN_NECESSARY(0, "无需作废"),
    HAVE_NOT_VOID(1, "未作废"),
    TO_DO_VOID(2, "待作废"),
    VOID_SUCCESS(3, "作废成功"),
    VOID_ERROR(4, "作废失败"),
    ;

    private Integer code;
    private String msg;

    CardAutoVoidEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    public Integer getCode() {
        return code;
    }

    public static String getMsgByCode(int val) {
        CardAutoVoidEnum[] enums = values();
        for (CardAutoVoidEnum cardAutoVoidEnum : enums) {
            if (val == cardAutoVoidEnum.getCode()) {
                return cardAutoVoidEnum.getMsg();
            }
        }
        return null;
    }

}
