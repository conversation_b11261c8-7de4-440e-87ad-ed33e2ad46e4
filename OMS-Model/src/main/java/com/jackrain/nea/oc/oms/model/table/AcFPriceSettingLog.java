package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * className: AcFPriceSettingLog
 * description:
 *
 * <AUTHOR>
 * create: 2021-10-25
 * @since JDK 1.8
 */
@Data
@TableName("ac_f_price_setting_log")
@Document(index = "ac_f_price_setting_log",type = "ac_f_price_setting_log")
@ApiModel(value = "ac_f_price_setting_log",description = "全渠道价格配置日志")
public class AcFPriceSettingLog extends BaseModel {

    private static final long serialVersionUID = 3895446076243588496L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "ID")
    private Long id;

    @JSONField(name = "AC_F_PRICE_SETTING_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "商品配置表id")
    private Long acFPriceSettingId;

    @JSONField(name = "LOG_TYPE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "日志类型:0-新增/1-修改")
    private String logType;

    @JSONField(name = "LOG_MESSAGE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "修改内容")
    private String logMessage;

    @JSONField(name = "BEFORE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "修改前数据")
    private String before;

    @JSONField(name = "AFTER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "修改后数据")
    private String after;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "创建人姓名")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "修改人姓名")
    private String modifierename;
}
