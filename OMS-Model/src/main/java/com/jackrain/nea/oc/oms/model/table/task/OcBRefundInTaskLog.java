package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "oc_b_refund_in_task_log")
@Data
@Document(index = "oc_b_refund_in_task_log", type = "oc_b_refund_in_task_log")
public class OcBRefundInTaskLog extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.ID_WORKER)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "MODCONTENT")
    @Field(type = FieldType.Keyword)
    private String modcontent;

    @JSONField(name = "BEFORE_MODIFICATION")
    @Field(type = FieldType.Keyword)
    private String beforeModification;

    @JSONField(name = "AFTER_MODIFICATION")
    @Field(type = FieldType.Keyword)
    private String afterModification;

    @JSONField(name = "REFUND_TASK_ID")
    @Field(type = FieldType.Long)
    private Long refundTaskId;
}