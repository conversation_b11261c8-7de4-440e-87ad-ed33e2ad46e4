package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 商品效期策略类型
 * @author: caomalai
 * @create: 2022-08-11 11:34
 **/
public enum AppointTypeEnum {
    DATE_SCOPE(1,"生产日期范围"),
    DAYS_SCOPE(2,"生产天数范围");

    @Getter
    private Integer key;
    @Getter
    private String desc;

    AppointTypeEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static AppointTypeEnum getByKey(Integer key) {
        for (AppointTypeEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }

    public static AppointTypeEnum getByDesc(String desc) {
        for (AppointTypeEnum current : values()) {
            if (Objects.equals(current.getDesc(), desc)) {
                return current;
            }
        }
        return null;
    }
}
