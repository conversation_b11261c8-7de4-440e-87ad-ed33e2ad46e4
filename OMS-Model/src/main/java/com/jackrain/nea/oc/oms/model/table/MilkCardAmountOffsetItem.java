package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @ClassName MilkCardAmountOffsetItem
 * @Description 奶卡金额冲抵明细
 * <AUTHOR>
 * @Date 2022/7/31 20:23
 * @Version 1.0
 */
@TableName(value = "milk_card_amount_offset_item")
@Data
@Document(index = "milk_card_amount_offset_item", type = "milk_card_amount_offset_item")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "milk_card_amount_offset_item", description = "奶卡提奶金额冲抵明细")
public class MilkCardAmountOffsetItem extends BaseModel {

    @ApiModelProperty(value = "id")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "奶卡卡号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CARD_CODE")
    private String cardCode;

    @ApiModelProperty(value = "条码id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @ApiModelProperty(value = "条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @ApiModelProperty(value = "商品id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @ApiModelProperty(value = "商品货号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @ApiModelProperty(value = "商品名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @ApiModelProperty(value = "行项目类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "ROW_ITEM_TYPE")
    private Integer rowItemType;

    @ApiModelProperty(value = "数量")
    @Field(type = FieldType.Integer)
    @JSONField(name = "QTY")
    private Integer qty;

    @ApiModelProperty(value = "单位")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "UNIT")
    private String unit;

    @ApiModelProperty(value = "冲抵金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "OFFSET_PRICE")
    private BigDecimal offsetPrice;

    @ApiModelProperty(value = "商品类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "ITEM_TYPE")
    private Integer itemType;

    @ApiModelProperty(value = "奶卡提奶冲抵订单id")
    @Field(type = FieldType.Long)
    @JSONField(name = "OFFSET_ORDER_ID")
    private Long offsetOrderId;

    @JSONField(name = "FACTORY_CODE")
    private String factoryCode;

}
