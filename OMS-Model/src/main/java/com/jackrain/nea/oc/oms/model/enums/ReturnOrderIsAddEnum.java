package com.jackrain.nea.oc.oms.model.enums;

/**
 * <AUTHOR> zhuxing
 * @Date : 2022-04-06 19:34
 * @Description : 退换货订单是否手动新增
 **/
public enum ReturnOrderIsAddEnum {

    /**
     * 是否手工新增
     */
    IS_NOT_MANUALLY_ADD("非手工新增", 0),

    IS_MANUALLY_ADD("手工新增", 1);

    String key;
    Integer val;

    ReturnOrderIsAddEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

}
