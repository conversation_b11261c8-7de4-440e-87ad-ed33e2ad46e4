package com.jackrain.nea.oc.oms.model.enums;

import com.alibaba.fastjson.JSONObject;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderExtend;
import com.jackrain.nea.oc.oms.model.relation.OcBOrderItemExtend;
import com.jackrain.nea.oc.oms.model.table.OcBOrder;
import com.jackrain.nea.oc.oms.model.table.OcBOrderItem;
import com.jackrain.nea.oc.oms.vo.OcBOrderImpVO;
import com.jackrain.nea.oc.oms.vo.OcBOrderPreImpVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 订单列表-字段值转换为文本
 *
 * @author: xiwen.z
 * create at: 2019/3/26 0026
 */
public class OcBorderListEnums {

    private static final String EMPSTRING = "";

    /**
     * 订单占单状态
     */
    public enum OccupyStatusEnum {
        UN_OCCUPY(0, "未占单"), PRE_DEAL_COMPLETE(1, "订单预处理完成"), DIS_STORE_COMPLETE(2, "分仓完成"), EXECUTE_GIFT_COMPLETE(3, "执行赠品活动完成"), OCCUPY_COMPLETE(4, "占单完成"), DIS_LOGISTIC_COMPLETE(5, "分物流完成");

        int val;
        String text;

        OccupyStatusEnum(int v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * @param v integer
         * @return string
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return EMPSTRING;
            }
            for (OccupyStatusEnum e : OccupyStatusEnum.values()) {
                if (e.getVal() == v) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        public int getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 付款方式
     */
    public enum PayTypeEnum {
        ONLINE(1, "在线支付"), PAY_ARRIVAL(2, "货到付款"), PAY_DEFAULT(0, "");

        int val;
        String text;

        PayTypeEnum(int v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * 获取支付方式文本
         *
         * @param v integer
         * @return string
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return PAY_DEFAULT.getText();
            }
            for (PayTypeEnum e : PayTypeEnum.values()) {
                if (v == e.getVal()) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        /**
         * 获取支付方式
         *
         * @param v integer
         * @return string
         */
        public static int getValByText(String text) {
            if (StringUtils.isEmpty(text)) {
                return PAY_DEFAULT.getVal();
            }
            for (PayTypeEnum e : PayTypeEnum.values()) {
                if (text.equals(e.getText())) {
                    return e.getVal();
                }
            }
            return 0;
        }

        /**
         * 转为map
         *
         * @return map
         */
        public static Map convertAllToHashVal() {
            Map<Integer, String> m = new HashMap<>();
            for (PayTypeEnum e : PayTypeEnum.values()) {
                m.put(e.getVal(), e.getText());
            }
            return m;
        }

        public int getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * wms撤回状态
     */
    public enum WmsCanceStatusEnum {
        UN_RECALL(0, "未撤回"), RECALL_SUCCESS(1, "撤回成功"), RECALL_FAIL(2, "撤回失败"), CANNOT_WITHDRAWN(3, "无法撤回"), UNKNOW_RECALL(-1, "");

        Integer val;
        String text;

        WmsCanceStatusEnum(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * @param v Integer
         * @return string
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return UNKNOW_RECALL.getText();
            }
            for (WmsCanceStatusEnum e : WmsCanceStatusEnum.values()) {
                if (e.getVal().equals(v)) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        /**
         * 转为map
         *
         * @return map
         */
        public static Map convertAllToHashVal() {
            Map<Integer, String> m = new HashMap<>();
            for (WmsCanceStatusEnum e : WmsCanceStatusEnum.values()) {
                m.put(e.getVal(), e.getText());
            }
            return m;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 自动审核状态
     */
    public enum AutoAuditStatusEnum {
        AUTO_AUDIT_NO(0, "未审核"), AUTO_AUDIT_FAIL(1, "审核失败"), AUTO_AUDIT_SUCCESS(2, "审核成功"), AUTO_AUDIT_ING(3, "审核中"), AUTO_AUDIT_UNKNOW(-1, "");
        int val;
        String text;

        AutoAuditStatusEnum(int v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * @param v integer
         * @return string
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return AUTO_AUDIT_UNKNOW.getText();
            }
            for (AutoAuditStatusEnum e : AutoAuditStatusEnum.values()) {
                if (e.getVal() == v) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        public int getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 是否生成开标通知
     */
    public enum IsGeninvoiceNoticeEnum {
        /* (1, "已生成"),(0, "未生成");*/
        IS_GENINVOICE_NOTICE_YES(1, "已生成"), IS_GENINVOICE_NOTICE_NO(0, "未生成");

        Integer val;
        String text;

        IsGeninvoiceNoticeEnum(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * @param v String
         * @return string
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return EMPSTRING;
            }
            for (IsGeninvoiceNoticeEnum e : IsGeninvoiceNoticeEnum.values()) {
                if (e.getVal().equals(v)) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 退货状态
     */
    public enum ReturnStatusEnum {
        RETURN_ING(1, "退货中"), NO_RETURN(0, "无退货"), PART_RETURN(2, "部分退货入库"), ALL_RETURN(3, "全部退货入库");

        int val;
        String text;

        ReturnStatusEnum(int v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * 退货状态
         *
         * @param v integer
         * @return string
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return NO_RETURN.getText();
            }
            for (ReturnStatusEnum e : ReturnStatusEnum.values()) {
                if (e.getVal() == v) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        /**
         * 转为map
         *
         * @return map
         */
        public static Map convertAllToHashVal() {
            Map<Integer, String> m = new HashMap<>();
            for (ReturnStatusEnum e : ReturnStatusEnum.values()) {
                m.put(e.getVal(), e.getText());
            }
            return m;
        }

        public int getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 开票状态
     */
    public enum InvoiceStatusEnum {
        UN_REGISTER(0, "未登记"), REGISTERED_UNINVOICED(1, "已登记未开票"), INVOICED(2, "已开票"), PARTIAL_INVOICE(3, "部分已开票");
        Integer val;
        String text;

        InvoiceStatusEnum(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * 开票状态
         *
         * @param v Integer
         * @return String
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return EMPSTRING;
            }
            for (InvoiceStatusEnum e : InvoiceStatusEnum.values()) {
                if (e.getVal().equals(v)) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }

    }

    /**
     * 发票类型
     */
    public enum InvoiceTypeEnum {
        ELECTRONIC_INVOICE(0, "电子发票"), PAPER_INVOICE(1, "纸质发票"), SPECIAL_INVOICE(2, "专用发票");

        Integer val;
        String text;

        InvoiceTypeEnum(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * 发票类型
         *
         * @param v Integer
         * @return string
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return EMPSTRING;
            }
            for (InvoiceTypeEnum e : InvoiceTypeEnum.values()) {
                if (e.getVal().equals(v)) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        /**
         * 获取所有
         *
         * @return list
         */
        public static List<JSONObject> getAllEnumToList() {
            List<JSONObject> lst = new ArrayList();
            JSONObject o;
            for (InvoiceTypeEnum e : InvoiceTypeEnum.values()) {
                o = new JSONObject();
                o.put("INVOICE_TYPE", e.getVal());
                o.put("INVOICE_TYPE_NAME", e.getText());
                lst.add(o);
            }
            return lst;
        }

        /**
         * 获取所有key
         *
         * @return set
         */
        public static Set<Integer> getAllEnumValueToSet() {
            Set<Integer> set = new HashSet<>();
            for (InvoiceTypeEnum e : InvoiceTypeEnum.values()) {
                set.add(e.getVal());
            }
            return set;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 抬头类型
     */
    public enum HeaderTypeEnum {
        PERSONAL(0, "个人"), COMPANY(1, "企业");

        Integer val;
        String text;

        HeaderTypeEnum(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * 抬头类型
         *
         * @param v Integer
         * @return String
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return EMPSTRING;
            }
            for (HeaderTypeEnum e : HeaderTypeEnum.values()) {
                if (e.getVal().equals(v)) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        /**
         * 获取所有
         *
         * @return list
         */
        public static List<JSONObject> getAllEnumToList() {
            List<JSONObject> lst = new ArrayList();
            JSONObject o;
            for (HeaderTypeEnum e : HeaderTypeEnum.values()) {
                o = new JSONObject();
                o.put("HEADER_TYPE", e.getVal());
                o.put("HEADER_TYPE_NAME", e.getText());
                lst.add(o);
            }
            return lst;
        }

        /**
         * 获取所有key
         *
         * @return set
         */
        public static Set<Integer> getAllEnumValueToSet() {
            Set<Integer> set = new HashSet<>();
            for (HeaderTypeEnum e : HeaderTypeEnum.values()) {
                set.add(e.getVal());
            }
            return set;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 刷单
     */
    public enum ScalpingTypeEnum {
        SDZDFH(1, "刷单自动发货"), THSPFH(2, "替换商品发货"), SDWBFH(3, "刷单外包发货");

        Integer val;
        String text;

        ScalpingTypeEnum(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * 刷单
         *
         * @param v Integer
         * @return String
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return EMPSTRING;
            }
            for (ScalpingTypeEnum e : ScalpingTypeEnum.values()) {
                if (e.getVal().equals(v)) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        /**
         * 获取所有刷单转化为哈希
         *
         * @return map
         */
        public static Map<Integer, String> getAllConvertToMap() {
            Map<Integer, String> m = new HashMap<>();
            for (ScalpingTypeEnum e : ScalpingTypeEnum.values()) {
                m.put(e.getVal(), e.getText());
            }
            return m;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 是否生在零售调拨单
     */
    public enum IsToDrpOrderEnum {
        // 0 否1 是
        IS_TO_DRP_YES(1, "是"), IS_TO_DRP_NO(0, "否");

        Integer val;
        String text;

        IsToDrpOrderEnum(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        /**
         * 转化值为文本
         *
         * @param v integer
         * @return string
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return EMPSTRING;
            }
            for (IsToDrpOrderEnum e : IsToDrpOrderEnum.values()) {
                if (e.getVal().equals(v)) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    /**
     * 传SAP状态
     */
    public enum SendSAPEnum {

        UN_SEND(0L, "未传"), SEND(1L, "传中"), FINISH(2L, "传成功"), FAIL(3L, "传失败"), NO_SEND(4L, "无需传");

        Long val;
        String txt;

        SendSAPEnum(Long val, String txt) {
            this.val = val;
            this.txt = txt;
        }

        /**
         * 转
         *
         * @return map
         */
        public static Map<Long, String> toMap() {
            Map<Long, String> m = new HashMap<>();
            for (SendSAPEnum o : SendSAPEnum.values()) {
                m.put(o.getVal(), o.getTxt());
            }
            m.put(null, "");
            return m;
        }

        /**
         * 转换为Integer类型
         *
         * @return Map
         */
        public static Map<Integer, String> toIntegerMap() {
            Map<Integer, String> m = new HashMap<>();
            for (SendSAPEnum o : SendSAPEnum.values()) {
                m.put(Integer.valueOf(o.getVal().toString()), o.getTxt());
            }
            m.put(null, "");
            return m;
        }

        public Long getVal() {
            return val;
        }

        public String getTxt() {
            return txt;
        }
    }


    /**
     * 通用是否
     */
    public enum YesOrNoEnum {
        // 0 否1 是
        IS_YES(1, "是"), IS_NO(0, "否");

        Integer val;
        String text;

        YesOrNoEnum(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }

        /**
         * @param v Integer
         * @return String
         */
        public static String getTextByVal(Integer v) {
            if (v == null) {
                return EMPSTRING;
            }
            for (YesOrNoEnum e : YesOrNoEnum.values()) {
                if (e.getVal().equals(v)) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }
    }

    /**
     * 退款状态
     */
    public enum OrderRefundStatus {
        NOTREFUND(0, "未退款"), WAIT_SELLER_AGREE(1, "买家已经申请退款，等待卖家同意"), WAIT_BUYER_RETURN_GOODS(2, "卖家已经同意退款，等待买家退货"), WAIT_SELLER_CONFIRM_GOODS(3, "买家已经退货，等待卖家确认收货"), SELLER_REFUSE_BUYER(4, "卖家拒绝退款"), CLOSED(5, "退款关闭"), SUCCESS(6, "退款成功");
        Integer val;
        String text;

        OrderRefundStatus(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }

        /**
         * 转为hash
         *
         * @return map
         */
        public static Map convertAllToHashVal() {
            Map<Integer, String> m = new HashMap<>();
            for (OrderRefundStatus o : OrderRefundStatus.values()) {
                m.put(o.getVal(), o.getText());
            }
            return m;
        }
    }

    /**
     * 退款状态
     */
    public enum OcBOrderRefundStatus {
        /**
         * 全部退款
         */
        ALL_PAY(0, "全部退款"),

        /**
         * 部分退款
         */
        PART_PAY(1, "部分退款"),

        /**
         * 未退款
         */
        NO_PAY(2, "未退款");
        Integer val;
        String text;

        OcBOrderRefundStatus(Integer v, String n) {
            this.val = v;
            this.text = n;
        }

        public Integer getVal() {
            return val;
        }

        public String getText() {
            return text;
        }

        /**
         * 转为hash
         *
         * @return map
         */
        public static Map convertAllToHashVal() {
            Map<Integer, String> m = new HashMap<>();
            for (OcBOrderRefundStatus o : OcBOrderRefundStatus.values()) {
                m.put(o.getVal(), o.getText());
            }
            return m;
        }
    }

    /**
     * 淘宝预售状态
     */
    public enum OrderPreSaleStatus {

        FRONT_PAID_FINAL_NOPAID("FRONT_PAID_FINAL_NOPAID", "预售尾款未付"), FRONT_PAID_FINAL_PAID("FRONT_PAID_FINAL_PAID", "预售尾款已付");
        String val;
        String text;

        OrderPreSaleStatus(String v, String n) {
            this.val = v;
            this.text = n;
        }

        public String getVal() {
            return val;
        }

        public String getText() {
            return text;
        }

        /**
         * 转为hash
         *
         * @return map
         */
        public static Map<String, String> convertAllToHashVal() {
            Map<String, String> m = new HashMap<>();
            for (OrderPreSaleStatus o : OrderPreSaleStatus.values()) {
                m.put(o.getVal(), o.getText());
            }
            m.put(null, "非预售");
            m.put("", "非预售");
            return m;
        }
    }

    private static Map<Integer, String> orderTypeDatas = new HashMap<>(); //订单类型
    private static Map<Integer, String> occupyStatusDatas = new HashMap<>(); //订单占单状态
    private static Map<Integer, String> platFormDatas = new HashMap<>(); //平台
    private static Map<Integer, String> payTypeDatas = new HashMap<>(); //付款方式
    private static Map<Integer, String> wmsCanceStatusDatas = new HashMap<>(); //wms撤回状态
    private static Map<Integer, String> autoAuditStatusDatas = new HashMap<>(); //自动审核状态
    private static Map<Integer, String> isGeninvoiceNoticeDatas = new HashMap<>(); //是否生成开标通知
    private static Map<Integer, String> returnStatusDatas = new HashMap<>(); //退货状态
    private static Map<Integer, String> ssToDrpOrderDatas = new HashMap<>(); //是否生在零售调拨单
    private static Map<Integer, String> yesOrNoDatas = new HashMap<>(); //通用是否
    private static Map<Integer, String> orderRefundDatas = new HashMap<>(); //退货状态

    static {
        //订单类型
        OrderTypeEnum[] orderType = OrderTypeEnum.values();
        for (OrderTypeEnum orderTypeEnum : orderType) {
            orderTypeDatas.put(orderTypeEnum.getVal(), orderTypeEnum.getDescription());
        }
        //订单占单状态
        OcBorderListEnums.OccupyStatusEnum[] occupyStatus = OcBorderListEnums.OccupyStatusEnum.values();
        for (OcBorderListEnums.OccupyStatusEnum occupyStatusEnum : occupyStatus) {
            occupyStatusDatas.put(occupyStatusEnum.getVal(), occupyStatusEnum.getText());
        }
        //平台
        PlatFormEnum[] platForm = PlatFormEnum.values();
        for (PlatFormEnum platFormEnum : platForm) {
            platFormDatas.put(platFormEnum.getCode(), platFormEnum.getName());
        }
        //付款方式
        OcBorderListEnums.PayTypeEnum[] payType = OcBorderListEnums.PayTypeEnum.values();
        for (OcBorderListEnums.PayTypeEnum payTypeEnum : payType) {
            payTypeDatas.put(payTypeEnum.getVal(), payTypeEnum.getText());
        }
        //wms撤回状态
        OcBorderListEnums.WmsCanceStatusEnum[] wmsCanceStatus = OcBorderListEnums.WmsCanceStatusEnum.values();
        for (OcBorderListEnums.WmsCanceStatusEnum wmsCanceStatusEnum : wmsCanceStatus) {
            wmsCanceStatusDatas.put(wmsCanceStatusEnum.getVal(), wmsCanceStatusEnum.getText());
        }
        //自动审核状态
        OcBorderListEnums.AutoAuditStatusEnum[] autoAuditStatus = OcBorderListEnums.AutoAuditStatusEnum.values();
        for (OcBorderListEnums.AutoAuditStatusEnum autoAuditStatusEnum : autoAuditStatus) {
            autoAuditStatusDatas.put(autoAuditStatusEnum.getVal(), autoAuditStatusEnum.getText());
        }
        //是否生成开标通知
        OcBorderListEnums.IsGeninvoiceNoticeEnum[] isGeninvoiceNotice = OcBorderListEnums.IsGeninvoiceNoticeEnum.values();
        for (OcBorderListEnums.IsGeninvoiceNoticeEnum isGeninvoiceNoticeEnum : isGeninvoiceNotice) {
            isGeninvoiceNoticeDatas.put(isGeninvoiceNoticeEnum.getVal(), isGeninvoiceNoticeEnum.getText());
        }
        //退货状态
        OcBorderListEnums.ReturnStatusEnum[] returnStatus = OcBorderListEnums.ReturnStatusEnum.values();
        for (OcBorderListEnums.ReturnStatusEnum returnStatusEnum : returnStatus) {
            returnStatusDatas.put(returnStatusEnum.getVal(), returnStatusEnum.getText());
        }
        //是否生在零售调拨单
        OcBorderListEnums.IsToDrpOrderEnum[] isToDrpOrder = OcBorderListEnums.IsToDrpOrderEnum.values();
        for (OcBorderListEnums.IsToDrpOrderEnum isToDrpOrderEnum : isToDrpOrder) {
            ssToDrpOrderDatas.put(isToDrpOrderEnum.getVal(), isToDrpOrderEnum.getText());
        }
        //通用是否
        OcBorderListEnums.YesOrNoEnum[] yesOrNo = OcBorderListEnums.YesOrNoEnum.values();
        for (OcBorderListEnums.YesOrNoEnum yesOrNoEnum : yesOrNo) {
            yesOrNoDatas.put(yesOrNoEnum.getVal(), yesOrNoEnum.getText());
        }
        //退款状态
        OcBorderListEnums.OrderRefundStatus[] orderRefund = OcBorderListEnums.OrderRefundStatus.values();
        for (OcBorderListEnums.OrderRefundStatus orderRefundEnum : orderRefund) {
            orderRefundDatas.put(orderRefundEnum.getVal(), orderRefundEnum.getText());
        }
    }

    //主表导出字段值转换成文字
    public static List<OcBOrderExtend> changeOrderChildClassList(List<OcBOrder> dbList) {
        List<OcBOrderExtend> collect = dbList.parallelStream().map(x -> {
            OcBOrderExtend ex = new OcBOrderExtend();
            BeanUtils.copyProperties(x, ex);
            ex.setOrderStatusName(OcOrderCheckBoxEnum.enumToStringByValue(ex.getOrderStatus()));
            ex.setOrderTypeName(orderTypeDatas.get(ex.getOrderType()));
            ex.setOccupyStatusName(occupyStatusDatas.get(ex.getOccupyStatus()));
            ex.setPlatformName(platFormDatas.get(ex.getPlatform()));
            ex.setPayTypeName(payTypeDatas.get(ex.getPayType()));
            ex.setWmsCancelStatusName(wmsCanceStatusDatas.get(ex.getWmsCancelStatus()));
            ex.setAutoAuditStatusName(autoAuditStatusDatas.get(ex.getAutoAuditStatus()));
            ex.setIsGeninvoiceNoticeName(isGeninvoiceNoticeDatas.get(ex.getIsGeninvoiceNotice()));
            ex.setReturnStatusName(returnStatusDatas.get(ex.getReturnStatus()));
            // ex.setIsTodrpName(ssToDrpOrderDatas.get(ex.getIsTodrp()));
            //通用是否
            ex.setIsInvoiceName(yesOrNoDatas.get(ex.getIsInvoice()));
            ex.setIsMergeName(yesOrNoDatas.get(ex.getIsMerge()));
            ex.setIsSplitName(yesOrNoDatas.get(ex.getIsSplit()));
            ex.setIsIntereceptName(yesOrNoDatas.get(ex.getIsInterecept()));
            ex.setIsInreturningName(yesOrNoDatas.get(ex.getIsInreturning()));
            ex.setIsHasgiftName(yesOrNoDatas.get(ex.getIsHasgift()));
            // ex.setIsGiveLogisticName(yesOrNoDatas.get(ex.getIsGiveLogistic()));
            ex.setIsInventedName(yesOrNoDatas.get(ex.getIsInvented()));
            ex.setIsCombinationName(yesOrNoDatas.get(ex.getIsCombination()));
            ex.setIsOutUrgencyName(yesOrNoDatas.get(ex.getIsOutUrgency()));
            //ex.setIsShopCommissionName(yesOrNoDatas.get(ex.getIsShopCommission()));
            ex.setIsHasTicketName(yesOrNoDatas.get(ex.getIsHasTicket()));
            // ex.setIsWriteoffName(yesOrNoDatas.get(ex.getIsWriteoff()));
            ex.setIsJcorderName(yesOrNoDatas.get(ex.getIsJcorder()));
//            ex.setIsLackstockName(yesOrNoDatas.get(ex.getIsLackstock()));
            return ex;
        }).collect(Collectors.toList());
        return collect;
    }

    //明细表导出字段值转换成文字
    public static List<OcBOrderItemExtend> changeItemChildClassList(List<OcBOrderItem> dbList) {
        List<OcBOrderItemExtend> collect = dbList.parallelStream().map(x -> {
            OcBOrderItemExtend ex = new OcBOrderItemExtend();
            BeanUtils.copyProperties(x, ex);
            ex.setIsGiftName(yesOrNoDatas.get(ex.getIsGift()));
            ex.setRefundStatusName(orderRefundDatas.get(ex.getRefundStatus()));
            ex.setIsLackstockName(yesOrNoDatas.get(ex.getIsLackstock()));
            return ex;
        }).collect(Collectors.toList());
        return collect;
    }


    private static Map<String, Integer> orderTypeValues = new HashMap<>(); //订单类型
    private static Map<String, Integer> occupyStatusValues = new HashMap<>(); //订单占单状态
    private static Map<String, Integer> platFormValues = new HashMap<>(); //平台
    private static Map<String, Integer> payTypeValues = new HashMap<>(); //付款方式
    private static Map<String, Integer> wmsCanceStatusValues = new HashMap<>(); //wms撤回状态
    private static Map<String, Integer> autoAuditStatusValues = new HashMap<>(); //自动审核状态
    private static Map<String, Integer> isGeninvoiceNoticeValues = new HashMap<>(); //是否生成开标通知
    private static Map<String, Integer> returnStatusValues = new HashMap<>(); //退货状态
    private static Map<String, Integer> ssToDrpOrderValues = new HashMap<>(); //是否生在零售调拨单
    private static Map<String, Integer> yesOrNoValues = new HashMap<>(); //通用是否

    static {
        //订单类型
        OrderTypeEnum[] orderType = OrderTypeEnum.values();
        for (OrderTypeEnum orderTypeEnum : orderType) {
            orderTypeValues.put(orderTypeEnum.getDescription(), orderTypeEnum.getVal());
        }
        //订单占单状态
        OcBorderListEnums.OccupyStatusEnum[] occupyStatus = OcBorderListEnums.OccupyStatusEnum.values();
        for (OcBorderListEnums.OccupyStatusEnum occupyStatusEnum : occupyStatus) {
            occupyStatusValues.put(occupyStatusEnum.getText(), occupyStatusEnum.getVal());
        }
        //平台
        PlatFormEnum[] platForm = PlatFormEnum.values();
        for (PlatFormEnum platFormEnum : platForm) {
            platFormValues.put(platFormEnum.getName(), platFormEnum.getCode());
        }
        //付款方式
        OcBorderListEnums.PayTypeEnum[] payType = OcBorderListEnums.PayTypeEnum.values();
        for (OcBorderListEnums.PayTypeEnum payTypeEnum : payType) {
            payTypeValues.put(payTypeEnum.getText(), payTypeEnum.getVal());
        }
        //wms撤回状态
        OcBorderListEnums.WmsCanceStatusEnum[] wmsCanceStatus = OcBorderListEnums.WmsCanceStatusEnum.values();
        for (OcBorderListEnums.WmsCanceStatusEnum wmsCanceStatusEnum : wmsCanceStatus) {
            wmsCanceStatusValues.put(wmsCanceStatusEnum.getText(), wmsCanceStatusEnum.getVal());
        }
        //自动审核状态
        OcBorderListEnums.AutoAuditStatusEnum[] autoAuditStatus = OcBorderListEnums.AutoAuditStatusEnum.values();
        for (OcBorderListEnums.AutoAuditStatusEnum autoAuditStatusEnum : autoAuditStatus) {
            autoAuditStatusValues.put(autoAuditStatusEnum.getText(), autoAuditStatusEnum.getVal());
        }
        //是否生成开标通知
        OcBorderListEnums.IsGeninvoiceNoticeEnum[] isGeninvoiceNotice = OcBorderListEnums.IsGeninvoiceNoticeEnum.values();
        for (OcBorderListEnums.IsGeninvoiceNoticeEnum isGeninvoiceNoticeEnum : isGeninvoiceNotice) {
            isGeninvoiceNoticeValues.put(isGeninvoiceNoticeEnum.getText(), isGeninvoiceNoticeEnum.getVal());
        }
        //退货状态
        OcBorderListEnums.ReturnStatusEnum[] returnStatus = OcBorderListEnums.ReturnStatusEnum.values();
        for (OcBorderListEnums.ReturnStatusEnum returnStatusEnum : returnStatus) {
            returnStatusValues.put(returnStatusEnum.getText(), returnStatusEnum.getVal());
        }
        //是否生在零售调拨单
        OcBorderListEnums.IsToDrpOrderEnum[] isToDrpOrder = OcBorderListEnums.IsToDrpOrderEnum.values();
        for (OcBorderListEnums.IsToDrpOrderEnum isToDrpOrderEnum : isToDrpOrder) {
            ssToDrpOrderValues.put(isToDrpOrderEnum.getText(), isToDrpOrderEnum.getVal());
        }
        //通用是否
        OcBorderListEnums.YesOrNoEnum[] yesOrNo = OcBorderListEnums.YesOrNoEnum.values();
        for (OcBorderListEnums.YesOrNoEnum yesOrNoEnum : yesOrNo) {
            ssToDrpOrderValues.put(yesOrNoEnum.getText(), yesOrNoEnum.getVal());
        }
    }

    //字段置换  导入文字转换成字段值
    public static void changeImportList(List<OcBOrderExtend> dbList) {
        List<OcBOrderExtend> collect = dbList.parallelStream().map(ex -> {
            ex.setOrderType(orderTypeValues.get(ex.getOrderTypeName()));
            ex.setPayType(payTypeValues.get(ex.getPayTypeName()));
            ex.setIsInvoice(yesOrNoValues.get(ex.getIsInvoiceName()));
//            ex.setOccupyStatus(occupyStatusValues.get(ex.getOccupyStatusName()));
//            ex.setPlatform(platFormValues.get(ex.getPlatformName()));
//            ex.setWmsCancelStatus(wmsCanceStatusValues.get(ex.getWmsCancelStatusName()));
//            ex.setAutoAuditStatus(autoAuditStatusValues.get(ex.getAutoAuditStatusName()));
//            ex.setIsGeninvoiceNotice(isGeninvoiceNoticeValues.get(ex.getIsGeninvoiceNoticeName()));
//            ex.setReturnStatus(returnStatusValues.get(ex.getReturnStatusName()));
//            ex.setIsTodrp(ssToDrpOrderValues.get(ex.getIsTodrpName()));
//            //通用是否
//            ex.setIsMerge(yesOrNoValues.get(ex.getIsMergeName()));
//            ex.setIsSplit(yesOrNoValues.get(ex.getIsSplitName()));
//            ex.setIsInterecept(yesOrNoValues.get(ex.getIsIntereceptName()));
//            ex.setIsInreturning(yesOrNoValues.get(ex.getIsInreturningName()));
//            ex.setIsHasgift(yesOrNoValues.get(ex.getIsHasgiftName()));
//            ex.setIsGiveLogistic(yesOrNoValues.get(ex.getIsGiveLogisticName()));
//            ex.setIsInvented(yesOrNoValues.get(ex.getIsInventedName()));
//            ex.setIsCombination(yesOrNoValues.get(ex.getIsCombinationName()));
//            ex.setIsOutUrgency(yesOrNoValues.get(ex.getIsOutUrgencyName()));
//            ex.setIsShopCommission(yesOrNoValues.get(ex.getIsShopCommissionName()));
//            ex.setIsHasTicket(yesOrNoValues.get(ex.getIsHasTicketName()));
//            ex.setIsWriteoff(yesOrNoValues.get(ex.getIsWriteoffName()));
//            ex.setIsJcorder(yesOrNoValues.get(ex.getIsJcorderName()));
//            ex.setIsLackstock(yesOrNoValues.get(ex.getIsLackstockName()));
            return ex;
        }).collect(Collectors.toList());
    }

    //字段置换  导入文字转换成字段值
    public static void changeImportListTo(List<OcBOrderImpVO> dbList) {
        List<OcBOrderImpVO> collect = dbList.parallelStream().map(ex -> {
            ex.setOrderType(orderTypeValues.get(ex.getOrderTypeName()));
            if (StringUtils.isNotBlank(ex.getPayTypeName())) {
                ex.setPayType(payTypeValues.get(ex.getPayTypeName()));
            } else {
                // 如果为空则默认default 一头牛代码逻辑调整 20220911 (浩南)
                ex.setPayType(PayTypeEnum.ONLINE.getVal());
                ex.setPayTypeName(PayTypeEnum.ONLINE.getText());
            }
            ex.setIsInvoice(yesOrNoValues.get(ex.getIsInvoiceName()));
            return ex;
        }).collect(Collectors.toList());
    }

    //字段置换  导入文字转换成字段值
    public static void changePreImportListTo(List<OcBOrderPreImpVO> dbList) {
        List<OcBOrderPreImpVO> collect = dbList.parallelStream().map(ex -> {
            ex.setOrderType(orderTypeValues.get(ex.getOrderTypeName()));
            if (StringUtils.isNotBlank(ex.getPayTypeName())) {
                ex.setPayType(payTypeValues.get(ex.getPayTypeName()));
            } else {
                // 如果为空则默认default 一头牛代码逻辑调整 20220911 (浩南)
                ex.setPayType(PayTypeEnum.ONLINE.getVal());
                ex.setPayTypeName(PayTypeEnum.ONLINE.getText());
            }
            ex.setIsInvoice(yesOrNoValues.get(ex.getIsInvoiceName()));
            return ex;
        }).collect(Collectors.toList());
    }
}
