package com.jackrain.nea.oc.oms.model.enums;

/**
 * <AUTHOR>
 * @date ：2020/12/4 3:21 下午
 * description ：
 * @ Modified By：
 */
public enum TimeOrderOccupyItemStatusEnum {
    /**
     * 未确认
     */
    NOT_CONFIRM(1),
    /**
     * 缺货
     */
    OUT_OF_STOCK(2),
    /**
     * 占用库存成功
     */
    OCCUPY_SUCCESS(3),
    /**
     * 已匹配成功
     */
    MATCH_SUCCESS(4),
    /**
     * 已作废
     */
    VOID(5);

    int value;

    TimeOrderOccupyItemStatusEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
