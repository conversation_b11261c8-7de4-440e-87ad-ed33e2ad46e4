package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @program: r3-oc-oms
 * @description: 溯源配置表
 * @author: lijin
 * @create: 2024-12-19
 **/
@TableName(value = "st_c_traceability_configuration")
@Data
public class StCTraceabilityConfiguration extends BaseModel {
    private static final long serialVersionUID = 1L;

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 商品SKU
     */
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    /**
     * 所属组织
     */
    @JSONField(name = "AD_ORG_ID")
    private Long adOrgId;

    /**
     * 可用
     */
    @JSONField(name = "ISACTIVE")
    private String isactive;

    /**
     * 所属公司
     */
    @JSONField(name = "AD_CLIENT_ID")
    private Long adClientId;

    /**
     * 创建人
     */
    @JSONField(name = "OWNERID")
    private Long ownerid;

    /**
     * 创建人姓名
     */
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    /**
     * 创建人名称
     */
    @JSONField(name = "OWNERNAME")
    private String ownername;

    /**
     * 创建时间
     */
    @JSONField(name = "CREATIONDATE")
    private java.util.Date creationdate;

    /**
     * 修改人
     */
    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    /**
     * 修改人姓名
     */
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    /**
     * 修改人名称
     */
    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    /**
     * 修改时间
     */
    @JSONField(name = "MODIFIEDDATE")
    private java.util.Date modifieddate;
}
