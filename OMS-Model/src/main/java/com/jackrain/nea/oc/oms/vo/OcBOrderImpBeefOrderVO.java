package com.jackrain.nea.oc.oms.vo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Data
public class OcBOrderImpBeefOrderVO implements Serializable {

    private static final long serialVersionUID = 1L;
    public static final String cellStr = "cell_";
    public static final String rowStr = "row_";
    private static final String REG = "[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\u2600-\u27ff]";
    private Integer orderStatus;
    private String billNo;
    private String shopCode;
    private String sourceCode;
    //子订单编号
    private String ooid;
    private String businessTypeCode;
    // 订单类型
    private String orderTypeName;
    private String receiverName;
    private String receiverMobile;
    private String cpCRegionProvinceEname;
    private String cpCRegionCityEname;
    private String cpCRegionAreaEname;
    private String receiverAddress;
    private String oaid;
    private String cpCPhyWarehouseEname;
    private String expresscode;
    private String cpCLogisticsEcode;
    private String buyerMessage;
    private String sellerMemo;
    private String psCSkuEcode;
    private BigDecimal qty;
    private String giftType;
    //成交单价
    private BigDecimal priceActual;
    private BigDecimal realAmt;
    private BigDecimal amtDiscount;
    private BigDecimal orderSplitAmt;
    //时间
    private String orderDateStr;
    private Date orderDate;
    private String payTimeStr;
    private Date payTime;
    private String scanTimeStr;
    private Date scanTime;

    //行号
    private int rowNum;
    //错误信息
    private String desc;


    /**
     * 导入生成模型
     *
     * @return
     */
    public static OcBOrderImpBeefOrderVO importCreate(int index, OcBOrderImpBeefOrderVO ocBOrderImpVo, Map<String, String> columnMap) {
        //JY单号
        String billNo = columnMap.get(rowStr + index + cellStr + 0);
        ocBOrderImpVo.setBillNo(billNo.replace("JY", "OM"));
        //下单店铺shopEcode
        ocBOrderImpVo.setShopCode(columnMap.get(rowStr + index + cellStr + 1));
        //平台单号
        ocBOrderImpVo.setSourceCode(columnMap.get(rowStr + index + cellStr + 2));
        //子订单号
        ocBOrderImpVo.setOoid(columnMap.get(rowStr + index + cellStr + 3));
        //业务类型
        ocBOrderImpVo.setBusinessTypeCode(columnMap.get(rowStr + index + cellStr + 4));
        //订单类型
        ocBOrderImpVo.setOrderTypeName(columnMap.get(rowStr + index + cellStr + 5));
        //收货人
        String receiverName = columnMap.get(rowStr + index + cellStr + 6);
        //过滤特殊字符和表情
        if (StringUtils.isNotBlank(receiverName)) {
            receiverName = filterSpecialStr(receiverName);
            receiverName = filterEmoji(receiverName);
        }
        ocBOrderImpVo.setReceiverName(receiverName);
        //收货人手机
        ocBOrderImpVo.setReceiverMobile(columnMap.get(rowStr + index + cellStr + 7));
        //收货人省份
        ocBOrderImpVo.setCpCRegionProvinceEname(columnMap.get(rowStr + index + cellStr + 8));
        //收货人市
        ocBOrderImpVo.setCpCRegionCityEname(columnMap.get(rowStr + index + cellStr + 9));
        //收货人区
        ocBOrderImpVo.setCpCRegionAreaEname(columnMap.get(rowStr + index + cellStr + 10));
        //收货人地址
        String receiverAddress = columnMap.get(rowStr + index + cellStr + 11);
        //过滤特殊字符和表情
        if (StringUtils.isNotBlank(receiverAddress)) {
            receiverAddress = filterSpecialStr(receiverAddress);
            receiverAddress = filterEmoji(receiverAddress);
        }
        ocBOrderImpVo.setReceiverAddress(receiverAddress);
        //OAID
        ocBOrderImpVo.setOaid(columnMap.get(rowStr + index + cellStr + 12));
        //仓库
        ocBOrderImpVo.setCpCPhyWarehouseEname(columnMap.get(rowStr + index + cellStr + 13));
        //快递单号
        ocBOrderImpVo.setExpresscode(columnMap.get(rowStr + index + cellStr + 14));
        //快递公司
        ocBOrderImpVo.setCpCLogisticsEcode(columnMap.get(rowStr + index + cellStr + 15));
        //买家备注
        ocBOrderImpVo.setBuyerMessage(columnMap.get(rowStr + index + cellStr + 16));
        //卖家备注
        ocBOrderImpVo.setSellerMemo(columnMap.get(rowStr + index + cellStr + 17));
        //商品编码
        ocBOrderImpVo.setPsCSkuEcode(columnMap.get(rowStr + index + cellStr + 18));
        //商品数量
        String qty = columnMap.get(rowStr + index + cellStr + 19);
        if (StringUtils.isNotBlank(qty)) {
            ocBOrderImpVo.setQty(new BigDecimal(qty));
        }
        //赠品类型
        ocBOrderImpVo.setGiftType(columnMap.get(rowStr + index + cellStr + 20));
        //成交单价
        String priceActual = columnMap.get(rowStr + index + cellStr + 21);
        if (StringUtils.isNotBlank(priceActual)) {
            ocBOrderImpVo.setPriceActual(new BigDecimal(priceActual));
        }
        // 成交金额
        String realAmt = columnMap.get(rowStr + index + cellStr + 22);
        if (StringUtils.isNotBlank(realAmt)) {
            ocBOrderImpVo.setRealAmt(new BigDecimal(realAmt));
        }
        // 商品优惠金额
        String amtDiscount = columnMap.get(rowStr + index + cellStr + 23);
        if (StringUtils.isNotBlank(amtDiscount)) {
            ocBOrderImpVo.setAmtDiscount(new BigDecimal(amtDiscount));
        }
        // 平摊金额
        String orderSplitAmt = columnMap.get(rowStr + index + cellStr + 24);
        if (StringUtils.isNotBlank(orderSplitAmt)) {
            ocBOrderImpVo.setOrderSplitAmt(new BigDecimal(orderSplitAmt));
        }
        // 下单时间
        ocBOrderImpVo.setOrderDateStr(columnMap.get(rowStr + index + cellStr + 25));
        // 支付时间
        ocBOrderImpVo.setPayTimeStr(columnMap.get(rowStr + index + cellStr + 26));
        // 出库时间
        ocBOrderImpVo.setScanTimeStr(columnMap.get(rowStr + index + cellStr + 27));
        ocBOrderImpVo.setRowNum(index + 1);
        return ocBOrderImpVo;
    }

    /**
     * 过滤特殊字符
     *
     * @param str
     * @return
     */
    private static String filterSpecialStr(String str) {
        String regEx = "[`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.replaceAll("-").trim();
    }

    /**
     * 过滤表情
     *
     * @param source
     * @return
     */
    private static String filterEmoji(String source) {
        if (source != null) {
            Pattern emoji = Pattern.compile(REG, Pattern.UNICODE_CASE | Pattern.CASE_INSENSITIVE);
            Matcher emojiMatcher = emoji.matcher(source);
            if (emojiMatcher.find()) {
                source = emojiMatcher.replaceAll("");
                return source;
            }
            return source;
        }
        return source;
    }
}
