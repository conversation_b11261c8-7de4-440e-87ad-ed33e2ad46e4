package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * @Auther: 黄志优
 * @Date: 2020/9/16 15:59
 * @Description:
 */


public enum OmsOrderItemProType {
    STANDPLAT_SUBSTITUTION("正常", 0),
    LUCKY_BAG("福袋", 1),
    COMBINATION("组合", 2),
    ADVANCE_SALE("预售", 3),
    UNDIVIDED_PORTFOLIO_GOODS("未拆分的组合商品", 4);

    OmsOrderItemProType(String key, int val) {
        this.key = key;
        this.val = val;
    }

    @Getter
    String key;

    @Getter
    int val;
    //商品类型(0:正常,1:福袋,2:组合,3:预售;4:未拆分的组合商品;)
}
