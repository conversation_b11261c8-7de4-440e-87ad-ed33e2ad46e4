package com.jackrain.nea.oc.oms.model.enums;

/**
 * 退换货退款原因
 *
 * @author: 郑立轩
 * @since: 2019/4/8
 * create at : 2019/4/8 16:47
 */
public enum RefundReasonEnum {
    //商品原因
    productReason("商品与页面描述不符", "1"),
    //商品损坏
    productFailure("商品损坏/包装脏污", "2"),
    //商品尺寸
    productSize("少/错商品", "3"),
    //客服原因
    serviceReason("发错货", "4"),
    //客服服务差
    service("其他", "5"),
    //客服不理会
    serviceIgnored("价格变化", "6"),
    //拒收
    refuse("买多/买错/不满意", "7"),
    //维修
    fix("质量问题", "8"),
    wontNeed("我不想要了", "9");

    String key;
    String value;

    RefundReasonEnum(String key, String value) {
        this.key = key;
        this.value = value;

    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return value;
    }


}
