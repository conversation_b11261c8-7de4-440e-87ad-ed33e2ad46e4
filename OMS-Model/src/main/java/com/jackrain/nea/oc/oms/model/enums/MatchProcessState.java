package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Desc : 匹配相关业务场景流程枚举
 * <AUTHOR> xiWen
 * @Date : 2022/7/26
 */
public enum MatchProcessState {

    /**
     * 初始状态
     */
    INIT(0),

    /**
     * b2c
     * 正常入库匹配
     */
    NORM2C(1),

    /**
     * b2c
     * 无名件.异常包裹 匹配
     */
    NAMELESS(2),

    /**
     * 调整单
     */
    ADJUST(3),

    /**
     * minus adjust
     * 负向调整单, 冲调整单
     */
    MINUS(4),

    /**
     * b2b
     * b2b 单据匹配
     */
    NORM2B(11),

    /**
     * finish
     */
    FINISH(20),

    /**
     * not deal
     * 无需处理, 单据已经匹配完成
     */
    UNNECESSARY(30);

    private Integer level;

    MatchProcessState(Integer level) {
        this.level = level;
    }

}
