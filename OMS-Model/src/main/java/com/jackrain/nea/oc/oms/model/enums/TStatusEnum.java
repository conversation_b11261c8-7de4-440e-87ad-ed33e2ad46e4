package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: gxx
 * @since: 2020-05-28
 * create at : 2020-05-28 14:46
 */
public enum TStatusEnum {


    // 显示名称, 实际字段,类型, 序号
    WAIT_SELLER_SEND_GOODS("WAIT_SELLER_SEND_GOODS", "等待卖家发货"),
    SELLER_CONSIGNED_PART("SELLER_CONSIGNED_PART", "卖家部分发货"),
    WAIT_BUYER_PAY("WAIT_BUYER_PAY", "待买家付款"),
    TRADE_FINISHED("TRADE_FINISHED", "完成"),
    TRADE_CLOSED("TRADE_CLOSED", "交易关闭"),
    TRADE_CLOSED_BY_TAOBAO("TRADE_CLOSED_BY_TAOBAO", "交易被淘宝关闭"),
    WAIT_BUYER_CONFIRM_GOODS("WAIT_BUYER_CONFIRM_GOODS", "等待买家确认收货"),
    REFUND_FINISHED("REFUND_FINISHED", "退款完成");


    String key;
    String val;

    TStatusEnum(String k, String v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public String getVal() {
        return val;
    }

    public static String getValueByKey(String key) {

        String s = "";
        if (key == null) {
            return s;
        }
        for (TStatusEnum e : TStatusEnum.values()) {
            if (e.getKey().equals(key)) {
                s = e.getVal();
                break;
            }
        }
        return s;
    }

    public static Map<String, String> getMap() {
        Map<String, String> m = new HashMap<>();
        for (TStatusEnum o : TStatusEnum.values()) {
            m.put(o.getKey(), o.getVal());
        }
        m.put(null, "");
        return m;
    }

    /**
     * check box 文本框查询
     *
     * @return 平台订单状态集
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (TStatusEnum e : TStatusEnum.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getVal());
            o.setValue(e.getKey());
            list.add(o);
        }
        return list;
    }

}