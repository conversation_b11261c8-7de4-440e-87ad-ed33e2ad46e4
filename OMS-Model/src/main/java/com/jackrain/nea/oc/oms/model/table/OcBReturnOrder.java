package com.jackrain.nea.oc.oms.model.table;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_return_order", type = "oc_b_return_order")
@ApiModel(value = "oc_b_return_order", description = "退换货单")
public class OcBReturnOrder extends BaseModel {

    private static final long serialVersionUID = -2427498934392956347L;
    @ApiModelProperty(value = "退款单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "BILL_NO")
    private String billNo;

    @ApiModelProperty(value = "原平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "强制入库")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_FORCE")
    private Integer isForce;

    @ApiModelProperty(value = "入库仓库id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_STORE_ID")
    private Long cpCStoreId;

    @ApiModelProperty(value = "入库仓库编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_STORE_ECODE")
    private String cpCStoreEcode;

    @ApiModelProperty(value = "入库仓库名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_STORE_ENAME")
    private String cpCStoreEname;

    @ApiModelProperty(value = "订单来源1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORDE_SOURCE")
    private String ordeSource;

    @ApiModelProperty(value = "WMS撤回状态 0:未撤回,1:撤回成功,2:撤回失败")
    @Field(type = FieldType.Integer)
    @JSONField(name = "WMS_CANCEL_STATUS")
    private Integer wmsCancelStatus;

    @ApiModelProperty(value = "退单标识")
    @Field(type = FieldType.Integer)
    @JSONField(name = "RETURN_FLAG")
    private Integer returnFlag;

    @ApiModelProperty(value = "系统提示信息")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SYS_MSG")
    private String sysMsg;

    @ApiModelProperty(value = "是否确认收货 1:是,0:否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_RECEIVE_CONFIRM")
    private Integer isReceiveConfirm;

    @ApiModelProperty(value = "淘宝换货平台单号")
    @Field(type = FieldType.Long)
    @JSONField(name = "TB_DISPUTE_ID", serializeUsing = ToStringSerializer.class)
    private Long tbDisputeId;

    @ApiModelProperty(value = "传wms失败原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "WMS_FAILREASON")
    private String wmsFailreason;

    @ApiModelProperty(value = "传WMS状态 0:未传WMS,1:传WMS中,2:传WMS成功,3:传WMS失败")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_TOWMS")
    private Integer isTowms;

    @ApiModelProperty(value = "是否有运单号 1:是,0:否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_NOTLOGMBER")
    private Integer isNotlogmber;

    @ApiModelProperty(value = "是否入仓成功 1:是,0:否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_INSTORAGE")
    private Integer isInstorage;

    @ApiModelProperty(value = "是否提交审核 1:是,0:否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_EXAMINE")
    private Integer isExamine;

    @ApiModelProperty(value = "匹配失败信息")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CHECK_FAILE_INFO")
    private String checkFaileInfo;

    @ApiModelProperty(value = "是否已匹配 1:是,0:否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_CHECK")
    private Integer isCheck;

    @ApiModelProperty(value = "是否拒收 1:是,0:否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_REFUND")
    private Integer isRefund;

    @ApiModelProperty(value = "是否生成调拨单 1:是,0:否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_TRANSFER")
    private Integer isTransfer;

    @ApiModelProperty(value = "入库人")
    @Field(type = FieldType.Long)
    @JSONField(name = "INER_ID")
    private Long inerId;

    @ApiModelProperty(value = "入库人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "INER_ENAME")
    private String inerEname;

    @ApiModelProperty(value = "入库人用户名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "INER_NAME")
    private String inerName;

    @ApiModelProperty(value = "审核人")
    @Field(type = FieldType.Long)
    @JSONField(name = "CHECKER_ID")
    private Long checkerId;

    @ApiModelProperty(value = "审核人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CHECKER_ENAME")
    private String checkerEname;

    @ApiModelProperty(value = "审核人用户名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CHECKER_NAME")
    private String checkerName;

    @ApiModelProperty(value = "入库时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "IN_TIME")
    private Date inTime;

    @ApiModelProperty(value = "审核时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "AUDIT_TIME")
    private Date auditTime;

    @ApiModelProperty(value = "商品数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_INSTORE")
    private BigDecimal qtyInstore;

    @ApiModelProperty(value = "售中/售后")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RETURN_PHASE")
    private String returnPhase;

    @ApiModelProperty(value = "所有sku")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ALL_SKU")
    private String allSku;

    @ApiModelProperty(value = "超时时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "OVERTIME_INTERVAL")
    private Long overtimeInterval;

    @ApiModelProperty(value = "旗帜")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORDERFLAG")
    private String orderflag;

    @ApiModelProperty(value = "换货预留库存 1:是,0:否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_RESERVED")
    private Integer isReserved;

    @ApiModelProperty(value = "分销商编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "DISTRIBUTOR_ID")
    private Long distributorId;

    @ApiModelProperty(value = "实体仓库")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @ApiModelProperty(value = "入库实体仓库")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_PHY_WAREHOUSE_IN_ID")
    private Long cpCPhyWarehouseInId;

    @ApiModelProperty(value = "卖家昵称（店铺）")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SELLER_NICK")
    private String sellerNick;

    @ApiModelProperty(value = "下单用户编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "USERID")
    private Long userid;

    @ApiModelProperty(value = "虚拟入库状态 0:未虚拟入库,1:虚拟入库未入库,2:虚拟入库已入库")
    @Field(type = FieldType.Integer)
    @JSONField(name = "INVENTED_STATUS")
    private Integer inventedStatus;

    @ApiModelProperty(value = "平台类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PLATFORM")
    private Integer platform;

    @ApiModelProperty(value = "换货邮费")
    @Field(type = FieldType.Double)
    @JSONField(name = "SHIP_AMT")
    private BigDecimal shipAmt;

    @ApiModelProperty(value = "原始订单ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "ORIG_ORDER_ID")
    private Long origOrderId;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORIG_ORDER_NO")
    private String origOrderNo;

    @ApiModelProperty(value = "换货收货人")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVE_NAME")
    private String receiveName;

    @ApiModelProperty(value = "买家所在区名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_AREA_NAME")
    private String receiverAreaName;

    @ApiModelProperty(value = "买家所在市名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_CITY_NAME")
    private String receiverCityName;

    @ApiModelProperty(value = "买家所在省名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVER_PROVINCE_NAME")
    private String receiverProvinceName;

    @ApiModelProperty(value = "平台退款单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RETURN_ID")
    private String returnId;

    @ApiModelProperty(value = "换货金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "EXCHANGE_AMT")
    private BigDecimal exchangeAmt;

    @ApiModelProperty(value = "退款金额区域")
    @Field(type = FieldType.Long)
    @JSONField(name = "RECEIVER_AREA_ID")
    private Long receiverAreaId;

    @ApiModelProperty(value = "换货收货人市")
    @Field(type = FieldType.Long)
    @JSONField(name = "RECEIVER_CITY_ID")
    private Long receiverCityId;

    @ApiModelProperty(value = "换货收货人省份")
    @Field(type = FieldType.Long)
    @JSONField(name = "RECEIVER_PROVINCE_ID")
    private Long receiverProvinceId;

    @ApiModelProperty(value = "备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REMARK")
    private String remark;

    @ApiModelProperty(value = "退回物流单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "LOGISTICS_CODE")
    private String logisticsCode;

    @ApiModelProperty(value = "换货收货人电话")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVE_PHONE")
    private String receivePhone;

    @ApiModelProperty(value = "换货收货人手机 ")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVE_MOBILE")
    private String receiveMobile;

    @ApiModelProperty(value = "换货收货人邮编 ")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVE_ZIP")
    private String receiveZip;

    @ApiModelProperty(value = "换货收货人地址")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RECEIVE_ADDRESS")
    private String receiveAddress;

    @ApiModelProperty(value = "退回物流公司id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @ApiModelProperty(value = "退回物流公司编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    private String cpCLogisticsEcode;

    @ApiModelProperty(value = "退回物流公司名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    private String cpCLogisticsEname;

    @ApiModelProperty(value = "退款说明")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RETURN_DESC")
    private String returnDesc;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "RETURN_REASON")
    private String returnReason;

    @ApiModelProperty(value = "买家昵称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUYER_NICK")
    private String buyerNick;

    @ApiModelProperty(value = "原始平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORIG_SOURCE_CODE")
    private String origSourceCode;

    @ApiModelProperty(value = "退还其他费用")
    @Field(type = FieldType.Double)
    @JSONField(name = "RETURN_AMT_OTHER")
    private BigDecimal returnAmtOther;

    @ApiModelProperty(value = "退还运费")
    @Field(type = FieldType.Double)
    @JSONField(name = "RETURN_AMT_SHIP")
    private BigDecimal returnAmtShip;

    @ApiModelProperty(value = "商品应退金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "RETURN_AMT_LIST")
    private BigDecimal returnAmtList;

    @ApiModelProperty(value = "退款金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "RETURN_AMT_ACTUAL")
    private BigDecimal returnAmtActual;

    @ApiModelProperty(value = "货物退回时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "RETURN_TIME")
    private Date returnTime;

    @ApiModelProperty(value = "退款平台上最后修改时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "LAST_UPDATE_TIME")
    private Date lastUpdateTime;

    @ApiModelProperty(value = "退款创建时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "RETURN_CREATE_TIME")
    private Date returnCreateTime;

    @Field(type = FieldType.Integer)
    @JSONField(name = "RETURN_STATUS")
    private Integer returnStatus;

    @ApiModelProperty(value = "原单状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "ORIG_ORDER_STATUS")
    private Integer origOrderStatus;

    @Field(type = FieldType.Integer)
    @JSONField(name = "BILL_TYPE")
    private Integer billType;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_ADD")
    private Integer isAdd;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_TODRP")
    private Integer isTodrp;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_TOAG")
    private Integer isToag;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_BACK")
    private Integer isBack;

    @ApiModelProperty(value = "平台店铺id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @ApiModelProperty(value = "店铺编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @ApiModelProperty(value = "平台店铺标题")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @ApiModelProperty(value = "代销结算金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "CONSIGN_AMT_SETTLE")
    private BigDecimal consignAmtSettle;

    @ApiModelProperty(value = "退货类型 客退 0，拦截 1，拒收 2")
    @Field(type = FieldType.Integer)
    @JSONField(name = "RETURN_PRO_TYPE")
    private Integer returnProType;

    @ApiModelProperty(value = "退款类型")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RETURN_TYPE")
    private String returnType;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_MANUAL_AUDIT")
    private Integer isManualAudit;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @ApiModelProperty(value = "是否插入核销流水")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_WRITEOFF")
    private Integer isWriteoff;

    @ApiModelProperty(value = "门店档案的门店编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STORE_CODE")
    private String storeCode;

    @ApiModelProperty(value = "门店档案的门店名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STORE_NAME")
    private String storeName;

    @ApiModelProperty(value = "门店档案的门店id")
    @Field(type = FieldType.Long)
    @JSONField(name = "STORE_ID")
    private Long storeId;

    @ApiModelProperty(value = "门店档案的结算组织名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_ORG_NAME")
    private String settleOrgName;

    @ApiModelProperty(value = "门店档案的结算组织编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_ORG_CODE")
    private String settleOrgCode;

    @ApiModelProperty(value = "门店档案的结算供应商编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_SUPPLIER_CODE")
    private String settleSupplierCode;

    @ApiModelProperty(value = "门店档案的结算供应商名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SETTLE_SUPPLIER_NAME")
    private String settleSupplierName;

    @ApiModelProperty(value = "传WMS失败次数")
    @Field(type = FieldType.Long)
    @JSONField(name = "QTY_WMS_FAIL")
    private Long qtyWmsFail;

    @ApiModelProperty(value = "待传结算标志（0:默认，1:待传，2：已传，3:失败）")
    @Field(type = FieldType.Long)
    @JSONField(name = "TO_SETTLE_STATUS")
    private Long toSettleStatus;

    @ApiModelProperty(value = "退货入库单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_REFUND_IN_ID")
    private Long ocBRefundInId;

    @Field(type = FieldType.Long)
    @JSONField(name = "STATUS_FUNDS")
    private Long statusFunds;

    @Field(type = FieldType.Long)
    @JSONField(name = "STATUS_DEFECTIVE_TRANS")
    private Long statusDefectiveTrans;

    @ApiModelProperty(value = "oms传sap补偿状态")
    @Field(type = FieldType.Long)
    @JSONField(name = "TO_SAP_STATUS")
    private Long toSapStatus;

    @ApiModelProperty(value = "传互道状态（0:未传，1:传送中，2：已传，3:失败, 4:传送失败)")
    @Field(type = FieldType.Long)
    @JSONField(name = "TO_THIRD_SYS_STATUS")
    private Long toThirdSysStatus;

    @ApiModelProperty(value = "传互道失败次数")
    @Field(type = FieldType.Long)
    @JSONField(name = "TO_THIRD_SYS_FAIL_TIMES")
    private Long toThirdSysFailTimes;

    @ApiModelProperty(value = "传sap次数")
    @Field(type = FieldType.Double)
    @JSONField(name = "TO_SAP_TIMES")
    private BigDecimal toSapTimes;

    @ApiModelProperty(value = "代销资金处理次数")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_FUNDS_FAIL")
    private BigDecimal qtyFundsFail;

    @ApiModelProperty(value = "WMS单据编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "WMS_BILL_NO")
    private String wmsBillNo;

    @ApiModelProperty(value = "退回说明")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BACK_MESSAGE")
    private String backMessage;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORIG_SELLER_REMARK")
    private String origSellerRemark;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORIG_BUYER_MESSAGE")
    private String origBuyerMessage;

    @ApiModelProperty(value = "是否生成换货单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_RETURN_ORDER_EXCHANGE")
    private Integer isReturnOrderExchange;

    @ApiModelProperty(value = "原单是否爆品下沉")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_HOT_SINK")
    private String isHotSink;

    @ApiModelProperty(value = "包裹拦截状态 未发起拦截 0、发起拦截成功 1、发起拦截失败 2、配送拦截成功 3、配送拦截失败 4")
    @Field(type = FieldType.Integer)
    @JSONField(name = "INTERCERPT_STATUS")
    private Integer intercerptStatus;

    @ApiModelProperty(value = "卖家备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SELLER_MEMO")
    private String sellerMemo;

    @ApiModelProperty(value = " 退货状态（0待入库 1 部分入库 2 全部入库）")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PRO_RETURN_STATUS")
    private Integer proReturnStatus;

    @ApiModelProperty(value = "第三方仓库类型 0 菜鸟 1 京东 2 其它")
    @Field(type = FieldType.Integer)
    @JSONField(name = "THIRD_WAREHOUSE_TYPE")
    private Integer thirdWarehouseType;

    @ApiModelProperty(value = "是否无名件匹配 1:是,0:否")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_ANONYMOUS")
    private Integer isAnonymous;

    @ApiModelProperty(value = "是否需要传递给WMS。0-不传WMS；1=传WMS")
    @Field(type = FieldType.Long)
    @JSONField(name = "IS_NEED_TO_WMS")
    private Long isNeedToWms;

    @ApiModelProperty(value = "数字类型备用字段1")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @ApiModelProperty(value = "数字类型备用字段2")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02;

    @ApiModelProperty(value = "数字类型备用字段3")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @ApiModelProperty(value = "数字类型备用字段4")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @ApiModelProperty(value = "数字类型备用字段5")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @ApiModelProperty(value = "价格备用字段1")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "价格备用字段3")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @ApiModelProperty(value = "价格备用字段4")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @ApiModelProperty(value = "价格备用字段5")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @ApiModelProperty(value = "文本备用字段1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @ApiModelProperty(value = "文本备用字段2")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @ApiModelProperty(value = "文本备用字段3")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @ApiModelProperty(value = "文本备用字段4")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @ApiModelProperty(value = "文本备用字段5")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;

    @ApiModelProperty(value = "空运单号延迟推单有效时间")
    @Field(type = FieldType.Long)
    @JSONField(name = "PUSH_DELAY_TIME")
    private Date pushDelayTime;

    @ApiModelProperty(value = "下单店仓性质")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STORE_NATURE")
    private String storeNature;


    /**
     * 0-未传DRP；1-传DRP中；2-传DRP成功；3-传DRP失败
     */
    @ApiModelProperty(value = "传DRP状态")
    @JSONField(name = "TO_DRP_STATUS")
    @Field(type = FieldType.Keyword)
    private String toDrpStatus;

    @ApiModelProperty(value = "传DRP次数")
    @JSONField(name = "TO_DRP_COUNT")
    @Field(type = FieldType.Integer)
    private Integer toDrpCount;

    @ApiModelProperty(value = "传DRP失败原因")
    @JSONField(name = "TO_DRP_FAILED_REASON")
    @Field(type = FieldType.Keyword)
    private String toDrpFailedReason;

    @ApiModelProperty(value = "已经存在由退单生成的退货入库单ID")
    @JSONField(name = "EXIST_REFUND_IN_ID")
    @Field(type = FieldType.Long)
    private Long existRefundInId;

    @ApiModelProperty(value = "淘宝收货人信息加密串")
    @JSONField(name = "OAID")
    @Field(type = FieldType.Keyword)
    private String oaid;

    @ApiModelProperty(value = "传第三方错误类型")
    @JSONField(name = "THIRD_PARTY_FAIL_STATUS")
    @Field(type = FieldType.Keyword)
    private String thirdPartyFailStatus;

    @ApiModelProperty(value = "wing将出库信息推送wms的时间", required = true)
    @Field(type = FieldType.Long)
    @JSONField(name = "IN_WING_TO_WMS_TIME")
    private Date inWingToWmsTime;

    @ApiModelProperty(value = "wms接收出库信息的时间", required = true)
    @Field(type = FieldType.Long)
    @JSONField(name = "IN_WMS_RECEIVE_TIME")
    private Date inWmsReceiveTime;

    @ApiModelProperty(value = "wms实际出库后告知wing的时间", required = true)
    @Field(type = FieldType.Long)
    @JSONField(name = "IN_WMS_OUT_TIME")
    private Date inWmsOutTime;

    @ApiModelProperty(value = "wing接收实际出库信息并通知中台的时间", required = true)
    @JSONField(name = "IN_WING_TO_SG_TIME")
    @Field(type = FieldType.Long)
    private Date inWingToSgTime;


    @ApiModelProperty(value = "自动审核失败次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "AUDIT_FAIL")
    private Integer auditFail;


    @ApiModelProperty(value = "是否已推送TMS追踪物流信息：0-否；1-是")
    @JSONField(name = "IS_SEND_TMS_LOGISTIC")
    @Field(type = FieldType.Keyword)
    private String isSendTmsLogistic;

    @ApiModelProperty(value = "物流状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "LOGISTICS_STATUS")
    private String logisticsStatus;

    @ApiModelProperty(value = "是否同意退款")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "AGREE_REFUND")
    private String agreeRefund;

    @ApiModelProperty(value = "退款原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REFUND_REASON")
    private String refundReason;

    @ApiModelProperty(value = "物流轨迹订阅失败次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "LOGISTICS_TRACE_FAIL_NUM")
    private Integer logisticsTraceFailNum;

    @ApiModelProperty(value = "订单业务类型id")
    @Field(type = FieldType.Long)
    @JSONField(name = "BUSINESS_TYPE_ID")
    private Long businessTypeId;

    @ApiModelProperty(value = "业务类型编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUSINESS_TYPE_CODE")
    private String businessTypeCode;

    @ApiModelProperty(value = "业务类型名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUSINESS_TYPE_NAME")
    private String businessTypeName;

    @ApiModelProperty(value = "确认时间")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CONFIRM_DATE")
    private Date confirmDate;

    @ApiModelProperty(value = "确认人")
    @Field(type = FieldType.Long)
    @JSONField(name = "CONFIRM_ID")
    private Long confirmId;

    @ApiModelProperty(value = "确认人用户名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CONFIRM_NAME")
    private String confirmName;

    @ApiModelProperty(value = "确认状态 0-未确认，1-已确认")
    @Field(type = FieldType.Integer)
    @JSONField(name = "CONFIRM_STATUS")
    private String confirmStatus;

    @ApiModelProperty(value = "入库通知单单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STO_IN_NOTICES_NO")
    private String stoInNoticesNo;

    @ApiModelProperty(value = "同步奶卡状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "TO_NAIKA_STATUS")
    private Integer toNaikaStatus;

    @ApiModelProperty(value = "入库通知单ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "STO_IN_NOTICES_ID")
    private Long stoInNoticesId;

    @ApiModelProperty(value = "原始单据编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORIGINAL_BILL_NO")
    private String originalBillNo;

    @ApiModelProperty(value = "成本中心")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "COST_CENTER")
    private String costCenter;

    @ApiModelProperty(value = "销售组织")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SALES_ORGANIZE")
    private String salesOrganize;

    @ApiModelProperty(value = "是否错收")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_WRONG_RECEIVE")
    private Integer isWrongReceive;

    @ApiModelProperty(value = "终止状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TERMINATION_TYPE")
    private String terminationType;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "DELIVERY_ORDER_NO")
    private String deliveryOrderNo;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "GW_SOURCE_CODE")
    private String gwSourceCode;

    @ApiModelProperty(value = "同步平台退款状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PLATFORM_REFUND_STATUS")
    private Integer platformRefundStatus;

    @ApiModelProperty(value = "买家备注")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BUYER_REMARK")
    private String buyerRemark;

    @ApiModelProperty(value = "终止时间")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TERMINATION_DATE")
    private Date terminationDate;

    @ApiModelProperty(value = "财务是否预处理,N:未处理;Y:已处理")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_CONFIRM_PRE")
    private String isConfirmPre;

    @ApiModelProperty(value = "对账单id")
    @Field(type = FieldType.Long)
    @JSONField(name = "CONFIRM_BILL_ID")
    private Long confirmBillId;

    @ApiModelProperty(value = "买家街道/乡镇名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CP_C_REGION_TOWN_ENAME")
    private String cpCRegionTownEname;

    /**
     * {@link com.jackrain.nea.oc.oms.model.enums.ReturnOrderDealTypeEnum}
     */
    @ApiModelProperty(value = "处理类型")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DEAL_TYPE")
    private String dealType;

    @ApiModelProperty(value = "通用标记")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GENERIC_MARK")
    private String genericMark;

    @ApiModelProperty(value = "取消原因")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "CANCEL_REASON")
    private String cancelReason;

    @ApiModelProperty(value = "签收状态：0-未签收，1-已签收，2-已退签")
    @Field(type = FieldType.Integer)
    @JSONField(name = "SIGNING_STATUS")
    private String signingStatus;

    @ApiModelProperty(value = "签收时间")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SIGNING_TIME")
    private Date signingTime;

    @ApiModelProperty(value = "是否订阅成功：0-未订阅，1-订阅成功，2-订阅失败")
    @Field(type = FieldType.Integer)
    @JSONField(name = "SUBSCRIPTION_STATUS")
    private String subscriptionStatus;

    @ApiModelProperty(value = "要求入库时间")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REQUIRED_STORAGE_TIME")
    private Date requiredStorageTime;

    @ApiModelProperty(value = "是否超时入库：0-未超时，1-已超时")
    @Field(type = FieldType.Integer)
    @JSONField(name = "OVERDUE_STORAGE_STATUS")
    private String overdueStorageStatus;

    @ApiModelProperty(value = "应退和实退差异比例")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "DIFFERENCE_RATIO")
    private String differenceRatio;

    @ApiModelProperty(value = "应退和实退差异金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "DIFFERENCE_AMOUNT")
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "应退和实退差异标记")
    @Field(type = FieldType.Integer)
    @JSONField(name = "DIFFERENCE_MARK")
    private Integer differenceMark;
}