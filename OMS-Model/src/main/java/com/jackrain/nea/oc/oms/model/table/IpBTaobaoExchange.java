package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_taobao_exchange")
@Data
@Document(index = "ip_b_taobao_exchange", type = "ip_b_taobao_exchange")
public class IpBTaobaoExchange extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "DISPUTE_ID")
    @Field(type = FieldType.Long)
    private Long disputeId;

    @JSONField(name = "BIZ_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long bizOrderId;

    @JSONField(name = "BUYER_LOGISTIC_NO")
    @Field(type = FieldType.Keyword)
    private String buyerLogisticNo;

    @JSONField(name = "SELLER_LOGISTIC_NO")
    @Field(type = FieldType.Keyword)
    private String sellerLogisticNo;

    @JSONField(name = "DESCRIPTION")
    @Field(type = FieldType.Keyword)
    private String description;

    @JSONField(name = "REASON")
    @Field(type = FieldType.Keyword)
    private String reason;

    @JSONField(name = "ADDRESS")
    @Field(type = FieldType.Keyword)
    private String address;

    @JSONField(name = "BUYER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String buyerAddress;

    @JSONField(name = "SELLER_LOGISTIC_NAME")
    @Field(type = FieldType.Keyword)
    private String sellerLogisticName;

    @JSONField(name = "BUYER_LOGISTIC_NAME")
    @Field(type = FieldType.Keyword)
    private String buyerLogisticName;

    @JSONField(name = "BUYER_PHONE")
    @Field(type = FieldType.Keyword)
    private String buyerPhone;

    @JSONField(name = "REFUND_PHASE")
    @Field(type = FieldType.Keyword)
    private String refundPhase;

    @JSONField(name = "TITLE")
    @Field(type = FieldType.Keyword)
    private String title;

    @JSONField(name = "CREATED")
    @Field(type = FieldType.Long)
    private Date created;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    @JSONField(name = "ADVANCE_STATUS")
    @Field(type = FieldType.Long)
    private Long advanceStatus;

    @JSONField(name = "BUYER_NICK")
    @Field(type = FieldType.Keyword)
    private String buyerNick;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    private String status;

    @JSONField(name = "REFUND_VERSION")
    @Field(type = FieldType.Long)
    private Long refundVersion;

    @JSONField(name = "QTY")
    @Field(type = FieldType.Long)
    private Long qty;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal price;

    @JSONField(name = "GOOD_STATUS")
    @Field(type = FieldType.Keyword)
    private String goodStatus;

    @JSONField(name = "CS_STATUS")
    @Field(type = FieldType.Long)
    private Long csStatus;

    @JSONField(name = "EXCHANGE_SKU")
    @Field(type = FieldType.Keyword)
    private String exchangeSku;

    @JSONField(name = "BOUGHT_SKU")
    @Field(type = FieldType.Keyword)
    private String boughtSku;

    @JSONField(name = "TIMEOUT")
    @Field(type = FieldType.Keyword)
    private String timeout;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertdate;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Keyword)
    private String istrans;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "EXCHANGE_OUTERID")
    @Field(type = FieldType.Keyword)
    private String exchangeOuterid;

    @JSONField(name = "BOUGHT_OUTERID")
    @Field(type = FieldType.Keyword)
    private String boughtOuterid;

    @JSONField(name = "TID")
    @Field(type = FieldType.Long)
    private Long tid;

    @JSONField(name = "BUYER_NAME")
    @Field(type = FieldType.Keyword)
    private String buyerName;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    /**
     * 自定义sku
     */
    @JSONField(name = "OUTER_SKU_ID")
    @Field(type = FieldType.Keyword)
    private String outerSkuId;

    /**
     * 占单处理结果
     */
    @JSONField(name = "OCCUPANCY_DISPOSE_RESULT")
    @Field(type = FieldType.Keyword)
    private String occupancyDisposeResult;

    /**
     * 占单处理状态
     */
    @JSONField(name = "OCCUPANCY_DISPOSE_STATUS")
    @Field(type = FieldType.Integer)
    private Integer occupancyDisposeStatus;

    /**
     * 占单状态
     */
    @JSONField(name = "OCCUPANCY_STATUS")
    @Field(type = FieldType.Integer)
    private Integer occupancyStatus;

    @JSONField(name = "OAID")
    @Field(type = FieldType.Keyword)
    private String oaid;
}