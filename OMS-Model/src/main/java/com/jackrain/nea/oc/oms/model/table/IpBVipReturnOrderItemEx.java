package com.jackrain.nea.oc.oms.model.table;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.ps.model.ProductSku;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date ：Created in 11:09 2020/5/11
 * description ：
 * @ Modified By：
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ip_b_vip_return_order_item")
@Data
public class IpBVipReturnOrderItemEx extends IpBVipReturnOrderItem {

    private static final long serialVersionUID = 6874879774333338296L;

    @TableField(exist = false)
    private ProductSku productSku;
}
