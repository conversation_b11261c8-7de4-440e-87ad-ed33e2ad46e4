package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName BnTaskData
 * @Description 班牛工单数据实体类
 * <AUTHOR>
 * @Date 2025/6/17 10:30
 * @Version 1.0
 */
@TableName(value = "bn_task_data")
@Data
@Document(index = "bn_task_data", type = "bn_task_data")
@NoArgsConstructor(access = AccessLevel.PUBLIC)
@ApiModel(value = "bn_task_data", description = "奶卡订单明细")
public class BnTaskData {
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id; // 主键ID

    @ApiModelProperty(value = "项目ID")
    @JSONField(name = "PROJECT_ID")
    private Long projectId; // 项目ID

    @ApiModelProperty(value = "班牛工单返回的JSON数据")
    @JSONField(name = "TASK_JSON")
    private String taskJson; // 班牛工单返回的JSON数据

    @ApiModelProperty(value = "处理状态: 0-未处理, 1-处理中, 2-已处理")
    @JSONField(name = "PROCESS_STATUS")
    private Integer processStatus; // 处理状态

    @ApiModelProperty(value = "创建时间")
    @JSONField(name = "CREATE_TIME")
    private Date createTime; // 创建时间

    @ApiModelProperty(value = "最后更新时间")
    @JSONField(name = "UPDATE_TIME")
    private Date updateTime; // 最后更新时间
}