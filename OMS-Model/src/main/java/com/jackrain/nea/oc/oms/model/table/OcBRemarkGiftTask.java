package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * 卖家添加备注任务表
 *
 * <AUTHOR>
 */
@Data
@TableName("oc_b_remark_gift_task")
public class OcBRemarkGiftTask extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 店铺id
     */
    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    /**
     * 平台单号
     */
    @JSONField(name = "TID")
    private String tid;

    /**
     * 状态。0:未执行；1:已执行；2:过期未执行；3:执行中
     */
    @JSONField(name = "TASK_STATUS")
    private Integer taskStatus;
}