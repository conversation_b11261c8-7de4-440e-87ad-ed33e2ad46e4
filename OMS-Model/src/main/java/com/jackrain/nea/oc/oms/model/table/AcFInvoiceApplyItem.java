package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ac_f_invoice_apply_item")
@Data
public class AcFInvoiceApplyItem extends BaseModel {
    private static final long serialVersionUID = 3838782929909770588L;
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "TID")
    private String tid;

    @J<PERSON><PERSON>ield(name = "AC_F_INVOICE_APPLY_ID")
    private Long acFInvoiceApplyId;

    @J<PERSON><PERSON><PERSON>(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @J<PERSON>NField(name = "AMT")
    private BigDecimal amt;

    @<PERSON><PERSON><PERSON>ield(name = "OWNERENAME")
    private String ownerename;

    @<PERSON><PERSON><PERSON>ield(name = "MODIFIERENAME")
    private String modifierename;
}