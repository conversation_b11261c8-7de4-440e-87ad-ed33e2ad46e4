package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * 店铺最新发货效期表
 * 
 * <AUTHOR>
 * @date 2024-12-16
 */
@TableName(value = "oc_b_shop_sku_batch_info")
@Data
public class OcBShopSkuBatchInfo extends BaseModel {
    
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "SHOP_ID")
    private Long shopId;

    @JSONField(name = "SHOP_CODE")
    private String shopCode;

    @JSONField(name = "SHOP_TITLE")
    private String shopTitle;

    @JSONField(name = "SKU")
    private String sku;

    @JSONField(name = "PRODUCE_DATE")
    private String produceDate;

    @JSONField(name = "RECEIVER_ADDRESS")
    private String receiverAddress;

    @JSONField(name = "VERSION")
    private Long version;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}
