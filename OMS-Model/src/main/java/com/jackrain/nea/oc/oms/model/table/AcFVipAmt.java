package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ac_f_vip_amt")
@Data
@Document(index = "ac_f_vip_amt",type = "ac_f_vip_amt")
@ApiModel(value = "ac_f_vip_amt", description = "唯品会商品销售额")
public class AcFVipAmt extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "ID")
    private Long id;

    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "订单编号")
    private String billNo;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(value = "平台店铺")
    private Long cpCShopId;

    @JSONField(name = "MONTH_OF_YEAR")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "年月")
    private String monthOfYear;

    @JSONField(name = "QTY_INCLUDE_REJECT")
    @Field(type = FieldType.Float)
    @ApiModelProperty(value = "销售量含拒退")
    private BigDecimal qtyIncludeReject;

    @JSONField(name = "AMT_INCLUDE_REJECT")
    @Field(type = FieldType.Float)
    @ApiModelProperty(value = "销售额扣满减含拒退")
    private BigDecimal amtIncludeReject;

    @JSONField(name = "AMT_AVERAGE")
    @Field(type = FieldType.Float)
    @ApiModelProperty(value = "平均销售额")
    private BigDecimal amtAverage;

    @JSONField(name = "PS_C_SKU_ECODE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "商品编码")
    private String psCSkuEcode;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "备注")
    private String remark;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "创建人姓名")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "修改人姓名")
    private String modifierename;

    @JSONField(name = "PS_C_PRO_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "商品id")
    private Long psCProId;

    @JSONField(name = "PS_C_PRO_ECODE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "商品编码")
    private String psCProEcode;

    @JSONField(name = "PS_C_PRO_ENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(value = "商品名称")
    private String psCProEname;
}