package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * st_c_traceability_strategy_item
 * <AUTHOR>
 * @create 2024-12-19
 */
@TableName
@Data
public class StCTraceabilityStrategyItem extends BaseModel {
    @JSONField(name = "ID")
    private Long id;

    /**
     * 商品SKU
     */
    @JSONField(name = "PS_C_SKU_ID", alternateNames = "psCSkuId")
    private Long psCSkuId;

    /**
     * 主表id
     */
    @JSONField(name = "ST_C_TRACEABILITY_STRATEGY_ID", alternateNames = "stCTraceabilityStrategyId")
    private Long stCTraceabilityStrategyId;

    private static final long serialVersionUID = 1L;
}
