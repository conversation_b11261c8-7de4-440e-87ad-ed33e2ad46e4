package com.jackrain.nea.oc.oms.model.table;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * oc_b_occupy_task
 * <AUTHOR>
@TableName
@Data
public class OcBOccupyTask implements Serializable {
    private Long id;

    private Long orderId;

    private Integer status;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 重试次数
     */
    private Integer retryNumber;

    /**
     * 自动寻源标识 1-自动 0-手动
     */
    private Integer isAuto;

    /**
     * 下次执行时间
     */
    private Date nextTime;

    private Date creationdate;

    private Date modifieddate;




    private static final long serialVersionUID = 1L;
}