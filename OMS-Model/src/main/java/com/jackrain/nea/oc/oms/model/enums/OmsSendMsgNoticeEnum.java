package com.jackrain.nea.oc.oms.model.enums;


import java.util.Objects;

/**
 * <AUTHOR> ruan.gz
 * @Description :
 * @Date : 2020/7/4
 **/
public enum OmsSendMsgNoticeEnum {

    LOGISTICS_DELIVERY("物流发货提醒", 1),
    RETURN_SWAP("退换货提醒", 2);


    String key;
    Integer val;

    OmsSendMsgNoticeEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }

    public static OmsSendMsgNoticeEnum getFromValue(Integer value) {
        OmsSendMsgNoticeEnum[] values = OmsSendMsgNoticeEnum.values();
        for (OmsSendMsgNoticeEnum statusEnum : values) {
            if (Objects.equals(statusEnum.val, value)) {
                return statusEnum;
            }
        }
        throw new IllegalArgumentException(String.valueOf(value));
    }
}


