package com.jackrain.nea.oc.oms.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

/**
 * @Author: 黄世新
 * @Date: 2022/7/11 下午3:14
 * @Version 1.0
 */
@Data
public class EqualExchangeItem implements Serializable {

    @JSONField(name = "sku_code")
    private String skuCode;

    @JSONField(name = "sku_title")
    private String skuTitle;

    @JSONField(name = "qty")
    private BigDecimal qty;

    @JSONField(name = "relation_ids")
    private Set<Long> relationIds;
}
