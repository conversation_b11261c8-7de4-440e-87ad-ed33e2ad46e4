package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.oc.oms.model.result.QueryOrderSelectResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 订单下拉查询条件
 *
 * @author: xiwen.z
 * create at: 2019/3/8 0008
 */
public enum OcOrderSelectEnum {
    // 显示名称, 实际字段,类型, 序号
    SELECT_SOURCECODE("平台单号", "SOURCE_CODE", "Input", 10),
    SELECT_BILL_NO("单据编号","BILL_NO","Input",20),
    SELECT_PT_STATUS("平台状态","PLATFORM_STATUS","Select",25),
    SELECT_SHOP("下单店铺", "CP_C_SHOP_TITLE", "DropDownSelectFilter", 30),
    SELECT_LOGISTICS_NUM("物流单号", "EXPRESSCODE", "Input", 40),
    SELECT_PHONE("手机号码", "RECEIVER_MOBILE", "Input", 50),
    SELECT_STATUS("状态", "ORDER_STATUS", "Select", 60),
    SELECT_ORDER_TIME("下单时间", "ORDER_DATE", "date", 70),
    SELECT_PAY_TIME("付款时间", "PAY_TIME", "date", 80),
    SELECT_DISTRIBUTION_TIME("配货时间", "DISTRIBUTION_TIME", "date", 90),
    SELECT_DELIVERY_WAREHOUSE("发货仓库", "CP_C_PHY_WAREHOUSE_ENAME", "DropDownSelectFilter", 100),
    SELECT_LOGISTICS_COMPANY("物流公司", "CP_C_LOGISTICS_ENAME", "DropDownSelectFilter", 110),
    SELECT_BUYER_NICKNAME("买家昵称", "USER_NICK", "Input", 120);

    String displayName;
    String queryName;
    String type;
    int sort;

    OcOrderSelectEnum(String dn, String qn, String t, int n) {
        this.displayName = dn;
        this.queryName = qn;
        this.type = t;
        this.sort = n;
    }

    /**
     * 转化为list<QueryOrderSelectResult>
     *
     * @return list<QueryOrderSelectResult>
     */
    public static List<QueryOrderSelectResult> toQueryOrderSelectResult() {
        List<QueryOrderSelectResult> list = new ArrayList<>();
        for (OcOrderSelectEnum e : OcOrderSelectEnum.values()) {
            QueryOrderSelectResult o = new QueryOrderSelectResult();
            o.setDisplayName(e.getDisplayName());
            o.setQueryName(e.getQueryName());
            o.setType(e.getType());
            o.setSort(e.getSort());
            list.add(o);
        }
        return list;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getQueryName() {
        return queryName;
    }

    public String getType() {
        return type;
    }

    public int getSort() {
        return sort;
    }


}
