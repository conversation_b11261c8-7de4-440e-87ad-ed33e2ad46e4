package com.jackrain.nea.oc.oms.model.relation;

import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategy;
import com.jackrain.nea.oc.oms.model.table.StCTraceabilityStrategyItem;
import lombok.Data;

import java.util.List;

/**
 * @program: r3-oc-oms
 * @description: 溯源标记策略关系
 * @author: lijin
 * @create: 2024-12-19
 **/
@Data
public class StCTraceabilityStrategyRelations {
    
    /**
     * 溯源标记策略主表
     */
    private StCTraceabilityStrategy stCTraceabilityStrategy;
    
    /**
     * 溯源标记策略明细列表
     */
    private List<StCTraceabilityStrategyItem> stCTraceabilityStrategyItems;
    
    public StCTraceabilityStrategyRelations() {
    }
    
    public StCTraceabilityStrategyRelations(StCTraceabilityStrategy stCTraceabilityStrategy, 
                                           List<StCTraceabilityStrategyItem> stCTraceabilityStrategyItems) {
        this.stCTraceabilityStrategy = stCTraceabilityStrategy;
        this.stCTraceabilityStrategyItems = stCTraceabilityStrategyItems;
    }
}
