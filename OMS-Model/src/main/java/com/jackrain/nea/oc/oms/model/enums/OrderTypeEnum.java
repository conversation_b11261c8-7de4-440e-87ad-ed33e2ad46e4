package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 订单类型
 *
 * @author: 易邵峰
 * @since: 2019-03-07
 * create at : 2019-03-07 21:28
 */
public enum OrderTypeEnum {
    NORMAL(1, "正常"),

    EXCHANGE(2, "换货"),

    REISSUE(3, "补发"),

    GIFT(4, "赠品"),

    INTEGRAL(5, "积分"),

    LOST(6, "补单"),

    SCAlPING(7, "刷单"),

    DIFFPRICE(8, "虚拟"),

    TBA_PRE_SALE(9, "预售"),

    VIRTUAL_DEPOSIT(10, "虚拟定金"),

    WHOLESALE_BOX(600, "批发有装箱"),

    WHOLESALE_NO_BOX(601, "批发无装箱"),

    TYPE_NONE(0, "");

    Integer val;
    String description;

    OrderTypeEnum(int v, String s) {
        this.val = v;
        this.description = s;
    }

    public Integer getVal() {
        return val;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据值,转换成文本
     *
     * @param integer integer
     * @return string
     */
    public static String getTextByVal(Integer integer) {
        if (integer == null) {
            return TYPE_NONE.getDescription();
        }
        for (OrderTypeEnum o : OrderTypeEnum.values()) {
            if (Objects.equals(o.getVal(), integer)) {
                return o.getDescription();
            }
        }
        return "";
    }

    /**
     * 转化为hashMap
     *
     * @return map
     */
    public static Map convertAllToHashVal() {
        Map<Integer, String> m = new HashMap<>();
        for (OrderTypeEnum o : OrderTypeEnum.values()) {
            m.put(o.getVal(), o.getDescription());
        }
        return m;
    }

    public static Integer getValByText(String description) {
        if (description == null) {
            return TYPE_NONE.getVal();
        }
        for (OrderTypeEnum o : OrderTypeEnum.values()) {
            if (Objects.equals(o.getDescription(), description)) {
                return o.getVal();
            }
        }
        return TYPE_NONE.getVal();
    }
}