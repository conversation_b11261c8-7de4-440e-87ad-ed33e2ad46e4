package com.jackrain.nea.oc.oms.model.enums;

public enum OrderHoldEnum {

    CATEGORIES("1", "大类"),

    IN_THE_CLASS("2", "中类"),

    SMALL_CLASS("3", "小类"),

    GOODS_CODE("4", "商品编码"),

    GOODS_SKU("5","中台条码"),

    PT_SPU_ID("6", "平台商品ID"),

    PT_SKU_ID("7", "平台条码"),

    PT_PRO_TITLE("8", "商品标题关键字"),

    ORDER_WEIGHT("9", "订单重量"),

    MOBILE_NUMBER("10", "手机号码"),

    BUYER_MEMO("11", "买家留言"),

    MOBILE_ORDER_NUM("12", "手机号下单数"),

    ADDRESS_ORDER_NUM("13", "地址下单数"),

    SELLER_MEMO("14", "卖家留言"),

    RECEIVE_PROVINCE("16", "按收货地省"),

    RECEIVE_CITY("17", "按收货地市"),

    ALL("0", "全部");

    public static String getMessageByKey(int key) {
        for (OrderHoldReasonEnum e : OrderHoldReasonEnum.values()) {
            if (e.getKey().equals(key)) {
                return e.getMessage();
            }
        }
        return null;
    }

    private final String key;

    private final String message;

    OrderHoldEnum(String key, String message) {
        this.key = key;
        this.message = message;
    }

    public String getKey() {
        return key;
    }

    public String getMessage() {
        return message;
    }
}
