package com.jackrain.nea.oc.oms.model.perm;

import java.util.ArrayList;
import java.util.List;

/**
 * permission Alias Field
 *
 * @Desc :
 * <AUTHOR> xiWen
 * @Date : 2020/4/20
 */
public class PermissionAlias {

    private List<String> shops;

    private List<String> wareHouses;

    private List<String> stores;

    private List<String> brands;

    private List<String> logistics;


    public List<String> getShop() {
        return shops;
    }

    public List<String> getWareHouse() {
        return wareHouses;
    }

    public List<String> getStore() {
        return stores;
    }

    public List<String> getBrand() {
        return brands;
    }

    public PermissionAlias setShop(String shop) {
        if (shops == null) {
            shops = new ArrayList<>();
        }
        shops.add(shop);
        return this;
    }

    public PermissionAlias setWareHouse(String shop) {
        if (wareHouses == null) {
            wareHouses = new ArrayList<>();
        }
        wareHouses.add(shop);
        return this;
    }

    public PermissionAlias setStore(String shop) {
        if (stores == null) {
            stores = new ArrayList<>();
        }
        stores.add(shop);
        return this;
    }

    public PermissionAlias setBrand(String shop) {
        if (brands == null) {
            brands = new ArrayList<>();
        }
        brands.add(shop);
        return this;
    }

    public List<String> getLogistics() {
        return logistics;
    }

    public PermissionAlias setLogistics(String logistic) {
        if (logistics == null) {
            logistics = new ArrayList<>();
        }
        logistics.add(logistic);
        return this;
    }


    public static PermissionAlias build() {
        return new PermissionAlias();
    }

}
