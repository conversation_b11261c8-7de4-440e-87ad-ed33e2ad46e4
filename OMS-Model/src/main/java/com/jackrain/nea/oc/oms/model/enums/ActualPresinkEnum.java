package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Objects;

/**
 * descriptio实际预下沉状态
 * @Author:  liuwenjin
 * @Date 2021/11/5 8:24 下午
 */
public enum ActualPresinkEnum {
    ACTUAL_PRESINK_STATUS_NOT_NOTIFIED("未通知", "1"),
    ACTUAL_PRESINK_STATUS_NOTIFIED("通知中", "2"),
    ACTUAL_PRESINK_STATUS_NOTIFIED_SUCCESS("已通知", "3"),
    ACTUAL_PRESINK_STATUS_COMPLETED("已完成", "4"),
    ACTUAL_PRESINK_STATUS_CANCELING("取消中", "5"),
    ACTUAL_PRESINK_STATUS_CANCELING_SUCCESS("取消成功", "6"),
    ACTUAL_PRESINK_STATUS_CANCELING_FALL("取消失败", "7");

    private String ename;
    private String val;
    ActualPresinkEnum(String ename, String val) {
        this.ename = ename;
        this.val = val;
    }

    public String getEname() {
        return ename;
    }

    public String getVal() {
        return val;
    }
    private static HashMap<String, String> map = new HashMap<>();
    public static String getValByEname(String ename) {
        if (map.size() != LogTypeEnum.values().length) {
            for (ActualPresinkEnum value : ActualPresinkEnum.values()) {
                map.put(value.getEname(), value.getVal());
            }
        }
        if (Objects.nonNull(ename) && map.containsKey(ename)) {
            return map.get(ename);
        }
        return "";
    }
}
