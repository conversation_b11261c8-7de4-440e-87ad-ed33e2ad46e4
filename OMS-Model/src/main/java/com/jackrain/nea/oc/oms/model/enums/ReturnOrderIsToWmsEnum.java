package com.jackrain.nea.oc.oms.model.enums;


/**
 * 退单是否传wms
 *
 * @author: ming.fz
 * create at: 2019/9/16
 */
public enum ReturnOrderIsToWmsEnum {


    /**
     * 退单未传wms
     */
    RETURN_ORDER_IS_TO_WMS("未传WMS", 0);

    String key;
    int val;

    ReturnOrderIsToWmsEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }


}


