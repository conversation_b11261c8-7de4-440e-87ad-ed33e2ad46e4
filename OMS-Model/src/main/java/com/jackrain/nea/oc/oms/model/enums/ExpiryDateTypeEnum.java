package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * @program: r3-oc-oms
 * @description: 商品效期策略类型
 * @author: caomalai
 * @create: 2022-08-11 11:34
 **/
public enum ExpiryDateTypeEnum {
    COMMUNAL(1, "公用"),
    APPOINT_SHOP(2,"指定店铺"),
    CUSTOMER_GROUPING(3,"客户分组");
    @Getter
    private Integer key;
    @Getter
    private String desc;

    ExpiryDateTypeEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static ExpiryDateTypeEnum getByKey(Integer key) {
        for (ExpiryDateTypeEnum current : values()) {
            if (Objects.equals(current.getKey(), key)) {
                return current;
            }
        }
        return null;
    }

    public static ExpiryDateTypeEnum getByDesc(String desc) {
        for (ExpiryDateTypeEnum current : values()) {
            if (Objects.equals(current.getDesc(), desc)) {
                return current;
            }
        }
        return null;
    }
}
