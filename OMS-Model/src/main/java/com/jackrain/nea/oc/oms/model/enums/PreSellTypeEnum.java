package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * description：预售类型
 *
 * <AUTHOR>
 * @date 2021/6/1
 */
public enum PreSellTypeEnum {
    DEPOSIT(1, "定金预售"),

    FULL_AMOUNT(2, "全款预售"),

    TYPE_NONE(0, "");

    Integer val;
    String description;

    PreSellTypeEnum(int v, String s) {
        this.val = v;
        this.description = s;
    }

    public Integer getVal() {
        return val;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据值,转换成文本
     *
     * @param integer integer
     * @return string
     */
    public static String getTextByVal(Integer integer) {
        if (integer == null) {
            return TYPE_NONE.getDescription();
        }
        for (PreSellTypeEnum o : PreSellTypeEnum.values()) {
            if (Objects.equals(o.getVal(), integer)) {
                return o.getDescription();
            }
        }
        return "";
    }

    /**
     * 转化为hashMap
     *
     * @return map
     */
    public static Map convertAllToHashVal() {
        Map<Integer, String> m = new HashMap<>();
        for (PreSellTypeEnum o : PreSellTypeEnum.values()) {
            m.put(o.getVal(), o.getDescription());
        }
        return m;
    }
}