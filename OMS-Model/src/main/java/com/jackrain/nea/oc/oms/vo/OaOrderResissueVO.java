package com.jackrain.nea.oc.oms.vo;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @ClassName OaOrderResissueVO
 * @Description OA补发
 * <AUTHOR>
 * @Date 2024/10/12 15:00
 * @Version 1.0
 */
@Data
public class OaOrderResissueVO implements Serializable {
    private static final long serialVersionUID = 8809655369181811391L;

    @NotEmpty(message = "平台单号不能为空")
    private String tid;

    @NotEmpty(message = "商品编码不能为空")
    private String psCSkuEcode;

    @NotNull(message = "数量不能为空")
    private Integer qty;

    /**
     * 创建人
     */
    private String ownername;
}
