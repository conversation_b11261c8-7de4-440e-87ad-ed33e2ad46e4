package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Auther: 黄志优
 * @Date: 2020/11/2 11:20
 * @Description:
 */
public enum OmsStOrderType {

    NORMAL("正常订单", 1,1),
    BOOKING("预售订单", 2,9),
    EXCHANGE("换货订单", 3,2),
    MANUAL_ADD("手工新增", 4,1);
    //CASH_ON_DELIVERY("货到付款", 4,1),
    //MERGE_ORDER("合并订单", 6,1);

    /**
     * 描述
     */
    String key;

    /**
     * oms订单类型值
     */
    Integer omsVal;

    /**
     * 策略页面值
     */
    Integer val;

    public String getKey() {
        return key;
    }

    public Integer getOmsVal() {
        return omsVal;
    }

    public Integer getVal() {
        return val;
    }

    OmsStOrderType(String k, Integer v, Integer ov) {
        this.key = k;
        this.val = v;
        this.omsVal = ov;
    }
}
