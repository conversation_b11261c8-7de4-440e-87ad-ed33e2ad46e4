package com.jackrain.nea.oc.oms.model.enums;

/**
 * 全渠道订单类型
 *
 * @author: ming.fz
 * create at: 2019/4/30
 */
public enum OrderBillTypeEnum {

    RETAIL_DELIVERY("零售发货", 1),

    VIP_ORDER("唯品会单", 8);

    String key;
    Integer val;

    OrderBillTypeEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }


}


