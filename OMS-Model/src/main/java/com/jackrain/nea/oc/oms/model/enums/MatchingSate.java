package com.jackrain.nea.oc.oms.model.enums;

/**
 * @author: 夏继超
 * @since: 2019/3/25
 * create at : 2019/3/25 21:38
 */
public enum MatchingSate {
    //0未匹配，1部分匹配，2全部匹配
    UNMATCHED,
    PARTIAL_MATCHING,
    MATCH_ALL,
    NOT_MATCH;

    public int toInteger() {
        if (this == MatchingSate.UNMATCHED) {
            return 0;
        } else if (this == MatchingSate.PARTIAL_MATCHING) {
            return 1;
        } else if (this == MatchingSate.NOT_MATCH) {
            return 3;
        } else {
            return 2;
        }
    }

    public String toDescription() {
        if (this == MatchingSate.UNMATCHED) {
            return "UNMATCHED";
        } else if (this == MatchingSate.PARTIAL_MATCHING) {
            return "PARTIAL_MATCHING";
        } else if (this == MatchingSate.NOT_MATCH) {
            return "NOT_MATCH";
        } else {
            return "MATCH_ALL";
        }
    }
}
