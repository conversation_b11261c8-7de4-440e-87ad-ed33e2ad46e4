package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @ClassName StCCycleItemStrategy
 * @Description 周期购促销赠品商品
 * <AUTHOR>
 * @Date 2024/8/19 14:53
 * @Version 1.0
 */
@Data
@TableName("st_c_cycle_item_strategy")
public class StCCycleItemStrategy extends BaseModel {
    private static final long serialVersionUID = -4416251114823982531L;

    /**
     * id
     */
    private Long id;

    /**
     * 策略ID
     */
    @JSONField(name = "STRATEGY_ID")
    private Long strategyId;

    /**
     * 商品编码
     */
    @JSONField(name = "SKU_CODE")
    private String skuCode;

    /**
     * 商品名称
     */
    @JSONField(name = "SKU_NAME")
    private String skuName;

    /**
     * 商品ID
     */
    @JSONField(name = "SKU_ID")
    private Long skuId;

    /**
     * 周期数
     */
    @JSONField(name = "CYCLE_NUM")
    private Integer cycleNum;

    /**
     * 拆单类型 1 不拆单 2 可拆单
     */
    @JSONField(name = "SPLIT_TYPE")
    private Integer splitType;

    /**
     * 数量
     */
    @JSONField(name = "QTY")
    private Integer qty;

    /**
     * 状态
     */
    @JSONField(name = "STATUS")
    private Integer status;
}
