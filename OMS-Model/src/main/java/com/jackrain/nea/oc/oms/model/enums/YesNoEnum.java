package com.jackrain.nea.oc.oms.model.enums;


/**
 * 是否
 *
 * @author: ming.fz
 * create at: 2019/7/23
 */
public enum YesNoEnum {


    /**
     * 是否可用
     */
    Y("Y", 1),
    N("N", 0),
    ZERO("0", 0),
    ONE("1", 1);

    String key;
    Integer val;

    YesNoEnum(String k, Integer v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return val;
    }


}


