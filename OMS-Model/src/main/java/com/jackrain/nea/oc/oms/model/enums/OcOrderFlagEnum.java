package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.oc.oms.model.result.QueryOrderFlagResult;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: xiwen.z
 * create at: 2019/4/10 0010
 */
public enum OcOrderFlagEnum {
    GRAY("0", "灰色", 1), RED("1", "红色", 2),
    ORAGER("2", "橙色", 3), GREEN("3", "绿色", 4),
    BLUE("4", "蓝色", 5), PURPLE("5", "紫色", 6);

    String val;
    String text;
    int sort;

    OcOrderFlagEnum(String v, String t, int n) {
        this.val = v;
        this.text = t;
        this.sort = n;
    }

    /**
     * 转化枚举
     *
     * @return List<QueryOrderFlagResult>
     */
    public static List<QueryOrderFlagResult> toQueryOrderFlagResult() {
        List<QueryOrderFlagResult> list = new ArrayList<>();
        for (OcOrderFlagEnum o : OcOrderFlagEnum.values()) {
            QueryOrderFlagResult ofr = new QueryOrderFlagResult();
            ofr.setVal(o.getVal());
            ofr.setText(o.getText());
            ofr.setSort(o.getSort());
            list.add(ofr);
        }
        return list;
    }

    public String getVal() {
        return val;
    }

    public String getText() {
        return text;
    }

    public int getSort() {
        return sort;
    }
}
