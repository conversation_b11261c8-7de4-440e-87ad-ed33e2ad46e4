package com.jackrain.nea.oc.oms.model.enums;

/**
 * 物流公司
 *
 * @author: ming.fz
 * @since: 2019-03-21
 */
public enum LogisticsCompany {

    /**
     * 京东
     */
    JINGDONG,

    JINGDONG_DX;


    public Long getLongId() {
        if (this == JINGDONG) {
            return new Long(-2);
        } else {
            return null;
        }
    }
    public String getCode() {
        if (this == JINGDONG_DX) {
            return "JD";
        } else {
            return null;
        }
    }

}
