package com.jackrain.nea.oc.oms.model.enums;

/**
 * ljp add
 * 出库状态
 */
public enum OutStatusEnum {

    OUT_NO("未出库", 1),
    OUT_YES("已出库", 2);

    String key;
    int val;

    OutStatusEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    /**
     * 根据状态值,获取状态名
     *
     * @param integer integer
     * @return String
     */
    public static String enumToStringByValue(Integer integer) {
        String s = "";
        if (integer == null) {
            return s;
        }
        for (OcOrderCheckBoxEnum e : OcOrderCheckBoxEnum.values()) {
            if (e.getVal() == integer) {
                s = e.getKey();
                break;
            }
        }
        return s;
    }
}
