package com.jackrain.nea.oc.oms.model.enums;


import com.jackrain.nea.oc.oms.model.result.QueryOrderCheckBoxResult;

import java.util.ArrayList;
import java.util.List;

/**
 * 退款状态选项值
 * <p>
 * '退换货状态,20等待退货入库，30等待售后确认，50完成，60取消'
 *
 * @author: 周琳胜
 * create at: 2019/3/20 13:20
 */
public enum ReturnStatusEnum {
    /**
     *
     */
    IS_BACK(1, "是原退"),

    /**
     *
     */
    NOT_BACK(0, "不是原退"),

    /**
     *
     */
    IS_TODRP(1, "已生成零售"),

    /**
     *
     */
    NOT_TODRP(0, "未生成零售"),

    /**
     * 待退货入库
     */
    WAIT_RETURN_STORAGE(20, "待退货入库"),

    /**
     * 等待售后确认
     */
    WAIT_AFTERSALE_ENSURE(30, "等待售后确认"),

    /**
     *
     */
    COMPLETION(50, "完成"),

    /**
     *
     */
    CANCLE(60, "取消");


    String name;
    Integer code;

    ReturnStatusEnum(Integer v, String k) {
        this.name = k;
        this.code = v;
    }

    public String getKey() {
        return name;
    }

    public Integer getVal() {
        return code;
    }

    /**
     * 转化成QueryOrderCheckBoxResult
     *
     * @return list<QueryOrderCheckBoxResult>
     */
    public static List<QueryOrderCheckBoxResult> toQueryOrderCheckBoxResult() {
        List<QueryOrderCheckBoxResult> list = new ArrayList<>();
        for (ReturnStatusEnum e : ReturnStatusEnum.values()) {
            QueryOrderCheckBoxResult o = new QueryOrderCheckBoxResult();
            o.setLabel(e.getKey());
            o.setValue(String.valueOf(e.getVal()));
            list.add(o);
        }
        return list;
    }

    public static String getNameByCode(Integer value) {
        ReturnStatusEnum[] businessModeEnums = values();
        for (ReturnStatusEnum returnStatus : businessModeEnums) {
            if (returnStatus.getVal().equals(value)) {
                return returnStatus.getKey();
            }
        }
        return null;
    }
}


