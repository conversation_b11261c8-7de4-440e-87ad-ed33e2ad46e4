package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Desc : 错收
 * <AUTHOR> xiWen
 * @Date : 2022/7/20
 */
public enum IsWrongReceive {

    /**
     * 否
     */
    NO(0, "否"),

    /**
     * 是
     */
    YES(1, "是");

    Integer val;

    String txt;

    IsWrongReceive(Integer value, String desc) {
        this.val = value;
        this.txt = desc;
    }

    public Integer val() {
        return this.val;
    }

    public String txt() {
        return this.txt;
    }

}
