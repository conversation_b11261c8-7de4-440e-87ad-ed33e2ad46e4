package com.jackrain.nea.oc.oms.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: 黄世新
 * @Date: 2022/3/28 下午5:41
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum JingDongDirectOrderStatus {

//7 新订单；10等待发货；16等待确认收货；19订单完成；22，29发生退款订单删除
    NEW_ORDER(7, "新订单"),

    WAIT_SELLER_SEND_GOODS(10, "等待发货"),

    WAIT_BUYER_CONFIRM_GOODS(16, "等待确认收货"),

    TRADE_FINISHED(19, "订单完成"),

    HAPPEN_REFUND(22, "发生退款"),

    ORDER_DELETE(29, "订单删除");

    private Integer code;

    private String message;


    public static boolean isExist(Integer code){
        JingDongDirectOrderStatus[] values = values();
        for (JingDongDirectOrderStatus value : values) {
            if (value.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }

}
