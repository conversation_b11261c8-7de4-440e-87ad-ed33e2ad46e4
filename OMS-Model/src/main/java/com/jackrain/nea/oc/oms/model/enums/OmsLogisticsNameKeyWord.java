package com.jackrain.nea.oc.oms.model.enums;

/**
 * 快递公司关键字
 *
 * @author: 胡林洋
 * @since: 2019-03-16
 * create at : 2019-03-16 16:45
 */
public enum OmsLogisticsNameKeyWord {
    /**
     * 天天快递
     */
    TIANTIAN,

    /**
     * 韵达
     */
    YUNDA,
    /**
     * 顺丰
     */
    SHUNFENG,
    /**
     * 中通
     */
    ZHONGTONG,
    /**
     * 申通
     */
    SHENTONG,
    /**
     * 圆通
     */
    YUANTONG,
    /**
     * 京东
     */
    JINGDONG,
    /**
     * 百世
     */
    BAISHI;

    public String parseValue() {
        if (this == TIANTIAN) {
            return "天天";
        } else if (this == YUNDA) {
            return "韵达";
        } else if (this == SHUNFENG) {
            return "顺丰";
        } else if (this == ZHONGTONG) {
            return "中通";
        } else if (this == SHENTONG) {
            return "申通";
        } else if (this == YUANTONG) {
            return "圆通";
        } else if (this == JINGDONG) {
            return "京东";
        } else if (this == BAISHI) {
            return "百世";
        } else {
            return "其他";
        }
    }

}
