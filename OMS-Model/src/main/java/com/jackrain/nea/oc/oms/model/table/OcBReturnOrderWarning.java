package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "oc_b_return_order_warning")
@Data
public class OcBReturnOrderWarning extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @J<PERSON>NField(name = "AD_ORG_ID")
    private Long adOrgId;

    @J<PERSON><PERSON>ield(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "order_id")
    private Long orderId;

    @JSONField(name = "TID")
    private String tid;

    @JSONField(name = "ORDER_BILL_NO")
    private String orderBillNo;

    @JSONField(name = "AUTO_INTERCEPT")
    private String autoIntercept;

    @J<PERSON><PERSON>ield(name = "sysremark")
    private String sysremark;

    @J<PERSON><PERSON>ield(name = "T_RETURN_ID")
    private String tReturnId;

    @JSONField(name = "USER_NICK")
    private String userNick;

    @JSONField(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "PS_C_PRO_SKU")
    private String psCProSku;

    @JSONField(name = "TOT_SNED_QTY")
    private BigDecimal totSnedQty;

    @JSONField(name = "TOT_REFUND_QTY")
    private BigDecimal totRefundQty;

    @JSONField(name = "SG_B_OUT_BILL_ID")
    private Long sgBOutBillId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    private Long cpCLogisticsId;

    @JSONField(name = "EXPRESSCODE")
    private String expresscode;

    @JSONField(name = "ORDER_RETURN_BILL_NO")
    private String orderReturnBillNo;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_IN_ID")
    private Long cpCPhyWarehouseInId;

    @JSONField(name = "CP_C_LOGISTICS_RETURN_ID")
    private Long cpCLogisticsReturnId;

    @JSONField(name = "LOGISTICS_CODE")
    private String logisticsCode;

    @JSONField(name = "AMT_RETURN")
    private BigDecimal amtReturn;

    @JSONField(name = "RETURNDATE")
    private Date returndate;

    @JSONField(name = "WARNING_TYPE")
    private Integer warningType;

    /**
     * 订单总金额
     */
    @JSONField(name = "ORDER_AMT")
    private BigDecimal orderAmt;

    /**
     * 付款时间
     */
    @JSONField(name = "PAY_TIME")
    private Date payTime;

    /**
     * 仓库发货时间
     */
    @JSONField(name = "WAREHOUSE_DELIVERY_TIME")
    private Date warehouseDeliveryTime;

    @JSONField(name = "MANAGE_RESULT")
    private String manageResult;

    @JSONField(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    private Date modifieddate;


}