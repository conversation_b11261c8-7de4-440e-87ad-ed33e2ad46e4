package com.jackrain.nea.oc.oms.model.enums;

/**
 * @Desc : 退单传wms 单据入库类型
 * <AUTHOR> xiWen
 * @Date : 2020/12/3
 */
public enum Return2WmsBillEnum {

    /**
     * 退货入库
     */
    RETURN("THRK", "退货入库"),

    /**
     * 换货入库
     */
    EXCHANGE("HHRK", "换货入库"),

    /**
     * 无名件入库
     */
    NO_NAME("WMJRK", "无名件入库");

    /**
     * 值
     */
    String val;

    /**
     * 文本
     */
    String txt;

    /**
     * 值
     *
     * @return 枚举值
     */
    public String val() {
        return this.val;
    }

    Return2WmsBillEnum(String k, String v) {
        this.val = k;
        this.txt = v;
    }
}
