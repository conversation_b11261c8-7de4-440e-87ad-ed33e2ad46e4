package com.jackrain.nea.oc.oms.model.enums;

/**
 * @author: 易邵峰
 * @since: 2019-02-25
 * create at : 2019-02-25 21:05
 */
public enum ErrorLogType {

    /**
     * 淘宝转单错误
     */
    TAOBAO_TRANSFER_ERROR,

    /**
     * 京东转单错误
     */
    JINGDONG_TRANSFER_ERROR,

    /**
     * 唯品会Jitx转单错误
     */
    JITX_TRANSFER_ERROR,
    /**
     * 唯品会寻仓订单转单错误
     */
    JITX_DELIVERY_TRANSFER_ERROR,
    /**
     * 唯品会取消时效订单转单错误
     */
    JITX_CANCELTIMEORDER_TRANSFER_ERROR,

    /**
     * 唯品会退供单转单错误
     */
    JITX_RETURN_ORDER_TRANSFER_ERROR,

    /**
     * 淘宝经销转单错误
     */
    TAOBAOJX_TRANSFER_ERROR,

    /**
     * 通用转单错误
     */
    STANDPLAT_TRANSFER_ERROR,

    /**
     * 审单错误
     */
    AUDIT_ORDER,

    /**
     * 锁单错误
     */
    LOCK_ORDER_ERROR,

    /**
     * 猫超直发转单错误
     */
    TMALL_ZF_TRANSFER_ERROR;

    public String toKeyword() {
        return this.toString();
    }

}
