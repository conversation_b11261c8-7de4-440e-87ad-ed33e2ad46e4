package com.jackrain.nea.oc.oms.model.enums;


/**
 * 淘宝预售地址更改状态
 *
 * @author: ming.fz
 * create at: 2019/10/12
 */
public enum TaoBaoUpdateAddressStatusEnum {


    /**
     * 淘宝预售地址更改状态
     */
    NOT_SYS("未同步", 0),
    SUCCESS_SYS("同步成功", 1),
    FAILED_SYS("同步失败", 2),
    AWAIT_SYS("待同步", 3);

    String key;
    int val;

    TaoBaoUpdateAddressStatusEnum(String k, int v) {
        this.key = k;
        this.val = v;
    }

    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }


}


