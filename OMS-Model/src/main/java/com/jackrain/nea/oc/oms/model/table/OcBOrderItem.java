package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单明细
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Document(index = "oc_b_order_item", type = "oc_b_order_item")
@ApiModel(value = "oc_b_order_item", description = "订单明细")
@TableName(value = "oc_b_order_item")
public class OcBOrderItem extends BaseModel {


    @ApiModelProperty(value = "明细编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "ID")
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @ApiModelProperty(value = "实缺标记")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_LACKSTOCK")
    private Integer isLackstock;

    @ApiModelProperty(value = "退货金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_REFUND")
    private BigDecimal amtRefund;

    @ApiModelProperty(value = "组合名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GROUP_NAME")
    private String groupName;

    @ApiModelProperty(value = "商品数字编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "NUM_IID")
    private String numIid;

    @ApiModelProperty(value = "是否是赠品")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_GIFT")
    private Integer isGift;

    @ApiModelProperty(value = "整单平摊金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "ORDER_SPLIT_AMT")
    private BigDecimal orderSplitAmt;

    /*@ApiModelProperty(value = "库位")
    @Field(type = FieldType.Long)
    @JSONField(name = "STORE_SITE")
    private Long storeSite;*/

    @ApiModelProperty(value = "国标码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "BARCODE")
    private String barcode;

    @ApiModelProperty(value = "商品id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_PRO_ID")
    private Long psCProId;

    @ApiModelProperty(value = "商品货号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ECODE")
    private String psCProEcode;

    @ApiModelProperty(value = "商品名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_ENAME")
    private String psCProEname;

    @ApiModelProperty(value = "颜色id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_CLR_ID")
    private Long psCClrId;

    @ApiModelProperty(value = "颜色编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_CLR_ECODE")
    private String psCClrEcode;

    @ApiModelProperty(value = "颜色名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_CLR_ENAME")
    private String psCClrEname;

    @ApiModelProperty(value = "尺寸id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SIZE_ID")
    private Long psCSizeId;

    @ApiModelProperty(value = "尺寸编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SIZE_ECODE")
    private String psCSizeEcode;

    @ApiModelProperty(value = "尺寸名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SIZE_ENAME")
    private String psCSizeEname;

    @ApiModelProperty(value = "商品属性")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_PRO_MATERIELTYPE")
    private String psCProMaterieltype;

    @ApiModelProperty(value = "商品供应类型")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_PRO_SUPPLY_TYPE")
    private Long psCProSupplyType;

    @ApiModelProperty(value = "规格")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SKU_SPEC")
    private String skuSpec;

    @ApiModelProperty(value = "标题")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TITLE")
    private String title;

    @ApiModelProperty(value = "商品路径")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PIC_PATH")
    private String picPath;

    @ApiModelProperty(value = "子订单编号(明细编号)")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OOID")
    private String ooid;

    @ApiModelProperty(value = "条码id")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_SKU_ID")
    private Long psCSkuId;

    @ApiModelProperty(value = "条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ECODE")
    private String psCSkuEcode;

    @ApiModelProperty(value = "标准重量")
    @Field(type = FieldType.Double)
    @JSONField(name = "STANDARD_WEIGHT")
    private BigDecimal standardWeight;

    @ApiModelProperty(value = "标准价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_LIST")
    private BigDecimal priceList;

    @ApiModelProperty(value = "成交价格")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE")
    private BigDecimal price;

    @ApiModelProperty(value = "优惠金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_DISCOUNT")
    private BigDecimal amtDiscount;

    @ApiModelProperty(value = "调整金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "ADJUST_AMT")
    private BigDecimal adjustAmt;

    @ApiModelProperty(value = "成交金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "REAL_AMT")
    private BigDecimal realAmt;

    @ApiModelProperty(value = "是否已经占用库存")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_ALLOCATESTOCK")
    private Integer isAllocatestock;

    @ApiModelProperty(value = "买家是否已评价")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_BUYER_RATE")
    private Integer isBuyerRate;

    @ApiModelProperty(value = "使用积分")
    @Field(type = FieldType.Long)
    @JSONField(name = "BUYER_USED_INTEGRAL")
    private Long buyerUsedIntegral;

    @ApiModelProperty(value = "订单编号")
    @Field(type = FieldType.Long)
    @JSONField(name = "OC_B_ORDER_ID")
    private Long ocBOrderId;

    @ApiModelProperty(value = "已退数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_REFUND")
    private BigDecimal qtyRefund;

    @ApiModelProperty(value = "平台退款编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "REFUND_ID")
    private String refundId;

    @ApiModelProperty(value = "数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY")
    private BigDecimal qty;

    @ApiModelProperty(value = "取消状态(0,1:否,6:是)")
    @Field(type = FieldType.Integer)
    @JSONField(name = "REFUND_STATUS")
    private Integer refundStatus;

    @ApiModelProperty(value = "平台单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TID")
    private String tid;

    @ApiModelProperty(value = "预售状态")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_PRESALESKU")
    private Integer isPresalesku;

    @ApiModelProperty(value = "活动编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ACTIVE_ID")
    private String activeId;

    @ApiModelProperty(value = "分销价格")
    @Field(type = FieldType.Double)
    @JSONField(name = "DISTRIBUTION_PRICE")
    private BigDecimal distributionPrice;

    @ApiModelProperty(value = "虚拟条码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GIFTBAG_SKU")
    private String giftbagSku;

    @ApiModelProperty(value = "是否已发货")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_SENDOUT")
    private Integer isSendout;

    @ApiModelProperty(value = "发货失败次数")
    @Field(type = FieldType.Integer)
    @JSONField(name = "OUTERRCOUNT")
    private Integer outerrcount;

    @ApiModelProperty(value = "版本号")
    @Field(type = FieldType.Long)
    @JSONField(name = "VERSION")
    private Long version;

    @ApiModelProperty(value = "创建人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "OWNERENAME")
    private String ownerename;

    @ApiModelProperty(value = "修改人姓名")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "MODIFIERENAME")
    private String modifierename;

    @ApiModelProperty(value = "商品类型(0:正常,1:福袋,2:组合,3:预售;4:未拆分的组合商品;5:轻供商品)")
    @Field(type = FieldType.Long)
    @JSONField(name = "PRO_TYPE")
    private Long proType;

    @ApiModelProperty(value = "平台换货单号")
    @Field(type = FieldType.Long)
    @JSONField(name = "EXCHANGE_BILL_NO")
    private Long exchangeBillNo;

    @ApiModelProperty(value = "商品已退数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_HAS_RETURN")
    private BigDecimal qtyHasReturn;

    @ApiModelProperty(value = "吊牌价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_TAG")
    private BigDecimal priceTag;

    @ApiModelProperty(value = "结算单价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_SETTLE")
    private BigDecimal priceSettle;

    @ApiModelProperty(value = "结算总额")
    @Field(type = FieldType.Double)
    @JSONField(name = "TOT_PRICE_SETTLE")
    private BigDecimal totPriceSettle;

    @ApiModelProperty(value = "单件实际成交价")
    @Field(type = FieldType.Double)
    @JSONField(name = "PRICE_ACTUAL")
    private BigDecimal priceActual;

    @ApiModelProperty(value = "缺货数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_LOST")
    private BigDecimal qtyLost;

    @ApiModelProperty(value = "性别")
    @Field(type = FieldType.Long)
    @JSONField(name = "SEX")
    private Long sex;

    @ApiModelProperty(value = "sku数字编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "SKU_NUMIID")
    private String skuNumiid;

    @ApiModelProperty(value = "赠品类型 0 否 1 是系统赠品 2 平台赠品")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GIFT_TYPE")
    private String giftType;

    @ApiModelProperty(value = "贴纸赠品标识，1表示贴纸赠品，默认为空或者0")
    @Field(type = FieldType.Integer)
    @JSONField(name = "STICKER_GIFT")
    private Integer stickerGift;

    @ApiModelProperty(value = "组合商品购买数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_GROUP")
    private BigDecimal qtyGroup;

    @ApiModelProperty(value = "是否手工新增商品")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_MANUAL_ADD")
    private String isManualAdd;

    @ApiModelProperty(value = "赠品挂靠关系")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GIFT_RELATION")
    private String giftRelation;

    @ApiModelProperty(value = "已申请退货数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "QTY_RETURN_APPLY")
    private BigDecimal qtyReturnApply;

    @ApiModelProperty(value = "平台sku编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_PT_ECODE")
    private String psCSkuPtEcode;

    @ApiModelProperty(value = "平台商品名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PT_PRO_NAME")
    private String ptProName;

    @ApiModelProperty(value = "条码名称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PS_C_SKU_ENAME")
    private String psCSkuEname;

    @ApiModelProperty(value = "商品毛重")
    @Field(type = FieldType.Double)
    @JSONField(name = "GROSS_WEIGHT")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "平台退款状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PT_RETURN_STATUS")
    private String ptReturnStatus;

    @ApiModelProperty(value = "平台子订单状态")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PLATFORM_STATUS")
    private String platformStatus;

    @ApiModelProperty(value = "jitx PO单号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "JITX_PO_NO")
    private String jitxPoNo;

    @ApiModelProperty(value = "主播ID")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ANCHOR_ID")
    private String anchorId;

    @ApiModelProperty(value = "主播昵称")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ANCHOR_NAME")
    private String anchorName;

    @ApiModelProperty(value = "直播平台：1-快手,2-抖音,3-蘑菇街,4-陌陌,5-淘宝")
    @Field(type = FieldType.Integer)
    @JSONField(name = "LIVE_PLATFORM")
    private String livePlatform;

    @ApiModelProperty(value = "直播标识：1-直播单，2-非直播单")
    @Field(type = FieldType.Integer)
    @JSONField(name = "LIVE_FLAG")
    private Integer liveFlag;

    @ApiModelProperty(value = "预售类型 0:非预售 1:平台定金预售  2:店铺全款预售 3:自定义全款预售")
    @Field(type = FieldType.Integer)
    @JSONField(name = "PRESELL_TYPE")
    private Integer presellType;

    @ApiModelProperty(value = "商品品牌ID")
    @Field(type = FieldType.Long)
    @JSONField(name = "PS_C_BRAND_ID")
    private Long psCBrandId;

    @ApiModelProperty(value = "是否换单明细（合单用）")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_EXCHANGE_ITEM")
    private Integer isExchangeItem;

    @ApiModelProperty(value = "京东平台优惠券应收金额")
    @Field(type = FieldType.Double)
    @JSONField(name = "AMT_JINGDONG_COUPON")
    private BigDecimal amtJingdongCoupon;

    /**
     * 赠品属性
     */
    @ApiModelProperty(value = "数字类型备用字段1")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT01")
    private Long reserveBigint01;

    @ApiModelProperty(value = "数字类型备用字段2")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT02")
    private Long reserveBigint02 = 0L;

    @ApiModelProperty(value = "数字类型备用字段3")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT03")
    private Long reserveBigint03;

    @ApiModelProperty(value = "数字类型备用字段4")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT04")
    private Long reserveBigint04;

    @ApiModelProperty(value = "数字类型备用字段5")
    @Field(type = FieldType.Long)
    @JSONField(name = "RESERVE_BIGINT05")
    private Long reserveBigint05;

    @ApiModelProperty(value = "价格备用字段1")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL01")
    private BigDecimal reserveDecimal01;

    @ApiModelProperty(value = "价格备用字段2")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL02")
    private BigDecimal reserveDecimal02;

    @ApiModelProperty(value = "价格备用字段3")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL03")
    private BigDecimal reserveDecimal03;

    @ApiModelProperty(value = "价格备用字段4")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL04")
    private BigDecimal reserveDecimal04;

    @ApiModelProperty(value = "价格备用字段5")
    @Field(type = FieldType.Double)
    @JSONField(name = "RESERVE_DECIMAL05")
    private BigDecimal reserveDecimal05;

    @ApiModelProperty(value = "文本备用字段1")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR01")
    private String reserveVarchar01;

    @ApiModelProperty(value = "贴标要求")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR02")
    private String reserveVarchar02;

    @ApiModelProperty(value = "文本备用字段3")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR03")
    private String reserveVarchar03;

    @ApiModelProperty(value = "文本备用字段4")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR04")
    private String reserveVarchar04;

    @ApiModelProperty(value = "文本备用字段5")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RESERVE_VARCHAR05")
    private String reserveVarchar05;


    @ApiModelProperty(value = "组合商品标识")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "GROUP_GOODS_MARK")
    private String groupGoodsMark;


    @ApiModelProperty(value = "退换货单的id")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "RETURN_ORDER_ID")
    private Long returnOrderId;

    // 直播主体
    @ApiModelProperty(value = "直播主体")
    @Field(type = FieldType.Long)
    @JSONField(name = "AC_F_MANAGE_ID")
    private Long acFManageId;

    // 直播主体 经营主体ecode
    @ApiModelProperty(value = "直播主体 经营主体ecode")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "AC_F_MANAGE_ECODE")
    private String acFManageEcode;

    // 直播主体 经营主体ename
    @ApiModelProperty(value = "直播主体 经营主体ename")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "AC_F_MANAGE_ENAME")
    private String acFManageEname;

    // 配合主体
    @ApiModelProperty(value = "配合主体")
    @Field(type = FieldType.Long)
    @JSONField(name = "COOPERATE_ID")
    private Long cooperateId;

    // 配合主体 经营主体ecode
    @ApiModelProperty(value = "配合主体 经营主体ename")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "COOPERATE_ECODE")
    private String cooperateEcode;

    // 配合主体 经营主体ename
    @ApiModelProperty(value = "直播主体")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "COOPERATE_ENAME")
    private String cooperateEname;

    // 直播场次
    @ApiModelProperty(value = "直播场次")
    @Field(type = FieldType.Integer)
    @JSONField(name = "LIVE_EVENTS")
    private Integer liveEvents;

    // 预计发货日期
    @ApiModelProperty(value = "预计发货日期")
    @Field(type = FieldType.Long)
    @JSONField(name = "ESTIMATE_CON_TIME")
    private Date estimateConTime;

    // 预售类型 新
    @ApiModelProperty(value = "预售类型新")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ADVANCE_TYPE")
    private String advanceType;


    @ApiModelProperty(value = "渠道预售活动id")
    @Field(type = FieldType.Long)
    @JSONField(name = "ADVANCE_SALE_ID")
    private Long advanceSaleId;

    @ApiModelProperty(value = "渠道预售活动NO")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ADVANCE_SALE_BILL_NO")
    private String advanceSaleBillNo;

    @ApiModelProperty(value = "购物金核销子订单权益金分摊金额")
    @JSONField(name = "EXPAND_CARD_EXPAND_PRICE_USED_SUBORDER")
    @Field(type = FieldType.Double)
    private BigDecimal expandCardExpandPriceUsedSuborder;

    @ApiModelProperty(value = "购物金核销子订单本金分摊金额")
    @JSONField(name = "EXPAND_CARD_BASIC_PRICE_USED_SUBORDER")
    @Field(type = FieldType.Double)
    private BigDecimal expandCardBasicPriceUsedSuborder;

    @ApiModelProperty(value = "逻辑占用单编号")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "STO_OUT_BILL_NO")
    private String stoOutBillNo;


    @ApiModelProperty(value = "原单明细id")
    @Field(type = FieldType.Long)
    @JSONField(name = "OLD_SOURCE_ITEM_ID")
    private Long oldSourceItemId;


    @ApiModelProperty(value = "是否存在预计发货日")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "IS_EXIST_CON_TIME")
    private String isExistConTime;

    @ApiModelProperty(value = "时效订单id")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "TIME_ORDER_ID")
    private String timeOrderId;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "GW_COUPON_CODE")
    private String gwCouponCode;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "CAN_SPLIT")
    private String canSplit;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "PT_GROUP_SKU_ID")
    private String ptGroupSkuId;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "PT_GROUP_NUM_ID")
    private String ptGroupNumId;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_ENABLE_EXPIRY")
    private Integer isEnableExpiry;

    @Field(type = FieldType.Integer)
    @JSONField(name = "EXPIRY_DATE_TYPE")
    private Integer expiryDateType;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "EXPIRY_DATE_RANGE")
    private String expiryDateRange;

    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_EQUAL_EXCHANGE")
    private Integer isEqualExchange;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "EQUAL_EXCHANGE_RATIO")
    private String equalExchangeRatio;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "EQUAL_EXCHANGE_MARK")
    private String equalExchangeMark;

    @ApiModelProperty(value = "计划行类别")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "PLAN_LINE_CATEGORY")
    private String planLineCategory;

    @ApiModelProperty(value = "品类别")
    @Field(type = FieldType.Long)
    @JSONField(name = "M_DIM4_ID")
    private Long mDim4Id;

    @ApiModelProperty(value = "品项")
    @Field(type = FieldType.Long)
    @JSONField(name = "M_DIM6_ID")
    private Long mDim6Id;

    /**
     * 赠品拆单类型 1不拆单  2 可拆弹  3赠品后发
     */
    @ApiModelProperty(value = "赠品拆单类型")
    @Field(type = FieldType.Integer)
    @JSONField(name = "IS_GIFT_SPLIT")
    private Integer isGiftSplit;

    @ApiModelProperty(value = "赠品发货节点发货时点 1=主品平台发货后 2=主品签收后发 3=主品发货后多久发")
    @Field(type = FieldType.Integer)
    @JSONField(name = "GIFT_DELIVER_NODE")
    private Integer giftDeliverNode;

    @ApiModelProperty(value = "间隔时长")
    @Field(type = FieldType.Integer)
    @JSONField(name = "GIFT_INTERVAL_TIME")
    private Integer giftIntervalTime;

    @Field(type = FieldType.Double)
    @JSONField(name = "GROUP_RADIO")
    private BigDecimal groupRadio;

    @TableField(exist = false)
    private Long originalId;

    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORDER_LABEL")
    private String orderLabel;

    @ApiModelProperty(value = "替换前条码编码")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORIGIN_SKU_ECODE")
    private String originSkuEcode;

    @ApiModelProperty(value = "替换前条码数量")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "ORIGIN_SKU_QTY")
    private BigDecimal originSkuQty;

    /**
     * 周期购提数
     */
    @ApiModelProperty(value = "周期购提数")
    @JSONField(name = "CYCLE_QTY")
    @Field(type = FieldType.Long)
    private Long cycleQty;

    @ApiModelProperty(value = "实发数量")
    @Field(type = FieldType.Double)
    @JSONField(name = "REAL_OUT_NUM")
    private BigDecimal realOutNum;

    /**
     * 增值服务
     */
    @ApiModelProperty(value = "增值服务")
    @Field(type = FieldType.Keyword)
    @JSONField(name = "LABELING_REQUIREMENTS")
    private String labelingRequirements;
}
