package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 退货类型
 *
 * @author: 夏继超
 * @since: 2020/3/2
 * create at : 2020/3/2 10:57
 */
public enum ReturnProTypeEnum {
    INTERCEPT("拦截", 1),

    REJECTION("拒收", 2),

    CUSTOMERRETREAT("客退", 0);

    String key;
    Integer value;

    ReturnProTypeEnum(String key, Integer value) {
        this.key = key;
        this.value = value;

    }

    public String getKey() {
        return key;
    }

    public Integer getVal() {
        return value;
    }

    public static Map<Integer, String> getMap() {
        Map<Integer, String> map = new HashMap<>();
        for (ReturnProTypeEnum em : ReturnProTypeEnum.values()) {
            map.put(em.getVal(), em.getKey());
        }
        map.put(null, "");
        return map;
    }

}
