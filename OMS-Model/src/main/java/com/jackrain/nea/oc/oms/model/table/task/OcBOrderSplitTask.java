package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_order_split_task")
@Data
public class OcBOrderSplitTask extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "oc_b_order_id")
    private Long ocBOrderId;

    @JSONField(name = "STATUS")
    private int status;

    @JSONField(name = "split_times")
    private int splitTimes;

    @JSONField(name = "next_time")
    private Date nextTime;

    @JSONField(name = "remark")
    private String remark;
    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "version")
    @Field(type = FieldType.Long)
    private Long version;

    @JSONField(name = "pay_time")
    private Date payTime;

    @JSONField(name = "is_lack_stock")
    private int isLackStock;
}