package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_taobao_order")
@Data
@Document(index = "ip_b_taobao_order", type = "ip_b_taobao_order")
public class IpBTaobaoOrder extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "SHIPPING_TYPE")
    @Field(type = FieldType.Keyword)
    private String shippingType;

    @JSONField(name = "BUYER_EMAIL")
    @Field(type = FieldType.Keyword)
    private String buyerEmail;

    @JSONField(name = "ISREMIND")
    @Field(type = FieldType.Integer)
    private Integer isremind;

    @JSONField(name = "YFX_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal yfxFee;

    @JSONField(name = "BUYER_AREA")
    @Field(type = FieldType.Keyword)
    private String buyerArea;

    @JSONField(name = "BUYER_NICK")
    @Field(type = FieldType.Keyword)
    private String buyerNick;

    @JSONField(name = "ALIPAY_NO")
    @Field(type = FieldType.Keyword)
    private String alipayNo;

    @JSONField(name = "LG_AGING_TYPE")
    @Field(type = FieldType.Keyword)
    private String lgAgingType;

    @JSONField(name = "LG_AGING")
    @Field(type = FieldType.Long)
    private Date lgAging;

    @JSONField(name = "STEP_PAID_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal stepPaidFee;

    @JSONField(name = "STEP_TRADE_STATUS")
    @Field(type = FieldType.Keyword)
    private String stepTradeStatus;

    @JSONField(name = "NUT_FEATURE")
    @Field(type = FieldType.Keyword)
    private String nutFeature;

    @JSONField(name = "CREDIT_CARD_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal creditCardFee;

    @JSONField(name = "BUYER_MESSAGE")
    @Field(type = FieldType.Keyword)
    private String buyerMessage;

    @JSONField(name = "YFX_ID")
    @Field(type = FieldType.Keyword)
    private String yfxId;

    @JSONField(name = "COD_STATUS")
    @Field(type = FieldType.Keyword)
    private String codStatus;

    @JSONField(name = "TRADE_FROM")
    @Field(type = FieldType.Keyword)
    private String tradeFrom;

    @JSONField(name = "COD_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal codFee;

    @JSONField(name = "ADJUST_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal adjustFee;

    @JSONField(name = "SEND_TIME")
    @Field(type = FieldType.Long)
    private Date sendTime;

    @JSONField(name = "MARK_DESC")
    @Field(type = FieldType.Keyword)
    private String markDesc;

    @JSONField(name = "POST_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal postFee;

    @JSONField(name = "PAYMENT")
    @Field(type = FieldType.Double)
    private BigDecimal payment;

    @JSONField(name = "PIC_PATH")
    @Field(type = FieldType.Keyword)
    private String picPath;

    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    @JSONField(name = "TRADE_SOURCE")
    @Field(type = FieldType.Keyword)
    private String tradeSource;

    @JSONField(name = "COMMISSION_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal commissionFee;

    @JSONField(name = "RECEIVER_ZIP")
    @Field(type = FieldType.Keyword)
    private String receiverZip;

    @JSONField(name = "RECEIVER_ADDRESS")
    @Field(type = FieldType.Keyword)
    private String receiverAddress;

    @JSONField(name = "RECEIVER_DISTRICT")
    @Field(type = FieldType.Keyword)
    private String receiverDistrict;

    @JSONField(name = "RECEIVER_CITY")
    @Field(type = FieldType.Keyword)
    private String receiverCity;

    @JSONField(name = "RECEIVER_STATE")
    @Field(type = FieldType.Keyword)
    private String receiverState;

    @JSONField(name = "RECEIVER_NAME")
    @Field(type = FieldType.Keyword)
    private String receiverName;

    @JSONField(name = "SELLER_MEMO")
    @Field(type = FieldType.Keyword)
    private String sellerMemo;

    @JSONField(name = "INVOICE_NAME")
    @Field(type = FieldType.Keyword)
    private String invoiceName;

    @JSONField(name = "RECEIVED_PAYMENT")
    @Field(type = FieldType.Double)
    private BigDecimal receivedPayment;

    @JSONField(name = "RECEIVER_PHONE")
    @Field(type = FieldType.Keyword)
    private String receiverPhone;

    @JSONField(name = "RECEIVER_MOBILE")
    @Field(type = FieldType.Keyword)
    private String receiverMobile;

    @JSONField(name = "TOTAL_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal totalFee;

    @JSONField(name = "DISCOUNT_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal discountFee;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal price;

    @JSONField(name = "TYPE")
    @Field(type = FieldType.Keyword)
    private String type;

    @JSONField(name = "TITLE")
    @Field(type = FieldType.Keyword)
    private String title;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    private String status;

    @JSONField(name = "NUM_IID")
    @Field(type = FieldType.Keyword)
    private String numIid;

    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    private String tid;

    @JSONField(name = "TRANSDATE")
    @Field(type = FieldType.Long)
    private Date transdate;

    @JSONField(name = "SELLER_RATE")
    @Field(type = FieldType.Keyword)
    private String sellerRate;

    @JSONField(name = "SELLER_CAN_RATE")
    @Field(type = FieldType.Keyword)
    private String sellerCanRate;

    @JSONField(name = "BUYER_RATE")
    @Field(type = FieldType.Keyword)
    private String buyerRate;

    @JSONField(name = "BUYER_ALIPAY_NO")
    @Field(type = FieldType.Keyword)
    private String buyerAlipayNo;

    @JSONField(name = "HAS_YFX")
    @Field(type = FieldType.Keyword)
    private String hasYfx;

    @JSONField(name = "IS_FORCE_WLB")
    @Field(type = FieldType.Integer)
    private Integer isForceWlb;

    @JSONField(name = "IS_BRAND_SALE")
    @Field(type = FieldType.Integer)
    private Integer isBrandSale;

    @JSONField(name = "IS_LGTYPE")
    @Field(type = FieldType.Integer)
    private Integer isLgtype;

    @JSONField(name = "ALIPAY_ID")
    @Field(type = FieldType.Keyword)
    private String alipayId;

    @JSONField(name = "INSERTDATE")
    @Field(type = FieldType.Long)
    private Date insertdate;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Long)
    private Date endTime;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    @JSONField(name = "PAY_TIME")
    @Field(type = FieldType.Long)
    private Date payTime;

    @JSONField(name = "CREATED")
    @Field(type = FieldType.Long)
    private Date created;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "ISTRANS")
    @Field(type = FieldType.Integer)
    private Integer istrans;

    @JSONField(name = "SYSREMARK")
    @Field(type = FieldType.Keyword)
    private String sysremark;

    @JSONField(name = "TRANS_COUNT")
    @Field(type = FieldType.Long)
    private Long transCount;

    @JSONField(name = "REAL_POINT_FEE")
    @Field(type = FieldType.Long)
    private Long realPointFee;

    @JSONField(name = "BUYER_OBTAIN_POINT_FEE")
    @Field(type = FieldType.Long)
    private Long buyerObtainPointFee;

    @JSONField(name = "YFX_TYPE")
    @Field(type = FieldType.Keyword)
    private String yfxType;

    @JSONField(name = "SELLER_FLAG")
    @Field(type = FieldType.Keyword)
    private String sellerFlag;

    @JSONField(name = "POINT_FEE")
    @Field(type = FieldType.Long)
    private Long pointFee;

    @JSONField(name = "NUM")
    @Field(type = FieldType.Long)
    private Long num;

    @JSONField(name = "MARKDESC")
    @Field(type = FieldType.Long)
    private Date markdesc;

    @JSONField(name = "SERVICE_TAG")
    @Field(type = FieldType.Keyword)
    private String serviceTag;

    @JSONField(name = "SERVICE_TYPE")
    @Field(type = FieldType.Keyword)
    private String serviceType;

    @JSONField(name = "OMNICHANNEL_PARAM")
    @Field(type = FieldType.Keyword)
    private String omnichannelParam;

    @JSONField(name = "ORDERTYPE")
    @Field(type = FieldType.Keyword)
    private String ordertype;

    @JSONField(name = "TARGETCODE")
    @Field(type = FieldType.Keyword)
    private String targetcode;

    @JSONField(name = "TARGETNAME")
    @Field(type = FieldType.Keyword)
    private String targetname;

    @JSONField(name = "EST_CON_TIME")
    @Field(type = FieldType.Long)
    private Date estConTime;

    @JSONField(name = "CNSERVICE")
    @Field(type = FieldType.Integer)
    private Integer cnservice;

    @JSONField(name = "OSDATE")
    @Field(type = FieldType.Long)
    private Date osdate;

    @JSONField(name = "OSRANGE")
    @Field(type = FieldType.Long)
    private Date osrange;

    @JSONField(name = "ESDATE")
    @Field(type = FieldType.Long)
    private Date esdate;

    @JSONField(name = "ESRANGE")
    @Field(type = FieldType.Long)
    private Date esrange;

    @JSONField(name = "PUSHTIME")
    @Field(type = FieldType.Long)
    private Date pushtime;

    @JSONField(name = "ERPHOLD")
    @Field(type = FieldType.Keyword)
    private String erphold;

    @JSONField(name = "TMALLDELIVERY")
    @Field(type = FieldType.Keyword)
    private String tmalldelivery;

    @JSONField(name = "THREEPLTIMING")
    @Field(type = FieldType.Keyword)
    private String threepltiming;

    @JSONField(name = "CUTOFFMINUTES")
    @Field(type = FieldType.Keyword)
    private String cutoffminutes;

    @JSONField(name = "ESTIME")
    @Field(type = FieldType.Integer)
    private Integer estime;

    @JSONField(name = "DELIVERYTIME")
    @Field(type = FieldType.Long)
    private Date deliverytime;

    @JSONField(name = "COLLECTTIME")
    @Field(type = FieldType.Long)
    private Date collecttime;

    @JSONField(name = "SENDTIME")
    @Field(type = FieldType.Long)
    private Date sendtime;

    @JSONField(name = "SIGNTIME")
    @Field(type = FieldType.Long)
    private Date signtime;

    @JSONField(name = "DELIVERYCPS")
    @Field(type = FieldType.Keyword)
    private String deliverycps;

    @JSONField(name = "STORECODE")
    @Field(type = FieldType.Keyword)
    private String storecode;

    @JSONField(name = "GATHERLASTCENTER")
    @Field(type = FieldType.Keyword)
    private String gatherlastcenter;

    @JSONField(name = "GATHERSTATION")
    @Field(type = FieldType.Keyword)
    private String gatherstation;

    @JSONField(name = "BIZ_CODE")
    @Field(type = FieldType.Keyword)
    private String bizCode;

    @JSONField(name = "CLOUD_STORE")
    @Field(type = FieldType.Keyword)
    private String cloudStore;

    @JSONField(name = "O2O_SHOPID")
    @Field(type = FieldType.Keyword)
    private String o2oShopid;

    @JSONField(name = "O2O_SHOP_NAME")
    @Field(type = FieldType.Keyword)
    private String o2oShopName;

    @JSONField(name = "ORDERTAKING")
    @Field(type = FieldType.Long)
    private Long ordertaking;

    @JSONField(name = "PLATFORM_FLAG")
    @Field(type = FieldType.Keyword)
    private String platformFlag;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "ABNORMAL_TYPE")
    @Field(type = FieldType.Keyword)
    private Integer abnormalType;

    @JSONField(name = "PRESALE_DEPOSIT_TIME")
    @Field(type = FieldType.Long)
    private Date presaleDepositTime;

    @JSONField(name = "BASIC_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal basicPrice;

    @JSONField(name = "EXPAND_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal expandPrice;

    @JSONField(name = "BASIC_PRICE_USED")
    @Field(type = FieldType.Double)
    private BigDecimal basicPriceUsed;

    @JSONField(name = "EXPAND_PRICE_USED")
    @Field(type = FieldType.Double)
    private BigDecimal expandPriceUsed;

    @JSONField(name = "OAID")
    @Field(type = FieldType.Keyword)
    private String oaid;

    @TableField(exist=false)
    private String suggestPresinkStatus;

    @JSONField(name = "BUSINESS_TYPE")
    @Field(type = FieldType.Keyword)
    private String businessType;

    @JSONField(name = "ASDP_ADS")
    @Field(type = FieldType.Keyword)
    private String asdpAds;

    /**
     * 街道/乡镇
     */
    @JSONField(name = "RECEIVER_TOWN")
    @Field(type = FieldType.Keyword)
    private String receiverTown;
}