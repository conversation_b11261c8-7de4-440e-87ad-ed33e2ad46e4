package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_b_taobao_order_item")
@Data
@Document(index = "ip_b_taobao_order", type = "ip_b_taobao_order_item")
public class IpBTaobaoOrderItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "OID")
    @Field(type = FieldType.Long)
    private Long oid;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Keyword)
    private String status;

    @JSONField(name = "TITLE")
    @Field(type = FieldType.Keyword)
    private String title;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal price;

    @JSONField(name = "NUM_IID")
    @Field(type = FieldType.Keyword)
    private String numIid;

    @JSONField(name = "ITEM_MEAL_ID")
    @Field(type = FieldType.Keyword)
    private String itemMealId;

    @JSONField(name = "SKU_ID")
    @Field(type = FieldType.Long)
    private Long skuId;

    @JSONField(name = "NUM")
    @Field(type = FieldType.Long)
    private Long num;

    @JSONField(name = "OUTER_SKU_ID")
    @Field(type = FieldType.Keyword)
    private String outerSkuId;

    @JSONField(name = "TOTAL_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal totalFee;

    @JSONField(name = "PAYMENT")
    @Field(type = FieldType.Double)
    private BigDecimal payment;

    @JSONField(name = "DISCOUNT_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal discountFee;

    @JSONField(name = "ADJUST_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal adjustFee;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    @JSONField(name = "SKU_PROPERTIES_NAME")
    @Field(type = FieldType.Keyword)
    private String skuPropertiesName;

    @JSONField(name = "REFUND_ID")
    @Field(type = FieldType.Keyword)
    private String refundId;

    @JSONField(name = "END_TIME")
    @Field(type = FieldType.Long)
    private Date endTime;

    @JSONField(name = "CONSIGN_TIME")
    @Field(type = FieldType.Long)
    private Date consignTime;

    @JSONField(name = "SHIPPING_TYPE")
    @Field(type = FieldType.Keyword)
    private String shippingType;

    @JSONField(name = "ITEM_MEAL_NAME")
    @Field(type = FieldType.Keyword)
    private String itemMealName;

    @JSONField(name = "PIC_PATH")
    @Field(type = FieldType.Keyword)
    private String picPath;

    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    private String sellerNick;

    @JSONField(name = "BUYER_NICK")
    @Field(type = FieldType.Keyword)
    private String buyerNick;

    @JSONField(name = "REFUND_STATUS")
    @Field(type = FieldType.Keyword)
    private String refundStatus;

    @JSONField(name = "OUTER_IID")
    @Field(type = FieldType.Keyword)
    private String outerIid;

    @JSONField(name = "BUYER_RATE")
    @Field(type = FieldType.Keyword)
    private String buyerRate;

    @JSONField(name = "SELLER_RATE")
    @Field(type = FieldType.Keyword)
    private String sellerRate;

    @JSONField(name = "SELLER_TYPE")
    @Field(type = FieldType.Keyword)
    private String sellerType;

    @JSONField(name = "CID")
    @Field(type = FieldType.Integer)
    private Integer cid;

    @JSONField(name = "IP_B_TAOBAO_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBTaobaoOrderId;

    @JSONField(name = "DIVIDE_ORDER_FEE")
    @Field(type = FieldType.Double)
    private BigDecimal divideOrderFee;

    @JSONField(name = "PART_MJZ_DISCOUNT")
    @Field(type = FieldType.Double)
    private BigDecimal partMjzDiscount;

    @JSONField(name = "TEMPORARYID")
    @Field(type = FieldType.Keyword)
    private String temporaryid;

    @JSONField(name = "OUTER_SKU_ID1")
    @Field(type = FieldType.Keyword)
    private String outerSkuId1;

    @JSONField(name = "STORECODE")
    @Field(type = FieldType.Keyword)
    private String storecode;

    @JSONField(name = "BIZ_CODE")
    @Field(type = FieldType.Keyword)
    private String bizCode;

    @JSONField(name = "CLOUD_STORE")
    @Field(type = FieldType.Keyword)
    private String cloudStore;

    @JSONField(name = "O2O_SHOPID")
    @Field(type = FieldType.Keyword)
    private String o2oShopid;

    @JSONField(name = "O2O_SHOP_NAME")
    @Field(type = FieldType.Keyword)
    private String o2oShopName;

    @JSONField(name = "O2O_GUIDE_ID")
    @Field(type = FieldType.Keyword)
    private String o2oGuideId;

    @JSONField(name = "O2O_GUIDE_NAME")
    @Field(type = FieldType.Keyword)
    private String o2oGuideName;

    @JSONField(name = "ORDER_TAKING")
    @Field(type = FieldType.Keyword)
    private String orderTaking;

    @JSONField(name = "CLOUD_STORE_BINDPOS")
    @Field(type = FieldType.Keyword)
    private String cloudStoreBindpos;

    @JSONField(name = "CLOUD_STORE_TOKEN")
    @Field(type = FieldType.Keyword)
    private String cloudStoreToken;

    @JSONField(name = "O2O_VOUCHER_PRICE")
    @Field(type = FieldType.Keyword)
    private String o2oVoucherPrice;

    @JSONField(name = "HJ_SETTLE_NO_COMMISSION")
    @Field(type = FieldType.Keyword)
    private String hjSettleNoCommission;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "ESTIMATE_CON_TIME")
    @Field(type = FieldType.Keyword)
    private String estimateConTime;

    @JSONField(name = "EXPAND_CARD_EXPAND_PRICE_USED_SUBORDER")
    @Field(type = FieldType.Keyword)
    private String expandCardExpandPriceUsedSuborder;

    @JSONField(name = "EXPAND_CARD_BASIC_PRICE_USED_SUBORDER")
    @Field(type = FieldType.Keyword)
    private String expandCardBasicPriceUsedSuborder;
}