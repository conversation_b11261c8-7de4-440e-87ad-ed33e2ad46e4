package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName(value = "ip_b_jitx_reset_ship_workflow")
@Data
@Document(index = "ip_b_jitx_reset_ship_workflow",type = "ip_b_jitx_reset_ship_workflow")
@ApiModel
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IpBJitxResetShipWorkflow extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ID")
    private Long id;

    @JSONField(name = "REQUEST_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "REQUEST_ID")
    private String requestId;

    @JSONField(name = "ORDER_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ORDER_ID")
    private Long orderId;

    @JSONField(name = "ORDER_NO")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ORDER_NO")
    private String orderNo;

    @JSONField(name = "ORDER_SN")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ORDER_SN")
    private String orderSn;

    @JSONField(name = "ORDER_SN_LIST")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ORDER_SN_LIST")
    private String orderSnList;

    @JSONField(name = "WORKFLOW_SN")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "WORKFLOW_SN")
    private String workflowSn;

    @JSONField(name = "CREATED_STATUS")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "CREATED_STATUS")
    private Integer createdStatus;

    @JSONField(name = "SELLER_NICK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SELLER_NICK")
    private String sellerNick;

    @JSONField(name = "VENDOR_ID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "VENDOR_ID")
    private String vendorId;

    @JSONField(name = "WORKFLOW_STATUS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "WORKFLOW_STATUS")
    private String workflowStatus;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CP_C_SHOP_ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_ECODE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "CP_C_SHOP_ECODE")
    private String cpCShopEcode;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "CP_C_SHOP_TITLE")
    private String cpCShopTitle;

    @JSONField(name = "REASON_CODE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "REASON_CODE")
    private String reasonCode;

    @JSONField(name = "REASON_REMARK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "REASON_REMARK")
    private String reasonRemark;

    @JSONField(name = "UPDATE_TIME")
    @Field(type = FieldType.Date)
    @ApiModelProperty(name = "UPDATE_TIME")
    private Date updateTime;

    @JSONField(name = "FAIL_REASON")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "FAIL_REASON")
    private String failReason;

    @JSONField(name = "REJECT_REMARK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "REJECT_REMARK")
    private String rejectRemark;

    @JSONField(name = "RETRY_RESULT")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "RETRY_RESULT")
    private String retryResult;

    @JSONField(name = "INTERFACE_TYPE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "INTERFACE_TYPE")
    private String interfaceType;

    @JSONField(name = "FAIL_NUMBER")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "FAIL_NUMBER")
    private Integer failNumber;

    @JSONField(name = "IP_ADDRESS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "IP_ADDRESS")
    private String ipAddress;

    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "VERSION")
    private Long version;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Date)
    @ApiModelProperty(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Date)
    @ApiModelProperty(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISACTIVE")
    private String isactive;
}