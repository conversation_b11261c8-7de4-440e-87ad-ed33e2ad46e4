package com.jackrain.nea.oc.oms.model.enums;

import java.util.HashMap;
import java.util.Objects;

/**
 * @author: DXF
 * @since: 2020/12/11
 * create at : 2020/12/11 15:44
 * 日志内容前缀
 */
public enum LogTypeEnum {
    //未获取到操作场景:
    NOT_CAPTURED_SCENE(0L, ""),
    // 手动反审核按钮 前端type固定为1
    MANUAL_REVERSE_AUDIT(1L, "手动反审核按钮: "),
    AUTOMATIC_UPDATE_ADDRESS_REVERSE_AUDIT(999L, "自助修改地址反审核: "),
    REFUND_REVERSE_AUDIT(998L, "退款反审核: "),
    COMBINED_ORDERS_REVERSE_AUDIT(997L, "合并订单反审核: "),
    UPDATE_ADDRESS_REVERSE_AUDIT(995L, "修改地址反审核: "),
    LACK_REVERSE_AUDIT(994L, "实缺反审核: "),
    BILL_REVERSE_AUDIT(993L, "拆单反审核: "),
    BEFORE_SHIPMENT_REFUND_REVERSE_AUDIT(992L, "发货前退款反审核: "),
    MANUAL_HOLD_ORDER_REVERSE_AUDIT(991L, "手工Hold单反审核: "),
    JITX_FORBIDDEN_DELIVERY(990L, "JITX订单禁发反审核: "),
    JITX_REFUND_BEFORE_DELIVERY(880L, "JITX订单未发货取消反审核: "),
    JITX_REJECT_DELIVERY(870L, "JITX订单拒单反审核: "),
    TMALL_CYCLE_BUY_REVERSE_AUDIT(860L, "天猫周期购修改预计发货时间反审核: ");

    private Long type;
    private String val;
    LogTypeEnum(Long type, String val) {
        this.type = type;
        this.val = val;
    }

    public Long getType() {
        return type;
    }

    public String getVal() {
        return val;
    }
    private static HashMap<Long, String> map = new HashMap<>();
    public static String getValByType(Long type) {
        if (map.size() != LogTypeEnum.values().length) {
            for (LogTypeEnum value : LogTypeEnum.values()) {
                map.put(value.getType(), value.getVal());
            }
        }
        if (Objects.nonNull(type) && map.containsKey(type)) {
            return map.get(type);
        }
        return "";
    }
}
