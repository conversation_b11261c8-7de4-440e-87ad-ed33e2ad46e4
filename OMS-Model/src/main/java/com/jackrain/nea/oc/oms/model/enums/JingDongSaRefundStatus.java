package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.oc.oms.model.taobao.TaobaoReturnOrderExt;

import java.lang.annotation.ElementType;

/**
 * @description: 京东审核状态
 * @author: 郑小龙
 * @date: 2020-06-01 17:50
 **/
public enum JingDongSaRefundStatus {

    /**
     * 未审核
     */
    NOAUDIT,

    /**
     * 审核通过
     */
    AUDITPASS,

    /**
     * 审核不通过
     */
    NOAUDITPASS,

    /**
     * 京东财务审核通过
     */
    JDFINANCEAUDITPASS,

    /**
     * 京东财务审核不通过
     */
    NOJDFINANCEAUDITPASS,

    /**
     * 人工审核通过
     */
    ARTIFICIALAUDITPASS,

    /**
     * 拦截并退款
     */
    INTERCEPTREFUND,
    /**
     * 青龙拦截成功
     */
    QLINTERCEPTSUCC,
    /**
     * 青龙拦截失败
     */
    QLINTERCEPTFAIL,
    /**
     * 强制关单并退款
     */
    FORCIBLYOFFSINGLEREFUND,
    /**
     * 物流待跟进
     */
    LOGISTICSFOLLOWUP,
    /**
     * 用户撤销
     */
    USERREVOKED;

    public int toInteger() {
        if (this == JingDongSaRefundStatus.NOAUDIT) {
            return 0;
        }
        if (this == JingDongSaRefundStatus.AUDITPASS) {
            return 1;
        } else if (this == JingDongSaRefundStatus.NOAUDITPASS) {
            return 2;
        } else if (this == JingDongSaRefundStatus.JDFINANCEAUDITPASS) {
            return 3;
        } else if (this == JingDongSaRefundStatus.NOJDFINANCEAUDITPASS) {
            return 4;
        } else if (this == JingDongSaRefundStatus.ARTIFICIALAUDITPASS) {
            return 5;
        } else if (this == JingDongSaRefundStatus.INTERCEPTREFUND) {
            return 6;
        } else if (this == JingDongSaRefundStatus.QLINTERCEPTSUCC) {
            return 7;
        } else if (this == JingDongSaRefundStatus.QLINTERCEPTFAIL) {
            return 8;
        } else if (this == JingDongSaRefundStatus.FORCIBLYOFFSINGLEREFUND) {
            return 9;
        } else if (this == JingDongSaRefundStatus.LOGISTICSFOLLOWUP) {
            return 10;
        } else if (this == JingDongSaRefundStatus.USERREVOKED) {
            return 11;
        } else {
            return -1;
        }
    }
}
