package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "ST_C_HOLD_PROVINCE_ITEM")
@Data
public class StCHoldProvinceItemDO extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "HOLD_ORDER_ID")
    private Long holdOrderId;

    @JSONField(name = "SELLER_PROVINCE_ID")
    private Long sellerProvinceId;

    @JSONField(name = "SELLER_CITY_ID")
    private Long sellerCityId;

    @J<PERSON>NField(name = "SELLER_AREA_ID")
    private Long sellerAreaId;

    @J<PERSON>NField(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    private String modifierename;
}