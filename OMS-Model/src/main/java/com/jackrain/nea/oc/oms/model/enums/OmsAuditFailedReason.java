package com.jackrain.nea.oc.oms.model.enums;

/**
 * 审核失败原因
 *
 * @Auther: 黄志优
 * @Date: 2020/11/2 13:54
 * @Description:
 */
public enum OmsAuditFailedReason {

    ERROR_99("订单执行审单流程异常", OmsAuditFailedType.OTHER_04.getVal()),
    DEFAULT_00("", OmsAuditFailedType.DEFAULT_00.getVal()),
    ERROR_01("只有待审核并且非拦截状态才可以审核", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_02("订单明细不存在", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_03("下单店铺为空", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_04("下单日期为空", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_05("付款时间不在自动审核时间范围内", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_06("全赠品订单自动审核未开启", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_07("手工新增单自动审核未开启", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_08("省或者市未维护", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_09("发货仓库为空", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_10("付款类型为空", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_11(" 发货物流为空", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_12("总金额为空", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_13("平台类型为空", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_14("订单类型为空", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_15("预售尾款未付为空", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_16("该订单类型不能自动审核", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_17("淘宝地址待修改", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_18("货到付款订单自动审核未开启", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_19("买家、卖家备注不符合自动审核条件", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_20("发货仓库或者物流无效", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_38("唯品会JITX订单物流单号不能为空", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_21("订单的明细商品成交价不正确", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_22("订单收货信息包含不能审核的关键字", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_23("京东货到付款订单非京东物流", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_24("订单存在重单", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_25("订单店铺自动审核策略限制了发货物流公司", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_26("双十一订单尾款未付", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_27("手工新增货到付款必须大于0", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_28(" 在线支付订单，代收货款及服务费必须为0", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_29(" JITX订单未改仓成功", OmsAuditFailedType.BUSINESS_03.getVal()),//调用接口
    ERROR_30("淘宝待修改表未更新", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_31("更新订单状态失败", OmsAuditFailedType.OTHER_04.getVal()),
    ERROR_32("订单主表商品金额与明细不一致", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_33("订单商品价格低于价格策略最低成交价", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_34("换货未完成", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_35("订单金额不符合自动审核条件", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_36("代销资金占用失败", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_37("组合福袋商品成交金额计算错误", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_39("订单折扣不符合自动审核条件", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_40("订单满足拆单不符合自动审核条件", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_41("不满足订单主表总金额不小于最低成交金额", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_42("不满足整单折扣不小于最低折扣", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_43("不满足订单中商品条码成交价不低于策略方案成交价", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_44("订单为JITX订单，且存在可合并的订单", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_45("唯品会JITX订单,订单已禁止发货", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_46("单条码数量上限不符合自动审核条件", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_47("预售尾款未付为空,且无需预下沉的定金预售订单", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_480("JitX订单正在进行发货重置未通过", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_490("JitX订单合包订单提前调用平台发货失败", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_50("预售尾款未付为空,且无需预下沉的定金预售订单", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_51("单据平台状态交易关闭,审核失败", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_48("单据平台状态交易关闭,审核失败", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_49("订单处于锁单状态,不允许自动审核", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_52("发货仓库为YY实体仓,寻仓结果表未返回YY仓库占用结果", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_53("订单自定义打标标签不符合自动审核", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_54("自动审核排除商品不能审核", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_55("自动审核排除仓库不能审核", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_56("自动审核排除订单业务类型不能审核", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_57("店铺已经冻结不能审核", OmsAuditFailedType.STRATEGY_02.getVal()),
    ERROR_58("该业务类型订单无法自动审核", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_59("订单处于卡单状态,不允许自动审核", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_60("订单总金额/已支付金额/明细成交金额,不允许为负数", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_61("平台订单所属店铺已冻结不可用", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_62("发货工厂与指定工厂不符", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_63("商品效期范围为空,无法审核", OmsAuditFailedType.BUSINESS_03.getVal()),
    ERROR_64("该业务类型的客户不允许发 toC类型订单！", OmsAuditFailedType.BASE_01.getVal()),
    ERROR_65("订单中存在效期倒挂商品，请重新寻源！倒挂的商品效期已重新修改！无法审核", OmsAuditFailedType.BASE_01.getVal());

    /**
     * 描述
     */
    String key;

    /**
     * 策略页面值
     */
    int val;


    public String getKey() {
        return key;
    }

    public int getVal() {
        return val;
    }

    OmsAuditFailedReason(String k, int v) {
        this.key = k;
        this.val = v;
    }


}
