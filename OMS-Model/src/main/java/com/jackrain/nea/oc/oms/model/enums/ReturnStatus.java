package com.jackrain.nea.oc.oms.model.enums;

/**
 * @author: 夏继超
 * @since: 2019/3/25
 * create at : 2019/3/25 20:07
 */
public enum ReturnStatus {
    //1等待入库，2已入库，3入库作废,4未关联退货单 5.异常
    /**
     * 等待入库
     */
    WAITING_FOR_STORAGE,
    /**
     * 已入库
     */
    WAREHOUSING,
    /**
     * 入库作废
     */
    WAREHOUSING_AND_SCRAP,
    /**
     * 未关联退货单
     */
    UNCORRELATED_RETURN_BILL,

    REFUND_EXCEPTION;

    public Integer toInteger() {
        if (this == ReturnStatus.WAITING_FOR_STORAGE) {
            return 1;
        } else if (this == ReturnStatus.WAREHOUSING) {
            return 2;
        } else if (this == ReturnStatus.WAREHOUSING_AND_SCRAP) {
            return 3;
        } else if (this == ReturnStatus.REFUND_EXCEPTION) {
            return 5;
        } else {
            return 4;
        }
    }

    public String toDescription() {
        if (this == ReturnStatus.WAITING_FOR_STORAGE) {
            return "WAITING_FOR_STORAGE";
        } else if (this == ReturnStatus.WAREHOUSING) {
            return "WAREHOUSING";
        } else if (this == ReturnStatus.WAREHOUSING_AND_SCRAP) {
            return "WAREHOUSING_AND_SCRAP";
        } else {
            return "UNCORRELATED_RETURN_BILL";
        }
    }
}
