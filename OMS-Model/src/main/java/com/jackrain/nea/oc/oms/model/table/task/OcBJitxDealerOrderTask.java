package com.jackrain.nea.oc.oms.model.table.task;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@TableName(value = "oc_b_jitx_dealer_order_task")
@Data
@Document(index = "oc_b_jitx_dealer_order_task", type = "oc_b_jitx_dealer_order_task")
@ApiModel
public class OcBJitxDealerOrderTask extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ID")
    private Long id;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty("店铺ID")
    private Long cpCShopId;

    @JSONField(name = "CP_C_PLATFORM_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty("平台ID")
    private Long cpCPlatformId;

    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty("平台单号")
    private String tid;

    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty("业务单据编号")
    private String billNo;

    @JSONField(name = "ORDER_TYPE")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "业务单据类型 1 时效单 2 发货单")
    private Integer orderType;

    @JSONField(name = "STATUS")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "状态: 0未处理 1成功 2 失败")
    private Integer state;

    @JSONField(name = "TYPE")
    @Field(type = FieldType.Integer)
    @ApiModelProperty("类型")
    private Integer type;

    @JSONField(name = "FAIL_NUMBER")
    @Field(type = FieldType.Integer)
    @ApiModelProperty("失败次数")
    private Integer failNumber;

    @JSONField(name = "MSG")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty("请求信息")
    private String msg;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "VERSION")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "VERSION")
    private Long version;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Date)
    @ApiModelProperty(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Date)
    @ApiModelProperty(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "MODIFY_WAREHOUSE_LOG_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "JITX改仓中间表id（任务类型为申请改仓时必填）")
    private Long modifyWarehouseLogId;

    @JSONField(name = "cp_c_phy_warehouse_id")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "中台占用实体仓id")
    private Long cpCPhyWarehouseId;
}