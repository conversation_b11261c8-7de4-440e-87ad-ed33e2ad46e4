package com.jackrain.nea.oc.oms.model.table;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;

@TableName(value = "ip_b_jingdong_order_item")
@Data
@Document(index = "ip_b_jingdong_order_item", type = "ip_b_jingdong_order_item")
public class IpBJingdongOrderItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "SKU_ID")
    @Field(type = FieldType.Long)
    private Long skuId;

    @JSONField(name = "OUTER_SKU_ID")
    @Field(type = FieldType.Keyword)
    private String outerSkuId;

    @JSONField(name = "SKU_NAME")
    @Field(type = FieldType.Keyword)
    private String skuName;

    @JSONField(name = "JD_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal jdPrice;

    @JSONField(name = "GIFT_POINT")
    @Field(type = FieldType.Long)
    private Long giftPoint;

    @JSONField(name = "WARE_ID")
    @Field(type = FieldType.Keyword)
    private String wareId;

    @JSONField(name = "ITEM_TOTAL")
    @Field(type = FieldType.Long)
    private Long itemTotal;

    @JSONField(name = "PRODUCT_NO")
    @Field(type = FieldType.Keyword)
    private String productNo;

    @JSONField(name = "IP_B_JINGDONG_ORDER_ID")
    @Field(type = FieldType.Long)
    private Long ipBJingdongOrderId;

    @JSONField(name = "AVERAGEDISCOUNT")
    @Field(type = FieldType.Double)
    private BigDecimal averagediscount;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "ISMATCH")
    @Field(type = FieldType.Integer)
    private Integer ismatch;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal01;
}