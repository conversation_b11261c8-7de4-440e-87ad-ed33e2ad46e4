package com.jackrain.nea.oc.oms.model.enums;

import lombok.Getter;

/**
 * @ClassName BnDetainedItemColumnEnum
 * @Description 班牛滞留件工单字段枚举
 * <AUTHOR>
 * @Date 2025/1/15 16:30
 * @Version 1.0
 */
@Getter
public enum BnDetainedItemColumnEnum {

    /**
     * 发货仓库
     */
    WAREHOUSE_OUT("warehouseOut", "发货仓库", "22450"),

    /**
     * 退货仓库
     */
    WAREHOUSE_IN("warehouseIn", "退货仓库", "22451"),

    /**
     * 快递公司
     */
    LOGISTICS_COMPANY("logisticsCompany", "快递公司", "22452"),

    /**
     * 快递单号
     */
    LOGISTICS_CODE("logisticsCode", "快递单号", "22453"),

    /**
     * 快递举证说明
     */
    LOGISTICS_EVIDENCE_DESC("logisticsEvidenceDesc", "快递举证说明", "22457"),

    /**
     * 快递附件上传
     */
    LOGISTICS_ATTACHMENT("logisticsAttachment", "快递附件上传", "22458"),

    /**
     * 判责结果
     */
    JUDGMENT_RESULT("judgmentResult", "判责结果", "22463"),

    /**
     * 任务状态
     */
    TASK_STATUS("taskStatus", "任务状态", "5"),

    /**
     * 关闭原因
     */
    CLOSE_REASON("closeReason", "关闭原因", "22464"),

    /**
     * OMS创建人
     */
    OMS_CREATOR("omsCreator", "OMS创建人", "22465"),

    /**
     * 创建人
     */
    CREATOR("creator", "创建人", "1"),

    /**
     * 执行人
     */
    EXECUTOR("executor", "执行人", "2"),

    /**
     * 创建时间
     */
    CREATE_TIME("createTime", "创建时间", "3"),

    /**
     * 截止时间
     */
    DEADLINE("deadline", "截止时间", "7"),

    /**
     * 修改时间
     */
    MODIFY_TIME("modifyTime", "修改时间", "4"),

    /**
     * 标题
     */
    TITLE("title", "标题", "6"),

    /**
     * 任务完结人
     */
    TASK_COMPLETER("taskCompleter", "任务完结人", "8"),

    /**
     * 任务完结时间
     */
    TASK_COMPLETE_TIME("taskCompleteTime", "任务完结时间", "9");

    /**
     * 字段名称
     */
    private final String columnName;

    /**
     * 字段描述
     */
    private final String columnDesc;

    /**
     * 字段ID
     */
    private final String columnId;

    BnDetainedItemColumnEnum(String columnName, String columnDesc, String columnId) {
        this.columnName = columnName;
        this.columnDesc = columnDesc;
        this.columnId = columnId;
    }
}
