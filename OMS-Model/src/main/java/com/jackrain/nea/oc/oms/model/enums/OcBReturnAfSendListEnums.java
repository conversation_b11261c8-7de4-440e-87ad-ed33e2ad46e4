package com.jackrain.nea.oc.oms.model.enums;

import com.jackrain.nea.oc.oms.model.relation.OcBReturnAfSendExtend;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 已发货后退款单
 *
 * @author: xiwen.z
 * create at: 2019/3/26 0026
 */
public class OcBReturnAfSendListEnums {

    private static final String EMPSTRING = "";

    /**
     * 退款类型
     */
    public enum ReturnTypeEnum {
        RETURNREFUND(0, "退货退款"),
        RETURNGOODS(1, "仅退款");

        int val;
        String text;

        ReturnTypeEnum(int v, String s) {
            this.val = v;
            this.text = s;
        }

        public int getVal() {
            return val;
        }

        public String getText() {
            return text;
        }


    }

    /**
     * 支付方式
     */
    public enum PayTypeEnum {
        Alipay("1", "支付宝"),
        WeChat("2", "微信"),
        CASH("3", "现金"),
        RESERVE_FUND("4", "备用金"),
        CAI_FU_TONG("5", "财付通"),
        Bank("6", "银行");

        String val;
        String text;

        PayTypeEnum(String v, String n) {
            this.val = v;
            this.text = n;
        }


        public String getVal() {
            return val;
        }

        public String getText() {
            return text;
        }

        /**
         * 获取支付方式文本
         *
         * @param v integer
         * @return string
         */
        public static String getTextByVal(String v) {
            for (PayTypeEnum e : PayTypeEnum.values()) {
                if (e.getVal().equalsIgnoreCase(v)) {
                    return e.getText();
                }
            }
            return EMPSTRING;
        }
    }

    /**
     * 判责方
     */
    public enum ResponsiblePartyEnum {
//        TP("1", "TP"),
//        Czech("2", "科捷"),
//        Procter_Gamble("4", "宝洁"),
//        GREEN_HAND("3", "菜鸟"),
//        For_236("5", "236"),
//        Customer("6", "客户"),
//        Tmall("7", "天猫")
        ;
        String val;
        String text;

        ResponsiblePartyEnum(String v, String x) {
            this.val = v;
            this.text = x;
        }


        public String getVal() {
            return val;
        }

        public String getText() {
            return text;
        }

        /**
         * 转为map
         *
         * @return map
         */
        public static Map convertAllToHashVal() {
            Map<String, String> m = new HashMap<>();
            for (OcBReturnAfSendListEnums.ResponsiblePartyEnum e : OcBReturnAfSendListEnums.ResponsiblePartyEnum.values()) {
                m.put(e.getVal(), e.getText());
            }
            return m;
        }

    }

    public enum ToSapStatusEunm {
        //        （0:默认，1:待传，2：已传，3:失败）
        DEFAULT(0L, "默认"),

        PENDING(1L, "待传"),

        ALREADYTRANSMITTED(2L, "已传"),

        FAIL(3L, "失败");

        ToSapStatusEunm(Long v, String n) {
            this.val = v;
            this.text = n;
        }

        Long val;
        String text;

        public Long getVal() {
            return val;
        }

        public String getText() {
            return text;
        }
    }

    private static Map<String, String> responsiblePartyValues = new HashMap<>(); //判责方
    private static Map<String, String> payTypeValues = new HashMap<>(); //支付方式
    private static Map<String, Integer> returnTypeValues = new HashMap<>(); //退款类型

    static {
        //退款类型
        OcBReturnAfSendListEnums.ReturnTypeEnum[] orderType = OcBReturnAfSendListEnums.ReturnTypeEnum.values();
        for (OcBReturnAfSendListEnums.ReturnTypeEnum returnTypeEnum : orderType) {
            returnTypeValues.put(returnTypeEnum.getText(), returnTypeEnum.getVal());
        }
        //支付方式
        OcBReturnAfSendListEnums.PayTypeEnum[] occupyStatus = OcBReturnAfSendListEnums.PayTypeEnum.values();
        for (OcBReturnAfSendListEnums.PayTypeEnum payTypeEnum : occupyStatus) {
            payTypeValues.put(payTypeEnum.getText(), payTypeEnum.getVal());
        }
        //判责方
        OcBReturnAfSendListEnums.ResponsiblePartyEnum[] platForm = OcBReturnAfSendListEnums.ResponsiblePartyEnum.values();
        for (OcBReturnAfSendListEnums.ResponsiblePartyEnum responsiblePartyEnum : platForm) {
            responsiblePartyValues.put(responsiblePartyEnum.getText(), responsiblePartyEnum.getVal());
        }

    }

    //字段置换  导入文字转换成字段值
    public static void changeImportList(List<OcBReturnAfSendExtend> dbList) {
        List<OcBReturnAfSendExtend> collect = dbList.parallelStream().map(ex -> {
            if (ex.getPayMode() != null) {
                ex.setPayMode(payTypeValues.get(ex.getPayMode()));
            }
            if (ex.getBillType() != null) {
//                ex.setBillType(returnTypeValues.get(ex.getBillType()));
                ex.setBillType(1);
            } else {
                ex.setBillType(1);
            }
            if (ex.getResponsibleParty() != null) {
                ex.setResponsibleParty(responsiblePartyValues.get(ex.getResponsibleParty()));
            }


            return ex;
        }).collect(Collectors.toList());
    }


}
