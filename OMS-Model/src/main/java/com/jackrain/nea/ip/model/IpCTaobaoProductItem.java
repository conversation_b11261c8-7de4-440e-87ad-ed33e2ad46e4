package com.jackrain.nea.ip.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ip_c_taobao_product_item")
@Data
@Document(index = "ip_c_taobao_product_item", type = "ip_c_taobao_product_item")
public class IpCTaobaoProductItem extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "CREATED")
    @Field(type = FieldType.Long)
    private Date created;

    @JSONField(name = "MODIFIED")
    @Field(type = FieldType.Long)
    private Date modified;

    @JSONField(name = "OUTER_ID")
    @Field(type = FieldType.Keyword)
    private String outerId;

    @JSONField(name = "PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal price;

    @JSONField(name = "PROPERTIES")
    @Field(type = FieldType.Keyword)
    private String properties;

    @JSONField(name = "PROPERTIES_NAME")
    @Field(type = FieldType.Keyword)
    private String propertiesName;

    @JSONField(name = "QUANTITY")
    @Field(type = FieldType.Long)
    private Long quantity;

    @JSONField(name = "SKU_ID")
    @Field(type = FieldType.Keyword)
    private String skuId;

    @JSONField(name = "WITH_HOLD_QUANTITY")
    @Field(type = FieldType.Integer)
    private Integer withHoldQuantity;

    @JSONField(name = "IP_C_TAOBAO_PRODUCT_ID")
    @Field(type = FieldType.Long)
    private Long ipCTaobaoProductId;

    @JSONField(name = "DELETE_FLAG")
    @Field(type = FieldType.Integer)
    private Integer deleteFlag;

    @JSONField(name = "DELETE_FLAG2")
    @Field(type = FieldType.Integer)
    private Integer deleteFlag2;

    @JSONField(name = "BARCODE")
    @Field(type = FieldType.Keyword)
    private String barcode;

    @JSONField(name = "ISMATCH")
    @Field(type = FieldType.Keyword)
    private String ismatch;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;
}