package com.jackrain.nea.ad.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@TableName(value = "ad_column")
@Data
@Document(index = "ad_column", type = "ad_column")
@ApiModel
public class AdColumn extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ID")
    private Long id;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "NAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "NAME")
    private String name;

    @JSONField(name = "DESCRIPTION")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "DESCRIPTION")
    private String description;

    @JSONField(name = "COMMENTS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "COMMENTS")
    private String comments;

    @JSONField(name = "ORDERNO")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ORDERNO")
    private Long orderno;

    @JSONField(name = "SUMMETHOD")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SUMMETHOD")
    private String summethod;

    @JSONField(name = "COLTYPE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "COLTYPE")
    private String coltype;

    @JSONField(name = "COLLENGTH")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "COLLENGTH")
    private Long collength;

    @JSONField(name = "COLPRECISION")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "COLPRECISION")
    private Long colprecision;

    @JSONField(name = "NULLABLE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "NULLABLE")
    private String nullable;

    @JSONField(name = "MASK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MASK")
    private String mask;

    @JSONField(name = "REF_TABLE_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "REF_TABLE_ID")
    private Long refTableId;

    @JSONField(name = "REF_COLUMN_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "REF_COLUMN_ID")
    private Long refColumnId;

    @JSONField(name = "OBTAINMANNER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OBTAINMANNER")
    private String obtainmanner;

    @JSONField(name = "AD_LIMITVALUE_GROUP_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_LIMITVALUE_GROUP_ID")
    private Long adLimitvalueGroupId;

    @JSONField(name = "DEFAULTVALUE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "DEFAULTVALUE")
    private String defaultvalue;

    @JSONField(name = "MODIFIABLE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIABLE")
    private String modifiable;

    @JSONField(name = "REGEXPRESSION")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "REGEXPRESSION")
    private String regexpression;

    @JSONField(name = "ERRMSG")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ERRMSG")
    private String errmsg;

    @JSONField(name = "INTERPRETER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "INTERPRETER")
    private String interpreter;

    @JSONField(name = "FILTER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "FILTER")
    private String filter;

    @JSONField(name = "DISPLAYTYPE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "DISPLAYTYPE")
    private String displaytype;

    @JSONField(name = "DISPLAYROWS")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "DISPLAYROWS")
    private Long displayrows;

    @JSONField(name = "DISPLAYCOLS")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "DISPLAYCOLS")
    private Long displaycols;

    @JSONField(name = "DISPLAYWIDTH")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "DISPLAYWIDTH")
    private Long displaywidth;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "ISSYSTEM")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISSYSTEM")
    private String issystem;

    @JSONField(name = "AD_TABLE_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_TABLE_ID")
    private Long adTableId;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "U_CLOB_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "U_CLOB_ID")
    private Long uClobId;

    @JSONField(name = "DBNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "DBNAME")
    private String dbname;

    @JSONField(name = "ISAK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISAK")
    private String isak;

    @JSONField(name = "AD_SEQUENCE_ID_BAD")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_SEQUENCE_ID_BAD")
    private Long adSequenceIdBad;

    @JSONField(name = "ISDK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISDK")
    private String isdk;

    @JSONField(name = "STATSIZE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "STATSIZE")
    private Long statsize;

    @JSONField(name = "SEQUENCENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SEQUENCENAME")
    private String sequencename;

    @JSONField(name = "ISUPPERCASE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISUPPERCASE")
    private String isuppercase;

    @JSONField(name = "ISINDEXED")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISINDEXED")
    private String isindexed;

    @JSONField(name = "ONDELETE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ONDELETE")
    private String ondelete;

    @JSONField(name = "PROPS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "PROPS")
    private String props;

    @JSONField(name = "CUSTOMIZENO")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "CUSTOMIZENO")
    private String customizeno;

    @JSONField(name = "SHOW_COMMENTS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SHOW_COMMENTS")
    private String showComments;

    @JSONField(name = "SHOW_TITLE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SHOW_TITLE")
    private String showTitle;

    @JSONField(name = "ROWSPAN")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ROWSPAN")
    private String rowspan;

    @JSONField(name = "ISORDER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISORDER")
    private String isorder;

    @JSONField(name = "SEARCHFOLDNUM")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "SEARCHFOLDNUM")
    private Long searchfoldnum;

    @JSONField(name = "ISSHOWEXTENDPAGE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISSHOWEXTENDPAGE")
    private String isshowextendpage;

    @JSONField(name = "REF_HRCOLUMN_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "REF_HRCOLUMN_ID")
    private Long refHrcolumnId;

    @JSONField(name = "FKDISPLAY")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "FKDISPLAY")
    private String fkdisplay;

    @JSONField(name = "QUERYDEFVAL")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "QUERYDEFVAL")
    private String querydefval;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "SEARCHTYPE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SEARCHTYPE")
    private String searchtype;

    @JSONField(name = "ISREMOTE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISREMOTE")
    private String isremote;

    @JSONField(name = "SEARCHMODEL")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SEARCHMODEL")
    private String searchmodel;

    @JSONField(name = "COMMENTSTP")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "COMMENTSTP")
    private String commentstp;

    @JSONField(name = "ISTHOUSAND")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISTHOUSAND")
    private String isthousand;

    @JSONField(name = "ISAGFILTER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISAGFILTER")
    private String isagfilter;

    @JSONField(name = "AGFILTER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "AGFILTER")
    private String agfilter;

    @JSONField(name = "COLUMN_DESC")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "COLUMN_DESC")
    private String columnDesc;
}