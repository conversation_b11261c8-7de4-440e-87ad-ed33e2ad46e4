package com.jackrain.nea.ad.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@TableName(value = "ad_table")
@Data
@Document(index = "ad_table", type = "ad_table")
@ApiModel
public class AdTable extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ID")
    private Long id;

    @JSONField(name = "AD_CLIENT_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_CLIENT_ID")
    private Long adClientId;

    @JSONField(name = "ISACTIVE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISACTIVE")
    private String isactive;

    @JSONField(name = "NAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "NAME")
    private String name;

    @JSONField(name = "REALTABLE_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "REALTABLE_ID")
    private Long realtableId;

    @JSONField(name = "FILTER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "FILTER")
    private String filter;

    @JSONField(name = "DESCRIPTION")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "DESCRIPTION")
    private String description;

    @JSONField(name = "MASK")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MASK")
    private String mask;

    @JSONField(name = "AD_TABLECATEGORY_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_TABLECATEGORY_ID")
    private Long adTablecategoryId;

    @JSONField(name = "ORDERNO")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ORDERNO")
    private Long orderno;

    @JSONField(name = "URL")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "URL")
    private String url;

    @JSONField(name = "PK_COLUMN_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "PK_COLUMN_ID")
    private Long pkColumnId;

    @JSONField(name = "AK_COLUMN_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AK_COLUMN_ID")
    private Long akColumnId;

    @JSONField(name = "ISDISPATCHABLE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISDISPATCHABLE")
    private String isdispatchable;

    @JSONField(name = "DISP_COLUMN_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "DISP_COLUMN_ID")
    private Long dispColumnId;

    @JSONField(name = "U_CLOB_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "U_CLOB_ID")
    private Long uClobId;

    @JSONField(name = "DIRECTORY_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "DIRECTORY_ID")
    private Long directoryId;

    @JSONField(name = "ISSYSTEM")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISSYSTEM")
    private String issystem;

    @JSONField(name = "HAS_TRIG_AM")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "HAS_TRIG_AM")
    private String hasTrigAm;

    @JSONField(name = "TRIG_AM")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_AM")
    private String trigAm;

    @JSONField(name = "HAS_TRIG_BM")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "HAS_TRIG_BM")
    private String hasTrigBm;

    @JSONField(name = "TRIG_BM")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_BM")
    private String trigBm;

    @JSONField(name = "HAS_TRIG_BD")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "HAS_TRIG_BD")
    private String hasTrigBd;

    @JSONField(name = "TRIG_BD")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_BD")
    private String trigBd;

    @JSONField(name = "CREATIONDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "CREATIONDATE")
    private Date creationdate;

    @JSONField(name = "MODIFIEDDATE")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIEDDATE")
    private Date modifieddate;

    @JSONField(name = "OWNERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "OWNERID")
    private Long ownerid;

    @JSONField(name = "MODIFIERID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "MODIFIERID")
    private Long modifierid;

    @JSONField(name = "COMMENTS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "COMMENTS")
    private String comments;

    @JSONField(name = "AD_ORG_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_ORG_ID")
    private Long adOrgId;

    @JSONField(name = "HAS_TRIG_AC")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "HAS_TRIG_AC")
    private String hasTrigAc;

    @JSONField(name = "TRIG_AC")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_AC")
    private String trigAc;

    @JSONField(name = "PROC_SUBMIT")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "PROC_SUBMIT")
    private String procSubmit;

    @JSONField(name = "PARENT_COLUMN_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "PARENT_COLUMN_ID")
    private Long parentColumnId;

    @JSONField(name = "ISTREE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISTREE")
    private String istree;

    @JSONField(name = "DK_COLUMN_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "DK_COLUMN_ID")
    private Long dkColumnId;

    @JSONField(name = "SUMMARY_COLUMN_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "SUMMARY_COLUMN_ID")
    private Long summaryColumnId;

    @JSONField(name = "CLASSNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "CLASSNAME")
    private String classname;

    @JSONField(name = "ISMENUOBJ")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISMENUOBJ")
    private String ismenuobj;

    @JSONField(name = "ISSMS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISSMS")
    private String issms;

    @JSONField(name = "ROWCNT")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "ROWCNT")
    private Long rowcnt;

    @JSONField(name = "ISDROPDOWN")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISDROPDOWN")
    private String isdropdown;

    @JSONField(name = "ISBIG")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISBIG")
    private String isbig;

    @JSONField(name = "PARENT_TABLE_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "PARENT_TABLE_ID")
    private Long parentTableId;

    @JSONField(name = "AD_OBJUICONF_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_OBJUICONF_ID")
    private Long adObjuiconfId;

    @JSONField(name = "PROPS")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "PROPS")
    private String props;

    @JSONField(name = "ALIASNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ALIASNAME")
    private String aliasname;

    @JSONField(name = "CUSTOMIZENO")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "CUSTOMIZENO")
    private String customizeno;

    @JSONField(name = "AD_ACCORDION_ID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "AD_ACCORDION_ID")
    private Long adAccordionId;

    @JSONField(name = "TABLE_IMG")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TABLE_IMG")
    private String tableImg;

    @JSONField(name = "AD_ACCORDION_IMG")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "AD_ACCORDION_IMG")
    private String adAccordionImg;

    @JSONField(name = "SEARCHFOLDNUM")
    @Field(type = FieldType.Integer)
    @ApiModelProperty(name = "SEARCHFOLDNUM")
    private Integer searchfoldnum;

    @JSONField(name = "ISSHOWEXTENDPAGE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ISSHOWEXTENDPAGE")
    private String isshowextendpage;

    @JSONField(name = "ASPECTTRIGGER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "ASPECTTRIGGER")
    private String aspecttrigger;

    @JSONField(name = "TRIG_DOADD")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_DOADD")
    private String trigDoadd;

    @JSONField(name = "TRIG_DOSAVE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_DOSAVE")
    private String trigDosave;

    @JSONField(name = "TRIG_DODELETE")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_DODELETE")
    private String trigDodelete;

    @JSONField(name = "TRIG_DOVOID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_DOVOID")
    private String trigDovoid;

    @JSONField(name = "TRIG_DOUNSUBMIT")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_DOUNSUBMIT")
    private String trigDounsubmit;

    @JSONField(name = "TRIG_DOUNVOID")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "TRIG_DOUNVOID")
    private String trigDounvoid;

    @JSONField(name = "MODIFIERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERNAME")
    private String modifiername;

    @JSONField(name = "OWNERNAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERNAME")
    private String ownername;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "MODIFIERENAME")
    private String modifierename;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "OWNERENAME")
    private String ownerename;

    @JSONField(name = "SOLRTRIGGER")
    @Field(type = FieldType.Keyword)
    @ApiModelProperty(name = "SOLRTRIGGER")
    private String solrtrigger;

    @JSONField(name = "IMPCOLID")
    @Field(type = FieldType.Long)
    @ApiModelProperty(name = "IMPCOLID")
    private Long impcolid;
}