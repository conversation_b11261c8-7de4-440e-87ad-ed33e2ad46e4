package com.jackrain.nea.ad.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

@TableName(value = "AD_PARAM")
@Data
public class AdParam extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    @JSONField(name = "NAME")
    private String name;

    @J<PERSON>NField(name = "DEFAULTVALUE")
    private String defaultvalue;

    @J<PERSON>NField(name = "VALUE")
    private String value;

    @J<PERSON><PERSON>ield(name = "VALUETYPE")
    private String valuetype;

    @J<PERSON>NField(name = "PARMTYPE")
    private String parmtype;

    @J<PERSON><PERSON><PERSON>(name = "VALUELIST")
    private String valuelist;

    @J<PERSON><PERSON>ield(name = "DESCRIPTION")
    private String description;

    @J<PERSON>NField(name = "MODIFIERE<PERSON><PERSON>")
    private String modifierename;

    @JSONField(name = "<PERSON>W<PERSON><PERSON><PERSON><PERSON>")
    private String ownerename;
}