package com.jackrain.nea.ac.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import lombok.Data;

/**
 * @author: 陈俊明
 * @since: 2019-04-09
 * @create at : 2019-04-09 18:22
 */
@TableName(value = "ac_f_payable_adjustment")
@Data
@Document(index = "ac_f_payable_adjustment",type = "ac_f_payable_adjustment")
public class AcFPayableAdjustmentExDO extends AcFPayableAdjustmentDO{
    @JSONField(name = "PAY_TYPE_NAME")
    @Field(type = FieldType.Keyword)
    private String payTypeName = "";

    @JSONField(name = "BILL_TYPE_NAME")
    @Field(type = FieldType.Keyword)
    private String billTypeName = "";

    @JSONField(name = "BILL_STATUS_NAME")
    @Field(type = FieldType.Keyword)
    private String billStatusName = "";

    @JSONField(name = "ADJUST_TYPE_NAME")
    @Field(type = FieldType.Keyword)
    private String adjustTypeName = "";

    @JSONField(name = "CHANNEL_TYPE_NAME")
    @Field(type = FieldType.Keyword)
    private String channelTypeName = "";

    @JSONField(name = "RESPONSIBLE_PARTY_NAME")
    private String responsiblePartyName = "";

}
