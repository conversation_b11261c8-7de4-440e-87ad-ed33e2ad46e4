package com.jackrain.nea.ac.model;


import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;

/**
 * @Author: anna
 * @CreateDate: 2020/7/10$ 17:11$
 * @Description: 赔付类型明细表()
 */
@TableName(value = "AC_F_COMPENSATION_REASON")
@Data
@Document(index = "AC_F_COMPENSATION_REASON", type = "AC_F_COMPENSATION_REASON")
public class AcFCompensationReason extends BaseModel {

    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "AC_F_COMPENSATION_TYPE_ID")
    @Field(type = FieldType.Long)
    private Long acFCompensationTypeId;

    @JSONField(name = "AC_F_COMPENSATION_TYPE_ECODE")
    @Field(type = FieldType.Keyword)
    private String acFCompensationTypeEcode;

    @JSONField(name = "ac_f_compensation_type_ename")
    @Field(type = FieldType.Keyword)
    private String acFCompensationTypeEname;


}
