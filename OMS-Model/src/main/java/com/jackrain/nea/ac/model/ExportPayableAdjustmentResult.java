package com.jackrain.nea.ac.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author:洪艺安
 * @since: 2019/7/12
 * @create at : 2019/7/12 11:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportPayableAdjustmentResult implements Serializable {
    private List<AcFPayableAdjustmentExcel> acFPayableAdjustmentList;
    private List<AcFPayableAdjustmentItemExcel> acFPayableAdjustmentItemList;
}

