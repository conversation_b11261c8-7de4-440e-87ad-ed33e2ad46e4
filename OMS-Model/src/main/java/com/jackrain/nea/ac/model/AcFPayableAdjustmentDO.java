package com.jackrain.nea.ac.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName(value = "ac_f_payable_adjustment")
@Data
@Document(index = "ac_f_payable_adjustment", type = "ac_f_payable_adjustment")
public class AcFPayableAdjustmentDO extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "BILL_NO")
    @Field(type = FieldType.Keyword)
    private String billNo;

    @JSONField(name = "CP_C_SHOP_ID")
    @Field(type = FieldType.Long)
    private Long cpCShopId;

    @JSONField(name = "CP_C_SHOP_TITLE")
    @Field(type = FieldType.Keyword)
    private String cpCShopTitle;

    @JSONField(name = "PAY_TYPE")
    @Field(type = FieldType.Integer)
    private Integer payType;

    @JSONField(name = "PAY_TIME")
    @Field(type = FieldType.Date)
    private Date payTime;

    @JSONField(name = "REMARK")
    @Field(type = FieldType.Keyword)
    private String remark;

    @JSONField(name = "BILL_TYPE")
    @Field(type = FieldType.Integer)
    private Integer billType;

    @JSONField(name = "ORDER_NO")
    @Field(type = FieldType.Keyword)
    private String orderNo;

    @JSONField(name = "CUSTOMER_NAME")
    @Field(type = FieldType.Keyword)
    private String customerName;

    @JSONField(name = "CUSTOMER_TEL")
    @Field(type = FieldType.Keyword)
    private String customerTel;

    @JSONField(name = "ADJUST_TYPE")
    @Field(type = FieldType.Integer)
    private Integer adjustType;

    @JSONField(name = "ALIPAY_ACCOUNT")
    @Field(type = FieldType.Keyword)
    private String alipayAccount;

    @JSONField(name = "CUSTOMER_NICK")
    @Field(type = FieldType.Keyword)
    private String customerNick;

    @JSONField(name = "TID")
    @Field(type = FieldType.Keyword)
    private String tid;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ID")
    @Field(type = FieldType.Long)
    private Long cpCPhyWarehouseId;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEcode;

    @JSONField(name = "CP_C_PHY_WAREHOUSE_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCPhyWarehouseEname;

    @JSONField(name = "CP_C_LOGISTICS_ID")
    @Field(type = FieldType.Long)
    private Long cpCLogisticsId;

    @JSONField(name = "CP_C_LOGISTICS_ECODE")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEcode;

    @JSONField(name = "CP_C_LOGISTICS_ENAME")
    @Field(type = FieldType.Keyword)
    private String cpCLogisticsEname;

    @JSONField(name = "LOGISTICS_NO")
    @Field(type = FieldType.Keyword)
    private String logisticsNo;

    @JSONField(name = "BILL_STATUS")
    @Field(type = FieldType.Integer)
    private Integer billStatus;

    @JSONField(name = "PAYABLE_PRICE")
    @Field(type = FieldType.Double)
    private BigDecimal payablePrice;

    @JSONField(name = "OWNERENAME")
    @Field(type = FieldType.Keyword)
    private String ownerename;

    @JSONField(name = "MODIFIERENAME")
    @Field(type = FieldType.Keyword)
    private String modifierename;

    @JSONField(name = "DELID")
    @Field(type = FieldType.Long)
    private Long delid;

    @JSONField(name = "DELENAME")
    @Field(type = FieldType.Keyword)
    private String delename;

    @JSONField(name = "DELNAME")
    @Field(type = FieldType.Keyword)
    private String delname;

    @JSONField(name = "DEL_TIME")
    @Field(type = FieldType.Date)
    private Date delTime;

    @JSONField(name = "GUEST_TRIAL_ID")
    @Field(type = FieldType.Long)
    private Long guestTrialId;

    @JSONField(name = "GUEST_TRIAL_ENAME")
    @Field(type = FieldType.Keyword)
    private String guestTrialEname;

    @JSONField(name = "GUEST_TRIAL_NAME")
    @Field(type = FieldType.Keyword)
    private String guestTrialName;

    @JSONField(name = "GUEST_TRIAL_TIME")
    @Field(type = FieldType.Date)
    private Date guestTrialTime;

    @JSONField(name = "FINANCIAL_TRIAL_ID")
    @Field(type = FieldType.Long)
    private Long financialTrialId;

    @JSONField(name = "FINANCIAL_TRIAL_ENAME")
    @Field(type = FieldType.Keyword)
    private String financialTrialEname;

    @JSONField(name = "FINANCIAL_TRIAL_NAME")
    @Field(type = FieldType.Keyword)
    private String financialTrialName;

    @JSONField(name = "FINANCIAL_TRIAL_TIME")
    @Field(type = FieldType.Date)
    private Date financialTrialTime;

    @JSONField(name = "IMAGE")
    @Field(type = FieldType.Keyword)
    private String image;

    @JSONField(name = "RESERVE_DECIMAL01")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal01;

    @JSONField(name = "RESERVE_DECIMAL02")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal02;

    @JSONField(name = "RESERVE_DECIMAL03")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal03;

    @JSONField(name = "RESERVE_DECIMAL04")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal04;

    @JSONField(name = "RESERVE_DECIMAL05")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal05;

    @JSONField(name = "RESERVE_DECIMAL06")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal06;

    @JSONField(name = "RESERVE_DECIMAL07")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal07;

    @JSONField(name = "RESERVE_DECIMAL08")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal08;

    @JSONField(name = "RESERVE_DECIMAL09")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal09;

    @JSONField(name = "RESERVE_DECIMAL10")
    @Field(type = FieldType.Double)
    private BigDecimal reserveDecimal10;

    @JSONField(name = "RESERVE_BIGINT01")
    @Field(type = FieldType.Long)
    private Long reserveBigint01;

    @JSONField(name = "RESERVE_BIGINT02")
    @Field(type = FieldType.Long)
    private Long reserveBigint02;

    @JSONField(name = "RESERVE_BIGINT03")
    @Field(type = FieldType.Long)
    private Long reserveBigint03;

    @JSONField(name = "RESERVE_BIGINT04")
    @Field(type = FieldType.Long)
    private Long reserveBigint04;

    @JSONField(name = "RESERVE_BIGINT05")
    @Field(type = FieldType.Long)
    private Long reserveBigint05;

    @JSONField(name = "RESERVE_BIGINT06")
    @Field(type = FieldType.Long)
    private Long reserveBigint06;

    @JSONField(name = "RESERVE_BIGINT07")
    @Field(type = FieldType.Long)
    private Long reserveBigint07;

    @JSONField(name = "RESERVE_BIGINT08")
    @Field(type = FieldType.Long)
    private Long reserveBigint08;

    @JSONField(name = "RESERVE_BIGINT09")
    @Field(type = FieldType.Long)
    private Long reserveBigint09;

    @JSONField(name = "RESERVE_BIGINT10")
    @Field(type = FieldType.Long)
    private Long reserveBigint10;

    @JSONField(name = "RESERVE_VARCHAR01")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar01;

    @JSONField(name = "RESERVE_VARCHAR02")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar02;

    @JSONField(name = "RESERVE_VARCHAR03")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar03;

    @JSONField(name = "RESERVE_VARCHAR04")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar04;

    @JSONField(name = "RESERVE_VARCHAR05")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar05;

    @JSONField(name = "RESERVE_VARCHAR06")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar06;

    @JSONField(name = "RESERVE_VARCHAR07")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar07;

    @JSONField(name = "RESERVE_VARCHAR08")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar08;

    @JSONField(name = "RESERVE_VARCHAR09")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar09;

    @JSONField(name = "RESERVE_VARCHAR10")
    @Field(type = FieldType.Keyword)
    private String reserveVarchar10;

    @JSONField(name = "SOURCE_TID")
    @Field(type = FieldType.Keyword)
    private Long sourceTid;

    @JSONField(name = "SOURCE_OUTSOURCE_DATE")
    @Field(type = FieldType.Date)
    private Date sourceOutsourceDate;

    //新增赔付原因 ,快递网点ac_f_compensation_reason_id

    @JSONField(name = "AC_F_COMPENSATION_REASON_ID")
    @Field(type = FieldType.Integer)
    private Integer acFCompensationReasonId;

    @JSONField(name = "COMPENSATION_REASON")
    @Field(type = FieldType.Keyword)
    private String compensationReason;

    @JSONField(name = "EXPRESS_OUTLETS")
    @Field(type = FieldType.Keyword)
    private String expressOutlets;

    @JSONField(name = "COMPENSATION_TYPE_ENAME")
    @Field(type = FieldType.Keyword)
    private String compensationTypeEname;

    @JSONField(name = "AC_F_COMPENSATION_TYPE_ID")
    @Field(type = FieldType.Integer)
    private Integer acFCompensationTypeId;

    @JSONField(name = "RESPONSIBLE_PARTY")
    @Field(type = FieldType.Integer)
    private Integer responsibleParty;

    @JSONField(name = "RESPONSIBLE_PERSON")
    @Field(type = FieldType.Keyword)
    private String responsiblePerson;

    @JSONField(name = "REASON_REMARK")
    @Field(type = FieldType.Keyword)
    private String reasonRemark;

    @JSONField(name = "ORIGIN_ORDER_AMT")
    @Field(type = FieldType.Double)
    private BigDecimal originOrderAmt;

    @JSONField(name = "IS_RECEIVE_PAYMENT")
    @Field(type = FieldType.Keyword)
    private String isReceivePayment;

    /**
     * 0-未传DRP；1-传DRP中；2-传DRP成功；3-传DRP失败
     */
    @ApiModelProperty(value = "传DRP状态")
    @JSONField(name = "TO_DRP_STATUS")
    @Field(type = FieldType.Keyword)
    private String toDrpStatus;

    @ApiModelProperty(value = "传DRP次数")
    @JSONField(name = "TO_DRP_COUNT")
    @Field(type = FieldType.Integer)
    private Integer toDrpCount;

    @ApiModelProperty(value = "传DRP失败原因")
    @JSONField(name = "TO_DRP_FAILED_REASON")
    @Field(type = FieldType.Keyword)
    private String toDrpFailedReason;

}