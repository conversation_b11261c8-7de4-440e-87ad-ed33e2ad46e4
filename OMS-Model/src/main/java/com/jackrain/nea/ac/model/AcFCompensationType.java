package com.jackrain.nea.ac.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jackrain.nea.es.annotations.Document;
import com.jackrain.nea.es.annotations.Field;
import com.jackrain.nea.es.constans.FieldType;
import com.jackrain.nea.sys.domain.BaseModel;
import lombok.Data;


/**
 * @Author: anna
 * @CreateDate: 2020/7/10$ 17:11$
 * @Description: 赔付类型主表
 */
@TableName(value = "ac_f_compensation_type")
@Data
@Document(index = "ac_f_compensation_type", type = "ac_f_compensation_type")
public class AcFCompensationType extends BaseModel {
    @JSONField(name = "ID")
    @TableField(fill = FieldFill.INSERT)
    @TableId(value = "id", type = IdType.INPUT)
    @Field(type = FieldType.Long)
    private Long id;

    @JSONField(name = "ECODE")
    @Field(type = FieldType.Keyword)
    private String ecode;

    @JSONField(name = "ENAME")
    @Field(type = FieldType.Keyword)
    private String ename;

}
