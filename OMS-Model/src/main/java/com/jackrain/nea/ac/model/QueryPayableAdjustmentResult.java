package com.jackrain.nea.ac.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: 陈俊明
 * @since: 2019-04-08
 * @create at : 2019-04-08 16:05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryPayableAdjustmentResult implements Serializable {
    private AcFPayableAdjustmentDO acFPayableAdjustment;
    private List<AcFPayableAdjustmentItemDO> acFPayableAdjustmentItemList;
    private List<AcFPayableAdjustmentLogDO> acFPayableAdjustmentLogList;
}
